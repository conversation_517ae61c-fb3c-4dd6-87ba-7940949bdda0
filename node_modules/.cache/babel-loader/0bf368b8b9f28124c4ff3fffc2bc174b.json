{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/utils/index.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/utils/index.js", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js", "mtime": 1751171659043}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}