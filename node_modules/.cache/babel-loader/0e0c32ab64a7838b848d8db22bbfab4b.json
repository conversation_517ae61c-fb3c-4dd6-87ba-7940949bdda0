{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/tool/build/FormDrawer.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/tool/build/FormDrawer.vue", "mtime": 1641277326000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["parse", "ClipboardJS", "saveAs", "makeUpHtml", "vueTemplate", "vueScript", "cssStyle", "makeUpJs", "makeUpCss", "exportDefault", "beautifierConf", "titleCase", "ResourceDialog", "loadMonaco", "loadBeautifier", "editor<PERSON><PERSON><PERSON>", "html", "js", "css", "mode", "beautifier", "monaco", "components", "props", "data", "activeTab", "htmlCode", "jsCode", "cssCode", "codeFrame", "isIframeLoaded", "isInitcode", "isRefreshCode", "resourceVisible", "scripts", "links", "computed", "resources", "concat", "watch", "created", "mounted", "_this", "window", "addEventListener", "preventDefaultSave", "clipboard", "text", "trigger", "codeStr", "generateCode", "$notify", "title", "message", "type", "on", "e", "$message", "error", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "methods", "key", "metaKey", "ctrl<PERSON>ey", "preventDefault", "onOpen", "_this2", "generateConf", "formData", "btf", "val", "setEditorValue", "runCode", "onClose", "iframeLoad", "id", "_this3", "setValue", "editor", "create", "document", "getElementById", "value", "theme", "language", "automaticLayout", "onKeyDown", "keyCode", "jsCodeStr", "getValue", "ast", "sourceType", "astBody", "program", "body", "length", "$confirm", "postData", "replace", "$refs", "previewPage", "contentWindow", "postMessage", "location", "origin", "err", "console", "script", "exportFile", "_this4", "$prompt", "inputValue", "Date", "closeOnClickModal", "inputPlaceholder", "then", "_ref", "blob", "Blob", "showResource", "setResource", "arr", "Array", "isArray", "for<PERSON>ach", "item", "endsWith", "push"], "sources": ["src/views/tool/build/FormDrawer.vue"], "sourcesContent": ["<template>\n  <div>\n    <el-drawer v-bind=\"$attrs\" v-on=\"$listeners\" @opened=\"onOpen\" @close=\"onClose\">\n      <div style=\"height:100%\">\n        <el-row style=\"height:100%;overflow:auto\">\n          <el-col :md=\"24\" :lg=\"12\" class=\"left-editor\">\n            <div class=\"setting\" title=\"资源引用\" @click=\"showResource\">\n              <el-badge :is-dot=\"!!resources.length\" class=\"item\">\n                <i class=\"el-icon-setting\" />\n              </el-badge>\n            </div>\n            <el-tabs v-model=\"activeTab\" type=\"card\" class=\"editor-tabs\">\n              <el-tab-pane name=\"html\">\n                <span slot=\"label\">\n                  <i v-if=\"activeTab==='html'\" class=\"el-icon-edit\" />\n                  <i v-else class=\"el-icon-document\" />\n                  template\n                </span>\n              </el-tab-pane>\n              <el-tab-pane name=\"js\">\n                <span slot=\"label\">\n                  <i v-if=\"activeTab==='js'\" class=\"el-icon-edit\" />\n                  <i v-else class=\"el-icon-document\" />\n                  script\n                </span>\n              </el-tab-pane>\n              <el-tab-pane name=\"css\">\n                <span slot=\"label\">\n                  <i v-if=\"activeTab==='css'\" class=\"el-icon-edit\" />\n                  <i v-else class=\"el-icon-document\" />\n                  css\n                </span>\n              </el-tab-pane>\n            </el-tabs>\n            <div v-show=\"activeTab==='html'\" id=\"editorHtml\" class=\"tab-editor\" />\n            <div v-show=\"activeTab==='js'\" id=\"editorJs\" class=\"tab-editor\" />\n            <div v-show=\"activeTab==='css'\" id=\"editorCss\" class=\"tab-editor\" />\n          </el-col>\n          <el-col :md=\"24\" :lg=\"12\" class=\"right-preview\">\n            <div class=\"action-bar\" :style=\"{'text-align': 'left'}\">\n              <span class=\"bar-btn\" @click=\"runCode\">\n                <i class=\"el-icon-refresh\" />\n                刷新\n              </span>\n              <span class=\"bar-btn\" @click=\"exportFile\">\n                <i class=\"el-icon-download\" />\n                导出vue文件\n              </span>\n              <span ref=\"copyBtn\" class=\"bar-btn copy-btn\">\n                <i class=\"el-icon-document-copy\" />\n                复制代码\n              </span>\n              <span class=\"bar-btn delete-btn\" @click=\"$emit('update:visible', false)\">\n                <i class=\"el-icon-circle-close\" />\n                关闭\n              </span>\n            </div>\n            <iframe\n              v-show=\"isIframeLoaded\"\n              ref=\"previewPage\"\n              class=\"result-wrapper\"\n              frameborder=\"0\"\n              src=\"preview.html\"\n              @load=\"iframeLoad\"\n            />\n            <div v-show=\"!isIframeLoaded\" v-loading=\"true\" class=\"result-wrapper\" />\n          </el-col>\n        </el-row>\n      </div>\n    </el-drawer>\n    <resource-dialog\n      :visible.sync=\"resourceVisible\"\n      :origin-resource=\"resources\"\n      @save=\"setResource\"\n    />\n  </div>\n</template>\n<script>\nimport { parse } from '@babel/parser'\nimport ClipboardJS from 'clipboard'\nimport { saveAs } from 'file-saver'\nimport {\n  makeUpHtml, vueTemplate, vueScript, cssStyle\n} from '@/utils/generator/html'\nimport { makeUpJs } from '@/utils/generator/js'\nimport { makeUpCss } from '@/utils/generator/css'\nimport { exportDefault, beautifierConf, titleCase } from '@/utils/index'\nimport ResourceDialog from './ResourceDialog'\nimport loadMonaco from '@/utils/loadMonaco'\nimport loadBeautifier from '@/utils/loadBeautifier'\n\nconst editorObj = {\n  html: null,\n  js: null,\n  css: null\n}\nconst mode = {\n  html: 'html',\n  js: 'javascript',\n  css: 'css'\n}\nlet beautifier\nlet monaco\n\nexport default {\n  components: { ResourceDialog },\n  props: ['formData', 'generateConf'],\n  data() {\n    return {\n      activeTab: 'html',\n      htmlCode: '',\n      jsCode: '',\n      cssCode: '',\n      codeFrame: '',\n      isIframeLoaded: false,\n      isInitcode: false, // 保证open后两个异步只执行一次runcode\n      isRefreshCode: false, // 每次打开都需要重新刷新代码\n      resourceVisible: false,\n      scripts: [],\n      links: [],\n      monaco: null\n    }\n  },\n  computed: {\n    resources() {\n      return this.scripts.concat(this.links)\n    }\n  },\n  watch: {},\n  created() {\n  },\n  mounted() {\n    window.addEventListener('keydown', this.preventDefaultSave)\n    const clipboard = new ClipboardJS('.copy-btn', {\n      text: trigger => {\n        const codeStr = this.generateCode()\n        this.$notify({\n          title: '成功',\n          message: '代码已复制到剪切板，可粘贴。',\n          type: 'success'\n        })\n        return codeStr\n      }\n    })\n    clipboard.on('error', e => {\n      this.$message.error('代码复制失败')\n    })\n  },\n  beforeDestroy() {\n    window.removeEventListener('keydown', this.preventDefaultSave)\n  },\n  methods: {\n    preventDefaultSave(e) {\n      if (e.key === 's' && (e.metaKey || e.ctrlKey)) {\n        e.preventDefault()\n      }\n    },\n    onOpen() {\n      const { type } = this.generateConf\n      this.htmlCode = makeUpHtml(this.formData, type)\n      this.jsCode = makeUpJs(this.formData, type)\n      this.cssCode = makeUpCss(this.formData)\n\n      loadBeautifier(btf => {\n        beautifier = btf\n        this.htmlCode = beautifier.html(this.htmlCode, beautifierConf.html)\n        this.jsCode = beautifier.js(this.jsCode, beautifierConf.js)\n        this.cssCode = beautifier.css(this.cssCode, beautifierConf.html)\n\n        loadMonaco(val => {\n          monaco = val\n          this.setEditorValue('editorHtml', 'html', this.htmlCode)\n          this.setEditorValue('editorJs', 'js', this.jsCode)\n          this.setEditorValue('editorCss', 'css', this.cssCode)\n          if (!this.isInitcode) {\n            this.isRefreshCode = true\n            this.isIframeLoaded && (this.isInitcode = true) && this.runCode()\n          }\n        })\n      })\n    },\n    onClose() {\n      this.isInitcode = false\n      this.isRefreshCode = false\n    },\n    iframeLoad() {\n      if (!this.isInitcode) {\n        this.isIframeLoaded = true\n        this.isRefreshCode && (this.isInitcode = true) && this.runCode()\n      }\n    },\n    setEditorValue(id, type, codeStr) {\n      if (editorObj[type]) {\n        editorObj[type].setValue(codeStr)\n      } else {\n        editorObj[type] = monaco.editor.create(document.getElementById(id), {\n          value: codeStr,\n          theme: 'vs-dark',\n          language: mode[type],\n          automaticLayout: true\n        })\n      }\n      // ctrl + s 刷新\n      editorObj[type].onKeyDown(e => {\n        if (e.keyCode === 49 && (e.metaKey || e.ctrlKey)) {\n          this.runCode()\n        }\n      })\n    },\n    runCode() {\n      const jsCodeStr = editorObj.js.getValue()\n      try {\n        const ast = parse(jsCodeStr, { sourceType: 'module' })\n        const astBody = ast.program.body\n        if (astBody.length > 1) {\n          this.$confirm(\n            'js格式不能识别，仅支持修改export default的对象内容',\n            '提示',\n            {\n              type: 'warning'\n            }\n          )\n          return\n        }\n        if (astBody[0].type === 'ExportDefaultDeclaration') {\n          const postData = {\n            type: 'refreshFrame',\n            data: {\n              generateConf: this.generateConf,\n              html: editorObj.html.getValue(),\n              js: jsCodeStr.replace(exportDefault, ''),\n              css: editorObj.css.getValue(),\n              scripts: this.scripts,\n              links: this.links\n            }\n          }\n\n          this.$refs.previewPage.contentWindow.postMessage(\n            postData,\n            location.origin\n          )\n        } else {\n          this.$message.error('请使用export default')\n        }\n      } catch (err) {\n        this.$message.error(`js错误：${err}`)\n        console.error(err)\n      }\n    },\n    generateCode() {\n      const html = vueTemplate(editorObj.html.getValue())\n      const script = vueScript(editorObj.js.getValue())\n      const css = cssStyle(editorObj.css.getValue())\n      return beautifier.html(html + script + css, beautifierConf.html)\n    },\n    exportFile() {\n      this.$prompt('文件名:', '导出文件', {\n        inputValue: `${+new Date()}.vue`,\n        closeOnClickModal: false,\n        inputPlaceholder: '请输入文件名'\n      }).then(({ value }) => {\n        if (!value) value = `${+new Date()}.vue`\n        const codeStr = this.generateCode()\n        const blob = new Blob([codeStr], { type: 'text/plain;charset=utf-8' })\n        saveAs(blob, value)\n      })\n    },\n    showResource() {\n      this.resourceVisible = true\n    },\n    setResource(arr) {\n      const scripts = []; const\n        links = []\n      if (Array.isArray(arr)) {\n        arr.forEach(item => {\n          if (item.endsWith('.css')) {\n            links.push(item)\n          } else {\n            scripts.push(item)\n          }\n        })\n        this.scripts = scripts\n        this.links = links\n      } else {\n        this.scripts = []\n        this.links = []\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import '@/styles/mixin.scss';\n.tab-editor {\n  position: absolute;\n  top: 33px;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  font-size: 14px;\n}\n.left-editor {\n  position: relative;\n  height: 100%;\n  background: #1e1e1e;\n  overflow: hidden;\n}\n.setting{\n  position: absolute;\n  right: 15px;\n  top: 3px;\n  color: #a9f122;\n  font-size: 18px;\n  cursor: pointer;\n  z-index: 1;\n}\n.right-preview {\n  height: 100%;\n  .result-wrapper {\n    height: calc(100vh - 33px);\n    width: 100%;\n    overflow: auto;\n    padding: 12px;\n    box-sizing: border-box;\n  }\n}\n@include action-bar;\n::v-deep .el-drawer__header {\n  display: none;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8EA,SAAAA,KAAA;AACA,OAAAC,WAAA;AACA,SAAAC,MAAA;AACA,SACAC,UAAA,EAAAC,WAAA,EAAAC,SAAA,EAAAC,QAAA,QACA;AACA,SAAAC,QAAA;AACA,SAAAC,SAAA;AACA,SAAAC,aAAA,EAAAC,cAAA,EAAAC,SAAA;AACA,OAAAC,cAAA;AACA,OAAAC,UAAA;AACA,OAAAC,cAAA;AAEA,IAAAC,SAAA;EACAC,IAAA;EACAC,EAAA;EACAC,GAAA;AACA;AACA,IAAAC,IAAA;EACAH,IAAA;EACAC,EAAA;EACAC,GAAA;AACA;AACA,IAAAE,UAAA;AACA,IAAAC,MAAA;AAEA;EACAC,UAAA;IAAAV,cAAA,EAAAA;EAAA;EACAW,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA;MACAC,QAAA;MACAC,MAAA;MACAC,OAAA;MACAC,SAAA;MACAC,cAAA;MACAC,UAAA;MAAA;MACAC,aAAA;MAAA;MACAC,eAAA;MACAC,OAAA;MACAC,KAAA;MACAd,MAAA;IACA;EACA;EACAe,QAAA;IACAC,SAAA,WAAAA,UAAA;MACA,YAAAH,OAAA,CAAAI,MAAA,MAAAH,KAAA;IACA;EACA;EACAI,KAAA;EACAC,OAAA,WAAAA,QAAA,GACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACAC,MAAA,CAAAC,gBAAA,iBAAAC,kBAAA;IACA,IAAAC,SAAA,OAAA7C,WAAA;MACA8C,IAAA,WAAAA,KAAAC,OAAA;QACA,IAAAC,OAAA,GAAAP,KAAA,CAAAQ,YAAA;QACAR,KAAA,CAAAS,OAAA;UACAC,KAAA;UACAC,OAAA;UACAC,IAAA;QACA;QACA,OAAAL,OAAA;MACA;IACA;IACAH,SAAA,CAAAS,EAAA,oBAAAC,CAAA;MACAd,KAAA,CAAAe,QAAA,CAAAC,KAAA;IACA;EACA;EACAC,aAAA,WAAAA,cAAA;IACAhB,MAAA,CAAAiB,mBAAA,iBAAAf,kBAAA;EACA;EACAgB,OAAA;IACAhB,kBAAA,WAAAA,mBAAAW,CAAA;MACA,IAAAA,CAAA,CAAAM,GAAA,aAAAN,CAAA,CAAAO,OAAA,IAAAP,CAAA,CAAAQ,OAAA;QACAR,CAAA,CAAAS,cAAA;MACA;IACA;IACAC,MAAA,WAAAA,OAAA;MAAA,IAAAC,MAAA;MACA,IAAAb,IAAA,QAAAc,YAAA,CAAAd,IAAA;MACA,KAAA5B,QAAA,GAAAvB,UAAA,MAAAkE,QAAA,EAAAf,IAAA;MACA,KAAA3B,MAAA,GAAApB,QAAA,MAAA8D,QAAA,EAAAf,IAAA;MACA,KAAA1B,OAAA,GAAApB,SAAA,MAAA6D,QAAA;MAEAvD,cAAA,WAAAwD,GAAA;QACAlD,UAAA,GAAAkD,GAAA;QACAH,MAAA,CAAAzC,QAAA,GAAAN,UAAA,CAAAJ,IAAA,CAAAmD,MAAA,CAAAzC,QAAA,EAAAhB,cAAA,CAAAM,IAAA;QACAmD,MAAA,CAAAxC,MAAA,GAAAP,UAAA,CAAAH,EAAA,CAAAkD,MAAA,CAAAxC,MAAA,EAAAjB,cAAA,CAAAO,EAAA;QACAkD,MAAA,CAAAvC,OAAA,GAAAR,UAAA,CAAAF,GAAA,CAAAiD,MAAA,CAAAvC,OAAA,EAAAlB,cAAA,CAAAM,IAAA;QAEAH,UAAA,WAAA0D,GAAA;UACAlD,MAAA,GAAAkD,GAAA;UACAJ,MAAA,CAAAK,cAAA,uBAAAL,MAAA,CAAAzC,QAAA;UACAyC,MAAA,CAAAK,cAAA,mBAAAL,MAAA,CAAAxC,MAAA;UACAwC,MAAA,CAAAK,cAAA,qBAAAL,MAAA,CAAAvC,OAAA;UACA,KAAAuC,MAAA,CAAApC,UAAA;YACAoC,MAAA,CAAAnC,aAAA;YACAmC,MAAA,CAAArC,cAAA,KAAAqC,MAAA,CAAApC,UAAA,YAAAoC,MAAA,CAAAM,OAAA;UACA;QACA;MACA;IACA;IACAC,OAAA,WAAAA,QAAA;MACA,KAAA3C,UAAA;MACA,KAAAC,aAAA;IACA;IACA2C,UAAA,WAAAA,WAAA;MACA,UAAA5C,UAAA;QACA,KAAAD,cAAA;QACA,KAAAE,aAAA,UAAAD,UAAA,iBAAA0C,OAAA;MACA;IACA;IACAD,cAAA,WAAAA,eAAAI,EAAA,EAAAtB,IAAA,EAAAL,OAAA;MAAA,IAAA4B,MAAA;MACA,IAAA9D,SAAA,CAAAuC,IAAA;QACAvC,SAAA,CAAAuC,IAAA,EAAAwB,QAAA,CAAA7B,OAAA;MACA;QACAlC,SAAA,CAAAuC,IAAA,IAAAjC,MAAA,CAAA0D,MAAA,CAAAC,MAAA,CAAAC,QAAA,CAAAC,cAAA,CAAAN,EAAA;UACAO,KAAA,EAAAlC,OAAA;UACAmC,KAAA;UACAC,QAAA,EAAAlE,IAAA,CAAAmC,IAAA;UACAgC,eAAA;QACA;MACA;MACA;MACAvE,SAAA,CAAAuC,IAAA,EAAAiC,SAAA,WAAA/B,CAAA;QACA,IAAAA,CAAA,CAAAgC,OAAA,YAAAhC,CAAA,CAAAO,OAAA,IAAAP,CAAA,CAAAQ,OAAA;UACAa,MAAA,CAAAJ,OAAA;QACA;MACA;IACA;IACAA,OAAA,WAAAA,QAAA;MACA,IAAAgB,SAAA,GAAA1E,SAAA,CAAAE,EAAA,CAAAyE,QAAA;MACA;QACA,IAAAC,GAAA,GAAA3F,KAAA,CAAAyF,SAAA;UAAAG,UAAA;QAAA;QACA,IAAAC,OAAA,GAAAF,GAAA,CAAAG,OAAA,CAAAC,IAAA;QACA,IAAAF,OAAA,CAAAG,MAAA;UACA,KAAAC,QAAA,CACA,qCACA,MACA;YACA3C,IAAA;UACA,CACA;UACA;QACA;QACA,IAAAuC,OAAA,IAAAvC,IAAA;UACA,IAAA4C,QAAA;YACA5C,IAAA;YACA9B,IAAA;cACA4C,YAAA,OAAAA,YAAA;cACApD,IAAA,EAAAD,SAAA,CAAAC,IAAA,CAAA0E,QAAA;cACAzE,EAAA,EAAAwE,SAAA,CAAAU,OAAA,CAAA1F,aAAA;cACAS,GAAA,EAAAH,SAAA,CAAAG,GAAA,CAAAwE,QAAA;cACAxD,OAAA,OAAAA,OAAA;cACAC,KAAA,OAAAA;YACA;UACA;UAEA,KAAAiE,KAAA,CAAAC,WAAA,CAAAC,aAAA,CAAAC,WAAA,CACAL,QAAA,EACAM,QAAA,CAAAC,MACA;QACA;UACA,KAAAhD,QAAA,CAAAC,KAAA;QACA;MACA,SAAAgD,GAAA;QACA,KAAAjD,QAAA,CAAAC,KAAA,wBAAApB,MAAA,CAAAoE,GAAA;QACAC,OAAA,CAAAjD,KAAA,CAAAgD,GAAA;MACA;IACA;IACAxD,YAAA,WAAAA,aAAA;MACA,IAAAlC,IAAA,GAAAZ,WAAA,CAAAW,SAAA,CAAAC,IAAA,CAAA0E,QAAA;MACA,IAAAkB,MAAA,GAAAvG,SAAA,CAAAU,SAAA,CAAAE,EAAA,CAAAyE,QAAA;MACA,IAAAxE,GAAA,GAAAZ,QAAA,CAAAS,SAAA,CAAAG,GAAA,CAAAwE,QAAA;MACA,OAAAtE,UAAA,CAAAJ,IAAA,CAAAA,IAAA,GAAA4F,MAAA,GAAA1F,GAAA,EAAAR,cAAA,CAAAM,IAAA;IACA;IACA6F,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,OAAA;QACAC,UAAA,KAAA1E,MAAA,MAAA2E,IAAA;QACAC,iBAAA;QACAC,gBAAA;MACA,GAAAC,IAAA,WAAAC,IAAA;QAAA,IAAAlC,KAAA,GAAAkC,IAAA,CAAAlC,KAAA;QACA,KAAAA,KAAA,EAAAA,KAAA,MAAA7C,MAAA,MAAA2E,IAAA;QACA,IAAAhE,OAAA,GAAA6D,MAAA,CAAA5D,YAAA;QACA,IAAAoE,IAAA,OAAAC,IAAA,EAAAtE,OAAA;UAAAK,IAAA;QAAA;QACApD,MAAA,CAAAoH,IAAA,EAAAnC,KAAA;MACA;IACA;IACAqC,YAAA,WAAAA,aAAA;MACA,KAAAvF,eAAA;IACA;IACAwF,WAAA,WAAAA,YAAAC,GAAA;MACA,IAAAxF,OAAA;MAAA,IACAC,KAAA;MACA,IAAAwF,KAAA,CAAAC,OAAA,CAAAF,GAAA;QACAA,GAAA,CAAAG,OAAA,WAAAC,IAAA;UACA,IAAAA,IAAA,CAAAC,QAAA;YACA5F,KAAA,CAAA6F,IAAA,CAAAF,IAAA;UACA;YACA5F,OAAA,CAAA8F,IAAA,CAAAF,IAAA;UACA;QACA;QACA,KAAA5F,OAAA,GAAAA,OAAA;QACA,KAAAC,KAAA,GAAAA,KAAA;MACA;QACA,KAAAD,OAAA;QACA,KAAAC,KAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}