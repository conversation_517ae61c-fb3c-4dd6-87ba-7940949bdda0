{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/monitor/server/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/monitor/server/index.vue", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldFNlcnZlciB9IGZyb20gIkAvYXBpL21vbml0b3Ivc2VydmVyIjsKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICJTZXJ2ZXIiLAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICAvLyDliqDovb3lsYLkv6Hmga8KICAgICAgbG9hZGluZzogW10sCiAgICAgIC8vIOacjeWKoeWZqOS/oeaBrwogICAgICBzZXJ2ZXI6IFtdCiAgICB9OwogIH0sCiAgY3JlYXRlZDogZnVuY3Rpb24gY3JlYXRlZCgpIHsKICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgdGhpcy5vcGVuTG9hZGluZygpOwogIH0sCiAgbWV0aG9kczogewogICAgLyoqIOafpeivouacjeWKoeWZqOS/oeaBryAqL2dldExpc3Q6IGZ1bmN0aW9uIGdldExpc3QoKSB7CiAgICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICAgIGdldFNlcnZlcigpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgX3RoaXMuc2VydmVyID0gcmVzcG9uc2UuZGF0YTsKICAgICAgICBfdGhpcy5sb2FkaW5nLmNsb3NlKCk7CiAgICAgIH0pOwogICAgfSwKICAgIC8vIOaJk+W8gOWKoOi9veWxggogICAgb3BlbkxvYWRpbmc6IGZ1bmN0aW9uIG9wZW5Mb2FkaW5nKCkgewogICAgICB0aGlzLmxvYWRpbmcgPSB0aGlzLiRsb2FkaW5nKHsKICAgICAgICBsb2NrOiB0cnVlLAogICAgICAgIHRleHQ6ICLmi7zlkb3or7vlj5bkuK0iLAogICAgICAgIHNwaW5uZXI6ICJlbC1pY29uLWxvYWRpbmciLAogICAgICAgIGJhY2tncm91bmQ6ICJyZ2JhKDAsIDAsIDAsIDAuNykiCiAgICAgIH0pOwogICAgfQogIH0KfTs="}, {"version": 3, "names": ["getServer", "name", "data", "loading", "server", "created", "getList", "openLoading", "methods", "_this", "then", "response", "close", "$loading", "lock", "text", "spinner", "background"], "sources": ["src/views/monitor/server/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-row>\n      <el-col :span=\"12\" class=\"card-box\">\n        <el-card>\n          <div slot=\"header\"><span>CPU</span></div>\n          <div class=\"el-table el-table--enable-row-hover el-table--medium\">\n            <table cellspacing=\"0\" style=\"width: 100%;\">\n              <thead>\n                <tr>\n                  <th class=\"is-leaf\"><div class=\"cell\">属性</div></th>\n                  <th class=\"is-leaf\"><div class=\"cell\">值</div></th>\n                </tr>\n              </thead>\n              <tbody>\n                <tr>\n                  <td><div class=\"cell\">核心数</div></td>\n                  <td><div class=\"cell\" v-if=\"server.cpu\">{{ server.cpu.cpuNum }}</div></td>\n                </tr>\n                <tr>\n                  <td><div class=\"cell\">用户使用率</div></td>\n                  <td><div class=\"cell\" v-if=\"server.cpu\">{{ server.cpu.used }}%</div></td>\n                </tr>\n                <tr>\n                  <td><div class=\"cell\">系统使用率</div></td>\n                  <td><div class=\"cell\" v-if=\"server.cpu\">{{ server.cpu.sys }}%</div></td>\n                </tr>\n                <tr>\n                  <td><div class=\"cell\">当前空闲率</div></td>\n                  <td><div class=\"cell\" v-if=\"server.cpu\">{{ server.cpu.free }}%</div></td>\n                </tr>\n              </tbody>\n            </table>\n          </div>\n        </el-card>\n      </el-col>\n\n      <el-col :span=\"12\" class=\"card-box\">\n        <el-card>\n          <div slot=\"header\"><span>内存</span></div>\n          <div class=\"el-table el-table--enable-row-hover el-table--medium\">\n            <table cellspacing=\"0\" style=\"width: 100%;\">\n              <thead>\n                <tr>\n                  <th class=\"is-leaf\"><div class=\"cell\">属性</div></th>\n                  <th class=\"is-leaf\"><div class=\"cell\">内存</div></th>\n                  <th class=\"is-leaf\"><div class=\"cell\">JVM</div></th>\n                </tr>\n              </thead>\n              <tbody>\n                <tr>\n                  <td><div class=\"cell\">总内存</div></td>\n                  <td><div class=\"cell\" v-if=\"server.mem\">{{ server.mem.total }}G</div></td>\n                  <td><div class=\"cell\" v-if=\"server.jvm\">{{ server.jvm.total }}M</div></td>\n                </tr>\n                <tr>\n                  <td><div class=\"cell\">已用内存</div></td>\n                  <td><div class=\"cell\" v-if=\"server.mem\">{{ server.mem.used}}G</div></td>\n                  <td><div class=\"cell\" v-if=\"server.jvm\">{{ server.jvm.used}}M</div></td>\n                </tr>\n                <tr>\n                  <td><div class=\"cell\">剩余内存</div></td>\n                  <td><div class=\"cell\" v-if=\"server.mem\">{{ server.mem.free }}G</div></td>\n                  <td><div class=\"cell\" v-if=\"server.jvm\">{{ server.jvm.free }}M</div></td>\n                </tr>\n                <tr>\n                  <td><div class=\"cell\">使用率</div></td>\n                  <td><div class=\"cell\" v-if=\"server.mem\" :class=\"{'text-danger': server.mem.usage > 80}\">{{ server.mem.usage }}%</div></td>\n                  <td><div class=\"cell\" v-if=\"server.jvm\" :class=\"{'text-danger': server.jvm.usage > 80}\">{{ server.jvm.usage }}%</div></td>\n                </tr>\n              </tbody>\n            </table>\n          </div>\n        </el-card>\n      </el-col>\n\n      <el-col :span=\"24\" class=\"card-box\">\n        <el-card>\n          <div slot=\"header\">\n            <span>服务器信息</span>\n          </div>\n          <div class=\"el-table el-table--enable-row-hover el-table--medium\">\n            <table cellspacing=\"0\" style=\"width: 100%;\">\n              <tbody>\n                <tr>\n                  <td><div class=\"cell\">服务器名称</div></td>\n                  <td><div class=\"cell\" v-if=\"server.sys\">{{ server.sys.computerName }}</div></td>\n                  <td><div class=\"cell\">操作系统</div></td>\n                  <td><div class=\"cell\" v-if=\"server.sys\">{{ server.sys.osName }}</div></td>\n                </tr>\n                <tr>\n                  <td><div class=\"cell\">服务器IP</div></td>\n                  <td><div class=\"cell\" v-if=\"server.sys\">{{ server.sys.computerIp }}</div></td>\n                  <td><div class=\"cell\">系统架构</div></td>\n                  <td><div class=\"cell\" v-if=\"server.sys\">{{ server.sys.osArch }}</div></td>\n                </tr>\n              </tbody>\n            </table>\n          </div>\n        </el-card>\n      </el-col>\n\n      <el-col :span=\"24\" class=\"card-box\">\n        <el-card>\n          <div slot=\"header\">\n            <span>Java虚拟机信息</span>\n          </div>\n          <div class=\"el-table el-table--enable-row-hover el-table--medium\">\n            <table cellspacing=\"0\" style=\"width: 100%;\">\n              <tbody>\n                <tr>\n                  <td><div class=\"cell\">Java名称</div></td>\n                  <td><div class=\"cell\" v-if=\"server.jvm\">{{ server.jvm.name }}</div></td>\n                  <td><div class=\"cell\">Java版本</div></td>\n                  <td><div class=\"cell\" v-if=\"server.jvm\">{{ server.jvm.version }}</div></td>\n                </tr>\n                <tr>\n                  <td><div class=\"cell\">启动时间</div></td>\n                  <td><div class=\"cell\" v-if=\"server.jvm\">{{ server.jvm.startTime }}</div></td>\n                  <td><div class=\"cell\">运行时长</div></td>\n                  <td><div class=\"cell\" v-if=\"server.jvm\">{{ server.jvm.runTime }}</div></td>\n                </tr>\n                <tr>\n                  <td colspan=\"1\"><div class=\"cell\">安装路径</div></td>\n                  <td colspan=\"3\"><div class=\"cell\" v-if=\"server.jvm\">{{ server.jvm.home }}</div></td>\n                </tr>\n                <tr>\n                  <td colspan=\"1\"><div class=\"cell\">项目路径</div></td>\n                  <td colspan=\"3\"><div class=\"cell\" v-if=\"server.sys\">{{ server.sys.userDir }}</div></td>\n                </tr>\n              </tbody>\n            </table>\n          </div>\n        </el-card>\n      </el-col>\n\n      <el-col :span=\"24\" class=\"card-box\">\n        <el-card>\n          <div slot=\"header\">\n            <span>磁盘状态</span>\n          </div>\n          <div class=\"el-table el-table--enable-row-hover el-table--medium\">\n            <table cellspacing=\"0\" style=\"width: 100%;\">\n              <thead>\n                <tr>\n                  <th class=\"is-leaf\"><div class=\"cell\">盘符路径</div></th>\n                  <th class=\"is-leaf\"><div class=\"cell\">文件系统</div></th>\n                  <th class=\"is-leaf\"><div class=\"cell\">盘符类型</div></th>\n                  <th class=\"is-leaf\"><div class=\"cell\">总大小</div></th>\n                  <th class=\"is-leaf\"><div class=\"cell\">可用大小</div></th>\n                  <th class=\"is-leaf\"><div class=\"cell\">已用大小</div></th>\n                  <th class=\"is-leaf\"><div class=\"cell\">已用百分比</div></th>\n                </tr>\n              </thead>\n              <tbody v-if=\"server.sysFiles\">\n                <tr v-for=\"sysFile in server.sysFiles\">\n                  <td><div class=\"cell\">{{ sysFile.dirName }}</div></td>\n                  <td><div class=\"cell\">{{ sysFile.sysTypeName }}</div></td>\n                  <td><div class=\"cell\">{{ sysFile.typeName }}</div></td>\n                  <td><div class=\"cell\">{{ sysFile.total }}</div></td>\n                  <td><div class=\"cell\">{{ sysFile.free }}</div></td>\n                  <td><div class=\"cell\">{{ sysFile.used }}</div></td>\n                  <td><div class=\"cell\" :class=\"{'text-danger': sysFile.usage > 80}\">{{ sysFile.usage }}%</div></td>\n                </tr>\n              </tbody>\n            </table>\n          </div>\n        </el-card>\n      </el-col>\n    </el-row>\n  </div>\n</template>\n\n<script>\nimport { getServer } from \"@/api/monitor/server\";\n\nexport default {\n  name: \"Server\",\n  data() {\n    return {\n      // 加载层信息\n      loading: [],\n      // 服务器信息\n      server: []\n    };\n  },\n  created() {\n    this.getList();\n    this.openLoading();\n  },\n  methods: {\n    /** 查询服务器信息 */\n    getList() {\n      getServer().then(response => {\n        this.server = response.data;\n        this.loading.close();\n      });\n    },\n    // 打开加载层\n    openLoading() {\n      this.loading = this.$loading({\n        lock: true,\n        text: \"拼命读取中\",\n        spinner: \"el-icon-loading\",\n        background: \"rgba(0, 0, 0, 0.7)\"\n      });\n    }\n  }\n};\n</script>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8KA,SAAAA,SAAA;AAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,MAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;IACA,KAAAC,WAAA;EACA;EACAC,OAAA;IACA,cACAF,OAAA,WAAAA,QAAA;MAAA,IAAAG,KAAA;MACAT,SAAA,GAAAU,IAAA,WAAAC,QAAA;QACAF,KAAA,CAAAL,MAAA,GAAAO,QAAA,CAAAT,IAAA;QACAO,KAAA,CAAAN,OAAA,CAAAS,KAAA;MACA;IACA;IACA;IACAL,WAAA,WAAAA,YAAA;MACA,KAAAJ,OAAA,QAAAU,QAAA;QACAC,IAAA;QACAC,IAAA;QACAC,OAAA;QACAC,UAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}