{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/tool/gen/genInfoForm.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/tool/gen/genInfoForm.vue", "mtime": 1641277326000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBUcmVlc2VsZWN0IGZyb20gIkByaW9waGFlL3Z1ZS10cmVlc2VsZWN0IjsKaW1wb3J0ICJAcmlvcGhhZS92dWUtdHJlZXNlbGVjdC9kaXN0L3Z1ZS10cmVlc2VsZWN0LmNzcyI7CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiQmFzaWNJbmZvRm9ybSIsCiAgY29tcG9uZW50czogewogICAgVHJlZXNlbGVjdDogVHJlZXNlbGVjdAogIH0sCiAgcHJvcHM6IHsKICAgIGluZm86IHsKICAgICAgdHlwZTogT2JqZWN0LAogICAgICBkZWZhdWx0OiBudWxsCiAgICB9LAogICAgdGFibGVzOiB7CiAgICAgIHR5cGU6IEFycmF5LAogICAgICBkZWZhdWx0OiBudWxsCiAgICB9LAogICAgbWVudXM6IHsKICAgICAgdHlwZTogQXJyYXksCiAgICAgIGRlZmF1bHQ6IFtdCiAgICB9CiAgfSwKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgc3ViQ29sdW1uczogW10sCiAgICAgIHJ1bGVzOiB7CiAgICAgICAgdHBsQ2F0ZWdvcnk6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICLor7fpgInmi6nnlJ/miJDmqKHmnb8iLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgfV0sCiAgICAgICAgcGFja2FnZU5hbWU6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICLor7fovpPlhaXnlJ/miJDljIXot6/lvoQiLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgfV0sCiAgICAgICAgbW9kdWxlTmFtZTogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogIuivt+i+k+WFpeeUn+aIkOaooeWdl+WQjSIsCiAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIKICAgICAgICB9XSwKICAgICAgICBidXNpbmVzc05hbWU6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICLor7fovpPlhaXnlJ/miJDkuJrliqHlkI0iLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgfV0sCiAgICAgICAgZnVuY3Rpb25OYW1lOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAi6K+36L6T5YWl55Sf5oiQ5Yqf6IO95ZCNIiwKICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgIH1dCiAgICAgIH0KICAgIH07CiAgfSwKICBjcmVhdGVkOiBmdW5jdGlvbiBjcmVhdGVkKCkge30sCiAgd2F0Y2g6IHsKICAgICdpbmZvLnN1YlRhYmxlTmFtZSc6IGZ1bmN0aW9uIGluZm9TdWJUYWJsZU5hbWUodmFsKSB7CiAgICAgIHRoaXMuc2V0U3ViVGFibGVDb2x1bW5zKHZhbCk7CiAgICB9CiAgfSwKICBtZXRob2RzOiB7CiAgICAvKiog6L2s5o2i6I+c5Y2V5pWw5o2u57uT5p6EICovbm9ybWFsaXplcjogZnVuY3Rpb24gbm9ybWFsaXplcihub2RlKSB7CiAgICAgIGlmIChub2RlLmNoaWxkcmVuICYmICFub2RlLmNoaWxkcmVuLmxlbmd0aCkgewogICAgICAgIGRlbGV0ZSBub2RlLmNoaWxkcmVuOwogICAgICB9CiAgICAgIHJldHVybiB7CiAgICAgICAgaWQ6IG5vZGUubWVudUlkLAogICAgICAgIGxhYmVsOiBub2RlLm1lbnVOYW1lLAogICAgICAgIGNoaWxkcmVuOiBub2RlLmNoaWxkcmVuCiAgICAgIH07CiAgICB9LAogICAgLyoqIOmAieaLqeWtkOihqOWQjeinpuWPkSAqL3N1YlNlbGVjdENoYW5nZTogZnVuY3Rpb24gc3ViU2VsZWN0Q2hhbmdlKHZhbHVlKSB7CiAgICAgIHRoaXMuaW5mby5zdWJUYWJsZUZrTmFtZSA9ICcnOwogICAgfSwKICAgIC8qKiDpgInmi6nnlJ/miJDmqKHmnb/op6blj5EgKi90cGxTZWxlY3RDaGFuZ2U6IGZ1bmN0aW9uIHRwbFNlbGVjdENoYW5nZSh2YWx1ZSkgewogICAgICBpZiAodmFsdWUgIT09ICdzdWInKSB7CiAgICAgICAgdGhpcy5pbmZvLnN1YlRhYmxlTmFtZSA9ICcnOwogICAgICAgIHRoaXMuaW5mby5zdWJUYWJsZUZrTmFtZSA9ICcnOwogICAgICB9CiAgICB9LAogICAgLyoqIOiuvue9ruWFs+iBlOWklumUriAqL3NldFN1YlRhYmxlQ29sdW1uczogZnVuY3Rpb24gc2V0U3ViVGFibGVDb2x1bW5zKHZhbHVlKSB7CiAgICAgIGZvciAodmFyIGl0ZW0gaW4gdGhpcy50YWJsZXMpIHsKICAgICAgICB2YXIgbmFtZSA9IHRoaXMudGFibGVzW2l0ZW1dLnRhYmxlTmFtZTsKICAgICAgICBpZiAodmFsdWUgPT09IG5hbWUpIHsKICAgICAgICAgIHRoaXMuc3ViQ29sdW1ucyA9IHRoaXMudGFibGVzW2l0ZW1dLmNvbHVtbnM7CiAgICAgICAgICBicmVhazsKICAgICAgICB9CiAgICAgIH0KICAgIH0KICB9Cn07"}, {"version": 3, "names": ["Treeselect", "name", "components", "props", "info", "type", "Object", "default", "tables", "Array", "menus", "data", "subColumns", "rules", "tplCategory", "required", "message", "trigger", "packageName", "moduleName", "businessName", "functionName", "created", "watch", "infoSubTableName", "val", "setSubTableColumns", "methods", "normalizer", "node", "children", "length", "id", "menuId", "label", "menuName", "subSelectChange", "value", "subTableFkName", "tplSelectChange", "subTableName", "item", "tableName", "columns"], "sources": ["src/views/tool/gen/genInfoForm.vue"], "sourcesContent": ["<template>\n  <el-form ref=\"genInfoForm\" :model=\"info\" :rules=\"rules\" label-width=\"150px\">\n    <el-row>\n      <el-col :span=\"12\">\n        <el-form-item prop=\"tplCategory\">\n          <span slot=\"label\">生成模板</span>\n          <el-select v-model=\"info.tplCategory\" @change=\"tplSelectChange\">\n            <el-option label=\"单表（增删改查）\" value=\"crud\" />\n            <el-option label=\"树表（增删改查）\" value=\"tree\" />\n            <el-option label=\"主子表（增删改查）\" value=\"sub\" />\n          </el-select>\n        </el-form-item>\n      </el-col>\n\n      <el-col :span=\"12\">\n        <el-form-item prop=\"packageName\">\n          <span slot=\"label\">\n            生成包路径\n            <el-tooltip content=\"生成在哪个java包下，例如 com.ruoyi.system\" placement=\"top\">\n              <i class=\"el-icon-question\"></i>\n            </el-tooltip>\n          </span>\n          <el-input v-model=\"info.packageName\" />\n        </el-form-item>\n      </el-col>\n\n      <el-col :span=\"12\">\n        <el-form-item prop=\"moduleName\">\n          <span slot=\"label\">\n            生成模块名\n            <el-tooltip content=\"可理解为子系统名，例如 system\" placement=\"top\">\n              <i class=\"el-icon-question\"></i>\n            </el-tooltip>\n          </span>\n          <el-input v-model=\"info.moduleName\" />\n        </el-form-item>\n      </el-col>\n\n      <el-col :span=\"12\">\n        <el-form-item prop=\"businessName\">\n          <span slot=\"label\">\n            生成业务名\n            <el-tooltip content=\"可理解为功能英文名，例如 user\" placement=\"top\">\n              <i class=\"el-icon-question\"></i>\n            </el-tooltip>\n          </span>\n          <el-input v-model=\"info.businessName\" />\n        </el-form-item>\n      </el-col>\n\n      <el-col :span=\"12\">\n        <el-form-item prop=\"functionName\">\n          <span slot=\"label\">\n            生成功能名\n            <el-tooltip content=\"用作类描述，例如 用户\" placement=\"top\">\n              <i class=\"el-icon-question\"></i>\n            </el-tooltip>\n          </span>\n          <el-input v-model=\"info.functionName\" />\n        </el-form-item>\n      </el-col>\n\n      <el-col :span=\"12\">\n        <el-form-item>\n          <span slot=\"label\">\n            上级菜单\n            <el-tooltip content=\"分配到指定菜单下，例如 系统管理\" placement=\"top\">\n              <i class=\"el-icon-question\"></i>\n            </el-tooltip>\n          </span>\n          <treeselect\n            :append-to-body=\"true\"\n            v-model=\"info.parentMenuId\"\n            :options=\"menus\"\n            :normalizer=\"normalizer\"\n            :show-count=\"true\"\n            placeholder=\"请选择系统菜单\"\n          />\n        </el-form-item>\n      </el-col>\n\n      <el-col :span=\"12\">\n        <el-form-item prop=\"genType\">\n          <span slot=\"label\">\n            生成代码方式\n            <el-tooltip content=\"默认为zip压缩包下载，也可以自定义生成路径\" placement=\"top\">\n              <i class=\"el-icon-question\"></i>\n            </el-tooltip>\n          </span>\n          <el-radio v-model=\"info.genType\" label=\"0\">zip压缩包</el-radio>\n          <el-radio v-model=\"info.genType\" label=\"1\">自定义路径</el-radio>\n        </el-form-item>\n      </el-col>\n\n      <el-col :span=\"24\" v-if=\"info.genType == '1'\">\n        <el-form-item prop=\"genPath\">\n          <span slot=\"label\">\n            自定义路径\n            <el-tooltip content=\"填写磁盘绝对路径，若不填写，则生成到当前Web项目下\" placement=\"top\">\n              <i class=\"el-icon-question\"></i>\n            </el-tooltip>\n          </span>\n          <el-input v-model=\"info.genPath\">\n            <el-dropdown slot=\"append\">\n              <el-button type=\"primary\">\n                最近路径快速选择\n                <i class=\"el-icon-arrow-down el-icon--right\"></i>\n              </el-button>\n              <el-dropdown-menu slot=\"dropdown\">\n                <el-dropdown-item @click.native=\"info.genPath = '/'\">恢复默认的生成基础路径</el-dropdown-item>\n              </el-dropdown-menu>\n            </el-dropdown>\n          </el-input>\n        </el-form-item>\n      </el-col>\n    </el-row>\n\n    <el-row v-show=\"info.tplCategory == 'tree'\">\n      <h4 class=\"form-header\">其他信息</h4>\n      <el-col :span=\"12\">\n        <el-form-item>\n          <span slot=\"label\">\n            树编码字段\n            <el-tooltip content=\"树显示的编码字段名， 如：dept_id\" placement=\"top\">\n              <i class=\"el-icon-question\"></i>\n            </el-tooltip>\n          </span>\n          <el-select v-model=\"info.treeCode\" placeholder=\"请选择\">\n            <el-option\n              v-for=\"(column, index) in info.columns\"\n              :key=\"index\"\n              :label=\"column.columnName + '：' + column.columnComment\"\n              :value=\"column.columnName\"\n            ></el-option>\n          </el-select>\n        </el-form-item>\n      </el-col>\n      <el-col :span=\"12\">\n        <el-form-item>\n          <span slot=\"label\">\n            树父编码字段\n            <el-tooltip content=\"树显示的父编码字段名， 如：parent_Id\" placement=\"top\">\n              <i class=\"el-icon-question\"></i>\n            </el-tooltip>\n          </span>\n          <el-select v-model=\"info.treeParentCode\" placeholder=\"请选择\">\n            <el-option\n              v-for=\"(column, index) in info.columns\"\n              :key=\"index\"\n              :label=\"column.columnName + '：' + column.columnComment\"\n              :value=\"column.columnName\"\n            ></el-option>\n          </el-select>\n        </el-form-item>\n      </el-col>\n      <el-col :span=\"12\">\n        <el-form-item>\n          <span slot=\"label\">\n            树名称字段\n            <el-tooltip content=\"树节点的显示名称字段名， 如：dept_name\" placement=\"top\">\n              <i class=\"el-icon-question\"></i>\n            </el-tooltip>\n          </span>\n          <el-select v-model=\"info.treeName\" placeholder=\"请选择\">\n            <el-option\n              v-for=\"(column, index) in info.columns\"\n              :key=\"index\"\n              :label=\"column.columnName + '：' + column.columnComment\"\n              :value=\"column.columnName\"\n            ></el-option>\n          </el-select>\n        </el-form-item>\n      </el-col>\n    </el-row>\n    <el-row v-show=\"info.tplCategory == 'sub'\">\n      <h4 class=\"form-header\">关联信息</h4>\n      <el-col :span=\"12\">\n        <el-form-item>\n          <span slot=\"label\">\n            关联子表的表名\n            <el-tooltip content=\"关联子表的表名， 如：sys_user\" placement=\"top\">\n              <i class=\"el-icon-question\"></i>\n            </el-tooltip>\n          </span>\n          <el-select v-model=\"info.subTableName\" placeholder=\"请选择\" @change=\"subSelectChange\">\n            <el-option\n              v-for=\"(table, index) in tables\"\n              :key=\"index\"\n              :label=\"table.tableName + '：' + table.tableComment\"\n              :value=\"table.tableName\"\n            ></el-option>\n          </el-select>\n        </el-form-item>\n      </el-col>\n      <el-col :span=\"12\">\n        <el-form-item>\n          <span slot=\"label\">\n            子表关联的外键名\n            <el-tooltip content=\"子表关联的外键名， 如：user_id\" placement=\"top\">\n              <i class=\"el-icon-question\"></i>\n            </el-tooltip>\n          </span>\n          <el-select v-model=\"info.subTableFkName\" placeholder=\"请选择\">\n            <el-option\n              v-for=\"(column, index) in subColumns\"\n              :key=\"index\"\n              :label=\"column.columnName + '：' + column.columnComment\"\n              :value=\"column.columnName\"\n            ></el-option>\n          </el-select>\n        </el-form-item>\n      </el-col>\n    </el-row>\n  </el-form>\n</template>\n<script>\nimport Treeselect from \"@riophae/vue-treeselect\";\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\";\n\nexport default {\n  name: \"BasicInfoForm\",\n  components: { Treeselect },\n  props: {\n    info: {\n      type: Object,\n      default: null\n    },\n    tables: {\n      type: Array,\n      default: null\n    },\n    menus: {\n      type: Array,\n      default: []\n    },\n  },\n  data() {\n    return {\n      subColumns: [],\n      rules: {\n        tplCategory: [\n          { required: true, message: \"请选择生成模板\", trigger: \"blur\" }\n        ],\n        packageName: [\n          { required: true, message: \"请输入生成包路径\", trigger: \"blur\" }\n        ],\n        moduleName: [\n          { required: true, message: \"请输入生成模块名\", trigger: \"blur\" }\n        ],\n        businessName: [\n          { required: true, message: \"请输入生成业务名\", trigger: \"blur\" }\n        ],\n        functionName: [\n          { required: true, message: \"请输入生成功能名\", trigger: \"blur\" }\n        ],\n      }\n    };\n  },\n  created() {},\n  watch: {\n    'info.subTableName': function(val) {\n      this.setSubTableColumns(val);\n    }\n  },\n  methods: {\n    /** 转换菜单数据结构 */\n    normalizer(node) {\n      if (node.children && !node.children.length) {\n        delete node.children;\n      }\n      return {\n        id: node.menuId,\n        label: node.menuName,\n        children: node.children\n      };\n    },\n    /** 选择子表名触发 */\n    subSelectChange(value) {\n      this.info.subTableFkName = '';\n    },\n    /** 选择生成模板触发 */\n    tplSelectChange(value) {\n      if(value !== 'sub') {\n        this.info.subTableName = '';\n        this.info.subTableFkName = '';\n      }\n    },\n    /** 设置关联外键 */\n    setSubTableColumns(value) {\n      for (var item in this.tables) {\n        const name = this.tables[item].tableName;\n        if (value === name) {\n          this.subColumns = this.tables[item].columns;\n          break;\n        }\n      }\n    }\n  }\n};\n</script>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwNA,OAAAA,UAAA;AACA;AAEA;EACAC,IAAA;EACAC,UAAA;IAAAF,UAAA,EAAAA;EAAA;EACAG,KAAA;IACAC,IAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAC,MAAA;MACAH,IAAA,EAAAI,KAAA;MACAF,OAAA;IACA;IACAG,KAAA;MACAL,IAAA,EAAAI,KAAA;MACAF,OAAA;IACA;EACA;EACAI,IAAA,WAAAA,KAAA;IACA;MACAC,UAAA;MACAC,KAAA;QACAC,WAAA,GACA;UAAAC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAC,WAAA,GACA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAE,UAAA,GACA;UAAAJ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAG,YAAA,GACA;UAAAL,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAI,YAAA,GACA;UAAAN,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAK,OAAA,WAAAA,QAAA;EACAC,KAAA;IACA,8BAAAC,iBAAAC,GAAA;MACA,KAAAC,kBAAA,CAAAD,GAAA;IACA;EACA;EACAE,OAAA;IACA,eACAC,UAAA,WAAAA,WAAAC,IAAA;MACA,IAAAA,IAAA,CAAAC,QAAA,KAAAD,IAAA,CAAAC,QAAA,CAAAC,MAAA;QACA,OAAAF,IAAA,CAAAC,QAAA;MACA;MACA;QACAE,EAAA,EAAAH,IAAA,CAAAI,MAAA;QACAC,KAAA,EAAAL,IAAA,CAAAM,QAAA;QACAL,QAAA,EAAAD,IAAA,CAAAC;MACA;IACA;IACA,cACAM,eAAA,WAAAA,gBAAAC,KAAA;MACA,KAAAjC,IAAA,CAAAkC,cAAA;IACA;IACA,eACAC,eAAA,WAAAA,gBAAAF,KAAA;MACA,IAAAA,KAAA;QACA,KAAAjC,IAAA,CAAAoC,YAAA;QACA,KAAApC,IAAA,CAAAkC,cAAA;MACA;IACA;IACA,aACAZ,kBAAA,WAAAA,mBAAAW,KAAA;MACA,SAAAI,IAAA,SAAAjC,MAAA;QACA,IAAAP,IAAA,QAAAO,MAAA,CAAAiC,IAAA,EAAAC,SAAA;QACA,IAAAL,KAAA,KAAApC,IAAA;UACA,KAAAW,UAAA,QAAAJ,MAAA,CAAAiC,IAAA,EAAAE,OAAA;UACA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}