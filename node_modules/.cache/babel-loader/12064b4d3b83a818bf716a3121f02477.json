{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/system/user/profile/userAvatar.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/system/user/profile/userAvatar.vue", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBzdG9yZSBmcm9tICJAL3N0b3JlIjsKaW1wb3J0IHsgVnVlQ3JvcHBlciB9IGZyb20gInZ1ZS1jcm9wcGVyIjsKaW1wb3J0IHsgdXBsb2FkQXZhdGFyIH0gZnJvbSAiQC9hcGkvc3lzdGVtL3VzZXIiOwpleHBvcnQgZGVmYXVsdCB7CiAgY29tcG9uZW50czogewogICAgVnVlQ3JvcHBlcjogVnVlQ3JvcHBlcgogIH0sCiAgcHJvcHM6IHsKICAgIHVzZXI6IHsKICAgICAgdHlwZTogT2JqZWN0CiAgICB9CiAgfSwKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGCCiAgICAgIG9wZW46IGZhbHNlLAogICAgICAvLyDmmK/lkKbmmL7npLpjcm9wcGVyCiAgICAgIHZpc2libGU6IGZhbHNlLAogICAgICAvLyDlvLnlh7rlsYLmoIfpopgKICAgICAgdGl0bGU6ICLkv67mlLnlpLTlg48iLAogICAgICBvcHRpb25zOiB7CiAgICAgICAgaW1nOiBzdG9yZS5nZXR0ZXJzLmF2YXRhciwKICAgICAgICAvL+ijgeWJquWbvueJh+eahOWcsOWdgAogICAgICAgIGF1dG9Dcm9wOiB0cnVlLAogICAgICAgIC8vIOaYr+WQpum7mOiupOeUn+aIkOaIquWbvuahhgogICAgICAgIGF1dG9Dcm9wV2lkdGg6IDIwMCwKICAgICAgICAvLyDpu5jorqTnlJ/miJDmiKrlm77moYblrr3luqYKICAgICAgICBhdXRvQ3JvcEhlaWdodDogMjAwLAogICAgICAgIC8vIOm7mOiupOeUn+aIkOaIquWbvuahhumrmOW6pgogICAgICAgIGZpeGVkQm94OiB0cnVlIC8vIOWbuuWumuaIquWbvuahhuWkp+WwjyDkuI3lhYHorrjmlLnlj5gKICAgICAgfSwKICAgICAgcHJldmlld3M6IHt9CiAgICB9OwogIH0sCiAgbWV0aG9kczogewogICAgLy8g57yW6L6R5aS05YOPCiAgICBlZGl0Q3JvcHBlcjogZnVuY3Rpb24gZWRpdENyb3BwZXIoKSB7CiAgICAgIHRoaXMub3BlbiA9IHRydWU7CiAgICB9LAogICAgLy8g5omT5byA5by55Ye65bGC57uT5p2f5pe255qE5Zue6LCDCiAgICBtb2RhbE9wZW5lZDogZnVuY3Rpb24gbW9kYWxPcGVuZWQoKSB7CiAgICAgIHRoaXMudmlzaWJsZSA9IHRydWU7CiAgICB9LAogICAgLy8g6KaG55uW6buY6K6k55qE5LiK5Lyg6KGM5Li6CiAgICByZXF1ZXN0VXBsb2FkOiBmdW5jdGlvbiByZXF1ZXN0VXBsb2FkKCkge30sCiAgICAvLyDlkJHlt6bml4vovawKICAgIHJvdGF0ZUxlZnQ6IGZ1bmN0aW9uIHJvdGF0ZUxlZnQoKSB7CiAgICAgIHRoaXMuJHJlZnMuY3JvcHBlci5yb3RhdGVMZWZ0KCk7CiAgICB9LAogICAgLy8g5ZCR5Y+z5peL6L2sCiAgICByb3RhdGVSaWdodDogZnVuY3Rpb24gcm90YXRlUmlnaHQoKSB7CiAgICAgIHRoaXMuJHJlZnMuY3JvcHBlci5yb3RhdGVSaWdodCgpOwogICAgfSwKICAgIC8vIOWbvueJh+e8qeaUvgogICAgY2hhbmdlU2NhbGU6IGZ1bmN0aW9uIGNoYW5nZVNjYWxlKG51bSkgewogICAgICBudW0gPSBudW0gfHwgMTsKICAgICAgdGhpcy4kcmVmcy5jcm9wcGVyLmNoYW5nZVNjYWxlKG51bSk7CiAgICB9LAogICAgLy8g5LiK5Lyg6aKE5aSE55CGCiAgICBiZWZvcmVVcGxvYWQ6IGZ1bmN0aW9uIGJlZm9yZVVwbG9hZChmaWxlKSB7CiAgICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICAgIGlmIChmaWxlLnR5cGUuaW5kZXhPZigiaW1hZ2UvIikgPT0gLTEpIHsKICAgICAgICB0aGlzLm1zZ0Vycm9yKCLmlofku7bmoLzlvI/plJnor6/vvIzor7fkuIrkvKDlm77niYfnsbvlnoss5aaC77yaSlBH77yMUE5H5ZCO57yA55qE5paH5Lu244CCIik7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdmFyIHJlYWRlciA9IG5ldyBGaWxlUmVhZGVyKCk7CiAgICAgICAgcmVhZGVyLnJlYWRBc0RhdGFVUkwoZmlsZSk7CiAgICAgICAgcmVhZGVyLm9ubG9hZCA9IGZ1bmN0aW9uICgpIHsKICAgICAgICAgIF90aGlzLm9wdGlvbnMuaW1nID0gcmVhZGVyLnJlc3VsdDsKICAgICAgICB9OwogICAgICB9CiAgICB9LAogICAgLy8g5LiK5Lyg5Zu+54mHCiAgICB1cGxvYWRJbWc6IGZ1bmN0aW9uIHVwbG9hZEltZygpIHsKICAgICAgdmFyIF90aGlzMiA9IHRoaXM7CiAgICAgIHRoaXMuJHJlZnMuY3JvcHBlci5nZXRDcm9wQmxvYihmdW5jdGlvbiAoZGF0YSkgewogICAgICAgIHZhciBmb3JtRGF0YSA9IG5ldyBGb3JtRGF0YSgpOwogICAgICAgIGZvcm1EYXRhLmFwcGVuZCgiYXZhdGFyZmlsZSIsIGRhdGEpOwogICAgICAgIHVwbG9hZEF2YXRhcihmb3JtRGF0YSkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICAgIF90aGlzMi5vcGVuID0gZmFsc2U7CiAgICAgICAgICBfdGhpczIub3B0aW9ucy5pbWcgPSBwcm9jZXNzLmVudi5WVUVfQVBQX0JBU0VfQVBJICsgcmVzcG9uc2UuaW1nVXJsOwogICAgICAgICAgc3RvcmUuY29tbWl0KCdTRVRfQVZBVEFSJywgX3RoaXMyLm9wdGlvbnMuaW1nKTsKICAgICAgICAgIF90aGlzMi5tc2dTdWNjZXNzKCLkv67mlLnmiJDlip8iKTsKICAgICAgICAgIF90aGlzMi52aXNpYmxlID0gZmFsc2U7CiAgICAgICAgfSk7CiAgICAgIH0pOwogICAgfSwKICAgIC8vIOWunuaXtumihOiniAogICAgcmVhbFRpbWU6IGZ1bmN0aW9uIHJlYWxUaW1lKGRhdGEpIHsKICAgICAgdGhpcy5wcmV2aWV3cyA9IGRhdGE7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["store", "VueCropper", "uploadAvatar", "components", "props", "user", "type", "Object", "data", "open", "visible", "title", "options", "img", "getters", "avatar", "autoCrop", "autoCropWidth", "autoCropHeight", "fixedBox", "previews", "methods", "editCropper", "modalOpened", "requestUpload", "rotateLeft", "$refs", "cropper", "rotateRight", "changeScale", "num", "beforeUpload", "file", "_this", "indexOf", "msgError", "reader", "FileReader", "readAsDataURL", "onload", "result", "uploadImg", "_this2", "getCropBlob", "formData", "FormData", "append", "then", "response", "process", "env", "VUE_APP_BASE_API", "imgUrl", "commit", "msgSuccess", "realTime"], "sources": ["src/views/system/user/profile/userAvatar.vue"], "sourcesContent": ["<template>\n  <div>\n    <div class=\"user-info-head\" @click=\"editCropper()\"><img v-bind:src=\"options.img\" title=\"点击上传头像\" class=\"img-circle img-lg\" /></div>\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"800px\" append-to-body @opened=\"modalOpened\">\n      <el-row>\n        <el-col :xs=\"24\" :md=\"12\" :style=\"{height: '350px'}\">\n          <vue-cropper\n            ref=\"cropper\"\n            :img=\"options.img\"\n            :info=\"true\"\n            :autoCrop=\"options.autoCrop\"\n            :autoCropWidth=\"options.autoCropWidth\"\n            :autoCropHeight=\"options.autoCropHeight\"\n            :fixedBox=\"options.fixedBox\"\n            @realTime=\"realTime\"\n            v-if=\"visible\"\n          />\n        </el-col>\n        <el-col :xs=\"24\" :md=\"12\" :style=\"{height: '350px'}\">\n          <div class=\"avatar-upload-preview\">\n            <img :src=\"previews.url\" :style=\"previews.img\" />\n          </div>\n        </el-col>\n      </el-row>\n      <br />\n      <el-row>\n        <el-col :lg=\"2\" :md=\"2\">\n          <el-upload action=\"#\" :http-request=\"requestUpload\" :show-file-list=\"false\" :before-upload=\"beforeUpload\">\n            <el-button size=\"small\">\n              选择\n              <i class=\"el-icon-upload el-icon--right\"></i>\n            </el-button>\n          </el-upload>\n        </el-col>\n        <el-col :lg=\"{span: 1, offset: 2}\" :md=\"2\">\n          <el-button icon=\"el-icon-plus\" size=\"small\" @click=\"changeScale(1)\"></el-button>\n        </el-col>\n        <el-col :lg=\"{span: 1, offset: 1}\" :md=\"2\">\n          <el-button icon=\"el-icon-minus\" size=\"small\" @click=\"changeScale(-1)\"></el-button>\n        </el-col>\n        <el-col :lg=\"{span: 1, offset: 1}\" :md=\"2\">\n          <el-button icon=\"el-icon-refresh-left\" size=\"small\" @click=\"rotateLeft()\"></el-button>\n        </el-col>\n        <el-col :lg=\"{span: 1, offset: 1}\" :md=\"2\">\n          <el-button icon=\"el-icon-refresh-right\" size=\"small\" @click=\"rotateRight()\"></el-button>\n        </el-col>\n        <el-col :lg=\"{span: 2, offset: 6}\" :md=\"2\">\n          <el-button type=\"primary\" size=\"small\" @click=\"uploadImg()\">提 交</el-button>\n        </el-col>\n      </el-row>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport store from \"@/store\";\nimport { VueCropper } from \"vue-cropper\";\nimport { uploadAvatar } from \"@/api/system/user\";\n\nexport default {\n  components: { VueCropper },\n  props: {\n    user: {\n      type: Object\n    }\n  },\n  data() {\n    return {\n      // 是否显示弹出层\n      open: false,\n      // 是否显示cropper\n      visible: false,\n      // 弹出层标题\n      title: \"修改头像\",\n      options: {\n        img: store.getters.avatar, //裁剪图片的地址\n        autoCrop: true, // 是否默认生成截图框\n        autoCropWidth: 200, // 默认生成截图框宽度\n        autoCropHeight: 200, // 默认生成截图框高度\n        fixedBox: true // 固定截图框大小 不允许改变\n      },\n      previews: {}\n    };\n  },\n  methods: {\n    // 编辑头像\n    editCropper() {\n      this.open = true;\n    },\n    // 打开弹出层结束时的回调\n    modalOpened() {\n      this.visible = true;\n    },\n    // 覆盖默认的上传行为\n    requestUpload() {\n    },\n    // 向左旋转\n    rotateLeft() {\n      this.$refs.cropper.rotateLeft();\n    },\n    // 向右旋转\n    rotateRight() {\n      this.$refs.cropper.rotateRight();\n    },\n    // 图片缩放\n    changeScale(num) {\n      num = num || 1;\n      this.$refs.cropper.changeScale(num);\n    },\n    // 上传预处理\n    beforeUpload(file) {\n      if (file.type.indexOf(\"image/\") == -1) {\n        this.msgError(\"文件格式错误，请上传图片类型,如：JPG，PNG后缀的文件。\");\n      } else {\n        const reader = new FileReader();\n        reader.readAsDataURL(file);\n        reader.onload = () => {\n          this.options.img = reader.result;\n        };\n      }\n    },\n    // 上传图片\n    uploadImg() {\n      this.$refs.cropper.getCropBlob(data => {\n        let formData = new FormData();\n        formData.append(\"avatarfile\", data);\n        uploadAvatar(formData).then(response => {\n          this.open = false;\n          this.options.img = process.env.VUE_APP_BASE_API + response.imgUrl;\n          store.commit('SET_AVATAR', this.options.img);\n          this.msgSuccess(\"修改成功\");\n          this.visible = false;\n        });\n      });\n    },\n    // 实时预览\n    realTime(data) {\n      this.previews = data;\n    }\n  }\n};\n</script>\n<style scoped lang=\"scss\">\n.user-info-head {\n  position: relative;\n  display: inline-block;\n  height: 120px;\n}\n\n.user-info-head:hover:after {\n  content: '+';\n  position: absolute;\n  left: 0;\n  right: 0;\n  top: 0;\n  bottom: 0;\n  color: #eee;\n  background: rgba(0, 0, 0, 0.5);\n  font-size: 24px;\n  font-style: normal;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  cursor: pointer;\n  line-height: 110px;\n  border-radius: 50%;\n}\n</style>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuDA,OAAAA,KAAA;AACA,SAAAC,UAAA;AACA,SAAAC,YAAA;AAEA;EACAC,UAAA;IAAAF,UAAA,EAAAA;EAAA;EACAG,KAAA;IACAC,IAAA;MACAC,IAAA,EAAAC;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,IAAA;MACA;MACAC,OAAA;MACA;MACAC,KAAA;MACAC,OAAA;QACAC,GAAA,EAAAb,KAAA,CAAAc,OAAA,CAAAC,MAAA;QAAA;QACAC,QAAA;QAAA;QACAC,aAAA;QAAA;QACAC,cAAA;QAAA;QACAC,QAAA;MACA;MACAC,QAAA;IACA;EACA;EACAC,OAAA;IACA;IACAC,WAAA,WAAAA,YAAA;MACA,KAAAb,IAAA;IACA;IACA;IACAc,WAAA,WAAAA,YAAA;MACA,KAAAb,OAAA;IACA;IACA;IACAc,aAAA,WAAAA,cAAA,GACA;IACA;IACAC,UAAA,WAAAA,WAAA;MACA,KAAAC,KAAA,CAAAC,OAAA,CAAAF,UAAA;IACA;IACA;IACAG,WAAA,WAAAA,YAAA;MACA,KAAAF,KAAA,CAAAC,OAAA,CAAAC,WAAA;IACA;IACA;IACAC,WAAA,WAAAA,YAAAC,GAAA;MACAA,GAAA,GAAAA,GAAA;MACA,KAAAJ,KAAA,CAAAC,OAAA,CAAAE,WAAA,CAAAC,GAAA;IACA;IACA;IACAC,YAAA,WAAAA,aAAAC,IAAA;MAAA,IAAAC,KAAA;MACA,IAAAD,IAAA,CAAA1B,IAAA,CAAA4B,OAAA;QACA,KAAAC,QAAA;MACA;QACA,IAAAC,MAAA,OAAAC,UAAA;QACAD,MAAA,CAAAE,aAAA,CAAAN,IAAA;QACAI,MAAA,CAAAG,MAAA;UACAN,KAAA,CAAArB,OAAA,CAAAC,GAAA,GAAAuB,MAAA,CAAAI,MAAA;QACA;MACA;IACA;IACA;IACAC,SAAA,WAAAA,UAAA;MAAA,IAAAC,MAAA;MACA,KAAAhB,KAAA,CAAAC,OAAA,CAAAgB,WAAA,WAAAnC,IAAA;QACA,IAAAoC,QAAA,OAAAC,QAAA;QACAD,QAAA,CAAAE,MAAA,eAAAtC,IAAA;QACAN,YAAA,CAAA0C,QAAA,EAAAG,IAAA,WAAAC,QAAA;UACAN,MAAA,CAAAjC,IAAA;UACAiC,MAAA,CAAA9B,OAAA,CAAAC,GAAA,GAAAoC,OAAA,CAAAC,GAAA,CAAAC,gBAAA,GAAAH,QAAA,CAAAI,MAAA;UACApD,KAAA,CAAAqD,MAAA,eAAAX,MAAA,CAAA9B,OAAA,CAAAC,GAAA;UACA6B,MAAA,CAAAY,UAAA;UACAZ,MAAA,CAAAhC,OAAA;QACA;MACA;IACA;IACA;IACA6C,QAAA,WAAAA,SAAA/C,IAAA;MACA,KAAAY,QAAA,GAAAZ,IAAA;IACA;EACA;AACA", "ignoreList": []}]}