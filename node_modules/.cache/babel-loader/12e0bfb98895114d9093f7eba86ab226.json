{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/api/system/dept.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/api/system/dept.js", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QgZnJvbSAnQC91dGlscy9yZXF1ZXN0JzsKCi8vIOafpeivoumDqOmXqOWIl+ihqApleHBvcnQgZnVuY3Rpb24gbGlzdERlcHQocXVlcnkpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvc3lzdGVtL2RlcHQvbGlzdCcsCiAgICBtZXRob2Q6ICdnZXQnLAogICAgcGFyYW1zOiBxdWVyeQogIH0pOwp9CgovLyDmn6Xor6Lpg6jpl6jliJfooajvvIjmjpLpmaToioLngrnvvIkKZXhwb3J0IGZ1bmN0aW9uIGxpc3REZXB0RXhjbHVkZUNoaWxkKGRlcHRJZCkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9zeXN0ZW0vZGVwdC9saXN0L2V4Y2x1ZGUvJyArIGRlcHRJZCwKICAgIG1ldGhvZDogJ2dldCcKICB9KTsKfQoKLy8g5p+l6K+i6YOo6Zeo6K+m57uGCmV4cG9ydCBmdW5jdGlvbiBnZXREZXB0KGRlcHRJZCkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9zeXN0ZW0vZGVwdC8nICsgZGVwdElkLAogICAgbWV0aG9kOiAnZ2V0JwogIH0pOwp9CgovLyDmn6Xor6Lpg6jpl6jkuIvmi4nmoJHnu5PmnoQKZXhwb3J0IGZ1bmN0aW9uIHRyZWVzZWxlY3QoKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL3N5c3RlbS9kZXB0L3RyZWVzZWxlY3QnLAogICAgbWV0aG9kOiAnZ2V0JwogIH0pOwp9CgovLyDmoLnmja7op5LoibJJROafpeivoumDqOmXqOagkee7k+aehApleHBvcnQgZnVuY3Rpb24gcm9sZURlcHRUcmVlc2VsZWN0KHJvbGVJZCkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9zeXN0ZW0vZGVwdC9yb2xlRGVwdFRyZWVzZWxlY3QvJyArIHJvbGVJZCwKICAgIG1ldGhvZDogJ2dldCcKICB9KTsKfQoKLy8g5paw5aKe6YOo6ZeoCmV4cG9ydCBmdW5jdGlvbiBhZGREZXB0KGRhdGEpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvc3lzdGVtL2RlcHQnLAogICAgbWV0aG9kOiAncG9zdCcsCiAgICBkYXRhOiBkYXRhCiAgfSk7Cn0KCi8vIOS/ruaUuemDqOmXqApleHBvcnQgZnVuY3Rpb24gdXBkYXRlRGVwdChkYXRhKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL3N5c3RlbS9kZXB0JywKICAgIG1ldGhvZDogJ3B1dCcsCiAgICBkYXRhOiBkYXRhCiAgfSk7Cn0KCi8vIOWIoOmZpOmDqOmXqApleHBvcnQgZnVuY3Rpb24gZGVsRGVwdChkZXB0SWQpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvc3lzdGVtL2RlcHQvJyArIGRlcHRJZCwKICAgIG1ldGhvZDogJ2RlbGV0ZScKICB9KTsKfQ=="}, {"version": 3, "names": ["request", "listDept", "query", "url", "method", "params", "listDept<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deptId", "getDept", "treeselect", "roleDeptTreeselect", "roleId", "addDept", "data", "updateDept", "delDept"], "sources": ["/Users/<USER>/develop/code/project/haijia/adminweb/src/api/system/dept.js"], "sourcesContent": ["import request from '@/utils/request'\n\n// 查询部门列表\nexport function listDept(query) {\n  return request({\n    url: '/system/dept/list',\n    method: 'get',\n    params: query\n  })\n}\n\n// 查询部门列表（排除节点）\nexport function listDeptExcludeChild(deptId) {\n  return request({\n    url: '/system/dept/list/exclude/' + deptId,\n    method: 'get'\n  })\n}\n\n// 查询部门详细\nexport function getDept(deptId) {\n  return request({\n    url: '/system/dept/' + deptId,\n    method: 'get'\n  })\n}\n\n// 查询部门下拉树结构\nexport function treeselect() {\n  return request({\n    url: '/system/dept/treeselect',\n    method: 'get'\n  })\n}\n\n// 根据角色ID查询部门树结构\nexport function roleDeptTreeselect(roleId) {\n  return request({\n    url: '/system/dept/roleDeptTreeselect/' + roleId,\n    method: 'get'\n  })\n}\n\n// 新增部门\nexport function addDept(data) {\n  return request({\n    url: '/system/dept',\n    method: 'post',\n    data: data\n  })\n}\n\n// 修改部门\nexport function updateDept(data) {\n  return request({\n    url: '/system/dept',\n    method: 'put',\n    data: data\n  })\n}\n\n// 删除部门\nexport function delDept(deptId) {\n  return request({\n    url: '/system/dept/' + deptId,\n    method: 'delete'\n  })\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;;AAErC;AACA,OAAO,SAASC,QAAQA,CAACC,KAAK,EAAE;EAC9B,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEH;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASI,oBAAoBA,CAACC,MAAM,EAAE;EAC3C,OAAOP,OAAO,CAAC;IACbG,GAAG,EAAE,4BAA4B,GAAGI,MAAM;IAC1CH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASI,OAAOA,CAACD,MAAM,EAAE;EAC9B,OAAOP,OAAO,CAAC;IACbG,GAAG,EAAE,eAAe,GAAGI,MAAM;IAC7BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASK,UAAUA,CAAA,EAAG;EAC3B,OAAOT,OAAO,CAAC;IACbG,GAAG,EAAE,yBAAyB;IAC9BC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASM,kBAAkBA,CAACC,MAAM,EAAE;EACzC,OAAOX,OAAO,CAAC;IACbG,GAAG,EAAE,kCAAkC,GAAGQ,MAAM;IAChDP,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASQ,OAAOA,CAACC,IAAI,EAAE;EAC5B,OAAOb,OAAO,CAAC;IACbG,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,MAAM;IACdS,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASC,UAAUA,CAACD,IAAI,EAAE;EAC/B,OAAOb,OAAO,CAAC;IACbG,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,KAAK;IACbS,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASE,OAAOA,CAACR,MAAM,EAAE;EAC9B,OAAOP,OAAO,CAAC;IACbG,GAAG,EAAE,eAAe,GAAGI,MAAM;IAC7BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}