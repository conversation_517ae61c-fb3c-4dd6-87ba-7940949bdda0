{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/api/flowable/finished.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/api/flowable/finished.js", "mtime": 1664027226000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js", "mtime": 1751171659043}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QgZnJvbSAnQC91dGlscy9yZXF1ZXN0JzsKaW1wb3J0IGRhIGZyb20gImVsZW1lbnQtdWkvc3JjL2xvY2FsZS9sYW5nL2RhIjsKCi8vIOafpeivouW3suWKnuS7u+W<PERSON>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"}, null]}