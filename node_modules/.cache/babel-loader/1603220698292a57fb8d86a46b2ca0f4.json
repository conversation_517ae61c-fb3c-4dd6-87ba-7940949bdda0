{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/utils/db.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/utils/db.js", "mtime": 1641277326000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IGRlIGZyb20gImVsZW1lbnQtdWkvc3JjL2xvY2FsZS9sYW5nL2RlIjsKdmFyIERSQVdJTkdfSVRFTVMgPSAnZHJhd2luZ0l0ZW1zJzsKdmFyIERSQVdJTkdfSVRFTVNfVkVSU0lPTiA9ICcxLjInOwp2YXIgRFJBV0lOR19JVEVNU19WRVJTSU9OX0tFWSA9ICdEUkFXSU5HX0lURU1TX1ZFUlNJT04nOwp2YXIgRFJBV0lOR19JRCA9ICdpZEdsb2JhbCc7CnZhciBUUkVFX05PREVfSUQgPSAndHJlZU5vZGVJZCc7CnZhciBGT1JNX0NPTkYgPSAnZm9ybUNvbmYnOwpleHBvcnQgZnVuY3Rpb24gZ2V0RHJhd2luZ0xpc3QoKSB7CiAgLy8g5Yqg5YWl57yT5a2Y54mI5pys55qE5qaC5b+177yM5L+d6K+B57yT5a2Y5pWw5o2u5LiO56iL5bqP5Yy56YWNCiAgdmFyIHZlcnNpb24gPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbShEUkFXSU5HX0lURU1TX1ZFUlNJT05fS0VZKTsKICBpZiAodmVyc2lvbiAhPT0gRFJBV0lOR19JVEVNU19WRVJTSU9OKSB7CiAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbShEUkFXSU5HX0lURU1TX1ZFUlNJT05fS0VZLCBEUkFXSU5HX0lURU1TX1ZFUlNJT04pOwogICAgc2F2ZURyYXdpbmdMaXN0KFtdKTsKICAgIHJldHVybiBudWxsOwogIH0KICB2YXIgc3RyID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oRFJBV0lOR19JVEVNUyk7CiAgaWYgKHN0cikgcmV0dXJuIEpTT04ucGFyc2Uoc3RyKTsKICByZXR1cm4gbnVsbDsKfQpleHBvcnQgZnVuY3Rpb24gc2F2ZURyYXdpbmdMaXN0KGxpc3QpIHsKICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbShEUkFXSU5HX0lURU1TLCBKU09OLnN0cmluZ2lmeShsaXN0KSk7Cn0KZXhwb3J0IGZ1bmN0aW9uIGdldElkR2xvYmFsKCkgewogIHZhciBzdHIgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbShEUkFXSU5HX0lEKTsKICBpZiAoc3RyKSByZXR1cm4gcGFyc2VJbnQoc3RyLCAxMCk7CiAgcmV0dXJuIDEwMDsKfQpleHBvcnQgZnVuY3Rpb24gc2F2ZUlkR2xvYmFsKGlkKSB7CiAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oRFJBV0lOR19JRCwgIiIuY29uY2F0KGlkKSk7Cn0KZXhwb3J0IGZ1bmN0aW9uIGdldFRyZWVOb2RlSWQoKSB7CiAgdmFyIHN0ciA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKFRSRUVfTk9ERV9JRCk7CiAgaWYgKHN0cikgcmV0dXJuIHBhcnNlSW50KHN0ciwgMTApOwogIHJldHVybiAxMDA7Cn0KZXhwb3J0IGZ1bmN0aW9uIHNhdmVUcmVlTm9kZUlkKGlkKSB7CiAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oVFJFRV9OT0RFX0lELCAiIi5jb25jYXQoaWQpKTsKfQpleHBvcnQgZnVuY3Rpb24gZ2V0Rm9ybUNvbmYoKSB7CiAgdmFyIHN0ciA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKEZPUk1fQ09ORik7CiAgaWYgKHN0cikgcmV0dXJuIEpTT04ucGFyc2Uoc3RyKTsKICByZXR1cm4gbnVsbDsKfQpleHBvcnQgZnVuY3Rpb24gc2F2ZUZvcm1Db25mKG9iaikgewogIGxvY2FsU3RvcmFnZS5zZXRJdGVtKEZPUk1fQ09ORiwgSlNPTi5zdHJpbmdpZnkob2JqKSk7Cn0="}, {"version": 3, "names": ["de", "DRAWING_ITEMS", "DRAWING_ITEMS_VERSION", "DRAWING_ITEMS_VERSION_KEY", "DRAWING_ID", "TREE_NODE_ID", "FORM_CONF", "getDrawingList", "version", "localStorage", "getItem", "setItem", "saveDrawingList", "str", "JSON", "parse", "list", "stringify", "getIdGlobal", "parseInt", "saveIdGlobal", "id", "concat", "getTreeNodeId", "saveTreeNodeId", "getFormConf", "saveFormConf", "obj"], "sources": ["/Users/<USER>/develop/code/project/haijia/adminweb/src/utils/db.js"], "sourcesContent": ["import de from \"element-ui/src/locale/lang/de\";\n\nconst DRAWING_ITEMS = 'drawingItems'\nconst DRAWING_ITEMS_VERSION = '1.2'\nconst DRAWING_ITEMS_VERSION_KEY = 'DRAWING_ITEMS_VERSION'\nconst DRAWING_ID = 'idGlobal'\nconst TREE_NODE_ID = 'treeNodeId'\nconst FORM_CONF = 'formConf'\n\nexport function getDrawingList() {\n  // 加入缓存版本的概念，保证缓存数据与程序匹配\n  const version = localStorage.getItem(DRAWING_ITEMS_VERSION_KEY)\n  if (version !== DRAWING_ITEMS_VERSION) {\n    localStorage.setItem(DRAWING_ITEMS_VERSION_KEY, DRAWING_ITEMS_VERSION)\n    saveDrawingList([])\n    return null\n  }\n\n  const str = localStorage.getItem(DRAWING_ITEMS)\n  if (str) return JSON.parse(str)\n  return null\n}\n\nexport function saveDrawingList(list) {\n  localStorage.setItem(DRAWING_ITEMS, JSON.stringify(list))\n}\n\nexport function getIdGlobal() {\n  const str = localStorage.getItem(DRAWING_ID)\n  if (str) return parseInt(str, 10)\n  return 100\n}\n\nexport function saveIdGlobal(id) {\n  localStorage.setItem(DRAWING_ID, `${id}`)\n}\n\nexport function getTreeNodeId() {\n  const str = localStorage.getItem(TREE_NODE_ID)\n  if (str) return parseInt(str, 10)\n  return 100\n}\n\nexport function saveTreeNodeId(id) {\n  localStorage.setItem(TREE_NODE_ID, `${id}`)\n}\n\nexport function getFormConf() {\n  const str = localStorage.getItem(FORM_CONF)\n  if (str) return JSON.parse(str)\n  return null\n}\n\nexport function saveFormConf(obj) {\n  localStorage.setItem(FORM_CONF, JSON.stringify(obj))\n}\n"], "mappings": "AAAA,OAAOA,EAAE,MAAM,+BAA+B;AAE9C,IAAMC,aAAa,GAAG,cAAc;AACpC,IAAMC,qBAAqB,GAAG,KAAK;AACnC,IAAMC,yBAAyB,GAAG,uBAAuB;AACzD,IAAMC,UAAU,GAAG,UAAU;AAC7B,IAAMC,YAAY,GAAG,YAAY;AACjC,IAAMC,SAAS,GAAG,UAAU;AAE5B,OAAO,SAASC,cAAcA,CAAA,EAAG;EAC/B;EACA,IAAMC,OAAO,GAAGC,YAAY,CAACC,OAAO,CAACP,yBAAyB,CAAC;EAC/D,IAAIK,OAAO,KAAKN,qBAAqB,EAAE;IACrCO,YAAY,CAACE,OAAO,CAACR,yBAAyB,EAAED,qBAAqB,CAAC;IACtEU,eAAe,CAAC,EAAE,CAAC;IACnB,OAAO,IAAI;EACb;EAEA,IAAMC,GAAG,GAAGJ,YAAY,CAACC,OAAO,CAACT,aAAa,CAAC;EAC/C,IAAIY,GAAG,EAAE,OAAOC,IAAI,CAACC,KAAK,CAACF,GAAG,CAAC;EAC/B,OAAO,IAAI;AACb;AAEA,OAAO,SAASD,eAAeA,CAACI,IAAI,EAAE;EACpCP,YAAY,CAACE,OAAO,CAACV,aAAa,EAAEa,IAAI,CAACG,SAAS,CAACD,IAAI,CAAC,CAAC;AAC3D;AAEA,OAAO,SAASE,WAAWA,CAAA,EAAG;EAC5B,IAAML,GAAG,GAAGJ,YAAY,CAACC,OAAO,CAACN,UAAU,CAAC;EAC5C,IAAIS,GAAG,EAAE,OAAOM,QAAQ,CAACN,GAAG,EAAE,EAAE,CAAC;EACjC,OAAO,GAAG;AACZ;AAEA,OAAO,SAASO,YAAYA,CAACC,EAAE,EAAE;EAC/BZ,YAAY,CAACE,OAAO,CAACP,UAAU,KAAAkB,MAAA,CAAKD,EAAE,CAAE,CAAC;AAC3C;AAEA,OAAO,SAASE,aAAaA,CAAA,EAAG;EAC9B,IAAMV,GAAG,GAAGJ,YAAY,CAACC,OAAO,CAACL,YAAY,CAAC;EAC9C,IAAIQ,GAAG,EAAE,OAAOM,QAAQ,CAACN,GAAG,EAAE,EAAE,CAAC;EACjC,OAAO,GAAG;AACZ;AAEA,OAAO,SAASW,cAAcA,CAACH,EAAE,EAAE;EACjCZ,YAAY,CAACE,OAAO,CAACN,YAAY,KAAAiB,MAAA,CAAKD,EAAE,CAAE,CAAC;AAC7C;AAEA,OAAO,SAASI,WAAWA,CAAA,EAAG;EAC5B,IAAMZ,GAAG,GAAGJ,YAAY,CAACC,OAAO,CAACJ,SAAS,CAAC;EAC3C,IAAIO,GAAG,EAAE,OAAOC,IAAI,CAACC,KAAK,CAACF,GAAG,CAAC;EAC/B,OAAO,IAAI;AACb;AAEA,OAAO,SAASa,YAAYA,CAACC,GAAG,EAAE;EAChClB,YAAY,CAACE,OAAO,CAACL,SAAS,EAAEQ,IAAI,CAACG,SAAS,CAACU,GAAG,CAAC,CAAC;AACtD", "ignoreList": []}]}