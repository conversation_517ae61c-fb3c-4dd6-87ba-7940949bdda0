{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/project/report/form.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/project/report/form.vue", "mtime": 1753528817894}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldFJlcG9ydCwgYWRkUmVwb3J0LCB1cGRhdGVSZXBvcnQsIGNoZWNrTmFtZVVuaXF1ZSB9IGZyb20gIkAvYXBpL3Byb2plY3QvcmVwb3J0IjsKaW1wb3J0IEZpbGVVcGxvYWQgZnJvbSAiQC9jb21wb25lbnRzL0ZpbGVVcGxvYWQiOwppbXBvcnQgZmxvd2FibGUgZnJvbSAnQC92aWV3cy9mbG93YWJsZS90YXNrL3JlY29yZC9pbmRleCc7CmltcG9ydCB7IHJlZ2lvbkRhdGEsIENvZGVUb1RleHQsIFRleHRUb0NvZGUgfSBmcm9tICJlbGVtZW50LWNoaW5hLWFyZWEtZGF0YSI7CmltcG9ydCBwcmludCBmcm9tICJwcmludC1qcyI7CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiUmVwb3J0IiwKICBjb21wb25lbnRzOiB7CiAgICBmbG93YWJsZTogZmxvd2FibGUsCiAgICBGaWxlVXBsb2FkOiBGaWxlVXBsb2FkLAogICAgcHJpbnQ6IHByaW50CiAgfSwKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgdmFyIHRoYXQgPSB0aGlzOwogICAgdmFyIGluZm9UeXBlVmFsdWVWYWxpID0gZnVuY3Rpb24gaW5mb1R5cGVWYWx1ZVZhbGkocnVsZSwgdmFsdWUsIGNhbGxiYWNrKSB7CiAgICAgIGlmICh0aGF0LmZvcm0uaW5mb1R5cGUuaW5kZXhPZignMScpID49IDAgJiYgIXRoYXQuZm9ybS5zY2FuRmlsZSkgewogICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcigi6YKu566x5Zyw5Z2A5b+F5aGrIikpOwogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICBjYWxsYmFjaygpOwogICAgfTsKICAgIHZhciBpbmZvVHlwZVZhbHVlVmFsaTIgPSBmdW5jdGlvbiBpbmZvVHlwZVZhbHVlVmFsaTIocnVsZSwgdmFsdWUsIGNhbGxiYWNrKSB7CiAgICAgIGlmICh0aGF0LmZvcm0uaW5mb1R5cGUuaW5kZXhPZignMicpID49IDAgJiYgIXRoYXQuZm9ybS5zZW5kQWRkcmVzcykgewogICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcigi5pS25Lu25Zyw5Z2A5b+F5aGrIikpOwogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICBjYWxsYmFjaygpOwogICAgfTsKICAgIHZhciBuYW1lVmFsaSA9IGZ1bmN0aW9uIG5hbWVWYWxpKHJ1bGUsIHZhbHVlLCBjYWxsYmFjaykgewogICAgICBpZiAoIXRoYXQuZm9ybS5wcm9qZWN0TmFtZSkgewogICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcigi6aG555uu5ZCN56ew5b+F5aGrIikpOwogICAgICB9IGVsc2UgewogICAgICAgIGlmICgvXHMrL2cudGVzdCh0aGF0LmZvcm0ucHJvamVjdE5hbWUpKSB7CiAgICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoIumhueebruWQjeensOS4jeinhOiMgyIpKTsKICAgICAgICAgIHJldHVybjsKICAgICAgICB9CiAgICAgICAgY2hlY2tOYW1lVW5pcXVlKHsKICAgICAgICAgIHByb2plY3ROYW1lOiB0aGF0LmZvcm0ucHJvamVjdE5hbWUsCiAgICAgICAgICBwcm9qZWN0SWQ6IHRoYXQuZm9ybS5wcm9qZWN0SWQKICAgICAgICB9KS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEgPT0gMCkgewogICAgICAgICAgICBjYWxsYmFjaygpOwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCLpobnnm67lkI3np7Dlt7LlrZjlnKgiKSk7CiAgICAgICAgICB9CiAgICAgICAgfSk7CiAgICAgIH0KICAgIH07CiAgICB2YXIgY29kZVZhbGkgPSBmdW5jdGlvbiBjb2RlVmFsaShydWxlLCB2YWx1ZSwgY2FsbGJhY2spIHsKICAgICAgaWYgKCF0aGF0LmZvcm0ucHJvamVjdE5vKSB7CiAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCLpobnnm67nvJblj7flv4XloasiKSk7CiAgICAgIH0gZWxzZSBpZiAoL1xzKy9nLnRlc3QodGhhdC5mb3JtLnByb2plY3RObykpIHsKICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoIumhueebrue8luWPt+S4jeinhOiMgyIpKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgY2FsbGJhY2soKTsKICAgIH07CiAgICB2YXIgb3BlbkRhdGVWYWxpID0gZnVuY3Rpb24gb3BlbkRhdGVWYWxpKHJ1bGUsIHZhbHVlLCBjYWxsYmFjaykgewogICAgICBpZiAoIXRoYXQuZm9ybS5vcGVuRGF0ZSkgewogICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcigi5byA5qCH5pel5pyf5b+F5aGrIikpOwogICAgICAgIHJldHVybjsKICAgICAgfSBlbHNlIGlmICh2YWx1ZSA9PT0gIuaXoCIpIHsKICAgICAgICBjYWxsYmFjaygpOwogICAgICAgIHJldHVybjsKICAgICAgfSBlbHNlIGlmICghL15cZHs0fS1cZHsyfS1cZHsyfSQvLnRlc3QodmFsdWUpKSB7CiAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCLlvIDmoIfml6XmnJ/moLzlvI/kuI3lkIjms5XvvIznpLrkvosyMDI1LTAxLTAxIikpOwogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICBjYWxsYmFjaygpOwogICAgfTsKICAgIHZhciBoYW5nRGF0ZVZhbGkgPSBmdW5jdGlvbiBoYW5nRGF0ZVZhbGkocnVsZSwgdmFsdWUsIGNhbGxiYWNrKSB7CiAgICAgIGlmICghdGhhdC5mb3JtLmhhbmdEYXRlKSB7CiAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCLmjILnvZHml6XmnJ/lv4XloasiKSk7CiAgICAgICAgcmV0dXJuOwogICAgICB9IGVsc2UgaWYgKHZhbHVlID09PSAi5pegIikgewogICAgICAgIGNhbGxiYWNrKCk7CiAgICAgICAgcmV0dXJuOwogICAgICB9IGVsc2UgaWYgKCEvXlxkezR9LVxkezJ9LVxkezJ9JC8udGVzdCh2YWx1ZSkpIHsKICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoIuaMgue9keaXpeacn+agvOW8j+S4jeWQiOazle+8jOekuuS+izIwMjUtMDEtMDEiKSk7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIGNhbGxiYWNrKCk7CiAgICB9OwogICAgcmV0dXJuIHsKICAgICAgLy8g6YGu572p5bGCCiAgICAgIGxvYWRpbmc6IHRydWUsCiAgICAgIC8vIOaTjeS9nOexu+Wei+Wtl+WFuAogICAgICBvcGVyYXRpb25UeXBlT3B0aW9uczogW10sCiAgICAgIC8vIOWuoeaguOeKtuaAgeWtl+WFuAogICAgICBhdWRpdFN0YXR1c09wdGlvbnM6IFtdLAogICAgICAvLyDnvJbovpHnirbmgIHlrZflhbgKICAgICAgZWRpdFN0YXR1c09wdGlvbnM6IFtdLAogICAgICAvLyDmi5vmoIfmlrnlvI/lrZflhbgKICAgICAgYmlkZGluZ1R5cGVPcHRpb25zOiBbXSwKICAgICAgLy8g5oqV5qCH5Lqn5ZOB5Z6L5Y+35a2X5YW4CiAgICAgIG1vZGVsT3B0aW9uczogW10sCiAgICAgIG1vZGVsT3B0aW9uMTogW10sCiAgICAgIC8vIOaJgOmcgOi1hOaWmeWtl+WFuAogICAgICByZXF1aXJlSW5mb09wdGlvbnM6IFtdLAogICAgICByZXF1aXJlSW5mb09wdGlvbjE6IFtdLAogICAgICAvLyDotYTmlpnnsbvlnovlrZflhbgKICAgICAgaW5mb1R5cGVPcHRpb25zOiBbXSwKICAgICAgLy8g5omA5bGe55yB5Lu95a2X5YW4CiAgICAgIGJlbG9uZ1Byb3ZpbmNlT3B0aW9uczogW10sCiAgICAgIGJlbG9uZ1Byb3ZpbmNlT3B0aW9uczE6IFtdLAogICAgICAvLyDllK7lkI7lubTpmZAKICAgICAgYWZ0ZXJTYWxlWWVhck9wdGlvbnM6IFtdLAogICAgICBhZnRlclNhbGVZZWFyT3B0aW9uczE6IFtdLAogICAgICBzcGVjT3B0aW9uczogW10sCiAgICAgIHNwZWNPcHRpb24xOiBbXSwKICAgICAgLy8g6KGo5Y2V5Y+C5pWwCiAgICAgIGZvcm06IHt9LAogICAgICAvLyDooajljZXmoKHpqowKICAgICAgLy8g6KGo5Y2V5qCh6aqMCiAgICAgIHJ1bGVzOiB7CiAgICAgICAgb3BlcmF0aW9uVHlwZTogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogIuaTjeS9nOexu+Wei+W/hemAiSIKICAgICAgICB9XSwKICAgICAgICBwcm9qZWN0Tm86IFt7CiAgICAgICAgICB2YWxpZGF0b3I6IGNvZGVWYWxpLAogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIKICAgICAgICB9XSwKICAgICAgICBwcm9qZWN0TmFtZTogW3sKICAgICAgICAgIHZhbGlkYXRvcjogbmFtZVZhbGksCiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgIH1dLAogICAgICAgIGFkZHJlc3M6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICLor6bnu4blnLDlnYDkuI3og73kuLrnqboiLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgfV0sCiAgICAgICAgYmlkZGluZ0NvbXBhbnk6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICLmi5vmoIfljZXkvY3kuI3og73kuLrnqboiLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgfV0sCiAgICAgICAgb3BlbkRhdGU6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIHZhbGlkYXRvcjogb3BlbkRhdGVWYWxpLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgfV0sCiAgICAgICAgYWZ0ZXJTYWxlWWVhcjogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogIuWUruWQjuW5tOmZkOS4jeiDveS4uuepuiIsCiAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIKICAgICAgICB9XSwKICAgICAgICBoYW5nRGF0ZTogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgdmFsaWRhdG9yOiBoYW5nRGF0ZVZhbGksCiAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIKICAgICAgICB9XSwKICAgICAgICBiZWxvbmdQcm92aW5jZTogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogIuWUruWQjuW5tOmZkOS4jeiDveS4uuepuiIsCiAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIKICAgICAgICB9XSwKICAgICAgICBkaXN0cmlidXRvcjogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogIuaJgOWxnue7j+mUgOWVhuS4jeiDveS4uuepuiIsCiAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIKICAgICAgICB9XSwKICAgICAgICBzY2FuRmlsZTogW3sKICAgICAgICAgIHZhbGlkYXRvcjogaW5mb1R5cGVWYWx1ZVZhbGksCiAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIKICAgICAgICB9XSwKICAgICAgICBzZW5kQWRkcmVzczogW3sKICAgICAgICAgIHZhbGlkYXRvcjogaW5mb1R5cGVWYWx1ZVZhbGkyLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgfV0sCiAgICAgICAgbW9kZWw6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICLmipXmoIfkuqflk4Hlnovlj7flv4XpgIkiCiAgICAgICAgfV0sCiAgICAgICAgc3BlYzogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogIuaKleagh+S6p+WTgeinhOagvOW/hemAiSIKICAgICAgICB9XSwKICAgICAgICBwcm92aW5jZTogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogIumhueebruaJgOWcqOWcsOW/hemAiSIKICAgICAgICB9XSwKICAgICAgICBpbmZvVHlwZTogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogIui1hOaWmeaOpeaUtuaWueW8j+W/hemAiSIsCiAgICAgICAgICB0cmlnZ2VyOiAiY2hhbmdlIgogICAgICAgIH1dLAogICAgICAgIGJpZGRpbmdDb250YWN0OiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAi5oub5qCH5Y2V5L2N6IGU57O75Lq6L+iBlOezu+eUteivneW/heWhqyIKICAgICAgICB9XSwKICAgICAgICBhdXRoQ29udGFjdDogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogIuaOiOadg+WFrOWPuOiBlOezu+S6ui/ogZTns7vnlLXor53lv4XloasiCiAgICAgICAgfV0KICAgICAgfSwKICAgICAgb3B0aW9uczogcmVnaW9uRGF0YSwKICAgICAgc2VsZWN0ZWRPcHRpb25zOiBbXSwKICAgICAgcXVlcnlBcmVhOiBbXSwKICAgICAgLy/lt6XkvZzmtYHlj4LmlbAKICAgICAgZmluaXNoZWQ6ICdmYWxzZScsCiAgICAgIHRhc2tJZDogdW5kZWZpbmVkLAogICAgICBwcm9jSW5zSWQ6IHVuZGVmaW5lZCwKICAgICAgYnVzaW5lc3NLZXk6IHVuZGVmaW5lZCwKICAgICAgYXVkaXQ6IGZhbHNlLAogICAgICBmb3JtRWRpdDogZmFsc2UsCiAgICAgIC8v5bel5L2c5rWB5Y+C5pWwZW5kCiAgICAgIGF1dGhDb21wYW55czogW10KICAgIH07CiAgfSwKICBhY3RpdmF0ZWQ6IGZ1bmN0aW9uIGFjdGl2YXRlZCgpIHsKICAgIHRoaXMucmVzZXQoKTsKICAgIC8vdGhpcy5mb3JtLnByb2plY3RObyA9IG1vbWVudChuZXcgRGF0ZSgpKS5mb3JtYXQoJ1lZWVlNTURESEhtbScpOwogICAgdGhpcy50YXNrSWQgPSB0aGlzLiRyb3V0ZS5xdWVyeSAmJiB0aGlzLiRyb3V0ZS5xdWVyeS50YXNrSWQ7CiAgICB0aGlzLnByb2NJbnNJZCA9IHRoaXMuJHJvdXRlLnF1ZXJ5ICYmIHRoaXMuJHJvdXRlLnF1ZXJ5LnByb2NJbnNJZDsKICAgIHRoaXMuZmluaXNoZWQgPSB0aGlzLiRyb3V0ZS5xdWVyeSAmJiB0aGlzLiRyb3V0ZS5xdWVyeS5maW5pc2hlZDsKICAgIHRoaXMuYnVzaW5lc3NLZXkgPSB0aGlzLiRyb3V0ZS5xdWVyeSAmJiB0aGlzLiRyb3V0ZS5xdWVyeS5idXNpbmVzc0tleTsKICAgIHZhciBlZGl0ID0gdGhpcy4kcm91dGUucXVlcnkgJiYgdGhpcy4kcm91dGUucXVlcnkuZm9ybUVkaXQ7CiAgICBpZiAoZWRpdCA9PSAidHJ1ZSIpIHsKICAgICAgdGhpcy5mb3JtRWRpdCA9IHRydWU7CiAgICB9IGVsc2UgewogICAgICB0aGlzLmZvcm1FZGl0ID0gZmFsc2U7CiAgICB9CiAgICBpZiAodGhpcy5idXNpbmVzc0tleSkgewogICAgICBpZiAodGhpcy5maW5pc2hlZCA9PSAiZmFsc2UiICYmICF0aGlzLmZvcm1FZGl0KSB7CiAgICAgICAgdGhpcy5hdWRpdCA9IHRydWU7CiAgICAgIH0KICAgICAgdGhpcy5nZXRSZXBvcnRJbmZvKHRoaXMuYnVzaW5lc3NLZXkpOwogICAgfQogICAgY29uc29sZS5sb2coIj09PT09PT09cHJvamVjdD09PT09PT09PT5hY3RpdmF0ZWQ+Zm9ybUVkaXQ+PiIgKyB0aGlzLmZvcm1FZGl0KTsKICB9LAogIGNyZWF0ZWQ6IGZ1bmN0aW9uIGNyZWF0ZWQoKSB7CiAgICB2YXIgX3RoaXMgPSB0aGlzOwogICAgdGhpcy5yZXNldCgpOwogICAgLy90aGlzLmZvcm0ucHJvamVjdE5vID0gbW9tZW50KG5ldyBEYXRlKCkpLmZvcm1hdCgnWVlZWU1NRERISG1tJyk7CiAgICB0aGlzLnRhc2tJZCA9IHRoaXMuJHJvdXRlLnF1ZXJ5ICYmIHRoaXMuJHJvdXRlLnF1ZXJ5LnRhc2tJZDsKICAgIHRoaXMucHJvY0luc0lkID0gdGhpcy4kcm91dGUucXVlcnkgJiYgdGhpcy4kcm91dGUucXVlcnkucHJvY0luc0lkOwogICAgdGhpcy5maW5pc2hlZCA9IHRoaXMuJHJvdXRlLnF1ZXJ5ICYmIHRoaXMuJHJvdXRlLnF1ZXJ5LmZpbmlzaGVkOwogICAgdGhpcy5idXNpbmVzc0tleSA9IHRoaXMuJHJvdXRlLnF1ZXJ5ICYmIHRoaXMuJHJvdXRlLnF1ZXJ5LmJ1c2luZXNzS2V5OwogICAgdmFyIGVkaXQgPSB0aGlzLiRyb3V0ZS5xdWVyeSAmJiB0aGlzLiRyb3V0ZS5xdWVyeS5mb3JtRWRpdDsKICAgIGlmIChlZGl0ID09ICJ0cnVlIikgewogICAgICB0aGlzLmZvcm1FZGl0ID0gdHJ1ZTsKICAgIH0gZWxzZSB7CiAgICAgIHRoaXMuZm9ybUVkaXQgPSBmYWxzZTsKICAgIH0KICAgIGlmICh0aGlzLmJ1c2luZXNzS2V5KSB7CiAgICAgIGlmICh0aGlzLmZpbmlzaGVkID09ICJmYWxzZSIgJiYgIXRoaXMuZm9ybUVkaXQpIHsKICAgICAgICB0aGlzLmF1ZGl0ID0gdHJ1ZTsKICAgICAgfQogICAgICB0aGlzLmdldFJlcG9ydEluZm8odGhpcy5idXNpbmVzc0tleSk7CiAgICB9CiAgICAvLyB0aGlzLmF1ZGl0ID0gdHJ1ZTsKICAgIGNvbnNvbGUubG9nKCI9PT09PT09PT1wcm9qZWN0PT09PT09PT0+Y3JlYXRlZD4+Zm9ybUVkaXQ+IiArIHRoaXMuZm9ybUVkaXQpOwogICAgdGhpcy5nZXREaWN0cygicHJfb3BlcmF0aW9uX3R5cGUiKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICBfdGhpcy5vcGVyYXRpb25UeXBlT3B0aW9ucyA9IHJlc3BvbnNlLmRhdGE7CiAgICB9KTsKICAgIHRoaXMuZ2V0RGljdHMoInByX2F1ZGl0X3N0YXR1cyIpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgIF90aGlzLmF1ZGl0U3RhdHVzT3B0aW9ucyA9IHJlc3BvbnNlLmRhdGE7CiAgICB9KTsKICAgIHRoaXMuZ2V0RGljdHMoInByX2VkaXRfc3RhdHVzIikudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgX3RoaXMuZWRpdFN0YXR1c09wdGlvbnMgPSByZXNwb25zZS5kYXRhOwogICAgfSk7CiAgICAvLyB0aGlzLmdldERpY3RzKCJwcl9iaWRkaW5nX3R5cGUiKS50aGVuKChyZXNwb25zZSkgPT4gewogICAgLy8gICB0aGlzLmJpZGRpbmdUeXBlT3B0aW9ucyA9IHJlc3BvbnNlLmRhdGE7CiAgICAvLyB9KTsKICAgIHRoaXMuZ2V0RGljdHMoInByX21vZGVsIikudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgX3RoaXMubW9kZWxPcHRpb24xID0gcmVzcG9uc2UuZGF0YTsKICAgICAgdmFyIG9wdCA9IFtdOwogICAgICByZXNwb25zZS5kYXRhLmZvckVhY2goZnVuY3Rpb24gKGVsZW0sIGluZGV4KSB7CiAgICAgICAgdmFyIG9iaiA9IHt9OwogICAgICAgIG9iai5sYWJlbCA9IGVsZW0uZGljdExhYmVsOwogICAgICAgIG9iai52YWx1ZSA9IGVsZW0uZGljdFZhbHVlOwogICAgICAgIG9wdC5wdXNoKG9iaik7CiAgICAgIH0pOwogICAgICBfdGhpcy5tb2RlbE9wdGlvbnMgPSBvcHQ7CiAgICB9KTsKICAgIHRoaXMuZ2V0RGljdHMoInByX3NwZWMiKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICBfdGhpcy5zcGVjT3B0aW9uMSA9IHJlc3BvbnNlLmRhdGE7CiAgICAgIHZhciBvcHQgPSBbXTsKICAgICAgcmVzcG9uc2UuZGF0YS5mb3JFYWNoKGZ1bmN0aW9uIChlbGVtLCBpbmRleCkgewogICAgICAgIHZhciBvYmogPSB7fTsKICAgICAgICBvYmoubGFiZWwgPSBlbGVtLmRpY3RMYWJlbDsKICAgICAgICBvYmoudmFsdWUgPSBlbGVtLmRpY3RWYWx1ZTsKICAgICAgICBvcHQucHVzaChvYmopOwogICAgICB9KTsKICAgICAgX3RoaXMuc3BlY09wdGlvbnMgPSBvcHQ7CiAgICB9KTsKICAgIHRoaXMuZ2V0RGljdHMoInByX2luZm8iKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICBfdGhpcy5yZXF1aXJlSW5mb09wdGlvbjEgPSByZXNwb25zZS5kYXRhOwogICAgICB2YXIgb3B0ID0gW107CiAgICAgIHJlc3BvbnNlLmRhdGEuZm9yRWFjaChmdW5jdGlvbiAoZWxlbSwgaW5kZXgpIHsKICAgICAgICB2YXIgb2JqID0ge307CiAgICAgICAgb2JqLmxhYmVsID0gZWxlbS5kaWN0TGFiZWw7CiAgICAgICAgb2JqLnZhbHVlID0gZWxlbS5kaWN0VmFsdWU7CiAgICAgICAgb3B0LnB1c2gob2JqKTsKICAgICAgfSk7CiAgICAgIF90aGlzLnJlcXVpcmVJbmZvT3B0aW9ucyA9IG9wdDsKICAgIH0pOwogICAgdGhpcy5nZXREaWN0cygicHJfcHJvdmluY2UiKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICBfdGhpcy5iZWxvbmdQcm92aW5jZU9wdGlvbnMxID0gcmVzcG9uc2UuZGF0YTsKICAgICAgdmFyIG9wdCA9IFtdOwogICAgICByZXNwb25zZS5kYXRhLmZvckVhY2goZnVuY3Rpb24gKGVsZW0sIGluZGV4KSB7CiAgICAgICAgdmFyIG9iaiA9IHt9OwogICAgICAgIG9iai5sYWJlbCA9IGVsZW0uZGljdExhYmVsOwogICAgICAgIG9iai52YWx1ZSA9IGVsZW0uZGljdFZhbHVlOwogICAgICAgIG9wdC5wdXNoKG9iaik7CiAgICAgIH0pOwogICAgICBfdGhpcy5iZWxvbmdQcm92aW5jZU9wdGlvbnMgPSBvcHQ7CiAgICB9KTsKICAgIHRoaXMuZ2V0RGljdHMoInByX2FmdGVyX3NhbGVfeWVhciIpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgIF90aGlzLmFmdGVyU2FsZVllYXJPcHRpb25zMSA9IHJlc3BvbnNlLmRhdGE7CiAgICAgIHZhciBvcHQgPSBbXTsKICAgICAgcmVzcG9uc2UuZGF0YS5mb3JFYWNoKGZ1bmN0aW9uIChlbGVtLCBpbmRleCkgewogICAgICAgIHZhciBvYmogPSB7fTsKICAgICAgICBvYmoubGFiZWwgPSBlbGVtLmRpY3RMYWJlbDsKICAgICAgICBvYmoudmFsdWUgPSBlbGVtLmRpY3RWYWx1ZTsKICAgICAgICBvcHQucHVzaChvYmopOwogICAgICB9KTsKICAgICAgX3RoaXMuYWZ0ZXJTYWxlWWVhck9wdGlvbnMgPSBvcHQ7CiAgICB9KTsKICAgIHRoaXMuZ2V0RGljdHMoInByX2RhdGFfdHlwZSIpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgIF90aGlzLmluZm9UeXBlT3B0aW9ucyA9IHJlc3BvbnNlLmRhdGE7CiAgICB9KTsKICAgIC8v6buY6K6k5oql5aSHCiAgICB0aGlzLmZvcm0ub3BlcmF0aW9uVHlwZSA9ICcxJzsKICB9LAogIG1ldGhvZHM6IHsKICAgIC8vIOihqOWNlemHjee9rgogICAgcmVzZXQ6IGZ1bmN0aW9uIHJlc2V0KCkgewogICAgICB0aGlzLmZvcm0gPSB7CiAgICAgICAgcHJvamVjdElkOiBudWxsLAogICAgICAgIHByb2plY3RObzogbnVsbCwKICAgICAgICBwcm9qZWN0TmFtZTogbnVsbCwKICAgICAgICBvcGVyYXRpb25UeXBlOiAxLAogICAgICAgIGF1ZGl0U3RhdHVzOiAiMSIsCiAgICAgICAgcmVqZWN0UmVhc29uOiBudWxsLAogICAgICAgIHByb3ZpbmNlOiBudWxsLAogICAgICAgIGNpdHk6IG51bGwsCiAgICAgICAgZGlzdHJpY3Q6IG51bGwsCiAgICAgICAgYWRkcmVzczogbnVsbCwKICAgICAgICBlZGl0U3RhdHVzOiAiMCIsCiAgICAgICAgYmVsb25nVXNlcjogbnVsbCwKICAgICAgICBiaWRkaW5nQ29tcGFueTogbnVsbCwKICAgICAgICBvcGVuRGF0ZTogbnVsbCwKICAgICAgICBiZWxvbmdQcm92aW5jZTogbnVsbCwKICAgICAgICBhZnRlclNhbGVZZWFyOiBudWxsLAogICAgICAgIGhhbmdEYXRlOiBudWxsLAogICAgICAgIGJpZGRpbmdUeXBlOiBudWxsLAogICAgICAgIGJ1ZGdldE1vbmV5OiBudWxsLAogICAgICAgIGF1dGhDb21wYW55OiBudWxsLAogICAgICAgIGJpZGRpbmdOZXQ6IG51bGwsCiAgICAgICAgZGlzdHJpYnV0b3I6IG51bGwsCiAgICAgICAgbW9kZWw6IFtdLAogICAgICAgIHNwZWM6IFtdLAogICAgICAgIGFyZWE6IG51bGwsCiAgICAgICAgYXV0aEZpbGU6IG51bGwsCiAgICAgICAgYWZ0ZXJTYWxlRmlsZTogbnVsbCwKICAgICAgICByZXF1aXJlSW5mbzogW10sCiAgICAgICAgaW5mb1R5cGU6IFtdLAogICAgICAgIHNjYW5GaWxlOiBudWxsLAogICAgICAgIHNlbmRBZGRyZXNzOiBudWxsLAogICAgICAgIG1haWxJbmZvOiBudWxsLAogICAgICAgIGV4cHJlc3NJbmZvOiBudWxsLAogICAgICAgIHJlbWFyazogbnVsbCwKICAgICAgICBzcGFyZTE6IG51bGwsCiAgICAgICAgc3BhcmUyOiBudWxsCiAgICAgIH07CiAgICAgIHRoaXMucmVzZXRGb3JtKCJmb3JtIik7CiAgICB9LAogICAgLy8g5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2U6IGZ1bmN0aW9uIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsKICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgcmV0dXJuIGl0ZW0ucHJvamVjdElkOwogICAgICB9KTsKICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoICE9PSAxOwogICAgICB0aGlzLm11bHRpcGxlID0gIXNlbGVjdGlvbi5sZW5ndGg7CiAgICAgIGNvbnNvbGUuaW5mbyhzZWxlY3Rpb24pOwogICAgfSwKICAgIC8qKiDkv67mlLnmjInpkq7mk43kvZwgKi9nZXRSZXBvcnRJbmZvOiBmdW5jdGlvbiBnZXRSZXBvcnRJbmZvKHByb2plY3RJZCkgewogICAgICB2YXIgX3RoaXMyID0gdGhpczsKICAgICAgZ2V0UmVwb3J0KHByb2plY3RJZCkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICBfdGhpczIuZm9ybSA9IHJlc3BvbnNlLmRhdGE7CiAgICAgICAgaWYgKF90aGlzMi5mb3JtLm1vZGVsKSBfdGhpczIuZm9ybS5tb2RlbCA9IF90aGlzMi5mb3JtLm1vZGVsLnNwbGl0KCIsIik7ZWxzZSBfdGhpczIuZm9ybS5tb2RlbCA9IFtdOwogICAgICAgIGlmIChfdGhpczIuZm9ybS5yZXF1aXJlSW5mbykgX3RoaXMyLmZvcm0ucmVxdWlyZUluZm8gPSBfdGhpczIuZm9ybS5yZXF1aXJlSW5mby5zcGxpdCgiLCIpO2Vsc2UgX3RoaXMyLmZvcm0ucmVxdWlyZUluZm8gPSBbXTsKICAgICAgICBpZiAoX3RoaXMyLmZvcm0uaW5mb1R5cGUpIF90aGlzMi5mb3JtLmluZm9UeXBlID0gX3RoaXMyLmZvcm0uaW5mb1R5cGUuc3BsaXQoIiwiKTtlbHNlIF90aGlzMi5mb3JtLmluZm9UeXBlID0gW107CiAgICAgICAgaWYgKF90aGlzMi5mb3JtLnNwZWMpIF90aGlzMi5mb3JtLnNwZWMgPSBfdGhpczIuZm9ybS5zcGVjLnNwbGl0KCIsIik7ZWxzZSBfdGhpczIuZm9ybS5zcGVjID0gW107CiAgICAgICAgdmFyIHByb3ZpbmNlcyA9IHJlc3BvbnNlLmRhdGEucHJvdmluY2U7CiAgICAgICAgaWYgKHByb3ZpbmNlcy5sZW5ndGggPiAwKSB7CiAgICAgICAgICB2YXIgYWRkcmVzcyA9IHByb3ZpbmNlcy5zcGxpdCgiLyIpOwogICAgICAgICAgdmFyIGNpdHlzID0gW107CiAgICAgICAgICAvLyDnnIHku70KICAgICAgICAgIGlmIChhZGRyZXNzLmxlbmd0aCA+IDApIGNpdHlzLnB1c2goVGV4dFRvQ29kZVthZGRyZXNzWzBdXS5jb2RlKTsKICAgICAgICAgIC8vIOWfjuW4ggogICAgICAgICAgaWYgKGFkZHJlc3MubGVuZ3RoID4gMSkgY2l0eXMucHVzaChUZXh0VG9Db2RlW2FkZHJlc3NbMF1dW2FkZHJlc3NbMV1dLmNvZGUpOwogICAgICAgICAgLy8g5Zyw5Yy6CiAgICAgICAgICBpZiAoYWRkcmVzcy5sZW5ndGggPiAyKSBjaXR5cy5wdXNoKFRleHRUb0NvZGVbYWRkcmVzc1swXV1bYWRkcmVzc1sxXV1bYWRkcmVzc1syXV0uY29kZSk7CiAgICAgICAgICBfdGhpczIuc2VsZWN0ZWRPcHRpb25zID0gY2l0eXM7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvKiog5o+Q5Lqk5oyJ6ZKuICovc3VibWl0Rm9ybTogZnVuY3Rpb24gc3VibWl0Rm9ybSgpIHsKICAgICAgdmFyIF90aGlzMyA9IHRoaXM7CiAgICAgIHZhciB0aGF0ID0gdGhpczsKICAgICAgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKGZ1bmN0aW9uICh2YWxpZCkgewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgaWYgKF90aGlzMy5mb3JtLmluZm9UeXBlLmluZGV4T2YoJzEnKSA+PSAwICYmIF90aGlzMy5mb3JtLnNjYW5GaWxlKSB7CiAgICAgICAgICAgIHZhciBlbWFpbFJlZyA9IC9eW2EtekEtWjAtOV8tXStAW2EtekEtWjAtOV8tXSsoXC5bYS16QS1aMC05Xy1dKykrJC87CiAgICAgICAgICAgIGlmICghZW1haWxSZWcudGVzdChfdGhpczMuZm9ybS5zY2FuRmlsZSkpIHsKICAgICAgICAgICAgICBfdGhpczMuJG1lc3NhZ2UuZXJyb3IoIui1hOaWmeaOpeaUtuaWueW8j+mCrueuseagvOW8j+mUmeivryIpOwogICAgICAgICAgICAgIHJldHVybjsKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgICAgLy8gaWYgKHRoaXMuZm9ybS5vcGVyYXRpb25UeXBlID09IDIgJiYgIXRoaXMuZm9ybS5hdXRoRmlsZSkgewogICAgICAgICAgLy8gICB0aGlzLiRtZXNzYWdlLmVycm9yKCLmjojmnYPnsbvlnovlv4XpnIDkuIrkvKDmjojmnYPkuaYiKTsKICAgICAgICAgIC8vICAgcmV0dXJuOwogICAgICAgICAgLy8gfQogICAgICAgICAgdmFyIGZvcm1TdHIgPSBKU09OLnN0cmluZ2lmeShfdGhpczMuZm9ybSk7CiAgICAgICAgICB2YXIgZm9ybURhdGEgPSBKU09OLnBhcnNlKGZvcm1TdHIpOwogICAgICAgICAgaWYgKGZvcm1EYXRhLm1vZGVsICYmIGZvcm1EYXRhLm1vZGVsLmxlbmd0aCA+IDApIGZvcm1EYXRhLm1vZGVsID0gZm9ybURhdGEubW9kZWwuam9pbigiLCIpO2Vsc2UgZm9ybURhdGEubW9kZWwgPSB1bmRlZmluZWQ7CiAgICAgICAgICBpZiAoZm9ybURhdGEucmVxdWlyZUluZm8gJiYgZm9ybURhdGEucmVxdWlyZUluZm8ubGVuZ3RoID4gMCkgZm9ybURhdGEucmVxdWlyZUluZm8gPSBmb3JtRGF0YS5yZXF1aXJlSW5mby5qb2luKCIsIik7ZWxzZSBmb3JtRGF0YS5yZXF1aXJlSW5mbyA9IHVuZGVmaW5lZDsKICAgICAgICAgIGlmIChmb3JtRGF0YS5pbmZvVHlwZSAmJiBmb3JtRGF0YS5pbmZvVHlwZS5sZW5ndGggPiAwKSBmb3JtRGF0YS5pbmZvVHlwZSA9IGZvcm1EYXRhLmluZm9UeXBlLmpvaW4oIiwiKTtlbHNlIGZvcm1EYXRhLmluZm9UeXBlID0gdW5kZWZpbmVkOwogICAgICAgICAgaWYgKGZvcm1EYXRhLnNwZWMgJiYgZm9ybURhdGEuc3BlYy5sZW5ndGggPiAwKSBmb3JtRGF0YS5zcGVjID0gZm9ybURhdGEuc3BlYy5qb2luKCIsIik7ZWxzZSBmb3JtRGF0YS5zcGVjID0gdW5kZWZpbmVkOwoKICAgICAgICAgIC8v5o6I5p2D5YWs5Y+4CiAgICAgICAgICBpZiAoX3RoaXMzLmF1dGhDb21wYW55cy5sZW5ndGggPiAwKSB7CiAgICAgICAgICAgIHZhciBhcnJheSA9IG5ldyBBcnJheSgpOwogICAgICAgICAgICBfdGhpczMuYXV0aENvbXBhbnlzLmZvckVhY2goZnVuY3Rpb24gKGUpIHsKICAgICAgICAgICAgICBhcnJheS5wdXNoKGUudmFsdWUpOwogICAgICAgICAgICB9KTsKICAgICAgICAgICAgZm9ybURhdGEuYXV0aENvbXBhbnkgKz0gIiwiICsgYXJyYXkuam9pbigiLCIpOwogICAgICAgICAgfQogICAgICAgICAgdmFyIGxvYWRpbmcgPSBfdGhpczMuJGxvYWRpbmcoewogICAgICAgICAgICBsb2NrOiB0cnVlLAogICAgICAgICAgICAvL2xvY2vnmoTkv67mlLnnrKYtLem7mOiupOaYr2ZhbHNlCiAgICAgICAgICAgIHRleHQ6ICdMb2FkaW5nJywKICAgICAgICAgICAgLy/mmL7npLrlnKjliqDovb3lm77moIfkuIvmlrnnmoTliqDovb3mlofmoYgKICAgICAgICAgICAgc3Bpbm5lcjogJ2VsLWljb24tbG9hZGluZycsCiAgICAgICAgICAgIC8v6Ieq5a6a5LmJ5Yqg6L295Zu+5qCH57G75ZCNCiAgICAgICAgICAgIGJhY2tncm91bmQ6ICdyZ2JhKDAsIDAsIDAsIDAuNyknLAogICAgICAgICAgICAvL+mBrue9qeWxguminOiJsgogICAgICAgICAgICB0YXJnZXQ6IGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3IoJy5tYWluLWNvbnRhaW5lcicpIC8vbG9hZGlu6KaG55uW55qEZG9t5YWD57Sg6IqC54K5CiAgICAgICAgICB9KTsKICAgICAgICAgIGRvY3VtZW50LmRvY3VtZW50RWxlbWVudC5zdHlsZS5vdmVyZmxvd1kgPSAnaGlkZGVuJzsgLy/npoHmraLlupXlsYJkaXbmu5rliqgKCiAgICAgICAgICBpZiAoZm9ybURhdGEucHJvamVjdElkICE9IG51bGwpIHsKICAgICAgICAgICAgdXBkYXRlUmVwb3J0KGZvcm1EYXRhKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgICAgICAgIC8vdGhpcy5tc2dTdWNjZXNzKCLkv67mlLnmiJDlip8iKTsKICAgICAgICAgICAgICBpZiAodGhhdC5idXNpbmVzc0tleSkgewogICAgICAgICAgICAgICAgdGhhdC4kcmVmc1snZmxvdyddLnRhc2tDb21wbGV0ZSgi6YeN5paw5o+Q5LqkIik7CiAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgIHRoYXQuc3RhcnRGbG93KGZvcm1EYXRhKTsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgc2V0VGltZW91dChmdW5jdGlvbiAoKSB7CiAgICAgICAgICAgICAgICBsb2FkaW5nLmNsb3NlKCk7CiAgICAgICAgICAgICAgICBkb2N1bWVudC5kb2N1bWVudEVsZW1lbnQuc3R5bGUub3ZlcmZsb3dZID0gJ2F1dG8nOyAvL+WFgeiuuOW6leWxgmRpdua7muWKqAogICAgICAgICAgICAgIH0sIDEwMDApOwogICAgICAgICAgICB9KS5jYXRjaChmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgICAgICAgY29uc29sZS5sb2cocmVzKTsKICAgICAgICAgICAgICBsb2FkaW5nLmNsb3NlKCk7CiAgICAgICAgICAgICAgZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50LnN0eWxlLm92ZXJmbG93WSA9ICdhdXRvJzsgLy/lhYHorrjlupXlsYJkaXbmu5rliqgKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICBhZGRSZXBvcnQoZm9ybURhdGEpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgICAgICAgY29uc29sZS5sb2coIj09PWFkZFJlcG9ydD0+Pj4iKTsKICAgICAgICAgICAgICAvL3RoaXMubXNnU3VjY2Vzcygi5paw5aKe5oiQ5YqfIik7CiAgICAgICAgICAgICAgZm9ybURhdGEucHJvamVjdElkID0gcmVzcG9uc2UuZGF0YTsKICAgICAgICAgICAgICB0aGF0LmZvcm0ucHJvamVjdElkID0gcmVzcG9uc2UuZGF0YTsKICAgICAgICAgICAgICB0aGF0LnN0YXJ0Rmxvdyhmb3JtRGF0YSk7CiAgICAgICAgICAgICAgc2V0VGltZW91dChmdW5jdGlvbiAoKSB7CiAgICAgICAgICAgICAgICBsb2FkaW5nLmNsb3NlKCk7CiAgICAgICAgICAgICAgICBkb2N1bWVudC5kb2N1bWVudEVsZW1lbnQuc3R5bGUub3ZlcmZsb3dZID0gJ2F1dG8nOyAvL+WFgeiuuOW6leWxgmRpdua7muWKqAogICAgICAgICAgICAgIH0sIDEwMDApOwogICAgICAgICAgICB9KS5jYXRjaChmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgICAgICAgY29uc29sZS5sb2cocmVzKTsKICAgICAgICAgICAgICBsb2FkaW5nLmNsb3NlKCk7CiAgICAgICAgICAgICAgZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50LnN0eWxlLm92ZXJmbG93WSA9ICdhdXRvJzsgLy/lhYHorrjlupXlsYJkaXbmu5rliqgKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICBzdGFydEZsb3c6IGZ1bmN0aW9uIHN0YXJ0Rmxvdyhmb3JtRGF0YSkgewogICAgICAvL+mhueebruWMuuWfnwogICAgICAvL3ZhciBhcmVhID0gZm9ybURhdGEuZGlzdHJpY3Q7CiAgICAgIC8v55So5oi35Yy65Z+fIDYtMTHkv67mlLnvvIzmoLnmja7nlKjmiLfmiYDlnKjljLrln5/liKTmlq0KICAgICAgdmFyIHZhcmlhYmxlcyA9IHt9OwogICAgICB2YXJpYWJsZXMuUFJPQ0VTU19BUkVBID0gdGhpcy4kc3RvcmUuc3RhdGUudXNlci5wcm92aW5jZTsKICAgICAgLy/mmK/lkKbnnIHotJ/otKPkurrop5LoibIKICAgICAgaWYgKHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIucm9sZXMgJiYgdGhpcy4kc3RvcmUuc3RhdGUudXNlci5yb2xlcy5pbmNsdWRlcygicHJvdmluY2VfYWRtaW4iKSkgewogICAgICAgIHZhcmlhYmxlcy5pc01hbmFnZSA9IDE7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdmFyaWFibGVzLmlzTWFuYWdlID0gMDsKICAgICAgfQogICAgICAvL+aWsOWinuWFqOi1sOaKpeWkh+WuoeaJuea1geeoiwogICAgICAvLyBpZihmb3JtRGF0YS5vcGVyYXRpb25UeXBlID09ICcyJyl7CiAgICAgIC8vICAgdmFyaWFibGVzLmlzQXV0aCA9IHRydWU7CiAgICAgIC8vIH1lbHNlewogICAgICAvLyAgIHZhcmlhYmxlcy5pc0F1dGggPSBmYWxzZTsKICAgICAgLy8gfQogICAgICAvL3ZhcmlhYmxlcy5pc0F1dGggPSBmYWxzZTsKICAgICAgdmFyaWFibGVzLkJVU0lORVNTS0VZID0gZm9ybURhdGEucHJvamVjdElkOwogICAgICB2YXIgdGFza05hbWUgPSAi6aG555uu5oql5aSHIjsKICAgICAgaWYgKGZvcm1EYXRhLm9wZXJhdGlvblR5cGUgPT0gJzInKSB7CiAgICAgICAgdGFza05hbWUgPSAi6aG555uu5o6I5p2DIjsKICAgICAgfQogICAgICB0aGlzLiRyZWZzWydmbG93J10uc3RhcnRGbG93KGZvcm1EYXRhLnByb2plY3RJZCwgdGFza05hbWUsIHZhcmlhYmxlcyk7CiAgICB9LAogICAgZG93bmxvYWRTUVM6IGZ1bmN0aW9uIGRvd25sb2FkU1FTKCkgewogICAgICB0aGlzLmRvd25sb2FkKCLmtbfkvbPpm4blm6It5o6I5p2D5LmmLmRvY3giLCBmYWxzZSk7CiAgICB9LAogICAgZG93bmxvYWRDUkg6IGZ1bmN0aW9uIGRvd25sb2FkQ1JIKCkgewogICAgICB0aGlzLmRvd25sb2FkKCLmtbfkvbPpm4blm6It5ZSu5ZCO5pyN5Yqh5om/6K+65Ye9LmRvYyIsIGZhbHNlKTsKICAgIH0sCiAgICBoYW5kbGVDaGFuZ2U6IGZ1bmN0aW9uIGhhbmRsZUNoYW5nZSh2YWx1ZSkgewogICAgICBpZiAoIXZhbHVlIHx8IHZhbHVlLmxlbmd0aCA9PSAwKSB7CiAgICAgICAgdGhpcy5zZWxlY3RlZE9wdGlvbnMgPSBudWxsOwogICAgICAgIHRoaXMuZm9ybS5wcm92aW5jZSA9IHVuZGVmaW5lZDsKICAgICAgICB0aGlzLmZvcm0uZGlzdHJpY3QgPSB1bmRlZmluZWQ7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIHRoaXMuc2VsZWN0ZWRPcHRpb25zID0gdmFsdWU7CiAgICAgIHZhciB0eHQgPSAiIjsKICAgICAgdmFsdWUuZm9yRWFjaChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgIHR4dCArPSBDb2RlVG9UZXh0W2l0ZW1dICsgIi8iOwogICAgICB9KTsKICAgICAgaWYgKHR4dC5sZW5ndGggPiAxKSB7CiAgICAgICAgdHh0ID0gdHh0LnN1YnN0cmluZygwLCB0eHQubGVuZ3RoIC0gMSk7CiAgICAgICAgdGhpcy5mb3JtLnByb3ZpbmNlID0gdHh0OwogICAgICAgIHRoaXMuZm9ybS5kaXN0cmljdCA9IHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIucHJvdmluY2U7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy5mb3JtLnByb3ZpbmNlID0gdW5kZWZpbmVkOwogICAgICAgIHRoaXMuZm9ybS5kaXN0cmljdCA9IHVuZGVmaW5lZDsKICAgICAgfQogICAgfSwKICAgIGhhbmRsZVF1ZXJ5Q2l0eUNoYW5nZTogZnVuY3Rpb24gaGFuZGxlUXVlcnlDaXR5Q2hhbmdlKHZhbHVlKSB7CiAgICAgIHRoaXMucXVlcnlBcmVhID0gdmFsdWU7CiAgICAgIHZhciB0eHQgPSAiIjsKICAgICAgdmFsdWUuZm9yRWFjaChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgIHR4dCArPSBDb2RlVG9UZXh0W2l0ZW1dICsgIi8iOwogICAgICB9KTsKICAgICAgaWYgKHR4dC5sZW5ndGggPiAxKSB7CiAgICAgICAgdHh0ID0gdHh0LnN1YnN0cmluZygwLCB0eHQubGVuZ3RoIC0gMSk7CiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wcm92aW5jZSA9IHR4dDsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnByb3ZpbmNlID0gdW5kZWZpbmVkOwogICAgICB9CiAgICB9LAogICAgLyoqIOWuoeaJuSAqL2hhbmRsZUNvbXBsZXRlOiBmdW5jdGlvbiBoYW5kbGVDb21wbGV0ZSgpIHsKICAgICAgdGhpcy4kcmVmc1snZmxvdyddLmhhbmRsZUNvbXBsZXRlKCk7CiAgICB9LAogICAgLyoqIOmAgOWbniAqL2hhbmRsZVJldHVybjogZnVuY3Rpb24gaGFuZGxlUmV0dXJuKCkgewogICAgICB0aGlzLiRyZWZzWydmbG93J10uaGFuZGxlUmV0dXJuKCk7CiAgICB9LAogICAgLyoqIOi/lOWbnumhtemdoiAqL2dvQmFjazogZnVuY3Rpb24gZ29CYWNrKCkgewogICAgICAvLyDlhbPpl63lvZPliY3moIfnrb7pobXlubbov5Tlm57kuIrkuKrpobXpnaIKICAgICAgdGhpcy4kc3RvcmUuZGlzcGF0Y2goInRhZ3NWaWV3L2RlbFZpZXciLCB0aGlzLiRyb3V0ZSk7CiAgICAgIHRoaXMuJHJvdXRlci5nbygtMSk7CiAgICB9LAogICAgcmVtb3ZlRG9tYWluOiBmdW5jdGlvbiByZW1vdmVEb21haW4oaW5kZXgpIHsKICAgICAgaWYgKGluZGV4ICE9PSAtMSkgewogICAgICAgIHRoaXMuYXV0aENvbXBhbnlzLnNwbGljZShpbmRleCwgMSk7CiAgICAgIH0KICAgIH0sCiAgICBhZGREb21haW46IGZ1bmN0aW9uIGFkZERvbWFpbigpIHsKICAgICAgdGhpcy5hdXRoQ29tcGFueXMucHVzaCh7CiAgICAgICAgdmFsdWU6ICcnLAogICAgICAgIGtleTogRGF0ZS5ub3coKQogICAgICB9KTsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["getReport", "addReport", "updateReport", "checkNameUnique", "FileUpload", "flowable", "regionData", "CodeToText", "TextToCode", "print", "name", "components", "data", "that", "infoTypeValueVali", "rule", "value", "callback", "form", "infoType", "indexOf", "scanFile", "Error", "infoTypeValueVali2", "send<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "projectName", "test", "projectId", "then", "response", "codeVali", "projectNo", "openDateVali", "openDate", "hang<PERSON>ate<PERSON><PERSON>", "hangDate", "loading", "operationTypeOptions", "auditStatusOptions", "editStatusOptions", "biddingTypeOptions", "modelOptions", "modelOption1", "requireInfoOptions", "requireInfoOption1", "infoTypeOptions", "belongProvinceOptions", "belongProvinceOptions1", "afterSaleYearOptions", "afterSaleYearOptions1", "specOptions", "specOption1", "rules", "operationType", "required", "message", "validator", "trigger", "address", "biddingCompany", "afterSaleYear", "belongProvince", "distributor", "model", "spec", "province", "biddingContact", "authContact", "options", "selectedOptions", "queryArea", "finished", "taskId", "undefined", "procInsId", "businessKey", "audit", "formEdit", "authCompanys", "activated", "reset", "$route", "query", "edit", "getReportInfo", "console", "log", "created", "_this", "getDicts", "opt", "for<PERSON>ach", "elem", "index", "obj", "label", "dict<PERSON><PERSON>l", "dict<PERSON><PERSON>ue", "push", "methods", "auditStatus", "rejectReason", "city", "district", "editStatus", "belongUser", "biddingType", "budgetMoney", "authCompany", "biddingNet", "area", "authFile", "afterSaleFile", "requireInfo", "mailInfo", "expressInfo", "remark", "spare1", "spare2", "resetForm", "handleSelectionChange", "selection", "ids", "map", "item", "single", "length", "multiple", "info", "_this2", "split", "provinces", "citys", "code", "submitForm", "_this3", "$refs", "validate", "valid", "emailReg", "$message", "error", "formStr", "JSON", "stringify", "formData", "parse", "join", "array", "Array", "e", "$loading", "lock", "text", "spinner", "background", "target", "document", "querySelector", "documentElement", "style", "overflowY", "taskComplete", "startFlow", "setTimeout", "close", "catch", "res", "variables", "PROCESS_AREA", "$store", "state", "user", "roles", "includes", "isManage", "BUSINESSKEY", "taskName", "downloadSQS", "download", "downloadCRH", "handleChange", "txt", "substring", "handleQueryCityChange", "queryParams", "handleComplete", "handleReturn", "goBack", "dispatch", "$router", "go", "removeDomain", "splice", "addDomain", "key", "Date", "now"], "sources": ["src/views/project/report/form.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-card class=\"box-card\">\r\n      <div slot=\"header\" class=\"clearfix\">\r\n        <span class=\"el-icon-document\">项目报备流程</span>\r\n        <span style=\"float: right;\">\r\n          <el-button icon=\"el-icon-edit-outline\" type=\"success\" v-if=\"audit\" @click=\"handleComplete\">审批</el-button>\r\n          <el-button icon=\"el-icon-refresh-left\" type=\"warning\" v-if=\"audit\" @click=\"handleReturn\">退回</el-button>\r\n          <el-button type=\"primary\" @click=\"goBack\">返回</el-button>\r\n        </span>\r\n\r\n      </div>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" :disabled=\"!formEdit\" label-width=\"120px\">\r\n\r\n        <!-- <el-row> -->\r\n        <!-- <el-col :span=\"12\">\r\n            <el-form-item label=\"编辑状态\">\r\n              <el-radio-group v-model=\"form.editStatus\">\r\n                <el-radio\r\n                  v-for=\"dict in editStatusOptions\"\r\n                  :key=\"dict.dictValue\"\r\n                  :label=\"dict.dictValue\"\r\n                  >{{ dict.dictLabel }}</el-radio\r\n                >\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col> -->\r\n        <!-- <el-col :span=\"12\">\r\n            <el-form-item label=\"所属用户\" prop=\"belongUser\">\r\n              <el-select v-model=\"form.belongUser\" placeholder=\"请选择所属用户\">\r\n                <el-option label=\"请选择字典生成\" value=\"\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col> -->\r\n        <!-- </el-row> -->\r\n        <!-- <el-row>\r\n          <el-col :span=\"20\">\r\n            <el-form-item label=\"驳回原因\" prop=\"rejectReason\">\r\n              <el-input\r\n                v-model=\"form.rejectReason\"\r\n                type=\"textarea\"\r\n                placeholder=\"请输入内容\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row> -->\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"项目编号\" prop=\"projectNo\">\r\n              <el-input v-model=\"form.projectNo\" placeholder=\"若无编号则为当前时间(年月日时间)\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"项目名称\" prop=\"projectName\">\r\n              <el-input v-model=\"form.projectName\" placeholder=\"请输入项目名称\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"项目所在地\" prop=\"province\">\r\n              <el-cascader :options=\"options\" clearable :props=\"{ expandTrigger: 'hover' }\" v-model=\"selectedOptions\"\r\n                @change=\"handleChange\">\r\n              </el-cascader>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"详细地址\" prop=\"address\">\r\n              <el-input v-model=\"form.address\" placeholder=\"请输入详细地址\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"项目所属省份\" prop=\"belongProvince\">\r\n              <el-select v-model=\"form.belongProvince\" clearable placeholder=\"请选择所属省份\">\r\n                <el-option v-for=\"item in belongProvinceOptions\" :key=\"item.value\" :label=\"item.label\"\r\n                  :value=\"item.value\">\r\n                </el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <!-- <el-form-item label=\"招标单位联系人/联系电话\" prop=\"biddingContact\">\r\n              <el-input v-model=\"form.biddingContact\" placeholder=\"请输入联系人/联系电话\" />\r\n            </el-form-item> -->\r\n            <el-form-item label=\"招标单位\" prop=\"biddingCompany\">\r\n              <el-input v-model=\"form.biddingCompany\" placeholder=\"请输入招标单位\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"挂网日期\" prop=\"hangDate\">\r\n              <el-input v-model=\"form.hangDate\" placeholder=\"请输入挂网日期，没有请写无，日期格式XXXX（年）-XX（月）-XX（日\" />\r\n              <!-- <el-date-picker clearable size=\"small\" v-model=\"form.hangDate\" type=\"date\" value-format=\"yyyy-MM-dd\"\r\n                placeholder=\"选择挂网日期\">\r\n              </el-date-picker> -->\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"开标日期\" prop=\"openDate\">\r\n              <el-input v-model=\"form.openDate\" placeholder=\"请输入开标日期，没有请写无，日期格式XXXX（年）-XX（月）-XX（日\" />\r\n              <!-- <el-date-picker clearable size=\"small\" v-model=\"form.openDate\" type=\"date\" value-format=\"yyyy-MM-dd\"\r\n                placeholder=\"选择开标日期\">\r\n              </el-date-picker> -->\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"所属经销商\" prop=\"distributor\">\r\n              <el-input v-model=\"form.distributor\" placeholder=\"请输入经销商\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <!-- <el-form-item label=\"招标单位联系人/联系电话\" prop=\"biddingContact\">\r\n              <el-input v-model=\"form.biddingContact\" placeholder=\"请输入联系人/联系电话\" />\r\n            </el-form-item> -->\r\n            <el-form-item label=\"售后年限\" prop=\"afterSaleYear\">\r\n              <el-select v-model=\"form.afterSaleYear\" clearable placeholder=\"请选择售后年限\">\r\n                <el-option v-for=\"item in afterSaleYearOptions\" :key=\"item.value\" :label=\"item.label\"\r\n                  :value=\"item.value\">\r\n                </el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <!-- <el-col :span=\"12\">\r\n            <el-form-item label=\"预算金额\" prop=\"budgetMoney\">\r\n              <el-input\r\n                type=\"number\"\r\n                v-model=\"form.budgetMoney\"\r\n                placeholder=\"请输入预算金额\"\r\n              />\r\n            </el-form-item>\r\n          </el-col> -->\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"被授权公司\" prop=\"authCompany\">\r\n              <el-input v-model=\"form.authCompany\" placeholder=\"请输入授权公司\" />\r\n              <el-link @click=\"addDomain\" type=\"primary\" :disabled=\"!formEdit\">添加</el-link>\r\n            </el-form-item>\r\n            <el-form-item v-for=\"(company, index) in authCompanys\" :label=\"'被授权公司' + (index + 1)\" :key=\"company.key\">\r\n              <el-input v-model=\"company.value\" :placeholder=\"'被授权公司' + (index + 1)\" style=\"max-width:300px\" />\r\n              <el-link @click=\"removeDomain(index)\" type=\"primary\" :disabled=\"!formEdit\">删除</el-link>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"授权公司联系人/联系电话\" prop=\"authContact\">\r\n              <el-input v-model=\"form.authContact\" placeholder=\"请输入授权公司联系人/联系电话\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <!-- <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"招标信息公布网站\" prop=\"biddingNet\">\r\n              <el-input\r\n                v-model=\"form.biddingNet\"\r\n                placeholder=\"请输入招标信息公布网站\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"招标单位\" prop=\"biddingCompany\">\r\n              <el-input\r\n                v-model=\"form.biddingCompany\"\r\n                placeholder=\"请输入招标单位\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row> -->\r\n        <!-- <el-row v-if=\"form.operationType == 2\">\r\n          <el-col :span=\"20\">\r\n            <el-form-item label=\"模板下载\">\r\n              <el-col :span=\"8\">\r\n                <el-link @click=\"downloadSQS\" type=\"primary\" :disabled=\"!formEdit\">海佳集团-授权书.docx</el-link>\r\n              </el-col>\r\n              <el-col :span=\"8\">\r\n                <el-link @click=\"downloadCRH\" type=\"primary\" :disabled=\"!formEdit\">海佳集团-售后服务承诺函.docx</el-link>\r\n              </el-col>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row> -->\r\n        <!-- <el-form-item label=\"授权书\" v-if=\"form.operationType == 2\" :required=\"form.operationType == 2\">\r\n          <fileUpload v-model=\"form.authFile\" :fileType=\"['doc', 'docx']\" />\r\n        </el-form-item> -->\r\n        <el-form-item label=\"其余附件\">\r\n          <span style=\"color: red;\">请勿上传项目授权书、售后声明函</span>\r\n          <fileUpload v-model=\"form.afterSaleFile\" :fileType=\"['doc', 'docx']\" />\r\n        </el-form-item>\r\n        <el-row>\r\n          <el-col :span=\"20\">\r\n            <el-form-item label=\"投标产品型号\" prop=\"model\" :required=\"true\">\r\n              <el-cascader class=\"mobile-width\" style=\"width: 700px\" v-model=\"form.model\" placeholder=\"可输入产品型号搜索\"\r\n                :options=\"modelOptions\" :props=\"{ multiple: true }\" clearable filterable></el-cascader>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"20\">\r\n            <el-form-item label=\"投标产品规格\" prop=\"spec\" :required=\"true\">\r\n              <el-cascader class=\"mobile-width\" style=\"width: 700px\" v-model=\"form.spec\" placeholder=\"可输入产品规格搜索\"\r\n                :options=\"specOptions\" :props=\"{ multiple: true }\" clearable filterable></el-cascader>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"安装面积\" prop=\"area\">\r\n              <el-input v-model=\"form.area\" type=\"number\" placeholder=\"请输入安装面积\">\r\n                <template slot=\"append\">m²</template>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"20\">\r\n            <el-form-item label=\"所需资料\">\r\n              <el-cascader class=\"mobile-width\" style=\"width: 700px\" v-model=\"form.requireInfo\" placeholder=\"可输入资料类型搜索\"\r\n                :options=\"requireInfoOptions\" :props=\"{ multiple: true }\" clearable filterable></el-cascader>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <!-- <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"资料类型\">\r\n              <el-checkbox-group v-model=\"form.infoType\">\r\n                <el-checkbox\r\n                  v-for=\"dict in infoTypeOptions\"\r\n                  :key=\"dict.dictValue\"\r\n                  :label=\"dict.dictValue\"\r\n                >\r\n                  {{ dict.dictLabel }}\r\n                </el-checkbox>\r\n              </el-checkbox-group>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row> -->\r\n        <el-row>\r\n          <el-form-item label=\"资料接收方式\" prop=\"infoType\" :required=\"true\">\r\n            <el-checkbox-group v-model=\"form.infoType\" class=\"info-type\">\r\n              <!-- 选项A -->\r\n              <el-row style=\"display:flex;margin-bottom: 22px;\">\r\n                <el-col :span=\"12\" style=\"display:flex;\">\r\n                  <el-checkbox label=\"1\" style=\"margin-left:20px;margin-right:10px !important;\">邮件</el-checkbox>\r\n                  <el-form-item prop=\"scanFile\">\r\n                    <el-input v-model=\"form.scanFile\" placeholder=\"请输入邮箱地址\" style=\"width:300px;\"\r\n                      type=\"email\"></el-input>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"12\">\r\n                  <el-form-item label=\"邮件发送信息\" prop=\"mailInfo\">\r\n                    <el-input v-model=\"form.mailInfo\" placeholder=\"请输入邮件发送信息\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <!-- 选项B  -->\r\n              <el-row style=\"display:flex;margin-bottom: 22px;\">\r\n                <el-col :span=\"12\" style=\"display:flex;\">\r\n                  <el-checkbox label=\"2\" style=\"margin-left:20px;margin-right:10px !important;\">邮寄</el-checkbox>\r\n                  <el-form-item prop=\"sendAddress\">\r\n                    <el-input v-model=\"form.sendAddress\" placeholder=\"请输入收件地址\" style=\"width:300px;\"></el-input>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"12\">\r\n                  <el-form-item label=\"快递单号\" prop=\"expressInfo\">\r\n                    <el-input v-model=\"form.expressInfo\" placeholder=\"请输入快递单号\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </el-checkbox-group>\r\n          </el-form-item>\r\n        </el-row>\r\n        <!-- <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"邮件发送信息\" prop=\"mailInfo\">\r\n              <el-input\r\n                v-model=\"form.mailInfo\"\r\n                placeholder=\"请输入邮件发送信息\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"快递单号\" prop=\"expressInfo\">\r\n              <el-input\r\n                v-model=\"form.expressInfo\"\r\n                placeholder=\"请输入快递单号\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row> -->\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"操作类型\" prop=\"operationType\">\r\n              <el-radio-group v-model=\"form.operationType\">\r\n                <el-radio v-for=\"dict in operationTypeOptions\" :key=\"dict.dictValue\"\r\n                  :label=\"dict.dictValue\">{{ dict.dictLabel }}</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col>\r\n          <!-- <el-col :span=\"12\">\r\n            <el-form-item label=\"审核状态\">\r\n              <el-radio-group v-model=\"form.auditStatus\">\r\n                <el-radio\r\n                  v-for=\"dict in auditStatusOptions\"\r\n                  :key=\"dict.dictValue\"\r\n                  :label=\"dict.dictValue\"\r\n                  >{{ dict.dictLabel }}</el-radio\r\n                >\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col> -->\r\n        </el-row>\r\n        <el-form-item label=\"备注\" prop=\"remark\">\r\n          <el-input v-model=\"form.remark\" type=\"textarea\" placeholder=\"请输入备注\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <el-col :span=\"16\" :offset=\"8\" v-if=\"formEdit\">\r\n        <div style=\"margin-left:10%;margin-bottom: 20px;font-size: 14px;\">\r\n          <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n          <el-button @click=\"goBack\">取 消</el-button>\r\n        </div>\r\n      </el-col>\r\n    </el-card>\r\n    <flowable :key=\"businessKey\" ref=\"flow\" procDefKey=\"process_project_report\" :procInsId=\"procInsId\" :taskId=\"taskId\"\r\n      :finished=\"finished\"></flowable>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  getReport,\r\n  addReport,\r\n  updateReport,\r\n  checkNameUnique\r\n} from \"@/api/project/report\";\r\nimport FileUpload from \"@/components/FileUpload\";\r\nimport flowable from '@/views/flowable/task/record/index'\r\nimport { regionData, CodeToText, TextToCode } from \"element-china-area-data\";\r\nimport print from \"print-js\";\r\nexport default {\r\n  name: \"Report\",\r\n  components: {\r\n    flowable,\r\n    FileUpload,\r\n    print\r\n  },\r\n  data() {\r\n    var that = this;\r\n    var infoTypeValueVali = (rule, value, callback) => {\r\n      if (that.form.infoType.indexOf('1') >= 0 && !that.form.scanFile) {\r\n        callback(new Error(\"邮箱地址必填\"));\r\n        return;\r\n      }\r\n      callback();\r\n    };\r\n    var infoTypeValueVali2 = (rule, value, callback) => {\r\n      if (that.form.infoType.indexOf('2') >= 0 && !that.form.sendAddress) {\r\n        callback(new Error(\"收件地址必填\"));\r\n        return;\r\n      }\r\n      callback();\r\n    };\r\n    var nameVali = (rule, value, callback) => {\r\n      if (!that.form.projectName) {\r\n        callback(new Error(\"项目名称必填\"));\r\n      } else {\r\n        if (/\\s+/g.test(that.form.projectName)) {\r\n          callback(new Error(\"项目名称不规范\"));\r\n          return;\r\n        }\r\n        checkNameUnique({ projectName: that.form.projectName, projectId: that.form.projectId }).then((response) => {\r\n          if (response.data == 0) {\r\n            callback();\r\n          } else {\r\n            callback(new Error(\"项目名称已存在\"));\r\n          }\r\n        })\r\n      }\r\n    };\r\n    var codeVali = (rule, value, callback) => {\r\n      if (!that.form.projectNo) {\r\n        callback(new Error(\"项目编号必填\"));\r\n      } else if (/\\s+/g.test(that.form.projectNo)) {\r\n        callback(new Error(\"项目编号不规范\"));\r\n        return;\r\n      }\r\n      callback();\r\n    };\r\n    var openDateVali = (rule, value, callback) => {\r\n      if (!that.form.openDate) {\r\n        callback(new Error(\"开标日期必填\"));\r\n        return;\r\n      } else if (value === \"无\") {\r\n        callback();\r\n        return;\r\n      } else if (!/^\\d{4}-\\d{2}-\\d{2}$/.test(value)) {\r\n        callback(new Error(\"开标日期格式不合法，示例2025-01-01\"));\r\n        return;\r\n      }\r\n      callback();\r\n    };\r\n    var hangDateVali = (rule, value, callback) => {\r\n      if (!that.form.hangDate) {\r\n        callback(new Error(\"挂网日期必填\"));\r\n        return;\r\n      } else if (value === \"无\") {\r\n        callback();\r\n        return;\r\n      } else if (!/^\\d{4}-\\d{2}-\\d{2}$/.test(value)) {\r\n        callback(new Error(\"挂网日期格式不合法，示例2025-01-01\"));\r\n        return;\r\n      }\r\n      callback();\r\n    };\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 操作类型字典\r\n      operationTypeOptions: [],\r\n      // 审核状态字典\r\n      auditStatusOptions: [],\r\n      // 编辑状态字典\r\n      editStatusOptions: [],\r\n      // 招标方式字典\r\n      biddingTypeOptions: [],\r\n      // 投标产品型号字典\r\n      modelOptions: [],\r\n      modelOption1: [],\r\n      // 所需资料字典\r\n      requireInfoOptions: [],\r\n      requireInfoOption1: [],\r\n      // 资料类型字典\r\n      infoTypeOptions: [],\r\n      // 所属省份字典\r\n      belongProvinceOptions: [],\r\n      belongProvinceOptions1: [],\r\n      // 售后年限\r\n      afterSaleYearOptions: [],\r\n      afterSaleYearOptions1: [],\r\n      specOptions: [],\r\n      specOption1: [],\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      // 表单校验\r\n      rules: {\r\n        operationType: [{ required: true, message: \"操作类型必选\" }],\r\n        projectNo: [\r\n          { validator: codeVali, required: true, trigger: \"blur\" },\r\n        ],\r\n        projectName: [\r\n          { validator: nameVali, required: true, trigger: \"blur\" },\r\n        ],\r\n        address: [\r\n          { required: true, message: \"详细地址不能为空\", trigger: \"blur\" },\r\n        ],\r\n        biddingCompany: [\r\n          { required: true, message: \"招标单位不能为空\", trigger: \"blur\" },\r\n        ],\r\n        openDate: [\r\n          { required: true, validator: openDateVali, trigger: \"blur\" },\r\n        ],\r\n        afterSaleYear: [\r\n          { required: true, message: \"售后年限不能为空\", trigger: \"blur\" },\r\n        ],\r\n        hangDate: [\r\n          { required: true, validator: hangDateVali, trigger: \"blur\" },\r\n        ],\r\n        belongProvince: [\r\n          { required: true, message: \"售后年限不能为空\", trigger: \"blur\" },\r\n        ],\r\n        distributor: [\r\n          { required: true, message: \"所属经销商不能为空\", trigger: \"blur\" },\r\n        ],\r\n        scanFile: [\r\n          { validator: infoTypeValueVali, trigger: \"blur\" },\r\n        ],\r\n        sendAddress: [\r\n          { validator: infoTypeValueVali2, trigger: \"blur\" },\r\n        ],\r\n        model: [\r\n          { required: true, message: \"投标产品型号必选\" },\r\n        ],\r\n        spec: [\r\n          { required: true, message: \"投标产品规格必选\" },\r\n        ],\r\n        province: [\r\n          { required: true, message: \"项目所在地必选\" },\r\n        ],\r\n        infoType: [\r\n          { required: true, message: \"资料接收方式必选\", trigger: \"change\" },\r\n        ],\r\n        biddingContact: [\r\n          { required: true, message: \"招标单位联系人/联系电话必填\" },\r\n        ],\r\n        authContact: [\r\n          { required: true, message: \"授权公司联系人/联系电话必填\" },\r\n        ]\r\n      },\r\n      options: regionData,\r\n      selectedOptions: [],\r\n      queryArea: [],\r\n      //工作流参数\r\n      finished: 'false',\r\n      taskId: undefined,\r\n      procInsId: undefined,\r\n      businessKey: undefined,\r\n      audit: false,\r\n      formEdit: false,\r\n      //工作流参数end\r\n      authCompanys: [],\r\n    };\r\n  },\r\n  activated() {\r\n\r\n    this.reset();\r\n    //this.form.projectNo = moment(new Date()).format('YYYYMMDDHHmm');\r\n    this.taskId = this.$route.query && this.$route.query.taskId;\r\n    this.procInsId = this.$route.query && this.$route.query.procInsId;\r\n    this.finished = this.$route.query && this.$route.query.finished;\r\n    this.businessKey = this.$route.query && this.$route.query.businessKey;\r\n    let edit = this.$route.query && this.$route.query.formEdit;\r\n    if (edit == \"true\") {\r\n      this.formEdit = true;\r\n    } else {\r\n      this.formEdit = false;\r\n    }\r\n    if (this.businessKey) {\r\n      if (this.finished == \"false\" && !this.formEdit) {\r\n        this.audit = true;\r\n      }\r\n      this.getReportInfo(this.businessKey);\r\n    }\r\n    console.log(\"========project=========>activated>formEdit>>\" + this.formEdit);\r\n  },\r\n  created() {\r\n\r\n    this.reset();\r\n    //this.form.projectNo = moment(new Date()).format('YYYYMMDDHHmm');\r\n    this.taskId = this.$route.query && this.$route.query.taskId;\r\n    this.procInsId = this.$route.query && this.$route.query.procInsId;\r\n    this.finished = this.$route.query && this.$route.query.finished;\r\n    this.businessKey = this.$route.query && this.$route.query.businessKey;\r\n    let edit = this.$route.query && this.$route.query.formEdit;\r\n    if (edit == \"true\") {\r\n      this.formEdit = true;\r\n    } else {\r\n      this.formEdit = false;\r\n    }\r\n    if (this.businessKey) {\r\n      if (this.finished == \"false\" && !this.formEdit) {\r\n        this.audit = true;\r\n      }\r\n      this.getReportInfo(this.businessKey);\r\n    }\r\n    // this.audit = true;\r\n    console.log(\"=========project========>created>>formEdit>\" + this.formEdit);\r\n\r\n    this.getDicts(\"pr_operation_type\").then((response) => {\r\n      this.operationTypeOptions = response.data;\r\n    });\r\n    this.getDicts(\"pr_audit_status\").then((response) => {\r\n      this.auditStatusOptions = response.data;\r\n    });\r\n    this.getDicts(\"pr_edit_status\").then((response) => {\r\n      this.editStatusOptions = response.data;\r\n    });\r\n    // this.getDicts(\"pr_bidding_type\").then((response) => {\r\n    //   this.biddingTypeOptions = response.data;\r\n    // });\r\n    this.getDicts(\"pr_model\").then((response) => {\r\n      this.modelOption1 = response.data;\r\n      var opt = [];\r\n      response.data.forEach((elem, index) => {\r\n        var obj = {};\r\n        obj.label = elem.dictLabel;\r\n        obj.value = elem.dictValue;\r\n        opt.push(obj);\r\n      });\r\n      this.modelOptions = opt;\r\n    });\r\n    this.getDicts(\"pr_spec\").then((response) => {\r\n      this.specOption1 = response.data;\r\n      var opt = [];\r\n      response.data.forEach((elem, index) => {\r\n        var obj = {};\r\n        obj.label = elem.dictLabel;\r\n        obj.value = elem.dictValue;\r\n        opt.push(obj);\r\n      });\r\n      this.specOptions = opt;\r\n    });\r\n    this.getDicts(\"pr_info\").then((response) => {\r\n      this.requireInfoOption1 = response.data;\r\n      var opt = [];\r\n      response.data.forEach((elem, index) => {\r\n        var obj = {};\r\n        obj.label = elem.dictLabel;\r\n        obj.value = elem.dictValue;\r\n        opt.push(obj);\r\n      });\r\n      this.requireInfoOptions = opt;\r\n    });\r\n    this.getDicts(\"pr_province\").then((response) => {\r\n      this.belongProvinceOptions1 = response.data;\r\n      var opt = [];\r\n      response.data.forEach((elem, index) => {\r\n        var obj = {};\r\n        obj.label = elem.dictLabel;\r\n        obj.value = elem.dictValue;\r\n        opt.push(obj);\r\n      });\r\n      this.belongProvinceOptions = opt;\r\n    });\r\n    this.getDicts(\"pr_after_sale_year\").then((response) => {\r\n      this.afterSaleYearOptions1 = response.data;\r\n      var opt = [];\r\n      response.data.forEach((elem, index) => {\r\n        var obj = {};\r\n        obj.label = elem.dictLabel;\r\n        obj.value = elem.dictValue;\r\n        opt.push(obj);\r\n      });\r\n      this.afterSaleYearOptions = opt;\r\n    });\r\n    this.getDicts(\"pr_data_type\").then((response) => {\r\n      this.infoTypeOptions = response.data;\r\n    });\r\n    //默认报备\r\n    this.form.operationType = '1';\r\n  },\r\n  methods: {\r\n\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        projectId: null,\r\n        projectNo: null,\r\n        projectName: null,\r\n        operationType: 1,\r\n        auditStatus: \"1\",\r\n        rejectReason: null,\r\n        province: null,\r\n        city: null,\r\n        district: null,\r\n        address: null,\r\n        editStatus: \"0\",\r\n        belongUser: null,\r\n        biddingCompany: null,\r\n        openDate: null,\r\n        belongProvince: null,\r\n        afterSaleYear: null,\r\n        hangDate: null,\r\n        biddingType: null,\r\n        budgetMoney: null,\r\n        authCompany: null,\r\n        biddingNet: null,\r\n        distributor: null,\r\n        model: [],\r\n        spec: [],\r\n        area: null,\r\n        authFile: null,\r\n        afterSaleFile: null,\r\n        requireInfo: [],\r\n        infoType: [],\r\n        scanFile: null,\r\n        sendAddress: null,\r\n        mailInfo: null,\r\n        expressInfo: null,\r\n        remark: null,\r\n        spare1: null,\r\n        spare2: null,\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map((item) => item.projectId);\r\n      this.single = selection.length !== 1;\r\n      this.multiple = !selection.length;\r\n      console.info(selection);\r\n    },\r\n\r\n    /** 修改按钮操作 */\r\n    getReportInfo(projectId) {\r\n      getReport(projectId).then((response) => {\r\n        this.form = response.data;\r\n        if (this.form.model) this.form.model = this.form.model.split(\",\");\r\n        else this.form.model = [];\r\n        if (this.form.requireInfo)\r\n          this.form.requireInfo = this.form.requireInfo.split(\",\");\r\n        else this.form.requireInfo = [];\r\n        if (this.form.infoType)\r\n          this.form.infoType = this.form.infoType.split(\",\");\r\n        else this.form.infoType = [];\r\n        if (this.form.spec) this.form.spec = this.form.spec.split(\",\");\r\n        else this.form.spec = [];\r\n        var provinces = response.data.province;\r\n        if (provinces.length > 0) {\r\n          var address = provinces.split(\"/\");\r\n          var citys = [];\r\n          // 省份\r\n          if (address.length > 0) citys.push(TextToCode[address[0]].code);\r\n          // 城市\r\n          if (address.length > 1)\r\n            citys.push(TextToCode[address[0]][address[1]].code);\r\n          // 地区\r\n          if (address.length > 2)\r\n            citys.push(TextToCode[address[0]][address[1]][address[2]].code);\r\n\r\n          this.selectedOptions = citys;\r\n        }\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      let that = this;\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n          if (this.form.infoType.indexOf('1') >= 0 && this.form.scanFile) {\r\n            let emailReg = /^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\\.[a-zA-Z0-9_-]+)+$/;\r\n            if (!emailReg.test(this.form.scanFile)) {\r\n              this.$message.error(\"资料接收方式邮箱格式错误\");\r\n              return;\r\n            }\r\n          }\r\n          // if (this.form.operationType == 2 && !this.form.authFile) {\r\n          //   this.$message.error(\"授权类型必需上传授权书\");\r\n          //   return;\r\n          // }\r\n          var formStr = JSON.stringify(this.form);\r\n          var formData = JSON.parse(formStr);\r\n          if (formData.model && formData.model.length > 0)\r\n            formData.model = formData.model.join(\",\");\r\n          else formData.model = undefined;\r\n          if (formData.requireInfo && formData.requireInfo.length > 0)\r\n            formData.requireInfo = formData.requireInfo.join(\",\");\r\n          else formData.requireInfo = undefined;\r\n          if (formData.infoType && formData.infoType.length > 0)\r\n            formData.infoType = formData.infoType.join(\",\");\r\n          else formData.infoType = undefined;\r\n          if (formData.spec && formData.spec.length > 0)\r\n            formData.spec = formData.spec.join(\",\");\r\n          else formData.spec = undefined;\r\n\r\n          //授权公司\r\n          if (this.authCompanys.length > 0) {\r\n            var array = new Array();\r\n            this.authCompanys.forEach(function (e) {\r\n              array.push(e.value);\r\n            })\r\n            formData.authCompany += \",\" + array.join(\",\")\r\n          }\r\n\r\n          const loading = this.$loading({\r\n            lock: true,//lock的修改符--默认是false\r\n            text: 'Loading',//显示在加载图标下方的加载文案\r\n            spinner: 'el-icon-loading',//自定义加载图标类名\r\n            background: 'rgba(0, 0, 0, 0.7)',//遮罩层颜色\r\n            target: document.querySelector('.main-container')//loadin覆盖的dom元素节点\r\n          });\r\n          document.documentElement.style.overflowY = 'hidden' //禁止底层div滚动\r\n\r\n          if (formData.projectId != null) {\r\n            updateReport(formData).then((response) => {\r\n              //this.msgSuccess(\"修改成功\");\r\n              if (that.businessKey) {\r\n                that.$refs['flow'].taskComplete(\"重新提交\");\r\n              } else {\r\n                that.startFlow(formData);\r\n              }\r\n              setTimeout(() => {\r\n                loading.close();\r\n                document.documentElement.style.overflowY = 'auto' //允许底层div滚动\r\n              }, 1000);\r\n            }).catch(res => {\r\n              console.log(res)\r\n              loading.close();\r\n              document.documentElement.style.overflowY = 'auto' //允许底层div滚动\r\n            });\r\n          } else {\r\n            addReport(formData).then((response) => {\r\n              console.log(\"===addReport=>>>\")\r\n              //this.msgSuccess(\"新增成功\");\r\n              formData.projectId = response.data;\r\n              that.form.projectId = response.data;\r\n              that.startFlow(formData);\r\n              setTimeout(() => {\r\n                loading.close();\r\n                document.documentElement.style.overflowY = 'auto' //允许底层div滚动\r\n              }, 1000);\r\n            }).catch(res => {\r\n              console.log(res)\r\n              loading.close();\r\n              document.documentElement.style.overflowY = 'auto' //允许底层div滚动\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    startFlow(formData) {\r\n      //项目区域\r\n      //var area = formData.district;\r\n      //用户区域 6-11修改，根据用户所在区域判断\r\n      var variables = {};\r\n      variables.PROCESS_AREA = this.$store.state.user.province;\r\n      //是否省负责人角色\r\n      if (this.$store.state.user.roles && this.$store.state.user.roles.includes(\"province_admin\")) {\r\n        variables.isManage = 1;\r\n      } else {\r\n        variables.isManage = 0;\r\n      }\r\n      //新增全走报备审批流程\r\n      // if(formData.operationType == '2'){\r\n      //   variables.isAuth = true;\r\n      // }else{\r\n      //   variables.isAuth = false;\r\n      // }\r\n      //variables.isAuth = false;\r\n      variables.BUSINESSKEY = formData.projectId;\r\n      var taskName = \"项目报备\";\r\n      if (formData.operationType == '2') {\r\n        taskName = \"项目授权\";\r\n      }\r\n      this.$refs['flow'].startFlow(formData.projectId, taskName, variables);\r\n    },\r\n    downloadSQS() {\r\n      this.download(\"海佳集团-授权书.docx\", false);\r\n    },\r\n    downloadCRH() {\r\n      this.download(\"海佳集团-售后服务承诺函.doc\", false);\r\n    },\r\n    handleChange(value) {\r\n      if (!value || value.length == 0) {\r\n        this.selectedOptions = null;\r\n        this.form.province = undefined;\r\n        this.form.district = undefined;\r\n        return\r\n      }\r\n      this.selectedOptions = value;\r\n      var txt = \"\";\r\n      value.forEach(function (item) {\r\n        txt += CodeToText[item] + \"/\";\r\n      });\r\n      if (txt.length > 1) {\r\n        txt = txt.substring(0, txt.length - 1);\r\n        this.form.province = txt;\r\n        this.form.district = this.$store.state.user.province;\r\n      } else {\r\n        this.form.province = undefined;\r\n        this.form.district = undefined;\r\n      }\r\n    },\r\n    handleQueryCityChange(value) {\r\n      this.queryArea = value;\r\n      var txt = \"\";\r\n      value.forEach(function (item) {\r\n        txt += CodeToText[item] + \"/\";\r\n      });\r\n      if (txt.length > 1) {\r\n        txt = txt.substring(0, txt.length - 1);\r\n        this.queryParams.province = txt;\r\n      } else {\r\n        this.queryParams.province = undefined;\r\n      }\r\n    },\r\n    /** 审批 */\r\n    handleComplete() {\r\n      this.$refs['flow'].handleComplete();\r\n    },\r\n    /** 退回 */\r\n    handleReturn() {\r\n      this.$refs['flow'].handleReturn();\r\n    },\r\n    /** 返回页面 */\r\n    goBack() {\r\n      // 关闭当前标签页并返回上个页面\r\n      this.$store.dispatch(\"tagsView/delView\", this.$route);\r\n      this.$router.go(-1)\r\n    },\r\n    removeDomain(index) {\r\n      if (index !== -1) {\r\n        this.authCompanys.splice(index, 1)\r\n      }\r\n    },\r\n    addDomain() {\r\n      this.authCompanys.push({\r\n        value: '',\r\n        key: Date.now()\r\n      });\r\n    }\r\n  },\r\n};\r\n</script>\r\n<style>\r\n.box-card {\r\n  width: 100%;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n@media screen and (max-width: 599px) {\r\n  .el-form .el-col {\r\n    width: 100% !important;\r\n  }\r\n\r\n  .info-type .el-row {\r\n    flex-direction: column;\r\n  }\r\n\r\n  .info-type .el-input {\r\n    width: 100% !important;\r\n  }\r\n\r\n  .mobile-width {\r\n    width: 100% !important;\r\n  }\r\n\r\n  .app-container {\r\n    padding: 0 !important;\r\n  }\r\n}\r\n</style>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6UA,SACAA,SAAA,EACAC,SAAA,EACAC,YAAA,EACAC,eAAA,QACA;AACA,OAAAC,UAAA;AACA,OAAAC,QAAA;AACA,SAAAC,UAAA,EAAAC,UAAA,EAAAC,UAAA;AACA,OAAAC,KAAA;AACA;EACAC,IAAA;EACAC,UAAA;IACAN,QAAA,EAAAA,QAAA;IACAD,UAAA,EAAAA,UAAA;IACAK,KAAA,EAAAA;EACA;EACAG,IAAA,WAAAA,KAAA;IACA,IAAAC,IAAA;IACA,IAAAC,iBAAA,YAAAA,kBAAAC,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,IAAAJ,IAAA,CAAAK,IAAA,CAAAC,QAAA,CAAAC,OAAA,eAAAP,IAAA,CAAAK,IAAA,CAAAG,QAAA;QACAJ,QAAA,KAAAK,KAAA;QACA;MACA;MACAL,QAAA;IACA;IACA,IAAAM,kBAAA,YAAAA,mBAAAR,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,IAAAJ,IAAA,CAAAK,IAAA,CAAAC,QAAA,CAAAC,OAAA,eAAAP,IAAA,CAAAK,IAAA,CAAAM,WAAA;QACAP,QAAA,KAAAK,KAAA;QACA;MACA;MACAL,QAAA;IACA;IACA,IAAAQ,QAAA,YAAAA,SAAAV,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,KAAAJ,IAAA,CAAAK,IAAA,CAAAQ,WAAA;QACAT,QAAA,KAAAK,KAAA;MACA;QACA,WAAAK,IAAA,CAAAd,IAAA,CAAAK,IAAA,CAAAQ,WAAA;UACAT,QAAA,KAAAK,KAAA;UACA;QACA;QACAnB,eAAA;UAAAuB,WAAA,EAAAb,IAAA,CAAAK,IAAA,CAAAQ,WAAA;UAAAE,SAAA,EAAAf,IAAA,CAAAK,IAAA,CAAAU;QAAA,GAAAC,IAAA,WAAAC,QAAA;UACA,IAAAA,QAAA,CAAAlB,IAAA;YACAK,QAAA;UACA;YACAA,QAAA,KAAAK,KAAA;UACA;QACA;MACA;IACA;IACA,IAAAS,QAAA,YAAAA,SAAAhB,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,KAAAJ,IAAA,CAAAK,IAAA,CAAAc,SAAA;QACAf,QAAA,KAAAK,KAAA;MACA,kBAAAK,IAAA,CAAAd,IAAA,CAAAK,IAAA,CAAAc,SAAA;QACAf,QAAA,KAAAK,KAAA;QACA;MACA;MACAL,QAAA;IACA;IACA,IAAAgB,YAAA,YAAAA,aAAAlB,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,KAAAJ,IAAA,CAAAK,IAAA,CAAAgB,QAAA;QACAjB,QAAA,KAAAK,KAAA;QACA;MACA,WAAAN,KAAA;QACAC,QAAA;QACA;MACA,kCAAAU,IAAA,CAAAX,KAAA;QACAC,QAAA,KAAAK,KAAA;QACA;MACA;MACAL,QAAA;IACA;IACA,IAAAkB,YAAA,YAAAA,aAAApB,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,KAAAJ,IAAA,CAAAK,IAAA,CAAAkB,QAAA;QACAnB,QAAA,KAAAK,KAAA;QACA;MACA,WAAAN,KAAA;QACAC,QAAA;QACA;MACA,kCAAAU,IAAA,CAAAX,KAAA;QACAC,QAAA,KAAAK,KAAA;QACA;MACA;MACAL,QAAA;IACA;IACA;MACA;MACAoB,OAAA;MACA;MACAC,oBAAA;MACA;MACAC,kBAAA;MACA;MACAC,iBAAA;MACA;MACAC,kBAAA;MACA;MACAC,YAAA;MACAC,YAAA;MACA;MACAC,kBAAA;MACAC,kBAAA;MACA;MACAC,eAAA;MACA;MACAC,qBAAA;MACAC,sBAAA;MACA;MACAC,oBAAA;MACAC,qBAAA;MACAC,WAAA;MACAC,WAAA;MACA;MACAlC,IAAA;MACA;MACA;MACAmC,KAAA;QACAC,aAAA;UAAAC,QAAA;UAAAC,OAAA;QAAA;QACAxB,SAAA,GACA;UAAAyB,SAAA,EAAA1B,QAAA;UAAAwB,QAAA;UAAAG,OAAA;QAAA,EACA;QACAhC,WAAA,GACA;UAAA+B,SAAA,EAAAhC,QAAA;UAAA8B,QAAA;UAAAG,OAAA;QAAA,EACA;QACAC,OAAA,GACA;UAAAJ,QAAA;UAAAC,OAAA;UAAAE,OAAA;QAAA,EACA;QACAE,cAAA,GACA;UAAAL,QAAA;UAAAC,OAAA;UAAAE,OAAA;QAAA,EACA;QACAxB,QAAA,GACA;UAAAqB,QAAA;UAAAE,SAAA,EAAAxB,YAAA;UAAAyB,OAAA;QAAA,EACA;QACAG,aAAA,GACA;UAAAN,QAAA;UAAAC,OAAA;UAAAE,OAAA;QAAA,EACA;QACAtB,QAAA,GACA;UAAAmB,QAAA;UAAAE,SAAA,EAAAtB,YAAA;UAAAuB,OAAA;QAAA,EACA;QACAI,cAAA,GACA;UAAAP,QAAA;UAAAC,OAAA;UAAAE,OAAA;QAAA,EACA;QACAK,WAAA,GACA;UAAAR,QAAA;UAAAC,OAAA;UAAAE,OAAA;QAAA,EACA;QACArC,QAAA,GACA;UAAAoC,SAAA,EAAA3C,iBAAA;UAAA4C,OAAA;QAAA,EACA;QACAlC,WAAA,GACA;UAAAiC,SAAA,EAAAlC,kBAAA;UAAAmC,OAAA;QAAA,EACA;QACAM,KAAA,GACA;UAAAT,QAAA;UAAAC,OAAA;QAAA,EACA;QACAS,IAAA,GACA;UAAAV,QAAA;UAAAC,OAAA;QAAA,EACA;QACAU,QAAA,GACA;UAAAX,QAAA;UAAAC,OAAA;QAAA,EACA;QACArC,QAAA,GACA;UAAAoC,QAAA;UAAAC,OAAA;UAAAE,OAAA;QAAA,EACA;QACAS,cAAA,GACA;UAAAZ,QAAA;UAAAC,OAAA;QAAA,EACA;QACAY,WAAA,GACA;UAAAb,QAAA;UAAAC,OAAA;QAAA;MAEA;MACAa,OAAA,EAAA/D,UAAA;MACAgE,eAAA;MACAC,SAAA;MACA;MACAC,QAAA;MACAC,MAAA,EAAAC,SAAA;MACAC,SAAA,EAAAD,SAAA;MACAE,WAAA,EAAAF,SAAA;MACAG,KAAA;MACAC,QAAA;MACA;MACAC,YAAA;IACA;EACA;EACAC,SAAA,WAAAA,UAAA;IAEA,KAAAC,KAAA;IACA;IACA,KAAAR,MAAA,QAAAS,MAAA,CAAAC,KAAA,SAAAD,MAAA,CAAAC,KAAA,CAAAV,MAAA;IACA,KAAAE,SAAA,QAAAO,MAAA,CAAAC,KAAA,SAAAD,MAAA,CAAAC,KAAA,CAAAR,SAAA;IACA,KAAAH,QAAA,QAAAU,MAAA,CAAAC,KAAA,SAAAD,MAAA,CAAAC,KAAA,CAAAX,QAAA;IACA,KAAAI,WAAA,QAAAM,MAAA,CAAAC,KAAA,SAAAD,MAAA,CAAAC,KAAA,CAAAP,WAAA;IACA,IAAAQ,IAAA,QAAAF,MAAA,CAAAC,KAAA,SAAAD,MAAA,CAAAC,KAAA,CAAAL,QAAA;IACA,IAAAM,IAAA;MACA,KAAAN,QAAA;IACA;MACA,KAAAA,QAAA;IACA;IACA,SAAAF,WAAA;MACA,SAAAJ,QAAA,qBAAAM,QAAA;QACA,KAAAD,KAAA;MACA;MACA,KAAAQ,aAAA,MAAAT,WAAA;IACA;IACAU,OAAA,CAAAC,GAAA,wDAAAT,QAAA;EACA;EACAU,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IAEA,KAAAR,KAAA;IACA;IACA,KAAAR,MAAA,QAAAS,MAAA,CAAAC,KAAA,SAAAD,MAAA,CAAAC,KAAA,CAAAV,MAAA;IACA,KAAAE,SAAA,QAAAO,MAAA,CAAAC,KAAA,SAAAD,MAAA,CAAAC,KAAA,CAAAR,SAAA;IACA,KAAAH,QAAA,QAAAU,MAAA,CAAAC,KAAA,SAAAD,MAAA,CAAAC,KAAA,CAAAX,QAAA;IACA,KAAAI,WAAA,QAAAM,MAAA,CAAAC,KAAA,SAAAD,MAAA,CAAAC,KAAA,CAAAP,WAAA;IACA,IAAAQ,IAAA,QAAAF,MAAA,CAAAC,KAAA,SAAAD,MAAA,CAAAC,KAAA,CAAAL,QAAA;IACA,IAAAM,IAAA;MACA,KAAAN,QAAA;IACA;MACA,KAAAA,QAAA;IACA;IACA,SAAAF,WAAA;MACA,SAAAJ,QAAA,qBAAAM,QAAA;QACA,KAAAD,KAAA;MACA;MACA,KAAAQ,aAAA,MAAAT,WAAA;IACA;IACA;IACAU,OAAA,CAAAC,GAAA,sDAAAT,QAAA;IAEA,KAAAY,QAAA,sBAAA7D,IAAA,WAAAC,QAAA;MACA2D,KAAA,CAAAnD,oBAAA,GAAAR,QAAA,CAAAlB,IAAA;IACA;IACA,KAAA8E,QAAA,oBAAA7D,IAAA,WAAAC,QAAA;MACA2D,KAAA,CAAAlD,kBAAA,GAAAT,QAAA,CAAAlB,IAAA;IACA;IACA,KAAA8E,QAAA,mBAAA7D,IAAA,WAAAC,QAAA;MACA2D,KAAA,CAAAjD,iBAAA,GAAAV,QAAA,CAAAlB,IAAA;IACA;IACA;IACA;IACA;IACA,KAAA8E,QAAA,aAAA7D,IAAA,WAAAC,QAAA;MACA2D,KAAA,CAAA9C,YAAA,GAAAb,QAAA,CAAAlB,IAAA;MACA,IAAA+E,GAAA;MACA7D,QAAA,CAAAlB,IAAA,CAAAgF,OAAA,WAAAC,IAAA,EAAAC,KAAA;QACA,IAAAC,GAAA;QACAA,GAAA,CAAAC,KAAA,GAAAH,IAAA,CAAAI,SAAA;QACAF,GAAA,CAAA/E,KAAA,GAAA6E,IAAA,CAAAK,SAAA;QACAP,GAAA,CAAAQ,IAAA,CAAAJ,GAAA;MACA;MACAN,KAAA,CAAA/C,YAAA,GAAAiD,GAAA;IACA;IACA,KAAAD,QAAA,YAAA7D,IAAA,WAAAC,QAAA;MACA2D,KAAA,CAAArC,WAAA,GAAAtB,QAAA,CAAAlB,IAAA;MACA,IAAA+E,GAAA;MACA7D,QAAA,CAAAlB,IAAA,CAAAgF,OAAA,WAAAC,IAAA,EAAAC,KAAA;QACA,IAAAC,GAAA;QACAA,GAAA,CAAAC,KAAA,GAAAH,IAAA,CAAAI,SAAA;QACAF,GAAA,CAAA/E,KAAA,GAAA6E,IAAA,CAAAK,SAAA;QACAP,GAAA,CAAAQ,IAAA,CAAAJ,GAAA;MACA;MACAN,KAAA,CAAAtC,WAAA,GAAAwC,GAAA;IACA;IACA,KAAAD,QAAA,YAAA7D,IAAA,WAAAC,QAAA;MACA2D,KAAA,CAAA5C,kBAAA,GAAAf,QAAA,CAAAlB,IAAA;MACA,IAAA+E,GAAA;MACA7D,QAAA,CAAAlB,IAAA,CAAAgF,OAAA,WAAAC,IAAA,EAAAC,KAAA;QACA,IAAAC,GAAA;QACAA,GAAA,CAAAC,KAAA,GAAAH,IAAA,CAAAI,SAAA;QACAF,GAAA,CAAA/E,KAAA,GAAA6E,IAAA,CAAAK,SAAA;QACAP,GAAA,CAAAQ,IAAA,CAAAJ,GAAA;MACA;MACAN,KAAA,CAAA7C,kBAAA,GAAA+C,GAAA;IACA;IACA,KAAAD,QAAA,gBAAA7D,IAAA,WAAAC,QAAA;MACA2D,KAAA,CAAAzC,sBAAA,GAAAlB,QAAA,CAAAlB,IAAA;MACA,IAAA+E,GAAA;MACA7D,QAAA,CAAAlB,IAAA,CAAAgF,OAAA,WAAAC,IAAA,EAAAC,KAAA;QACA,IAAAC,GAAA;QACAA,GAAA,CAAAC,KAAA,GAAAH,IAAA,CAAAI,SAAA;QACAF,GAAA,CAAA/E,KAAA,GAAA6E,IAAA,CAAAK,SAAA;QACAP,GAAA,CAAAQ,IAAA,CAAAJ,GAAA;MACA;MACAN,KAAA,CAAA1C,qBAAA,GAAA4C,GAAA;IACA;IACA,KAAAD,QAAA,uBAAA7D,IAAA,WAAAC,QAAA;MACA2D,KAAA,CAAAvC,qBAAA,GAAApB,QAAA,CAAAlB,IAAA;MACA,IAAA+E,GAAA;MACA7D,QAAA,CAAAlB,IAAA,CAAAgF,OAAA,WAAAC,IAAA,EAAAC,KAAA;QACA,IAAAC,GAAA;QACAA,GAAA,CAAAC,KAAA,GAAAH,IAAA,CAAAI,SAAA;QACAF,GAAA,CAAA/E,KAAA,GAAA6E,IAAA,CAAAK,SAAA;QACAP,GAAA,CAAAQ,IAAA,CAAAJ,GAAA;MACA;MACAN,KAAA,CAAAxC,oBAAA,GAAA0C,GAAA;IACA;IACA,KAAAD,QAAA,iBAAA7D,IAAA,WAAAC,QAAA;MACA2D,KAAA,CAAA3C,eAAA,GAAAhB,QAAA,CAAAlB,IAAA;IACA;IACA;IACA,KAAAM,IAAA,CAAAoC,aAAA;EACA;EACA8C,OAAA;IAEA;IACAnB,KAAA,WAAAA,MAAA;MACA,KAAA/D,IAAA;QACAU,SAAA;QACAI,SAAA;QACAN,WAAA;QACA4B,aAAA;QACA+C,WAAA;QACAC,YAAA;QACApC,QAAA;QACAqC,IAAA;QACAC,QAAA;QACA7C,OAAA;QACA8C,UAAA;QACAC,UAAA;QACA9C,cAAA;QACA1B,QAAA;QACA4B,cAAA;QACAD,aAAA;QACAzB,QAAA;QACAuE,WAAA;QACAC,WAAA;QACAC,WAAA;QACAC,UAAA;QACA/C,WAAA;QACAC,KAAA;QACAC,IAAA;QACA8C,IAAA;QACAC,QAAA;QACAC,aAAA;QACAC,WAAA;QACA/F,QAAA;QACAE,QAAA;QACAG,WAAA;QACA2F,QAAA;QACAC,WAAA;QACAC,MAAA;QACAC,MAAA;QACAC,MAAA;MACA;MACA,KAAAC,SAAA;IACA;IACA;IACAC,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAC,GAAA,GAAAD,SAAA,CAAAE,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAjG,SAAA;MAAA;MACA,KAAAkG,MAAA,GAAAJ,SAAA,CAAAK,MAAA;MACA,KAAAC,QAAA,IAAAN,SAAA,CAAAK,MAAA;MACAzC,OAAA,CAAA2C,IAAA,CAAAP,SAAA;IACA;IAEA,aACArC,aAAA,WAAAA,cAAAzD,SAAA;MAAA,IAAAsG,MAAA;MACAlI,SAAA,CAAA4B,SAAA,EAAAC,IAAA,WAAAC,QAAA;QACAoG,MAAA,CAAAhH,IAAA,GAAAY,QAAA,CAAAlB,IAAA;QACA,IAAAsH,MAAA,CAAAhH,IAAA,CAAA8C,KAAA,EAAAkE,MAAA,CAAAhH,IAAA,CAAA8C,KAAA,GAAAkE,MAAA,CAAAhH,IAAA,CAAA8C,KAAA,CAAAmE,KAAA,WACAD,MAAA,CAAAhH,IAAA,CAAA8C,KAAA;QACA,IAAAkE,MAAA,CAAAhH,IAAA,CAAAgG,WAAA,EACAgB,MAAA,CAAAhH,IAAA,CAAAgG,WAAA,GAAAgB,MAAA,CAAAhH,IAAA,CAAAgG,WAAA,CAAAiB,KAAA,WACAD,MAAA,CAAAhH,IAAA,CAAAgG,WAAA;QACA,IAAAgB,MAAA,CAAAhH,IAAA,CAAAC,QAAA,EACA+G,MAAA,CAAAhH,IAAA,CAAAC,QAAA,GAAA+G,MAAA,CAAAhH,IAAA,CAAAC,QAAA,CAAAgH,KAAA,WACAD,MAAA,CAAAhH,IAAA,CAAAC,QAAA;QACA,IAAA+G,MAAA,CAAAhH,IAAA,CAAA+C,IAAA,EAAAiE,MAAA,CAAAhH,IAAA,CAAA+C,IAAA,GAAAiE,MAAA,CAAAhH,IAAA,CAAA+C,IAAA,CAAAkE,KAAA,WACAD,MAAA,CAAAhH,IAAA,CAAA+C,IAAA;QACA,IAAAmE,SAAA,GAAAtG,QAAA,CAAAlB,IAAA,CAAAsD,QAAA;QACA,IAAAkE,SAAA,CAAAL,MAAA;UACA,IAAApE,OAAA,GAAAyE,SAAA,CAAAD,KAAA;UACA,IAAAE,KAAA;UACA;UACA,IAAA1E,OAAA,CAAAoE,MAAA,MAAAM,KAAA,CAAAlC,IAAA,CAAA3F,UAAA,CAAAmD,OAAA,KAAA2E,IAAA;UACA;UACA,IAAA3E,OAAA,CAAAoE,MAAA,MACAM,KAAA,CAAAlC,IAAA,CAAA3F,UAAA,CAAAmD,OAAA,KAAAA,OAAA,KAAA2E,IAAA;UACA;UACA,IAAA3E,OAAA,CAAAoE,MAAA,MACAM,KAAA,CAAAlC,IAAA,CAAA3F,UAAA,CAAAmD,OAAA,KAAAA,OAAA,KAAAA,OAAA,KAAA2E,IAAA;UAEAJ,MAAA,CAAA5D,eAAA,GAAA+D,KAAA;QACA;MACA;IACA;IACA,WACAE,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,IAAA3H,IAAA;MACA,KAAA4H,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAH,MAAA,CAAAtH,IAAA,CAAAC,QAAA,CAAAC,OAAA,cAAAoH,MAAA,CAAAtH,IAAA,CAAAG,QAAA;YACA,IAAAuH,QAAA;YACA,KAAAA,QAAA,CAAAjH,IAAA,CAAA6G,MAAA,CAAAtH,IAAA,CAAAG,QAAA;cACAmH,MAAA,CAAAK,QAAA,CAAAC,KAAA;cACA;YACA;UACA;UACA;UACA;UACA;UACA;UACA,IAAAC,OAAA,GAAAC,IAAA,CAAAC,SAAA,CAAAT,MAAA,CAAAtH,IAAA;UACA,IAAAgI,QAAA,GAAAF,IAAA,CAAAG,KAAA,CAAAJ,OAAA;UACA,IAAAG,QAAA,CAAAlF,KAAA,IAAAkF,QAAA,CAAAlF,KAAA,CAAA+D,MAAA,MACAmB,QAAA,CAAAlF,KAAA,GAAAkF,QAAA,CAAAlF,KAAA,CAAAoF,IAAA,WACAF,QAAA,CAAAlF,KAAA,GAAAU,SAAA;UACA,IAAAwE,QAAA,CAAAhC,WAAA,IAAAgC,QAAA,CAAAhC,WAAA,CAAAa,MAAA,MACAmB,QAAA,CAAAhC,WAAA,GAAAgC,QAAA,CAAAhC,WAAA,CAAAkC,IAAA,WACAF,QAAA,CAAAhC,WAAA,GAAAxC,SAAA;UACA,IAAAwE,QAAA,CAAA/H,QAAA,IAAA+H,QAAA,CAAA/H,QAAA,CAAA4G,MAAA,MACAmB,QAAA,CAAA/H,QAAA,GAAA+H,QAAA,CAAA/H,QAAA,CAAAiI,IAAA,WACAF,QAAA,CAAA/H,QAAA,GAAAuD,SAAA;UACA,IAAAwE,QAAA,CAAAjF,IAAA,IAAAiF,QAAA,CAAAjF,IAAA,CAAA8D,MAAA,MACAmB,QAAA,CAAAjF,IAAA,GAAAiF,QAAA,CAAAjF,IAAA,CAAAmF,IAAA,WACAF,QAAA,CAAAjF,IAAA,GAAAS,SAAA;;UAEA;UACA,IAAA8D,MAAA,CAAAzD,YAAA,CAAAgD,MAAA;YACA,IAAAsB,KAAA,OAAAC,KAAA;YACAd,MAAA,CAAAzD,YAAA,CAAAa,OAAA,WAAA2D,CAAA;cACAF,KAAA,CAAAlD,IAAA,CAAAoD,CAAA,CAAAvI,KAAA;YACA;YACAkI,QAAA,CAAArC,WAAA,UAAAwC,KAAA,CAAAD,IAAA;UACA;UAEA,IAAA/G,OAAA,GAAAmG,MAAA,CAAAgB,QAAA;YACAC,IAAA;YAAA;YACAC,IAAA;YAAA;YACAC,OAAA;YAAA;YACAC,UAAA;YAAA;YACAC,MAAA,EAAAC,QAAA,CAAAC,aAAA;UACA;UACAD,QAAA,CAAAE,eAAA,CAAAC,KAAA,CAAAC,SAAA;;UAEA,IAAAhB,QAAA,CAAAtH,SAAA;YACA1B,YAAA,CAAAgJ,QAAA,EAAArH,IAAA,WAAAC,QAAA;cACA;cACA,IAAAjB,IAAA,CAAA+D,WAAA;gBACA/D,IAAA,CAAA4H,KAAA,SAAA0B,YAAA;cACA;gBACAtJ,IAAA,CAAAuJ,SAAA,CAAAlB,QAAA;cACA;cACAmB,UAAA;gBACAhI,OAAA,CAAAiI,KAAA;gBACAR,QAAA,CAAAE,eAAA,CAAAC,KAAA,CAAAC,SAAA;cACA;YACA,GAAAK,KAAA,WAAAC,GAAA;cACAlF,OAAA,CAAAC,GAAA,CAAAiF,GAAA;cACAnI,OAAA,CAAAiI,KAAA;cACAR,QAAA,CAAAE,eAAA,CAAAC,KAAA,CAAAC,SAAA;YACA;UACA;YACAjK,SAAA,CAAAiJ,QAAA,EAAArH,IAAA,WAAAC,QAAA;cACAwD,OAAA,CAAAC,GAAA;cACA;cACA2D,QAAA,CAAAtH,SAAA,GAAAE,QAAA,CAAAlB,IAAA;cACAC,IAAA,CAAAK,IAAA,CAAAU,SAAA,GAAAE,QAAA,CAAAlB,IAAA;cACAC,IAAA,CAAAuJ,SAAA,CAAAlB,QAAA;cACAmB,UAAA;gBACAhI,OAAA,CAAAiI,KAAA;gBACAR,QAAA,CAAAE,eAAA,CAAAC,KAAA,CAAAC,SAAA;cACA;YACA,GAAAK,KAAA,WAAAC,GAAA;cACAlF,OAAA,CAAAC,GAAA,CAAAiF,GAAA;cACAnI,OAAA,CAAAiI,KAAA;cACAR,QAAA,CAAAE,eAAA,CAAAC,KAAA,CAAAC,SAAA;YACA;UACA;QACA;MACA;IACA;IACAE,SAAA,WAAAA,UAAAlB,QAAA;MACA;MACA;MACA;MACA,IAAAuB,SAAA;MACAA,SAAA,CAAAC,YAAA,QAAAC,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAA3G,QAAA;MACA;MACA,SAAAyG,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,KAAA,SAAAH,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,KAAA,CAAAC,QAAA;QACAN,SAAA,CAAAO,QAAA;MACA;QACAP,SAAA,CAAAO,QAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAP,SAAA,CAAAQ,WAAA,GAAA/B,QAAA,CAAAtH,SAAA;MACA,IAAAsJ,QAAA;MACA,IAAAhC,QAAA,CAAA5F,aAAA;QACA4H,QAAA;MACA;MACA,KAAAzC,KAAA,SAAA2B,SAAA,CAAAlB,QAAA,CAAAtH,SAAA,EAAAsJ,QAAA,EAAAT,SAAA;IACA;IACAU,WAAA,WAAAA,YAAA;MACA,KAAAC,QAAA;IACA;IACAC,WAAA,WAAAA,YAAA;MACA,KAAAD,QAAA;IACA;IACAE,YAAA,WAAAA,aAAAtK,KAAA;MACA,KAAAA,KAAA,IAAAA,KAAA,CAAA+G,MAAA;QACA,KAAAzD,eAAA;QACA,KAAApD,IAAA,CAAAgD,QAAA,GAAAQ,SAAA;QACA,KAAAxD,IAAA,CAAAsF,QAAA,GAAA9B,SAAA;QACA;MACA;MACA,KAAAJ,eAAA,GAAAtD,KAAA;MACA,IAAAuK,GAAA;MACAvK,KAAA,CAAA4E,OAAA,WAAAiC,IAAA;QACA0D,GAAA,IAAAhL,UAAA,CAAAsH,IAAA;MACA;MACA,IAAA0D,GAAA,CAAAxD,MAAA;QACAwD,GAAA,GAAAA,GAAA,CAAAC,SAAA,IAAAD,GAAA,CAAAxD,MAAA;QACA,KAAA7G,IAAA,CAAAgD,QAAA,GAAAqH,GAAA;QACA,KAAArK,IAAA,CAAAsF,QAAA,QAAAmE,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAA3G,QAAA;MACA;QACA,KAAAhD,IAAA,CAAAgD,QAAA,GAAAQ,SAAA;QACA,KAAAxD,IAAA,CAAAsF,QAAA,GAAA9B,SAAA;MACA;IACA;IACA+G,qBAAA,WAAAA,sBAAAzK,KAAA;MACA,KAAAuD,SAAA,GAAAvD,KAAA;MACA,IAAAuK,GAAA;MACAvK,KAAA,CAAA4E,OAAA,WAAAiC,IAAA;QACA0D,GAAA,IAAAhL,UAAA,CAAAsH,IAAA;MACA;MACA,IAAA0D,GAAA,CAAAxD,MAAA;QACAwD,GAAA,GAAAA,GAAA,CAAAC,SAAA,IAAAD,GAAA,CAAAxD,MAAA;QACA,KAAA2D,WAAA,CAAAxH,QAAA,GAAAqH,GAAA;MACA;QACA,KAAAG,WAAA,CAAAxH,QAAA,GAAAQ,SAAA;MACA;IACA;IACA,SACAiH,cAAA,WAAAA,eAAA;MACA,KAAAlD,KAAA,SAAAkD,cAAA;IACA;IACA,SACAC,YAAA,WAAAA,aAAA;MACA,KAAAnD,KAAA,SAAAmD,YAAA;IACA;IACA,WACAC,MAAA,WAAAA,OAAA;MACA;MACA,KAAAlB,MAAA,CAAAmB,QAAA,0BAAA5G,MAAA;MACA,KAAA6G,OAAA,CAAAC,EAAA;IACA;IACAC,YAAA,WAAAA,aAAAnG,KAAA;MACA,IAAAA,KAAA;QACA,KAAAf,YAAA,CAAAmH,MAAA,CAAApG,KAAA;MACA;IACA;IACAqG,SAAA,WAAAA,UAAA;MACA,KAAApH,YAAA,CAAAoB,IAAA;QACAnF,KAAA;QACAoL,GAAA,EAAAC,IAAA,CAAAC,GAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}