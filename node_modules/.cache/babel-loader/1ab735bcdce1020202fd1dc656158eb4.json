{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/monitor/online/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/monitor/online/index.vue", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGxpc3QsIGZvcmNlTG9nb3V0IH0gZnJvbSAiQC9hcGkvbW9uaXRvci9vbmxpbmUiOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogIk9ubGluZSIsCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIC8vIOmBrue9qeWxggogICAgICBsb2FkaW5nOiB0cnVlLAogICAgICAvLyDmgLvmnaHmlbAKICAgICAgdG90YWw6IDAsCiAgICAgIC8vIOihqOagvOaVsOaNrgogICAgICBsaXN0OiBbXSwKICAgICAgcGFnZU51bTogMSwKICAgICAgcGFnZVNpemU6IDEwLAogICAgICAvLyDmn6Xor6Llj4LmlbAKICAgICAgcXVlcnlQYXJhbXM6IHsKICAgICAgICBpcGFkZHI6IHVuZGVmaW5lZCwKICAgICAgICB1c2VyTmFtZTogdW5kZWZpbmVkCiAgICAgIH0KICAgIH07CiAgfSwKICBjcmVhdGVkOiBmdW5jdGlvbiBjcmVhdGVkKCkgewogICAgdGhpcy5nZXRMaXN0KCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICAvKiog5p+l6K+i55m75b2V5pel5b+X5YiX6KGoICovZ2V0TGlzdDogZnVuY3Rpb24gZ2V0TGlzdCgpIHsKICAgICAgdmFyIF90aGlzID0gdGhpczsKICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsKICAgICAgbGlzdCh0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgIF90aGlzLmxpc3QgPSByZXNwb25zZS5yb3dzOwogICAgICAgIF90aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWw7CiAgICAgICAgX3RoaXMubG9hZGluZyA9IGZhbHNlOwogICAgICB9KTsKICAgIH0sCiAgICAvKiog5pCc57Si5oyJ6ZKu5pON5L2cICovaGFuZGxlUXVlcnk6IGZ1bmN0aW9uIGhhbmRsZVF1ZXJ5KCkgewogICAgICB0aGlzLnBhZ2VOdW0gPSAxOwogICAgICB0aGlzLmdldExpc3QoKTsKICAgIH0sCiAgICAvKiog6YeN572u5oyJ6ZKu5pON5L2cICovcmVzZXRRdWVyeTogZnVuY3Rpb24gcmVzZXRRdWVyeSgpIHsKICAgICAgdGhpcy5yZXNldEZvcm0oInF1ZXJ5Rm9ybSIpOwogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7CiAgICB9LAogICAgLyoqIOW8uumAgOaMiemSruaTjeS9nCAqL2hhbmRsZUZvcmNlTG9nb3V0OiBmdW5jdGlvbiBoYW5kbGVGb3JjZUxvZ291dChyb3cpIHsKICAgICAgdmFyIF90aGlzMiA9IHRoaXM7CiAgICAgIHRoaXMuJGNvbmZpcm0oJ+aYr+WQpuehruiupOW8uumAgOWQjeensOS4uiInICsgcm93LnVzZXJOYW1lICsgJyLnmoTmlbDmja7pobk/JywgIuitpuWRiiIsIHsKICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogIuehruWumiIsCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogIuWPlua2iCIsCiAgICAgICAgdHlwZTogIndhcm5pbmciCiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgIHJldHVybiBmb3JjZUxvZ291dChyb3cudG9rZW5JZCk7CiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgIF90aGlzMi5nZXRMaXN0KCk7CiAgICAgICAgX3RoaXMyLm1zZ1N1Y2Nlc3MoIuW8uumAgOaIkOWKnyIpOwogICAgICB9KTsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["list", "forceLogout", "name", "data", "loading", "total", "pageNum", "pageSize", "queryParams", "ipaddr", "undefined", "userName", "created", "getList", "methods", "_this", "then", "response", "rows", "handleQuery", "reset<PERSON><PERSON>y", "resetForm", "handleForceLogout", "row", "_this2", "$confirm", "confirmButtonText", "cancelButtonText", "type", "tokenId", "msgSuccess"], "sources": ["src/views/monitor/online/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" label-width=\"68px\">\n      <el-form-item label=\"登录地址\" prop=\"ipaddr\">\n        <el-input\n          v-model=\"queryParams.ipaddr\"\n          placeholder=\"请输入登录地址\"\n          clearable\n          size=\"small\"\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"用户名称\" prop=\"userName\">\n        <el-input\n          v-model=\"queryParams.userName\"\n          placeholder=\"请输入用户名称\"\n          clearable\n          size=\"small\"\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n\n    </el-form>\n    <el-table\n      v-loading=\"loading\"\n      :data=\"list.slice((pageNum-1)*pageSize,pageNum*pageSize)\"\n      style=\"width: 100%;\"\n    >\n      <el-table-column label=\"序号\" type=\"index\" align=\"center\">\n        <template slot-scope=\"scope\">\n          <span>{{(pageNum - 1) * pageSize + scope.$index + 1}}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"会话编号\" align=\"center\" prop=\"tokenId\" :show-overflow-tooltip=\"true\" />\n      <el-table-column label=\"登录名称\" align=\"center\" prop=\"userName\" :show-overflow-tooltip=\"true\" />\n      <el-table-column label=\"部门名称\" align=\"center\" prop=\"deptName\" />\n      <el-table-column label=\"主机\" align=\"center\" prop=\"ipaddr\" :show-overflow-tooltip=\"true\" />\n      <el-table-column label=\"登录地点\" align=\"center\" prop=\"loginLocation\" :show-overflow-tooltip=\"true\" />\n      <el-table-column label=\"浏览器\" align=\"center\" prop=\"browser\" />\n      <el-table-column label=\"操作系统\" align=\"center\" prop=\"os\" />\n      <el-table-column label=\"登录时间\" align=\"center\" prop=\"loginTime\" width=\"180\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.loginTime) }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-delete\"\n            @click=\"handleForceLogout(scope.row)\"\n            v-hasPermi=\"['monitor:online:forceLogout']\"\n          >强退</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <pagination v-show=\"total>0\" :total=\"total\" :page.sync=\"pageNum\" :limit.sync=\"pageSize\" />\n  </div>\n</template>\n\n<script>\nimport { list, forceLogout } from \"@/api/monitor/online\";\n\nexport default {\n  name: \"Online\",\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 总条数\n      total: 0,\n      // 表格数据\n      list: [],\n      pageNum: 1,\n      pageSize: 10,\n      // 查询参数\n      queryParams: {\n        ipaddr: undefined,\n        userName: undefined\n      }\n    };\n  },\n  created() {\n    this.getList();\n  },\n  methods: {\n    /** 查询登录日志列表 */\n    getList() {\n      this.loading = true;\n      list(this.queryParams).then(response => {\n        this.list = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    /** 强退按钮操作 */\n    handleForceLogout(row) {\n      this.$confirm('是否确认强退名称为\"' + row.userName + '\"的数据项?', \"警告\", {\n          confirmButtonText: \"确定\",\n          cancelButtonText: \"取消\",\n          type: \"warning\"\n        }).then(function() {\n          return forceLogout(row.tokenId);\n        }).then(() => {\n          this.getList();\n          this.msgSuccess(\"强退成功\");\n        })\n    }\n  }\n};\n</script>\n\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmEA,SAAAA,IAAA,EAAAC,WAAA;AAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,KAAA;MACA;MACAL,IAAA;MACAM,OAAA;MACAC,QAAA;MACA;MACAC,WAAA;QACAC,MAAA,EAAAC,SAAA;QACAC,QAAA,EAAAD;MACA;IACA;EACA;EACAE,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA,eACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAAX,OAAA;MACAJ,IAAA,MAAAQ,WAAA,EAAAQ,IAAA,WAAAC,QAAA;QACAF,KAAA,CAAAf,IAAA,GAAAiB,QAAA,CAAAC,IAAA;QACAH,KAAA,CAAAV,KAAA,GAAAY,QAAA,CAAAZ,KAAA;QACAU,KAAA,CAAAX,OAAA;MACA;IACA;IACA,aACAe,WAAA,WAAAA,YAAA;MACA,KAAAb,OAAA;MACA,KAAAO,OAAA;IACA;IACA,aACAO,UAAA,WAAAA,WAAA;MACA,KAAAC,SAAA;MACA,KAAAF,WAAA;IACA;IACA,aACAG,iBAAA,WAAAA,kBAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,QAAA,gBAAAF,GAAA,CAAAZ,QAAA;QACAe,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAAZ,IAAA;QACA,OAAAf,WAAA,CAAAsB,GAAA,CAAAM,OAAA;MACA,GAAAb,IAAA;QACAQ,MAAA,CAAAX,OAAA;QACAW,MAAA,CAAAM,UAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}