{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/store/modules/user.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/store/modules/user.js", "mtime": 1655042714000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js", "mtime": 1751171659043}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}