{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/utils/validate.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/utils/validate.js", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js", "mtime": 1751171659043}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}