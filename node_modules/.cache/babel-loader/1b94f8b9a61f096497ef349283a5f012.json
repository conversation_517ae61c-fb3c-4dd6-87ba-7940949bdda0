{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/index_v1.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/index_v1.vue", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBQYW5lbEdyb3VwIGZyb20gJy4vZGFzaGJvYXJkL1BhbmVsR3JvdXAnOwppbXBvcnQgTGluZUNoYXJ0IGZyb20gJy4vZGFzaGJvYXJkL0xpbmVDaGFydCc7CmltcG9ydCBSYWRkYXJDaGFydCBmcm9tICcuL2Rhc2hib2FyZC9SYWRkYXJDaGFydCc7CmltcG9ydCBQaWVDaGFydCBmcm9tICcuL2Rhc2hib2FyZC9QaWVDaGFydCc7CmltcG9ydCBCYXJDaGFydCBmcm9tICcuL2Rhc2hib2FyZC9CYXJDaGFydCc7CnZhciBsaW5lQ2hhcnREYXRhID0gewogIG5ld1Zpc2l0aXM6IHsKICAgIGV4cGVjdGVkRGF0YTogWzEwMCwgMTIwLCAxNjEsIDEzNCwgMTA1LCAxNjAsIDE2NV0sCiAgICBhY3R1YWxEYXRhOiBbMTIwLCA4MiwgOTEsIDE1NCwgMTYyLCAxNDAsIDE0NV0KICB9LAogIG1lc3NhZ2VzOiB7CiAgICBleHBlY3RlZERhdGE6IFsyMDAsIDE5MiwgMTIwLCAxNDQsIDE2MCwgMTMwLCAxNDBdLAogICAgYWN0dWFsRGF0YTogWzE4MCwgMTYwLCAxNTEsIDEwNiwgMTQ1LCAxNTAsIDEzMF0KICB9LAogIHB1cmNoYXNlczogewogICAgZXhwZWN0ZWREYXRhOiBbODAsIDEwMCwgMTIxLCAxMDQsIDEwNSwgOTAsIDEwMF0sCiAgICBhY3R1YWxEYXRhOiBbMTIwLCA5MCwgMTAwLCAxMzgsIDE0MiwgMTMwLCAxMzBdCiAgfSwKICBzaG9wcGluZ3M6IHsKICAgIGV4cGVjdGVkRGF0YTogWzEzMCwgMTQwLCAxNDEsIDE0MiwgMTQ1LCAxNTAsIDE2MF0sCiAgICBhY3R1YWxEYXRhOiBbMTIwLCA4MiwgOTEsIDE1NCwgMTYyLCAxNDAsIDEzMF0KICB9Cn07CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnSW5kZXgnLAogIGNvbXBvbmVudHM6IHsKICAgIFBhbmVsR3JvdXA6IFBhbmVsR3JvdXAsCiAgICBMaW5lQ2hhcnQ6IExpbmVDaGFydCwKICAgIFJhZGRhckNoYXJ0OiBSYWRkYXJDaGFydCwKICAgIFBpZUNoYXJ0OiBQaWVDaGFydCwKICAgIEJhckNoYXJ0OiBCYXJDaGFydAogIH0sCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGxpbmVDaGFydERhdGE6IGxpbmVDaGFydERhdGEubmV3VmlzaXRpcwogICAgfTsKICB9LAogIG1ldGhvZHM6IHsKICAgIGhhbmRsZVNldExpbmVDaGFydERhdGE6IGZ1bmN0aW9uIGhhbmRsZVNldExpbmVDaGFydERhdGEodHlwZSkgewogICAgICB0aGlzLmxpbmVDaGFydERhdGEgPSBsaW5lQ2hhcnREYXRhW3R5cGVdOwogICAgfQogIH0KfTs="}, {"version": 3, "names": ["PanelGroup", "Line<PERSON>hart", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "lineChartData", "new<PERSON><PERSON><PERSON>", "expectedData", "actualData", "messages", "purchases", "shoppings", "name", "components", "data", "methods", "handleSetLineChartData", "type"], "sources": ["src/views/index_v1.vue"], "sourcesContent": ["<template>\n  <div class=\"dashboard-editor-container\">\n\n    <panel-group @handleSetLineChartData=\"handleSetLineChartData\" />\n\n    <el-row style=\"background:#fff;padding:16px 16px 0;margin-bottom:32px;\">\n      <line-chart :chart-data=\"lineChartData\" />\n    </el-row>\n\n    <el-row :gutter=\"32\">\n      <el-col :xs=\"24\" :sm=\"24\" :lg=\"8\">\n        <div class=\"chart-wrapper\">\n          <raddar-chart />\n        </div>\n      </el-col>\n      <el-col :xs=\"24\" :sm=\"24\" :lg=\"8\">\n        <div class=\"chart-wrapper\">\n          <pie-chart />\n        </div>\n      </el-col>\n      <el-col :xs=\"24\" :sm=\"24\" :lg=\"8\">\n        <div class=\"chart-wrapper\">\n          <bar-chart />\n        </div>\n      </el-col>\n    </el-row>\n\n    \n  </div>\n</template>\n\n<script>\nimport PanelGroup from './dashboard/PanelGroup'\nimport LineChart from './dashboard/LineChart'\nimport RaddarChart from './dashboard/Raddar<PERSON>hart'\nimport PieChart from './dashboard/PieChart'\nimport BarChart from './dashboard/BarChart'\n\nconst lineChartData = {\n  newVisitis: {\n    expectedData: [100, 120, 161, 134, 105, 160, 165],\n    actualData: [120, 82, 91, 154, 162, 140, 145]\n  },\n  messages: {\n    expectedData: [200, 192, 120, 144, 160, 130, 140],\n    actualData: [180, 160, 151, 106, 145, 150, 130]\n  },\n  purchases: {\n    expectedData: [80, 100, 121, 104, 105, 90, 100],\n    actualData: [120, 90, 100, 138, 142, 130, 130]\n  },\n  shoppings: {\n    expectedData: [130, 140, 141, 142, 145, 150, 160],\n    actualData: [120, 82, 91, 154, 162, 140, 130]\n  }\n}\n\nexport default {\n  name: 'Index',\n  components: {\n    PanelGroup,\n    LineChart,\n    RaddarChart,\n    PieChart,\n    BarChart\n  },\n  data() {\n    return {\n      lineChartData: lineChartData.newVisitis\n    }\n  },\n  methods: {\n    handleSetLineChartData(type) {\n      this.lineChartData = lineChartData[type]\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.dashboard-editor-container {\n  padding: 32px;\n  background-color: rgb(240, 242, 245);\n  position: relative;\n\n  .chart-wrapper {\n    background: #fff;\n    padding: 16px 16px 0;\n    margin-bottom: 32px;\n  }\n}\n\n@media (max-width:1024px) {\n  .chart-wrapper {\n    padding: 8px;\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgCA,OAAAA,UAAA;AACA,OAAAC,SAAA;AACA,OAAAC,WAAA;AACA,OAAAC,QAAA;AACA,OAAAC,QAAA;AAEA,IAAAC,aAAA;EACAC,UAAA;IACAC,YAAA;IACAC,UAAA;EACA;EACAC,QAAA;IACAF,YAAA;IACAC,UAAA;EACA;EACAE,SAAA;IACAH,YAAA;IACAC,UAAA;EACA;EACAG,SAAA;IACAJ,YAAA;IACAC,UAAA;EACA;AACA;AAEA;EACAI,IAAA;EACAC,UAAA;IACAb,UAAA,EAAAA,UAAA;IACAC,SAAA,EAAAA,SAAA;IACAC,WAAA,EAAAA,WAAA;IACAC,QAAA,EAAAA,QAAA;IACAC,QAAA,EAAAA;EACA;EACAU,IAAA,WAAAA,KAAA;IACA;MACAT,aAAA,EAAAA,aAAA,CAAAC;IACA;EACA;EACAS,OAAA;IACAC,sBAAA,WAAAA,uBAAAC,IAAA;MACA,KAAAZ,aAAA,GAAAA,aAAA,CAAAY,IAAA;IACA;EACA;AACA", "ignoreList": []}]}