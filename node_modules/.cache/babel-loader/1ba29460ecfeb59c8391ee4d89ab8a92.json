{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/tool/build/DraggableItem.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/tool/build/DraggableItem.vue", "mtime": 1641277326000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js", "mtime": 1751171659043}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IGRyYWdnYWJsZSBmcm9tICd2dWVkcmFnZ2FibGUnOwppbXBvcnQgcmVuZGVyIGZyb20gJ0AvY29tcG9uZW50cy9yZW5kZXIvcmVuZGVyJzsKdmFyIGNvbXBvbmVudHMgPSB7CiAgaXRlbUJ0bnM6IGZ1bmN0aW9uIGl0ZW1CdG5zKGgsIGN1cnJlbnRJdGVtLCBpbmRleCwgbGlzdCkgewogICAgdmFyIF90aGlzJCRsaXN0ZW5lcnMgPSB0aGlzLiRsaXN0ZW5lcnMsCiAgICAgIGNvcHlJdGVtID0gX3RoaXMkJGxpc3RlbmVycy5jb3B5SXRlbSwKICAgICAgZGVsZXRlSXRlbSA9IF90aGlzJCRsaXN0ZW5lcnMuZGVsZXRlSXRlbTsKICAgIHJldHVybiBbaCgic3BhbiIsIHsKICAgICAgImNsYXNzIjogImRyYXdpbmctaXRlbS1jb3B5IiwKICAgICAgImF0dHJzIjogewogICAgICAgICJ0aXRsZSI6ICLlpI3liLYiCiAgICAgIH0sCiAgICAgICJvbiI6IHsKICAgICAgICAiY2xpY2siOiBmdW5jdGlvbiBjbGljayhldmVudCkgewogICAgICAgICAgY29weUl0ZW0oY3VycmVudEl0ZW0sIGxpc3QpOwogICAgICAgICAgZXZlbnQuc3RvcFByb3BhZ2F0aW9uKCk7CiAgICAgICAgfQogICAgICB9CiAgICB9LCBbaCgiaSIsIHsKICAgICAgImNsYXNzIjogImVsLWljb24tY29weS1kb2N1bWVudCIKICAgIH0pXSksIGgoInNwYW4iLCB7CiAgICAgICJjbGFzcyI6ICJkcmF3aW5nLWl0ZW0tZGVsZXRlIiwKICAgICAgImF0dHJzIjogewogICAgICAgICJ0aXRsZSI6ICLliKDpmaQiCiAgICAgIH0sCiAgICAgICJvbiI6IHsKICAgICAgICAiY2xpY2siOiBmdW5jdGlvbiBjbGljayhldmVudCkgewogICAgICAgICAgZGVsZXRlSXRlbShpbmRleCwgbGlzdCk7CiAgICAgICAgICBldmVudC5zdG9wUHJvcGFnYXRpb24oKTsKICAgICAgICB9CiAgICAgIH0KICAgIH0sIFtoKCJpIiwgewogICAgICAiY2xhc3MiOiAiZWwtaWNvbi1kZWxldGUiCiAgICB9KV0pXTsKICB9Cn07CnZhciBsYXlvdXRzID0gewogIGNvbEZvcm1JdGVtOiBmdW5jdGlvbiBjb2xGb3JtSXRlbShoLCBjdXJyZW50SXRlbSwgaW5kZXgsIGxpc3QpIHsKICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICB2YXIgYWN0aXZlSXRlbSA9IHRoaXMuJGxpc3RlbmVycy5hY3RpdmVJdGVtOwogICAgdmFyIGNvbmZpZyA9IGN1cnJlbnRJdGVtLl9fY29uZmlnX187CiAgICB2YXIgY2hpbGQgPSByZW5kZXJDaGlsZHJlbi5hcHBseSh0aGlzLCBhcmd1bWVudHMpOwogICAgdmFyIGNsYXNzTmFtZSA9IHRoaXMuYWN0aXZlSWQgPT09IGNvbmZpZy5mb3JtSWQgPyAnZHJhd2luZy1pdGVtIGFjdGl2ZS1mcm9tLWl0ZW0nIDogJ2RyYXdpbmctaXRlbSc7CiAgICBpZiAodGhpcy5mb3JtQ29uZi51bkZvY3VzZWRDb21wb25lbnRCb3JkZXIpIGNsYXNzTmFtZSArPSAnIHVuZm9jdXMtYm9yZGVyZWQnOwogICAgdmFyIGxhYmVsV2lkdGggPSBjb25maWcubGFiZWxXaWR0aCA/ICIiLmNvbmNhdChjb25maWcubGFiZWxXaWR0aCwgInB4IikgOiBudWxsOwogICAgaWYgKGNvbmZpZy5zaG93TGFiZWwgPT09IGZhbHNlKSBsYWJlbFdpZHRoID0gJzAnOwogICAgcmV0dXJuIGgoImVsLWNvbCIsIHsKICAgICAgImF0dHJzIjogewogICAgICAgICJzcGFuIjogY29uZmlnLnNwYW4KICAgICAgfSwKICAgICAgImNsYXNzIjogY2xhc3NOYW1lLAogICAgICAibmF0aXZlT24iOiB7CiAgICAgICAgImNsaWNrIjogZnVuY3Rpb24gY2xpY2soZXZlbnQpIHsKICAgICAgICAgIGFjdGl2ZUl0ZW0oY3VycmVudEl0ZW0pOwogICAgICAgICAgZXZlbnQuc3RvcFByb3BhZ2F0aW9uKCk7CiAgICAgICAgfQogICAgICB9CiAgICB9LCBbaCgiZWwtZm9ybS1pdGVtIiwgewogICAgICAiYXR0cnMiOiB7CiAgICAgICAgImxhYmVsLXdpZHRoIjogbGFiZWxXaWR0aCwKICAgICAgICAibGFiZWwiOiBjb25maWcuc2hvd0xhYmVsID8gY29uZmlnLmxhYmVsIDogJycsCiAgICAgICAgInJlcXVpcmVkIjogY29uZmlnLnJlcXVpcmVkCiAgICAgIH0KICAgIH0sIFtoKHJlbmRlciwgewogICAgICAia2V5IjogY29uZmlnLnJlbmRlcktleSwKICAgICAgImF0dHJzIjogewogICAgICAgICJjb25mIjogY3VycmVudEl0ZW0KICAgICAgfSwKICAgICAgIm9uIjogewogICAgICAgICJpbnB1dCI6IGZ1bmN0aW9uIGlucHV0KGV2ZW50KSB7CiAgICAgICAgICBfdGhpcy4kc2V0KGNvbmZpZywgJ2RlZmF1bHRWYWx1ZScsIGV2ZW50KTsKICAgICAgICB9CiAgICAgIH0KICAgIH0sIFtjaGlsZF0pXSksIGNvbXBvbmVudHMuaXRlbUJ0bnMuYXBwbHkodGhpcywgYXJndW1lbnRzKV0pOwogIH0sCiAgcm93Rm9ybUl0ZW06IGZ1bmN0aW9uIHJvd0Zvcm1JdGVtKGgsIGN1cnJlbnRJdGVtLCBpbmRleCwgbGlzdCkgewogICAgdmFyIGFjdGl2ZUl0ZW0gPSB0aGlzLiRsaXN0ZW5lcnMuYWN0aXZlSXRlbTsKICAgIHZhciBjb25maWcgPSBjdXJyZW50SXRlbS5fX2NvbmZpZ19fOwogICAgdmFyIGNsYXNzTmFtZSA9IHRoaXMuYWN0aXZlSWQgPT09IGNvbmZpZy5mb3JtSWQgPyAnZHJhd2luZy1yb3ctaXRlbSBhY3RpdmUtZnJvbS1pdGVtJyA6ICdkcmF3aW5nLXJvdy1pdGVtJzsKICAgIHZhciBjaGlsZCA9IHJlbmRlckNoaWxkcmVuLmFwcGx5KHRoaXMsIGFyZ3VtZW50cyk7CiAgICBpZiAoY3VycmVudEl0ZW0udHlwZSA9PT0gJ2ZsZXgnKSB7CiAgICAgIGNoaWxkID0gaCgiZWwtcm93IiwgewogICAgICAgICJhdHRycyI6IHsKICAgICAgICAgICJ0eXBlIjogY3VycmVudEl0ZW0udHlwZSwKICAgICAgICAgICJqdXN0aWZ5IjogY3VycmVudEl0ZW0uanVzdGlmeSwKICAgICAgICAgICJhbGlnbiI6IGN1cnJlbnRJdGVtLmFsaWduCiAgICAgICAgfQogICAgICB9LCBbY2hpbGRdKTsKICAgIH0KICAgIHJldHVybiBoKCJlbC1jb2wiLCB7CiAgICAgICJhdHRycyI6IHsKICAgICAgICAic3BhbiI6IGNvbmZpZy5zcGFuCiAgICAgIH0KICAgIH0sIFtoKCJlbC1yb3ciLCB7CiAgICAgICJhdHRycyI6IHsKICAgICAgICAiZ3V0dGVyIjogY29uZmlnLmd1dHRlcgogICAgICB9LAogICAgICAiY2xhc3MiOiBjbGFzc05hbWUsCiAgICAgICJuYXRpdmVPbiI6IHsKICAgICAgICAiY2xpY2siOiBmdW5jdGlvbiBjbGljayhldmVudCkgewogICAgICAgICAgYWN0aXZlSXRlbShjdXJyZW50SXRlbSk7CiAgICAgICAgICBldmVudC5zdG9wUHJvcGFnYXRpb24oKTsKICAgICAgICB9CiAgICAgIH0KICAgIH0sIFtoKCJzcGFuIiwgewogICAgICAiY2xhc3MiOiAiY29tcG9uZW50LW5hbWUiCiAgICB9LCBbY29uZmlnLmNvbXBvbmVudE5hbWVdKSwgaChkcmFnZ2FibGUsIHsKICAgICAgImF0dHJzIjogewogICAgICAgICJsaXN0IjogY29uZmlnLmNoaWxkcmVuIHx8IFtdLAogICAgICAgICJhbmltYXRpb24iOiAzNDAsCiAgICAgICAgImdyb3VwIjogImNvbXBvbmVudHNHcm91cCIKICAgICAgfSwKICAgICAgImNsYXNzIjogImRyYWctd3JhcHBlciIKICAgIH0sIFtjaGlsZF0pLCBjb21wb25lbnRzLml0ZW1CdG5zLmFwcGx5KHRoaXMsIGFyZ3VtZW50cyldKV0pOwogIH0sCiAgcmF3OiBmdW5jdGlvbiByYXcoaCwgY3VycmVudEl0ZW0sIGluZGV4LCBsaXN0KSB7CiAgICB2YXIgX3RoaXMyID0gdGhpczsKICAgIHZhciBjb25maWcgPSBjdXJyZW50SXRlbS5fX2NvbmZpZ19fOwogICAgdmFyIGNoaWxkID0gcmVuZGVyQ2hpbGRyZW4uYXBwbHkodGhpcywgYXJndW1lbnRzKTsKICAgIHJldHVybiBoKHJlbmRlciwgewogICAgICAia2V5IjogY29uZmlnLnJlbmRlcktleSwKICAgICAgImF0dHJzIjogewogICAgICAgICJjb25mIjogY3VycmVudEl0ZW0KICAgICAgfSwKICAgICAgIm9uIjogewogICAgICAgICJpbnB1dCI6IGZ1bmN0aW9uIGlucHV0KGV2ZW50KSB7CiAgICAgICAgICBfdGhpczIuJHNldChjb25maWcsICdkZWZhdWx0VmFsdWUnLCBldmVudCk7CiAgICAgICAgfQogICAgICB9CiAgICB9LCBbY2hpbGRdKTsKICB9Cn07CmZ1bmN0aW9uIHJlbmRlckNoaWxkcmVuKGgsIGN1cnJlbnRJdGVtLCBpbmRleCwgbGlzdCkgewogIHZhciBfdGhpczMgPSB0aGlzOwogIHZhciBjb25maWcgPSBjdXJyZW50SXRlbS5fX2NvbmZpZ19fOwogIGlmICghQXJyYXkuaXNBcnJheShjb25maWcuY2hpbGRyZW4pKSByZXR1cm4gbnVsbDsKICByZXR1cm4gY29uZmlnLmNoaWxkcmVuLm1hcChmdW5jdGlvbiAoZWwsIGkpIHsKICAgIHZhciBsYXlvdXQgPSBsYXlvdXRzW2VsLl9fY29uZmlnX18ubGF5b3V0XTsKICAgIGlmIChsYXlvdXQpIHsKICAgICAgcmV0dXJuIGxheW91dC5jYWxsKF90aGlzMywgaCwgZWwsIGksIGNvbmZpZy5jaGlsZHJlbik7CiAgICB9CiAgICByZXR1cm4gbGF5b3V0SXNOb3RGb3VuZC5jYWxsKF90aGlzMyk7CiAgfSk7Cn0KZnVuY3Rpb24gbGF5b3V0SXNOb3RGb3VuZCgpIHsKICB0aHJvdyBuZXcgRXJyb3IoIlx1NkNBMVx1NjcwOVx1NEUwRSIuY29uY2F0KHRoaXMuY3VycmVudEl0ZW0uX19jb25maWdfXy5sYXlvdXQsICJcdTUzMzlcdTkxNERcdTc2ODRsYXlvdXQiKSk7Cn0KZXhwb3J0IGRlZmF1bHQgewogIGNvbXBvbmVudHM6IHsKICAgIHJlbmRlcjogcmVuZGVyLAogICAgZHJhZ2dhYmxlOiBkcmFnZ2FibGUKICB9LAogIHByb3BzOiBbJ2N1cnJlbnRJdGVtJywgJ2luZGV4JywgJ2RyYXdpbmdMaXN0JywgJ2FjdGl2ZUlkJywgJ2Zvcm1Db25mJ10sCiAgcmVuZGVyOiBmdW5jdGlvbiByZW5kZXIoaCkgewogICAgdmFyIGxheW91dCA9IGxheW91dHNbdGhpcy5jdXJyZW50SXRlbS5fX2NvbmZpZ19fLmxheW91dF07CiAgICBpZiAobGF5b3V0KSB7CiAgICAgIHJldHVybiBsYXlvdXQuY2FsbCh0aGlzLCBoLCB0aGlzLmN1cnJlbnRJdGVtLCB0aGlzLmluZGV4LCB0aGlzLmRyYXdpbmdMaXN0KTsKICAgIH0KICAgIHJldHVybiBsYXlvdXRJc05vdEZvdW5kLmNhbGwodGhpcyk7CiAgfQp9Ow=="}, null]}