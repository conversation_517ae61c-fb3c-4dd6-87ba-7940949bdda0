{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/utils/request.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/utils/request.js", "mtime": 1716992753042}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["axios", "Notification", "MessageBox", "Message", "store", "getToken", "errorCode", "defaults", "headers", "service", "create", "baseURL", "process", "env", "VUE_APP_BASE_API", "timeout", "interceptors", "request", "use", "config", "isToken", "method", "params", "url", "_i", "_Object$keys", "Object", "keys", "length", "propName", "value", "part", "encodeURIComponent", "_typeof", "_i2", "_Object$keys2", "key", "subPart", "slice", "error", "console", "log", "Promise", "reject", "response", "res", "code", "data", "msg", "confirm", "confirmButtonText", "cancelButtonText", "type", "then", "dispatch", "location", "href", "message", "Error", "title", "includes", "substr", "duration"], "sources": ["/Users/<USER>/develop/code/project/haijia/adminweb/src/utils/request.js"], "sourcesContent": ["import axios from \"axios\";\nimport { Notification, MessageBox, Message } from \"element-ui\";\nimport store from \"@/store\";\nimport { getToken } from \"@/utils/auth\";\nimport errorCode from \"@/utils/errorCode\";\n\naxios.defaults.headers[\"Content-Type\"] = \"application/json;charset=utf-8\";\n// 创建axios实例\nconst service = axios.create({\n  // axios中请求配置有baseURL选项，表示请求URL公共部分\n  // baseURL: \"http://localhost:8080\",\n  baseURL: process.env.VUE_APP_BASE_API,\n  // 超时\n  timeout: 10000,\n});\n// request拦截器\nservice.interceptors.request.use(\n  (config) => {\n    // 是否需要设置 token\n    const isToken = (config.headers || {}).isToken === false;\n    if (getToken() && !isToken) {\n      config.headers[\"Authorization\"] = \"Bearer \" + getToken(); // 让每个请求携带自定义token 请根据实际情况自行修改\n    }\n    // get请求映射params参数\n    if (config.method === \"get\" && config.params) {\n      let url = config.url + \"?\";\n      for (const propName of Object.keys(config.params)) {\n        const value = config.params[propName];\n        var part = encodeURIComponent(propName) + \"=\";\n        if (value !== null && typeof value !== \"undefined\") {\n          if (typeof value === \"object\") {\n            for (const key of Object.keys(value)) {\n              let params = propName + \"[\" + key + \"]\";\n              var subPart = encodeURIComponent(params) + \"=\";\n              url += subPart + encodeURIComponent(value[key]) + \"&\";\n            }\n          } else {\n            url += part + encodeURIComponent(value) + \"&\";\n          }\n        }\n      }\n      url = url.slice(0, -1);\n      config.params = {};\n      config.url = url;\n    }\n    return config;\n  },\n  (error) => {\n    console.log(error);\n    Promise.reject(error);\n  }\n);\n\n// 响应拦截器\nservice.interceptors.response.use(\n  (res) => {\n    // 未设置状态码则默认成功状态\n    const code = res.data.code || 200;\n    // 获取错误信息\n    const msg = errorCode[code] || res.data.msg || errorCode[\"default\"];\n    if (code === 401) {\n      MessageBox.confirm(\n        \"登录状态已过期，您可以继续留在该页面，或者重新登录\",\n        \"系统提示\",\n        {\n          confirmButtonText: \"重新登录\",\n          cancelButtonText: \"取消\",\n          type: \"warning\",\n        }\n      ).then(() => {\n        store.dispatch(\"LogOut\").then(() => {\n          location.href = \"/index\";\n        });\n      });\n    } else if (code === 500) {\n      Message({\n        message: msg,\n        type: \"error\",\n      });\n      return Promise.reject(new Error(msg));\n    } else if (code !== 200) {\n      Notification.error({\n        title: msg,\n      });\n      return Promise.reject(\"error\");\n    } else {\n      return res.data;\n    }\n  },\n  (error) => {\n    console.log(\"err\" + error);\n    let { message } = error;\n    if (message == \"Network Error\") {\n      message = \"后端接口连接异常\";\n    } else if (message.includes(\"timeout\")) {\n      message = \"系统接口请求超时\";\n    } else if (message.includes(\"Request failed with status code\")) {\n      message = \"系统接口\" + message.substr(message.length - 3) + \"异常\";\n    }\n    Message({\n      message: message,\n      type: \"error\",\n      duration: 5 * 1000,\n    });\n    return Promise.reject(error);\n  }\n);\n\nexport default service;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,YAAY,EAAEC,UAAU,EAAEC,OAAO,QAAQ,YAAY;AAC9D,OAAOC,KAAK,MAAM,SAAS;AAC3B,SAASC,QAAQ,QAAQ,cAAc;AACvC,OAAOC,SAAS,MAAM,mBAAmB;AAEzCN,KAAK,CAACO,QAAQ,CAACC,OAAO,CAAC,cAAc,CAAC,GAAG,gCAAgC;AACzE;AACA,IAAMC,OAAO,GAAGT,KAAK,CAACU,MAAM,CAAC;EAC3B;EACA;EACAC,OAAO,EAAEC,OAAO,CAACC,GAAG,CAACC,gBAAgB;EACrC;EACAC,OAAO,EAAE;AACX,CAAC,CAAC;AACF;AACAN,OAAO,CAACO,YAAY,CAACC,OAAO,CAACC,GAAG,CAC9B,UAACC,MAAM,EAAK;EACV;EACA,IAAMC,OAAO,GAAG,CAACD,MAAM,CAACX,OAAO,IAAI,CAAC,CAAC,EAAEY,OAAO,KAAK,KAAK;EACxD,IAAIf,QAAQ,CAAC,CAAC,IAAI,CAACe,OAAO,EAAE;IAC1BD,MAAM,CAACX,OAAO,CAAC,eAAe,CAAC,GAAG,SAAS,GAAGH,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5D;EACA;EACA,IAAIc,MAAM,CAACE,MAAM,KAAK,KAAK,IAAIF,MAAM,CAACG,MAAM,EAAE;IAC5C,IAAIC,GAAG,GAAGJ,MAAM,CAACI,GAAG,GAAG,GAAG;IAC1B,SAAAC,EAAA,MAAAC,YAAA,GAAuBC,MAAM,CAACC,IAAI,CAACR,MAAM,CAACG,MAAM,CAAC,EAAAE,EAAA,GAAAC,YAAA,CAAAG,MAAA,EAAAJ,EAAA,IAAE;MAA9C,IAAMK,QAAQ,GAAAJ,YAAA,CAAAD,EAAA;MACjB,IAAMM,KAAK,GAAGX,MAAM,CAACG,MAAM,CAACO,QAAQ,CAAC;MACrC,IAAIE,IAAI,GAAGC,kBAAkB,CAACH,QAAQ,CAAC,GAAG,GAAG;MAC7C,IAAIC,KAAK,KAAK,IAAI,IAAI,OAAOA,KAAK,KAAK,WAAW,EAAE;QAClD,IAAIG,OAAA,CAAOH,KAAK,MAAK,QAAQ,EAAE;UAC7B,SAAAI,GAAA,MAAAC,aAAA,GAAkBT,MAAM,CAACC,IAAI,CAACG,KAAK,CAAC,EAAAI,GAAA,GAAAC,aAAA,CAAAP,MAAA,EAAAM,GAAA,IAAE;YAAjC,IAAME,GAAG,GAAAD,aAAA,CAAAD,GAAA;YACZ,IAAIZ,MAAM,GAAGO,QAAQ,GAAG,GAAG,GAAGO,GAAG,GAAG,GAAG;YACvC,IAAIC,OAAO,GAAGL,kBAAkB,CAACV,MAAM,CAAC,GAAG,GAAG;YAC9CC,GAAG,IAAIc,OAAO,GAAGL,kBAAkB,CAACF,KAAK,CAACM,GAAG,CAAC,CAAC,GAAG,GAAG;UACvD;QACF,CAAC,MAAM;UACLb,GAAG,IAAIQ,IAAI,GAAGC,kBAAkB,CAACF,KAAK,CAAC,GAAG,GAAG;QAC/C;MACF;IACF;IACAP,GAAG,GAAGA,GAAG,CAACe,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACtBnB,MAAM,CAACG,MAAM,GAAG,CAAC,CAAC;IAClBH,MAAM,CAACI,GAAG,GAAGA,GAAG;EAClB;EACA,OAAOJ,MAAM;AACf,CAAC,EACD,UAACoB,KAAK,EAAK;EACTC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;EAClBG,OAAO,CAACC,MAAM,CAACJ,KAAK,CAAC;AACvB,CACF,CAAC;;AAED;AACA9B,OAAO,CAACO,YAAY,CAAC4B,QAAQ,CAAC1B,GAAG,CAC/B,UAAC2B,GAAG,EAAK;EACP;EACA,IAAMC,IAAI,GAAGD,GAAG,CAACE,IAAI,CAACD,IAAI,IAAI,GAAG;EACjC;EACA,IAAME,GAAG,GAAG1C,SAAS,CAACwC,IAAI,CAAC,IAAID,GAAG,CAACE,IAAI,CAACC,GAAG,IAAI1C,SAAS,CAAC,SAAS,CAAC;EACnE,IAAIwC,IAAI,KAAK,GAAG,EAAE;IAChB5C,UAAU,CAAC+C,OAAO,CAChB,2BAA2B,EAC3B,MAAM,EACN;MACEC,iBAAiB,EAAE,MAAM;MACzBC,gBAAgB,EAAE,IAAI;MACtBC,IAAI,EAAE;IACR,CACF,CAAC,CAACC,IAAI,CAAC,YAAM;MACXjD,KAAK,CAACkD,QAAQ,CAAC,QAAQ,CAAC,CAACD,IAAI,CAAC,YAAM;QAClCE,QAAQ,CAACC,IAAI,GAAG,QAAQ;MAC1B,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,MAAM,IAAIV,IAAI,KAAK,GAAG,EAAE;IACvB3C,OAAO,CAAC;MACNsD,OAAO,EAAET,GAAG;MACZI,IAAI,EAAE;IACR,CAAC,CAAC;IACF,OAAOV,OAAO,CAACC,MAAM,CAAC,IAAIe,KAAK,CAACV,GAAG,CAAC,CAAC;EACvC,CAAC,MAAM,IAAIF,IAAI,KAAK,GAAG,EAAE;IACvB7C,YAAY,CAACsC,KAAK,CAAC;MACjBoB,KAAK,EAAEX;IACT,CAAC,CAAC;IACF,OAAON,OAAO,CAACC,MAAM,CAAC,OAAO,CAAC;EAChC,CAAC,MAAM;IACL,OAAOE,GAAG,CAACE,IAAI;EACjB;AACF,CAAC,EACD,UAACR,KAAK,EAAK;EACTC,OAAO,CAACC,GAAG,CAAC,KAAK,GAAGF,KAAK,CAAC;EAC1B,IAAMkB,OAAO,GAAKlB,KAAK,CAAjBkB,OAAO;EACb,IAAIA,OAAO,IAAI,eAAe,EAAE;IAC9BA,OAAO,GAAG,UAAU;EACtB,CAAC,MAAM,IAAIA,OAAO,CAACG,QAAQ,CAAC,SAAS,CAAC,EAAE;IACtCH,OAAO,GAAG,UAAU;EACtB,CAAC,MAAM,IAAIA,OAAO,CAACG,QAAQ,CAAC,iCAAiC,CAAC,EAAE;IAC9DH,OAAO,GAAG,MAAM,GAAGA,OAAO,CAACI,MAAM,CAACJ,OAAO,CAAC7B,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI;EAC9D;EACAzB,OAAO,CAAC;IACNsD,OAAO,EAAEA,OAAO;IAChBL,IAAI,EAAE,OAAO;IACbU,QAAQ,EAAE,CAAC,GAAG;EAChB,CAAC,CAAC;EACF,OAAOpB,OAAO,CAACC,MAAM,CAACJ,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,eAAe9B,OAAO", "ignoreList": []}]}