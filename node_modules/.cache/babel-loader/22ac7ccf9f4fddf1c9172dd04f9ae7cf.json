{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/layout/components/Sidebar/Logo.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/layout/components/Sidebar/Logo.vue", "mtime": 1660751098000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBsb2dvSW1nIGZyb20gJ0AvYXNzZXRzL2xvZ28vbG9nby5wbmcnOwppbXBvcnQgX3ZhcmlhYmxlcyBmcm9tICdAL2Fzc2V0cy9zdHlsZXMvdmFyaWFibGVzLnNjc3MnOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ1NpZGViYXJMb2dvJywKICBwcm9wczogewogICAgY29sbGFwc2U6IHsKICAgICAgdHlwZTogQm9vbGVhbiwKICAgICAgcmVxdWlyZWQ6IHRydWUKICAgIH0KICB9LAogIGNvbXB1dGVkOiB7CiAgICB2YXJpYWJsZXM6IGZ1bmN0aW9uIHZhcmlhYmxlcygpIHsKICAgICAgcmV0dXJuIF92YXJpYWJsZXM7CiAgICB9LAogICAgc2lkZVRoZW1lOiBmdW5jdGlvbiBzaWRlVGhlbWUoKSB7CiAgICAgIHJldHVybiB0aGlzLiRzdG9yZS5zdGF0ZS5zZXR0aW5ncy5zaWRlVGhlbWU7CiAgICB9CiAgfSwKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgdGl0bGU6ICfpobnnm67nrqHnkIbns7vnu58nLAogICAgICBsb2dvOiBsb2dvSW1nCiAgICB9OwogIH0KfTs="}, {"version": 3, "names": ["logoImg", "variables", "name", "props", "collapse", "type", "Boolean", "required", "computed", "sideTheme", "$store", "state", "settings", "data", "title", "logo"], "sources": ["src/layout/components/Sidebar/Logo.vue"], "sourcesContent": ["<template>\n  <div class=\"sidebar-logo-container\" :class=\"{'collapse':collapse}\">\n    <transition name=\"sidebarLogoFade\">\n      <router-link v-if=\"collapse\" key=\"collapse\" class=\"sidebar-logo-link\" to=\"/\">\n        <img v-if=\"logo\" :src=\"logo\" class=\"sidebar-logo\">\n        <h1 v-else class=\"sidebar-title\" style=\"color: #000;\">{{ title }} </h1>\n      </router-link>\n      <router-link v-else key=\"expand\" class=\"sidebar-logo-link\" to=\"/\">\n        <img v-if=\"logo\" :src=\"logo\" class=\"sidebar-logo\">\n        <h1 class=\"sidebar-title\" style=\"color: #000;\">{{ title }} </h1>\n      </router-link>\n    </transition>\n  </div>\n</template>\n\n<script>\nimport logoImg from '@/assets/logo/logo.png'\nimport variables from '@/assets/styles/variables.scss'\n\nexport default {\n  name: 'SidebarLogo',\n  props: {\n    collapse: {\n      type: Boolean,\n      required: true\n    }\n  },\n  computed: {\n    variables() {\n      return variables;\n    },\n\tsideTheme() {\n      return this.$store.state.settings.sideTheme\n    }\n  },\n  data() {\n    return {\n      title: '项目管理系统',\n      logo: logoImg\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.sidebarLogoFade-enter-active {\n  transition: opacity 1.5s;\n}\n\n.sidebarLogoFade-enter,\n.sidebarLogoFade-leave-to {\n  opacity: 0;\n}\n\n.sidebar-logo-container {\n  position: relative;\n  width: 100%;\n  background: #fff;\n  text-align: center;\n  overflow: hidden;\n\n  & .sidebar-logo-link {\n    height: 100%;\n    width: 100%;\n\n    & .sidebar-logo {\n      margin: 5px 0 5px 0;\n      background: #fefefe;\n      height: 48px;\n      vertical-align: middle;\n    }\n\n    & .sidebar-title {\n      display: inline-block;\n      margin: 0;\n      color: #fff;\n      font-weight: 600;\n      font-size: 14px;\n      font-family: Avenir, Helvetica Neue, Arial, Helvetica, sans-serif;\n      vertical-align: middle;\n      padding-bottom: 6px;\n    }\n  }\n\n  &.collapse {\n    .sidebar-logo {\n      margin-right: 0px;\n    }\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;AAgBA,OAAAA,OAAA;AACA,OAAAC,UAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACAC,QAAA;MACAC,IAAA,EAAAC,OAAA;MACAC,QAAA;IACA;EACA;EACAC,QAAA;IACAP,SAAA,WAAAA,UAAA;MACA,OAAAA,UAAA;IACA;IACAQ,SAAA,WAAAA,UAAA;MACA,YAAAC,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAH,SAAA;IACA;EACA;EACAI,IAAA,WAAAA,KAAA;IACA;MACAC,KAAA;MACAC,IAAA,EAAAf;IACA;EACA;AACA", "ignoreList": []}]}