{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/api/flowable/process.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/api/flowable/process.js", "mtime": 1641277326000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QgZnJvbSAnQC91dGlscy9yZXF1ZXN0JzsKaW1wb3J0IGRhIGZyb20gImVsZW1lbnQtdWkvc3JjL2xvY2FsZS9sYW5nL2RhIjsKCi8vIOaIkeeahOWPkei1t+eahOa1geeoiwpleHBvcnQgZnVuY3Rpb24gbXlQcm9jZXNzTGlzdChxdWVyeSkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9mbG93YWJsZS90YXNrL215UHJvY2VzcycsCiAgICBtZXRob2Q6ICdnZXQnLAogICAgcGFyYW1zOiBxdWVyeQogIH0pOwp9CgovLyDlrozmiJDku7vliqEKZXhwb3J0IGZ1bmN0aW9uIGNvbXBsZXRlKGRhdGEpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvZmxvd2FibGUvdGFzay9jb21wbGV0ZScsCiAgICBtZXRob2Q6ICdwb3N0JywKICAgIGRhdGE6IGRhdGEKICB9KTsKfQoKLy8g5Y+W5raI55Sz6K+3CmV4cG9ydCBmdW5jdGlvbiBzdG9wUHJvY2VzcyhkYXRhKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL2Zsb3dhYmxlL3Rhc2svc3RvcFByb2Nlc3MnLAogICAgbWV0aG9kOiAncG9zdCcsCiAgICBkYXRhOiBkYXRhCiAgfSk7Cn0KCi8vIOmps+WbnuS7u+WKoQpleHBvcnQgZnVuY3Rpb24gcmVqZWN0VGFzayhkYXRhKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL2Zsb3dhYmxlL3Rhc2svcmVqZWN0JywKICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgZGF0YTogZGF0YQogIH0pOwp9CgovLyDlj6/pgIDlm57ku7vliqHliJfooagKZXhwb3J0IGZ1bmN0aW9uIHJldHVybkxpc3QoZGF0YSkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9mbG93YWJsZS90YXNrL3JldHVybkxpc3QnLAogICAgbWV0aG9kOiAncG9zdCcsCiAgICBkYXRhOiBkYXRhCiAgfSk7Cn0KCi8vIOmDqOe9sua1geeoi+WunuS+iwpleHBvcnQgZnVuY3Rpb24gZGVwbG95U3RhcnQoZGVwbG95SWQpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvZmxvd2FibGUvcHJvY2Vzcy9zdGFydEZsb3cvJyArIGRlcGxveUlkLAogICAgbWV0aG9kOiAnZ2V0JwogIH0pOwp9CgovLyDmn6Xor6LmtYHnqIvlrprkuYnor6bnu4YKZXhwb3J0IGZ1bmN0aW9uIGdldERlcGxveW1lbnQoaWQpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvc3lzdGVtL2RlcGxveW1lbnQvJyArIGlkLAogICAgbWV0aG9kOiAnZ2V0JwogIH0pOwp9CgovLyDmlrDlop7mtYHnqIvlrprkuYkKZXhwb3J0IGZ1bmN0aW9uIGFkZERlcGxveW1lbnQoZGF0YSkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9zeXN0ZW0vZGVwbG95bWVudCcsCiAgICBtZXRob2Q6ICdwb3N0JywKICAgIGRhdGE6IGRhdGEKICB9KTsKfQoKLy8g5L+u5pS55rWB56iL5a6a5LmJCmV4cG9ydCBmdW5jdGlvbiB1cGRhdGVEZXBsb3ltZW50KGRhdGEpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvc3lzdGVtL2RlcGxveW1lbnQnLAogICAgbWV0aG9kOiAncHV0JywKICAgIGRhdGE6IGRhdGEKICB9KTsKfQoKLy8g5Yig6Zmk5rWB56iL5a6a5LmJCmV4cG9ydCBmdW5jdGlvbiBkZWxEZXBsb3ltZW50KGlkKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL3N5c3RlbS9kZXBsb3ltZW50LycgKyBpZCwKICAgIG1ldGhvZDogJ2RlbGV0ZScKICB9KTsKfQoKLy8g5a+85Ye65rWB56iL5a6a5LmJCmV4cG9ydCBmdW5jdGlvbiBleHBvcnREZXBsb3ltZW50KHF1ZXJ5KSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL3N5c3RlbS9kZXBsb3ltZW50L2V4cG9ydCcsCiAgICBtZXRob2Q6ICdnZXQnLAogICAgcGFyYW1zOiBxdWVyeQogIH0pOwp9"}, {"version": 3, "names": ["request", "da", "myProcessList", "query", "url", "method", "params", "complete", "data", "stopProcess", "rejectTask", "returnList", "deployStart", "deployId", "getDeployment", "id", "addDeployment", "updateDeployment", "delDeployment", "exportDeployment"], "sources": ["/Users/<USER>/develop/code/project/haijia/adminweb/src/api/flowable/process.js"], "sourcesContent": ["import request from '@/utils/request'\nimport da from \"element-ui/src/locale/lang/da\";\n\n// 我的发起的流程\nexport function myProcessList(query) {\n  return request({\n    url: '/flowable/task/myProcess',\n    method: 'get',\n    params: query\n  })\n}\n\n// 完成任务\nexport function complete(data) {\n  return request({\n    url: '/flowable/task/complete',\n    method: 'post',\n    data: data\n  })\n}\n\n// 取消申请\nexport function stopProcess(data) {\n  return request({\n    url: '/flowable/task/stopProcess',\n    method: 'post',\n    data: data\n  })\n}\n\n// 驳回任务\nexport function rejectTask(data) {\n  return request({\n    url: '/flowable/task/reject',\n    method: 'post',\n    data: data\n  })\n}\n\n// 可退回任务列表\nexport function returnList(data) {\n  return request({\n    url: '/flowable/task/returnList',\n    method: 'post',\n    data: data\n  })\n}\n\n// 部署流程实例\nexport function deployStart(deployId) {\n  return request({\n    url: '/flowable/process/startFlow/' + deployId,\n    method: 'get',\n  })\n}\n\n// 查询流程定义详细\nexport function getDeployment(id) {\n  return request({\n    url: '/system/deployment/' + id,\n    method: 'get'\n  })\n}\n\n// 新增流程定义\nexport function addDeployment(data) {\n  return request({\n    url: '/system/deployment',\n    method: 'post',\n    data: data\n  })\n}\n\n// 修改流程定义\nexport function updateDeployment(data) {\n  return request({\n    url: '/system/deployment',\n    method: 'put',\n    data: data\n  })\n}\n\n// 删除流程定义\nexport function delDeployment(id) {\n  return request({\n    url: '/system/deployment/' + id,\n    method: 'delete'\n  })\n}\n\n// 导出流程定义\nexport function exportDeployment(query) {\n  return request({\n    url: '/system/deployment/export',\n    method: 'get',\n    params: query\n  })\n}\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;AACrC,OAAOC,EAAE,MAAM,+BAA+B;;AAE9C;AACA,OAAO,SAASC,aAAaA,CAACC,KAAK,EAAE;EACnC,OAAOH,OAAO,CAAC;IACbI,GAAG,EAAE,0BAA0B;IAC/BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEH;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASI,QAAQA,CAACC,IAAI,EAAE;EAC7B,OAAOR,OAAO,CAAC;IACbI,GAAG,EAAE,yBAAyB;IAC9BC,MAAM,EAAE,MAAM;IACdG,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASC,WAAWA,CAACD,IAAI,EAAE;EAChC,OAAOR,OAAO,CAAC;IACbI,GAAG,EAAE,4BAA4B;IACjCC,MAAM,EAAE,MAAM;IACdG,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASE,UAAUA,CAACF,IAAI,EAAE;EAC/B,OAAOR,OAAO,CAAC;IACbI,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE,MAAM;IACdG,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASG,UAAUA,CAACH,IAAI,EAAE;EAC/B,OAAOR,OAAO,CAAC;IACbI,GAAG,EAAE,2BAA2B;IAChCC,MAAM,EAAE,MAAM;IACdG,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASI,WAAWA,CAACC,QAAQ,EAAE;EACpC,OAAOb,OAAO,CAAC;IACbI,GAAG,EAAE,8BAA8B,GAAGS,QAAQ;IAC9CR,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASS,aAAaA,CAACC,EAAE,EAAE;EAChC,OAAOf,OAAO,CAAC;IACbI,GAAG,EAAE,qBAAqB,GAAGW,EAAE;IAC/BV,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASW,aAAaA,CAACR,IAAI,EAAE;EAClC,OAAOR,OAAO,CAAC;IACbI,GAAG,EAAE,oBAAoB;IACzBC,MAAM,EAAE,MAAM;IACdG,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASS,gBAAgBA,CAACT,IAAI,EAAE;EACrC,OAAOR,OAAO,CAAC;IACbI,GAAG,EAAE,oBAAoB;IACzBC,MAAM,EAAE,KAAK;IACbG,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASU,aAAaA,CAACH,EAAE,EAAE;EAChC,OAAOf,OAAO,CAAC;IACbI,GAAG,EAAE,qBAAqB,GAAGW,EAAE;IAC/BV,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASc,gBAAgBA,CAAChB,KAAK,EAAE;EACtC,OAAOH,OAAO,CAAC;IACbI,GAAG,EAAE,2BAA2B;IAChCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEH;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}