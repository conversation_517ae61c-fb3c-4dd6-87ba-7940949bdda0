{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/tool/build/App.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/tool/build/App.vue", "mtime": 1641277326000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KCmV4cG9ydCBkZWZhdWx0IHsKICBtb3VudGVkOiBmdW5jdGlvbiBtb3VudGVkKCkgewogICAgLy8g5Y+W5raI5byA5aeL55qEbG9hZGluZ+WKqOeUuwogICAgdmFyIHByZUxvYWRlciA9IGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3IoJyNwcmUtbG9hZGVyJyk7CiAgICBwcmVMb2FkZXIuc3R5bGUuZGlzcGxheSA9ICdub25lJzsKCiAgICAvLyBmaXg6IGZpcmVmb3gg5LiLIOaLluaLvSDkvJrmlrDmiZPljaHkuIDkuKrpgInpobnljaEKICAgIC8vIGh0dHBzOi8vZ2l0aHViLmNvbS9KYWtIdWFuZy9mb3JtLWdlbmVyYXRvci9pc3N1ZXMvMTUKICAgIGRvY3VtZW50LmJvZHkub25kcm9wID0gZnVuY3Rpb24gKGV2ZW50KSB7CiAgICAgIGV2ZW50LnByZXZlbnREZWZhdWx0KCk7CiAgICAgIGV2ZW50LnN0b3BQcm9wYWdhdGlvbigpOwogICAgfTsKICB9Cn07"}, {"version": 3, "names": ["mounted", "<PERSON>L<PERSON><PERSON>", "document", "querySelector", "style", "display", "body", "ondrop", "event", "preventDefault", "stopPropagation"], "sources": ["src/views/tool/build/App.vue"], "sourcesContent": ["<template>\n  <div>\n    <router-view />\n  </div>\n</template>\n\n<script>\nexport default {\n  mounted() {\n    // 取消开始的loading动画\n    const preLoader = document.querySelector('#pre-loader')\n    preLoader.style.display = 'none'\n\n    // fix: firefox 下 拖拽 会新打卡一个选项卡\n    // https://github.com/JakHuang/form-generator/issues/15\n    document.body.ondrop = event => {\n      event.preventDefault()\n      event.stopPropagation()\n    }\n  }\n}\n</script>\n"], "mappings": ";;;;;;;AAOA;EACAA,OAAA,WAAAA,QAAA;IACA;IACA,IAAAC,SAAA,GAAAC,QAAA,CAAAC,aAAA;IACAF,SAAA,CAAAG,KAAA,CAAAC,OAAA;;IAEA;IACA;IACAH,QAAA,CAAAI,IAAA,CAAAC,MAAA,aAAAC,KAAA;MACAA,KAAA,CAAAC,cAAA;MACAD,KAAA,CAAAE,eAAA;IACA;EACA;AACA", "ignoreList": []}]}