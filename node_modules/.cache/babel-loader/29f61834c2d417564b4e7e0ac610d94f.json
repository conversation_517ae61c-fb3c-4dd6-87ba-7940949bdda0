{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/utils/generator/html.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/utils/generator/html.js", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["trigger", "confGlobal", "someSpanIsNot24", "dialogWrapper", "str", "concat", "vueTemplate", "vueScript", "cssStyle", "cssStr", "buildFormTemplate", "conf", "child", "type", "labelPosition", "disabled", "formRef", "formModel", "formRules", "size", "labelWidth", "buildFromBtns", "gutter", "formBtns", "colWrapper", "element", "span", "layouts", "colFormItem", "required", "tag", "tagDom", "tags", "label", "vModel", "rowFormItem", "justify", "align", "children", "map", "el", "layout", "join", "elButton", "_attrBuilder", "attrBuilder", "icon", "buildElButtonChild", "elInput", "_attrBuilder2", "clearable", "placeholder", "width", "maxlength", "showWordLimit", "readonly", "prefixIcon", "suffixIcon", "showPassword", "autosize", "minRows", "maxRows", "buildElInputChild", "elInputNumber", "_attrBuilder3", "controlsPosition", "min", "max", "step", "stepStrictly", "precision", "elSelect", "_attrBuilder4", "filterable", "multiple", "buildElSelectChild", "elRadioGroup", "_attrBuilder5", "buildElRadioGroupChild", "elCheckboxGroup", "_attrBuilder6", "buildElCheckboxGroupChild", "elSwitch", "_attrBuilder7", "activeText", "inactiveText", "activeColor", "inactiveColor", "activeValue", "JSON", "stringify", "inactiveValue", "elCascader", "_attrBuilder8", "options", "props", "showAllLevels", "separator", "<PERSON><PERSON><PERSON><PERSON>", "_attrBuilder9", "range", "showStops", "elTimePicker", "_attrBuilder10", "startPlaceholder", "endPlaceholder", "rangeSeparator", "isRange", "format", "valueFormat", "pickerOptions", "elDatePicker", "_attrBuilder11", "elRate", "_attrBuilder12", "allowHalf", "showText", "showScore", "elColorPicker", "_attrBuilder13", "showAlpha", "colorFormat", "elUpload", "action", "listType", "accept", "name", "autoUpload", "beforeUpload", "fileList", "ref", "buildElUploadChild", "style", "default", "push", "prepend", "append", "length", "optionType", "border", "list", "buttonText", "showTip", "fileSize", "sizeUnit", "makeUpHtml", "htmlList", "fields", "some", "item", "for<PERSON>ach", "htmlStr", "temp"], "sources": ["/Users/<USER>/develop/code/project/haijia/adminweb/src/utils/generator/html.js"], "sourcesContent": ["/* eslint-disable max-len */\nimport { trigger } from './config'\n\nlet confGlobal\nlet someSpanIsNot24\n\nexport function dialogWrapper(str) {\n  return `<el-dialog v-bind=\"$attrs\" v-on=\"$listeners\" @open=\"onOpen\" @close=\"onClose\" title=\"Dialog Titile\">\n    ${str}\n    <div slot=\"footer\">\n      <el-button @click=\"close\">取消</el-button>\n      <el-button type=\"primary\" @click=\"handelConfirm\">确定</el-button>\n    </div>\n  </el-dialog>`\n}\n\nexport function vueTemplate(str) {\n  return `<template>\n    <div>\n      ${str}\n    </div>\n  </template>`\n}\n\nexport function vueScript(str) {\n  return `<script>\n    ${str}\n  </script>`\n}\n\nexport function cssStyle(cssStr) {\n  return `<style>\n    ${cssStr}\n  </style>`\n}\n\nfunction buildFormTemplate(conf, child, type) {\n  let labelPosition = ''\n  if (conf.labelPosition !== 'right') {\n    labelPosition = `label-position=\"${conf.labelPosition}\"`\n  }\n  const disabled = conf.disabled ? `:disabled=\"${conf.disabled}\"` : ''\n  let str = `<el-form ref=\"${conf.formRef}\" :model=\"${conf.formModel}\" :rules=\"${conf.formRules}\" size=\"${conf.size}\" ${disabled} label-width=\"${conf.labelWidth}px\" ${labelPosition}>\n      ${child}\n      ${buildFromBtns(conf, type)}\n    </el-form>`\n  if (someSpanIsNot24) {\n    str = `<el-row :gutter=\"${conf.gutter}\">\n        ${str}\n      </el-row>`\n  }\n  return str\n}\n\nfunction buildFromBtns(conf, type) {\n  let str = ''\n  if (conf.formBtns && type === 'file') {\n    str = `<el-form-item size=\"large\">\n          <el-button type=\"primary\" @click=\"submitForm\">提交</el-button>\n          <el-button @click=\"resetForm\">重置</el-button>\n        </el-form-item>`\n    if (someSpanIsNot24) {\n      str = `<el-col :span=\"24\">\n          ${str}\n        </el-col>`\n    }\n  }\n  return str\n}\n\n// span不为24的用el-col包裹\nfunction colWrapper(element, str) {\n  if (someSpanIsNot24 || element.span !== 24) {\n    return `<el-col :span=\"${element.span}\">\n      ${str}\n    </el-col>`\n  }\n  return str\n}\n\nconst layouts = {\n  colFormItem(element) {\n    let labelWidth = ''\n    if (element.labelWidth && element.labelWidth !== confGlobal.labelWidth) {\n      labelWidth = `label-width=\"${element.labelWidth}px\"`\n    }\n    const required = !trigger[element.tag] && element.required ? 'required' : ''\n    const tagDom = tags[element.tag] ? tags[element.tag](element) : null\n    let str = `<el-form-item ${labelWidth} label=\"${element.label}\" prop=\"${element.vModel}\" ${required}>\n        ${tagDom}\n      </el-form-item>`\n    str = colWrapper(element, str)\n    return str\n  },\n  rowFormItem(element) {\n    const type = element.type === 'default' ? '' : `type=\"${element.type}\"`\n    const justify = element.type === 'default' ? '' : `justify=\"${element.justify}\"`\n    const align = element.type === 'default' ? '' : `align=\"${element.align}\"`\n    const gutter = element.gutter ? `gutter=\"${element.gutter}\"` : ''\n    const children = element.children.map(el => layouts[el.layout](el))\n    let str = `<el-row ${type} ${justify} ${align} ${gutter}>\n      ${children.join('\\n')}\n    </el-row>`\n    str = colWrapper(element, str)\n    return str\n  }\n}\n\nconst tags = {\n  'el-button': el => {\n    const {\n      tag, disabled\n    } = attrBuilder(el)\n    const type = el.type ? `type=\"${el.type}\"` : ''\n    const icon = el.icon ? `icon=\"${el.icon}\"` : ''\n    const size = el.size ? `size=\"${el.size}\"` : ''\n    let child = buildElButtonChild(el)\n\n    if (child) child = `\\n${child}\\n` // 换行\n    return `<${el.tag} ${type} ${icon} ${size} ${disabled}>${child}</${el.tag}>`\n  },\n  'el-input': el => {\n    const {\n      disabled, vModel, clearable, placeholder, width\n    } = attrBuilder(el)\n    const maxlength = el.maxlength ? `:maxlength=\"${el.maxlength}\"` : ''\n    const showWordLimit = el['show-word-limit'] ? 'show-word-limit' : ''\n    const readonly = el.readonly ? 'readonly' : ''\n    const prefixIcon = el['prefix-icon'] ? `prefix-icon='${el['prefix-icon']}'` : ''\n    const suffixIcon = el['suffix-icon'] ? `suffix-icon='${el['suffix-icon']}'` : ''\n    const showPassword = el['show-password'] ? 'show-password' : ''\n    const type = el.type ? `type=\"${el.type}\"` : ''\n    const autosize = el.autosize && el.autosize.minRows\n      ? `:autosize=\"{minRows: ${el.autosize.minRows}, maxRows: ${el.autosize.maxRows}}\"`\n      : ''\n    let child = buildElInputChild(el)\n\n    if (child) child = `\\n${child}\\n` // 换行\n    return `<${el.tag} ${vModel} ${type} ${placeholder} ${maxlength} ${showWordLimit} ${readonly} ${disabled} ${clearable} ${prefixIcon} ${suffixIcon} ${showPassword} ${autosize} ${width}>${child}</${el.tag}>`\n  },\n  'el-input-number': el => {\n    const { disabled, vModel, placeholder } = attrBuilder(el)\n    const controlsPosition = el['controls-position'] ? `controls-position=${el['controls-position']}` : ''\n    const min = el.min ? `:min='${el.min}'` : ''\n    const max = el.max ? `:max='${el.max}'` : ''\n    const step = el.step ? `:step='${el.step}'` : ''\n    const stepStrictly = el['step-strictly'] ? 'step-strictly' : ''\n    const precision = el.precision ? `:precision='${el.precision}'` : ''\n\n    return `<${el.tag} ${vModel} ${placeholder} ${step} ${stepStrictly} ${precision} ${controlsPosition} ${min} ${max} ${disabled}></${el.tag}>`\n  },\n  'el-select': el => {\n    const {\n      disabled, vModel, clearable, placeholder, width\n    } = attrBuilder(el)\n    const filterable = el.filterable ? 'filterable' : ''\n    const multiple = el.multiple ? 'multiple' : ''\n    let child = buildElSelectChild(el)\n\n    if (child) child = `\\n${child}\\n` // 换行\n    return `<${el.tag} ${vModel} ${placeholder} ${disabled} ${multiple} ${filterable} ${clearable} ${width}>${child}</${el.tag}>`\n  },\n  'el-radio-group': el => {\n    const { disabled, vModel } = attrBuilder(el)\n    const size = `size=\"${el.size}\"`\n    let child = buildElRadioGroupChild(el)\n\n    if (child) child = `\\n${child}\\n` // 换行\n    return `<${el.tag} ${vModel} ${size} ${disabled}>${child}</${el.tag}>`\n  },\n  'el-checkbox-group': el => {\n    const { disabled, vModel } = attrBuilder(el)\n    const size = `size=\"${el.size}\"`\n    const min = el.min ? `:min=\"${el.min}\"` : ''\n    const max = el.max ? `:max=\"${el.max}\"` : ''\n    let child = buildElCheckboxGroupChild(el)\n\n    if (child) child = `\\n${child}\\n` // 换行\n    return `<${el.tag} ${vModel} ${min} ${max} ${size} ${disabled}>${child}</${el.tag}>`\n  },\n  'el-switch': el => {\n    const { disabled, vModel } = attrBuilder(el)\n    const activeText = el['active-text'] ? `active-text=\"${el['active-text']}\"` : ''\n    const inactiveText = el['inactive-text'] ? `inactive-text=\"${el['inactive-text']}\"` : ''\n    const activeColor = el['active-color'] ? `active-color=\"${el['active-color']}\"` : ''\n    const inactiveColor = el['inactive-color'] ? `inactive-color=\"${el['inactive-color']}\"` : ''\n    const activeValue = el['active-value'] !== true ? `:active-value='${JSON.stringify(el['active-value'])}'` : ''\n    const inactiveValue = el['inactive-value'] !== false ? `:inactive-value='${JSON.stringify(el['inactive-value'])}'` : ''\n\n    return `<${el.tag} ${vModel} ${activeText} ${inactiveText} ${activeColor} ${inactiveColor} ${activeValue} ${inactiveValue} ${disabled}></${el.tag}>`\n  },\n  'el-cascader': el => {\n    const {\n      disabled, vModel, clearable, placeholder, width\n    } = attrBuilder(el)\n    const options = el.options ? `:options=\"${el.vModel}Options\"` : ''\n    const props = el.props ? `:props=\"${el.vModel}Props\"` : ''\n    const showAllLevels = el['show-all-levels'] ? '' : ':show-all-levels=\"false\"'\n    const filterable = el.filterable ? 'filterable' : ''\n    const separator = el.separator === '/' ? '' : `separator=\"${el.separator}\"`\n\n    return `<${el.tag} ${vModel} ${options} ${props} ${width} ${showAllLevels} ${placeholder} ${separator} ${filterable} ${clearable} ${disabled}></${el.tag}>`\n  },\n  'el-slider': el => {\n    const { disabled, vModel } = attrBuilder(el)\n    const min = el.min ? `:min='${el.min}'` : ''\n    const max = el.max ? `:max='${el.max}'` : ''\n    const step = el.step ? `:step='${el.step}'` : ''\n    const range = el.range ? 'range' : ''\n    const showStops = el['show-stops'] ? `:show-stops=\"${el['show-stops']}\"` : ''\n\n    return `<${el.tag} ${min} ${max} ${step} ${vModel} ${range} ${showStops} ${disabled}></${el.tag}>`\n  },\n  'el-time-picker': el => {\n    const {\n      disabled, vModel, clearable, placeholder, width\n    } = attrBuilder(el)\n    const startPlaceholder = el['start-placeholder'] ? `start-placeholder=\"${el['start-placeholder']}\"` : ''\n    const endPlaceholder = el['end-placeholder'] ? `end-placeholder=\"${el['end-placeholder']}\"` : ''\n    const rangeSeparator = el['range-separator'] ? `range-separator=\"${el['range-separator']}\"` : ''\n    const isRange = el['is-range'] ? 'is-range' : ''\n    const format = el.format ? `format=\"${el.format}\"` : ''\n    const valueFormat = el['value-format'] ? `value-format=\"${el['value-format']}\"` : ''\n    const pickerOptions = el['picker-options'] ? `:picker-options='${JSON.stringify(el['picker-options'])}'` : ''\n\n    return `<${el.tag} ${vModel} ${isRange} ${format} ${valueFormat} ${pickerOptions} ${width} ${placeholder} ${startPlaceholder} ${endPlaceholder} ${rangeSeparator} ${clearable} ${disabled}></${el.tag}>`\n  },\n  'el-date-picker': el => {\n    const {\n      disabled, vModel, clearable, placeholder, width\n    } = attrBuilder(el)\n    const startPlaceholder = el['start-placeholder'] ? `start-placeholder=\"${el['start-placeholder']}\"` : ''\n    const endPlaceholder = el['end-placeholder'] ? `end-placeholder=\"${el['end-placeholder']}\"` : ''\n    const rangeSeparator = el['range-separator'] ? `range-separator=\"${el['range-separator']}\"` : ''\n    const format = el.format ? `format=\"${el.format}\"` : ''\n    const valueFormat = el['value-format'] ? `value-format=\"${el['value-format']}\"` : ''\n    const type = el.type === 'date' ? '' : `type=\"${el.type}\"`\n    const readonly = el.readonly ? 'readonly' : ''\n\n    return `<${el.tag} ${type} ${vModel} ${format} ${valueFormat} ${width} ${placeholder} ${startPlaceholder} ${endPlaceholder} ${rangeSeparator} ${clearable} ${readonly} ${disabled}></${el.tag}>`\n  },\n  'el-rate': el => {\n    const { disabled, vModel } = attrBuilder(el)\n    const max = el.max ? `:max='${el.max}'` : ''\n    const allowHalf = el['allow-half'] ? 'allow-half' : ''\n    const showText = el['show-text'] ? 'show-text' : ''\n    const showScore = el['show-score'] ? 'show-score' : ''\n\n    return `<${el.tag} ${vModel} ${allowHalf} ${showText} ${showScore} ${disabled}></${el.tag}>`\n  },\n  'el-color-picker': el => {\n    const { disabled, vModel } = attrBuilder(el)\n    const size = `size=\"${el.size}\"`\n    const showAlpha = el['show-alpha'] ? 'show-alpha' : ''\n    const colorFormat = el['color-format'] ? `color-format=\"${el['color-format']}\"` : ''\n\n    return `<${el.tag} ${vModel} ${size} ${showAlpha} ${colorFormat} ${disabled}></${el.tag}>`\n  },\n  'el-upload': el => {\n    const disabled = el.disabled ? ':disabled=\\'true\\'' : ''\n    const action = el.action ? `:action=\"${el.vModel}Action\"` : ''\n    const multiple = el.multiple ? 'multiple' : ''\n    const listType = el['list-type'] !== 'text' ? `list-type=\"${el['list-type']}\"` : ''\n    const accept = el.accept ? `accept=\"${el.accept}\"` : ''\n    const name = el.name !== 'file' ? `name=\"${el.name}\"` : ''\n    const autoUpload = el['auto-upload'] === false ? ':auto-upload=\"false\"' : ''\n    const beforeUpload = `:before-upload=\"${el.vModel}BeforeUpload\"`\n    const fileList = `:file-list=\"${el.vModel}fileList\"`\n    const ref = `ref=\"${el.vModel}\"`\n    let child = buildElUploadChild(el)\n\n    if (child) child = `\\n${child}\\n` // 换行\n    return `<${el.tag} ${ref} ${fileList} ${action} ${autoUpload} ${multiple} ${beforeUpload} ${listType} ${accept} ${name} ${disabled}>${child}</${el.tag}>`\n  }\n}\n\nfunction attrBuilder(el) {\n  return {\n    vModel: `v-model=\"${confGlobal.formModel}.${el.vModel}\"`,\n    clearable: el.clearable ? 'clearable' : '',\n    placeholder: el.placeholder ? `placeholder=\"${el.placeholder}\"` : '',\n    width: el.style && el.style.width ? ':style=\"{width: \\'100%\\'}\"' : '',\n    disabled: el.disabled ? ':disabled=\\'true\\'' : ''\n  }\n}\n\n// el-buttin 子级\nfunction buildElButtonChild(conf) {\n  const children = []\n  if (conf.default) {\n    children.push(conf.default)\n  }\n  return children.join('\\n')\n}\n\n// el-input innerHTML\nfunction buildElInputChild(conf) {\n  const children = []\n  if (conf.prepend) {\n    children.push(`<template slot=\"prepend\">${conf.prepend}</template>`)\n  }\n  if (conf.append) {\n    children.push(`<template slot=\"append\">${conf.append}</template>`)\n  }\n  return children.join('\\n')\n}\n\nfunction buildElSelectChild(conf) {\n  const children = []\n  if (conf.options && conf.options.length) {\n    children.push(`<el-option v-for=\"(item, index) in ${conf.vModel}Options\" :key=\"index\" :label=\"item.label\" :value=\"item.value\" :disabled=\"item.disabled\"></el-option>`)\n  }\n  return children.join('\\n')\n}\n\nfunction buildElRadioGroupChild(conf) {\n  const children = []\n  if (conf.options && conf.options.length) {\n    const tag = conf.optionType === 'button' ? 'el-radio-button' : 'el-radio'\n    const border = conf.border ? 'border' : ''\n    children.push(`<${tag} v-for=\"(item, index) in ${conf.vModel}Options\" :key=\"index\" :label=\"item.value\" :disabled=\"item.disabled\" ${border}>{{item.label}}</${tag}>`)\n  }\n  return children.join('\\n')\n}\n\nfunction buildElCheckboxGroupChild(conf) {\n  const children = []\n  if (conf.options && conf.options.length) {\n    const tag = conf.optionType === 'button' ? 'el-checkbox-button' : 'el-checkbox'\n    const border = conf.border ? 'border' : ''\n    children.push(`<${tag} v-for=\"(item, index) in ${conf.vModel}Options\" :key=\"index\" :label=\"item.value\" :disabled=\"item.disabled\" ${border}>{{item.label}}</${tag}>`)\n  }\n  return children.join('\\n')\n}\n\nfunction buildElUploadChild(conf) {\n  const list = []\n  if (conf['list-type'] === 'picture-card') list.push('<i class=\"el-icon-plus\"></i>')\n  else list.push(`<el-button size=\"small\" type=\"primary\" icon=\"el-icon-upload\">${conf.buttonText}</el-button>`)\n  if (conf.showTip) list.push(`<div slot=\"tip\" class=\"el-upload__tip\">只能上传不超过 ${conf.fileSize}${conf.sizeUnit} 的${conf.accept}文件</div>`)\n  return list.join('\\n')\n}\n\nexport function makeUpHtml(conf, type) {\n  const htmlList = []\n  confGlobal = conf\n  someSpanIsNot24 = conf.fields.some(item => item.span !== 24)\n  conf.fields.forEach(el => {\n    htmlList.push(layouts[el.layout](el))\n  })\n  const htmlStr = htmlList.join('\\n')\n\n  let temp = buildFormTemplate(conf, htmlStr, type)\n  if (type === 'dialog') {\n    temp = dialogWrapper(temp)\n  }\n  confGlobal = null\n  return temp\n}\n"], "mappings": "AAAA;AACA,SAASA,OAAO,QAAQ,UAAU;AAElC,IAAIC,UAAU;AACd,IAAIC,eAAe;AAEnB,OAAO,SAASC,aAAaA,CAACC,GAAG,EAAE;EACjC,6HAAAC,MAAA,CACID,GAAG;AAMT;AAEA,OAAO,SAASE,WAAWA,CAACF,GAAG,EAAE;EAC/B,uCAAAC,MAAA,CAEMD,GAAG;AAGX;AAEA,OAAO,SAASG,SAASA,CAACH,GAAG,EAAE;EAC7B,wBAAAC,MAAA,CACID,GAAG;AAET;AAEA,OAAO,SAASI,QAAQA,CAACC,MAAM,EAAE;EAC/B,uBAAAJ,MAAA,CACII,MAAM;AAEZ;AAEA,SAASC,iBAAiBA,CAACC,IAAI,EAAEC,KAAK,EAAEC,IAAI,EAAE;EAC5C,IAAIC,aAAa,GAAG,EAAE;EACtB,IAAIH,IAAI,CAACG,aAAa,KAAK,OAAO,EAAE;IAClCA,aAAa,uBAAAT,MAAA,CAAsBM,IAAI,CAACG,aAAa,OAAG;EAC1D;EACA,IAAMC,QAAQ,GAAGJ,IAAI,CAACI,QAAQ,kBAAAV,MAAA,CAAiBM,IAAI,CAACI,QAAQ,UAAM,EAAE;EACpE,IAAIX,GAAG,qBAAAC,MAAA,CAAoBM,IAAI,CAACK,OAAO,kBAAAX,MAAA,CAAaM,IAAI,CAACM,SAAS,kBAAAZ,MAAA,CAAaM,IAAI,CAACO,SAAS,gBAAAb,MAAA,CAAWM,IAAI,CAACQ,IAAI,SAAAd,MAAA,CAAKU,QAAQ,qBAAAV,MAAA,CAAiBM,IAAI,CAACS,UAAU,WAAAf,MAAA,CAAOS,aAAa,eAAAT,MAAA,CAC5KO,KAAK,cAAAP,MAAA,CACLgB,aAAa,CAACV,IAAI,EAAEE,IAAI,CAAC,qBAClB;EACb,IAAIX,eAAe,EAAE;IACnBE,GAAG,wBAAAC,MAAA,CAAuBM,IAAI,CAACW,MAAM,mBAAAjB,MAAA,CAC/BD,GAAG,sBACG;EACd;EACA,OAAOA,GAAG;AACZ;AAEA,SAASiB,aAAaA,CAACV,IAAI,EAAEE,IAAI,EAAE;EACjC,IAAIT,GAAG,GAAG,EAAE;EACZ,IAAIO,IAAI,CAACY,QAAQ,IAAIV,IAAI,KAAK,MAAM,EAAE;IACpCT,GAAG,qNAGiB;IACpB,IAAIF,eAAe,EAAE;MACnBE,GAAG,uCAAAC,MAAA,CACGD,GAAG,wBACG;IACd;EACF;EACA,OAAOA,GAAG;AACZ;;AAEA;AACA,SAASoB,UAAUA,CAACC,OAAO,EAAErB,GAAG,EAAE;EAChC,IAAIF,eAAe,IAAIuB,OAAO,CAACC,IAAI,KAAK,EAAE,EAAE;IAC1C,0BAAArB,MAAA,CAAyBoB,OAAO,CAACC,IAAI,iBAAArB,MAAA,CACjCD,GAAG;EAET;EACA,OAAOA,GAAG;AACZ;AAEA,IAAMuB,OAAO,GAAG;EACdC,WAAW,WAAAA,YAACH,OAAO,EAAE;IACnB,IAAIL,UAAU,GAAG,EAAE;IACnB,IAAIK,OAAO,CAACL,UAAU,IAAIK,OAAO,CAACL,UAAU,KAAKnB,UAAU,CAACmB,UAAU,EAAE;MACtEA,UAAU,oBAAAf,MAAA,CAAmBoB,OAAO,CAACL,UAAU,SAAK;IACtD;IACA,IAAMS,QAAQ,GAAG,CAAC7B,OAAO,CAACyB,OAAO,CAACK,GAAG,CAAC,IAAIL,OAAO,CAACI,QAAQ,GAAG,UAAU,GAAG,EAAE;IAC5E,IAAME,MAAM,GAAGC,IAAI,CAACP,OAAO,CAACK,GAAG,CAAC,GAAGE,IAAI,CAACP,OAAO,CAACK,GAAG,CAAC,CAACL,OAAO,CAAC,GAAG,IAAI;IACpE,IAAIrB,GAAG,oBAAAC,MAAA,CAAoBe,UAAU,eAAAf,MAAA,CAAWoB,OAAO,CAACQ,KAAK,gBAAA5B,MAAA,CAAWoB,OAAO,CAACS,MAAM,SAAA7B,MAAA,CAAKwB,QAAQ,iBAAAxB,MAAA,CAC7F0B,MAAM,4BACM;IAClB3B,GAAG,GAAGoB,UAAU,CAACC,OAAO,EAAErB,GAAG,CAAC;IAC9B,OAAOA,GAAG;EACZ,CAAC;EACD+B,WAAW,WAAAA,YAACV,OAAO,EAAE;IACnB,IAAMZ,IAAI,GAAGY,OAAO,CAACZ,IAAI,KAAK,SAAS,GAAG,EAAE,aAAAR,MAAA,CAAYoB,OAAO,CAACZ,IAAI,OAAG;IACvE,IAAMuB,OAAO,GAAGX,OAAO,CAACZ,IAAI,KAAK,SAAS,GAAG,EAAE,gBAAAR,MAAA,CAAeoB,OAAO,CAACW,OAAO,OAAG;IAChF,IAAMC,KAAK,GAAGZ,OAAO,CAACZ,IAAI,KAAK,SAAS,GAAG,EAAE,cAAAR,MAAA,CAAaoB,OAAO,CAACY,KAAK,OAAG;IAC1E,IAAMf,MAAM,GAAGG,OAAO,CAACH,MAAM,eAAAjB,MAAA,CAAcoB,OAAO,CAACH,MAAM,UAAM,EAAE;IACjE,IAAMgB,QAAQ,GAAGb,OAAO,CAACa,QAAQ,CAACC,GAAG,CAAC,UAAAC,EAAE;MAAA,OAAIb,OAAO,CAACa,EAAE,CAACC,MAAM,CAAC,CAACD,EAAE,CAAC;IAAA,EAAC;IACnE,IAAIpC,GAAG,cAAAC,MAAA,CAAcQ,IAAI,OAAAR,MAAA,CAAI+B,OAAO,OAAA/B,MAAA,CAAIgC,KAAK,OAAAhC,MAAA,CAAIiB,MAAM,eAAAjB,MAAA,CACnDiC,QAAQ,CAACI,IAAI,CAAC,IAAI,CAAC,oBACb;IACVtC,GAAG,GAAGoB,UAAU,CAACC,OAAO,EAAErB,GAAG,CAAC;IAC9B,OAAOA,GAAG;EACZ;AACF,CAAC;AAED,IAAM4B,IAAI,GAAG;EACX,WAAW,EAAE,SAAAW,SAAAH,EAAE,EAAI;IACjB,IAAAI,YAAA,GAEIC,WAAW,CAACL,EAAE,CAAC;MADjBV,GAAG,GAAAc,YAAA,CAAHd,GAAG;MAAEf,QAAQ,GAAA6B,YAAA,CAAR7B,QAAQ;IAEf,IAAMF,IAAI,GAAG2B,EAAE,CAAC3B,IAAI,aAAAR,MAAA,CAAYmC,EAAE,CAAC3B,IAAI,UAAM,EAAE;IAC/C,IAAMiC,IAAI,GAAGN,EAAE,CAACM,IAAI,aAAAzC,MAAA,CAAYmC,EAAE,CAACM,IAAI,UAAM,EAAE;IAC/C,IAAM3B,IAAI,GAAGqB,EAAE,CAACrB,IAAI,aAAAd,MAAA,CAAYmC,EAAE,CAACrB,IAAI,UAAM,EAAE;IAC/C,IAAIP,KAAK,GAAGmC,kBAAkB,CAACP,EAAE,CAAC;IAElC,IAAI5B,KAAK,EAAEA,KAAK,QAAAP,MAAA,CAAQO,KAAK,OAAI,EAAC;IAClC,WAAAP,MAAA,CAAWmC,EAAE,CAACV,GAAG,OAAAzB,MAAA,CAAIQ,IAAI,OAAAR,MAAA,CAAIyC,IAAI,OAAAzC,MAAA,CAAIc,IAAI,OAAAd,MAAA,CAAIU,QAAQ,OAAAV,MAAA,CAAIO,KAAK,QAAAP,MAAA,CAAKmC,EAAE,CAACV,GAAG;EAC3E,CAAC;EACD,UAAU,EAAE,SAAAkB,QAAAR,EAAE,EAAI;IAChB,IAAAS,aAAA,GAEIJ,WAAW,CAACL,EAAE,CAAC;MADjBzB,QAAQ,GAAAkC,aAAA,CAARlC,QAAQ;MAAEmB,MAAM,GAAAe,aAAA,CAANf,MAAM;MAAEgB,SAAS,GAAAD,aAAA,CAATC,SAAS;MAAEC,WAAW,GAAAF,aAAA,CAAXE,WAAW;MAAEC,KAAK,GAAAH,aAAA,CAALG,KAAK;IAEjD,IAAMC,SAAS,GAAGb,EAAE,CAACa,SAAS,mBAAAhD,MAAA,CAAkBmC,EAAE,CAACa,SAAS,UAAM,EAAE;IACpE,IAAMC,aAAa,GAAGd,EAAE,CAAC,iBAAiB,CAAC,GAAG,iBAAiB,GAAG,EAAE;IACpE,IAAMe,QAAQ,GAAGf,EAAE,CAACe,QAAQ,GAAG,UAAU,GAAG,EAAE;IAC9C,IAAMC,UAAU,GAAGhB,EAAE,CAAC,aAAa,CAAC,mBAAAnC,MAAA,CAAmBmC,EAAE,CAAC,aAAa,CAAC,SAAM,EAAE;IAChF,IAAMiB,UAAU,GAAGjB,EAAE,CAAC,aAAa,CAAC,mBAAAnC,MAAA,CAAmBmC,EAAE,CAAC,aAAa,CAAC,SAAM,EAAE;IAChF,IAAMkB,YAAY,GAAGlB,EAAE,CAAC,eAAe,CAAC,GAAG,eAAe,GAAG,EAAE;IAC/D,IAAM3B,IAAI,GAAG2B,EAAE,CAAC3B,IAAI,aAAAR,MAAA,CAAYmC,EAAE,CAAC3B,IAAI,UAAM,EAAE;IAC/C,IAAM8C,QAAQ,GAAGnB,EAAE,CAACmB,QAAQ,IAAInB,EAAE,CAACmB,QAAQ,CAACC,OAAO,4BAAAvD,MAAA,CACvBmC,EAAE,CAACmB,QAAQ,CAACC,OAAO,iBAAAvD,MAAA,CAAcmC,EAAE,CAACmB,QAAQ,CAACE,OAAO,WAC5E,EAAE;IACN,IAAIjD,KAAK,GAAGkD,iBAAiB,CAACtB,EAAE,CAAC;IAEjC,IAAI5B,KAAK,EAAEA,KAAK,QAAAP,MAAA,CAAQO,KAAK,OAAI,EAAC;IAClC,WAAAP,MAAA,CAAWmC,EAAE,CAACV,GAAG,OAAAzB,MAAA,CAAI6B,MAAM,OAAA7B,MAAA,CAAIQ,IAAI,OAAAR,MAAA,CAAI8C,WAAW,OAAA9C,MAAA,CAAIgD,SAAS,OAAAhD,MAAA,CAAIiD,aAAa,OAAAjD,MAAA,CAAIkD,QAAQ,OAAAlD,MAAA,CAAIU,QAAQ,OAAAV,MAAA,CAAI6C,SAAS,OAAA7C,MAAA,CAAImD,UAAU,OAAAnD,MAAA,CAAIoD,UAAU,OAAApD,MAAA,CAAIqD,YAAY,OAAArD,MAAA,CAAIsD,QAAQ,OAAAtD,MAAA,CAAI+C,KAAK,OAAA/C,MAAA,CAAIO,KAAK,QAAAP,MAAA,CAAKmC,EAAE,CAACV,GAAG;EAC5M,CAAC;EACD,iBAAiB,EAAE,SAAAiC,cAAAvB,EAAE,EAAI;IACvB,IAAAwB,aAAA,GAA0CnB,WAAW,CAACL,EAAE,CAAC;MAAjDzB,QAAQ,GAAAiD,aAAA,CAARjD,QAAQ;MAAEmB,MAAM,GAAA8B,aAAA,CAAN9B,MAAM;MAAEiB,WAAW,GAAAa,aAAA,CAAXb,WAAW;IACrC,IAAMc,gBAAgB,GAAGzB,EAAE,CAAC,mBAAmB,CAAC,wBAAAnC,MAAA,CAAwBmC,EAAE,CAAC,mBAAmB,CAAC,IAAK,EAAE;IACtG,IAAM0B,GAAG,GAAG1B,EAAE,CAAC0B,GAAG,YAAA7D,MAAA,CAAYmC,EAAE,CAAC0B,GAAG,SAAM,EAAE;IAC5C,IAAMC,GAAG,GAAG3B,EAAE,CAAC2B,GAAG,YAAA9D,MAAA,CAAYmC,EAAE,CAAC2B,GAAG,SAAM,EAAE;IAC5C,IAAMC,IAAI,GAAG5B,EAAE,CAAC4B,IAAI,aAAA/D,MAAA,CAAamC,EAAE,CAAC4B,IAAI,SAAM,EAAE;IAChD,IAAMC,YAAY,GAAG7B,EAAE,CAAC,eAAe,CAAC,GAAG,eAAe,GAAG,EAAE;IAC/D,IAAM8B,SAAS,GAAG9B,EAAE,CAAC8B,SAAS,kBAAAjE,MAAA,CAAkBmC,EAAE,CAAC8B,SAAS,SAAM,EAAE;IAEpE,WAAAjE,MAAA,CAAWmC,EAAE,CAACV,GAAG,OAAAzB,MAAA,CAAI6B,MAAM,OAAA7B,MAAA,CAAI8C,WAAW,OAAA9C,MAAA,CAAI+D,IAAI,OAAA/D,MAAA,CAAIgE,YAAY,OAAAhE,MAAA,CAAIiE,SAAS,OAAAjE,MAAA,CAAI4D,gBAAgB,OAAA5D,MAAA,CAAI6D,GAAG,OAAA7D,MAAA,CAAI8D,GAAG,OAAA9D,MAAA,CAAIU,QAAQ,SAAAV,MAAA,CAAMmC,EAAE,CAACV,GAAG;EAC3I,CAAC;EACD,WAAW,EAAE,SAAAyC,SAAA/B,EAAE,EAAI;IACjB,IAAAgC,aAAA,GAEI3B,WAAW,CAACL,EAAE,CAAC;MADjBzB,QAAQ,GAAAyD,aAAA,CAARzD,QAAQ;MAAEmB,MAAM,GAAAsC,aAAA,CAANtC,MAAM;MAAEgB,SAAS,GAAAsB,aAAA,CAATtB,SAAS;MAAEC,WAAW,GAAAqB,aAAA,CAAXrB,WAAW;MAAEC,KAAK,GAAAoB,aAAA,CAALpB,KAAK;IAEjD,IAAMqB,UAAU,GAAGjC,EAAE,CAACiC,UAAU,GAAG,YAAY,GAAG,EAAE;IACpD,IAAMC,QAAQ,GAAGlC,EAAE,CAACkC,QAAQ,GAAG,UAAU,GAAG,EAAE;IAC9C,IAAI9D,KAAK,GAAG+D,kBAAkB,CAACnC,EAAE,CAAC;IAElC,IAAI5B,KAAK,EAAEA,KAAK,QAAAP,MAAA,CAAQO,KAAK,OAAI,EAAC;IAClC,WAAAP,MAAA,CAAWmC,EAAE,CAACV,GAAG,OAAAzB,MAAA,CAAI6B,MAAM,OAAA7B,MAAA,CAAI8C,WAAW,OAAA9C,MAAA,CAAIU,QAAQ,OAAAV,MAAA,CAAIqE,QAAQ,OAAArE,MAAA,CAAIoE,UAAU,OAAApE,MAAA,CAAI6C,SAAS,OAAA7C,MAAA,CAAI+C,KAAK,OAAA/C,MAAA,CAAIO,KAAK,QAAAP,MAAA,CAAKmC,EAAE,CAACV,GAAG;EAC5H,CAAC;EACD,gBAAgB,EAAE,SAAA8C,aAAApC,EAAE,EAAI;IACtB,IAAAqC,aAAA,GAA6BhC,WAAW,CAACL,EAAE,CAAC;MAApCzB,QAAQ,GAAA8D,aAAA,CAAR9D,QAAQ;MAAEmB,MAAM,GAAA2C,aAAA,CAAN3C,MAAM;IACxB,IAAMf,IAAI,aAAAd,MAAA,CAAYmC,EAAE,CAACrB,IAAI,OAAG;IAChC,IAAIP,KAAK,GAAGkE,sBAAsB,CAACtC,EAAE,CAAC;IAEtC,IAAI5B,KAAK,EAAEA,KAAK,QAAAP,MAAA,CAAQO,KAAK,OAAI,EAAC;IAClC,WAAAP,MAAA,CAAWmC,EAAE,CAACV,GAAG,OAAAzB,MAAA,CAAI6B,MAAM,OAAA7B,MAAA,CAAIc,IAAI,OAAAd,MAAA,CAAIU,QAAQ,OAAAV,MAAA,CAAIO,KAAK,QAAAP,MAAA,CAAKmC,EAAE,CAACV,GAAG;EACrE,CAAC;EACD,mBAAmB,EAAE,SAAAiD,gBAAAvC,EAAE,EAAI;IACzB,IAAAwC,aAAA,GAA6BnC,WAAW,CAACL,EAAE,CAAC;MAApCzB,QAAQ,GAAAiE,aAAA,CAARjE,QAAQ;MAAEmB,MAAM,GAAA8C,aAAA,CAAN9C,MAAM;IACxB,IAAMf,IAAI,aAAAd,MAAA,CAAYmC,EAAE,CAACrB,IAAI,OAAG;IAChC,IAAM+C,GAAG,GAAG1B,EAAE,CAAC0B,GAAG,aAAA7D,MAAA,CAAYmC,EAAE,CAAC0B,GAAG,UAAM,EAAE;IAC5C,IAAMC,GAAG,GAAG3B,EAAE,CAAC2B,GAAG,aAAA9D,MAAA,CAAYmC,EAAE,CAAC2B,GAAG,UAAM,EAAE;IAC5C,IAAIvD,KAAK,GAAGqE,yBAAyB,CAACzC,EAAE,CAAC;IAEzC,IAAI5B,KAAK,EAAEA,KAAK,QAAAP,MAAA,CAAQO,KAAK,OAAI,EAAC;IAClC,WAAAP,MAAA,CAAWmC,EAAE,CAACV,GAAG,OAAAzB,MAAA,CAAI6B,MAAM,OAAA7B,MAAA,CAAI6D,GAAG,OAAA7D,MAAA,CAAI8D,GAAG,OAAA9D,MAAA,CAAIc,IAAI,OAAAd,MAAA,CAAIU,QAAQ,OAAAV,MAAA,CAAIO,KAAK,QAAAP,MAAA,CAAKmC,EAAE,CAACV,GAAG;EACnF,CAAC;EACD,WAAW,EAAE,SAAAoD,SAAA1C,EAAE,EAAI;IACjB,IAAA2C,aAAA,GAA6BtC,WAAW,CAACL,EAAE,CAAC;MAApCzB,QAAQ,GAAAoE,aAAA,CAARpE,QAAQ;MAAEmB,MAAM,GAAAiD,aAAA,CAANjD,MAAM;IACxB,IAAMkD,UAAU,GAAG5C,EAAE,CAAC,aAAa,CAAC,oBAAAnC,MAAA,CAAmBmC,EAAE,CAAC,aAAa,CAAC,UAAM,EAAE;IAChF,IAAM6C,YAAY,GAAG7C,EAAE,CAAC,eAAe,CAAC,sBAAAnC,MAAA,CAAqBmC,EAAE,CAAC,eAAe,CAAC,UAAM,EAAE;IACxF,IAAM8C,WAAW,GAAG9C,EAAE,CAAC,cAAc,CAAC,qBAAAnC,MAAA,CAAoBmC,EAAE,CAAC,cAAc,CAAC,UAAM,EAAE;IACpF,IAAM+C,aAAa,GAAG/C,EAAE,CAAC,gBAAgB,CAAC,uBAAAnC,MAAA,CAAsBmC,EAAE,CAAC,gBAAgB,CAAC,UAAM,EAAE;IAC5F,IAAMgD,WAAW,GAAGhD,EAAE,CAAC,cAAc,CAAC,KAAK,IAAI,qBAAAnC,MAAA,CAAqBoF,IAAI,CAACC,SAAS,CAAClD,EAAE,CAAC,cAAc,CAAC,CAAC,SAAM,EAAE;IAC9G,IAAMmD,aAAa,GAAGnD,EAAE,CAAC,gBAAgB,CAAC,KAAK,KAAK,uBAAAnC,MAAA,CAAuBoF,IAAI,CAACC,SAAS,CAAClD,EAAE,CAAC,gBAAgB,CAAC,CAAC,SAAM,EAAE;IAEvH,WAAAnC,MAAA,CAAWmC,EAAE,CAACV,GAAG,OAAAzB,MAAA,CAAI6B,MAAM,OAAA7B,MAAA,CAAI+E,UAAU,OAAA/E,MAAA,CAAIgF,YAAY,OAAAhF,MAAA,CAAIiF,WAAW,OAAAjF,MAAA,CAAIkF,aAAa,OAAAlF,MAAA,CAAImF,WAAW,OAAAnF,MAAA,CAAIsF,aAAa,OAAAtF,MAAA,CAAIU,QAAQ,SAAAV,MAAA,CAAMmC,EAAE,CAACV,GAAG;EACnJ,CAAC;EACD,aAAa,EAAE,SAAA8D,WAAApD,EAAE,EAAI;IACnB,IAAAqD,aAAA,GAEIhD,WAAW,CAACL,EAAE,CAAC;MADjBzB,QAAQ,GAAA8E,aAAA,CAAR9E,QAAQ;MAAEmB,MAAM,GAAA2D,aAAA,CAAN3D,MAAM;MAAEgB,SAAS,GAAA2C,aAAA,CAAT3C,SAAS;MAAEC,WAAW,GAAA0C,aAAA,CAAX1C,WAAW;MAAEC,KAAK,GAAAyC,aAAA,CAALzC,KAAK;IAEjD,IAAM0C,OAAO,GAAGtD,EAAE,CAACsD,OAAO,iBAAAzF,MAAA,CAAgBmC,EAAE,CAACN,MAAM,iBAAa,EAAE;IAClE,IAAM6D,KAAK,GAAGvD,EAAE,CAACuD,KAAK,eAAA1F,MAAA,CAAcmC,EAAE,CAACN,MAAM,eAAW,EAAE;IAC1D,IAAM8D,aAAa,GAAGxD,EAAE,CAAC,iBAAiB,CAAC,GAAG,EAAE,GAAG,0BAA0B;IAC7E,IAAMiC,UAAU,GAAGjC,EAAE,CAACiC,UAAU,GAAG,YAAY,GAAG,EAAE;IACpD,IAAMwB,SAAS,GAAGzD,EAAE,CAACyD,SAAS,KAAK,GAAG,GAAG,EAAE,kBAAA5F,MAAA,CAAiBmC,EAAE,CAACyD,SAAS,OAAG;IAE3E,WAAA5F,MAAA,CAAWmC,EAAE,CAACV,GAAG,OAAAzB,MAAA,CAAI6B,MAAM,OAAA7B,MAAA,CAAIyF,OAAO,OAAAzF,MAAA,CAAI0F,KAAK,OAAA1F,MAAA,CAAI+C,KAAK,OAAA/C,MAAA,CAAI2F,aAAa,OAAA3F,MAAA,CAAI8C,WAAW,OAAA9C,MAAA,CAAI4F,SAAS,OAAA5F,MAAA,CAAIoE,UAAU,OAAApE,MAAA,CAAI6C,SAAS,OAAA7C,MAAA,CAAIU,QAAQ,SAAAV,MAAA,CAAMmC,EAAE,CAACV,GAAG;EAC1J,CAAC;EACD,WAAW,EAAE,SAAAoE,SAAA1D,EAAE,EAAI;IACjB,IAAA2D,aAAA,GAA6BtD,WAAW,CAACL,EAAE,CAAC;MAApCzB,QAAQ,GAAAoF,aAAA,CAARpF,QAAQ;MAAEmB,MAAM,GAAAiE,aAAA,CAANjE,MAAM;IACxB,IAAMgC,GAAG,GAAG1B,EAAE,CAAC0B,GAAG,YAAA7D,MAAA,CAAYmC,EAAE,CAAC0B,GAAG,SAAM,EAAE;IAC5C,IAAMC,GAAG,GAAG3B,EAAE,CAAC2B,GAAG,YAAA9D,MAAA,CAAYmC,EAAE,CAAC2B,GAAG,SAAM,EAAE;IAC5C,IAAMC,IAAI,GAAG5B,EAAE,CAAC4B,IAAI,aAAA/D,MAAA,CAAamC,EAAE,CAAC4B,IAAI,SAAM,EAAE;IAChD,IAAMgC,KAAK,GAAG5D,EAAE,CAAC4D,KAAK,GAAG,OAAO,GAAG,EAAE;IACrC,IAAMC,SAAS,GAAG7D,EAAE,CAAC,YAAY,CAAC,oBAAAnC,MAAA,CAAmBmC,EAAE,CAAC,YAAY,CAAC,UAAM,EAAE;IAE7E,WAAAnC,MAAA,CAAWmC,EAAE,CAACV,GAAG,OAAAzB,MAAA,CAAI6D,GAAG,OAAA7D,MAAA,CAAI8D,GAAG,OAAA9D,MAAA,CAAI+D,IAAI,OAAA/D,MAAA,CAAI6B,MAAM,OAAA7B,MAAA,CAAI+F,KAAK,OAAA/F,MAAA,CAAIgG,SAAS,OAAAhG,MAAA,CAAIU,QAAQ,SAAAV,MAAA,CAAMmC,EAAE,CAACV,GAAG;EACjG,CAAC;EACD,gBAAgB,EAAE,SAAAwE,aAAA9D,EAAE,EAAI;IACtB,IAAA+D,cAAA,GAEI1D,WAAW,CAACL,EAAE,CAAC;MADjBzB,QAAQ,GAAAwF,cAAA,CAARxF,QAAQ;MAAEmB,MAAM,GAAAqE,cAAA,CAANrE,MAAM;MAAEgB,SAAS,GAAAqD,cAAA,CAATrD,SAAS;MAAEC,WAAW,GAAAoD,cAAA,CAAXpD,WAAW;MAAEC,KAAK,GAAAmD,cAAA,CAALnD,KAAK;IAEjD,IAAMoD,gBAAgB,GAAGhE,EAAE,CAAC,mBAAmB,CAAC,0BAAAnC,MAAA,CAAyBmC,EAAE,CAAC,mBAAmB,CAAC,UAAM,EAAE;IACxG,IAAMiE,cAAc,GAAGjE,EAAE,CAAC,iBAAiB,CAAC,wBAAAnC,MAAA,CAAuBmC,EAAE,CAAC,iBAAiB,CAAC,UAAM,EAAE;IAChG,IAAMkE,cAAc,GAAGlE,EAAE,CAAC,iBAAiB,CAAC,wBAAAnC,MAAA,CAAuBmC,EAAE,CAAC,iBAAiB,CAAC,UAAM,EAAE;IAChG,IAAMmE,OAAO,GAAGnE,EAAE,CAAC,UAAU,CAAC,GAAG,UAAU,GAAG,EAAE;IAChD,IAAMoE,MAAM,GAAGpE,EAAE,CAACoE,MAAM,eAAAvG,MAAA,CAAcmC,EAAE,CAACoE,MAAM,UAAM,EAAE;IACvD,IAAMC,WAAW,GAAGrE,EAAE,CAAC,cAAc,CAAC,qBAAAnC,MAAA,CAAoBmC,EAAE,CAAC,cAAc,CAAC,UAAM,EAAE;IACpF,IAAMsE,aAAa,GAAGtE,EAAE,CAAC,gBAAgB,CAAC,uBAAAnC,MAAA,CAAuBoF,IAAI,CAACC,SAAS,CAAClD,EAAE,CAAC,gBAAgB,CAAC,CAAC,SAAM,EAAE;IAE7G,WAAAnC,MAAA,CAAWmC,EAAE,CAACV,GAAG,OAAAzB,MAAA,CAAI6B,MAAM,OAAA7B,MAAA,CAAIsG,OAAO,OAAAtG,MAAA,CAAIuG,MAAM,OAAAvG,MAAA,CAAIwG,WAAW,OAAAxG,MAAA,CAAIyG,aAAa,OAAAzG,MAAA,CAAI+C,KAAK,OAAA/C,MAAA,CAAI8C,WAAW,OAAA9C,MAAA,CAAImG,gBAAgB,OAAAnG,MAAA,CAAIoG,cAAc,OAAApG,MAAA,CAAIqG,cAAc,OAAArG,MAAA,CAAI6C,SAAS,OAAA7C,MAAA,CAAIU,QAAQ,SAAAV,MAAA,CAAMmC,EAAE,CAACV,GAAG;EACvM,CAAC;EACD,gBAAgB,EAAE,SAAAiF,aAAAvE,EAAE,EAAI;IACtB,IAAAwE,cAAA,GAEInE,WAAW,CAACL,EAAE,CAAC;MADjBzB,QAAQ,GAAAiG,cAAA,CAARjG,QAAQ;MAAEmB,MAAM,GAAA8E,cAAA,CAAN9E,MAAM;MAAEgB,SAAS,GAAA8D,cAAA,CAAT9D,SAAS;MAAEC,WAAW,GAAA6D,cAAA,CAAX7D,WAAW;MAAEC,KAAK,GAAA4D,cAAA,CAAL5D,KAAK;IAEjD,IAAMoD,gBAAgB,GAAGhE,EAAE,CAAC,mBAAmB,CAAC,0BAAAnC,MAAA,CAAyBmC,EAAE,CAAC,mBAAmB,CAAC,UAAM,EAAE;IACxG,IAAMiE,cAAc,GAAGjE,EAAE,CAAC,iBAAiB,CAAC,wBAAAnC,MAAA,CAAuBmC,EAAE,CAAC,iBAAiB,CAAC,UAAM,EAAE;IAChG,IAAMkE,cAAc,GAAGlE,EAAE,CAAC,iBAAiB,CAAC,wBAAAnC,MAAA,CAAuBmC,EAAE,CAAC,iBAAiB,CAAC,UAAM,EAAE;IAChG,IAAMoE,MAAM,GAAGpE,EAAE,CAACoE,MAAM,eAAAvG,MAAA,CAAcmC,EAAE,CAACoE,MAAM,UAAM,EAAE;IACvD,IAAMC,WAAW,GAAGrE,EAAE,CAAC,cAAc,CAAC,qBAAAnC,MAAA,CAAoBmC,EAAE,CAAC,cAAc,CAAC,UAAM,EAAE;IACpF,IAAM3B,IAAI,GAAG2B,EAAE,CAAC3B,IAAI,KAAK,MAAM,GAAG,EAAE,aAAAR,MAAA,CAAYmC,EAAE,CAAC3B,IAAI,OAAG;IAC1D,IAAM0C,QAAQ,GAAGf,EAAE,CAACe,QAAQ,GAAG,UAAU,GAAG,EAAE;IAE9C,WAAAlD,MAAA,CAAWmC,EAAE,CAACV,GAAG,OAAAzB,MAAA,CAAIQ,IAAI,OAAAR,MAAA,CAAI6B,MAAM,OAAA7B,MAAA,CAAIuG,MAAM,OAAAvG,MAAA,CAAIwG,WAAW,OAAAxG,MAAA,CAAI+C,KAAK,OAAA/C,MAAA,CAAI8C,WAAW,OAAA9C,MAAA,CAAImG,gBAAgB,OAAAnG,MAAA,CAAIoG,cAAc,OAAApG,MAAA,CAAIqG,cAAc,OAAArG,MAAA,CAAI6C,SAAS,OAAA7C,MAAA,CAAIkD,QAAQ,OAAAlD,MAAA,CAAIU,QAAQ,SAAAV,MAAA,CAAMmC,EAAE,CAACV,GAAG;EAC/L,CAAC;EACD,SAAS,EAAE,SAAAmF,OAAAzE,EAAE,EAAI;IACf,IAAA0E,cAAA,GAA6BrE,WAAW,CAACL,EAAE,CAAC;MAApCzB,QAAQ,GAAAmG,cAAA,CAARnG,QAAQ;MAAEmB,MAAM,GAAAgF,cAAA,CAANhF,MAAM;IACxB,IAAMiC,GAAG,GAAG3B,EAAE,CAAC2B,GAAG,YAAA9D,MAAA,CAAYmC,EAAE,CAAC2B,GAAG,SAAM,EAAE;IAC5C,IAAMgD,SAAS,GAAG3E,EAAE,CAAC,YAAY,CAAC,GAAG,YAAY,GAAG,EAAE;IACtD,IAAM4E,QAAQ,GAAG5E,EAAE,CAAC,WAAW,CAAC,GAAG,WAAW,GAAG,EAAE;IACnD,IAAM6E,SAAS,GAAG7E,EAAE,CAAC,YAAY,CAAC,GAAG,YAAY,GAAG,EAAE;IAEtD,WAAAnC,MAAA,CAAWmC,EAAE,CAACV,GAAG,OAAAzB,MAAA,CAAI6B,MAAM,OAAA7B,MAAA,CAAI8G,SAAS,OAAA9G,MAAA,CAAI+G,QAAQ,OAAA/G,MAAA,CAAIgH,SAAS,OAAAhH,MAAA,CAAIU,QAAQ,SAAAV,MAAA,CAAMmC,EAAE,CAACV,GAAG;EAC3F,CAAC;EACD,iBAAiB,EAAE,SAAAwF,cAAA9E,EAAE,EAAI;IACvB,IAAA+E,cAAA,GAA6B1E,WAAW,CAACL,EAAE,CAAC;MAApCzB,QAAQ,GAAAwG,cAAA,CAARxG,QAAQ;MAAEmB,MAAM,GAAAqF,cAAA,CAANrF,MAAM;IACxB,IAAMf,IAAI,aAAAd,MAAA,CAAYmC,EAAE,CAACrB,IAAI,OAAG;IAChC,IAAMqG,SAAS,GAAGhF,EAAE,CAAC,YAAY,CAAC,GAAG,YAAY,GAAG,EAAE;IACtD,IAAMiF,WAAW,GAAGjF,EAAE,CAAC,cAAc,CAAC,qBAAAnC,MAAA,CAAoBmC,EAAE,CAAC,cAAc,CAAC,UAAM,EAAE;IAEpF,WAAAnC,MAAA,CAAWmC,EAAE,CAACV,GAAG,OAAAzB,MAAA,CAAI6B,MAAM,OAAA7B,MAAA,CAAIc,IAAI,OAAAd,MAAA,CAAImH,SAAS,OAAAnH,MAAA,CAAIoH,WAAW,OAAApH,MAAA,CAAIU,QAAQ,SAAAV,MAAA,CAAMmC,EAAE,CAACV,GAAG;EACzF,CAAC;EACD,WAAW,EAAE,SAAA4F,SAAAlF,EAAE,EAAI;IACjB,IAAMzB,QAAQ,GAAGyB,EAAE,CAACzB,QAAQ,GAAG,oBAAoB,GAAG,EAAE;IACxD,IAAM4G,MAAM,GAAGnF,EAAE,CAACmF,MAAM,gBAAAtH,MAAA,CAAemC,EAAE,CAACN,MAAM,gBAAY,EAAE;IAC9D,IAAMwC,QAAQ,GAAGlC,EAAE,CAACkC,QAAQ,GAAG,UAAU,GAAG,EAAE;IAC9C,IAAMkD,QAAQ,GAAGpF,EAAE,CAAC,WAAW,CAAC,KAAK,MAAM,kBAAAnC,MAAA,CAAiBmC,EAAE,CAAC,WAAW,CAAC,UAAM,EAAE;IACnF,IAAMqF,MAAM,GAAGrF,EAAE,CAACqF,MAAM,eAAAxH,MAAA,CAAcmC,EAAE,CAACqF,MAAM,UAAM,EAAE;IACvD,IAAMC,IAAI,GAAGtF,EAAE,CAACsF,IAAI,KAAK,MAAM,aAAAzH,MAAA,CAAYmC,EAAE,CAACsF,IAAI,UAAM,EAAE;IAC1D,IAAMC,UAAU,GAAGvF,EAAE,CAAC,aAAa,CAAC,KAAK,KAAK,GAAG,sBAAsB,GAAG,EAAE;IAC5E,IAAMwF,YAAY,uBAAA3H,MAAA,CAAsBmC,EAAE,CAACN,MAAM,mBAAe;IAChE,IAAM+F,QAAQ,mBAAA5H,MAAA,CAAkBmC,EAAE,CAACN,MAAM,eAAW;IACpD,IAAMgG,GAAG,YAAA7H,MAAA,CAAWmC,EAAE,CAACN,MAAM,OAAG;IAChC,IAAItB,KAAK,GAAGuH,kBAAkB,CAAC3F,EAAE,CAAC;IAElC,IAAI5B,KAAK,EAAEA,KAAK,QAAAP,MAAA,CAAQO,KAAK,OAAI,EAAC;IAClC,WAAAP,MAAA,CAAWmC,EAAE,CAACV,GAAG,OAAAzB,MAAA,CAAI6H,GAAG,OAAA7H,MAAA,CAAI4H,QAAQ,OAAA5H,MAAA,CAAIsH,MAAM,OAAAtH,MAAA,CAAI0H,UAAU,OAAA1H,MAAA,CAAIqE,QAAQ,OAAArE,MAAA,CAAI2H,YAAY,OAAA3H,MAAA,CAAIuH,QAAQ,OAAAvH,MAAA,CAAIwH,MAAM,OAAAxH,MAAA,CAAIyH,IAAI,OAAAzH,MAAA,CAAIU,QAAQ,OAAAV,MAAA,CAAIO,KAAK,QAAAP,MAAA,CAAKmC,EAAE,CAACV,GAAG;EACxJ;AACF,CAAC;AAED,SAASe,WAAWA,CAACL,EAAE,EAAE;EACvB,OAAO;IACLN,MAAM,eAAA7B,MAAA,CAAcJ,UAAU,CAACgB,SAAS,OAAAZ,MAAA,CAAImC,EAAE,CAACN,MAAM,OAAG;IACxDgB,SAAS,EAAEV,EAAE,CAACU,SAAS,GAAG,WAAW,GAAG,EAAE;IAC1CC,WAAW,EAAEX,EAAE,CAACW,WAAW,oBAAA9C,MAAA,CAAmBmC,EAAE,CAACW,WAAW,UAAM,EAAE;IACpEC,KAAK,EAAEZ,EAAE,CAAC4F,KAAK,IAAI5F,EAAE,CAAC4F,KAAK,CAAChF,KAAK,GAAG,4BAA4B,GAAG,EAAE;IACrErC,QAAQ,EAAEyB,EAAE,CAACzB,QAAQ,GAAG,oBAAoB,GAAG;EACjD,CAAC;AACH;;AAEA;AACA,SAASgC,kBAAkBA,CAACpC,IAAI,EAAE;EAChC,IAAM2B,QAAQ,GAAG,EAAE;EACnB,IAAI3B,IAAI,CAAC0H,OAAO,EAAE;IAChB/F,QAAQ,CAACgG,IAAI,CAAC3H,IAAI,CAAC0H,OAAO,CAAC;EAC7B;EACA,OAAO/F,QAAQ,CAACI,IAAI,CAAC,IAAI,CAAC;AAC5B;;AAEA;AACA,SAASoB,iBAAiBA,CAACnD,IAAI,EAAE;EAC/B,IAAM2B,QAAQ,GAAG,EAAE;EACnB,IAAI3B,IAAI,CAAC4H,OAAO,EAAE;IAChBjG,QAAQ,CAACgG,IAAI,+BAAAjI,MAAA,CAA6BM,IAAI,CAAC4H,OAAO,gBAAa,CAAC;EACtE;EACA,IAAI5H,IAAI,CAAC6H,MAAM,EAAE;IACflG,QAAQ,CAACgG,IAAI,8BAAAjI,MAAA,CAA4BM,IAAI,CAAC6H,MAAM,gBAAa,CAAC;EACpE;EACA,OAAOlG,QAAQ,CAACI,IAAI,CAAC,IAAI,CAAC;AAC5B;AAEA,SAASiC,kBAAkBA,CAAChE,IAAI,EAAE;EAChC,IAAM2B,QAAQ,GAAG,EAAE;EACnB,IAAI3B,IAAI,CAACmF,OAAO,IAAInF,IAAI,CAACmF,OAAO,CAAC2C,MAAM,EAAE;IACvCnG,QAAQ,CAACgG,IAAI,wCAAAjI,MAAA,CAAuCM,IAAI,CAACuB,MAAM,kHAAsG,CAAC;EACxK;EACA,OAAOI,QAAQ,CAACI,IAAI,CAAC,IAAI,CAAC;AAC5B;AAEA,SAASoC,sBAAsBA,CAACnE,IAAI,EAAE;EACpC,IAAM2B,QAAQ,GAAG,EAAE;EACnB,IAAI3B,IAAI,CAACmF,OAAO,IAAInF,IAAI,CAACmF,OAAO,CAAC2C,MAAM,EAAE;IACvC,IAAM3G,GAAG,GAAGnB,IAAI,CAAC+H,UAAU,KAAK,QAAQ,GAAG,iBAAiB,GAAG,UAAU;IACzE,IAAMC,MAAM,GAAGhI,IAAI,CAACgI,MAAM,GAAG,QAAQ,GAAG,EAAE;IAC1CrG,QAAQ,CAACgG,IAAI,KAAAjI,MAAA,CAAKyB,GAAG,gCAAAzB,MAAA,CAA4BM,IAAI,CAACuB,MAAM,iFAAA7B,MAAA,CAAuEsI,MAAM,uBAAAtI,MAAA,CAAoByB,GAAG,MAAG,CAAC;EACtK;EACA,OAAOQ,QAAQ,CAACI,IAAI,CAAC,IAAI,CAAC;AAC5B;AAEA,SAASuC,yBAAyBA,CAACtE,IAAI,EAAE;EACvC,IAAM2B,QAAQ,GAAG,EAAE;EACnB,IAAI3B,IAAI,CAACmF,OAAO,IAAInF,IAAI,CAACmF,OAAO,CAAC2C,MAAM,EAAE;IACvC,IAAM3G,GAAG,GAAGnB,IAAI,CAAC+H,UAAU,KAAK,QAAQ,GAAG,oBAAoB,GAAG,aAAa;IAC/E,IAAMC,MAAM,GAAGhI,IAAI,CAACgI,MAAM,GAAG,QAAQ,GAAG,EAAE;IAC1CrG,QAAQ,CAACgG,IAAI,KAAAjI,MAAA,CAAKyB,GAAG,gCAAAzB,MAAA,CAA4BM,IAAI,CAACuB,MAAM,iFAAA7B,MAAA,CAAuEsI,MAAM,uBAAAtI,MAAA,CAAoByB,GAAG,MAAG,CAAC;EACtK;EACA,OAAOQ,QAAQ,CAACI,IAAI,CAAC,IAAI,CAAC;AAC5B;AAEA,SAASyF,kBAAkBA,CAACxH,IAAI,EAAE;EAChC,IAAMiI,IAAI,GAAG,EAAE;EACf,IAAIjI,IAAI,CAAC,WAAW,CAAC,KAAK,cAAc,EAAEiI,IAAI,CAACN,IAAI,CAAC,8BAA8B,CAAC,MAC9EM,IAAI,CAACN,IAAI,uEAAAjI,MAAA,CAAiEM,IAAI,CAACkI,UAAU,iBAAc,CAAC;EAC7G,IAAIlI,IAAI,CAACmI,OAAO,EAAEF,IAAI,CAACN,IAAI,0FAAAjI,MAAA,CAAmDM,IAAI,CAACoI,QAAQ,EAAA1I,MAAA,CAAGM,IAAI,CAACqI,QAAQ,aAAA3I,MAAA,CAAKM,IAAI,CAACkH,MAAM,uBAAU,CAAC;EACtI,OAAOe,IAAI,CAAClG,IAAI,CAAC,IAAI,CAAC;AACxB;AAEA,OAAO,SAASuG,UAAUA,CAACtI,IAAI,EAAEE,IAAI,EAAE;EACrC,IAAMqI,QAAQ,GAAG,EAAE;EACnBjJ,UAAU,GAAGU,IAAI;EACjBT,eAAe,GAAGS,IAAI,CAACwI,MAAM,CAACC,IAAI,CAAC,UAAAC,IAAI;IAAA,OAAIA,IAAI,CAAC3H,IAAI,KAAK,EAAE;EAAA,EAAC;EAC5Df,IAAI,CAACwI,MAAM,CAACG,OAAO,CAAC,UAAA9G,EAAE,EAAI;IACxB0G,QAAQ,CAACZ,IAAI,CAAC3G,OAAO,CAACa,EAAE,CAACC,MAAM,CAAC,CAACD,EAAE,CAAC,CAAC;EACvC,CAAC,CAAC;EACF,IAAM+G,OAAO,GAAGL,QAAQ,CAACxG,IAAI,CAAC,IAAI,CAAC;EAEnC,IAAI8G,IAAI,GAAG9I,iBAAiB,CAACC,IAAI,EAAE4I,OAAO,EAAE1I,IAAI,CAAC;EACjD,IAAIA,IAAI,KAAK,QAAQ,EAAE;IACrB2I,IAAI,GAAGrJ,aAAa,CAACqJ,IAAI,CAAC;EAC5B;EACAvJ,UAAU,GAAG,IAAI;EACjB,OAAOuJ,IAAI;AACb", "ignoreList": []}]}