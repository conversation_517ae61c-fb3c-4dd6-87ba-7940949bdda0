{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/project/data/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/project/data/index.vue", "mtime": 1718616598765}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGxpc3RQcm9qZWN0RGF0YSwgZ2V0UHJvamVjdERhdGEsIGRlbFByb2plY3REYXRhLCBhZGRQcm9qZWN0RGF0YSwgdXBkYXRlUHJvamVjdERhdGEsIGV4cG9ydFByb2plY3REYXRhIH0gZnJvbSAiQC9hcGkvcHJvamVjdC9kYXRhIjsKaW1wb3J0IEVkaXRvciBmcm9tICJAL2NvbXBvbmVudHMvRWRpdG9yIjsKaW1wb3J0IFVwbG9hZCBmcm9tICJAL2NvbXBvbmVudHMvRmlsZVVwbG9hZCI7CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiUHJvamVjdERhdGEiLAogIGNvbXBvbmVudHM6IHsKICAgIEVkaXRvcjogRWRpdG9yLAogICAgVXBsb2FkOiBVcGxvYWQKICB9LAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICAvLyDpga7nvanlsYIKICAgICAgbG9hZGluZzogdHJ1ZSwKICAgICAgLy8g6YCJ5Lit5pWw57uECiAgICAgIGlkczogW10sCiAgICAgIC8vIOmdnuWNleS4quemgeeUqAogICAgICBzaW5nbGU6IHRydWUsCiAgICAgIC8vIOmdnuWkmuS4quemgeeUqAogICAgICBtdWx0aXBsZTogdHJ1ZSwKICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2CiAgICAgIHNob3dTZWFyY2g6IHRydWUsCiAgICAgIC8vIOaAu+adoeaVsAogICAgICB0b3RhbDogMCwKICAgICAgLy8g5YWs5ZGK6KGo5qC85pWw5o2uCiAgICAgIHByb2plY3REYXRhTGlzdDogW10sCiAgICAgIHR5cGVBcnI6IFsicGRmIl0sCiAgICAgIC8vIOW8ueWHuuWxguagh+mimAogICAgICB0aXRsZTogIiIsCiAgICAgIC8vIOaYr+WQpuaYvuekuuW8ueWHuuWxggogICAgICBvcGVuOiBmYWxzZSwKICAgICAgLy8g57G75Z6L5pWw5o2u5a2X5YW4CiAgICAgIHN0YXR1c09wdGlvbnM6IFtdLAogICAgICAvLyDnirbmgIHmlbDmja7lrZflhbgKICAgICAgdHlwZU9wdGlvbnM6IFtdLAogICAgICAvLyDmn6Xor6Llj4LmlbAKICAgICAgcXVlcnlQYXJhbXM6IHsKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICBub3RpY2VUaXRsZTogdW5kZWZpbmVkLAogICAgICAgIGNyZWF0ZUJ5OiB1bmRlZmluZWQsCiAgICAgICAgc3RhdHVzOiB1bmRlZmluZWQKICAgICAgfSwKICAgICAgLy8g5omA6ZyA6LWE5paZ5a2X5YW4CiAgICAgIHJlcXVpcmVJbmZvT3B0aW9uczogW10sCiAgICAgIHJlcXVpcmVJbmZvT3B0aW9uMTogW10sCiAgICAgIC8vIOihqOWNleWPguaVsAogICAgICBmb3JtOiB7fSwKICAgICAgLy8g6KGo5Y2V5qCh6aqMCiAgICAgIHJ1bGVzOiB7CiAgICAgICAgZGljdFZhbHVlOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAi6LWE5paZ5LiN6IO95Li656m6IiwKICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgIH1dLAogICAgICAgIHVybDogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogIuS4iuS8oOeahOaWh+S7tuS4jeiDveS4uuepuiIsCiAgICAgICAgICB0cmlnZ2VyOiAiY2hhbmdlIgogICAgICAgIH1dCiAgICAgIH0KICAgIH07CiAgfSwKICBjcmVhdGVkOiBmdW5jdGlvbiBjcmVhdGVkKCkgewogICAgdmFyIF90aGlzID0gdGhpczsKICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgdGhpcy5nZXREaWN0cygic3lzX25vdGljZV9zdGF0dXMiKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICBfdGhpcy5zdGF0dXNPcHRpb25zID0gcmVzcG9uc2UuZGF0YTsKICAgIH0pOwogICAgdGhpcy5nZXREaWN0cygicHJfaW5mbyIpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgIF90aGlzLnJlcXVpcmVJbmZvT3B0aW9uMSA9IHJlc3BvbnNlLmRhdGE7CiAgICAgIHZhciBvcHQgPSBbXTsKICAgICAgcmVzcG9uc2UuZGF0YS5mb3JFYWNoKGZ1bmN0aW9uIChlbGVtLCBpbmRleCkgewogICAgICAgIHZhciBvYmogPSB7fTsKICAgICAgICBvYmoubGFiZWwgPSBlbGVtLmRpY3RMYWJlbDsKICAgICAgICBvYmoudmFsdWUgPSBlbGVtLmRpY3RWYWx1ZTsKICAgICAgICBvcHQucHVzaChvYmopOwogICAgICB9KTsKICAgICAgX3RoaXMucmVxdWlyZUluZm9PcHRpb25zID0gb3B0OwogICAgfSk7CiAgfSwKICBtZXRob2RzOiB7CiAgICAvKiog5p+l6K+i6LWE5paZ5YiX6KGoICovZ2V0TGlzdDogZnVuY3Rpb24gZ2V0TGlzdCgpIHsKICAgICAgdmFyIF90aGlzMiA9IHRoaXM7CiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7CiAgICAgIGxpc3RQcm9qZWN0RGF0YSh0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgIF90aGlzMi5wcm9qZWN0RGF0YUxpc3QgPSByZXNwb25zZS5yb3dzOwogICAgICAgIF90aGlzMi50b3RhbCA9IHJlc3BvbnNlLnRvdGFsOwogICAgICAgIF90aGlzMi5sb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKICAgIHVybDogZnVuY3Rpb24gdXJsKHZhbHVlKSB7CiAgICAgIHRoaXMuZm9ybS51cmwgPSB2YWx1ZTsKICAgIH0sCiAgICAvLyDlhazlkYrnirbmgIHlrZflhbjnv7vor5EKICAgIHN0YXR1c0Zvcm1hdDogZnVuY3Rpb24gc3RhdHVzRm9ybWF0KHJvdywgY29sdW1uKSB7CiAgICAgIHJldHVybiB0aGlzLnNlbGVjdERpY3RMYWJlbCh0aGlzLnN0YXR1c09wdGlvbnMsIHJvdy5zdGF0dXMpOwogICAgfSwKICAgIC8vIOWFrOWRiueKtuaAgeWtl+WFuOe/u+ivkQogICAgZGljdFZhbHVlRm9ybWF0OiBmdW5jdGlvbiBkaWN0VmFsdWVGb3JtYXQocm93LCBjb2x1bW4pIHsKICAgICAgcmV0dXJuIHRoaXMuc2VsZWN0RGljdExhYmVsKHRoaXMucmVxdWlyZUluZm9PcHRpb24xLCByb3cuZGljdFZhbHVlKTsKICAgIH0sCiAgICAvLyDlj5bmtojmjInpkq4KICAgIGNhbmNlbDogZnVuY3Rpb24gY2FuY2VsKCkgewogICAgICB0aGlzLm9wZW4gPSBmYWxzZTsKICAgICAgdGhpcy5yZXNldCgpOwogICAgfSwKICAgIC8vIOihqOWNlemHjee9rgogICAgcmVzZXQ6IGZ1bmN0aW9uIHJlc2V0KCkgewogICAgICB0aGlzLmZvcm0gPSB7CiAgICAgICAgZGljdFZhbHVlOiB1bmRlZmluZWQsCiAgICAgICAgdXJsOiB1bmRlZmluZWQsCiAgICAgICAgc3RhdHVzOiAiMCIKICAgICAgfTsKICAgICAgdGhpcy5yZXNldEZvcm0oImZvcm0iKTsKICAgIH0sCiAgICAvKiog5pCc57Si5oyJ6ZKu5pON5L2cICovaGFuZGxlUXVlcnk6IGZ1bmN0aW9uIGhhbmRsZVF1ZXJ5KCkgewogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOwogICAgICB0aGlzLmdldExpc3QoKTsKICAgIH0sCiAgICAvKiog6YeN572u5oyJ6ZKu5pON5L2cICovcmVzZXRRdWVyeTogZnVuY3Rpb24gcmVzZXRRdWVyeSgpIHsKICAgICAgdGhpcy5yZXNldEZvcm0oInF1ZXJ5Rm9ybSIpOwogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7CiAgICB9LAogICAgLy8g5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2U6IGZ1bmN0aW9uIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsKICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgcmV0dXJuIGl0ZW0uaWQ7CiAgICAgIH0pOwogICAgICB0aGlzLnNpbmdsZSA9IHNlbGVjdGlvbi5sZW5ndGggIT0gMTsKICAgICAgdGhpcy5tdWx0aXBsZSA9ICFzZWxlY3Rpb24ubGVuZ3RoOwogICAgfSwKICAgIC8qKiDmlrDlop7mjInpkq7mk43kvZwgKi9oYW5kbGVBZGQ6IGZ1bmN0aW9uIGhhbmRsZUFkZCgpIHsKICAgICAgdGhpcy5yZXNldCgpOwogICAgICB0aGlzLm9wZW4gPSB0cnVlOwogICAgICB0aGlzLnRpdGxlID0gIua3u+WKoOi1hOaWmSI7CiAgICB9LAogICAgLyoqIOS/ruaUueaMiemSruaTjeS9nCAqL2hhbmRsZVVwZGF0ZTogZnVuY3Rpb24gaGFuZGxlVXBkYXRlKHJvdykgewogICAgICB0aGlzLnJlc2V0KCk7CiAgICAgIHRoaXMuZm9ybSA9IHsKICAgICAgICBpZDogcm93LmlkLAogICAgICAgIHVybDogcm93LnVybCwKICAgICAgICBkaWN0VmFsdWU6IHJvdy5kaWN0VmFsdWUKICAgICAgfTsKICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsKICAgICAgdGhpcy50aXRsZSA9ICLkv67mlLnotYTmlpkiOwogICAgfSwKICAgIGhhbmRsZURvd25sb2FkOiBmdW5jdGlvbiBoYW5kbGVEb3dubG9hZChyb3cpIHsKICAgICAgdmFyIGEgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCJhIik7IC8vIOWIm+W7uuS4gOS4qkhUTUwg5YWD57SgCiAgICAgIGEuc2V0QXR0cmlidXRlKCJ0YXJnZXQiLCAiX2JsYW5rIik7CiAgICAgIGEuc2V0QXR0cmlidXRlKCJkb3dubG9hZCIsICIiKTsgLy9kb3dubG9hZOWxnuaApwogICAgICB2YXIgaHJlZiA9ICJodHRwczovL3JlcG9ydC5jbGxlZC5jb20vcHJvZC1hcGkvY29tbW9uL2Rvd25sb2FkL3Jlc291cmNlP3Jlc291cmNlPSIgKyByb3cudXJsOwogICAgICBhLnNldEF0dHJpYnV0ZSgiaHJlZiIsIGhyZWYpOyAvLyBocmVm6ZO+5o6lCiAgICAgIGEuY2xpY2soKTsgLy8g6Ieq5omn6KGM54K55Ye75LqL5Lu2CiAgICB9LAogICAgLyoqIOaPkOS6pOaMiemSriAqLwogICAgc3VibWl0Rm9ybTogZnVuY3Rpb24gc3VibWl0Rm9ybSgpIHsKICAgICAgdmFyIF90aGlzMyA9IHRoaXM7CiAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZShmdW5jdGlvbiAodmFsaWQpIHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIGlmIChfdGhpczMuZm9ybS51cmwgPT0gdW5kZWZpbmVkIHx8IF90aGlzMy5mb3JtLnVybCA9PSAiIikgewogICAgICAgICAgICByZXR1cm4gX3RoaXMzLm1zZ0Vycm9yKCLor7fpgInmi6npnIDopoHkuIrkvKDnmoTmlofku7YiKTsKICAgICAgICAgIH0KICAgICAgICAgIGlmIChfdGhpczMuZm9ybS5pZCAhPSB1bmRlZmluZWQpIHsKICAgICAgICAgICAgdXBkYXRlUHJvamVjdERhdGEoX3RoaXMzLmZvcm0pLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgICAgICAgX3RoaXMzLm1zZ1N1Y2Nlc3MoIuS/ruaUueaIkOWKnyIpOwogICAgICAgICAgICAgIF90aGlzMy5vcGVuID0gZmFsc2U7CiAgICAgICAgICAgICAgX3RoaXMzLmdldExpc3QoKTsKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICBhZGRQcm9qZWN0RGF0YShfdGhpczMuZm9ybSkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICAgICAgICBfdGhpczMubXNnU3VjY2Vzcygi5paw5aKe5oiQ5YqfIik7CiAgICAgICAgICAgICAgX3RoaXMzLm9wZW4gPSBmYWxzZTsKICAgICAgICAgICAgICBfdGhpczMuZ2V0TGlzdCgpOwogICAgICAgICAgICB9KTsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDliKDpmaTmjInpkq7mk43kvZwgKi9oYW5kbGVEZWxldGU6IGZ1bmN0aW9uIGhhbmRsZURlbGV0ZShyb3cpIHsKICAgICAgdmFyIF90aGlzNCA9IHRoaXM7CiAgICAgIHZhciBpZHMgPSByb3cuaWQgfHwgdGhpcy5pZHM7CiAgICAgIHRoaXMuJGNvbmZpcm0oJ+aYr+WQpuehruiupOWIoOmZpOmhueebrui1hOaWmee8luWPt+S4uiInICsgaWRzICsgJyLnmoTmlbDmja7pobk/JywgIuitpuWRiiIsIHsKICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogIuehruWumiIsCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogIuWPlua2iCIsCiAgICAgICAgdHlwZTogIndhcm5pbmciCiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgIHJldHVybiBkZWxQcm9qZWN0RGF0YShpZHMpOwogICAgICB9KS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICBfdGhpczQuZ2V0TGlzdCgpOwogICAgICAgIF90aGlzNC5tc2dTdWNjZXNzKCLliKDpmaTmiJDlip8iKTsKICAgICAgfSk7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["listProjectData", "getProjectData", "delProjectData", "addProjectData", "updateProjectData", "exportProjectData", "Editor", "Upload", "name", "components", "data", "loading", "ids", "single", "multiple", "showSearch", "total", "projectDataList", "typeArr", "title", "open", "statusOptions", "typeOptions", "queryParams", "pageNum", "pageSize", "noticeTitle", "undefined", "createBy", "status", "requireInfoOptions", "requireInfoOption1", "form", "rules", "dict<PERSON><PERSON>ue", "required", "message", "trigger", "url", "created", "_this", "getList", "getDicts", "then", "response", "opt", "for<PERSON>ach", "elem", "index", "obj", "label", "dict<PERSON><PERSON>l", "value", "push", "methods", "_this2", "rows", "statusFormat", "row", "column", "selectDictLabel", "dictValueFormat", "cancel", "reset", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "map", "item", "id", "length", "handleAdd", "handleUpdate", "handleDownload", "a", "document", "createElement", "setAttribute", "href", "click", "submitForm", "_this3", "$refs", "validate", "valid", "msgError", "msgSuccess", "handleDelete", "_this4", "$confirm", "confirmButtonText", "cancelButtonText", "type"], "sources": ["src/views/project/data/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form\n      :model=\"queryParams\"\n      ref=\"queryForm\"\n      :inline=\"true\"\n      v-show=\"showSearch\"\n      label-width=\"68px\"\n    >\n      <el-form-item label=\"所需资料\" prop=\"dictValue\">\n        <el-select\n          v-model=\"queryParams.dictValue\"\n          placeholder=\"所需资料\"\n          clearable\n          size=\"small\"\n        >\n          <el-option\n            v-for=\"dict in requireInfoOptions\"\n            :key=\"dict.value\"\n            :label=\"dict.label\"\n            :value=\"dict.value\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item>\n        <el-button\n          type=\"primary\"\n          icon=\"el-icon-search\"\n          size=\"mini\"\n          @click=\"handleQuery\"\n          >搜索</el-button\n        >\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\"\n          >重置</el-button\n        >\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"primary\"\n          plain\n          icon=\"el-icon-plus\"\n          size=\"mini\"\n          @click=\"handleAdd\"\n          v-hasPermi=\"['project:data:add']\"\n          >新增</el-button\n        >\n      </el-col>\n      <!-- <el-col :span=\"1.5\">\n        <el-button\n          type=\"success\"\n          plain\n          icon=\"el-icon-edit\"\n          size=\"mini\"\n          :disabled=\"single\"\n          @click=\"handleUpdate\"\n          v-hasPermi=\"['project:data:edit']\"\n          >修改</el-button\n        >\n      </el-col> -->\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"danger\"\n          plain\n          icon=\"el-icon-delete\"\n          size=\"mini\"\n          :disabled=\"multiple\"\n          @click=\"handleDelete\"\n          v-hasPermi=\"['project:data:remove']\"\n          >删除</el-button\n        >\n      </el-col>\n      <right-toolbar\n        :showSearch.sync=\"showSearch\"\n        @queryTable=\"getList\"\n      ></right-toolbar>\n    </el-row>\n\n    <el-table\n      v-loading=\"loading\"\n      :data=\"projectDataList\"\n      @selection-change=\"handleSelectionChange\"\n    >\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column label=\"序号\" align=\"center\" prop=\"id\" width=\"100\" />\n      <el-table-column\n        label=\"资料\"\n        align=\"center\"\n        prop=\"dictValue\"\n        :formatter=\"dictValueFormat\"\n        :show-overflow-tooltip=\"true\"\n        width=\"100\"\n      />\n      <el-table-column\n        label=\"地址\"\n        align=\"center\"\n        prop=\"url\"\n        :show-overflow-tooltip=\"true\"\n      />\n      <!-- <el-table-column\n        label=\"状态\"\n        align=\"center\"\n        prop=\"status\"\n        :formatter=\"statusFormat\"\n        width=\"100\"\n      /> -->\n      <el-table-column\n        label=\"创建者\"\n        align=\"center\"\n        prop=\"createBy\"\n        width=\"100\"\n      />\n      <el-table-column\n        label=\"创建时间\"\n        align=\"center\"\n        prop=\"createTime\"\n        width=\"100\"\n      >\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.createTime, \"{y}-{m}-{d}\") }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column\n        label=\"操作\"\n        align=\"center\"\n        class-name=\"small-padding fixed-width\"\n      >\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-edit\"\n            @click=\"handleUpdate(scope.row)\"\n            v-hasPermi=\"['project:data:edit']\"\n            >修改</el-button\n          >\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-delete\"\n            @click=\"handleDelete(scope.row)\"\n            v-hasPermi=\"['project:data:remove']\"\n            >删除</el-button\n          >\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-download\"\n            @click=\"handleDownload(scope.row)\"\n            v-hasPermi=\"['project:data:download']\"\n            >下载</el-button\n          >\n          <!-- <el-link\n            target=\"_blank\"\n            v-hasPermi=\"['project:data:download']\"\n            :href=\"scope.row.url\"\n            type=\"primary\"\n            >下载</el-link\n          > -->\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <pagination\n      v-show=\"total > 0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 添加或修改公告对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"780px\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"所需资料\" prop=\"dictValue\">\n              <el-select\n                v-model=\"form.dictValue\"\n                placeholder=\"所需资料\"\n                clearable\n                size=\"small\"\n              >\n                <el-option\n                  v-for=\"dict in requireInfoOptions\"\n                  :key=\"dict.value\"\n                  :label=\"dict.label\"\n                  :value=\"dict.value\"\n                />\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"内容\">\n              <Upload\n                :fileSize=\"100\"\n                :value=\"form.url\"\n                @input=\"url\"\n                :fileType=\"typeArr\"\n              ></Upload>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport {\n  listProjectData,\n  getProjectData,\n  delProjectData,\n  addProjectData,\n  updateProjectData,\n  exportProjectData,\n} from \"@/api/project/data\";\nimport Editor from \"@/components/Editor\";\nimport Upload from \"@/components/FileUpload\";\n\nexport default {\n  name: \"ProjectData\",\n  components: {\n    Editor,\n    Upload,\n  },\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 公告表格数据\n      projectDataList: [],\n      typeArr: [\"pdf\"],\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      open: false,\n      // 类型数据字典\n      statusOptions: [],\n      // 状态数据字典\n      typeOptions: [],\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        noticeTitle: undefined,\n        createBy: undefined,\n        status: undefined,\n      },\n      // 所需资料字典\n      requireInfoOptions: [],\n      requireInfoOption1: [],\n      // 表单参数\n      form: {},\n      // 表单校验\n      rules: {\n        dictValue: [\n          { required: true, message: \"资料不能为空\", trigger: \"blur\" },\n        ],\n        url: [\n          { required: true, message: \"上传的文件不能为空\", trigger: \"change\" },\n        ],\n      },\n    };\n  },\n  created() {\n    this.getList();\n    this.getDicts(\"sys_notice_status\").then((response) => {\n      this.statusOptions = response.data;\n    });\n    this.getDicts(\"pr_info\").then((response) => {\n      this.requireInfoOption1 = response.data;\n      var opt = [];\n      response.data.forEach((elem, index) => {\n        var obj = {};\n        obj.label = elem.dictLabel;\n        obj.value = elem.dictValue;\n        opt.push(obj);\n      });\n      this.requireInfoOptions = opt;\n    });\n  },\n  methods: {\n    /** 查询资料列表 */\n    getList() {\n      this.loading = true;\n      listProjectData(this.queryParams).then((response) => {\n        this.projectDataList = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    url(value) {\n      this.form.url = value;\n    },\n    // 公告状态字典翻译\n    statusFormat(row, column) {\n      return this.selectDictLabel(this.statusOptions, row.status);\n    },\n    // 公告状态字典翻译\n    dictValueFormat(row, column) {\n      return this.selectDictLabel(this.requireInfoOption1, row.dictValue);\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        dictValue: undefined,\n        url: undefined,\n        status: \"0\",\n      };\n      this.resetForm(\"form\");\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map((item) => item.id);\n      this.single = selection.length != 1;\n      this.multiple = !selection.length;\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.reset();\n      this.open = true;\n      this.title = \"添加资料\";\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset();\n\n      this.form = {\n        id: row.id,\n        url: row.url,\n        dictValue: row.dictValue,\n      };\n      this.open = true;\n      this.title = \"修改资料\";\n    },\n    handleDownload(row) {\n      const a = document.createElement(\"a\"); // 创建一个HTML 元素\n      a.setAttribute(\"target\", \"_blank\");\n      a.setAttribute(\"download\", \"\"); //download属性\n      const href =\n        \"https://report.clled.com/prod-api/common/download/resource?resource=\" +\n        row.url;\n      a.setAttribute(\"href\", href); // href链接\n      a.click(); // 自执行点击事件\n    },\n    /** 提交按钮 */\n    submitForm: function () {\n      this.$refs[\"form\"].validate((valid) => {\n        if (valid) {\n          if (this.form.url == undefined || this.form.url == \"\") {\n            return this.msgError(\"请选择需要上传的文件\");\n          }\n          if (this.form.id != undefined) {\n            updateProjectData(this.form).then((response) => {\n              this.msgSuccess(\"修改成功\");\n              this.open = false;\n              this.getList();\n            });\n          } else {\n            addProjectData(this.form).then((response) => {\n              this.msgSuccess(\"新增成功\");\n              this.open = false;\n              this.getList();\n            });\n          }\n        }\n      });\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const ids = row.id || this.ids;\n      this.$confirm(\n        '是否确认删除项目资料编号为\"' + ids + '\"的数据项?',\n        \"警告\",\n        {\n          confirmButtonText: \"确定\",\n          cancelButtonText: \"取消\",\n          type: \"warning\",\n        }\n      )\n        .then(function () {\n          return delProjectData(ids);\n        })\n        .then(() => {\n          this.getList();\n          this.msgSuccess(\"删除成功\");\n        });\n    },\n  },\n};\n</script>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuNA,SACAA,eAAA,EACAC,cAAA,EACAC,cAAA,EACAC,cAAA,EACAC,iBAAA,EACAC,iBAAA,QACA;AACA,OAAAC,MAAA;AACA,OAAAC,MAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAH,MAAA,EAAAA,MAAA;IACAC,MAAA,EAAAA;EACA;EACAG,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,eAAA;MACAC,OAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,aAAA;MACA;MACAC,WAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,WAAA,EAAAC,SAAA;QACAC,QAAA,EAAAD,SAAA;QACAE,MAAA,EAAAF;MACA;MACA;MACAG,kBAAA;MACAC,kBAAA;MACA;MACAC,IAAA;MACA;MACAC,KAAA;QACAC,SAAA,GACA;UAAAC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAC,GAAA,GACA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAE,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA,KAAAC,OAAA;IACA,KAAAC,QAAA,sBAAAC,IAAA,WAAAC,QAAA;MACAJ,KAAA,CAAAnB,aAAA,GAAAuB,QAAA,CAAAlC,IAAA;IACA;IACA,KAAAgC,QAAA,YAAAC,IAAA,WAAAC,QAAA;MACAJ,KAAA,CAAAT,kBAAA,GAAAa,QAAA,CAAAlC,IAAA;MACA,IAAAmC,GAAA;MACAD,QAAA,CAAAlC,IAAA,CAAAoC,OAAA,WAAAC,IAAA,EAAAC,KAAA;QACA,IAAAC,GAAA;QACAA,GAAA,CAAAC,KAAA,GAAAH,IAAA,CAAAI,SAAA;QACAF,GAAA,CAAAG,KAAA,GAAAL,IAAA,CAAAb,SAAA;QACAW,GAAA,CAAAQ,IAAA,CAAAJ,GAAA;MACA;MACAT,KAAA,CAAAV,kBAAA,GAAAe,GAAA;IACA;EACA;EACAS,OAAA;IACA,aACAb,OAAA,WAAAA,QAAA;MAAA,IAAAc,MAAA;MACA,KAAA5C,OAAA;MACAX,eAAA,MAAAuB,WAAA,EAAAoB,IAAA,WAAAC,QAAA;QACAW,MAAA,CAAAtC,eAAA,GAAA2B,QAAA,CAAAY,IAAA;QACAD,MAAA,CAAAvC,KAAA,GAAA4B,QAAA,CAAA5B,KAAA;QACAuC,MAAA,CAAA5C,OAAA;MACA;IACA;IACA2B,GAAA,WAAAA,IAAAc,KAAA;MACA,KAAApB,IAAA,CAAAM,GAAA,GAAAc,KAAA;IACA;IACA;IACAK,YAAA,WAAAA,aAAAC,GAAA,EAAAC,MAAA;MACA,YAAAC,eAAA,MAAAvC,aAAA,EAAAqC,GAAA,CAAA7B,MAAA;IACA;IACA;IACAgC,eAAA,WAAAA,gBAAAH,GAAA,EAAAC,MAAA;MACA,YAAAC,eAAA,MAAA7B,kBAAA,EAAA2B,GAAA,CAAAxB,SAAA;IACA;IACA;IACA4B,MAAA,WAAAA,OAAA;MACA,KAAA1C,IAAA;MACA,KAAA2C,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAA/B,IAAA;QACAE,SAAA,EAAAP,SAAA;QACAW,GAAA,EAAAX,SAAA;QACAE,MAAA;MACA;MACA,KAAAmC,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAA1C,WAAA,CAAAC,OAAA;MACA,KAAAiB,OAAA;IACA;IACA,aACAyB,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAAC,WAAA;IACA;IACA;IACAE,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAxD,GAAA,GAAAwD,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,EAAA;MAAA;MACA,KAAA1D,MAAA,GAAAuD,SAAA,CAAAI,MAAA;MACA,KAAA1D,QAAA,IAAAsD,SAAA,CAAAI,MAAA;IACA;IACA,aACAC,SAAA,WAAAA,UAAA;MACA,KAAAV,KAAA;MACA,KAAA3C,IAAA;MACA,KAAAD,KAAA;IACA;IACA,aACAuD,YAAA,WAAAA,aAAAhB,GAAA;MACA,KAAAK,KAAA;MAEA,KAAA/B,IAAA;QACAuC,EAAA,EAAAb,GAAA,CAAAa,EAAA;QACAjC,GAAA,EAAAoB,GAAA,CAAApB,GAAA;QACAJ,SAAA,EAAAwB,GAAA,CAAAxB;MACA;MACA,KAAAd,IAAA;MACA,KAAAD,KAAA;IACA;IACAwD,cAAA,WAAAA,eAAAjB,GAAA;MACA,IAAAkB,CAAA,GAAAC,QAAA,CAAAC,aAAA;MACAF,CAAA,CAAAG,YAAA;MACAH,CAAA,CAAAG,YAAA;MACA,IAAAC,IAAA,GACA,yEACAtB,GAAA,CAAApB,GAAA;MACAsC,CAAA,CAAAG,YAAA,SAAAC,IAAA;MACAJ,CAAA,CAAAK,KAAA;IACA;IACA;IACAC,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAH,MAAA,CAAAnD,IAAA,CAAAM,GAAA,IAAAX,SAAA,IAAAwD,MAAA,CAAAnD,IAAA,CAAAM,GAAA;YACA,OAAA6C,MAAA,CAAAI,QAAA;UACA;UACA,IAAAJ,MAAA,CAAAnD,IAAA,CAAAuC,EAAA,IAAA5C,SAAA;YACAvB,iBAAA,CAAA+E,MAAA,CAAAnD,IAAA,EAAAW,IAAA,WAAAC,QAAA;cACAuC,MAAA,CAAAK,UAAA;cACAL,MAAA,CAAA/D,IAAA;cACA+D,MAAA,CAAA1C,OAAA;YACA;UACA;YACAtC,cAAA,CAAAgF,MAAA,CAAAnD,IAAA,EAAAW,IAAA,WAAAC,QAAA;cACAuC,MAAA,CAAAK,UAAA;cACAL,MAAA,CAAA/D,IAAA;cACA+D,MAAA,CAAA1C,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACAgD,YAAA,WAAAA,aAAA/B,GAAA;MAAA,IAAAgC,MAAA;MACA,IAAA9E,GAAA,GAAA8C,GAAA,CAAAa,EAAA,SAAA3D,GAAA;MACA,KAAA+E,QAAA,CACA,mBAAA/E,GAAA,aACA,MACA;QACAgF,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,CACA,EACAnD,IAAA;QACA,OAAAzC,cAAA,CAAAU,GAAA;MACA,GACA+B,IAAA;QACA+C,MAAA,CAAAjD,OAAA;QACAiD,MAAA,CAAAF,UAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}