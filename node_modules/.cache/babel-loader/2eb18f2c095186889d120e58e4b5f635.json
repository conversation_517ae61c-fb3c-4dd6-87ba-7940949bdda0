{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/components/render/slots/el-input.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/components/render/slots/el-input.js", "mtime": 1641277326000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:ZXhwb3J0IGRlZmF1bHQgewogIHByZXBlbmQ6IGZ1bmN0aW9uIHByZXBlbmQoaCwgY29uZiwga2V5KSB7CiAgICByZXR1cm4gaCgidGVtcGxhdGUiLCB7CiAgICAgICJzbG90IjogInByZXBlbmQiCiAgICB9LCBbY29uZi5fX3Nsb3RfX1trZXldXSk7CiAgfSwKICBhcHBlbmQ6IGZ1bmN0aW9uIGFwcGVuZChoLCBjb25mLCBrZXkpIHsKICAgIHJldHVybiBoKCJ0ZW1wbGF0ZSIsIHsKICAgICAgInNsb3QiOiAiYXBwZW5kIgogICAgfSwgW2NvbmYuX19zbG90X19ba2V5XV0pOwogIH0KfTs="}, {"version": 3, "names": ["prepend", "h", "conf", "key", "__slot__", "append"], "sources": ["/Users/<USER>/develop/code/project/haijia/adminweb/src/components/render/slots/el-input.js"], "sourcesContent": ["export default {\n  prepend(h, conf, key) {\n    return <template slot=\"prepend\">{conf.__slot__[key]}</template>\n  },\n  append(h, conf, key) {\n    return <template slot=\"append\">{conf.__slot__[key]}</template>\n  }\n}\n"], "mappings": "AAAA,eAAe;EACbA,OAAO,WAAAA,QAACC,CAAC,EAAEC,IAAI,EAAEC,GAAG,EAAE;IACpB,OAAAF,CAAA;MAAA,QAAsB;IAAS,IAAEC,IAAI,CAACE,QAAQ,CAACD,GAAG,CAAC;EACrD,CAAC;EACDE,MAAM,WAAAA,OAACJ,CAAC,EAAEC,IAAI,EAAEC,GAAG,EAAE;IACnB,OAAAF,CAAA;MAAA,QAAsB;IAAQ,IAAEC,IAAI,CAACE,QAAQ,CAACD,GAAG,CAAC;EACpD;AACF,CAAC", "ignoreList": []}]}