{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Breadcrumb/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Breadcrumb/index.vue", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmV4cG9ydCBkZWZhdWx0IHsKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgbGV2ZWxMaXN0OiBudWxsCiAgICB9OwogIH0sCiAgd2F0Y2g6IHsKICAgICRyb3V0ZTogZnVuY3Rpb24gJHJvdXRlKHJvdXRlKSB7CiAgICAgIC8vIGlmIHlvdSBnbyB0byB0aGUgcmVkaXJlY3QgcGFnZSwgZG8gbm90IHVwZGF0ZSB0aGUgYnJlYWRjcnVtYnMKICAgICAgaWYgKHJvdXRlLnBhdGguc3RhcnRzV2l0aCgnL3JlZGlyZWN0LycpKSB7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIHRoaXMuZ2V0QnJlYWRjcnVtYigpOwogICAgfQogIH0sCiAgY3JlYXRlZDogZnVuY3Rpb24gY3JlYXRlZCgpIHsKICAgIHRoaXMuZ2V0QnJlYWRjcnVtYigpOwogIH0sCiAgbWV0aG9kczogewogICAgZ2V0QnJlYWRjcnVtYjogZnVuY3Rpb24gZ2V0QnJlYWRjcnVtYigpIHsKICAgICAgLy8gb25seSBzaG93IHJvdXRlcyB3aXRoIG1ldGEudGl0bGUKICAgICAgdmFyIG1hdGNoZWQgPSB0aGlzLiRyb3V0ZS5tYXRjaGVkLmZpbHRlcihmdW5jdGlvbiAoaXRlbSkgewogICAgICAgIHJldHVybiBpdGVtLm1ldGEgJiYgaXRlbS5tZXRhLnRpdGxlOwogICAgICB9KTsKICAgICAgdmFyIGZpcnN0ID0gbWF0Y2hlZFswXTsKICAgICAgaWYgKCF0aGlzLmlzRGFzaGJvYXJkKGZpcnN0KSkgewogICAgICAgIG1hdGNoZWQgPSBbewogICAgICAgICAgcGF0aDogJy9pbmRleCcsCiAgICAgICAgICBtZXRhOiB7CiAgICAgICAgICAgIHRpdGxlOiAn6aaW6aG1JwogICAgICAgICAgfQogICAgICAgIH1dLmNvbmNhdChtYXRjaGVkKTsKICAgICAgfQogICAgICB0aGlzLmxldmVsTGlzdCA9IG1hdGNoZWQuZmlsdGVyKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgcmV0dXJuIGl0ZW0ubWV0YSAmJiBpdGVtLm1ldGEudGl0bGUgJiYgaXRlbS5tZXRhLmJyZWFkY3J1bWIgIT09IGZhbHNlOwogICAgICB9KTsKICAgIH0sCiAgICBpc0Rhc2hib2FyZDogZnVuY3Rpb24gaXNEYXNoYm9hcmQocm91dGUpIHsKICAgICAgdmFyIG5hbWUgPSByb3V0ZSAmJiByb3V0ZS5uYW1lOwogICAgICBpZiAoIW5hbWUpIHsKICAgICAgICByZXR1cm4gZmFsc2U7CiAgICAgIH0KICAgICAgcmV0dXJuIG5hbWUudHJpbSgpID09PSAn6aaW6aG1JzsKICAgIH0sCiAgICBoYW5kbGVMaW5rOiBmdW5jdGlvbiBoYW5kbGVMaW5rKGl0ZW0pIHsKICAgICAgdmFyIHJlZGlyZWN0ID0gaXRlbS5yZWRpcmVjdCwKICAgICAgICBwYXRoID0gaXRlbS5wYXRoOwogICAgICBpZiAocmVkaXJlY3QpIHsKICAgICAgICB0aGlzLiRyb3V0ZXIucHVzaChyZWRpcmVjdCk7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHBhdGgpOwogICAgfQogIH0KfTs="}, {"version": 3, "names": ["data", "levelList", "watch", "$route", "route", "path", "startsWith", "getBreadcrumb", "created", "methods", "matched", "filter", "item", "meta", "title", "first", "isDashboard", "concat", "breadcrumb", "name", "trim", "handleLink", "redirect", "$router", "push"], "sources": ["src/components/Breadcrumb/index.vue"], "sourcesContent": ["<template>\n  <el-breadcrumb class=\"app-breadcrumb\" separator=\"/\">\n    <transition-group name=\"breadcrumb\">\n      <el-breadcrumb-item v-for=\"(item,index) in levelList\" :key=\"item.path\">\n        <span v-if=\"item.redirect==='noRedirect'||index==levelList.length-1\" class=\"no-redirect\">{{ item.meta.title }}</span>\n        <a v-else @click.prevent=\"handleLink(item)\">{{ item.meta.title }}</a>\n      </el-breadcrumb-item>\n    </transition-group>\n  </el-breadcrumb>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      levelList: null\n    }\n  },\n  watch: {\n    $route(route) {\n      // if you go to the redirect page, do not update the breadcrumbs\n      if (route.path.startsWith('/redirect/')) {\n        return\n      }\n      this.getBreadcrumb()\n    }\n  },\n  created() {\n    this.getBreadcrumb()\n  },\n  methods: {\n    getBreadcrumb() {\n      // only show routes with meta.title\n      let matched = this.$route.matched.filter(item => item.meta && item.meta.title)\n      const first = matched[0]\n\n      if (!this.isDashboard(first)) {\n        matched = [{ path: '/index', meta: { title: '首页' }}].concat(matched)\n      }\n\n      this.levelList = matched.filter(item => item.meta && item.meta.title && item.meta.breadcrumb !== false)\n    },\n    isDashboard(route) {\n      const name = route && route.name\n      if (!name) {\n        return false\n      }\n      return name.trim() === '首页'\n    },\n    handleLink(item) {\n      const { redirect, path } = item\n      if (redirect) {\n        this.$router.push(redirect)\n        return\n      }\n      this.$router.push(path)\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.app-breadcrumb.el-breadcrumb {\n  display: inline-block;\n  font-size: 14px;\n  line-height: 50px;\n  margin-left: 8px;\n\n  .no-redirect {\n    color: #97a8be;\n    cursor: text;\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;AAYA;EACAA,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA;IACA;EACA;EACAC,KAAA;IACAC,MAAA,WAAAA,OAAAC,KAAA;MACA;MACA,IAAAA,KAAA,CAAAC,IAAA,CAAAC,UAAA;QACA;MACA;MACA,KAAAC,aAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAD,aAAA;EACA;EACAE,OAAA;IACAF,aAAA,WAAAA,cAAA;MACA;MACA,IAAAG,OAAA,QAAAP,MAAA,CAAAO,OAAA,CAAAC,MAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,IAAA,IAAAD,IAAA,CAAAC,IAAA,CAAAC,KAAA;MAAA;MACA,IAAAC,KAAA,GAAAL,OAAA;MAEA,UAAAM,WAAA,CAAAD,KAAA;QACAL,OAAA;UAAAL,IAAA;UAAAQ,IAAA;YAAAC,KAAA;UAAA;QAAA,GAAAG,MAAA,CAAAP,OAAA;MACA;MAEA,KAAAT,SAAA,GAAAS,OAAA,CAAAC,MAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,IAAA,IAAAD,IAAA,CAAAC,IAAA,CAAAC,KAAA,IAAAF,IAAA,CAAAC,IAAA,CAAAK,UAAA;MAAA;IACA;IACAF,WAAA,WAAAA,YAAAZ,KAAA;MACA,IAAAe,IAAA,GAAAf,KAAA,IAAAA,KAAA,CAAAe,IAAA;MACA,KAAAA,IAAA;QACA;MACA;MACA,OAAAA,IAAA,CAAAC,IAAA;IACA;IACAC,UAAA,WAAAA,WAAAT,IAAA;MACA,IAAAU,QAAA,GAAAV,IAAA,CAAAU,QAAA;QAAAjB,IAAA,GAAAO,IAAA,CAAAP,IAAA;MACA,IAAAiB,QAAA;QACA,KAAAC,OAAA,CAAAC,IAAA,CAAAF,QAAA;QACA;MACA;MACA,KAAAC,OAAA,CAAAC,IAAA,CAAAnB,IAAA;IACA;EACA;AACA", "ignoreList": []}]}