{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Process/components/nodePanel/property/multiInstance.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Process/components/nodePanel/property/multiInstance.vue", "mtime": 1641277326000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["mixinPanel", "formatJsonKeyValue", "mixins", "data", "dialogVisible", "formData", "computed", "formConfig", "_this", "inline", "item", "xType", "name", "label", "tooltip", "dic", "value", "operate", "text", "show", "click", "save", "mounted", "_this$element$busines", "_cache$completionCond", "cache", "JSON", "parse", "stringify", "element", "businessObject", "loopCharacteristics", "completionCondition", "body", "methods", "updateElement", "isSequential", "undefined", "get", "modeler", "create", "collection", "elementVariable", "updateProperties"], "sources": ["src/components/Process/components/nodePanel/property/multiInstance.vue"], "sourcesContent": ["<template>\n  <div>\n    <el-dialog\n      title=\"多实例配置\"\n      :visible.sync=\"dialogVisible\"\n      width=\"500px\"\n      :close-on-click-modal=\"false\"\n      :close-on-press-escape=\"false\"\n      :show-close=\"false\"\n      class=\"muti-instance\"\n      @closed=\"$emit('close')\"\n    >\n      <el-alert\n        type=\"info\"\n        :closable=\"false\"\n        show-icon\n        style=\"margin-bottom: 20px\"\n      >\n        <template #title>\n          按照BPMN2.0规范的要求，用于为每个实例创建执行的父执行，会提供下列变量:<br>\n          nrOfInstances：实例总数。<br>\n          nrOfActiveInstances：当前活动的（即未完成的），实例数量。对于顺序多实例，这个值总为1。<br>\n          nrOfCompletedInstances：已完成的实例数量。<br>\n          loopCounter：给定实例在for-each循环中的index。<br>\n        </template>\n      </el-alert>\n      <x-form ref=\"xForm\" v-model=\"formData\" :config=\"formConfig\" />\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport mixinPanel from '../../../common/mixinPanel'\nimport { formatJsonKeyValue } from '../../../common/parseElement'\nexport default {\n  mixins: [mixinPanel],\n  data() {\n    return {\n      dialogVisible: true,\n      formData: {}\n    }\n  },\n  computed: {\n    formConfig() {\n      const _this = this\n      return {\n        inline: false,\n        item: [\n          {\n            xType: 'input',\n            name: 'collection',\n            label: '集合',\n            tooltip: '属性会作为表达式进行解析。如果表达式解析为字符串而不是一个集合，<br />不论是因为本身配置的就是静态字符串值，还是表达式计算结果为字符串，<br />这个字符串都会被当做变量名，并从流程变量中用于获取实际的集合。'\n          },\n          {\n            xType: 'input',\n            name: 'elementVariable',\n            label: '元素变量',\n            tooltip: '每创建一个用户任务前，先以该元素变量为label，集合中的一项为value，<br />创建（局部）流程变量，该局部流程变量被用于指派用户任务。<br />一般来说，该字符串应与指定人员变量相同。'\n          },\n          {\n            xType: 'radio',\n            name: 'isSequential',\n            label: '执行方式',\n            dic: [{ label: '串行', value: true }, { label: '并行', value: false }]\n          },\n          {\n            xType: 'input',\n            name: 'completionCondition',\n            label: '完成条件',\n            tooltip: '多实例活动在所有实例都完成时结束，然而也可以指定一个表达式，在每个实例<br />结束时进行计算。当表达式计算为true时，将销毁所有剩余的实例，并结束多实例<br />活动，继续执行流程。例如 ${nrOfCompletedInstances/nrOfInstances >= 0.6 }，<br />表示当任务完成60%时，该节点就算完成'\n          }\n        ],\n        operate: [\n          { text: '确定', show: true, click: _this.save },\n          { text: '清空', show: true, click: () => { _this.formData = {} } }\n        ]\n      }\n    }\n  },\n  mounted() {\n    const cache = JSON.parse(JSON.stringify(this.element.businessObject.loopCharacteristics ?? {}))\n    cache.completionCondition = cache.completionCondition?.body\n    this.formData = formatJsonKeyValue(cache)\n  },\n  methods: {\n    updateElement() {\n      if (this.formData.isSequential !== null && this.formData.isSequential !== undefined) {\n        let loopCharacteristics = this.element.businessObject.get('loopCharacteristics')\n        if (!loopCharacteristics) {\n          loopCharacteristics = this.modeler.get('moddle').create('bpmn:MultiInstanceLoopCharacteristics')\n        }\n        loopCharacteristics['isSequential'] = this.formData.isSequential\n        loopCharacteristics['collection'] = this.formData.collection\n        loopCharacteristics['elementVariable'] = this.formData.elementVariable\n        if (this.formData.completionCondition) {\n          const completionCondition = this.modeler.get('moddle').create('bpmn:Expression', { body: this.formData.completionCondition })\n          loopCharacteristics['completionCondition'] = completionCondition\n        }\n        this.updateProperties({ loopCharacteristics: loopCharacteristics })\n      } else {\n        delete this.element.businessObject.loopCharacteristics\n      }\n    },\n    save() {\n      this.updateElement()\n      this.dialogVisible = false\n    }\n  }\n}\n</script>\n\n<style>\n.muti-instance .el-form-item {\n  margin-bottom: 22px;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgCA,OAAAA,UAAA;AACA,SAAAC,kBAAA;AACA;EACAC,MAAA,GAAAF,UAAA;EACAG,IAAA,WAAAA,KAAA;IACA;MACAC,aAAA;MACAC,QAAA;IACA;EACA;EACAC,QAAA;IACAC,UAAA,WAAAA,WAAA;MACA,IAAAC,KAAA;MACA;QACAC,MAAA;QACAC,IAAA,GACA;UACAC,KAAA;UACAC,IAAA;UACAC,KAAA;UACAC,OAAA;QACA,GACA;UACAH,KAAA;UACAC,IAAA;UACAC,KAAA;UACAC,OAAA;QACA,GACA;UACAH,KAAA;UACAC,IAAA;UACAC,KAAA;UACAE,GAAA;YAAAF,KAAA;YAAAG,KAAA;UAAA;YAAAH,KAAA;YAAAG,KAAA;UAAA;QACA,GACA;UACAL,KAAA;UACAC,IAAA;UACAC,KAAA;UACAC,OAAA;QACA,EACA;QACAG,OAAA,GACA;UAAAC,IAAA;UAAAC,IAAA;UAAAC,KAAA,EAAAZ,KAAA,CAAAa;QAAA,GACA;UAAAH,IAAA;UAAAC,IAAA;UAAAC,KAAA,WAAAA,MAAA;YAAAZ,KAAA,CAAAH,QAAA;UAAA;QAAA;MAEA;IACA;EACA;EACAiB,OAAA,WAAAA,QAAA;IAAA,IAAAC,qBAAA,EAAAC,qBAAA;IACA,IAAAC,KAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,EAAAL,qBAAA,QAAAM,OAAA,CAAAC,cAAA,CAAAC,mBAAA,cAAAR,qBAAA,cAAAA,qBAAA;IACAE,KAAA,CAAAO,mBAAA,IAAAR,qBAAA,GAAAC,KAAA,CAAAO,mBAAA,cAAAR,qBAAA,uBAAAA,qBAAA,CAAAS,IAAA;IACA,KAAA5B,QAAA,GAAAJ,kBAAA,CAAAwB,KAAA;EACA;EACAS,OAAA;IACAC,aAAA,WAAAA,cAAA;MACA,SAAA9B,QAAA,CAAA+B,YAAA,kBAAA/B,QAAA,CAAA+B,YAAA,KAAAC,SAAA;QACA,IAAAN,mBAAA,QAAAF,OAAA,CAAAC,cAAA,CAAAQ,GAAA;QACA,KAAAP,mBAAA;UACAA,mBAAA,QAAAQ,OAAA,CAAAD,GAAA,WAAAE,MAAA;QACA;QACAT,mBAAA,wBAAA1B,QAAA,CAAA+B,YAAA;QACAL,mBAAA,sBAAA1B,QAAA,CAAAoC,UAAA;QACAV,mBAAA,2BAAA1B,QAAA,CAAAqC,eAAA;QACA,SAAArC,QAAA,CAAA2B,mBAAA;UACA,IAAAA,mBAAA,QAAAO,OAAA,CAAAD,GAAA,WAAAE,MAAA;YAAAP,IAAA,OAAA5B,QAAA,CAAA2B;UAAA;UACAD,mBAAA,0BAAAC,mBAAA;QACA;QACA,KAAAW,gBAAA;UAAAZ,mBAAA,EAAAA;QAAA;MACA;QACA,YAAAF,OAAA,CAAAC,cAAA,CAAAC,mBAAA;MACA;IACA;IACAV,IAAA,WAAAA,KAAA;MACA,KAAAc,aAAA;MACA,KAAA/B,aAAA;IACA;EACA;AACA", "ignoreList": []}]}