{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/components/parser/Parser.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/components/parser/Parser.vue", "mtime": 1650124704000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js", "mtime": 1751171659043}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}