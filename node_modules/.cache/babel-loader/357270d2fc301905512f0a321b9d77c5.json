{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/api/monitor/cache.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/api/monitor/cache.js", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QgZnJvbSAnQC91dGlscy9yZXF1ZXN0JzsKCi8vIOafpeivoue8k+WtmOivpue7hgpleHBvcnQgZnVuY3Rpb24gZ2V0Q2FjaGUoKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL21vbml0b3IvY2FjaGUnLAogICAgbWV0aG9kOiAnZ2V0JwogIH0pOwp9"}, {"version": 3, "names": ["request", "getCache", "url", "method"], "sources": ["/Users/<USER>/develop/code/project/haijia/adminweb/src/api/monitor/cache.js"], "sourcesContent": ["import request from '@/utils/request'\n\n// 查询缓存详细\nexport function getCache() {\n  return request({\n    url: '/monitor/cache',\n    method: 'get'\n  })\n}\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;;AAErC;AACA,OAAO,SAASC,QAAQA,CAAA,EAAG;EACzB,OAAOD,OAAO,CAAC;IACbE,GAAG,EAAE,gBAAgB;IACrBC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}