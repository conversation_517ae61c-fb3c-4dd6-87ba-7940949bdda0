{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/components/render/render.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/components/render/render.js", "mtime": 1641277326000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["deepClone", "componentChild", "slotsFiles", "require", "context", "keys", "for<PERSON>ach", "key", "tag", "replace", "value", "default", "vModel", "dataObject", "defaultValue", "_this", "props", "on", "input", "val", "$emit", "mountSlotFiles", "h", "confClone", "children", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "__config__", "Object", "childFunc", "__slot__", "push", "emitEvents", "_this2", "attr", "eventKeyList", "event", "buildDataObject", "_this3", "call", "undefined", "RegExp", "includes", "_typeof", "Array", "isArray", "concat", "_toConsumableArray", "_objectSpread", "attrs", "clearAttrs", "__methods__", "makeDataObject", "class", "domProps", "nativeOn", "style", "directives", "scopedSlots", "slot", "ref", "refInFor", "conf", "type", "required", "render", "$slots"], "sources": ["/Users/<USER>/develop/code/project/haijia/adminweb/src/components/render/render.js"], "sourcesContent": ["import { deepClone } from '@/utils/index'\n\nconst componentChild = {}\n/**\n * 将./slots中的文件挂载到对象componentChild上\n * 文件名为key，对应JSON配置中的__config__.tag\n * 文件内容为value，解析JSON配置中的__slot__\n */\nconst slotsFiles = require.context('./slots', false, /\\.js$/)\nconst keys = slotsFiles.keys() || []\nkeys.forEach(key => {\n  const tag = key.replace(/^\\.\\/(.*)\\.\\w+$/, '$1')\n  const value = slotsFiles(key).default\n  componentChild[tag] = value\n})\n\nfunction vModel(dataObject, defaultValue) {\n  dataObject.props.value = defaultValue\n\n  dataObject.on.input = val => {\n    this.$emit('input', val)\n  }\n}\n\nfunction mountSlotFiles(h, confClone, children) {\n  const childObjs = componentChild[confClone.__config__.tag]\n  if (childObjs) {\n    Object.keys(childObjs).forEach(key => {\n      const childFunc = childObjs[key]\n      if (confClone.__slot__ && confClone.__slot__[key]) {\n        children.push(childFunc(h, confClone, key))\n      }\n    })\n  }\n}\n\nfunction emitEvents(confClone) {\n  ['on', 'nativeOn'].forEach(attr => {\n    const eventKeyList = Object.keys(confClone[attr] || {})\n    eventKeyList.forEach(key => {\n      const val = confClone[attr][key]\n      if (typeof val === 'string') {\n        confClone[attr][key] = event => this.$emit(val, event)\n      }\n    })\n  })\n}\n\nfunction buildDataObject(confClone, dataObject) {\n  Object.keys(confClone).forEach(key => {\n    const val = confClone[key]\n    if (key === '__vModel__') {\n      vModel.call(this, dataObject, confClone.__config__.defaultValue)\n    } else if (dataObject[key] !== undefined) {\n      if (dataObject[key] === null\n        || dataObject[key] instanceof RegExp\n        || ['boolean', 'string', 'number', 'function'].includes(typeof dataObject[key])) {\n        dataObject[key] = val\n      } else if (Array.isArray(dataObject[key])) {\n        dataObject[key] = [...dataObject[key], ...val]\n      } else {\n        dataObject[key] = { ...dataObject[key], ...val }\n      }\n    } else {\n      dataObject.attrs[key] = val\n    }\n  })\n\n  // 清理属性\n  clearAttrs(dataObject)\n}\n\nfunction clearAttrs(dataObject) {\n  delete dataObject.attrs.__config__\n  delete dataObject.attrs.__slot__\n  delete dataObject.attrs.__methods__\n}\n\nfunction makeDataObject() {\n  // 深入数据对象：\n  // https://cn.vuejs.org/v2/guide/render-function.html#%E6%B7%B1%E5%85%A5%E6%95%B0%E6%8D%AE%E5%AF%B9%E8%B1%A1\n  return {\n    class: {},\n    attrs: {},\n    props: {},\n    domProps: {},\n    nativeOn: {},\n    on: {},\n    style: {},\n    directives: [],\n    scopedSlots: {},\n    slot: null,\n    key: null,\n    ref: null,\n    refInFor: true\n  }\n}\n\nexport default {\n  props: {\n    conf: {\n      type: Object,\n      required: true\n    }\n  },\n  render(h) {\n    const dataObject = makeDataObject()\n    const confClone = deepClone(this.conf)\n    const children = this.$slots.default || []\n\n    // 如果slots文件夹存在与当前tag同名的文件，则执行文件中的代码\n    mountSlotFiles.call(this, h, confClone, children)\n\n    // 将字符串类型的事件，发送为消息\n    emitEvents.call(this, confClone)\n\n    // 将json表单配置转化为vue render可以识别的 “数据对象（dataObject）”\n    buildDataObject.call(this, confClone, dataObject)\n\n    return h(this.conf.__config__.tag, dataObject, children)\n  }\n}\n"], "mappings": ";;;;;;;;;;;;AAAA,SAASA,SAAS,QAAQ,eAAe;AAEzC,IAAMC,cAAc,GAAG,CAAC,CAAC;AACzB;AACA;AACA;AACA;AACA;AACA,IAAMC,UAAU,GAAGC,OAAO,CAACC,OAAO,CAAC,SAAS,EAAE,KAAK,EAAE,OAAO,CAAC;AAC7D,IAAMC,IAAI,GAAGH,UAAU,CAACG,IAAI,CAAC,CAAC,IAAI,EAAE;AACpCA,IAAI,CAACC,OAAO,CAAC,UAAAC,GAAG,EAAI;EAClB,IAAMC,GAAG,GAAGD,GAAG,CAACE,OAAO,CAAC,iBAAiB,EAAE,IAAI,CAAC;EAChD,IAAMC,KAAK,GAAGR,UAAU,CAACK,GAAG,CAAC,CAACI,OAAO;EACrCV,cAAc,CAACO,GAAG,CAAC,GAAGE,KAAK;AAC7B,CAAC,CAAC;AAEF,SAASE,MAAMA,CAACC,UAAU,EAAEC,YAAY,EAAE;EAAA,IAAAC,KAAA;EACxCF,UAAU,CAACG,KAAK,CAACN,KAAK,GAAGI,YAAY;EAErCD,UAAU,CAACI,EAAE,CAACC,KAAK,GAAG,UAAAC,GAAG,EAAI;IAC3BJ,KAAI,CAACK,KAAK,CAAC,OAAO,EAAED,GAAG,CAAC;EAC1B,CAAC;AACH;AAEA,SAASE,cAAcA,CAACC,CAAC,EAAEC,SAAS,EAAEC,QAAQ,EAAE;EAC9C,IAAMC,SAAS,GAAGxB,cAAc,CAACsB,SAAS,CAACG,UAAU,CAAClB,GAAG,CAAC;EAC1D,IAAIiB,SAAS,EAAE;IACbE,MAAM,CAACtB,IAAI,CAACoB,SAAS,CAAC,CAACnB,OAAO,CAAC,UAAAC,GAAG,EAAI;MACpC,IAAMqB,SAAS,GAAGH,SAAS,CAAClB,GAAG,CAAC;MAChC,IAAIgB,SAAS,CAACM,QAAQ,IAAIN,SAAS,CAACM,QAAQ,CAACtB,GAAG,CAAC,EAAE;QACjDiB,QAAQ,CAACM,IAAI,CAACF,SAAS,CAACN,CAAC,EAAEC,SAAS,EAAEhB,GAAG,CAAC,CAAC;MAC7C;IACF,CAAC,CAAC;EACJ;AACF;AAEA,SAASwB,UAAUA,CAACR,SAAS,EAAE;EAAA,IAAAS,MAAA;EAC7B,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC1B,OAAO,CAAC,UAAA2B,IAAI,EAAI;IACjC,IAAMC,YAAY,GAAGP,MAAM,CAACtB,IAAI,CAACkB,SAAS,CAACU,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IACvDC,YAAY,CAAC5B,OAAO,CAAC,UAAAC,GAAG,EAAI;MAC1B,IAAMY,GAAG,GAAGI,SAAS,CAACU,IAAI,CAAC,CAAC1B,GAAG,CAAC;MAChC,IAAI,OAAOY,GAAG,KAAK,QAAQ,EAAE;QAC3BI,SAAS,CAACU,IAAI,CAAC,CAAC1B,GAAG,CAAC,GAAG,UAAA4B,KAAK;UAAA,OAAIH,MAAI,CAACZ,KAAK,CAACD,GAAG,EAAEgB,KAAK,CAAC;QAAA;MACxD;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AAEA,SAASC,eAAeA,CAACb,SAAS,EAAEV,UAAU,EAAE;EAAA,IAAAwB,MAAA;EAC9CV,MAAM,CAACtB,IAAI,CAACkB,SAAS,CAAC,CAACjB,OAAO,CAAC,UAAAC,GAAG,EAAI;IACpC,IAAMY,GAAG,GAAGI,SAAS,CAAChB,GAAG,CAAC;IAC1B,IAAIA,GAAG,KAAK,YAAY,EAAE;MACxBK,MAAM,CAAC0B,IAAI,CAACD,MAAI,EAAExB,UAAU,EAAEU,SAAS,CAACG,UAAU,CAACZ,YAAY,CAAC;IAClE,CAAC,MAAM,IAAID,UAAU,CAACN,GAAG,CAAC,KAAKgC,SAAS,EAAE;MACxC,IAAI1B,UAAU,CAACN,GAAG,CAAC,KAAK,IAAI,IACvBM,UAAU,CAACN,GAAG,CAAC,YAAYiC,MAAM,IACjC,CAAC,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU,CAAC,CAACC,QAAQ,CAAAC,OAAA,CAAQ7B,UAAU,CAACN,GAAG,CAAC,EAAC,EAAE;QACjFM,UAAU,CAACN,GAAG,CAAC,GAAGY,GAAG;MACvB,CAAC,MAAM,IAAIwB,KAAK,CAACC,OAAO,CAAC/B,UAAU,CAACN,GAAG,CAAC,CAAC,EAAE;QACzCM,UAAU,CAACN,GAAG,CAAC,MAAAsC,MAAA,CAAAC,kBAAA,CAAOjC,UAAU,CAACN,GAAG,CAAC,GAAAuC,kBAAA,CAAK3B,GAAG,EAAC;MAChD,CAAC,MAAM;QACLN,UAAU,CAACN,GAAG,CAAC,GAAAwC,aAAA,CAAAA,aAAA,KAAQlC,UAAU,CAACN,GAAG,CAAC,GAAKY,GAAG,CAAE;MAClD;IACF,CAAC,MAAM;MACLN,UAAU,CAACmC,KAAK,CAACzC,GAAG,CAAC,GAAGY,GAAG;IAC7B;EACF,CAAC,CAAC;;EAEF;EACA8B,UAAU,CAACpC,UAAU,CAAC;AACxB;AAEA,SAASoC,UAAUA,CAACpC,UAAU,EAAE;EAC9B,OAAOA,UAAU,CAACmC,KAAK,CAACtB,UAAU;EAClC,OAAOb,UAAU,CAACmC,KAAK,CAACnB,QAAQ;EAChC,OAAOhB,UAAU,CAACmC,KAAK,CAACE,WAAW;AACrC;AAEA,SAASC,cAAcA,CAAA,EAAG;EACxB;EACA;EACA,OAAO;IACLC,KAAK,EAAE,CAAC,CAAC;IACTJ,KAAK,EAAE,CAAC,CAAC;IACThC,KAAK,EAAE,CAAC,CAAC;IACTqC,QAAQ,EAAE,CAAC,CAAC;IACZC,QAAQ,EAAE,CAAC,CAAC;IACZrC,EAAE,EAAE,CAAC,CAAC;IACNsC,KAAK,EAAE,CAAC,CAAC;IACTC,UAAU,EAAE,EAAE;IACdC,WAAW,EAAE,CAAC,CAAC;IACfC,IAAI,EAAE,IAAI;IACVnD,GAAG,EAAE,IAAI;IACToD,GAAG,EAAE,IAAI;IACTC,QAAQ,EAAE;EACZ,CAAC;AACH;AAEA,eAAe;EACb5C,KAAK,EAAE;IACL6C,IAAI,EAAE;MACJC,IAAI,EAAEnC,MAAM;MACZoC,QAAQ,EAAE;IACZ;EACF,CAAC;EACDC,MAAM,WAAAA,OAAC1C,CAAC,EAAE;IACR,IAAMT,UAAU,GAAGsC,cAAc,CAAC,CAAC;IACnC,IAAM5B,SAAS,GAAGvB,SAAS,CAAC,IAAI,CAAC6D,IAAI,CAAC;IACtC,IAAMrC,QAAQ,GAAG,IAAI,CAACyC,MAAM,CAACtD,OAAO,IAAI,EAAE;;IAE1C;IACAU,cAAc,CAACiB,IAAI,CAAC,IAAI,EAAEhB,CAAC,EAAEC,SAAS,EAAEC,QAAQ,CAAC;;IAEjD;IACAO,UAAU,CAACO,IAAI,CAAC,IAAI,EAAEf,SAAS,CAAC;;IAEhC;IACAa,eAAe,CAACE,IAAI,CAAC,IAAI,EAAEf,SAAS,EAAEV,UAAU,CAAC;IAEjD,OAAOS,CAAC,CAAC,IAAI,CAACuC,IAAI,CAACnC,UAAU,CAAClB,GAAG,EAAEK,UAAU,EAAEW,QAAQ,CAAC;EAC1D;AACF,CAAC", "ignoreList": []}]}