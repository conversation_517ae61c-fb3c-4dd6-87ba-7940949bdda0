{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/utils/scroll-to.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/utils/scroll-to.js", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js", "mtime": 1751171659043}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}