{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/api/system/dict/data.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/api/system/dict/data.js", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QgZnJvbSAnQC91dGlscy9yZXF1ZXN0JzsKCi8vIOafpeivouWtl+WFuOaVsOaNruWIl+ihqApleHBvcnQgZnVuY3Rpb24gbGlzdERhdGEocXVlcnkpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvc3lzdGVtL2RpY3QvZGF0YS9saXN0JywKICAgIG1ldGhvZDogJ2dldCcsCiAgICBwYXJhbXM6IHF1ZXJ5CiAgfSk7Cn0KCi8vIOafpeivouWtl+WFuOaVsOaNruivpue7hgpleHBvcnQgZnVuY3Rpb24gZ2V0RGF0YShkaWN0Q29kZSkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9zeXN0ZW0vZGljdC9kYXRhLycgKyBkaWN0Q29kZSwKICAgIG1ldGhvZDogJ2dldCcKICB9KTsKfQoKLy8g5qC55o2u5a2X5YW457G75Z6L5p+l6K+i5a2X5YW45pWw5o2u5L+h5oGvCmV4cG9ydCBmdW5jdGlvbiBnZXREaWN0cyhkaWN0VHlwZSkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9zeXN0ZW0vZGljdC9kYXRhL3R5cGUvJyArIGRpY3RUeXBlLAogICAgbWV0aG9kOiAnZ2V0JwogIH0pOwp9CgovLyDmlrDlop7lrZflhbjmlbDmja4KZXhwb3J0IGZ1bmN0aW9uIGFkZERhdGEoZGF0YSkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9zeXN0ZW0vZGljdC9kYXRhJywKICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgZGF0YTogZGF0YQogIH0pOwp9CgovLyDkv67mlLnlrZflhbjmlbDmja4KZXhwb3J0IGZ1bmN0aW9uIHVwZGF0ZURhdGEoZGF0YSkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9zeXN0ZW0vZGljdC9kYXRhJywKICAgIG1ldGhvZDogJ3B1dCcsCiAgICBkYXRhOiBkYXRhCiAgfSk7Cn0KCi8vIOWIoOmZpOWtl+WFuOaVsOaNrgpleHBvcnQgZnVuY3Rpb24gZGVsRGF0YShkaWN0Q29kZSkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9zeXN0ZW0vZGljdC9kYXRhLycgKyBkaWN0Q29kZSwKICAgIG1ldGhvZDogJ2RlbGV0ZScKICB9KTsKfQoKLy8g5a+85Ye65a2X5YW45pWw5o2uCmV4cG9ydCBmdW5jdGlvbiBleHBvcnREYXRhKHF1ZXJ5KSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL3N5c3RlbS9kaWN0L2RhdGEvZXhwb3J0JywKICAgIG1ldGhvZDogJ2dldCcsCiAgICBwYXJhbXM6IHF1ZXJ5CiAgfSk7Cn0="}, {"version": 3, "names": ["request", "listData", "query", "url", "method", "params", "getData", "dictCode", "getDicts", "dictType", "addData", "data", "updateData", "delData", "exportData"], "sources": ["/Users/<USER>/develop/code/project/haijia/adminweb/src/api/system/dict/data.js"], "sourcesContent": ["import request from '@/utils/request'\n\n// 查询字典数据列表\nexport function listData(query) {\n  return request({\n    url: '/system/dict/data/list',\n    method: 'get',\n    params: query\n  })\n}\n\n// 查询字典数据详细\nexport function getData(dictCode) {\n  return request({\n    url: '/system/dict/data/' + dictCode,\n    method: 'get'\n  })\n}\n\n// 根据字典类型查询字典数据信息\nexport function getDicts(dictType) {\n  return request({\n    url: '/system/dict/data/type/' + dictType,\n    method: 'get'\n  })\n}\n\n// 新增字典数据\nexport function addData(data) {\n  return request({\n    url: '/system/dict/data',\n    method: 'post',\n    data: data\n  })\n}\n\n// 修改字典数据\nexport function updateData(data) {\n  return request({\n    url: '/system/dict/data',\n    method: 'put',\n    data: data\n  })\n}\n\n// 删除字典数据\nexport function delData(dictCode) {\n  return request({\n    url: '/system/dict/data/' + dictCode,\n    method: 'delete'\n  })\n}\n\n// 导出字典数据\nexport function exportData(query) {\n  return request({\n    url: '/system/dict/data/export',\n    method: 'get',\n    params: query\n  })\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;;AAErC;AACA,OAAO,SAASC,QAAQA,CAACC,KAAK,EAAE;EAC9B,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,wBAAwB;IAC7BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEH;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASI,OAAOA,CAACC,QAAQ,EAAE;EAChC,OAAOP,OAAO,CAAC;IACbG,GAAG,EAAE,oBAAoB,GAAGI,QAAQ;IACpCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASI,QAAQA,CAACC,QAAQ,EAAE;EACjC,OAAOT,OAAO,CAAC;IACbG,GAAG,EAAE,yBAAyB,GAAGM,QAAQ;IACzCL,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASM,OAAOA,CAACC,IAAI,EAAE;EAC5B,OAAOX,OAAO,CAAC;IACbG,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,MAAM;IACdO,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASC,UAAUA,CAACD,IAAI,EAAE;EAC/B,OAAOX,OAAO,CAAC;IACbG,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,KAAK;IACbO,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASE,OAAOA,CAACN,QAAQ,EAAE;EAChC,OAAOP,OAAO,CAAC;IACbG,GAAG,EAAE,oBAAoB,GAAGI,QAAQ;IACpCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASU,UAAUA,CAACZ,KAAK,EAAE;EAChC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,0BAA0B;IAC/BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEH;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}