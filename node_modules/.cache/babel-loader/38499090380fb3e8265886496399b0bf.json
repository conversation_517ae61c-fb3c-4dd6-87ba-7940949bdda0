{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Process/components/nodePanel/sequenceFlow.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Process/components/nodePanel/sequenceFlow.vue", "mtime": 1641277326000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["mixinPanel", "mixinExecutionListener", "commonParse", "conditionExpressionParse", "mixins", "data", "formData", "computed", "formConfig", "inline", "item", "xType", "name", "label", "rules", "required", "message", "watch", "formDataConditionExpression", "val", "newCondition", "modeler", "get", "create", "body", "updateProperties", "conditionExpression", "formDataSkipExpression", "created", "cache", "element"], "sources": ["src/components/Process/components/nodePanel/sequenceFlow.vue"], "sourcesContent": ["<template>\n  <div>\n    <x-form ref=\"xForm\" v-model=\"formData\" :config=\"formConfig\">\n      <template #executionListener>\n        <el-badge :value=\"executionListenerLength\">\n          <el-button size=\"small\" @click=\"dialogName = 'executionListenerDialog'\">编辑</el-button>\n        </el-badge>\n      </template>\n    </x-form>\n    <executionListenerDialog\n      v-if=\"dialogName === 'executionListenerDialog'\"\n      :element=\"element\"\n      :modeler=\"modeler\"\n      @close=\"finishExecutionListener\"\n    />\n  </div>\n</template>\n\n<script>\nimport mixinPanel from '../../common/mixinPanel'\nimport mixinExecutionListener from '../../common/mixinExecutionListener'\nimport { commonParse, conditionExpressionParse } from '../../common/parseElement'\nexport default {\n  mixins: [mixinPanel, mixinExecutionListener],\n  data() {\n    return {\n      formData: {}\n    }\n  },\n  computed: {\n    formConfig() {\n      return {\n        inline: false,\n        item: [\n          {\n            xType: 'input',\n            name: 'id',\n            label: '节点 id',\n            rules: [{ required: true, message: 'Id 不能为空' }]\n          },\n          {\n            xType: 'input',\n            name: 'name',\n            label: '节点名称'\n          },\n          {\n            xType: 'input',\n            name: 'documentation',\n            label: '节点描述'\n          },\n          {\n            xType: 'slot',\n            name: 'executionListener',\n            label: '执行监听器'\n          },\n          {\n            xType: 'input',\n            name: 'conditionExpression',\n            label: '跳转条件'\n          },\n          {\n            xType: 'input',\n            name: 'skipExpression',\n            label: '跳过表达式'\n          }\n        ]\n      }\n    }\n  },\n  watch: {\n    'formData.conditionExpression': function(val) {\n      if (val) {\n        const newCondition = this.modeler.get('moddle').create('bpmn:FormalExpression', { body: val })\n        this.updateProperties({ conditionExpression: newCondition })\n      } else {\n        this.updateProperties({ conditionExpression: null })\n      }\n    },\n    'formData.skipExpression': function(val) {\n      if (val === '') val = null\n      this.updateProperties({ 'flowable:skipExpression': val })\n    }\n  },\n  created() {\n    let cache = commonParse(this.element)\n    cache = conditionExpressionParse(cache)\n    this.formData = cache\n  }\n}\n</script>\n\n<style></style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAmBA,OAAAA,UAAA;AACA,OAAAC,sBAAA;AACA,SAAAC,WAAA,EAAAC,wBAAA;AACA;EACAC,MAAA,GAAAJ,UAAA,EAAAC,sBAAA;EACAI,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;IACA;EACA;EACAC,QAAA;IACAC,UAAA,WAAAA,WAAA;MACA;QACAC,MAAA;QACAC,IAAA,GACA;UACAC,KAAA;UACAC,IAAA;UACAC,KAAA;UACAC,KAAA;YAAAC,QAAA;YAAAC,OAAA;UAAA;QACA,GACA;UACAL,KAAA;UACAC,IAAA;UACAC,KAAA;QACA,GACA;UACAF,KAAA;UACAC,IAAA;UACAC,KAAA;QACA,GACA;UACAF,KAAA;UACAC,IAAA;UACAC,KAAA;QACA,GACA;UACAF,KAAA;UACAC,IAAA;UACAC,KAAA;QACA,GACA;UACAF,KAAA;UACAC,IAAA;UACAC,KAAA;QACA;MAEA;IACA;EACA;EACAI,KAAA;IACA,yCAAAC,4BAAAC,GAAA;MACA,IAAAA,GAAA;QACA,IAAAC,YAAA,QAAAC,OAAA,CAAAC,GAAA,WAAAC,MAAA;UAAAC,IAAA,EAAAL;QAAA;QACA,KAAAM,gBAAA;UAAAC,mBAAA,EAAAN;QAAA;MACA;QACA,KAAAK,gBAAA;UAAAC,mBAAA;QAAA;MACA;IACA;IACA,oCAAAC,uBAAAR,GAAA;MACA,IAAAA,GAAA,SAAAA,GAAA;MACA,KAAAM,gBAAA;QAAA,2BAAAN;MAAA;IACA;EACA;EACAS,OAAA,WAAAA,QAAA;IACA,IAAAC,KAAA,GAAA3B,WAAA,MAAA4B,OAAA;IACAD,KAAA,GAAA1B,wBAAA,CAAA0B,KAAA;IACA,KAAAvB,QAAA,GAAAuB,KAAA;EACA;AACA", "ignoreList": []}]}