{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/utils/generator/js.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/utils/generator/js.js", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js", "mtime": 1751171659043}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}