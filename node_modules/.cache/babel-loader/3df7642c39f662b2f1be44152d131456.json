{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/components/icons/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/components/icons/index.vue", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBzdmdJY29ucyBmcm9tICcuL3N2Zy1pY29ucyc7CmltcG9ydCBlbGVtZW50SWNvbnMgZnJvbSAnLi9lbGVtZW50LWljb25zJzsKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdJY29ucycsCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIHN2Z0ljb25zOiBzdmdJY29ucywKICAgICAgZWxlbWVudEljb25zOiBlbGVtZW50SWNvbnMKICAgIH07CiAgfSwKICBtZXRob2RzOiB7CiAgICBnZW5lcmF0ZUljb25Db2RlOiBmdW5jdGlvbiBnZW5lcmF0ZUljb25Db2RlKHN5bWJvbCkgewogICAgICByZXR1cm4gIjxzdmctaWNvbiBpY29uLWNsYXNzPVwiIi5jb25jYXQoc3ltYm9sLCAiXCIgLz4iKTsKICAgIH0sCiAgICBnZW5lcmF0ZUVsZW1lbnRJY29uQ29kZTogZnVuY3Rpb24gZ2VuZXJhdGVFbGVtZW50SWNvbkNvZGUoc3ltYm9sKSB7CiAgICAgIHJldHVybiAiPGkgY2xhc3M9XCJlbC1pY29uLSIuY29uY2F0KHN5bWJvbCwgIlwiIC8+Iik7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["svgIcons", "elementIcons", "name", "data", "methods", "generateIconCode", "symbol", "concat", "generateElementIconCode"], "sources": ["src/views/components/icons/index.vue"], "sourcesContent": ["<template>\n  <div class=\"icons-container\">\n    <aside>\n      <a href=\"#\" target=\"_blank\">Add and use\n      </a>\n    </aside>\n    <el-tabs type=\"border-card\">\n      <el-tab-pane label=\"Icons\">\n        <div v-for=\"item of svgIcons\" :key=\"item\">\n          <el-tooltip placement=\"top\">\n            <div slot=\"content\">\n              {{ generateIconCode(item) }}\n            </div>\n            <div class=\"icon-item\">\n              <svg-icon :icon-class=\"item\" class-name=\"disabled\" />\n              <span>{{ item }}</span>\n            </div>\n          </el-tooltip>\n        </div>\n      </el-tab-pane>\n      <el-tab-pane label=\"Element-UI Icons\">\n        <div v-for=\"item of elementIcons\" :key=\"item\">\n          <el-tooltip placement=\"top\">\n            <div slot=\"content\">\n              {{ generateElementIconCode(item) }}\n            </div>\n            <div class=\"icon-item\">\n              <i :class=\"'el-icon-' + item\" />\n              <span>{{ item }}</span>\n            </div>\n          </el-tooltip>\n        </div>\n      </el-tab-pane>\n    </el-tabs>\n  </div>\n</template>\n\n<script>\nimport svgIcons from './svg-icons'\nimport elementIcons from './element-icons'\n\nexport default {\n  name: 'Icons',\n  data() {\n    return {\n      svgIcons,\n      elementIcons\n    }\n  },\n  methods: {\n    generateIconCode(symbol) {\n      return `<svg-icon icon-class=\"${symbol}\" />`\n    },\n    generateElementIconCode(symbol) {\n      return `<i class=\"el-icon-${symbol}\" />`\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.icons-container {\n  margin: 10px 20px 0;\n  overflow: hidden;\n\n  .icon-item {\n    margin: 20px;\n    height: 85px;\n    text-align: center;\n    width: 100px;\n    float: left;\n    font-size: 30px;\n    color: #24292e;\n    cursor: pointer;\n  }\n\n  span {\n    display: block;\n    font-size: 16px;\n    margin-top: 10px;\n  }\n\n  .disabled {\n    pointer-events: none;\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsCA,OAAAA,QAAA;AACA,OAAAC,YAAA;AAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAH,QAAA,EAAAA,QAAA;MACAC,YAAA,EAAAA;IACA;EACA;EACAG,OAAA;IACAC,gBAAA,WAAAA,iBAAAC,MAAA;MACA,iCAAAC,MAAA,CAAAD,MAAA;IACA;IACAE,uBAAA,WAAAA,wBAAAF,MAAA;MACA,6BAAAC,MAAA,CAAAD,MAAA;IACA;EACA;AACA", "ignoreList": []}]}