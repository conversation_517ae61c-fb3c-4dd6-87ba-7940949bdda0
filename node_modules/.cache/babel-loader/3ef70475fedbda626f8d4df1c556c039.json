{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/system/user/form.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/system/user/form.vue", "mtime": 1668865468000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js", "mtime": 1751171659043}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldFVzZXIsIGFkZFVzZXIsIHVwZGF0ZVVzZXIgfSBmcm9tICJAL2FwaS9zeXN0ZW0vdXNlciI7CmltcG9ydCBUcmVlc2VsZWN0IGZyb20gIkByaW9waGFlL3Z1ZS10cmVlc2VsZWN0IjsKaW1wb3J0ICJAcmlvcGhhZS92dWUtdHJlZXNlbGVjdC9kaXN0L3Z1ZS10cmVlc2VsZWN0LmNzcyI7CmltcG9ydCBJbWFnZVVwbG9hZCBmcm9tICJAL2NvbXBvbmVudHMvSW1hZ2VVcGxvYWQiOwppbXBvcnQgZmxvd2FibGUgZnJvbSAnQC92aWV3cy9mbG93YWJsZS90YXNrL3JlY29yZC9pbmRleCc7CmltcG9ydCB7IHJlZ2lvbkRhdGEsIENvZGVUb1RleHQsIFRleHRUb0NvZGUgfSBmcm9tICJlbGVtZW50LWNoaW5hLWFyZWEtZGF0YSI7CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiVXNlciIsCiAgY29tcG9uZW50czogewogICAgVHJlZXNlbGVjdDogVHJlZXNlbGVjdCwKICAgIEltYWdlVXBsb2FkOiBJbWFnZVVwbG9hZCwKICAgIGZsb3dhYmxlOiBmbG93YWJsZQogIH0sCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIC8vIOmBrue9qeWxggogICAgICBsb2FkaW5nOiB0cnVlLAogICAgICAvLyDpgInkuK3mlbDnu4QKICAgICAgaWRzOiBbXSwKICAgICAgLy8g6Z2e5Y2V5Liq56aB55SoCiAgICAgIHNpbmdsZTogdHJ1ZSwKICAgICAgLy8g6Z2e5aSa5Liq56aB55SoCiAgICAgIG11bHRpcGxlOiB0cnVlLAogICAgICAvLyDmmL7npLrmkJzntKLmnaHku7YKICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwKICAgICAgLy8g5oC75p2h5pWwCiAgICAgIHRvdGFsOiAwLAogICAgICAvLyDnlKjmiLfooajmoLzmlbDmja4KICAgICAgdXNlckxpc3Q6IG51bGwsCiAgICAgIC8vIOW8ueWHuuWxguagh+mimAogICAgICB0aXRsZTogIiIsCiAgICAgIC8vIOmDqOmXqOagkemAiemhuQogICAgICBkZXB0T3B0aW9uczogdW5kZWZpbmVkLAogICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYIKICAgICAgb3BlbjogZmFsc2UsCiAgICAgIC8vIOmDqOmXqOWQjeensAogICAgICBkZXB0TmFtZTogdW5kZWZpbmVkLAogICAgICAvLyDpu5jorqTlr4bnoIEKICAgICAgaW5pdFBhc3N3b3JkOiB1bmRlZmluZWQsCiAgICAgIC8vIOaXpeacn+iMg+WbtAogICAgICBkYXRlUmFuZ2U6IFtdLAogICAgICAvLyDnirbmgIHmlbDmja7lrZflhbgKICAgICAgc3RhdHVzT3B0aW9uczogW10sCiAgICAgIC8vIOaAp+WIq+eKtuaAgeWtl+WFuAogICAgICBzZXhPcHRpb25zOiBbXSwKICAgICAgLy8g55+t5L+h6YCa55+l5a2X5YW4CiAgICAgIHNtc1NlbmRPcHRpb25zOiBbXSwKICAgICAgLy8g5a6h5qC454q25oCB5a2X5YW4CiAgICAgIGF1ZGl0U3RhdHVzT3B0aW9uczogW10sCiAgICAgIC8vIOinkuiJsumAiemhuQogICAgICByb2xlT3B0aW9uczogW10sCiAgICAgIC8vIOihqOWNleWPguaVsAogICAgICBmb3JtOiB7fSwKICAgICAgZGVmYXVsdFByb3BzOiB7CiAgICAgICAgY2hpbGRyZW46ICJjaGlsZHJlbiIsCiAgICAgICAgbGFiZWw6ICJsYWJlbCIKICAgICAgfSwKICAgICAgLy8g6KGo5Y2V5qCh6aqMCiAgICAgIHJ1bGVzOiB7CiAgICAgICAgdXNlck5hbWU6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICLnlKjmiLflkI3kuI3og73kuLrnqboiLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgfV0sCiAgICAgICAgY29tcGFueTogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogIuWFrOWPuOWFqOensOS4jeiDveS4uuepuiIsCiAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIKICAgICAgICB9XSwKICAgICAgICBidXNpbmVzc05vOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAi6JCl5Lia5omn54Wn5Y+356CB5LiN6IO95Li656m6IiwKICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgIH1dLAogICAgICAgIGJ1c2luZXNzTm9QaWM6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICLokKXkuJrmiafnhaflm77niYflv4XkvKAiCiAgICAgICAgfV0sCiAgICAgICAgZW1haWw6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIHR5cGU6ICJlbWFpbCIsCiAgICAgICAgICBtZXNzYWdlOiAiJ+ivt+i+k+WF<PERSON>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"}, null]}