{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/components/icons/element-icons.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/components/icons/element-icons.js", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["elementIcons"], "sources": ["/Users/<USER>/develop/code/project/haijia/adminweb/src/views/components/icons/element-icons.js"], "sourcesContent": ["const elementIcons = ['platform-eleme', 'eleme', 'delete-solid', 'delete', 's-tools', 'setting', 'user-solid', 'user', 'phone', 'phone-outline', 'more', 'more-outline', 'star-on', 'star-off', 's-goods', 'goods', 'warning', 'warning-outline', 'question', 'info', 'remove', 'circle-plus', 'success', 'error', 'zoom-in', 'zoom-out', 'remove-outline', 'circle-plus-outline', 'circle-check', 'circle-close', 's-help', 'help', 'minus', 'plus', 'check', 'close', 'picture', 'picture-outline', 'picture-outline-round', 'upload', 'upload2', 'download', 'camera-solid', 'camera', 'video-camera-solid', 'video-camera', 'message-solid', 'bell', 's-cooperation', 's-order', 's-platform', 's-fold', 's-unfold', 's-operation', 's-promotion', 's-home', 's-release', 's-ticket', 's-management', 's-open', 's-shop', 's-marketing', 's-flag', 's-comment', 's-finance', 's-claim', 's-custom', 's-opportunity', 's-data', 's-check', 's-grid', 'menu', 'share', 'd-caret', 'caret-left', 'caret-right', 'caret-bottom', 'caret-top', 'bottom-left', 'bottom-right', 'back', 'right', 'bottom', 'top', 'top-left', 'top-right', 'arrow-left', 'arrow-right', 'arrow-down', 'arrow-up', 'd-arrow-left', 'd-arrow-right', 'video-pause', 'video-play', 'refresh', 'refresh-right', 'refresh-left', 'finished', 'sort', 'sort-up', 'sort-down', 'rank', 'loading', 'view', 'c-scale-to-original', 'date', 'edit', 'edit-outline', 'folder', 'folder-opened', 'folder-add', 'folder-remove', 'folder-delete', 'folder-checked', 'tickets', 'document-remove', 'document-delete', 'document-copy', 'document-checked', 'document', 'document-add', 'printer', 'paperclip', 'takeaway-box', 'search', 'monitor', 'attract', 'mobile', 'scissors', 'umbrella', 'headset', 'brush', 'mouse', 'coordinate', 'magic-stick', 'reading', 'data-line', 'data-board', 'pie-chart', 'data-analysis', 'collection-tag', 'film', 'suitcase', 'suitcase-1', 'receiving', 'collection', 'files', 'notebook-1', 'notebook-2', 'toilet-paper', 'office-building', 'school', 'table-lamp', 'house', 'no-smoking', 'smoking', 'shopping-cart-full', 'shopping-cart-1', 'shopping-cart-2', 'shopping-bag-1', 'shopping-bag-2', 'sold-out', 'sell', 'present', 'box', 'bank-card', 'money', 'coin', 'wallet', 'discount', 'price-tag', 'news', 'guide', 'male', 'female', 'thumb', 'cpu', 'link', 'connection', 'open', 'turn-off', 'set-up', 'chat-round', 'chat-line-round', 'chat-square', 'chat-dot-round', 'chat-dot-square', 'chat-line-square', 'message', 'postcard', 'position', 'turn-off-microphone', 'microphone', 'close-notification', 'bangzhu', 'time', 'odometer', 'crop', 'aim', 'switch-button', 'full-screen', 'copy-document', 'mic', 'stopwatch', 'medal-1', 'medal', 'trophy', 'trophy-1', 'first-aid-kit', 'discover', 'place', 'location', 'location-outline', 'location-information', 'add-location', 'delete-location', 'map-location', 'alarm-clock', 'timer', 'watch-1', 'watch', 'lock', 'unlock', 'key', 'service', 'mobile-phone', 'bicycle', 'truck', 'ship', 'basketball', 'football', 'soccer', 'baseball', 'wind-power', 'light-rain', 'lightning', 'heavy-rain', 'sunrise', 'sunrise-1', 'sunset', 'sunny', 'cloudy', 'partly-cloudy', 'cloudy-and-sunny', 'moon', 'moon-night', 'dish', 'dish-1', 'food', 'chicken', 'fork-spoon', 'knife-fork', 'burger', 'tableware', 'sugar', 'dessert', 'ice-cream', 'hot-water', 'water-cup', 'coffee-cup', 'cold-drink', 'goblet', 'goblet-full', 'goblet-square', 'goblet-square-full', 'refrigerator', 'grape', 'watermelon', 'cherry', 'apple', 'pear', 'orange', 'coffee', 'ice-tea', 'ice-drink', 'milk-tea', 'potato-strips', 'lollipop', 'ice-cream-square', 'ice-cream-round']\n\nexport default elementIcons\n"], "mappings": "AAAA,IAAMA,YAAY,GAAG,CAAC,gBAAgB,EAAE,OAAO,EAAE,cAAc,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,YAAY,EAAE,MAAM,EAAE,OAAO,EAAE,eAAe,EAAE,MAAM,EAAE,cAAc,EAAE,SAAS,EAAE,UAAU,EAAE,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,iBAAiB,EAAE,UAAU,EAAE,MAAM,EAAE,QAAQ,EAAE,aAAa,EAAE,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,gBAAgB,EAAE,qBAAqB,EAAE,cAAc,EAAE,cAAc,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,iBAAiB,EAAE,uBAAuB,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAE,cAAc,EAAE,QAAQ,EAAE,oBAAoB,EAAE,cAAc,EAAE,eAAe,EAAE,MAAM,EAAE,eAAe,EAAE,SAAS,EAAE,YAAY,EAAE,QAAQ,EAAE,UAAU,EAAE,aAAa,EAAE,aAAa,EAAE,QAAQ,EAAE,WAAW,EAAE,UAAU,EAAE,cAAc,EAAE,QAAQ,EAAE,QAAQ,EAAE,aAAa,EAAE,QAAQ,EAAE,WAAW,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,eAAe,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,YAAY,EAAE,aAAa,EAAE,cAAc,EAAE,WAAW,EAAE,aAAa,EAAE,cAAc,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAE,WAAW,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,UAAU,EAAE,cAAc,EAAE,eAAe,EAAE,aAAa,EAAE,YAAY,EAAE,SAAS,EAAE,eAAe,EAAE,cAAc,EAAE,UAAU,EAAE,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,qBAAqB,EAAE,MAAM,EAAE,MAAM,EAAE,cAAc,EAAE,QAAQ,EAAE,eAAe,EAAE,YAAY,EAAE,eAAe,EAAE,eAAe,EAAE,gBAAgB,EAAE,SAAS,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,eAAe,EAAE,kBAAkB,EAAE,UAAU,EAAE,cAAc,EAAE,SAAS,EAAE,WAAW,EAAE,cAAc,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,YAAY,EAAE,aAAa,EAAE,SAAS,EAAE,WAAW,EAAE,YAAY,EAAE,WAAW,EAAE,eAAe,EAAE,gBAAgB,EAAE,MAAM,EAAE,UAAU,EAAE,YAAY,EAAE,WAAW,EAAE,YAAY,EAAE,OAAO,EAAE,YAAY,EAAE,YAAY,EAAE,cAAc,EAAE,iBAAiB,EAAE,QAAQ,EAAE,YAAY,EAAE,OAAO,EAAE,YAAY,EAAE,SAAS,EAAE,oBAAoB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,UAAU,EAAE,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,WAAW,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,YAAY,EAAE,iBAAiB,EAAE,aAAa,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,qBAAqB,EAAE,YAAY,EAAE,oBAAoB,EAAE,SAAS,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,KAAK,EAAE,eAAe,EAAE,aAAa,EAAE,eAAe,EAAE,KAAK,EAAE,WAAW,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,eAAe,EAAE,UAAU,EAAE,OAAO,EAAE,UAAU,EAAE,kBAAkB,EAAE,sBAAsB,EAAE,cAAc,EAAE,iBAAiB,EAAE,cAAc,EAAE,aAAa,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAE,cAAc,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE,YAAY,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,EAAE,YAAY,EAAE,YAAY,EAAE,WAAW,EAAE,YAAY,EAAE,SAAS,EAAE,WAAW,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,eAAe,EAAE,kBAAkB,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,YAAY,EAAE,QAAQ,EAAE,WAAW,EAAE,OAAO,EAAE,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,EAAE,YAAY,EAAE,QAAQ,EAAE,aAAa,EAAE,eAAe,EAAE,oBAAoB,EAAE,cAAc,EAAE,OAAO,EAAE,YAAY,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,eAAe,EAAE,UAAU,EAAE,kBAAkB,EAAE,iBAAiB,CAAC;AAEjhH,eAAeA,YAAY", "ignoreList": []}]}