{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/system/user/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/system/user/index.vue", "mtime": 1665234686000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js", "mtime": 1751171659043}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGxpc3RVc2VyLCBnZXRVc2VyLCBkZWxVc2VyLCBhZGRVc2VyLCB1cGRhdGVVc2VyLCBleHBvcnRVc2VyLCByZXNldFVzZXJQd2QsIGNoYW5nZVVzZXJTdGF0dXMsIGltcG9ydFRlbXBsYXRlIGFzIF9pbXBvcnRUZW1wbGF0ZSwgcHJpbnRVc2VyIH0gZnJvbSAiQC9hcGkvc3lzdGVtL3VzZXIiOwppbXBvcnQgeyBnZXRUb2tlbiB9IGZyb20gIkAvdXRpbHMvYXV0aCI7CmltcG9ydCB7IHRyZWVzZWxlY3QgfSBmcm9tICJAL2FwaS9zeXN0ZW0vZGVwdCI7CmltcG9ydCBUcmVlc2VsZWN0IGZyb20gIkByaW9waGFlL3Z1ZS10cmVlc2VsZWN0IjsKaW1wb3J0ICJAcmlvcGhhZS92dWUtdHJlZXNlbGVjdC9kaXN0L3Z1ZS10cmVlc2VsZWN0LmNzcyI7CmltcG9ydCBJbWFnZVVwbG9hZCBmcm9tICJAL2NvbXBvbmVudHMvSW1hZ2VVcGxvYWQiOwppbXBvcnQgeyBwcm92aW5jZUFuZENpdHlEYXRhLCByZWdpb25EYXRhLCBDb2RlVG9UZXh0LCBUZXh0VG9Db2RlIH0gZnJvbSAiZWxlbWVudC1jaGluYS1hcmVhLWRhdGEiOwppbXBvcnQgZmxvd2FibGUgZnJvbSAnQC92aWV3cy9mbG93YWJsZS90YXNrL3JlY29yZC92aWV3JzsKaW1wb3J0IHsgZ2V0SW5zSWRCeUJpektleSB9IGZyb20gIkAvYXBpL2Zsb3dhYmxlL3RvZG8iOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogIlVzZXIiLAogIGNvbXBvbmVudHM6IHsKICAgIFRyZWVzZWxlY3Q6IFRyZWVzZWxlY3QsCiAgICBJbWFnZVVwbG9hZDogSW1hZ2VVcGxvYWQsCiAgICBmbG93YWJsZTogZmxvd2FibGUKICB9LAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBpc01vYmlsZTogZmFsc2UsCiAgICAgIHBhZ2VMYXlvdXQ6ICJ0b3RhbCwgc2l6ZXMsIHByZXYsIHBhZ2VyLCBuZXh0LCBqdW1wZXIiLAogICAgICAvLyDpga7nvanlsYIKICAgICAgbG9hZGluZzogdHJ1ZSwKICAgICAgLy8g6YCJ5Lit5pWw57uECiAgICAgIGlkczogW10sCiAgICAgIC8vIOmdnuWNleS4quemgeeUqAogICAgICBzaW5nbGU6IHRydWUsCiAgICAgIC8vIOmdnuWkmuS4quemgeeUqAogICAgICBtdWx0aXBsZTogdHJ1ZSwKICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2CiAgICAgIHNob3dTZWFyY2g6IHRydWUsCiAgICAgIHNob3dFeHBvcnQ6IGZhbHNlLAogICAgICBzaG93UHJpbnQ6IGZhbHNlLAogICAgICAvLyDmgLvmnaHmlbAKICAgICAgdG90YWw6IDAsCiAgICAgIC8vIOeUqOaIt+ihqOagvOaVsOaNrgogICAgICB1c2VyTGlzdDogbnVsbCwKICAgICAgLy8g5by55Ye65bGC5qCH6aKYCiAgICAgIHRpdGxlOiAiIiwKICAgICAgLy8g6YOo6Zeo5qCR6YCJ6aG5CiAgICAgIGRlcHRPcHRpb25zOiB1bmRlZmluZWQsCiAgICAgIC8vIOaYr+WQpuaYvuekuuW8ueWHuuWxggogICAgICBvcGVuOiBmYWxzZSwKICAgICAgLy8g6YOo6Zeo5ZCN56ewCiAgICAgIGRlcHROYW1lOiB1bmRlZmluZWQsCiAgICAgIC8vIOm7mOiupOWvhueggQogICAgICBpbml0UGFzc3dvcmQ6IHVuZGVmaW5lZCwKICAgICAgLy8g5pel5pyf6IyD5Zu0CiAgICAgIGRhdGVSYW5nZTogW10sCiAgICAgIC8vIOeKtuaAgeaVsOaNruWtl+WFuAogICAgICBzdGF0dXNPcHRpb25zOiBbXSwKICAgICAgLy8g5oCn5Yir54q25oCB5a2X5YW4CiAgICAgIHNleE9wdGlvbnM6IFtdLAogICAgICAvLyDnn63kv6HpgJrnn6XlrZflhbgKICAgICAgc21zU2VuZE9wdGlvbnM6IFtdLAogICAgICAvLyDlrqHmoLjnirbmgIHlrZflhbgKICAgICAgYXVkaXRTdGF0dXNPcHRpb25zOiBbXSwKICAgICAgLy8g6KeS6Imy6YCJ6aG5CiAgICAgIHJvbGVPcHRpb25zOiBbXSwKICAgICAgLy8g6KGo5Y2V5Y+C5pWwCiAgICAgIGZvcm06IHt9LAogICAgICBkZWZhdWx0UHJvcHM6IHsKICAgICAgICBjaGlsZHJlbjogImNoaWxkcmVuIiwKICAgICAgICBsYWJlbDogImxhYmVsIgogICAgICB9LAogICAgICAvLyDnlKjmiLflr7zlhaXlj4LmlbAKICAgICAgdXBsb2FkOiB7CiAgICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGC77yI55So5oi35a+85YWl77yJCiAgICAgICAgb3BlbjogZmFsc2UsCiAgICAgICAgLy8g5by55Ye65bGC5qCH6aKY77yI55So5oi35a+85YWl77yJCiAgICAgICAgdGl0bGU6ICIiLAogICAgICAgIC8vIOaYr+WQpuemgeeUqOS4iuS8oAogICAgICAgIGlzVXBsb2FkaW5nOiBmYWxzZSwKICAgICAgICAvLyDmmK/lkKbmm7TmlrDlt7Lnu4/lrZjlnKjnmoTnlKjmiLfmlbDmja4KICAgICAgICB1cGRhdGVTdXBwb3J0OiAwLAogICAgICAgIC8vIOiuvue9ruS4iuS8oOeahOivt+axguWktOmDqAogICAgICAgIGhlYWRlcnM6IHsKICAgICAgICAgIEF1dGhvcml6YXRpb246ICJCZWFyZXIgIiArIGdldFRva2VuKCkKICAgICAgICB9LAogICAgICAgIC8vIOS4iuS8oOeahOWcsOWdgAogICAgICAgIHVybDogcHJvY2Vzcy5lbnYuVlVFX0FQUF9CQVNFX0FQSSArICIvc3lzdGVtL3VzZXIvaW1wb3J0RGF0YSIKICAgICAgfSwKICAgICAgLy8g5p+l6K+i5Y+C5pWwCiAgICAgIHF1ZXJ5UGFyYW1zOiB7CiAgICAgICAgcGFnZU51bTogMSwKICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgdXNlck5hbWU6IHVuZGVmaW5lZCwKICAgICAgICBwaG9uZW51bWJlcjogdW5kZWZpbmVkLAogICAgICAgIHN0YXR1czogdW5kZWZpbmVkLAogICAgICAgIGRlcHRJZDogdW5kZWZpbmVkLAogICAgICAgIGF1ZGl0U3RhdHVzOiB1bmRlZmluZWQKICAgICAgfSwKICAgICAgLy8g5YiX5L+h5oGvCiAgICAgIGNvbHVtbnM6IFt7CiAgICAgICAga2V5OiAnc3RhdHVzJywKICAgICAgICBpbmRleDogMCwKICAgICAgICBsYWJlbDogIlx1NUUxMFx1NTNGN1x1NzJCNlx1NjAwMSIsCiAgICAgICAgdmlzaWJsZTogdHJ1ZQogICAgICB9LCB7CiAgICAgICAga2V5OiAndXNlcklkJywKICAgICAgICBpbmRleDogMSwKICAgICAgICBsYWJlbDogIlx1NzUyOFx1NjIzN0lEIiwKICAgICAgICB2aXNpYmxlOiB0cnVlCiAgICAgIH0sIHsKICAgICAgICBrZXk6ICd1c2VyTmFtZScsCiAgICAgICAgaW5kZXg6IDIsCiAgICAgICAgbGFiZWw6ICJcdTc1MjhcdTYyMzdcdThEMjZcdTUzRjciLAogICAgICAgIHZpc2libGU6IHRydWUKICAgICAgfSwgewogICAgICAgIGtleTogJ25pY2tOYW1lJywKICAgICAgICBpbmRleDogMywKICAgICAgICBsYWJlbDogIlx1NjJBNVx1NTkwN1x1NEVCQVx1NTlEM1x1NTQwRCIsCiAgICAgICAgdmlzaWJsZTogdHJ1ZQogICAgICB9LCB7CiAgICAgICAga2V5OiAndXNlclR5cGUnLAogICAgICAgIGluZGV4OiA0LAogICAgICAgIGxhYmVsOiAiXHU3NTI4XHU2MjM3XHU3QzdCXHU1NzhCIiwKICAgICAgICB2aXNpYmxlOiB0cnVlCiAgICAgIH0sIHsKICAgICAgICBrZXk6ICdhdWRpdFN0YXR1cycsCiAgICAgICAgaW5kZXg6IDUsCiAgICAgICAgbGFiZWw6ICJcdTVCQTFcdTY4MzhcdTcyQjZcdTYwMDEiLAogICAgICAgIHZpc2libGU6IHRydWUKICAgICAgfSwgewogICAgICAgIGtleTogJ2VtYWlsJywKICAgICAgICBpbmRleDogNiwKICAgICAgICBsYWJlbDogIlx1OEQ0NFx1NjU5OVx1NjNBNVx1NjUzNlx1OTBBRVx1N0JCMSIsCiAgICAgICAgdmlzaWJsZTogdHJ1ZQogICAgICB9LCB7CiAgICAgICAga2V5OiAncGhvbmVudW1iZXInLAogICAgICAgIGluZGV4OiA3LAogICAgICAgIGxhYmVsOiAiXHU2MkE1XHU1OTA3XHU0RUJBXHU3NTM1XHU4QkREIiwKICAgICAgICB2aXNpYmxlOiB0cnVlCiAgICAgIH0sIHsKICAgICAgICBrZXk6ICdzZXgnLAogICAgICAgIGluZGV4OiA4LAogICAgICAgIGxhYmVsOiAiXHU3NTI4XHU2MjM3XHU2MDI3XHU1MjJCIiwKICAgICAgICB2aXNpYmxlOiB0cnVlCiAgICAgIH0sIHsKICAgICAgICBrZXk6ICdhdmF0YXInLAogICAgICAgIGluZGV4OiA5LAogICAgICAgIGxhYmVsOiAiXHU1OTM0XHU1MENGIiwKICAgICAgICB2aXNpYmxlOiB0cnVlCiAgICAgIH0sIHsKICAgICAgICBrZXk6ICdjb21wYW55JywKICAgICAgICBpbmRleDogMTAsCiAgICAgICAgbGFiZWw6ICJcdTUxNkNcdTUzRjhcdTUxNjhcdTc5RjAiLAogICAgICAgIHZpc2libGU6IHRydWUKICAgICAgfSwgewogICAgICAgIGtleTogJ2J1c2luZXNzTm8nLAogICAgICAgIGluZGV4OiAxMSwKICAgICAgICBsYWJlbDogIlx1ODQyNVx1NEUxQVx1NjI2N1x1NzE2N1x1NTNGN1x1NzgwMSIsCiAgICAgICAgdmlzaWJsZTogdHJ1ZQogICAgICB9LCB7CiAgICAgICAga2V5OiAnYnVzaW5lc3NOb1BpYycsCiAgICAgICAgaW5kZXg6IDEyLAogICAgICAgIGxhYmVsOiAiXHU4NDI1XHU0RTFBXHU2MjY3XHU3MTY3XHU1NkZFXHU3MjQ3IiwKICAgICAgICB2aXNpYmxlOiB0cnVlCiAgICAgIH0sIHsKICAgICAgICBrZXk6ICdwcm92aW5jZScsCiAgICAgICAgaW5kZXg6IDEzLAogICAgICAgIGxhYmVsOiAiXHU2MjQwXHU1NzI4XHU1MzNBXHU1N0RGIiwKICAgICAgICB2aXNpYmxlOiB0cnVlCiAgICAgIH0sIHsKICAgICAgICBrZXk6ICdhZGRyZXNzJywKICAgICAgICBpbmRleDogMTQsCiAgICAgICAgbGFiZWw6ICJcdThENDRcdTY1OTlcdTkwQUVcdTVCQzRcdTU3MzBcdTU3NDAiLAogICAgICAgIHZpc2libGU6IHRydWUKICAgICAgfSwgewogICAgICAgIGtleTogJ2RlYWxlcicsCiAgICAgICAgaW5kZXg6IDE1LAogICAgICAgIGxhYmVsOiAiXHU5NkI2XHU1QzVFXHU3RUNGXHU5NTAwXHU1NTQ2IiwKICAgICAgICB2aXNpYmxlOiB0cnVlCiAgICAgIH0sIHsKICAgICAgICBrZXk6ICdjcmVhdGVUaW1lJywKICAgICAgICBpbmRleDogMTYsCiAgICAgICAgbGFiZWw6ICJcdTYzRDBcdTRFQTRcdTY1RjZcdTk1RjQiLAogICAgICAgIHZpc2libGU6IHRydWUKICAgICAgfSwgewogICAgICAgIGtleTogJ3VwZGF0ZVRpbWUnLAogICAgICAgIGluZGV4OiAxNywKICAgICAgICBsYWJlbDogIlx1NjcwMFx1NTQwRVx1NEZFRVx1NjUzOVx1NjVGNlx1OTVGNCIsCiAgICAgICAgdmlzaWJsZTogdHJ1ZQogICAgICB9LCB7CiAgICAgICAga2V5OiAnc21zU2VuZCcsCiAgICAgICAgaW5kZXg6IDE4LAogICAgICAgIGxhYmVsOiAiXHU3N0VEXHU0RkUxXHU5MDFBXHU3N0U1IiwKICAgICAgICB2aXNpYmxlOiB0cnVlCiAgICAgIH1dLAogICAgICAvLyDooajljZXmoKHpqowKICAgICAgcnVsZXM6IHsKICAgICAgICB1c2VyTmFtZTogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogIueUqOaIt+WQjeS4jeiDveS4uuepuiIsCiAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIKICAgICAgICB9XSwKICAgICAgICBjb21wYW55OiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAi5YWs5Y+45YWo56ew5LiN6IO95Li656m6IiwKICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgIH1dLAogICAgICAgIGJ1c2luZXNzTm86IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICLokKXkuJrmiafnhaflj7fnoIHkuI3og73kuLrnqboiLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgfV0sCiAgICAgICAgYnVzaW5lc3NOb1BpYzogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogIuiQpeS4muaJp+eFp+WbvueJh+W/heS8oCIKICAgICAgICB9XSwKICAgICAgICBlbWFpbDogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgdHlwZTogImVtYWlsIiwKICAgICAgICAgIG1lc3NhZ2U6ICIn6K+36L6T5YWl5q2j56Gu55qE6YKu566x5Zyw5Z2AIiwKICAgICAgICAgIHRyaWdnZXI6IFsiYmx1ciIsICJjaGFuZ2UiXQogICAgICAgIH1dLAogICAgICAgIHBob25lbnVtYmVyOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBwYXR0ZXJuOiAvXjFbM3w0fDV8Nnw3fDh8OV1bMC05XVxkezh9JC8sCiAgICAgICAgICBtZXNzYWdlOiAi6K+36L6T5YWl5q2j56Gu55qE5omL5py65Y+356CBIiwKICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgIH1dLAogICAgICAgIG5pY2tOYW1lOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAi5oql5aSH5Lq65aeT5ZCN5LiN6IO95Li656m6IiwKICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgIH1dLAogICAgICAgIHByb3ZpbmNlOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAi5omA5Zyo5Yy65Z+f5LiN6IO95Li656m6IiwKICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgIH1dLAogICAgICAgIGFkZHJlc3M6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICLotYTmlpnpgq7lr4TlnLDlnYDkuI3og73kuLrnqboiLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgfV0KICAgICAgfSwKICAgICAgYXVkaXRTdGF0dXNUcmVlOiBbXSwKICAgICAgcHJvdmluY2VBbmRDaXR5RGF0YTogcmVnaW9uRGF0YSwKICAgICAgY2l0eU9wdGlvbnM6IFtdLAogICAgICBxdWVyeUFyZWE6IFtdLAogICAgICBwcm92aW5jZVRyZWVzOiBbXSwKICAgICAgdmlld09wZW46IGZhbHNlLAogICAgICB2aWV3OiB7fSwKICAgICAgcHJvY0luc0lkOiB1bmRlZmluZWQsCiAgICAgIHRhc2tJZDogdW5kZWZpbmVkLAogICAgICBiaXpLZXk6IHVuZGVmaW5lZCwKICAgICAgc2hvd0FyZWE6IGZhbHNlCiAgICB9OwogIH0sCiAgd2F0Y2g6IHsKICAgIC8vIOagueaNruWQjeensOetm+mAiemDqOmXqOagkQogICAgLy8gZGVwdE5hbWUodmFsKSB7CiAgICAvLyAgIHRoaXMuJHJlZnMudHJlZS5maWx0ZXIodmFsKTsKICAgIC8vIH0KICB9LAogIGNyZWF0ZWQ6IGZ1bmN0aW9uIGNyZWF0ZWQoKSB7CiAgICB2YXIgX3RoaXMgPSB0aGlzOwogICAgaWYgKHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIucm9sZXMgJiYgKHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIucm9sZXMuaW5jbHVkZXMoImFkbWluIikgfHwgdGhpcy4kc3RvcmUuc3RhdGUudXNlci5yb2xlcy5pbmNsdWRlcygidXNlcl9hZG1pbiIpIHx8IHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIucm9sZXMuaW5jbHVkZXMoInJlcG9ydF9hZG1pbiIpIC8vIHx8IHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIucm9sZXMuaW5jbHVkZXMoInByb3ZpbmNlX2FkbWluIikKICAgICkpIHsKICAgICAgdGhpcy5zaG93QXJlYSA9IHRydWU7CiAgICB9CiAgICB0aGlzLmdldExpc3QoKTsKICAgIC8vdGhpcy5nZXRUcmVlc2VsZWN0KCk7CiAgICB0aGlzLmdldERpY3RzKCJzeXNfbm9ybWFsX2Rpc2FibGUiKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICBfdGhpcy5zdGF0dXNPcHRpb25zID0gcmVzcG9uc2UuZGF0YTsKICAgIH0pOwogICAgdGhpcy5nZXREaWN0cygic3lzX3VzZXJfc2V4IikudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgX3RoaXMuc2V4T3B0aW9ucyA9IHJlc3BvbnNlLmRhdGE7CiAgICB9KTsKICAgIHRoaXMuZ2V0Q29uZmlnS2V5KCJzeXMudXNlci5pbml0UGFzc3dvcmQiKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICBfdGhpcy5pbml0UGFzc3dvcmQgPSByZXNwb25zZS5tc2c7CiAgICB9KTsKICAgIHRoaXMuZ2V0RGljdHMoInByX3Ntc19ub3RpZnkiKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICBfdGhpcy5zbXNTZW5kT3B0aW9ucyA9IHJlc3BvbnNlLmRhdGE7CiAgICB9KTsKICAgIHRoaXMuZ2V0RGljdHMoInByX2F1ZGl0X3N0YXR1cyIpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgIHZhciB0eXBlID0gMDsKICAgICAgaWYgKF90aGlzLiRzdG9yZS5zdGF0ZS51c2VyLnJvbGVzKSB7CiAgICAgICAgaWYgKF90aGlzLiRzdG9yZS5zdGF0ZS51c2VyLnJvbGVzLmluY2x1ZGVzKCJjb21tb24iKSkgewogICAgICAgICAgdHlwZSA9IDE7CiAgICAgICAgfQogICAgICAgIGlmIChfdGhpcy4kc3RvcmUuc3RhdGUudXNlci5yb2xlcy5pbmNsdWRlcygicHJvdmluY2VfYWRtaW4iKSkgewogICAgICAgICAgdHlwZSA9IDI7CiAgICAgICAgfQogICAgICAgIGlmIChfdGhpcy4kc3RvcmUuc3RhdGUudXNlci5yb2xlcy5pbmNsdWRlcygicmVwb3J0X2FkbWluIikpIHsKICAgICAgICAgIHR5cGUgPSAzOwogICAgICAgIH0KICAgICAgICBpZiAoX3RoaXMuJHN0b3JlLnN0YXRlLnVzZXIucm9sZXMuaW5jbHVkZXMoInVzZXJfYWRtaW4iKSkgewogICAgICAgICAgdHlwZSA9IDQ7CiAgICAgICAgfQogICAgICB9CiAgICAgIF90aGlzLmF1ZGl0U3RhdHVzT3B0aW9ucyA9IHJlc3BvbnNlLmRhdGE7CiAgICAgIHZhciBvcHQgPSBbXTsKICAgICAgb3B0LnB1c2goewogICAgICAgIGlkOiA5LAogICAgICAgIGxhYmVsOiAn5YWo6YOoJwogICAgICB9KTsKICAgICAgaWYgKHR5cGUgPT0gMiB8fCB0eXBlID09IDMgfHwgdHlwZSA9PSA0KSB7CiAgICAgICAgb3B0LnB1c2goewogICAgICAgICAgaWQ6IDEwLAogICAgICAgICAgbGFiZWw6ICfmnKrlrqHmibknCiAgICAgICAgfSk7CiAgICAgIH0KICAgICAgcmVzcG9uc2UuZGF0YS5mb3JFYWNoKGZ1bmN0aW9uIChlbGVtLCBpbmRleCkgewogICAgICAgIHZhciBvYmogPSB7fTsKICAgICAgICBvYmoubGFiZWwgPSBlbGVtLmRpY3RMYWJlbDsKICAgICAgICBvYmouaWQgPSBlbGVtLmRpY3RWYWx1ZTsKICAgICAgICBvcHQucHVzaChvYmopOwogICAgICB9KTsKICAgICAgdmFyIGF1ZGl0U3RhdHVzVHJlZSA9IHt9OwogICAgICBhdWRpdFN0YXR1c1RyZWUubGFiZWwgPSAi5a6h5qC454q25oCBIjsKICAgICAgYXVkaXRTdGF0dXNUcmVlLmNoaWxkcmVuID0gb3B0OwogICAgICB2YXIgYXVkaXRTdGF0dXNUcmVlcyA9IFtdOwogICAgICBhdWRpdFN0YXR1c1RyZWVzLnB1c2goYXVkaXRTdGF0dXNUcmVlKTsKICAgICAgX3RoaXMuYXVkaXRTdGF0dXNUcmVlID0gYXVkaXRTdGF0dXNUcmVlczsKICAgIH0pOwogICAgLy/miYDlnKjljLrln5/mlbDmja7lpITnkIYKICAgIHZhciBvcHQgPSBbXTsKICAgIG9wdC5wdXNoKHsKICAgICAgaWQ6IDAsCiAgICAgIGxhYmVsOiAn5YWo6YOoJwogICAgfSk7CiAgICBwcm92aW5jZUFuZENpdHlEYXRhLmZvckVhY2goZnVuY3Rpb24gKGVsZW0sIGluZGV4KSB7CiAgICAgIHZhciBvYmogPSB7fTsKICAgICAgb2JqLmxhYmVsID0gZWxlbS5sYWJlbDsKICAgICAgb2JqLmlkID0gZWxlbS5sYWJlbDsKICAgICAgb3B0LnB1c2gob2JqKTsKICAgIH0pOwogICAgdmFyIHByb3ZpbmNlVHJlZSA9IHt9OwogICAgcHJvdmluY2VUcmVlLmxhYmVsID0gIuaJgOWcqOWMuuWfnyI7CiAgICBwcm92aW5jZVRyZWUuY2hpbGRyZW4gPSBvcHQ7CiAgICB2YXIgcHJvdmluY2VUcmVlcyA9IFtdOwogICAgcHJvdmluY2VUcmVlcy5wdXNoKHByb3ZpbmNlVHJlZSk7CiAgICB0aGlzLnByb3ZpbmNlVHJlZXMgPSBwcm92aW5jZVRyZWVzOwogICAgaWYgKHRoaXMuX2lzTW9iaWxlKCkpIHsKICAgICAgdGhpcy5pc01vYmlsZSA9IHRydWU7CiAgICAgIHRoaXMucGFnZUxheW91dCA9ICJ0b3RhbCwgcHJldiwgbmV4dCwganVtcGVyIjsKICAgIH0KICB9LAogIG1ldGhvZHM6IHsKICAgIF9pc01vYmlsZTogZnVuY3Rpb24gX2lzTW9iaWxlKCkgewogICAgICB2YXIgZmxhZyA9IG5hdmlnYXRvci51c2VyQWdlbnQubWF0Y2goLyhwaG9uZXxwYWR8cG9kfGlQaG9uZXxpUG9kfGlvc3xpUGFkfEFuZHJvaWR8TW9iaWxlfEJsYWNrQmVycnl8SUVNb2JpbGV8TVFRQnJvd3NlcnxKVUN8RmVubmVjfHdPU0Jyb3dzZXJ8QnJvd3Nlck5HfFdlYk9TfFN5bWJpYW58V2luZG93cyBQaG9uZSkvaSk7CiAgICAgIHJldHVybiBmbGFnOwogICAgfSwKICAgIC8qKiDmn6Xor6LnlKjmiLfliJfooaggKi9nZXRMaXN0OiBmdW5jdGlvbiBnZXRMaXN0KCkgewogICAgICB2YXIgX3RoaXMyID0gdGhpczsKICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsKICAgICAgbGlzdFVzZXIodGhpcy5hZGREYXRlUmFuZ2UodGhpcy5xdWVyeVBhcmFtcywgdGhpcy5kYXRlUmFuZ2UpKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgIF90aGlzMi51c2VyTGlzdCA9IHJlc3BvbnNlLnJvd3M7CiAgICAgICAgX3RoaXMyLnRvdGFsID0gcmVzcG9uc2UudG90YWw7CiAgICAgICAgX3RoaXMyLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgfSk7CiAgICB9LAogICAgLy8g55So5oi35oCn5Yir5a2X5YW457+76K+RCiAgICBzZXhGb3JtYXQ6IGZ1bmN0aW9uIHNleEZvcm1hdChyb3csIGNvbHVtbikgewogICAgICByZXR1cm4gdGhpcy5zZWxlY3REaWN0TGFiZWwodGhpcy5zZXhPcHRpb25zLCByb3cuc2V4KTsKICAgIH0sCiAgICAvLyDluJDlj7fnirbmgIHlrZflhbjnv7vor5EKICAgIHN0YXR1c0Zvcm1hdDogZnVuY3Rpb24gc3RhdHVzRm9ybWF0KHJvdywgY29sdW1uKSB7CiAgICAgIHJldHVybiB0aGlzLnNlbGVjdERpY3RMYWJlbCh0aGlzLnN0YXR1c09wdGlvbnMsIHJvdy5zdGF0dXMpOwogICAgfSwKICAgIC8vIOefreS/oemAmuefpeWtl+WFuOe/u+ivkQogICAgc21zU2VuZEZvcm1hdDogZnVuY3Rpb24gc21zU2VuZEZvcm1hdChyb3csIGNvbHVtbikgewogICAgICByZXR1cm4gdGhpcy5zZWxlY3REaWN0TGFiZWwodGhpcy5zbXNTZW5kT3B0aW9ucywgcm93LnNtc1NlbmQpOwogICAgfSwKICAgIC8vIOWuoeaguOeKtuaAgeWtl+WFuOe/u+ivkQogICAgYXVkaXRTdGF0dXNGb3JtYXQ6IGZ1bmN0aW9uIGF1ZGl0U3RhdHVzRm9ybWF0KHJvdywgY29sdW1uKSB7CiAgICAgIHJldHVybiB0aGlzLnNlbGVjdERpY3RMYWJlbCh0aGlzLmF1ZGl0U3RhdHVzT3B0aW9ucywgcm93LmF1ZGl0U3RhdHVzKTsKICAgIH0sCiAgICAvKiog5p+l6K+i6YOo6Zeo5LiL5ouJ5qCR57uT5p6EICovZ2V0VHJlZXNlbGVjdDogZnVuY3Rpb24gZ2V0VHJlZXNlbGVjdCgpIHsKICAgICAgdmFyIF90aGlzMyA9IHRoaXM7CiAgICAgIHRyZWVzZWxlY3QoKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgIF90aGlzMy5kZXB0T3B0aW9ucyA9IHJlc3BvbnNlLmRhdGE7CiAgICAgIH0pOwogICAgfSwKICAgIC8vIOetm+mAieiKgueCuQogICAgZmlsdGVyTm9kZTogZnVuY3Rpb24gZmlsdGVyTm9kZSh2YWx1ZSwgZGF0YSkgewogICAgICBpZiAoIXZhbHVlKSByZXR1cm4gdHJ1ZTsKICAgICAgcmV0dXJuIGRhdGEubGFiZWwuaW5kZXhPZih2YWx1ZSkgIT09IC0xOwogICAgfSwKICAgIC8vIOiKgueCueWNleWHu+S6i+S7tgogICAgaGFuZGxlTm9kZUNsaWNrOiBmdW5jdGlvbiBoYW5kbGVOb2RlQ2xpY2soZGF0YSkgewogICAgICBpZiAoZGF0YS5pZCA9PSA5KSB7CiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5hdWRpdFN0YXR1cyA9IHVuZGVmaW5lZDsKICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLm5vZGUgPSB1bmRlZmluZWQ7CiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5zcGFyZTEgPSB1bmRlZmluZWQ7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgaWYgKGRhdGEuaWQgPT0gMSB8fCBkYXRhLmlkID09IDEwKSB7CiAgICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmF1ZGl0U3RhdHVzID0gMTsKICAgICAgICAgIGlmICh0aGlzLiRzdG9yZS5zdGF0ZS51c2VyLnJvbGVzKSB7CiAgICAgICAgICAgIGlmICh0aGlzLiRzdG9yZS5zdGF0ZS51c2VyLnJvbGVzLmluY2x1ZGVzKCJwcm92aW5jZV9hZG1pbiIpKSB7CiAgICAgICAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5ub2RlID0gJ+ecgei0n+i0o+S6uic7CiAgICAgICAgICAgICAgaWYgKGRhdGEuaWQgPT0gMTApIHsKICAgICAgICAgICAgICAgIHRoaXMucXVlcnlQYXJhbXMuc3BhcmUxID0gJz0nOwogICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnNwYXJlMSA9ICchPSc7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9CiAgICAgICAgICAgIGlmICh0aGlzLiRzdG9yZS5zdGF0ZS51c2VyLnJvbGVzLmluY2x1ZGVzKCJyZXBvcnRfYWRtaW4iKSkgewogICAgICAgICAgICAgIHRoaXMucXVlcnlQYXJhbXMubm9kZSA9ICflrqHmoLjlkZgnOwogICAgICAgICAgICAgIGlmIChkYXRhLmlkID09IDEwKSB7CiAgICAgICAgICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnNwYXJlMSA9ICc9JzsKICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5zcGFyZTEgPSAnIT0nOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfQogICAgICAgICAgICBpZiAodGhpcy4kc3RvcmUuc3RhdGUudXNlci5yb2xlcy5pbmNsdWRlcygidXNlcl9hZG1pbiIpKSB7CiAgICAgICAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5ub2RlID0gJ+euoeeQhuWRmCc7CiAgICAgICAgICAgICAgaWYgKGRhdGEuaWQgPT0gMTApIHsKICAgICAgICAgICAgICAgIHRoaXMucXVlcnlQYXJhbXMuc3BhcmUxID0gJz0nOwogICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnNwYXJlMSA9ICchPSc7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMucXVlcnlQYXJhbXMuYXVkaXRTdGF0dXMgPSBkYXRhLmlkOwogICAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5ub2RlID0gdW5kZWZpbmVkOwogICAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5zcGFyZTEgPSB1bmRlZmluZWQ7CiAgICAgICAgfQogICAgICB9CiAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgfSwKICAgIC8vIOWMuuWfn+iKgueCueWNleWHu+S6i+S7tgogICAgaGFuZGxlUHJvdmluY2VDbGljazogZnVuY3Rpb24gaGFuZGxlUHJvdmluY2VDbGljayhkYXRhKSB7CiAgICAgIGlmIChkYXRhLmlkID09IDApIHsKICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnByb3ZpbmNlID0gdW5kZWZpbmVkOwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMucXVlcnlQYXJhbXMucHJvdmluY2UgPSBkYXRhLmlkOwogICAgICB9CiAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgfSwKICAgIC8vIOeUqOaIt+eKtuaAgeS/ruaUuQogICAgaGFuZGxlU3RhdHVzQ2hhbmdlOiBmdW5jdGlvbiBoYW5kbGVTdGF0dXNDaGFuZ2Uocm93KSB7CiAgICAgIHZhciBfdGhpczQgPSB0aGlzOwogICAgICB2YXIgdGV4dCA9IHJvdy5zdGF0dXMgPT09ICIwIiA/ICLlkK/nlKgiIDogIuWBnOeUqCI7CiAgICAgIHRoaXMuJGNvbmZpcm0oJ+ehruiupOimgSInICsgdGV4dCArICciIicgKyByb3cudXNlck5hbWUgKyAnIueUqOaIt+WQlz8nLCAi6K2m5ZGKIiwgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAi56Gu5a6aIiwKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAi5Y+W5raIIiwKICAgICAgICB0eXBlOiAid2FybmluZyIKICAgICAgfSkudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgcmV0dXJuIGNoYW5nZVVzZXJTdGF0dXMocm93LnVzZXJJZCwgcm93LnN0YXR1cyk7CiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgIF90aGlzNC5tc2dTdWNjZXNzKHRleHQgKyAi5oiQ5YqfIik7CiAgICAgIH0pLmNhdGNoKGZ1bmN0aW9uICgpIHsKICAgICAgICByb3cuc3RhdHVzID0gcm93LnN0YXR1cyA9PT0gIjAiID8gIjEiIDogIjAiOwogICAgICB9KTsKICAgIH0sCiAgICAvLyDlj5bmtojmjInpkq4KICAgIGNhbmNlbDogZnVuY3Rpb24gY2FuY2VsKCkgewogICAgICB0aGlzLm9wZW4gPSBmYWxzZTsKICAgICAgdGhpcy5yZXNldCgpOwogICAgfSwKICAgIC8vIOihqOWNlemHjee9rgogICAgcmVzZXQ6IGZ1bmN0aW9uIHJlc2V0KCkgewogICAgICB0aGlzLmZvcm0gPSB7CiAgICAgICAgdXNlcklkOiB1bmRlZmluZWQsCiAgICAgICAgZGVwdElkOiB1bmRlZmluZWQsCiAgICAgICAgdXNlck5hbWU6IHVuZGVmaW5lZCwKICAgICAgICBuaWNrTmFtZTogdW5kZWZpbmVkLAogICAgICAgIHVzZXJUeXBlOiB1bmRlZmluZWQsCiAgICAgICAgZW1haWw6IHVuZGVmaW5lZCwKICAgICAgICBwaG9uZW51bWJlcjogdW5kZWZpbmVkLAogICAgICAgIHNleDogIjAiLAogICAgICAgIGF2YXRhcjogdW5kZWZpbmVkLAogICAgICAgIHBhc3N3b3JkOiB1bmRlZmluZWQsCiAgICAgICAgc3RhdHVzOiAiMCIsCiAgICAgICAgZGVsRmxhZzogdW5kZWZpbmVkLAogICAgICAgIGxvZ2luSXA6IHVuZGVmaW5lZCwKICAgICAgICBsb2dpbkRhdGU6IHVuZGVmaW5lZCwKICAgICAgICBjcmVhdGVCeTogdW5kZWZpbmVkLAogICAgICAgIGNyZWF0ZVRpbWU6IHVuZGVmaW5lZCwKICAgICAgICB1cGRhdGVCeTogdW5kZWZpbmVkLAogICAgICAgIHVwZGF0ZVRpbWU6IHVuZGVmaW5lZCwKICAgICAgICByZW1hcms6IHVuZGVmaW5lZCwKICAgICAgICBjb21wYW55OiB1bmRlZmluZWQsCiAgICAgICAgYnVzaW5lc3NObzogdW5kZWZpbmVkLAogICAgICAgIGJ1c2luZXNzTm9QaWM6IHVuZGVmaW5lZCwKICAgICAgICBwcm92aW5jZTogW10sCiAgICAgICAgYWRkcmVzczogdW5kZWZpbmVkLAogICAgICAgIGRlYWxlcjogdW5kZWZpbmVkLAogICAgICAgIHNtc1NlbmQ6ICIwIiwKICAgICAgICBhdWRpdFN0YXR1czogIjAiLAogICAgICAgIHJvbGVJZHM6IFtdCiAgICAgIH07CiAgICAgIHRoaXMucmVzZXRGb3JtKCJmb3JtIik7CiAgICB9LAogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqL2hhbmRsZVF1ZXJ5OiBmdW5jdGlvbiBoYW5kbGVRdWVyeSgpIHsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlID0gMTsKICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICB9LAogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqL3Jlc2V0UXVlcnk6IGZ1bmN0aW9uIHJlc2V0UXVlcnkoKSB7CiAgICAgIHRoaXMuZGF0ZVJhbmdlID0gW107CiAgICAgIHRoaXMucXVlcnlBcmVhID0gW107CiAgICAgIHRoaXMucmVzZXRGb3JtKCJxdWVyeUZvcm0iKTsKICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOwogICAgfSwKICAgIC8vIOWkmumAieahhumAieS4reaVsOaNrgogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlOiBmdW5jdGlvbiBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7CiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgIHJldHVybiBpdGVtLnVzZXJJZDsKICAgICAgfSk7CiAgICAgIHRoaXMuc2luZ2xlID0gc2VsZWN0aW9uLmxlbmd0aCAhPSAxOwogICAgICB0aGlzLm11bHRpcGxlID0gIXNlbGVjdGlvbi5sZW5ndGg7CiAgICB9LAogICAgaGFuZGxlVmlldzogZnVuY3Rpb24gaGFuZGxlVmlldyhyb3cpIHsKICAgICAgdmFyIF90aGlzNSA9IHRoaXM7CiAgICAgIHRoaXMudmlldyA9IEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkocm93KSk7CiAgICAgIDsKICAgICAgdGhpcy52aWV3LmF1ZGl0U3RhdHVzID0gdGhpcy5hdWRpdFN0YXR1c0Zvcm1hdChyb3cpOwogICAgICB0aGlzLnZpZXcuc3RhdHVzID0gdGhpcy5zdGF0dXNGb3JtYXQocm93KTsKICAgICAgdGhpcy52aWV3LnNtc1NlbmQgPSB0aGlzLnNtc1NlbmRGb3JtYXQocm93KTsKICAgICAgdGhpcy50aXRsZSA9ICLmn6XnnIvnlKjmiLfor6bmg4UiOwogICAgICB2YXIgcGFyYW1zID0gewogICAgICAgIGJpektleTogcm93LnVzZXJJZCwKICAgICAgICBkZWZLZXk6ICdwcm9jZXNzX3VzZXJfcmVnJwogICAgICB9OwogICAgICB0aGlzLmJpektleSA9IHJvdy51c2VySWQ7CiAgICAgIGdldEluc0lkQnlCaXpLZXkocGFyYW1zKS50aGVuKGZ1bmN0aW9uIChyZXNwKSB7CiAgICAgICAgaWYgKHJlc3AuZGF0YSAmJiByZXNwLmRhdGEuaW5zdGFuY2VJZCkgewogICAgICAgICAgX3RoaXM1LnByb2NJbnNJZCA9IHJlc3AuZGF0YS5pbnN0YW5jZUlkOwogICAgICAgICAgX3RoaXM1LnRhc2tJZCA9IHJlc3AuZGF0YS50YXNrSWQ7CiAgICAgICAgICBpZiAocmVzcC5kYXRhLmluc3RhbmNlSWQgJiYgIXJlc3AuZGF0YS5lbmRUaW1lICYmIHJlc3AuZGF0YS5hc3NpZ25lZSA9PSBfdGhpczUuJHN0b3JlLnN0YXRlLnVzZXIudXNlcklkKSB7CiAgICAgICAgICAgIF90aGlzNS5maW5pc2hlZCA9IGZhbHNlOwogICAgICAgICAgfSBlbHNlIGlmIChfdGhpczUuJHN0b3JlLnN0YXRlLnVzZXIucm9sZXMgJiYgX3RoaXM1LiRzdG9yZS5zdGF0ZS51c2VyLnJvbGVzLmluY2x1ZGVzKCJyZXBvcnRfYWRtaW4iKSkgewogICAgICAgICAgICAvL+WuoeaguOWRmOinkuiJsuS4jeaOp+WItuiwgeaTjeS9nAogICAgICAgICAgICBfdGhpczUuZmluaXNoZWQgPSBmYWxzZTsKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIF90aGlzNS5maW5pc2hlZCA9IHRydWU7CiAgICAgICAgICB9CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIF90aGlzNS5maW5pc2hlZCA9IHRydWU7CiAgICAgICAgfQogICAgICAgIF90aGlzNS52aWV3T3BlbiA9IHRydWU7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDmlrDlop7mjInpkq7mk43kvZwgKi9oYW5kbGVBZGQ6IGZ1bmN0aW9uIGhhbmRsZUFkZCgpIHsKICAgICAgLy8gdGhpcy5yZXNldCgpOwogICAgICAvLyAvL3RoaXMuZ2V0VHJlZXNlbGVjdCgpOwogICAgICAvLyBnZXRVc2VyKCkudGhlbigocmVzcG9uc2UpID0+IHsKICAgICAgLy8gICB0aGlzLnBvc3RPcHRpb25zID0gcmVzcG9uc2UucG9zdHM7CiAgICAgIC8vICAgdGhpcy5yb2xlT3B0aW9ucyA9IHJlc3BvbnNlLnJvbGVzOwogICAgICAvLyAgIHRoaXMub3BlbiA9IHRydWU7CiAgICAgIC8vICAgdGhpcy50aXRsZSA9ICLmt7vliqDnlKjmiLciOwogICAgICAvLyAgIHRoaXMuZm9ybS5wYXNzd29yZCA9IHRoaXMuaW5pdFBhc3N3b3JkOwogICAgICAvLyB9KTsKICAgICAgdGhpcy4kcm91dGVyLnB1c2goewogICAgICAgIHBhdGg6ICcvc3lzdGVtL3VzZXIvZm9ybScsCiAgICAgICAgcXVlcnk6IHsKICAgICAgICAgIGJ1c2luZXNzS2V5OiB1bmRlZmluZWQsCiAgICAgICAgICBmb3JtRWRpdDogdHJ1ZQogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLyoqIOS/ruaUueaMiemSruaTjeS9nCAqL2hhbmRsZVVwZGF0ZTogZnVuY3Rpb24gaGFuZGxlVXBkYXRlKHJvdykgewogICAgICB2YXIgX3RoaXM2ID0gdGhpczsKICAgICAgdGhpcy5yZXNldCgpOwogICAgICAvL3RoaXMuZ2V0VHJlZXNlbGVjdCgpOwogICAgICB2YXIgdXNlcklkID0gcm93LnVzZXJJZCB8fCB0aGlzLmlkczsKICAgICAgZ2V0VXNlcih1c2VySWQpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgX3RoaXM2LmZvcm0gPSByZXNwb25zZS5kYXRhOwogICAgICAgIF90aGlzNi5wb3N0T3B0aW9ucyA9IHJlc3BvbnNlLnBvc3RzOwogICAgICAgIF90aGlzNi5yb2xlT3B0aW9ucyA9IHJlc3BvbnNlLnJvbGVzOwogICAgICAgIF90aGlzNi5mb3JtLnBvc3RJZHMgPSByZXNwb25zZS5wb3N0SWRzOwogICAgICAgIF90aGlzNi5mb3JtLnJvbGVJZHMgPSByZXNwb25zZS5yb2xlSWRzOwogICAgICAgIF90aGlzNi5vcGVuID0gdHJ1ZTsKICAgICAgICBfdGhpczYudGl0bGUgPSAi5L+u5pS555So5oi3IjsKICAgICAgICBfdGhpczYuZm9ybS5wYXNzd29yZCA9ICIiOwogICAgICAgIHZhciBwcm92aW5jZXMgPSByZXNwb25zZS5kYXRhLnByb3ZpbmNlOwogICAgICAgIGlmIChwcm92aW5jZXMubGVuZ3RoID4gMCkgewogICAgICAgICAgdmFyIGFkZHJlc3MgPSBwcm92aW5jZXMuc3BsaXQoIi8iKTsKICAgICAgICAgIHZhciBjaXR5cyA9IFtdOwogICAgICAgICAgLy8g55yB5Lu9CiAgICAgICAgICBpZiAoYWRkcmVzcy5sZW5ndGggPiAwKSBjaXR5cy5wdXNoKFRleHRUb0NvZGVbYWRkcmVzc1swXV0uY29kZSk7CiAgICAgICAgICAvLyDln47luIIKICAgICAgICAgIGlmIChhZGRyZXNzLmxlbmd0aCA+IDEpIGNpdHlzLnB1c2goVGV4dFRvQ29kZVthZGRyZXNzWzBdXVthZGRyZXNzWzFdXS5jb2RlKTsKICAgICAgICAgIC8vIOWcsOWMugogICAgICAgICAgaWYgKGFkZHJlc3MubGVuZ3RoID4gMikgY2l0eXMucHVzaChUZXh0VG9Db2RlW2FkZHJlc3NbMF1dW2FkZHJlc3NbMV1dW2FkZHJlc3NbMl1dLmNvZGUpOwogICAgICAgICAgX3RoaXM2LmNpdHlPcHRpb25zID0gY2l0eXM7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvKiog6YeN572u5a+G56CB5oyJ6ZKu5pON5L2cICovaGFuZGxlUmVzZXRQd2Q6IGZ1bmN0aW9uIGhhbmRsZVJlc2V0UHdkKHJvdykgewogICAgICB2YXIgX3RoaXM3ID0gdGhpczsKICAgICAgdGhpcy4kcHJvbXB0KCfor7fovpPlhaUiJyArIHJvdy51c2VyTmFtZSArICci55qE5paw5a+G56CBJywgIuaPkOekuiIsIHsKICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogIuehruWumiIsCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogIuWPlua2iCIKICAgICAgfSkudGhlbihmdW5jdGlvbiAoX3JlZikgewogICAgICAgIHZhciB2YWx1ZSA9IF9yZWYudmFsdWU7CiAgICAgICAgcmVzZXRVc2VyUHdkKHJvdy51c2VySWQsIHZhbHVlKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgICAgX3RoaXM3Lm1zZ1N1Y2Nlc3MoIuS/ruaUueaIkOWKn++8jOaWsOWvhueggeaYr++8miIgKyB2YWx1ZSk7CiAgICAgICAgfSk7CiAgICAgIH0pLmNhdGNoKGZ1bmN0aW9uICgpIHt9KTsKICAgIH0sCiAgICAvKiog5o+Q5Lqk5oyJ6ZKuICovCiAgICBzdWJtaXRGb3JtOiBmdW5jdGlvbiBzdWJtaXRGb3JtKCkgewogICAgICB2YXIgX3RoaXM4ID0gdGhpczsKICAgICAgaWYgKHRoaXMuY2l0eU9wdGlvbnMubGVuZ3RoIDwgMSkge30KICAgICAgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKGZ1bmN0aW9uICh2YWxpZCkgewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgaWYgKF90aGlzOC5mb3JtLnVzZXJJZCAhPSB1bmRlZmluZWQpIHsKICAgICAgICAgICAgdXBkYXRlVXNlcihfdGhpczguZm9ybSkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICAgICAgICBfdGhpczgubXNnU3VjY2Vzcygi5L+u5pS55oiQ5YqfIik7CiAgICAgICAgICAgICAgX3RoaXM4Lm9wZW4gPSBmYWxzZTsKICAgICAgICAgICAgICBfdGhpczguZ2V0TGlzdCgpOwogICAgICAgICAgICB9KTsKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIGFkZFVzZXIoX3RoaXM4LmZvcm0pLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgICAgICAgX3RoaXM4Lm1zZ1N1Y2Nlc3MoIuaWsOWinuaIkOWKnyIpOwogICAgICAgICAgICAgIF90aGlzOC5vcGVuID0gZmFsc2U7CiAgICAgICAgICAgICAgX3RoaXM4LmdldExpc3QoKTsKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvKiog5Yig6Zmk5oyJ6ZKu5pON5L2cICovaGFuZGxlRGVsZXRlOiBmdW5jdGlvbiBoYW5kbGVEZWxldGUocm93KSB7CiAgICAgIHZhciBfdGhpczkgPSB0aGlzOwogICAgICB2YXIgdXNlcklkcyA9IHJvdy51c2VySWQgfHwgdGhpcy5pZHM7CiAgICAgIHRoaXMuJGNvbmZpcm0oJ+aYr+WQpuehruiupOWIoOmZpOeUqOaIt+e8luWPt+S4uiInICsgdXNlcklkcyArICci55qE5pWw5o2u6aG5PycsICLorablkYoiLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLAogICAgICAgIHR5cGU6ICJ3YXJuaW5nIgogICAgICB9KS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICByZXR1cm4gZGVsVXNlcih1c2VySWRzKTsKICAgICAgfSkudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgX3RoaXM5LmdldExpc3QoKTsKICAgICAgICBfdGhpczkubXNnU3VjY2Vzcygi5Yig6Zmk5oiQ5YqfIik7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDlr7zlhaXmjInpkq7mk43kvZwgKi9oYW5kbGVJbXBvcnQ6IGZ1bmN0aW9uIGhhbmRsZUltcG9ydCgpIHsKICAgICAgdGhpcy51cGxvYWQudGl0bGUgPSAi55So5oi35a+85YWlIjsKICAgICAgdGhpcy51cGxvYWQub3BlbiA9IHRydWU7CiAgICB9LAogICAgLyoqIOS4i+i9veaooeadv+aTjeS9nCAqL2ltcG9ydFRlbXBsYXRlOiBmdW5jdGlvbiBpbXBvcnRUZW1wbGF0ZSgpIHsKICAgICAgdmFyIF90aGlzMTAgPSB0aGlzOwogICAgICBfaW1wb3J0VGVtcGxhdGUoKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgIF90aGlzMTAuZG93bmxvYWQocmVzcG9uc2UubXNnKTsKICAgICAgfSk7CiAgICB9LAogICAgLy8g5paH5Lu25LiK5Lyg5Lit5aSE55CGCiAgICBoYW5kbGVGaWxlVXBsb2FkUHJvZ3Jlc3M6IGZ1bmN0aW9uIGhhbmRsZUZpbGVVcGxvYWRQcm9ncmVzcyhldmVudCwgZmlsZSwgZmlsZUxpc3QpIHsKICAgICAgdGhpcy51cGxvYWQuaXNVcGxvYWRpbmcgPSB0cnVlOwogICAgfSwKICAgIC8vIOaWh+S7tuS4iuS8oOaIkOWKn+WkhOeQhgogICAgaGFuZGxlRmlsZVN1Y2Nlc3M6IGZ1bmN0aW9uIGhhbmRsZUZpbGVTdWNjZXNzKHJlc3BvbnNlLCBmaWxlLCBmaWxlTGlzdCkgewogICAgICB0aGlzLnVwbG9hZC5vcGVuID0gZmFsc2U7CiAgICAgIHRoaXMudXBsb2FkLmlzVXBsb2FkaW5nID0gZmFsc2U7CiAgICAgIHRoaXMuJHJlZnMudXBsb2FkLmNsZWFyRmlsZXMoKTsKICAgICAgdGhpcy4kYWxlcnQocmVzcG9uc2UubXNnLCAi5a+85YWl57uT5p6cIiwgewogICAgICAgIGRhbmdlcm91c2x5VXNlSFRNTFN0cmluZzogdHJ1ZQogICAgICB9KTsKICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICB9LAogICAgLy8g5o+Q5Lqk5LiK5Lyg5paH5Lu2CiAgICBzdWJtaXRGaWxlRm9ybTogZnVuY3Rpb24gc3VibWl0RmlsZUZvcm0oKSB7CiAgICAgIHRoaXMuJHJlZnMudXBsb2FkLnN1Ym1pdCgpOwogICAgfSwKICAgIGhhbmRsZUNpdHlDaGFuZ2U6IGZ1bmN0aW9uIGhhbmRsZUNpdHlDaGFuZ2UodmFsdWUpIHsKICAgICAgaWYgKCF2YWx1ZSB8fCB2YWx1ZS5sZW5ndGggPT0gMCkgewogICAgICAgIHRoaXMuY2l0eU9wdGlvbnMgPSBudWxsOwogICAgICAgIHRoaXMuZm9ybS5wcm92aW5jZSA9IHVuZGVmaW5lZDsKICAgICAgICB0aGlzLmZvcm0uZGlzdHJpY3QgPSB1bmRlZmluZWQ7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIHRoaXMuY2l0eU9wdGlvbnMgPSB2YWx1ZTsKICAgICAgdmFyIHR4dCA9ICIiOwogICAgICB2YWx1ZS5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgdHh0ICs9IENvZGVUb1RleHRbaXRlbV0gKyAiLyI7CiAgICAgIH0pOwogICAgICBpZiAodHh0Lmxlbmd0aCA+IDEpIHsKICAgICAgICB0eHQgPSB0eHQuc3Vic3RyaW5nKDAsIHR4dC5sZW5ndGggLSAxKTsKICAgICAgICB0aGlzLmZvcm0ucHJvdmluY2UgPSB0eHQ7CiAgICAgICAgdGhpcy5mb3JtLmRpc3RyaWN0ID0gQ29kZVRvVGV4dFt2YWx1ZVswXV07CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy5mb3JtLnByb3ZpbmNlID0gdW5kZWZpbmVkOwogICAgICB9CiAgICB9LAogICAgaGFuZGxlUXVlcnlDaXR5Q2hhbmdlOiBmdW5jdGlvbiBoYW5kbGVRdWVyeUNpdHlDaGFuZ2UodmFsdWUpIHsKICAgICAgdGhpcy5xdWVyeUFyZWEgPSB2YWx1ZTsKICAgICAgdmFyIHR4dCA9ICIiOwogICAgICB2YWx1ZS5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgdHh0ICs9IENvZGVUb1RleHRbaXRlbV0gKyAiLyI7CiAgICAgIH0pOwogICAgICBpZiAodHh0Lmxlbmd0aCA+IDEpIHsKICAgICAgICB0eHQgPSB0eHQuc3Vic3RyaW5nKDAsIHR4dC5sZW5ndGggLSAxKTsKICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnByb3ZpbmNlID0gdHh0OwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMucXVlcnlQYXJhbXMucHJvdmluY2UgPSB1bmRlZmluZWQ7CiAgICAgIH0KICAgIH0sCiAgICBjbGlja0V4cG9ydDogZnVuY3Rpb24gY2xpY2tFeHBvcnQoKSB7CiAgICAgIHRoaXMuc2hvd0V4cG9ydCA9IHRydWU7CiAgICB9LAogICAgY2xpY2tQcmludDogZnVuY3Rpb24gY2xpY2tQcmludCgpIHsKICAgICAgdGhpcy5zaG93UHJpbnQgPSB0cnVlOwogICAgfSwKICAgIC8qKiDlr7zlh7rmjInpkq7mk43kvZwgKi9oYW5kbGVFeHBvcnQ6IGZ1bmN0aW9uIGhhbmRsZUV4cG9ydCh0eXBlKSB7CiAgICAgIHZhciBfdGhpczExID0gdGhpczsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5zcGFyZTEgPSB0eXBlOwogICAgICB2YXIgY29sID0gW107CiAgICAgIHRoaXMuY29sdW1ucy5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgaWYgKGl0ZW0udmlzaWJsZSkgewogICAgICAgICAgY29sLnB1c2goaXRlbS5sYWJlbCk7CiAgICAgICAgfQogICAgICB9KTsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5zcGFyZTIgPSBjb2wuam9pbignLCcpOwogICAgICB2YXIgcXVlcnlQYXJhbXMgPSB0aGlzLnF1ZXJ5UGFyYW1zOwogICAgICB0aGlzLiRjb25maXJtKCLmmK/lkKbnoa7orqTlr7zlh7rnlKjmiLfmkJzntKLnu5PmnpwiICsgKHR5cGUgPT0gMCA/ICfmnKzpobUnIDogJ+WFqOmDqCcpICsgIuaVsOaNrumhuT8iLCAi6K2m5ZGKIiwgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAi56Gu5a6aIiwKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAi5Y+W5raIIiwKICAgICAgICB0eXBlOiAid2FybmluZyIKICAgICAgfSkudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgcmV0dXJuIGV4cG9ydFVzZXIocXVlcnlQYXJhbXMpOwogICAgICB9KS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgIF90aGlzMTEuZG93bmxvYWQocmVzcG9uc2UubXNnKTsKICAgICAgICBfdGhpczExLnNob3dFeHBvcnQgPSBmYWxzZTsKICAgICAgICBsb2FkaW5nLmNsb3NlKCk7CiAgICAgIH0pOwogICAgfSwKICAgIGhhbmRsZVByaW50OiBmdW5jdGlvbiBoYW5kbGVQcmludCh0eXBlKSB7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMuc3BhcmUxID0gdHlwZTsKICAgICAgdmFyIHByb3BlcnRpZXMgPSBbXTsKICAgICAgcHJvcGVydGllcy5wdXNoKHsKICAgICAgICBmaWVsZDogJ2luZGV4JywKICAgICAgICBkaXNwbGF5TmFtZTogJ+W6j+WPtycKICAgICAgfSk7CiAgICAgIHRoaXMuY29sdW1ucy5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgaWYgKGl0ZW0udmlzaWJsZSkgewogICAgICAgICAgcHJvcGVydGllcy5wdXNoKHsKICAgICAgICAgICAgZmllbGQ6IGl0ZW0ua2V5LAogICAgICAgICAgICBkaXNwbGF5TmFtZTogaXRlbS5sYWJlbAogICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICB9KTsKICAgICAgcHJpbnRVc2VyKHRoaXMucXVlcnlQYXJhbXMpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgcHJpbnRKUyh7CiAgICAgICAgICBwcmludGFibGU6IHJlc3BvbnNlLmRhdGEsCiAgICAgICAgICB0eXBlOiAnanNvbicsCiAgICAgICAgICBwcm9wZXJ0aWVzOiBwcm9wZXJ0aWVzLAogICAgICAgICAgaGVhZGVyOiAnPGRpdiBzdHlsZT0idGV4dC1hbGlnbjogY2VudGVyIj48aDM+55So5oi35YiX6KGo5YiX6KGoPC9oMz48L2Rpdj4nLAogICAgICAgICAgdGFyZ2V0U3R5bGVzOiBbJyonXSwKICAgICAgICAgIGdyaWRIZWFkZXJTdHlsZTogJ2JvcmRlcjogMXB4IHNvbGlkICMwMDA7dGV4dC1hbGlnbjpjZW50ZXInLAogICAgICAgICAgZ3JpZFN0eWxlOiAnYm9yZGVyOiAxcHggc29saWQgIzAwMDt0ZXh0LWFsaWduOmNlbnRlcicsCiAgICAgICAgICBzdHlsZTogIkBwYWdlIHttYXJnaW46MCAxMG1tfSIKICAgICAgICB9KTsKICAgICAgfSk7CiAgICB9CiAgfQp9Ow=="}, null]}