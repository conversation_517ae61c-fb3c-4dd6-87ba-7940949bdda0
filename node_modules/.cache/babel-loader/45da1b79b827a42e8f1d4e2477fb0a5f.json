{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/utils/generator/js.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/utils/generator/js.js", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["isArray", "exportDefault", "titleCase", "trigger", "units", "KB", "MB", "GB", "confGlobal", "inheritAttrs", "file", "dialog", "makeUpJs", "conf", "type", "JSON", "parse", "stringify", "dataList", "ruleList", "optionsList", "propsList", "methodList", "mixinMethod", "uploadVarList", "fields", "for<PERSON>ach", "el", "buildAttributes", "script", "buildexport", "join", "buildData", "buildRules", "options", "length", "buildOptions", "dataType", "model", "concat", "vModel", "buildOptionMethod", "props", "buildProps", "action", "tag", "push", "buildBeforeUpload", "buildSubmitUpload", "children", "el2", "list", "minxins", "formBtns", "submitForm", "formRef", "resetForm", "onOpen", "onClose", "close", "handelConfirm", "methods", "Object", "keys", "key", "undefined", "defaultValue", "multiple", "rules", "required", "message", "placeholder", "label", "regList", "item", "pattern", "eval", "str", "valueKey", "value", "labelKey", "<PERSON><PERSON><PERSON>", "unitNum", "sizeUnit", "rightSizeCode", "acceptCode", "returnList", "fileSize", "accept", "methodName", "data", "selectOptions", "uploadVar", "formModel", "formRules"], "sources": ["/Users/<USER>/develop/code/project/haijia/adminweb/src/utils/generator/js.js"], "sourcesContent": ["import { isArray } from 'util'\nimport { exportDefault, titleCase } from '@/utils/index'\nimport { trigger } from './config'\n\nconst units = {\n  KB: '1024',\n  MB: '1024 / 1024',\n  GB: '1024 / 1024 / 1024'\n}\nlet confGlobal\nconst inheritAttrs = {\n  file: '',\n  dialog: 'inheritAttrs: false,'\n}\n\n\nexport function makeUpJs(conf, type) {\n  confGlobal = conf = JSON.parse(JSON.stringify(conf))\n  const dataList = []\n  const ruleList = []\n  const optionsList = []\n  const propsList = []\n  const methodList = mixinMethod(type)\n  const uploadVarList = []\n\n  conf.fields.forEach(el => {\n    buildAttributes(el, dataList, ruleList, optionsList, methodList, propsList, uploadVarList)\n  })\n\n  const script = buildexport(\n    conf,\n    type,\n    dataList.join('\\n'),\n    ruleList.join('\\n'),\n    optionsList.join('\\n'),\n    uploadVarList.join('\\n'),\n    propsList.join('\\n'),\n    methodList.join('\\n')\n  )\n  confGlobal = null\n  return script\n}\n\nfunction buildAttributes(el, dataList, ruleList, optionsList, methodList, propsList, uploadVarList) {\n  buildData(el, dataList)\n  buildRules(el, ruleList)\n\n  if (el.options && el.options.length) {\n    buildOptions(el, optionsList)\n    if (el.dataType === 'dynamic') {\n      const model = `${el.vModel}Options`\n      const options = titleCase(model)\n      buildOptionMethod(`get${options}`, model, methodList)\n    }\n  }\n\n  if (el.props && el.props.props) {\n    buildProps(el, propsList)\n  }\n\n  if (el.action && el.tag === 'el-upload') {\n    uploadVarList.push(\n      `${el.vModel}Action: '${el.action}',\n      ${el.vModel}fileList: [],`\n    )\n    methodList.push(buildBeforeUpload(el))\n    if (!el['auto-upload']) {\n      methodList.push(buildSubmitUpload(el))\n    }\n  }\n\n  if (el.children) {\n    el.children.forEach(el2 => {\n      buildAttributes(el2, dataList, ruleList, optionsList, methodList, propsList, uploadVarList)\n    })\n  }\n}\n\nfunction mixinMethod(type) {\n  const list = []; const\n    minxins = {\n      file: confGlobal.formBtns ? {\n        submitForm: `submitForm() {\n        this.$refs['${confGlobal.formRef}'].validate(valid => {\n          if(!valid) return\n          // TODO 提交表单\n        })\n      },`,\n        resetForm: `resetForm() {\n        this.$refs['${confGlobal.formRef}'].resetFields()\n      },`\n      } : null,\n      dialog: {\n        onOpen: 'onOpen() {},',\n        onClose: `onClose() {\n        this.$refs['${confGlobal.formRef}'].resetFields()\n      },`,\n        close: `close() {\n        this.$emit('update:visible', false)\n      },`,\n        handelConfirm: `handelConfirm() {\n        this.$refs['${confGlobal.formRef}'].validate(valid => {\n          if(!valid) return\n          this.close()\n        })\n      },`\n      }\n    }\n\n  const methods = minxins[type]\n  if (methods) {\n    Object.keys(methods).forEach(key => {\n      list.push(methods[key])\n    })\n  }\n\n  return list\n}\n\nfunction buildData(conf, dataList) {\n  if (conf.vModel === undefined) return\n  let defaultValue\n  if (typeof (conf.defaultValue) === 'string' && !conf.multiple) {\n    defaultValue = `'${conf.defaultValue}'`\n  } else {\n    defaultValue = `${JSON.stringify(conf.defaultValue)}`\n  }\n  dataList.push(`${conf.vModel}: ${defaultValue},`)\n}\n\nfunction buildRules(conf, ruleList) {\n  if (conf.vModel === undefined) return\n  const rules = []\n  if (trigger[conf.tag]) {\n    if (conf.required) {\n      const type = isArray(conf.defaultValue) ? 'type: \\'array\\',' : ''\n      let message = isArray(conf.defaultValue) ? `请至少选择一个${conf.vModel}` : conf.placeholder\n      if (message === undefined) message = `${conf.label}不能为空`\n      rules.push(`{ required: true, ${type} message: '${message}', trigger: '${trigger[conf.tag]}' }`)\n    }\n    if (conf.regList && isArray(conf.regList)) {\n      conf.regList.forEach(item => {\n        if (item.pattern) {\n          rules.push(`{ pattern: ${eval(item.pattern)}, message: '${item.message}', trigger: '${trigger[conf.tag]}' }`)\n        }\n      })\n    }\n    ruleList.push(`${conf.vModel}: [${rules.join(',')}],`)\n  }\n}\n\nfunction buildOptions(conf, optionsList) {\n  if (conf.vModel === undefined) return\n  if (conf.dataType === 'dynamic') { conf.options = [] }\n  const str = `${conf.vModel}Options: ${JSON.stringify(conf.options)},`\n  optionsList.push(str)\n}\n\nfunction buildProps(conf, propsList) {\n  if (conf.dataType === 'dynamic') {\n    conf.valueKey !== 'value' && (conf.props.props.value = conf.valueKey)\n    conf.labelKey !== 'label' && (conf.props.props.label = conf.labelKey)\n    conf.childrenKey !== 'children' && (conf.props.props.children = conf.childrenKey)\n  }\n  const str = `${conf.vModel}Props: ${JSON.stringify(conf.props.props)},`\n  propsList.push(str)\n}\n\nfunction buildBeforeUpload(conf) {\n  const unitNum = units[conf.sizeUnit]; let rightSizeCode = ''; let acceptCode = ''; const\n    returnList = []\n  if (conf.fileSize) {\n    rightSizeCode = `let isRightSize = file.size / ${unitNum} < ${conf.fileSize}\n    if(!isRightSize){\n      this.$message.error('文件大小超过 ${conf.fileSize}${conf.sizeUnit}')\n    }`\n    returnList.push('isRightSize')\n  }\n  if (conf.accept) {\n    acceptCode = `let isAccept = new RegExp('${conf.accept}').test(file.type)\n    if(!isAccept){\n      this.$message.error('应该选择${conf.accept}类型的文件')\n    }`\n    returnList.push('isAccept')\n  }\n  const str = `${conf.vModel}BeforeUpload(file) {\n    ${rightSizeCode}\n    ${acceptCode}\n    return ${returnList.join('&&')}\n  },`\n  return returnList.length ? str : ''\n}\n\nfunction buildSubmitUpload(conf) {\n  const str = `submitUpload() {\n    this.$refs['${conf.vModel}'].submit()\n  },`\n  return str\n}\n\nfunction buildOptionMethod(methodName, model, methodList) {\n  const str = `${methodName}() {\n    // TODO 发起请求获取数据\n    this.${model}\n  },`\n  methodList.push(str)\n}\n\nfunction buildexport(conf, type, data, rules, selectOptions, uploadVar, props, methods) {\n  const str = `${exportDefault}{\n  ${inheritAttrs[type]}\n  components: {},\n  props: [],\n  data () {\n    return {\n      ${conf.formModel}: {\n        ${data}\n      },\n      ${conf.formRules}: {\n        ${rules}\n      },\n      ${uploadVar}\n      ${selectOptions}\n      ${props}\n    }\n  },\n  computed: {},\n  watch: {},\n  created () {},\n  mounted () {},\n  methods: {\n    ${methods}\n  }\n}`\n  return str\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,MAAM;AAC9B,SAASC,aAAa,EAAEC,SAAS,QAAQ,eAAe;AACxD,SAASC,OAAO,QAAQ,UAAU;AAElC,IAAMC,KAAK,GAAG;EACZC,EAAE,EAAE,MAAM;EACVC,EAAE,EAAE,aAAa;EACjBC,EAAE,EAAE;AACN,CAAC;AACD,IAAIC,UAAU;AACd,IAAMC,YAAY,GAAG;EACnBC,IAAI,EAAE,EAAE;EACRC,MAAM,EAAE;AACV,CAAC;AAGD,OAAO,SAASC,QAAQA,CAACC,IAAI,EAAEC,IAAI,EAAE;EACnCN,UAAU,GAAGK,IAAI,GAAGE,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACJ,IAAI,CAAC,CAAC;EACpD,IAAMK,QAAQ,GAAG,EAAE;EACnB,IAAMC,QAAQ,GAAG,EAAE;EACnB,IAAMC,WAAW,GAAG,EAAE;EACtB,IAAMC,SAAS,GAAG,EAAE;EACpB,IAAMC,UAAU,GAAGC,WAAW,CAACT,IAAI,CAAC;EACpC,IAAMU,aAAa,GAAG,EAAE;EAExBX,IAAI,CAACY,MAAM,CAACC,OAAO,CAAC,UAAAC,EAAE,EAAI;IACxBC,eAAe,CAACD,EAAE,EAAET,QAAQ,EAAEC,QAAQ,EAAEC,WAAW,EAAEE,UAAU,EAAED,SAAS,EAAEG,aAAa,CAAC;EAC5F,CAAC,CAAC;EAEF,IAAMK,MAAM,GAAGC,WAAW,CACxBjB,IAAI,EACJC,IAAI,EACJI,QAAQ,CAACa,IAAI,CAAC,IAAI,CAAC,EACnBZ,QAAQ,CAACY,IAAI,CAAC,IAAI,CAAC,EACnBX,WAAW,CAACW,IAAI,CAAC,IAAI,CAAC,EACtBP,aAAa,CAACO,IAAI,CAAC,IAAI,CAAC,EACxBV,SAAS,CAACU,IAAI,CAAC,IAAI,CAAC,EACpBT,UAAU,CAACS,IAAI,CAAC,IAAI,CACtB,CAAC;EACDvB,UAAU,GAAG,IAAI;EACjB,OAAOqB,MAAM;AACf;AAEA,SAASD,eAAeA,CAACD,EAAE,EAAET,QAAQ,EAAEC,QAAQ,EAAEC,WAAW,EAAEE,UAAU,EAAED,SAAS,EAAEG,aAAa,EAAE;EAClGQ,SAAS,CAACL,EAAE,EAAET,QAAQ,CAAC;EACvBe,UAAU,CAACN,EAAE,EAAER,QAAQ,CAAC;EAExB,IAAIQ,EAAE,CAACO,OAAO,IAAIP,EAAE,CAACO,OAAO,CAACC,MAAM,EAAE;IACnCC,YAAY,CAACT,EAAE,EAAEP,WAAW,CAAC;IAC7B,IAAIO,EAAE,CAACU,QAAQ,KAAK,SAAS,EAAE;MAC7B,IAAMC,KAAK,MAAAC,MAAA,CAAMZ,EAAE,CAACa,MAAM,YAAS;MACnC,IAAMN,OAAO,GAAGhC,SAAS,CAACoC,KAAK,CAAC;MAChCG,iBAAiB,OAAAF,MAAA,CAAOL,OAAO,GAAII,KAAK,EAAEhB,UAAU,CAAC;IACvD;EACF;EAEA,IAAIK,EAAE,CAACe,KAAK,IAAIf,EAAE,CAACe,KAAK,CAACA,KAAK,EAAE;IAC9BC,UAAU,CAAChB,EAAE,EAAEN,SAAS,CAAC;EAC3B;EAEA,IAAIM,EAAE,CAACiB,MAAM,IAAIjB,EAAE,CAACkB,GAAG,KAAK,WAAW,EAAE;IACvCrB,aAAa,CAACsB,IAAI,IAAAP,MAAA,CACbZ,EAAE,CAACa,MAAM,eAAAD,MAAA,CAAYZ,EAAE,CAACiB,MAAM,gBAAAL,MAAA,CAC/BZ,EAAE,CAACa,MAAM,kBACb,CAAC;IACDlB,UAAU,CAACwB,IAAI,CAACC,iBAAiB,CAACpB,EAAE,CAAC,CAAC;IACtC,IAAI,CAACA,EAAE,CAAC,aAAa,CAAC,EAAE;MACtBL,UAAU,CAACwB,IAAI,CAACE,iBAAiB,CAACrB,EAAE,CAAC,CAAC;IACxC;EACF;EAEA,IAAIA,EAAE,CAACsB,QAAQ,EAAE;IACftB,EAAE,CAACsB,QAAQ,CAACvB,OAAO,CAAC,UAAAwB,GAAG,EAAI;MACzBtB,eAAe,CAACsB,GAAG,EAAEhC,QAAQ,EAAEC,QAAQ,EAAEC,WAAW,EAAEE,UAAU,EAAED,SAAS,EAAEG,aAAa,CAAC;IAC7F,CAAC,CAAC;EACJ;AACF;AAEA,SAASD,WAAWA,CAACT,IAAI,EAAE;EACzB,IAAMqC,IAAI,GAAG,EAAE;EAAE,IACfC,OAAO,GAAG;IACR1C,IAAI,EAAEF,UAAU,CAAC6C,QAAQ,GAAG;MAC1BC,UAAU,yCAAAf,MAAA,CACI/B,UAAU,CAAC+C,OAAO,0HAI/B;MACDC,SAAS,wCAAAjB,MAAA,CACK/B,UAAU,CAAC+C,OAAO;IAElC,CAAC,GAAG,IAAI;IACR5C,MAAM,EAAE;MACN8C,MAAM,EAAE,cAAc;MACtBC,OAAO,sCAAAnB,MAAA,CACO/B,UAAU,CAAC+C,OAAO,+BAC/B;MACDI,KAAK,oEAEJ;MACDC,aAAa,4CAAArB,MAAA,CACC/B,UAAU,CAAC+C,OAAO;IAKlC;EACF,CAAC;EAEH,IAAMM,OAAO,GAAGT,OAAO,CAACtC,IAAI,CAAC;EAC7B,IAAI+C,OAAO,EAAE;IACXC,MAAM,CAACC,IAAI,CAACF,OAAO,CAAC,CAACnC,OAAO,CAAC,UAAAsC,GAAG,EAAI;MAClCb,IAAI,CAACL,IAAI,CAACe,OAAO,CAACG,GAAG,CAAC,CAAC;IACzB,CAAC,CAAC;EACJ;EAEA,OAAOb,IAAI;AACb;AAEA,SAASnB,SAASA,CAACnB,IAAI,EAAEK,QAAQ,EAAE;EACjC,IAAIL,IAAI,CAAC2B,MAAM,KAAKyB,SAAS,EAAE;EAC/B,IAAIC,YAAY;EAChB,IAAI,OAAQrD,IAAI,CAACqD,YAAa,KAAK,QAAQ,IAAI,CAACrD,IAAI,CAACsD,QAAQ,EAAE;IAC7DD,YAAY,OAAA3B,MAAA,CAAO1B,IAAI,CAACqD,YAAY,MAAG;EACzC,CAAC,MAAM;IACLA,YAAY,MAAA3B,MAAA,CAAMxB,IAAI,CAACE,SAAS,CAACJ,IAAI,CAACqD,YAAY,CAAC,CAAE;EACvD;EACAhD,QAAQ,CAAC4B,IAAI,IAAAP,MAAA,CAAI1B,IAAI,CAAC2B,MAAM,QAAAD,MAAA,CAAK2B,YAAY,MAAG,CAAC;AACnD;AAEA,SAASjC,UAAUA,CAACpB,IAAI,EAAEM,QAAQ,EAAE;EAClC,IAAIN,IAAI,CAAC2B,MAAM,KAAKyB,SAAS,EAAE;EAC/B,IAAMG,KAAK,GAAG,EAAE;EAChB,IAAIjE,OAAO,CAACU,IAAI,CAACgC,GAAG,CAAC,EAAE;IACrB,IAAIhC,IAAI,CAACwD,QAAQ,EAAE;MACjB,IAAMvD,IAAI,GAAGd,OAAO,CAACa,IAAI,CAACqD,YAAY,CAAC,GAAG,kBAAkB,GAAG,EAAE;MACjE,IAAII,OAAO,GAAGtE,OAAO,CAACa,IAAI,CAACqD,YAAY,CAAC,gDAAA3B,MAAA,CAAa1B,IAAI,CAAC2B,MAAM,IAAK3B,IAAI,CAAC0D,WAAW;MACrF,IAAID,OAAO,KAAKL,SAAS,EAAEK,OAAO,MAAA/B,MAAA,CAAM1B,IAAI,CAAC2D,KAAK,6BAAM;MACxDJ,KAAK,CAACtB,IAAI,sBAAAP,MAAA,CAAsBzB,IAAI,iBAAAyB,MAAA,CAAc+B,OAAO,mBAAA/B,MAAA,CAAgBpC,OAAO,CAACU,IAAI,CAACgC,GAAG,CAAC,QAAK,CAAC;IAClG;IACA,IAAIhC,IAAI,CAAC4D,OAAO,IAAIzE,OAAO,CAACa,IAAI,CAAC4D,OAAO,CAAC,EAAE;MACzC5D,IAAI,CAAC4D,OAAO,CAAC/C,OAAO,CAAC,UAAAgD,IAAI,EAAI;QAC3B,IAAIA,IAAI,CAACC,OAAO,EAAE;UAChBP,KAAK,CAACtB,IAAI,eAAAP,MAAA,CAAeqC,IAAI,CAACF,IAAI,CAACC,OAAO,CAAC,kBAAApC,MAAA,CAAemC,IAAI,CAACJ,OAAO,mBAAA/B,MAAA,CAAgBpC,OAAO,CAACU,IAAI,CAACgC,GAAG,CAAC,QAAK,CAAC;QAC/G;MACF,CAAC,CAAC;IACJ;IACA1B,QAAQ,CAAC2B,IAAI,IAAAP,MAAA,CAAI1B,IAAI,CAAC2B,MAAM,SAAAD,MAAA,CAAM6B,KAAK,CAACrC,IAAI,CAAC,GAAG,CAAC,OAAI,CAAC;EACxD;AACF;AAEA,SAASK,YAAYA,CAACvB,IAAI,EAAEO,WAAW,EAAE;EACvC,IAAIP,IAAI,CAAC2B,MAAM,KAAKyB,SAAS,EAAE;EAC/B,IAAIpD,IAAI,CAACwB,QAAQ,KAAK,SAAS,EAAE;IAAExB,IAAI,CAACqB,OAAO,GAAG,EAAE;EAAC;EACrD,IAAM2C,GAAG,MAAAtC,MAAA,CAAM1B,IAAI,CAAC2B,MAAM,eAAAD,MAAA,CAAYxB,IAAI,CAACE,SAAS,CAACJ,IAAI,CAACqB,OAAO,CAAC,MAAG;EACrEd,WAAW,CAAC0B,IAAI,CAAC+B,GAAG,CAAC;AACvB;AAEA,SAASlC,UAAUA,CAAC9B,IAAI,EAAEQ,SAAS,EAAE;EACnC,IAAIR,IAAI,CAACwB,QAAQ,KAAK,SAAS,EAAE;IAC/BxB,IAAI,CAACiE,QAAQ,KAAK,OAAO,KAAKjE,IAAI,CAAC6B,KAAK,CAACA,KAAK,CAACqC,KAAK,GAAGlE,IAAI,CAACiE,QAAQ,CAAC;IACrEjE,IAAI,CAACmE,QAAQ,KAAK,OAAO,KAAKnE,IAAI,CAAC6B,KAAK,CAACA,KAAK,CAAC8B,KAAK,GAAG3D,IAAI,CAACmE,QAAQ,CAAC;IACrEnE,IAAI,CAACoE,WAAW,KAAK,UAAU,KAAKpE,IAAI,CAAC6B,KAAK,CAACA,KAAK,CAACO,QAAQ,GAAGpC,IAAI,CAACoE,WAAW,CAAC;EACnF;EACA,IAAMJ,GAAG,MAAAtC,MAAA,CAAM1B,IAAI,CAAC2B,MAAM,aAAAD,MAAA,CAAUxB,IAAI,CAACE,SAAS,CAACJ,IAAI,CAAC6B,KAAK,CAACA,KAAK,CAAC,MAAG;EACvErB,SAAS,CAACyB,IAAI,CAAC+B,GAAG,CAAC;AACrB;AAEA,SAAS9B,iBAAiBA,CAAClC,IAAI,EAAE;EAC/B,IAAMqE,OAAO,GAAG9E,KAAK,CAACS,IAAI,CAACsE,QAAQ,CAAC;EAAE,IAAIC,aAAa,GAAG,EAAE;EAAE,IAAIC,UAAU,GAAG,EAAE;EAAE,IACjFC,UAAU,GAAG,EAAE;EACjB,IAAIzE,IAAI,CAAC0E,QAAQ,EAAE;IACjBH,aAAa,oCAAA7C,MAAA,CAAoC2C,OAAO,SAAA3C,MAAA,CAAM1B,IAAI,CAAC0E,QAAQ,+FAAAhD,MAAA,CAE3C1B,IAAI,CAAC0E,QAAQ,EAAAhD,MAAA,CAAG1B,IAAI,CAACsE,QAAQ,cAC3D;IACFG,UAAU,CAACxC,IAAI,CAAC,aAAa,CAAC;EAChC;EACA,IAAIjC,IAAI,CAAC2E,MAAM,EAAE;IACfH,UAAU,iCAAA9C,MAAA,CAAiC1B,IAAI,CAAC2E,MAAM,iGAAAjD,MAAA,CAEzB1B,IAAI,CAAC2E,MAAM,4CACtC;IACFF,UAAU,CAACxC,IAAI,CAAC,UAAU,CAAC;EAC7B;EACA,IAAM+B,GAAG,MAAAtC,MAAA,CAAM1B,IAAI,CAAC2B,MAAM,gCAAAD,MAAA,CACtB6C,aAAa,YAAA7C,MAAA,CACb8C,UAAU,mBAAA9C,MAAA,CACH+C,UAAU,CAACvD,IAAI,CAAC,IAAI,CAAC,WAC7B;EACH,OAAOuD,UAAU,CAACnD,MAAM,GAAG0C,GAAG,GAAG,EAAE;AACrC;AAEA,SAAS7B,iBAAiBA,CAACnC,IAAI,EAAE;EAC/B,IAAMgE,GAAG,wCAAAtC,MAAA,CACO1B,IAAI,CAAC2B,MAAM,sBACxB;EACH,OAAOqC,GAAG;AACZ;AAEA,SAASpC,iBAAiBA,CAACgD,UAAU,EAAEnD,KAAK,EAAEhB,UAAU,EAAE;EACxD,IAAMuD,GAAG,MAAAtC,MAAA,CAAMkD,UAAU,mFAAAlD,MAAA,CAEhBD,KAAK,WACX;EACHhB,UAAU,CAACwB,IAAI,CAAC+B,GAAG,CAAC;AACtB;AAEA,SAAS/C,WAAWA,CAACjB,IAAI,EAAEC,IAAI,EAAE4E,IAAI,EAAEtB,KAAK,EAAEuB,aAAa,EAAEC,SAAS,EAAElD,KAAK,EAAEmB,OAAO,EAAE;EACtF,IAAMgB,GAAG,MAAAtC,MAAA,CAAMtC,aAAa,WAAAsC,MAAA,CAC1B9B,YAAY,CAACK,IAAI,CAAC,0EAAAyB,MAAA,CAKd1B,IAAI,CAACgF,SAAS,mBAAAtD,MAAA,CACZmD,IAAI,wBAAAnD,MAAA,CAEN1B,IAAI,CAACiF,SAAS,mBAAAvD,MAAA,CACZ6B,KAAK,wBAAA7B,MAAA,CAEPqD,SAAS,cAAArD,MAAA,CACToD,aAAa,cAAApD,MAAA,CACbG,KAAK,0GAAAH,MAAA,CAQPsB,OAAO,aAEX;EACA,OAAOgB,GAAG;AACZ", "ignoreList": []}]}