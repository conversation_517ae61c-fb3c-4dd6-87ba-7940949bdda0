{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/flowable/task/record/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/flowable/task/record/index.vue", "mtime": 1668865255000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGZsb3dSZWNvcmQgfSBmcm9tICJAL2FwaS9mbG93YWJsZS9maW5pc2hlZCI7CmltcG9ydCBQYXJzZXIgZnJvbSAnQC9jb21wb25lbnRzL3BhcnNlci9QYXJzZXInOwppbXBvcnQgeyBkZWZpbml0aW9uU3RhcnRCeUtleSwgZ2V0UHJvY2Vzc1ZhcmlhYmxlcywgcmVhZFhtbEJ5S2V5LCBnZXRGbG93Vmlld2VyIGFzIF9nZXRGbG93Vmlld2VyIH0gZnJvbSAiQC9hcGkvZmxvd2FibGUvZGVmaW5pdGlvbiI7CmltcG9ydCB7IGNvbXBsZXRlLCByZWplY3RUYXNrLCByZXR1cm5MaXN0LCByZXR1cm5UYXNrLCBnZXROZXh0Rmxvd05vZGUgYXMgX2dldE5leHRGbG93Tm9kZSwgZGVsZWdhdGUgfSBmcm9tICJAL2FwaS9mbG93YWJsZS90b2RvIjsKaW1wb3J0IGZsb3cgZnJvbSAnQC92aWV3cy9mbG93YWJsZS90YXNrL3JlY29yZC9mbG93JzsKaW1wb3J0IHsgdHJlZXNlbGVjdCB9IGZyb20gIkAvYXBpL3N5c3RlbS9kZXB0IjsKaW1wb3J0ICJAcmlvcGhhZS92dWUtdHJlZXNlbGVjdC9kaXN0L3Z1ZS10cmVlc2VsZWN0LmNzcyI7CmltcG9ydCBUcmVlc2VsZWN0IGZyb20gIkByaW9waGFlL3Z1ZS10cmVlc2VsZWN0IjsKaW1wb3J0IHsgbGlzdFVzZXIgfSBmcm9tICJAL2FwaS9zeXN0ZW0vdXNlciI7CmltcG9ydCBtb21lbnQgZnJvbSAnbW9tZW50JzsKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICJSZWNvcmQiLAogIGNvbXBvbmVudHM6IHsKICAgIFBhcnNlcjogUGFyc2VyLAogICAgZmxvdzogZmxvdywKICAgIFRyZWVzZWxlY3Q6IFRyZWVzZWxlY3QKICB9LAogIHByb3BzOiB7CiAgICBwcm9jRGVmS2V5OiB7CiAgICAgIHR5cGU6IFN0cmluZywKICAgICAgZGVmYXVsdDogdW5kZWZpbmVkCiAgICB9LAogICAgdGFza0lkOiB7CiAgICAgIHR5cGU6IFN0cmluZywKICAgICAgZGVmYXVsdDogdW5kZWZpbmVkCiAgICB9LAogICAgcHJvY0luc0lkOiB7CiAgICAgIHR5cGU6IFN0cmluZywKICAgICAgZGVmYXVsdDogdW5kZWZpbmVkCiAgICB9CiAgfSwKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgLy8g5qih5Z6LeG1s5pWw5o2uCiAgICAgIHhtbERhdGE6ICIiLAogICAgICB0YXNrTGlzdDogW10sCiAgICAgIC8vIOmDqOmXqOWQjeensAogICAgICBkZXB0TmFtZTogdW5kZWZpbmVkLAogICAgICAvLyDpg6jpl6jmoJHpgInpobkKICAgICAgZGVwdE9wdGlvbnM6IHVuZGVmaW5lZCwKICAgICAgLy8g55So5oi36KGo5qC85pWw5o2uCiAgICAgIHVzZXJMaXN0OiBudWxsLAogICAgICBkZWZhdWx0UHJvcHM6IHsKICAgICAgICBjaGlsZHJlbjogImNoaWxkcmVuIiwKICAgICAgICBsYWJlbDogImxhYmVsIgogICAgICB9LAogICAgICAvLyDmn6Xor6Llj4LmlbAKICAgICAgcXVlcnlQYXJhbXM6IHsKICAgICAgICBkZXB0SWQ6IHVuZGVmaW5lZAogICAgICB9LAogICAgICAvLyDpga7nvanlsYIKICAgICAgbG9hZGluZzogdHJ1ZSwKICAgICAgZmxvd1JlY29yZExpc3Q6IFtdLAogICAgICAvLyDmtYHnqIvmtYHovazmlbDmja4KICAgICAgZm9ybUNvbmZDb3B5OiB7fSwKICAgICAgc3JjOiBudWxsLAogICAgICBydWxlczoge30sCiAgICAgIC8vIOihqOWNleagoemqjAogICAgICB2YXJpYWJsZXNGb3JtOiB7fSwKICAgICAgLy8g5rWB56iL5Y+Y6YeP5pWw5o2uCiAgICAgIHRhc2tGb3JtOiB7CiAgICAgICAgcmV0dXJuVGFza1Nob3c6IGZhbHNlLAogICAgICAgIC8vIOaYr+WQpuWxleekuuWbnumAgOihqOWNlQogICAgICAgIGRlbGVnYXRlVGFza1Nob3c6IGZhbHNlLAogICAgICAgIC8vIOaYr+WQpuWxleekuuWbnumAgOihqOWNlQogICAgICAgIGRlZmF1bHRUYXNrU2hvdzogdHJ1ZSwKICAgICAgICAvLyDpu5jorqTlpITnkIYKICAgICAgICBzZW5kVXNlclNob3c6IGZhbHNlLAogICAgICAgIC8vIOWuoeaJueeUqOaItwogICAgICAgIG11bHRpcGxlOiBmYWxzZSwKICAgICAgICBjb21tZW50OiAiIiwKICAgICAgICAvLyDmhI/op4HlhoXlrrkKICAgICAgICBwcm9jSW5zSWQ6ICIiLAogICAgICAgIC8vIOa1geeoi+WunuS+i+e8luWPtwogICAgICAgIGluc3RhbmNlSWQ6ICIiLAogICAgICAgIC8vIOa1geeoi+WunuS+i+e8luWPtwogICAgICAgIHRhc2tJZDogIiIsCiAgICAgICAgLy8g5rWB56iL5Lu75Yqh57yW5Y+3CiAgICAgICAgcHJvY0RlZktleTogIiIsCiAgICAgICAgLy8g5rWB56iL57yW5Y+3CiAgICAgICAgdmFyczogIiIsCiAgICAgICAgdGFyZ2V0S2V5OiAiIgogICAgICB9LAogICAgICB1c2VyRGF0YUxpc3Q6IFtdLAogICAgICAvLyDmtYHnqIvlgJnpgInkuroKICAgICAgYXNzaWduZWU6IG51bGwsCiAgICAgIGZvcm1Db25mOiB7fSwKICAgICAgLy8g6buY6K6k6KGo5Y2V5pWw5o2uCiAgICAgIGZvcm1Db25mT3BlbjogZmFsc2UsCiAgICAgIC8vIOaYr+WQpuWKoOi9vem7mOiupOihqOWNleaVsOaNrgogICAgICB2YXJpYWJsZXM6IFtdLAogICAgICAvLyDmtYHnqIvlj5jph4/mlbDmja4KICAgICAgdmFyaWFibGVzRGF0YToge30sCiAgICAgIC8vIOa1geeoi+WPmOmHj+aVsOaNrgogICAgICB2YXJpYWJsZU9wZW46IGZhbHNlLAogICAgICAvLyDmmK/lkKbliqDovb3mtYHnqIvlj5jph4/mlbDmja4KICAgICAgcmV0dXJuVGFza0xpc3Q6IFtdLAogICAgICAvLyDlm57pgIDliJfooajmlbDmja4KICAgICAgZmluaXNoZWQ6ICdmYWxzZScsCiAgICAgIGNvbXBsZXRlVGl0bGU6IG51bGwsCiAgICAgIGNvbXBsZXRlT3BlbjogZmFsc2UsCiAgICAgIHJldHVyblRpdGxlOiBudWxsLAogICAgICByZXR1cm5PcGVuOiBmYWxzZSwKICAgICAgcmVqZWN0T3BlbjogZmFsc2UsCiAgICAgIHJlamVjdFRpdGxlOiBudWxsLAogICAgICB1c2VyRGF0YTogW10KICAgIH07CiAgfSwKICBjcmVhdGVkOiBmdW5jdGlvbiBjcmVhdGVkKCkgewogICAgY29uc29sZS5sb2coIj09PT09PT09cmVjb3JkPT09PT09PT1jcmVhdGVkPT4+PiIpOwogICAgY29uc29sZS5sb2codGhpcy5fcHJvcHMpOwogICAgdmFyIF90aGlzJF9wcm9wcyA9IHRoaXMuX3Byb3BzLAogICAgICB0YXNrSWQgPSBfdGhpcyRfcHJvcHMudGFza0lkLAogICAgICBwcm9jRGVmS2V5ID0gX3RoaXMkX3Byb3BzLnByb2NEZWZLZXksCiAgICAgIHByb2NJbnNJZCA9IF90aGlzJF9wcm9wcy5wcm9jSW5zSWQsCiAgICAgIGZpbmlzaGVkID0gX3RoaXMkX3Byb3BzLmZpbmlzaGVkOwogICAgdGhpcy50YXNrRm9ybS50YXNrSWQgPSB0YXNrSWQ7CiAgICB0aGlzLnRhc2tGb3JtLnByb2NJbnNJZCA9IHByb2NJbnNJZDsKICAgIHRoaXMudGFza0Zvcm0uaW5zdGFuY2VJZCA9IHByb2NJbnNJZDsKICAgIC8vIOWIneWni+WMluihqOWNlQogICAgdGhpcy50YXNrRm9ybS5wcm9jRGVmS2V5ID0gcHJvY0RlZktleTsKICAgIHRoaXMuZmluaXNoZWQgPSBmaW5pc2hlZDsKICAgIC8vIOWbnuaYvua1geeoi+iusOW9lQogICAgdGhpcy5nZXRGbG93Vmlld2VyKHRoaXMudGFza0Zvcm0ucHJvY0luc0lkLCB0aGlzLnRhc2tGb3JtLnByb2NEZWZLZXkpOwogICAgLy8g5rWB56iL5Lu75Yqh6YeN6I635Y+W5Y+Y6YeP6KGo5Y2VCiAgICBpZiAodGhpcy50YXNrRm9ybS50YXNrSWQpIHsKICAgICAgdGhpcy5wcm9jZXNzVmFyaWFibGVzKHRoaXMudGFza0Zvcm0udGFza0lkKTsKICAgICAgdGhpcy5nZXROZXh0Rmxvd05vZGUodGhpcy50YXNrRm9ybS50YXNrSWQpOwogICAgfQogICAgdGhpcy5nZXRGbG93UmVjb3JkTGlzdCh0aGlzLnRhc2tGb3JtLnByb2NJbnNJZCk7CiAgICB0aGlzLmZpbmlzaGVkID0gdGhpcy4kcm91dGUucXVlcnkgJiYgdGhpcy4kcm91dGUucXVlcnkuZmluaXNoZWQ7CiAgfSwKICBhY3RpdmF0ZWQ6IGZ1bmN0aW9uIGFjdGl2YXRlZCgpIHsKICAgIGNvbnNvbGUubG9nKCI9PT09PT09PXJlY29yZD09PT09PT09YWN0aXZhdGVkPT4+PiIpOwogICAgdmFyIF90aGlzJF9wcm9wczIgPSB0aGlzLl9wcm9wcywKICAgICAgdGFza0lkID0gX3RoaXMkX3Byb3BzMi50YXNrSWQsCiAgICAgIHByb2NEZWZLZXkgPSBfdGhpcyRfcHJvcHMyLnByb2NEZWZLZXksCiAgICAgIHByb2NJbnNJZCA9IF90aGlzJF9wcm9wczIucHJvY0luc0lkLAogICAgICBmaW5pc2hlZCA9IF90aGlzJF9wcm9wczIuZmluaXNoZWQ7CiAgICBjb25zb2xlLmxvZyh0aGlzLl9wcm9wcyk7CiAgICB0aGlzLnRhc2tGb3JtLnRhc2tJZCA9IHRhc2tJZDsKICAgIHRoaXMudGFza0Zvcm0ucHJvY0luc0lkID0gcHJvY0luc0lkOwogICAgdGhpcy50YXNrRm9ybS5pbnN0YW5jZUlkID0gcHJvY0luc0lkOwogICAgLy8g5Yid5aeL5YyW6KGo5Y2VCiAgICB0aGlzLnRhc2tGb3JtLnByb2NEZWZLZXkgPSBwcm9jRGVmS2V5OwogICAgdGhpcy5maW5pc2hlZCA9IGZpbmlzaGVkOwogICAgLy8g5Zue5pi+5rWB56iL6K6w5b2VCiAgICB0aGlzLmdldEZsb3dWaWV3ZXIodGhpcy50YXNrRm9ybS5wcm9jSW5zSWQsIHRoaXMudGFza0Zvcm0ucHJvY0RlZktleSk7CiAgICAvLyDmtYHnqIvku7vliqHph43ojrflj5blj5jph4/ooajljZUKICAgIGlmICh0aGlzLnRhc2tGb3JtLnRhc2tJZCkgewogICAgICB0aGlzLnByb2Nlc3NWYXJpYWJsZXModGhpcy50YXNrRm9ybS50YXNrSWQpOwogICAgICB0aGlzLmdldE5leHRGbG93Tm9kZSh0aGlzLnRhc2tGb3JtLnRhc2tJZCk7CiAgICB9CiAgICB0aGlzLmdldEZsb3dSZWNvcmRMaXN0KHRoaXMudGFza0Zvcm0ucHJvY0luc0lkKTsKICAgIHRoaXMuZmluaXNoZWQgPSB0aGlzLiRyb3V0ZS5xdWVyeSAmJiB0aGlzLiRyb3V0ZS5xdWVyeS5maW5pc2hlZDsKICB9LAogIG1vdW50ZWQ6IGZ1bmN0aW9uIG1vdW50ZWQoKSB7CiAgICAvLyAvLyDooajljZXmlbDmja7lm57loavvvIzmqKHmi5/lvILmraXor7fmsYLlnLrmma8KICAgIC8vIHNldFRpbWVvdXQoKCkgPT4gewogICAgLy8gICAvLyDor7fmsYLlm57mnaXnmoTooajljZXmlbDmja4KICAgIC8vICAgY29uc3QgZGF0YSA9IHsKICAgIC8vICAgICBmaWVsZDEwMjogJzE4ODM2NjYyNTU1JwogICAgLy8gICB9CiAgICAvLyAgIC8vIOWbnuWhq+aVsOaNrgogICAgLy8gICB0aGlzLmZpbGxGb3JtRGF0YSh0aGlzLmZvcm1Db25mLCBkYXRhKQogICAgLy8gICAvLyDmm7TmlrDooajljZUKICAgIC8vICAgdGhpcy5rZXkgPSArbmV3IERhdGUoKS5nZXRUaW1lKCkKICAgIC8vIH0sIDEwMDApCiAgfSwKICBtZXRob2RzOiB7CiAgICAvKiog5p+l6K+i6YOo6Zeo5LiL5ouJ5qCR57uT5p6EICovZ2V0VHJlZXNlbGVjdDogZnVuY3Rpb24gZ2V0VHJlZXNlbGVjdCgpIHsKICAgICAgdmFyIF90aGlzID0gdGhpczsKICAgICAgdHJlZXNlbGVjdCgpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgX3RoaXMuZGVwdE9wdGlvbnMgPSByZXNwb25zZS5kYXRhOwogICAgICB9KTsKICAgIH0sCiAgICAvKiog5p+l6K+i55So5oi35YiX6KGoICovZ2V0TGlzdDogZnVuY3Rpb24gZ2V0TGlzdCgpIHsKICAgICAgdmFyIF90aGlzMiA9IHRoaXM7CiAgICAgIGxpc3RVc2VyKHRoaXMuYWRkRGF0ZVJhbmdlKHRoaXMucXVlcnlQYXJhbXMsIHRoaXMuZGF0ZVJhbmdlKSkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICBfdGhpczIudXNlckxpc3QgPSByZXNwb25zZS5yb3dzOwogICAgICAgIF90aGlzMi50b3RhbCA9IHJlc3BvbnNlLnRvdGFsOwogICAgICB9KTsKICAgIH0sCiAgICAvLyDnrZvpgInoioLngrkKICAgIGZpbHRlck5vZGU6IGZ1bmN0aW9uIGZpbHRlck5vZGUodmFsdWUsIGRhdGEpIHsKICAgICAgaWYgKCF2YWx1ZSkgcmV0dXJuIHRydWU7CiAgICAgIHJldHVybiBkYXRhLmxhYmVsLmluZGV4T2YodmFsdWUpICE9PSAtMTsKICAgIH0sCiAgICAvLyDoioLngrnljZXlh7vkuovku7YKICAgIGhhbmRsZU5vZGVDbGljazogZnVuY3Rpb24gaGFuZGxlTm9kZUNsaWNrKGRhdGEpIHsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5kZXB0SWQgPSBkYXRhLmlkOwogICAgICB0aGlzLmdldExpc3QoKTsKICAgIH0sCiAgICAvKiogeG1sIOaWh+S7tiAqL2dldE1vZGVsRGV0YWlsOiBmdW5jdGlvbiBnZXRNb2RlbERldGFpbChkZXBsb3lLZXkpIHsKICAgICAgdmFyIF90aGlzMyA9IHRoaXM7CiAgICAgIC8vIOWPkemAgeivt+axgu+8jOiOt+WPlnhtbAogICAgICByZWFkWG1sQnlLZXkoZGVwbG95S2V5KS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICBfdGhpczMueG1sRGF0YSA9IHJlcy5kYXRhOwogICAgICB9KTsKICAgIH0sCiAgICBnZXRGbG93Vmlld2VyOiBmdW5jdGlvbiBnZXRGbG93Vmlld2VyKHByb2NJbnNJZCwgZGVwbG95S2V5KSB7CiAgICAgIHZhciBfdGhpczQgPSB0aGlzOwogICAgICBfZ2V0Rmxvd1ZpZXdlcihwcm9jSW5zSWQpLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgIF90aGlzNC50YXNrTGlzdCA9IHJlcy5kYXRhOwogICAgICAgIF90aGlzNC5nZXRNb2RlbERldGFpbChkZXBsb3lLZXkpOwogICAgICB9KTsKICAgIH0sCiAgICBzZXRJY29uOiBmdW5jdGlvbiBzZXRJY29uKHZhbCkgewogICAgICBpZiAodmFsKSB7CiAgICAgICAgcmV0dXJuICJlbC1pY29uLWNoZWNrIjsKICAgICAgfSBlbHNlIHsKICAgICAgICByZXR1cm4gImVsLWljb24tdGltZSI7CiAgICAgIH0KICAgIH0sCiAgICBzZXRDb2xvcjogZnVuY3Rpb24gc2V0Q29sb3IodmFsKSB7CiAgICAgIGlmICh2YWwpIHsKICAgICAgICByZXR1cm4gIiMyYmM0MTgiOwogICAgICB9IGVsc2UgewogICAgICAgIHJldHVybiAiI2IzYmRiYiI7CiAgICAgIH0KICAgIH0sCiAgICAvLyDlpJrpgInmoYbpgInkuK3mlbDmja4KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZTogZnVuY3Rpb24gaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgewogICAgICB0aGlzLnVzZXJEYXRhID0gc2VsZWN0aW9uOwogICAgICB2YXIgdmFsID0gc2VsZWN0aW9uLm1hcChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgIHJldHVybiBpdGVtLnVzZXJJZDsKICAgICAgfSlbMF07CiAgICAgIGlmICh2YWwgaW5zdGFuY2VvZiBBcnJheSkgewogICAgICAgIHRoaXMudGFza0Zvcm0udmFsdWVzID0gewogICAgICAgICAgImFwcHJvdmFsIjogdmFsLmpvaW4oJywnKQogICAgICAgIH07CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy50YXNrRm9ybS52YWx1ZXMgPSB7CiAgICAgICAgICAiYXBwcm92YWwiOiB2YWwKICAgICAgICB9OwogICAgICB9CiAgICB9LAogICAgLy8g5YWz6Zet5qCH562+CiAgICBoYW5kbGVDbG9zZTogZnVuY3Rpb24gaGFuZGxlQ2xvc2UodGFnKSB7CiAgICAgIHRoaXMudXNlckRhdGEuc3BsaWNlKHRoaXMudXNlckRhdGEuaW5kZXhPZih0YWcpLCAxKTsKICAgIH0sCiAgICAvKiog5rWB56iL5Y+Y6YeP6LWL5YC8ICovaGFuZGxlQ2hlY2tDaGFuZ2U6IGZ1bmN0aW9uIGhhbmRsZUNoZWNrQ2hhbmdlKHZhbCkgewogICAgICBpZiAodmFsIGluc3RhbmNlb2YgQXJyYXkpIHsKICAgICAgICB0aGlzLnRhc2tGb3JtLnZhbHVlcyA9IHsKICAgICAgICAgICJhcHByb3ZhbCI6IHZhbC5qb2luKCcsJykKICAgICAgICB9OwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMudGFza0Zvcm0udmFsdWVzID0gewogICAgICAgICAgImFwcHJvdmFsIjogdmFsCiAgICAgICAgfTsKICAgICAgfQogICAgfSwKICAgIC8qKiDmtYHnqIvmtYHovazorrDlvZUgKi9nZXRGbG93UmVjb3JkTGlzdDogZnVuY3Rpb24gZ2V0Rmxvd1JlY29yZExpc3QocHJvY0luc0lkKSB7CiAgICAgIHZhciBfdGhpczUgPSB0aGlzOwogICAgICB2YXIgcGFyYW1zID0gewogICAgICAgIHByb2NJbnNJZDogcHJvY0luc0lkCiAgICAgIH07CiAgICAgIGZsb3dSZWNvcmQocGFyYW1zKS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICBfdGhpczUuZmxvd1JlY29yZExpc3QgPSByZXMuZGF0YS5mbG93TGlzdDsKICAgICAgICAvLyDmtYHnqIvov4fnqIvkuK3kuI3lrZjlnKjliJ3lp4vljJbooajljZUg55u05o6l6K+75Y+W55qE5rWB56iL5Y+Y6YeP5Lit5a2Y5YKo55qE6KGo5Y2V5YC8CiAgICAgICAgaWYgKHJlcy5kYXRhLmZvcm1EYXRhKSB7CiAgICAgICAgICBfdGhpczUuZm9ybUNvbmYgPSByZXMuZGF0YS5mb3JtRGF0YTsKICAgICAgICAgIF90aGlzNS5mb3JtQ29uZk9wZW4gPSB0cnVlOwogICAgICAgIH0KICAgICAgfSkuY2F0Y2goZnVuY3Rpb24gKHJlcykgewogICAgICAgIF90aGlzNS5nb0JhY2soKTsKICAgICAgfSk7CiAgICB9LAogICAgZmlsbEZvcm1EYXRhOiBmdW5jdGlvbiBmaWxsRm9ybURhdGEoZm9ybSwgZGF0YSkgewogICAgICBmb3JtLmZpZWxkcy5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgdmFyIHZhbCA9IGRhdGFbaXRlbS5fX3ZNb2RlbF9fXTsKICAgICAgICBpZiAodmFsKSB7CiAgICAgICAgICBpdGVtLl9fY29uZmlnX18uZGVmYXVsdFZhbHVlID0gdmFsOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLyoqIOiOt+WPlua1geeoi+WPmOmHj+WGheWuuSAqL3Byb2Nlc3NWYXJpYWJsZXM6IGZ1bmN0aW9uIHByb2Nlc3NWYXJpYWJsZXModGFza0lkKSB7CiAgICAgIHZhciBfdGhpczYgPSB0aGlzOwogICAgICBpZiAodGFza0lkKSB7CiAgICAgICAgLy8g5o+Q5Lqk5rWB56iL55Sz6K+35pe25aGr5YaZ55qE6KGo5Y2V5a2Y5YWl5LqG5rWB56iL5Y+Y6YeP5Lit5ZCO57ut5Lu75Yqh5aSE55CG5pe26ZyA6KaB5bGV56S6CiAgICAgICAgZ2V0UHJvY2Vzc1ZhcmlhYmxlcyh0YXNrSWQpLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgICAgLy8gdGhpcy52YXJpYWJsZXMgPSByZXMuZGF0YS52YXJpYWJsZXM7CiAgICAgICAgICBfdGhpczYudmFyaWFibGVzRGF0YSA9IHJlcy5kYXRhLnZhcmlhYmxlczsKICAgICAgICAgIF90aGlzNi52YXJpYWJsZU9wZW4gPSB0cnVlOwogICAgICAgIH0pOwogICAgICB9CiAgICB9LAogICAgLyoqIOagueaNruW9k+WJjeS7u+WKoeaIluiAhea1geeoi+iuvuiuoemFjee9rueahOS4i+S4gOatpeiKgueCuSAqL2dldE5leHRGbG93Tm9kZTogZnVuY3Rpb24gZ2V0TmV4dEZsb3dOb2RlKHRhc2tJZCkgewogICAgICB2YXIgX3RoaXM3ID0gdGhpczsKICAgICAgLy8g5qC55o2u5b2T5YmN5Lu75Yqh5oiW6ICF5rWB56iL6K6+6K6h6YWN572u55qE5LiL5LiA5q2l6IqC54K5IHRvZG8g5pqC5pe25pyq5raJ5Y+K5Yiw6ICD6JmR572R5YWz44CB6KGo6L6+5byP5ZKM5aSa6IqC54K55oOF5Ya1CiAgICAgIHZhciBwYXJhbXMgPSB7CiAgICAgICAgdGFza0lkOiB0YXNrSWQKICAgICAgfTsKICAgICAgX2dldE5leHRGbG93Tm9kZShwYXJhbXMpLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgIHZhciBkYXRhID0gcmVzLmRhdGE7CiAgICAgICAgaWYgKGRhdGEpIHsKICAgICAgICAgIGlmIChkYXRhLnR5cGUgPT09ICdhc3NpZ25lZScpIHsKICAgICAgICAgICAgX3RoaXM3LnVzZXJEYXRhTGlzdCA9IHJlcy5kYXRhLnVzZXJMaXN0OwogICAgICAgICAgfSBlbHNlIGlmIChkYXRhLnR5cGUgPT09ICdjYW5kaWRhdGVVc2VycycpIHsKICAgICAgICAgICAgX3RoaXM3LnVzZXJEYXRhTGlzdCA9IHJlcy5kYXRhLnVzZXJMaXN0OwogICAgICAgICAgICBfdGhpczcudGFza0Zvcm0ubXVsdGlwbGUgPSB0cnVlOwogICAgICAgICAgfSBlbHNlIGlmIChkYXRhLnR5cGUgPT09ICdjYW5kaWRhdGVHcm91cHMnKSB7CiAgICAgICAgICAgIHJlcy5kYXRhLnJvbGVMaXN0LmZvckVhY2goZnVuY3Rpb24gKHJvbGUpIHsKICAgICAgICAgICAgICByb2xlLnVzZXJJZCA9IHJvbGUucm9sZUlkOwogICAgICAgICAgICAgIHJvbGUubmlja05hbWUgPSByb2xlLnJvbGVOYW1lOwogICAgICAgICAgICB9KTsKICAgICAgICAgICAgX3RoaXM3LnVzZXJEYXRhTGlzdCA9IHJlcy5kYXRhLnJvbGVMaXN0OwogICAgICAgICAgICBfdGhpczcudGFza0Zvcm0ubXVsdGlwbGUgPSBmYWxzZTsKICAgICAgICAgIH0gZWxzZSBpZiAoZGF0YS50eXBlID09PSAnbXVsdGlJbnN0YW5jZScpIHsKICAgICAgICAgICAgX3RoaXM3LnVzZXJEYXRhTGlzdCA9IHJlcy5kYXRhLnVzZXJMaXN0OwogICAgICAgICAgICBfdGhpczcudGFza0Zvcm0ubXVsdGlwbGUgPSB0cnVlOwogICAgICAgICAgfQogICAgICAgICAgX3RoaXM3LnRhc2tGb3JtLnNlbmRVc2VyU2hvdyA9IHRydWU7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvKiog5a6h5om55Lu75Yqh6YCJ5oupICovaGFuZGxlQ29tcGxldGU6IGZ1bmN0aW9uIGhhbmRsZUNvbXBsZXRlKCkgewogICAgICB0aGlzLmNvbXBsZXRlT3BlbiA9IHRydWU7CiAgICAgIHRoaXMuY29tcGxldGVUaXRsZSA9ICLlrqHmibnmtYHnqIsiOwogICAgICAvL3RoaXMuZ2V0VHJlZXNlbGVjdCgpOwogICAgfSwKICAgIC8qKiDlrqHmibnku7vliqEgKi90YXNrQ29tcGxldGU6IGZ1bmN0aW9uIHRhc2tDb21wbGV0ZShjb21tZW50KSB7CiAgICAgIHZhciBfdGhpczggPSB0aGlzOwogICAgICAvLyBpZiAoIXRoaXMudGFza0Zvcm0udmFsdWVzKXsKICAgICAgLy8gICB0aGlzLm1zZ0Vycm9yKCLor7fpgInmi6nmtYHnqIvmjqXmlLbkurrlkZgiKTsKICAgICAgLy8gICByZXR1cm47CiAgICAgIC8vIH0KICAgICAgaWYgKGNvbW1lbnQgJiYgdHlwZW9mIGNvbW1lbnQgPT0gJ3N0cmluZycgJiYgY29tbWVudC5jb25zdHJ1Y3RvciA9PSBTdHJpbmcpIHsKICAgICAgICB0aGlzLnRhc2tGb3JtLmNvbW1lbnQgPSBjb21tZW50OwogICAgICB9CiAgICAgIGlmICghdGhpcy50YXNrRm9ybS5jb21tZW50KSB7CiAgICAgICAgdGhpcy5tc2dFcnJvcigi6K+36L6T5YWl5a6h5om55oSP6KeBIik7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIGNvbXBsZXRlKHRoaXMudGFza0Zvcm0pLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgX3RoaXM4Lm1zZ1N1Y2Nlc3MocmVzcG9uc2UubXNnKTsKICAgICAgICBfdGhpczguZ29CYWNrKCk7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDlp5TmtL7ku7vliqEgKi9oYW5kbGVEZWxlZ2F0ZTogZnVuY3Rpb24gaGFuZGxlRGVsZWdhdGUoKSB7CiAgICAgIHRoaXMudGFza0Zvcm0uZGVsZWdhdGVUYXNrU2hvdyA9IHRydWU7CiAgICAgIHRoaXMudGFza0Zvcm0uZGVmYXVsdFRhc2tTaG93ID0gZmFsc2U7CiAgICB9LAogICAgaGFuZGxlQXNzaWduOiBmdW5jdGlvbiBoYW5kbGVBc3NpZ24oKSB7fSwKICAgIC8qKiDov5Tlm57pobXpnaIgKi9nb0JhY2s6IGZ1bmN0aW9uIGdvQmFjaygpIHsKICAgICAgLy8g5YWz6Zet5b2T5YmN5qCH562+6aG15bm26L+U5Zue5LiK5Liq6aG16Z2iCiAgICAgIHRoaXMuJHN0b3JlLmRpc3BhdGNoKCJ0YWdzVmlldy9kZWxWaWV3IiwgdGhpcy4kcm91dGUpOwogICAgICB0aGlzLiRyb3V0ZXIuZ28oLTEpOwogICAgfSwKICAgIC8qKiDmjqXmlLblrZDnu4Tku7bkvKDnmoTlgLwgKi9nZXREYXRhOiBmdW5jdGlvbiBnZXREYXRhKGRhdGEpIHsKICAgICAgaWYgKGRhdGEpIHsKICAgICAgICB2YXIgdmFyaWFibGVzID0gW107CiAgICAgICAgZGF0YS5maWVsZHMuZm9yRWFjaChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgdmFyIHZhcmlhYmxlRGF0YSA9IHt9OwogICAgICAgICAgdmFyaWFibGVEYXRhLmxhYmVsID0gaXRlbS5fX2NvbmZpZ19fLmxhYmVsOwogICAgICAgICAgLy8g6KGo5Y2V5YC85Li65aSa5Liq6YCJ6aG55pe2CiAgICAgICAgICBpZiAoaXRlbS5fX2NvbmZpZ19fLmRlZmF1bHRWYWx1ZSBpbnN0YW5jZW9mIEFycmF5KSB7CiAgICAgICAgICAgIHZhciBhcnJheSA9IFtdOwogICAgICAgICAgICBpdGVtLl9fY29uZmlnX18uZGVmYXVsdFZhbHVlLmZvckVhY2goZnVuY3Rpb24gKHZhbCkgewogICAgICAgICAgICAgIGFycmF5LnB1c2godmFsKTsKICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIHZhcmlhYmxlRGF0YS52YWwgPSBhcnJheTsKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIHZhcmlhYmxlRGF0YS52YWwgPSBpdGVtLl9fY29uZmlnX18uZGVmYXVsdFZhbHVlOwogICAgICAgICAgfQogICAgICAgICAgdmFyaWFibGVzLnB1c2godmFyaWFibGVEYXRhKTsKICAgICAgICB9KTsKICAgICAgICB0aGlzLnZhcmlhYmxlcyA9IHZhcmlhYmxlczsKICAgICAgfQogICAgfSwKICAgIC8qKiDnlLPor7fmtYHnqIvooajljZXmlbDmja7mj5DkuqQtLS0t5rKh55SoICovCiAgICAvLyBzdWJtaXRGb3JtKGRhdGEpIHsKICAgIC8vICAgaWYgKGRhdGEpIHsKICAgIC8vICAgICBjb25zdCB2YXJpYWJsZXMgPSBkYXRhLnZhbERhdGE7CiAgICAvLyAgICAgY29uc3QgZm9ybURhdGEgPSBkYXRhLmZvcm1EYXRhOwogICAgLy8gICAgIGZvcm1EYXRhLmRpc2FibGVkID0gdHJ1ZTsKICAgIC8vICAgICBmb3JtRGF0YS5mb3JtQnRucyA9IGZhbHNlOwogICAgLy8gICAgIGlmICh0aGlzLnRhc2tGb3JtLnByb2NEZWZLZXkpIHsKICAgIC8vICAgICAgIHZhcmlhYmxlcy52YXJpYWJsZXMgPSBmb3JtRGF0YTsKICAgIC8vICAgICAgIHZhcmlhYmxlcy5idXNpbmVzc0tleSA9IGRhdGEuYnVzaW5lc3NLZXk7ICAgICAgICAgIAogICAgLy8gICAgICAgIC8vIOWQr+WKqOa1geeoi+W5tuWwhuihqOWNleaVsOaNruWKoOWFpea1geeoi+WPmOmHjwogICAgLy8gICAgICAgZGVmaW5pdGlvblN0YXJ0QnlLZXkodGhpcy50YXNrRm9ybS5wcm9jRGVmS2V5LCBKU09OLnN0cmluZ2lmeSh2YXJpYWJsZXMpKS50aGVuKHJlcyA9PiB7CiAgICAvLyAgICAgICAgIHRoaXMubXNnU3VjY2VzcyhyZXMubXNnKTsKICAgIC8vICAgICAgICAgdGhpcy5nb0JhY2soKTsKICAgIC8vICAgICAgIH0pCiAgICAvLyAgICAgfQogICAgLy8gICB9CiAgICAvLyB9LAogICAgc3RhcnRGbG93OiBmdW5jdGlvbiBzdGFydEZsb3coYnVzaW5lc3NLZXksIG5hbWUsIHZhcmlhYmxlcykgewogICAgICB2YXIgX3RoaXM5ID0gdGhpczsKICAgICAgdmFyIHN0YXJ0RGF0ZSA9IG1vbWVudChuZXcgRGF0ZSgpKS5mb3JtYXQoJ1lZWVlNTURESEhtbXNzJyk7CiAgICAgIHZhciBkYXRhID0ge307CiAgICAgIGlmICh0aGlzLnRhc2tGb3JtLnByb2NEZWZLZXkpIHsKICAgICAgICBpZiAoIXZhcmlhYmxlcykgewogICAgICAgICAgZGF0YS52YXJpYWJsZXMgPSB7fTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgZGF0YS52YXJpYWJsZXMgPSB2YXJpYWJsZXM7CiAgICAgICAgfQogICAgICAgIGRhdGEuYnVzaW5lc3NLZXkgPSBidXNpbmVzc0tleTsKICAgICAgICBkYXRhLnByb2NEZWZLZXkgPSB0aGlzLnRhc2tGb3JtLnByb2NEZWZLZXk7CiAgICAgICAgZGF0YS50YXNrTmFtZSA9IG5hbWU7CiAgICAgICAgLy8g5ZCv5Yqo5rWB56iL5bm25bCG6KGo5Y2V5pWw5o2u5Yqg5YWl5rWB56iL5Y+Y6YePCiAgICAgICAgZGVmaW5pdGlvblN0YXJ0QnlLZXkoSlNPTi5zdHJpbmdpZnkoZGF0YSkpLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgICAgX3RoaXM5Lm1zZ1N1Y2Nlc3MocmVzLm1zZyk7CiAgICAgICAgICBfdGhpczkuZ29CYWNrKCk7CiAgICAgICAgfSk7CiAgICAgIH0KICAgIH0sCiAgICAvKiog6amz5Zue5Lu75YqhICovaGFuZGxlUmVqZWN0OiBmdW5jdGlvbiBoYW5kbGVSZWplY3QoKSB7CiAgICAgIHRoaXMucmVqZWN0T3BlbiA9IHRydWU7CiAgICAgIHRoaXMucmVqZWN0VGl0bGUgPSAi6amz5Zue5rWB56iLIjsKICAgIH0sCiAgICAvKiog6amz5Zue5Lu75YqhICovdGFza1JlamVjdDogZnVuY3Rpb24gdGFza1JlamVjdCgpIHsKICAgICAgdmFyIF90aGlzMTAgPSB0aGlzOwogICAgICB0aGlzLiRyZWZzWyJ0YXNrRm9ybSJdLnZhbGlkYXRlKGZ1bmN0aW9uICh2YWxpZCkgewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgcmVqZWN0VGFzayhfdGhpczEwLnRhc2tGb3JtKS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICAgICAgX3RoaXMxMC5tc2dTdWNjZXNzKHJlcy5tc2cpOwogICAgICAgICAgICBfdGhpczEwLmdvQmFjaygpOwogICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvKiog5Y+v6YCA5Zue5Lu75Yqh5YiX6KGoICovaGFuZGxlUmV0dXJuOiBmdW5jdGlvbiBoYW5kbGVSZXR1cm4oKSB7CiAgICAgIHZhciBfdGhpczExID0gdGhpczsKICAgICAgdGhpcy5yZXR1cm5PcGVuID0gdHJ1ZTsKICAgICAgdGhpcy5yZXR1cm5UaXRsZSA9ICLpgIDlm57mtYHnqIsiOwogICAgICByZXR1cm5MaXN0KHRoaXMudGFza0Zvcm0pLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgIF90aGlzMTEucmV0dXJuVGFza0xpc3QgPSByZXMuZGF0YTsKICAgICAgICBfdGhpczExLnRhc2tGb3JtLnZhbHVlcyA9IG51bGw7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDmj5DkuqTpgIDlm57ku7vliqEgKi90YXNrUmV0dXJuOiBmdW5jdGlvbiB0YXNrUmV0dXJuKCkgewogICAgICB2YXIgX3RoaXMxMiA9IHRoaXM7CiAgICAgIHRoaXMuJHJlZnNbInRhc2tGb3JtIl0udmFsaWRhdGUoZnVuY3Rpb24gKHZhbGlkKSB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICByZXR1cm5UYXNrKF90aGlzMTIudGFza0Zvcm0pLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgICAgICBfdGhpczEyLm1zZ1N1Y2Nlc3MocmVzLm1zZyk7CiAgICAgICAgICAgIF90aGlzMTIuZ29CYWNrKCk7CiAgICAgICAgICB9KTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDlj5bmtojlm57pgIDku7vliqHmjInpkq4gKi9jYW5jZWxUYXNrOiBmdW5jdGlvbiBjYW5jZWxUYXNrKCkgewogICAgICB0aGlzLnRhc2tGb3JtLnJldHVyblRhc2tTaG93ID0gZmFsc2U7CiAgICAgIHRoaXMudGFza0Zvcm0uZGVmYXVsdFRhc2tTaG93ID0gdHJ1ZTsKICAgICAgdGhpcy50YXNrRm9ybS5zZW5kVXNlclNob3cgPSB0cnVlOwogICAgICB0aGlzLnJldHVyblRhc2tMaXN0ID0gW107CiAgICB9LAogICAgLyoqIOWnlOa0vuS7u+WKoSAqL3N1Ym1pdERlbGV0ZVRhc2s6IGZ1bmN0aW9uIHN1Ym1pdERlbGV0ZVRhc2soKSB7CiAgICAgIHZhciBfdGhpczEzID0gdGhpczsKICAgICAgdGhpcy4kcmVmc1sidGFza0Zvcm0iXS52YWxpZGF0ZShmdW5jdGlvbiAodmFsaWQpIHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIGRlbGVnYXRlKF90aGlzMTMudGFza0Zvcm0pLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgICAgIF90aGlzMTMubXNnU3VjY2VzcyhyZXNwb25zZS5tc2cpOwogICAgICAgICAgICBfdGhpczEzLmdvQmFjaygpOwogICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvKiog5Y+W5raI5Zue6YCA5Lu75Yqh5oyJ6ZKuICovY2FuY2VsRGVsZWdhdGVUYXNrOiBmdW5jdGlvbiBjYW5jZWxEZWxlZ2F0ZVRhc2soKSB7CiAgICAgIHRoaXMudGFza0Zvcm0uZGVsZWdhdGVUYXNrU2hvdyA9IGZhbHNlOwogICAgICB0aGlzLnRhc2tGb3JtLmRlZmF1bHRUYXNrU2hvdyA9IHRydWU7CiAgICAgIHRoaXMudGFza0Zvcm0uc2VuZFVzZXJTaG93ID0gdHJ1ZTsKICAgICAgdGhpcy5yZXR1cm5UYXNrTGlzdCA9IFtdOwogICAgfQogIH0KfTs="}, {"version": 3, "names": ["flowRecord", "<PERSON><PERSON><PERSON>", "definitionStartByKey", "getProcessVariables", "readXmlByKey", "getFlowViewer", "complete", "rejectTask", "returnList", "returnTask", "getNextFlowNode", "delegate", "flow", "treeselect", "Treeselect", "listUser", "moment", "name", "components", "props", "procDef<PERSON>ey", "type", "String", "default", "undefined", "taskId", "procInsId", "data", "xmlData", "taskList", "deptName", "deptOptions", "userList", "defaultProps", "children", "label", "queryParams", "deptId", "loading", "flowRecordList", "formConfCopy", "src", "rules", "variablesForm", "taskForm", "returnTaskShow", "delegateTaskShow", "defaultTaskShow", "sendUserShow", "multiple", "comment", "instanceId", "vars", "<PERSON><PERSON><PERSON>", "userDataList", "assignee", "formConf", "formConfOpen", "variables", "variablesData", "variableOpen", "returnTaskList", "finished", "completeTitle", "completeOpen", "returnTitle", "returnOpen", "rejectOpen", "rejectTitle", "userData", "created", "console", "log", "_props", "_this$_props", "processVariables", "getFlowRecordList", "$route", "query", "activated", "_this$_props2", "mounted", "methods", "getTreeselect", "_this", "then", "response", "getList", "_this2", "addDateRange", "date<PERSON><PERSON><PERSON>", "rows", "total", "filterNode", "value", "indexOf", "handleNodeClick", "id", "getModelDetail", "deployKey", "_this3", "res", "_this4", "setIcon", "val", "setColor", "handleSelectionChange", "selection", "map", "item", "userId", "Array", "values", "join", "handleClose", "tag", "splice", "handleCheckChange", "_this5", "params", "flowList", "formData", "catch", "goBack", "fillFormData", "form", "fields", "for<PERSON>ach", "__vModel__", "__config__", "defaultValue", "_this6", "_this7", "roleList", "role", "roleId", "nick<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "handleComplete", "taskComplete", "_this8", "constructor", "msgError", "msgSuccess", "msg", "handleDelegate", "handleAssign", "$store", "dispatch", "$router", "go", "getData", "variableData", "array", "push", "startFlow", "businessKey", "_this9", "startDate", "Date", "format", "taskName", "JSON", "stringify", "handleReject", "taskReject", "_this10", "$refs", "validate", "valid", "handleReturn", "_this11", "taskReturn", "_this12", "cancelTask", "submitDeleteTask", "_this13", "cancelDelegateTask"], "sources": ["src/views/flowable/task/record/index.vue"], "sourcesContent": ["<template>\n  <div class=\"\">\n    <!--流程流转记录-->\n    <el-card class=\"box-card\" v-if=\"flowRecordList\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span class=\"el-icon-notebook-1\">审批记录</span>\n          </div>\n          <el-col :span=\"16\" :offset=\"4\" >\n            <div class=\"block\">\n              <el-timeline>\n                <el-timeline-item\n                  v-for=\"(item,index ) in flowRecordList\"\n                  :key=\"index\"\n                  :icon=\"setIcon(item.finishTime)\"\n                  :color=\"setColor(item.finishTime)\"\n                >\n                  <p style=\"font-weight: 700\">{{item.taskName}}</p>\n                  <el-card :body-style=\"{ padding: '10px' }\">\n                    <label v-if=\"item.assigneeName\" style=\"font-weight: normal;margin-right: 30px;\">实际办理： {{item.assigneeName}} <el-tag type=\"info\" size=\"mini\">{{item.deptName}}</el-tag></label>\n                    <label v-if=\"item.candidate\" style=\"font-weight: normal;margin-right: 30px;\">候选办理： {{item.candidate}}</label>\n                    <label style=\"font-weight: normal\">接收时间： </label><label style=\"color:#8a909c;font-weight: normal\">{{item.createTime}}</label>\n                    <label v-if=\"item.finishTime\" style=\"margin-left: 30px;font-weight: normal\">办结时间： </label><label style=\"color:#8a909c;font-weight: normal\">{{item.finishTime}}</label>\n                    <label v-if=\"item.duration\" style=\"margin-left: 30px;font-weight: normal\">耗时： </label><label style=\"color:#8a909c;font-weight: normal\">{{item.duration}}</label>\n\n                    <p  v-if=\"item.comment\">\n                      <el-tag type=\"success\" v-if=\"item.comment.type === '1'\">  {{item.comment.comment}}</el-tag>\n                      <el-tag type=\"warning\" v-if=\"item.comment.type === '2'\">  {{item.comment.comment}}</el-tag>\n                      <el-tag type=\"danger\" v-if=\"item.comment.type === '3'\">  {{item.comment.comment}}</el-tag>\n                    </p>\n                  </el-card>\n                </el-timeline-item>\n              </el-timeline>\n            </div>\n          </el-col>\n      </el-card>\n    <el-card class=\"box-card\">\n        <div slot=\"header\" class=\"clearfix\">\n          <span class=\"el-icon-picture-outline\">流程图</span>\n        </div>\n        <flow :xmlData=\"xmlData\" :taskData=\"taskList\"></flow>\n    </el-card>\n\n    <!--审批正常流程-->\n    <el-dialog :title=\"completeTitle\" visible.sync=\"false\" width=\"60%\" append-to-body>\n      <el-row :gutter=\"20\">\n        <!--部门数据-->\n        <el-col :span=\"4\" :xs=\"24\">\n          <h6>部门列表</h6>\n          <div class=\"head-container\">\n            <el-input\n              v-model=\"deptName\"\n              placeholder=\"请输入部门名称\"\n              clearable\n              size=\"small\"\n              prefix-icon=\"el-icon-search\"\n              style=\"margin-bottom: 20px\"\n            />\n          </div>\n          <div class=\"head-container\">\n            <el-tree\n              :data=\"deptOptions\"\n              :props=\"defaultProps\"\n              :expand-on-click-node=\"false\"\n              :filter-node-method=\"filterNode\"\n              ref=\"tree\"\n              default-expand-all\n              @node-click=\"handleNodeClick\"\n            />\n          </div>\n        </el-col>\n        <el-col :span=\"12\" :xs=\"24\">\n          <h6>待选人员</h6>\n          <el-table\n            ref=\"singleTable\"\n            :data=\"userList\"\n            border\n            style=\"width: 100%\"\n            @selection-change=\"handleSelectionChange\">\n            <el-table-column type=\"selection\" width=\"50\" align=\"center\" />\n            <el-table-column label=\"用户名\" align=\"center\" prop=\"nickName\" />\n            <el-table-column label=\"部门\" align=\"center\" prop=\"dept.deptName\" />\n          </el-table>\n        </el-col>\n        <el-col :span=\"8\" :xs=\"24\">\n          <h6>已选人员</h6>\n          <el-tag\n            v-for=\"tag in userData\"\n            :key=\"tag.nickName\"\n            closable\n            @close=\"handleClose(tag)\">\n            {{tag.nickName}} {{tag.dept.deptName}}\n          </el-tag>\n        </el-col>\n      </el-row>\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-input style=\"width: 50%;margin-right: 34%\" type=\"textarea\" v-model=\"taskForm.comment\" placeholder=\"请输入处理意见\"/>\n        <el-button @click=\"completeOpen = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"taskComplete\">确 定</el-button>\n      </span>\n    </el-dialog>\n\n    <el-dialog :title=\"completeTitle\" :visible.sync=\"completeOpen\" width=\"40%\" append-to-body>\n      <el-form ref=\"taskForm\" :model=\"taskForm\" label-width=\"80px\" >\n        <el-form-item label=\"审批意见\" prop=\"comment\" :rules=\"[{ required: true, message: '请输入处理意见', trigger: 'blur' }]\">\n          <el-input style=\"width: 50%\" type=\"textarea\" v-model=\"taskForm.comment\" placeholder=\"请输入处理意见\"/>\n        </el-form-item>\n      </el-form>\n      <span slot=\"footer\" class=\"dialog-footer\">\n          <el-button @click=\"completeOpen = false\">取 消</el-button>\n          <el-button type=\"primary\" @click=\"taskComplete\">确 定</el-button>\n        </span>\n    </el-dialog>\n\n    <!--退回流程-->\n    <el-dialog :title=\"returnTitle\" :visible.sync=\"returnOpen\" width=\"40%\" append-to-body>\n        <el-form ref=\"taskForm\" :model=\"taskForm\" label-width=\"80px\" >\n            <el-form-item label=\"退回节点\" prop=\"targetKey\" :rules=\"[{ required: true, message: '请选择退回节点', trigger: 'change' }]\">\n              <el-radio-group v-model=\"taskForm.targetKey\">\n                <el-radio-button\n                  v-for=\"item in returnTaskList\"\n                  :key=\"item.id\"\n                  :label=\"item.id\"\n                >{{item.name}}</el-radio-button>\n              </el-radio-group>\n            </el-form-item>\n          <el-form-item label=\"退回意见\" prop=\"comment\" :rules=\"[{ required: true, message: '请输入意见', trigger: 'blur' }]\">\n            <el-input style=\"width: 50%\" type=\"textarea\" v-model=\"taskForm.comment\" placeholder=\"请输入意见\"/>\n          </el-form-item>\n        </el-form>\n        <span slot=\"footer\" class=\"dialog-footer\">\n            <el-button @click=\"returnOpen = false\">取 消</el-button>\n            <el-button type=\"primary\" @click=\"taskReturn\">确 定</el-button>\n        </span>\n    </el-dialog>\n\n    <!--驳回流程-->\n    <el-dialog :title=\"rejectTitle\" :visible.sync=\"rejectOpen\" width=\"40%\" append-to-body>\n      <el-form ref=\"taskForm\" :model=\"taskForm\" label-width=\"80px\" >\n        <el-form-item label=\"驳回意见\" prop=\"comment\" :rules=\"[{ required: true, message: '请输入意见', trigger: 'blur' }]\">\n          <el-input style=\"width: 50%\" type=\"textarea\" v-model=\"taskForm.comment\" placeholder=\"请输入意见\"/>\n        </el-form-item>\n      </el-form>\n      <span slot=\"footer\" class=\"dialog-footer\">\n          <el-button @click=\"rejectOpen = false\">取 消</el-button>\n          <el-button type=\"primary\" @click=\"taskReject\">确 定</el-button>\n        </span>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport {flowRecord} from \"@/api/flowable/finished\";\nimport Parser from '@/components/parser/Parser'\nimport {definitionStartByKey, getProcessVariables, readXmlByKey, getFlowViewer} from \"@/api/flowable/definition\";\nimport {complete, rejectTask, returnList, returnTask, getNextFlowNode, delegate} from \"@/api/flowable/todo\";\nimport flow from '@/views/flowable/task/record/flow'\nimport {treeselect} from \"@/api/system/dept\";\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\";\nimport Treeselect from \"@riophae/vue-treeselect\";\nimport {listUser} from \"@/api/system/user\";\nimport moment from 'moment';\nexport default {\n  name: \"Record\",\n  components: {\n    Parser,\n    flow,\n    Treeselect\n  },\n  props: {\n    procDefKey: {\n      type: String,\n      default: undefined\n    },\n    taskId: {\n      type: String,\n      default: undefined\n    },\n    procInsId: {\n      type: String,\n      default: undefined\n    },\n  },\n  data() {\n    return {\n      // 模型xml数据\n      xmlData: \"\",\n      taskList: [],\n      // 部门名称\n      deptName: undefined,\n      // 部门树选项\n      deptOptions: undefined,\n      // 用户表格数据\n      userList: null,\n      defaultProps: {\n        children: \"children\",\n        label: \"label\"\n      },\n      // 查询参数\n      queryParams: {\n        deptId: undefined\n      },\n      // 遮罩层\n      loading: true,\n      flowRecordList: [], // 流程流转数据\n      formConfCopy: {},\n      src: null,\n      rules: {}, // 表单校验\n      variablesForm: {}, // 流程变量数据\n      taskForm:{\n        returnTaskShow: false, // 是否展示回退表单\n        delegateTaskShow: false, // 是否展示回退表单\n        defaultTaskShow: true, // 默认处理\n        sendUserShow: false, // 审批用户\n        multiple: false,\n        comment:\"\", // 意见内容\n        procInsId: \"\", // 流程实例编号\n        instanceId: \"\", // 流程实例编号\n        taskId: \"\" ,// 流程任务编号\n        procDefKey: \"\",  // 流程编号\n        vars: \"\",\n        targetKey:\"\"\n      },\n      userDataList:[], // 流程候选人\n      assignee: null,\n      formConf: {}, // 默认表单数据\n      formConfOpen: false, // 是否加载默认表单数据\n      variables: [], // 流程变量数据\n      variablesData: {}, // 流程变量数据\n      variableOpen: false, // 是否加载流程变量数据\n      returnTaskList: [],  // 回退列表数据\n      finished: 'false',\n      completeTitle: null,\n      completeOpen: false,\n      returnTitle: null,\n      returnOpen: false,\n      rejectOpen: false,\n      rejectTitle: null,\n      userData:[],\n    };\n  },\n  created() {\n    console.log(\"========record========created=>>>\")\n    console.log(this._props)\n    let {taskId,procDefKey,procInsId,finished}=this._props;\n    this.taskForm.taskId  = taskId;\n    this.taskForm.procInsId = procInsId;\n    this.taskForm.instanceId = procInsId;\n    // 初始化表单\n    this.taskForm.procDefKey  = procDefKey;\n    this.finished = finished;\n    // 回显流程记录\n    this.getFlowViewer(this.taskForm.procInsId,this.taskForm.procDefKey);\n    // 流程任务重获取变量表单\n    if (this.taskForm.taskId){\n      this.processVariables( this.taskForm.taskId)\n      this.getNextFlowNode(this.taskForm.taskId)\n    }\n    this.getFlowRecordList( this.taskForm.procInsId);\n    this.finished =  this.$route.query && this.$route.query.finished\n  },\n  activated() {\n    console.log(\"========record========activated=>>>\")\n    let {taskId,procDefKey,procInsId,finished}=this._props;\n    console.log(this._props)\n    this.taskForm.taskId  = taskId;\n    this.taskForm.procInsId = procInsId;\n    this.taskForm.instanceId = procInsId;\n    // 初始化表单\n    this.taskForm.procDefKey  = procDefKey;\n    this.finished = finished;\n    // 回显流程记录\n    this.getFlowViewer(this.taskForm.procInsId,this.taskForm.procDefKey);\n    // 流程任务重获取变量表单\n    if (this.taskForm.taskId){\n      this.processVariables( this.taskForm.taskId)\n      this.getNextFlowNode(this.taskForm.taskId)\n    }\n    this.getFlowRecordList( this.taskForm.procInsId);\n    this.finished =  this.$route.query && this.$route.query.finished\n  },\n  mounted() {\n    // // 表单数据回填，模拟异步请求场景\n    // setTimeout(() => {\n    //   // 请求回来的表单数据\n    //   const data = {\n    //     field102: '18836662555'\n    //   }\n    //   // 回填数据\n    //   this.fillFormData(this.formConf, data)\n    //   // 更新表单\n    //   this.key = +new Date().getTime()\n    // }, 1000)\n  },\n  methods: {\n    /** 查询部门下拉树结构 */\n    getTreeselect() {\n      treeselect().then(response => {\n        this.deptOptions = response.data;\n      });\n    },\n    /** 查询用户列表 */\n    getList() {\n      listUser(this.addDateRange(this.queryParams, this.dateRange)).then(response => {\n          this.userList = response.rows;\n          this.total = response.total;\n        }\n      );\n    },\n    // 筛选节点\n    filterNode(value, data) {\n      if (!value) return true;\n      return data.label.indexOf(value) !== -1;\n    },\n    // 节点单击事件\n    handleNodeClick(data) {\n      this.queryParams.deptId = data.id;\n      this.getList();\n    },\n    /** xml 文件 */\n    getModelDetail(deployKey) {\n      // 发送请求，获取xml\n      readXmlByKey(deployKey).then(res => {\n        this.xmlData = res.data\n      })\n    },\n    getFlowViewer(procInsId,deployKey) {\n      getFlowViewer(procInsId).then(res => {\n        this.taskList = res.data\n        this.getModelDetail(deployKey);\n      })\n    },\n    setIcon(val) {\n      if (val) {\n        return \"el-icon-check\";\n      } else {\n        return \"el-icon-time\";\n      }\n    },\n    setColor(val) {\n      if (val) {\n        return \"#2bc418\";\n      } else {\n        return \"#b3bdbb\";\n      }\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.userData = selection\n      const val = selection.map(item => item.userId)[0];\n      if (val instanceof Array) {\n        this.taskForm.values = {\n          \"approval\": val.join(',')\n        }\n      } else {\n        this.taskForm.values = {\n          \"approval\": val\n        }\n      }\n    },\n    // 关闭标签\n    handleClose(tag) {\n      this.userData.splice(this.userData.indexOf(tag), 1);\n    },\n    /** 流程变量赋值 */\n    handleCheckChange(val) {\n      if (val instanceof Array) {\n        this.taskForm.values = {\n          \"approval\": val.join(',')\n        }\n      } else {\n        this.taskForm.values = {\n          \"approval\": val\n        }\n      }\n    },\n    /** 流程流转记录 */\n    getFlowRecordList(procInsId) {\n      const params = {procInsId: procInsId}\n      flowRecord(params).then(res => {\n        this.flowRecordList = res.data.flowList;\n        // 流程过程中不存在初始化表单 直接读取的流程变量中存储的表单值\n        if (res.data.formData) {\n          this.formConf = res.data.formData;\n          this.formConfOpen = true\n        }\n      }).catch(res => {\n        this.goBack();\n      })\n    },\n    fillFormData(form, data) {\n      form.fields.forEach(item => {\n        const val = data[item.__vModel__]\n        if (val) {\n          item.__config__.defaultValue = val\n        }\n      })\n    },\n    /** 获取流程变量内容 */\n    processVariables(taskId) {\n      if (taskId) {\n        // 提交流程申请时填写的表单存入了流程变量中后续任务处理时需要展示\n        getProcessVariables(taskId).then(res => {\n          // this.variables = res.data.variables;\n          this.variablesData = res.data.variables;\n          this.variableOpen = true\n        });\n      }\n    },\n    /** 根据当前任务或者流程设计配置的下一步节点 */\n    getNextFlowNode(taskId) {\n      // 根据当前任务或者流程设计配置的下一步节点 todo 暂时未涉及到考虑网关、表达式和多节点情况\n      const params = {taskId: taskId}\n      getNextFlowNode(params).then(res => {\n        const data = res.data;\n        if (data) {\n          if (data.type === 'assignee') {\n            this.userDataList = res.data.userList;\n          } else if (data.type === 'candidateUsers') {\n            this.userDataList = res.data.userList;\n            this.taskForm.multiple = true;\n          } else if (data.type === 'candidateGroups') {\n            res.data.roleList.forEach(role => {\n              role.userId = role.roleId;\n              role.nickName = role.roleName;\n            })\n            this.userDataList = res.data.roleList;\n            this.taskForm.multiple = false;\n          } else if (data.type === 'multiInstance') {\n            this.userDataList = res.data.userList;\n            this.taskForm.multiple = true;\n          }\n          this.taskForm.sendUserShow = true;\n        }\n      })\n    },\n    /** 审批任务选择 */\n    handleComplete() {\n      this.completeOpen = true;\n      this.completeTitle = \"审批流程\";\n      //this.getTreeselect();\n    },\n    /** 审批任务 */\n    taskComplete(comment) {\n      // if (!this.taskForm.values){\n      //   this.msgError(\"请选择流程接收人员\");\n      //   return;\n      // }\n      if(comment && (typeof comment=='string')&&comment.constructor==String){\n        this.taskForm.comment = comment;\n      } \n      if (!this.taskForm.comment){\n        this.msgError(\"请输入审批意见\");\n        return;\n      }\n      complete(this.taskForm).then(response => {\n        this.msgSuccess(response.msg);\n        this.goBack();\n      });\n    },\n    /** 委派任务 */\n    handleDelegate() {\n      this.taskForm.delegateTaskShow = true;\n      this.taskForm.defaultTaskShow = false;\n    },\n    handleAssign(){\n\n    },\n    /** 返回页面 */\n    goBack() {\n      // 关闭当前标签页并返回上个页面\n      this.$store.dispatch(\"tagsView/delView\", this.$route);\n      this.$router.go(-1)\n    },\n    /** 接收子组件传的值 */\n    getData(data) {\n      if (data) {\n        const variables = [];\n        data.fields.forEach(item => {\n          let variableData = {};\n          variableData.label = item.__config__.label\n          // 表单值为多个选项时\n          if (item.__config__.defaultValue instanceof Array) {\n            const array = [];\n            item.__config__.defaultValue.forEach(val => {\n              array.push(val)\n            })\n            variableData.val = array;\n          } else {\n            variableData.val = item.__config__.defaultValue\n          }\n          variables.push(variableData)\n        })\n        this.variables = variables;\n      }\n    },\n    /** 申请流程表单数据提交----没用 */\n    // submitForm(data) {\n    //   if (data) {\n    //     const variables = data.valData;\n    //     const formData = data.formData;\n    //     formData.disabled = true;\n    //     formData.formBtns = false;\n    //     if (this.taskForm.procDefKey) {\n    //       variables.variables = formData;\n    //       variables.businessKey = data.businessKey;          \n    //        // 启动流程并将表单数据加入流程变量\n    //       definitionStartByKey(this.taskForm.procDefKey, JSON.stringify(variables)).then(res => {\n    //         this.msgSuccess(res.msg);\n    //         this.goBack();\n    //       })\n    //     }\n    //   }\n    // },\n    startFlow(businessKey, name, variables) {\n      let startDate = moment(new Date()).format('YYYYMMDDHHmmss');\n      const data = {}\n      if (this.taskForm.procDefKey) {\n        if(!variables){\n          data.variables = {};\n        }else{\n          data.variables = variables;\n        }\n        \n        data.businessKey = businessKey;\n        data.procDefKey = this.taskForm.procDefKey;\n        data.taskName = name;\n          // 启动流程并将表单数据加入流程变量\n        definitionStartByKey(JSON.stringify(data)).then(res => {\n          this.msgSuccess(res.msg);\n          this.goBack();\n        })\n      }\n      \n    },\n    /** 驳回任务 */\n    handleReject() {\n      this.rejectOpen = true;\n      this.rejectTitle = \"驳回流程\";\n    },\n    /** 驳回任务 */\n    taskReject() {\n      this.$refs[\"taskForm\"].validate(valid => {\n        if (valid) {\n          rejectTask(this.taskForm).then(res => {\n            this.msgSuccess(res.msg);\n            this.goBack();\n          });\n        }\n      });\n    },\n    /** 可退回任务列表 */\n    handleReturn() {\n      this.returnOpen = true;\n      this.returnTitle = \"退回流程\";\n      returnList(this.taskForm).then(res => {\n        this.returnTaskList = res.data;\n        this.taskForm.values = null;\n      })\n    },\n    /** 提交退回任务 */\n   taskReturn() {\n      this.$refs[\"taskForm\"].validate(valid => {\n        if (valid) {\n          returnTask(this.taskForm).then(res => {\n            this.msgSuccess(res.msg);\n            this.goBack()\n          });\n        }\n      });\n    },\n    /** 取消回退任务按钮 */\n    cancelTask() {\n      this.taskForm.returnTaskShow = false;\n      this.taskForm.defaultTaskShow = true;\n      this.taskForm.sendUserShow = true;\n      this.returnTaskList = [];\n    },\n    /** 委派任务 */\n    submitDeleteTask() {\n      this.$refs[\"taskForm\"].validate(valid => {\n        if (valid) {\n          delegate(this.taskForm).then(response => {\n            this.msgSuccess(response.msg);\n            this.goBack();\n          });\n        }\n      });\n    },\n    /** 取消回退任务按钮 */\n    cancelDelegateTask() {\n      this.taskForm.delegateTaskShow = false;\n      this.taskForm.defaultTaskShow = true;\n      this.taskForm.sendUserShow = true;\n      this.returnTaskList = [];\n    },\n  }\n};\n</script>\n<style lang=\"scss\" scoped>\n@media screen and (max-width: 600px) {\n  .el-timeline {\n      margin: 0;\n      font-size: 14px;\n      list-style: none;\n      padding-left: 0;\n  }\n}\n.test-form {\n  margin: 15px auto;\n  width: 800px;\n  padding: 15px;\n}\n\n.clearfix:before,\n.clearfix:after {\n  display: table;\n  content: \"\";\n}\n.clearfix:after {\n  clear: both\n}\n\n.box-card {\n  width: 100%;\n  margin-bottom: 20px;\n}\n\n.el-tag + .el-tag {\n  margin-left: 10px;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuJA,SAAAA,UAAA;AACA,OAAAC,MAAA;AACA,SAAAC,oBAAA,EAAAC,mBAAA,EAAAC,YAAA,EAAAC,aAAA,IAAAA,cAAA;AACA,SAAAC,QAAA,EAAAC,UAAA,EAAAC,UAAA,EAAAC,UAAA,EAAAC,eAAA,IAAAA,gBAAA,EAAAC,QAAA;AACA,OAAAC,IAAA;AACA,SAAAC,UAAA;AACA;AACA,OAAAC,UAAA;AACA,SAAAC,QAAA;AACA,OAAAC,MAAA;AACA;EACAC,IAAA;EACAC,UAAA;IACAjB,MAAA,EAAAA,MAAA;IACAW,IAAA,EAAAA,IAAA;IACAE,UAAA,EAAAA;EACA;EACAK,KAAA;IACAC,UAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA,EAAAC;IACA;IACAC,MAAA;MACAJ,IAAA,EAAAC,MAAA;MACAC,OAAA,EAAAC;IACA;IACAE,SAAA;MACAL,IAAA,EAAAC,MAAA;MACAC,OAAA,EAAAC;IACA;EACA;EACAG,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACAC,QAAA;MACA;MACAC,QAAA,EAAAN,SAAA;MACA;MACAO,WAAA,EAAAP,SAAA;MACA;MACAQ,QAAA;MACAC,YAAA;QACAC,QAAA;QACAC,KAAA;MACA;MACA;MACAC,WAAA;QACAC,MAAA,EAAAb;MACA;MACA;MACAc,OAAA;MACAC,cAAA;MAAA;MACAC,YAAA;MACAC,GAAA;MACAC,KAAA;MAAA;MACAC,aAAA;MAAA;MACAC,QAAA;QACAC,cAAA;QAAA;QACAC,gBAAA;QAAA;QACAC,eAAA;QAAA;QACAC,YAAA;QAAA;QACAC,QAAA;QACAC,OAAA;QAAA;QACAxB,SAAA;QAAA;QACAyB,UAAA;QAAA;QACA1B,MAAA;QAAA;QACAL,UAAA;QAAA;QACAgC,IAAA;QACAC,SAAA;MACA;MACAC,YAAA;MAAA;MACAC,QAAA;MACAC,QAAA;MAAA;MACAC,YAAA;MAAA;MACAC,SAAA;MAAA;MACAC,aAAA;MAAA;MACAC,YAAA;MAAA;MACAC,cAAA;MAAA;MACAC,QAAA;MACAC,aAAA;MACAC,YAAA;MACAC,WAAA;MACAC,UAAA;MACAC,UAAA;MACAC,WAAA;MACAC,QAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACAC,OAAA,CAAAC,GAAA;IACAD,OAAA,CAAAC,GAAA,MAAAC,MAAA;IACA,IAAAC,YAAA,QAAAD,MAAA;MAAAhD,MAAA,GAAAiD,YAAA,CAAAjD,MAAA;MAAAL,UAAA,GAAAsD,YAAA,CAAAtD,UAAA;MAAAM,SAAA,GAAAgD,YAAA,CAAAhD,SAAA;MAAAoC,QAAA,GAAAY,YAAA,CAAAZ,QAAA;IACA,KAAAlB,QAAA,CAAAnB,MAAA,GAAAA,MAAA;IACA,KAAAmB,QAAA,CAAAlB,SAAA,GAAAA,SAAA;IACA,KAAAkB,QAAA,CAAAO,UAAA,GAAAzB,SAAA;IACA;IACA,KAAAkB,QAAA,CAAAxB,UAAA,GAAAA,UAAA;IACA,KAAA0C,QAAA,GAAAA,QAAA;IACA;IACA,KAAAzD,aAAA,MAAAuC,QAAA,CAAAlB,SAAA,OAAAkB,QAAA,CAAAxB,UAAA;IACA;IACA,SAAAwB,QAAA,CAAAnB,MAAA;MACA,KAAAkD,gBAAA,MAAA/B,QAAA,CAAAnB,MAAA;MACA,KAAAf,eAAA,MAAAkC,QAAA,CAAAnB,MAAA;IACA;IACA,KAAAmD,iBAAA,MAAAhC,QAAA,CAAAlB,SAAA;IACA,KAAAoC,QAAA,QAAAe,MAAA,CAAAC,KAAA,SAAAD,MAAA,CAAAC,KAAA,CAAAhB,QAAA;EACA;EACAiB,SAAA,WAAAA,UAAA;IACAR,OAAA,CAAAC,GAAA;IACA,IAAAQ,aAAA,QAAAP,MAAA;MAAAhD,MAAA,GAAAuD,aAAA,CAAAvD,MAAA;MAAAL,UAAA,GAAA4D,aAAA,CAAA5D,UAAA;MAAAM,SAAA,GAAAsD,aAAA,CAAAtD,SAAA;MAAAoC,QAAA,GAAAkB,aAAA,CAAAlB,QAAA;IACAS,OAAA,CAAAC,GAAA,MAAAC,MAAA;IACA,KAAA7B,QAAA,CAAAnB,MAAA,GAAAA,MAAA;IACA,KAAAmB,QAAA,CAAAlB,SAAA,GAAAA,SAAA;IACA,KAAAkB,QAAA,CAAAO,UAAA,GAAAzB,SAAA;IACA;IACA,KAAAkB,QAAA,CAAAxB,UAAA,GAAAA,UAAA;IACA,KAAA0C,QAAA,GAAAA,QAAA;IACA;IACA,KAAAzD,aAAA,MAAAuC,QAAA,CAAAlB,SAAA,OAAAkB,QAAA,CAAAxB,UAAA;IACA;IACA,SAAAwB,QAAA,CAAAnB,MAAA;MACA,KAAAkD,gBAAA,MAAA/B,QAAA,CAAAnB,MAAA;MACA,KAAAf,eAAA,MAAAkC,QAAA,CAAAnB,MAAA;IACA;IACA,KAAAmD,iBAAA,MAAAhC,QAAA,CAAAlB,SAAA;IACA,KAAAoC,QAAA,QAAAe,MAAA,CAAAC,KAAA,SAAAD,MAAA,CAAAC,KAAA,CAAAhB,QAAA;EACA;EACAmB,OAAA,WAAAA,QAAA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EAAA,CACA;EACAC,OAAA;IACA,gBACAC,aAAA,WAAAA,cAAA;MAAA,IAAAC,KAAA;MACAvE,UAAA,GAAAwE,IAAA,WAAAC,QAAA;QACAF,KAAA,CAAArD,WAAA,GAAAuD,QAAA,CAAA3D,IAAA;MACA;IACA;IACA,aACA4D,OAAA,WAAAA,QAAA;MAAA,IAAAC,MAAA;MACAzE,QAAA,MAAA0E,YAAA,MAAArD,WAAA,OAAAsD,SAAA,GAAAL,IAAA,WAAAC,QAAA;QACAE,MAAA,CAAAxD,QAAA,GAAAsD,QAAA,CAAAK,IAAA;QACAH,MAAA,CAAAI,KAAA,GAAAN,QAAA,CAAAM,KAAA;MACA,CACA;IACA;IACA;IACAC,UAAA,WAAAA,WAAAC,KAAA,EAAAnE,IAAA;MACA,KAAAmE,KAAA;MACA,OAAAnE,IAAA,CAAAQ,KAAA,CAAA4D,OAAA,CAAAD,KAAA;IACA;IACA;IACAE,eAAA,WAAAA,gBAAArE,IAAA;MACA,KAAAS,WAAA,CAAAC,MAAA,GAAAV,IAAA,CAAAsE,EAAA;MACA,KAAAV,OAAA;IACA;IACA,aACAW,cAAA,WAAAA,eAAAC,SAAA;MAAA,IAAAC,MAAA;MACA;MACAhG,YAAA,CAAA+F,SAAA,EAAAd,IAAA,WAAAgB,GAAA;QACAD,MAAA,CAAAxE,OAAA,GAAAyE,GAAA,CAAA1E,IAAA;MACA;IACA;IACAtB,aAAA,WAAAA,cAAAqB,SAAA,EAAAyE,SAAA;MAAA,IAAAG,MAAA;MACAjG,cAAA,CAAAqB,SAAA,EAAA2D,IAAA,WAAAgB,GAAA;QACAC,MAAA,CAAAzE,QAAA,GAAAwE,GAAA,CAAA1E,IAAA;QACA2E,MAAA,CAAAJ,cAAA,CAAAC,SAAA;MACA;IACA;IACAI,OAAA,WAAAA,QAAAC,GAAA;MACA,IAAAA,GAAA;QACA;MACA;QACA;MACA;IACA;IACAC,QAAA,WAAAA,SAAAD,GAAA;MACA,IAAAA,GAAA;QACA;MACA;QACA;MACA;IACA;IACA;IACAE,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAtC,QAAA,GAAAsC,SAAA;MACA,IAAAH,GAAA,GAAAG,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,MAAA;MAAA;MACA,IAAAN,GAAA,YAAAO,KAAA;QACA,KAAAnE,QAAA,CAAAoE,MAAA;UACA,YAAAR,GAAA,CAAAS,IAAA;QACA;MACA;QACA,KAAArE,QAAA,CAAAoE,MAAA;UACA,YAAAR;QACA;MACA;IACA;IACA;IACAU,WAAA,WAAAA,YAAAC,GAAA;MACA,KAAA9C,QAAA,CAAA+C,MAAA,MAAA/C,QAAA,CAAA0B,OAAA,CAAAoB,GAAA;IACA;IACA,aACAE,iBAAA,WAAAA,kBAAAb,GAAA;MACA,IAAAA,GAAA,YAAAO,KAAA;QACA,KAAAnE,QAAA,CAAAoE,MAAA;UACA,YAAAR,GAAA,CAAAS,IAAA;QACA;MACA;QACA,KAAArE,QAAA,CAAAoE,MAAA;UACA,YAAAR;QACA;MACA;IACA;IACA,aACA5B,iBAAA,WAAAA,kBAAAlD,SAAA;MAAA,IAAA4F,MAAA;MACA,IAAAC,MAAA;QAAA7F,SAAA,EAAAA;MAAA;MACA1B,UAAA,CAAAuH,MAAA,EAAAlC,IAAA,WAAAgB,GAAA;QACAiB,MAAA,CAAA/E,cAAA,GAAA8D,GAAA,CAAA1E,IAAA,CAAA6F,QAAA;QACA;QACA,IAAAnB,GAAA,CAAA1E,IAAA,CAAA8F,QAAA;UACAH,MAAA,CAAA9D,QAAA,GAAA6C,GAAA,CAAA1E,IAAA,CAAA8F,QAAA;UACAH,MAAA,CAAA7D,YAAA;QACA;MACA,GAAAiE,KAAA,WAAArB,GAAA;QACAiB,MAAA,CAAAK,MAAA;MACA;IACA;IACAC,YAAA,WAAAA,aAAAC,IAAA,EAAAlG,IAAA;MACAkG,IAAA,CAAAC,MAAA,CAAAC,OAAA,WAAAlB,IAAA;QACA,IAAAL,GAAA,GAAA7E,IAAA,CAAAkF,IAAA,CAAAmB,UAAA;QACA,IAAAxB,GAAA;UACAK,IAAA,CAAAoB,UAAA,CAAAC,YAAA,GAAA1B,GAAA;QACA;MACA;IACA;IACA,eACA7B,gBAAA,WAAAA,iBAAAlD,MAAA;MAAA,IAAA0G,MAAA;MACA,IAAA1G,MAAA;QACA;QACAtB,mBAAA,CAAAsB,MAAA,EAAA4D,IAAA,WAAAgB,GAAA;UACA;UACA8B,MAAA,CAAAxE,aAAA,GAAA0C,GAAA,CAAA1E,IAAA,CAAA+B,SAAA;UACAyE,MAAA,CAAAvE,YAAA;QACA;MACA;IACA;IACA,2BACAlD,eAAA,WAAAA,gBAAAe,MAAA;MAAA,IAAA2G,MAAA;MACA;MACA,IAAAb,MAAA;QAAA9F,MAAA,EAAAA;MAAA;MACAf,gBAAA,CAAA6G,MAAA,EAAAlC,IAAA,WAAAgB,GAAA;QACA,IAAA1E,IAAA,GAAA0E,GAAA,CAAA1E,IAAA;QACA,IAAAA,IAAA;UACA,IAAAA,IAAA,CAAAN,IAAA;YACA+G,MAAA,CAAA9E,YAAA,GAAA+C,GAAA,CAAA1E,IAAA,CAAAK,QAAA;UACA,WAAAL,IAAA,CAAAN,IAAA;YACA+G,MAAA,CAAA9E,YAAA,GAAA+C,GAAA,CAAA1E,IAAA,CAAAK,QAAA;YACAoG,MAAA,CAAAxF,QAAA,CAAAK,QAAA;UACA,WAAAtB,IAAA,CAAAN,IAAA;YACAgF,GAAA,CAAA1E,IAAA,CAAA0G,QAAA,CAAAN,OAAA,WAAAO,IAAA;cACAA,IAAA,CAAAxB,MAAA,GAAAwB,IAAA,CAAAC,MAAA;cACAD,IAAA,CAAAE,QAAA,GAAAF,IAAA,CAAAG,QAAA;YACA;YACAL,MAAA,CAAA9E,YAAA,GAAA+C,GAAA,CAAA1E,IAAA,CAAA0G,QAAA;YACAD,MAAA,CAAAxF,QAAA,CAAAK,QAAA;UACA,WAAAtB,IAAA,CAAAN,IAAA;YACA+G,MAAA,CAAA9E,YAAA,GAAA+C,GAAA,CAAA1E,IAAA,CAAAK,QAAA;YACAoG,MAAA,CAAAxF,QAAA,CAAAK,QAAA;UACA;UACAmF,MAAA,CAAAxF,QAAA,CAAAI,YAAA;QACA;MACA;IACA;IACA,aACA0F,cAAA,WAAAA,eAAA;MACA,KAAA1E,YAAA;MACA,KAAAD,aAAA;MACA;IACA;IACA,WACA4E,YAAA,WAAAA,aAAAzF,OAAA;MAAA,IAAA0F,MAAA;MACA;MACA;MACA;MACA;MACA,IAAA1F,OAAA,WAAAA,OAAA,gBAAAA,OAAA,CAAA2F,WAAA,IAAAvH,MAAA;QACA,KAAAsB,QAAA,CAAAM,OAAA,GAAAA,OAAA;MACA;MACA,UAAAN,QAAA,CAAAM,OAAA;QACA,KAAA4F,QAAA;QACA;MACA;MACAxI,QAAA,MAAAsC,QAAA,EAAAyC,IAAA,WAAAC,QAAA;QACAsD,MAAA,CAAAG,UAAA,CAAAzD,QAAA,CAAA0D,GAAA;QACAJ,MAAA,CAAAjB,MAAA;MACA;IACA;IACA,WACAsB,cAAA,WAAAA,eAAA;MACA,KAAArG,QAAA,CAAAE,gBAAA;MACA,KAAAF,QAAA,CAAAG,eAAA;IACA;IACAmG,YAAA,WAAAA,aAAA,GAEA;IACA,WACAvB,MAAA,WAAAA,OAAA;MACA;MACA,KAAAwB,MAAA,CAAAC,QAAA,0BAAAvE,MAAA;MACA,KAAAwE,OAAA,CAAAC,EAAA;IACA;IACA,eACAC,OAAA,WAAAA,QAAA5H,IAAA;MACA,IAAAA,IAAA;QACA,IAAA+B,SAAA;QACA/B,IAAA,CAAAmG,MAAA,CAAAC,OAAA,WAAAlB,IAAA;UACA,IAAA2C,YAAA;UACAA,YAAA,CAAArH,KAAA,GAAA0E,IAAA,CAAAoB,UAAA,CAAA9F,KAAA;UACA;UACA,IAAA0E,IAAA,CAAAoB,UAAA,CAAAC,YAAA,YAAAnB,KAAA;YACA,IAAA0C,KAAA;YACA5C,IAAA,CAAAoB,UAAA,CAAAC,YAAA,CAAAH,OAAA,WAAAvB,GAAA;cACAiD,KAAA,CAAAC,IAAA,CAAAlD,GAAA;YACA;YACAgD,YAAA,CAAAhD,GAAA,GAAAiD,KAAA;UACA;YACAD,YAAA,CAAAhD,GAAA,GAAAK,IAAA,CAAAoB,UAAA,CAAAC,YAAA;UACA;UACAxE,SAAA,CAAAgG,IAAA,CAAAF,YAAA;QACA;QACA,KAAA9F,SAAA,GAAAA,SAAA;MACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAiG,SAAA,WAAAA,UAAAC,WAAA,EAAA3I,IAAA,EAAAyC,SAAA;MAAA,IAAAmG,MAAA;MACA,IAAAC,SAAA,GAAA9I,MAAA,KAAA+I,IAAA,IAAAC,MAAA;MACA,IAAArI,IAAA;MACA,SAAAiB,QAAA,CAAAxB,UAAA;QACA,KAAAsC,SAAA;UACA/B,IAAA,CAAA+B,SAAA;QACA;UACA/B,IAAA,CAAA+B,SAAA,GAAAA,SAAA;QACA;QAEA/B,IAAA,CAAAiI,WAAA,GAAAA,WAAA;QACAjI,IAAA,CAAAP,UAAA,QAAAwB,QAAA,CAAAxB,UAAA;QACAO,IAAA,CAAAsI,QAAA,GAAAhJ,IAAA;QACA;QACAf,oBAAA,CAAAgK,IAAA,CAAAC,SAAA,CAAAxI,IAAA,GAAA0D,IAAA,WAAAgB,GAAA;UACAwD,MAAA,CAAAd,UAAA,CAAA1C,GAAA,CAAA2C,GAAA;UACAa,MAAA,CAAAlC,MAAA;QACA;MACA;IAEA;IACA,WACAyC,YAAA,WAAAA,aAAA;MACA,KAAAjG,UAAA;MACA,KAAAC,WAAA;IACA;IACA,WACAiG,UAAA,WAAAA,WAAA;MAAA,IAAAC,OAAA;MACA,KAAAC,KAAA,aAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAlK,UAAA,CAAA+J,OAAA,CAAA1H,QAAA,EAAAyC,IAAA,WAAAgB,GAAA;YACAiE,OAAA,CAAAvB,UAAA,CAAA1C,GAAA,CAAA2C,GAAA;YACAsB,OAAA,CAAA3C,MAAA;UACA;QACA;MACA;IACA;IACA,cACA+C,YAAA,WAAAA,aAAA;MAAA,IAAAC,OAAA;MACA,KAAAzG,UAAA;MACA,KAAAD,WAAA;MACAzD,UAAA,MAAAoC,QAAA,EAAAyC,IAAA,WAAAgB,GAAA;QACAsE,OAAA,CAAA9G,cAAA,GAAAwC,GAAA,CAAA1E,IAAA;QACAgJ,OAAA,CAAA/H,QAAA,CAAAoE,MAAA;MACA;IACA;IACA,aACA4D,UAAA,WAAAA,WAAA;MAAA,IAAAC,OAAA;MACA,KAAAN,KAAA,aAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAhK,UAAA,CAAAoK,OAAA,CAAAjI,QAAA,EAAAyC,IAAA,WAAAgB,GAAA;YACAwE,OAAA,CAAA9B,UAAA,CAAA1C,GAAA,CAAA2C,GAAA;YACA6B,OAAA,CAAAlD,MAAA;UACA;QACA;MACA;IACA;IACA,eACAmD,UAAA,WAAAA,WAAA;MACA,KAAAlI,QAAA,CAAAC,cAAA;MACA,KAAAD,QAAA,CAAAG,eAAA;MACA,KAAAH,QAAA,CAAAI,YAAA;MACA,KAAAa,cAAA;IACA;IACA,WACAkH,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,OAAA;MACA,KAAAT,KAAA,aAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA9J,QAAA,CAAAqK,OAAA,CAAApI,QAAA,EAAAyC,IAAA,WAAAC,QAAA;YACA0F,OAAA,CAAAjC,UAAA,CAAAzD,QAAA,CAAA0D,GAAA;YACAgC,OAAA,CAAArD,MAAA;UACA;QACA;MACA;IACA;IACA,eACAsD,kBAAA,WAAAA,mBAAA;MACA,KAAArI,QAAA,CAAAE,gBAAA;MACA,KAAAF,QAAA,CAAAG,eAAA;MACA,KAAAH,QAAA,CAAAI,YAAA;MACA,KAAAa,cAAA;IACA;EACA;AACA", "ignoreList": []}]}