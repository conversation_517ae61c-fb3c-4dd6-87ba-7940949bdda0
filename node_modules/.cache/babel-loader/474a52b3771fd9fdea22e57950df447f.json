{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/flowable/definition/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/flowable/definition/index.vue", "mtime": 1655049558000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js", "mtime": 1751171659043}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGxpc3REZWZpbml0aW9uLCB1cGRhdGVTdGF0ZSwgZGVsRGVwbG95bWVudCwgYWRkRGVwbG95bWVudCwgdXBkYXRlRGVwbG95bWVudCwgZXhwb3J0RGVwbG95bWVudCwgZGVmaW5pdGlvblN0YXJ0LCByZWFkWG1sIH0gZnJvbSAiQC9hcGkvZmxvd2FibGUvZGVmaW5pdGlvbiI7CmltcG9ydCB7IGdldFRva2VuIH0gZnJvbSAiQC91dGlscy9hdXRoIjsKaW1wb3J0IHsgZ2V0Rm9ybSwgYWRkRGVwbG95Rm9ybSwgbGlzdEZvcm0gfSBmcm9tICJAL2FwaS9mbG93YWJsZS9mb3JtIjsKaW1wb3J0IFBhcnNlciBmcm9tICdAL2NvbXBvbmVudHMvcGFyc2VyL1BhcnNlcic7CmltcG9ydCBmbG93IGZyb20gJ0Avdmlld3MvZmxvd2FibGUvdGFzay9yZWNvcmQvZmxvdyc7CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiRGVmaW5pdGlvbiIsCiAgY29tcG9uZW50czogewogICAgUGFyc2VyOiBQYXJzZXIsCiAgICBmbG93OiBmbG93CiAgfSwKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgLy8g6YGu572p5bGCCiAgICAgIGxvYWRpbmc6IHRydWUsCiAgICAgIC8vIOmAieS4reaVsOe7hAogICAgICBpZHM6IFtdLAogICAgICAvLyDpnZ7ljZXkuKrnpoHnlKgKICAgICAgc2luZ2xlOiB0cnVlLAogICAgICAvLyDpnZ7lpJrkuKrnpoHnlKgKICAgICAgbXVsdGlwbGU6IHRydWUsCiAgICAgIC8vIOaYvuekuuaQnOe0ouadoeS7tgogICAgICBzaG93U2VhcmNoOiB0cnVlLAogICAgICAvLyDmgLvmnaHmlbAKICAgICAgdG90YWw6IDAsCiAgICAgIC8vIOa1geeoi+WumuS5ieihqOagvOaVsOaNrgogICAgICBkZWZpbml0aW9uTGlzdDogW10sCiAgICAgIC8vIOW8ueWHuuWxguagh+mimAogICAgICB0aXRsZTogIiIsCiAgICAgIC8vIOaYr+WQpuaYvuekuuW8ueWHuuWxggogICAgICBvcGVuOiBmYWxzZSwKICAgICAgZm9ybUNvbmZPcGVuOiBmYWxzZSwKICAgICAgZm9ybVRpdGxlOiAiIiwKICAgICAgZm9ybURlcGxveU9wZW46IGZhbHNlLAogICAgICBmb3JtRGVwbG95VGl0bGU6ICIiLAogICAgICBmb3JtTGlzdDogW10sCiAgICAgIGZvcm1Ub3RhbDogMCwKICAgICAgZm9ybUNvbmY6IHt9LAogICAgICAvLyDpu5jorqTooajljZXmlbDmja4KICAgICAgcmVhZEltYWdlOiB7CiAgICAgICAgb3BlbjogZmFsc2UsCiAgICAgICAgc3JjOiAiIgogICAgICB9LAogICAgICAvLyBicG1uLnhtbCDlr7zlhaUKICAgICAgdXBsb2FkOiB7CiAgICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGC77yIeG1s5a+85YWl77yJCiAgICAgICAgb3BlbjogZmFsc2UsCiAgICAgICAgLy8g5by55Ye65bGC5qCH6aKY77yIeG1s5a+85YWl77yJCiAgICAgICAgdGl0bGU6ICIiLAogICAgICAgIC8vIOaYr+WQpuemgeeUqOS4iuS8oAogICAgICAgIGlzVXBsb2FkaW5nOiBmYWxzZSwKICAgICAgICBuYW1lOiBudWxsLAogICAgICAgIGNhdGVnb3J5OiBudWxsLAogICAgICAgIC8vIOiuvue9ruS4iuS8oOeahOivt+axguWktOmDqAogICAgICAgIGhlYWRlcnM6IHsKICAgICAgICAgIEF1dGhvcml6YXRpb246ICJCZWFyZXIgIiArIGdldFRva2VuKCkKICAgICAgICB9LAogICAgICAgIC8vIOS4iuS8oOeahOWcsOWdgAogICAgICAgIHVybDogcHJvY2Vzcy5lbnYuVlVFX0FQUF9CQVNFX0FQSSArICIvZmxvd2FibGUvZGVmaW5pdGlvbi9pbXBvcnQiCiAgICAgIH0sCiAgICAgIC8vIOafpeivouWPguaVsAogICAgICBxdWVyeVBhcmFtczogewogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgIG5hbWU6IG51bGwsCiAgICAgICAgY2F0ZWdvcnk6IG51bGwsCiAgICAgICAga2V5OiBudWxsLAogICAgICAgIHRlbmFudElkOiBudWxsLAogICAgICAgIGRlcGxveVRpbWU6IG51bGwsCiAgICAgICAgZGVyaXZlZEZyb206IG51bGwsCiAgICAgICAgZGVyaXZlZEZyb21Sb290OiBudWxsLAogICAgICAgIHBhcmVudERlcGxveW1lbnRJZDogbnVsbCwKICAgICAgICBlbmdpbmVWZXJzaW9uOiBudWxsCiAgICAgIH0sCiAgICAgIGZvcm1RdWVyeVBhcmFtczogewogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDEwCiAgICAgIH0sCiAgICAgIC8vIOaMgui9veihqOWNleWIsOa1geeoi+WunuS+iwogICAgICBmb3JtRGVwbG95UGFyYW06IHsKICAgICAgICBmb3JtSWQ6IG51bGwsCiAgICAgICAgZGVwbG95SWQ6IG51bGwKICAgICAgfSwKICAgICAgY3VycmVudFJvdzogbnVsbCwKICAgICAgLy8geG1sCiAgICAgIHhtbERhdGE6ICIiLAogICAgICAvLyDooajljZXlj4LmlbAKICAgICAgZm9ybToge30sCiAgICAgIC8vIOihqOWNleagoemqjAogICAgICBydWxlczoge30sCiAgICAgIHByb2Nlc3NDYXRlZ29yeU9wdGlvbnM6IFtdCiAgICB9OwogIH0sCiAgYWN0aXZhdGVkOiBmdW5jdGlvbiBhY3RpdmF0ZWQoKSB7CiAgICB0aGlzLmdldExpc3QoKTsKICB9LAogIGNyZWF0ZWQ6IGZ1bmN0aW9uIGNyZWF0ZWQoKSB7CiAgICB2YXIgX3RoaXMgPSB0aGlzOwogICAgdGhpcy5nZXRMaXN0KCk7CiAgICB0aGlzLmdldERpY3RzKCJzeXNfcHJvY2Vzc19jYXRlZ29yeSIpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgIF90aGlzLnByb2Nlc3NDYXRlZ29yeU9wdGlvbnMgPSByZXNwb25zZS5kYXRhOwogICAgfSk7CiAgfSwKICBtZXRob2RzOiB7CiAgICAvKiog5p+l6K+i5rWB56iL5a6a5LmJ5YiX6KGoICovZ2V0TGlzdDogZnVuY3Rpb24gZ2V0TGlzdCgpIHsKICAgICAgdmFyIF90aGlzMiA9IHRoaXM7CiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7CiAgICAgIGxpc3REZWZpbml0aW9uKHRoaXMucXVlcnlQYXJhbXMpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgX3RoaXMyLmRlZmluaXRpb25MaXN0ID0gcmVzcG9uc2UuZGF0YS5yZWNvcmRzOwogICAgICAgIF90aGlzMi50b3RhbCA9IHJlc3BvbnNlLmRhdGEudG90YWw7CiAgICAgICAgX3RoaXMyLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgfSk7CiAgICB9LAogICAgLy8g57yW6L6R54q25oCB5a2X5YW457+76K+RCiAgICBwcm9jZXNzQ2F0ZWdvcnlGb3JtYXQ6IGZ1bmN0aW9uIHByb2Nlc3NDYXRlZ29yeUZvcm1hdChyb3csIGNvbHVtbikgewogICAgICByZXR1cm4gdGhpcy5zZWxlY3REaWN0TGFiZWwodGhpcy5wcm9jZXNzQ2F0ZWdvcnlPcHRpb25zLCByb3cuY2F0ZWdvcnkpOwogICAgfSwKICAgIC8vIOWPlua2iOaMiemSrgogICAgY2FuY2VsOiBmdW5jdGlvbiBjYW5jZWwoKSB7CiAgICAgIHRoaXMub3BlbiA9IGZhbHNlOwogICAgICB0aGlzLnJlc2V0KCk7CiAgICB9LAogICAgLy8g6KGo5Y2V6YeN572uCiAgICByZXNldDogZnVuY3Rpb24gcmVzZXQoKSB7CiAgICAgIHRoaXMuZm9ybSA9IHsKICAgICAgICBpZDogbnVsbCwKICAgICAgICBuYW1lOiBudWxsLAogICAgICAgIGNhdGVnb3J5OiBudWxsLAogICAgICAgIGtleTogbnVsbCwKICAgICAgICB0ZW5hbnRJZDogbnVsbCwKICAgICAgICBkZXBsb3lUaW1lOiBudWxsLAogICAgICAgIGRlcml2ZWRGcm9tOiBudWxsLAogICAgICAgIGRlcml2ZWRGcm9tUm9vdDogbnVsbCwKICAgICAgICBwYXJlbnREZXBsb3ltZW50SWQ6IG51bGwsCiAgICAgICAgZW5naW5lVmVyc2lvbjogbnVsbAogICAgICB9OwogICAgICB0aGlzLnJlc2V0Rm9ybSgiZm9ybSIpOwogICAgfSwKICAgIC8qKiDmkJzntKLmjInpkq7mk43kvZwgKi9oYW5kbGVRdWVyeTogZnVuY3Rpb24gaGFuZGxlUXVlcnkoKSB7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDE7CiAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgfSwKICAgIC8qKiDph43nva7mjInpkq7mk43kvZwgKi9yZXNldFF1ZXJ5OiBmdW5jdGlvbiByZXNldFF1ZXJ5KCkgewogICAgICB0aGlzLnJlc2V0Rm9ybSgicXVlcnlGb3JtIik7CiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsKICAgIH0sCiAgICAvLyDlpJrpgInmoYbpgInkuK3mlbDmja4KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZTogZnVuY3Rpb24gaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgewogICAgICB0aGlzLmlkcyA9IHNlbGVjdGlvbi5tYXAoZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICByZXR1cm4gaXRlbS5pZDsKICAgICAgfSk7CiAgICAgIHRoaXMuc2luZ2xlID0gc2VsZWN0aW9uLmxlbmd0aCAhPT0gMTsKICAgICAgdGhpcy5tdWx0aXBsZSA9ICFzZWxlY3Rpb24ubGVuZ3RoOwogICAgfSwKICAgIC8qKiDmlrDlop7mjInpkq7mk43kvZwgKi9oYW5kbGVBZGQ6IGZ1bmN0aW9uIGhhbmRsZUFkZCgpIHsKICAgICAgdGhpcy5yZXNldCgpOwogICAgICB0aGlzLm9wZW4gPSB0cnVlOwogICAgICB0aGlzLnRpdGxlID0gIua3u+WKoOa1geeoi+WumuS5iSI7CiAgICB9LAogICAgLyoqIOi3s+i9rOWIsOa1geeoi+iuvuiuoemhtemdoiAqL2hhbmRsZUxvYWRYbWw6IGZ1bmN0aW9uIGhhbmRsZUxvYWRYbWwocm93KSB7CiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHsKICAgICAgICBwYXRoOiAnL2Zsb3dhYmxlL2RlZmluaXRpb24vbW9kZWwnLAogICAgICAgIHF1ZXJ5OiB7CiAgICAgICAgICBkZXBsb3lJZDogcm93LmRlcGxveW1lbnRJZAogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLyoqIOa1geeoi+WbvuafpeeciyAqL2hhbmRsZVJlYWRJbWFnZTogZnVuY3Rpb24gaGFuZGxlUmVhZEltYWdlKGRlcGxveW1lbnRJZCkgewogICAgICB2YXIgX3RoaXMzID0gdGhpczsKICAgICAgdGhpcy5yZWFkSW1hZ2UudGl0bGUgPSAi5rWB56iL5Zu+IjsKICAgICAgdGhpcy5yZWFkSW1hZ2Uub3BlbiA9IHRydWU7CiAgICAgIC8vIHRoaXMucmVhZEltYWdlLnNyYyA9IHByb2Nlc3MuZW52LlZVRV9BUFBfQkFTRV9BUEkgKyAiL2Zsb3dhYmxlL2RlZmluaXRpb24vcmVhZEltYWdlLyIgKyBkZXBsb3ltZW50SWQ7CiAgICAgIC8vIOWPkemAgeivt+axgu+8jOiOt+WPlnhtbAogICAgICByZWFkWG1sKGRlcGxveW1lbnRJZCkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgX3RoaXMzLnhtbERhdGEgPSByZXMuZGF0YTsKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOihqOWNleafpeeciyAqL2hhbmRsZUZvcm06IGZ1bmN0aW9uIGhhbmRsZUZvcm0oZm9ybUlkKSB7CiAgICAgIHZhciBfdGhpczQgPSB0aGlzOwogICAgICBnZXRGb3JtKGZvcm1JZCkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgX3RoaXM0LmZvcm1UaXRsZSA9ICLooajljZXor6bmg4UiOwogICAgICAgIF90aGlzNC5mb3JtQ29uZk9wZW4gPSB0cnVlOwogICAgICAgIF90aGlzNC5mb3JtQ29uZiA9IEpTT04ucGFyc2UocmVzLmRhdGEuZm9ybUNvbnRlbnQpOwogICAgICB9KTsKICAgIH0sCiAgICAvKiog5ZCv5Yqo5rWB56iLICovaGFuZGxlRGVmaW5pdGlvblN0YXJ0OiBmdW5jdGlvbiBoYW5kbGVEZWZpbml0aW9uU3RhcnQocm93KSB7CiAgICAgIHZhciBfdGhpczUgPSB0aGlzOwogICAgICBkZWZpbml0aW9uU3RhcnQocm93LmlkKS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICBfdGhpczUubXNnU3VjY2VzcyhyZXMubXNnKTsKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOaMgui9veihqOWNleW8ueahhiAqL2hhbmRsZUFkZEZvcm06IGZ1bmN0aW9uIGhhbmRsZUFkZEZvcm0ocm93KSB7CiAgICAgIHRoaXMuZm9ybURlcGxveVBhcmFtLmRlcGxveUlkID0gcm93LmRlcGxveW1lbnRJZDsKICAgICAgdGhpcy5MaXN0Rm9ybURlcGxveSgpOwogICAgfSwKICAgIC8qKiDmjILovb3ooajljZXliJfooaggKi9MaXN0Rm9ybURlcGxveTogZnVuY3Rpb24gTGlzdEZvcm1EZXBsb3koKSB7CiAgICAgIHZhciBfdGhpczYgPSB0aGlzOwogICAgICBsaXN0Rm9ybSh0aGlzLmZvcm1RdWVyeVBhcmFtcykudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgX3RoaXM2LmZvcm1MaXN0ID0gcmVzLnJvd3M7CiAgICAgICAgX3RoaXM2LmZvcm1Ub3RhbCA9IHJlcy50b3RhbDsKICAgICAgICBfdGhpczYuZm9ybURlcGxveU9wZW4gPSB0cnVlOwogICAgICAgIF90aGlzNi5mb3JtRGVwbG95VGl0bGUgPSAi5oyC6L296KGo5Y2VIjsKICAgICAgfSk7CiAgICB9LAogICAgLy8gLyoqIOabtOaUueaMgui9veihqOWNleW8ueahhiAqLwogICAgLy8gaGFuZGxlRWRpdEZvcm0ocm93KXsKICAgIC8vICAgdGhpcy5mb3JtRGVwbG95UGFyYW0uZGVwbG95SWQgPSByb3cuZGVwbG95bWVudElkCiAgICAvLyAgIGNvbnN0IHF1ZXJ5UGFyYW1zID0gewogICAgLy8gICAgIHBhZ2VOdW06IDEsCiAgICAvLyAgICAgcGFnZVNpemU6IDEwCiAgICAvLyAgIH0KICAgIC8vICAgbGlzdEZvcm0ocXVlcnlQYXJhbXMpLnRoZW4ocmVzID0+ewogICAgLy8gICAgIHRoaXMuZm9ybUxpc3QgPSByZXMucm93czsKICAgIC8vICAgICB0aGlzLmZvcm1EZXBsb3lPcGVuID0gdHJ1ZTsKICAgIC8vICAgICB0aGlzLmZvcm1EZXBsb3lUaXRsZSA9ICLmjILovb3ooajljZUiOwogICAgLy8gICB9KQogICAgLy8gfSwKICAgIC8qKiDmjILovb3ooajljZUgKi8KICAgIHN1Ym1pdEZvcm1EZXBsb3k6IGZ1bmN0aW9uIHN1Ym1pdEZvcm1EZXBsb3kocm93KSB7CiAgICAgIHZhciBfdGhpczcgPSB0aGlzOwogICAgICB0aGlzLmZvcm1EZXBsb3lQYXJhbS5mb3JtSWQgPSByb3cuZm9ybUlkOwogICAgICBhZGREZXBsb3lGb3JtKHRoaXMuZm9ybURlcGxveVBhcmFtKS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICBfdGhpczcubXNnU3VjY2VzcyhyZXMubXNnKTsKICAgICAgICBfdGhpczcuZm9ybURlcGxveU9wZW4gPSBmYWxzZTsKICAgICAgICBfdGhpczcuZ2V0TGlzdCgpOwogICAgICB9KTsKICAgIH0sCiAgICBoYW5kbGVDdXJyZW50Q2hhbmdlOiBmdW5jdGlvbiBoYW5kbGVDdXJyZW50Q2hhbmdlKGRhdGEpIHsKICAgICAgaWYgKGRhdGEpIHsKICAgICAgICB0aGlzLmN1cnJlbnRSb3cgPSBKU09OLnBhcnNlKGRhdGEuZm9ybUNvbnRlbnQpOwogICAgICB9CiAgICB9LAogICAgLyoqIOaMgui1ty/mv4DmtLvmtYHnqIsgKi9oYW5kbGVVcGRhdGVTdXNwZW5zaW9uU3RhdGU6IGZ1bmN0aW9uIGhhbmRsZVVwZGF0ZVN1c3BlbnNpb25TdGF0ZShyb3cpIHsKICAgICAgdmFyIF90aGlzOCA9IHRoaXM7CiAgICAgIHZhciBzdGF0ZSA9IDE7CiAgICAgIGlmIChyb3cuc3VzcGVuc2lvblN0YXRlID09PSAxKSB7CiAgICAgICAgc3RhdGUgPSAyOwogICAgICB9CiAgICAgIHZhciBwYXJhbXMgPSB7CiAgICAgICAgZGVwbG95SWQ6IHJvdy5kZXBsb3ltZW50SWQsCiAgICAgICAgc3RhdGU6IHN0YXRlCiAgICAgIH07CiAgICAgIHVwZGF0ZVN0YXRlKHBhcmFtcykudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgX3RoaXM4Lm1zZ1N1Y2Nlc3MocmVzLm1zZyk7CiAgICAgICAgX3RoaXM4LmdldExpc3QoKTsKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOS/ruaUueaMiemSruaTjeS9nCAqL2hhbmRsZVVwZGF0ZTogZnVuY3Rpb24gaGFuZGxlVXBkYXRlKHJvdykgewogICAgICB2YXIgX3RoaXM5ID0gdGhpczsKICAgICAgdGhpcy5yZXNldCgpOwogICAgICB2YXIgaWQgPSByb3cuaWQgfHwgdGhpcy5pZHM7CiAgICAgIGdldERlcGxveW1lbnQoaWQpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgX3RoaXM5LmZvcm0gPSByZXNwb25zZS5kYXRhOwogICAgICAgIF90aGlzOS5vcGVuID0gdHJ1ZTsKICAgICAgICBfdGhpczkudGl0bGUgPSAi5L+u5pS55rWB56iL5a6a5LmJIjsKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOaPkOS6pOaMiemSriAqL3N1Ym1pdEZvcm06IGZ1bmN0aW9uIHN1Ym1pdEZvcm0oKSB7CiAgICAgIHZhciBfdGhpczEwID0gdGhpczsKICAgICAgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKGZ1bmN0aW9uICh2YWxpZCkgewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgaWYgKF90aGlzMTAuZm9ybS5pZCAhPSBudWxsKSB7CiAgICAgICAgICAgIHVwZGF0ZURlcGxveW1lbnQoX3RoaXMxMC5mb3JtKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgICAgICAgIF90aGlzMTAubXNnU3VjY2Vzcygi5L+u5pS55oiQ5YqfIik7CiAgICAgICAgICAgICAgX3RoaXMxMC5vcGVuID0gZmFsc2U7CiAgICAgICAgICAgICAgX3RoaXMxMC5nZXRMaXN0KCk7CiAgICAgICAgICAgIH0pOwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgYWRkRGVwbG95bWVudChfdGhpczEwLmZvcm0pLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgICAgICAgX3RoaXMxMC5tc2dTdWNjZXNzKCLmlrDlop7miJDlip8iKTsKICAgICAgICAgICAgICBfdGhpczEwLm9wZW4gPSBmYWxzZTsKICAgICAgICAgICAgICBfdGhpczEwLmdldExpc3QoKTsKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvKiog5Yig6Zmk5oyJ6ZKu5pON5L2cICovaGFuZGxlRGVsZXRlOiBmdW5jdGlvbiBoYW5kbGVEZWxldGUocm93KSB7CiAgICAgIHZhciBfdGhpczExID0gdGhpczsKICAgICAgLy8gY29uc3QgaWRzID0gcm93LmRlcGxveW1lbnRJZCB8fCB0aGlzLmlkczsKICAgICAgdmFyIHBhcmFtcyA9IHsKICAgICAgICBkZXBsb3lJZDogcm93LmRlcGxveW1lbnRJZAogICAgICB9OwogICAgICB0aGlzLiRjb25maXJtKCfmmK/lkKbnoa7orqTliKDpmaTmtYHnqIvlrprkuYnnvJblj7fkuLoiJyArIHBhcmFtcy5kZXBsb3lJZCArICci55qE5pWw5o2u6aG5PycsICLorablkYoiLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLAogICAgICAgIHR5cGU6ICJ3YXJuaW5nIgogICAgICB9KS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICByZXR1cm4gZGVsRGVwbG95bWVudChwYXJhbXMpOwogICAgICB9KS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICBfdGhpczExLmdldExpc3QoKTsKICAgICAgICBfdGhpczExLm1zZ1N1Y2Nlc3MoIuWIoOmZpOaIkOWKnyIpOwogICAgICB9KTsKICAgIH0sCiAgICAvKiog5a+85Ye65oyJ6ZKu5pON5L2cICovaGFuZGxlRXhwb3J0OiBmdW5jdGlvbiBoYW5kbGVFeHBvcnQoKSB7CiAgICAgIHZhciBfdGhpczEyID0gdGhpczsKICAgICAgdmFyIHF1ZXJ5UGFyYW1zID0gdGhpcy5xdWVyeVBhcmFtczsKICAgICAgdGhpcy4kY29uZmlybSgn5piv5ZCm56Gu6K6k5a+85Ye65omA5pyJ5rWB56iL5a6a5LmJ5pWw5o2u6aG5PycsICLorablkYoiLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLAogICAgICAgIHR5cGU6ICJ3YXJuaW5nIgogICAgICB9KS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICByZXR1cm4gZXhwb3J0RGVwbG95bWVudChxdWVyeVBhcmFtcyk7CiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgX3RoaXMxMi5kb3dubG9hZChyZXNwb25zZS5tc2cpOwogICAgICB9KTsKICAgIH0sCiAgICAvKiog5a+85YWlYnBtbi54bWzmlofku7YgKi9oYW5kbGVJbXBvcnQ6IGZ1bmN0aW9uIGhhbmRsZUltcG9ydCgpIHsKICAgICAgdGhpcy51cGxvYWQudGl0bGUgPSAiYnBtbjIwLnhtbOaWh+S7tuWvvOWFpSI7CiAgICAgIHRoaXMudXBsb2FkLm9wZW4gPSB0cnVlOwogICAgfSwKICAgIC8vIOaWh+S7tuS4iuS8oOS4reWkhOeQhgogICAgaGFuZGxlRmlsZVVwbG9hZFByb2dyZXNzOiBmdW5jdGlvbiBoYW5kbGVGaWxlVXBsb2FkUHJvZ3Jlc3MoZXZlbnQsIGZpbGUsIGZpbGVMaXN0KSB7CiAgICAgIHRoaXMudXBsb2FkLmlzVXBsb2FkaW5nID0gdHJ1ZTsKICAgIH0sCiAgICAvLyDmlofku7bkuIrkvKDmiJDlip/lpITnkIYKICAgIGhhbmRsZUZpbGVTdWNjZXNzOiBmdW5jdGlvbiBoYW5kbGVGaWxlU3VjY2VzcyhyZXNwb25zZSwgZmlsZSwgZmlsZUxpc3QpIHsKICAgICAgdGhpcy51cGxvYWQub3BlbiA9IGZhbHNlOwogICAgICB0aGlzLnVwbG9hZC5pc1VwbG9hZGluZyA9IGZhbHNlOwogICAgICB0aGlzLiRyZWZzLnVwbG9hZC5jbGVhckZpbGVzKCk7CiAgICAgIHRoaXMuJG1lc3NhZ2UocmVzcG9uc2UubXNnKTsKICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICB9LAogICAgLy8g5o+Q5Lqk5LiK5Lyg5paH5Lu2CiAgICBzdWJtaXRGaWxlRm9ybTogZnVuY3Rpb24gc3VibWl0RmlsZUZvcm0oKSB7CiAgICAgIHRoaXMuJHJlZnMudXBsb2FkLnN1Ym1pdCgpOwogICAgfQogIH0KfTs="}, null]}