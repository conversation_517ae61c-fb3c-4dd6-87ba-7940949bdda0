{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/dashboard/mixins/resize.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/dashboard/mixins/resize.js", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["debounce", "data", "$_sidebarElm", "$_resizeHandler", "mounted", "initListener", "activated", "resize", "<PERSON><PERSON><PERSON><PERSON>", "destroyListener", "deactivated", "methods", "$_sidebarResizeHandler", "e", "propertyName", "_this", "window", "addEventListener", "document", "getElementsByClassName", "removeEventListener", "chart"], "sources": ["/Users/<USER>/develop/code/project/haijia/adminweb/src/views/dashboard/mixins/resize.js"], "sourcesContent": ["import { debounce } from '@/utils'\n\nexport default {\n  data() {\n    return {\n      $_sidebarElm: null,\n      $_resizeHandler: null\n    }\n  },\n  mounted() {\n    this.initListener()\n  },\n  activated() {\n    if (!this.$_resizeHandler) {\n      // avoid duplication init\n      this.initListener()\n    }\n\n    // when keep-alive chart activated, auto resize\n    this.resize()\n  },\n  beforeD<PERSON>roy() {\n    this.destroyListener()\n  },\n  deactivated() {\n    this.destroyListener()\n  },\n  methods: {\n    // use $_ for mixins properties\n    // https://vuejs.org/v2/style-guide/index.html#Private-property-names-essential\n    $_sidebarResizeHandler(e) {\n      if (e.propertyName === 'width') {\n        this.$_resizeHandler()\n      }\n    },\n    initListener() {\n      this.$_resizeHandler = debounce(() => {\n        this.resize()\n      }, 100)\n      window.addEventListener('resize', this.$_resizeHandler)\n\n      this.$_sidebarElm = document.getElementsByClassName('sidebar-container')[0]\n      this.$_sidebarElm && this.$_sidebarElm.addEventListener('transitionend', this.$_sidebarResizeHandler)\n    },\n    destroyListener() {\n      window.removeEventListener('resize', this.$_resizeHandler)\n      this.$_resizeHandler = null\n\n      this.$_sidebarElm && this.$_sidebarElm.removeEventListener('transitionend', this.$_sidebarResizeHandler)\n    },\n    resize() {\n      const { chart } = this\n      chart && chart.resize()\n    }\n  }\n}\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,SAAS;AAElC,eAAe;EACbC,IAAI,WAAAA,KAAA,EAAG;IACL,OAAO;MACLC,YAAY,EAAE,IAAI;MAClBC,eAAe,EAAE;IACnB,CAAC;EACH,CAAC;EACDC,OAAO,WAAAA,QAAA,EAAG;IACR,IAAI,CAACC,YAAY,CAAC,CAAC;EACrB,CAAC;EACDC,SAAS,WAAAA,UAAA,EAAG;IACV,IAAI,CAAC,IAAI,CAACH,eAAe,EAAE;MACzB;MACA,IAAI,CAACE,YAAY,CAAC,CAAC;IACrB;;IAEA;IACA,IAAI,CAACE,MAAM,CAAC,CAAC;EACf,CAAC;EACDC,aAAa,WAAAA,cAAA,EAAG;IACd,IAAI,CAACC,eAAe,CAAC,CAAC;EACxB,CAAC;EACDC,WAAW,WAAAA,YAAA,EAAG;IACZ,IAAI,CAACD,eAAe,CAAC,CAAC;EACxB,CAAC;EACDE,OAAO,EAAE;IACP;IACA;IACAC,sBAAsB,WAAAA,uBAACC,CAAC,EAAE;MACxB,IAAIA,CAAC,CAACC,YAAY,KAAK,OAAO,EAAE;QAC9B,IAAI,CAACX,eAAe,CAAC,CAAC;MACxB;IACF,CAAC;IACDE,YAAY,WAAAA,aAAA,EAAG;MAAA,IAAAU,KAAA;MACb,IAAI,CAACZ,eAAe,GAAGH,QAAQ,CAAC,YAAM;QACpCe,KAAI,CAACR,MAAM,CAAC,CAAC;MACf,CAAC,EAAE,GAAG,CAAC;MACPS,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAACd,eAAe,CAAC;MAEvD,IAAI,CAACD,YAAY,GAAGgB,QAAQ,CAACC,sBAAsB,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC;MAC3E,IAAI,CAACjB,YAAY,IAAI,IAAI,CAACA,YAAY,CAACe,gBAAgB,CAAC,eAAe,EAAE,IAAI,CAACL,sBAAsB,CAAC;IACvG,CAAC;IACDH,eAAe,WAAAA,gBAAA,EAAG;MAChBO,MAAM,CAACI,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACjB,eAAe,CAAC;MAC1D,IAAI,CAACA,eAAe,GAAG,IAAI;MAE3B,IAAI,CAACD,YAAY,IAAI,IAAI,CAACA,YAAY,CAACkB,mBAAmB,CAAC,eAAe,EAAE,IAAI,CAACR,sBAAsB,CAAC;IAC1G,CAAC;IACDL,MAAM,WAAAA,OAAA,EAAG;MACP,IAAQc,KAAK,GAAK,IAAI,CAAdA,KAAK;MACbA,KAAK,IAAIA,KAAK,CAACd,MAAM,CAAC,CAAC;IACzB;EACF;AACF,CAAC", "ignoreList": []}]}