{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/directive/permission/hasPermi.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/directive/permission/hasPermi.js", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:LyoqCiog5pON5L2c5p2D6ZmQ5aSE55CGCiogQ29weXJpZ2h0IChjKSAyMDE5IHJ1b3lpCiovCgppbXBvcnQgc3RvcmUgZnJvbSAnQC9zdG9yZSc7CmV4cG9ydCBkZWZhdWx0IHsKICBpbnNlcnRlZDogZnVuY3Rpb24gaW5zZXJ0ZWQoZWwsIGJpbmRpbmcsIHZub2RlKSB7CiAgICB2YXIgdmFsdWUgPSBiaW5kaW5nLnZhbHVlOwogICAgdmFyIGFsbF9wZXJtaXNzaW9uID0gIio6KjoqIjsKICAgIHZhciBwZXJtaXNzaW9ucyA9IHN0b3JlLmdldHRlcnMgJiYgc3RvcmUuZ2V0dGVycy5wZXJtaXNzaW9uczsKICAgIGlmICh2YWx1ZSAmJiB2YWx1ZSBpbnN0YW5jZW9mIEFycmF5ICYmIHZhbHVlLmxlbmd0aCA+IDApIHsKICAgICAgdmFyIHBlcm1pc3Npb25GbGFnID0gdmFsdWU7CiAgICAgIHZhciBoYXNQZXJtaXNzaW9ucyA9IHBlcm1pc3Npb25zLnNvbWUoZnVuY3Rpb24gKHBlcm1pc3Npb24pIHsKICAgICAgICByZXR1cm4gYWxsX3Blcm1pc3Npb24gPT09IHBlcm1pc3Npb24gfHwgcGVybWlzc2lvbkZsYWcuaW5jbHVkZXMocGVybWlzc2lvbik7CiAgICAgIH0pOwogICAgICBpZiAoIWhhc1Blcm1pc3Npb25zKSB7CiAgICAgICAgZWwucGFyZW50Tm9kZSAmJiBlbC5wYXJlbnROb2RlLnJlbW92ZUNoaWxkKGVsKTsKICAgICAgfQogICAgfSBlbHNlIHsKICAgICAgdGhyb3cgbmV3IEVycm9yKCJcdThCRjdcdThCQkVcdTdGNkVcdTY0Q0RcdTRGNUNcdTY3NDNcdTk2NTBcdTY4MDdcdTdCN0VcdTUwM0MiKTsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["store", "inserted", "el", "binding", "vnode", "value", "all_permission", "permissions", "getters", "Array", "length", "permissionFlag", "hasPermissions", "some", "permission", "includes", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "Error"], "sources": ["/Users/<USER>/develop/code/project/haijia/adminweb/src/directive/permission/hasPermi.js"], "sourcesContent": [" /**\n * 操作权限处理\n * Copyright (c) 2019 ruoyi\n */\n \nimport store from '@/store'\n\nexport default {\n  inserted(el, binding, vnode) {\n    const { value } = binding\n    const all_permission = \"*:*:*\";\n    const permissions = store.getters && store.getters.permissions\n\n    if (value && value instanceof Array && value.length > 0) {\n      const permissionFlag = value\n\n      const hasPermissions = permissions.some(permission => {\n        return all_permission === permission || permissionFlag.includes(permission)\n      })\n\n      if (!hasPermissions) {\n        el.parentNode && el.parentNode.removeChild(el)\n      }\n    } else {\n      throw new Error(`请设置操作权限标签值`)\n    }\n  }\n}\n"], "mappings": "AAAC;AACD;AACA;AACA;;AAEA,OAAOA,KAAK,MAAM,SAAS;AAE3B,eAAe;EACbC,QAAQ,WAAAA,SAACC,EAAE,EAAEC,OAAO,EAAEC,KAAK,EAAE;IAC3B,IAAQC,KAAK,GAAKF,OAAO,CAAjBE,KAAK;IACb,IAAMC,cAAc,GAAG,OAAO;IAC9B,IAAMC,WAAW,GAAGP,KAAK,CAACQ,OAAO,IAAIR,KAAK,CAACQ,OAAO,CAACD,WAAW;IAE9D,IAAIF,KAAK,IAAIA,KAAK,YAAYI,KAAK,IAAIJ,KAAK,CAACK,MAAM,GAAG,CAAC,EAAE;MACvD,IAAMC,cAAc,GAAGN,KAAK;MAE5B,IAAMO,cAAc,GAAGL,WAAW,CAACM,IAAI,CAAC,UAAAC,UAAU,EAAI;QACpD,OAAOR,cAAc,KAAKQ,UAAU,IAAIH,cAAc,CAACI,QAAQ,CAACD,UAAU,CAAC;MAC7E,CAAC,CAAC;MAEF,IAAI,CAACF,cAAc,EAAE;QACnBV,EAAE,CAACc,UAAU,IAAId,EAAE,CAACc,UAAU,CAACC,WAAW,CAACf,EAAE,CAAC;MAChD;IACF,CAAC,MAAM;MACL,MAAM,IAAIgB,KAAK,+DAAa,CAAC;IAC/B;EACF;AACF,CAAC", "ignoreList": []}]}