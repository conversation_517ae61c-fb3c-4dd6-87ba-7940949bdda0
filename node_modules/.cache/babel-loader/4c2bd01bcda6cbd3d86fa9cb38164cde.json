{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Process/common/customTranslate.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Process/common/customTranslate.js", "mtime": 1641277326000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHRyYW5zbGF0aW9ucyBmcm9tICcuLi9sYW5nL3poJzsKZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gY3VzdG9tVHJhbnNsYXRlKHRlbXBsYXRlLCByZXBsYWNlbWVudHMpIHsKICByZXBsYWNlbWVudHMgPSByZXBsYWNlbWVudHMgfHwge307CgogIC8vIFRyYW5zbGF0ZQogIHRlbXBsYXRlID0gdHJhbnNsYXRpb25zW3RlbXBsYXRlXSB8fCB0ZW1wbGF0ZTsKCiAgLy8gUmVwbGFjZQogIHJldHVybiB0ZW1wbGF0ZS5yZXBsYWNlKC97KFtefV0rKX0vZywgZnVuY3Rpb24gKF8sIGtleSkgewogICAgdmFyIHN0ciA9IHJlcGxhY2VtZW50c1trZXldOwogICAgaWYgKHRyYW5zbGF0aW9uc1tyZXBsYWNlbWVudHNba2V5XV0gIT09IG51bGwgJiYgdHJhbnNsYXRpb25zW3JlcGxhY2VtZW50c1trZXldXSAhPT0gJ3VuZGVmaW5lZCcpIHsKICAgICAgc3RyID0gdHJhbnNsYXRpb25zW3JlcGxhY2VtZW50c1trZXldXTsKICAgIH0KICAgIHJldHVybiBzdHIgfHwgJ3snICsga2V5ICsgJ30nOwogIH0pOwp9"}, {"version": 3, "names": ["translations", "customTranslate", "template", "replacements", "replace", "_", "key", "str"], "sources": ["/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Process/common/customTranslate.js"], "sourcesContent": ["import translations from '../lang/zh'\n\nexport default function customTranslate(template, replacements) {\n  replacements = replacements || {}\n\n  // Translate\n  template = translations[template] || template\n\n  // Replace\n  return template.replace(/{([^}]+)}/g, function(_, key) {\n    var str = replacements[key]\n    if (\n      translations[replacements[key]] !== null &&\n      translations[replacements[key]] !== 'undefined'\n    ) {\n      str = translations[replacements[key]]\n    }\n    return str || '{' + key + '}'\n  })\n}\n"], "mappings": "AAAA,OAAOA,YAAY,MAAM,YAAY;AAErC,eAAe,SAASC,eAAeA,CAACC,QAAQ,EAAEC,YAAY,EAAE;EAC9DA,YAAY,GAAGA,YAAY,IAAI,CAAC,CAAC;;EAEjC;EACAD,QAAQ,GAAGF,YAAY,CAACE,QAAQ,CAAC,IAAIA,QAAQ;;EAE7C;EACA,OAAOA,QAAQ,CAACE,OAAO,CAAC,YAAY,EAAE,UAASC,CAAC,EAAEC,GAAG,EAAE;IACrD,IAAIC,GAAG,GAAGJ,YAAY,CAACG,GAAG,CAAC;IAC3B,IACEN,YAAY,CAACG,YAAY,CAACG,GAAG,CAAC,CAAC,KAAK,IAAI,IACxCN,YAAY,CAACG,YAAY,CAACG,GAAG,CAAC,CAAC,KAAK,WAAW,EAC/C;MACAC,GAAG,GAAGP,YAAY,CAACG,YAAY,CAACG,<PERSON>G,CAAC,CAAC;IACvC;IACA,OAAOC,GAAG,IAAI,GAAG,GAAGD,GAAG,GAAG,GAAG;EAC/B,CAAC,CAAC;AACJ", "ignoreList": []}]}