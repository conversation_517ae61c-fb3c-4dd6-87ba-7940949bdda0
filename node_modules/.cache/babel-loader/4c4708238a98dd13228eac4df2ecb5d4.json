{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/layout/components/Sidebar/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/layout/components/Sidebar/index.vue", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["mapGetters", "mapState", "Logo", "SidebarItem", "variables", "components", "computed", "_objectSpread", "activeMenu", "route", "$route", "meta", "path", "showLogo", "$store", "state", "settings", "sidebarLogo", "isCollapse", "sidebar", "opened"], "sources": ["src/layout/components/Sidebar/index.vue"], "sourcesContent": ["<template>\n    <div :class=\"{'has-logo':showLogo}\" :style=\"{ backgroundColor: settings.sideTheme === 'theme-dark' ? variables.menuBg : variables.menuLightBg }\">\n        <logo v-if=\"showLogo\" :collapse=\"isCollapse\" />\n        <el-scrollbar :class=\"settings.sideTheme\" wrap-class=\"scrollbar-wrapper\">\n            <el-menu\n                :default-active=\"activeMenu\"\n                :collapse=\"isCollapse\"\n                :background-color=\"settings.sideTheme === 'theme-dark' ? variables.menuBg : variables.menuLightBg\"\n                :text-color=\"settings.sideTheme === 'theme-dark' ? variables.menuText : 'rgba(0,0,0,.65)'\"\n                :unique-opened=\"true\"\n                :active-text-color=\"settings.theme\"\n                :collapse-transition=\"false\"\n                mode=\"vertical\"\n            >\n                <sidebar-item\n                    v-for=\"(route, index) in sidebarRouters\"\n                    :key=\"route.path  + index\"\n                    :item=\"route\"\n                    :base-path=\"route.path\"\n                />\n            </el-menu>\n        </el-scrollbar>\n    </div>\n</template>\n\n<script>\nimport { mapGetters, mapState } from \"vuex\";\nimport Logo from \"./Logo\";\nimport SidebarItem from \"./SidebarItem\";\nimport variables from \"@/assets/styles/variables.scss\";\n\nexport default {\n    components: { SidebarItem, Logo },\n    computed: {\n        ...mapState([\"settings\"]),\n        ...mapGetters([\"sidebarRouters\", \"sidebar\"]),\n        activeMenu() {\n            const route = this.$route;\n            const { meta, path } = route;\n            // if set path, the sidebar will highlight the path you set\n            if (meta.activeMenu) {\n                return meta.activeMenu;\n            }\n            return path;\n        },\n        showLogo() {\n            return this.$store.state.settings.sidebarLogo;\n        },\n        variables() {\n            return variables;\n        },\n        isCollapse() {\n            return !this.sidebar.opened;\n        }\n    }\n};\n</script>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BA,SAAAA,UAAA,EAAAC,QAAA;AACA,OAAAC,IAAA;AACA,OAAAC,WAAA;AACA,OAAAC,UAAA;AAEA;EACAC,UAAA;IAAAF,WAAA,EAAAA,WAAA;IAAAD,IAAA,EAAAA;EAAA;EACAI,QAAA,EAAAC,aAAA,CAAAA,aAAA,CAAAA,aAAA,KACAN,QAAA,iBACAD,UAAA;IACAQ,UAAA,WAAAA,WAAA;MACA,IAAAC,KAAA,QAAAC,MAAA;MACA,IAAAC,IAAA,GAAAF,KAAA,CAAAE,IAAA;QAAAC,IAAA,GAAAH,KAAA,CAAAG,IAAA;MACA;MACA,IAAAD,IAAA,CAAAH,UAAA;QACA,OAAAG,IAAA,CAAAH,UAAA;MACA;MACA,OAAAI,IAAA;IACA;IACAC,QAAA,WAAAA,SAAA;MACA,YAAAC,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAC,WAAA;IACA;IACAb,SAAA,WAAAA,UAAA;MACA,OAAAA,UAAA;IACA;IACAc,UAAA,WAAAA,WAAA;MACA,aAAAC,OAAA,CAAAC,MAAA;IACA;EAAA;AAEA", "ignoreList": []}]}