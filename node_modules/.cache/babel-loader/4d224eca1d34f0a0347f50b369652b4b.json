{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/api/system/post.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/api/system/post.js", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QgZnJvbSAnQC91dGlscy9yZXF1ZXN0JzsKCi8vIOafpeivouWyl+S9jeWIl+ihqApleHBvcnQgZnVuY3Rpb24gbGlzdFBvc3QocXVlcnkpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvc3lzdGVtL3Bvc3QvbGlzdCcsCiAgICBtZXRob2Q6ICdnZXQnLAogICAgcGFyYW1zOiBxdWVyeQogIH0pOwp9CgovLyDmn6Xor6LlspfkvY3or6bnu4YKZXhwb3J0IGZ1bmN0aW9uIGdldFBvc3QocG9zdElkKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL3N5c3RlbS9wb3N0LycgKyBwb3N0SWQsCiAgICBtZXRob2Q6ICdnZXQnCiAgfSk7Cn0KCi8vIOaWsOWinuWyl+S9jQpleHBvcnQgZnVuY3Rpb24gYWRkUG9zdChkYXRhKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL3N5c3RlbS9wb3N0JywKICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgZGF0YTogZGF0YQogIH0pOwp9CgovLyDkv67mlLnlspfkvY0KZXhwb3J0IGZ1bmN0aW9uIHVwZGF0ZVBvc3QoZGF0YSkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9zeXN0ZW0vcG9zdCcsCiAgICBtZXRob2Q6ICdwdXQnLAogICAgZGF0YTogZGF0YQogIH0pOwp9CgovLyDliKDpmaTlspfkvY0KZXhwb3J0IGZ1bmN0aW9uIGRlbFBvc3QocG9zdElkKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL3N5c3RlbS9wb3N0LycgKyBwb3N0SWQsCiAgICBtZXRob2Q6ICdkZWxldGUnCiAgfSk7Cn0KCi8vIOWvvOWHuuWyl+S9jQpleHBvcnQgZnVuY3Rpb24gZXhwb3J0UG9zdChxdWVyeSkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9zeXN0ZW0vcG9zdC9leHBvcnQnLAogICAgbWV0aG9kOiAnZ2V0JywKICAgIHBhcmFtczogcXVlcnkKICB9KTsKfQ=="}, {"version": 3, "names": ["request", "listPost", "query", "url", "method", "params", "getPost", "postId", "addPost", "data", "updatePost", "delPost", "exportPost"], "sources": ["/Users/<USER>/develop/code/project/haijia/adminweb/src/api/system/post.js"], "sourcesContent": ["import request from '@/utils/request'\n\n// 查询岗位列表\nexport function listPost(query) {\n  return request({\n    url: '/system/post/list',\n    method: 'get',\n    params: query\n  })\n}\n\n// 查询岗位详细\nexport function getPost(postId) {\n  return request({\n    url: '/system/post/' + postId,\n    method: 'get'\n  })\n}\n\n// 新增岗位\nexport function addPost(data) {\n  return request({\n    url: '/system/post',\n    method: 'post',\n    data: data\n  })\n}\n\n// 修改岗位\nexport function updatePost(data) {\n  return request({\n    url: '/system/post',\n    method: 'put',\n    data: data\n  })\n}\n\n// 删除岗位\nexport function delPost(postId) {\n  return request({\n    url: '/system/post/' + postId,\n    method: 'delete'\n  })\n}\n\n// 导出岗位\nexport function exportPost(query) {\n  return request({\n    url: '/system/post/export',\n    method: 'get',\n    params: query\n  })\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;;AAErC;AACA,OAAO,SAASC,QAAQA,CAACC,KAAK,EAAE;EAC9B,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEH;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASI,OAAOA,CAACC,MAAM,EAAE;EAC9B,OAAOP,OAAO,CAAC;IACbG,GAAG,EAAE,eAAe,GAAGI,MAAM;IAC7BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASI,OAAOA,CAACC,IAAI,EAAE;EAC5B,OAAOT,OAAO,CAAC;IACbG,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASC,UAAUA,CAACD,IAAI,EAAE;EAC/B,OAAOT,OAAO,CAAC;IACbG,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASE,OAAOA,CAACJ,MAAM,EAAE;EAC9B,OAAOP,OAAO,CAAC;IACbG,GAAG,EAAE,eAAe,GAAGI,MAAM;IAC7BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASQ,UAAUA,CAACV,KAAK,EAAE;EAChC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,qBAAqB;IAC1BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEH;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}