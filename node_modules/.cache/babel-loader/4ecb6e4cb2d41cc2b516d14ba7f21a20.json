{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Process/flowable/showConfig.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Process/flowable/showConfig.js", "mtime": 1650986882000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:ZXhwb3J0IGRlZmF1bHQgewogICdicG1uOkVuZEV2ZW50Jzoge30sCiAgJ2JwbW46U3RhcnRFdmVudCc6IHsKICAgIGluaXRpYXRvcjogdHJ1ZSwKICAgIGZvcm1LZXk6IHRydWUKICB9LAogICdicG1uOlVzZXJUYXNrJzogewogICAgdXNlclR5cGU6IHRydWUsCiAgICBkYXRhVHlwZTogdHJ1ZSwKICAgIGFzc2lnbmVlOiB0cnVlLAogICAgY2FuZGlkYXRlVXNlcnM6IHRydWUsCiAgICBjYW5kaWRhdGVHcm91cHM6IHRydWUsCiAgICAvLyBhc3NpZ25lZUZpeGVkOiB0cnVlLAogICAgLy8gY2FuZGlkYXRlVXNlcnNGaXhlZDogdHJ1ZSwKICAgIC8vIGNhbmRpZGF0ZUdyb3Vwc0ZpeGVkOiB0cnVlLAogICAgYXN5bmM6IGZhbHNlLAogICAgcHJpb3JpdHk6IGZhbHNlLAogICAgZm9ybUtleTogZmFsc2UsCiAgICBza2lwRXhwcmVzc2lvbjogZmFsc2UsCiAgICAvLyBkdWVEYXRlOiB0cnVlLAogICAgdGFza0xpc3RlbmVyOiB0cnVlCiAgfSwKICAnYnBtbjpTZXJ2aWNlVGFzayc6IHsKICAgIGFzeW5jOiB0cnVlLAogICAgc2tpcEV4cHJlc3Npb246IHRydWUsCiAgICBpc0ZvckNvbXBlbnNhdGlvbjogdHJ1ZSwKICAgIHRyaWdnZXJhYmxlOiB0cnVlLAogICAgY2xhc3M6IHRydWUKICB9LAogICdicG1uOlNjcmlwdFRhc2snOiB7CiAgICBhc3luYzogdHJ1ZSwKICAgIGlzRm9yQ29tcGVuc2F0aW9uOiB0cnVlLAogICAgYXV0b1N0b3JlVmFyaWFibGVzOiB0cnVlCiAgfSwKICAnYnBtbjpNYW51YWxUYXNrJzogewogICAgYXN5bmM6IHRydWUsCiAgICBpc0ZvckNvbXBlbnNhdGlvbjogdHJ1ZQogIH0sCiAgJ2JwbW46UmVjZWl2ZVRhc2snOiB7CiAgICBhc3luYzogdHJ1ZSwKICAgIGlzRm9yQ29tcGVuc2F0aW9uOiB0cnVlCiAgfSwKICAnYnBtbjpTZW5kVGFzayc6IHsKICAgIGFzeW5jOiB0cnVlLAogICAgaXNGb3JDb21wZW5zYXRpb246IHRydWUKICB9LAogICdicG1uOkJ1c2luZXNzUnVsZVRhc2snOiB7CiAgICBhc3luYzogdHJ1ZSwKICAgIGlzRm9yQ29tcGVuc2F0aW9uOiB0cnVlLAogICAgcnVsZVZhcmlhYmxlc0lucHV0OiB0cnVlLAogICAgcnVsZXM6IHRydWUsCiAgICByZXN1bHRWYXJpYWJsZTogdHJ1ZSwKICAgIGV4Y2x1ZGU6IHRydWUKICB9Cn07"}, {"version": 3, "names": ["initiator", "formKey", "userType", "dataType", "assignee", "candidateUsers", "candidateGroups", "async", "priority", "skipExpression", "taskListener", "isForCompensation", "triggerable", "class", "autoStoreVariables", "ruleVariablesInput", "rules", "resultVariable", "exclude"], "sources": ["/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Process/flowable/showConfig.js"], "sourcesContent": ["export default {\n  'bpmn:EndEvent': {},\n  'bpmn:StartEvent': {\n    initiator: true,\n    formKey: true\n  },\n  'bpmn:UserTask': {\n    userType: true,\n    dataType: true,\n    assignee: true,\n    candidateUsers: true,\n    candidateGroups: true,\n    // assigneeFixed: true,\n    // candidateUsersFixed: true,\n    // candidateGroupsFixed: true,\n    async: false,\n    priority: false,\n    formKey: false,\n    skipExpression: false,\n    // dueDate: true,\n    taskListener: true\n  },\n  'bpmn:ServiceTask': {\n    async: true,\n    skipExpression: true,\n    isForCompensation: true,\n    triggerable: true,\n    class: true\n  },\n  'bpmn:ScriptTask': {\n    async: true,\n    isForCompensation: true,\n    autoStoreVariables: true\n  },\n  'bpmn:ManualTask': {\n    async: true,\n    isForCompensation: true\n  },\n  'bpmn:ReceiveTask': {\n    async: true,\n    isForCompensation: true\n  },\n  'bpmn:SendTask': {\n    async: true,\n    isForCompensation: true\n  },\n  'bpmn:BusinessRuleTask': {\n    async: true,\n    isForCompensation: true,\n    ruleVariablesInput: true,\n    rules: true,\n    resultVariable: true,\n    exclude: true\n  }\n}\n"], "mappings": "AAAA,eAAe;EACb,eAAe,EAAE,CAAC,CAAC;EACnB,iBAAiB,EAAE;IACjBA,SAAS,EAAE,IAAI;IACfC,OAAO,EAAE;EACX,CAAC;EACD,eAAe,EAAE;IACfC,QAAQ,EAAE,IAAI;IACdC,QAAQ,EAAE,IAAI;IACdC,QAAQ,EAAE,IAAI;IACdC,cAAc,EAAE,IAAI;IACpBC,eAAe,EAAE,IAAI;IACrB;IACA;IACA;IACAC,KAAK,EAAE,KAAK;IACZC,QAAQ,EAAE,KAAK;IACfP,OAAO,EAAE,KAAK;IACdQ,cAAc,EAAE,KAAK;IACrB;IACAC,YAAY,EAAE;EAChB,CAAC;EACD,kBAAkB,EAAE;IAClBH,KAAK,EAAE,IAAI;IACXE,cAAc,EAAE,IAAI;IACpBE,iBAAiB,EAAE,IAAI;IACvBC,WAAW,EAAE,IAAI;IACjBC,KAAK,EAAE;EACT,CAAC;EACD,iBAAiB,EAAE;IACjBN,KAAK,EAAE,IAAI;IACXI,iBAAiB,EAAE,IAAI;IACvBG,kBAAkB,EAAE;EACtB,CAAC;EACD,iBAAiB,EAAE;IACjBP,KAAK,EAAE,IAAI;IACXI,iBAAiB,EAAE;EACrB,CAAC;EACD,kBAAkB,EAAE;IAClBJ,KAAK,EAAE,IAAI;IACXI,iBAAiB,EAAE;EACrB,CAAC;EACD,eAAe,EAAE;IACfJ,KAAK,EAAE,IAAI;IACXI,iBAAiB,EAAE;EACrB,CAAC;EACD,uBAAuB,EAAE;IACvBJ,KAAK,EAAE,IAAI;IACXI,iBAAiB,EAAE,IAAI;IACvBI,kBAAkB,EAAE,IAAI;IACxBC,KAAK,EAAE,IAAI;IACXC,cAAc,EAAE,IAAI;IACpBC,OAAO,EAAE;EACX;AACF,CAAC", "ignoreList": []}]}