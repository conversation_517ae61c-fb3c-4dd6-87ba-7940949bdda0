{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/utils/scroll-to.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/utils/scroll-to.js", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["Math", "easeInOutQuad", "t", "b", "c", "d", "requestAnimFrame", "window", "requestAnimationFrame", "webkitRequestAnimationFrame", "mozRequestAnimationFrame", "callback", "setTimeout", "move", "amount", "document", "documentElement", "scrollTop", "body", "parentNode", "position", "scrollTo", "to", "duration", "start", "change", "increment", "currentTime", "animateScroll", "val"], "sources": ["/Users/<USER>/develop/code/project/haijia/adminweb/src/utils/scroll-to.js"], "sourcesContent": ["Math.easeInOutQuad = function(t, b, c, d) {\n  t /= d / 2\n  if (t < 1) {\n    return c / 2 * t * t + b\n  }\n  t--\n  return -c / 2 * (t * (t - 2) - 1) + b\n}\n\n// requestAnimationFrame for Smart Animating http://goo.gl/sx5sts\nvar requestAnimFrame = (function() {\n  return window.requestAnimationFrame || window.webkitRequestAnimationFrame || window.mozRequestAnimationFrame || function(callback) { window.setTimeout(callback, 1000 / 60) }\n})()\n\n/**\n * Because it's so fucking difficult to detect the scrolling element, just move them all\n * @param {number} amount\n */\nfunction move(amount) {\n  document.documentElement.scrollTop = amount\n  document.body.parentNode.scrollTop = amount\n  document.body.scrollTop = amount\n}\n\nfunction position() {\n  return document.documentElement.scrollTop || document.body.parentNode.scrollTop || document.body.scrollTop\n}\n\n/**\n * @param {number} to\n * @param {number} duration\n * @param {Function} callback\n */\nexport function scrollTo(to, duration, callback) {\n  const start = position()\n  const change = to - start\n  const increment = 20\n  let currentTime = 0\n  duration = (typeof (duration) === 'undefined') ? 500 : duration\n  var animateScroll = function() {\n    // increment the time\n    currentTime += increment\n    // find the value with the quadratic in-out easing function\n    var val = Math.easeInOutQuad(currentTime, start, change, duration)\n    // move the document.body\n    move(val)\n    // do the animation unless its over\n    if (currentTime < duration) {\n      requestAnimFrame(animateScroll)\n    } else {\n      if (callback && typeof (callback) === 'function') {\n        // the animation is done so lets callback\n        callback()\n      }\n    }\n  }\n  animateScroll()\n}\n"], "mappings": "AAAAA,IAAI,CAACC,aAAa,GAAG,UAASC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EACxCH,CAAC,IAAIG,CAAC,GAAG,CAAC;EACV,IAAIH,CAAC,GAAG,CAAC,EAAE;IACT,OAAOE,CAAC,GAAG,CAAC,GAAGF,CAAC,GAAGA,CAAC,GAAGC,CAAC;EAC1B;EACAD,CAAC,EAAE;EACH,OAAO,CAACE,CAAC,GAAG,CAAC,IAAIF,CAAC,IAAIA,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAGC,CAAC;AACvC,CAAC;;AAED;AACA,IAAIG,gBAAgB,GAAI,YAAW;EACjC,OAAOC,MAAM,CAACC,qBAAqB,IAAID,MAAM,CAACE,2BAA2B,IAAIF,MAAM,CAACG,wBAAwB,IAAI,UAASC,QAAQ,EAAE;IAAEJ,MAAM,CAACK,UAAU,CAACD,QAAQ,EAAE,IAAI,GAAG,EAAE,CAAC;EAAC,CAAC;AAC/K,CAAC,CAAE,CAAC;;AAEJ;AACA;AACA;AACA;AACA,SAASE,IAAIA,CAACC,MAAM,EAAE;EACpBC,QAAQ,CAACC,eAAe,CAACC,SAAS,GAAGH,MAAM;EAC3CC,QAAQ,CAACG,IAAI,CAACC,UAAU,CAACF,SAAS,GAAGH,MAAM;EAC3CC,QAAQ,CAACG,IAAI,CAACD,SAAS,GAAGH,MAAM;AAClC;AAEA,SAASM,QAAQA,CAAA,EAAG;EAClB,OAAOL,QAAQ,CAACC,eAAe,CAACC,SAAS,IAAIF,QAAQ,CAACG,IAAI,CAACC,UAAU,CAACF,SAAS,IAAIF,QAAQ,CAACG,IAAI,CAACD,SAAS;AAC5G;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASI,QAAQA,CAACC,EAAE,EAAEC,QAAQ,EAAEZ,QAAQ,EAAE;EAC/C,IAAMa,KAAK,GAAGJ,QAAQ,CAAC,CAAC;EACxB,IAAMK,MAAM,GAAGH,EAAE,GAAGE,KAAK;EACzB,IAAME,SAAS,GAAG,EAAE;EACpB,IAAIC,WAAW,GAAG,CAAC;EACnBJ,QAAQ,GAAI,OAAQA,QAAS,KAAK,WAAW,GAAI,GAAG,GAAGA,QAAQ;EAC/D,IAAIK,aAAa,GAAG,SAAhBA,aAAaA,CAAA,EAAc;IAC7B;IACAD,WAAW,IAAID,SAAS;IACxB;IACA,IAAIG,GAAG,GAAG7B,IAAI,CAACC,aAAa,CAAC0B,WAAW,EAAEH,KAAK,EAAEC,MAAM,EAAEF,QAAQ,CAAC;IAClE;IACAV,IAAI,CAACgB,GAAG,CAAC;IACT;IACA,IAAIF,WAAW,GAAGJ,QAAQ,EAAE;MAC1BjB,gBAAgB,CAACsB,aAAa,CAAC;IACjC,CAAC,MAAM;MACL,IAAIjB,QAAQ,IAAI,OAAQA,QAAS,KAAK,UAAU,EAAE;QAChD;QACAA,QAAQ,CAAC,CAAC;MACZ;IACF;EACF,CAAC;EACDiB,aAAa,CAAC,CAAC;AACjB", "ignoreList": []}]}