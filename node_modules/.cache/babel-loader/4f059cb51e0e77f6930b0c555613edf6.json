{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/api/menu.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/api/menu.js", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QgZnJvbSAnQC91dGlscy9yZXF1ZXN0JzsKCi8vIOiOt+WPlui3r+eUsQpleHBvcnQgdmFyIGdldFJvdXRlcnMgPSBmdW5jdGlvbiBnZXRSb3V0ZXJzKCkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9nZXRSb3V0ZXJzJywKICAgIG1ldGhvZDogJ2dldCcKICB9KTsKfTs="}, {"version": 3, "names": ["request", "getRouters", "url", "method"], "sources": ["/Users/<USER>/develop/code/project/haijia/adminweb/src/api/menu.js"], "sourcesContent": ["import request from '@/utils/request'\n\n// 获取路由\nexport const getRouters = () => {\n  return request({\n    url: '/getRouters',\n    method: 'get'\n  })\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;;AAErC;AACA,OAAO,IAAMC,UAAU,GAAG,SAAbA,UAAUA,CAAA,EAAS;EAC9B,OAAOD,OAAO,CAAC;IACbE,GAAG,EAAE,aAAa;IAClBC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}]}