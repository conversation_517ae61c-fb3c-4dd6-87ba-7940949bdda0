{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/flowable/definition/model.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/flowable/definition/model.vue", "mtime": 1662389806000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["readXml", "roleList", "saveXml", "userList", "bpmnModeler", "vkbeautify", "Hljs", "name", "components", "directives", "highlight", "el", "blocks", "querySelectorAll", "for<PERSON>ach", "block", "highlightBlock", "data", "xml", "modeler", "xmlOpen", "xmlTitle", "xmlContent", "users", "groups", "categorys", "activated", "_this", "deployId", "$route", "query", "getModelDetail", "getDicts", "then", "res", "getDataList", "methods", "_this2", "save", "_this3", "params", "process", "category", "$message", "msg", "$store", "dispatch", "$router", "go", "_this4", "val", "userId", "toString", "arr", "nick<PERSON><PERSON>", "push", "roleId", "showXML", "dataType", "userType", "<PERSON><PERSON><PERSON>"], "sources": ["src/views/flowable/definition/model.vue"], "sourcesContent": ["<template>\n  <div>\n    <bpmn-modeler\n      ref=\"refNode\"\n      :xml=\"xml\"\n      :users=\"users\"\n      :groups=\"groups\"\n      :categorys=\"categorys\"\n      :is-view=\"false\"\n      @save=\"save\"\n      @showXML=\"showXML\"\n      @dataType=\"dataType\"\n    />\n    <!--在线查看xml-->\n    <el-dialog :title=\"xmlTitle\" :visible.sync=\"xmlOpen\" width=\"60%\" append-to-body>\n      <div>\n        <pre v-highlight>\n           <code class=\"xml\">\n                {{xmlContent}}\n           </code>\n        </pre>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n<script>\nimport {readXml, roleList, saveXml, userList} from \"@/api/flowable/definition\";\nimport bpmnModeler from '@/components/Process/index'\nimport vkbeautify from 'vkbeautify'\nimport Hljs from 'highlight.js'\nimport 'highlight.js/styles/atom-one-dark.css'\n\nexport default {\n  name: \"Model\",\n  components: {\n    bpmnModeler,\n    vkbeautify\n  },\n  // 自定义指令\n  directives: {\n    highlight:(el) => {\n      let blocks = el.querySelectorAll('pre code');\n      blocks.forEach((block) => {\n        Hljs.highlightBlock(block)\n      })\n    }\n  },\n  data() {\n    return {\n      xml: \"\", // 后端查询到的xml\n      modeler:\"\",\n      xmlOpen: false,\n      xmlTitle: '',\n      xmlContent: '',\n      users: [],\n      groups: [],\n      categorys: [],\n\n    };\n  },\n  activated () {\n    const deployId = this.$route.query && this.$route.query.deployId;\n    //console.log(\"deployId :\"+deployId)\n    //  查询流程xml\n    if (deployId) {\n      //console.log(\"deployId :\"+deployId)\n      this.getModelDetail(deployId);\n    }\n    this.getDicts(\"sys_process_category\").then(res => {\n      this.categorys = res.data;\n    });\n    this.getDataList()\n  },\n  methods: {\n    /** xml 文件 */\n    getModelDetail(deployId) {\n      // 发送请求，获取xml\n      readXml(deployId).then(res =>{\n        this.xml = res.data;\n        this.modeler = res.data\n      })\n    },\n    /** 保存xml */\n    save(data) {\n      const params = {\n        name: data.process.name,\n        category: data.process.category,\n        xml: data.xml\n      }\n      saveXml(params).then(res => {\n        this.$message(res.msg)\n        // 关闭当前标签页并返回上个页面\n        this.$store.dispatch(\"tagsView/delView\", this.$route);\n        this.$router.go(-1)\n      })\n    },\n    /** 指定流程办理人员列表 */\n    getDataList() {\n      // todo 待根据部门选择人员\n      // const params = {\n      //\n      // }\n      userList().then(res =>{\n        res.data.forEach(val =>{\n          val.userId = val.userId.toString();\n        })\n        this.users = res.data;\n        let arr = {nickName: \"流程发起人\", userId: \"${INITIATOR}\"}\n        this.users.push(arr)\n      });\n      roleList().then(res =>{\n        res.data.forEach(val =>{\n          val.roleId = val.roleId.toString();\n        })\n        this.groups = res.data;\n      });\n    },\n    /** 展示xml */\n    showXML(data){\n      this.xmlTitle = 'xml查看';\n      this.xmlOpen = true;\n      this.xmlContent = vkbeautify.xml(data);\n    },\n    /** 获取数据类型 */\n    dataType(data){\n      this.users = [];\n      this.groups = [];\n      if (data) {\n        if (data.dataType === 'dynamic') {\n          if (data.userType === 'assignee') {\n            this.users = [{nickName: \"${INITIATOR}\", userId: \"${INITIATOR}\"},\n                          {nickName: \"#{approval}\", userId: \"#{approval}\"}\n              ]\n          } else if (data.userType === 'candidateUsers') {\n            this.users = [ {nickName: \"#{approval}\", userId: \"#{approval}\"}]\n          } else {\n            this.groups = [{roleName: \"#{approval}\", roleId: \"#{approval}\"}]\n          }\n        } else {\n          this.getDataList()\n        }\n      }\n    }\n  },\n};\n</script>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AA0BA,SAAAA,OAAA,EAAAC,QAAA,EAAAC,OAAA,EAAAC,QAAA;AACA,OAAAC,WAAA;AACA,OAAAC,UAAA;AACA,OAAAC,IAAA;AACA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAJ,WAAA,EAAAA,WAAA;IACAC,UAAA,EAAAA;EACA;EACA;EACAI,UAAA;IACAC,SAAA,WAAAA,UAAAC,EAAA;MACA,IAAAC,MAAA,GAAAD,EAAA,CAAAE,gBAAA;MACAD,MAAA,CAAAE,OAAA,WAAAC,KAAA;QACAT,IAAA,CAAAU,cAAA,CAAAD,KAAA;MACA;IACA;EACA;EACAE,IAAA,WAAAA,KAAA;IACA;MACAC,GAAA;MAAA;MACAC,OAAA;MACAC,OAAA;MACAC,QAAA;MACAC,UAAA;MACAC,KAAA;MACAC,MAAA;MACAC,SAAA;IAEA;EACA;EACAC,SAAA,WAAAA,UAAA;IAAA,IAAAC,KAAA;IACA,IAAAC,QAAA,QAAAC,MAAA,CAAAC,KAAA,SAAAD,MAAA,CAAAC,KAAA,CAAAF,QAAA;IACA;IACA;IACA,IAAAA,QAAA;MACA;MACA,KAAAG,cAAA,CAAAH,QAAA;IACA;IACA,KAAAI,QAAA,yBAAAC,IAAA,WAAAC,GAAA;MACAP,KAAA,CAAAF,SAAA,GAAAS,GAAA,CAAAjB,IAAA;IACA;IACA,KAAAkB,WAAA;EACA;EACAC,OAAA;IACA,aACAL,cAAA,WAAAA,eAAAH,QAAA;MAAA,IAAAS,MAAA;MACA;MACArC,OAAA,CAAA4B,QAAA,EAAAK,IAAA,WAAAC,GAAA;QACAG,MAAA,CAAAnB,GAAA,GAAAgB,GAAA,CAAAjB,IAAA;QACAoB,MAAA,CAAAlB,OAAA,GAAAe,GAAA,CAAAjB,IAAA;MACA;IACA;IACA,YACAqB,IAAA,WAAAA,KAAArB,IAAA;MAAA,IAAAsB,MAAA;MACA,IAAAC,MAAA;QACAjC,IAAA,EAAAU,IAAA,CAAAwB,OAAA,CAAAlC,IAAA;QACAmC,QAAA,EAAAzB,IAAA,CAAAwB,OAAA,CAAAC,QAAA;QACAxB,GAAA,EAAAD,IAAA,CAAAC;MACA;MACAhB,OAAA,CAAAsC,MAAA,EAAAP,IAAA,WAAAC,GAAA;QACAK,MAAA,CAAAI,QAAA,CAAAT,GAAA,CAAAU,GAAA;QACA;QACAL,MAAA,CAAAM,MAAA,CAAAC,QAAA,qBAAAP,MAAA,CAAAV,MAAA;QACAU,MAAA,CAAAQ,OAAA,CAAAC,EAAA;MACA;IACA;IACA,iBACAb,WAAA,WAAAA,YAAA;MAAA,IAAAc,MAAA;MACA;MACA;MACA;MACA;MACA9C,QAAA,GAAA8B,IAAA,WAAAC,GAAA;QACAA,GAAA,CAAAjB,IAAA,CAAAH,OAAA,WAAAoC,GAAA;UACAA,GAAA,CAAAC,MAAA,GAAAD,GAAA,CAAAC,MAAA,CAAAC,QAAA;QACA;QACAH,MAAA,CAAA1B,KAAA,GAAAW,GAAA,CAAAjB,IAAA;QACA,IAAAoC,GAAA;UAAAC,QAAA;UAAAH,MAAA;QAAA;QACAF,MAAA,CAAA1B,KAAA,CAAAgC,IAAA,CAAAF,GAAA;MACA;MACApD,QAAA,GAAAgC,IAAA,WAAAC,GAAA;QACAA,GAAA,CAAAjB,IAAA,CAAAH,OAAA,WAAAoC,GAAA;UACAA,GAAA,CAAAM,MAAA,GAAAN,GAAA,CAAAM,MAAA,CAAAJ,QAAA;QACA;QACAH,MAAA,CAAAzB,MAAA,GAAAU,GAAA,CAAAjB,IAAA;MACA;IACA;IACA,YACAwC,OAAA,WAAAA,QAAAxC,IAAA;MACA,KAAAI,QAAA;MACA,KAAAD,OAAA;MACA,KAAAE,UAAA,GAAAjB,UAAA,CAAAa,GAAA,CAAAD,IAAA;IACA;IACA,aACAyC,QAAA,WAAAA,SAAAzC,IAAA;MACA,KAAAM,KAAA;MACA,KAAAC,MAAA;MACA,IAAAP,IAAA;QACA,IAAAA,IAAA,CAAAyC,QAAA;UACA,IAAAzC,IAAA,CAAA0C,QAAA;YACA,KAAApC,KAAA;cAAA+B,QAAA;cAAAH,MAAA;YAAA,GACA;cAAAG,QAAA;cAAAH,MAAA;YAAA,EACA;UACA,WAAAlC,IAAA,CAAA0C,QAAA;YACA,KAAApC,KAAA;cAAA+B,QAAA;cAAAH,MAAA;YAAA;UACA;YACA,KAAA3B,MAAA;cAAAoC,QAAA;cAAAJ,MAAA;YAAA;UACA;QACA;UACA,KAAArB,WAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}