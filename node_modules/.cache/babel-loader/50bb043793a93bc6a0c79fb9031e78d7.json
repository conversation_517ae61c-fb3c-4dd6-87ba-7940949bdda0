{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/store/modules/permission.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/store/modules/permission.js", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["constantRoutes", "getRouters", "Layout", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "permission", "state", "routes", "addRoutes", "sidebarRouters", "mutations", "SET_ROUTES", "concat", "SET_SIDEBAR_ROUTERS", "routers", "actions", "GenerateRoutes", "_ref", "commit", "Promise", "resolve", "then", "res", "sdata", "JSON", "parse", "stringify", "data", "rdata", "sidebarRoutes", "filterAsyncRouter", "rewriteRoutes", "push", "path", "redirect", "hidden", "asyncRouterMap", "isRewrite", "arguments", "length", "undefined", "filter", "route", "children", "filterChildren", "component", "loadView", "childrenMap", "for<PERSON>ach", "el", "index", "c", "view", "require"], "sources": ["/Users/<USER>/develop/code/project/haijia/adminweb/src/store/modules/permission.js"], "sourcesContent": ["import { constantRoutes } from '@/router'\nimport { getRouters } from '@/api/menu'\nimport Layout from '@/layout/index'\nimport ParentView from '@/components/ParentView';\n\nconst permission = {\n  state: {\n    routes: [],\n    addRoutes: [],\n    sidebarRouters: []\n  },\n  mutations: {\n    SET_ROUTES: (state, routes) => {\n      state.addRoutes = routes\n      state.routes = constantRoutes.concat(routes)\n    },\n    SET_SIDEBAR_ROUTERS: (state, routers) => {\n      state.sidebarRouters = constantRoutes.concat(routers)\n    },\n  },\n  actions: {\n    // 生成路由\n    GenerateRoutes({ commit }) {\n      return new Promise(resolve => {\n        // 向后端请求路由数据\n        getRouters().then(res => {\n          const sdata = JSON.parse(JSON.stringify(res.data))\n          const rdata = JSON.parse(JSON.stringify(res.data))\n          const sidebarRoutes = filterAsyncRouter(sdata)\n          const rewriteRoutes = filterAsyncRouter(rdata, true)\n          rewriteRoutes.push({ path: '*', redirect: '/404', hidden: true })\n          commit('SET_ROUTES', rewriteRoutes)\n          commit('SET_SIDEBAR_ROUTERS', sidebarRoutes)\n          resolve(rewriteRoutes)\n        })\n      })\n    }\n  }\n}\n\n// 遍历后台传来的路由字符串，转换为组件对象\nfunction filterAsyncRouter(asyncRouterMap, isRewrite = false) {\n  return asyncRouterMap.filter(route => {\n    if (isRewrite && route.children) {\n      route.children = filterChildren(route.children)\n    }\n    if (route.component) {\n      // Layout ParentView 组件特殊处理\n      if (route.component === 'Layout') {\n        route.component = Layout\n      } else if (route.component === 'ParentView') {\n        route.component = ParentView\n      } else {\n        route.component = loadView(route.component)\n      }\n    }\n    if (route.children != null && route.children && route.children.length) {\n      route.children = filterAsyncRouter(route.children, route, isRewrite)\n    }\n    return true\n  })\n}\n\nfunction filterChildren(childrenMap) {\n  var children = []\n  childrenMap.forEach((el, index) => {\n    if (el.children && el.children.length) {\n      if (el.component === 'ParentView') {\n        el.children.forEach(c => {\n          c.path = el.path + '/' + c.path\n          if (c.children && c.children.length) {\n            children = children.concat(filterChildren(c.children, c))\n            return\n          }\n          children.push(c)\n        })\n        return\n      }\n    }\n    children = children.concat(el)\n  })\n  return children\n}\n\nexport const loadView = (view) => { // 路由懒加载\n  return (resolve) => require([`@/views/${view}`], resolve)\n}\n\nexport default permission\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,UAAU;AACzC,SAASC,UAAU,QAAQ,YAAY;AACvC,OAAOC,MAAM,MAAM,gBAAgB;AACnC,OAAOC,UAAU,MAAM,yBAAyB;AAEhD,IAAMC,UAAU,GAAG;EACjBC,KAAK,EAAE;IACLC,MAAM,EAAE,EAAE;IACVC,SAAS,EAAE,EAAE;IACbC,cAAc,EAAE;EAClB,CAAC;EACDC,SAAS,EAAE;IACTC,UAAU,EAAE,SAAAA,WAACL,KAAK,EAAEC,MAAM,EAAK;MAC7BD,KAAK,CAACE,SAAS,GAAGD,MAAM;MACxBD,KAAK,CAACC,MAAM,GAAGN,cAAc,CAACW,MAAM,CAACL,MAAM,CAAC;IAC9C,CAAC;IACDM,mBAAmB,EAAE,SAAAA,oBAACP,KAAK,EAAEQ,OAAO,EAAK;MACvCR,KAAK,CAACG,cAAc,GAAGR,cAAc,CAACW,MAAM,CAACE,OAAO,CAAC;IACvD;EACF,CAAC;EACDC,OAAO,EAAE;IACP;IACAC,cAAc,WAAAA,eAAAC,IAAA,EAAa;MAAA,IAAVC,MAAM,GAAAD,IAAA,CAANC,MAAM;MACrB,OAAO,IAAIC,OAAO,CAAC,UAAAC,OAAO,EAAI;QAC5B;QACAlB,UAAU,CAAC,CAAC,CAACmB,IAAI,CAAC,UAAAC,GAAG,EAAI;UACvB,IAAMC,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACJ,GAAG,CAACK,IAAI,CAAC,CAAC;UAClD,IAAMC,KAAK,GAAGJ,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACJ,GAAG,CAACK,IAAI,CAAC,CAAC;UAClD,IAAME,aAAa,GAAGC,iBAAiB,CAACP,KAAK,CAAC;UAC9C,IAAMQ,aAAa,GAAGD,iBAAiB,CAACF,KAAK,EAAE,IAAI,CAAC;UACpDG,aAAa,CAACC,IAAI,CAAC;YAAEC,IAAI,EAAE,GAAG;YAAEC,QAAQ,EAAE,MAAM;YAAEC,MAAM,EAAE;UAAK,CAAC,CAAC;UACjEjB,MAAM,CAAC,YAAY,EAAEa,aAAa,CAAC;UACnCb,MAAM,CAAC,qBAAqB,EAAEW,aAAa,CAAC;UAC5CT,OAAO,CAACW,aAAa,CAAC;QACxB,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EACF;AACF,CAAC;;AAED;AACA,SAASD,iBAAiBA,CAACM,cAAc,EAAqB;EAAA,IAAnBC,SAAS,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;EAC1D,OAAOF,cAAc,CAACK,MAAM,CAAC,UAAAC,KAAK,EAAI;IACpC,IAAIL,SAAS,IAAIK,KAAK,CAACC,QAAQ,EAAE;MAC/BD,KAAK,CAACC,QAAQ,GAAGC,cAAc,CAACF,KAAK,CAACC,QAAQ,CAAC;IACjD;IACA,IAAID,KAAK,CAACG,SAAS,EAAE;MACnB;MACA,IAAIH,KAAK,CAACG,SAAS,KAAK,QAAQ,EAAE;QAChCH,KAAK,CAACG,SAAS,GAAG1C,MAAM;MAC1B,CAAC,MAAM,IAAIuC,KAAK,CAACG,SAAS,KAAK,YAAY,EAAE;QAC3CH,KAAK,CAACG,SAAS,GAAGzC,UAAU;MAC9B,CAAC,MAAM;QACLsC,KAAK,CAACG,SAAS,GAAGC,QAAQ,CAACJ,KAAK,CAACG,SAAS,CAAC;MAC7C;IACF;IACA,IAAIH,KAAK,CAACC,QAAQ,IAAI,IAAI,IAAID,KAAK,CAACC,QAAQ,IAAID,KAAK,CAACC,QAAQ,CAACJ,MAAM,EAAE;MACrEG,KAAK,CAACC,QAAQ,GAAGb,iBAAiB,CAACY,KAAK,CAACC,QAAQ,EAAED,KAAK,EAAEL,SAAS,CAAC;IACtE;IACA,OAAO,IAAI;EACb,CAAC,CAAC;AACJ;AAEA,SAASO,cAAcA,CAACG,WAAW,EAAE;EACnC,IAAIJ,QAAQ,GAAG,EAAE;EACjBI,WAAW,CAACC,OAAO,CAAC,UAACC,EAAE,EAAEC,KAAK,EAAK;IACjC,IAAID,EAAE,CAACN,QAAQ,IAAIM,EAAE,CAACN,QAAQ,CAACJ,MAAM,EAAE;MACrC,IAAIU,EAAE,CAACJ,SAAS,KAAK,YAAY,EAAE;QACjCI,EAAE,CAACN,QAAQ,CAACK,OAAO,CAAC,UAAAG,CAAC,EAAI;UACvBA,CAAC,CAAClB,IAAI,GAAGgB,EAAE,CAAChB,IAAI,GAAG,GAAG,GAAGkB,CAAC,CAAClB,IAAI;UAC/B,IAAIkB,CAAC,CAACR,QAAQ,IAAIQ,CAAC,CAACR,QAAQ,CAACJ,MAAM,EAAE;YACnCI,QAAQ,GAAGA,QAAQ,CAAC/B,MAAM,CAACgC,cAAc,CAACO,CAAC,CAACR,QAAQ,EAAEQ,CAAC,CAAC,CAAC;YACzD;UACF;UACAR,QAAQ,CAACX,IAAI,CAACmB,CAAC,CAAC;QAClB,CAAC,CAAC;QACF;MACF;IACF;IACAR,QAAQ,GAAGA,QAAQ,CAAC/B,MAAM,CAACqC,EAAE,CAAC;EAChC,CAAC,CAAC;EACF,OAAON,QAAQ;AACjB;AAEA,OAAO,IAAMG,QAAQ,GAAG,SAAXA,QAAQA,CAAIM,IAAI,EAAK;EAAE;EAClC,OAAO,UAAChC,OAAO;IAAA,OAAKiC,OAAO,CAAC,YAAAzC,MAAA,CAAYwC,IAAI,EAAG,EAAEhC,OAAO,CAAC;EAAA;AAC3D,CAAC;AAED,eAAef,UAAU", "ignoreList": []}]}