{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/project/report/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/project/report/index.vue", "mtime": 1752653921015}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js", "mtime": 1751171659043}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGxpc3RSZXBvcnQsIGdldFJlcG9ydCwgZGVsUmVwb3J0LCBhZGRSZXBvcnQsIHVwZGF0ZVJlcG9ydCwgZXhwb3J0UmVwb3J0LCBpbXBvcnRUZW1wbGF0ZSBhcyBfaW1wb3J0VGVtcGxhdGUsIHByaW50UmVwb3J0LCBjaGVja05hbWVVbmlxdWUsIGdldExpa2VMaXN0LCBhdXRoUmVwb3J0IH0gZnJvbSAiQC9hcGkvcHJvamVjdC9yZXBvcnQiOwppbXBvcnQgeyBnZXRUb2tlbiB9IGZyb20gIkAvdXRpbHMvYXV0aCI7CmltcG9ydCBGaWxlVXBsb2FkIGZyb20gIkAvY29tcG9uZW50cy9GaWxlVXBsb2FkIjsKaW1wb3J0IGZsb3dhYmxlIGZyb20gIkAvdmlld3MvZmxvd2FibGUvdGFzay9yZWNvcmQvdmlldyI7CmltcG9ydCB7IGdldEluc0lkQnlCaXpLZXkgfSBmcm9tICJAL2FwaS9mbG93YWJsZS90b2RvIjsKaW1wb3J0IHsgcmVnaW9uRGF0YSwgQ29kZVRvVGV4dCwgVGV4dFRvQ29kZSB9IGZyb20gImVsZW1lbnQtY2hpbmEtYXJlYS1kYXRhIjsKaW1wb3J0IHByaW50IGZyb20gInByaW50LWpzIjsKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICJSZXBvcnQiLAogIGNvbXBvbmVudHM6IHsKICAgIEZpbGVVcGxvYWQ6IEZpbGVVcGxvYWQsCiAgICBwcmludDogcHJpbnQsCiAgICBmbG93YWJsZTogZmxvd2FibGUKICB9LAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICB2YXIgX3RoaXMgPSB0aGlzOwogICAgdmFyIGluZm9UeXBlVmFsdWVWYWxpID0gZnVuY3Rpb24gaW5mb1R5cGVWYWx1ZVZhbGkocnVsZSwgdmFsdWUsIGNhbGxiYWNrKSB7CiAgICAgIGlmIChfdGhpcy5mb3JtLmluZm9UeXBlLmluZGV4T2YoIjEiKSA+PSAwICYmICFfdGhpcy5mb3JtLnNjYW5GaWxlKSB7CiAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCLpgq7nrrHlnLDlnYDlv4XloasiKSk7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIGNhbGxiYWNrKCk7CiAgICB9OwogICAgdmFyIGluZm9UeXBlVmFsdWVWYWxpMiA9IGZ1bmN0aW9uIGluZm9UeXBlVmFsdWVWYWxpMihydWxlLCB2YWx1ZSwgY2FsbGJhY2spIHsKICAgICAgaWYgKF90aGlzLmZvcm0uaW5mb1R5cGUuaW5kZXhPZigiMiIpID49IDAgJiYgIV90aGlzLmZvcm0uc2VuZEFkZHJlc3MpIHsKICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoIuaUtuS7tuWcsOWdgOW/heWhqyIpKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgY2FsbGJhY2soKTsKICAgIH07CiAgICB2YXIgbmFtZVZhbGkgPSBmdW5jdGlvbiBuYW1lVmFsaShydWxlLCB2YWx1ZSwgY2FsbGJhY2spIHsKICAgICAgaWYgKCFfdGhpcy5mb3JtLnByb2plY3ROYW1lKSB7CiAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCLpobnnm67lkI3np7Dlv4XloasiKSk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgaWYgKC9ccysvZy50ZXN0KF90aGlzLmZvcm0ucHJvamVjdE5hbWUpKSB7CiAgICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoIumhueebruWQjeensOS4jeinhOiMgyIpKTsKICAgICAgICAgIHJldHVybjsKICAgICAgICB9CiAgICAgICAgY2hlY2tOYW1lVW5pcXVlKHsKICAgICAgICAgIHByb2plY3ROYW1lOiBfdGhpcy5mb3JtLnByb2plY3ROYW1lLAogICAgICAgICAgcHJvamVjdElkOiBfdGhpcy5mb3JtLnByb2plY3RJZAogICAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgICBpZiAocmVzcG9uc2UuZGF0YSA9PSAwKSB7CiAgICAgICAgICAgIGNhbGxiYWNrKCk7CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoIumhueebruWQjeensOW3suWtmOWcqCIpKTsKICAgICAgICAgIH0KICAgICAgICB9KTsKICAgICAgfQogICAgfTsKICAgIHZhciBjb2RlVmFsaSA9IGZ1bmN0aW9uIGNvZGVWYWxpKHJ1bGUsIHZhbHVlLCBjYWxsYmFjaykgewogICAgICBpZiAoIXRoYXQuZm9ybS5wcm9qZWN0Tm8pIHsKICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoIumhueebrue8luWPt+W/heWhqyIpKTsKICAgICAgfSBlbHNlIGlmICgvXHMrL2cudGVzdCh0aGF0LmZvcm0ucHJvamVjdE5vKSkgewogICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcigi6aG555uu57yW5Y+35LiN6KeE6IyDIikpOwogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICBjYWxsYmFjaygpOwogICAgfTsKICAgIHZhciBhdXRoRmlsZVZhbHVlVmFsaSA9IGZ1bmN0aW9uIGF1dGhGaWxlVmFsdWVWYWxpKHJ1bGUsIHZhbHVlLCBjYWxsYmFjaykgewogICAgICBpZiAoX3RoaXMuZm9ybS5vcGVyYXRpb25UeXBlID09IDIgJiYgIV90aGlzLmZvcm0uYXV0aEZpbGUpIHsKICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoIuaOiOadg+exu+Wei+W/heS8oOaOiOadg+S5piIpKTsKICAgICAgfQogICAgICBjYWxsYmFjaygpOwogICAgfTsKICAgIHJldHVybiB7CiAgICAgIGlzTW9iaWxlOiBmYWxzZSwKICAgICAgcGFnZUxheW91dDogInRvdGFsLCBzaXplcywgcHJldiwgcGFnZXIsIG5leHQsIGp1bXBlciIsCiAgICAgIC8vIOmBrue9qeWxggogICAgICBsb2FkaW5nOiB0cnVlLAogICAgICAvLyDpgInkuK3mlbDnu4QKICAgICAgaWRzOiBbXSwKICAgICAgLy8g6Z2e5Y2V5Liq56aB55SoCiAgICAgIHNpbmdsZTogdHJ1ZSwKICAgICAgLy8g6Z2e5aSa5Liq56aB55SoCiAgICAgIG11bHRpcGxlOiB0cnVlLAogICAgICAvLyDmmL7npLrmkJzntKLmnaHku7YKICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwKICAgICAgc2hvd0V4cG9ydDogZmFsc2UsCiAgICAgIHNob3dQcmludDogZmFsc2UsCiAgICAgIC8vIOaAu+adoeaVsAogICAgICB0b3RhbDogMCwKICAgICAgLy8g6aG555uu5oql5aSH6KGo5qC85pWw5o2uCiAgICAgIHJlcG9ydExpc3Q6IFtdLAogICAgICAvLyDlvLnlh7rlsYLmoIfpopgKICAgICAgdGl0bGU6ICIiLAogICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYIKICAgICAgb3BlbjogZmFsc2UsCiAgICAgIC8vIOaTjeS9nOexu+Wei+Wtl+WFuAogICAgICBvcGVyYXRpb25UeXBlT3B0aW9uczogW10sCiAgICAgIC8vIOWuoeaguOeKtuaAgeWtl+WFuAogICAgICBhdWRpdFN0YXR1c09wdGlvbnM6IFtdLAogICAgICAvLyDnvJbovpHnirbmgIHlrZflhbgKICAgICAgZWRpdFN0YXR1c09wdGlvbnM6IFtdLAogICAgICAvLyDmi5vmoIfmlrnlvI/lrZflhbgKICAgICAgYmlkZGluZ1R5cGVPcHRpb25zOiBbXSwKICAgICAgLy8g5oqV5qCH5Lqn5ZOB5Z6L5Y+35a2X5YW4CiAgICAgIG1vZGVsT3B0aW9uczogW10sCiAgICAgIG1vZGVsT3B0aW9uMTogW10sCiAgICAgIC8vIOaJgOmcgOi1hOaWmeWtl+WFuAogICAgICByZXF1aXJlSW5mb09wdGlvbnM6IFtdLAogICAgICByZXF1aXJlSW5mb09wdGlvbjE6IFtdLAogICAgICAvLyDotYTmlpnnsbvlnovlrZflhbgKICAgICAgaW5mb1R5cGVPcHRpb25zOiBbXSwKICAgICAgc3BlY09wdGlvbnM6IFtdLAogICAgICBzcGVjT3B0aW9uMTogW10sCiAgICAgIC8vIOaJgOWxnuecgeS7veWtl+WFuAogICAgICBiZWxvbmdQcm92aW5jZU9wdGlvbnM6IFtdLAogICAgICBiZWxvbmdQcm92aW5jZU9wdGlvbnMxOiBbXSwKICAgICAgLy8g5ZSu5ZCO5bm06ZmQCiAgICAgIGFmdGVyU2FsZVllYXJPcHRpb25zOiBbXSwKICAgICAgYWZ0ZXJTYWxlWWVhck9wdGlvbnMxOiBbXSwKICAgICAgLy8g6aG555uu5a+85YWl5Y+C5pWwCiAgICAgIHVwbG9hZDogewogICAgICAgIC8vIOaYr+WQpuaYvuekuuW8ueWHuuWxgu+8iOeUqOaIt+WvvOWFpe+8iQogICAgICAgIG9wZW46IGZhbHNlLAogICAgICAgIC8vIOW8ueWHuuWxguagh+mimO+8iOeUqOaIt+WvvOWFpe+8iQogICAgICAgIHRpdGxlOiAiIiwKICAgICAgICAvLyDmmK/lkKbnpoHnlKjkuIrkvKAKICAgICAgICBpc1VwbG9hZGluZzogZmFsc2UsCiAgICAgICAgLy8g5piv5ZCm5pu05paw5bey57uP5a2Y5Zyo55qE55So5oi35pWw5o2uCiAgICAgICAgdXBkYXRlU3VwcG9ydDogMCwKICAgICAgICAvLyDorr7nva7kuIrkvKDnmoTor7fmsYLlpLTpg6gKICAgICAgICBoZWFkZXJzOiB7CiAgICAgICAgICBBdXRob3JpemF0aW9uOiAiQmVhcmVyICIgKyBnZXRUb2tlbigpCiAgICAgICAgfSwKICAgICAgICAvLyDkuIrkvKDnmoTlnLDlnYAKICAgICAgICB1cmw6IHByb2Nlc3MuZW52LlZVRV9BUFBfQkFTRV9BUEkgKyAiL3Byb2plY3QvcmVwb3J0L2ltcG9ydERhdGEiCiAgICAgIH0sCiAgICAgIC8vIOafpeivouWPguaVsAogICAgICBxdWVyeVBhcmFtczogewogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgIHByb2plY3RObzogbnVsbCwKICAgICAgICBwcm9qZWN0TmFtZTogbnVsbCwKICAgICAgICBvcGVyYXRpb25UeXBlOiBudWxsLAogICAgICAgIGF1ZGl0U3RhdHVzOiBudWxsLAogICAgICAgIHByb3ZpbmNlOiBudWxsLAogICAgICAgIHVzZXJUeXBlOiBudWxsLAogICAgICAgIGJlbG9uZ1VzZXI6IG51bGwsCiAgICAgICAgdXBkYXRlVGltZUFycjogW10sCiAgICAgICAgc3BhcmUxOiBudWxsLAogICAgICAgIGFkZHJlc3M6IG51bGwsCiAgICAgICAgYmlkZGluZ0NvbXBhbnk6IG51bGwsCiAgICAgICAgYXV0aENvbXBhbnk6IG51bGwsCiAgICAgICAgZnVsbEZpZWxkOiBudWxsCiAgICAgIH0sCiAgICAgIC8vIOihqOWNleWPguaVsAogICAgICBmb3JtOiB7fSwKICAgICAgLy8g6KGo5Y2V5qCh6aqMCiAgICAgIC8vIOihqOWNleagoemqjAogICAgICBydWxlczogewogICAgICAgIG9wZXJhdGlvblR5cGU6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICLmk43kvZznsbvlnovlv4XpgIkiCiAgICAgICAgfV0sCiAgICAgICAgcHJvamVjdE5vOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICB2YWxpZGF0ZTogY29kZVZhbGksCiAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIKICAgICAgICB9XSwKICAgICAgICBwcm9qZWN0TmFtZTogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgdmFsaWRhdGU6IG5hbWVWYWxpLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgfV0sCiAgICAgICAgYWRkcmVzczogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogIuivpue7huWcsOWdgOS4jeiDveS4uuepuiIsCiAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIKICAgICAgICB9XSwKICAgICAgICBiaWRkaW5nQ29tcGFueTogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogIuaLm+agh+WNleS9jeS4jeiDveS4uuepuiIsCiAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIKICAgICAgICB9XSwKICAgICAgICBvcGVuRGF0ZTogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogIuW8gOagh+aXpeacn+S4jeiDveS4uuepuiIsCiAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIKICAgICAgICB9XSwKICAgICAgICBhZnRlclNhbGVZZWFyOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAi5ZSu5ZCO5bm06ZmQ5LiN6IO95Li656m6IiwKICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgIH1dLAogICAgICAgIGhhbmdEYXRlOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAi5oyC572R5pel5pyf5LiN6IO95Li656m6IiwKICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgIH1dLAogICAgICAgIGJlbG9uZ1Byb3ZpbmNlOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAi5ZSu5ZCO5bm06ZmQ5LiN6IO95Li656m6IiwKICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgIH1dLAogICAgICAgIGRpc3RyaWJ1dG9yOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAi5omA5bGe57uP6ZSA5ZWG5LiN6IO95Li656m6IiwKICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgIH1dLAogICAgICAgIHNjYW5GaWxlOiBbewogICAgICAgICAgdmFsaWRhdG9yOiBpbmZvVHlwZVZhbHVlVmFsaSwKICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgIH1dLAogICAgICAgIHNlbmRBZGRyZXNzOiBbewogICAgICAgICAgdmFsaWRhdG9yOiBpbmZvVHlwZVZhbHVlVmFsaTIsCiAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIKICAgICAgICB9XSwKICAgICAgICBtb2RlbDogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogIuaKleagh+S6p+WTgeWei+WPt+W/hemAiSIKICAgICAgICB9XSwKICAgICAgICBzcGVjOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAi5oqV5qCH5Lqn5ZOB6KeE5qC85b+F6YCJIgogICAgICAgIH1dLAogICAgICAgIHByb3ZpbmNlOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAi6aG555uu5omA5Zyo5Zyw5b+F6YCJIgogICAgICAgIH1dLAogICAgICAgIGluZm9UeXBlOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAi6LWE5paZ5o6l5pS25pa55byP5b+F6YCJIiwKICAgICAgICAgIHRyaWdnZXI6ICJjaGFuZ2UiCiAgICAgICAgfV0sCiAgICAgICAgYXV0aEZpbGU6IFt7CiAgICAgICAgICB2YWxpZGF0b3I6IGF1dGhGaWxlVmFsdWVWYWxpLAogICAgICAgICAgdHJpZ2dlcjogImNoYW5nZSIKICAgICAgICB9XSwKICAgICAgICBiaWRkaW5nQ29udGFjdDogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogIuaLm+agh+WNleS9jeiBlOezu+S6ui/ogZTns7vnlLXor53lv4XloasiCiAgICAgICAgfV0sCiAgICAgICAgYXV0aENvbnRhY3Q6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICLmjojmnYPlhazlj7jogZTns7vkurov6IGU57O755S16K+d5b+F5aGrIgogICAgICAgIH1dCiAgICAgIH0sCiAgICAgIC8vIOWIl+S/oeaBrwogICAgICBjb2x1bW5zOiBbewogICAgICAgIGtleTogImJlbG9uZ1VzZXIiLAogICAgICAgIGluZGV4OiAxLAogICAgICAgIGxhYmVsOiAiXHU2MjQwXHU1QzVFXHU3NTI4XHU2MjM3IiwKICAgICAgICB2aXNpYmxlOiB0cnVlCiAgICAgIH0sIHsKICAgICAgICBrZXk6ICJwcm9qZWN0Tm8iLAogICAgICAgIGluZGV4OiAyLAogICAgICAgIGxhYmVsOiAiXHU5ODc5XHU3NkVFXHU3RjE2XHU1M0Y3IiwKICAgICAgICB2aXNpYmxlOiB0cnVlCiAgICAgIH0sIHsKICAgICAgICBrZXk6ICJwcm9qZWN0TmFtZSIsCiAgICAgICAgaW5kZXg6IDMsCiAgICAgICAgbGFiZWw6ICJcdTk4NzlcdTc2RUVcdTU0MERcdTc5RjAiLAogICAgICAgIHZpc2libGU6IHRydWUKICAgICAgfSwgewogICAgICAgIGtleTogIm9wZXJhdGlvblR5cGUiLAogICAgICAgIGluZGV4OiA0LAogICAgICAgIGxhYmVsOiAiXHU2NENEXHU0RjVDXHU3QzdCXHU1NzhCIiwKICAgICAgICB2aXNpYmxlOiB0cnVlCiAgICAgIH0sIHsKICAgICAgICBrZXk6ICJwcm92aW5jZSIsCiAgICAgICAgaW5kZXg6IDUsCiAgICAgICAgbGFiZWw6ICJcdTk4NzlcdTc2RUVcdTYyNDBcdTU3MjhcdTU3MzAiLAogICAgICAgIHZpc2libGU6IHRydWUKICAgICAgfSwgewogICAgICAgIGtleTogImFkZHJlc3MiLAogICAgICAgIGluZGV4OiA2LAogICAgICAgIGxhYmVsOiAiXHU4QkU2XHU3RUM2XHU1NzMwXHU1NzQwIiwKICAgICAgICB2aXNpYmxlOiB0cnVlCiAgICAgIH0sIHsKICAgICAgICBrZXk6ICJhdXRoQ29tcGFueSIsCiAgICAgICAgaW5kZXg6IDcsCiAgICAgICAgbGFiZWw6ICJcdTg4QUJcdTYzODhcdTY3NDNcdTUxNkNcdTUzRjgiLAogICAgICAgIHZpc2libGU6IHRydWUKICAgICAgfSwgewogICAgICAgIGtleTogImRpc3RyaWJ1dG9yIiwKICAgICAgICBpbmRleDogOCwKICAgICAgICBsYWJlbDogIlx1NjI0MFx1NUM1RVx1N0VDRlx1OTUwMFx1NTU0NiIsCiAgICAgICAgdmlzaWJsZTogdHJ1ZQogICAgICB9LCB7CiAgICAgICAga2V5OiAiYmlkZGluZ0NvbXBhbnkiLAogICAgICAgIGluZGV4OiA5LAogICAgICAgIGxhYmVsOiAiXHU2MkRCXHU2ODA3XHU1MzU1XHU0RjREIiwKICAgICAgICB2aXNpYmxlOiB0cnVlCiAgICAgIH0sIHsKICAgICAgICBrZXk6ICJtb2RlbCIsCiAgICAgICAgaW5kZXg6IDEwLAogICAgICAgIGxhYmVsOiAiXHU2Mjk1XHU2ODA3XHU0RUE3XHU1NEMxXHU1NzhCXHU1M0Y3IiwKICAgICAgICB2aXNpYmxlOiB0cnVlCiAgICAgIH0sIHsKICAgICAgICBrZXk6ICJzcGVjIiwKICAgICAgICBpbmRleDogMTEsCiAgICAgICAgbGFiZWw6ICJcdTYyOTVcdTY4MDdcdTRFQTdcdTU0QzFcdTg5QzRcdTY4M0MiLAogICAgICAgIHZpc2libGU6IHRydWUKICAgICAgfSwgewogICAgICAgIGtleTogImFyZWEiLAogICAgICAgIGluZGV4OiAxMiwKICAgICAgICBsYWJlbDogIlx1NUI4OVx1ODhDNVx1OTc2Mlx1NzlFRiIsCiAgICAgICAgdmlzaWJsZTogdHJ1ZQogICAgICB9LCB7CiAgICAgICAga2V5OiAicmVxdWlyZUluZm8iLAogICAgICAgIGluZGV4OiAxMywKICAgICAgICBsYWJlbDogIlx1NjI0MFx1OTcwMFx1OEQ0NFx1NjU5OSIsCiAgICAgICAgdmlzaWJsZTogdHJ1ZQogICAgICB9LCB7CiAgICAgICAga2V5OiAiaW5mb1R5cGUiLAogICAgICAgIGluZGV4OiAxNCwKICAgICAgICBsYWJlbDogIlx1OEQ0NFx1NjU5OVx1N0M3Qlx1NTc4QiIsCiAgICAgICAgdmlzaWJsZTogdHJ1ZQogICAgICB9LCB7CiAgICAgICAga2V5OiAic2NhbkZpbGUiLAogICAgICAgIGluZGV4OiAxNSwKICAgICAgICBsYWJlbDogIlx1OEQ0NFx1NjU5OVx1NjNBNVx1NjUzNlx1OTBBRVx1NEVGNiIsCiAgICAgICAgdmlzaWJsZTogdHJ1ZQogICAgICB9LCB7CiAgICAgICAga2V5OiAic2NhbkZpbGUiLAogICAgICAgIGluZGV4OiAxNiwKICAgICAgICBsYWJlbDogIlx1OEQ0NFx1NjU5OVx1NjNBNVx1NjUzNlx1NTczMFx1NTc0MCIsCiAgICAgICAgdmlzaWJsZTogdHJ1ZQogICAgICB9LCB7CiAgICAgICAga2V5OiAiYmVsb25nUHJvdmluY2UiLAogICAgICAgIGluZGV4OiAxNywKICAgICAgICBsYWJlbDogIlx1OTg3OVx1NzZFRVx1NjI0MFx1NUM1RVx1NzcwMVx1NEVGRCIsCiAgICAgICAgdmlzaWJsZTogdHJ1ZQogICAgICB9LCB7CiAgICAgICAga2V5OiAiYWZ0ZXJTYWxlWWVhciIsCiAgICAgICAgaW5kZXg6IDE4LAogICAgICAgIGxhYmVsOiAiXHU1NTJFXHU1NDBFXHU1RTc0XHU5NjUwIiwKICAgICAgICB2aXNpYmxlOiB0cnVlCiAgICAgIH0sIHsKICAgICAgICBrZXk6ICJvcGVuRGF0ZSIsCiAgICAgICAgaW5kZXg6IDE5LAogICAgICAgIGxhYmVsOiAiXHU1RjAwXHU2ODA3XHU2NUU1XHU2NzFGIiwKICAgICAgICB2aXNpYmxlOiB0cnVlCiAgICAgIH0sIHsKICAgICAgICBrZXk6ICJoYW5nRGF0ZSIsCiAgICAgICAgaW5kZXg6IDIwLAogICAgICAgIGxhYmVsOiAiXHU2MzAyXHU3RjUxXHU2NUU1XHU2NzFGIiwKICAgICAgICB2aXNpYmxlOiB0cnVlCiAgICAgIH0sIHsKICAgICAgICBrZXk6ICJjcmVhdGVUaW1lIiwKICAgICAgICBpbmRleDogMjEsCiAgICAgICAgbGFiZWw6ICJcdTYzRDBcdTRFQTRcdTY1RjZcdTk1RjQiLAogICAgICAgIHZpc2libGU6IHRydWUKICAgICAgfSwgewogICAgICAgIGtleTogInVwZGF0ZVRpbWUiLAogICAgICAgIGluZGV4OiAyMiwKICAgICAgICBsYWJlbDogIlx1NEZFRVx1NjUzOVx1NjVGNlx1OTVGNCIsCiAgICAgICAgdmlzaWJsZTogdHJ1ZQogICAgICB9CiAgICAgIC8vIHsga2V5OiAiYXVkaXRTdGF0dXMiLCBpbmRleDogMTksIGxhYmVsOiBg5a6h5qC454q25oCBYCwgdmlzaWJsZTogZmFsc2UgfSwKICAgICAgLy8geyBrZXk6ICJlZGl0U3RhdHVzIiwgaW5kZXg6IDIwLCBsYWJlbDogYOe8lui+keeKtuaAgWAsIHZpc2libGU6IGZhbHNlIH0sCiAgICAgIC8vIHsga2V5OiAiMTEiLCBpbmRleDogMjEsIGxhYmVsOiBg5o6I5p2D5LmmYCwgdmlzaWJsZTogZmFsc2UgfSwKICAgICAgLy97IGtleTogIjEyIiwgaW5kZXg6IDIzLCBsYWJlbDogYOWUruWQjuacjeWKoeaJv+ivuuWHvWAsIHZpc2libGU6IGZhbHNlIH0sCiAgICAgIF0sCiAgICAgIG9wdGlvbnM6IHJlZ2lvbkRhdGEsCiAgICAgIHNlbGVjdGVkT3B0aW9uczogW10sCiAgICAgIHF1ZXJ5QXJlYTogW10sCiAgICAgIHZpZXdPcGVuOiBmYWxzZSwKICAgICAgdmlldzoge30sCiAgICAgIGluZm9MaXN0MTogW10sCiAgICAgIGluZm9MaXN0MjogW10sCiAgICAgIGRlZktleTogInByb2Nlc3NfcHJvamVjdF9yZXBvcnQiLAogICAgICBwcm9jSW5zSWQ6IHVuZGVmaW5lZCwKICAgICAgdGFza0lkOiB1bmRlZmluZWQsCiAgICAgIGZpbmlzaGVkOiB0cnVlLAogICAgICBiaXpLZXk6IHVuZGVmaW5lZCwKICAgICAgYXVkaXRTdGF0dXNUcmVlOiBbXSwKICAgICAgb3BlcmF0aW9uVHlwZVRyZWU6IFtdLAogICAgICB1c2VyVHlwZVRyZWU6IFtdLAogICAgICBkZWZhdWx0UHJvcHM6IHsKICAgICAgICBjaGlsZHJlbjogImNoaWxkcmVuIiwKICAgICAgICBsYWJlbDogImxhYmVsIgogICAgICB9LAogICAgICBvbGRPcGVyYXRpb25UeXBlOiB1bmRlZmluZWQsCiAgICAgIHNob3dVVHlwZTogdHJ1ZSwKICAgICAgY2hvb3NlT3B0VHlwZTogdW5kZWZpbmVkLAogICAgICBjaG9vc2VBdWRpdFN0YXR1czogdW5kZWZpbmVkLAogICAgICBjaG9vc2VVc2VySWQ6IHVuZGVmaW5lZCwKICAgICAgY2hvb3NlRWRpdFN0YXR1czogdW5kZWZpbmVkLAogICAgICBjaG9vc2VTcGFyZTI6IHVuZGVmaW5lZCwKICAgICAgbGlrZUxpc3Q6IHVuZGVmaW5lZCwKICAgICAgbGlrZUNvdW50OiB1bmRlZmluZWQsCiAgICAgIGF1dGhDb21wYW55czogW10sCiAgICAgIGlzQWRtaW46IHRydWUsCiAgICAgIGF1ZGl0U3RhdHVzRWRpdDogdHJ1ZSwKICAgICAgaXNBdXRoSW1hZ2VzOiBmYWxzZSwKICAgICAgc2VhcmNoUGlja2VyT3B0aW9uczogewogICAgICAgIHNob3J0Y3V0czogW3sKICAgICAgICAgIHRleHQ6ICfmnIDov5HkuIDlkagnLAogICAgICAgICAgb25DbGljazogZnVuY3Rpb24gb25DbGljayhwaWNrZXIpIHsKICAgICAgICAgICAgdmFyIGVuZCA9IG5ldyBEYXRlKCk7CiAgICAgICAgICAgIHZhciBzdGFydCA9IG5ldyBEYXRlKCk7CiAgICAgICAgICAgIHN0YXJ0LnNldFRpbWUoc3RhcnQuZ2V0VGltZSgpIC0gMzYwMCAqIDEwMDAgKiAyNCAqIDcpOwogICAgICAgICAgICBwaWNrZXIuJGVtaXQoJ3BpY2snLCBbc3RhcnQsIGVuZF0pOwogICAgICAgICAgfQogICAgICAgIH0sIHsKICAgICAgICAgIHRleHQ6ICfmnIDov5HkuIDkuKrmnIgnLAogICAgICAgICAgb25DbGljazogZnVuY3Rpb24gb25DbGljayhwaWNrZXIpIHsKICAgICAgICAgICAgdmFyIGVuZCA9IG5ldyBEYXRlKCk7CiAgICAgICAgICAgIHZhciBzdGFydCA9IG5ldyBEYXRlKCk7CiAgICAgICAgICAgIHN0YXJ0LnNldFRpbWUoc3RhcnQuZ2V0VGltZSgpIC0gMzYwMCAqIDEwMDAgKiAyNCAqIDMwKTsKICAgICAgICAgICAgcGlja2VyLiRlbWl0KCdwaWNrJywgW3N0YXJ0LCBlbmRdKTsKICAgICAgICAgIH0KICAgICAgICB9LCB7CiAgICAgICAgICB0ZXh0OiAn5pyA6L+R5LiJ5Liq5pyIJywKICAgICAgICAgIG9uQ2xpY2s6IGZ1bmN0aW9uIG9uQ2xpY2socGlja2VyKSB7CiAgICAgICAgICAgIHZhciBlbmQgPSBuZXcgRGF0ZSgpOwogICAgICAgICAgICB2YXIgc3RhcnQgPSBuZXcgRGF0ZSgpOwogICAgICAgICAgICBzdGFydC5zZXRUaW1lKHN0YXJ0LmdldFRpbWUoKSAtIDM2MDAgKiAxMDAwICogMjQgKiA5MCk7CiAgICAgICAgICAgIHBpY2tlci4kZW1pdCgncGljaycsIFtzdGFydCwgZW5kXSk7CiAgICAgICAgICB9CiAgICAgICAgfV0KICAgICAgfSwKICAgICAgcGlja2VyT3B0aW9uczogewogICAgICAgIGRpc2FibGVkRGF0ZTogZnVuY3Rpb24gZGlzYWJsZWREYXRlKHRpbWUpIHsKICAgICAgICAgIHJldHVybiB0aW1lLmdldFRpbWUoKSA+IERhdGUubm93KCk7CiAgICAgICAgfSwKICAgICAgICBzaG9ydGN1dHM6IFt7CiAgICAgICAgICB0ZXh0OiAi5LuK5aSpIiwKICAgICAgICAgIG9uQ2xpY2s6IGZ1bmN0aW9uIG9uQ2xpY2socGlja2VyKSB7CiAgICAgICAgICAgIHBpY2tlci4kZW1pdCgicGljayIsIG5ldyBEYXRlKCkpOwogICAgICAgICAgfQogICAgICAgIH0sIHsKICAgICAgICAgIHRleHQ6ICLmmKjlpKkiLAogICAgICAgICAgb25DbGljazogZnVuY3Rpb24gb25DbGljayhwaWNrZXIpIHsKICAgICAgICAgICAgdmFyIGRhdGUgPSBuZXcgRGF0ZSgpOwogICAgICAgICAgICBkYXRlLnNldFRpbWUoZGF0ZS5nZXRUaW1lKCkgLSAzNjAwICogMTAwMCAqIDI0KTsKICAgICAgICAgICAgcGlja2VyLiRlbWl0KCJwaWNrIiwgZGF0ZSk7CiAgICAgICAgICB9CiAgICAgICAgfSwgewogICAgICAgICAgdGV4dDogIuS4gOWRqOWJjSIsCiAgICAgICAgICBvbkNsaWNrOiBmdW5jdGlvbiBvbkNsaWNrKHBpY2tlcikgewogICAgICAgICAgICB2YXIgZGF0ZSA9IG5ldyBEYXRlKCk7CiAgICAgICAgICAgIGRhdGUuc2V0VGltZShkYXRlLmdldFRpbWUoKSAtIDM2MDAgKiAxMDAwICogMjQgKiA3KTsKICAgICAgICAgICAgcGlja2VyLiRlbWl0KCJwaWNrIiwgZGF0ZSk7CiAgICAgICAgICB9CiAgICAgICAgfV0KICAgICAgfSwKICAgICAgc2VhcmNoRmllbGQ6ICdhbGwnLAogICAgICAvLyDmlrDlop7vvJrlvZPliY3pgInkuK3nmoTmkJzntKLlrZfmrrXvvIzpu5jorqTlhajlrZfmrrUKICAgICAgc2VhcmNoRmllbGRPcHRpb25zOiBbewogICAgICAgIGxhYmVsOiAn5YWo5a2X5q61JywKICAgICAgICB2YWx1ZTogJ2FsbCcKICAgICAgfSwgewogICAgICAgIGxhYmVsOiAn6aG555uu57yW5Y+3JywKICAgICAgICB2YWx1ZTogJ3Byb2plY3RObycKICAgICAgfSwgewogICAgICAgIGxhYmVsOiAn6aG555uu5ZCN56ewJywKICAgICAgICB2YWx1ZTogJ3Byb2plY3ROYW1lJwogICAgICB9LCB7CiAgICAgICAgbGFiZWw6ICfor6bnu4blnLDlnYAnLAogICAgICAgIHZhbHVlOiAnYWRkcmVzcycKICAgICAgfSwgewogICAgICAgIGxhYmVsOiAn5oub5qCH5Y2V5L2NJywKICAgICAgICB2YWx1ZTogJ2JpZGRpbmdDb21wYW55JwogICAgICB9LCB7CiAgICAgICAgbGFiZWw6ICfmjojmnYPlhazlj7gnLAogICAgICAgIHZhbHVlOiAnYXV0aENvbXBhbnknCiAgICAgIH1dLAogICAgICBzZWFyY2hWYWx1ZTogJycsCiAgICAgIC8vIOaWsOWinu+8muaQnOe0ouWGheWuuQogICAgICBoaWdobGlnaHRGaWVsZHM6IFsncHJvamVjdE5vJywgJ3Byb2plY3ROYW1lJywgJ2FkZHJlc3MnLCAnYmlkZGluZ0NvbXBhbnknLCAnYXV0aENvbXBhbnknXSAvLyDmlrDlop7vvJrpq5jkuq7lrZfmrrUKICAgIH07CiAgfSwKICBhY3RpdmF0ZWQ6IGZ1bmN0aW9uIGFjdGl2YXRlZCgpIHsKICAgIGNvbnNvbGUubG9nKCI9cmVwb3J0IGluZGV4PT0+PmFjdGl2YXRlZCIpOwogICAgdGhpcy5nZXRMaXN0KCk7CiAgfSwKICBjcmVhdGVkOiBmdW5jdGlvbiBjcmVhdGVkKCkgewogICAgdmFyIF90aGlzMiA9IHRoaXM7CiAgICB0aGlzLmdldExpc3QoKTsKICAgIHRoaXMuZ2V0RGljdHMoInByX29wZXJhdGlvbl90eXBlIikudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgX3RoaXMyLm9wZXJhdGlvblR5cGVPcHRpb25zID0gcmVzcG9uc2UuZGF0YTsKICAgICAgdmFyIG9wdCA9IFtdOwogICAgICBvcHQucHVzaCh7CiAgICAgICAgaWQ6IDAsCiAgICAgICAgbGFiZWw6ICLlhajpg6giCiAgICAgIH0pOwogICAgICByZXNwb25zZS5kYXRhLmZvckVhY2goZnVuY3Rpb24gKGVsZW0sIGluZGV4KSB7CiAgICAgICAgdmFyIG9iaiA9IHt9OwogICAgICAgIG9iai5sYWJlbCA9IGVsZW0uZGljdExhYmVsOwogICAgICAgIG9iai5pZCA9IGVsZW0uZGljdFZhbHVlOwogICAgICAgIG9wdC5wdXNoKG9iaik7CiAgICAgIH0pOwogICAgICB2YXIgb3BlcmF0aW9uVHlwZSA9IHt9OwogICAgICBvcGVyYXRpb25UeXBlLmxhYmVsID0gIuaTjeS9nOexu+WeiyI7CiAgICAgIG9wZXJhdGlvblR5cGUuY2hpbGRyZW4gPSBvcHQ7CiAgICAgIHZhciBvcGVyYXRpb25UeXBlcyA9IFtdOwogICAgICBvcGVyYXRpb25UeXBlcy5wdXNoKG9wZXJhdGlvblR5cGUpOwogICAgICBfdGhpczIub3BlcmF0aW9uVHlwZVRyZWUgPSBvcGVyYXRpb25UeXBlczsKICAgIH0pOwogICAgdGhpcy5nZXREaWN0cygicHJfYXVkaXRfc3RhdHVzIikudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgdmFyIHR5cGUgPSAwOwogICAgICBpZiAoX3RoaXMyLiRzdG9yZS5zdGF0ZS51c2VyLnJvbGVzKSB7CiAgICAgICAgaWYgKF90aGlzMi4kc3RvcmUuc3RhdGUudXNlci5yb2xlcy5pbmNsdWRlcygiY29tbW9uIikpIHsKICAgICAgICAgIHR5cGUgPSAxOwogICAgICAgIH0KICAgICAgICBpZiAoX3RoaXMyLiRzdG9yZS5zdGF0ZS51c2VyLnJvbGVzLmluY2x1ZGVzKCJwcm92aW5jZV9hZG1pbiIpKSB7CiAgICAgICAgICB0eXBlID0gMjsKICAgICAgICB9CiAgICAgICAgaWYgKF90aGlzMi4kc3RvcmUuc3RhdGUudXNlci5yb2xlcy5pbmNsdWRlcygicmVwb3J0X2FkbWluIikpIHsKICAgICAgICAgIHR5cGUgPSAzOwogICAgICAgIH0KICAgICAgfQogICAgICBfdGhpczIuYXVkaXRTdGF0dXNPcHRpb25zID0gcmVzcG9uc2UuZGF0YTsKICAgICAgdmFyIG9wdCA9IFtdOwogICAgICBvcHQucHVzaCh7CiAgICAgICAgaWQ6IDksCiAgICAgICAgbGFiZWw6ICLlhajpg6giCiAgICAgIH0pOwogICAgICBpZiAodHlwZSA9PSAyIHx8IHR5cGUgPT0gMykgewogICAgICAgIG9wdC5wdXNoKHsKICAgICAgICAgIGlkOiAxMCwKICAgICAgICAgIGxhYmVsOiAi5pyq5a6h5om5IgogICAgICAgIH0pOwogICAgICB9CiAgICAgIHJlc3BvbnNlLmRhdGEuZm9yRWFjaChmdW5jdGlvbiAoZWxlbSwgaW5kZXgpIHsKICAgICAgICB2YXIgb2JqID0ge307CiAgICAgICAgb2JqLmxhYmVsID0gZWxlbS5kaWN0TGFiZWw7CiAgICAgICAgb2JqLmlkID0gZWxlbS5kaWN0VmFsdWU7CiAgICAgICAgb3B0LnB1c2gob2JqKTsKICAgICAgfSk7CiAgICAgIHZhciBhdWRpdFN0YXR1c1RyZWUgPSB7fTsKICAgICAgYXVkaXRTdGF0dXNUcmVlLmxhYmVsID0gIuWuoeaguOeKtuaAgSI7CiAgICAgIGF1ZGl0U3RhdHVzVHJlZS5jaGlsZHJlbiA9IG9wdDsKICAgICAgdmFyIGF1ZGl0U3RhdHVzVHJlZXMgPSBbXTsKICAgICAgYXVkaXRTdGF0dXNUcmVlcy5wdXNoKGF1ZGl0U3RhdHVzVHJlZSk7CiAgICAgIF90aGlzMi5hdWRpdFN0YXR1c1RyZWUgPSBhdWRpdFN0YXR1c1RyZWVzOwogICAgfSk7CiAgICB0aGlzLmdldERpY3RzKCJwcl9lZGl0X3N0YXR1cyIpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgIF90aGlzMi5lZGl0U3RhdHVzT3B0aW9ucyA9IHJlc3BvbnNlLmRhdGE7CiAgICB9KTsKICAgIC8vIHRoaXMuZ2V0RGljdHMoInByX2JpZGRpbmdfdHlwZSIpLnRoZW4oKHJlc3BvbnNlKSA9PiB7CiAgICAvLyAgIHRoaXMuYmlkZGluZ1R5cGVPcHRpb25zID0gcmVzcG9uc2UuZGF0YTsKICAgIC8vIH0pOwogICAgdGhpcy5nZXREaWN0cygicHJfbW9kZWwiKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICBfdGhpczIubW9kZWxPcHRpb24xID0gcmVzcG9uc2UuZGF0YTsKICAgICAgdmFyIG9wdCA9IFtdOwogICAgICByZXNwb25zZS5kYXRhLmZvckVhY2goZnVuY3Rpb24gKGVsZW0sIGluZGV4KSB7CiAgICAgICAgdmFyIG9iaiA9IHt9OwogICAgICAgIG9iai5sYWJlbCA9IGVsZW0uZGljdExhYmVsOwogICAgICAgIG9iai52YWx1ZSA9IGVsZW0uZGljdFZhbHVlOwogICAgICAgIG9wdC5wdXNoKG9iaik7CiAgICAgIH0pOwogICAgICBfdGhpczIubW9kZWxPcHRpb25zID0gb3B0OwogICAgfSk7CiAgICB0aGlzLmdldERpY3RzKCJwcl9zcGVjIikudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgX3RoaXMyLnNwZWNPcHRpb24xID0gcmVzcG9uc2UuZGF0YTsKICAgICAgdmFyIG9wdCA9IFtdOwogICAgICByZXNwb25zZS5kYXRhLmZvckVhY2goZnVuY3Rpb24gKGVsZW0sIGluZGV4KSB7CiAgICAgICAgdmFyIG9iaiA9IHt9OwogICAgICAgIG9iai5sYWJlbCA9IGVsZW0uZGljdExhYmVsOwogICAgICAgIG9iai52YWx1ZSA9IGVsZW0uZGljdFZhbHVlOwogICAgICAgIG9wdC5wdXNoKG9iaik7CiAgICAgIH0pOwogICAgICBfdGhpczIuc3BlY09wdGlvbnMgPSBvcHQ7CiAgICB9KTsKICAgIHRoaXMuZ2V0RGljdHMoInByX2luZm8iKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICBfdGhpczIucmVxdWlyZUluZm9PcHRpb24xID0gcmVzcG9uc2UuZGF0YTsKICAgICAgdmFyIG9wdCA9IFtdOwogICAgICByZXNwb25zZS5kYXRhLmZvckVhY2goZnVuY3Rpb24gKGVsZW0sIGluZGV4KSB7CiAgICAgICAgdmFyIG9iaiA9IHt9OwogICAgICAgIG9iai5sYWJlbCA9IGVsZW0uZGljdExhYmVsOwogICAgICAgIG9iai52YWx1ZSA9IGVsZW0uZGljdFZhbHVlOwogICAgICAgIG9wdC5wdXNoKG9iaik7CiAgICAgIH0pOwogICAgICBfdGhpczIucmVxdWlyZUluZm9PcHRpb25zID0gb3B0OwogICAgfSk7CiAgICB0aGlzLmdldERpY3RzKCJwcl9wcm92aW5jZSIpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgIF90aGlzMi5iZWxvbmdQcm92aW5jZU9wdGlvbnMxID0gcmVzcG9uc2UuZGF0YTsKICAgICAgdmFyIG9wdCA9IFtdOwogICAgICByZXNwb25zZS5kYXRhLmZvckVhY2goZnVuY3Rpb24gKGVsZW0sIGluZGV4KSB7CiAgICAgICAgdmFyIG9iaiA9IHt9OwogICAgICAgIG9iai5sYWJlbCA9IGVsZW0uZGljdExhYmVsOwogICAgICAgIG9iai52YWx1ZSA9IGVsZW0uZGljdFZhbHVlOwogICAgICAgIG9wdC5wdXNoKG9iaik7CiAgICAgIH0pOwogICAgICBfdGhpczIuYmVsb25nUHJvdmluY2VPcHRpb25zID0gb3B0OwogICAgfSk7CiAgICB0aGlzLmdldERpY3RzKCJwcl9hZnRlcl9zYWxlX3llYXIiKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICBfdGhpczIuYWZ0ZXJTYWxlWWVhck9wdGlvbnMxID0gcmVzcG9uc2UuZGF0YTsKICAgICAgdmFyIG9wdCA9IFtdOwogICAgICByZXNwb25zZS5kYXRhLmZvckVhY2goZnVuY3Rpb24gKGVsZW0sIGluZGV4KSB7CiAgICAgICAgdmFyIG9iaiA9IHt9OwogICAgICAgIG9iai5sYWJlbCA9IGVsZW0uZGljdExhYmVsOwogICAgICAgIG9iai52YWx1ZSA9IGVsZW0uZGljdFZhbHVlOwogICAgICAgIG9wdC5wdXNoKG9iaik7CiAgICAgIH0pOwogICAgICBfdGhpczIuYWZ0ZXJTYWxlWWVhck9wdGlvbnMgPSBvcHQ7CiAgICB9KTsKICAgIHRoaXMuZ2V0RGljdHMoInByX2RhdGFfdHlwZSIpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgIF90aGlzMi5pbmZvVHlwZU9wdGlvbnMgPSByZXNwb25zZS5kYXRhOwogICAgfSk7CiAgICB2YXIgb3B0ID0gW107CiAgICBvcHQucHVzaCh7CiAgICAgIGlkOiAwLAogICAgICBsYWJlbDogIuWFqOmDqCIKICAgIH0pOwogICAgb3B0LnB1c2goewogICAgICBpZDogMiwKICAgICAgbGFiZWw6ICLmma7pgJrnlKjmiLciCiAgICB9KTsKICAgIG9wdC5wdXNoKHsKICAgICAgaWQ6IDEwLAogICAgICBsYWJlbDogIuecgei0n+i0o+S6uiIKICAgIH0pOwogICAgdmFyIHVzZXJUeXBlID0ge307CiAgICB1c2VyVHlwZS5sYWJlbCA9ICLmiYDlsZ7nlKjmiLciOwogICAgdXNlclR5cGUuY2hpbGRyZW4gPSBvcHQ7CiAgICB2YXIgdXNlclR5cGVzID0gW107CiAgICB1c2VyVHlwZXMucHVzaCh1c2VyVHlwZSk7CiAgICB0aGlzLnVzZXJUeXBlVHJlZSA9IHVzZXJUeXBlczsKICAgIGlmICh0aGlzLiRzdG9yZS5zdGF0ZS51c2VyLnJvbGVzICYmIHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIucm9sZXMuaW5jbHVkZXMoImNvbW1vbiIpKSB7CiAgICAgIHRoaXMuc2hvd1VUeXBlID0gZmFsc2U7CiAgICB9CiAgICBpZiAodGhpcy5faXNNb2JpbGUoKSkgewogICAgICB0aGlzLmlzTW9iaWxlID0gdHJ1ZTsKICAgICAgdGhpcy5wYWdlTGF5b3V0ID0gInRvdGFsLCBwcmV2LCBuZXh0LCBqdW1wZXIiOwogICAgfQogICAgaWYgKHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIucm9sZXMgJiYgdGhpcy4kc3RvcmUuc3RhdGUudXNlci5yb2xlcy5pbmNsdWRlcygicmVwb3J0X2FkbWluIikpIHsKICAgICAgdGhpcy5pc0FkbWluID0gZmFsc2U7CiAgICB9CiAgfSwKICBtZXRob2RzOiB7CiAgICBfaXNNb2JpbGU6IGZ1bmN0aW9uIF9pc01vYmlsZSgpIHsKICAgICAgdmFyIGZsYWcgPSBuYXZpZ2F0b3IudXNlckFnZW50Lm1hdGNoKC8ocGhvbmV8cGFkfHBvZHxpUGhvbmV8aVBvZHxpb3N8aVBhZHxBbmRyb2lkfE1vYmlsZXxCbGFja0JlcnJ5fElFTW9iaWxlfE1RUUJyb3dzZXJ8SlVDfEZlbm5lY3x3T1NCcm93c2VyfEJyb3dzZXJOR3xXZWJPU3xTeW1iaWFufFdpbmRvd3MgUGhvbmUpL2kpOwogICAgICByZXR1cm4gZmxhZzsKICAgIH0sCiAgICAvKiog5p+l6K+i6aG555uu5oql5aSH5YiX6KGoICovZ2V0TGlzdDogZnVuY3Rpb24gZ2V0TGlzdCgpIHsKICAgICAgdmFyIF90aGlzMyA9IHRoaXM7CiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7CiAgICAgIGxpc3RSZXBvcnQodGhpcy5xdWVyeVBhcmFtcykudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICBfdGhpczMucmVwb3J0TGlzdCA9IHJlc3BvbnNlLnJvd3M7CiAgICAgICAgX3RoaXMzLnRvdGFsID0gcmVzcG9uc2UudG90YWw7CiAgICAgICAgX3RoaXMzLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgfSk7CiAgICB9LAogICAgaGFuZGxlRG93bmxvYWQ6IGZ1bmN0aW9uIGhhbmRsZURvd25sb2FkKHVybCkgewogICAgICB2YXIgYSA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoImEiKTsgLy8g5Yib5bu65LiA5LiqSFRNTCDlhYPntKAKICAgICAgYS5zZXRBdHRyaWJ1dGUoInRhcmdldCIsICJfYmxhbmsiKTsKICAgICAgYS5zZXRBdHRyaWJ1dGUoImRvd25sb2FkIiwgIiIpOyAvL2Rvd25sb2Fk5bGe5oCnCiAgICAgIHZhciBocmVmID0gImh0dHBzOi8vcmVwb3J0LmNsbGVkLmNvbS9wcm9kLWFwaS9jb21tb24vZG93bmxvYWQvcmVzb3VyY2U/cmVzb3VyY2U9IiArIHVybDsKICAgICAgY29uc29sZS5sb2coaHJlZik7CiAgICAgIGEuc2V0QXR0cmlidXRlKCJocmVmIiwgaHJlZik7IC8vIGhyZWbpk77mjqUKICAgICAgYS5jbGljaygpOyAvLyDoh6rmiafooYzngrnlh7vkuovku7YKICAgIH0sCiAgICAvLyDlrqHmoLjnirbmgIHoioLngrnljZXlh7vkuovku7YKICAgIGhhbmRsZUF1ZGl0Tm9kZUNsaWNrOiBmdW5jdGlvbiBoYW5kbGVBdWRpdE5vZGVDbGljayhkYXRhKSB7CiAgICAgIGlmIChkYXRhLmlkID09IDkpIHsKICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmF1ZGl0U3RhdHVzID0gdW5kZWZpbmVkOwogICAgICAgIHRoaXMucXVlcnlQYXJhbXMubm9kZSA9IHVuZGVmaW5lZDsKICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnNwYXJlMSA9IHVuZGVmaW5lZDsKICAgICAgfSBlbHNlIHsKICAgICAgICBpZiAoZGF0YS5pZCA9PSAxIHx8IGRhdGEuaWQgPT0gMTApIHsKICAgICAgICAgIHRoaXMucXVlcnlQYXJhbXMuYXVkaXRTdGF0dXMgPSAxOwogICAgICAgICAgaWYgKHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIucm9sZXMpIHsKICAgICAgICAgICAgaWYgKHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIucm9sZXMuaW5jbHVkZXMoInByb3ZpbmNlX2FkbWluIikpIHsKICAgICAgICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLm5vZGUgPSAi55yB6LSf6LSj5Lq6IjsKICAgICAgICAgICAgICBpZiAoZGF0YS5pZCA9PSAxMCkgewogICAgICAgICAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5zcGFyZTEgPSAiPSI7CiAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgIHRoaXMucXVlcnlQYXJhbXMuc3BhcmUxID0gIiE9IjsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0KICAgICAgICAgICAgaWYgKHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIucm9sZXMuaW5jbHVkZXMoInJlcG9ydF9hZG1pbiIpKSB7CiAgICAgICAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5ub2RlID0gIuWuoeaguOWRmCI7CiAgICAgICAgICAgICAgaWYgKGRhdGEuaWQgPT0gMTApIHsKICAgICAgICAgICAgICAgIHRoaXMucXVlcnlQYXJhbXMuc3BhcmUxID0gIj0iOwogICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnNwYXJlMSA9ICIhPSI7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMucXVlcnlQYXJhbXMuYXVkaXRTdGF0dXMgPSBkYXRhLmlkOwogICAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5ub2RlID0gdW5kZWZpbmVkOwogICAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5zcGFyZTEgPSB1bmRlZmluZWQ7CiAgICAgICAgfQogICAgICB9CiAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgfSwKICAgIC8vIOaTjeS9nOexu+Wei+iKgueCueWNleWHu+S6i+S7tgogICAgaGFuZGxlT3B0Tm9kZUNsaWNrOiBmdW5jdGlvbiBoYW5kbGVPcHROb2RlQ2xpY2soZGF0YSkgewogICAgICBpZiAoZGF0YS5pZCA9PSAwKSB7CiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5vcGVyYXRpb25UeXBlID0gdW5kZWZpbmVkOwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMucXVlcnlQYXJhbXMub3BlcmF0aW9uVHlwZSA9IGRhdGEuaWQ7CiAgICAgIH0KICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICB9LAogICAgLy8g55So5oi357G75Z6L6IqC54K55Y2V5Ye75LqL5Lu2CiAgICBoYW5kbGVVc2VyTm9kZUNsaWNrOiBmdW5jdGlvbiBoYW5kbGVVc2VyTm9kZUNsaWNrKGRhdGEpIHsKICAgICAgaWYgKGRhdGEuaWQgPT0gMCkgewogICAgICAgIHRoaXMucXVlcnlQYXJhbXMudXNlclR5cGUgPSB1bmRlZmluZWQ7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy51c2VyVHlwZSA9IGRhdGEuaWQ7CiAgICAgIH0KICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICB9LAogICAgLy8g562b6YCJ6IqC54K5CiAgICBmaWx0ZXJOb2RlOiBmdW5jdGlvbiBmaWx0ZXJOb2RlKHZhbHVlLCBkYXRhKSB7CiAgICAgIGlmICghdmFsdWUpIHJldHVybiB0cnVlOwogICAgICByZXR1cm4gZGF0YS5sYWJlbC5pbmRleE9mKHZhbHVlKSAhPT0gLTE7CiAgICB9LAogICAgc2VhcmNoRm9ybWF0OiBmdW5jdGlvbiBzZWFyY2hGb3JtYXQocm93LCBjb2x1bW4pIHsKICAgICAgaWYgKHJvdy5pbmRleE9mKHRoaXMucXVlcnlQYXJhbXMuc3BhcmUxKSAhPT0gLTEgJiYgdGhpcy5xdWVyeVBhcmFtcy5zcGFyZTEgIT09ICIiKSB7CiAgICAgICAgcmV0dXJuIHJvdy5yZXBsYWNlKHRoaXMucXVlcnlQYXJhbXMuc3BhcmUxLCAnPGZvbnQgY29sb3I9IiNmMDAiPicgKyB0aGlzLnF1ZXJ5UGFyYW1zLnNwYXJlMSArICI8L2ZvbnQ+Iik7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgcmV0dXJuIHJvdzsKICAgICAgfQogICAgfSwKICAgIC8vIOaTjeS9nOexu+Wei+Wtl+WFuOe/u+ivkQogICAgb3BlcmF0aW9uVHlwZUZvcm1hdDogZnVuY3Rpb24gb3BlcmF0aW9uVHlwZUZvcm1hdChyb3csIGNvbHVtbikgewogICAgICByZXR1cm4gdGhpcy5zZWxlY3REaWN0TGFiZWwodGhpcy5vcGVyYXRpb25UeXBlT3B0aW9ucywgcm93Lm9wZXJhdGlvblR5cGUpOwogICAgfSwKICAgIC8vIOWuoeaguOeKtuaAgeWtl+WFuOe/u+ivkQogICAgYXVkaXRTdGF0dXNGb3JtYXQ6IGZ1bmN0aW9uIGF1ZGl0U3RhdHVzRm9ybWF0KHJvdywgY29sdW1uKSB7CiAgICAgIHJldHVybiB0aGlzLnNlbGVjdERpY3RMYWJlbCh0aGlzLmF1ZGl0U3RhdHVzT3B0aW9ucywgcm93LmF1ZGl0U3RhdHVzKTsKICAgIH0sCiAgICAvLyDnvJbovpHnirbmgIHlrZflhbjnv7vor5EKICAgIGVkaXRTdGF0dXNGb3JtYXQ6IGZ1bmN0aW9uIGVkaXRTdGF0dXNGb3JtYXQocm93LCBjb2x1bW4pIHsKICAgICAgcmV0dXJuIHRoaXMuc2VsZWN0RGljdExhYmVsKHRoaXMuZWRpdFN0YXR1c09wdGlvbnMsIHJvdy5lZGl0U3RhdHVzKTsKICAgIH0sCiAgICAvLyDmi5vmoIfmlrnlvI/lrZflhbjnv7vor5EKICAgIGJpZGRpbmdUeXBlRm9ybWF0OiBmdW5jdGlvbiBiaWRkaW5nVHlwZUZvcm1hdChyb3csIGNvbHVtbikgewogICAgICByZXR1cm4gdGhpcy5zZWxlY3REaWN0TGFiZWwodGhpcy5iaWRkaW5nVHlwZU9wdGlvbnMsIHJvdy5iaWRkaW5nVHlwZSk7CiAgICB9LAogICAgLy8g5oqV5qCH5Lqn5ZOB5Z6L5Y+35a2X5YW457+76K+RCiAgICBtb2RlbEZvcm1hdDogZnVuY3Rpb24gbW9kZWxGb3JtYXQocm93LCBjb2x1bW4pIHsKICAgICAgcmV0dXJuIHRoaXMuc2VsZWN0RGljdExhYmVscyh0aGlzLm1vZGVsT3B0aW9uMSwgcm93Lm1vZGVsKTsKICAgIH0sCiAgICAvLyDmipXmoIfkuqflk4Hop4TmoLzlrZflhbjnv7vor5EKICAgIHNwZWNGb3JtYXQ6IGZ1bmN0aW9uIHNwZWNGb3JtYXQocm93LCBjb2x1bW4pIHsKICAgICAgcmV0dXJuIHRoaXMuc2VsZWN0RGljdExhYmVscyh0aGlzLnNwZWNPcHRpb24xLCByb3cuc3BlYyk7CiAgICB9LAogICAgLy8g5omA6ZyA6LWE5paZ5a2X5YW457+76K+RCiAgICByZXF1aXJlSW5mb0Zvcm1hdDogZnVuY3Rpb24gcmVxdWlyZUluZm9Gb3JtYXQocm93LCBjb2x1bW4pIHsKICAgICAgaWYgKHJvdy5yZXF1aXJlSW5mbykgewogICAgICAgIHJldHVybiB0aGlzLnNlbGVjdERpY3RMYWJlbHModGhpcy5yZXF1aXJlSW5mb09wdGlvbjEsIHJvdy5yZXF1aXJlSW5mbyk7CiAgICAgIH0KICAgIH0sCiAgICAvLyDotYTmlpnnsbvlnovlrZflhbjnv7vor5EKICAgIGluZm9UeXBlRm9ybWF0OiBmdW5jdGlvbiBpbmZvVHlwZUZvcm1hdChyb3csIGNvbHVtbikgewogICAgICByZXR1cm4gdGhpcy5zZWxlY3REaWN0TGFiZWxzKHRoaXMuaW5mb1R5cGVPcHRpb25zLCByb3cuaW5mb1R5cGUpOwogICAgfSwKICAgIC8vIOaJgOWxnuecgeS7veWtl+WFuOe/u+ivkQogICAgYmVsb25nUHJvdmluY2VGb3JtYXQ6IGZ1bmN0aW9uIGJlbG9uZ1Byb3ZpbmNlRm9ybWF0KHJvdywgY29sdW1uKSB7CiAgICAgIHJldHVybiB0aGlzLnNlbGVjdERpY3RMYWJlbHModGhpcy5iZWxvbmdQcm92aW5jZU9wdGlvbnMxLCByb3cuYmVsb25nUHJvdmluY2UpOwogICAgfSwKICAgIC8vIOWUruWQjuW5tOmZkOWtl+WFuOe/u+ivkQogICAgYWZ0ZXJTYWxlWWVhckZvcm1hdDogZnVuY3Rpb24gYWZ0ZXJTYWxlWWVhckZvcm1hdChyb3csIGNvbHVtbikgewogICAgICByZXR1cm4gdGhpcy5zZWxlY3REaWN0TGFiZWxzKHRoaXMuYWZ0ZXJTYWxlWWVhck9wdGlvbnMxLCByb3cuYWZ0ZXJTYWxlWWVhcik7CiAgICB9LAogICAgLy8g5Y+W5raI5oyJ6ZKuCiAgICBjYW5jZWw6IGZ1bmN0aW9uIGNhbmNlbCgpIHsKICAgICAgdGhpcy5vcGVuID0gZmFsc2U7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgIH0sCiAgICB2aWV3T2s6IGZ1bmN0aW9uIHZpZXdPaygpIHsKICAgICAgdGhpcy52aWV3T3BlbiA9IGZhbHNlOwogICAgfSwKICAgIC8vIOihqOWNlemHjee9rgogICAgcmVzZXQ6IGZ1bmN0aW9uIHJlc2V0KCkgewogICAgICB0aGlzLmZvcm0gPSB7CiAgICAgICAgcHJvamVjdElkOiBudWxsLAogICAgICAgIHByb2plY3RObzogbnVsbCwKICAgICAgICBwcm9qZWN0TmFtZTogbnVsbCwKICAgICAgICBvcGVyYXRpb25UeXBlOiBudWxsLAogICAgICAgIGF1ZGl0U3RhdHVzOiAiMCIsCiAgICAgICAgcmVqZWN0UmVhc29uOiBudWxsLAogICAgICAgIHByb3ZpbmNlOiBudWxsLAogICAgICAgIGNpdHk6IG51bGwsCiAgICAgICAgZGlzdHJpY3Q6IG51bGwsCiAgICAgICAgYWRkcmVzczogbnVsbCwKICAgICAgICBlZGl0U3RhdHVzOiAiMCIsCiAgICAgICAgYmVsb25nVXNlcjogbnVsbCwKICAgICAgICBiaWRkaW5nQ29tcGFueTogbnVsbCwKICAgICAgICBvcGVuRGF0ZTogbnVsbCwKICAgICAgICBiaWRkaW5nVHlwZTogbnVsbCwKICAgICAgICBidWRnZXRNb25leTogbnVsbCwKICAgICAgICBhdXRoQ29tcGFueTogbnVsbCwKICAgICAgICBiaWRkaW5nTmV0OiBudWxsLAogICAgICAgIGRpc3RyaWJ1dG9yOiBudWxsLAogICAgICAgIG1vZGVsOiBbXSwKICAgICAgICBzcGVjOiBbXSwKICAgICAgICBhcmVhOiBudWxsLAogICAgICAgIGF1dGhGaWxlOiBudWxsLAogICAgICAgIGFmdGVyU2FsZUZpbGU6IG51bGwsCiAgICAgICAgcmVxdWlyZUluZm86IFtdLAogICAgICAgIGluZm9UeXBlOiBbXSwKICAgICAgICBzY2FuRmlsZTogbnVsbCwKICAgICAgICBzZW5kQWRkcmVzczogbnVsbCwKICAgICAgICBtYWlsSW5mbzogbnVsbCwKICAgICAgICBleHByZXNzSW5mbzogbnVsbCwKICAgICAgICByZW1hcms6IG51bGwsCiAgICAgICAgc3BhcmUxOiBudWxsLAogICAgICAgIHNwYXJlMjogbnVsbAogICAgICB9OwogICAgICB0aGlzLnJlc2V0Rm9ybSgiZm9ybSIpOwogICAgfSwKICAgIC8qKiDmkJzntKLmjInpkq7mk43kvZwgKi9oYW5kbGVRdWVyeTogZnVuY3Rpb24gaGFuZGxlUXVlcnkoKSB7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDE7CiAgICAgIC8vIOa4heepuuaJgOacieebuOWFs+Wtl+autQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnByb2plY3RObyA9IG51bGw7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMucHJvamVjdE5hbWUgPSBudWxsOwogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmFkZHJlc3MgPSBudWxsOwogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmJpZGRpbmdDb21wYW55ID0gbnVsbDsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5hdXRoQ29tcGFueSA9IG51bGw7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMuZnVsbEZpZWxkID0gbnVsbDsKICAgICAgaWYgKHRoaXMuc2VhcmNoRmllbGQgPT09ICdhbGwnKSB7CiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5mdWxsRmllbGQgPSB0aGlzLnNlYXJjaFZhbHVlOyAvLyDlgYforr7lkI7nq68gZnVsbEZpZWxkIOWBmuWFqOWtl+auteaooeezigogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMucXVlcnlQYXJhbXNbdGhpcy5zZWFyY2hGaWVsZF0gPSB0aGlzLnNlYXJjaFZhbHVlOwogICAgICB9CiAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgfSwKICAgIC8qKiDph43nva7mjInpkq7mk43kvZwgKi9yZXNldFF1ZXJ5OiBmdW5jdGlvbiByZXNldFF1ZXJ5KCkgewogICAgICB0aGlzLnF1ZXJ5QXJlYSA9IFtdOwogICAgICB0aGlzLnF1ZXJ5UGFyYW1zID0gewogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgIHByb2plY3RObzogbnVsbCwKICAgICAgICBwcm9qZWN0TmFtZTogbnVsbCwKICAgICAgICBvcGVyYXRpb25UeXBlOiBudWxsLAogICAgICAgIGF1ZGl0U3RhdHVzOiBudWxsLAogICAgICAgIHByb3ZpbmNlOiBudWxsLAogICAgICAgIHVzZXJUeXBlOiBudWxsLAogICAgICAgIGJlbG9uZ1VzZXI6IG51bGwsCiAgICAgICAgc3BhcmUxOiBudWxsLAogICAgICAgIGFkZHJlc3M6IG51bGwsCiAgICAgICAgYmlkZGluZ0NvbXBhbnk6IG51bGwsCiAgICAgICAgYXV0aENvbXBhbnk6IG51bGwsCiAgICAgICAgZnVsbEZpZWxkOiBudWxsCiAgICAgIH07CiAgICAgIHRoaXMuc2VhcmNoRmllbGQgPSAnYWxsJzsKICAgICAgdGhpcy5zZWFyY2hWYWx1ZSA9ICcnOwogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7CiAgICB9LAogICAgLy8g5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2U6IGZ1bmN0aW9uIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsKICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgcmV0dXJuIGl0ZW0ucHJvamVjdElkOwogICAgICB9KTsKICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoICE9PSAxOwogICAgICB0aGlzLm11bHRpcGxlID0gIXNlbGVjdGlvbi5sZW5ndGg7CiAgICAgIHRoaXMuY2hvb3NlT3B0VHlwZSA9IHNlbGVjdGlvblswXS5vcGVyYXRpb25UeXBlOwogICAgICB0aGlzLmNob29zZUF1ZGl0U3RhdHVzID0gc2VsZWN0aW9uWzBdLmF1ZGl0U3RhdHVzOwogICAgICB0aGlzLmNob29zZVVzZXJJZCA9IHNlbGVjdGlvblswXS51c2VySWQ7CiAgICAgIHRoaXMuY2hvb3NlRWRpdFN0YXR1cyA9IHNlbGVjdGlvblswXS5lZGl0U3RhdHVzOwogICAgICB0aGlzLmNob29zZVNwYXJlMiA9IHNlbGVjdGlvblswXS5zcGFyZTI7CiAgICB9LAogICAgLyoqIOaWsOWinuaMiemSruaTjeS9nCAqL2hhbmRsZUFkZDogZnVuY3Rpb24gaGFuZGxlQWRkKCkgewogICAgICAvLyB0aGlzLnJlc2V0KCk7CiAgICAgIC8vIHRoaXMub3BlbiA9IHRydWU7CiAgICAgIC8vIHRoaXMudGl0bGUgPSAi5re75Yqg6aG555uu5oql5aSHIjsKICAgICAgdGhpcy4kcm91dGVyLnB1c2goewogICAgICAgIHBhdGg6ICIvcHJvamVjdC9yZXBvcnQvZm9ybSIsCiAgICAgICAgcXVlcnk6IHsKICAgICAgICAgIGJ1c2luZXNzS2V5OiB1bmRlZmluZWQsCiAgICAgICAgICBmb3JtRWRpdDogdHJ1ZQogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgaGFuZGxlVmlldzogZnVuY3Rpb24gaGFuZGxlVmlldyhyb3cpIHsKICAgICAgdmFyIF90aGlzNCA9IHRoaXM7CiAgICAgIHZhciB0aGF0ID0gdGhpczsKICAgICAgdGhpcy52aWV3ID0gSlNPTi5wYXJzZShKU09OLnN0cmluZ2lmeShyb3cpKTsKICAgICAgdGhpcy52aWV3Lm9wZXJhdGlvblR5cGUgPSB0aGlzLm9wZXJhdGlvblR5cGVGb3JtYXQocm93KTsKICAgICAgdGhpcy52aWV3LmF1ZGl0U3RhdHVzID0gdGhpcy5hdWRpdFN0YXR1c0Zvcm1hdChyb3cpOwogICAgICB0aGlzLnZpZXcuZWRpdFN0YXR1cyA9IHRoaXMuZWRpdFN0YXR1c0Zvcm1hdChyb3cpOwogICAgICB0aGlzLnZpZXcuYmlkZGluZ1R5cGUgPSB0aGlzLmJpZGRpbmdUeXBlRm9ybWF0KHJvdyk7CiAgICAgIHRoaXMudmlldy5tb2RlbCA9IHRoaXMubW9kZWxGb3JtYXQocm93KTsKICAgICAgdGhpcy52aWV3LnNwZWMgPSB0aGlzLnNwZWNGb3JtYXQocm93KTsKICAgICAgdGhpcy52aWV3LnJlcXVpcmVJbmZvID0gdGhpcy5yZXF1aXJlSW5mb0Zvcm1hdChyb3cpOwogICAgICB0aGlzLnZpZXcuaW5mb1R5cGUgPSB0aGlzLmluZm9UeXBlRm9ybWF0KHJvdyk7CiAgICAgIHRoaXMudmlldy5iZWxvbmdQcm92aW5jZSA9IHRoaXMuYmVsb25nUHJvdmluY2VGb3JtYXQocm93KTsKICAgICAgdGhpcy52aWV3LmFmdGVyU2FsZVllYXIgPSB0aGlzLmFmdGVyU2FsZVllYXJGb3JtYXQocm93KTsKICAgICAgaWYgKHJvdy5yZXF1aXJlSW5mb3MpIHsKICAgICAgICB0aGlzLnNlbGVjdERpY3RMYWJlbHModGhpcy5yZXF1aXJlSW5mb09wdGlvbjEsIHJvdy5yZXF1aXJlSW5mbyk7CiAgICAgICAgLy90aGlzLnZpZXcucmVxdWlyZUluZm8gPQogICAgICAgIC8vIGNvbnN0IGluZm9MaXN0ID0gdGhpcy52aWV3LnJlcXVpcmVJbmZvLnNwbGl0KCIsIik7CiAgICAgICAgdmFyIGhhbGYgPSBNYXRoLmNlaWwocm93LnJlcXVpcmVJbmZvcy5sZW5ndGggLyAyKTsKICAgICAgICB0aGlzLmluZm9MaXN0MSA9IHJvdy5yZXF1aXJlSW5mb3Muc3BsaWNlKDAsIGhhbGYpOwogICAgICAgIHRoaXMuaW5mb0xpc3QyID0gcm93LnJlcXVpcmVJbmZvcy5zcGxpY2UoLWhhbGYpOwoKICAgICAgICAvLyBjb25zdCB0bXBMaXN0MSA9IGluZm9MaXN0LnNwbGljZSgwLCBoYWxmKTsKICAgICAgICAvLyBjb25zdCB0bXBMaXN0MiA9IGluZm9MaXN0LnNwbGljZSgtaGFsZik7CiAgICAgICAgLy8gdG1wTGlzdDEuZm9yRWFjaCgoZWxlbWVudCkgPT4gewogICAgICAgIC8vICAgY29uc29sZS5sb2coZWxlbWVudCk7CiAgICAgICAgLy8gfSk7CiAgICAgICAgLy8g5b6q546v5a+56LGh6LWL5YC8CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy5pbmZvTGlzdDEgPSBbXTsKICAgICAgICB0aGlzLmluZm9MaXN0MiA9IFtdOwogICAgICB9CiAgICAgIGlmIChyb3cub3BlcmF0aW9uVHlwZSA9PSAiMiIgJiYgcm93LnNwYXJlMSA9PSAiMSIpIHsKICAgICAgICB0aGlzLmRlZktleSA9ICJwcm9jZXNzX3Byb2plY3RfYXV0aCI7CiAgICAgICAgdGhpcy50aXRsZSA9ICLmn6XnnIvpobnnm67miqXlpIfovazmjojmnYMiOwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuZGVmS2V5ID0gInByb2Nlc3NfcHJvamVjdF9yZXBvcnQiOwogICAgICAgIHRoaXMudGl0bGUgPSAi5p+l55yL6aG555uu5oql5aSHL+aOiOadgyI7CiAgICAgIH0KICAgICAgdmFyIHBhcmFtcyA9IHsKICAgICAgICBiaXpLZXk6IHJvdy5wcm9qZWN0SWQsCiAgICAgICAgZGVmS2V5OiB0aGlzLmRlZktleQogICAgICB9OwogICAgICBnZXRJbnNJZEJ5Qml6S2V5KHBhcmFtcykudGhlbihmdW5jdGlvbiAocmVzcCkgewogICAgICAgIF90aGlzNC5iaXpLZXkgPSByb3cucHJvamVjdElkOwogICAgICAgIGlmIChyZXNwLmRhdGEgJiYgcmVzcC5kYXRhLmluc3RhbmNlSWQpIHsKICAgICAgICAgIF90aGlzNC5wcm9jSW5zSWQgPSByZXNwLmRhdGEuaW5zdGFuY2VJZDsKICAgICAgICAgIF90aGlzNC50YXNrSWQgPSByZXNwLmRhdGEudGFza0lkOwogICAgICAgICAgLy9jb25zb2xlLmxvZygiPT1oYW5kbGVWaWV3PT4+IikKICAgICAgICAgIC8vY29uc29sZS5sb2cocmVzcC5kYXRhKQogICAgICAgICAgaWYgKHJlc3AuZGF0YS5pbnN0YW5jZUlkICYmICFyZXNwLmRhdGEuZW5kVGltZSAmJiByZXNwLmRhdGEuYXNzaWduZWUgPT0gX3RoaXM0LiRzdG9yZS5zdGF0ZS51c2VyLnVzZXJJZCkgewogICAgICAgICAgICBpZiAoX3RoaXM0LiRzdG9yZS5zdGF0ZS51c2VyLnJvbGVzICYmIF90aGlzNC4kc3RvcmUuc3RhdGUudXNlci5yb2xlcy5pbmNsdWRlcygicmVwb3J0X2FkbWluIikgJiYgcm93Lm9wZXJhdGlvblR5cGUgPT0gIjIiKSB7CiAgICAgICAgICAgICAgX3RoaXM0LmlzQXV0aEltYWdlcyA9IHRydWU7CiAgICAgICAgICAgIH0KICAgICAgICAgICAgX3RoaXM0LmZpbmlzaGVkID0gZmFsc2U7CiAgICAgICAgICB9IGVsc2UgaWYgKF90aGlzNC4kc3RvcmUuc3RhdGUudXNlci5yb2xlcyAmJiBfdGhpczQuJHN0b3JlLnN0YXRlLnVzZXIucm9sZXMuaW5jbHVkZXMoInJlcG9ydF9hZG1pbiIpICYmIHJvdy5ub2RlID09ICLlrqHmoLjlkZgiKSB7CiAgICAgICAgICAgIGlmIChyb3cub3BlcmF0aW9uVHlwZSA9PSAiMiIpIHsKICAgICAgICAgICAgICBfdGhpczQuaXNBdXRoSW1hZ2VzID0gdHJ1ZTsKICAgICAgICAgICAgfQogICAgICAgICAgICAvL+WuoeaguOWRmOinkuiJsuS4jeaOp+WItuiwgeaTjeS9nAogICAgICAgICAgICBfdGhpczQuZmluaXNoZWQgPSBmYWxzZTsKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIF90aGlzNC5maW5pc2hlZCA9IHRydWU7CiAgICAgICAgICB9CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIF90aGlzNC5maW5pc2hlZCA9IHRydWU7CiAgICAgICAgICBfdGhpczQucHJvY0luc0lkID0gdW5kZWZpbmVkOwogICAgICAgICAgX3RoaXM0LnRhc2tJZCA9IHVuZGVmaW5lZDsKICAgICAgICB9CgogICAgICAgIC8vIGNvbnNvbGUubG9nKCI9PT09Pj4+6amz5ZueIikKICAgICAgICAvLyAvL+mps+WbnueUqOaItwogICAgICAgIC8vIGlmKHJvdy5hdWRpdFN0YXR1cyA9PSAnMycgJiYgcm93LnVzZXJJZCA9PSB0aGlzLiRzdG9yZS5zdGF0ZS51c2VyLnVzZXJJZCl7CiAgICAgICAgLy8gICB0aGlzLmZpbmlzaGVkID0gZmFsc2U7CiAgICAgICAgLy8gfQogICAgICAgIC8vIGNvbnNvbGUubG9nKCI9PT09Pj4+6amz5Zue77yaIiArIHRoaXMuZmluaXNoZWQpCgogICAgICAgIF90aGlzNC52aWV3T3BlbiA9IHRydWU7CiAgICAgIH0pOwogICAgICBnZXRMaWtlTGlzdCh7CiAgICAgICAgcHJvamVjdE5hbWU6IHJvdy5wcm9qZWN0TmFtZSwKICAgICAgICBwcm9qZWN0SWQ6IHJvdy5wcm9qZWN0SWQKICAgICAgfSkudGhlbihmdW5jdGlvbiAocmVzcCkgewogICAgICAgIC8vY29uc29sZS5sb2cocmVzcCkKICAgICAgICBpZiAocmVzcC5kYXRhICYmIHJlc3AuZGF0YS5sZW5ndGggPiAwKSB7CiAgICAgICAgICBfdGhpczQubGlrZUxpc3QgPSByZXNwLmRhdGE7CiAgICAgICAgICB0aGF0Lmxpa2Vjb3VudCA9IHJlc3AuZGF0YS5sZW5ndGg7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIF90aGlzNC5saWtlTGlzdCA9IHVuZGVmaW5lZDsKICAgICAgICAgIHRoYXQubGlrZWNvdW50ID0gdW5kZWZpbmVkOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLyoqIOaOiOadg+aMiemSruaTjeS9nCAqL2hhbmRsZUF1dGg6IGZ1bmN0aW9uIGhhbmRsZUF1dGgocm93KSB7CiAgICAgIHZhciBfdGhpczUgPSB0aGlzOwogICAgICB2YXIgbG9hZGluZyA9IHRoaXMuJGxvYWRpbmcoewogICAgICAgIGxvY2s6IHRydWUsCiAgICAgICAgdGV4dDogIuaOiOadg+S4rS4uLiIsCiAgICAgICAgc3Bpbm5lcjogImVsLWljb24tbG9hZGluZyIsCiAgICAgICAgYmFja2dyb3VuZDogInJnYmEoMCwgMCwgMCwgMC43KSIKICAgICAgfSk7CiAgICAgIGF1dGhSZXBvcnQocm93KS50aGVuKGZ1bmN0aW9uIChyZXNwKSB7CiAgICAgICAgbG9hZGluZy5jbG9zZSgpOwogICAgICAgIF90aGlzNS5tc2dTdWNjZXNzKHJlc3AubXNnKTsKICAgICAgICBfdGhpczUuJHJvdXRlci5nbygwKTsKICAgICAgfSkuY2F0Y2goZnVuY3Rpb24gKGUpIHsKICAgICAgICBsb2FkaW5nLmNsb3NlKCk7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDkv67mlLnmjInpkq7mk43kvZwgKi9oYW5kbGVVcGRhdGU6IGZ1bmN0aW9uIGhhbmRsZVVwZGF0ZSgpIHsKICAgICAgdmFyIF90aGlzNiA9IHRoaXM7CiAgICAgIHZhciB0aGF0ID0gdGhpczsKICAgICAgLy8gdGhhdC5hdWRpdFN0YXR1c0VkaXQgPSB0cnVlOwogICAgICAvLyBpZighdGhhdC5pc0FkbWluICYmIHRoYXQuY2hvb3NlQXVkaXRTdGF0dXMgPT0gMyl7CiAgICAgIC8vICAgdGhhdC5hdWRpdFN0YXR1c0VkaXQgPSBmYWxzZTsKICAgICAgLy8gfWVsc2V7fQogICAgICAvL+eUs+ivt+iAhQogICAgICB2YXIgaXNBcHBseSA9IHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIucm9sZXMgJiYgKHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIucm9sZXMuaW5jbHVkZXMoImNvbW1vbiIpIHx8IHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIucm9sZXMuaW5jbHVkZXMoInByb3ZpbmNlX2FkbWluIikpOwogICAgICBpZiAoaXNBcHBseSAmJiB0aGlzLmNob29zZVVzZXJJZCAhPSB0aGlzLiRzdG9yZS5zdGF0ZS51c2VyLnVzZXJJZCkgewogICAgICAgIHRoaXMubXNnRXJyb3IoIuWPquiDveS/ruaUueacrOS6uuaPkOS6pOeahOmhueebriIpOwogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICBpZiAodGhpcy5jaG9vc2VPcHRUeXBlID09IDIpIHsKICAgICAgICBpZiAoaXNBcHBseSAmJiB0aGlzLmNob29zZVNwYXJlMiAhPSAxKSB7CiAgICAgICAgICB0aGlzLm1zZ0Vycm9yKCLmjojmnYPooqvpgIDlm57miY3og73kv67mlLkiKTsKICAgICAgICAgIHJldHVybjsKICAgICAgICB9CiAgICAgIH0KICAgICAgaWYgKHRoaXMuY2hvb3NlQXVkaXRTdGF0dXMgPT0gMyAmJiBpc0FwcGx5KSB7CiAgICAgICAgaWYgKHRoaXMuY2hvb3NlRWRpdFN0YXR1cyA9PSAiMCIpIHsKICAgICAgICAgIHRoaXMubXNnRXJyb3IoIuWuoeaJueiiq+mps+WbnuaXoOazleS/ruaUuSIpOwogICAgICAgICAgcmV0dXJuOwogICAgICAgIH0KICAgICAgfQogICAgICAvLyBpZih0aGlzLmNob29zZU9wdFR5cGUgPT0gMSl7CiAgICAgIC8vICAgaWYoaXNBcHBseSAmJiB0aGlzLmNob29zZVVzZXJJZCAhPSB0aGlzLiRzdG9yZS5zdGF0ZS51c2VyLnVzZXJJZCApewogICAgICAvLyAgICAgdGhpcy5tc2dFcnJvcigi5Y+q6IO95L+u5pS55pys5Lq65o+Q5Lqk55qE5oql5aSH6aG555uuIik7CiAgICAgIC8vICAgICByZXR1cm47CiAgICAgIC8vICAgfQogICAgICAvLyB9CiAgICAgIHRoaXMucmVzZXQoKTsKICAgICAgdmFyIHByb2plY3RJZCA9IHRoaXMuaWRzOwogICAgICBnZXRSZXBvcnQocHJvamVjdElkKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgIF90aGlzNi5mb3JtID0gcmVzcG9uc2UuZGF0YTsKICAgICAgICBpZiAoX3RoaXM2LmZvcm0ubW9kZWwpIF90aGlzNi5mb3JtLm1vZGVsID0gX3RoaXM2LmZvcm0ubW9kZWwuc3BsaXQoIiwiKTtlbHNlIF90aGlzNi5mb3JtLm1vZGVsID0gW107CiAgICAgICAgaWYgKF90aGlzNi5mb3JtLnJlcXVpcmVJbmZvKSBfdGhpczYuZm9ybS5yZXF1aXJlSW5mbyA9IF90aGlzNi5mb3JtLnJlcXVpcmVJbmZvLnNwbGl0KCIsIik7ZWxzZSBfdGhpczYuZm9ybS5yZXF1aXJlSW5mbyA9IFtdOwogICAgICAgIGlmIChfdGhpczYuZm9ybS5pbmZvVHlwZSkgX3RoaXM2LmZvcm0uaW5mb1R5cGUgPSBfdGhpczYuZm9ybS5pbmZvVHlwZS5zcGxpdCgiLCIpO2Vsc2UgX3RoaXM2LmZvcm0uaW5mb1R5cGUgPSBbXTsKICAgICAgICBpZiAoX3RoaXM2LmZvcm0uc3BlYykgX3RoaXM2LmZvcm0uc3BlYyA9IF90aGlzNi5mb3JtLnNwZWMuc3BsaXQoIiwiKTtlbHNlIF90aGlzNi5mb3JtLnNwZWMgPSBbXTsKICAgICAgICBpZiAoX3RoaXM2LmZvcm0uYXV0aENvbXBhbnkpIHsKICAgICAgICAgIHRoYXQuYXV0aENvbXBhbnlzID0gW107CiAgICAgICAgICB2YXIgYXJyYXkgPSBfdGhpczYuZm9ybS5hdXRoQ29tcGFueS5zcGxpdCgiLCIpOwogICAgICAgICAgYXJyYXkuZm9yRWFjaChmdW5jdGlvbiAoZSkgewogICAgICAgICAgICB0aGF0LmF1dGhDb21wYW55cy5wdXNoKHsKICAgICAgICAgICAgICB2YWx1ZTogZSwKICAgICAgICAgICAgICBrZXk6IERhdGUubm93KCkKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9KTsKICAgICAgICAgIHRoYXQuZm9ybS5hdXRoQ29tcGFueSA9IHRoYXQuYXV0aENvbXBhbnlzWzBdLnZhbHVlOwogICAgICAgICAgdGhhdC5hdXRoQ29tcGFueXMuc3BsaWNlKDAsIDEpOwogICAgICAgICAgLy9jb25zb2xlLmxvZyh0aGF0LmF1dGhDb21wYW55cykKICAgICAgICB9IGVsc2UgX3RoaXM2LmF1dGhDb21wYW55cyA9IFtdOwogICAgICAgIF90aGlzNi5vbGRPcGVyYXRpb25UeXBlID0gcmVzcG9uc2UuZGF0YS5vcGVyYXRpb25UeXBlOwogICAgICAgIF90aGlzNi5vcGVuID0gdHJ1ZTsKICAgICAgICBfdGhpczYudGl0bGUgPSAi5L+u5pS56aG555uu5oql5aSHIjsKICAgICAgICB2YXIgcHJvdmluY2VzID0gcmVzcG9uc2UuZGF0YS5wcm92aW5jZTsKICAgICAgICBpZiAocHJvdmluY2VzLmxlbmd0aCA+IDApIHsKICAgICAgICAgIHZhciBhZGRyZXNzID0gcHJvdmluY2VzLnNwbGl0KCIvIik7CiAgICAgICAgICB2YXIgY2l0eXMgPSBbXTsKICAgICAgICAgIC8vIOecgeS7vQogICAgICAgICAgaWYgKGFkZHJlc3MubGVuZ3RoID4gMCkgY2l0eXMucHVzaChUZXh0VG9Db2RlW2FkZHJlc3NbMF1dLmNvZGUpOwogICAgICAgICAgLy8g5Z+O5biCCiAgICAgICAgICBpZiAoYWRkcmVzcy5sZW5ndGggPiAxKSBjaXR5cy5wdXNoKFRleHRUb0NvZGVbYWRkcmVzc1swXV1bYWRkcmVzc1sxXV0uY29kZSk7CiAgICAgICAgICAvLyDlnLDljLoKICAgICAgICAgIGlmIChhZGRyZXNzLmxlbmd0aCA+IDIpIGNpdHlzLnB1c2goVGV4dFRvQ29kZVthZGRyZXNzWzBdXVthZGRyZXNzWzFdXVthZGRyZXNzWzJdXS5jb2RlKTsKICAgICAgICAgIF90aGlzNi5zZWxlY3RlZE9wdGlvbnMgPSBjaXR5czsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDmj5DkuqTmjInpkq4gKi9zdWJtaXRGb3JtOiBmdW5jdGlvbiBzdWJtaXRGb3JtKCkgewogICAgICB2YXIgX3RoaXM3ID0gdGhpczsKICAgICAgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKGZ1bmN0aW9uICh2YWxpZCkgewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgaWYgKF90aGlzNy5mb3JtLmluZm9UeXBlLmluZGV4T2YoIjEiKSA+PSAwICYmIF90aGlzNy5mb3JtLnNjYW5GaWxlKSB7CiAgICAgICAgICAgIHZhciBlbWFpbFJlZyA9IC9eW2EtekEtWjAtOV8tXStAW2EtekEtWjAtOV8tXSsoXC5bYS16QS1aMC05Xy1dKykrJC87CiAgICAgICAgICAgIGlmICghZW1haWxSZWcudGVzdChfdGhpczcuZm9ybS5zY2FuRmlsZSkpIHsKICAgICAgICAgICAgICBfdGhpczcuJG1lc3NhZ2UuZXJyb3IoIui1hOaWmeaOpeaUtuaWueW8j+mCrueuseagvOW8j+mUmeivryIpOwogICAgICAgICAgICAgIHJldHVybjsKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgICAgaWYgKF90aGlzNy5mb3JtLm9wZXJhdGlvblR5cGUgPT0gMiAmJiAhX3RoaXM3LmZvcm0uYXV0aEZpbGUpIHsKICAgICAgICAgICAgX3RoaXM3LiRtZXNzYWdlLmVycm9yKCLmjojmnYPnsbvlnovlv4XpnIDkuIrkvKDmjojmnYPkuaYiKTsKICAgICAgICAgICAgcmV0dXJuOwogICAgICAgICAgfQogICAgICAgICAgaWYgKF90aGlzNy5mb3JtLm9wZXJhdGlvblR5cGUgPT0gMSkgewogICAgICAgICAgICBfdGhpczcuZm9ybS5hdXRoRmlsZSA9ICIiOwogICAgICAgICAgICBfdGhpczcuZm9ybS5hZnRlclNhbGVGaWxlID0gIiI7CiAgICAgICAgICB9CiAgICAgICAgICB2YXIgZm9ybVN0ciA9IEpTT04uc3RyaW5naWZ5KF90aGlzNy5mb3JtKTsKICAgICAgICAgIHZhciBmb3JtRGF0YSA9IEpTT04ucGFyc2UoZm9ybVN0cik7CiAgICAgICAgICBpZiAoZm9ybURhdGEubW9kZWwgJiYgZm9ybURhdGEubW9kZWwubGVuZ3RoID4gMCkgZm9ybURhdGEubW9kZWwgPSBmb3JtRGF0YS5tb2RlbC5qb2luKCIsIik7ZWxzZSBmb3JtRGF0YS5tb2RlbCA9IHVuZGVmaW5lZDsKICAgICAgICAgIGlmIChmb3JtRGF0YS5yZXF1aXJlSW5mbyAmJiBmb3JtRGF0YS5yZXF1aXJlSW5mby5sZW5ndGggPiAwKSBmb3JtRGF0YS5yZXF1aXJlSW5mbyA9IGZvcm1EYXRhLnJlcXVpcmVJbmZvLmpvaW4oIiwiKTtlbHNlIGZvcm1EYXRhLnJlcXVpcmVJbmZvID0gdW5kZWZpbmVkOwogICAgICAgICAgaWYgKGZvcm1EYXRhLmluZm9UeXBlICYmIGZvcm1EYXRhLmluZm9UeXBlLmxlbmd0aCA+IDApIGZvcm1EYXRhLmluZm9UeXBlID0gZm9ybURhdGEuaW5mb1R5cGUuam9pbigiLCIpO2Vsc2UgZm9ybURhdGEuaW5mb1R5cGUgPSB1bmRlZmluZWQ7CiAgICAgICAgICBpZiAoZm9ybURhdGEuc3BlYyAmJiBmb3JtRGF0YS5zcGVjLmxlbmd0aCA+IDApIGZvcm1EYXRhLnNwZWMgPSBmb3JtRGF0YS5zcGVjLmpvaW4oIiwiKTtlbHNlIGZvcm1EYXRhLnNwZWMgPSB1bmRlZmluZWQ7CgogICAgICAgICAgLy/mjojmnYPlhazlj7gKICAgICAgICAgIGlmIChfdGhpczcuYXV0aENvbXBhbnlzLmxlbmd0aCA+IDApIHsKICAgICAgICAgICAgdmFyIGFycmF5ID0gbmV3IEFycmF5KCk7CiAgICAgICAgICAgIF90aGlzNy5hdXRoQ29tcGFueXMuZm9yRWFjaChmdW5jdGlvbiAoZSkgewogICAgICAgICAgICAgIGFycmF5LnB1c2goZS52YWx1ZSk7CiAgICAgICAgICAgIH0pOwogICAgICAgICAgICBmb3JtRGF0YS5hdXRoQ29tcGFueSArPSAiLCIgKyBhcnJheS5qb2luKCIsIik7CiAgICAgICAgICB9CiAgICAgICAgICBpZiAoZm9ybURhdGEucHJvamVjdElkICE9IG51bGwpIHsKICAgICAgICAgICAgdXBkYXRlUmVwb3J0KGZvcm1EYXRhKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgICAgICAgIF90aGlzNy5tc2dTdWNjZXNzKCLkv67mlLnmiJDlip8iKTsKICAgICAgICAgICAgICBpZiAoX3RoaXM3Lm9sZE9wZXJhdGlvblR5cGUgPT0gMSAmJiBmb3JtRGF0YS5vcGVyYXRpb25UeXBlID09IDIpIHsKICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCI9PT09PT4+PuaKpeWkh+aUueaOiOadgyIpOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICBfdGhpczcub3BlbiA9IGZhbHNlOwogICAgICAgICAgICAgIF90aGlzNy5nZXRMaXN0KCk7CiAgICAgICAgICAgIH0pOwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgYWRkUmVwb3J0KGZvcm1EYXRhKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgICAgICAgIF90aGlzNy5tc2dTdWNjZXNzKCLmlrDlop7miJDlip8iKTsKICAgICAgICAgICAgICBfdGhpczcub3BlbiA9IGZhbHNlOwogICAgICAgICAgICAgIF90aGlzNy5nZXRMaXN0KCk7CiAgICAgICAgICAgIH0pOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLyoqIOWIoOmZpOaMiemSruaTjeS9nCAqL2hhbmRsZURlbGV0ZTogZnVuY3Rpb24gaGFuZGxlRGVsZXRlKHJvdykgewogICAgICB2YXIgX3RoaXM4ID0gdGhpczsKICAgICAgdmFyIHByb2plY3RJZHMgPSByb3cucHJvamVjdElkIHx8IHRoaXMuaWRzOwogICAgICB0aGlzLiRjb25maXJtKCfmmK/lkKbnoa7orqTliKDpmaTpobnnm67miqXlpIfnvJblj7fkuLoiJyArIHByb2plY3RJZHMgKyAnIueahOaVsOaNrumhuT8nLCAi6K2m5ZGKIiwgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAi56Gu5a6aIiwKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAi5Y+W5raIIiwKICAgICAgICB0eXBlOiAid2FybmluZyIKICAgICAgfSkudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgcmV0dXJuIGRlbFJlcG9ydChwcm9qZWN0SWRzKTsKICAgICAgfSkudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgX3RoaXM4LmdldExpc3QoKTsKICAgICAgICBfdGhpczgubXNnU3VjY2Vzcygi5Yig6Zmk5oiQ5YqfIik7CiAgICAgIH0pOwogICAgfSwKICAgIGNsaWNrRXhwb3J0OiBmdW5jdGlvbiBjbGlja0V4cG9ydCgpIHsKICAgICAgdGhpcy5zaG93RXhwb3J0ID0gdHJ1ZTsKICAgIH0sCiAgICBjbGlja1ByaW50OiBmdW5jdGlvbiBjbGlja1ByaW50KCkgewogICAgICB0aGlzLnNob3dQcmludCA9IHRydWU7CiAgICB9LAogICAgLyoqIOWvvOWHuuaMiemSruaTjeS9nCAqL2hhbmRsZUV4cG9ydDogZnVuY3Rpb24gaGFuZGxlRXhwb3J0KHR5cGUpIHsKICAgICAgdmFyIF90aGlzOSA9IHRoaXM7CiAgICAgIHZhciBsb2FkaW5nd2luOwogICAgICB2YXIgdGhhdCA9IHRoaXM7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMuc3BhcmUxID0gdHlwZTsKICAgICAgdmFyIGNvbCA9IFtdOwogICAgICB0aGlzLmNvbHVtbnMuZm9yRWFjaChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgIGlmIChpdGVtLnZpc2libGUpIHsKICAgICAgICAgIGNvbC5wdXNoKGl0ZW0ubGFiZWwpOwogICAgICAgIH0KICAgICAgfSk7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMuc3BhcmUyID0gY29sLmpvaW4oIiwiKTsKICAgICAgdmFyIHF1ZXJ5UGFyYW1zID0gdGhpcy5xdWVyeVBhcmFtczsKICAgICAgdGhpcy4kY29uZmlybSgi5piv5ZCm56Gu6K6k5a+85Ye66aG555uu5oql5aSH5pCc57Si57uT5p6cIiArICh0eXBlID09IDAgPyAi5pys6aG1IiA6ICLlhajpg6giKSArICLmlbDmja7pobk/IiwgIuitpuWRiiIsIHsKICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogIuehruWumiIsCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogIuWPlua2iCIsCiAgICAgICAgdHlwZTogIndhcm5pbmciCiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgIGxvYWRpbmd3aW4gPSB0aGF0LiRsb2FkaW5nKHsKICAgICAgICAgIGxvY2s6IHRydWUsCiAgICAgICAgICAvL2xvY2vnmoTkv67mlLnnrKYtLem7mOiupOaYr2ZhbHNlCiAgICAgICAgICB0ZXh0OiAi5a+85Ye65LitLi4uIiwKICAgICAgICAgIC8v5pi+56S65Zyo5Yqg6L295Zu+5qCH5LiL5pa555qE5Yqg6L295paH5qGICiAgICAgICAgICBzcGlubmVyOiAiZWwtaWNvbi1sb2FkaW5nIiwKICAgICAgICAgIC8v6Ieq5a6a5LmJ5Yqg6L295Zu+5qCH57G75ZCNCiAgICAgICAgICBiYWNrZ3JvdW5kOiAicmdiYSgwLCAwLCAwLCAwLjcpIiwKICAgICAgICAgIC8v6YGu572p5bGC6aKc6ImyCiAgICAgICAgICB0YXJnZXQ6IGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3IoIi5hcHAtd3JhcHBlciIpIC8vbG9hZGlu6KaG55uW55qEZG9t5YWD57Sg6IqC54K5CiAgICAgICAgfSk7CiAgICAgICAgZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50LnN0eWxlLm92ZXJmbG93WSA9ICJoaWRkZW4iOyAvL+emgeatouW6leWxgmRpdua7muWKqAogICAgICAgIHJldHVybiBleHBvcnRSZXBvcnQocXVlcnlQYXJhbXMpOwogICAgICB9KS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgIF90aGlzOS5kb3dubG9hZChyZXNwb25zZS5tc2cpOwogICAgICAgIF90aGlzOS5zaG93RXhwb3J0ID0gZmFsc2U7CiAgICAgICAgbG9hZGluZ3dpbi5jbG9zZSgpOwogICAgICAgIGRvY3VtZW50LmRvY3VtZW50RWxlbWVudC5zdHlsZS5vdmVyZmxvd1kgPSAiYXV0byI7IC8v5YWB6K645bqV5bGCZGl25rua5YqoCiAgICAgIH0pLmNhdGNoKGZ1bmN0aW9uICgpIHsKICAgICAgICBfdGhpczkuc2hvd0V4cG9ydCA9IGZhbHNlOwogICAgICAgIGxvYWRpbmd3aW4uY2xvc2UoKTsKICAgICAgICBkb2N1bWVudC5kb2N1bWVudEVsZW1lbnQuc3R5bGUub3ZlcmZsb3dZID0gImF1dG8iOyAvL+WFgeiuuOW6leWxgmRpdua7muWKqAogICAgICB9KTsKICAgIH0sCiAgICAvKiog5a+85YWl5oyJ6ZKu5pON5L2cICovaGFuZGxlSW1wb3J0OiBmdW5jdGlvbiBoYW5kbGVJbXBvcnQoKSB7CiAgICAgIHRoaXMudXBsb2FkLnRpdGxlID0gIumhueebruWvvOWFpSI7CiAgICAgIHRoaXMudXBsb2FkLm9wZW4gPSB0cnVlOwogICAgfSwKICAgIC8qKiDkuIvovb3mqKHmnb/mk43kvZwgKi9pbXBvcnRUZW1wbGF0ZTogZnVuY3Rpb24gaW1wb3J0VGVtcGxhdGUoKSB7CiAgICAgIHZhciBfdGhpczEwID0gdGhpczsKICAgICAgX2ltcG9ydFRlbXBsYXRlKCkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICBfdGhpczEwLmRvd25sb2FkKHJlc3BvbnNlLm1zZyk7CiAgICAgIH0pOwogICAgfSwKICAgIGRvd25sb2FkU1FTOiBmdW5jdGlvbiBkb3dubG9hZFNRUygpIHsKICAgICAgdGhpcy5kb3dubG9hZCgi5rW35L2z6ZuG5ZuiLeaOiOadg+S5pi5kb2N4IiwgZmFsc2UpOwogICAgfSwKICAgIGRvd25sb2FkQ1JIOiBmdW5jdGlvbiBkb3dubG9hZENSSCgpIHsKICAgICAgdGhpcy5kb3dubG9hZCgi5rW35L2z6ZuG5ZuiLeWUruWQjuacjeWKoeaJv+ivuuWHvS5kb2MiLCBmYWxzZSk7CiAgICB9LAogICAgLy8g5paH5Lu25LiK5Lyg5Lit5aSE55CGCiAgICBoYW5kbGVGaWxlVXBsb2FkUHJvZ3Jlc3M6IGZ1bmN0aW9uIGhhbmRsZUZpbGVVcGxvYWRQcm9ncmVzcyhldmVudCwgZmlsZSwgZmlsZUxpc3QpIHsKICAgICAgdGhpcy51cGxvYWQuaXNVcGxvYWRpbmcgPSB0cnVlOwogICAgfSwKICAgIC8vIOaWh+S7tuS4iuS8oOaIkOWKn+WkhOeQhgogICAgaGFuZGxlRmlsZVN1Y2Nlc3M6IGZ1bmN0aW9uIGhhbmRsZUZpbGVTdWNjZXNzKHJlc3BvbnNlLCBmaWxlLCBmaWxlTGlzdCkgewogICAgICB0aGlzLnVwbG9hZC5vcGVuID0gZmFsc2U7CiAgICAgIHRoaXMudXBsb2FkLmlzVXBsb2FkaW5nID0gZmFsc2U7CiAgICAgIHRoaXMuJHJlZnMudXBsb2FkLmNsZWFyRmlsZXMoKTsKICAgICAgdGhpcy4kYWxlcnQocmVzcG9uc2UubXNnLCAi5a+85YWl57uT5p6cIiwgewogICAgICAgIGRhbmdlcm91c2x5VXNlSFRNTFN0cmluZzogdHJ1ZQogICAgICB9KTsKICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICB9LAogICAgLy8g5o+Q5Lqk5LiK5Lyg5paH5Lu2CiAgICBzdWJtaXRGaWxlRm9ybTogZnVuY3Rpb24gc3VibWl0RmlsZUZvcm0oKSB7CiAgICAgIHRoaXMuJHJlZnMudXBsb2FkLnN1Ym1pdCgpOwogICAgfSwKICAgIGhhbmRsZUNoYW5nZTogZnVuY3Rpb24gaGFuZGxlQ2hhbmdlKHZhbHVlKSB7CiAgICAgIGlmICghdmFsdWUgfHwgdmFsdWUubGVuZ3RoID09IDApIHsKICAgICAgICB0aGlzLnNlbGVjdGVkT3B0aW9ucyA9IG51bGw7CiAgICAgICAgdGhpcy5mb3JtLnByb3ZpbmNlID0gdW5kZWZpbmVkOwogICAgICAgIHRoaXMuZm9ybS5kaXN0cmljdCA9IHVuZGVmaW5lZDsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgdGhpcy5zZWxlY3RlZE9wdGlvbnMgPSB2YWx1ZTsKICAgICAgdmFyIHR4dCA9ICIiOwogICAgICB2YWx1ZS5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgdHh0ICs9IENvZGVUb1RleHRbaXRlbV0gKyAiLyI7CiAgICAgIH0pOwogICAgICBpZiAodHh0Lmxlbmd0aCA+IDEpIHsKICAgICAgICB0eHQgPSB0eHQuc3Vic3RyaW5nKDAsIHR4dC5sZW5ndGggLSAxKTsKICAgICAgICB0aGlzLmZvcm0uZGlzdHJpY3QgPSB0aGlzLiRzdG9yZS5zdGF0ZS51c2VyLnByb3ZpbmNlOwogICAgICAgIHRoaXMuZm9ybS5wcm92aW5jZSA9IHR4dDsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLmZvcm0ucHJvdmluY2UgPSB1bmRlZmluZWQ7CiAgICAgICAgdGhpcy5mb3JtLmRpc3RyaWN0ID0gdW5kZWZpbmVkOwogICAgICB9CiAgICB9LAogICAgaGFuZGxlUXVlcnlDaXR5Q2hhbmdlOiBmdW5jdGlvbiBoYW5kbGVRdWVyeUNpdHlDaGFuZ2UodmFsdWUpIHsKICAgICAgdGhpcy5xdWVyeUFyZWEgPSB2YWx1ZTsKICAgICAgdmFyIHR4dCA9ICIiOwogICAgICB2YWx1ZS5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgdHh0ICs9IENvZGVUb1RleHRbaXRlbV0gKyAiLyI7CiAgICAgIH0pOwogICAgICBpZiAodHh0Lmxlbmd0aCA+IDEpIHsKICAgICAgICB0eHQgPSB0eHQuc3Vic3RyaW5nKDAsIHR4dC5sZW5ndGggLSAxKTsKICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnByb3ZpbmNlID0gdHh0OwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMucXVlcnlQYXJhbXMucHJvdmluY2UgPSB1bmRlZmluZWQ7CiAgICAgIH0KICAgIH0sCiAgICBoYW5kbGVQcmludDogZnVuY3Rpb24gaGFuZGxlUHJpbnQodHlwZSkgewogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnNwYXJlMSA9IHR5cGU7CiAgICAgIHZhciBwcm9wZXJ0aWVzID0gW107CiAgICAgIC8vcHJvcGVydGllcy5wdXNoKHtmaWVsZDogJ2luZGV4JywgZGlzcGxheU5hbWU6ICfluo/lj7cnfSk7CiAgICAgIHByb3BlcnRpZXMucHVzaCh7CiAgICAgICAgZmllbGQ6ICJwcm9qZWN0SWQiLAogICAgICAgIGRpc3BsYXlOYW1lOiAi6aG555uuSUQiCiAgICAgIH0pOwogICAgICB0aGlzLmNvbHVtbnMuZm9yRWFjaChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgIGlmIChpdGVtLnZpc2libGUpIHsKICAgICAgICAgIHByb3BlcnRpZXMucHVzaCh7CiAgICAgICAgICAgIGZpZWxkOiBpdGVtLmtleSwKICAgICAgICAgICAgZGlzcGxheU5hbWU6IGl0ZW0ubGFiZWwKICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgfSk7CiAgICAgIHByaW50UmVwb3J0KHRoaXMucXVlcnlQYXJhbXMpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgcHJpbnRKUyh7CiAgICAgICAgICBwcmludGFibGU6IHJlc3BvbnNlLmRhdGEsCiAgICAgICAgICB0eXBlOiAianNvbiIsCiAgICAgICAgICBwcm9wZXJ0aWVzOiBwcm9wZXJ0aWVzLAogICAgICAgICAgaGVhZGVyOiAnPGRpdiBzdHlsZT0idGV4dC1hbGlnbjogY2VudGVyIj48aDM+6aG555uu5oql5aSH5YiX6KGoPC9oMz48L2Rpdj4nLAogICAgICAgICAgdGFyZ2V0U3R5bGVzOiBbIioiXSwKICAgICAgICAgIGdyaWRIZWFkZXJTdHlsZTogIm1hcmdpbi10b3A6MjBweDtib3JkZXI6IDFweCBzb2xpZCAjMDAwO3RleHQtYWxpZ246Y2VudGVyIiwKICAgICAgICAgIGdyaWRTdHlsZTogImJvcmRlcjogMXB4IHNvbGlkICMwMDA7dGV4dC1hbGlnbjpjZW50ZXI7bWluLXdpZHRoOjUwcHg7IiwKICAgICAgICAgIHN0eWxlOiAiQHBhZ2Uge21hcmdpbjowIDEwbW07bWFyZ2luLXRvcDoxMG1tO30iCiAgICAgICAgfSk7CiAgICAgIH0pOwogICAgICAvLyBwcmludEpTKHsKICAgICAgLy8gICBwcmludGFibGU6ICJwcmludEFyZWEiLAogICAgICAvLyAgIHR5cGU6J2h0bWwnLAogICAgICAvLyAgIGhlYWRlcjpudWxsLAogICAgICAvLyAgIHRhcmdldFN0eWxlczpbJyonXSwKICAgICAgLy8gICBzdHlsZToiQHBhZ2Uge21hcmdpbjowIDEwbW19IgogICAgICAvLyB9KQogICAgfSwKICAgIC8vIOWIoOmZpCBzaG93TmFtZUNvcmxvciDlkowgc2hvd05vQ29ybG9yIOaWueazle+8jOaWsOWinumrmOS6ruaWueazlQogICAgaGlnaGxpZ2h0VGV4dDogZnVuY3Rpb24gaGlnaGxpZ2h0VGV4dCh0ZXh0LCBrZXl3b3JkKSB7CiAgICAgIGlmICgha2V5d29yZCkgcmV0dXJuIHRleHQ7CiAgICAgIC8vIOWFqOmDqOmrmOS6rgogICAgICByZXR1cm4gdGV4dCA/IHRleHQucmVwbGFjZShuZXcgUmVnRXhwKGtleXdvcmQsICdnJyksICI8Zm9udCBjb2xvcj1cIiNmMDBcIj4iLmNvbmNhdChrZXl3b3JkLCAiPC9mb250PiIpKSA6IHRleHQ7CiAgICB9LAogICAgaGlnaGxpZ2h0Q2VsbDogZnVuY3Rpb24gaGlnaGxpZ2h0Q2VsbChmaWVsZCwgdGV4dCkgewogICAgICBpZiAodGhpcy5zZWFyY2hGaWVsZCA9PT0gJ2FsbCcgJiYgdGhpcy5zZWFyY2hWYWx1ZSAmJiB0aGlzLmhpZ2hsaWdodEZpZWxkcy5pbmNsdWRlcyhmaWVsZCkpIHsKICAgICAgICByZXR1cm4gdGhpcy5oaWdobGlnaHRUZXh0KHRleHQsIHRoaXMuc2VhcmNoVmFsdWUpOwogICAgICB9CiAgICAgIGlmICh0aGlzLnNlYXJjaEZpZWxkID09PSBmaWVsZCAmJiB0aGlzLnNlYXJjaFZhbHVlKSB7CiAgICAgICAgcmV0dXJuIHRoaXMuaGlnaGxpZ2h0VGV4dCh0ZXh0LCB0aGlzLnNlYXJjaFZhbHVlKTsKICAgICAgfQogICAgICByZXR1cm4gdGV4dDsKICAgIH0sCiAgICByZW1vdmVEb21haW46IGZ1bmN0aW9uIHJlbW92ZURvbWFpbihpbmRleCkgewogICAgICBpZiAoaW5kZXggIT09IC0xKSB7CiAgICAgICAgdGhpcy5hdXRoQ29tcGFueXMuc3BsaWNlKGluZGV4LCAxKTsKICAgICAgfQogICAgfSwKICAgIGFkZERvbWFpbjogZnVuY3Rpb24gYWRkRG9tYWluKCkgewogICAgICB0aGlzLmF1dGhDb21wYW55cy5wdXNoKHsKICAgICAgICB2YWx1ZTogIiIsCiAgICAgICAga2V5OiBEYXRlLm5vdygpCiAgICAgIH0pOwogICAgfSwKICAgIHVzZXJTZWFyY2g6IGZ1bmN0aW9uIHVzZXJTZWFyY2goY3JlYXRlQnkpIHsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5iZWxvbmdVc2VyID0gY3JlYXRlQnk7CiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsKICAgIH0sCiAgICBvcHRUeXBlU2VhcmNoOiBmdW5jdGlvbiBvcHRUeXBlU2VhcmNoKHR5cGUpIHsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5vcGVyYXRpb25UeXBlID0gdHlwZTsKICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOwogICAgfSwKICAgIG9wdFR5cGVDaGFuZ2U6IGZ1bmN0aW9uIG9wdFR5cGVDaGFuZ2UoZSkgewogICAgICBjb25zb2xlLmxvZyhlKTsKICAgIH0KICB9Cn07"}, null]}