{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/components/render/slots/el-button.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/components/render/slots/el-button.js", "mtime": 1641277326000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:ZXhwb3J0IGRlZmF1bHQgewogIGRlZmF1bHQ6IGZ1bmN0aW9uIF9kZWZhdWx0KGgsIGNvbmYsIGtleSkgewogICAgcmV0dXJuIGNvbmYuX19zbG90X19ba2V5XTsKICB9Cn07"}, {"version": 3, "names": ["default", "_default", "h", "conf", "key", "__slot__"], "sources": ["/Users/<USER>/develop/code/project/haijia/adminweb/src/components/render/slots/el-button.js"], "sourcesContent": ["export default {\n  default(h, conf, key) {\n    return conf.__slot__[key]\n  }\n}\n"], "mappings": "AAAA,eAAe;EACbA,OAAO,WAAAC,SAACC,CAAC,EAAEC,IAAI,EAAEC,GAAG,EAAE;IACpB,OAAOD,IAAI,CAACE,QAAQ,CAACD,GAAG,CAAC;EAC3B;AACF,CAAC", "ignoreList": []}]}