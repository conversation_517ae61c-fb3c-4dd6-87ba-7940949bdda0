{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/layout/components/Sidebar/FixiOSBug.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/layout/components/Sidebar/FixiOSBug.js", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:ZXhwb3J0IGRlZmF1bHQgewogIGNvbXB1dGVkOiB7CiAgICBkZXZpY2U6IGZ1bmN0aW9uIGRldmljZSgpIHsKICAgICAgcmV0dXJuIHRoaXMuJHN0b3JlLnN0YXRlLmFwcC5kZXZpY2U7CiAgICB9CiAgfSwKICBtb3VudGVkOiBmdW5jdGlvbiBtb3VudGVkKCkgewogICAgLy8gSW4gb3JkZXIgdG8gZml4IHRoZSBjbGljayBvbiBtZW51IG9uIHRoZSBpb3MgZGV2aWNlIHdpbGwgdHJpZ2dlciB0aGUgbW91c2VsZWF2ZSBidWcKICAgIHRoaXMuZml4QnVnSW5pT1MoKTsKICB9LAogIG1ldGhvZHM6IHsKICAgIGZpeEJ1Z0luaU9TOiBmdW5jdGlvbiBmaXhCdWdJbmlPUygpIHsKICAgICAgdmFyIF90aGlzID0gdGhpczsKICAgICAgdmFyICRzdWJNZW51ID0gdGhpcy4kcmVmcy5zdWJNZW51OwogICAgICBpZiAoJHN1Yk1lbnUpIHsKICAgICAgICB2YXIgaGFuZGxlTW91c2VsZWF2ZSA9ICRzdWJNZW51LmhhbmRsZU1vdXNlbGVhdmU7CiAgICAgICAgJHN1Yk1lbnUuaGFuZGxlTW91c2VsZWF2ZSA9IGZ1bmN0aW9uIChlKSB7CiAgICAgICAgICBpZiAoX3RoaXMuZGV2aWNlID09PSAnbW9iaWxlJykgewogICAgICAgICAgICByZXR1cm47CiAgICAgICAgICB9CiAgICAgICAgICBoYW5kbGVNb3VzZWxlYXZlKGUpOwogICAgICAgIH07CiAgICAgIH0KICAgIH0KICB9Cn07"}, {"version": 3, "names": ["computed", "device", "$store", "state", "app", "mounted", "fixBugIniOS", "methods", "_this", "$subMenu", "$refs", "subMenu", "handleMouseleave", "e"], "sources": ["/Users/<USER>/develop/code/project/haijia/adminweb/src/layout/components/Sidebar/FixiOSBug.js"], "sourcesContent": ["export default {\n  computed: {\n    device() {\n      return this.$store.state.app.device\n    }\n  },\n  mounted() {\n    // In order to fix the click on menu on the ios device will trigger the mouseleave bug\n    this.fixBugIniOS()\n  },\n  methods: {\n    fixBugIniOS() {\n      const $subMenu = this.$refs.subMenu\n      if ($subMenu) {\n        const handleMouseleave = $subMenu.handleMouseleave\n        $subMenu.handleMouseleave = (e) => {\n          if (this.device === 'mobile') {\n            return\n          }\n          handleMouseleave(e)\n        }\n      }\n    }\n  }\n}\n"], "mappings": "AAAA,eAAe;EACbA,QAAQ,EAAE;IACRC,MAAM,WAAAA,OAAA,EAAG;MACP,OAAO,IAAI,CAACC,MAAM,CAACC,KAAK,CAACC,GAAG,CAACH,MAAM;IACrC;EACF,CAAC;EACDI,OAAO,WAAAA,QAAA,EAAG;IACR;IACA,IAAI,CAACC,WAAW,CAAC,CAAC;EACpB,CAAC;EACDC,OAAO,EAAE;IACPD,WAAW,WAAAA,YAAA,EAAG;MAAA,IAAAE,KAAA;MACZ,IAAMC,QAAQ,GAAG,IAAI,CAACC,KAAK,CAACC,OAAO;MACnC,IAAIF,QAAQ,EAAE;QACZ,IAAMG,gBAAgB,GAAGH,QAAQ,CAACG,gBAAgB;QAClDH,QAAQ,CAACG,gBAAgB,GAAG,UAACC,CAAC,EAAK;UACjC,IAAIL,KAAI,CAACP,MAAM,KAAK,QAAQ,EAAE;YAC5B;UACF;UACAW,gBAAgB,CAACC,CAAC,CAAC;QACrB,CAAC;MACH;IACF;EACF;AACF,CAAC", "ignoreList": []}]}