{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/api/project/data.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/api/project/data.js", "mtime": 1717330512763}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QgZnJvbSAiQC91dGlscy9yZXF1ZXN0IjsKCi8vIOafpeivouWFrOWRiuWIl+ihqApleHBvcnQgZnVuY3Rpb24gbGlzdFByb2plY3REYXRhKHF1ZXJ5KSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAiL3Byb2plY3QvZGF0YS9saXN0IiwKICAgIG1ldGhvZDogImdldCIsCiAgICBwYXJhbXM6IHF1ZXJ5CiAgfSk7Cn0KCi8vIOafpeivouWFrOWRiuivpue7hgpleHBvcnQgZnVuY3Rpb24gZ2V0UHJvamVjdERhdGEobm90aWNlSWQpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICIvcHJvamVjdC9kYXRhLyIgKyBub3RpY2VJZCwKICAgIG1ldGhvZDogImdldCIKICB9KTsKfQoKLy8g5paw5aKe5YWs5ZGKCmV4cG9ydCBmdW5jdGlvbiBhZGRQcm9qZWN0RGF0YShkYXRhKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAiL3Byb2plY3QvZGF0YSIsCiAgICBtZXRob2Q6ICJwb3N0IiwKICAgIGRhdGE6IGRhdGEKICB9KTsKfQoKLy8g5L+u5pS55YWs5ZGKCmV4cG9ydCBmdW5jdGlvbiB1cGRhdGVQcm9qZWN0RGF0YShkYXRhKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAiL3Byb2plY3QvZGF0YSIsCiAgICBtZXRob2Q6ICJwdXQiLAogICAgZGF0YTogZGF0YQogIH0pOwp9CgovLyDliKDpmaTlhazlkYoKZXhwb3J0IGZ1bmN0aW9uIGRlbFByb2plY3REYXRhKG5vdGljZUlkKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAiL3Byb2plY3QvZGF0YS8iICsgbm90aWNlSWQsCiAgICBtZXRob2Q6ICJkZWxldGUiCiAgfSk7Cn0KZXhwb3J0IGZ1bmN0aW9uIHJlYWRQcm9qZWN0RGF0YShub3RpY2VJZCkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogIi9wcm9qZWN0L2RhdGEvcmVhZC8iICsgbm90aWNlSWQsCiAgICBtZXRob2Q6ICJwdXQiCiAgfSk7Cn0KZXhwb3J0IGZ1bmN0aW9uIG5Qcm9qZWN0RGF0YSgpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICIvcHJvamVjdC9kYXRhL2NvdW50IiwKICAgIG1ldGhvZDogImdldCIKICB9KTsKfQ=="}, {"version": 3, "names": ["request", "listProjectData", "query", "url", "method", "params", "getProjectData", "noticeId", "addProjectData", "data", "updateProjectData", "delProjectData", "readProjectData", "nProjectData"], "sources": ["/Users/<USER>/develop/code/project/haijia/adminweb/src/api/project/data.js"], "sourcesContent": ["import request from \"@/utils/request\";\n\n// 查询公告列表\nexport function listProjectData(query) {\n  return request({\n    url: \"/project/data/list\",\n    method: \"get\",\n    params: query,\n  });\n}\n\n// 查询公告详细\nexport function getProjectData(noticeId) {\n  return request({\n    url: \"/project/data/\" + noticeId,\n    method: \"get\",\n  });\n}\n\n// 新增公告\nexport function addProjectData(data) {\n  return request({\n    url: \"/project/data\",\n    method: \"post\",\n    data: data,\n  });\n}\n\n// 修改公告\nexport function updateProjectData(data) {\n  return request({\n    url: \"/project/data\",\n    method: \"put\",\n    data: data,\n  });\n}\n\n// 删除公告\nexport function delProjectData(noticeId) {\n  return request({\n    url: \"/project/data/\" + noticeId,\n    method: \"delete\",\n  });\n}\n\nexport function readProjectData(noticeId) {\n  return request({\n    url: \"/project/data/read/\" + noticeId,\n    method: \"put\",\n  });\n}\n\nexport function nProjectData() {\n  return request({\n    url: \"/project/data/count\",\n    method: \"get\",\n  });\n}\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;;AAErC;AACA,OAAO,SAASC,eAAeA,CAACC,KAAK,EAAE;EACrC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,oBAAoB;IACzBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEH;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASI,cAAcA,CAACC,QAAQ,EAAE;EACvC,OAAOP,OAAO,CAAC;IACbG,GAAG,EAAE,gBAAgB,GAAGI,QAAQ;IAChCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASI,cAAcA,CAACC,IAAI,EAAE;EACnC,OAAOT,OAAO,CAAC;IACbG,GAAG,EAAE,eAAe;IACpBC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASC,iBAAiBA,CAACD,IAAI,EAAE;EACtC,OAAOT,OAAO,CAAC;IACbG,GAAG,EAAE,eAAe;IACpBC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASE,cAAcA,CAACJ,QAAQ,EAAE;EACvC,OAAOP,OAAO,CAAC;IACbG,GAAG,EAAE,gBAAgB,GAAGI,QAAQ;IAChCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;AAEA,OAAO,SAASQ,eAAeA,CAACL,QAAQ,EAAE;EACxC,OAAOP,OAAO,CAAC;IACbG,GAAG,EAAE,qBAAqB,GAAGI,QAAQ;IACrCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;AAEA,OAAO,SAASS,YAAYA,CAAA,EAAG;EAC7B,OAAOb,OAAO,CAAC;IACbG,GAAG,EAAE,qBAAqB;IAC1BC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}