{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/utils/zipdownload.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/utils/zipdownload.js", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IGF4aW9zIGZyb20gJ2F4aW9zJzsKaW1wb3J0IHsgZ2V0VG9rZW4gfSBmcm9tICdAL3V0aWxzL2F1dGgnOwp2YXIgbWltZU1hcCA9IHsKICB4bHN4OiAnYXBwbGljYXRpb24vdm5kLm9wZW54bWxmb3JtYXRzLW9mZmljZWRvY3VtZW50LnNwcmVhZHNoZWV0bWwuc2hlZXQnLAogIHppcDogJ2FwcGxpY2F0aW9uL3ppcCcKfTsKdmFyIGJhc2VVcmwgPSBwcm9jZXNzLmVudi5WVUVfQVBQX0JBU0VfQVBJOwpleHBvcnQgZnVuY3Rpb24gZG93bkxvYWRaaXAoc3RyLCBmaWxlbmFtZSkgewogIHZhciB1cmwgPSBiYXNlVXJsICsgc3RyOwogIGF4aW9zKHsKICAgIG1ldGhvZDogJ2dldCcsCiAgICB1cmw6IHVybCwKICAgIHJlc3BvbnNlVHlwZTogJ2Jsb2InLAogICAgaGVhZGVyczogewogICAgICAnQXV0aG9yaXphdGlvbic6ICdCZWFyZXIgJyArIGdldFRva2VuKCkKICAgIH0KICB9KS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgIHJlc29sdmVCbG9iKHJlcywgbWltZU1hcC56aXApOwogIH0pOwp9Ci8qKgogKiDop6PmnpBibG9i5ZON5bqU5YaF5a655bm25LiL6L29CiAqIEBwYXJhbSB7Kn0gcmVzIGJsb2Llk43lupTlhoXlrrkKICogQHBhcmFtIHtTdHJpbmd9IG1pbWVUeXBlIE1JTUXnsbvlnosKICovCmV4cG9ydCBmdW5jdGlvbiByZXNvbHZlQmxvYihyZXMsIG1pbWVUeXBlKSB7CiAgdmFyIGFMaW5rID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnYScpOwogIHZhciBibG9iID0gbmV3IEJsb2IoW3Jlcy5kYXRhXSwgewogICAgdHlwZTogbWltZVR5cGUKICB9KTsKICAvLyAvL+S7jnJlc3BvbnNl55qEaGVhZGVyc+S4reiOt+WPlmZpbGVuYW1lLCDlkI7nq69yZXNwb25zZS5zZXRIZWFkZXIoIkNvbnRlbnQtZGlzcG9zaXRpb24iLCAiYXR0YWNobWVudDsgZmlsZW5hbWU9eHh4eC5kb2N4Iikg6K6+572u55qE5paH5Lu25ZCNOwogIHZhciBwYXR0ID0gbmV3IFJlZ0V4cCgnZmlsZW5hbWU9KFteO10rXFwuW15cXC47XSspOyonKTsKICB2YXIgY29udGVudERpc3Bvc2l0aW9uID0gZGVjb2RlVVJJKHJlcy5oZWFkZXJzWydjb250ZW50LWRpc3Bvc2l0aW9uJ10pOwogIHZhciByZXN1bHQgPSBwYXR0LmV4ZWMoY29udGVudERpc3Bvc2l0aW9uKTsKICB2YXIgZmlsZU5hbWUgPSByZXN1bHRbMV07CiAgZmlsZU5hbWUgPSBmaWxlTmFtZS5yZXBsYWNlKC9cIi9nLCAnJyk7CiAgYUxpbmsuaHJlZiA9IFVSTC5jcmVhdGVPYmplY3RVUkwoYmxvYik7CiAgYUxpbmsuc2V0QXR0cmlidXRlKCdkb3dubG9hZCcsIGZpbGVOYW1lKTsgLy8g6K6+572u5LiL6L295paH5Lu25ZCN56ewCiAgZG9jdW1lbnQuYm9keS5hcHBlbmRDaGlsZChhTGluayk7CiAgYUxpbmsuY2xpY2soKTsKICBkb2N1bWVudC5ib2R5LmFwcGVuZENoaWxkKGFMaW5rKTsKfQ=="}, {"version": 3, "names": ["axios", "getToken", "mimeMap", "xlsx", "zip", "baseUrl", "process", "env", "VUE_APP_BASE_API", "downLoadZip", "str", "filename", "url", "method", "responseType", "headers", "then", "res", "resolveBlob", "mimeType", "aLink", "document", "createElement", "blob", "Blob", "data", "type", "patt", "RegExp", "contentDisposition", "decodeURI", "result", "exec", "fileName", "replace", "href", "URL", "createObjectURL", "setAttribute", "body", "append<PERSON><PERSON><PERSON>", "click"], "sources": ["/Users/<USER>/develop/code/project/haijia/adminweb/src/utils/zipdownload.js"], "sourcesContent": ["import axios from 'axios'\nimport { getToken } from '@/utils/auth'\n\nconst mimeMap = {\n  xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',\n  zip: 'application/zip'\n}\n\nconst baseUrl = process.env.VUE_APP_BASE_API\nexport function downLoadZip(str, filename) {\n  var url = baseUrl + str\n  axios({\n    method: 'get',\n    url: url,\n    responseType: 'blob',\n    headers: { 'Authorization': 'Bearer ' + getToken() }\n  }).then(res => {\n    resolveBlob(res, mimeMap.zip)\n  })\n}\n/**\n * 解析blob响应内容并下载\n * @param {*} res blob响应内容\n * @param {String} mimeType MIME类型\n */\nexport function resolveBlob(res, mimeType) {\n  const aLink = document.createElement('a')\n  var blob = new Blob([res.data], { type: mimeType })\n  // //从response的headers中获取filename, 后端response.setHeader(\"Content-disposition\", \"attachment; filename=xxxx.docx\") 设置的文件名;\n  var patt = new RegExp('filename=([^;]+\\\\.[^\\\\.;]+);*')\n  var contentDisposition = decodeURI(res.headers['content-disposition'])\n  var result = patt.exec(contentDisposition)\n  var fileName = result[1]\n  fileName = fileName.replace(/\\\"/g, '')\n  aLink.href = URL.createObjectURL(blob)\n  aLink.setAttribute('download', fileName) // 设置下载文件名称\n  document.body.appendChild(aLink)\n  aLink.click()\n  document.body.appendChild(aLink)\n}\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,QAAQ,cAAc;AAEvC,IAAMC,OAAO,GAAG;EACdC,IAAI,EAAE,mEAAmE;EACzEC,GAAG,EAAE;AACP,CAAC;AAED,IAAMC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,gBAAgB;AAC5C,OAAO,SAASC,WAAWA,CAACC,GAAG,EAAEC,QAAQ,EAAE;EACzC,IAAIC,GAAG,GAAGP,OAAO,GAAGK,GAAG;EACvBV,KAAK,CAAC;IACJa,MAAM,EAAE,KAAK;IACbD,GAAG,EAAEA,GAAG;IACRE,YAAY,EAAE,MAAM;IACpBC,OAAO,EAAE;MAAE,eAAe,EAAE,SAAS,GAAGd,QAAQ,CAAC;IAAE;EACrD,CAAC,CAAC,CAACe,IAAI,CAAC,UAAAC,GAAG,EAAI;IACbC,WAAW,CAACD,GAAG,EAAEf,OAAO,CAACE,GAAG,CAAC;EAC/B,CAAC,CAAC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASc,WAAWA,CAACD,GAAG,EAAEE,QAAQ,EAAE;EACzC,IAAMC,KAAK,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;EACzC,IAAIC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACP,GAAG,CAACQ,IAAI,CAAC,EAAE;IAAEC,IAAI,EAAEP;EAAS,CAAC,CAAC;EACnD;EACA,IAAIQ,IAAI,GAAG,IAAIC,MAAM,CAAC,+BAA+B,CAAC;EACtD,IAAIC,kBAAkB,GAAGC,SAAS,CAACb,GAAG,CAACF,OAAO,CAAC,qBAAqB,CAAC,CAAC;EACtE,IAAIgB,MAAM,GAAGJ,IAAI,CAACK,IAAI,CAACH,kBAAkB,CAAC;EAC1C,IAAII,QAAQ,GAAGF,MAAM,CAAC,CAAC,CAAC;EACxBE,QAAQ,GAAGA,QAAQ,CAACC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;EACtCd,KAAK,CAACe,IAAI,GAAGC,GAAG,CAACC,eAAe,CAACd,IAAI,CAAC;EACtCH,KAAK,CAACkB,YAAY,CAAC,UAAU,EAAEL,QAAQ,CAAC,EAAC;EACzCZ,QAAQ,CAACkB,IAAI,CAACC,WAAW,CAACpB,KAAK,CAAC;EAChCA,KAAK,CAACqB,KAAK,CAAC,CAAC;EACbpB,QAAQ,CAACkB,IAAI,CAACC,WAAW,CAACpB,KAAK,CAAC;AAClC", "ignoreList": []}]}