{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Process/components/nodePanel/property/executionListener.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Process/components/nodePanel/property/executionListener.vue", "mtime": 1641277326000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["mixinPanel", "listenerParam", "components", "mixins", "data", "dialogVisible", "showParamDialog", "nowIndex", "formData", "executionListener", "computed", "formConfig", "inline", "item", "xType", "tabs", "label", "name", "column", "width", "rules", "required", "message", "trigger", "dic", "value", "tooltip", "slot", "mounted", "_this$element$busines", "_this$element$busines2", "element", "businessObject", "extensionElements", "values", "filter", "$type", "map", "_item$fields$map", "_item$fields", "type", "event", "className", "params", "fields", "field", "fieldType", "methods", "config<PERSON><PERSON><PERSON>", "index", "nowObj", "finishConfigParam", "param", "cache", "$set", "updateElement", "_this$formData$execut", "_this", "length", "_extensionElements$va", "_extensionElements$va2", "get", "modeler", "create", "for<PERSON>ach", "fieldElement", "push", "updateProperties", "_extensionElements$va3", "_extensionElements$va4", "closeDialog", "_this2", "$refs", "xForm", "validate", "then", "catch", "e", "console", "error"], "sources": ["src/components/Process/components/nodePanel/property/executionListener.vue"], "sourcesContent": ["<template>\n  <div>\n    <el-dialog\n      title=\"执行监听器\"\n      :visible.sync=\"dialogVisible\"\n      width=\"900px\"\n      :close-on-click-modal=\"false\"\n      :close-on-press-escape=\"false\"\n      :show-close=\"false\"\n      @closed=\"$emit('close')\"\n    >\n      <x-form ref=\"xForm\" v-model=\"formData\" :config=\"formConfig\">\n        <template #params=\"scope\">\n          <el-badge :value=\"scope.row.params ? scope.row.params.length : 0\" type=\"primary\">\n            <el-button size=\"small\" @click=\"configParam(scope.$index)\">配置</el-button>\n          </el-badge>\n        </template>\n      </x-form>\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" size=\"medium\" @click=\"closeDialog\">确 定</el-button>\n      </span>\n    </el-dialog>\n    <listenerParam v-if=\"showParamDialog\" :value=\"formData.executionListener[nowIndex].params\" @close=\"finishConfigParam\" />\n  </div>\n</template>\n\n<script>\nimport mixinPanel from '../../../common/mixinPanel'\nimport listenerParam from './listenerParam'\nexport default {\n  components: { listenerParam },\n  mixins: [mixinPanel],\n  data() {\n    return {\n      dialogVisible: true,\n      showParamDialog: false,\n      nowIndex: null,\n      formData: {\n        executionListener: []\n      }\n    }\n  },\n  computed: {\n    formConfig() {\n    //   const _this = this\n      return {\n        inline: false,\n        item: [\n          {\n            xType: 'tabs',\n            tabs: [\n              {\n                label: '执行监听器',\n                name: 'executionListener',\n                column: [\n                  {\n                    label: '事件',\n                    name: 'event',\n                    width: 180,\n                    rules: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],\n                    xType: 'select',\n                    dic: [\n                      { label: 'start', value: 'start' },\n                      { label: 'end', value: 'end' },\n                      { label: 'take', value: 'take' }\n                    ]\n                  },\n                  {\n                    label: '类型',\n                    name: 'type',\n                    width: 180,\n                    rules: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],\n                    xType: 'select',\n                    dic: [\n                      { label: '类', value: 'class' },\n                      { label: '表达式', value: 'expression' },\n                      { label: '委托表达式', value: 'delegateExpression' }\n                    ],\n                    tooltip: `类：示例 com.company.MyCustomListener，自定义类必须实现 org.flowable.engine.delegate.TaskListener 接口 <br />\n                              表达式：示例 \\${myObject.callMethod(task, task.eventName)} <br />\n                              委托表达式：示例 \\${myListenerSpringBean} ，该 springBean 需要实现 org.flowable.engine.delegate.TaskListener 接口\n                    `\n                  },\n                  {\n                    label: 'java 类名',\n                    name: 'className',\n                    xType: 'input',\n                    rules: [{ required: true, message: '请输入', trigger: ['blur', 'change'] }]\n                  },\n                  {\n                    xType: 'slot',\n                    label: '参数',\n                    width: 120,\n                    slot: true,\n                    name: 'params'\n                  }\n                ]\n              }\n            ]\n          }\n        ]\n      }\n    }\n  },\n  mounted() {\n    this.formData.executionListener = this.element.businessObject.extensionElements?.values\n      .filter(item => item.$type === 'flowable:ExecutionListener')\n      .map(item => {\n        let type\n        if ('class' in item) type = 'class'\n        if ('expression' in item) type = 'expression'\n        if ('delegateExpression' in item) type = 'delegateExpression'\n        return {\n          event: item.event,\n          type: type,\n          className: item[type],\n          params: item.fields?.map(field => {\n            let fieldType\n            if ('stringValue' in field) fieldType = 'stringValue'\n            if ('expression' in field) fieldType = 'expression'\n            return {\n              name: field.name,\n              type: fieldType,\n              value: field[fieldType]\n            }\n          }) ?? []\n        }\n      }) ?? []\n  },\n  methods: {\n    configParam(index) {\n      this.nowIndex = index\n      const nowObj = this.formData.executionListener[index]\n      if (!nowObj.params) {\n        nowObj.params = []\n      }\n      this.showParamDialog = true\n    },\n    finishConfigParam(param) {\n      this.showParamDialog = false\n      // hack 数量不更新问题\n      const cache = this.formData.executionListener[this.nowIndex]\n      cache.params = param\n      this.$set(this.formData.executionListener[this.nowIndex], this.nowIndex, cache)\n      this.nowIndex = null\n    },\n    updateElement() {\n      if (this.formData.executionListener?.length) {\n        let extensionElements = this.element.businessObject.get('extensionElements')\n        if (!extensionElements) {\n          extensionElements = this.modeler.get('moddle').create('bpmn:ExtensionElements')\n        }\n        // 清除旧值\n        extensionElements.values = extensionElements.values?.filter(item => item.$type !== 'flowable:ExecutionListener') ?? []\n        this.formData.executionListener.forEach(item => {\n          const executionListener = this.modeler.get('moddle').create('flowable:ExecutionListener')\n          executionListener['event'] = item.event\n          executionListener[item.type] = item.className\n          if (item.params && item.params.length) {\n            item.params.forEach(field => {\n              const fieldElement = this.modeler.get('moddle').create('flowable:Field')\n              fieldElement['name'] = field.name\n              fieldElement[field.type] = field.value\n              // 注意：flowable.json 中定义的string和expression类为小写，不然会和原生的String类冲突，此处为hack\n              // const valueElement = this.modeler.get('moddle').create(`flowable:${field.type}`, { body: field.value })\n              // fieldElement[field.type] = valueElement\n              executionListener.get('fields').push(fieldElement)\n            })\n          }\n          extensionElements.get('values').push(executionListener)\n        })\n        this.updateProperties({ extensionElements: extensionElements })\n      } else {\n        const extensionElements = this.element.businessObject[`extensionElements`]\n        if (extensionElements) {\n          extensionElements.values = extensionElements.values?.filter(item => item.$type !== 'flowable:ExecutionListener') ?? []\n        }\n      }\n    },\n    closeDialog() {\n      this.$refs.xForm.validate().then(() => {\n        this.updateElement()\n        this.dialogVisible = false\n      }).catch(e => console.error(e))\n    }\n  }\n}\n</script>\n\n<style>\n.flow-containers  .el-badge__content.is-fixed {\n    top: 18px;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BA,OAAAA,UAAA;AACA,OAAAC,aAAA;AACA;EACAC,UAAA;IAAAD,aAAA,EAAAA;EAAA;EACAE,MAAA,GAAAH,UAAA;EACAI,IAAA,WAAAA,KAAA;IACA;MACAC,aAAA;MACAC,eAAA;MACAC,QAAA;MACAC,QAAA;QACAC,iBAAA;MACA;IACA;EACA;EACAC,QAAA;IACAC,UAAA,WAAAA,WAAA;MACA;MACA;QACAC,MAAA;QACAC,IAAA,GACA;UACAC,KAAA;UACAC,IAAA,GACA;YACAC,KAAA;YACAC,IAAA;YACAC,MAAA,GACA;cACAF,KAAA;cACAC,IAAA;cACAE,KAAA;cACAC,KAAA;gBAAAC,QAAA;gBAAAC,OAAA;gBAAAC,OAAA;cAAA;cACAT,KAAA;cACAU,GAAA,GACA;gBAAAR,KAAA;gBAAAS,KAAA;cAAA,GACA;gBAAAT,KAAA;gBAAAS,KAAA;cAAA,GACA;gBAAAT,KAAA;gBAAAS,KAAA;cAAA;YAEA,GACA;cACAT,KAAA;cACAC,IAAA;cACAE,KAAA;cACAC,KAAA;gBAAAC,QAAA;gBAAAC,OAAA;gBAAAC,OAAA;cAAA;cACAT,KAAA;cACAU,GAAA,GACA;gBAAAR,KAAA;gBAAAS,KAAA;cAAA,GACA;gBAAAT,KAAA;gBAAAS,KAAA;cAAA,GACA;gBAAAT,KAAA;gBAAAS,KAAA;cAAA,EACA;cACAC,OAAA;YAIA,GACA;cACAV,KAAA;cACAC,IAAA;cACAH,KAAA;cACAM,KAAA;gBAAAC,QAAA;gBAAAC,OAAA;gBAAAC,OAAA;cAAA;YACA,GACA;cACAT,KAAA;cACAE,KAAA;cACAG,KAAA;cACAQ,IAAA;cACAV,IAAA;YACA;UAEA;QAEA;MAEA;IACA;EACA;EACAW,OAAA,WAAAA,QAAA;IAAA,IAAAC,qBAAA,EAAAC,sBAAA;IACA,KAAAtB,QAAA,CAAAC,iBAAA,IAAAoB,qBAAA,IAAAC,sBAAA,QAAAC,OAAA,CAAAC,cAAA,CAAAC,iBAAA,cAAAH,sBAAA,uBAAAA,sBAAA,CAAAI,MAAA,CACAC,MAAA,WAAAtB,IAAA;MAAA,OAAAA,IAAA,CAAAuB,KAAA;IAAA,GACAC,GAAA,WAAAxB,IAAA;MAAA,IAAAyB,gBAAA,EAAAC,YAAA;MACA,IAAAC,IAAA;MACA,eAAA3B,IAAA,EAAA2B,IAAA;MACA,oBAAA3B,IAAA,EAAA2B,IAAA;MACA,4BAAA3B,IAAA,EAAA2B,IAAA;MACA;QACAC,KAAA,EAAA5B,IAAA,CAAA4B,KAAA;QACAD,IAAA,EAAAA,IAAA;QACAE,SAAA,EAAA7B,IAAA,CAAA2B,IAAA;QACAG,MAAA,GAAAL,gBAAA,IAAAC,YAAA,GAAA1B,IAAA,CAAA+B,MAAA,cAAAL,YAAA,uBAAAA,YAAA,CAAAF,GAAA,WAAAQ,KAAA;UACA,IAAAC,SAAA;UACA,qBAAAD,KAAA,EAAAC,SAAA;UACA,oBAAAD,KAAA,EAAAC,SAAA;UACA;YACA7B,IAAA,EAAA4B,KAAA,CAAA5B,IAAA;YACAuB,IAAA,EAAAM,SAAA;YACArB,KAAA,EAAAoB,KAAA,CAAAC,SAAA;UACA;QACA,gBAAAR,gBAAA,cAAAA,gBAAA;MACA;IACA,gBAAAT,qBAAA,cAAAA,qBAAA;EACA;EACAkB,OAAA;IACAC,WAAA,WAAAA,YAAAC,KAAA;MACA,KAAA1C,QAAA,GAAA0C,KAAA;MACA,IAAAC,MAAA,QAAA1C,QAAA,CAAAC,iBAAA,CAAAwC,KAAA;MACA,KAAAC,MAAA,CAAAP,MAAA;QACAO,MAAA,CAAAP,MAAA;MACA;MACA,KAAArC,eAAA;IACA;IACA6C,iBAAA,WAAAA,kBAAAC,KAAA;MACA,KAAA9C,eAAA;MACA;MACA,IAAA+C,KAAA,QAAA7C,QAAA,CAAAC,iBAAA,MAAAF,QAAA;MACA8C,KAAA,CAAAV,MAAA,GAAAS,KAAA;MACA,KAAAE,IAAA,MAAA9C,QAAA,CAAAC,iBAAA,MAAAF,QAAA,QAAAA,QAAA,EAAA8C,KAAA;MACA,KAAA9C,QAAA;IACA;IACAgD,aAAA,WAAAA,cAAA;MAAA,IAAAC,qBAAA;QAAAC,KAAA;MACA,KAAAD,qBAAA,QAAAhD,QAAA,CAAAC,iBAAA,cAAA+C,qBAAA,eAAAA,qBAAA,CAAAE,MAAA;QAAA,IAAAC,qBAAA,EAAAC,sBAAA;QACA,IAAA3B,iBAAA,QAAAF,OAAA,CAAAC,cAAA,CAAA6B,GAAA;QACA,KAAA5B,iBAAA;UACAA,iBAAA,QAAA6B,OAAA,CAAAD,GAAA,WAAAE,MAAA;QACA;QACA;QACA9B,iBAAA,CAAAC,MAAA,IAAAyB,qBAAA,IAAAC,sBAAA,GAAA3B,iBAAA,CAAAC,MAAA,cAAA0B,sBAAA,uBAAAA,sBAAA,CAAAzB,MAAA,WAAAtB,IAAA;UAAA,OAAAA,IAAA,CAAAuB,KAAA;QAAA,gBAAAuB,qBAAA,cAAAA,qBAAA;QACA,KAAAnD,QAAA,CAAAC,iBAAA,CAAAuD,OAAA,WAAAnD,IAAA;UACA,IAAAJ,iBAAA,GAAAgD,KAAA,CAAAK,OAAA,CAAAD,GAAA,WAAAE,MAAA;UACAtD,iBAAA,YAAAI,IAAA,CAAA4B,KAAA;UACAhC,iBAAA,CAAAI,IAAA,CAAA2B,IAAA,IAAA3B,IAAA,CAAA6B,SAAA;UACA,IAAA7B,IAAA,CAAA8B,MAAA,IAAA9B,IAAA,CAAA8B,MAAA,CAAAe,MAAA;YACA7C,IAAA,CAAA8B,MAAA,CAAAqB,OAAA,WAAAnB,KAAA;cACA,IAAAoB,YAAA,GAAAR,KAAA,CAAAK,OAAA,CAAAD,GAAA,WAAAE,MAAA;cACAE,YAAA,WAAApB,KAAA,CAAA5B,IAAA;cACAgD,YAAA,CAAApB,KAAA,CAAAL,IAAA,IAAAK,KAAA,CAAApB,KAAA;cACA;cACA;cACA;cACAhB,iBAAA,CAAAoD,GAAA,WAAAK,IAAA,CAAAD,YAAA;YACA;UACA;UACAhC,iBAAA,CAAA4B,GAAA,WAAAK,IAAA,CAAAzD,iBAAA;QACA;QACA,KAAA0D,gBAAA;UAAAlC,iBAAA,EAAAA;QAAA;MACA;QACA,IAAAA,kBAAA,QAAAF,OAAA,CAAAC,cAAA;QACA,IAAAC,kBAAA;UAAA,IAAAmC,sBAAA,EAAAC,sBAAA;UACApC,kBAAA,CAAAC,MAAA,IAAAkC,sBAAA,IAAAC,sBAAA,GAAApC,kBAAA,CAAAC,MAAA,cAAAmC,sBAAA,uBAAAA,sBAAA,CAAAlC,MAAA,WAAAtB,IAAA;YAAA,OAAAA,IAAA,CAAAuB,KAAA;UAAA,gBAAAgC,sBAAA,cAAAA,sBAAA;QACA;MACA;IACA;IACAE,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,CAAAC,KAAA,CAAAC,QAAA,GAAAC,IAAA;QACAJ,MAAA,CAAAhB,aAAA;QACAgB,MAAA,CAAAlE,aAAA;MACA,GAAAuE,KAAA,WAAAC,CAAA;QAAA,OAAAC,OAAA,CAAAC,KAAA,CAAAF,CAAA;MAAA;IACA;EACA;AACA", "ignoreList": []}]}