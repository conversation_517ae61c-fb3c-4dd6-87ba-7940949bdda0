{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/directive/permission/hasRole.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/directive/permission/hasRole.js", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:LyoqCiog6KeS6Imy5p2D6ZmQ5aSE55CGCiogQ29weXJpZ2h0IChjKSAyMDE5IHJ1b3lpCiovCgppbXBvcnQgc3RvcmUgZnJvbSAnQC9zdG9yZSc7CmV4cG9ydCBkZWZhdWx0IHsKICBpbnNlcnRlZDogZnVuY3Rpb24gaW5zZXJ0ZWQoZWwsIGJpbmRpbmcsIHZub2RlKSB7CiAgICB2YXIgdmFsdWUgPSBiaW5kaW5nLnZhbHVlOwogICAgdmFyIHN1cGVyX2FkbWluID0gImFkbWluIjsKICAgIHZhciByb2xlcyA9IHN0b3JlLmdldHRlcnMgJiYgc3RvcmUuZ2V0dGVycy5yb2xlczsKICAgIGlmICh2YWx1ZSAmJiB2YWx1ZSBpbnN0YW5jZW9mIEFycmF5ICYmIHZhbHVlLmxlbmd0aCA+IDApIHsKICAgICAgdmFyIHJvbGVGbGFnID0gdmFsdWU7CiAgICAgIHZhciBoYXNSb2xlID0gcm9sZXMuc29tZShmdW5jdGlvbiAocm9sZSkgewogICAgICAgIHJldHVybiBzdXBlcl9hZG1pbiA9PT0gcm9sZSB8fCByb2xlRmxhZy5pbmNsdWRlcyhyb2xlKTsKICAgICAgfSk7CiAgICAgIGlmICghaGFzUm9sZSkgewogICAgICAgIGVsLnBhcmVudE5vZGUgJiYgZWwucGFyZW50Tm9kZS5yZW1vdmVDaGlsZChlbCk7CiAgICAgIH0KICAgIH0gZWxzZSB7CiAgICAgIHRocm93IG5ldyBFcnJvcigiXHU4QkY3XHU4QkJFXHU3RjZFXHU4OUQyXHU4MjcyXHU2NzQzXHU5NjUwXHU2ODA3XHU3QjdFXHU1MDNDXCIiKTsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["store", "inserted", "el", "binding", "vnode", "value", "super_admin", "roles", "getters", "Array", "length", "roleFlag", "hasRole", "some", "role", "includes", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "Error"], "sources": ["/Users/<USER>/develop/code/project/haijia/adminweb/src/directive/permission/hasRole.js"], "sourcesContent": [" /**\n * 角色权限处理\n * Copyright (c) 2019 ruoyi\n */\n \nimport store from '@/store'\n\nexport default {\n  inserted(el, binding, vnode) {\n    const { value } = binding\n    const super_admin = \"admin\";\n    const roles = store.getters && store.getters.roles\n\n    if (value && value instanceof Array && value.length > 0) {\n      const roleFlag = value\n\n      const hasRole = roles.some(role => {\n        return super_admin === role || roleFlag.includes(role)\n      })\n\n      if (!hasRole) {\n        el.parentNode && el.parentNode.removeChild(el)\n      }\n    } else {\n      throw new Error(`请设置角色权限标签值\"`)\n    }\n  }\n}\n"], "mappings": "AAAC;AACD;AACA;AACA;;AAEA,OAAOA,KAAK,MAAM,SAAS;AAE3B,eAAe;EACbC,QAAQ,WAAAA,SAACC,EAAE,EAAEC,OAAO,EAAEC,KAAK,EAAE;IAC3B,IAAQC,KAAK,GAAKF,OAAO,CAAjBE,KAAK;IACb,IAAMC,WAAW,GAAG,OAAO;IAC3B,IAAMC,KAAK,GAAGP,KAAK,CAACQ,OAAO,IAAIR,KAAK,CAACQ,OAAO,CAACD,KAAK;IAElD,IAAIF,KAAK,IAAIA,KAAK,YAAYI,KAAK,IAAIJ,KAAK,CAACK,MAAM,GAAG,CAAC,EAAE;MACvD,IAAMC,QAAQ,GAAGN,KAAK;MAEtB,IAAMO,OAAO,GAAGL,KAAK,CAACM,IAAI,CAAC,UAAAC,IAAI,EAAI;QACjC,OAAOR,WAAW,KAAKQ,IAAI,IAAIH,QAAQ,CAACI,QAAQ,CAACD,IAAI,CAAC;MACxD,CAAC,CAAC;MAEF,IAAI,CAACF,OAAO,EAAE;QACZV,EAAE,CAACc,UAAU,IAAId,EAAE,CAACc,UAAU,CAACC,WAAW,CAACf,EAAE,CAAC;MAChD;IACF,CAAC,MAAM;MACL,MAAM,IAAIgB,KAAK,iEAAc,CAAC;IAChC;EACF;AACF,CAAC", "ignoreList": []}]}