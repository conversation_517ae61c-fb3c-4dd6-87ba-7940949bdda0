{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/components/tinymce/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/components/tinymce/index.vue", "mtime": 1641277326000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js", "mtime": 1751171659043}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}