{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Process/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Process/index.vue", "mtime": 1661782128000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["customTranslate", "Modeler", "panel", "BpmData", "getInitStr", "customContextPad", "flowableModdle", "name", "components", "props", "xml", "type", "String", "default", "users", "Array", "_default", "groups", "categorys", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "taskList", "data", "modeler", "zoom", "watch", "val", "createNewDiagram", "mounted", "container", "$refs", "canvas", "additionalModules", "translate", "moddleExtensions", "flowable", "newDiagram", "methods", "fitViewport", "get", "bbox", "document", "querySelector", "getBBox", "currentViewbox", "viewbox", "elementMid", "x", "width", "y", "height", "zoomViewport", "zoomIn", "arguments", "length", "undefined", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "replace", "match", "str", "importXML", "adjustPalette", "fillColor", "t0", "console", "error", "message", "warnings", "stop", "dj<PERSON><PERSON><PERSON><PERSON>", "children", "djs<PERSON>alS<PERSON>le", "padding", "background", "left", "borderRadius", "key", "style", "palette", "allGroups", "g<PERSON>ey", "group", "c<PERSON><PERSON>", "control", "controlStyle", "display", "justifyContent", "alignItems", "className", "dataset", "indexOf", "controlProps", "getControl", "action", "innerHTML", "concat", "csKey", "e", "log", "_this2", "_definitions", "rootElements", "flowElements", "for<PERSON>ach", "n", "completeTask", "find", "m", "id", "todoTask", "completed", "endTask", "$type", "_n$outgoing", "add<PERSON><PERSON><PERSON>", "outgoing", "nn", "targetTask", "targetRef", "_n$outgoing2", "_n$outgoing3", "getProcess", "element", "getProcessElement", "category", "$attrs", "getDefinitions", "i", "saveXML", "_arguments", "_this3", "_callee2", "download", "_yield$_this3$modeler", "_callee2$", "_context2", "format", "sent", "downloadFile", "abrupt", "showXML", "_this4", "_callee3", "_yield$_this4$modeler", "_callee3$", "_context3", "$emit", "saveImg", "_arguments2", "_this5", "_callee4", "_yield$_this5$modeler", "svg", "_callee4$", "_context4", "saveSVG", "save", "_this6", "_callee5", "process", "result", "_callee5$", "_context5", "window", "parent", "postMessage", "openBpmn", "file", "_this7", "reader", "FileReader", "readAsText", "onload", "filename", "a", "createElement", "url", "URL", "createObjectURL", "Blob", "href", "click", "revokeObjectURL", "dataType"], "sources": ["src/components/Process/index.vue"], "sourcesContent": ["<template>\n  <div v-loading=\"isView\" class=\"flow-containers\" :class=\"{ 'view-mode': isView }\">\n    <el-container style=\"height: 100%\">\n      <el-header style=\"border-bottom: 1px solid rgb(218 218 218);height: auto;\">\n        <div style=\"display: flex; padding: 10px 0px; justify-content: space-between;\">\n          <div>\n            <el-upload action=\"\" :before-upload=\"openBpmn\" style=\"margin-right: 10px; display:inline-block;\">\n              <el-tooltip effect=\"dark\" content=\"加载xml\" placement=\"bottom\">\n                <el-button size=\"mini\" icon=\"el-icon-folder-opened\" />\n              </el-tooltip>\n            </el-upload>\n            <el-tooltip effect=\"dark\" content=\"新建\" placement=\"bottom\">\n              <el-button size=\"mini\" icon=\"el-icon-circle-plus\" @click=\"newDiagram\" />\n            </el-tooltip>\n            <el-tooltip effect=\"dark\" content=\"自适应屏幕\" placement=\"bottom\">\n              <el-button size=\"mini\" icon=\"el-icon-rank\" @click=\"fitViewport\" />\n            </el-tooltip>\n            <el-tooltip effect=\"dark\" content=\"放大\" placement=\"bottom\">\n              <el-button size=\"mini\" icon=\"el-icon-zoom-in\" @click=\"zoomViewport(true)\" />\n            </el-tooltip>\n            <el-tooltip effect=\"dark\" content=\"缩小\" placement=\"bottom\">\n              <el-button size=\"mini\" icon=\"el-icon-zoom-out\" @click=\"zoomViewport(false)\" />\n            </el-tooltip>\n            <el-tooltip effect=\"dark\" content=\"后退\" placement=\"bottom\">\n              <el-button size=\"mini\" icon=\"el-icon-back\" @click=\"modeler.get('commandStack').undo()\" />\n            </el-tooltip>\n            <el-tooltip effect=\"dark\" content=\"前进\" placement=\"bottom\">\n              <el-button size=\"mini\" icon=\"el-icon-right\" @click=\"modeler.get('commandStack').redo()\" />\n            </el-tooltip>\n          </div>\n          <div>\n            <el-button size=\"mini\" icon=\"el-icon-view\" @click=\"showXML\">查看xml</el-button>\n            <el-button size=\"mini\" icon=\"el-icon-download\" @click=\"saveXML(true)\">下载xml</el-button>\n            <el-button size=\"mini\" icon=\"el-icon-picture\" @click=\"saveImg('svg', true)\">下载svg</el-button>\n            <el-button size=\"mini\" type=\"primary\" @click=\"save\">保存模型</el-button>\n          </div>\n        </div>\n      </el-header>\n      <el-container style=\"align-items: stretch\">\n        <el-main style=\"padding: 0;\">\n          <div ref=\"canvas\" class=\"canvas\" />\n        </el-main>\n        <el-aside style=\"width: 400px; min-height: 650px; background-color: #f0f2f5\">\n          <panel v-if=\"modeler\" :modeler=\"modeler\" :users=\"users\" :groups=\"groups\" :categorys=\"categorys\" @dataType=\"dataType\" />\n        </el-aside>\n      </el-container>\n    </el-container>\n  </div>\n</template>\n\n<script>\n// 汉化\nimport customTranslate from './common/customTranslate'\nimport Modeler from 'bpmn-js/lib/Modeler'\nimport panel from './PropertyPanel'\nimport BpmData from './BpmData'\nimport getInitStr from './flowable/init'\nimport customContextPad from './components/custom'\n// 引入flowable的节点文件\nimport flowableModdle from './flowable/flowable.json'\nexport default {\n  name: 'WorkflowBpmnModeler',\n  components: {\n    panel\n  },\n  props: {\n    xml: {\n      type: String,\n      default: ''\n    },\n    users: {\n      type: Array,\n      default: () => []\n    },\n    groups: {\n      type: Array,\n      default: () => []\n    },\n    categorys: {\n      type: Array,\n      default: () => []\n    },\n    isView: {\n      type: Boolean,\n      default: false\n    },\n    taskList: {\n      type: Array,\n      default: () => []\n    }\n  },\n  data() {\n    return {\n      modeler: null,\n      // taskList: [],\n      zoom: 1\n    }\n  },\n  watch: {\n    xml: function(val) {\n      if (val) {\n        this.createNewDiagram(val)\n      }\n    }\n  },\n  mounted() {\n    // 生成实例\n    this.modeler = new Modeler({\n      container: this.$refs.canvas,\n      additionalModules: [\n        {\n          translate: ['value', customTranslate]\n        },\n        customContextPad\n      ],\n      moddleExtensions: {\n        flowable: flowableModdle\n      }\n    })\n    // 新增流程定义\n    if (!this.xml) {\n      this.newDiagram()\n    } else {\n      this.createNewDiagram(this.xml)\n    }\n  },\n  methods: {\n    newDiagram() {\n      this.createNewDiagram(getInitStr())\n    },\n    // 让图能自适应屏幕\n    fitViewport() {\n      this.zoom = this.modeler.get('canvas').zoom('fit-viewport')\n      const bbox = document.querySelector('.flow-containers .viewport').getBBox()\n      const currentViewbox = this.modeler.get('canvas').viewbox()\n      const elementMid = {\n        x: bbox.x + bbox.width / 2 - 65,\n        y: bbox.y + bbox.height / 2\n      }\n      this.modeler.get('canvas').viewbox({\n        x: elementMid.x - currentViewbox.width / 2,\n        y: elementMid.y - currentViewbox.height / 2,\n        width: currentViewbox.width,\n        height: currentViewbox.height\n      })\n      this.zoom = bbox.width / currentViewbox.width * 1.8\n    },\n    // 放大缩小\n    zoomViewport(zoomIn = true) {\n      this.zoom = this.modeler.get('canvas').zoom()\n      this.zoom += (zoomIn ? 0.1 : -0.1)\n      this.modeler.get('canvas').zoom(this.zoom)\n    },\n    async createNewDiagram(data) {\n      // 将字符串转换成图显示出来\n      // data = data.replace(/<!\\[CDATA\\[(.+?)]]>/g, '&lt;![CDATA[$1]]&gt;')\n      data = data.replace(/<!\\[CDATA\\[(.+?)]]>/g, function(match, str) {\n        return str.replace(/</g, '&lt;')\n      })\n      try {\n        await this.modeler.importXML(data)\n        this.adjustPalette()\n        this.fitViewport()\n        if (this.taskList !==undefined && this.taskList.length > 0 ) {\n          this.fillColor()\n        }\n      } catch (err) {\n        console.error(err.message, err.warnings)\n      }\n    },\n    // 调整左侧工具栏排版\n    adjustPalette() {\n      try {\n        // 获取 bpmn 设计器实例\n        const canvas = this.$refs.canvas\n        const djsPalette = canvas.children[0].children[1].children[4]\n        const djsPalStyle = {\n          width: '130px',\n          padding: '5px',\n          background: 'white',\n          left: '20px',\n          borderRadius: 0\n        }\n        for (var key in djsPalStyle) {\n          djsPalette.style[key] = djsPalStyle[key]\n        }\n        const palette = djsPalette.children[0]\n        const allGroups = palette.children\n        // console.log(\"===============>>>\")\n        // console.log(allGroups)\n        // allGroups[0].children[2].style['display'] = 'none'\n        // allGroups[0].children[3].style['display'] = 'none'\n        // 修改控件样式\n        for (var gKey in allGroups) {\n          const group = allGroups[gKey]\n          for (var cKey in group.children) {\n            const control = group.children[cKey]\n            const controlStyle = {\n              display: 'flex',\n              justifyContent: 'flex-start',\n              alignItems: 'center',\n              width: '100%',\n              padding: '5px'\n            }\n            if (\n              control.className &&\n              control.dataset &&\n              control.className.indexOf('entry') !== -1\n            ) {\n              const controlProps = new BpmData().getControl(\n                control.dataset.action\n              )\n              control.innerHTML = `<div style='font-size: 14px;font-weight:500;margin-left:15px;'>${\n                controlProps['title']\n              }</div>`\n              for (var csKey in controlStyle) {\n                control.style[csKey] = controlStyle[csKey]\n              }\n            }\n          }\n        }\n      } catch (e) {\n        console.log(e)\n      }\n    },\n    fillColor() {\n      const canvas = this.modeler.get('canvas')\n      this.modeler._definitions.rootElements[0].flowElements.forEach(n => {\n        const completeTask = this.taskList.find(m => m.key === n.id)\n        const todoTask = this.taskList.find(m => !m.completed)\n        const endTask = this.taskList[this.taskList.length - 1]\n        if (n.$type === 'bpmn:UserTask') {\n          if (completeTask) {\n            canvas.addMarker(n.id, completeTask.completed ? 'highlight' : 'highlight-todo')\n            n.outgoing?.forEach(nn => {\n              const targetTask = this.taskList.find(m => m.key === nn.targetRef.id)\n              if (targetTask) {\n                if (todoTask && completeTask.key === todoTask.key && !todoTask.completed){\n                  canvas.addMarker(nn.id, todoTask.completed ? 'highlight' : 'highlight-todo')\n                  canvas.addMarker(nn.targetRef.id, todoTask.completed ? 'highlight' : 'highlight-todo')\n                }else {\n                  canvas.addMarker(nn.id, targetTask.completed ? 'highlight' : 'highlight-todo')\n                  canvas.addMarker(nn.targetRef.id, targetTask.completed ? 'highlight' : 'highlight-todo')\n                }\n              }\n            })\n          }\n        }\n        // 排他网关\n        else if (n.$type === 'bpmn:ExclusiveGateway') {\n          if (completeTask) {\n            canvas.addMarker(n.id, completeTask.completed ? 'highlight' : 'highlight-todo')\n            n.outgoing?.forEach(nn => {\n              const targetTask = this.taskList.find(m => m.key === nn.targetRef.id)\n              if (targetTask) {\n\n                canvas.addMarker(nn.id, targetTask.completed ? 'highlight' : 'highlight-todo')\n                canvas.addMarker(nn.targetRef.id, targetTask.completed ? 'highlight' : 'highlight-todo')\n              }\n\n            })\n          }\n\n        }\n        // 并行网关\n        else if (n.$type === 'bpmn:ParallelGateway') {\n          if (completeTask) {\n            canvas.addMarker(n.id, completeTask.completed ? 'highlight' : 'highlight-todo')\n            n.outgoing?.forEach(nn => {\n              const targetTask = this.taskList.find(m => m.key === nn.targetRef.id)\n              if (targetTask) {\n                canvas.addMarker(nn.id, targetTask.completed ? 'highlight' : 'highlight-todo')\n                canvas.addMarker(nn.targetRef.id, targetTask.completed ? 'highlight' : 'highlight-todo')\n              }\n            })\n          }\n        }\n        else if (n.$type === 'bpmn:StartEvent') {\n          n.outgoing.forEach(nn => {\n            const completeTask = this.taskList.find(m => m.key === nn.targetRef.id)\n            if (completeTask) {\n              canvas.addMarker(nn.id, 'highlight')\n              canvas.addMarker(n.id, 'highlight')\n              return\n            }\n          })\n        }\n        else if (n.$type === 'bpmn:EndEvent') {\n          if (endTask.key === n.id && endTask.completed) {\n            canvas.addMarker(n.id, 'highlight')\n            return\n          }\n        }\n      })\n    },\n    // 对外 api\n    getProcess() {\n      const element = this.getProcessElement()\n      return {\n        id: element.id,\n        name: element.name,\n        category: element.$attrs['flowable:processCategory']\n      }\n    },\n    getProcessElement() {\n      const rootElements = this.modeler.getDefinitions().rootElements\n      for (let i = 0; i < rootElements.length; i++) {\n        if (rootElements[i].$type === 'bpmn:Process') return rootElements[i]\n      }\n    },\n    async saveXML(download = false) {\n      try {\n        const { xml } = await this.modeler.saveXML({ format: true })\n        if (download) {\n          this.downloadFile(`${this.getProcessElement().name}.bpmn20.xml`, xml, 'application/xml')\n        }\n        return xml\n      } catch (err) {\n        console.log(err)\n      }\n    },\n    async showXML() {\n      try {\n        const { xml } = await this.modeler.saveXML({ format: true })\n        this.$emit('showXML',xml)\n      } catch (err) {\n        console.log(err)\n      }\n    },\n    async saveImg(type = 'svg', download = false) {\n      try {\n        const { svg } = await this.modeler.saveSVG({ format: true })\n        if (download) {\n          this.downloadFile(this.getProcessElement().name, svg, 'image/svg+xml')\n        }\n        return svg\n      } catch (err) {\n        console.log(err)\n      }\n    },\n    async save() {\n      const process = this.getProcess()\n      const xml = await this.saveXML()\n      const svg = await this.saveImg()\n      const result = { process, xml, svg }\n      this.$emit('save', result)\n      window.parent.postMessage(result, '*')\n    },\n    openBpmn(file) {\n      const reader = new FileReader()\n      reader.readAsText(file, 'utf-8')\n      reader.onload = () => {\n        this.createNewDiagram(reader.result)\n      }\n      return false\n    },\n    downloadFile(filename, data, type) {\n      var a = document.createElement('a')\n      var url = window.URL.createObjectURL(new Blob([data], { type: type }))\n      a.href = url\n      a.download = filename\n      a.click()\n      window.URL.revokeObjectURL(url)\n    },\n    /** 获取数据类型 */\n    dataType(data){\n      this.$emit('dataType', data)\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\">\n/*左边工具栏以及编辑节点的样式*/\n@import \"~bpmn-js/dist/assets/diagram-js.css\";\n@import \"~bpmn-js/dist/assets/bpmn-font/css/bpmn.css\";\n@import \"~bpmn-js/dist/assets/bpmn-font/css/bpmn-codes.css\";\n@import \"~bpmn-js/dist/assets/bpmn-font/css/bpmn-embedded.css\";\n.view-mode {\n  .el-header, .el-aside, .djs-palette, .bjs-powered-by {\n    display: none;\n  }\n  .el-loading-mask {\n    background-color: initial;\n  }\n  .el-loading-spinner {\n    display: none;\n  }\n}\n.flow-containers {\n  // background-color: #ffffff;\n  width: 100%;\n  height: 100%;\n  .canvas {\n    width: 100%;\n    height: 100%;\n  }\n  .panel {\n    position: absolute;\n    right: 0;\n    top: 50px;\n    width: 300px;\n  }\n  .load {\n    margin-right: 10px;\n  }\n  .el-form-item__label{\n    font-size: 13px;\n  }\n\n  .djs-palette{\n    left: 0px!important;\n    top: 0px;\n    border-top: none;\n  }\n\n  .djs-container svg {\n    min-height: 650px;\n  }\n\n   .highlight.djs-shape .djs-visual > :nth-child(1) {\n     fill: green !important;\n     stroke: green !important;\n     fill-opacity: 0.2 !important;\n   }\n   .highlight.djs-shape .djs-visual > :nth-child(2) {\n     fill: green !important;\n   }\n   .highlight.djs-shape .djs-visual > path {\n     fill: green !important;\n     fill-opacity: 0.2 !important;\n     stroke: green !important;\n   }\n   .highlight.djs-connection > .djs-visual > path {\n     stroke: green !important;\n   }\n   // .djs-connection > .djs-visual > path {\n   //   stroke: orange !important;\n   //   stroke-dasharray: 4px !important;\n   //   fill-opacity: 0.2 !important;\n   // }\n   // .djs-shape .djs-visual > :nth-child(1) {\n   //   fill: orange !important;\n   //   stroke: orange !important;\n   //   stroke-dasharray: 4px !important;\n   //   fill-opacity: 0.2 !important;\n   // }\n   .highlight-todo.djs-connection > .djs-visual > path {\n     stroke: orange !important;\n     stroke-dasharray: 4px !important;\n     fill-opacity: 0.2 !important;\n   }\n   .highlight-todo.djs-shape .djs-visual > :nth-child(1) {\n     fill: orange !important;\n     stroke: orange !important;\n     stroke-dasharray: 4px !important;\n     fill-opacity: 0.2 !important;\n   }\n   .overlays-div {\n     font-size: 10px;\n     color: red;\n     width: 100px;\n     top: -20px !important;\n   }\n}\n@media screen and (max-width: 600px) {\n  .flow-containers .djs-container svg {\n      min-height: 350px;\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmDA;AACA,OAAAA,eAAA;AACA,OAAAC,OAAA;AACA,OAAAC,KAAA;AACA,OAAAC,OAAA;AACA,OAAAC,UAAA;AACA,OAAAC,gBAAA;AACA;AACA,OAAAC,cAAA;AACA;EACAC,IAAA;EACAC,UAAA;IACAN,KAAA,EAAAA;EACA;EACAO,KAAA;IACAC,GAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAC,KAAA;MACAH,IAAA,EAAAI,KAAA;MACAF,OAAA,WAAAG,SAAA;QAAA;MAAA;IACA;IACAC,MAAA;MACAN,IAAA,EAAAI,KAAA;MACAF,OAAA,WAAAG,SAAA;QAAA;MAAA;IACA;IACAE,SAAA;MACAP,IAAA,EAAAI,KAAA;MACAF,OAAA,WAAAG,SAAA;QAAA;MAAA;IACA;IACAG,MAAA;MACAR,IAAA,EAAAS,OAAA;MACAP,OAAA;IACA;IACAQ,QAAA;MACAV,IAAA,EAAAI,KAAA;MACAF,OAAA,WAAAG,SAAA;QAAA;MAAA;IACA;EACA;EACAM,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACA;MACAC,IAAA;IACA;EACA;EACAC,KAAA;IACAf,GAAA,WAAAA,IAAAgB,GAAA;MACA,IAAAA,GAAA;QACA,KAAAC,gBAAA,CAAAD,GAAA;MACA;IACA;EACA;EACAE,OAAA,WAAAA,QAAA;IACA;IACA,KAAAL,OAAA,OAAAtB,OAAA;MACA4B,SAAA,OAAAC,KAAA,CAAAC,MAAA;MACAC,iBAAA,GACA;QACAC,SAAA,YAAAjC,eAAA;MACA,GACAK,gBAAA,CACA;MACA6B,gBAAA;QACAC,QAAA,EAAA7B;MACA;IACA;IACA;IACA,UAAAI,GAAA;MACA,KAAA0B,UAAA;IACA;MACA,KAAAT,gBAAA,MAAAjB,GAAA;IACA;EACA;EACA2B,OAAA;IACAD,UAAA,WAAAA,WAAA;MACA,KAAAT,gBAAA,CAAAvB,UAAA;IACA;IACA;IACAkC,WAAA,WAAAA,YAAA;MACA,KAAAd,IAAA,QAAAD,OAAA,CAAAgB,GAAA,WAAAf,IAAA;MACA,IAAAgB,IAAA,GAAAC,QAAA,CAAAC,aAAA,+BAAAC,OAAA;MACA,IAAAC,cAAA,QAAArB,OAAA,CAAAgB,GAAA,WAAAM,OAAA;MACA,IAAAC,UAAA;QACAC,CAAA,EAAAP,IAAA,CAAAO,CAAA,GAAAP,IAAA,CAAAQ,KAAA;QACAC,CAAA,EAAAT,IAAA,CAAAS,CAAA,GAAAT,IAAA,CAAAU,MAAA;MACA;MACA,KAAA3B,OAAA,CAAAgB,GAAA,WAAAM,OAAA;QACAE,CAAA,EAAAD,UAAA,CAAAC,CAAA,GAAAH,cAAA,CAAAI,KAAA;QACAC,CAAA,EAAAH,UAAA,CAAAG,CAAA,GAAAL,cAAA,CAAAM,MAAA;QACAF,KAAA,EAAAJ,cAAA,CAAAI,KAAA;QACAE,MAAA,EAAAN,cAAA,CAAAM;MACA;MACA,KAAA1B,IAAA,GAAAgB,IAAA,CAAAQ,KAAA,GAAAJ,cAAA,CAAAI,KAAA;IACA;IACA;IACAG,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA;MACA,KAAA7B,IAAA,QAAAD,OAAA,CAAAgB,GAAA,WAAAf,IAAA;MACA,KAAAA,IAAA,IAAA4B,MAAA;MACA,KAAA7B,OAAA,CAAAgB,GAAA,WAAAf,IAAA,MAAAA,IAAA;IACA;IACAG,gBAAA,WAAAA,iBAAAL,IAAA;MAAA,IAAAkC,KAAA;MAAA,OAAAC,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,OAAAF,mBAAA,GAAAG,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cACA;cACA;cACA3C,IAAA,GAAAA,IAAA,CAAA4C,OAAA,mCAAAC,KAAA,EAAAC,GAAA;gBACA,OAAAA,GAAA,CAAAF,OAAA;cACA;cAAAH,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAAE,IAAA;cAAA,OAEAT,KAAA,CAAAjC,OAAA,CAAA8C,SAAA,CAAA/C,IAAA;YAAA;cACAkC,KAAA,CAAAc,aAAA;cACAd,KAAA,CAAAlB,WAAA;cACA,IAAAkB,KAAA,CAAAnC,QAAA,KAAAkC,SAAA,IAAAC,KAAA,CAAAnC,QAAA,CAAAiC,MAAA;gBACAE,KAAA,CAAAe,SAAA;cACA;cAAAR,QAAA,CAAAE,IAAA;cAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAAS,EAAA,GAAAT,QAAA;cAEAU,OAAA,CAAAC,KAAA,CAAAX,QAAA,CAAAS,EAAA,CAAAG,OAAA,EAAAZ,QAAA,CAAAS,EAAA,CAAAI,QAAA;YAAA;YAAA;cAAA,OAAAb,QAAA,CAAAc,IAAA;UAAA;QAAA,GAAAjB,OAAA;MAAA;IAEA;IACA;IACAU,aAAA,WAAAA,cAAA;MACA;QACA;QACA,IAAAvC,MAAA,QAAAD,KAAA,CAAAC,MAAA;QACA,IAAA+C,UAAA,GAAA/C,MAAA,CAAAgD,QAAA,IAAAA,QAAA,IAAAA,QAAA;QACA,IAAAC,WAAA;UACAhC,KAAA;UACAiC,OAAA;UACAC,UAAA;UACAC,IAAA;UACAC,YAAA;QACA;QACA,SAAAC,GAAA,IAAAL,WAAA;UACAF,UAAA,CAAAQ,KAAA,CAAAD,GAAA,IAAAL,WAAA,CAAAK,GAAA;QACA;QACA,IAAAE,OAAA,GAAAT,UAAA,CAAAC,QAAA;QACA,IAAAS,SAAA,GAAAD,OAAA,CAAAR,QAAA;QACA;QACA;QACA;QACA;QACA;QACA,SAAAU,IAAA,IAAAD,SAAA;UACA,IAAAE,KAAA,GAAAF,SAAA,CAAAC,IAAA;UACA,SAAAE,IAAA,IAAAD,KAAA,CAAAX,QAAA;YACA,IAAAa,OAAA,GAAAF,KAAA,CAAAX,QAAA,CAAAY,IAAA;YACA,IAAAE,YAAA;cACAC,OAAA;cACAC,cAAA;cACAC,UAAA;cACAhD,KAAA;cACAiC,OAAA;YACA;YACA,IACAW,OAAA,CAAAK,SAAA,IACAL,OAAA,CAAAM,OAAA,IACAN,OAAA,CAAAK,SAAA,CAAAE,OAAA,kBACA;cACA,IAAAC,YAAA,OAAAjG,OAAA,GAAAkG,UAAA,CACAT,OAAA,CAAAM,OAAA,CAAAI,MACA;cACAV,OAAA,CAAAW,SAAA,qEAAAC,MAAA,CACAJ,YAAA,oBACA;cACA,SAAAK,KAAA,IAAAZ,YAAA;gBACAD,OAAA,CAAAN,KAAA,CAAAmB,KAAA,IAAAZ,YAAA,CAAAY,KAAA;cACA;YACA;UACA;QACA;MACA,SAAAC,CAAA;QACAjC,OAAA,CAAAkC,GAAA,CAAAD,CAAA;MACA;IACA;IACAnC,SAAA,WAAAA,UAAA;MAAA,IAAAqC,MAAA;MACA,IAAA7E,MAAA,QAAAR,OAAA,CAAAgB,GAAA;MACA,KAAAhB,OAAA,CAAAsF,YAAA,CAAAC,YAAA,IAAAC,YAAA,CAAAC,OAAA,WAAAC,CAAA;QACA,IAAAC,YAAA,GAAAN,MAAA,CAAAvF,QAAA,CAAA8F,IAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAA/B,GAAA,KAAA4B,CAAA,CAAAI,EAAA;QAAA;QACA,IAAAC,QAAA,GAAAV,MAAA,CAAAvF,QAAA,CAAA8F,IAAA,WAAAC,CAAA;UAAA,QAAAA,CAAA,CAAAG,SAAA;QAAA;QACA,IAAAC,OAAA,GAAAZ,MAAA,CAAAvF,QAAA,CAAAuF,MAAA,CAAAvF,QAAA,CAAAiC,MAAA;QACA,IAAA2D,CAAA,CAAAQ,KAAA;UACA,IAAAP,YAAA;YAAA,IAAAQ,WAAA;YACA3F,MAAA,CAAA4F,SAAA,CAAAV,CAAA,CAAAI,EAAA,EAAAH,YAAA,CAAAK,SAAA;YACA,CAAAG,WAAA,GAAAT,CAAA,CAAAW,QAAA,cAAAF,WAAA,eAAAA,WAAA,CAAAV,OAAA,WAAAa,EAAA;cACA,IAAAC,UAAA,GAAAlB,MAAA,CAAAvF,QAAA,CAAA8F,IAAA,WAAAC,CAAA;gBAAA,OAAAA,CAAA,CAAA/B,GAAA,KAAAwC,EAAA,CAAAE,SAAA,CAAAV,EAAA;cAAA;cACA,IAAAS,UAAA;gBACA,IAAAR,QAAA,IAAAJ,YAAA,CAAA7B,GAAA,KAAAiC,QAAA,CAAAjC,GAAA,KAAAiC,QAAA,CAAAC,SAAA;kBACAxF,MAAA,CAAA4F,SAAA,CAAAE,EAAA,CAAAR,EAAA,EAAAC,QAAA,CAAAC,SAAA;kBACAxF,MAAA,CAAA4F,SAAA,CAAAE,EAAA,CAAAE,SAAA,CAAAV,EAAA,EAAAC,QAAA,CAAAC,SAAA;gBACA;kBACAxF,MAAA,CAAA4F,SAAA,CAAAE,EAAA,CAAAR,EAAA,EAAAS,UAAA,CAAAP,SAAA;kBACAxF,MAAA,CAAA4F,SAAA,CAAAE,EAAA,CAAAE,SAAA,CAAAV,EAAA,EAAAS,UAAA,CAAAP,SAAA;gBACA;cACA;YACA;UACA;QACA;QACA;QAAA,KACA,IAAAN,CAAA,CAAAQ,KAAA;UACA,IAAAP,YAAA;YAAA,IAAAc,YAAA;YACAjG,MAAA,CAAA4F,SAAA,CAAAV,CAAA,CAAAI,EAAA,EAAAH,YAAA,CAAAK,SAAA;YACA,CAAAS,YAAA,GAAAf,CAAA,CAAAW,QAAA,cAAAI,YAAA,eAAAA,YAAA,CAAAhB,OAAA,WAAAa,EAAA;cACA,IAAAC,UAAA,GAAAlB,MAAA,CAAAvF,QAAA,CAAA8F,IAAA,WAAAC,CAAA;gBAAA,OAAAA,CAAA,CAAA/B,GAAA,KAAAwC,EAAA,CAAAE,SAAA,CAAAV,EAAA;cAAA;cACA,IAAAS,UAAA;gBAEA/F,MAAA,CAAA4F,SAAA,CAAAE,EAAA,CAAAR,EAAA,EAAAS,UAAA,CAAAP,SAAA;gBACAxF,MAAA,CAAA4F,SAAA,CAAAE,EAAA,CAAAE,SAAA,CAAAV,EAAA,EAAAS,UAAA,CAAAP,SAAA;cACA;YAEA;UACA;QAEA;QACA;QAAA,KACA,IAAAN,CAAA,CAAAQ,KAAA;UACA,IAAAP,YAAA;YAAA,IAAAe,YAAA;YACAlG,MAAA,CAAA4F,SAAA,CAAAV,CAAA,CAAAI,EAAA,EAAAH,YAAA,CAAAK,SAAA;YACA,CAAAU,YAAA,GAAAhB,CAAA,CAAAW,QAAA,cAAAK,YAAA,eAAAA,YAAA,CAAAjB,OAAA,WAAAa,EAAA;cACA,IAAAC,UAAA,GAAAlB,MAAA,CAAAvF,QAAA,CAAA8F,IAAA,WAAAC,CAAA;gBAAA,OAAAA,CAAA,CAAA/B,GAAA,KAAAwC,EAAA,CAAAE,SAAA,CAAAV,EAAA;cAAA;cACA,IAAAS,UAAA;gBACA/F,MAAA,CAAA4F,SAAA,CAAAE,EAAA,CAAAR,EAAA,EAAAS,UAAA,CAAAP,SAAA;gBACAxF,MAAA,CAAA4F,SAAA,CAAAE,EAAA,CAAAE,SAAA,CAAAV,EAAA,EAAAS,UAAA,CAAAP,SAAA;cACA;YACA;UACA;QACA,OACA,IAAAN,CAAA,CAAAQ,KAAA;UACAR,CAAA,CAAAW,QAAA,CAAAZ,OAAA,WAAAa,EAAA;YACA,IAAAX,YAAA,GAAAN,MAAA,CAAAvF,QAAA,CAAA8F,IAAA,WAAAC,CAAA;cAAA,OAAAA,CAAA,CAAA/B,GAAA,KAAAwC,EAAA,CAAAE,SAAA,CAAAV,EAAA;YAAA;YACA,IAAAH,YAAA;cACAnF,MAAA,CAAA4F,SAAA,CAAAE,EAAA,CAAAR,EAAA;cACAtF,MAAA,CAAA4F,SAAA,CAAAV,CAAA,CAAAI,EAAA;cACA;YACA;UACA;QACA,OACA,IAAAJ,CAAA,CAAAQ,KAAA;UACA,IAAAD,OAAA,CAAAnC,GAAA,KAAA4B,CAAA,CAAAI,EAAA,IAAAG,OAAA,CAAAD,SAAA;YACAxF,MAAA,CAAA4F,SAAA,CAAAV,CAAA,CAAAI,EAAA;YACA;UACA;QACA;MACA;IACA;IACA;IACAa,UAAA,WAAAA,WAAA;MACA,IAAAC,OAAA,QAAAC,iBAAA;MACA;QACAf,EAAA,EAAAc,OAAA,CAAAd,EAAA;QACA9G,IAAA,EAAA4H,OAAA,CAAA5H,IAAA;QACA8H,QAAA,EAAAF,OAAA,CAAAG,MAAA;MACA;IACA;IACAF,iBAAA,WAAAA,kBAAA;MACA,IAAAtB,YAAA,QAAAvF,OAAA,CAAAgH,cAAA,GAAAzB,YAAA;MACA,SAAA0B,CAAA,MAAAA,CAAA,GAAA1B,YAAA,CAAAxD,MAAA,EAAAkF,CAAA;QACA,IAAA1B,YAAA,CAAA0B,CAAA,EAAAf,KAAA,4BAAAX,YAAA,CAAA0B,CAAA;MACA;IACA;IACAC,OAAA,WAAAA,QAAA;MAAA,IAAAC,UAAA,GAAArF,SAAA;QAAAsF,MAAA;MAAA,OAAAlF,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAiF,SAAA;QAAA,IAAAC,QAAA,EAAAC,qBAAA,EAAApI,GAAA;QAAA,OAAAgD,mBAAA,GAAAG,IAAA,UAAAkF,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAhF,IAAA,GAAAgF,SAAA,CAAA/E,IAAA;YAAA;cAAA4E,QAAA,GAAAH,UAAA,CAAApF,MAAA,QAAAoF,UAAA,QAAAnF,SAAA,GAAAmF,UAAA;cAAAM,SAAA,CAAAhF,IAAA;cAAAgF,SAAA,CAAA/E,IAAA;cAAA,OAEA0E,MAAA,CAAApH,OAAA,CAAAkH,OAAA;gBAAAQ,MAAA;cAAA;YAAA;cAAAH,qBAAA,GAAAE,SAAA,CAAAE,IAAA;cAAAxI,GAAA,GAAAoI,qBAAA,CAAApI,GAAA;cACA,IAAAmI,QAAA;gBACAF,MAAA,CAAAQ,YAAA,IAAA3C,MAAA,CAAAmC,MAAA,CAAAP,iBAAA,GAAA7H,IAAA,kBAAAG,GAAA;cACA;cAAA,OAAAsI,SAAA,CAAAI,MAAA,WACA1I,GAAA;YAAA;cAAAsI,SAAA,CAAAhF,IAAA;cAAAgF,SAAA,CAAAxE,EAAA,GAAAwE,SAAA;cAEAvE,OAAA,CAAAkC,GAAA,CAAAqC,SAAA,CAAAxE,EAAA;YAAA;YAAA;cAAA,OAAAwE,SAAA,CAAAnE,IAAA;UAAA;QAAA,GAAA+D,QAAA;MAAA;IAEA;IACAS,OAAA,WAAAA,QAAA;MAAA,IAAAC,MAAA;MAAA,OAAA7F,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAA4F,SAAA;QAAA,IAAAC,qBAAA,EAAA9I,GAAA;QAAA,OAAAgD,mBAAA,GAAAG,IAAA,UAAA4F,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA1F,IAAA,GAAA0F,SAAA,CAAAzF,IAAA;YAAA;cAAAyF,SAAA,CAAA1F,IAAA;cAAA0F,SAAA,CAAAzF,IAAA;cAAA,OAEAqF,MAAA,CAAA/H,OAAA,CAAAkH,OAAA;gBAAAQ,MAAA;cAAA;YAAA;cAAAO,qBAAA,GAAAE,SAAA,CAAAR,IAAA;cAAAxI,GAAA,GAAA8I,qBAAA,CAAA9I,GAAA;cACA4I,MAAA,CAAAK,KAAA,YAAAjJ,GAAA;cAAAgJ,SAAA,CAAAzF,IAAA;cAAA;YAAA;cAAAyF,SAAA,CAAA1F,IAAA;cAAA0F,SAAA,CAAAlF,EAAA,GAAAkF,SAAA;cAEAjF,OAAA,CAAAkC,GAAA,CAAA+C,SAAA,CAAAlF,EAAA;YAAA;YAAA;cAAA,OAAAkF,SAAA,CAAA7E,IAAA;UAAA;QAAA,GAAA0E,QAAA;MAAA;IAEA;IACAK,OAAA,WAAAA,QAAA;MAAA,IAAAC,WAAA,GAAAxG,SAAA;QAAAyG,MAAA;MAAA,OAAArG,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAoG,SAAA;QAAA,IAAApJ,IAAA,EAAAkI,QAAA,EAAAmB,qBAAA,EAAAC,GAAA;QAAA,OAAAvG,mBAAA,GAAAG,IAAA,UAAAqG,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAnG,IAAA,GAAAmG,SAAA,CAAAlG,IAAA;YAAA;cAAAtD,IAAA,GAAAkJ,WAAA,CAAAvG,MAAA,QAAAuG,WAAA,QAAAtG,SAAA,GAAAsG,WAAA;cAAAhB,QAAA,GAAAgB,WAAA,CAAAvG,MAAA,QAAAuG,WAAA,QAAAtG,SAAA,GAAAsG,WAAA;cAAAM,SAAA,CAAAnG,IAAA;cAAAmG,SAAA,CAAAlG,IAAA;cAAA,OAEA6F,MAAA,CAAAvI,OAAA,CAAA6I,OAAA;gBAAAnB,MAAA;cAAA;YAAA;cAAAe,qBAAA,GAAAG,SAAA,CAAAjB,IAAA;cAAAe,GAAA,GAAAD,qBAAA,CAAAC,GAAA;cACA,IAAApB,QAAA;gBACAiB,MAAA,CAAAX,YAAA,CAAAW,MAAA,CAAA1B,iBAAA,GAAA7H,IAAA,EAAA0J,GAAA;cACA;cAAA,OAAAE,SAAA,CAAAf,MAAA,WACAa,GAAA;YAAA;cAAAE,SAAA,CAAAnG,IAAA;cAAAmG,SAAA,CAAA3F,EAAA,GAAA2F,SAAA;cAEA1F,OAAA,CAAAkC,GAAA,CAAAwD,SAAA,CAAA3F,EAAA;YAAA;YAAA;cAAA,OAAA2F,SAAA,CAAAtF,IAAA;UAAA;QAAA,GAAAkF,QAAA;MAAA;IAEA;IACAM,IAAA,WAAAA,KAAA;MAAA,IAAAC,MAAA;MAAA,OAAA7G,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAA4G,SAAA;QAAA,IAAAC,OAAA,EAAA9J,GAAA,EAAAuJ,GAAA,EAAAQ,MAAA;QAAA,OAAA/G,mBAAA,GAAAG,IAAA,UAAA6G,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA3G,IAAA,GAAA2G,SAAA,CAAA1G,IAAA;YAAA;cACAuG,OAAA,GAAAF,MAAA,CAAApC,UAAA;cAAAyC,SAAA,CAAA1G,IAAA;cAAA,OACAqG,MAAA,CAAA7B,OAAA;YAAA;cAAA/H,GAAA,GAAAiK,SAAA,CAAAzB,IAAA;cAAAyB,SAAA,CAAA1G,IAAA;cAAA,OACAqG,MAAA,CAAAV,OAAA;YAAA;cAAAK,GAAA,GAAAU,SAAA,CAAAzB,IAAA;cACAuB,MAAA;gBAAAD,OAAA,EAAAA,OAAA;gBAAA9J,GAAA,EAAAA,GAAA;gBAAAuJ,GAAA,EAAAA;cAAA;cACAK,MAAA,CAAAX,KAAA,SAAAc,MAAA;cACAG,MAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAL,MAAA;YAAA;YAAA;cAAA,OAAAE,SAAA,CAAA9F,IAAA;UAAA;QAAA,GAAA0F,QAAA;MAAA;IACA;IACAQ,QAAA,WAAAA,SAAAC,IAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,MAAA,OAAAC,UAAA;MACAD,MAAA,CAAAE,UAAA,CAAAJ,IAAA;MACAE,MAAA,CAAAG,MAAA;QACAJ,MAAA,CAAAtJ,gBAAA,CAAAuJ,MAAA,CAAAT,MAAA;MACA;MACA;IACA;IACAtB,YAAA,WAAAA,aAAAmC,QAAA,EAAAhK,IAAA,EAAAX,IAAA;MACA,IAAA4K,CAAA,GAAA9I,QAAA,CAAA+I,aAAA;MACA,IAAAC,GAAA,GAAAb,MAAA,CAAAc,GAAA,CAAAC,eAAA,KAAAC,IAAA,EAAAtK,IAAA;QAAAX,IAAA,EAAAA;MAAA;MACA4K,CAAA,CAAAM,IAAA,GAAAJ,GAAA;MACAF,CAAA,CAAA1C,QAAA,GAAAyC,QAAA;MACAC,CAAA,CAAAO,KAAA;MACAlB,MAAA,CAAAc,GAAA,CAAAK,eAAA,CAAAN,GAAA;IACA;IACA,aACAO,QAAA,WAAAA,SAAA1K,IAAA;MACA,KAAAqI,KAAA,aAAArI,IAAA;IACA;EACA;AACA", "ignoreList": []}]}