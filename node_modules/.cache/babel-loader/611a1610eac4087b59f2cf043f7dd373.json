{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/dashboard/mixins/resize.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/dashboard/mixins/resize.js", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js", "mtime": 1751171659043}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}