{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/router/index.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/router/index.js", "mtime": 1717760559368}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js", "mtime": 1751171659043}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}