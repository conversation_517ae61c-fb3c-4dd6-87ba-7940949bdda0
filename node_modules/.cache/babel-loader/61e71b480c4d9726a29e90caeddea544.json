{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/store/modules/permission.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/store/modules/permission.js", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js", "mtime": 1751171659043}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgY29uc3RhbnRSb3V0ZXMgfSBmcm9tICdAL3JvdXRlcic7CmltcG9ydCB7IGdldFJvdXRlcnMgfSBmcm9tICdAL2FwaS9tZW51JzsKaW1wb3J0IExheW91dCBmcm9tICdAL2xheW91dC9pbmRleCc7CmltcG9ydCBQYXJlbnRWaWV3IGZyb20gJ0AvY29tcG9uZW50cy9QYXJlbnRWaWV3JzsKdmFyIHBlcm1pc3Npb24gPSB7CiAgc3RhdGU6IHsKICAgIHJvdXRlczogW10sCiAgICBhZGRSb3V0ZXM6IFtdLAogICAgc2lkZWJhclJvdXRlcnM6IFtdCiAgfSwKICBtdXRhdGlvbnM6IHsKICAgIFNFVF9ST1VURVM6IGZ1bmN0aW9uIFNFVF9ST1VURVMoc3RhdGUsIHJvdXRlcykgewogICAgICBzdGF0ZS5hZGRSb3V0ZXMgPSByb3V0ZXM7CiAgICAgIHN0YXRlLnJvdXRlcyA9IGNvbnN0YW50Um91dGVzLmNvbmNhdChyb3V0ZXMpOwogICAgfSwKICAgIFNFVF9TSURFQkFSX1JPVVRFUlM6IGZ1bmN0aW9uIFNFVF9TSURFQkFSX1JPVVRFUlMoc3RhdGUsIHJvdXRlcnMpIHsKICAgICAgc3RhdGUuc2lkZWJhclJvdXRlcnMgPSBjb25zdGFudFJvdXRlcy5jb25jYXQocm91dGVycyk7CiAgICB9CiAgfSwKICBhY3Rpb25zOiB7CiAgICAvLyDnlJ/miJDot6/nlLEKICAgIEdlbmVyYXRlUm91dGVzOiBmdW5jdGlvbiBHZW5lcmF0ZVJvdXRlcyhfcmVmKSB7CiAgICAgIHZhciBjb21taXQgPSBfcmVmLmNvbW1pdDsKICAgICAgcmV0dXJuIG5ldyBQcm9taXNlKGZ1bmN0aW9uIChyZXNvbHZlKSB7CiAgICAgICAgLy8g5ZCR5ZCO56uv6K+35rGC6Lev55Sx5pWw5o2uCiAgICAgICAgZ2V0Um91dGVycygpLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgICAgdmFyIHNkYXRhID0gSlNPTi5wYXJzZShKU09OLnN0cmluZ2lmeShyZXMuZGF0YSkpOwogICAgICAgICAgdmFyIHJkYXRhID0gSlNPTi5wYXJzZShKU09OLnN0cmluZ2lmeShyZXMuZGF0YSkpOwogICAgICAgICAgdmFyIHNpZGViYXJSb3V0ZXMgPSBmaWx0ZXJBc3luY1JvdXRlcihzZGF0YSk7CiAgICAgICAgICB2YXIgcmV3cml0ZVJvdXRlcyA9IGZpbHRlckFzeW5jUm91dGVyKHJkYXRhLCB0cnVlKTsKICAgICAgICAgIHJld3JpdGVSb3V0ZXMucHVzaCh7CiAgICAgICAgICAgIHBhdGg6ICcqJywKICAgICAgICAgICAgcmVkaXJlY3Q6ICcvNDA0JywKICAgICAgICAgICAgaGlkZGVuOiB0cnVlCiAgICAgICAgICB9KTsKICAgICAgICAgIGNvbW1pdCgnU0VUX1JPVVRFUycsIHJld3JpdGVSb3V0ZXMpOwogICAgICAgICAgY29tbWl0KCdTRVRfU0lERUJBUl9ST1VURVJTJywgc2lkZWJhclJvdXRlcyk7CiAgICAgICAgICByZXNvbHZlKHJld3JpdGVSb3V0ZXMpOwogICAgICAgIH0pOwogICAgICB9KTsKICAgIH0KICB9Cn07CgovLyDpgY3ljoblkI7lj7DkvKDmnaXnmoTot6/nlLHlrZfnrKbkuLLvvIzovazmjaLkuLrnu4Tku7blr7nosaEKZnVuY3Rpb24gZmlsdGVyQXN5bmNSb3V0ZXIoYXN5bmNSb3V0ZXJNYXApIHsKICB2YXIgaXNSZXdyaXRlID0gYXJndW1lbnRzLmxlbmd0aCA+IDEgJiYgYXJndW1lbnRzWzFdICE9PSB1bmRlZmluZWQgPyBhcmd1bWVudHNbMV0gOiBmYWxzZTsKICByZXR1cm4gYXN5bmNSb3V0ZXJNYXAuZmlsdGVyKGZ1bmN0aW9uIChyb3V0ZSkgewogICAgaWYgKGlzUmV3cml0ZSAmJiByb3V0ZS5jaGlsZHJlbikgewogICAgICByb3V0ZS5jaGlsZHJlbiA9IGZpbHRlckNoaWxkcmVuKHJvdXRlLmNoaWxkcmVuKTsKICAgIH0KICAgIGlmIChyb3V0ZS5jb21wb25lbnQpIHsKICAgICAgLy8gTGF5b3V0IFBhcmVudFZpZXcg57uE5Lu254m55q6K5aSE55CGCiAgICAgIGlmIChyb3V0ZS5jb21wb25lbnQgPT09ICdMYXlvdXQnKSB7CiAgICAgICAgcm91dGUuY29tcG9uZW50ID0gTGF5b3V0OwogICAgICB9IGVsc2UgaWYgKHJvdXRlLmNvbXBvbmVudCA9PT0gJ1BhcmVudFZpZXcnKSB7CiAgICAgICAgcm91dGUuY29tcG9uZW50ID0gUGFyZW50VmlldzsKICAgICAgfSBlbHNlIHsKICAgICAgICByb3V0ZS5jb21wb25lbnQgPSBsb2FkVmlldyhyb3V0ZS5jb21wb25lbnQpOwogICAgICB9CiAgICB9CiAgICBpZiAocm91dGUuY2hpbGRyZW4gIT0gbnVsbCAmJiByb3V0ZS5jaGlsZHJlbiAmJiByb3V0ZS5jaGlsZHJlbi5sZW5ndGgpIHsKICAgICAgcm91dGUuY2hpbGRyZW4gPSBmaWx0ZXJBc3luY1JvdXRlcihyb3V0ZS5jaGlsZHJlbiwgcm91dGUsIGlzUmV3cml0ZSk7CiAgICB9CiAgICByZXR1cm4gdHJ1ZTsKICB9KTsKfQpmdW5jdGlvbiBmaWx0ZXJDaGlsZHJlbihjaGlsZHJlbk1hcCkgewogIHZhciBjaGlsZHJlbiA9IFtdOwogIGNoaWxkcmVuTWFwLmZvckVhY2goZnVuY3Rpb24gKGVsLCBpbmRleCkgewogICAgaWYgKGVsLmNoaWxkcmVuICYmIGVsLmNoaWxkcmVuLmxlbmd0aCkgewogICAgICBpZiAoZWwuY29tcG9uZW50ID09PSAnUGFyZW50VmlldycpIHsKICAgICAgICBlbC5jaGlsZHJlbi5mb3JFYWNoKGZ1bmN0aW9uIChjKSB7CiAgICAgICAgICBjLnBhdGggPSBlbC5wYXRoICsgJy8nICsgYy5wYXRoOwogICAgICAgICAgaWYgKGMuY2hpbGRyZW4gJiYgYy5jaGlsZHJlbi5sZW5ndGgpIHsKICAgICAgICAgICAgY2hpbGRyZW4gPSBjaGlsZHJlbi5jb25jYXQoZmlsdGVyQ2hpbGRyZW4oYy5jaGlsZHJlbiwgYykpOwogICAgICAgICAgICByZXR1cm47CiAgICAgICAgICB9CiAgICAgICAgICBjaGlsZHJlbi5wdXNoKGMpOwogICAgICAgIH0pOwogICAgICAgIHJldHVybjsKICAgICAgfQogICAgfQogICAgY2hpbGRyZW4gPSBjaGlsZHJlbi5jb25jYXQoZWwpOwogIH0pOwogIHJldHVybiBjaGlsZHJlbjsKfQpleHBvcnQgdmFyIGxvYWRWaWV3ID0gZnVuY3Rpb24gbG9hZFZpZXcodmlldykgewogIC8vIOi3r+eUseaHkuWKoOi9vQogIHJldHVybiBmdW5jdGlvbiAocmVzb2x2ZSkgewogICAgcmV0dXJuIHJlcXVpcmUoWyJAL3ZpZXdzLyIuY29uY2F0KHZpZXcpXSwgcmVzb2x2ZSk7CiAgfTsKfTsKZXhwb3J0IGRlZmF1bHQgcGVybWlzc2lvbjs="}, null]}