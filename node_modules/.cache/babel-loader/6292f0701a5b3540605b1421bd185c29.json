{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/flowable/task/process/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/flowable/task/process/index.vue", "mtime": 1662389810000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldERlcGxveW1lbnQsIGRlbERlcGxveW1lbnQsIGFkZERlcGxveW1lbnQsIHVwZGF0ZURlcGxveW1lbnQsIGV4cG9ydERlcGxveW1lbnQsIGZsb3dSZWNvcmQgfSBmcm9tICJAL2FwaS9mbG93YWJsZS9maW5pc2hlZCI7CmltcG9ydCB7IG15UHJvY2Vzc0xpc3QsIHN0b3BQcm9jZXNzIH0gZnJvbSAiQC9hcGkvZmxvd2FibGUvcHJvY2VzcyI7CmltcG9ydCB7IGxpc3REZWZpbml0aW9uIGFzIF9saXN0RGVmaW5pdGlvbiB9IGZyb20gIkAvYXBpL2Zsb3dhYmxlL2RlZmluaXRpb24iOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogIkRlcGxveSIsCiAgY29tcG9uZW50czoge30sCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIC8vIOmBrue9qeWxggogICAgICBsb2FkaW5nOiB0cnVlLAogICAgICBwcm9jZXNzTG9hZGluZzogdHJ1ZSwKICAgICAgLy8g6YCJ5Lit5pWw57uECiAgICAgIGlkczogW10sCiAgICAgIC8vIOmdnuWNleS4quemgeeUqAogICAgICBzaW5nbGU6IHRydWUsCiAgICAgIC8vIOmdnuWkmuS4quemgeeUqAogICAgICBtdWx0aXBsZTogdHJ1ZSwKICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2CiAgICAgIHNob3dTZWFyY2g6IHRydWUsCiAgICAgIC8vIOaAu+adoeaVsAogICAgICB0b3RhbDogMCwKICAgICAgcHJvY2Vzc1RvdGFsOiAwLAogICAgICAvLyDmiJHlj5HotbfnmoTmtYHnqIvliJfooajmlbDmja4KICAgICAgbXlQcm9jZXNzTGlzdDogW10sCiAgICAgIC8vIOW8ueWHuuWxguagh+mimAogICAgICB0aXRsZTogIiIsCiAgICAgIC8vIOaYr+WQpuaYvuekuuW8ueWHuuWxggogICAgICBvcGVuOiBmYWxzZSwKICAgICAgc3JjOiAiIiwKICAgICAgZGVmaW5pdGlvbkxpc3Q6IFtdLAogICAgICAvLyDmn6Xor6Llj4LmlbAKICAgICAgcXVlcnlQYXJhbXM6IHsKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICBuYW1lOiBudWxsLAogICAgICAgIGNhdGVnb3J5OiBudWxsLAogICAgICAgIGtleTogbnVsbCwKICAgICAgICB0ZW5hbnRJZDogbnVsbCwKICAgICAgICBkZXBsb3lUaW1lOiBudWxsLAogICAgICAgIGRlcml2ZWRGcm9tOiBudWxsLAogICAgICAgIGRlcml2ZWRGcm9tUm9vdDogbnVsbCwKICAgICAgICBwYXJlbnREZXBsb3ltZW50SWQ6IG51bGwsCiAgICAgICAgZW5naW5lVmVyc2lvbjogbnVsbAogICAgICB9LAogICAgICAvLyDooajljZXlj4LmlbAKICAgICAgZm9ybToge30sCiAgICAgIC8vIOihqOWNleagoemqjAogICAgICBydWxlczoge30KICAgIH07CiAgfSwKICBjcmVhdGVkOiBmdW5jdGlvbiBjcmVhdGVkKCkgewogICAgdGhpcy5nZXRMaXN0KCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICAvKiog5p+l6K+i5rWB56iL5a6a5LmJ5YiX6KGoICovZ2V0TGlzdDogZnVuY3Rpb24gZ2V0TGlzdCgpIHsKICAgICAgdmFyIF90aGlzID0gdGhpczsKICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsKICAgICAgbXlQcm9jZXNzTGlzdCh0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgIF90aGlzLm15UHJvY2Vzc0xpc3QgPSByZXNwb25zZS5kYXRhLnJlY29yZHM7CiAgICAgICAgX3RoaXMudG90YWwgPSByZXNwb25zZS5kYXRhLnRvdGFsOwogICAgICAgIF90aGlzLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgfSk7CiAgICB9LAogICAgLy8g5Y+W5raI5oyJ6ZKuCiAgICBjYW5jZWw6IGZ1bmN0aW9uIGNhbmNlbCgpIHsKICAgICAgdGhpcy5vcGVuID0gZmFsc2U7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgIH0sCiAgICAvLyDooajljZXph43nva4KICAgIHJlc2V0OiBmdW5jdGlvbiByZXNldCgpIHsKICAgICAgdGhpcy5mb3JtID0gewogICAgICAgIGlkOiBudWxsLAogICAgICAgIG5hbWU6IG51bGwsCiAgICAgICAgY2F0ZWdvcnk6IG51bGwsCiAgICAgICAga2V5OiBudWxsLAogICAgICAgIHRlbmFudElkOiBudWxsLAogICAgICAgIGRlcGxveVRpbWU6IG51bGwsCiAgICAgICAgZGVyaXZlZEZyb206IG51bGwsCiAgICAgICAgZGVyaXZlZEZyb21Sb290OiBudWxsLAogICAgICAgIHBhcmVudERlcGxveW1lbnRJZDogbnVsbCwKICAgICAgICBlbmdpbmVWZXJzaW9uOiBudWxsCiAgICAgIH07CiAgICAgIHRoaXMucmVzZXRGb3JtKCJmb3JtIik7CiAgICB9LAogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqL2hhbmRsZVF1ZXJ5OiBmdW5jdGlvbiBoYW5kbGVRdWVyeSgpIHsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsKICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICB9LAogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqL3Jlc2V0UXVlcnk6IGZ1bmN0aW9uIHJlc2V0UXVlcnkoKSB7CiAgICAgIHRoaXMucmVzZXRGb3JtKCJxdWVyeUZvcm0iKTsKICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOwogICAgfSwKICAgIC8vIOWkmumAieahhumAieS4reaVsOaNrgogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlOiBmdW5jdGlvbiBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7CiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgIHJldHVybiBpdGVtLmlkOwogICAgICB9KTsKICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoICE9PSAxOwogICAgICB0aGlzLm11bHRpcGxlID0gIXNlbGVjdGlvbi5sZW5ndGg7CiAgICB9LAogICAgLyoqIOaWsOWinuaMiemSruaTjeS9nCAqL2hhbmRsZUFkZDogZnVuY3Rpb24gaGFuZGxlQWRkKCkgewogICAgICB0aGlzLm9wZW4gPSB0cnVlOwogICAgICB0aGlzLnRpdGxlID0gIuWPkei1t+a1geeoiyI7CiAgICAgIHRoaXMubGlzdERlZmluaXRpb24oKTsKICAgIH0sCiAgICBsaXN0RGVmaW5pdGlvbjogZnVuY3Rpb24gbGlzdERlZmluaXRpb24oKSB7CiAgICAgIHZhciBfdGhpczIgPSB0aGlzOwogICAgICBfbGlzdERlZmluaXRpb24odGhpcy5xdWVyeVBhcmFtcykudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICBfdGhpczIuZGVmaW5pdGlvbkxpc3QgPSByZXNwb25zZS5kYXRhLnJlY29yZHM7CiAgICAgICAgX3RoaXMyLnByb2Nlc3NUb3RhbCA9IHJlc3BvbnNlLmRhdGEudG90YWw7CiAgICAgICAgX3RoaXMyLnByb2Nlc3NMb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiAg5Y+R6LW35rWB56iL55Sz6K+3ICovaGFuZGxlU3RhcnRQcm9jZXNzOiBmdW5jdGlvbiBoYW5kbGVTdGFydFByb2Nlc3Mocm93KSB7CiAgICAgIC8vIHRoaXMuJHJvdXRlci5wdXNoKHsgcGF0aDogJy9mbG93YWJsZS90YXNrL3JlY29yZC9pbmRleCcsCiAgICAgIC8vICAgcXVlcnk6IHsKICAgICAgLy8gICAgIGRlcGxveUlkOiByb3cuZGVwbG95bWVudElkLAogICAgICAvLyAgICAgcHJvY0RlZklkOnJvdy5pZCwKICAgICAgLy8gICAgIGZpbmlzaGVkOiB0cnVlCiAgICAgIC8vICAgICB9CiAgICAgIC8vIH0pCiAgICAgIHZhciBwYXRoOwogICAgICBpZiAocm93LmtleSA9PSAncHJvY2Vzc19wcm9qZWN0X3JlcG9ydCcpIHsKICAgICAgICBwYXRoID0gJy9wcm9qZWN0L3JlcG9ydC9mb3JtJzsKICAgICAgfSBlbHNlIGlmIChyb3cua2V5ID09ICdwcm9jZXNzX3VzZXJfcmVnJykgewogICAgICAgIHBhdGggPSAnL3N5c3RlbS91c2VyL2Zvcm0nOwogICAgICB9CiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHsKICAgICAgICBwYXRoOiBwYXRoCiAgICAgIH0pOwogICAgfSwKICAgIC8qKiAg5Y+W5raI5rWB56iL55Sz6K+3ICovaGFuZGxlU3RvcDogZnVuY3Rpb24gaGFuZGxlU3RvcChyb3cpIHsKICAgICAgdmFyIF90aGlzMyA9IHRoaXM7CiAgICAgIHZhciBwYXJhbXMgPSB7CiAgICAgICAgaW5zdGFuY2VJZDogcm93LnByb2NJbnNJZAogICAgICB9OwogICAgICBzdG9wUHJvY2VzcyhwYXJhbXMpLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgIF90aGlzMy5tc2dTdWNjZXNzKHJlcy5tc2cpOwogICAgICAgIF90aGlzMy5nZXRMaXN0KCk7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDmtYHnqIvmtYHovazorrDlvZUgKi9oYW5kbGVGbG93UmVjb3JkOiBmdW5jdGlvbiBoYW5kbGVGbG93UmVjb3JkKHJvdykgewogICAgICAvLyB0aGlzLiRyb3V0ZXIucHVzaCh7IHBhdGg6ICcvZmxvd2FibGUvdGFzay9yZWNvcmQvaW5kZXgnLAogICAgICAvLyAgIHF1ZXJ5OiB7CiAgICAgIC8vICAgICBwcm9jSW5zSWQ6IHJvdy5wcm9jSW5zSWQsCiAgICAgIC8vICAgICBkZXBsb3lJZDogcm93LmRlcGxveUlkLAogICAgICAvLyAgICAgdGFza0lkOiByb3cudGFza0lkLAogICAgICAvLyAgICAgZmluaXNoZWQ6IGZhbHNlCiAgICAgIC8vIH19KQoKICAgICAgdmFyIHBhdGg7CiAgICAgIC8vY29uc29sZS5sb2cocm93LnByb2NEZWZLZXkpCiAgICAgIGlmIChyb3cucHJvY0RlZktleSA9PSAncHJvY2Vzc19wcm9qZWN0X3JlcG9ydCcpIHsKICAgICAgICBwYXRoID0gJy9wcm9qZWN0L3JlcG9ydC9mb3JtJzsKICAgICAgfSBlbHNlIGlmIChyb3cucHJvY0RlZktleSA9PSAncHJvY2Vzc191c2VyX3JlZycpIHsKICAgICAgICBwYXRoID0gJy9zeXN0ZW0vdXNlci9mb3JtJzsKICAgICAgfQogICAgICB2YXIgZmluaXNoZWQgPSB0cnVlOwogICAgICB2YXIgZm9ybUVkaXQgPSBmYWxzZTsKICAgICAgaWYgKHJvdy5maW5pc2hUaW1lID09IG51bGwgJiYgcm93LmFzc2lnbmVlSWQgPT0gdGhpcy4kc3RvcmUuc3RhdGUudXNlci51c2VySWQpIHsKICAgICAgICBmaW5pc2hlZCA9IGZhbHNlOwogICAgICAgIGZvcm1FZGl0ID0gdHJ1ZTsKICAgICAgfQogICAgICB0aGlzLiRyb3V0ZXIucHVzaCh7CiAgICAgICAgcGF0aDogcGF0aCwKICAgICAgICBxdWVyeTogewogICAgICAgICAgYnVzaW5lc3NLZXk6IHJvdy5idXNpbmVzc0tleSwKICAgICAgICAgIHByb2NJbnNJZDogcm93LnByb2NJbnNJZCwKICAgICAgICAgIHRhc2tJZDogcm93LnRhc2tJZCwKICAgICAgICAgIGZpbmlzaGVkOiBmaW5pc2hlZCwKICAgICAgICAgIGZvcm1FZGl0OiBmb3JtRWRpdAogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLyoqIOS/ruaUueaMiemSruaTjeS9nCAqL2hhbmRsZVVwZGF0ZTogZnVuY3Rpb24gaGFuZGxlVXBkYXRlKHJvdykgewogICAgICB2YXIgX3RoaXM0ID0gdGhpczsKICAgICAgdGhpcy5yZXNldCgpOwogICAgICB2YXIgaWQgPSByb3cuaWQgfHwgdGhpcy5pZHM7CiAgICAgIGdldERlcGxveW1lbnQoaWQpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgX3RoaXM0LmZvcm0gPSByZXNwb25zZS5kYXRhOwogICAgICAgIF90aGlzNC5vcGVuID0gdHJ1ZTsKICAgICAgICBfdGhpczQudGl0bGUgPSAi5L+u5pS55rWB56iL5a6a5LmJIjsKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOaPkOS6pOaMiemSriAqL3N1Ym1pdEZvcm06IGZ1bmN0aW9uIHN1Ym1pdEZvcm0oKSB7CiAgICAgIHZhciBfdGhpczUgPSB0aGlzOwogICAgICB0aGlzLiRyZWZzWyJmb3JtIl0udmFsaWRhdGUoZnVuY3Rpb24gKHZhbGlkKSB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICBpZiAoX3RoaXM1LmZvcm0uaWQgIT0gbnVsbCkgewogICAgICAgICAgICB1cGRhdGVEZXBsb3ltZW50KF90aGlzNS5mb3JtKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgICAgICAgIF90aGlzNS5tc2dTdWNjZXNzKCLkv67mlLnmiJDlip8iKTsKICAgICAgICAgICAgICBfdGhpczUub3BlbiA9IGZhbHNlOwogICAgICAgICAgICAgIF90aGlzNS5nZXRMaXN0KCk7CiAgICAgICAgICAgIH0pOwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgYWRkRGVwbG95bWVudChfdGhpczUuZm9ybSkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICAgICAgICBfdGhpczUubXNnU3VjY2Vzcygi5paw5aKe5oiQ5YqfIik7CiAgICAgICAgICAgICAgX3RoaXM1Lm9wZW4gPSBmYWxzZTsKICAgICAgICAgICAgICBfdGhpczUuZ2V0TGlzdCgpOwogICAgICAgICAgICB9KTsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDliKDpmaTmjInpkq7mk43kvZwgKi9oYW5kbGVEZWxldGU6IGZ1bmN0aW9uIGhhbmRsZURlbGV0ZShyb3cpIHsKICAgICAgdmFyIF90aGlzNiA9IHRoaXM7CiAgICAgIHZhciBpZHMgPSByb3cuaWQgfHwgdGhpcy5pZHM7CiAgICAgIHRoaXMuJGNvbmZpcm0oJ+aYr+WQpuehruiupOWIoOmZpOa1geeoi+WumuS5iee8luWPt+S4uiInICsgaWRzICsgJyLnmoTmlbDmja7pobk/JywgIuitpuWRiiIsIHsKICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogIuehruWumiIsCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogIuWPlua2iCIsCiAgICAgICAgdHlwZTogIndhcm5pbmciCiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgIHJldHVybiBkZWxEZXBsb3ltZW50KGlkcyk7CiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgIF90aGlzNi5nZXRMaXN0KCk7CiAgICAgICAgX3RoaXM2Lm1zZ1N1Y2Nlc3MoIuWIoOmZpOaIkOWKnyIpOwogICAgICB9KTsKICAgIH0sCiAgICAvKiog5a+85Ye65oyJ6ZKu5pON5L2cICovaGFuZGxlRXhwb3J0OiBmdW5jdGlvbiBoYW5kbGVFeHBvcnQoKSB7CiAgICAgIHZhciBfdGhpczcgPSB0aGlzOwogICAgICB2YXIgcXVlcnlQYXJhbXMgPSB0aGlzLnF1ZXJ5UGFyYW1zOwogICAgICB0aGlzLiRjb25maXJtKCfmmK/lkKbnoa7orqTlr7zlh7rmiYDmnInmtYHnqIvlrprkuYnmlbDmja7pobk/JywgIuitpuWRiiIsIHsKICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogIuehruWumiIsCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogIuWPlua2iCIsCiAgICAgICAgdHlwZTogIndhcm5pbmciCiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgIHJldHVybiBleHBvcnREZXBsb3ltZW50KHF1ZXJ5UGFyYW1zKTsKICAgICAgfSkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICBfdGhpczcuZG93bmxvYWQocmVzcG9uc2UubXNnKTsKICAgICAgfSk7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["getDeployment", "delDeployment", "addDeployment", "updateDeployment", "exportDeployment", "flowRecord", "myProcessList", "stopProcess", "listDefinition", "name", "components", "data", "loading", "processLoading", "ids", "single", "multiple", "showSearch", "total", "processTotal", "title", "open", "src", "definitionList", "queryParams", "pageNum", "pageSize", "category", "key", "tenantId", "deployTime", "derivedFrom", "derivedFromRoot", "parentDeploymentId", "engineVersion", "form", "rules", "created", "getList", "methods", "_this", "then", "response", "records", "cancel", "reset", "id", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "map", "item", "length", "handleAdd", "_this2", "handleStartProcess", "row", "path", "$router", "push", "handleStop", "_this3", "params", "instanceId", "procInsId", "res", "msgSuccess", "msg", "handleFlowRecord", "procDef<PERSON>ey", "finished", "formEdit", "finishTime", "assigneeId", "$store", "state", "user", "userId", "query", "businessKey", "taskId", "handleUpdate", "_this4", "submitForm", "_this5", "$refs", "validate", "valid", "handleDelete", "_this6", "$confirm", "confirmButtonText", "cancelButtonText", "type", "handleExport", "_this7", "download"], "sources": ["src/views/flowable/task/process/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"任务名称\" prop=\"name\">\n        <el-input\n          v-model=\"queryParams.name\"\n          placeholder=\"请输入名称\"\n          clearable\n          size=\"small\"\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"开始时间\" prop=\"deployTime\">\n        <el-date-picker clearable size=\"small\"\n                        v-model=\"queryParams.deployTime\"\n                        type=\"date\"\n                        value-format=\"yyyy-MM-dd\"\n                        placeholder=\"选择时间\">\n        </el-date-picker>\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <!-- <el-col :span=\"1.5\">\n        <el-button\n          type=\"primary\"\n          plain\n          icon=\"el-icon-plus\"\n          size=\"mini\"\n          @click=\"handleAdd\"\n          v-hasPermi=\"['system:deployment:add']\"\n        >新增流程</el-button>\n      </el-col> -->\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"danger\"\n          plain\n          icon=\"el-icon-delete\"\n          size=\"mini\"\n          :disabled=\"multiple\"\n          @click=\"handleDelete\"\n          v-hasPermi=\"['system:deployment:remove']\"\n        >删除</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"warning\"\n          plain\n          icon=\"el-icon-download\"\n          size=\"mini\"\n          @click=\"handleExport\"\n          v-hasPermi=\"['system:deployment:export']\"\n        >导出</el-button>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <el-table v-loading=\"loading\" :data=\"myProcessList\" border @selection-change=\"handleSelectionChange\">\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column label=\"流程编号\" align=\"center\" prop=\"procInsId\" :show-overflow-tooltip=\"true\"/>\n      <el-table-column label=\"流程名称\" align=\"center\" prop=\"procDefName\" :show-overflow-tooltip=\"true\"/>\n      <el-table-column label=\"任务名称\" align=\"center\" prop=\"name\" :show-overflow-tooltip=\"true\"/>\n      <!-- <el-table-column label=\"流程类别\" align=\"center\" prop=\"category\" width=\"100px\" /> -->\n      <el-table-column label=\"流程版本\" align=\"center\" width=\"80px\">\n        <template slot-scope=\"scope\">\n          <el-tag size=\"medium\" >v{{ scope.row.procDefVersion }}</el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"提交时间\" align=\"center\" prop=\"createTime\" width=\"180\"/>\n      <el-table-column label=\"流程状态\" align=\"center\" width=\"100\">\n        <template slot-scope=\"scope\">\n          <el-tag v-if=\"scope.row.finishTime == null\" size=\"mini\">进行中</el-tag>\n          <el-tag type=\"success\" v-if=\"scope.row.finishTime != null\" size=\"mini\">已完成</el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"耗时\" align=\"center\" prop=\"duration\" width=\"180\"/>\n      <el-table-column label=\"当前节点\" align=\"center\" prop=\"taskName\"/>\n      <el-table-column label=\"办理\" align=\"center\">\n        <template slot-scope=\"scope\">\n          <label v-if=\"scope.row.assigneeName\">{{scope.row.assigneeName}} <el-tag type=\"info\" size=\"mini\">{{scope.row.deptName}}</el-tag></label>\n          <label v-if=\"scope.row.candidate\">{{scope.row.candidate}}</label>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"scope\">\n          <el-dropdown>\n            <span class=\"el-dropdown-link\">\n              更多操作<i class=\"el-icon-arrow-down el-icon--right\"></i>\n            </span>\n            <el-dropdown-menu slot=\"dropdown\">\n              <el-dropdown-item icon=\"el-icon-tickets\" @click.native=\"handleFlowRecord(scope.row)\">\n                详情\n              </el-dropdown-item>\n              <el-dropdown-item icon=\"el-icon-circle-close\" @click.native=\"handleStop(scope.row)\">\n                取消申请\n              </el-dropdown-item>\n              <el-dropdown-item icon=\"el-icon-delete\" @click.native=\"handleDelete(scope.row)\" v-hasPermi=\"['system:deployment:remove']\">\n                删除\n              </el-dropdown-item>\n            </el-dropdown-menu>\n          </el-dropdown>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 发起流程 -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"60%\" append-to-body>\n      <el-table v-loading=\"processLoading\" fit :data=\"definitionList\" border >\n        <el-table-column label=\"流程名称\" align=\"center\" prop=\"name\" />\n        <el-table-column label=\"流程版本\" align=\"center\">\n          <template slot-scope=\"scope\">\n            <el-tag size=\"medium\" >v{{ scope.row.version }}</el-tag>\n          </template>\n        </el-table-column>\n        <!-- <el-table-column label=\"流程分类\" align=\"center\" prop=\"category\" /> -->\n        <el-table-column label=\"操作\" align=\"center\" width=\"300\" class-name=\"small-padding fixed-width\">\n          <template slot-scope=\"scope\">\n            <el-button\n              size=\"mini\"\n              type=\"text\"\n              icon=\"el-icon-edit-outline\"\n              @click=\"handleStartProcess(scope.row)\"\n            >发起流程</el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n      <pagination\n        v-show=\"processTotal>0\"\n        :total=\"processTotal\"\n        :page.sync=\"queryParams.pageNum\"\n        :limit.sync=\"queryParams.pageSize\"\n        @pagination=\"listDefinition\"\n      />\n    </el-dialog>\n\n  </div>\n</template>\n\n<script>\nimport {\n  getDeployment,\n  delDeployment,\n  addDeployment,\n  updateDeployment,\n  exportDeployment,\n  flowRecord\n} from \"@/api/flowable/finished\";\nimport { myProcessList,stopProcess } from \"@/api/flowable/process\";\nimport {listDefinition} from \"@/api/flowable/definition\";\nexport default {\n  name: \"Deploy\",\n  components: {\n  },\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      processLoading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      processTotal:0,\n      // 我发起的流程列表数据\n      myProcessList: [],\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      open: false,\n      src: \"\",\n      definitionList:[],\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        name: null,\n        category: null,\n        key: null,\n        tenantId: null,\n        deployTime: null,\n        derivedFrom: null,\n        derivedFromRoot: null,\n        parentDeploymentId: null,\n        engineVersion: null\n      },\n      // 表单参数\n      form: {},\n      // 表单校验\n      rules: {\n      },\n    };\n  },\n  created() {\n    this.getList();\n  },\n  methods: {\n    /** 查询流程定义列表 */\n    getList() {\n      this.loading = true;\n      myProcessList(this.queryParams).then(response => {\n        this.myProcessList = response.data.records;\n        this.total = response.data.total;\n        this.loading = false;\n      });\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        id: null,\n        name: null,\n        category: null,\n        key: null,\n        tenantId: null,\n        deployTime: null,\n        derivedFrom: null,\n        derivedFromRoot: null,\n        parentDeploymentId: null,\n        engineVersion: null\n      };\n      this.resetForm(\"form\");\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.id)\n      this.single = selection.length!==1\n      this.multiple = !selection.length\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.open = true;\n      this.title = \"发起流程\";\n      this.listDefinition();\n    },\n    listDefinition(){\n      listDefinition(this.queryParams).then(response => {\n        this.definitionList = response.data.records;\n        this.processTotal = response.data.total;\n        this.processLoading = false;\n      });\n    },\n    /**  发起流程申请 */\n    handleStartProcess(row){\n      // this.$router.push({ path: '/flowable/task/record/index',\n      //   query: {\n      //     deployId: row.deploymentId,\n      //     procDefId:row.id,\n      //     finished: true\n      //     }\n      // })\n      var path;\n      if(row.key == 'process_project_report'){\n        path = '/project/report/form';\n      }else if(row.key == 'process_user_reg'){\n        path = '/system/user/form';\n      }\n\n      this.$router.push({ path: path})\n    },\n    /**  取消流程申请 */\n    handleStop(row){\n      const params = {\n        instanceId: row.procInsId\n      }\n      stopProcess(params).then( res => {\n        this.msgSuccess(res.msg);\n        this.getList();\n      });\n    },\n    /** 流程流转记录 */\n    handleFlowRecord(row){\n      // this.$router.push({ path: '/flowable/task/record/index',\n      //   query: {\n      //     procInsId: row.procInsId,\n      //     deployId: row.deployId,\n      //     taskId: row.taskId,\n      //     finished: false\n      // }})\n\n      var path;\n      //console.log(row.procDefKey)\n      if(row.procDefKey == 'process_project_report'){\n        path = '/project/report/form';\n      }else if(row.procDefKey == 'process_user_reg'){\n        path = '/system/user/form';\n      }\n\n      let finished = true;\n      let formEdit = false;\n      if(row.finishTime == null && row.assigneeId == this.$store.state.user.userId){\n        finished = false;\n        formEdit = true;\n      }\n      this.$router.push({ path: path,\n        query: {\n          businessKey: row.businessKey,\n          procInsId: row.procInsId,\n          taskId: row.taskId,\n          finished: finished,\n          formEdit: formEdit\n      }})\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset();\n      const id = row.id || this.ids\n      getDeployment(id).then(response => {\n        this.form = response.data;\n        this.open = true;\n        this.title = \"修改流程定义\";\n      });\n    },\n    /** 提交按钮 */\n    submitForm() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          if (this.form.id != null) {\n            updateDeployment(this.form).then(response => {\n              this.msgSuccess(\"修改成功\");\n              this.open = false;\n              this.getList();\n            });\n          } else {\n            addDeployment(this.form).then(response => {\n              this.msgSuccess(\"新增成功\");\n              this.open = false;\n              this.getList();\n            });\n          }\n        }\n      });\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const ids = row.id || this.ids;\n      this.$confirm('是否确认删除流程定义编号为\"' + ids + '\"的数据项?', \"警告\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      }).then(function() {\n        return delDeployment(ids);\n      }).then(() => {\n        this.getList();\n        this.msgSuccess(\"删除成功\");\n      })\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      const queryParams = this.queryParams;\n      this.$confirm('是否确认导出所有流程定义数据项?', \"警告\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      }).then(function() {\n        return exportDeployment(queryParams);\n      }).then(response => {\n        this.download(response.msg);\n      })\n    }\n  }\n};\n</script>\n\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuJA,SACAA,aAAA,EACAC,aAAA,EACAC,aAAA,EACAC,gBAAA,EACAC,gBAAA,EACAC,UAAA,QACA;AACA,SAAAC,aAAA,EAAAC,WAAA;AACA,SAAAC,cAAA,IAAAA,eAAA;AACA;EACAC,IAAA;EACAC,UAAA,GACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACAC,cAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACAC,YAAA;MACA;MACAb,aAAA;MACA;MACAc,KAAA;MACA;MACAC,IAAA;MACAC,GAAA;MACAC,cAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAjB,IAAA;QACAkB,QAAA;QACAC,GAAA;QACAC,QAAA;QACAC,UAAA;QACAC,WAAA;QACAC,eAAA;QACAC,kBAAA;QACAC,aAAA;MACA;MACA;MACAC,IAAA;MACA;MACAC,KAAA,GACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA,eACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAA5B,OAAA;MACAN,aAAA,MAAAkB,WAAA,EAAAiB,IAAA,WAAAC,QAAA;QACAF,KAAA,CAAAlC,aAAA,GAAAoC,QAAA,CAAA/B,IAAA,CAAAgC,OAAA;QACAH,KAAA,CAAAtB,KAAA,GAAAwB,QAAA,CAAA/B,IAAA,CAAAO,KAAA;QACAsB,KAAA,CAAA5B,OAAA;MACA;IACA;IACA;IACAgC,MAAA,WAAAA,OAAA;MACA,KAAAvB,IAAA;MACA,KAAAwB,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAAV,IAAA;QACAW,EAAA;QACArC,IAAA;QACAkB,QAAA;QACAC,GAAA;QACAC,QAAA;QACAC,UAAA;QACAC,WAAA;QACAC,eAAA;QACAC,kBAAA;QACAC,aAAA;MACA;MACA,KAAAa,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAAxB,WAAA,CAAAC,OAAA;MACA,KAAAa,OAAA;IACA;IACA,aACAW,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAAC,WAAA;IACA;IACA;IACAE,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAArC,GAAA,GAAAqC,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAP,EAAA;MAAA;MACA,KAAA/B,MAAA,GAAAoC,SAAA,CAAAG,MAAA;MACA,KAAAtC,QAAA,IAAAmC,SAAA,CAAAG,MAAA;IACA;IACA,aACAC,SAAA,WAAAA,UAAA;MACA,KAAAlC,IAAA;MACA,KAAAD,KAAA;MACA,KAAAZ,cAAA;IACA;IACAA,cAAA,WAAAA,eAAA;MAAA,IAAAgD,MAAA;MACAhD,eAAA,MAAAgB,WAAA,EAAAiB,IAAA,WAAAC,QAAA;QACAc,MAAA,CAAAjC,cAAA,GAAAmB,QAAA,CAAA/B,IAAA,CAAAgC,OAAA;QACAa,MAAA,CAAArC,YAAA,GAAAuB,QAAA,CAAA/B,IAAA,CAAAO,KAAA;QACAsC,MAAA,CAAA3C,cAAA;MACA;IACA;IACA,cACA4C,kBAAA,WAAAA,mBAAAC,GAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAAC,IAAA;MACA,IAAAD,GAAA,CAAA9B,GAAA;QACA+B,IAAA;MACA,WAAAD,GAAA,CAAA9B,GAAA;QACA+B,IAAA;MACA;MAEA,KAAAC,OAAA,CAAAC,IAAA;QAAAF,IAAA,EAAAA;MAAA;IACA;IACA,cACAG,UAAA,WAAAA,WAAAJ,GAAA;MAAA,IAAAK,MAAA;MACA,IAAAC,MAAA;QACAC,UAAA,EAAAP,GAAA,CAAAQ;MACA;MACA3D,WAAA,CAAAyD,MAAA,EAAAvB,IAAA,WAAA0B,GAAA;QACAJ,MAAA,CAAAK,UAAA,CAAAD,GAAA,CAAAE,GAAA;QACAN,MAAA,CAAAzB,OAAA;MACA;IACA;IACA,aACAgC,gBAAA,WAAAA,iBAAAZ,GAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA,IAAAC,IAAA;MACA;MACA,IAAAD,GAAA,CAAAa,UAAA;QACAZ,IAAA;MACA,WAAAD,GAAA,CAAAa,UAAA;QACAZ,IAAA;MACA;MAEA,IAAAa,QAAA;MACA,IAAAC,QAAA;MACA,IAAAf,GAAA,CAAAgB,UAAA,YAAAhB,GAAA,CAAAiB,UAAA,SAAAC,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,MAAA;QACAP,QAAA;QACAC,QAAA;MACA;MACA,KAAAb,OAAA,CAAAC,IAAA;QAAAF,IAAA,EAAAA,IAAA;QACAqB,KAAA;UACAC,WAAA,EAAAvB,GAAA,CAAAuB,WAAA;UACAf,SAAA,EAAAR,GAAA,CAAAQ,SAAA;UACAgB,MAAA,EAAAxB,GAAA,CAAAwB,MAAA;UACAV,QAAA,EAAAA,QAAA;UACAC,QAAA,EAAAA;QACA;MAAA;IACA;IACA,aACAU,YAAA,WAAAA,aAAAzB,GAAA;MAAA,IAAA0B,MAAA;MACA,KAAAvC,KAAA;MACA,IAAAC,EAAA,GAAAY,GAAA,CAAAZ,EAAA,SAAAhC,GAAA;MACAd,aAAA,CAAA8C,EAAA,EAAAL,IAAA,WAAAC,QAAA;QACA0C,MAAA,CAAAjD,IAAA,GAAAO,QAAA,CAAA/B,IAAA;QACAyE,MAAA,CAAA/D,IAAA;QACA+D,MAAA,CAAAhE,KAAA;MACA;IACA;IACA,WACAiE,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAH,MAAA,CAAAnD,IAAA,CAAAW,EAAA;YACA3C,gBAAA,CAAAmF,MAAA,CAAAnD,IAAA,EAAAM,IAAA,WAAAC,QAAA;cACA4C,MAAA,CAAAlB,UAAA;cACAkB,MAAA,CAAAjE,IAAA;cACAiE,MAAA,CAAAhD,OAAA;YACA;UACA;YACApC,aAAA,CAAAoF,MAAA,CAAAnD,IAAA,EAAAM,IAAA,WAAAC,QAAA;cACA4C,MAAA,CAAAlB,UAAA;cACAkB,MAAA,CAAAjE,IAAA;cACAiE,MAAA,CAAAhD,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACAoD,YAAA,WAAAA,aAAAhC,GAAA;MAAA,IAAAiC,MAAA;MACA,IAAA7E,GAAA,GAAA4C,GAAA,CAAAZ,EAAA,SAAAhC,GAAA;MACA,KAAA8E,QAAA,oBAAA9E,GAAA;QACA+E,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAAtD,IAAA;QACA,OAAAxC,aAAA,CAAAa,GAAA;MACA,GAAA2B,IAAA;QACAkD,MAAA,CAAArD,OAAA;QACAqD,MAAA,CAAAvB,UAAA;MACA;IACA;IACA,aACA4B,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,IAAAzE,WAAA,QAAAA,WAAA;MACA,KAAAoE,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAAtD,IAAA;QACA,OAAArC,gBAAA,CAAAoB,WAAA;MACA,GAAAiB,IAAA,WAAAC,QAAA;QACAuD,MAAA,CAAAC,QAAA,CAAAxD,QAAA,CAAA2B,GAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}