{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Screenfull/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Screenfull/index.vue", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBzY3JlZW5mdWxsIGZyb20gJ3NjcmVlbmZ1bGwnOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ1NjcmVlbmZ1bGwnLAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBpc0Z1bGxzY3JlZW46IGZhbHNlCiAgICB9OwogIH0sCiAgbW91bnRlZDogZnVuY3Rpb24gbW91bnRlZCgpIHsKICAgIHRoaXMuaW5pdCgpOwogIH0sCiAgYmVmb3JlRGVzdHJveTogZnVuY3Rpb24gYmVmb3JlRGVzdHJveSgpIHsKICAgIHRoaXMuZGVzdHJveSgpOwogIH0sCiAgbWV0aG9kczogewogICAgY2xpY2s6IGZ1bmN0aW9uIGNsaWNrKCkgewogICAgICBpZiAoIXNjcmVlbmZ1bGwuaXNFbmFibGVkKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICBtZXNzYWdlOiAn5L2g55qE5rWP6KeI5Zmo5LiN5pSv5oyB5YWo5bGPJywKICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJwogICAgICAgIH0pOwogICAgICAgIHJldHVybiBmYWxzZTsKICAgICAgfQogICAgICBzY3JlZW5mdWxsLnRvZ2dsZSgpOwogICAgfSwKICAgIGNoYW5nZTogZnVuY3Rpb24gY2hhbmdlKCkgewogICAgICB0aGlzLmlzRnVsbHNjcmVlbiA9IHNjcmVlbmZ1bGwuaXNGdWxsc2NyZWVuOwogICAgfSwKICAgIGluaXQ6IGZ1bmN0aW9uIGluaXQoKSB7CiAgICAgIGlmIChzY3JlZW5mdWxsLmlzRW5hYmxlZCkgewogICAgICAgIHNjcmVlbmZ1bGwub24oJ2NoYW5nZScsIHRoaXMuY2hhbmdlKTsKICAgICAgfQogICAgfSwKICAgIGRlc3Ryb3k6IGZ1bmN0aW9uIGRlc3Ryb3koKSB7CiAgICAgIGlmIChzY3JlZW5mdWxsLmlzRW5hYmxlZCkgewogICAgICAgIHNjcmVlbmZ1bGwub2ZmKCdjaGFuZ2UnLCB0aGlzLmNoYW5nZSk7CiAgICAgIH0KICAgIH0KICB9Cn07"}, {"version": 3, "names": ["screenfull", "name", "data", "isFullscreen", "mounted", "init", "<PERSON><PERSON><PERSON><PERSON>", "destroy", "methods", "click", "isEnabled", "$message", "message", "type", "toggle", "change", "on", "off"], "sources": ["src/components/Screenfull/index.vue"], "sourcesContent": ["<template>\n  <div>\n    <svg-icon :icon-class=\"isFullscreen?'exit-fullscreen':'fullscreen'\" @click=\"click\" />\n  </div>\n</template>\n\n<script>\nimport screenfull from 'screenfull'\n\nexport default {\n  name: 'Screenfull',\n  data() {\n    return {\n      isFullscreen: false\n    }\n  },\n  mounted() {\n    this.init()\n  },\n  beforeDestroy() {\n    this.destroy()\n  },\n  methods: {\n    click() {\n      if (!screenfull.isEnabled) {\n        this.$message({ message: '你的浏览器不支持全屏', type: 'warning' })\n        return false\n      }\n      screenfull.toggle()\n    },\n    change() {\n      this.isFullscreen = screenfull.isFullscreen\n    },\n    init() {\n      if (screenfull.isEnabled) {\n        screenfull.on('change', this.change)\n      }\n    },\n    destroy() {\n      if (screenfull.isEnabled) {\n        screenfull.off('change', this.change)\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.screenfull-svg {\n  display: inline-block;\n  cursor: pointer;\n  fill: #5a5e66;;\n  width: 20px;\n  height: 20px;\n  vertical-align: 10px;\n}\n</style>\n"], "mappings": ";;;;;;;AAOA,OAAAA,UAAA;AAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,YAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,IAAA;EACA;EACAC,aAAA,WAAAA,cAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACAC,KAAA,WAAAA,MAAA;MACA,KAAAT,UAAA,CAAAU,SAAA;QACA,KAAAC,QAAA;UAAAC,OAAA;UAAAC,IAAA;QAAA;QACA;MACA;MACAb,UAAA,CAAAc,MAAA;IACA;IACAC,MAAA,WAAAA,OAAA;MACA,KAAAZ,YAAA,GAAAH,UAAA,CAAAG,YAAA;IACA;IACAE,IAAA,WAAAA,KAAA;MACA,IAAAL,UAAA,CAAAU,SAAA;QACAV,UAAA,CAAAgB,EAAA,gBAAAD,MAAA;MACA;IACA;IACAR,OAAA,WAAAA,QAAA;MACA,IAAAP,UAAA,CAAAU,SAAA;QACAV,UAAA,CAAAiB,GAAA,gBAAAF,MAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}