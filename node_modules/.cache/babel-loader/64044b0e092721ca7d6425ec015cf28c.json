{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Process/BpmData.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Process/BpmData.js", "mtime": 1650120220000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["BpmData", "_classCallCheck", "controls", "init", "_createClass", "key", "value", "action", "title", "getControl", "result", "filter", "item", "default"], "sources": ["/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Process/BpmData.js"], "sourcesContent": ["/**\n * 存储流程设计相关参数\n */\nexport default class BpmData {\n  constructor() {\n    this.controls = [] // 设计器控件\n    this.init()\n  }\n\n  init() {\n    this.controls = [\n      {\n        action: 'hand-tool',\n        title: '抓手'\n      },\n      {\n        action: 'lasso-tool',\n        title: '套手'\n      },\n      {\n        action: 'create.start-event',\n        title: '开始'\n      },\n      {\n        action: 'create.intermediate-event',\n        title: '中间'\n      },\n      {\n        action: 'create.end-event',\n        title: '结束'\n      },\n      {\n        action: 'create.exclusive-gateway',\n        title: '互斥网关'\n      },\n      {\n        action: 'create.parallel-gateway',\n        title: '合并网关'\n      },\n      {\n        action: 'create.inclusive-gateway',\n        title: '融合网关'\n      },\n      {\n        action: 'create.task',\n        title: '任务'\n      },\n      {\n        action: 'create.user-task',\n        title: '用户任务'\n      },\n      {\n        action: 'create.user-sign-task',\n        title: '会签任务'\n      },\n      {\n        action: 'create.subprocess-expanded',\n        title: '子流程'\n      },\n      {\n        action: 'create.data-object',\n        title: '数据对象'\n      },\n      {\n        action: 'create.data-store',\n        title: '数据存储'\n      },\n      {\n        action: 'create.participant-expanded',\n        title: '扩展流程'\n      },\n      {\n        action: 'create.group',\n        title: '分组'\n      }\n    ]\n  }\n\n  //  获取控件配置信息\n  getControl(action) {\n    const result = this.controls.filter(item => item.action === action)\n    return result[0] || {}\n  }\n}\n"], "mappings": ";;;;;;AAAA;AACA;AACA;AAFA,IAGqBA,OAAO;EAC1B,SAAAA,QAAA,EAAc;IAAAC,eAAA,OAAAD,OAAA;IACZ,IAAI,CAACE,QAAQ,GAAG,EAAE,EAAC;IACnB,IAAI,CAACC,IAAI,CAAC,CAAC;EACb;EAAC,OAAAC,YAAA,CAAAJ,OAAA;IAAAK,GAAA;IAAAC,KAAA,EAED,SAAAH,KAAA,EAAO;MACL,IAAI,CAACD,QAAQ,GAAG,CACd;QACEK,MAAM,EAAE,WAAW;QACnBC,KAAK,EAAE;MACT,CAAC,EACD;QACED,MAAM,EAAE,YAAY;QACpBC,KAAK,EAAE;MACT,CAAC,EACD;QACED,MAAM,EAAE,oBAAoB;QAC5BC,KAAK,EAAE;MACT,CAAC,EACD;QACED,MAAM,EAAE,2BAA2B;QACnCC,KAAK,EAAE;MACT,CAAC,EACD;QACED,MAAM,EAAE,kBAAkB;QAC1BC,KAAK,EAAE;MACT,CAAC,EACD;QACED,MAAM,EAAE,0BAA0B;QAClCC,KAAK,EAAE;MACT,CAAC,EACD;QACED,MAAM,EAAE,yBAAyB;QACjCC,KAAK,EAAE;MACT,CAAC,EACD;QACED,MAAM,EAAE,0BAA0B;QAClCC,KAAK,EAAE;MACT,CAAC,EACD;QACED,MAAM,EAAE,aAAa;QACrBC,KAAK,EAAE;MACT,CAAC,EACD;QACED,MAAM,EAAE,kBAAkB;QAC1BC,KAAK,EAAE;MACT,CAAC,EACD;QACED,MAAM,EAAE,uBAAuB;QAC/BC,KAAK,EAAE;MACT,CAAC,EACD;QACED,MAAM,EAAE,4BAA4B;QACpCC,KAAK,EAAE;MACT,CAAC,EACD;QACED,MAAM,EAAE,oBAAoB;QAC5BC,KAAK,EAAE;MACT,CAAC,EACD;QACED,MAAM,EAAE,mBAAmB;QAC3BC,KAAK,EAAE;MACT,CAAC,EACD;QACED,MAAM,EAAE,6BAA6B;QACrCC,KAAK,EAAE;MACT,CAAC,EACD;QACED,MAAM,EAAE,cAAc;QACtBC,KAAK,EAAE;MACT,CAAC,CACF;IACH;;IAEA;EAAA;IAAAH,GAAA;IAAAC,KAAA,EACA,SAAAG,WAAWF,MAAM,EAAE;MACjB,IAAMG,MAAM,GAAG,IAAI,CAACR,QAAQ,CAACS,MAAM,CAAC,UAAAC,IAAI;QAAA,OAAIA,IAAI,CAACL,MAAM,KAAKA,MAAM;MAAA,EAAC;MACnE,OAAOG,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IACxB;EAAC;AAAA;AAAA,SA/EkBV,OAAO,IAAAa,OAAA", "ignoreList": []}]}