{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Process/common/parseElement.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Process/common/parseElement.js", "mtime": 1641277326000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js", "mtime": 1751171659043}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}