{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/map.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/map.vue", "mtime": 1660748060000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["echarts", "mapData", "indexReportCount", "name", "data", "version", "mounted", "console", "log", "chartInit", "_this", "window", "onresize", "mapResize", "methods", "goTarget", "href", "open", "oMap", "init", "document", "getElementById", "getDom", "style", "height", "innerHeight", "resize", "_this2", "registerMap", "option", "title", "text", "left", "tooltip", "trigger", "showDelay", "transitionDuration", "visualMap", "show", "min", "max", "inRange", "color", "calculable", "series", "type", "roam", "center", "zoom", "map", "emphasis", "label", "itemStyle", "normal", "borderWidth", "borderColor", "areaColor", "then", "response", "slice", "setOption"], "sources": ["src/views/map.vue"], "sourcesContent": ["<template>\n  <div>\n    <div class=\"mapBox\" id=\"mapChar\" style=\"height:600px; width:100%;\"></div>\n    <div class=\"counting\">\n      <span>城市项目报备统计</span>\n      <el-row v-for=\"(item,index) in data\" :key=\"index\"><el-col><span class=\"name\">{{item.name}}</span> <span class=\"value\">{{item.value}}</span></el-col></el-row>\n    </div>\n  </div>\n</template>\n\n<script>\nimport * as echarts from \"echarts\";\nimport 'echarts/map/js/china.js' // 引入中国地图数据\nimport mapData from \"./china.json\";\nimport {indexReportCount} from \"@/api/project/report\";\n\nexport default {\n  name: \"index\",\n  data() {\n    return {\n      // 版本号\n      version: \"3.3.0\",\n      data:[]\n    };\n  },\n  mounted() {\n    \n    console.log(\"=======mounted== map=>>>\")\n    this.chartInit();\n    //this.map();\n    let _this = this;//赋值vue的this\n    window.onresize = ()=>{\n      //调用methods中的事件\n      _this.mapResize();\n    }\n    \n  },\n  methods: {\n    goTarget(href) {\n      window.open(href, \"_blank\");\n    },\n    mapResize(){\n      const oMap = echarts.init(document.getElementById(\"mapChar\"));\n      oMap.getDom().style.height = (window.innerHeight-86) +'px';//600 + \"px\";\n      oMap.resize();\n    },\n    chartInit() {\n      //初始Echarts实例对象\n      const oMap = echarts.init(document.getElementById(\"mapChar\"));\n\n      //指定加载省、市、县、区（注：由于这里没有用模块化方式导入，所以把zunyi.json文件改为zunyi.js文件，并在里面用一个zunyi常量来引入的）\n      echarts.registerMap(\"china\", mapData);\n      \n      var option = {\n        title: {\n          text: \"海佳彩亮项目管理系统\",\n          left: \"left\",\n        },\n        tooltip: {\n          trigger: \"item\",\n          showDelay: 0,\n          transitionDuration: 0.2,\n        },\n        visualMap: {\n          show:false,\n          left: \"right\",\n          min: 10,\n          max: 1000,\n          inRange: {\n            color: [\n              \"#D5DCE4\",\n              \"#AED1FF\",\n              \"#5E9BEB\",\n              \"#1A74EB\",\n            ],\n          },\n          text: [\"High\", \"Low\"],\n          calculable: true,\n        },\n        \n        series: [\n          {\n            name: \"项目报备\",\n            type: \"map\",\n            roam: false,//缩放\n            center: [104.114129, 37.550339],//当前视角的中心点\n            zoom: 1.4,//当前视角的缩放比例\n            map: \"china\",\n            emphasis: {\n              label: {\n                show: true,\n              },\n            },\n            data: [\n              // { name: \"福建\", value: 4822023 },\n              // { name: \"河北\", value: 73149 },\n              // { name: \"山西\", value: 655255 },\n              // { name: \"辽宁\", value: 291031 },\n              // { name: \"吉林\", value: 3801430 },\n              // { name: \"黑龙江\", value: 5187582 },\n              // { name: \"江苏\", value: 3590347 },\n              // { name: \"浙江\", value: 917092 },\n              // { name: \"安徽\", value: 632323 },\n              // { name: \"江西\", value: 19317568 },\n              // { name: \"山东\", value: 999945 },\n              // { name: \"河南\", value: 1392313 },\n              // { name: \"湖北\", value: 15728 },\n              // { name: \"湖南\", value: 12875255 },\n              // { name: \"广东\", value: 657334 },\n              // { name: \"海南\", value: 3074186 },\n              // { name: \"四川\", value: 2885905 },\n              // { name: \"贵州\", value: 4380415 },\n              // { name: \"云南\", value: 461893 },\n              // { name: \"陕西\", value: 1329192 },\n              // { name: \"甘肃\", value: 54563 },\n              // { name: \"青海\", value: 666144 },\n              // { name: \"台湾\", value: 9883360 },\n              // { name: \"北京\", value: 5379139 },\n              // { name: \"上海\", value: 2984926 },\n              // { name: \"重庆\", value: 6021988 },\n              // { name: \"天津\", value: 1005141 },\n              // { name: \"广西\", value: 1855525 },\n              // { name: \"宁夏\", value: 2758931 },\n              // { name: \"西藏\", value: 1320718 },\n              // { name: \"新疆\", value: 8864590 },\n              // { name: \"内蒙古\", value: 2085538 },\n              // { name: \"香港\", value: 19570261 },\n              // { name: \"澳门\", value: 9752073 },\n            ],\n            itemStyle: {\n              normal: {\n                borderWidth: 1.5,//边际线大小\n                //areaColor: '#323c48',\n                borderColor: '#fff'\n              },\n              emphasis: {//鼠标移入高亮显颜色\n                areaColor: '#FFC278'\n              }\n            },\n          },\n        ],\n      };\n      indexReportCount({}).then((response) => {\n        option.series[0].data = response.data;\n        this.data = response.data.slice(0, 10);\n        oMap.setOption(option);\n      })\n      \n    },\n    \n  },\n};\n</script>\n\n<style scoped lang=\"scss\">\n.mapBox{\n  background: url(\"../assets/images/home-bg.png\") center;\n  background-size:100%;\n\tbackground-repeat:no-repeat;\n}\n.counting{\n  position: absolute;\n  top: 100px;\n  left: 50px;\n}\n.counting .el-row{\n  margin-top: 15px;\n}\n.counting .el-col{\n  display: flex;\n  justify-content: space-between;\n  padding-bottom: 10px;\n  border-bottom: solid 1px #D7D7EB;\n}\n</style>\n\n"], "mappings": ";;;;;;;;;;;AAWA,YAAAA,OAAA;AACA;AACA,OAAAC,OAAA;AACA,SAAAC,gBAAA;AAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACAD,IAAA;IACA;EACA;EACAE,OAAA,WAAAA,QAAA;IAEAC,OAAA,CAAAC,GAAA;IACA,KAAAC,SAAA;IACA;IACA,IAAAC,KAAA;IACAC,MAAA,CAAAC,QAAA;MACA;MACAF,KAAA,CAAAG,SAAA;IACA;EAEA;EACAC,OAAA;IACAC,QAAA,WAAAA,SAAAC,IAAA;MACAL,MAAA,CAAAM,IAAA,CAAAD,IAAA;IACA;IACAH,SAAA,WAAAA,UAAA;MACA,IAAAK,IAAA,GAAAlB,OAAA,CAAAmB,IAAA,CAAAC,QAAA,CAAAC,cAAA;MACAH,IAAA,CAAAI,MAAA,GAAAC,KAAA,CAAAC,MAAA,GAAAb,MAAA,CAAAc,WAAA;MACAP,IAAA,CAAAQ,MAAA;IACA;IACAjB,SAAA,WAAAA,UAAA;MAAA,IAAAkB,MAAA;MACA;MACA,IAAAT,IAAA,GAAAlB,OAAA,CAAAmB,IAAA,CAAAC,QAAA,CAAAC,cAAA;;MAEA;MACArB,OAAA,CAAA4B,WAAA,UAAA3B,OAAA;MAEA,IAAA4B,MAAA;QACAC,KAAA;UACAC,IAAA;UACAC,IAAA;QACA;QACAC,OAAA;UACAC,OAAA;UACAC,SAAA;UACAC,kBAAA;QACA;QACAC,SAAA;UACAC,IAAA;UACAN,IAAA;UACAO,GAAA;UACAC,GAAA;UACAC,OAAA;YACAC,KAAA,GACA,WACA,WACA,WACA;UAEA;UACAX,IAAA;UACAY,UAAA;QACA;QAEAC,MAAA,GACA;UACAzC,IAAA;UACA0C,IAAA;UACAC,IAAA;UAAA;UACAC,MAAA;UAAA;UACAC,IAAA;UAAA;UACAC,GAAA;UACAC,QAAA;YACAC,KAAA;cACAb,IAAA;YACA;UACA;UACAlC,IAAA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UAAA,CACA;UACAgD,SAAA;YACAC,MAAA;cACAC,WAAA;cAAA;cACA;cACAC,WAAA;YACA;YACAL,QAAA;cAAA;cACAM,SAAA;YACA;UACA;QACA;MAEA;MACAtD,gBAAA,KAAAuD,IAAA,WAAAC,QAAA;QACA7B,MAAA,CAAAe,MAAA,IAAAxC,IAAA,GAAAsD,QAAA,CAAAtD,IAAA;QACAuB,MAAA,CAAAvB,IAAA,GAAAsD,QAAA,CAAAtD,IAAA,CAAAuD,KAAA;QACAzC,IAAA,CAAA0C,SAAA,CAAA/B,MAAA;MACA;IAEA;EAEA;AACA", "ignoreList": []}]}