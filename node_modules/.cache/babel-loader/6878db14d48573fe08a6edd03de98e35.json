{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/tool/build/IconsDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/tool/build/IconsDialog.vue", "mtime": 1641277326000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBpY29uTGlzdCBmcm9tICdAL3V0aWxzL2dlbmVyYXRvci9pY29uLmpzb24nOwp2YXIgb3JpZ2luTGlzdCA9IGljb25MaXN0Lm1hcChmdW5jdGlvbiAobmFtZSkgewogIHJldHVybiAiZWwtaWNvbi0iLmNvbmNhdChuYW1lKTsKfSk7CmV4cG9ydCBkZWZhdWx0IHsKICBpbmhlcml0QXR0cnM6IGZhbHNlLAogIHByb3BzOiBbJ2N1cnJlbnQnXSwKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgaWNvbkxpc3Q6IG9yaWdpbkxpc3QsCiAgICAgIGFjdGl2ZTogbnVsbCwKICAgICAga2V5OiAnJwogICAgfTsKICB9LAogIHdhdGNoOiB7CiAgICBrZXk6IGZ1bmN0aW9uIGtleSh2YWwpIHsKICAgICAgaWYgKHZhbCkgewogICAgICAgIHRoaXMuaWNvbkxpc3QgPSBvcmlnaW5MaXN0LmZpbHRlcihmdW5jdGlvbiAobmFtZSkgewogICAgICAgICAgcmV0dXJuIG5hbWUuaW5kZXhPZih2YWwpID4gLTE7CiAgICAgICAgfSk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy5pY29uTGlzdCA9IG9yaWdpbkxpc3Q7CiAgICAgIH0KICAgIH0KICB9LAogIG1ldGhvZHM6IHsKICAgIG9uT3BlbjogZnVuY3Rpb24gb25PcGVuKCkgewogICAgICB0aGlzLmFjdGl2ZSA9IHRoaXMuY3VycmVudDsKICAgICAgdGhpcy5rZXkgPSAnJzsKICAgIH0sCiAgICBvbkNsb3NlOiBmdW5jdGlvbiBvbkNsb3NlKCkge30sCiAgICBvblNlbGVjdDogZnVuY3Rpb24gb25TZWxlY3QoaWNvbikgewogICAgICB0aGlzLmFjdGl2ZSA9IGljb247CiAgICAgIHRoaXMuJGVtaXQoJ3NlbGVjdCcsIGljb24pOwogICAgICB0aGlzLiRlbWl0KCd1cGRhdGU6dmlzaWJsZScsIGZhbHNlKTsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["iconList", "originList", "map", "name", "concat", "inheritAttrs", "props", "data", "active", "key", "watch", "val", "filter", "indexOf", "methods", "onOpen", "current", "onClose", "onSelect", "icon", "$emit"], "sources": ["src/views/tool/build/IconsDialog.vue"], "sourcesContent": ["<template>\n  <div class=\"icon-dialog\">\n    <el-dialog\n      v-bind=\"$attrs\"\n      width=\"980px\"\n      :modal-append-to-body=\"false\"\n      v-on=\"$listeners\"\n      @open=\"onOpen\"\n      @close=\"onClose\"\n    >\n      <div slot=\"title\">\n        选择图标\n        <el-input\n          v-model=\"key\"\n          size=\"mini\"\n          :style=\"{width: '260px'}\"\n          placeholder=\"请输入图标名称\"\n          prefix-icon=\"el-icon-search\"\n          clearable\n        />\n      </div>\n      <ul class=\"icon-ul\">\n        <li\n          v-for=\"icon in iconList\"\n          :key=\"icon\"\n          :class=\"active===icon?'active-item':''\"\n          @click=\"onSelect(icon)\"\n        >\n          <i :class=\"icon\" />\n          <div>{{ icon }}</div>\n        </li>\n      </ul>\n    </el-dialog>\n  </div>\n</template>\n<script>\nimport iconList from '@/utils/generator/icon.json'\n\nconst originList = iconList.map(name => `el-icon-${name}`)\n\nexport default {\n  inheritAttrs: false,\n  props: ['current'],\n  data() {\n    return {\n      iconList: originList,\n      active: null,\n      key: ''\n    }\n  },\n  watch: {\n    key(val) {\n      if (val) {\n        this.iconList = originList.filter(name => name.indexOf(val) > -1)\n      } else {\n        this.iconList = originList\n      }\n    }\n  },\n  methods: {\n    onOpen() {\n      this.active = this.current\n      this.key = ''\n    },\n    onClose() {},\n    onSelect(icon) {\n      this.active = icon\n      this.$emit('select', icon)\n      this.$emit('update:visible', false)\n    }\n  }\n}\n</script>\n<style lang=\"scss\" scoped>\n.icon-ul {\n  margin: 0;\n  padding: 0;\n  font-size: 0;\n  li {\n    list-style-type: none;\n    text-align: center;\n    font-size: 14px;\n    display: inline-block;\n    width: 16.66%;\n    box-sizing: border-box;\n    height: 108px;\n    padding: 15px 6px 6px 6px;\n    cursor: pointer;\n    overflow: hidden;\n    &:hover {\n      background: #f2f2f2;\n    }\n    &.active-item{\n      background: #e1f3fb;\n      color: #7a6df0\n    }\n    > i {\n      font-size: 30px;\n      line-height: 50px;\n    }\n  }\n}\n.icon-dialog {\n  ::v-deep .el-dialog {\n    border-radius: 8px;\n    margin-bottom: 0;\n    margin-top: 4vh !important;\n    display: flex;\n    flex-direction: column;\n    max-height: 92vh;\n    overflow: hidden;\n    box-sizing: border-box;\n    .el-dialog__header {\n      padding-top: 14px;\n    }\n    .el-dialog__body {\n      margin: 0 20px 20px 20px;\n      padding: 0;\n      overflow: auto;\n    }\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoCA,OAAAA,QAAA;AAEA,IAAAC,UAAA,GAAAD,QAAA,CAAAE,GAAA,WAAAC,IAAA;EAAA,kBAAAC,MAAA,CAAAD,IAAA;AAAA;AAEA;EACAE,YAAA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAP,QAAA,EAAAC,UAAA;MACAO,MAAA;MACAC,GAAA;IACA;EACA;EACAC,KAAA;IACAD,GAAA,WAAAA,IAAAE,GAAA;MACA,IAAAA,GAAA;QACA,KAAAX,QAAA,GAAAC,UAAA,CAAAW,MAAA,WAAAT,IAAA;UAAA,OAAAA,IAAA,CAAAU,OAAA,CAAAF,GAAA;QAAA;MACA;QACA,KAAAX,QAAA,GAAAC,UAAA;MACA;IACA;EACA;EACAa,OAAA;IACAC,MAAA,WAAAA,OAAA;MACA,KAAAP,MAAA,QAAAQ,OAAA;MACA,KAAAP,GAAA;IACA;IACAQ,OAAA,WAAAA,QAAA;IACAC,QAAA,WAAAA,SAAAC,IAAA;MACA,KAAAX,MAAA,GAAAW,IAAA;MACA,KAAAC,KAAA,WAAAD,IAAA;MACA,KAAAC,KAAA;IACA;EACA;AACA", "ignoreList": []}]}