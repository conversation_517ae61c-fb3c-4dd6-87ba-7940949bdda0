{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Process/components/nodePanel/process.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Process/components/nodePanel/process.vue", "mtime": 1641277326000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["mixinPanel", "mixinExecutionListener", "signalDialog", "commonParse", "components", "mixins", "data", "signalLength", "formData", "computed", "formConfig", "_this", "inline", "item", "xType", "name", "label", "dic", "categorys", "value", "rules", "required", "message", "watch", "formDataProcessCategory", "val", "updateProperties", "created", "element", "methods", "computedSignal<PERSON>ength", "_this$element$busines", "_this$element$busines2", "businessObject", "extensionElements", "values", "length", "finishSignal", "dialogName"], "sources": ["src/components/Process/components/nodePanel/process.vue"], "sourcesContent": ["<template>\n  <div>\n    <x-form ref=\"xForm\" v-model=\"formData\" :config=\"formConfig\">\n      <template #executionListener>\n        <el-badge :value=\"executionListenerLength\">\n          <el-button size=\"small\" @click=\"dialogName = 'executionListenerDialog'\">编辑</el-button>\n        </el-badge>\n      </template>\n      <template #signal>\n        <el-badge :value=\"signalLength\">\n          <el-button size=\"small\" @click=\"dialogName = 'signalDialog'\">编辑</el-button>\n        </el-badge>\n      </template>\n    </x-form>\n    <executionListenerDialog\n      v-if=\"dialogName === 'executionListenerDialog'\"\n      :element=\"element\"\n      :modeler=\"modeler\"\n      @close=\"finishExecutionListener\"\n    />\n    <signalDialog\n      v-if=\"dialogName === 'signalDialog'\"\n      :element=\"element\"\n      :modeler=\"modeler\"\n      @close=\"finishExecutionListener\"\n    />\n  </div>\n</template>\n\n<script>\nimport mixinPanel from '../../common/mixinPanel'\nimport mixinExecutionListener from '../../common/mixinExecutionListener'\nimport signalDialog from './property/signal'\nimport { commonParse } from '../../common/parseElement'\nexport default {\n  components: {\n    signalDialog\n  },\n  mixins: [mixinPanel, mixinExecutionListener],\n  data() {\n    return {\n      signalLength: 0,\n      formData: {}\n    }\n  },\n  computed: {\n    formConfig() {\n      const _this = this\n      return {\n        inline: false,\n        item: [\n          {\n            xType: 'select',\n            name: 'processCategory',\n            label: '流程分类',\n            dic: { data: _this.categorys, label: 'dictLabel', value: 'dictValue' }\n          },\n          {\n            xType: 'input',\n            name: 'id',\n            label: '流程标识key',\n            rules: [{ required: true, message: 'Id 不能为空' }]\n          },\n          {\n            xType: 'input',\n            name: 'name',\n            label: '流程名称'\n          },\n          {\n            xType: 'input',\n            name: 'documentation',\n            label: '节点描述'\n          },\n          {\n            xType: 'slot',\n            name: 'executionListener',\n            label: '执行监听器'\n          },\n          {\n            xType: 'slot',\n            name: 'signal',\n            label: '信号定义'\n          }\n        ]\n      }\n    }\n  },\n  watch: {\n    'formData.processCategory': function(val) {\n      if (val === '') val = null\n      this.updateProperties({ 'flowable:processCategory': val })\n    }\n  },\n  created() {\n    this.formData = commonParse(this.element)\n  },\n  methods: {\n    computedSignalLength() {\n      this.signalLength = this.element.businessObject.extensionElements?.values?.length ?? 0\n    },\n    finishSignal() {\n      if (this.dialogName === 'signalDialog') {\n        this.computedSignalLength()\n      }\n      this.dialogName = ''\n    }\n  }\n}\n</script>\n\n<style>\n\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BA,OAAAA,UAAA;AACA,OAAAC,sBAAA;AACA,OAAAC,YAAA;AACA,SAAAC,WAAA;AACA;EACAC,UAAA;IACAF,YAAA,EAAAA;EACA;EACAG,MAAA,GAAAL,UAAA,EAAAC,sBAAA;EACAK,IAAA,WAAAA,KAAA;IACA;MACAC,YAAA;MACAC,QAAA;IACA;EACA;EACAC,QAAA;IACAC,UAAA,WAAAA,WAAA;MACA,IAAAC,KAAA;MACA;QACAC,MAAA;QACAC,IAAA,GACA;UACAC,KAAA;UACAC,IAAA;UACAC,KAAA;UACAC,GAAA;YAAAX,IAAA,EAAAK,KAAA,CAAAO,SAAA;YAAAF,KAAA;YAAAG,KAAA;UAAA;QACA,GACA;UACAL,KAAA;UACAC,IAAA;UACAC,KAAA;UACAI,KAAA;YAAAC,QAAA;YAAAC,OAAA;UAAA;QACA,GACA;UACAR,KAAA;UACAC,IAAA;UACAC,KAAA;QACA,GACA;UACAF,KAAA;UACAC,IAAA;UACAC,KAAA;QACA,GACA;UACAF,KAAA;UACAC,IAAA;UACAC,KAAA;QACA,GACA;UACAF,KAAA;UACAC,IAAA;UACAC,KAAA;QACA;MAEA;IACA;EACA;EACAO,KAAA;IACA,qCAAAC,wBAAAC,GAAA;MACA,IAAAA,GAAA,SAAAA,GAAA;MACA,KAAAC,gBAAA;QAAA,4BAAAD;MAAA;IACA;EACA;EACAE,OAAA,WAAAA,QAAA;IACA,KAAAnB,QAAA,GAAAL,WAAA,MAAAyB,OAAA;EACA;EACAC,OAAA;IACAC,oBAAA,WAAAA,qBAAA;MAAA,IAAAC,qBAAA,EAAAC,sBAAA;MACA,KAAAzB,YAAA,IAAAwB,qBAAA,IAAAC,sBAAA,QAAAJ,OAAA,CAAAK,cAAA,CAAAC,iBAAA,cAAAF,sBAAA,gBAAAA,sBAAA,GAAAA,sBAAA,CAAAG,MAAA,cAAAH,sBAAA,uBAAAA,sBAAA,CAAAI,MAAA,cAAAL,qBAAA,cAAAA,qBAAA;IACA;IACAM,YAAA,WAAAA,aAAA;MACA,SAAAC,UAAA;QACA,KAAAR,oBAAA;MACA;MACA,KAAAQ,UAAA;IACA;EACA;AACA", "ignoreList": []}]}