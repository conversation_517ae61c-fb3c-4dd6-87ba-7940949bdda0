{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/components/render/slots/el-radio-group.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/components/render/slots/el-radio-group.js", "mtime": 1641277326000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:ZXhwb3J0IGRlZmF1bHQgewogIG9wdGlvbnM6IGZ1bmN0aW9uIG9wdGlvbnMoaCwgY29uZiwga2V5KSB7CiAgICB2YXIgbGlzdCA9IFtdOwogICAgY29uZi5fX3Nsb3RfXy5vcHRpb25zLmZvckVhY2goZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgaWYgKGNvbmYuX19jb25maWdfXy5vcHRpb25UeXBlID09PSAnYnV0dG9uJykgewogICAgICAgIGxpc3QucHVzaChoKCJlbC1yYWRpby1idXR0b24iLCB7CiAgICAgICAgICAiYXR0cnMiOiB7CiAgICAgICAgICAgICJsYWJlbCI6IGl0ZW0udmFsdWUKICAgICAgICAgIH0KICAgICAgICB9LCBbaXRlbS5sYWJlbF0pKTsKICAgICAgfSBlbHNlIHsKICAgICAgICBsaXN0LnB1c2goaCgiZWwtcmFkaW8iLCB7CiAgICAgICAgICAiYXR0cnMiOiB7CiAgICAgICAgICAgICJsYWJlbCI6IGl0ZW0udmFsdWUsCiAgICAgICAgICAgICJib3JkZXIiOiBjb25mLmJvcmRlcgogICAgICAgICAgfQogICAgICAgIH0sIFtpdGVtLmxhYmVsXSkpOwogICAgICB9CiAgICB9KTsKICAgIHJldHVybiBsaXN0OwogIH0KfTs="}, {"version": 3, "names": ["options", "h", "conf", "key", "list", "__slot__", "for<PERSON>ach", "item", "__config__", "optionType", "push", "value", "label", "border"], "sources": ["/Users/<USER>/develop/code/project/haijia/adminweb/src/components/render/slots/el-radio-group.js"], "sourcesContent": ["export default {\n  options(h, conf, key) {\n    const list = []\n    conf.__slot__.options.forEach(item => {\n      if (conf.__config__.optionType === 'button') {\n        list.push(<el-radio-button label={item.value}>{item.label}</el-radio-button>)\n      } else {\n        list.push(<el-radio label={item.value} border={conf.border}>{item.label}</el-radio>)\n      }\n    })\n    return list\n  }\n}\n"], "mappings": "AAAA,eAAe;EACbA,OAAO,WAAAA,QAACC,CAAC,EAAEC,IAAI,EAAEC,GAAG,EAAE;IACpB,IAAMC,IAAI,GAAG,EAAE;IACfF,IAAI,CAACG,QAAQ,CAACL,OAAO,CAACM,OAAO,CAAC,UAAAC,IAAI,EAAI;MACpC,IAAIL,IAAI,CAACM,UAAU,CAACC,UAAU,KAAK,QAAQ,EAAE;QAC3CL,IAAI,CAACM,IAAI,CAAAT,CAAA;UAAA;YAAA,SAAyBM,IAAI,CAACI;UAAK;QAAA,IAAGJ,IAAI,CAACK,KAAK,EAAmB,CAAC;MAC/E,CAAC,MAAM;QACLR,IAAI,CAACM,IAAI,CAAAT,CAAA;UAAA;YAAA,SAAkBM,IAAI,CAACI,KAAK;YAAA,UAAUT,IAAI,CAACW;UAAM;QAAA,IAAGN,IAAI,CAACK,KAAK,EAAY,CAAC;MACtF;IACF,CAAC,CAAC;IACF,OAAOR,IAAI;EACb;AACF,CAAC", "ignoreList": []}]}