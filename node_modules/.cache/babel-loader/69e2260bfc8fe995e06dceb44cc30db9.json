{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/tool/build/ResourceDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/tool/build/ResourceDialog.vue", "mtime": 1641277326000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js", "mtime": 1751171659043}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGRlZXBDbG9uZSB9IGZyb20gJ0AvdXRpbHMvaW5kZXgnOwpleHBvcnQgZGVmYXVsdCB7CiAgY29tcG9uZW50czoge30sCiAgaW5oZXJpdEF0dHJzOiBmYWxzZSwKICBwcm9wczogWydvcmlnaW5SZXNvdXJjZSddLAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICByZXNvdXJjZXM6IG51bGwKICAgIH07CiAgfSwKICBjb21wdXRlZDoge30sCiAgd2F0Y2g6IHt9LAogIGNyZWF0ZWQ6IGZ1bmN0aW9uIGNyZWF0ZWQoKSB7fSwKICBtb3VudGVkOiBmdW5jdGlvbiBtb3VudGVkKCkge30sCiAgbWV0aG9kczogewogICAgb25PcGVuOiBmdW5jdGlvbiBvbk9wZW4oKSB7CiAgICAgIHRoaXMucmVzb3VyY2VzID0gdGhpcy5vcmlnaW5SZXNvdXJjZS5sZW5ndGggPyBkZWVwQ2xvbmUodGhpcy5vcmlnaW5SZXNvdXJjZSkgOiBbJyddOwogICAgfSwKICAgIG9uQ2xvc2U6IGZ1bmN0aW9uIG9uQ2xvc2UoKSB7fSwKICAgIGNsb3NlOiBmdW5jdGlvbiBjbG9zZSgpIHsKICAgICAgdGhpcy4kZW1pdCgndXBkYXRlOnZpc2libGUnLCBmYWxzZSk7CiAgICB9LAogICAgaGFuZGVsQ29uZmlybTogZnVuY3Rpb24gaGFuZGVsQ29uZmlybSgpIHsKICAgICAgdmFyIHJlc3VsdHMgPSB0aGlzLnJlc291cmNlcy5maWx0ZXIoZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICByZXR1cm4gISFpdGVtOwogICAgICB9KSB8fCBbXTsKICAgICAgdGhpcy4kZW1pdCgnc2F2ZScsIHJlc3VsdHMpOwogICAgICB0aGlzLmNsb3NlKCk7CiAgICAgIGlmIChyZXN1bHRzLmxlbmd0aCkgewogICAgICAgIHRoaXMucmVzb3VyY2VzID0gcmVzdWx0czsKICAgICAgfQogICAgfSwKICAgIGRlbGV0ZU9uZTogZnVuY3Rpb24gZGVsZXRlT25lKGluZGV4KSB7CiAgICAgIHRoaXMucmVzb3VyY2VzLnNwbGljZShpbmRleCwgMSk7CiAgICB9LAogICAgYWRkT25lOiBmdW5jdGlvbiBhZGRPbmUodXJsKSB7CiAgICAgIGlmICh0aGlzLnJlc291cmNlcy5pbmRleE9mKHVybCkgPiAtMSkgewogICAgICAgIHRoaXMuJG1lc3NhZ2UoJ+i1hOa6kOW3suWtmOWcqCcpOwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMucmVzb3VyY2VzLnB1c2godXJsKTsKICAgICAgfQogICAgfQogIH0KfTs="}, null]}