{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/tool/gen/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/tool/gen/index.vue", "mtime": 1641277326000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGxpc3RUYWJsZSwgcHJldmlld1RhYmxlLCBkZWxUYWJsZSwgZ2VuQ29kZSwgc3luY2hEYiB9IGZyb20gIkAvYXBpL3Rvb2wvZ2VuIjsKaW1wb3J0IGltcG9ydFRhYmxlIGZyb20gIi4vaW1wb3J0VGFibGUiOwppbXBvcnQgeyBkb3duTG9hZFppcCB9IGZyb20gIkAvdXRpbHMvemlwZG93bmxvYWQiOwppbXBvcnQgaGxqcyBmcm9tICJoaWdobGlnaHQuanMvbGliL2hpZ2hsaWdodCI7CmltcG9ydCAiaGlnaGxpZ2h0LmpzL3N0eWxlcy9naXRodWItZ2lzdC5jc3MiOwpobGpzLnJlZ2lzdGVyTGFuZ3VhZ2UoImphdmEiLCByZXF1aXJlKCJoaWdobGlnaHQuanMvbGliL2xhbmd1YWdlcy9qYXZhIikpOwpobGpzLnJlZ2lzdGVyTGFuZ3VhZ2UoInhtbCIsIHJlcXVpcmUoImhpZ2hsaWdodC5qcy9saWIvbGFuZ3VhZ2VzL3htbCIpKTsKaGxqcy5yZWdpc3Rlckxhbmd1YWdlKCJodG1sIiwgcmVxdWlyZSgiaGlnaGxpZ2h0LmpzL2xpYi9sYW5ndWFnZXMveG1sIikpOwpobGpzLnJlZ2lzdGVyTGFuZ3VhZ2UoInZ1ZSIsIHJlcXVpcmUoImhpZ2hsaWdodC5qcy9saWIvbGFuZ3VhZ2VzL3htbCIpKTsKaGxqcy5yZWdpc3Rlckxhbmd1YWdlKCJqYXZhc2NyaXB0IiwgcmVxdWlyZSgiaGlnaGxpZ2h0LmpzL2xpYi9sYW5ndWFnZXMvamF2YXNjcmlwdCIpKTsKaGxqcy5yZWdpc3Rlckxhbmd1YWdlKCJzcWwiLCByZXF1aXJlKCJoaWdobGlnaHQuanMvbGliL2xhbmd1YWdlcy9zcWwiKSk7CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiR2VuIiwKICBjb21wb25lbnRzOiB7CiAgICBpbXBvcnRUYWJsZTogaW1wb3J0VGFibGUKICB9LAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICAvLyDpga7nvanlsYIKICAgICAgbG9hZGluZzogdHJ1ZSwKICAgICAgLy8g5ZSv5LiA5qCH6K+G56ymCiAgICAgIHVuaXF1ZUlkOiAiIiwKICAgICAgLy8g6YCJ5Lit5pWw57uECiAgICAgIGlkczogW10sCiAgICAgIC8vIOmAieS4reihqOaVsOe7hAogICAgICB0YWJsZU5hbWVzOiBbXSwKICAgICAgLy8g6Z2e5Y2V5Liq56aB55SoCiAgICAgIHNpbmdsZTogdHJ1ZSwKICAgICAgLy8g6Z2e5aSa5Liq56aB55SoCiAgICAgIG11bHRpcGxlOiB0cnVlLAogICAgICAvLyDmmL7npLrmkJzntKLmnaHku7YKICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwKICAgICAgLy8g5oC75p2h5pWwCiAgICAgIHRvdGFsOiAwLAogICAgICAvLyDooajmlbDmja4KICAgICAgdGFibGVMaXN0OiBbXSwKICAgICAgLy8g5pel5pyf6IyD5Zu0CiAgICAgIGRhdGVSYW5nZTogIiIsCiAgICAgIC8vIOafpeivouWPguaVsAogICAgICBxdWVyeVBhcmFtczogewogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgIHRhYmxlTmFtZTogdW5kZWZpbmVkLAogICAgICAgIHRhYmxlQ29tbWVudDogdW5kZWZpbmVkCiAgICAgIH0sCiAgICAgIC8vIOmihOiniOWPguaVsAogICAgICBwcmV2aWV3OiB7CiAgICAgICAgb3BlbjogZmFsc2UsCiAgICAgICAgdGl0bGU6ICLku6PnoIHpooTop4giLAogICAgICAgIGRhdGE6IHt9LAogICAgICAgIGFjdGl2ZU5hbWU6ICJkb21haW4uamF2YSIKICAgICAgfQogICAgfTsKICB9LAogIGNyZWF0ZWQ6IGZ1bmN0aW9uIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmdldExpc3QoKTsKICB9LAogIGFjdGl2YXRlZDogZnVuY3Rpb24gYWN0aXZhdGVkKCkgewogICAgdmFyIHRpbWUgPSB0aGlzLiRyb3V0ZS5xdWVyeS50OwogICAgaWYgKHRpbWUgIT0gbnVsbCAmJiB0aW1lICE9IHRoaXMudW5pcXVlSWQpIHsKICAgICAgdGhpcy51bmlxdWVJZCA9IHRpbWU7CiAgICAgIHRoaXMucmVzZXRRdWVyeSgpOwogICAgfQogIH0sCiAgbWV0aG9kczogewogICAgLyoqIOafpeivouihqOmbhuWQiCAqL2dldExpc3Q6IGZ1bmN0aW9uIGdldExpc3QoKSB7CiAgICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7CiAgICAgIGxpc3RUYWJsZSh0aGlzLmFkZERhdGVSYW5nZSh0aGlzLnF1ZXJ5UGFyYW1zLCB0aGlzLmRhdGVSYW5nZSkpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgX3RoaXMudGFibGVMaXN0ID0gcmVzcG9uc2Uucm93czsKICAgICAgICBfdGhpcy50b3RhbCA9IHJlc3BvbnNlLnRvdGFsOwogICAgICAgIF90aGlzLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqL2hhbmRsZVF1ZXJ5OiBmdW5jdGlvbiBoYW5kbGVRdWVyeSgpIHsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsKICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICB9LAogICAgLyoqIOeUn+aIkOS7o+eggeaTjeS9nCAqL2hhbmRsZUdlblRhYmxlOiBmdW5jdGlvbiBoYW5kbGVHZW5UYWJsZShyb3cpIHsKICAgICAgdmFyIF90aGlzMiA9IHRoaXM7CiAgICAgIHZhciB0YWJsZU5hbWVzID0gcm93LnRhYmxlTmFtZSB8fCB0aGlzLnRhYmxlTmFtZXM7CiAgICAgIGlmICh0YWJsZU5hbWVzID09ICIiKSB7CiAgICAgICAgdGhpcy5tc2dFcnJvcigi6K+36YCJ5oup6KaB55Sf5oiQ55qE5pWw5o2uIik7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIGlmIChyb3cuZ2VuVHlwZSA9PT0gIjEiKSB7CiAgICAgICAgZ2VuQ29kZShyb3cudGFibGVOYW1lKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgICAgX3RoaXMyLm1zZ1N1Y2Nlc3MoIuaIkOWKn+eUn+aIkOWIsOiHquWumuS5iei3r+W+hO+8miIgKyByb3cuZ2VuUGF0aCk7CiAgICAgICAgfSk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgZG93bkxvYWRaaXAoIi90b29sL2dlbi9iYXRjaEdlbkNvZGU/dGFibGVzPSIgKyB0YWJsZU5hbWVzLCAicnVveWkiKTsKICAgICAgfQogICAgfSwKICAgIC8qKiDlkIzmraXmlbDmja7lupPmk43kvZwgKi9oYW5kbGVTeW5jaERiOiBmdW5jdGlvbiBoYW5kbGVTeW5jaERiKHJvdykgewogICAgICB2YXIgX3RoaXMzID0gdGhpczsKICAgICAgdmFyIHRhYmxlTmFtZSA9IHJvdy50YWJsZU5hbWU7CiAgICAgIHRoaXMuJGNvbmZpcm0oJ+ehruiupOimgeW8uuWItuWQjOatpSInICsgdGFibGVOYW1lICsgJyLooajnu5PmnoTlkJfvvJ8nLCAi6K2m5ZGKIiwgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAi56Gu5a6aIiwKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAi5Y+W5raIIiwKICAgICAgICB0eXBlOiAid2FybmluZyIKICAgICAgfSkudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgcmV0dXJuIHN5bmNoRGIodGFibGVOYW1lKTsKICAgICAgfSkudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgX3RoaXMzLm1zZ1N1Y2Nlc3MoIuWQjOatpeaIkOWKnyIpOwogICAgICB9KTsKICAgIH0sCiAgICAvKiog5omT5byA5a+85YWl6KGo5by556qXICovb3BlbkltcG9ydFRhYmxlOiBmdW5jdGlvbiBvcGVuSW1wb3J0VGFibGUoKSB7CiAgICAgIHRoaXMuJHJlZnMuaW1wb3J0LnNob3coKTsKICAgIH0sCiAgICAvKiog6YeN572u5oyJ6ZKu5pON5L2cICovcmVzZXRRdWVyeTogZnVuY3Rpb24gcmVzZXRRdWVyeSgpIHsKICAgICAgdGhpcy5kYXRlUmFuZ2UgPSBbXTsKICAgICAgdGhpcy5yZXNldEZvcm0oInF1ZXJ5Rm9ybSIpOwogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7CiAgICB9LAogICAgLyoqIOmihOiniOaMiemSriAqL2hhbmRsZVByZXZpZXc6IGZ1bmN0aW9uIGhhbmRsZVByZXZpZXcocm93KSB7CiAgICAgIHZhciBfdGhpczQgPSB0aGlzOwogICAgICBwcmV2aWV3VGFibGUocm93LnRhYmxlSWQpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgX3RoaXM0LnByZXZpZXcuZGF0YSA9IHJlc3BvbnNlLmRhdGE7CiAgICAgICAgX3RoaXM0LnByZXZpZXcub3BlbiA9IHRydWU7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDpq5jkuq7mmL7npLogKi9oaWdobGlnaHRlZENvZGU6IGZ1bmN0aW9uIGhpZ2hsaWdodGVkQ29kZShjb2RlLCBrZXkpIHsKICAgICAgdmFyIHZtTmFtZSA9IGtleS5zdWJzdHJpbmcoa2V5Lmxhc3RJbmRleE9mKCIvIikgKyAxLCBrZXkuaW5kZXhPZigiLnZtIikpOwogICAgICB2YXIgbGFuZ3VhZ2UgPSB2bU5hbWUuc3Vic3RyaW5nKHZtTmFtZS5pbmRleE9mKCIuIikgKyAxLCB2bU5hbWUubGVuZ3RoKTsKICAgICAgdmFyIHJlc3VsdCA9IGhsanMuaGlnaGxpZ2h0KGxhbmd1YWdlLCBjb2RlIHx8ICIiLCB0cnVlKTsKICAgICAgcmV0dXJuIHJlc3VsdC52YWx1ZSB8fCAnJm5ic3A7JzsKICAgIH0sCiAgICAvLyDlpJrpgInmoYbpgInkuK3mlbDmja4KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZTogZnVuY3Rpb24gaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgewogICAgICB0aGlzLmlkcyA9IHNlbGVjdGlvbi5tYXAoZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICByZXR1cm4gaXRlbS50YWJsZUlkOwogICAgICB9KTsKICAgICAgdGhpcy50YWJsZU5hbWVzID0gc2VsZWN0aW9uLm1hcChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgIHJldHVybiBpdGVtLnRhYmxlTmFtZTsKICAgICAgfSk7CiAgICAgIHRoaXMuc2luZ2xlID0gc2VsZWN0aW9uLmxlbmd0aCAhPSAxOwogICAgICB0aGlzLm11bHRpcGxlID0gIXNlbGVjdGlvbi5sZW5ndGg7CiAgICB9LAogICAgLyoqIOS/ruaUueaMiemSruaTjeS9nCAqL2hhbmRsZUVkaXRUYWJsZTogZnVuY3Rpb24gaGFuZGxlRWRpdFRhYmxlKHJvdykgewogICAgICB2YXIgdGFibGVJZCA9IHJvdy50YWJsZUlkIHx8IHRoaXMuaWRzWzBdOwogICAgICB0aGlzLiRyb3V0ZXIucHVzaCgiL2dlbi9lZGl0LyIgKyB0YWJsZUlkKTsKICAgIH0sCiAgICAvKiog5Yig6Zmk5oyJ6ZKu5pON5L2cICovaGFuZGxlRGVsZXRlOiBmdW5jdGlvbiBoYW5kbGVEZWxldGUocm93KSB7CiAgICAgIHZhciBfdGhpczUgPSB0aGlzOwogICAgICB2YXIgdGFibGVJZHMgPSByb3cudGFibGVJZCB8fCB0aGlzLmlkczsKICAgICAgdGhpcy4kY29uZmlybSgn5piv5ZCm56Gu6K6k5Yig6Zmk6KGo57yW5Y+35Li6IicgKyB0YWJsZUlkcyArICci55qE5pWw5o2u6aG5PycsICLorablkYoiLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLAogICAgICAgIHR5cGU6ICJ3YXJuaW5nIgogICAgICB9KS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICByZXR1cm4gZGVsVGFibGUodGFibGVJZHMpOwogICAgICB9KS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICBfdGhpczUuZ2V0TGlzdCgpOwogICAgICAgIF90aGlzNS5tc2dTdWNjZXNzKCLliKDpmaTmiJDlip8iKTsKICAgICAgfSk7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["listTable", "previewTable", "delTable", "genCode", "synchDb", "importTable", "downLoadZip", "hljs", "registerLanguage", "require", "name", "components", "data", "loading", "uniqueId", "ids", "tableNames", "single", "multiple", "showSearch", "total", "tableList", "date<PERSON><PERSON><PERSON>", "queryParams", "pageNum", "pageSize", "tableName", "undefined", "tableComment", "preview", "open", "title", "activeName", "created", "getList", "activated", "time", "$route", "query", "t", "reset<PERSON><PERSON>y", "methods", "_this", "addDateRange", "then", "response", "rows", "handleQuery", "handleGenTable", "row", "_this2", "msgError", "genType", "msgSuccess", "gen<PERSON><PERSON>", "handleSynchDb", "_this3", "$confirm", "confirmButtonText", "cancelButtonText", "type", "openImportTable", "$refs", "import", "show", "resetForm", "handlePreview", "_this4", "tableId", "highlightedCode", "code", "key", "vmName", "substring", "lastIndexOf", "indexOf", "language", "length", "result", "highlight", "value", "handleSelectionChange", "selection", "map", "item", "handleEditTable", "$router", "push", "handleDelete", "_this5", "tableIds"], "sources": ["src/views/tool/gen/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"表名称\" prop=\"tableName\">\n        <el-input\n          v-model=\"queryParams.tableName\"\n          placeholder=\"请输入表名称\"\n          clearable\n          size=\"small\"\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"表描述\" prop=\"tableComment\">\n        <el-input\n          v-model=\"queryParams.tableComment\"\n          placeholder=\"请输入表描述\"\n          clearable\n          size=\"small\"\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"创建时间\">\n        <el-date-picker\n          v-model=\"dateRange\"\n          size=\"small\"\n          style=\"width: 240px\"\n          value-format=\"yyyy-MM-dd\"\n          type=\"daterange\"\n          range-separator=\"-\"\n          start-placeholder=\"开始日期\"\n          end-placeholder=\"结束日期\"\n        ></el-date-picker>\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"primary\"\n          plain\n          icon=\"el-icon-download\"\n          size=\"mini\"\n          @click=\"handleGenTable\"\n          v-hasPermi=\"['tool:gen:code']\"\n        >生成</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"info\"\n          plain\n          icon=\"el-icon-upload\"\n          size=\"mini\"\n          @click=\"openImportTable\"\n          v-hasPermi=\"['tool:gen:import']\"\n        >导入</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"success\"\n          plain\n          icon=\"el-icon-edit\"\n          size=\"mini\"\n          :disabled=\"single\"\n          @click=\"handleEditTable\"\n          v-hasPermi=\"['tool:gen:edit']\"\n        >修改</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"danger\"\n          plain\n          icon=\"el-icon-delete\"\n          size=\"mini\"\n          :disabled=\"multiple\"\n          @click=\"handleDelete\"\n          v-hasPermi=\"['tool:gen:remove']\"\n        >删除</el-button>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <el-table v-loading=\"loading\" :data=\"tableList\" @selection-change=\"handleSelectionChange\">\n      <el-table-column type=\"selection\" align=\"center\" width=\"55\"></el-table-column>\n      <el-table-column label=\"序号\" type=\"index\" width=\"50\" align=\"center\">\n        <template slot-scope=\"scope\">\n          <span>{{(queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1}}</span>\n        </template>\n      </el-table-column>\n      <el-table-column\n        label=\"表名称\"\n        align=\"center\"\n        prop=\"tableName\"\n        :show-overflow-tooltip=\"true\"\n        width=\"120\"\n      />\n      <el-table-column\n        label=\"表描述\"\n        align=\"center\"\n        prop=\"tableComment\"\n        :show-overflow-tooltip=\"true\"\n        width=\"120\"\n      />\n      <el-table-column\n        label=\"实体\"\n        align=\"center\"\n        prop=\"className\"\n        :show-overflow-tooltip=\"true\"\n        width=\"120\"\n      />\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"160\" />\n      <el-table-column label=\"更新时间\" align=\"center\" prop=\"updateTime\" width=\"160\" />\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"scope\">\n          <el-button\n            type=\"text\"\n            size=\"small\"\n            icon=\"el-icon-view\"\n            @click=\"handlePreview(scope.row)\"\n            v-hasPermi=\"['tool:gen:preview']\"\n          >预览</el-button>\n          <el-button\n            type=\"text\"\n            size=\"small\"\n            icon=\"el-icon-edit\"\n            @click=\"handleEditTable(scope.row)\"\n            v-hasPermi=\"['tool:gen:edit']\"\n          >编辑</el-button>\n          <el-button\n            type=\"text\"\n            size=\"small\"\n            icon=\"el-icon-delete\"\n            @click=\"handleDelete(scope.row)\"\n            v-hasPermi=\"['tool:gen:remove']\"\n          >删除</el-button>\n          <el-button\n            type=\"text\"\n            size=\"small\"\n            icon=\"el-icon-refresh\"\n            @click=\"handleSynchDb(scope.row)\"\n            v-hasPermi=\"['tool:gen:edit']\"\n          >同步</el-button>\n          <el-button\n            type=\"text\"\n            size=\"small\"\n            icon=\"el-icon-download\"\n            @click=\"handleGenTable(scope.row)\"\n            v-hasPermi=\"['tool:gen:code']\"\n          >生成代码</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n    <!-- 预览界面 -->\n    <el-dialog :title=\"preview.title\" :visible.sync=\"preview.open\" width=\"80%\" top=\"5vh\" append-to-body>\n      <el-tabs v-model=\"preview.activeName\">\n        <el-tab-pane\n          v-for=\"(value, key) in preview.data\"\n          :label=\"key.substring(key.lastIndexOf('/')+1,key.indexOf('.vm'))\"\n          :name=\"key.substring(key.lastIndexOf('/')+1,key.indexOf('.vm'))\"\n          :key=\"key\"\n        >\n        <pre><code class=\"hljs\" v-html=\"highlightedCode(value, key)\"></code></pre>\n        </el-tab-pane>\n      </el-tabs>\n    </el-dialog>\n    <import-table ref=\"import\" @ok=\"handleQuery\" />\n  </div>\n</template>\n\n<script>\nimport { listTable, previewTable, delTable, genCode, synchDb } from \"@/api/tool/gen\";\nimport importTable from \"./importTable\";\nimport { downLoadZip } from \"@/utils/zipdownload\";\nimport hljs from \"highlight.js/lib/highlight\";\nimport \"highlight.js/styles/github-gist.css\";\nhljs.registerLanguage(\"java\", require(\"highlight.js/lib/languages/java\"));\nhljs.registerLanguage(\"xml\", require(\"highlight.js/lib/languages/xml\"));\nhljs.registerLanguage(\"html\", require(\"highlight.js/lib/languages/xml\"));\nhljs.registerLanguage(\"vue\", require(\"highlight.js/lib/languages/xml\"));\nhljs.registerLanguage(\"javascript\", require(\"highlight.js/lib/languages/javascript\"));\nhljs.registerLanguage(\"sql\", require(\"highlight.js/lib/languages/sql\"));\n\nexport default {\n  name: \"Gen\",\n  components: { importTable },\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 唯一标识符\n      uniqueId: \"\",\n      // 选中数组\n      ids: [],\n      // 选中表数组\n      tableNames: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 表数据\n      tableList: [],\n      // 日期范围\n      dateRange: \"\",\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        tableName: undefined,\n        tableComment: undefined\n      },\n      // 预览参数\n      preview: {\n        open: false,\n        title: \"代码预览\",\n        data: {},\n        activeName: \"domain.java\"\n      }\n    };\n  },\n  created() {\n    this.getList();\n  },\n  activated() {\n    const time = this.$route.query.t;\n    if (time != null && time != this.uniqueId) {\n      this.uniqueId = time;\n      this.resetQuery();\n    }\n  },\n  methods: {\n    /** 查询表集合 */\n    getList() {\n      this.loading = true;\n      listTable(this.addDateRange(this.queryParams, this.dateRange)).then(response => {\n          this.tableList = response.rows;\n          this.total = response.total;\n          this.loading = false;\n        }\n      );\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 生成代码操作 */\n    handleGenTable(row) {\n      const tableNames = row.tableName || this.tableNames;\n      if (tableNames == \"\") {\n        this.msgError(\"请选择要生成的数据\");\n        return;\n      }\n      if(row.genType === \"1\") {\n        genCode(row.tableName).then(response => {\n          this.msgSuccess(\"成功生成到自定义路径：\" + row.genPath);\n        });\n      } else {\n        downLoadZip(\"/tool/gen/batchGenCode?tables=\" + tableNames, \"ruoyi\");\n      }\n    },\n    /** 同步数据库操作 */\n    handleSynchDb(row) {\n      const tableName = row.tableName;\n      this.$confirm('确认要强制同步\"' + tableName + '\"表结构吗？', \"警告\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      }).then(function() {\n          return synchDb(tableName);\n      }).then(() => {\n          this.msgSuccess(\"同步成功\");\n      })\n    },\n    /** 打开导入表弹窗 */\n    openImportTable() {\n      this.$refs.import.show();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.dateRange = [];\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    /** 预览按钮 */\n    handlePreview(row) {\n      previewTable(row.tableId).then(response => {\n        this.preview.data = response.data;\n        this.preview.open = true;\n      });\n    },\n    /** 高亮显示 */\n    highlightedCode(code, key) {\n      const vmName = key.substring(key.lastIndexOf(\"/\") + 1, key.indexOf(\".vm\"));\n      var language = vmName.substring(vmName.indexOf(\".\") + 1, vmName.length);\n      const result = hljs.highlight(language, code || \"\", true);\n      return result.value || '&nbsp;';\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.tableId);\n      this.tableNames = selection.map(item => item.tableName);\n      this.single = selection.length != 1;\n      this.multiple = !selection.length;\n    },\n    /** 修改按钮操作 */\n    handleEditTable(row) {\n      const tableId = row.tableId || this.ids[0];\n      this.$router.push(\"/gen/edit/\" + tableId);\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const tableIds = row.tableId || this.ids;\n      this.$confirm('是否确认删除表编号为\"' + tableIds + '\"的数据项?', \"警告\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      }).then(function() {\n          return delTable(tableIds);\n      }).then(() => {\n          this.getList();\n          this.msgSuccess(\"删除成功\");\n      })\n    }\n  }\n};\n</script>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoLA,SAAAA,SAAA,EAAAC,YAAA,EAAAC,QAAA,EAAAC,OAAA,EAAAC,OAAA;AACA,OAAAC,WAAA;AACA,SAAAC,WAAA;AACA,OAAAC,IAAA;AACA;AACAA,IAAA,CAAAC,gBAAA,SAAAC,OAAA;AACAF,IAAA,CAAAC,gBAAA,QAAAC,OAAA;AACAF,IAAA,CAAAC,gBAAA,SAAAC,OAAA;AACAF,IAAA,CAAAC,gBAAA,QAAAC,OAAA;AACAF,IAAA,CAAAC,gBAAA,eAAAC,OAAA;AACAF,IAAA,CAAAC,gBAAA,QAAAC,OAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IAAAN,WAAA,EAAAA;EAAA;EACAO,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,QAAA;MACA;MACAC,GAAA;MACA;MACAC,UAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,SAAA;MACA;MACAC,SAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,SAAA,EAAAC,SAAA;QACAC,YAAA,EAAAD;MACA;MACA;MACAE,OAAA;QACAC,IAAA;QACAC,KAAA;QACAnB,IAAA;QACAoB,UAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,SAAA,WAAAA,UAAA;IACA,IAAAC,IAAA,QAAAC,MAAA,CAAAC,KAAA,CAAAC,CAAA;IACA,IAAAH,IAAA,YAAAA,IAAA,SAAAtB,QAAA;MACA,KAAAA,QAAA,GAAAsB,IAAA;MACA,KAAAI,UAAA;IACA;EACA;EACAC,OAAA;IACA,YACAP,OAAA,WAAAA,QAAA;MAAA,IAAAQ,KAAA;MACA,KAAA7B,OAAA;MACAb,SAAA,MAAA2C,YAAA,MAAApB,WAAA,OAAAD,SAAA,GAAAsB,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAArB,SAAA,GAAAwB,QAAA,CAAAC,IAAA;QACAJ,KAAA,CAAAtB,KAAA,GAAAyB,QAAA,CAAAzB,KAAA;QACAsB,KAAA,CAAA7B,OAAA;MACA,CACA;IACA;IACA,aACAkC,WAAA,WAAAA,YAAA;MACA,KAAAxB,WAAA,CAAAC,OAAA;MACA,KAAAU,OAAA;IACA;IACA,aACAc,cAAA,WAAAA,eAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,IAAAlC,UAAA,GAAAiC,GAAA,CAAAvB,SAAA,SAAAV,UAAA;MACA,IAAAA,UAAA;QACA,KAAAmC,QAAA;QACA;MACA;MACA,IAAAF,GAAA,CAAAG,OAAA;QACAjD,OAAA,CAAA8C,GAAA,CAAAvB,SAAA,EAAAkB,IAAA,WAAAC,QAAA;UACAK,MAAA,CAAAG,UAAA,iBAAAJ,GAAA,CAAAK,OAAA;QACA;MACA;QACAhD,WAAA,oCAAAU,UAAA;MACA;IACA;IACA,cACAuC,aAAA,WAAAA,cAAAN,GAAA;MAAA,IAAAO,MAAA;MACA,IAAA9B,SAAA,GAAAuB,GAAA,CAAAvB,SAAA;MACA,KAAA+B,QAAA,cAAA/B,SAAA;QACAgC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAAhB,IAAA;QACA,OAAAxC,OAAA,CAAAsB,SAAA;MACA,GAAAkB,IAAA;QACAY,MAAA,CAAAH,UAAA;MACA;IACA;IACA,cACAQ,eAAA,WAAAA,gBAAA;MACA,KAAAC,KAAA,CAAAC,MAAA,CAAAC,IAAA;IACA;IACA,aACAxB,UAAA,WAAAA,WAAA;MACA,KAAAlB,SAAA;MACA,KAAA2C,SAAA;MACA,KAAAlB,WAAA;IACA;IACA,WACAmB,aAAA,WAAAA,cAAAjB,GAAA;MAAA,IAAAkB,MAAA;MACAlE,YAAA,CAAAgD,GAAA,CAAAmB,OAAA,EAAAxB,IAAA,WAAAC,QAAA;QACAsB,MAAA,CAAAtC,OAAA,CAAAjB,IAAA,GAAAiC,QAAA,CAAAjC,IAAA;QACAuD,MAAA,CAAAtC,OAAA,CAAAC,IAAA;MACA;IACA;IACA,WACAuC,eAAA,WAAAA,gBAAAC,IAAA,EAAAC,GAAA;MACA,IAAAC,MAAA,GAAAD,GAAA,CAAAE,SAAA,CAAAF,GAAA,CAAAG,WAAA,WAAAH,GAAA,CAAAI,OAAA;MACA,IAAAC,QAAA,GAAAJ,MAAA,CAAAC,SAAA,CAAAD,MAAA,CAAAG,OAAA,WAAAH,MAAA,CAAAK,MAAA;MACA,IAAAC,MAAA,GAAAvE,IAAA,CAAAwE,SAAA,CAAAH,QAAA,EAAAN,IAAA;MACA,OAAAQ,MAAA,CAAAE,KAAA;IACA;IACA;IACAC,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAnE,GAAA,GAAAmE,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAhB,OAAA;MAAA;MACA,KAAApD,UAAA,GAAAkE,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAA1D,SAAA;MAAA;MACA,KAAAT,MAAA,GAAAiE,SAAA,CAAAL,MAAA;MACA,KAAA3D,QAAA,IAAAgE,SAAA,CAAAL,MAAA;IACA;IACA,aACAQ,eAAA,WAAAA,gBAAApC,GAAA;MACA,IAAAmB,OAAA,GAAAnB,GAAA,CAAAmB,OAAA,SAAArD,GAAA;MACA,KAAAuE,OAAA,CAAAC,IAAA,gBAAAnB,OAAA;IACA;IACA,aACAoB,YAAA,WAAAA,aAAAvC,GAAA;MAAA,IAAAwC,MAAA;MACA,IAAAC,QAAA,GAAAzC,GAAA,CAAAmB,OAAA,SAAArD,GAAA;MACA,KAAA0C,QAAA,iBAAAiC,QAAA;QACAhC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAAhB,IAAA;QACA,OAAA1C,QAAA,CAAAwF,QAAA;MACA,GAAA9C,IAAA;QACA6C,MAAA,CAAAvD,OAAA;QACAuD,MAAA,CAAApC,UAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}