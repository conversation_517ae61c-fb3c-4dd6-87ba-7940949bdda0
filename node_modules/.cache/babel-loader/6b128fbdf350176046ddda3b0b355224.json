{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Process/components/nodePanel/property/taskListener.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Process/components/nodePanel/property/taskListener.vue", "mtime": 1641277326000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js", "mtime": 1751171659043}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}