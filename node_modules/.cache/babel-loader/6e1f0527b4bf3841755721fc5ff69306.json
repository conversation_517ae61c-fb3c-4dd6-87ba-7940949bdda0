{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/utils/pluginsConfig.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/utils/pluginsConfig.js", "mtime": 1641277326000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:dmFyIENETiA9ICdodHRwczovL2xpYi5iYW9taXR1LmNvbS8nOyAvLyBDRE4gSG9tZXBhZ2U6IGh0dHBzOi8vY2RuLmJhb21pdHUuY29tLwp2YXIgcHVibGljUGF0aCA9IHByb2Nlc3MuZW52LkJBU0VfVVJMOwpmdW5jdGlvbiBzcGxpY2luZ1BsdWdpblVybChQbHVnaW5OYW1lLCB2ZXJzaW9uLCBmaWxlTmFtZSkgewogIHJldHVybiAiIi5jb25jYXQoQ0ROKS5jb25jYXQoUGx1Z2luTmFtZSwgIi8iKS5jb25jYXQodmVyc2lvbiwgIi8iKS5jb25jYXQoZmlsZU5hbWUpOwp9CmV4cG9ydCBkZWZhdWx0IHsKICBiZWF1dGlmaWVyVXJsOiBzcGxpY2luZ1BsdWdpblVybCgnanMtYmVhdXRpZnknLCAnMS4xMy41JywgJ2JlYXV0aWZpZXIubWluLmpzJyksCiAgLy8gbW9uYWNvRWRpdG9yVXJsOiBzcGxpY2luZ1BsdWdpblVybCgnbW9uYWNvLWVkaXRvcicsICcwLjE5LjMnLCAnbWluL3ZzJyksIC8vIOS9v+eUqCBtb25hY28tZWRpdG9yIENETiDpk77mjqUKICBtb25hY29FZGl0b3JVcmw6ICIiLmNvbmNhdChwdWJsaWNQYXRoLCAibGlicy9tb25hY28tZWRpdG9yL3ZzIiksCiAgLy8g5L2/55SoIG1vbmFjby1lZGl0b3Ig5pys5Zyw5Luj56CBCiAgdGlueW1jZVVybDogc3BsaWNpbmdQbHVnaW5VcmwoJ3RpbnltY2UnLCAnNS43LjAnLCAndGlueW1jZS5taW4uanMnKQp9Ow=="}, {"version": 3, "names": ["CDN", "publicPath", "process", "env", "BASE_URL", "splicingPluginUrl", "PluginName", "version", "fileName", "concat", "beautifierUrl", "monacoEditorUrl", "tinymceUrl"], "sources": ["/Users/<USER>/develop/code/project/haijia/adminweb/src/utils/pluginsConfig.js"], "sourcesContent": ["const CDN = 'https://lib.baomitu.com/' // CDN Homepage: https://cdn.baomitu.com/\nconst publicPath = process.env.BASE_URL\n\nfunction splicingPluginUrl(PluginName, version, fileName) {\n  return `${CDN}${PluginName}/${version}/${fileName}`\n}\n\nexport default {\n  beautifierUrl: splicingPluginUrl('js-beautify', '1.13.5', 'beautifier.min.js'),\n  // monacoEditorUrl: splicingPluginUrl('monaco-editor', '0.19.3', 'min/vs'), // 使用 monaco-editor CDN 链接\n  monacoEditorUrl: `${publicPath}libs/monaco-editor/vs`, // 使用 monaco-editor 本地代码\n  tinymceUrl: splicingPluginUrl('tinymce', '5.7.0', 'tinymce.min.js')\n}\n"], "mappings": "AAAA,IAAMA,GAAG,GAAG,0BAA0B,EAAC;AACvC,IAAMC,UAAU,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ;AAEvC,SAASC,iBAAiBA,CAACC,UAAU,EAAEC,OAAO,EAAEC,QAAQ,EAAE;EACxD,UAAAC,MAAA,CAAUT,GAAG,EAAAS,MAAA,CAAGH,UAAU,OAAAG,MAAA,CAAIF,OAAO,OAAAE,MAAA,CAAID,QAAQ;AACnD;AAEA,eAAe;EACbE,aAAa,EAAEL,iBAAiB,CAAC,aAAa,EAAE,QAAQ,EAAE,mBAAmB,CAAC;EAC9E;EACAM,eAAe,KAAAF,MAAA,CAAKR,UAAU,0BAAuB;EAAE;EACvDW,UAAU,EAAEP,iBAAiB,CAAC,SAAS,EAAE,OAAO,EAAE,gBAAgB;AACpE,CAAC", "ignoreList": []}]}