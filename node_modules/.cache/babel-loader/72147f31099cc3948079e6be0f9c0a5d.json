{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/flowable/task/finished/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/flowable/task/finished/index.vue", "mtime": 1650978246000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["finishedList", "getDeployment", "delDeployment", "addDeployment", "updateDeployment", "exportDeployment", "revokeProcess", "name", "components", "data", "loading", "ids", "single", "multiple", "showSearch", "total", "title", "open", "src", "queryParams", "pageNum", "pageSize", "category", "key", "tenantId", "deployTime", "derivedFrom", "derivedFromRoot", "parentDeploymentId", "engineVersion", "form", "rules", "created", "getList", "methods", "_this", "then", "response", "records", "cancel", "reset", "id", "resetForm", "setIcon", "val", "setColor", "handleQuery", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "map", "item", "length", "handleAdd", "handleFlowRecord", "row", "path", "procDef<PERSON>ey", "$router", "push", "query", "businessKey", "procInsId", "taskId", "finished", "handleRevoke", "_this2", "params", "instanceId", "res", "msgSuccess", "msg", "handleUpdate", "_this3", "submitForm", "_this4", "$refs", "validate", "valid", "handleDelete", "_this5", "$confirm", "confirmButtonText", "cancelButtonText", "type", "handleExport", "_this6", "download"], "sources": ["src/views/flowable/task/finished/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"名称\" prop=\"name\">\n        <el-input\n          v-model=\"queryParams.name\"\n          placeholder=\"请输入名称\"\n          clearable\n          size=\"small\"\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"开始时间\" prop=\"deployTime\">\n        <el-date-picker clearable size=\"small\"\n                        v-model=\"queryParams.deployTime\"\n                        type=\"date\"\n                        value-format=\"yyyy-MM-dd\"\n                        placeholder=\"选择时间\">\n        </el-date-picker>\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"danger\"\n          plain\n          icon=\"el-icon-delete\"\n          size=\"mini\"\n          :disabled=\"multiple\"\n          @click=\"handleDelete\"\n          v-hasPermi=\"['system:deployment:remove']\"\n        >删除</el-button>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <el-table v-loading=\"loading\" :data=\"finishedList\" border @selection-change=\"handleSelectionChange\">\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column label=\"任务编号\" align=\"center\" prop=\"taskId\" :show-overflow-tooltip=\"true\"/>\n      <el-table-column label=\"流程名称\" align=\"center\" prop=\"procDefName\" :show-overflow-tooltip=\"true\"/>\n      <el-table-column label=\"任务名称\" align=\"center\" prop=\"name\" :show-overflow-tooltip=\"true\"/>\n      <el-table-column label=\"任务节点\" align=\"center\" prop=\"taskName\" />\n      <el-table-column label=\"流程发起人\" align=\"center\">\n        <template slot-scope=\"scope\">\n          <label>{{scope.row.startUserName}} <el-tag type=\"info\" size=\"mini\">{{scope.row.startDeptName}}</el-tag></label>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"接收时间\" align=\"center\" prop=\"createTime\" width=\"180\"/>\n      <el-table-column label=\"审批时间\" align=\"center\" prop=\"finishTime\" width=\"180\"/>\n      <el-table-column label=\"耗时\" align=\"center\" prop=\"duration\" width=\"180\"/>\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-tickets\"\n            @click=\"handleFlowRecord(scope.row)\"\n          >流转记录</el-button>\n           <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-tickets\"\n            @click=\"handleRevoke(scope.row)\"\n          >撤回\n          </el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n  </div>\n</template>\n\n<script>\nimport { finishedList, getDeployment, delDeployment, addDeployment, updateDeployment, exportDeployment, revokeProcess } from \"@/api/flowable/finished\";\n\nexport default {\n  name: \"Deploy\",\n  components: {\n  },\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 已办任务列表数据\n      finishedList: [],\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      open: false,\n      src: \"\",\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        name: null,\n        category: null,\n        key: null,\n        tenantId: null,\n        deployTime: null,\n        derivedFrom: null,\n        derivedFromRoot: null,\n        parentDeploymentId: null,\n        engineVersion: null\n      },\n      // 表单参数\n      form: {},\n      // 表单校验\n      rules: {\n      }\n    };\n  },\n  created() {\n    this.getList();\n  },\n  methods: {\n    /** 查询流程定义列表 */\n    getList() {\n      this.loading = true;\n      finishedList(this.queryParams).then(response => {\n        this.finishedList = response.data.records;\n        this.total = response.data.total;\n        this.loading = false;\n      });\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        id: null,\n        name: null,\n        category: null,\n        key: null,\n        tenantId: null,\n        deployTime: null,\n        derivedFrom: null,\n        derivedFromRoot: null,\n        parentDeploymentId: null,\n        engineVersion: null\n      };\n      this.resetForm(\"form\");\n    },\n    setIcon(val){\n      if (val){\n        return \"el-icon-check\";\n      }else {\n        return \"el-icon-time\";\n      }\n\n    },\n    setColor(val){\n      if (val){\n        return \"#2bc418\";\n      }else {\n        return \"#b3bdbb\";\n      }\n\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.id)\n      this.single = selection.length!==1\n      this.multiple = !selection.length\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.reset();\n      this.open = true;\n      this.title = \"添加流程定义\";\n    },\n    /** 流程流转记录 */\n    handleFlowRecord(row){\n      // this.$router.push({ path: '/flowable/task/record/index',\n      //   query: {\n      //     procInsId: row.procInsId,\n      //     deployId: row.deployId,\n      //     taskId: row.taskId,\n      //     finished: false\n      // }})\n      var path;\n      if(row.procDefKey == 'process_project_report'){\n        path = '/project/report/form';\n      }else if(row.procDefKey == 'process_user_reg'){\n        path = '/system/user/form';\n      }\n      this.$router.push({ path: path,\n        query: {\n          businessKey: row.businessKey,\n          procInsId: row.procInsId,\n          taskId: row.taskId,\n          finished: true\n      }})\n    },\n    /** 撤回任务 */\n    handleRevoke(row){\n      const params = {\n        instanceId: row.procInsId\n      }\n      revokeProcess(params).then( res => {\n        this.msgSuccess(res.msg);\n        this.getList();\n      });\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset();\n      const id = row.id || this.ids\n      getDeployment(id).then(response => {\n        this.form = response.data;\n        this.open = true;\n        this.title = \"修改流程定义\";\n      });\n    },\n    /** 提交按钮 */\n    submitForm() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          if (this.form.id != null) {\n            updateDeployment(this.form).then(response => {\n              this.msgSuccess(\"修改成功\");\n              this.open = false;\n              this.getList();\n            });\n          } else {\n            addDeployment(this.form).then(response => {\n              this.msgSuccess(\"新增成功\");\n              this.open = false;\n              this.getList();\n            });\n          }\n        }\n      });\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const ids = row.id || this.ids;\n      this.$confirm('是否确认删除流程定义编号为\"' + ids + '\"的数据项?', \"警告\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      }).then(function() {\n        return delDeployment(ids);\n      }).then(() => {\n        this.getList();\n        this.msgSuccess(\"删除成功\");\n      })\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      const queryParams = this.queryParams;\n      this.$confirm('是否确认导出所有流程定义数据项?', \"警告\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      }).then(function() {\n        return exportDeployment(queryParams);\n      }).then(response => {\n        this.download(response.msg);\n      })\n    }\n  }\n};\n</script>\n\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqFA,SAAAA,YAAA,EAAAC,aAAA,EAAAC,aAAA,EAAAC,aAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,aAAA;AAEA;EACAC,IAAA;EACAC,UAAA,GACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAf,YAAA;MACA;MACAgB,KAAA;MACA;MACAC,IAAA;MACAC,GAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAd,IAAA;QACAe,QAAA;QACAC,GAAA;QACAC,QAAA;QACAC,UAAA;QACAC,WAAA;QACAC,eAAA;QACAC,kBAAA;QACAC,aAAA;MACA;MACA;MACAC,IAAA;MACA;MACAC,KAAA,GACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA,eACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAAzB,OAAA;MACAV,YAAA,MAAAmB,WAAA,EAAAiB,IAAA,WAAAC,QAAA;QACAF,KAAA,CAAAnC,YAAA,GAAAqC,QAAA,CAAA5B,IAAA,CAAA6B,OAAA;QACAH,KAAA,CAAApB,KAAA,GAAAsB,QAAA,CAAA5B,IAAA,CAAAM,KAAA;QACAoB,KAAA,CAAAzB,OAAA;MACA;IACA;IACA;IACA6B,MAAA,WAAAA,OAAA;MACA,KAAAtB,IAAA;MACA,KAAAuB,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAAV,IAAA;QACAW,EAAA;QACAlC,IAAA;QACAe,QAAA;QACAC,GAAA;QACAC,QAAA;QACAC,UAAA;QACAC,WAAA;QACAC,eAAA;QACAC,kBAAA;QACAC,aAAA;MACA;MACA,KAAAa,SAAA;IACA;IACAC,OAAA,WAAAA,QAAAC,GAAA;MACA,IAAAA,GAAA;QACA;MACA;QACA;MACA;IAEA;IACAC,QAAA,WAAAA,SAAAD,GAAA;MACA,IAAAA,GAAA;QACA;MACA;QACA;MACA;IAEA;IACA,aACAE,WAAA,WAAAA,YAAA;MACA,KAAA3B,WAAA,CAAAC,OAAA;MACA,KAAAa,OAAA;IACA;IACA,aACAc,UAAA,WAAAA,WAAA;MACA,KAAAL,SAAA;MACA,KAAAI,WAAA;IACA;IACA;IACAE,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAtC,GAAA,GAAAsC,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAV,EAAA;MAAA;MACA,KAAA7B,MAAA,GAAAqC,SAAA,CAAAG,MAAA;MACA,KAAAvC,QAAA,IAAAoC,SAAA,CAAAG,MAAA;IACA;IACA,aACAC,SAAA,WAAAA,UAAA;MACA,KAAAb,KAAA;MACA,KAAAvB,IAAA;MACA,KAAAD,KAAA;IACA;IACA,aACAsC,gBAAA,WAAAA,iBAAAC,GAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAAC,IAAA;MACA,IAAAD,GAAA,CAAAE,UAAA;QACAD,IAAA;MACA,WAAAD,GAAA,CAAAE,UAAA;QACAD,IAAA;MACA;MACA,KAAAE,OAAA,CAAAC,IAAA;QAAAH,IAAA,EAAAA,IAAA;QACAI,KAAA;UACAC,WAAA,EAAAN,GAAA,CAAAM,WAAA;UACAC,SAAA,EAAAP,GAAA,CAAAO,SAAA;UACAC,MAAA,EAAAR,GAAA,CAAAQ,MAAA;UACAC,QAAA;QACA;MAAA;IACA;IACA,WACAC,YAAA,WAAAA,aAAAV,GAAA;MAAA,IAAAW,MAAA;MACA,IAAAC,MAAA;QACAC,UAAA,EAAAb,GAAA,CAAAO;MACA;MACAxD,aAAA,CAAA6D,MAAA,EAAA/B,IAAA,WAAAiC,GAAA;QACAH,MAAA,CAAAI,UAAA,CAAAD,GAAA,CAAAE,GAAA;QACAL,MAAA,CAAAjC,OAAA;MACA;IACA;IACA,aACAuC,YAAA,WAAAA,aAAAjB,GAAA;MAAA,IAAAkB,MAAA;MACA,KAAAjC,KAAA;MACA,IAAAC,EAAA,GAAAc,GAAA,CAAAd,EAAA,SAAA9B,GAAA;MACAV,aAAA,CAAAwC,EAAA,EAAAL,IAAA,WAAAC,QAAA;QACAoC,MAAA,CAAA3C,IAAA,GAAAO,QAAA,CAAA5B,IAAA;QACAgE,MAAA,CAAAxD,IAAA;QACAwD,MAAA,CAAAzD,KAAA;MACA;IACA;IACA,WACA0D,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAH,MAAA,CAAA7C,IAAA,CAAAW,EAAA;YACArC,gBAAA,CAAAuE,MAAA,CAAA7C,IAAA,EAAAM,IAAA,WAAAC,QAAA;cACAsC,MAAA,CAAAL,UAAA;cACAK,MAAA,CAAA1D,IAAA;cACA0D,MAAA,CAAA1C,OAAA;YACA;UACA;YACA9B,aAAA,CAAAwE,MAAA,CAAA7C,IAAA,EAAAM,IAAA,WAAAC,QAAA;cACAsC,MAAA,CAAAL,UAAA;cACAK,MAAA,CAAA1D,IAAA;cACA0D,MAAA,CAAA1C,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACA8C,YAAA,WAAAA,aAAAxB,GAAA;MAAA,IAAAyB,MAAA;MACA,IAAArE,GAAA,GAAA4C,GAAA,CAAAd,EAAA,SAAA9B,GAAA;MACA,KAAAsE,QAAA,oBAAAtE,GAAA;QACAuE,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAAhD,IAAA;QACA,OAAAlC,aAAA,CAAAS,GAAA;MACA,GAAAyB,IAAA;QACA4C,MAAA,CAAA/C,OAAA;QACA+C,MAAA,CAAAV,UAAA;MACA;IACA;IACA,aACAe,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,IAAAnE,WAAA,QAAAA,WAAA;MACA,KAAA8D,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAAhD,IAAA;QACA,OAAA/B,gBAAA,CAAAc,WAAA;MACA,GAAAiB,IAAA,WAAAC,QAAA;QACAiD,MAAA,CAAAC,QAAA,CAAAlD,QAAA,CAAAkC,GAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}