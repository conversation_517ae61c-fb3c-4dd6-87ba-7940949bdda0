{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/utils/ruoyi.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/utils/ruoyi.js", "mtime": 1655226564000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js", "mtime": 1751171659043}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}