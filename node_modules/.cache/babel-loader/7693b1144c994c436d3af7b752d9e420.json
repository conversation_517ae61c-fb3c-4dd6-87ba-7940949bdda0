{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/tool/build/TreeNodeDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/tool/build/TreeNodeDialog.vue", "mtime": 1641277326000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["isNumberStr", "getTreeNodeId", "saveTreeNodeId", "id", "components", "inheritAttrs", "props", "data", "formData", "label", "undefined", "value", "rules", "required", "message", "trigger", "dataType", "dataTypeOptions", "computed", "watch", "formDataValue", "val", "created", "mounted", "methods", "onOpen", "onClose", "close", "$emit", "handelConfirm", "_this", "$refs", "elForm", "validate", "valid", "parseFloat"], "sources": ["src/views/tool/build/TreeNodeDialog.vue"], "sourcesContent": ["<template>\n  <div>\n    <el-dialog\n      v-bind=\"$attrs\"\n      :close-on-click-modal=\"false\"\n      :modal-append-to-body=\"false\"\n      v-on=\"$listeners\"\n      @open=\"onOpen\"\n      @close=\"onClose\"\n    >\n      <el-row :gutter=\"0\">\n        <el-form\n          ref=\"elForm\"\n          :model=\"formData\"\n          :rules=\"rules\"\n          size=\"small\"\n          label-width=\"100px\"\n        >\n          <el-col :span=\"24\">\n            <el-form-item\n              label=\"选项名\"\n              prop=\"label\"\n            >\n              <el-input\n                v-model=\"formData.label\"\n                placeholder=\"请输入选项名\"\n                clearable\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item\n              label=\"选项值\"\n              prop=\"value\"\n            >\n              <el-input\n                v-model=\"formData.value\"\n                placeholder=\"请输入选项值\"\n                clearable\n              >\n                <el-select\n                  slot=\"append\"\n                  v-model=\"dataType\"\n                  :style=\"{width: '100px'}\"\n                >\n                  <el-option\n                    v-for=\"(item, index) in dataTypeOptions\"\n                    :key=\"index\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                    :disabled=\"item.disabled\"\n                  />\n                </el-select>\n              </el-input>\n            </el-form-item>\n          </el-col>\n        </el-form>\n      </el-row>\n      <div slot=\"footer\">\n        <el-button\n          type=\"primary\"\n          @click=\"handelConfirm\"\n        >\n          确定\n        </el-button>\n        <el-button @click=\"close\">\n          取消\n        </el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n<script>\nimport { isNumberStr } from '@/utils/index'\nimport { getTreeNodeId, saveTreeNodeId } from '@/utils/db'\n\nconst id = getTreeNodeId()\n\nexport default {\n  components: {},\n  inheritAttrs: false,\n  props: [],\n  data() {\n    return {\n      id,\n      formData: {\n        label: undefined,\n        value: undefined\n      },\n      rules: {\n        label: [\n          {\n            required: true,\n            message: '请输入选项名',\n            trigger: 'blur'\n          }\n        ],\n        value: [\n          {\n            required: true,\n            message: '请输入选项值',\n            trigger: 'blur'\n          }\n        ]\n      },\n      dataType: 'string',\n      dataTypeOptions: [\n        {\n          label: '字符串',\n          value: 'string'\n        },\n        {\n          label: '数字',\n          value: 'number'\n        }\n      ]\n    }\n  },\n  computed: {},\n  watch: {\n    // eslint-disable-next-line func-names\n    'formData.value': function (val) {\n      this.dataType = isNumberStr(val) ? 'number' : 'string'\n    },\n    id(val) {\n      saveTreeNodeId(val)\n    }\n  },\n  created() {},\n  mounted() {},\n  methods: {\n    onOpen() {\n      this.formData = {\n        label: undefined,\n        value: undefined\n      }\n    },\n    onClose() {},\n    close() {\n      this.$emit('update:visible', false)\n    },\n    handelConfirm() {\n      this.$refs.elForm.validate(valid => {\n        if (!valid) return\n        if (this.dataType === 'number') {\n          this.formData.value = parseFloat(this.formData.value)\n        }\n        this.formData.id = this.id++\n        this.$emit('commit', this.formData)\n        this.close()\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyEA,SAAAA,WAAA;AACA,SAAAC,aAAA,EAAAC,cAAA;AAEA,IAAAC,EAAA,GAAAF,aAAA;AAEA;EACAG,UAAA;EACAC,YAAA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAJ,EAAA,EAAAA,EAAA;MACAK,QAAA;QACAC,KAAA,EAAAC,SAAA;QACAC,KAAA,EAAAD;MACA;MACAE,KAAA;QACAH,KAAA,GACA;UACAI,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACAJ,KAAA,GACA;UACAE,QAAA;UACAC,OAAA;UACAC,OAAA;QACA;MAEA;MACAC,QAAA;MACAC,eAAA,GACA;QACAR,KAAA;QACAE,KAAA;MACA,GACA;QACAF,KAAA;QACAE,KAAA;MACA;IAEA;EACA;EACAO,QAAA;EACAC,KAAA;IACA;IACA,2BAAAC,cAAAC,GAAA;MACA,KAAAL,QAAA,GAAAhB,WAAA,CAAAqB,GAAA;IACA;IACAlB,EAAA,WAAAA,GAAAkB,GAAA;MACAnB,cAAA,CAAAmB,GAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;EACAC,OAAA,WAAAA,QAAA;EACAC,OAAA;IACAC,MAAA,WAAAA,OAAA;MACA,KAAAjB,QAAA;QACAC,KAAA,EAAAC,SAAA;QACAC,KAAA,EAAAD;MACA;IACA;IACAgB,OAAA,WAAAA,QAAA;IACAC,KAAA,WAAAA,MAAA;MACA,KAAAC,KAAA;IACA;IACAC,aAAA,WAAAA,cAAA;MAAA,IAAAC,KAAA;MACA,KAAAC,KAAA,CAAAC,MAAA,CAAAC,QAAA,WAAAC,KAAA;QACA,KAAAA,KAAA;QACA,IAAAJ,KAAA,CAAAd,QAAA;UACAc,KAAA,CAAAtB,QAAA,CAAAG,KAAA,GAAAwB,UAAA,CAAAL,KAAA,CAAAtB,QAAA,CAAAG,KAAA;QACA;QACAmB,KAAA,CAAAtB,QAAA,CAAAL,EAAA,GAAA2B,KAAA,CAAA3B,EAAA;QACA2B,KAAA,CAAAF,KAAA,WAAAE,KAAA,CAAAtB,QAAA;QACAsB,KAAA,CAAAH,KAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}