{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/components/RightPanel/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/components/RightPanel/index.vue", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["addClass", "removeClass", "name", "props", "clickNotClose", "default", "type", "Boolean", "buttonTop", "Number", "computed", "show", "get", "$store", "state", "settings", "showSettings", "set", "val", "dispatch", "key", "value", "theme", "watch", "addEventClick", "document", "body", "mounted", "insertToBody", "<PERSON><PERSON><PERSON><PERSON>", "elx", "$refs", "rightPanel", "remove", "methods", "window", "addEventListener", "closeSidebar", "evt", "parent", "target", "closest", "removeEventListener", "querySelector", "insertBefore", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["src/components/RightPanel/index.vue"], "sourcesContent": ["<template>\n  <div ref=\"rightPanel\" :class=\"{show:show}\" class=\"rightPanel-container\">\n    <div class=\"rightPanel-background\" />\n    <div class=\"rightPanel\">\n      <div class=\"rightPanel-items\">\n        <slot />\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { addClass, removeClass } from '@/utils'\n\nexport default {\n  name: 'RightPanel',\n  props: {\n    clickNotClose: {\n      default: false,\n      type: Boolean\n    },\n    buttonTop: {\n      default: 250,\n      type: Number\n    }\n  },\n  computed: {\n    show: {\n      get() {\n        return this.$store.state.settings.showSettings\n      },\n      set(val) {\n        this.$store.dispatch('settings/changeSetting', {\n          key: 'showSettings',\n          value: val\n        })\n      }\n    },\n    theme() {\n      return this.$store.state.settings.theme\n    },\n  },\n  watch: {\n    show(value) {\n      if (value && !this.clickNotClose) {\n        this.addEventClick()\n      }\n      if (value) {\n        addClass(document.body, 'showRightPanel')\n      } else {\n        removeClass(document.body, 'showRightPanel')\n      }\n    }\n  },\n  mounted() {\n    this.insertToBody()\n    this.addEventClick()\n  },\n  beforeDestroy() {\n    const elx = this.$refs.rightPanel\n    elx.remove()\n  },\n  methods: {\n    addEventClick() {\n      window.addEventListener('click', this.closeSidebar)\n    },\n    closeSidebar(evt) {\n      const parent = evt.target.closest('.rightPanel')\n      if (!parent) {\n        this.show = false\n        window.removeEventListener('click', this.closeSidebar)\n      }\n    },\n    insertToBody() {\n      const elx = this.$refs.rightPanel\n      const body = document.querySelector('body')\n      body.insertBefore(elx, body.firstChild)\n    }\n  }\n}\n</script>\n\n<style>\n.showRightPanel {\n  overflow: hidden;\n  position: relative;\n  width: calc(100% - 15px);\n}\n</style>\n\n<style lang=\"scss\" scoped>\n.rightPanel-background {\n  position: fixed;\n  top: 0;\n  left: 0;\n  opacity: 0;\n  transition: opacity .3s cubic-bezier(.7, .3, .1, 1);\n  background: rgba(0, 0, 0, .2);\n  z-index: -1;\n}\n\n.rightPanel {\n  width: 100%;\n  max-width: 260px;\n  height: 100vh;\n  position: fixed;\n  top: 0;\n  right: 0;\n  box-shadow: 0px 0px 15px 0px rgba(0, 0, 0, .05);\n  transition: all .25s cubic-bezier(.7, .3, .1, 1);\n  transform: translate(100%);\n  background: #fff;\n  z-index: 40000;\n}\n\n.show {\n  transition: all .3s cubic-bezier(.7, .3, .1, 1);\n\n  .rightPanel-background {\n    z-index: 20000;\n    opacity: 1;\n    width: 100%;\n    height: 100%;\n  }\n\n  .rightPanel {\n    transform: translate(0);\n  }\n}\n\n.handle-button {\n  width: 48px;\n  height: 48px;\n  position: absolute;\n  left: -48px;\n  text-align: center;\n  font-size: 24px;\n  border-radius: 6px 0 0 6px !important;\n  z-index: 0;\n  pointer-events: auto;\n  cursor: pointer;\n  color: #fff;\n  line-height: 48px;\n  i {\n    font-size: 24px;\n    line-height: 48px;\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;AAYA,SAAAA,QAAA,EAAAC,WAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACAC,aAAA;MACAC,OAAA;MACAC,IAAA,EAAAC;IACA;IACAC,SAAA;MACAH,OAAA;MACAC,IAAA,EAAAG;IACA;EACA;EACAC,QAAA;IACAC,IAAA;MACAC,GAAA,WAAAA,IAAA;QACA,YAAAC,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAC,YAAA;MACA;MACAC,GAAA,WAAAA,IAAAC,GAAA;QACA,KAAAL,MAAA,CAAAM,QAAA;UACAC,GAAA;UACAC,KAAA,EAAAH;QACA;MACA;IACA;IACAI,KAAA,WAAAA,MAAA;MACA,YAAAT,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAO,KAAA;IACA;EACA;EACAC,KAAA;IACAZ,IAAA,WAAAA,KAAAU,KAAA;MACA,IAAAA,KAAA,UAAAjB,aAAA;QACA,KAAAoB,aAAA;MACA;MACA,IAAAH,KAAA;QACArB,QAAA,CAAAyB,QAAA,CAAAC,IAAA;MACA;QACAzB,WAAA,CAAAwB,QAAA,CAAAC,IAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,YAAA;IACA,KAAAJ,aAAA;EACA;EACAK,aAAA,WAAAA,cAAA;IACA,IAAAC,GAAA,QAAAC,KAAA,CAAAC,UAAA;IACAF,GAAA,CAAAG,MAAA;EACA;EACAC,OAAA;IACAV,aAAA,WAAAA,cAAA;MACAW,MAAA,CAAAC,gBAAA,eAAAC,YAAA;IACA;IACAA,YAAA,WAAAA,aAAAC,GAAA;MACA,IAAAC,MAAA,GAAAD,GAAA,CAAAE,MAAA,CAAAC,OAAA;MACA,KAAAF,MAAA;QACA,KAAA5B,IAAA;QACAwB,MAAA,CAAAO,mBAAA,eAAAL,YAAA;MACA;IACA;IACAT,YAAA,WAAAA,aAAA;MACA,IAAAE,GAAA,QAAAC,KAAA,CAAAC,UAAA;MACA,IAAAN,IAAA,GAAAD,QAAA,CAAAkB,aAAA;MACAjB,IAAA,CAAAkB,YAAA,CAAAd,GAAA,EAAAJ,IAAA,CAAAmB,UAAA;IACA;EACA;AACA", "ignoreList": []}]}