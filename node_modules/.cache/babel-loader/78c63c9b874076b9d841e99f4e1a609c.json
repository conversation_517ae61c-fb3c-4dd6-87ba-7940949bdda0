{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/tool/build/RightPanel.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/tool/build/RightPanel.vue", "mtime": 1641277326000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js", "mtime": 1751171659043}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGlzQXJyYXkgfSBmcm9tICd1dGlsJzsKaW1wb3J0IFRyZWVOb2RlRGlhbG9nIGZyb20gJy4vVHJlZU5vZGVEaWFsb2cnOwppbXBvcnQgeyBpc051bWJlclN0ciB9IGZyb20gJ0AvdXRpbHMvaW5kZXgnOwppbXBvcnQgSWNvbnNEaWFsb2cgZnJvbSAnLi9JY29uc0RpYWxvZyc7CmltcG9ydCB7IGlucHV0Q29tcG9uZW50cywgc2VsZWN0Q29tcG9uZW50cywgbGF5b3V0Q29tcG9uZW50cyB9IGZyb20gJ0AvdXRpbHMvZ2VuZXJhdG9yL2NvbmZpZyc7CmltcG9ydCB7IHNhdmVGb3JtQ29uZiB9IGZyb20gJ0AvdXRpbHMvZGInOwp2YXIgZGF0ZVRpbWVGb3JtYXQgPSB7CiAgZGF0ZTogJ3l5eXktTU0tZGQnLAogIHdlZWs6ICd5eXl5IOesrCBXVyDlkagnLAogIG1vbnRoOiAneXl5eS1NTScsCiAgeWVhcjogJ3l5eXknLAogIGRhdGV0aW1lOiAneXl5eS1NTS1kZCBISDptbTpzcycsCiAgZGF0ZXJhbmdlOiAneXl5eS1NTS1kZCcsCiAgbW9udGhyYW5nZTogJ3l5eXktTU0nLAogIGRhdGV0aW1lcmFuZ2U6ICd5eXl5LU1NLWRkIEhIOm1tOnNzJwp9OwoKLy8g5L2/Y2hhbmdlUmVuZGVyS2V55Zyo55uu5qCH57uE5Lu25pS55Y+Y5pe25Y+v55SoCnZhciBuZWVkUmVyZW5kZXJMaXN0ID0gWyd0aW55bWNlJ107CmV4cG9ydCBkZWZhdWx0IHsKICBjb21wb25lbnRzOiB7CiAgICBUcmVlTm9kZURpYWxvZzogVHJlZU5vZGVEaWFsb2csCiAgICBJY29uc0RpYWxvZzogSWNvbnNEaWFsb2cKICB9LAogIHByb3BzOiBbJ3Nob3dGaWVsZCcsICdhY3RpdmVEYXRhJywgJ2Zvcm1Db25mJ10sCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGN1cnJlbnRUYWI6ICdmaWVsZCcsCiAgICAgIGN1cnJlbnROb2RlOiBudWxsLAogICAgICBkaWFsb2dWaXNpYmxlOiBmYWxzZSwKICAgICAgaWNvbnNWaXNpYmxlOiBmYWxzZSwKICAgICAgY3VycmVudEljb25Nb2RlbDogbnVsbCwKICAgICAgZGF0ZVR5cGVPcHRpb25zOiBbewogICAgICAgIGxhYmVsOiAn5pelKGRhdGUpJywKICAgICAgICB2YWx1ZTogJ2RhdGUnCiAgICAgIH0sIHsKICAgICAgICBsYWJlbDogJ+WRqCh3ZWVrKScsCiAgICAgICAgdmFsdWU6ICd3ZWVrJwogICAgICB9LCB7CiAgICAgICAgbGFiZWw6ICfmnIgobW9udGgpJywKICAgICAgICB2YWx1ZTogJ21vbnRoJwogICAgICB9LCB7CiAgICAgICAgbGFiZWw6ICflubQoeWVhciknLAogICAgICAgIHZhbHVlOiAneWVhcicKICAgICAgfSwgewogICAgICAgIGxhYmVsOiAn5pel5pyf5pe26Ze0KGRhdGV0aW1lKScsCiAgICAgICAgdmFsdWU6ICdkYXRldGltZScKICAgICAgfV0sCiAgICAgIGRhdGVSYW5nZVR5cGVPcHRpb25zOiBbewogICAgICAgIGxhYmVsOiAn5pel5pyf6IyD5Zu0KGRhdGVyYW5nZSknLAogICAgICAgIHZhbHVlOiAnZGF0ZXJhbmdlJwogICAgICB9LCB7CiAgICAgICAgbGFiZWw6ICfmnIjojIPlm7QobW9udGhyYW5nZSknLAogICAgICAgIHZhbHVlOiAnbW9udGhyYW5nZScKICAgICAgfSwgewogICAgICAgIGxhYmVsOiAn5pel5pyf5pe26Ze06IyD5Zu0KGRhdGV0aW1lcmFuZ2UpJywKICAgICAgICB2YWx1ZTogJ2RhdGV0aW1lcmFuZ2UnCiAgICAgIH1dLAogICAgICBjb2xvckZvcm1hdE9wdGlvbnM6IFt7CiAgICAgICAgbGFiZWw6ICdoZXgnLAogICAgICAgIHZhbHVlOiAnaGV4JwogICAgICB9LCB7CiAgICAgICAgbGFiZWw6ICdyZ2InLAogICAgICAgIHZhbHVlOiAncmdiJwogICAgICB9LCB7CiAgICAgICAgbGFiZWw6ICdyZ2JhJywKICAgICAgICB2YWx1ZTogJ3JnYmEnCiAgICAgIH0sIHsKICAgICAgICBsYWJlbDogJ2hzdicsCiAgICAgICAgdmFsdWU6ICdoc3YnCiAgICAgIH0sIHsKICAgICAgICBsYWJlbDogJ2hzbCcsCiAgICAgICAgdmFsdWU6ICdoc2wnCiAgICAgIH1dLAogICAgICBqdXN0aWZ5T3B0aW9uczogW3sKICAgICAgICBsYWJlbDogJ3N0YXJ0JywKICAgICAgICB2YWx1ZTogJ3N0YXJ0JwogICAgICB9LCB7CiAgICAgICAgbGFiZWw6ICdlbmQnLAogICAgICAgIHZhbHVlOiAnZW5kJwogICAgICB9LCB7CiAgICAgICAgbGFiZWw6ICdjZW50ZXInLAogICAgICAgIHZhbHVlOiAnY2VudGVyJwogICAgICB9LCB7CiAgICAgICAgbGFiZWw6ICdzcGFjZS1hcm91bmQnLAogICAgICAgIHZhbHVlOiAnc3BhY2UtYXJvdW5kJwogICAgICB9LCB7CiAgICAgICAgbGFiZWw6ICdzcGFjZS1iZXR3ZWVuJywKICAgICAgICB2YWx1ZTogJ3NwYWNlLWJldHdlZW4nCiAgICAgIH1dLAogICAgICBsYXlvdXRUcmVlUHJvcHM6IHsKICAgICAgICBsYWJlbDogZnVuY3Rpb24gbGFiZWwoZGF0YSwgbm9kZSkgewogICAgICAgICAgdmFyIGNvbmZpZyA9IGRhdGEuX19jb25maWdfXzsKICAgICAgICAgIHJldHVybiBkYXRhLmNvbXBvbmVudE5hbWUgfHwgIiIuY29uY2F0KGNvbmZpZy5sYWJlbCwgIjogIikuY29uY2F0KGRhdGEuX192TW9kZWxfXyk7CiAgICAgICAgfQogICAgICB9CiAgICB9OwogIH0sCiAgY29tcHV0ZWQ6IHsKICAgIGRvY3VtZW50TGluazogZnVuY3Rpb24gZG9jdW1lbnRMaW5rKCkgewogICAgICByZXR1cm4gdGhpcy5hY3RpdmVEYXRhLl9fY29uZmlnX18uZG9jdW1lbnQgfHwgJ2h0dHBzOi8vZWxlbWVudC5lbGVtZS5jbi8jL3poLUNOL2NvbXBvbmVudC9pbnN0YWxsYXRpb24nOwogICAgfSwKICAgIGRhdGVPcHRpb25zOiBmdW5jdGlvbiBkYXRlT3B0aW9ucygpIHsKICAgICAgaWYgKHRoaXMuYWN0aXZlRGF0YS50eXBlICE9PSB1bmRlZmluZWQgJiYgdGhpcy5hY3RpdmVEYXRhLl9fY29uZmlnX18udGFnID09PSAnZWwtZGF0ZS1waWNrZXInKSB7CiAgICAgICAgaWYgKHRoaXMuYWN0aXZlRGF0YVsnc3RhcnQtcGxhY2Vob2xkZXInXSA9PT0gdW5kZWZpbmVkKSB7CiAgICAgICAgICByZXR1cm4gdGhpcy5kYXRlVHlwZU9wdGlvbnM7CiAgICAgICAgfQogICAgICAgIHJldHVybiB0aGlzLmRhdGVSYW5nZVR5cGVPcHRpb25zOwogICAgICB9CiAgICAgIHJldHVybiBbXTsKICAgIH0sCiAgICB0YWdMaXN0OiBmdW5jdGlvbiB0YWdMaXN0KCkgewogICAgICByZXR1cm4gW3sKICAgICAgICBsYWJlbDogJ+i+k+WFpeWei+e7hOS7ticsCiAgICAgICAgb3B0aW9uczogaW5wdXRDb21wb25lbnRzCiAgICAgIH0sIHsKICAgICAgICBsYWJlbDogJ+mAieaLqeWei+e7hOS7ticsCiAgICAgICAgb3B0aW9uczogc2VsZWN0Q29tcG9uZW50cwogICAgICB9XTsKICAgIH0sCiAgICBhY3RpdmVUYWc6IGZ1bmN0aW9uIGFjdGl2ZVRhZygpIHsKICAgICAgcmV0dXJuIHRoaXMuYWN0aXZlRGF0YS5fX2NvbmZpZ19fLnRhZzsKICAgIH0sCiAgICBpc1Nob3dNaW46IGZ1bmN0aW9uIGlzU2hvd01pbigpIHsKICAgICAgcmV0dXJuIFsnZWwtaW5wdXQtbnVtYmVyJywgJ2VsLXNsaWRlciddLmluZGV4T2YodGhpcy5hY3RpdmVUYWcpID4gLTE7CiAgICB9LAogICAgaXNTaG93TWF4OiBmdW5jdGlvbiBpc1Nob3dNYXgoKSB7CiAgICAgIHJldHVybiBbJ2VsLWlucHV0LW51bWJlcicsICdlbC1zbGlkZXInLCAnZWwtcmF0ZSddLmluZGV4T2YodGhpcy5hY3RpdmVUYWcpID4gLTE7CiAgICB9LAogICAgaXNTaG93U3RlcDogZnVuY3Rpb24gaXNTaG93U3RlcCgpIHsKICAgICAgcmV0dXJuIFsnZWwtaW5wdXQtbnVtYmVyJywgJ2VsLXNsaWRlciddLmluZGV4T2YodGhpcy5hY3RpdmVUYWcpID4gLTE7CiAgICB9CiAgfSwKICB3YXRjaDogewogICAgZm9ybUNvbmY6IHsKICAgICAgaGFuZGxlcjogZnVuY3Rpb24gaGFuZGxlcih2YWwpIHsKICAgICAgICBzYXZlRm9ybUNvbmYodmFsKTsKICAgICAgfSwKICAgICAgZGVlcDogdHJ1ZQogICAgfQogIH0sCiAgbWV0aG9kczogewogICAgYWRkUmVnOiBmdW5jdGlvbiBhZGRSZWcoKSB7CiAgICAgIHRoaXMuYWN0aXZlRGF0YS5fX2NvbmZpZ19fLnJlZ0xpc3QucHVzaCh7CiAgICAgICAgcGF0dGVybjogJycsCiAgICAgICAgbWVzc2FnZTogJycKICAgICAgfSk7CiAgICB9LAogICAgYWRkU2VsZWN0SXRlbTogZnVuY3Rpb24gYWRkU2VsZWN0SXRlbSgpIHsKICAgICAgdGhpcy5hY3RpdmVEYXRhLl9fc2xvdF9fLm9wdGlvbnMucHVzaCh7CiAgICAgICAgbGFiZWw6ICcnLAogICAgICAgIHZhbHVlOiAnJwogICAgICB9KTsKICAgIH0sCiAgICBhZGRUcmVlSXRlbTogZnVuY3Rpb24gYWRkVHJlZUl0ZW0oKSB7CiAgICAgICsrdGhpcy5pZEdsb2JhbDsKICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gdHJ1ZTsKICAgICAgdGhpcy5jdXJyZW50Tm9kZSA9IHRoaXMuYWN0aXZlRGF0YS5vcHRpb25zOwogICAgfSwKICAgIHJlbmRlckNvbnRlbnQ6IGZ1bmN0aW9uIHJlbmRlckNvbnRlbnQoaCwgX3JlZikgewogICAgICB2YXIgX3RoaXMgPSB0aGlzOwogICAgICB2YXIgbm9kZSA9IF9yZWYubm9kZSwKICAgICAgICBkYXRhID0gX3JlZi5kYXRhLAogICAgICAgIHN0b3JlID0gX3JlZi5zdG9yZTsKICAgICAgcmV0dXJuIGgoImRpdiIsIHsKICAgICAgICAiY2xhc3MiOiAiY3VzdG9tLXRyZWUtbm9kZSIKICAgICAgfSwgW2goInNwYW4iLCBbbm9kZS5sYWJlbF0pLCBoKCJzcGFuIiwgewogICAgICAgICJjbGFzcyI6ICJub2RlLW9wZXJhdGlvbiIKICAgICAgfSwgW2goImkiLCB7CiAgICAgICAgIm9uIjogewogICAgICAgICAgImNsaWNrIjogZnVuY3Rpb24gY2xpY2soKSB7CiAgICAgICAgICAgIHJldHVybiBfdGhpcy5hcHBlbmQoZGF0YSk7CiAgICAgICAgICB9CiAgICAgICAgfSwKICAgICAgICAiY2xhc3MiOiAiZWwtaWNvbi1wbHVzIiwKICAgICAgICAiYXR0cnMiOiB7CiAgICAgICAgICAidGl0bGUiOiAi5re75YqgIgogICAgICAgIH0KICAgICAgfSksIGgoImkiLCB7CiAgICAgICAgIm9uIjogewogICAgICAgICAgImNsaWNrIjogZnVuY3Rpb24gY2xpY2soKSB7CiAgICAgICAgICAgIHJldHVybiBfdGhpcy5yZW1vdmUobm9kZSwgZGF0YSk7CiAgICAgICAgICB9CiAgICAgICAgfSwKICAgICAgICAiY2xhc3MiOiAiZWwtaWNvbi1kZWxldGUiLAogICAgICAgICJhdHRycyI6IHsKICAgICAgICAgICJ0aXRsZSI6ICLliKDpmaQiCiAgICAgICAgfQogICAgICB9KV0pXSk7CiAgICB9LAogICAgYXBwZW5kOiBmdW5jdGlvbiBhcHBlbmQoZGF0YSkgewogICAgICBpZiAoIWRhdGEuY2hpbGRyZW4pIHsKICAgICAgICB0aGlzLiRzZXQoZGF0YSwgJ2NoaWxkcmVuJywgW10pOwogICAgICB9CiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHRydWU7CiAgICAgIHRoaXMuY3VycmVudE5vZGUgPSBkYXRhLmNoaWxkcmVuOwogICAgfSwKICAgIHJlbW92ZTogZnVuY3Rpb24gcmVtb3ZlKG5vZGUsIGRhdGEpIHsKICAgICAgdGhpcy5hY3RpdmVEYXRhLl9fY29uZmlnX18uZGVmYXVsdFZhbHVlID0gW107IC8vIOmBv+WFjeWIoOmZpOaXtuaKpemUmQogICAgICB2YXIgcGFyZW50ID0gbm9kZS5wYXJlbnQ7CiAgICAgIHZhciBjaGlsZHJlbiA9IHBhcmVudC5kYXRhLmNoaWxkcmVuIHx8IHBhcmVudC5kYXRhOwogICAgICB2YXIgaW5kZXggPSBjaGlsZHJlbi5maW5kSW5kZXgoZnVuY3Rpb24gKGQpIHsKICAgICAgICByZXR1cm4gZC5pZCA9PT0gZGF0YS5pZDsKICAgICAgfSk7CiAgICAgIGNoaWxkcmVuLnNwbGljZShpbmRleCwgMSk7CiAgICB9LAogICAgYWRkTm9kZTogZnVuY3Rpb24gYWRkTm9kZShkYXRhKSB7CiAgICAgIHRoaXMuY3VycmVudE5vZGUucHVzaChkYXRhKTsKICAgIH0sCiAgICBzZXRPcHRpb25WYWx1ZTogZnVuY3Rpb24gc2V0T3B0aW9uVmFsdWUoaXRlbSwgdmFsKSB7CiAgICAgIGl0ZW0udmFsdWUgPSBpc051bWJlclN0cih2YWwpID8gK3ZhbCA6IHZhbDsKICAgIH0sCiAgICBzZXREZWZhdWx0VmFsdWU6IGZ1bmN0aW9uIHNldERlZmF1bHRWYWx1ZSh2YWwpIHsKICAgICAgaWYgKEFycmF5LmlzQXJyYXkodmFsKSkgewogICAgICAgIHJldHVybiB2YWwuam9pbignLCcpOwogICAgICB9CiAgICAgIC8vIGlmIChbJ3N0cmluZycsICdudW1iZXInXS5pbmRleE9mKHR5cGVvZiB2YWwpID4gLTEpIHsKICAgICAgLy8gICByZXR1cm4gdmFsCiAgICAgIC8vIH0KICAgICAgaWYgKHR5cGVvZiB2YWwgPT09ICdib29sZWFuJykgewogICAgICAgIHJldHVybiAiIi5jb25jYXQodmFsKTsKICAgICAgfQogICAgICByZXR1cm4gdmFsOwogICAgfSwKICAgIG9uRGVmYXVsdFZhbHVlSW5wdXQ6IGZ1bmN0aW9uIG9uRGVmYXVsdFZhbHVlSW5wdXQoc3RyKSB7CiAgICAgIGlmIChpc0FycmF5KHRoaXMuYWN0aXZlRGF0YS5fX2NvbmZpZ19fLmRlZmF1bHRWYWx1ZSkpIHsKICAgICAgICAvLyDmlbDnu4QKICAgICAgICB0aGlzLiRzZXQodGhpcy5hY3RpdmVEYXRhLl9fY29uZmlnX18sICdkZWZhdWx0VmFsdWUnLCBzdHIuc3BsaXQoJywnKS5tYXAoZnVuY3Rpb24gKHZhbCkgewogICAgICAgICAgcmV0dXJuIGlzTnVtYmVyU3RyKHZhbCkgPyArdmFsIDogdmFsOwogICAgICAgIH0pKTsKICAgICAgfSBlbHNlIGlmIChbJ3RydWUnLCAnZmFsc2UnXS5pbmRleE9mKHN0cikgPiAtMSkgewogICAgICAgIC8vIOW4g+WwlAogICAgICAgIHRoaXMuJHNldCh0aGlzLmFjdGl2ZURhdGEuX19jb25maWdfXywgJ2RlZmF1bHRWYWx1ZScsIEpTT04ucGFyc2Uoc3RyKSk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgLy8g5a2X56ym5Liy5ZKM5pWw5a2XCiAgICAgICAgdGhpcy4kc2V0KHRoaXMuYWN0aXZlRGF0YS5fX2NvbmZpZ19fLCAnZGVmYXVsdFZhbHVlJywgaXNOdW1iZXJTdHIoc3RyKSA/ICtzdHIgOiBzdHIpOwogICAgICB9CiAgICB9LAogICAgb25Td2l0Y2hWYWx1ZUlucHV0OiBmdW5jdGlvbiBvblN3aXRjaFZhbHVlSW5wdXQodmFsLCBuYW1lKSB7CiAgICAgIGlmIChbJ3RydWUnLCAnZmFsc2UnXS5pbmRleE9mKHZhbCkgPiAtMSkgewogICAgICAgIHRoaXMuJHNldCh0aGlzLmFjdGl2ZURhdGEsIG5hbWUsIEpTT04ucGFyc2UodmFsKSk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy4kc2V0KHRoaXMuYWN0aXZlRGF0YSwgbmFtZSwgaXNOdW1iZXJTdHIodmFsKSA/ICt2YWwgOiB2YWwpOwogICAgICB9CiAgICB9LAogICAgc2V0VGltZVZhbHVlOiBmdW5jdGlvbiBzZXRUaW1lVmFsdWUodmFsLCB0eXBlKSB7CiAgICAgIHZhciB2YWx1ZUZvcm1hdCA9IHR5cGUgPT09ICd3ZWVrJyA/IGRhdGVUaW1lRm9ybWF0LmRhdGUgOiB2YWw7CiAgICAgIHRoaXMuJHNldCh0aGlzLmFjdGl2ZURhdGEuX19jb25maWdfXywgJ2RlZmF1bHRWYWx1ZScsIG51bGwpOwogICAgICB0aGlzLiRzZXQodGhpcy5hY3RpdmVEYXRhLCAndmFsdWUtZm9ybWF0JywgdmFsdWVGb3JtYXQpOwogICAgICB0aGlzLiRzZXQodGhpcy5hY3RpdmVEYXRhLCAnZm9ybWF0JywgdmFsKTsKICAgIH0sCiAgICBzcGFuQ2hhbmdlOiBmdW5jdGlvbiBzcGFuQ2hhbmdlKHZhbCkgewogICAgICB0aGlzLmZvcm1Db25mLnNwYW4gPSB2YWw7CiAgICB9LAogICAgbXVsdGlwbGVDaGFuZ2U6IGZ1bmN0aW9uIG11bHRpcGxlQ2hhbmdlKHZhbCkgewogICAgICB0aGlzLiRzZXQodGhpcy5hY3RpdmVEYXRhLl9fY29uZmlnX18sICdkZWZhdWx0VmFsdWUnLCB2YWwgPyBbXSA6ICcnKTsKICAgIH0sCiAgICBkYXRlVHlwZUNoYW5nZTogZnVuY3Rpb24gZGF0ZVR5cGVDaGFuZ2UodmFsKSB7CiAgICAgIHRoaXMuc2V0VGltZVZhbHVlKGRhdGVUaW1lRm9ybWF0W3ZhbF0sIHZhbCk7CiAgICB9LAogICAgcmFuZ2VDaGFuZ2U6IGZ1bmN0aW9uIHJhbmdlQ2hhbmdlKHZhbCkgewogICAgICB0aGlzLiRzZXQodGhpcy5hY3RpdmVEYXRhLl9fY29uZmlnX18sICdkZWZhdWx0VmFsdWUnLCB2YWwgPyBbdGhpcy5hY3RpdmVEYXRhLm1pbiwgdGhpcy5hY3RpdmVEYXRhLm1heF0gOiB0aGlzLmFjdGl2ZURhdGEubWluKTsKICAgIH0sCiAgICByYXRlVGV4dENoYW5nZTogZnVuY3Rpb24gcmF0ZVRleHRDaGFuZ2UodmFsKSB7CiAgICAgIGlmICh2YWwpIHRoaXMuYWN0aXZlRGF0YVsnc2hvdy1zY29yZSddID0gZmFsc2U7CiAgICB9LAogICAgcmF0ZVNjb3JlQ2hhbmdlOiBmdW5jdGlvbiByYXRlU2NvcmVDaGFuZ2UodmFsKSB7CiAgICAgIGlmICh2YWwpIHRoaXMuYWN0aXZlRGF0YVsnc2hvdy10ZXh0J10gPSBmYWxzZTsKICAgIH0sCiAgICBjb2xvckZvcm1hdENoYW5nZTogZnVuY3Rpb24gY29sb3JGb3JtYXRDaGFuZ2UodmFsKSB7CiAgICAgIHRoaXMuYWN0aXZlRGF0YS5fX2NvbmZpZ19fLmRlZmF1bHRWYWx1ZSA9IG51bGw7CiAgICAgIHRoaXMuYWN0aXZlRGF0YVsnc2hvdy1hbHBoYSddID0gdmFsLmluZGV4T2YoJ2EnKSA+IC0xOwogICAgICB0aGlzLmFjdGl2ZURhdGEuX19jb25maWdfXy5yZW5kZXJLZXkgPSArbmV3IERhdGUoKTsgLy8g5pu05pawcmVuZGVyS2V5LOmHjeaWsOa4suafk+ivpee7hOS7tgogICAgfSwKICAgIG9wZW5JY29uc0RpYWxvZzogZnVuY3Rpb24gb3Blbkljb25zRGlhbG9nKG1vZGVsKSB7CiAgICAgIHRoaXMuaWNvbnNWaXNpYmxlID0gdHJ1ZTsKICAgICAgdGhpcy5jdXJyZW50SWNvbk1vZGVsID0gbW9kZWw7CiAgICB9LAogICAgc2V0SWNvbjogZnVuY3Rpb24gc2V0SWNvbih2YWwpIHsKICAgICAgdGhpcy5hY3RpdmVEYXRhW3RoaXMuY3VycmVudEljb25Nb2RlbF0gPSB2YWw7CiAgICB9LAogICAgdGFnQ2hhbmdlOiBmdW5jdGlvbiB0YWdDaGFuZ2UodGFnSWNvbikgewogICAgICB2YXIgdGFyZ2V0ID0gaW5wdXRDb21wb25lbnRzLmZpbmQoZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICByZXR1cm4gaXRlbS5fX2NvbmZpZ19fLnRhZ0ljb24gPT09IHRhZ0ljb247CiAgICAgIH0pOwogICAgICBpZiAoIXRhcmdldCkgdGFyZ2V0ID0gc2VsZWN0Q29tcG9uZW50cy5maW5kKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgcmV0dXJuIGl0ZW0uX19jb25maWdfXy50YWdJY29uID09PSB0YWdJY29uOwogICAgICB9KTsKICAgICAgdGhpcy4kZW1pdCgndGFnLWNoYW5nZScsIHRhcmdldCk7CiAgICB9LAogICAgY2hhbmdlUmVuZGVyS2V5OiBmdW5jdGlvbiBjaGFuZ2VSZW5kZXJLZXkoKSB7CiAgICAgIGlmIChuZWVkUmVyZW5kZXJMaXN0LmluY2x1ZGVzKHRoaXMuYWN0aXZlRGF0YS5fX2NvbmZpZ19fLnRhZykpIHsKICAgICAgICB0aGlzLmFjdGl2ZURhdGEuX19jb25maWdfXy5yZW5kZXJLZXkgPSArbmV3IERhdGUoKTsKICAgICAgfQogICAgfQogIH0KfTs="}, null]}