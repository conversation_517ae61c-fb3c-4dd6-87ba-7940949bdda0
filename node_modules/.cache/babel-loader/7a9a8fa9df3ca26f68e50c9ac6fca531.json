{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/utils/permission.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/utils/permission.js", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js", "mtime": 1751171659043}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHN0b3JlIGZyb20gJ0Avc3RvcmUnOwoKLyoqCiAqIOWtl+espuadg+mZkOagoemqjAogKiBAcGFyYW0ge0FycmF5fSB2YWx1ZSDmoKHpqozlgLwKICogQHJldHVybnMge0Jvb2xlYW59CiAqLwpleHBvcnQgZnVuY3Rpb24gY2hlY2tQZXJtaSh2YWx1ZSkgewogIGlmICh2YWx1ZSAmJiB2YWx1ZSBpbnN0YW5jZW9mIEFycmF5ICYmIHZhbHVlLmxlbmd0aCA+IDApIHsKICAgIHZhciBwZXJtaXNzaW9ucyA9IHN0b3JlLmdldHRlcnMgJiYgc3RvcmUuZ2V0dGVycy5wZXJtaXNzaW9uczsKICAgIHZhciBwZXJtaXNzaW9uRGF0YXMgPSB2YWx1ZTsKICAgIHZhciBhbGxfcGVybWlzc2lvbiA9ICIqOio6KiI7CiAgICB2YXIgaGFzUGVybWlzc2lvbiA9IHBlcm1pc3Npb25zLnNvbWUoZnVuY3Rpb24gKHBlcm1pc3Npb24pIHsKICAgICAgcmV0dXJuIGFsbF9wZXJtaXNzaW9uID09PSBwZXJtaXNzaW9uIHx8IHBlcm1pc3Npb25EYXRhcy5pbmNsdWRlcyhwZXJtaXNzaW9uKTsKICAgIH0pOwogICAgaWYgKCFoYXNQZXJtaXNzaW9uKSB7CiAgICAgIHJldHVybiBmYWxzZTsKICAgIH0KICAgIHJldHVybiB0cnVlOwogIH0gZWxzZSB7CiAgICBjb25zb2xlLmVycm9yKCJuZWVkIHJvbGVzISBMaWtlIGNoZWNrUGVybWk9XCJbJ3N5c3RlbTp1c2VyOmFkZCcsJ3N5c3RlbTp1c2VyOmVkaXQnXVwiIik7CiAgICByZXR1cm4gZmFsc2U7CiAgfQp9CgovKioKICog6KeS6Imy5p2D6ZmQ5qCh6aqMCiAqIEBwYXJhbSB7QXJyYXl9IHZhbHVlIOagoemqjOWAvAogKiBAcmV0dXJucyB7Qm9vbGVhbn0KICovCmV4cG9ydCBmdW5jdGlvbiBjaGVja1JvbGUodmFsdWUpIHsKICBpZiAodmFsdWUgJiYgdmFsdWUgaW5zdGFuY2VvZiBBcnJheSAmJiB2YWx1ZS5sZW5ndGggPiAwKSB7CiAgICB2YXIgcm9sZXMgPSBzdG9yZS5nZXR0ZXJzICYmIHN0b3JlLmdldHRlcnMucm9sZXM7CiAgICB2YXIgcGVybWlzc2lvblJvbGVzID0gdmFsdWU7CiAgICB2YXIgc3VwZXJfYWRtaW4gPSAiYWRtaW4iOwogICAgdmFyIGhhc1JvbGUgPSByb2xlcy5zb21lKGZ1bmN0aW9uIChyb2xlKSB7CiAgICAgIHJldHVybiBzdXBlcl9hZG1pbiA9PT0gcm9sZSB8fCBwZXJtaXNzaW9uUm9sZXMuaW5jbHVkZXMocm9sZSk7CiAgICB9KTsKICAgIGlmICghaGFzUm9sZSkgewogICAgICByZXR1cm4gZmFsc2U7CiAgICB9CiAgICByZXR1cm4gdHJ1ZTsKICB9IGVsc2UgewogICAgY29uc29sZS5lcnJvcigibmVlZCByb2xlcyEgTGlrZSBjaGVja1JvbGU9XCJbJ2FkbWluJywnZWRpdG9yJ11cIiIpOwogICAgcmV0dXJuIGZhbHNlOwogIH0KfQ=="}, null]}