{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/store/getters.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/store/getters.js", "mtime": 1655728538000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:dmFyIGdldHRlcnMgPSB7CiAgc2lkZWJhcjogZnVuY3Rpb24gc2lkZWJhcihzdGF0ZSkgewogICAgcmV0dXJuIHN0YXRlLmFwcC5zaWRlYmFyOwogIH0sCiAgc2l6ZTogZnVuY3Rpb24gc2l6ZShzdGF0ZSkgewogICAgcmV0dXJuIHN0YXRlLmFwcC5zaXplOwogIH0sCiAgZGV2aWNlOiBmdW5jdGlvbiBkZXZpY2Uoc3RhdGUpIHsKICAgIHJldHVybiBzdGF0ZS5hcHAuZGV2aWNlOwogIH0sCiAgdmlzaXRlZFZpZXdzOiBmdW5jdGlvbiB2aXNpdGVkVmlld3Moc3RhdGUpIHsKICAgIHJldHVybiBzdGF0ZS50YWdzVmlldy52aXNpdGVkVmlld3M7CiAgfSwKICBjYWNoZWRWaWV3czogZnVuY3Rpb24gY2FjaGVkVmlld3Moc3RhdGUpIHsKICAgIHJldHVybiBzdGF0ZS50YWdzVmlldy5jYWNoZWRWaWV3czsKICB9LAogIHRva2VuOiBmdW5jdGlvbiB0b2tlbihzdGF0ZSkgewogICAgcmV0dXJuIHN0YXRlLnVzZXIudG9rZW47CiAgfSwKICBhdmF0YXI6IGZ1bmN0aW9uIGF2YXRhcihzdGF0ZSkgewogICAgcmV0dXJuIHN0YXRlLnVzZXIuYXZhdGFyOwogIH0sCiAgbmFtZTogZnVuY3Rpb24gbmFtZShzdGF0ZSkgewogICAgcmV0dXJuIHN0YXRlLnVzZXIubmFtZTsKICB9LAogIGludHJvZHVjdGlvbjogZnVuY3Rpb24gaW50cm9kdWN0aW9uKHN0YXRlKSB7CiAgICByZXR1cm4gc3RhdGUudXNlci5pbnRyb2R1Y3Rpb247CiAgfSwKICByb2xlczogZnVuY3Rpb24gcm9sZXMoc3RhdGUpIHsKICAgIHJldHVybiBzdGF0ZS51c2VyLnJvbGVzOwogIH0sCiAgcGVybWlzc2lvbnM6IGZ1bmN0aW9uIHBlcm1pc3Npb25zKHN0YXRlKSB7CiAgICByZXR1cm4gc3RhdGUudXNlci5wZXJtaXNzaW9uczsKICB9LAogIHBlcm1pc3Npb25fcm91dGVzOiBmdW5jdGlvbiBwZXJtaXNzaW9uX3JvdXRlcyhzdGF0ZSkgewogICAgcmV0dXJuIHN0YXRlLnBlcm1pc3Npb24ucm91dGVzOwogIH0sCiAgc2lkZWJhclJvdXRlcnM6IGZ1bmN0aW9uIHNpZGViYXJSb3V0ZXJzKHN0YXRlKSB7CiAgICByZXR1cm4gc3RhdGUucGVybWlzc2lvbi5zaWRlYmFyUm91dGVyczsKICB9LAogIHVzZXJJZDogZnVuY3Rpb24gdXNlcklkKHN0YXRlKSB7CiAgICByZXR1cm4gc3RhdGUudXNlci51c2VySWQ7CiAgfQp9OwpleHBvcnQgZGVmYXVsdCBnZXR0ZXJzOw=="}, {"version": 3, "names": ["getters", "sidebar", "state", "app", "size", "device", "visitedViews", "tagsView", "cachedViews", "token", "user", "avatar", "name", "introduction", "roles", "permissions", "permission_routes", "permission", "routes", "sidebarRouters", "userId"], "sources": ["/Users/<USER>/develop/code/project/haijia/adminweb/src/store/getters.js"], "sourcesContent": ["const getters = {\n  sidebar: state => state.app.sidebar,\n  size: state => state.app.size,\n  device: state => state.app.device,\n  visitedViews: state => state.tagsView.visitedViews,\n  cachedViews: state => state.tagsView.cachedViews,\n  token: state => state.user.token,\n  avatar: state => state.user.avatar,\n  name: state => state.user.name,\n  introduction: state => state.user.introduction,\n  roles: state => state.user.roles,\n  permissions: state => state.user.permissions,\n  permission_routes: state => state.permission.routes,\n  sidebarRouters:state => state.permission.sidebarRouters,\n  userId: state=> state.user.userId,\n}\nexport default getters\n"], "mappings": "AAAA,IAAMA,OAAO,GAAG;EACdC,OAAO,EAAE,SAAAA,QAAAC,KAAK;IAAA,OAAIA,KAAK,CAACC,GAAG,CAACF,OAAO;EAAA;EACnCG,IAAI,EAAE,SAAAA,KAAAF,KAAK;IAAA,OAAIA,KAAK,CAACC,GAAG,CAACC,IAAI;EAAA;EAC7BC,MAAM,EAAE,SAAAA,OAAAH,KAAK;IAAA,OAAIA,KAAK,CAACC,GAAG,CAACE,MAAM;EAAA;EACjCC,YAAY,EAAE,SAAAA,aAAAJ,KAAK;IAAA,OAAIA,KAAK,CAACK,QAAQ,CAACD,YAAY;EAAA;EAClDE,WAAW,EAAE,SAAAA,YAAAN,KAAK;IAAA,OAAIA,KAAK,CAACK,QAAQ,CAACC,WAAW;EAAA;EAChDC,KAAK,EAAE,SAAAA,MAAAP,KAAK;IAAA,OAAIA,KAAK,CAACQ,IAAI,CAACD,KAAK;EAAA;EAChCE,MAAM,EAAE,SAAAA,OAAAT,KAAK;IAAA,OAAIA,KAAK,CAACQ,IAAI,CAACC,MAAM;EAAA;EAClCC,IAAI,EAAE,SAAAA,KAAAV,KAAK;IAAA,OAAIA,KAAK,CAACQ,IAAI,CAACE,IAAI;EAAA;EAC9BC,YAAY,EAAE,SAAAA,aAAAX,KAAK;IAAA,OAAIA,KAAK,CAACQ,IAAI,CAACG,YAAY;EAAA;EAC9CC,KAAK,EAAE,SAAAA,MAAAZ,KAAK;IAAA,OAAIA,KAAK,CAACQ,IAAI,CAACI,KAAK;EAAA;EAChCC,WAAW,EAAE,SAAAA,YAAAb,KAAK;IAAA,OAAIA,KAAK,CAACQ,IAAI,CAACK,WAAW;EAAA;EAC5CC,iBAAiB,EAAE,SAAAA,kBAAAd,KAAK;IAAA,OAAIA,KAAK,CAACe,UAAU,CAACC,MAAM;EAAA;EACnDC,cAAc,EAAC,SAAAA,eAAAjB,KAAK;IAAA,OAAIA,KAAK,CAACe,UAAU,CAACE,cAAc;EAAA;EACvDC,MAAM,EAAE,SAAAA,OAAAlB,KAAK;IAAA,OAAGA,KAAK,CAACQ,IAAI,CAACU,MAAM;EAAA;AACnC,CAAC;AACD,eAAepB,OAAO", "ignoreList": []}]}