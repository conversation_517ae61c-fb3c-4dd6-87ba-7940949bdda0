{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/store/index.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/store/index.js", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IFZ1ZSBmcm9tICd2dWUnOwppbXBvcnQgVnVleCBmcm9tICd2dWV4JzsKaW1wb3J0IGFwcCBmcm9tICcuL21vZHVsZXMvYXBwJzsKaW1wb3J0IHVzZXIgZnJvbSAnLi9tb2R1bGVzL3VzZXInOwppbXBvcnQgdGFnc1ZpZXcgZnJvbSAnLi9tb2R1bGVzL3RhZ3NWaWV3JzsKaW1wb3J0IHBlcm1pc3Npb24gZnJvbSAnLi9tb2R1bGVzL3Blcm1pc3Npb24nOwppbXBvcnQgc2V0dGluZ3MgZnJvbSAnLi9tb2R1bGVzL3NldHRpbmdzJzsKaW1wb3J0IGdldHRlcnMgZnJvbSAnLi9nZXR0ZXJzJzsKVnVlLnVzZShWdWV4KTsKdmFyIHN0b3JlID0gbmV3IFZ1ZXguU3RvcmUoewogIG1vZHVsZXM6IHsKICAgIGFwcDogYXBwLAogICAgdXNlcjogdXNlciwKICAgIHRhZ3NWaWV3OiB0YWdzVmlldywKICAgIHBlcm1pc3Npb246IHBlcm1pc3Npb24sCiAgICBzZXR0aW5nczogc2V0dGluZ3MKICB9LAogIGdldHRlcnM6IGdldHRlcnMKfSk7CmV4cG9ydCBkZWZhdWx0IHN0b3JlOw=="}, {"version": 3, "names": ["<PERSON><PERSON>", "Vuex", "app", "user", "tagsView", "permission", "settings", "getters", "use", "store", "Store", "modules"], "sources": ["/Users/<USER>/develop/code/project/haijia/adminweb/src/store/index.js"], "sourcesContent": ["import Vue from 'vue'\nimport Vuex from 'vuex'\nimport app from './modules/app'\nimport user from './modules/user'\nimport tagsView from './modules/tagsView'\nimport permission from './modules/permission'\nimport settings from './modules/settings'\nimport getters from './getters'\n\nVue.use(Vuex)\n\nconst store = new Vuex.Store({\n  modules: {\n    app,\n    user,\n    tagsView,\n    permission,\n    settings\n  },\n  getters\n})\n\nexport default store\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,GAAG,MAAM,eAAe;AAC/B,OAAOC,IAAI,MAAM,gBAAgB;AACjC,OAAOC,QAAQ,MAAM,oBAAoB;AACzC,OAAOC,UAAU,MAAM,sBAAsB;AAC7C,OAAOC,QAAQ,MAAM,oBAAoB;AACzC,OAAOC,OAAO,MAAM,WAAW;AAE/BP,GAAG,CAACQ,GAAG,CAACP,IAAI,CAAC;AAEb,IAAMQ,KAAK,GAAG,IAAIR,IAAI,CAACS,KAAK,CAAC;EAC3BC,OAAO,EAAE;IACPT,GAAG,EAAHA,GAAG;IACHC,IAAI,EAAJA,IAAI;IACJC,QAAQ,EAARA,QAAQ;IACRC,UAAU,EAAVA,UAAU;IACVC,QAAQ,EAARA;EACF,CAAC;EACDC,OAAO,EAAPA;AACF,CAAC,CAAC;AAEF,eAAeE,KAAK", "ignoreList": []}]}