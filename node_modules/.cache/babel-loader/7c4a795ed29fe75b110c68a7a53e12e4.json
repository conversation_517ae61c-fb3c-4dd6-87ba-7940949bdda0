{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/project/report/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/project/report/index.vue", "mtime": 1753529759964}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGxpc3RSZXBvcnQsIGdldFJlcG9ydCwgZGVsUmVwb3J0LCBhZGRSZXBvcnQsIHVwZGF0ZVJlcG9ydCwgZXhwb3J0UmVwb3J0LCBpbXBvcnRUZW1wbGF0ZSBhcyBfaW1wb3J0VGVtcGxhdGUsIHByaW50UmVwb3J0LCBjaGVja05hbWVVbmlxdWUsIGdldExpa2VMaXN0LCBhdXRoUmVwb3J0IH0gZnJvbSAiQC9hcGkvcHJvamVjdC9yZXBvcnQiOwppbXBvcnQgeyBnZXRUb2tlbiB9IGZyb20gIkAvdXRpbHMvYXV0aCI7CmltcG9ydCBGaWxlVXBsb2FkIGZyb20gIkAvY29tcG9uZW50cy9GaWxlVXBsb2FkIjsKaW1wb3J0IGZsb3dhYmxlIGZyb20gIkAvdmlld3MvZmxvd2FibGUvdGFzay9yZWNvcmQvdmlldyI7CmltcG9ydCB7IGdldEluc0lkQnlCaXpLZXkgfSBmcm9tICJAL2FwaS9mbG93YWJsZS90b2RvIjsKaW1wb3J0IHsgcmVnaW9uRGF0YSwgQ29kZVRvVGV4dCwgVGV4dFRvQ29kZSB9IGZyb20gImVsZW1lbnQtY2hpbmEtYXJlYS1kYXRhIjsKaW1wb3J0IHByaW50IGZyb20gInByaW50LWpzIjsKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICJSZXBvcnQiLAogIGNvbXBvbmVudHM6IHsKICAgIEZpbGVVcGxvYWQ6IEZpbGVVcGxvYWQsCiAgICBwcmludDogcHJpbnQsCiAgICBmbG93YWJsZTogZmxvd2FibGUKICB9LAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICB2YXIgX3RoaXMgPSB0aGlzOwogICAgdmFyIGluZm9UeXBlVmFsdWVWYWxpID0gZnVuY3Rpb24gaW5mb1R5cGVWYWx1ZVZhbGkocnVsZSwgdmFsdWUsIGNhbGxiYWNrKSB7CiAgICAgIGlmIChfdGhpcy5mb3JtLmluZm9UeXBlLmluZGV4T2YoIjEiKSA+PSAwICYmICFfdGhpcy5mb3JtLnNjYW5GaWxlKSB7CiAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCLpgq7nrrHlnLDlnYDlv4XloasiKSk7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIGNhbGxiYWNrKCk7CiAgICB9OwogICAgdmFyIGluZm9UeXBlVmFsdWVWYWxpMiA9IGZ1bmN0aW9uIGluZm9UeXBlVmFsdWVWYWxpMihydWxlLCB2YWx1ZSwgY2FsbGJhY2spIHsKICAgICAgaWYgKF90aGlzLmZvcm0uaW5mb1R5cGUuaW5kZXhPZigiMiIpID49IDAgJiYgIV90aGlzLmZvcm0uc2VuZEFkZHJlc3MpIHsKICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoIuaUtuS7tuWcsOWdgOW/heWhqyIpKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgY2FsbGJhY2soKTsKICAgIH07CiAgICB2YXIgbmFtZVZhbGkgPSBmdW5jdGlvbiBuYW1lVmFsaShydWxlLCB2YWx1ZSwgY2FsbGJhY2spIHsKICAgICAgaWYgKCFfdGhpcy5mb3JtLnByb2plY3ROYW1lKSB7CiAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCLpobnnm67lkI3np7Dlv4XloasiKSk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgaWYgKC9ccysvZy50ZXN0KF90aGlzLmZvcm0ucHJvamVjdE5hbWUpKSB7CiAgICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoIumhueebruWQjeensOS4jeinhOiMgyIpKTsKICAgICAgICAgIHJldHVybjsKICAgICAgICB9CiAgICAgICAgY2hlY2tOYW1lVW5pcXVlKHsKICAgICAgICAgIHByb2plY3ROYW1lOiBfdGhpcy5mb3JtLnByb2plY3ROYW1lLAogICAgICAgICAgcHJvamVjdElkOiBfdGhpcy5mb3JtLnByb2plY3RJZAogICAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgICBpZiAocmVzcG9uc2UuZGF0YSA9PSAwKSB7CiAgICAgICAgICAgIGNhbGxiYWNrKCk7CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoIumhueebruWQjeensOW3suWtmOWcqCIpKTsKICAgICAgICAgIH0KICAgICAgICB9KTsKICAgICAgfQogICAgfTsKICAgIHZhciBjb2RlVmFsaSA9IGZ1bmN0aW9uIGNvZGVWYWxpKHJ1bGUsIHZhbHVlLCBjYWxsYmFjaykgewogICAgICBpZiAoIXRoYXQuZm9ybS5wcm9qZWN0Tm8pIHsKICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoIumhueebrue8luWPt+W/heWhqyIpKTsKICAgICAgfSBlbHNlIGlmICgvXHMrL2cudGVzdCh0aGF0LmZvcm0ucHJvamVjdE5vKSkgewogICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcigi6aG555uu57yW5Y+35LiN6KeE6IyDIikpOwogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICBjYWxsYmFjaygpOwogICAgfTsKICAgIHZhciBhdXRoRmlsZVZhbHVlVmFsaSA9IGZ1bmN0aW9uIGF1dGhGaWxlVmFsdWVWYWxpKHJ1bGUsIHZhbHVlLCBjYWxsYmFjaykgewogICAgICBpZiAoX3RoaXMuZm9ybS5vcGVyYXRpb25UeXBlID09IDIgJiYgIV90aGlzLmZvcm0uYXV0aEZpbGUpIHsKICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoIuaOiOadg+exu+Wei+W/heS8oOaOiOadg+S5piIpKTsKICAgICAgfQogICAgICBjYWxsYmFjaygpOwogICAgfTsKICAgIHZhciBvcGVuRGF0ZVZhbGkgPSBmdW5jdGlvbiBvcGVuRGF0ZVZhbGkocnVsZSwgdmFsdWUsIGNhbGxiYWNrKSB7CiAgICAgIGNvbnNvbGUubG9nKDEyMzU2Nik7CiAgICAgIGlmICghdGhhdC5mb3JtLm9wZW5EYXRlKSB7CiAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCLlvIDmoIfml6XmnJ/lv4XloasiKSk7CiAgICAgICAgcmV0dXJuOwogICAgICB9IGVsc2UgaWYgKHZhbHVlID09PSAi5pegIikgewogICAgICAgIGNhbGxiYWNrKCk7CiAgICAgICAgcmV0dXJuOwogICAgICB9IGVsc2UgaWYgKCEvXlxkezR9LVxkezJ9LVxkezJ9JC8udGVzdCh2YWx1ZSkpIHsKICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoIuW8gOagh+aXpeacn+agvOW8j+S4jeWQiOazle+8jOekuuS+izIwMjUtMDEtMDEiKSk7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIGNhbGxiYWNrKCk7CiAgICB9OwogICAgdmFyIGhhbmdEYXRlVmFsaSA9IGZ1bmN0aW9uIGhhbmdEYXRlVmFsaShydWxlLCB2YWx1ZSwgY2FsbGJhY2spIHsKICAgICAgaWYgKCF0aGF0LmZvcm0uaGFuZ0RhdGUpIHsKICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoIuaMgue9keaXpeacn+W/heWhqyIpKTsKICAgICAgICByZXR1cm47CiAgICAgIH0gZWxzZSBpZiAodmFsdWUgPT09ICLml6AiKSB7CiAgICAgICAgY2FsbGJhY2soKTsKICAgICAgICByZXR1cm47CiAgICAgIH0gZWxzZSBpZiAoIS9eXGR7NH0tXGR7Mn0tXGR7Mn0kLy50ZXN0KHZhbHVlKSkgewogICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcigi5oyC572R5pel5pyf5qC85byP5LiN5ZCI5rOV77yM56S65L6LMjAyNS0wMS0wMSIpKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgY2FsbGJhY2soKTsKICAgIH07CiAgICByZXR1cm4gewogICAgICBpc01vYmlsZTogZmFsc2UsCiAgICAgIHBhZ2VMYXlvdXQ6ICJ0b3RhbCwgc2l6ZXMsIHByZXYsIHBhZ2VyLCBuZXh0LCBqdW1wZXIiLAogICAgICAvLyDpga7nvanlsYIKICAgICAgbG9hZGluZzogdHJ1ZSwKICAgICAgLy8g6YCJ5Lit5pWw57uECiAgICAgIGlkczogW10sCiAgICAgIC8vIOmdnuWNleS4quemgeeUqAogICAgICBzaW5nbGU6IHRydWUsCiAgICAgIC8vIOmdnuWkmuS4quemgeeUqAogICAgICBtdWx0aXBsZTogdHJ1ZSwKICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2CiAgICAgIHNob3dTZWFyY2g6IHRydWUsCiAgICAgIHNob3dFeHBvcnQ6IGZhbHNlLAogICAgICBzaG93UHJpbnQ6IGZhbHNlLAogICAgICAvLyDmgLvmnaHmlbAKICAgICAgdG90YWw6IDAsCiAgICAgIC8vIOmhueebruaKpeWkh+ihqOagvOaVsOaNrgogICAgICByZXBvcnRMaXN0OiBbXSwKICAgICAgLy8g5by55Ye65bGC5qCH6aKYCiAgICAgIHRpdGxlOiAiIiwKICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGCCiAgICAgIG9wZW46IGZhbHNlLAogICAgICAvLyDmk43kvZznsbvlnovlrZflhbgKICAgICAgb3BlcmF0aW9uVHlwZU9wdGlvbnM6IFtdLAogICAgICAvLyDlrqHmoLjnirbmgIHlrZflhbgKICAgICAgYXVkaXRTdGF0dXNPcHRpb25zOiBbXSwKICAgICAgLy8g57yW6L6R54q25oCB5a2X5YW4CiAgICAgIGVkaXRTdGF0dXNPcHRpb25zOiBbXSwKICAgICAgLy8g5oub5qCH5pa55byP5a2X5YW4CiAgICAgIGJpZGRpbmdUeXBlT3B0aW9uczogW10sCiAgICAgIC8vIOaKleagh+S6p+WTgeWei+WPt+Wtl+WFuAogICAgICBtb2RlbE9wdGlvbnM6IFtdLAogICAgICBtb2RlbE9wdGlvbjE6IFtdLAogICAgICAvLyDmiYDpnIDotYTmlpnlrZflhbgKICAgICAgcmVxdWlyZUluZm9PcHRpb25zOiBbXSwKICAgICAgcmVxdWlyZUluZm9PcHRpb24xOiBbXSwKICAgICAgLy8g6LWE5paZ57G75Z6L5a2X5YW4CiAgICAgIGluZm9UeXBlT3B0aW9uczogW10sCiAgICAgIHNwZWNPcHRpb25zOiBbXSwKICAgICAgc3BlY09wdGlvbjE6IFtdLAogICAgICAvLyDmiYDlsZ7nnIHku73lrZflhbgKICAgICAgYmVsb25nUHJvdmluY2VPcHRpb25zOiBbXSwKICAgICAgYmVsb25nUHJvdmluY2VPcHRpb25zMTogW10sCiAgICAgIC8vIOWUruWQjuW5tOmZkAogICAgICBhZnRlclNhbGVZZWFyT3B0aW9uczogW10sCiAgICAgIGFmdGVyU2FsZVllYXJPcHRpb25zMTogW10sCiAgICAgIC8vIOmhueebruWvvOWFpeWPguaVsAogICAgICB1cGxvYWQ6IHsKICAgICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYLvvIjnlKjmiLflr7zlhaXvvIkKICAgICAgICBvcGVuOiBmYWxzZSwKICAgICAgICAvLyDlvLnlh7rlsYLmoIfpopjvvIjnlKjmiLflr7zlhaXvvIkKICAgICAgICB0aXRsZTogIiIsCiAgICAgICAgLy8g5piv5ZCm56aB55So5LiK5LygCiAgICAgICAgaXNVcGxvYWRpbmc6IGZhbHNlLAogICAgICAgIC8vIOaYr+WQpuabtOaWsOW3sue7j+WtmOWcqOeahOeUqOaIt+aVsOaNrgogICAgICAgIHVwZGF0ZVN1cHBvcnQ6IDAsCiAgICAgICAgLy8g6K6+572u5LiK5Lyg55qE6K+35rGC5aS06YOoCiAgICAgICAgaGVhZGVyczogewogICAgICAgICAgQXV0aG9yaXphdGlvbjogIkJlYXJlciAiICsgZ2V0VG9rZW4oKQogICAgICAgIH0sCiAgICAgICAgLy8g5LiK5Lyg55qE5Zyw5Z2ACiAgICAgICAgdXJsOiBwcm9jZXNzLmVudi5WVUVfQVBQX0JBU0VfQVBJICsgIi9wcm9qZWN0L3JlcG9ydC9pbXBvcnREYXRhIgogICAgICB9LAogICAgICAvLyDmn6Xor6Llj4LmlbAKICAgICAgcXVlcnlQYXJhbXM6IHsKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICBwcm9qZWN0Tm86IG51bGwsCiAgICAgICAgcHJvamVjdE5hbWU6IG51bGwsCiAgICAgICAgb3BlcmF0aW9uVHlwZTogbnVsbCwKICAgICAgICBhdWRpdFN0YXR1czogbnVsbCwKICAgICAgICBwcm92aW5jZTogbnVsbCwKICAgICAgICB1c2VyVHlwZTogbnVsbCwKICAgICAgICBiZWxvbmdVc2VyOiBudWxsLAogICAgICAgIHVwZGF0ZVRpbWVBcnI6IFtdLAogICAgICAgIHNwYXJlMTogbnVsbCwKICAgICAgICBhZGRyZXNzOiBudWxsLAogICAgICAgIGJpZGRpbmdDb21wYW55OiBudWxsLAogICAgICAgIGF1dGhDb21wYW55OiBudWxsLAogICAgICAgIGZ1bGxGaWVsZDogbnVsbAogICAgICB9LAogICAgICAvLyDooajljZXlj4LmlbAKICAgICAgZm9ybToge30sCiAgICAgIC8vIOihqOWNleagoemqjAogICAgICAvLyDooajljZXmoKHpqowKICAgICAgcnVsZXM6IHsKICAgICAgICBvcGVyYXRpb25UeXBlOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAi5pON5L2c57G75Z6L5b+F6YCJIgogICAgICAgIH1dLAogICAgICAgIHByb2plY3RObzogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgdmFsaWRhdGU6IGNvZGVWYWxpLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgfV0sCiAgICAgICAgcHJvamVjdE5hbWU6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIHZhbGlkYXRlOiBuYW1lVmFsaSwKICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgIH1dLAogICAgICAgIGFkZHJlc3M6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICLor6bnu4blnLDlnYDkuI3og73kuLrnqboiLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgfV0sCiAgICAgICAgYmlkZGluZ0NvbXBhbnk6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICLmi5vmoIfljZXkvY3kuI3og73kuLrnqboiLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgfV0sCiAgICAgICAgb3BlbkRhdGU6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIHZhbGlkYXRlOiBvcGVuRGF0ZVZhbGksCiAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIKICAgICAgICB9XSwKICAgICAgICBhZnRlclNhbGVZZWFyOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAi5ZSu5ZCO5bm06ZmQ5LiN6IO95Li656m6IiwKICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgIH1dLAogICAgICAgIGhhbmdEYXRlOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICB2YWxpZGF0ZTogaGFuZ0RhdGVWYWxpLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgfV0sCiAgICAgICAgYmVsb25nUHJvdmluY2U6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICLllK7lkI7lubTpmZDkuI3og73kuLrnqboiLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgfV0sCiAgICAgICAgZGlzdHJpYnV0b3I6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICLmiYDlsZ7nu4/plIDllYbkuI3og73kuLrnqboiLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgfV0sCiAgICAgICAgc2NhbkZpbGU6IFt7CiAgICAgICAgICB2YWxpZGF0b3I6IGluZm9UeXBlVmFsdWVWYWxpLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgfV0sCiAgICAgICAgc2VuZEFkZHJlc3M6IFt7CiAgICAgICAgICB2YWxpZGF0b3I6IGluZm9UeXBlVmFsdWVWYWxpMiwKICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgIH1dLAogICAgICAgIG1vZGVsOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAi5oqV5qCH5Lqn5ZOB5Z6L5Y+35b+F6YCJIgogICAgICAgIH1dLAogICAgICAgIHNwZWM6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICLmipXmoIfkuqflk4Hop4TmoLzlv4XpgIkiCiAgICAgICAgfV0sCiAgICAgICAgcHJvdmluY2U6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICLpobnnm67miYDlnKjlnLDlv4XpgIkiCiAgICAgICAgfV0sCiAgICAgICAgaW5mb1R5cGU6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICLotYTmlpnmjqXmlLbmlrnlvI/lv4XpgIkiLAogICAgICAgICAgdHJpZ2dlcjogImNoYW5nZSIKICAgICAgICB9XSwKICAgICAgICBhdXRoRmlsZTogW3sKICAgICAgICAgIHZhbGlkYXRvcjogYXV0aEZpbGVWYWx1ZVZhbGksCiAgICAgICAgICB0cmlnZ2VyOiAiY2hhbmdlIgogICAgICAgIH1dLAogICAgICAgIGJpZGRpbmdDb250YWN0OiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAi5oub5qCH5Y2V5L2N6IGU57O75Lq6L+iBlOezu+eUteivneW/heWhqyIKICAgICAgICB9XSwKICAgICAgICBhdXRoQ29udGFjdDogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogIuaOiOadg+WFrOWPuOiBlOezu+S6ui/ogZTns7vnlLXor53lv4XloasiCiAgICAgICAgfV0KICAgICAgfSwKICAgICAgLy8g5YiX5L+h5oGvCiAgICAgIGNvbHVtbnM6IFt7CiAgICAgICAga2V5OiAiYmVsb25nVXNlciIsCiAgICAgICAgaW5kZXg6IDEsCiAgICAgICAgbGFiZWw6ICJcdTYyNDBcdTVDNUVcdTc1MjhcdTYyMzciLAogICAgICAgIHZpc2libGU6IHRydWUKICAgICAgfSwgewogICAgICAgIGtleTogInByb2plY3RObyIsCiAgICAgICAgaW5kZXg6IDIsCiAgICAgICAgbGFiZWw6ICJcdTk4NzlcdTc2RUVcdTdGMTZcdTUzRjciLAogICAgICAgIHZpc2libGU6IHRydWUKICAgICAgfSwgewogICAgICAgIGtleTogInByb2plY3ROYW1lIiwKICAgICAgICBpbmRleDogMywKICAgICAgICBsYWJlbDogIlx1OTg3OVx1NzZFRVx1NTQwRFx1NzlGMCIsCiAgICAgICAgdmlzaWJsZTogdHJ1ZQogICAgICB9LCB7CiAgICAgICAga2V5OiAib3BlcmF0aW9uVHlwZSIsCiAgICAgICAgaW5kZXg6IDQsCiAgICAgICAgbGFiZWw6ICJcdTY0Q0RcdTRGNUNcdTdDN0JcdTU3OEIiLAogICAgICAgIHZpc2libGU6IHRydWUKICAgICAgfSwgewogICAgICAgIGtleTogInByb3ZpbmNlIiwKICAgICAgICBpbmRleDogNSwKICAgICAgICBsYWJlbDogIlx1OTg3OVx1NzZFRVx1NjI0MFx1NTcyOFx1NTczMCIsCiAgICAgICAgdmlzaWJsZTogdHJ1ZQogICAgICB9LCB7CiAgICAgICAga2V5OiAiYWRkcmVzcyIsCiAgICAgICAgaW5kZXg6IDYsCiAgICAgICAgbGFiZWw6ICJcdThCRTZcdTdFQzZcdTU3MzBcdTU3NDAiLAogICAgICAgIHZpc2libGU6IHRydWUKICAgICAgfSwgewogICAgICAgIGtleTogImF1dGhDb21wYW55IiwKICAgICAgICBpbmRleDogNywKICAgICAgICBsYWJlbDogIlx1ODhBQlx1NjM4OFx1Njc0M1x1NTE2Q1x1NTNGOCIsCiAgICAgICAgdmlzaWJsZTogdHJ1ZQogICAgICB9LCB7CiAgICAgICAga2V5OiAiZGlzdHJpYnV0b3IiLAogICAgICAgIGluZGV4OiA4LAogICAgICAgIGxhYmVsOiAiXHU2MjQwXHU1QzVFXHU3RUNGXHU5NTAwXHU1NTQ2IiwKICAgICAgICB2aXNpYmxlOiB0cnVlCiAgICAgIH0sIHsKICAgICAgICBrZXk6ICJiaWRkaW5nQ29tcGFueSIsCiAgICAgICAgaW5kZXg6IDksCiAgICAgICAgbGFiZWw6ICJcdTYyREJcdTY4MDdcdTUzNTVcdTRGNEQiLAogICAgICAgIHZpc2libGU6IHRydWUKICAgICAgfSwgewogICAgICAgIGtleTogIm1vZGVsIiwKICAgICAgICBpbmRleDogMTAsCiAgICAgICAgbGFiZWw6ICJcdTYyOTVcdTY4MDdcdTRFQTdcdTU0QzFcdTU3OEJcdTUzRjciLAogICAgICAgIHZpc2libGU6IHRydWUKICAgICAgfSwgewogICAgICAgIGtleTogInNwZWMiLAogICAgICAgIGluZGV4OiAxMSwKICAgICAgICBsYWJlbDogIlx1NjI5NVx1NjgwN1x1NEVBN1x1NTRDMVx1ODlDNFx1NjgzQyIsCiAgICAgICAgdmlzaWJsZTogdHJ1ZQogICAgICB9LCB7CiAgICAgICAga2V5OiAiYXJlYSIsCiAgICAgICAgaW5kZXg6IDEyLAogICAgICAgIGxhYmVsOiAiXHU1Qjg5XHU4OEM1XHU5NzYyXHU3OUVGIiwKICAgICAgICB2aXNpYmxlOiB0cnVlCiAgICAgIH0sIHsKICAgICAgICBrZXk6ICJyZXF1aXJlSW5mbyIsCiAgICAgICAgaW5kZXg6IDEzLAogICAgICAgIGxhYmVsOiAiXHU2MjQwXHU5NzAwXHU4RDQ0XHU2NTk5IiwKICAgICAgICB2aXNpYmxlOiB0cnVlCiAgICAgIH0sIHsKICAgICAgICBrZXk6ICJpbmZvVHlwZSIsCiAgICAgICAgaW5kZXg6IDE0LAogICAgICAgIGxhYmVsOiAiXHU4RDQ0XHU2NTk5XHU3QzdCXHU1NzhCIiwKICAgICAgICB2aXNpYmxlOiB0cnVlCiAgICAgIH0sIHsKICAgICAgICBrZXk6ICJzY2FuRmlsZSIsCiAgICAgICAgaW5kZXg6IDE1LAogICAgICAgIGxhYmVsOiAiXHU4RDQ0XHU2NTk5XHU2M0E1XHU2NTM2XHU5MEFFXHU0RUY2IiwKICAgICAgICB2aXNpYmxlOiB0cnVlCiAgICAgIH0sIHsKICAgICAgICBrZXk6ICJzY2FuRmlsZSIsCiAgICAgICAgaW5kZXg6IDE2LAogICAgICAgIGxhYmVsOiAiXHU4RDQ0XHU2NTk5XHU2M0E1XHU2NTM2XHU1NzMwXHU1NzQwIiwKICAgICAgICB2aXNpYmxlOiB0cnVlCiAgICAgIH0sIHsKICAgICAgICBrZXk6ICJiZWxvbmdQcm92aW5jZSIsCiAgICAgICAgaW5kZXg6IDE3LAogICAgICAgIGxhYmVsOiAiXHU5ODc5XHU3NkVFXHU2MjQwXHU1QzVFXHU3NzAxXHU0RUZEIiwKICAgICAgICB2aXNpYmxlOiB0cnVlCiAgICAgIH0sIHsKICAgICAgICBrZXk6ICJhZnRlclNhbGVZZWFyIiwKICAgICAgICBpbmRleDogMTgsCiAgICAgICAgbGFiZWw6ICJcdTU1MkVcdTU0MEVcdTVFNzRcdTk2NTAiLAogICAgICAgIHZpc2libGU6IHRydWUKICAgICAgfSwgewogICAgICAgIGtleTogIm9wZW5EYXRlIiwKICAgICAgICBpbmRleDogMTksCiAgICAgICAgbGFiZWw6ICJcdTVGMDBcdTY4MDdcdTY1RTVcdTY3MUYiLAogICAgICAgIHZpc2libGU6IHRydWUKICAgICAgfSwgewogICAgICAgIGtleTogImhhbmdEYXRlIiwKICAgICAgICBpbmRleDogMjAsCiAgICAgICAgbGFiZWw6ICJcdTYzMDJcdTdGNTFcdTY1RTVcdTY3MUYiLAogICAgICAgIHZpc2libGU6IHRydWUKICAgICAgfSwgewogICAgICAgIGtleTogImNyZWF0ZVRpbWUiLAogICAgICAgIGluZGV4OiAyMSwKICAgICAgICBsYWJlbDogIlx1NjNEMFx1NEVBNFx1NjVGNlx1OTVGNCIsCiAgICAgICAgdmlzaWJsZTogdHJ1ZQogICAgICB9LCB7CiAgICAgICAga2V5OiAidXBkYXRlVGltZSIsCiAgICAgICAgaW5kZXg6IDIyLAogICAgICAgIGxhYmVsOiAiXHU0RkVFXHU2NTM5XHU2NUY2XHU5NUY0IiwKICAgICAgICB2aXNpYmxlOiB0cnVlCiAgICAgIH0KICAgICAgLy8geyBrZXk6ICJhdWRpdFN0YXR1cyIsIGluZGV4OiAxOSwgbGFiZWw6IGDlrqHmoLjnirbmgIFgLCB2aXNpYmxlOiBmYWxzZSB9LAogICAgICAvLyB7IGtleTogImVkaXRTdGF0dXMiLCBpbmRleDogMjAsIGxhYmVsOiBg57yW6L6R54q25oCBYCwgdmlzaWJsZTogZmFsc2UgfSwKICAgICAgLy8geyBrZXk6ICIxMSIsIGluZGV4OiAyMSwgbGFiZWw6IGDmjojmnYPkuaZgLCB2aXNpYmxlOiBmYWxzZSB9LAogICAgICAvL3sga2V5OiAiMTIiLCBpbmRleDogMjMsIGxhYmVsOiBg5ZSu5ZCO5pyN5Yqh5om/6K+65Ye9YCwgdmlzaWJsZTogZmFsc2UgfSwKICAgICAgXSwKICAgICAgb3B0aW9uczogcmVnaW9uRGF0YSwKICAgICAgc2VsZWN0ZWRPcHRpb25zOiBbXSwKICAgICAgcXVlcnlBcmVhOiBbXSwKICAgICAgdmlld09wZW46IGZhbHNlLAogICAgICB2aWV3OiB7fSwKICAgICAgaW5mb0xpc3QxOiBbXSwKICAgICAgaW5mb0xpc3QyOiBbXSwKICAgICAgZGVmS2V5OiAicHJvY2Vzc19wcm9qZWN0X3JlcG9ydCIsCiAgICAgIHByb2NJbnNJZDogdW5kZWZpbmVkLAogICAgICB0YXNrSWQ6IHVuZGVmaW5lZCwKICAgICAgZmluaXNoZWQ6IHRydWUsCiAgICAgIGJpektleTogdW5kZWZpbmVkLAogICAgICBhdWRpdFN0YXR1c1RyZWU6IFtdLAogICAgICBvcGVyYXRpb25UeXBlVHJlZTogW10sCiAgICAgIHVzZXJUeXBlVHJlZTogW10sCiAgICAgIGRlZmF1bHRQcm9wczogewogICAgICAgIGNoaWxkcmVuOiAiY2hpbGRyZW4iLAogICAgICAgIGxhYmVsOiAibGFiZWwiCiAgICAgIH0sCiAgICAgIG9sZE9wZXJhdGlvblR5cGU6IHVuZGVmaW5lZCwKICAgICAgc2hvd1VUeXBlOiB0cnVlLAogICAgICBjaG9vc2VPcHRUeXBlOiB1bmRlZmluZWQsCiAgICAgIGNob29zZUF1ZGl0U3RhdHVzOiB1bmRlZmluZWQsCiAgICAgIGNob29zZVVzZXJJZDogdW5kZWZpbmVkLAogICAgICBjaG9vc2VFZGl0U3RhdHVzOiB1bmRlZmluZWQsCiAgICAgIGNob29zZVNwYXJlMjogdW5kZWZpbmVkLAogICAgICBsaWtlTGlzdDogdW5kZWZpbmVkLAogICAgICBsaWtlQ291bnQ6IHVuZGVmaW5lZCwKICAgICAgYXV0aENvbXBhbnlzOiBbXSwKICAgICAgaXNBZG1pbjogdHJ1ZSwKICAgICAgYXVkaXRTdGF0dXNFZGl0OiB0cnVlLAogICAgICBpc0F1dGhJbWFnZXM6IGZhbHNlLAogICAgICBzZWFyY2hQaWNrZXJPcHRpb25zOiB7CiAgICAgICAgc2hvcnRjdXRzOiBbewogICAgICAgICAgdGV4dDogJ+acgOi/keS4gOWRqCcsCiAgICAgICAgICBvbkNsaWNrOiBmdW5jdGlvbiBvbkNsaWNrKHBpY2tlcikgewogICAgICAgICAgICB2YXIgZW5kID0gbmV3IERhdGUoKTsKICAgICAgICAgICAgdmFyIHN0YXJ0ID0gbmV3IERhdGUoKTsKICAgICAgICAgICAgc3RhcnQuc2V0VGltZShzdGFydC5nZXRUaW1lKCkgLSAzNjAwICogMTAwMCAqIDI0ICogNyk7CiAgICAgICAgICAgIHBpY2tlci4kZW1pdCgncGljaycsIFtzdGFydCwgZW5kXSk7CiAgICAgICAgICB9CiAgICAgICAgfSwgewogICAgICAgICAgdGV4dDogJ+acgOi/keS4gOS4quaciCcsCiAgICAgICAgICBvbkNsaWNrOiBmdW5jdGlvbiBvbkNsaWNrKHBpY2tlcikgewogICAgICAgICAgICB2YXIgZW5kID0gbmV3IERhdGUoKTsKICAgICAgICAgICAgdmFyIHN0YXJ0ID0gbmV3IERhdGUoKTsKICAgICAgICAgICAgc3RhcnQuc2V0VGltZShzdGFydC5nZXRUaW1lKCkgLSAzNjAwICogMTAwMCAqIDI0ICogMzApOwogICAgICAgICAgICBwaWNrZXIuJGVtaXQoJ3BpY2snLCBbc3RhcnQsIGVuZF0pOwogICAgICAgICAgfQogICAgICAgIH0sIHsKICAgICAgICAgIHRleHQ6ICfmnIDov5HkuInkuKrmnIgnLAogICAgICAgICAgb25DbGljazogZnVuY3Rpb24gb25DbGljayhwaWNrZXIpIHsKICAgICAgICAgICAgdmFyIGVuZCA9IG5ldyBEYXRlKCk7CiAgICAgICAgICAgIHZhciBzdGFydCA9IG5ldyBEYXRlKCk7CiAgICAgICAgICAgIHN0YXJ0LnNldFRpbWUoc3RhcnQuZ2V0VGltZSgpIC0gMzYwMCAqIDEwMDAgKiAyNCAqIDkwKTsKICAgICAgICAgICAgcGlja2VyLiRlbWl0KCdwaWNrJywgW3N0YXJ0LCBlbmRdKTsKICAgICAgICAgIH0KICAgICAgICB9XQogICAgICB9LAogICAgICBwaWNrZXJPcHRpb25zOiB7CiAgICAgICAgZGlzYWJsZWREYXRlOiBmdW5jdGlvbiBkaXNhYmxlZERhdGUodGltZSkgewogICAgICAgICAgcmV0dXJuIHRpbWUuZ2V0VGltZSgpID4gRGF0ZS5ub3coKTsKICAgICAgICB9LAogICAgICAgIHNob3J0Y3V0czogW3sKICAgICAgICAgIHRleHQ6ICLku4rlpKkiLAogICAgICAgICAgb25DbGljazogZnVuY3Rpb24gb25DbGljayhwaWNrZXIpIHsKICAgICAgICAgICAgcGlja2VyLiRlbWl0KCJwaWNrIiwgbmV3IERhdGUoKSk7CiAgICAgICAgICB9CiAgICAgICAgfSwgewogICAgICAgICAgdGV4dDogIuaYqOWkqSIsCiAgICAgICAgICBvbkNsaWNrOiBmdW5jdGlvbiBvbkNsaWNrKHBpY2tlcikgewogICAgICAgICAgICB2YXIgZGF0ZSA9IG5ldyBEYXRlKCk7CiAgICAgICAgICAgIGRhdGUuc2V0VGltZShkYXRlLmdldFRpbWUoKSAtIDM2MDAgKiAxMDAwICogMjQpOwogICAgICAgICAgICBwaWNrZXIuJGVtaXQoInBpY2siLCBkYXRlKTsKICAgICAgICAgIH0KICAgICAgICB9LCB7CiAgICAgICAgICB0ZXh0OiAi5LiA5ZGo5YmNIiwKICAgICAgICAgIG9uQ2xpY2s6IGZ1bmN0aW9uIG9uQ2xpY2socGlja2VyKSB7CiAgICAgICAgICAgIHZhciBkYXRlID0gbmV3IERhdGUoKTsKICAgICAgICAgICAgZGF0ZS5zZXRUaW1lKGRhdGUuZ2V0VGltZSgpIC0gMzYwMCAqIDEwMDAgKiAyNCAqIDcpOwogICAgICAgICAgICBwaWNrZXIuJGVtaXQoInBpY2siLCBkYXRlKTsKICAgICAgICAgIH0KICAgICAgICB9XQogICAgICB9LAogICAgICBzZWFyY2hGaWVsZDogJ2FsbCcsCiAgICAgIC8vIOaWsOWinu+8muW9k+WJjemAieS4reeahOaQnOe0ouWtl+aute+8jOm7mOiupOWFqOWtl+autQogICAgICBzZWFyY2hGaWVsZE9wdGlvbnM6IFt7CiAgICAgICAgbGFiZWw6ICflhajlrZfmrrUnLAogICAgICAgIHZhbHVlOiAnYWxsJwogICAgICB9LCB7CiAgICAgICAgbGFiZWw6ICfpobnnm67nvJblj7cnLAogICAgICAgIHZhbHVlOiAncHJvamVjdE5vJwogICAgICB9LCB7CiAgICAgICAgbGFiZWw6ICfpobnnm67lkI3np7AnLAogICAgICAgIHZhbHVlOiAncHJvamVjdE5hbWUnCiAgICAgIH0sIHsKICAgICAgICBsYWJlbDogJ+ivpue7huWcsOWdgCcsCiAgICAgICAgdmFsdWU6ICdhZGRyZXNzJwogICAgICB9LCB7CiAgICAgICAgbGFiZWw6ICfmi5vmoIfljZXkvY0nLAogICAgICAgIHZhbHVlOiAnYmlkZGluZ0NvbXBhbnknCiAgICAgIH0sIHsKICAgICAgICBsYWJlbDogJ+aOiOadg+WFrOWPuCcsCiAgICAgICAgdmFsdWU6ICdhdXRoQ29tcGFueScKICAgICAgfV0sCiAgICAgIHNlYXJjaFZhbHVlOiAnJywKICAgICAgLy8g5paw5aKe77ya5pCc57Si5YaF5a65CiAgICAgIGhpZ2hsaWdodEZpZWxkczogWydwcm9qZWN0Tm8nLCAncHJvamVjdE5hbWUnLCAnYWRkcmVzcycsICdiaWRkaW5nQ29tcGFueScsICdhdXRoQ29tcGFueSddIC8vIOaWsOWinu+8mumrmOS6ruWtl+autQogICAgfTsKICB9LAogIGFjdGl2YXRlZDogZnVuY3Rpb24gYWN0aXZhdGVkKCkgewogICAgY29uc29sZS5sb2coIj1yZXBvcnQgaW5kZXg9PT4+YWN0aXZhdGVkIik7CiAgICB0aGlzLmdldExpc3QoKTsKICB9LAogIGNyZWF0ZWQ6IGZ1bmN0aW9uIGNyZWF0ZWQoKSB7CiAgICB2YXIgX3RoaXMyID0gdGhpczsKICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgdGhpcy5nZXREaWN0cygicHJfb3BlcmF0aW9uX3R5cGUiKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICBfdGhpczIub3BlcmF0aW9uVHlwZU9wdGlvbnMgPSByZXNwb25zZS5kYXRhOwogICAgICB2YXIgb3B0ID0gW107CiAgICAgIG9wdC5wdXNoKHsKICAgICAgICBpZDogMCwKICAgICAgICBsYWJlbDogIuWFqOmDqCIKICAgICAgfSk7CiAgICAgIHJlc3BvbnNlLmRhdGEuZm9yRWFjaChmdW5jdGlvbiAoZWxlbSwgaW5kZXgpIHsKICAgICAgICB2YXIgb2JqID0ge307CiAgICAgICAgb2JqLmxhYmVsID0gZWxlbS5kaWN0TGFiZWw7CiAgICAgICAgb2JqLmlkID0gZWxlbS5kaWN0VmFsdWU7CiAgICAgICAgb3B0LnB1c2gob2JqKTsKICAgICAgfSk7CiAgICAgIHZhciBvcGVyYXRpb25UeXBlID0ge307CiAgICAgIG9wZXJhdGlvblR5cGUubGFiZWwgPSAi5pON5L2c57G75Z6LIjsKICAgICAgb3BlcmF0aW9uVHlwZS5jaGlsZHJlbiA9IG9wdDsKICAgICAgdmFyIG9wZXJhdGlvblR5cGVzID0gW107CiAgICAgIG9wZXJhdGlvblR5cGVzLnB1c2gob3BlcmF0aW9uVHlwZSk7CiAgICAgIF90aGlzMi5vcGVyYXRpb25UeXBlVHJlZSA9IG9wZXJhdGlvblR5cGVzOwogICAgfSk7CiAgICB0aGlzLmdldERpY3RzKCJwcl9hdWRpdF9zdGF0dXMiKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICB2YXIgdHlwZSA9IDA7CiAgICAgIGlmIChfdGhpczIuJHN0b3JlLnN0YXRlLnVzZXIucm9sZXMpIHsKICAgICAgICBpZiAoX3RoaXMyLiRzdG9yZS5zdGF0ZS51c2VyLnJvbGVzLmluY2x1ZGVzKCJjb21tb24iKSkgewogICAgICAgICAgdHlwZSA9IDE7CiAgICAgICAgfQogICAgICAgIGlmIChfdGhpczIuJHN0b3JlLnN0YXRlLnVzZXIucm9sZXMuaW5jbHVkZXMoInByb3ZpbmNlX2FkbWluIikpIHsKICAgICAgICAgIHR5cGUgPSAyOwogICAgICAgIH0KICAgICAgICBpZiAoX3RoaXMyLiRzdG9yZS5zdGF0ZS51c2VyLnJvbGVzLmluY2x1ZGVzKCJyZXBvcnRfYWRtaW4iKSkgewogICAgICAgICAgdHlwZSA9IDM7CiAgICAgICAgfQogICAgICB9CiAgICAgIF90aGlzMi5hdWRpdFN0YXR1c09wdGlvbnMgPSByZXNwb25zZS5kYXRhOwogICAgICB2YXIgb3B0ID0gW107CiAgICAgIG9wdC5wdXNoKHsKICAgICAgICBpZDogOSwKICAgICAgICBsYWJlbDogIuWFqOmDqCIKICAgICAgfSk7CiAgICAgIGlmICh0eXBlID09IDIgfHwgdHlwZSA9PSAzKSB7CiAgICAgICAgb3B0LnB1c2goewogICAgICAgICAgaWQ6IDEwLAogICAgICAgICAgbGFiZWw6ICLmnKrlrqHmibkiCiAgICAgICAgfSk7CiAgICAgIH0KICAgICAgcmVzcG9uc2UuZGF0YS5mb3JFYWNoKGZ1bmN0aW9uIChlbGVtLCBpbmRleCkgewogICAgICAgIHZhciBvYmogPSB7fTsKICAgICAgICBvYmoubGFiZWwgPSBlbGVtLmRpY3RMYWJlbDsKICAgICAgICBvYmouaWQgPSBlbGVtLmRpY3RWYWx1ZTsKICAgICAgICBvcHQucHVzaChvYmopOwogICAgICB9KTsKICAgICAgdmFyIGF1ZGl0U3RhdHVzVHJlZSA9IHt9OwogICAgICBhdWRpdFN0YXR1c1RyZWUubGFiZWwgPSAi5a6h5qC454q25oCBIjsKICAgICAgYXVkaXRTdGF0dXNUcmVlLmNoaWxkcmVuID0gb3B0OwogICAgICB2YXIgYXVkaXRTdGF0dXNUcmVlcyA9IFtdOwogICAgICBhdWRpdFN0YXR1c1RyZWVzLnB1c2goYXVkaXRTdGF0dXNUcmVlKTsKICAgICAgX3RoaXMyLmF1ZGl0U3RhdHVzVHJlZSA9IGF1ZGl0U3RhdHVzVHJlZXM7CiAgICB9KTsKICAgIHRoaXMuZ2V0RGljdHMoInByX2VkaXRfc3RhdHVzIikudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgX3RoaXMyLmVkaXRTdGF0dXNPcHRpb25zID0gcmVzcG9uc2UuZGF0YTsKICAgIH0pOwogICAgLy8gdGhpcy5nZXREaWN0cygicHJfYmlkZGluZ190eXBlIikudGhlbigocmVzcG9uc2UpID0+IHsKICAgIC8vICAgdGhpcy5iaWRkaW5nVHlwZU9wdGlvbnMgPSByZXNwb25zZS5kYXRhOwogICAgLy8gfSk7CiAgICB0aGlzLmdldERpY3RzKCJwcl9tb2RlbCIpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgIF90aGlzMi5tb2RlbE9wdGlvbjEgPSByZXNwb25zZS5kYXRhOwogICAgICB2YXIgb3B0ID0gW107CiAgICAgIHJlc3BvbnNlLmRhdGEuZm9yRWFjaChmdW5jdGlvbiAoZWxlbSwgaW5kZXgpIHsKICAgICAgICB2YXIgb2JqID0ge307CiAgICAgICAgb2JqLmxhYmVsID0gZWxlbS5kaWN0TGFiZWw7CiAgICAgICAgb2JqLnZhbHVlID0gZWxlbS5kaWN0VmFsdWU7CiAgICAgICAgb3B0LnB1c2gob2JqKTsKICAgICAgfSk7CiAgICAgIF90aGlzMi5tb2RlbE9wdGlvbnMgPSBvcHQ7CiAgICB9KTsKICAgIHRoaXMuZ2V0RGljdHMoInByX3NwZWMiKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICBfdGhpczIuc3BlY09wdGlvbjEgPSByZXNwb25zZS5kYXRhOwogICAgICB2YXIgb3B0ID0gW107CiAgICAgIHJlc3BvbnNlLmRhdGEuZm9yRWFjaChmdW5jdGlvbiAoZWxlbSwgaW5kZXgpIHsKICAgICAgICB2YXIgb2JqID0ge307CiAgICAgICAgb2JqLmxhYmVsID0gZWxlbS5kaWN0TGFiZWw7CiAgICAgICAgb2JqLnZhbHVlID0gZWxlbS5kaWN0VmFsdWU7CiAgICAgICAgb3B0LnB1c2gob2JqKTsKICAgICAgfSk7CiAgICAgIF90aGlzMi5zcGVjT3B0aW9ucyA9IG9wdDsKICAgIH0pOwogICAgdGhpcy5nZXREaWN0cygicHJfaW5mbyIpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgIF90aGlzMi5yZXF1aXJlSW5mb09wdGlvbjEgPSByZXNwb25zZS5kYXRhOwogICAgICB2YXIgb3B0ID0gW107CiAgICAgIHJlc3BvbnNlLmRhdGEuZm9yRWFjaChmdW5jdGlvbiAoZWxlbSwgaW5kZXgpIHsKICAgICAgICB2YXIgb2JqID0ge307CiAgICAgICAgb2JqLmxhYmVsID0gZWxlbS5kaWN0TGFiZWw7CiAgICAgICAgb2JqLnZhbHVlID0gZWxlbS5kaWN0VmFsdWU7CiAgICAgICAgb3B0LnB1c2gob2JqKTsKICAgICAgfSk7CiAgICAgIF90aGlzMi5yZXF1aXJlSW5mb09wdGlvbnMgPSBvcHQ7CiAgICB9KTsKICAgIHRoaXMuZ2V0RGljdHMoInByX3Byb3ZpbmNlIikudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgX3RoaXMyLmJlbG9uZ1Byb3ZpbmNlT3B0aW9uczEgPSByZXNwb25zZS5kYXRhOwogICAgICB2YXIgb3B0ID0gW107CiAgICAgIHJlc3BvbnNlLmRhdGEuZm9yRWFjaChmdW5jdGlvbiAoZWxlbSwgaW5kZXgpIHsKICAgICAgICB2YXIgb2JqID0ge307CiAgICAgICAgb2JqLmxhYmVsID0gZWxlbS5kaWN0TGFiZWw7CiAgICAgICAgb2JqLnZhbHVlID0gZWxlbS5kaWN0VmFsdWU7CiAgICAgICAgb3B0LnB1c2gob2JqKTsKICAgICAgfSk7CiAgICAgIF90aGlzMi5iZWxvbmdQcm92aW5jZU9wdGlvbnMgPSBvcHQ7CiAgICB9KTsKICAgIHRoaXMuZ2V0RGljdHMoInByX2FmdGVyX3NhbGVfeWVhciIpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgIF90aGlzMi5hZnRlclNhbGVZZWFyT3B0aW9uczEgPSByZXNwb25zZS5kYXRhOwogICAgICB2YXIgb3B0ID0gW107CiAgICAgIHJlc3BvbnNlLmRhdGEuZm9yRWFjaChmdW5jdGlvbiAoZWxlbSwgaW5kZXgpIHsKICAgICAgICB2YXIgb2JqID0ge307CiAgICAgICAgb2JqLmxhYmVsID0gZWxlbS5kaWN0TGFiZWw7CiAgICAgICAgb2JqLnZhbHVlID0gZWxlbS5kaWN0VmFsdWU7CiAgICAgICAgb3B0LnB1c2gob2JqKTsKICAgICAgfSk7CiAgICAgIF90aGlzMi5hZnRlclNhbGVZZWFyT3B0aW9ucyA9IG9wdDsKICAgIH0pOwogICAgdGhpcy5nZXREaWN0cygicHJfZGF0YV90eXBlIikudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgX3RoaXMyLmluZm9UeXBlT3B0aW9ucyA9IHJlc3BvbnNlLmRhdGE7CiAgICB9KTsKICAgIHZhciBvcHQgPSBbXTsKICAgIG9wdC5wdXNoKHsKICAgICAgaWQ6IDAsCiAgICAgIGxhYmVsOiAi5YWo6YOoIgogICAgfSk7CiAgICBvcHQucHVzaCh7CiAgICAgIGlkOiAyLAogICAgICBsYWJlbDogIuaZrumAmueUqOaItyIKICAgIH0pOwogICAgb3B0LnB1c2goewogICAgICBpZDogMTAsCiAgICAgIGxhYmVsOiAi55yB6LSf6LSj5Lq6IgogICAgfSk7CiAgICB2YXIgdXNlclR5cGUgPSB7fTsKICAgIHVzZXJUeXBlLmxhYmVsID0gIuaJgOWxnueUqOaItyI7CiAgICB1c2VyVHlwZS5jaGlsZHJlbiA9IG9wdDsKICAgIHZhciB1c2VyVHlwZXMgPSBbXTsKICAgIHVzZXJUeXBlcy5wdXNoKHVzZXJUeXBlKTsKICAgIHRoaXMudXNlclR5cGVUcmVlID0gdXNlclR5cGVzOwogICAgaWYgKHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIucm9sZXMgJiYgdGhpcy4kc3RvcmUuc3RhdGUudXNlci5yb2xlcy5pbmNsdWRlcygiY29tbW9uIikpIHsKICAgICAgdGhpcy5zaG93VVR5cGUgPSBmYWxzZTsKICAgIH0KICAgIGlmICh0aGlzLl9pc01vYmlsZSgpKSB7CiAgICAgIHRoaXMuaXNNb2JpbGUgPSB0cnVlOwogICAgICB0aGlzLnBhZ2VMYXlvdXQgPSAidG90YWwsIHByZXYsIG5leHQsIGp1bXBlciI7CiAgICB9CiAgICBpZiAodGhpcy4kc3RvcmUuc3RhdGUudXNlci5yb2xlcyAmJiB0aGlzLiRzdG9yZS5zdGF0ZS51c2VyLnJvbGVzLmluY2x1ZGVzKCJyZXBvcnRfYWRtaW4iKSkgewogICAgICB0aGlzLmlzQWRtaW4gPSBmYWxzZTsKICAgIH0KICB9LAogIG1ldGhvZHM6IHsKICAgIF9pc01vYmlsZTogZnVuY3Rpb24gX2lzTW9iaWxlKCkgewogICAgICB2YXIgZmxhZyA9IG5hdmlnYXRvci51c2VyQWdlbnQubWF0Y2goLyhwaG9uZXxwYWR8cG9kfGlQaG9uZXxpUG9kfGlvc3xpUGFkfEFuZHJvaWR8TW9iaWxlfEJsYWNrQmVycnl8SUVNb2JpbGV8TVFRQnJvd3NlcnxKVUN8RmVubmVjfHdPU0Jyb3dzZXJ8QnJvd3Nlck5HfFdlYk9TfFN5bWJpYW58V2luZG93cyBQaG9uZSkvaSk7CiAgICAgIHJldHVybiBmbGFnOwogICAgfSwKICAgIC8qKiDmn6Xor6Lpobnnm67miqXlpIfliJfooaggKi9nZXRMaXN0OiBmdW5jdGlvbiBnZXRMaXN0KCkgewogICAgICB2YXIgX3RoaXMzID0gdGhpczsKICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsKICAgICAgbGlzdFJlcG9ydCh0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgIF90aGlzMy5yZXBvcnRMaXN0ID0gcmVzcG9uc2Uucm93czsKICAgICAgICBfdGhpczMudG90YWwgPSByZXNwb25zZS50b3RhbDsKICAgICAgICBfdGhpczMubG9hZGluZyA9IGZhbHNlOwogICAgICB9KTsKICAgIH0sCiAgICBoYW5kbGVEb3dubG9hZDogZnVuY3Rpb24gaGFuZGxlRG93bmxvYWQodXJsKSB7CiAgICAgIHZhciBhID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgiYSIpOyAvLyDliJvlu7rkuIDkuKpIVE1MIOWFg+e0oAogICAgICBhLnNldEF0dHJpYnV0ZSgidGFyZ2V0IiwgIl9ibGFuayIpOwogICAgICBhLnNldEF0dHJpYnV0ZSgiZG93bmxvYWQiLCAiIik7IC8vZG93bmxvYWTlsZ7mgKcKICAgICAgdmFyIGhyZWYgPSAiaHR0cHM6Ly9yZXBvcnQuY2xsZWQuY29tL3Byb2QtYXBpL2NvbW1vbi9kb3dubG9hZC9yZXNvdXJjZT9yZXNvdXJjZT0iICsgdXJsOwogICAgICBjb25zb2xlLmxvZyhocmVmKTsKICAgICAgYS5zZXRBdHRyaWJ1dGUoImhyZWYiLCBocmVmKTsgLy8gaHJlZumTvuaOpQogICAgICBhLmNsaWNrKCk7IC8vIOiHquaJp+ihjOeCueWHu+S6i+S7tgogICAgfSwKICAgIC8vIOWuoeaguOeKtuaAgeiKgueCueWNleWHu+S6i+S7tgogICAgaGFuZGxlQXVkaXROb2RlQ2xpY2s6IGZ1bmN0aW9uIGhhbmRsZUF1ZGl0Tm9kZUNsaWNrKGRhdGEpIHsKICAgICAgaWYgKGRhdGEuaWQgPT0gOSkgewogICAgICAgIHRoaXMucXVlcnlQYXJhbXMuYXVkaXRTdGF0dXMgPSB1bmRlZmluZWQ7CiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5ub2RlID0gdW5kZWZpbmVkOwogICAgICAgIHRoaXMucXVlcnlQYXJhbXMuc3BhcmUxID0gdW5kZWZpbmVkOwogICAgICB9IGVsc2UgewogICAgICAgIGlmIChkYXRhLmlkID09IDEgfHwgZGF0YS5pZCA9PSAxMCkgewogICAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5hdWRpdFN0YXR1cyA9IDE7CiAgICAgICAgICBpZiAodGhpcy4kc3RvcmUuc3RhdGUudXNlci5yb2xlcykgewogICAgICAgICAgICBpZiAodGhpcy4kc3RvcmUuc3RhdGUudXNlci5yb2xlcy5pbmNsdWRlcygicHJvdmluY2VfYWRtaW4iKSkgewogICAgICAgICAgICAgIHRoaXMucXVlcnlQYXJhbXMubm9kZSA9ICLnnIHotJ/otKPkuroiOwogICAgICAgICAgICAgIGlmIChkYXRhLmlkID09IDEwKSB7CiAgICAgICAgICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnNwYXJlMSA9ICI9IjsKICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5zcGFyZTEgPSAiIT0iOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfQogICAgICAgICAgICBpZiAodGhpcy4kc3RvcmUuc3RhdGUudXNlci5yb2xlcy5pbmNsdWRlcygicmVwb3J0X2FkbWluIikpIHsKICAgICAgICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLm5vZGUgPSAi5a6h5qC45ZGYIjsKICAgICAgICAgICAgICBpZiAoZGF0YS5pZCA9PSAxMCkgewogICAgICAgICAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5zcGFyZTEgPSAiPSI7CiAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgIHRoaXMucXVlcnlQYXJhbXMuc3BhcmUxID0gIiE9IjsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5hdWRpdFN0YXR1cyA9IGRhdGEuaWQ7CiAgICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLm5vZGUgPSB1bmRlZmluZWQ7CiAgICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnNwYXJlMSA9IHVuZGVmaW5lZDsKICAgICAgICB9CiAgICAgIH0KICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICB9LAogICAgLy8g5pON5L2c57G75Z6L6IqC54K55Y2V5Ye75LqL5Lu2CiAgICBoYW5kbGVPcHROb2RlQ2xpY2s6IGZ1bmN0aW9uIGhhbmRsZU9wdE5vZGVDbGljayhkYXRhKSB7CiAgICAgIGlmIChkYXRhLmlkID09IDApIHsKICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLm9wZXJhdGlvblR5cGUgPSB1bmRlZmluZWQ7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5vcGVyYXRpb25UeXBlID0gZGF0YS5pZDsKICAgICAgfQogICAgICB0aGlzLmdldExpc3QoKTsKICAgIH0sCiAgICAvLyDnlKjmiLfnsbvlnovoioLngrnljZXlh7vkuovku7YKICAgIGhhbmRsZVVzZXJOb2RlQ2xpY2s6IGZ1bmN0aW9uIGhhbmRsZVVzZXJOb2RlQ2xpY2soZGF0YSkgewogICAgICBpZiAoZGF0YS5pZCA9PSAwKSB7CiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy51c2VyVHlwZSA9IHVuZGVmaW5lZDsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnVzZXJUeXBlID0gZGF0YS5pZDsKICAgICAgfQogICAgICB0aGlzLmdldExpc3QoKTsKICAgIH0sCiAgICAvLyDnrZvpgInoioLngrkKICAgIGZpbHRlck5vZGU6IGZ1bmN0aW9uIGZpbHRlck5vZGUodmFsdWUsIGRhdGEpIHsKICAgICAgaWYgKCF2YWx1ZSkgcmV0dXJuIHRydWU7CiAgICAgIHJldHVybiBkYXRhLmxhYmVsLmluZGV4T2YodmFsdWUpICE9PSAtMTsKICAgIH0sCiAgICBzZWFyY2hGb3JtYXQ6IGZ1bmN0aW9uIHNlYXJjaEZvcm1hdChyb3csIGNvbHVtbikgewogICAgICBpZiAocm93LmluZGV4T2YodGhpcy5xdWVyeVBhcmFtcy5zcGFyZTEpICE9PSAtMSAmJiB0aGlzLnF1ZXJ5UGFyYW1zLnNwYXJlMSAhPT0gIiIpIHsKICAgICAgICByZXR1cm4gcm93LnJlcGxhY2UodGhpcy5xdWVyeVBhcmFtcy5zcGFyZTEsICc8Zm9udCBjb2xvcj0iI2YwMCI+JyArIHRoaXMucXVlcnlQYXJhbXMuc3BhcmUxICsgIjwvZm9udD4iKTsKICAgICAgfSBlbHNlIHsKICAgICAgICByZXR1cm4gcm93OwogICAgICB9CiAgICB9LAogICAgLy8g5pON5L2c57G75Z6L5a2X5YW457+76K+RCiAgICBvcGVyYXRpb25UeXBlRm9ybWF0OiBmdW5jdGlvbiBvcGVyYXRpb25UeXBlRm9ybWF0KHJvdywgY29sdW1uKSB7CiAgICAgIHJldHVybiB0aGlzLnNlbGVjdERpY3RMYWJlbCh0aGlzLm9wZXJhdGlvblR5cGVPcHRpb25zLCByb3cub3BlcmF0aW9uVHlwZSk7CiAgICB9LAogICAgLy8g5a6h5qC454q25oCB5a2X5YW457+76K+RCiAgICBhdWRpdFN0YXR1c0Zvcm1hdDogZnVuY3Rpb24gYXVkaXRTdGF0dXNGb3JtYXQocm93LCBjb2x1bW4pIHsKICAgICAgcmV0dXJuIHRoaXMuc2VsZWN0RGljdExhYmVsKHRoaXMuYXVkaXRTdGF0dXNPcHRpb25zLCByb3cuYXVkaXRTdGF0dXMpOwogICAgfSwKICAgIC8vIOe8lui+keeKtuaAgeWtl+WFuOe/u+ivkQogICAgZWRpdFN0YXR1c0Zvcm1hdDogZnVuY3Rpb24gZWRpdFN0YXR1c0Zvcm1hdChyb3csIGNvbHVtbikgewogICAgICByZXR1cm4gdGhpcy5zZWxlY3REaWN0TGFiZWwodGhpcy5lZGl0U3RhdHVzT3B0aW9ucywgcm93LmVkaXRTdGF0dXMpOwogICAgfSwKICAgIC8vIOaLm+agh+aWueW8j+Wtl+WFuOe/u+ivkQogICAgYmlkZGluZ1R5cGVGb3JtYXQ6IGZ1bmN0aW9uIGJpZGRpbmdUeXBlRm9ybWF0KHJvdywgY29sdW1uKSB7CiAgICAgIHJldHVybiB0aGlzLnNlbGVjdERpY3RMYWJlbCh0aGlzLmJpZGRpbmdUeXBlT3B0aW9ucywgcm93LmJpZGRpbmdUeXBlKTsKICAgIH0sCiAgICAvLyDmipXmoIfkuqflk4Hlnovlj7flrZflhbjnv7vor5EKICAgIG1vZGVsRm9ybWF0OiBmdW5jdGlvbiBtb2RlbEZvcm1hdChyb3csIGNvbHVtbikgewogICAgICByZXR1cm4gdGhpcy5zZWxlY3REaWN0TGFiZWxzKHRoaXMubW9kZWxPcHRpb24xLCByb3cubW9kZWwpOwogICAgfSwKICAgIC8vIOaKleagh+S6p+WTgeinhOagvOWtl+WFuOe/u+ivkQogICAgc3BlY0Zvcm1hdDogZnVuY3Rpb24gc3BlY0Zvcm1hdChyb3csIGNvbHVtbikgewogICAgICByZXR1cm4gdGhpcy5zZWxlY3REaWN0TGFiZWxzKHRoaXMuc3BlY09wdGlvbjEsIHJvdy5zcGVjKTsKICAgIH0sCiAgICAvLyDmiYDpnIDotYTmlpnlrZflhbjnv7vor5EKICAgIHJlcXVpcmVJbmZvRm9ybWF0OiBmdW5jdGlvbiByZXF1aXJlSW5mb0Zvcm1hdChyb3csIGNvbHVtbikgewogICAgICBpZiAocm93LnJlcXVpcmVJbmZvKSB7CiAgICAgICAgcmV0dXJuIHRoaXMuc2VsZWN0RGljdExhYmVscyh0aGlzLnJlcXVpcmVJbmZvT3B0aW9uMSwgcm93LnJlcXVpcmVJbmZvKTsKICAgICAgfQogICAgfSwKICAgIC8vIOi1hOaWmeexu+Wei+Wtl+WFuOe/u+ivkQogICAgaW5mb1R5cGVGb3JtYXQ6IGZ1bmN0aW9uIGluZm9UeXBlRm9ybWF0KHJvdywgY29sdW1uKSB7CiAgICAgIHJldHVybiB0aGlzLnNlbGVjdERpY3RMYWJlbHModGhpcy5pbmZvVHlwZU9wdGlvbnMsIHJvdy5pbmZvVHlwZSk7CiAgICB9LAogICAgLy8g5omA5bGe55yB5Lu95a2X5YW457+76K+RCiAgICBiZWxvbmdQcm92aW5jZUZvcm1hdDogZnVuY3Rpb24gYmVsb25nUHJvdmluY2VGb3JtYXQocm93LCBjb2x1bW4pIHsKICAgICAgcmV0dXJuIHRoaXMuc2VsZWN0RGljdExhYmVscyh0aGlzLmJlbG9uZ1Byb3ZpbmNlT3B0aW9uczEsIHJvdy5iZWxvbmdQcm92aW5jZSk7CiAgICB9LAogICAgLy8g5ZSu5ZCO5bm06ZmQ5a2X5YW457+76K+RCiAgICBhZnRlclNhbGVZZWFyRm9ybWF0OiBmdW5jdGlvbiBhZnRlclNhbGVZZWFyRm9ybWF0KHJvdywgY29sdW1uKSB7CiAgICAgIHJldHVybiB0aGlzLnNlbGVjdERpY3RMYWJlbHModGhpcy5hZnRlclNhbGVZZWFyT3B0aW9uczEsIHJvdy5hZnRlclNhbGVZZWFyKTsKICAgIH0sCiAgICAvLyDlj5bmtojmjInpkq4KICAgIGNhbmNlbDogZnVuY3Rpb24gY2FuY2VsKCkgewogICAgICB0aGlzLm9wZW4gPSBmYWxzZTsKICAgICAgdGhpcy5yZXNldCgpOwogICAgfSwKICAgIHZpZXdPazogZnVuY3Rpb24gdmlld09rKCkgewogICAgICB0aGlzLnZpZXdPcGVuID0gZmFsc2U7CiAgICB9LAogICAgLy8g6KGo5Y2V6YeN572uCiAgICByZXNldDogZnVuY3Rpb24gcmVzZXQoKSB7CiAgICAgIHRoaXMuZm9ybSA9IHsKICAgICAgICBwcm9qZWN0SWQ6IG51bGwsCiAgICAgICAgcHJvamVjdE5vOiBudWxsLAogICAgICAgIHByb2plY3ROYW1lOiBudWxsLAogICAgICAgIG9wZXJhdGlvblR5cGU6IG51bGwsCiAgICAgICAgYXVkaXRTdGF0dXM6ICIwIiwKICAgICAgICByZWplY3RSZWFzb246IG51bGwsCiAgICAgICAgcHJvdmluY2U6IG51bGwsCiAgICAgICAgY2l0eTogbnVsbCwKICAgICAgICBkaXN0cmljdDogbnVsbCwKICAgICAgICBhZGRyZXNzOiBudWxsLAogICAgICAgIGVkaXRTdGF0dXM6ICIwIiwKICAgICAgICBiZWxvbmdVc2VyOiBudWxsLAogICAgICAgIGJpZGRpbmdDb21wYW55OiBudWxsLAogICAgICAgIG9wZW5EYXRlOiBudWxsLAogICAgICAgIGJpZGRpbmdUeXBlOiBudWxsLAogICAgICAgIGJ1ZGdldE1vbmV5OiBudWxsLAogICAgICAgIGF1dGhDb21wYW55OiBudWxsLAogICAgICAgIGJpZGRpbmdOZXQ6IG51bGwsCiAgICAgICAgZGlzdHJpYnV0b3I6IG51bGwsCiAgICAgICAgbW9kZWw6IFtdLAogICAgICAgIHNwZWM6IFtdLAogICAgICAgIGFyZWE6IG51bGwsCiAgICAgICAgYXV0aEZpbGU6IG51bGwsCiAgICAgICAgYWZ0ZXJTYWxlRmlsZTogbnVsbCwKICAgICAgICByZXF1aXJlSW5mbzogW10sCiAgICAgICAgaW5mb1R5cGU6IFtdLAogICAgICAgIHNjYW5GaWxlOiBudWxsLAogICAgICAgIHNlbmRBZGRyZXNzOiBudWxsLAogICAgICAgIG1haWxJbmZvOiBudWxsLAogICAgICAgIGV4cHJlc3NJbmZvOiBudWxsLAogICAgICAgIHJlbWFyazogbnVsbCwKICAgICAgICBzcGFyZTE6IG51bGwsCiAgICAgICAgc3BhcmUyOiBudWxsCiAgICAgIH07CiAgICAgIHRoaXMucmVzZXRGb3JtKCJmb3JtIik7CiAgICB9LAogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqL2hhbmRsZVF1ZXJ5OiBmdW5jdGlvbiBoYW5kbGVRdWVyeSgpIHsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsKICAgICAgLy8g5riF56m65omA5pyJ55u45YWz5a2X5q61CiAgICAgIHRoaXMucXVlcnlQYXJhbXMucHJvamVjdE5vID0gbnVsbDsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wcm9qZWN0TmFtZSA9IG51bGw7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMuYWRkcmVzcyA9IG51bGw7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMuYmlkZGluZ0NvbXBhbnkgPSBudWxsOwogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmF1dGhDb21wYW55ID0gbnVsbDsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5mdWxsRmllbGQgPSBudWxsOwogICAgICBpZiAodGhpcy5zZWFyY2hGaWVsZCA9PT0gJ2FsbCcpIHsKICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmZ1bGxGaWVsZCA9IHRoaXMuc2VhcmNoVmFsdWU7IC8vIOWBh+iuvuWQjuerryBmdWxsRmllbGQg5YGa5YWo5a2X5q615qih57OKCiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy5xdWVyeVBhcmFtc1t0aGlzLnNlYXJjaEZpZWxkXSA9IHRoaXMuc2VhcmNoVmFsdWU7CiAgICAgIH0KICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICB9LAogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqL3Jlc2V0UXVlcnk6IGZ1bmN0aW9uIHJlc2V0UXVlcnkoKSB7CiAgICAgIHRoaXMucXVlcnlBcmVhID0gW107CiAgICAgIHRoaXMucXVlcnlQYXJhbXMgPSB7CiAgICAgICAgcGFnZU51bTogMSwKICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgcHJvamVjdE5vOiBudWxsLAogICAgICAgIHByb2plY3ROYW1lOiBudWxsLAogICAgICAgIG9wZXJhdGlvblR5cGU6IG51bGwsCiAgICAgICAgYXVkaXRTdGF0dXM6IG51bGwsCiAgICAgICAgcHJvdmluY2U6IG51bGwsCiAgICAgICAgdXNlclR5cGU6IG51bGwsCiAgICAgICAgYmVsb25nVXNlcjogbnVsbCwKICAgICAgICBzcGFyZTE6IG51bGwsCiAgICAgICAgYWRkcmVzczogbnVsbCwKICAgICAgICBiaWRkaW5nQ29tcGFueTogbnVsbCwKICAgICAgICBhdXRoQ29tcGFueTogbnVsbCwKICAgICAgICBmdWxsRmllbGQ6IG51bGwKICAgICAgfTsKICAgICAgdGhpcy5zZWFyY2hGaWVsZCA9ICdhbGwnOwogICAgICB0aGlzLnNlYXJjaFZhbHVlID0gJyc7CiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsKICAgIH0sCiAgICAvLyDlpJrpgInmoYbpgInkuK3mlbDmja4KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZTogZnVuY3Rpb24gaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgewogICAgICB0aGlzLmlkcyA9IHNlbGVjdGlvbi5tYXAoZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICByZXR1cm4gaXRlbS5wcm9qZWN0SWQ7CiAgICAgIH0pOwogICAgICB0aGlzLnNpbmdsZSA9IHNlbGVjdGlvbi5sZW5ndGggIT09IDE7CiAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aDsKICAgICAgdGhpcy5jaG9vc2VPcHRUeXBlID0gc2VsZWN0aW9uWzBdLm9wZXJhdGlvblR5cGU7CiAgICAgIHRoaXMuY2hvb3NlQXVkaXRTdGF0dXMgPSBzZWxlY3Rpb25bMF0uYXVkaXRTdGF0dXM7CiAgICAgIHRoaXMuY2hvb3NlVXNlcklkID0gc2VsZWN0aW9uWzBdLnVzZXJJZDsKICAgICAgdGhpcy5jaG9vc2VFZGl0U3RhdHVzID0gc2VsZWN0aW9uWzBdLmVkaXRTdGF0dXM7CiAgICAgIHRoaXMuY2hvb3NlU3BhcmUyID0gc2VsZWN0aW9uWzBdLnNwYXJlMjsKICAgIH0sCiAgICAvKiog5paw5aKe5oyJ6ZKu5pON5L2cICovaGFuZGxlQWRkOiBmdW5jdGlvbiBoYW5kbGVBZGQoKSB7CiAgICAgIC8vIHRoaXMucmVzZXQoKTsKICAgICAgLy8gdGhpcy5vcGVuID0gdHJ1ZTsKICAgICAgLy8gdGhpcy50aXRsZSA9ICLmt7vliqDpobnnm67miqXlpIciOwogICAgICB0aGlzLiRyb3V0ZXIucHVzaCh7CiAgICAgICAgcGF0aDogIi9wcm9qZWN0L3JlcG9ydC9mb3JtIiwKICAgICAgICBxdWVyeTogewogICAgICAgICAgYnVzaW5lc3NLZXk6IHVuZGVmaW5lZCwKICAgICAgICAgIGZvcm1FZGl0OiB0cnVlCiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICBoYW5kbGVWaWV3OiBmdW5jdGlvbiBoYW5kbGVWaWV3KHJvdykgewogICAgICB2YXIgX3RoaXM0ID0gdGhpczsKICAgICAgdmFyIHRoYXQgPSB0aGlzOwogICAgICB0aGlzLnZpZXcgPSBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KHJvdykpOwogICAgICB0aGlzLnZpZXcub3BlcmF0aW9uVHlwZSA9IHRoaXMub3BlcmF0aW9uVHlwZUZvcm1hdChyb3cpOwogICAgICB0aGlzLnZpZXcuYXVkaXRTdGF0dXMgPSB0aGlzLmF1ZGl0U3RhdHVzRm9ybWF0KHJvdyk7CiAgICAgIHRoaXMudmlldy5lZGl0U3RhdHVzID0gdGhpcy5lZGl0U3RhdHVzRm9ybWF0KHJvdyk7CiAgICAgIHRoaXMudmlldy5iaWRkaW5nVHlwZSA9IHRoaXMuYmlkZGluZ1R5cGVGb3JtYXQocm93KTsKICAgICAgdGhpcy52aWV3Lm1vZGVsID0gdGhpcy5tb2RlbEZvcm1hdChyb3cpOwogICAgICB0aGlzLnZpZXcuc3BlYyA9IHRoaXMuc3BlY0Zvcm1hdChyb3cpOwogICAgICB0aGlzLnZpZXcucmVxdWlyZUluZm8gPSB0aGlzLnJlcXVpcmVJbmZvRm9ybWF0KHJvdyk7CiAgICAgIHRoaXMudmlldy5pbmZvVHlwZSA9IHRoaXMuaW5mb1R5cGVGb3JtYXQocm93KTsKICAgICAgdGhpcy52aWV3LmJlbG9uZ1Byb3ZpbmNlID0gdGhpcy5iZWxvbmdQcm92aW5jZUZvcm1hdChyb3cpOwogICAgICB0aGlzLnZpZXcuYWZ0ZXJTYWxlWWVhciA9IHRoaXMuYWZ0ZXJTYWxlWWVhckZvcm1hdChyb3cpOwogICAgICBpZiAocm93LnJlcXVpcmVJbmZvcykgewogICAgICAgIHRoaXMuc2VsZWN0RGljdExhYmVscyh0aGlzLnJlcXVpcmVJbmZvT3B0aW9uMSwgcm93LnJlcXVpcmVJbmZvKTsKICAgICAgICAvL3RoaXMudmlldy5yZXF1aXJlSW5mbyA9CiAgICAgICAgLy8gY29uc3QgaW5mb0xpc3QgPSB0aGlzLnZpZXcucmVxdWlyZUluZm8uc3BsaXQoIiwiKTsKICAgICAgICB2YXIgaGFsZiA9IE1hdGguY2VpbChyb3cucmVxdWlyZUluZm9zLmxlbmd0aCAvIDIpOwogICAgICAgIHRoaXMuaW5mb0xpc3QxID0gcm93LnJlcXVpcmVJbmZvcy5zcGxpY2UoMCwgaGFsZik7CiAgICAgICAgdGhpcy5pbmZvTGlzdDIgPSByb3cucmVxdWlyZUluZm9zLnNwbGljZSgtaGFsZik7CgogICAgICAgIC8vIGNvbnN0IHRtcExpc3QxID0gaW5mb0xpc3Quc3BsaWNlKDAsIGhhbGYpOwogICAgICAgIC8vIGNvbnN0IHRtcExpc3QyID0gaW5mb0xpc3Quc3BsaWNlKC1oYWxmKTsKICAgICAgICAvLyB0bXBMaXN0MS5mb3JFYWNoKChlbGVtZW50KSA9PiB7CiAgICAgICAgLy8gICBjb25zb2xlLmxvZyhlbGVtZW50KTsKICAgICAgICAvLyB9KTsKICAgICAgICAvLyDlvqrnjq/lr7nosaHotYvlgLwKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLmluZm9MaXN0MSA9IFtdOwogICAgICAgIHRoaXMuaW5mb0xpc3QyID0gW107CiAgICAgIH0KICAgICAgaWYgKHJvdy5vcGVyYXRpb25UeXBlID09ICIyIiAmJiByb3cuc3BhcmUxID09ICIxIikgewogICAgICAgIHRoaXMuZGVmS2V5ID0gInByb2Nlc3NfcHJvamVjdF9hdXRoIjsKICAgICAgICB0aGlzLnRpdGxlID0gIuafpeeci+mhueebruaKpeWkh+i9rOaOiOadgyI7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy5kZWZLZXkgPSAicHJvY2Vzc19wcm9qZWN0X3JlcG9ydCI7CiAgICAgICAgdGhpcy50aXRsZSA9ICLmn6XnnIvpobnnm67miqXlpIcv5o6I5p2DIjsKICAgICAgfQogICAgICB2YXIgcGFyYW1zID0gewogICAgICAgIGJpektleTogcm93LnByb2plY3RJZCwKICAgICAgICBkZWZLZXk6IHRoaXMuZGVmS2V5CiAgICAgIH07CiAgICAgIGdldEluc0lkQnlCaXpLZXkocGFyYW1zKS50aGVuKGZ1bmN0aW9uIChyZXNwKSB7CiAgICAgICAgX3RoaXM0LmJpektleSA9IHJvdy5wcm9qZWN0SWQ7CiAgICAgICAgaWYgKHJlc3AuZGF0YSAmJiByZXNwLmRhdGEuaW5zdGFuY2VJZCkgewogICAgICAgICAgX3RoaXM0LnByb2NJbnNJZCA9IHJlc3AuZGF0YS5pbnN0YW5jZUlkOwogICAgICAgICAgX3RoaXM0LnRhc2tJZCA9IHJlc3AuZGF0YS50YXNrSWQ7CiAgICAgICAgICAvL2NvbnNvbGUubG9nKCI9PWhhbmRsZVZpZXc9Pj4iKQogICAgICAgICAgLy9jb25zb2xlLmxvZyhyZXNwLmRhdGEpCiAgICAgICAgICBpZiAocmVzcC5kYXRhLmluc3RhbmNlSWQgJiYgIXJlc3AuZGF0YS5lbmRUaW1lICYmIHJlc3AuZGF0YS5hc3NpZ25lZSA9PSBfdGhpczQuJHN0b3JlLnN0YXRlLnVzZXIudXNlcklkKSB7CiAgICAgICAgICAgIGlmIChfdGhpczQuJHN0b3JlLnN0YXRlLnVzZXIucm9sZXMgJiYgX3RoaXM0LiRzdG9yZS5zdGF0ZS51c2VyLnJvbGVzLmluY2x1ZGVzKCJyZXBvcnRfYWRtaW4iKSAmJiByb3cub3BlcmF0aW9uVHlwZSA9PSAiMiIpIHsKICAgICAgICAgICAgICBfdGhpczQuaXNBdXRoSW1hZ2VzID0gdHJ1ZTsKICAgICAgICAgICAgfQogICAgICAgICAgICBfdGhpczQuZmluaXNoZWQgPSBmYWxzZTsKICAgICAgICAgIH0gZWxzZSBpZiAoX3RoaXM0LiRzdG9yZS5zdGF0ZS51c2VyLnJvbGVzICYmIF90aGlzNC4kc3RvcmUuc3RhdGUudXNlci5yb2xlcy5pbmNsdWRlcygicmVwb3J0X2FkbWluIikgJiYgcm93Lm5vZGUgPT0gIuWuoeaguOWRmCIpIHsKICAgICAgICAgICAgaWYgKHJvdy5vcGVyYXRpb25UeXBlID09ICIyIikgewogICAgICAgICAgICAgIF90aGlzNC5pc0F1dGhJbWFnZXMgPSB0cnVlOwogICAgICAgICAgICB9CiAgICAgICAgICAgIC8v5a6h5qC45ZGY6KeS6Imy5LiN5o6n5Yi26LCB5pON5L2cCiAgICAgICAgICAgIF90aGlzNC5maW5pc2hlZCA9IGZhbHNlOwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgX3RoaXM0LmZpbmlzaGVkID0gdHJ1ZTsKICAgICAgICAgIH0KICAgICAgICB9IGVsc2UgewogICAgICAgICAgX3RoaXM0LmZpbmlzaGVkID0gdHJ1ZTsKICAgICAgICAgIF90aGlzNC5wcm9jSW5zSWQgPSB1bmRlZmluZWQ7CiAgICAgICAgICBfdGhpczQudGFza0lkID0gdW5kZWZpbmVkOwogICAgICAgIH0KCiAgICAgICAgLy8gY29uc29sZS5sb2coIj09PT0+Pj7pqbPlm54iKQogICAgICAgIC8vIC8v6amz5Zue55So5oi3CiAgICAgICAgLy8gaWYocm93LmF1ZGl0U3RhdHVzID09ICczJyAmJiByb3cudXNlcklkID09IHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIudXNlcklkKXsKICAgICAgICAvLyAgIHRoaXMuZmluaXNoZWQgPSBmYWxzZTsKICAgICAgICAvLyB9CiAgICAgICAgLy8gY29uc29sZS5sb2coIj09PT0+Pj7pqbPlm57vvJoiICsgdGhpcy5maW5pc2hlZCkKCiAgICAgICAgX3RoaXM0LnZpZXdPcGVuID0gdHJ1ZTsKICAgICAgfSk7CiAgICAgIGdldExpa2VMaXN0KHsKICAgICAgICBwcm9qZWN0TmFtZTogcm93LnByb2plY3ROYW1lLAogICAgICAgIHByb2plY3RJZDogcm93LnByb2plY3RJZAogICAgICB9KS50aGVuKGZ1bmN0aW9uIChyZXNwKSB7CiAgICAgICAgLy9jb25zb2xlLmxvZyhyZXNwKQogICAgICAgIGlmIChyZXNwLmRhdGEgJiYgcmVzcC5kYXRhLmxlbmd0aCA+IDApIHsKICAgICAgICAgIF90aGlzNC5saWtlTGlzdCA9IHJlc3AuZGF0YTsKICAgICAgICAgIHRoYXQubGlrZWNvdW50ID0gcmVzcC5kYXRhLmxlbmd0aDsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgX3RoaXM0Lmxpa2VMaXN0ID0gdW5kZWZpbmVkOwogICAgICAgICAgdGhhdC5saWtlY291bnQgPSB1bmRlZmluZWQ7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvKiog5o6I5p2D5oyJ6ZKu5pON5L2cICovaGFuZGxlQXV0aDogZnVuY3Rpb24gaGFuZGxlQXV0aChyb3cpIHsKICAgICAgdmFyIF90aGlzNSA9IHRoaXM7CiAgICAgIHZhciBsb2FkaW5nID0gdGhpcy4kbG9hZGluZyh7CiAgICAgICAgbG9jazogdHJ1ZSwKICAgICAgICB0ZXh0OiAi5o6I5p2D5LitLi4uIiwKICAgICAgICBzcGlubmVyOiAiZWwtaWNvbi1sb2FkaW5nIiwKICAgICAgICBiYWNrZ3JvdW5kOiAicmdiYSgwLCAwLCAwLCAwLjcpIgogICAgICB9KTsKICAgICAgYXV0aFJlcG9ydChyb3cpLnRoZW4oZnVuY3Rpb24gKHJlc3ApIHsKICAgICAgICBsb2FkaW5nLmNsb3NlKCk7CiAgICAgICAgX3RoaXM1Lm1zZ1N1Y2Nlc3MocmVzcC5tc2cpOwogICAgICAgIF90aGlzNS4kcm91dGVyLmdvKDApOwogICAgICB9KS5jYXRjaChmdW5jdGlvbiAoZSkgewogICAgICAgIGxvYWRpbmcuY2xvc2UoKTsKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOS/ruaUueaMiemSruaTjeS9nCAqL2hhbmRsZVVwZGF0ZTogZnVuY3Rpb24gaGFuZGxlVXBkYXRlKCkgewogICAgICB2YXIgX3RoaXM2ID0gdGhpczsKICAgICAgdmFyIHRoYXQgPSB0aGlzOwogICAgICAvLyB0aGF0LmF1ZGl0U3RhdHVzRWRpdCA9IHRydWU7CiAgICAgIC8vIGlmKCF0aGF0LmlzQWRtaW4gJiYgdGhhdC5jaG9vc2VBdWRpdFN0YXR1cyA9PSAzKXsKICAgICAgLy8gICB0aGF0LmF1ZGl0U3RhdHVzRWRpdCA9IGZhbHNlOwogICAgICAvLyB9ZWxzZXt9CiAgICAgIC8v55Sz6K+36ICFCiAgICAgIHZhciBpc0FwcGx5ID0gdGhpcy4kc3RvcmUuc3RhdGUudXNlci5yb2xlcyAmJiAodGhpcy4kc3RvcmUuc3RhdGUudXNlci5yb2xlcy5pbmNsdWRlcygiY29tbW9uIikgfHwgdGhpcy4kc3RvcmUuc3RhdGUudXNlci5yb2xlcy5pbmNsdWRlcygicHJvdmluY2VfYWRtaW4iKSk7CiAgICAgIGlmIChpc0FwcGx5ICYmIHRoaXMuY2hvb3NlVXNlcklkICE9IHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIudXNlcklkKSB7CiAgICAgICAgdGhpcy5tc2dFcnJvcigi5Y+q6IO95L+u5pS55pys5Lq65o+Q5Lqk55qE6aG555uuIik7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIGlmICh0aGlzLmNob29zZU9wdFR5cGUgPT0gMikgewogICAgICAgIGlmIChpc0FwcGx5ICYmIHRoaXMuY2hvb3NlU3BhcmUyICE9IDEpIHsKICAgICAgICAgIHRoaXMubXNnRXJyb3IoIuaOiOadg+iiq+mAgOWbnuaJjeiDveS/ruaUuSIpOwogICAgICAgICAgcmV0dXJuOwogICAgICAgIH0KICAgICAgfQogICAgICBpZiAodGhpcy5jaG9vc2VBdWRpdFN0YXR1cyA9PSAzICYmIGlzQXBwbHkpIHsKICAgICAgICBpZiAodGhpcy5jaG9vc2VFZGl0U3RhdHVzID09ICIwIikgewogICAgICAgICAgdGhpcy5tc2dFcnJvcigi5a6h5om56KKr6amz5Zue5peg5rOV5L+u5pS5Iik7CiAgICAgICAgICByZXR1cm47CiAgICAgICAgfQogICAgICB9CiAgICAgIC8vIGlmKHRoaXMuY2hvb3NlT3B0VHlwZSA9PSAxKXsKICAgICAgLy8gICBpZihpc0FwcGx5ICYmIHRoaXMuY2hvb3NlVXNlcklkICE9IHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIudXNlcklkICl7CiAgICAgIC8vICAgICB0aGlzLm1zZ0Vycm9yKCLlj6rog73kv67mlLnmnKzkurrmj5DkuqTnmoTmiqXlpIfpobnnm64iKTsKICAgICAgLy8gICAgIHJldHVybjsKICAgICAgLy8gICB9CiAgICAgIC8vIH0KICAgICAgdGhpcy5yZXNldCgpOwogICAgICB2YXIgcHJvamVjdElkID0gdGhpcy5pZHM7CiAgICAgIGdldFJlcG9ydChwcm9qZWN0SWQpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgX3RoaXM2LmZvcm0gPSByZXNwb25zZS5kYXRhOwogICAgICAgIGlmIChfdGhpczYuZm9ybS5tb2RlbCkgX3RoaXM2LmZvcm0ubW9kZWwgPSBfdGhpczYuZm9ybS5tb2RlbC5zcGxpdCgiLCIpO2Vsc2UgX3RoaXM2LmZvcm0ubW9kZWwgPSBbXTsKICAgICAgICBpZiAoX3RoaXM2LmZvcm0ucmVxdWlyZUluZm8pIF90aGlzNi5mb3JtLnJlcXVpcmVJbmZvID0gX3RoaXM2LmZvcm0ucmVxdWlyZUluZm8uc3BsaXQoIiwiKTtlbHNlIF90aGlzNi5mb3JtLnJlcXVpcmVJbmZvID0gW107CiAgICAgICAgaWYgKF90aGlzNi5mb3JtLmluZm9UeXBlKSBfdGhpczYuZm9ybS5pbmZvVHlwZSA9IF90aGlzNi5mb3JtLmluZm9UeXBlLnNwbGl0KCIsIik7ZWxzZSBfdGhpczYuZm9ybS5pbmZvVHlwZSA9IFtdOwogICAgICAgIGlmIChfdGhpczYuZm9ybS5zcGVjKSBfdGhpczYuZm9ybS5zcGVjID0gX3RoaXM2LmZvcm0uc3BlYy5zcGxpdCgiLCIpO2Vsc2UgX3RoaXM2LmZvcm0uc3BlYyA9IFtdOwogICAgICAgIGlmIChfdGhpczYuZm9ybS5hdXRoQ29tcGFueSkgewogICAgICAgICAgdGhhdC5hdXRoQ29tcGFueXMgPSBbXTsKICAgICAgICAgIHZhciBhcnJheSA9IF90aGlzNi5mb3JtLmF1dGhDb21wYW55LnNwbGl0KCIsIik7CiAgICAgICAgICBhcnJheS5mb3JFYWNoKGZ1bmN0aW9uIChlKSB7CiAgICAgICAgICAgIHRoYXQuYXV0aENvbXBhbnlzLnB1c2goewogICAgICAgICAgICAgIHZhbHVlOiBlLAogICAgICAgICAgICAgIGtleTogRGF0ZS5ub3coKQogICAgICAgICAgICB9KTsKICAgICAgICAgIH0pOwogICAgICAgICAgdGhhdC5mb3JtLmF1dGhDb21wYW55ID0gdGhhdC5hdXRoQ29tcGFueXNbMF0udmFsdWU7CiAgICAgICAgICB0aGF0LmF1dGhDb21wYW55cy5zcGxpY2UoMCwgMSk7CiAgICAgICAgICAvL2NvbnNvbGUubG9nKHRoYXQuYXV0aENvbXBhbnlzKQogICAgICAgIH0gZWxzZSBfdGhpczYuYXV0aENvbXBhbnlzID0gW107CiAgICAgICAgX3RoaXM2Lm9sZE9wZXJhdGlvblR5cGUgPSByZXNwb25zZS5kYXRhLm9wZXJhdGlvblR5cGU7CiAgICAgICAgX3RoaXM2Lm9wZW4gPSB0cnVlOwogICAgICAgIF90aGlzNi50aXRsZSA9ICLkv67mlLnpobnnm67miqXlpIciOwogICAgICAgIHZhciBwcm92aW5jZXMgPSByZXNwb25zZS5kYXRhLnByb3ZpbmNlOwogICAgICAgIGlmIChwcm92aW5jZXMubGVuZ3RoID4gMCkgewogICAgICAgICAgdmFyIGFkZHJlc3MgPSBwcm92aW5jZXMuc3BsaXQoIi8iKTsKICAgICAgICAgIHZhciBjaXR5cyA9IFtdOwogICAgICAgICAgLy8g55yB5Lu9CiAgICAgICAgICBpZiAoYWRkcmVzcy5sZW5ndGggPiAwKSBjaXR5cy5wdXNoKFRleHRUb0NvZGVbYWRkcmVzc1swXV0uY29kZSk7CiAgICAgICAgICAvLyDln47luIIKICAgICAgICAgIGlmIChhZGRyZXNzLmxlbmd0aCA+IDEpIGNpdHlzLnB1c2goVGV4dFRvQ29kZVthZGRyZXNzWzBdXVthZGRyZXNzWzFdXS5jb2RlKTsKICAgICAgICAgIC8vIOWcsOWMugogICAgICAgICAgaWYgKGFkZHJlc3MubGVuZ3RoID4gMikgY2l0eXMucHVzaChUZXh0VG9Db2RlW2FkZHJlc3NbMF1dW2FkZHJlc3NbMV1dW2FkZHJlc3NbMl1dLmNvZGUpOwogICAgICAgICAgX3RoaXM2LnNlbGVjdGVkT3B0aW9ucyA9IGNpdHlzOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLyoqIOaPkOS6pOaMiemSriAqL3N1Ym1pdEZvcm06IGZ1bmN0aW9uIHN1Ym1pdEZvcm0oKSB7CiAgICAgIHZhciBfdGhpczcgPSB0aGlzOwogICAgICB0aGlzLiRyZWZzWyJmb3JtIl0udmFsaWRhdGUoZnVuY3Rpb24gKHZhbGlkKSB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICBpZiAoX3RoaXM3LmZvcm0uaW5mb1R5cGUuaW5kZXhPZigiMSIpID49IDAgJiYgX3RoaXM3LmZvcm0uc2NhbkZpbGUpIHsKICAgICAgICAgICAgdmFyIGVtYWlsUmVnID0gL15bYS16QS1aMC05Xy1dK0BbYS16QS1aMC05Xy1dKyhcLlthLXpBLVowLTlfLV0rKSskLzsKICAgICAgICAgICAgaWYgKCFlbWFpbFJlZy50ZXN0KF90aGlzNy5mb3JtLnNjYW5GaWxlKSkgewogICAgICAgICAgICAgIF90aGlzNy4kbWVzc2FnZS5lcnJvcigi6LWE5paZ5o6l5pS25pa55byP6YKu566x5qC85byP6ZSZ6K+vIik7CiAgICAgICAgICAgICAgcmV0dXJuOwogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgICBpZiAoX3RoaXM3LmZvcm0ub3BlcmF0aW9uVHlwZSA9PSAxKSB7CiAgICAgICAgICAgIF90aGlzNy5mb3JtLmF1dGhGaWxlID0gIiI7CiAgICAgICAgICAgIF90aGlzNy5mb3JtLmFmdGVyU2FsZUZpbGUgPSAiIjsKICAgICAgICAgIH0KICAgICAgICAgIHZhciBmb3JtU3RyID0gSlNPTi5zdHJpbmdpZnkoX3RoaXM3LmZvcm0pOwogICAgICAgICAgdmFyIGZvcm1EYXRhID0gSlNPTi5wYXJzZShmb3JtU3RyKTsKICAgICAgICAgIGlmIChmb3JtRGF0YS5tb2RlbCAmJiBmb3JtRGF0YS5tb2RlbC5sZW5ndGggPiAwKSBmb3JtRGF0YS5tb2RlbCA9IGZvcm1EYXRhLm1vZGVsLmpvaW4oIiwiKTtlbHNlIGZvcm1EYXRhLm1vZGVsID0gdW5kZWZpbmVkOwogICAgICAgICAgaWYgKGZvcm1EYXRhLnJlcXVpcmVJbmZvICYmIGZvcm1EYXRhLnJlcXVpcmVJbmZvLmxlbmd0aCA+IDApIGZvcm1EYXRhLnJlcXVpcmVJbmZvID0gZm9ybURhdGEucmVxdWlyZUluZm8uam9pbigiLCIpO2Vsc2UgZm9ybURhdGEucmVxdWlyZUluZm8gPSB1bmRlZmluZWQ7CiAgICAgICAgICBpZiAoZm9ybURhdGEuaW5mb1R5cGUgJiYgZm9ybURhdGEuaW5mb1R5cGUubGVuZ3RoID4gMCkgZm9ybURhdGEuaW5mb1R5cGUgPSBmb3JtRGF0YS5pbmZvVHlwZS5qb2luKCIsIik7ZWxzZSBmb3JtRGF0YS5pbmZvVHlwZSA9IHVuZGVmaW5lZDsKICAgICAgICAgIGlmIChmb3JtRGF0YS5zcGVjICYmIGZvcm1EYXRhLnNwZWMubGVuZ3RoID4gMCkgZm9ybURhdGEuc3BlYyA9IGZvcm1EYXRhLnNwZWMuam9pbigiLCIpO2Vsc2UgZm9ybURhdGEuc3BlYyA9IHVuZGVmaW5lZDsKCiAgICAgICAgICAvL+aOiOadg+WFrOWPuAogICAgICAgICAgaWYgKF90aGlzNy5hdXRoQ29tcGFueXMubGVuZ3RoID4gMCkgewogICAgICAgICAgICB2YXIgYXJyYXkgPSBuZXcgQXJyYXkoKTsKICAgICAgICAgICAgX3RoaXM3LmF1dGhDb21wYW55cy5mb3JFYWNoKGZ1bmN0aW9uIChlKSB7CiAgICAgICAgICAgICAgYXJyYXkucHVzaChlLnZhbHVlKTsKICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIGZvcm1EYXRhLmF1dGhDb21wYW55ICs9ICIsIiArIGFycmF5LmpvaW4oIiwiKTsKICAgICAgICAgIH0KICAgICAgICAgIGlmIChmb3JtRGF0YS5wcm9qZWN0SWQgIT0gbnVsbCkgewogICAgICAgICAgICB1cGRhdGVSZXBvcnQoZm9ybURhdGEpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgICAgICAgX3RoaXM3Lm1zZ1N1Y2Nlc3MoIuS/ruaUueaIkOWKnyIpOwogICAgICAgICAgICAgIGlmIChfdGhpczcub2xkT3BlcmF0aW9uVHlwZSA9PSAxICYmIGZvcm1EYXRhLm9wZXJhdGlvblR5cGUgPT0gMikgewogICAgICAgICAgICAgICAgY29uc29sZS5sb2coIj09PT09Pj4+5oql5aSH5pS55o6I5p2DIik7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIF90aGlzNy5vcGVuID0gZmFsc2U7CiAgICAgICAgICAgICAgX3RoaXM3LmdldExpc3QoKTsKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICBhZGRSZXBvcnQoZm9ybURhdGEpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgICAgICAgX3RoaXM3Lm1zZ1N1Y2Nlc3MoIuaWsOWinuaIkOWKnyIpOwogICAgICAgICAgICAgIF90aGlzNy5vcGVuID0gZmFsc2U7CiAgICAgICAgICAgICAgX3RoaXM3LmdldExpc3QoKTsKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvKiog5Yig6Zmk5oyJ6ZKu5pON5L2cICovaGFuZGxlRGVsZXRlOiBmdW5jdGlvbiBoYW5kbGVEZWxldGUocm93KSB7CiAgICAgIHZhciBfdGhpczggPSB0aGlzOwogICAgICB2YXIgcHJvamVjdElkcyA9IHJvdy5wcm9qZWN0SWQgfHwgdGhpcy5pZHM7CiAgICAgIHRoaXMuJGNvbmZpcm0oJ+aYr+WQpuehruiupOWIoOmZpOmhueebruaKpeWkh+e8luWPt+S4uiInICsgcHJvamVjdElkcyArICci55qE5pWw5o2u6aG5PycsICLorablkYoiLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLAogICAgICAgIHR5cGU6ICJ3YXJuaW5nIgogICAgICB9KS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICByZXR1cm4gZGVsUmVwb3J0KHByb2plY3RJZHMpOwogICAgICB9KS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICBfdGhpczguZ2V0TGlzdCgpOwogICAgICAgIF90aGlzOC5tc2dTdWNjZXNzKCLliKDpmaTmiJDlip8iKTsKICAgICAgfSk7CiAgICB9LAogICAgY2xpY2tFeHBvcnQ6IGZ1bmN0aW9uIGNsaWNrRXhwb3J0KCkgewogICAgICB0aGlzLnNob3dFeHBvcnQgPSB0cnVlOwogICAgfSwKICAgIGNsaWNrUHJpbnQ6IGZ1bmN0aW9uIGNsaWNrUHJpbnQoKSB7CiAgICAgIHRoaXMuc2hvd1ByaW50ID0gdHJ1ZTsKICAgIH0sCiAgICAvKiog5a+85Ye65oyJ6ZKu5pON5L2cICovaGFuZGxlRXhwb3J0OiBmdW5jdGlvbiBoYW5kbGVFeHBvcnQodHlwZSkgewogICAgICB2YXIgX3RoaXM5ID0gdGhpczsKICAgICAgdmFyIGxvYWRpbmd3aW47CiAgICAgIHZhciB0aGF0ID0gdGhpczsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5zcGFyZTEgPSB0eXBlOwogICAgICB2YXIgY29sID0gW107CiAgICAgIHRoaXMuY29sdW1ucy5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgaWYgKGl0ZW0udmlzaWJsZSkgewogICAgICAgICAgY29sLnB1c2goaXRlbS5sYWJlbCk7CiAgICAgICAgfQogICAgICB9KTsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5zcGFyZTIgPSBjb2wuam9pbigiLCIpOwogICAgICB2YXIgcXVlcnlQYXJhbXMgPSB0aGlzLnF1ZXJ5UGFyYW1zOwogICAgICB0aGlzLiRjb25maXJtKCLmmK/lkKbnoa7orqTlr7zlh7rpobnnm67miqXlpIfmkJzntKLnu5PmnpwiICsgKHR5cGUgPT0gMCA/ICLmnKzpobUiIDogIuWFqOmDqCIpICsgIuaVsOaNrumhuT8iLCAi6K2m5ZGKIiwgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAi56Gu5a6aIiwKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAi5Y+W5raIIiwKICAgICAgICB0eXBlOiAid2FybmluZyIKICAgICAgfSkudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgbG9hZGluZ3dpbiA9IHRoYXQuJGxvYWRpbmcoewogICAgICAgICAgbG9jazogdHJ1ZSwKICAgICAgICAgIC8vbG9ja+eahOS/ruaUueespi0t6buY6K6k5pivZmFsc2UKICAgICAgICAgIHRleHQ6ICLlr7zlh7rkuK0uLi4iLAogICAgICAgICAgLy/mmL7npLrlnKjliqDovb3lm77moIfkuIvmlrnnmoTliqDovb3mlofmoYgKICAgICAgICAgIHNwaW5uZXI6ICJlbC1pY29uLWxvYWRpbmciLAogICAgICAgICAgLy/oh6rlrprkuYnliqDovb3lm77moIfnsbvlkI0KICAgICAgICAgIGJhY2tncm91bmQ6ICJyZ2JhKDAsIDAsIDAsIDAuNykiLAogICAgICAgICAgLy/pga7nvanlsYLpopzoibIKICAgICAgICAgIHRhcmdldDogZG9jdW1lbnQucXVlcnlTZWxlY3RvcigiLmFwcC13cmFwcGVyIikgLy9sb2FkaW7opobnm5bnmoRkb23lhYPntKDoioLngrkKICAgICAgICB9KTsKICAgICAgICBkb2N1bWVudC5kb2N1bWVudEVsZW1lbnQuc3R5bGUub3ZlcmZsb3dZID0gImhpZGRlbiI7IC8v56aB5q2i5bqV5bGCZGl25rua5YqoCiAgICAgICAgcmV0dXJuIGV4cG9ydFJlcG9ydChxdWVyeVBhcmFtcyk7CiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgX3RoaXM5LmRvd25sb2FkKHJlc3BvbnNlLm1zZyk7CiAgICAgICAgX3RoaXM5LnNob3dFeHBvcnQgPSBmYWxzZTsKICAgICAgICBsb2FkaW5nd2luLmNsb3NlKCk7CiAgICAgICAgZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50LnN0eWxlLm92ZXJmbG93WSA9ICJhdXRvIjsgLy/lhYHorrjlupXlsYJkaXbmu5rliqgKICAgICAgfSkuY2F0Y2goZnVuY3Rpb24gKCkgewogICAgICAgIF90aGlzOS5zaG93RXhwb3J0ID0gZmFsc2U7CiAgICAgICAgbG9hZGluZ3dpbi5jbG9zZSgpOwogICAgICAgIGRvY3VtZW50LmRvY3VtZW50RWxlbWVudC5zdHlsZS5vdmVyZmxvd1kgPSAiYXV0byI7IC8v5YWB6K645bqV5bGCZGl25rua5YqoCiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDlr7zlhaXmjInpkq7mk43kvZwgKi9oYW5kbGVJbXBvcnQ6IGZ1bmN0aW9uIGhhbmRsZUltcG9ydCgpIHsKICAgICAgdGhpcy51cGxvYWQudGl0bGUgPSAi6aG555uu5a+85YWlIjsKICAgICAgdGhpcy51cGxvYWQub3BlbiA9IHRydWU7CiAgICB9LAogICAgLyoqIOS4i+i9veaooeadv+aTjeS9nCAqL2ltcG9ydFRlbXBsYXRlOiBmdW5jdGlvbiBpbXBvcnRUZW1wbGF0ZSgpIHsKICAgICAgdmFyIF90aGlzMTAgPSB0aGlzOwogICAgICBfaW1wb3J0VGVtcGxhdGUoKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgIF90aGlzMTAuZG93bmxvYWQocmVzcG9uc2UubXNnKTsKICAgICAgfSk7CiAgICB9LAogICAgZG93bmxvYWRTUVM6IGZ1bmN0aW9uIGRvd25sb2FkU1FTKCkgewogICAgICB0aGlzLmRvd25sb2FkKCLmtbfkvbPpm4blm6It5o6I5p2D5LmmLmRvY3giLCBmYWxzZSk7CiAgICB9LAogICAgZG93bmxvYWRDUkg6IGZ1bmN0aW9uIGRvd25sb2FkQ1JIKCkgewogICAgICB0aGlzLmRvd25sb2FkKCLmtbfkvbPpm4blm6It5ZSu5ZCO5pyN5Yqh5om/6K+65Ye9LmRvYyIsIGZhbHNlKTsKICAgIH0sCiAgICAvLyDmlofku7bkuIrkvKDkuK3lpITnkIYKICAgIGhhbmRsZUZpbGVVcGxvYWRQcm9ncmVzczogZnVuY3Rpb24gaGFuZGxlRmlsZVVwbG9hZFByb2dyZXNzKGV2ZW50LCBmaWxlLCBmaWxlTGlzdCkgewogICAgICB0aGlzLnVwbG9hZC5pc1VwbG9hZGluZyA9IHRydWU7CiAgICB9LAogICAgLy8g5paH5Lu25LiK5Lyg5oiQ5Yqf5aSE55CGCiAgICBoYW5kbGVGaWxlU3VjY2VzczogZnVuY3Rpb24gaGFuZGxlRmlsZVN1Y2Nlc3MocmVzcG9uc2UsIGZpbGUsIGZpbGVMaXN0KSB7CiAgICAgIHRoaXMudXBsb2FkLm9wZW4gPSBmYWxzZTsKICAgICAgdGhpcy51cGxvYWQuaXNVcGxvYWRpbmcgPSBmYWxzZTsKICAgICAgdGhpcy4kcmVmcy51cGxvYWQuY2xlYXJGaWxlcygpOwogICAgICB0aGlzLiRhbGVydChyZXNwb25zZS5tc2csICLlr7zlhaXnu5PmnpwiLCB7CiAgICAgICAgZGFuZ2Vyb3VzbHlVc2VIVE1MU3RyaW5nOiB0cnVlCiAgICAgIH0pOwogICAgICB0aGlzLmdldExpc3QoKTsKICAgIH0sCiAgICAvLyDmj5DkuqTkuIrkvKDmlofku7YKICAgIHN1Ym1pdEZpbGVGb3JtOiBmdW5jdGlvbiBzdWJtaXRGaWxlRm9ybSgpIHsKICAgICAgdGhpcy4kcmVmcy51cGxvYWQuc3VibWl0KCk7CiAgICB9LAogICAgaGFuZGxlQ2hhbmdlOiBmdW5jdGlvbiBoYW5kbGVDaGFuZ2UodmFsdWUpIHsKICAgICAgaWYgKCF2YWx1ZSB8fCB2YWx1ZS5sZW5ndGggPT0gMCkgewogICAgICAgIHRoaXMuc2VsZWN0ZWRPcHRpb25zID0gbnVsbDsKICAgICAgICB0aGlzLmZvcm0ucHJvdmluY2UgPSB1bmRlZmluZWQ7CiAgICAgICAgdGhpcy5mb3JtLmRpc3RyaWN0ID0gdW5kZWZpbmVkOwogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICB0aGlzLnNlbGVjdGVkT3B0aW9ucyA9IHZhbHVlOwogICAgICB2YXIgdHh0ID0gIiI7CiAgICAgIHZhbHVlLmZvckVhY2goZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICB0eHQgKz0gQ29kZVRvVGV4dFtpdGVtXSArICIvIjsKICAgICAgfSk7CiAgICAgIGlmICh0eHQubGVuZ3RoID4gMSkgewogICAgICAgIHR4dCA9IHR4dC5zdWJzdHJpbmcoMCwgdHh0Lmxlbmd0aCAtIDEpOwogICAgICAgIHRoaXMuZm9ybS5kaXN0cmljdCA9IHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIucHJvdmluY2U7CiAgICAgICAgdGhpcy5mb3JtLnByb3ZpbmNlID0gdHh0OwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuZm9ybS5wcm92aW5jZSA9IHVuZGVmaW5lZDsKICAgICAgICB0aGlzLmZvcm0uZGlzdHJpY3QgPSB1bmRlZmluZWQ7CiAgICAgIH0KICAgIH0sCiAgICBoYW5kbGVRdWVyeUNpdHlDaGFuZ2U6IGZ1bmN0aW9uIGhhbmRsZVF1ZXJ5Q2l0eUNoYW5nZSh2YWx1ZSkgewogICAgICB0aGlzLnF1ZXJ5QXJlYSA9IHZhbHVlOwogICAgICB2YXIgdHh0ID0gIiI7CiAgICAgIHZhbHVlLmZvckVhY2goZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICB0eHQgKz0gQ29kZVRvVGV4dFtpdGVtXSArICIvIjsKICAgICAgfSk7CiAgICAgIGlmICh0eHQubGVuZ3RoID4gMSkgewogICAgICAgIHR4dCA9IHR4dC5zdWJzdHJpbmcoMCwgdHh0Lmxlbmd0aCAtIDEpOwogICAgICAgIHRoaXMucXVlcnlQYXJhbXMucHJvdmluY2UgPSB0eHQ7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wcm92aW5jZSA9IHVuZGVmaW5lZDsKICAgICAgfQogICAgfSwKICAgIGhhbmRsZVByaW50OiBmdW5jdGlvbiBoYW5kbGVQcmludCh0eXBlKSB7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMuc3BhcmUxID0gdHlwZTsKICAgICAgdmFyIHByb3BlcnRpZXMgPSBbXTsKICAgICAgLy9wcm9wZXJ0aWVzLnB1c2goe2ZpZWxkOiAnaW5kZXgnLCBkaXNwbGF5TmFtZTogJ+W6j+WPtyd9KTsKICAgICAgcHJvcGVydGllcy5wdXNoKHsKICAgICAgICBmaWVsZDogInByb2plY3RJZCIsCiAgICAgICAgZGlzcGxheU5hbWU6ICLpobnnm65JRCIKICAgICAgfSk7CiAgICAgIHRoaXMuY29sdW1ucy5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgaWYgKGl0ZW0udmlzaWJsZSkgewogICAgICAgICAgcHJvcGVydGllcy5wdXNoKHsKICAgICAgICAgICAgZmllbGQ6IGl0ZW0ua2V5LAogICAgICAgICAgICBkaXNwbGF5TmFtZTogaXRlbS5sYWJlbAogICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICB9KTsKICAgICAgcHJpbnRSZXBvcnQodGhpcy5xdWVyeVBhcmFtcykudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICBwcmludEpTKHsKICAgICAgICAgIHByaW50YWJsZTogcmVzcG9uc2UuZGF0YSwKICAgICAgICAgIHR5cGU6ICJqc29uIiwKICAgICAgICAgIHByb3BlcnRpZXM6IHByb3BlcnRpZXMsCiAgICAgICAgICBoZWFkZXI6ICc8ZGl2IHN0eWxlPSJ0ZXh0LWFsaWduOiBjZW50ZXIiPjxoMz7pobnnm67miqXlpIfliJfooag8L2gzPjwvZGl2PicsCiAgICAgICAgICB0YXJnZXRTdHlsZXM6IFsiKiJdLAogICAgICAgICAgZ3JpZEhlYWRlclN0eWxlOiAibWFyZ2luLXRvcDoyMHB4O2JvcmRlcjogMXB4IHNvbGlkICMwMDA7dGV4dC1hbGlnbjpjZW50ZXIiLAogICAgICAgICAgZ3JpZFN0eWxlOiAiYm9yZGVyOiAxcHggc29saWQgIzAwMDt0ZXh0LWFsaWduOmNlbnRlcjttaW4td2lkdGg6NTBweDsiLAogICAgICAgICAgc3R5bGU6ICJAcGFnZSB7bWFyZ2luOjAgMTBtbTttYXJnaW4tdG9wOjEwbW07fSIKICAgICAgICB9KTsKICAgICAgfSk7CiAgICAgIC8vIHByaW50SlMoewogICAgICAvLyAgIHByaW50YWJsZTogInByaW50QXJlYSIsCiAgICAgIC8vICAgdHlwZTonaHRtbCcsCiAgICAgIC8vICAgaGVhZGVyOm51bGwsCiAgICAgIC8vICAgdGFyZ2V0U3R5bGVzOlsnKiddLAogICAgICAvLyAgIHN0eWxlOiJAcGFnZSB7bWFyZ2luOjAgMTBtbX0iCiAgICAgIC8vIH0pCiAgICB9LAogICAgLy8g5Yig6ZmkIHNob3dOYW1lQ29ybG9yIOWSjCBzaG93Tm9Db3Jsb3Ig5pa55rOV77yM5paw5aKe6auY5Lqu5pa55rOVCiAgICBoaWdobGlnaHRUZXh0OiBmdW5jdGlvbiBoaWdobGlnaHRUZXh0KHRleHQsIGtleXdvcmQpIHsKICAgICAgaWYgKCFrZXl3b3JkKSByZXR1cm4gdGV4dDsKICAgICAgLy8g5YWo6YOo6auY5LquCiAgICAgIHJldHVybiB0ZXh0ID8gdGV4dC5yZXBsYWNlKG5ldyBSZWdFeHAoa2V5d29yZCwgJ2cnKSwgIjxmb250IGNvbG9yPVwiI2YwMFwiPiIuY29uY2F0KGtleXdvcmQsICI8L2ZvbnQ+IikpIDogdGV4dDsKICAgIH0sCiAgICBoaWdobGlnaHRDZWxsOiBmdW5jdGlvbiBoaWdobGlnaHRDZWxsKGZpZWxkLCB0ZXh0KSB7CiAgICAgIGlmICh0aGlzLnNlYXJjaEZpZWxkID09PSAnYWxsJyAmJiB0aGlzLnNlYXJjaFZhbHVlICYmIHRoaXMuaGlnaGxpZ2h0RmllbGRzLmluY2x1ZGVzKGZpZWxkKSkgewogICAgICAgIHJldHVybiB0aGlzLmhpZ2hsaWdodFRleHQodGV4dCwgdGhpcy5zZWFyY2hWYWx1ZSk7CiAgICAgIH0KICAgICAgaWYgKHRoaXMuc2VhcmNoRmllbGQgPT09IGZpZWxkICYmIHRoaXMuc2VhcmNoVmFsdWUpIHsKICAgICAgICByZXR1cm4gdGhpcy5oaWdobGlnaHRUZXh0KHRleHQsIHRoaXMuc2VhcmNoVmFsdWUpOwogICAgICB9CiAgICAgIHJldHVybiB0ZXh0OwogICAgfSwKICAgIHJlbW92ZURvbWFpbjogZnVuY3Rpb24gcmVtb3ZlRG9tYWluKGluZGV4KSB7CiAgICAgIGlmIChpbmRleCAhPT0gLTEpIHsKICAgICAgICB0aGlzLmF1dGhDb21wYW55cy5zcGxpY2UoaW5kZXgsIDEpOwogICAgICB9CiAgICB9LAogICAgYWRkRG9tYWluOiBmdW5jdGlvbiBhZGREb21haW4oKSB7CiAgICAgIHRoaXMuYXV0aENvbXBhbnlzLnB1c2goewogICAgICAgIHZhbHVlOiAiIiwKICAgICAgICBrZXk6IERhdGUubm93KCkKICAgICAgfSk7CiAgICB9LAogICAgdXNlclNlYXJjaDogZnVuY3Rpb24gdXNlclNlYXJjaChjcmVhdGVCeSkgewogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmJlbG9uZ1VzZXIgPSBjcmVhdGVCeTsKICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOwogICAgfSwKICAgIG9wdFR5cGVTZWFyY2g6IGZ1bmN0aW9uIG9wdFR5cGVTZWFyY2godHlwZSkgewogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLm9wZXJhdGlvblR5cGUgPSB0eXBlOwogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7CiAgICB9LAogICAgb3B0VHlwZUNoYW5nZTogZnVuY3Rpb24gb3B0VHlwZUNoYW5nZShlKSB7CiAgICAgIGNvbnNvbGUubG9nKGUpOwogICAgfQogIH0KfTs="}, {"version": 3, "names": ["listReport", "getReport", "delReport", "addReport", "updateReport", "exportReport", "importTemplate", "printReport", "checkNameUnique", "getLikeList", "authReport", "getToken", "FileUpload", "flowable", "getInsIdByBizKey", "regionData", "CodeToText", "TextToCode", "print", "name", "components", "data", "_this", "infoTypeValueVali", "rule", "value", "callback", "form", "infoType", "indexOf", "scanFile", "Error", "infoTypeValueVali2", "send<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "projectName", "test", "projectId", "then", "response", "codeVali", "that", "projectNo", "authFile<PERSON><PERSON><PERSON><PERSON>", "operationType", "authFile", "openDateVali", "console", "log", "openDate", "hang<PERSON>ate<PERSON><PERSON>", "hangDate", "isMobile", "pageLayout", "loading", "ids", "single", "multiple", "showSearch", "showExport", "showPrint", "total", "reportList", "title", "open", "operationTypeOptions", "auditStatusOptions", "editStatusOptions", "biddingTypeOptions", "modelOptions", "modelOption1", "requireInfoOptions", "requireInfoOption1", "infoTypeOptions", "specOptions", "specOption1", "belongProvinceOptions", "belongProvinceOptions1", "afterSaleYearOptions", "afterSaleYearOptions1", "upload", "isUploading", "updateSupport", "headers", "Authorization", "url", "process", "env", "VUE_APP_BASE_API", "queryParams", "pageNum", "pageSize", "auditStatus", "province", "userType", "belongUser", "updateTimeArr", "spare1", "address", "biddingCompany", "authCompany", "fullField", "rules", "required", "message", "validate", "trigger", "afterSaleYear", "belongProvince", "distributor", "validator", "model", "spec", "biddingContact", "authContact", "columns", "key", "index", "label", "visible", "options", "selectedOptions", "queryArea", "viewOpen", "view", "infoList1", "infoList2", "def<PERSON><PERSON>", "procInsId", "undefined", "taskId", "finished", "bizKey", "auditStatusTree", "operationTypeTree", "userTypeTree", "defaultProps", "children", "oldOperationType", "showUType", "chooseOptType", "chooseAuditStatus", "chooseUserId", "chooseEditStatus", "chooseSpare2", "likeList", "likeCount", "authCompanys", "isAdmin", "auditStatusEdit", "isAuthImages", "searchPickerOptions", "shortcuts", "text", "onClick", "picker", "end", "Date", "start", "setTime", "getTime", "$emit", "pickerOptions", "disabledDate", "time", "now", "date", "searchField", "searchFieldOptions", "searchValue", "highlightFields", "activated", "getList", "created", "_this2", "getDicts", "opt", "push", "id", "for<PERSON>ach", "elem", "obj", "dict<PERSON><PERSON>l", "dict<PERSON><PERSON>ue", "operationTypes", "type", "$store", "state", "user", "roles", "includes", "auditStatusTrees", "userTypes", "_isMobile", "methods", "flag", "navigator", "userAgent", "match", "_this3", "rows", "handleDownload", "a", "document", "createElement", "setAttribute", "href", "click", "handleAuditNodeClick", "node", "handleOptNodeClick", "handleUserNodeClick", "filterNode", "searchFormat", "row", "column", "replace", "operationTypeFormat", "selectDictLabel", "auditStatusFormat", "editStatusFormat", "editStatus", "biddingTypeFormat", "biddingType", "modelFormat", "selectDictLabels", "specFormat", "requireInfoFormat", "requireInfo", "infoTypeFormat", "belongProvinceFormat", "afterSaleYearFormat", "cancel", "reset", "viewOk", "rejectReason", "city", "district", "budgetMoney", "biddingNet", "area", "afterSaleFile", "mailInfo", "expressInfo", "remark", "spare2", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "map", "item", "length", "userId", "handleAdd", "$router", "path", "query", "businessKey", "formEdit", "handleView", "_this4", "JSON", "parse", "stringify", "requireInfos", "half", "Math", "ceil", "splice", "params", "resp", "instanceId", "endTime", "assignee", "likecount", "handleAuth", "_this5", "$loading", "lock", "spinner", "background", "close", "msgSuccess", "msg", "go", "catch", "e", "handleUpdate", "_this6", "isApply", "msgError", "split", "array", "provinces", "citys", "code", "submitForm", "_this7", "$refs", "valid", "emailReg", "$message", "error", "formStr", "formData", "join", "Array", "handleDelete", "_this8", "projectIds", "$confirm", "confirmButtonText", "cancelButtonText", "clickExport", "clickPrint", "handleExport", "_this9", "loadingwin", "col", "target", "querySelector", "documentElement", "style", "overflowY", "download", "handleImport", "_this10", "downloadSQS", "downloadCRH", "handleFileUploadProgress", "event", "file", "fileList", "handleFileSuccess", "clearFiles", "$alert", "dangerouslyUseHTMLString", "submitFileForm", "submit", "handleChange", "txt", "substring", "handleQueryCityChange", "handlePrint", "properties", "field", "displayName", "printJS", "printable", "header", "targetStyles", "gridHeaderStyle", "gridStyle", "highlightText", "keyword", "RegExp", "concat", "highlightCell", "removeDomain", "addDomain", "userSearch", "createBy", "optTypeSearch", "optTypeChange"], "sources": ["src/views/project/report/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-row :gutter=\"20\">\n      <!--部门数据-->\n      <el-col :span=\"3\" :xs=\"24\">\n        <div class=\"head-container\">\n          <el-tree :data=\"auditStatusTree\" :props=\"defaultProps\" :expand-on-click-node=\"false\"\n            :filter-node-method=\"filterNode\" ref=\"tree\" default-expand-all node-key=\"id\" :current-node-key=\"9\"\n            @node-click=\"handleAuditNodeClick\" />\n          <el-tree :data=\"operationTypeTree\" :props=\"defaultProps\" :expand-on-click-node=\"false\"\n            :filter-node-method=\"filterNode\" ref=\"tree\" default-expand-all node-key=\"id\" :current-node-key=\"0\"\n            @node-click=\"handleOptNodeClick\" />\n          <el-tree v-if=\"showUType\" :data=\"userTypeTree\" :props=\"defaultProps\" :expand-on-click-node=\"false\"\n            :filter-node-method=\"filterNode\" ref=\"tree\" default-expand-all node-key=\"id\" :current-node-key=\"0\"\n            @node-click=\"handleUserNodeClick\" />\n        </div>\n      </el-col>\n      <!--用户数据-->\n      <el-col :span=\"21\" :xs=\"24\">\n        <el-form :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" v-show=\"showSearch\" label-width=\"100px\">\n          <!-- <el-form-item label=\"项目所在地\" label-width=\"100px\" prop=\"area\">\n            <el-cascader\n              size=\"small\"\n              clearable\n              :options=\"options\"\n              :props=\"{ checkStrictly: true, expandTrigger: 'hover' }\"\n              v-model=\"queryArea\"\n              @change=\"handleQueryCityChange\"\n            />\n          </el-form-item> -->\n          <el-form-item label=\"所属用户\" prop=\"belongUser\">\n            <el-input v-model=\"queryParams.belongUser\" placeholder=\"请输入所属用户\" clearable size=\"small\"\n              @keyup.enter.native=\"handleQuery\" />\n          </el-form-item>\n          <!-- <el-form-item label=\"项目所在地\" label-width=\"100px\" prop=\"province\">\n            <el-select\n              v-model=\"queryParams.province\"\n              placeholder=\"请选择项目所在地\"\n              clearable\n              size=\"small\"\n            >\n              <el-option\n                v-for=\"dict in options\"\n                :key=\"dict.dictValue\"\n                :label=\"dict.label\"\n                :value=\"dict.label\"\n              />\n            </el-select>\n          </el-form-item> -->\n          <el-form-item label=\"修改时间\">\n            <!-- <el-input\n              v-model=\"queryParams.updateTime\"\n              placeholder=\"请输入搜索内容\"\n              clearable\n              size=\"small\"\n              @keyup.enter.native=\"handleQuery\"\n            /> -->\n            <!-- <el-date-picker v-model=\"queryParams.updateTime\" value-format=\"yyyy-MM-dd\" align=\"right\" type=\"date\"\n              placeholder=\"选择日期\" :picker-options=\"pickerOptions\">\n            </el-date-picker> -->\n            <el-date-picker v-model=\"queryParams.updateTimeArr\" value-format=\"yyyy-MM-dd\" type=\"daterange\" align=\"right\"\n              unlink-panels range-separator=\"至\" start-placeholder=\"开始日期\" end-placeholder=\"结束日期\"\n              :picker-options=\"searchPickerOptions\">\n            </el-date-picker>\n          </el-form-item>\n          <el-form-item label=\"搜索\" prop=\"search\">\n            <el-select v-model=\"searchField\" size=\"small\" style=\"width: 120px; margin-right: 8px;\">\n              <el-option v-for=\"item in searchFieldOptions\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\" />\n            </el-select>\n            <el-input v-model=\"searchValue\"\n              :placeholder=\"`请输入${searchField === 'all' ? '内容' : searchFieldOptions.find(f => f.value === searchField).label}`\"\n              clearable size=\"small\" style=\"width: 200px\" @keyup.enter.native=\"handleQuery\" />\n          </el-form-item>\n          <el-form-item>\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n          </el-form-item>\n        </el-form>\n\n        <el-row :gutter=\"10\" class=\"mb8\">\n          <el-col :span=\"1.5\">\n            <el-button type=\"primary\" plain icon=\"el-icon-plus\" size=\"mini\" @click=\"handleAdd\"\n              v-hasPermi=\"['project:report:add']\">新增</el-button>\n          </el-col>\n          <el-col :span=\"1.5\">\n            <el-button type=\"success\" plain icon=\"el-icon-edit\" size=\"mini\" :disabled=\"single\" @click=\"handleUpdate\"\n              v-hasPermi=\"['project:report:edit']\">修改</el-button>\n          </el-col>\n          <el-col :span=\"1.5\">\n            <el-button type=\"danger\" plain icon=\"el-icon-delete\" size=\"mini\" :disabled=\"multiple\" @click=\"handleDelete\"\n              v-hasPermi=\"['project:report:remove']\">删除</el-button>\n          </el-col>\n          <el-col :span=\"1.5\">\n            <el-button type=\"info\" plain icon=\"el-icon-upload2\" size=\"mini\" @click=\"handleImport\"\n              v-hasPermi=\"['project:report:import']\">导入</el-button>\n          </el-col>\n          <el-col :span=\"1.5\">\n            <el-button type=\"warning\" plain icon=\"el-icon-download\" size=\"mini\" @click=\"clickExport\"\n              v-hasPermi=\"['project:report:export']\">导出</el-button>\n          </el-col>\n          <el-col :span=\"1.5\">\n            <el-button icon=\"el-icon-printer\" size=\"mini\" @click=\"clickPrint\" type=\"info\"\n              v-hasPermi=\"['project:report:print']\" plain>\n              打印\n            </el-button>\n          </el-col>\n          <right-toolbar :showSearch.sync=\"showSearch\" :showExport.sync=\"showExport\" :showPrint.sync=\"showPrint\"\n            @queryTable=\"getList\" @export=\"handleExport\" @print=\"handlePrint\" :columns=\"columns\"></right-toolbar>\n        </el-row>\n\n        <el-table v-loading=\"loading\" border :data=\"reportList\" @selection-change=\"handleSelectionChange\"\n          id=\"printArea\">\n          <el-table-column type=\"selection\" min-width=\"55\" align=\"center\" />\n          <el-table-column prop=\"projectId\" label=\"项目ID\" min-width=\"80\" />\n          <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" min-width=\"120\">\n            <template slot-scope=\"scope\">\n              <el-button size=\"mini\" type=\"text\" icon=\"el-icon-view\" @click=\"handleView(scope.row)\">查看</el-button>\n              <!-- <el-button v-has=\"[scope.row, 'auth']\"\n                size=\"mini\"\n                type=\"text\"\n                icon=\"el-icon-edit-outline\"\n                @click=\"handleAuth(scope.row)\"\n                >授权</el-button> -->\n              <!-- <el-dropdown v-has=\"[scope.row, 'edit']\">\n                <span class=\"el-dropdown-link\">\n                  &nbsp;&nbsp;<i class=\"el-icon-arrow-down el-icon--right\"></i>\n                </span>\n                <el-dropdown-menu slot=\"dropdown\">\n\n                  <el-dropdown-item icon=\"el-icon-edit\" @click.native=\"handleUpdate(scope.row)\" v-hasPermi=\"['project:report:edit']\">\n                    修改\n                  </el-dropdown-item>\n                  <el-dropdown-item icon=\"el-icon-delete\" @click.native=\"handleDelete(scope.row)\" v-hasPermi=\"['project:report:remove']\">\n                    删除\n                  </el-dropdown-item>\n                </el-dropdown-menu>\n              </el-dropdown> -->\n            </template>\n          </el-table-column>\n          <el-table-column label=\"所属用户\" min-width=\"150\" align=\"center\" prop=\"belongUser\" show-overflow-tooltip\n            v-if=\"columns['0'].visible\">\n            <template slot-scope=\"scope\">\n              <span @click=\"userSearch(scope.row.belongUser)\" class=\"link-type\">{{ scope.row.belongUser }}</span>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"项目编号\" min-width=\"150\" align=\"center\" prop=\"projectNo\" show-overflow-tooltip\n            v-if=\"columns['1'].visible\">\n            <template slot-scope=\"scope\">\n              <span v-html=\"highlightCell('projectNo', scope.row.projectNo)\"></span>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"项目名称\" min-width=\"300\" align=\"center\" prop=\"projectName\" :formatter=\"searchFormat\"\n            show-overflow-tooltip v-if=\"columns['2'].visible\">\n            <template slot-scope=\"scope\">\n              <span v-html=\"highlightCell('projectName', scope.row.projectName)\"></span>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"操作类型\" align=\"center\" prop=\"operationType\" :formatter=\"operationTypeFormat\"\n            v-if=\"columns['3'].visible\" min-width=\"100\">\n          </el-table-column>\n          <!-- <el-table-column\n            label=\"审核状态\"\n            align=\"center\"\n            prop=\"auditStatus\"\n            :formatter=\"auditStatusFormat\"\n            v-if=\"columns['3'].visible\"\n          />\n          <el-table-column\n            label=\"编辑状态\"\n            align=\"center\"\n            prop=\"editStatus\"\n            :formatter=\"editStatusFormat\"\n            v-if=\"columns['5'].visible\"\n          /> -->\n          <el-table-column min-width=\"200\" label=\"项目所在地\" align=\"center\" prop=\"province\" v-if=\"columns['4'].visible\" />\n          <el-table-column min-width=\"200\" label=\"详细地址\" align=\"center\" prop=\"address\" show-overflow-tooltip\n            v-if=\"columns['5'].visible\">\n            <template slot-scope=\"scope\">\n              <span v-html=\"highlightCell('address', scope.row.address)\"></span>\n            </template>\n          </el-table-column>\n          <el-table-column min-width=\"200\" label=\"被授权公司\" align=\"center\" prop=\"authCompany\" show-overflow-tooltip\n            v-if=\"columns['6'].visible\">\n            <template slot-scope=\"scope\">\n              <span v-html=\"highlightCell('authCompany', scope.row.authCompany)\"></span>\n            </template>\n          </el-table-column>\n          <el-table-column min-width=\"200\" label=\"所属经销商\" align=\"center\" prop=\"distributor\" show-overflow-tooltip\n            v-if=\"columns['7'].visible\" />\n          <el-table-column min-width=\"200\" label=\"招标单位\" align=\"center\" prop=\"biddingCompany\" show-overflow-tooltip\n            v-if=\"columns['8'].visible\">\n            <template slot-scope=\"scope\">\n              <span v-html=\"highlightCell('biddingCompany', scope.row.biddingCompany)\"></span>\n            </template>\n          </el-table-column>\n          <el-table-column min-width=\"150\" label=\"投标产品型号\" align=\"center\" prop=\"model\" :formatter=\"modelFormat\"\n            show-overflow-tooltip v-if=\"columns['9'].visible\" />\n          <el-table-column min-width=\"150\" label=\"投标产品规格\" align=\"center\" prop=\"spec\" :formatter=\"specFormat\"\n            v-if=\"columns['10'].visible\" show-overflow-tooltip />\n          <el-table-column min-width=\"150\" label=\"安装面积(m²)\" align=\"center\" prop=\"area\" show-overflow-tooltip\n            v-if=\"columns['11'].visible\" />\n          <el-table-column min-width=\"100\" label=\"所需资料\" align=\"center\" prop=\"requireInfo\" :formatter=\"requireInfoFormat\"\n            show-overflow-tooltip v-if=\"columns['12'].visible\" />\n          <el-table-column label=\"资料类型\" align=\"center\" prop=\"infoType\" :formatter=\"infoTypeFormat\"\n            v-if=\"columns['13'].visible\" />\n          <el-table-column min-width=\"150\" label=\"资料接收邮件\" align=\"center\" prop=\"scanFile\" show-overflow-tooltip\n            v-if=\"columns['14'].visible\" />\n          <el-table-column min-width=\"150\" label=\"资料接收地址\" align=\"center\" prop=\"sendAddress\" show-overflow-tooltip\n            v-if=\"columns['15'].visible\" />\n          <el-table-column min-width=\"150\" label=\"项目所属省份\" align=\"center\" prop=\"belongProvince\"\n            :formatter=\"belongProvinceFormat\" show-overflow-tooltip v-if=\"columns['16'].visible\" />\n          <el-table-column min-width=\"150\" label=\"售后年限\" align=\"center\" prop=\"afterSaleYear\"\n            :formatter=\"afterSaleYearFormat\" show-overflow-tooltip v-if=\"columns['17'].visible\" />\n          <el-table-column min-width=\"150\" label=\"开标日期\" align=\"center\" prop=\"openDate\" show-overflow-tooltip\n            v-if=\"columns['18'].visible\" />\n          <el-table-column min-width=\"150\" label=\"挂网日期\" align=\"center\" prop=\"hangDate\" show-overflow-tooltip\n            v-if=\"columns['19'].visible\" />\n          <el-table-column min-width=\"110\" label=\"提交时间\" align=\"center\" prop=\"createTime\" v-if=\"columns['20'].visible\"\n            show-overflow-tooltip />\n          <el-table-column min-width=\"110\" label=\"修改时间\" align=\"center\" prop=\"updateTime\" v-if=\"columns['21'].visible\"\n            show-overflow-tooltip />\n        </el-table>\n\n        <pagination v-show=\"total > 0\" :total=\"total\" :page.sync=\"queryParams.pageNum\"\n          :limit.sync=\"queryParams.pageSize\" :layout=\"pageLayout\" @pagination=\"getList\" />\n\n        <!-- 添加或修改项目报备对话框 -->\n        <el-dialog :title=\"title\" :visible.sync=\"open\" :close-on-click-modal=\"false\" width=\"80%\"\n          custom-class=\"edit-dialog\" append-to-body>\n          <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"编辑状态\">\n                  <el-radio-group :disabled=\"isAdmin\" v-model=\"form.editStatus\">\n                    <el-radio v-for=\"dict in editStatusOptions\" :key=\"dict.dictValue\"\n                      :label=\"dict.dictValue\">{{ dict.dictLabel }}</el-radio>\n                  </el-radio-group>\n                </el-form-item>\n              </el-col>\n              <!-- <el-col :span=\"12\">\n                <el-form-item label=\"所属用户\" prop=\"belongUser\">\n                  <el-select v-model=\"form.belongUser\" placeholder=\"请选择所属用户\">\n                    <el-option label=\"请选择字典生成\" value=\"\" />\n                  </el-select>\n                </el-form-item>\n              </el-col> -->\n            </el-row>\n            <!-- <el-row>\n              <el-col :span=\"20\">\n                <el-form-item label=\"驳回原因\" prop=\"rejectReason\">\n                  <el-input\n                    v-model=\"form.rejectReason\"\n                    type=\"textarea\"\n                    placeholder=\"请输入内容\"\n                  />\n                </el-form-item>\n              </el-col>\n            </el-row> -->\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"项目编号\" prop=\"projectNo\">\n                  <el-input v-model=\"form.projectNo\" placeholder=\"无编号则为提交时间(年月日时间)\" />\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"项目名称\" prop=\"projectName\">\n                  <el-input v-model=\"form.projectName\" placeholder=\"请输入项目名称\" />\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"项目所在地\" prop=\"province\">\n                  <el-cascader ref=\"cascader\" :options=\"options\" clearable :props=\"{ expandTrigger: 'hover' }\"\n                    v-model=\"selectedOptions\" @change=\"handleChange\">\n                  </el-cascader>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"详细地址\" prop=\"address\">\n                  <el-input v-model=\"form.address\" placeholder=\"请输入详细地址\" />\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"项目所属省份\" prop=\"belongProvince\">\n                  <el-select v-model=\"form.belongProvince\" clearable placeholder=\"请选择所属省份\">\n                    <el-option v-for=\"item in belongProvinceOptions\" :key=\"item.value\" :label=\"item.label\"\n                      :value=\"item.value\">\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"招标单位\" prop=\"biddingCompany\">\n                  <el-input v-model=\"form.biddingCompany\" placeholder=\"请输入招标单位\" />\n                </el-form-item>\n                <!-- <el-form-item label=\"招标单位联系人/联系电话\" prop=\"biddingContact\">\n                  <el-input v-model=\"form.biddingContact\" placeholder=\"请输入联系人/联系电话\" />\n                </el-form-item> -->\n              </el-col>\n            </el-row>\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"挂网日期\" prop=\"hangDate\">\n                  <el-input v-model=\"form.hangDate\" placeholder=\"请输入挂网日期，没有请写无，日期格式XXXX（年）-XX（月）-XX（日)\" />\n                  <!-- <el-date-picker clearable size=\"small\" v-model=\"form.hangDate\" type=\"date\" value-format=\"yyyy-MM-dd\"\n                    placeholder=\"选择挂网日期\">\n                  </el-date-picker> -->\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"开标日期\" prop=\"openDate\">\n                  <el-input v-model=\"form.openDate\" placeholder=\"请输入开标日期，没有请写无，日期格式XXXX（年）-XX（月）-XX（日\" />\n                  <!-- <el-date-picker clearable size=\"small\" v-model=\"form.openDate\" type=\"date\" value-format=\"yyyy-MM-dd\"\n                    placeholder=\"选择开标日期\">\n                  </el-date-picker> -->\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"所属经销商\" prop=\"distributor\">\n                  <el-input v-model=\"form.distributor\" placeholder=\"请输入经销商\" />\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <!-- <el-form-item label=\"招标单位联系人/联系电话\" prop=\"biddingContact\">\n              <el-input v-model=\"form.biddingContact\" placeholder=\"请输入联系人/联系电话\" />\n            </el-form-item> -->\n                <el-form-item label=\"售后年限\" prop=\"afterSaleYear\">\n                  <el-select v-model=\"form.afterSaleYear\" clearable placeholder=\"请选择所属省份\">\n                    <el-option v-for=\"item in afterSaleYearOptions\" :key=\"item.value\" :label=\"item.label\"\n                      :value=\"item.value\">\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <!-- <el-row>\n              <el-col :span=\"12\">\n\n              </el-col>\n            </el-row> -->\n            <!-- <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"开标日期\" prop=\"openDate\">\n                  <el-date-picker\n                    clearable\n                    size=\"small\"\n                    v-model=\"form.openDate\"\n                    type=\"date\"\n                    value-format=\"yyyy-MM-dd\"\n                    placeholder=\"选择开标日期\"\n                  >\n                  </el-date-picker>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"招标方式\" prop=\"biddingType\">\n                  <el-select\n                    v-model=\"form.biddingType\"\n                    placeholder=\"请选择招标方式\"\n                  >\n                    <el-option\n                      v-for=\"dict in biddingTypeOptions\"\n                      :key=\"dict.dictValue\"\n                      :label=\"dict.dictLabel\"\n                      :value=\"dict.dictValue\"\n                    ></el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n            </el-row> -->\n            <el-row>\n              <!-- <el-col :span=\"12\">\n                <el-form-item label=\"预算金额\" prop=\"budgetMoney\">\n                  <el-input\n                    type=\"number\"\n                    v-model=\"form.budgetMoney\"\n                    placeholder=\"请输入预算金额\"\n                  />\n                </el-form-item>\n              </el-col> -->\n              <el-col :span=\"12\">\n                <el-form-item label=\"被授权公司\" prop=\"authCompany\">\n                  <el-input v-model=\"form.authCompany\" placeholder=\"请输入授权公司\" />\n                  <el-link @click=\"addDomain\" type=\"primary\">添加</el-link>\n                </el-form-item>\n                <el-form-item v-for=\"(company, index) in authCompanys\" :label=\"'被授权公司' + (index + 1)\" :key=\"company.key\"\n                  class=\"info-type\">\n                  <el-input v-model=\"company.value\" :placeholder=\"'被授权公司' + (index + 1)\" style=\"max-width: 300px\" />\n                  <el-link @click=\"removeDomain(index)\" type=\"primary\">删除</el-link>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"授权公司联系人/联系电话\" prop=\"authContact\">\n                  <el-input v-model=\"form.authContact\" placeholder=\"请输入授权公司联系人/联系电话\" />\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <!-- <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"招标信息公布网站\" prop=\"biddingNet\">\n                  <el-input\n                    v-model=\"form.biddingNet\"\n                    placeholder=\"请输入招标信息公布网站\"\n                  />\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"招标单位\" prop=\"biddingCompany\">\n                  <el-input\n                    v-model=\"form.biddingCompany\"\n                    placeholder=\"请输入招标单位\"\n                  />\n                </el-form-item>\n              </el-col>\n            </el-row> -->\n            <!-- <el-row v-if=\"form.operationType == 2\">\n              <el-col :span=\"20\">\n                <el-form-item label=\"模板下载\">\n                  <el-col :span=\"8\">\n                    <el-link @click=\"downloadSQS\" type=\"primary\">海佳集团-授权书.docx</el-link>\n                  </el-col>\n                  <el-col :span=\"8\">\n                    <el-link @click=\"downloadCRH\" type=\"primary\">海佳集团-售后服务承诺函.docx</el-link>\n                  </el-col>\n                </el-form-item>\n              </el-col>\n            </el-row> -->\n            <!-- <el-form-item label=\"授权书\" v-if=\"form.operationType == 2\" :required=\"form.operationType == 2\">\n              <fileUpload v-model=\"form.authFile\" :fileType=\"['doc', 'docx']\" />\n            </el-form-item> -->\n            <el-form-item label=\"其余附件\">\n              <fileUpload v-model=\"form.afterSaleFile\" :fileType=\"['doc', 'docx']\" />\n            </el-form-item>\n            <el-row>\n              <el-col :span=\"20\">\n                <el-form-item label=\"投标产品型号\" prop=\"model\" :required=\"true\">\n                  <el-cascader class=\"mobile-width\" style=\"width: 700px\" v-model=\"form.model\" placeholder=\"请输入产品型号\"\n                    :options=\"modelOptions\" :props=\"{ multiple: true }\" clearable filterable></el-cascader>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row>\n              <el-col :span=\"20\">\n                <el-form-item label=\"投标产品规格\" prop=\"spec\" :required=\"true\">\n                  <el-cascader class=\"mobile-width\" style=\"width: 700px\" v-model=\"form.spec\" placeholder=\"请输入产品规格\"\n                    :options=\"specOptions\" :props=\"{ multiple: true }\" clearable filterable></el-cascader>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"安装面积\" prop=\"area\">\n                  <el-input v-model=\"form.area\" type=\"number\" placeholder=\"请输入安装面积\">\n                    <template slot=\"append\">m²</template>\n                  </el-input>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row>\n              <el-col :span=\"20\">\n                <el-form-item label=\"所需资料\">\n                  <el-cascader class=\"mobile-width\" style=\"width: 700px\" v-model=\"form.requireInfo\"\n                    placeholder=\"请输入资料类型\" :options=\"requireInfoOptions\" :props=\"{ multiple: true }\" clearable\n                    filterable></el-cascader>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <!-- <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"资料类型\">\n                  <el-checkbox-group v-model=\"form.infoType\">\n                    <el-checkbox\n                      v-for=\"dict in infoTypeOptions\"\n                      :key=\"dict.dictValue\"\n                      :label=\"dict.dictValue\"\n                    >\n                      {{ dict.dictLabel }}\n                    </el-checkbox>\n                  </el-checkbox-group>\n                </el-form-item>\n              </el-col>\n            </el-row> -->\n            <!-- <el-row>\n              <el-form-item label=\"资料接收方式\" prop=\"infoType\" :required=\"true\">\n                <el-checkbox-group v-model=\"form.infoType\" class=\"info-type\" style=\"display:flex;\">\n                \n                  <el-checkbox label=\"1\" style=\"margin-left:20px;margin-right:10px !important;\">邮件</el-checkbox>\n                  <el-form-item prop=\"scanFile\">\n                    <el-input v-model=\"form.scanFile\" placeholder=\"请输入邮箱地址\" type=\"email\" style=\"width:300px;\" ></el-input>\n                  </el-form-item>\n                \n                  <el-checkbox label=\"2\" style=\"margin-left:20px;margin-right:10px !important;\">邮寄</el-checkbox>\n                  <el-form-item prop=\"sendAddress\">\n                    <el-input v-model=\"form.sendAddress\" placeholder=\"请输入收件地址\" style=\"width:300px;\" ></el-input>\n                  </el-form-item>\n                </el-checkbox-group>\n              </el-form-item>\n            </el-row>\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"邮件发送信息\" prop=\"mailInfo\">\n                  <el-input\n                    v-model=\"form.mailInfo\"\n                    placeholder=\"请输入邮件发送信息\"\n                  />\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"快递单号\" prop=\"expressInfo\">\n                  <el-input\n                    v-model=\"form.expressInfo\"\n                    placeholder=\"请输入快递单号\"\n                  />\n                </el-form-item>\n              </el-col>\n            </el-row> -->\n            <el-row>\n              <el-form-item label=\"资料接收方式\" prop=\"infoType\" :required=\"true\">\n                <el-checkbox-group v-model=\"form.infoType\" class=\"info-type\">\n                  <!-- 选项A -->\n                  <el-row style=\"display: flex; margin-bottom: 22px\">\n                    <el-col :span=\"12\" style=\"display: flex\">\n                      <el-checkbox label=\"1\" style=\"margin-left: 20px; margin-right: 10px !important\">邮件</el-checkbox>\n                      <el-form-item prop=\"scanFile\">\n                        <el-input v-model=\"form.scanFile\" placeholder=\"请输入邮箱地址\" style=\"width: 300px\"\n                          type=\"email\"></el-input>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"12\">\n                      <el-form-item label=\"邮件发送信息\" prop=\"mailInfo\">\n                        <el-input v-model=\"form.mailInfo\" placeholder=\"请输入邮件发送信息\" />\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n                  <!-- 选项B  -->\n                  <el-row style=\"display: flex; margin-bottom: 22px\">\n                    <el-col :span=\"12\" style=\"display: flex\">\n                      <el-checkbox label=\"2\" style=\"margin-left: 20px; margin-right: 10px !important\">邮寄</el-checkbox>\n                      <el-form-item prop=\"sendAddress\">\n                        <el-input v-model=\"form.sendAddress\" placeholder=\"请输入收件地址\" style=\"width: 300px\"></el-input>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"12\">\n                      <el-form-item label=\"快递单号\" prop=\"expressInfo\">\n                        <el-input v-model=\"form.expressInfo\" placeholder=\"请输入快递单号\" />\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n                </el-checkbox-group>\n              </el-form-item>\n            </el-row>\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"操作类型\" prop=\"operationType\">\n                  <el-radio-group @change=\"optTypeChange\" v-model=\"form.operationType\">\n                    <el-radio v-for=\"dict in operationTypeOptions\" :key=\"dict.dictValue\"\n                      :label=\"dict.dictValue\">{{ dict.dictLabel }}</el-radio>\n                  </el-radio-group>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"审核状态\">\n                  <el-radio-group :disabled=\"auditStatusEdit\" v-model=\"form.auditStatus\">\n                    <el-radio v-for=\"dict in auditStatusOptions\" :key=\"dict.dictValue\"\n                      :label=\"dict.dictValue\">{{ dict.dictLabel }}</el-radio>\n                  </el-radio-group>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-form-item label=\"备注\" prop=\"remark\">\n              <el-input v-model=\"form.remark\" type=\"textarea\" placeholder=\"请输入备注\" />\n            </el-form-item>\n          </el-form>\n          <div slot=\"footer\" class=\"dialog-footer\">\n            <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n            <el-button @click=\"cancel\">取 消</el-button>\n          </div>\n        </el-dialog>\n        <!-- 查看项目报备对话框 -->\n        <el-dialog :title=\"title\" :visible.sync=\"viewOpen\" :fullscreen=\"true\" :lock-scroll=\"true\"\n          :destroy-on-close=\"true\" custom-class=\"view-dialog\" @close=\"viewOk\">\n          <flowable v-if=\"viewOpen\" ref=\"flow\" :procDefKey=\"defKey\" :procInsId=\"procInsId\" :taskId=\"taskId\"\n            :bizKey=\"bizKey\" :finished=\"finished\" :isAuthImages=\"isAuthImages\">\n            <template v-slot:title>项目信息</template>\n            <template v-slot:content>\n              <el-descriptions label-width=\"120px\" :column=\"isMobile ? 1 : 3\">\n                <el-descriptions-item label=\"操作类型\" prop=\"operationType\">\n                  {{ view.operationType }}\n                </el-descriptions-item>\n                <el-descriptions-item label=\"审核状态\">\n                  {{ view.auditStatus }}\n                </el-descriptions-item>\n                <!-- <el-descriptions-item label=\"驳回原因\" prop=\"rejectReason\">\n                {{ view.rejectReason }}\n              </el-descriptions-item> -->\n                <el-descriptions-item label=\"项目ID\">\n                  {{ view.projectId }}\n                </el-descriptions-item>\n                <el-descriptions-item label=\"项目编号\" prop=\"projectNo\">\n                  {{ view.projectNo }}\n                </el-descriptions-item>\n                <el-descriptions-item label=\"项目名称\" span=\"3\" prop=\"projectName\">\n                  <el-popover v-if=\"likecount > 0\" placement=\"top-start\" title=\"相似项目\" width=\"450\" trigger=\"hover\">\n                    <el-table :data=\"likeList\">\n                      <el-table-column width=\"100\" property=\"value\" label=\"项目ID\"></el-table-column>\n                      <el-table-column width=\"300\" property=\"name\" label=\"项目名称\" show-overflow-tooltip></el-table-column>\n                    </el-table>\n                    <el-badge slot=\"reference\" :value=\"likecount\" class=\"item\">\n                      {{ view.projectName\n                      }}<span class=\"likeTip\">&nbsp;&nbsp;存在相似项目</span>\n                    </el-badge>\n                  </el-popover>\n                  <span v-if=\"!likecount\">{{ view.projectName }}</span>\n                </el-descriptions-item>\n                <el-descriptions-item label=\"编辑状态\">\n                  {{ view.editStatus }}\n                </el-descriptions-item>\n                <el-descriptions-item label=\"项目所在地\" prop=\"area\">\n                  {{ view.province }}\n                </el-descriptions-item>\n                <el-descriptions-item label=\"详细地址\">\n                  {{ view.address }}\n                </el-descriptions-item>\n                <el-descriptions-item label=\"安装面积(m²)\">\n                  {{ view.area }}\n                </el-descriptions-item>\n                <el-descriptions-item label=\"被授权公司\">\n                  {{ view.authCompany }}\n                </el-descriptions-item>\n                <el-descriptions-item label=\"授权公司联系人/联系电话\">\n                  {{ view.authContact }}\n                </el-descriptions-item>\n                <el-descriptions-item label=\"招标单位\">\n                  {{ view.biddingCompany }}\n                </el-descriptions-item>\n                <!-- <el-descriptions-item label=\"招标单位联系人/联系电话\">\n                  {{ view.biddingContact }}\n                </el-descriptions-item> -->\n                <el-descriptions-item label=\"项目所属省份\" prop=\"belongProvince\">\n                  {{ view.belongProvince }}\n                </el-descriptions-item>\n                <el-descriptions-item label=\"售后年限\" prop=\"afterSaleYear\">\n                  {{ view.afterSaleYear }}\n                </el-descriptions-item>\n                <el-descriptions-item label=\"开标日期\">\n                  {{ view.openDate }}\n                </el-descriptions-item>\n                <el-descriptions-item label=\"挂网日期\">\n                  {{ view.hangDate }}\n                </el-descriptions-item>\n                <el-descriptions-item label=\"所属经销商\" prop=\"distributor\">\n                  {{ view.distributor }}\n                </el-descriptions-item>\n                <el-descriptions-item label=\"所需资料\" span=\"3\">\n                  <template v-if=\"infoList1.length > 0\">\n                    <ul class=\"infinite-list\" v-infinite-scroll=\"load\" style=\"overflow: auto\">\n                      <li v-for=\"(i, index) in infoList1\" v-bind:key=\"index\" class=\"infinite-list-item\">\n                        {{ i.dictLabel }}\n                        <el-link v-if=\"i.targetUrl && view.auditStatus == '已审批'\"\n                          @click.prevent=\"handleDownload(i.targetUrl)\" type=\"primary\">下载</el-link>\n                      </li>\n                    </ul>\n                    <ul class=\"infinite-list\" v-infinite-scroll=\"load\" style=\"overflow: auto\">\n                      <li v-for=\"(i, index) in infoList2\" v-bind:key=\"index\" class=\"infinite-list-item\">\n                        {{ i.dictLabel }}\n                        <el-link target=\"_blank\" v-if=\"i.targetUrl && view.auditStatus == '已审批'\"\n                          @click.prevent=\"handleDownload(i.targetUrl)\" type=\"primary\">下载</el-link>\n                      </li>\n                    </ul>\n                  </template>\n                  <span v-else>{{ view.requireInfo }}</span>\n                </el-descriptions-item>\n                <el-descriptions-item label=\"投标产品型号\">\n                  {{ view.model }}\n                </el-descriptions-item>\n                <el-descriptions-item label=\"投标产品规格\">\n                  {{ view.spec }}\n                </el-descriptions-item>\n                <!-- <el-descriptions-item label=\"资料类型\">\n                {{ view.infoType }}\n              </el-descriptions-item> -->\n                <el-descriptions-item label=\"授权书\" v-if=\"view.operationType == '授权'\">\n                  <el-link target=\"_blank\" v-if=\"view.authFile\" :href=\"view.authFile\" type=\"primary\">下载</el-link>\n                </el-descriptions-item>\n                <el-descriptions-item label=\"其余附件\" v-if=\"view.operationType == '授权'\">\n                  <el-link target=\"_blank\" v-if=\"view.afterSaleFile\" :href=\"view.afterSaleFile\"\n                    type=\"primary\">下载</el-link>\n                </el-descriptions-item>\n\n                <el-descriptions-item label=\"授权书图片\" v-if=\"view.operationType == '授权'\">\n                  <el-link target=\"_blank\" v-if=\"view.authImages && view.auditStatus === '已审批'\" :href=\"view.authImages\"\n                    type=\"primary\">下载</el-link>\n                </el-descriptions-item>\n                <el-descriptions-item label=\"资料接收邮件\" prop=\"scanFile\">\n                  {{ view.scanFile }}\n                </el-descriptions-item>\n                <el-descriptions-item label=\"资料接收地址\" prop=\"sendAddress\">\n                  {{ view.sendAddress }}\n                </el-descriptions-item>\n                <el-descriptions-item label=\"邮件发送信息\" prop=\"mailInfo\">\n                  {{ view.mailInfo }}\n                </el-descriptions-item>\n                <el-descriptions-item label=\"快递单号\" prop=\"expressInfo\">\n                  {{ view.expressInfo }}\n                </el-descriptions-item>\n                <el-descriptions-item label=\"备注\" prop=\"remark\">\n                  {{ view.remark }}\n                </el-descriptions-item>\n                <el-descriptions-item label=\"提交时间\">\n                  {{ view.createTime }}\n                </el-descriptions-item>\n                <el-descriptions-item label=\"修改时间\">\n                  {{ view.updateTime }}\n                </el-descriptions-item>\n              </el-descriptions>\n            </template>\n          </flowable>\n        </el-dialog>\n        <!-- 项目导入对话框 -->\n        <el-dialog :title=\"upload.title\" :visible.sync=\"upload.open\" width=\"400px\" append-to-body>\n          <el-upload ref=\"upload\" :limit=\"1\" accept=\".xlsx, .xls\" :headers=\"upload.headers\"\n            :action=\"upload.url + '?updateSupport=' + upload.updateSupport\" :disabled=\"upload.isUploading\"\n            :on-progress=\"handleFileUploadProgress\" :on-success=\"handleFileSuccess\" :auto-upload=\"false\" drag>\n            <i class=\"el-icon-upload\"></i>\n            <div class=\"el-upload__text\">\n              将文件拖到此处，或\n              <em>点击上传</em>\n            </div>\n            <div class=\"el-upload__tip\" slot=\"tip\">\n              <el-checkbox v-model=\"upload.updateSupport\" />是否更新已经存在的项目数据\n              <el-link type=\"info\" style=\"font-size: 12px\" @click=\"importTemplate\">下载模板</el-link>\n            </div>\n            <div class=\"el-upload__tip\" style=\"color: red\" slot=\"tip\">\n              提示：仅允许导入\"xls\"或\"xlsx\"格式文件！\n            </div>\n          </el-upload>\n          <div slot=\"footer\" class=\"dialog-footer\">\n            <el-button type=\"primary\" @click=\"submitFileForm\">确 定</el-button>\n            <el-button @click=\"upload.open = false\">取 消</el-button>\n          </div>\n        </el-dialog>\n      </el-col>\n    </el-row>\n  </div>\n</template>\n\n<script>\nimport {\n  listReport,\n  getReport,\n  delReport,\n  addReport,\n  updateReport,\n  exportReport,\n  importTemplate,\n  printReport,\n  checkNameUnique,\n  getLikeList,\n  authReport,\n} from \"@/api/project/report\";\nimport { getToken } from \"@/utils/auth\";\nimport FileUpload from \"@/components/FileUpload\";\nimport flowable from \"@/views/flowable/task/record/view\";\nimport { getInsIdByBizKey } from \"@/api/flowable/todo\";\nimport { regionData, CodeToText, TextToCode } from \"element-china-area-data\";\nimport print from \"print-js\";\nexport default {\n  name: \"Report\",\n  components: {\n    FileUpload,\n    print,\n    flowable,\n  },\n  data() {\n    var infoTypeValueVali = (rule, value, callback) => {\n      if (this.form.infoType.indexOf(\"1\") >= 0 && !this.form.scanFile) {\n        callback(new Error(\"邮箱地址必填\"));\n        return;\n      }\n      callback();\n    };\n    var infoTypeValueVali2 = (rule, value, callback) => {\n      if (this.form.infoType.indexOf(\"2\") >= 0 && !this.form.sendAddress) {\n        callback(new Error(\"收件地址必填\"));\n        return;\n      }\n      callback();\n    };\n    var nameVali = (rule, value, callback) => {\n      if (!this.form.projectName) {\n        callback(new Error(\"项目名称必填\"));\n      } else {\n        if (/\\s+/g.test(this.form.projectName)) {\n          callback(new Error(\"项目名称不规范\"));\n          return;\n        }\n        checkNameUnique({\n          projectName: this.form.projectName,\n          projectId: this.form.projectId,\n        }).then((response) => {\n          if (response.data == 0) {\n            callback();\n          } else {\n            callback(new Error(\"项目名称已存在\"));\n          }\n        });\n      }\n    };\n    var codeVali = (rule, value, callback) => {\n      if (!that.form.projectNo) {\n        callback(new Error(\"项目编号必填\"));\n      } else if (/\\s+/g.test(that.form.projectNo)) {\n        callback(new Error(\"项目编号不规范\"));\n        return;\n      }\n      callback();\n    };\n    var authFileValueVali = (rule, value, callback) => {\n      if (this.form.operationType == 2 && !this.form.authFile) {\n        callback(new Error(\"授权类型必传授权书\"));\n      }\n      callback();\n    };\n    var openDateVali = (rule, value, callback) => {\n      console.log(123566)\n      if (!that.form.openDate) {\n        callback(new Error(\"开标日期必填\"));\n        return;\n      } else if (value === \"无\") {\n        callback();\n        return;\n      } else if (!/^\\d{4}-\\d{2}-\\d{2}$/.test(value)) {\n        callback(new Error(\"开标日期格式不合法，示例2025-01-01\"));\n        return;\n      }\n      callback();\n    };\n    var hangDateVali = (rule, value, callback) => {\n      if (!that.form.hangDate) {\n        callback(new Error(\"挂网日期必填\"));\n        return;\n      } else if (value === \"无\") {\n        callback();\n        return;\n      } else if (!/^\\d{4}-\\d{2}-\\d{2}$/.test(value)) {\n        callback(new Error(\"挂网日期格式不合法，示例2025-01-01\"));\n        return;\n      }\n      callback();\n    };\n    return {\n      isMobile: false,\n      pageLayout: \"total, sizes, prev, pager, next, jumper\",\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      showExport: false,\n      showPrint: false,\n      // 总条数\n      total: 0,\n      // 项目报备表格数据\n      reportList: [],\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      open: false,\n      // 操作类型字典\n      operationTypeOptions: [],\n      // 审核状态字典\n      auditStatusOptions: [],\n      // 编辑状态字典\n      editStatusOptions: [],\n      // 招标方式字典\n      biddingTypeOptions: [],\n      // 投标产品型号字典\n      modelOptions: [],\n      modelOption1: [],\n      // 所需资料字典\n      requireInfoOptions: [],\n      requireInfoOption1: [],\n      // 资料类型字典\n      infoTypeOptions: [],\n      specOptions: [],\n      specOption1: [],\n      // 所属省份字典\n      belongProvinceOptions: [],\n      belongProvinceOptions1: [],\n      // 售后年限\n      afterSaleYearOptions: [],\n      afterSaleYearOptions1: [],\n      // 项目导入参数\n      upload: {\n        // 是否显示弹出层（用户导入）\n        open: false,\n        // 弹出层标题（用户导入）\n        title: \"\",\n        // 是否禁用上传\n        isUploading: false,\n        // 是否更新已经存在的用户数据\n        updateSupport: 0,\n        // 设置上传的请求头部\n        headers: { Authorization: \"Bearer \" + getToken() },\n        // 上传的地址\n        url: process.env.VUE_APP_BASE_API + \"/project/report/importData\",\n      },\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        projectNo: null,\n        projectName: null,\n        operationType: null,\n        auditStatus: null,\n        province: null,\n        userType: null,\n        belongUser: null,\n        updateTimeArr: [],\n        spare1: null,\n        address: null,\n        biddingCompany: null,\n        authCompany: null,\n        fullField: null\n      },\n      // 表单参数\n      form: {},\n      // 表单校验\n      // 表单校验\n      rules: {\n        operationType: [{ required: true, message: \"操作类型必选\" }],\n        projectNo: [{ required: true, validate: codeVali, trigger: \"blur\" }],\n        projectName: [{ required: true, validate: nameVali, trigger: \"blur\" }],\n        address: [\n          { required: true, message: \"详细地址不能为空\", trigger: \"blur\" },\n        ],\n        biddingCompany: [\n          { required: true, message: \"招标单位不能为空\", trigger: \"blur\" },\n        ],\n        openDate: [\n          { required: true, validate: openDateVali, trigger: \"blur\" },\n        ],\n        afterSaleYear: [\n          { required: true, message: \"售后年限不能为空\", trigger: \"blur\" },\n        ],\n        hangDate: [\n          { required: true, validate: hangDateVali, trigger: \"blur\" },\n        ],\n        belongProvince: [\n          { required: true, message: \"售后年限不能为空\", trigger: \"blur\" },\n        ],\n        distributor: [\n          { required: true, message: \"所属经销商不能为空\", trigger: \"blur\" },\n        ],\n        scanFile: [{ validator: infoTypeValueVali, trigger: \"blur\" }],\n        sendAddress: [{ validator: infoTypeValueVali2, trigger: \"blur\" }],\n        model: [{ required: true, message: \"投标产品型号必选\" }],\n        spec: [{ required: true, message: \"投标产品规格必选\" }],\n        province: [{ required: true, message: \"项目所在地必选\" }],\n        infoType: [\n          { required: true, message: \"资料接收方式必选\", trigger: \"change\" },\n        ],\n        authFile: [{ validator: authFileValueVali, trigger: \"change\" }],\n        biddingContact: [\n          { required: true, message: \"招标单位联系人/联系电话必填\" },\n        ],\n        authContact: [\n          { required: true, message: \"授权公司联系人/联系电话必填\" },\n        ],\n      },\n      // 列信息\n      columns: [\n        { key: \"belongUser\", index: 1, label: `所属用户`, visible: true },\n        { key: \"projectNo\", index: 2, label: `项目编号`, visible: true },\n        { key: \"projectName\", index: 3, label: `项目名称`, visible: true },\n        { key: \"operationType\", index: 4, label: `操作类型`, visible: true },\n        { key: \"province\", index: 5, label: `项目所在地`, visible: true },\n        { key: \"address\", index: 6, label: `详细地址`, visible: true },\n        { key: \"authCompany\", index: 7, label: `被授权公司`, visible: true },\n        { key: \"distributor\", index: 8, label: `所属经销商`, visible: true },\n        { key: \"biddingCompany\", index: 9, label: `招标单位`, visible: true },\n        { key: \"model\", index: 10, label: `投标产品型号`, visible: true },\n        { key: \"spec\", index: 11, label: `投标产品规格`, visible: true },\n        { key: \"area\", index: 12, label: `安装面积`, visible: true },\n        { key: \"requireInfo\", index: 13, label: `所需资料`, visible: true },\n        { key: \"infoType\", index: 14, label: `资料类型`, visible: true },\n        { key: \"scanFile\", index: 15, label: `资料接收邮件`, visible: true },\n        { key: \"scanFile\", index: 16, label: `资料接收地址`, visible: true },\n        { key: \"belongProvince\", index: 17, label: `项目所属省份`, visible: true },\n        { key: \"afterSaleYear\", index: 18, label: `售后年限`, visible: true },\n        { key: \"openDate\", index: 19, label: `开标日期`, visible: true },\n        { key: \"hangDate\", index: 20, label: `挂网日期`, visible: true },\n        { key: \"createTime\", index: 21, label: `提交时间`, visible: true },\n        { key: \"updateTime\", index: 22, label: `修改时间`, visible: true },\n        // { key: \"auditStatus\", index: 19, label: `审核状态`, visible: false },\n        // { key: \"editStatus\", index: 20, label: `编辑状态`, visible: false },\n        // { key: \"11\", index: 21, label: `授权书`, visible: false },\n        //{ key: \"12\", index: 23, label: `售后服务承诺函`, visible: false },\n      ],\n      options: regionData,\n      selectedOptions: [],\n      queryArea: [],\n      viewOpen: false,\n      view: {},\n      infoList1: [],\n      infoList2: [],\n      defKey: \"process_project_report\",\n      procInsId: undefined,\n      taskId: undefined,\n      finished: true,\n      bizKey: undefined,\n      auditStatusTree: [],\n      operationTypeTree: [],\n      userTypeTree: [],\n      defaultProps: {\n        children: \"children\",\n        label: \"label\",\n      },\n      oldOperationType: undefined,\n      showUType: true,\n      chooseOptType: undefined,\n      chooseAuditStatus: undefined,\n      chooseUserId: undefined,\n      chooseEditStatus: undefined,\n      chooseSpare2: undefined,\n      likeList: undefined,\n      likeCount: undefined,\n      authCompanys: [],\n      isAdmin: true,\n      auditStatusEdit: true,\n      isAuthImages: false,\n      searchPickerOptions: {\n        shortcuts: [{\n          text: '最近一周',\n          onClick(picker) {\n            const end = new Date();\n            const start = new Date();\n            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);\n            picker.$emit('pick', [start, end]);\n          }\n        }, {\n          text: '最近一个月',\n          onClick(picker) {\n            const end = new Date();\n            const start = new Date();\n            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);\n            picker.$emit('pick', [start, end]);\n          }\n        }, {\n          text: '最近三个月',\n          onClick(picker) {\n            const end = new Date();\n            const start = new Date();\n            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);\n            picker.$emit('pick', [start, end]);\n          }\n        }]\n      },\n      pickerOptions: {\n        disabledDate(time) {\n          return time.getTime() > Date.now();\n        },\n        shortcuts: [\n          {\n            text: \"今天\",\n            onClick(picker) {\n              picker.$emit(\"pick\", new Date());\n            },\n          },\n          {\n            text: \"昨天\",\n            onClick(picker) {\n              const date = new Date();\n              date.setTime(date.getTime() - 3600 * 1000 * 24);\n              picker.$emit(\"pick\", date);\n            },\n          },\n          {\n            text: \"一周前\",\n            onClick(picker) {\n              const date = new Date();\n              date.setTime(date.getTime() - 3600 * 1000 * 24 * 7);\n              picker.$emit(\"pick\", date);\n            },\n          },\n        ],\n      },\n      searchField: 'all', // 新增：当前选中的搜索字段，默认全字段\n      searchFieldOptions: [\n        { label: '全字段', value: 'all' },\n        { label: '项目编号', value: 'projectNo' },\n        { label: '项目名称', value: 'projectName' },\n        { label: '详细地址', value: 'address' },\n        { label: '招标单位', value: 'biddingCompany' },\n        { label: '授权公司', value: 'authCompany' }\n      ],\n      searchValue: '', // 新增：搜索内容\n      highlightFields: ['projectNo', 'projectName', 'address', 'biddingCompany', 'authCompany'], // 新增：高亮字段\n    };\n  },\n  activated() {\n    console.log(\"=report index==>>activated\");\n    this.getList();\n  },\n  created() {\n    this.getList();\n    this.getDicts(\"pr_operation_type\").then((response) => {\n      this.operationTypeOptions = response.data;\n      var opt = [];\n      opt.push({ id: 0, label: \"全部\" });\n      response.data.forEach((elem, index) => {\n        var obj = {};\n        obj.label = elem.dictLabel;\n        obj.id = elem.dictValue;\n        opt.push(obj);\n      });\n      var operationType = {};\n      operationType.label = \"操作类型\";\n      operationType.children = opt;\n      var operationTypes = [];\n      operationTypes.push(operationType);\n      this.operationTypeTree = operationTypes;\n    });\n    this.getDicts(\"pr_audit_status\").then((response) => {\n      var type = 0;\n      if (this.$store.state.user.roles) {\n        if (this.$store.state.user.roles.includes(\"common\")) {\n          type = 1;\n        }\n        if (this.$store.state.user.roles.includes(\"province_admin\")) {\n          type = 2;\n        }\n        if (this.$store.state.user.roles.includes(\"report_admin\")) {\n          type = 3;\n        }\n      }\n      this.auditStatusOptions = response.data;\n      var opt = [];\n      opt.push({ id: 9, label: \"全部\" });\n      if (type == 2 || type == 3) {\n        opt.push({ id: 10, label: \"未审批\" });\n      }\n      response.data.forEach((elem, index) => {\n        var obj = {};\n        obj.label = elem.dictLabel;\n        obj.id = elem.dictValue;\n        opt.push(obj);\n      });\n      var auditStatusTree = {};\n      auditStatusTree.label = \"审核状态\";\n      auditStatusTree.children = opt;\n      var auditStatusTrees = [];\n      auditStatusTrees.push(auditStatusTree);\n      this.auditStatusTree = auditStatusTrees;\n    });\n    this.getDicts(\"pr_edit_status\").then((response) => {\n      this.editStatusOptions = response.data;\n    });\n    // this.getDicts(\"pr_bidding_type\").then((response) => {\n    //   this.biddingTypeOptions = response.data;\n    // });\n    this.getDicts(\"pr_model\").then((response) => {\n      this.modelOption1 = response.data;\n      var opt = [];\n      response.data.forEach((elem, index) => {\n        var obj = {};\n        obj.label = elem.dictLabel;\n        obj.value = elem.dictValue;\n        opt.push(obj);\n      });\n      this.modelOptions = opt;\n    });\n    this.getDicts(\"pr_spec\").then((response) => {\n      this.specOption1 = response.data;\n      var opt = [];\n      response.data.forEach((elem, index) => {\n        var obj = {};\n        obj.label = elem.dictLabel;\n        obj.value = elem.dictValue;\n        opt.push(obj);\n      });\n      this.specOptions = opt;\n    });\n    this.getDicts(\"pr_info\").then((response) => {\n      this.requireInfoOption1 = response.data;\n      var opt = [];\n      response.data.forEach((elem, index) => {\n        var obj = {};\n        obj.label = elem.dictLabel;\n        obj.value = elem.dictValue;\n        opt.push(obj);\n      });\n      this.requireInfoOptions = opt;\n    });\n    this.getDicts(\"pr_province\").then((response) => {\n      this.belongProvinceOptions1 = response.data;\n      var opt = [];\n      response.data.forEach((elem, index) => {\n        var obj = {};\n        obj.label = elem.dictLabel;\n        obj.value = elem.dictValue;\n        opt.push(obj);\n      });\n      this.belongProvinceOptions = opt;\n    });\n    this.getDicts(\"pr_after_sale_year\").then((response) => {\n      this.afterSaleYearOptions1 = response.data;\n      var opt = [];\n      response.data.forEach((elem, index) => {\n        var obj = {};\n        obj.label = elem.dictLabel;\n        obj.value = elem.dictValue;\n        opt.push(obj);\n      });\n      this.afterSaleYearOptions = opt;\n    });\n    this.getDicts(\"pr_data_type\").then((response) => {\n      this.infoTypeOptions = response.data;\n    });\n\n    var opt = [];\n    opt.push({ id: 0, label: \"全部\" });\n    opt.push({ id: 2, label: \"普通用户\" });\n    opt.push({ id: 10, label: \"省负责人\" });\n\n    var userType = {};\n    userType.label = \"所属用户\";\n    userType.children = opt;\n    var userTypes = [];\n    userTypes.push(userType);\n    this.userTypeTree = userTypes;\n    if (\n      this.$store.state.user.roles &&\n      this.$store.state.user.roles.includes(\"common\")\n    ) {\n      this.showUType = false;\n    }\n    if (this._isMobile()) {\n      this.isMobile = true;\n      this.pageLayout = \"total, prev, next, jumper\";\n    }\n    if (\n      this.$store.state.user.roles &&\n      this.$store.state.user.roles.includes(\"report_admin\")\n    ) {\n      this.isAdmin = false;\n    }\n  },\n  methods: {\n    _isMobile() {\n      let flag = navigator.userAgent.match(\n        /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i\n      );\n      return flag;\n    },\n    /** 查询项目报备列表 */\n    getList() {\n      this.loading = true;\n      listReport(this.queryParams).then((response) => {\n        this.reportList = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    handleDownload(url) {\n      const a = document.createElement(\"a\"); // 创建一个HTML 元素\n      a.setAttribute(\"target\", \"_blank\");\n      a.setAttribute(\"download\", \"\"); //download属性\n      const href =\n        \"https://report.clled.com/prod-api/common/download/resource?resource=\" +\n        url;\n      console.log(href);\n      a.setAttribute(\"href\", href); // href链接\n      a.click(); // 自执行点击事件\n    },\n    // 审核状态节点单击事件\n    handleAuditNodeClick(data) {\n      if (data.id == 9) {\n        this.queryParams.auditStatus = undefined;\n        this.queryParams.node = undefined;\n        this.queryParams.spare1 = undefined;\n      } else {\n        if (data.id == 1 || data.id == 10) {\n          this.queryParams.auditStatus = 1;\n          if (this.$store.state.user.roles) {\n            if (this.$store.state.user.roles.includes(\"province_admin\")) {\n              this.queryParams.node = \"省负责人\";\n              if (data.id == 10) {\n                this.queryParams.spare1 = \"=\";\n              } else {\n                this.queryParams.spare1 = \"!=\";\n              }\n            }\n            if (this.$store.state.user.roles.includes(\"report_admin\")) {\n              this.queryParams.node = \"审核员\";\n              if (data.id == 10) {\n                this.queryParams.spare1 = \"=\";\n              } else {\n                this.queryParams.spare1 = \"!=\";\n              }\n            }\n          }\n        } else {\n          this.queryParams.auditStatus = data.id;\n          this.queryParams.node = undefined;\n          this.queryParams.spare1 = undefined;\n        }\n      }\n      this.getList();\n    },\n    // 操作类型节点单击事件\n    handleOptNodeClick(data) {\n      if (data.id == 0) {\n        this.queryParams.operationType = undefined;\n      } else {\n        this.queryParams.operationType = data.id;\n      }\n      this.getList();\n    },\n    // 用户类型节点单击事件\n    handleUserNodeClick(data) {\n      if (data.id == 0) {\n        this.queryParams.userType = undefined;\n      } else {\n        this.queryParams.userType = data.id;\n      }\n      this.getList();\n    },\n    // 筛选节点\n    filterNode(value, data) {\n      if (!value) return true;\n      return data.label.indexOf(value) !== -1;\n    },\n    searchFormat(row, column) {\n      if (\n        row.indexOf(this.queryParams.spare1) !== -1 &&\n        this.queryParams.spare1 !== \"\"\n      ) {\n        return row.replace(\n          this.queryParams.spare1,\n          '<font color=\"#f00\">' + this.queryParams.spare1 + \"</font>\"\n        );\n      } else {\n        return row;\n      }\n    },\n    // 操作类型字典翻译\n    operationTypeFormat(row, column) {\n      return this.selectDictLabel(this.operationTypeOptions, row.operationType);\n    },\n    // 审核状态字典翻译\n    auditStatusFormat(row, column) {\n      return this.selectDictLabel(this.auditStatusOptions, row.auditStatus);\n    },\n    // 编辑状态字典翻译\n    editStatusFormat(row, column) {\n      return this.selectDictLabel(this.editStatusOptions, row.editStatus);\n    },\n    // 招标方式字典翻译\n    biddingTypeFormat(row, column) {\n      return this.selectDictLabel(this.biddingTypeOptions, row.biddingType);\n    },\n    // 投标产品型号字典翻译\n    modelFormat(row, column) {\n      return this.selectDictLabels(this.modelOption1, row.model);\n    },\n    // 投标产品规格字典翻译\n    specFormat(row, column) {\n      return this.selectDictLabels(this.specOption1, row.spec);\n    },\n    // 所需资料字典翻译\n    requireInfoFormat(row, column) {\n      if (row.requireInfo) {\n        return this.selectDictLabels(this.requireInfoOption1, row.requireInfo);\n      }\n    },\n    // 资料类型字典翻译\n    infoTypeFormat(row, column) {\n      return this.selectDictLabels(this.infoTypeOptions, row.infoType);\n    },\n    // 所属省份字典翻译\n    belongProvinceFormat(row, column) {\n      return this.selectDictLabels(this.belongProvinceOptions1, row.belongProvince);\n    },\n    // 售后年限字典翻译\n    afterSaleYearFormat(row, column) {\n      return this.selectDictLabels(this.afterSaleYearOptions1, row.afterSaleYear);\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    viewOk() {\n      this.viewOpen = false;\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        projectId: null,\n        projectNo: null,\n        projectName: null,\n        operationType: null,\n        auditStatus: \"0\",\n        rejectReason: null,\n        province: null,\n        city: null,\n        district: null,\n        address: null,\n        editStatus: \"0\",\n        belongUser: null,\n        biddingCompany: null,\n        openDate: null,\n        biddingType: null,\n        budgetMoney: null,\n        authCompany: null,\n        biddingNet: null,\n        distributor: null,\n        model: [],\n        spec: [],\n        area: null,\n        authFile: null,\n        afterSaleFile: null,\n        requireInfo: [],\n        infoType: [],\n        scanFile: null,\n        sendAddress: null,\n        mailInfo: null,\n        expressInfo: null,\n        remark: null,\n        spare1: null,\n        spare2: null,\n      };\n      this.resetForm(\"form\");\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      // 清空所有相关字段\n      this.queryParams.projectNo = null;\n      this.queryParams.projectName = null;\n      this.queryParams.address = null;\n      this.queryParams.biddingCompany = null;\n      this.queryParams.authCompany = null;\n      this.queryParams.fullField = null;\n\n      if (this.searchField === 'all') {\n        this.queryParams.fullField = this.searchValue; // 假设后端 fullField 做全字段模糊\n      } else {\n        this.queryParams[this.searchField] = this.searchValue;\n      }\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.queryArea = [];\n      this.queryParams = {\n        pageNum: 1,\n        pageSize: 10,\n        projectNo: null,\n        projectName: null,\n        operationType: null,\n        auditStatus: null,\n        province: null,\n        userType: null,\n        belongUser: null,\n        spare1: null,\n        address: null,\n        biddingCompany: null,\n        authCompany: null,\n        fullField: null\n      };\n      this.searchField = 'all';\n      this.searchValue = '';\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map((item) => item.projectId);\n      this.single = selection.length !== 1;\n      this.multiple = !selection.length;\n      this.chooseOptType = selection[0].operationType;\n      this.chooseAuditStatus = selection[0].auditStatus;\n      this.chooseUserId = selection[0].userId;\n      this.chooseEditStatus = selection[0].editStatus;\n      this.chooseSpare2 = selection[0].spare2;\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      // this.reset();\n      // this.open = true;\n      // this.title = \"添加项目报备\";\n      this.$router.push({\n        path: \"/project/report/form\",\n        query: {\n          businessKey: undefined,\n          formEdit: true,\n        },\n      });\n    },\n    handleView(row) {\n      let that = this;\n      this.view = JSON.parse(JSON.stringify(row));\n      this.view.operationType = this.operationTypeFormat(row);\n      this.view.auditStatus = this.auditStatusFormat(row);\n      this.view.editStatus = this.editStatusFormat(row);\n      this.view.biddingType = this.biddingTypeFormat(row);\n      this.view.model = this.modelFormat(row);\n      this.view.spec = this.specFormat(row);\n      this.view.requireInfo = this.requireInfoFormat(row);\n      this.view.infoType = this.infoTypeFormat(row);\n      this.view.belongProvince = this.belongProvinceFormat(row);\n      this.view.afterSaleYear = this.afterSaleYearFormat(row);\n      if (row.requireInfos) {\n        this.selectDictLabels(this.requireInfoOption1, row.requireInfo);\n        //this.view.requireInfo =\n        // const infoList = this.view.requireInfo.split(\",\");\n        const half = Math.ceil(row.requireInfos.length / 2);\n\n        this.infoList1 = row.requireInfos.splice(0, half);\n        this.infoList2 = row.requireInfos.splice(-half);\n\n        // const tmpList1 = infoList.splice(0, half);\n        // const tmpList2 = infoList.splice(-half);\n        // tmpList1.forEach((element) => {\n        //   console.log(element);\n        // });\n        // 循环对象赋值\n      } else {\n        this.infoList1 = [];\n        this.infoList2 = [];\n      }\n\n      if (row.operationType == \"2\" && row.spare1 == \"1\") {\n        this.defKey = \"process_project_auth\";\n        this.title = \"查看项目报备转授权\";\n      } else {\n        this.defKey = \"process_project_report\";\n        this.title = \"查看项目报备/授权\";\n      }\n      const params = { bizKey: row.projectId, defKey: this.defKey };\n      getInsIdByBizKey(params).then((resp) => {\n        this.bizKey = row.projectId;\n        if (resp.data && resp.data.instanceId) {\n          this.procInsId = resp.data.instanceId;\n          this.taskId = resp.data.taskId;\n          //console.log(\"==handleView=>>\")\n          //console.log(resp.data)\n          if (\n            resp.data.instanceId &&\n            !resp.data.endTime &&\n            resp.data.assignee == this.$store.state.user.userId\n          ) {\n            if (\n              this.$store.state.user.roles &&\n              this.$store.state.user.roles.includes(\"report_admin\") &&\n              row.operationType == \"2\"\n            ) {\n              this.isAuthImages = true;\n            }\n            this.finished = false;\n          } else if (\n            this.$store.state.user.roles &&\n            this.$store.state.user.roles.includes(\"report_admin\") &&\n            row.node == \"审核员\"\n          ) {\n            if (row.operationType == \"2\") {\n              this.isAuthImages = true;\n            }\n            //审核员角色不控制谁操作\n            this.finished = false;\n          } else {\n            this.finished = true;\n          }\n        } else {\n          this.finished = true;\n          this.procInsId = undefined;\n          this.taskId = undefined;\n        }\n\n        // console.log(\"====>>>驳回\")\n        // //驳回用户\n        // if(row.auditStatus == '3' && row.userId == this.$store.state.user.userId){\n        //   this.finished = false;\n        // }\n        // console.log(\"====>>>驳回：\" + this.finished)\n\n        this.viewOpen = true;\n      });\n      getLikeList({\n        projectName: row.projectName,\n        projectId: row.projectId,\n      }).then((resp) => {\n        //console.log(resp)\n        if (resp.data && resp.data.length > 0) {\n          this.likeList = resp.data;\n          that.likecount = resp.data.length;\n        } else {\n          this.likeList = undefined;\n          that.likecount = undefined;\n        }\n      });\n    },\n    /** 授权按钮操作 */\n    handleAuth(row) {\n      const loading = this.$loading({\n        lock: true,\n        text: \"授权中...\",\n        spinner: \"el-icon-loading\",\n        background: \"rgba(0, 0, 0, 0.7)\",\n      });\n      authReport(row)\n        .then((resp) => {\n          loading.close();\n          this.msgSuccess(resp.msg);\n          this.$router.go(0);\n        })\n        .catch((e) => {\n          loading.close();\n        });\n    },\n    /** 修改按钮操作 */\n    handleUpdate() {\n      let that = this;\n      // that.auditStatusEdit = true;\n      // if(!that.isAdmin && that.chooseAuditStatus == 3){\n      //   that.auditStatusEdit = false;\n      // }else{}\n      //申请者\n      var isApply =\n        this.$store.state.user.roles &&\n        (this.$store.state.user.roles.includes(\"common\") ||\n          this.$store.state.user.roles.includes(\"province_admin\"));\n\n      if (isApply && this.chooseUserId != this.$store.state.user.userId) {\n        this.msgError(\"只能修改本人提交的项目\");\n        return;\n      }\n\n      if (this.chooseOptType == 2) {\n        if (isApply && this.chooseSpare2 != 1) {\n          this.msgError(\"授权被退回才能修改\");\n          return;\n        }\n      }\n      if (this.chooseAuditStatus == 3 && isApply) {\n        if (this.chooseEditStatus == \"0\") {\n          this.msgError(\"审批被驳回无法修改\");\n          return;\n        }\n      }\n      // if(this.chooseOptType == 1){\n      //   if(isApply && this.chooseUserId != this.$store.state.user.userId ){\n      //     this.msgError(\"只能修改本人提交的报备项目\");\n      //     return;\n      //   }\n      // }\n      this.reset();\n      const projectId = this.ids;\n      getReport(projectId).then((response) => {\n        this.form = response.data;\n        if (this.form.model) this.form.model = this.form.model.split(\",\");\n        else this.form.model = [];\n        if (this.form.requireInfo)\n          this.form.requireInfo = this.form.requireInfo.split(\",\");\n        else this.form.requireInfo = [];\n        if (this.form.infoType)\n          this.form.infoType = this.form.infoType.split(\",\");\n        else this.form.infoType = [];\n        if (this.form.spec) this.form.spec = this.form.spec.split(\",\");\n        else this.form.spec = [];\n\n        if (this.form.authCompany) {\n          that.authCompanys = [];\n          var array = this.form.authCompany.split(\",\");\n          array.forEach(function (e) {\n            that.authCompanys.push({\n              value: e,\n              key: Date.now(),\n            });\n          });\n          that.form.authCompany = that.authCompanys[0].value;\n          that.authCompanys.splice(0, 1);\n          //console.log(that.authCompanys)\n        } else this.authCompanys = [];\n\n        this.oldOperationType = response.data.operationType;\n\n        this.open = true;\n        this.title = \"修改项目报备\";\n        var provinces = response.data.province;\n        if (provinces.length > 0) {\n          var address = provinces.split(\"/\");\n          var citys = [];\n          // 省份\n          if (address.length > 0) citys.push(TextToCode[address[0]].code);\n          // 城市\n          if (address.length > 1)\n            citys.push(TextToCode[address[0]][address[1]].code);\n          // 地区\n          if (address.length > 2)\n            citys.push(TextToCode[address[0]][address[1]][address[2]].code);\n\n          this.selectedOptions = citys;\n        }\n      });\n    },\n    /** 提交按钮 */\n    submitForm() {\n      this.$refs[\"form\"].validate((valid) => {\n        if (valid) {\n          if (this.form.infoType.indexOf(\"1\") >= 0 && this.form.scanFile) {\n            let emailReg = /^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\\.[a-zA-Z0-9_-]+)+$/;\n            if (!emailReg.test(this.form.scanFile)) {\n              this.$message.error(\"资料接收方式邮箱格式错误\");\n              return;\n            }\n          }\n          if (this.form.operationType == 1) {\n            this.form.authFile = \"\";\n            this.form.afterSaleFile = \"\";\n          }\n          var formStr = JSON.stringify(this.form);\n          var formData = JSON.parse(formStr);\n          if (formData.model && formData.model.length > 0)\n            formData.model = formData.model.join(\",\");\n          else formData.model = undefined;\n          if (formData.requireInfo && formData.requireInfo.length > 0)\n            formData.requireInfo = formData.requireInfo.join(\",\");\n          else formData.requireInfo = undefined;\n          if (formData.infoType && formData.infoType.length > 0)\n            formData.infoType = formData.infoType.join(\",\");\n          else formData.infoType = undefined;\n          if (formData.spec && formData.spec.length > 0)\n            formData.spec = formData.spec.join(\",\");\n          else formData.spec = undefined;\n\n          //授权公司\n          if (this.authCompanys.length > 0) {\n            var array = new Array();\n            this.authCompanys.forEach(function (e) {\n              array.push(e.value);\n            });\n            formData.authCompany += \",\" + array.join(\",\");\n          }\n\n          if (formData.projectId != null) {\n            updateReport(formData).then((response) => {\n              this.msgSuccess(\"修改成功\");\n\n              if (this.oldOperationType == 1 && formData.operationType == 2) {\n                console.log(\"=====>>>报备改授权\");\n              }\n\n              this.open = false;\n              this.getList();\n            });\n          } else {\n            addReport(formData).then((response) => {\n              this.msgSuccess(\"新增成功\");\n              this.open = false;\n              this.getList();\n            });\n          }\n        }\n      });\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const projectIds = row.projectId || this.ids;\n      this.$confirm(\n        '是否确认删除项目报备编号为\"' + projectIds + '\"的数据项?',\n        \"警告\",\n        {\n          confirmButtonText: \"确定\",\n          cancelButtonText: \"取消\",\n          type: \"warning\",\n        }\n      )\n        .then(function () {\n          return delReport(projectIds);\n        })\n        .then(() => {\n          this.getList();\n          this.msgSuccess(\"删除成功\");\n        });\n    },\n    clickExport() {\n      this.showExport = true;\n    },\n    clickPrint() {\n      this.showPrint = true;\n    },\n    /** 导出按钮操作 */\n    handleExport(type) {\n      let loadingwin;\n      let that = this;\n      this.queryParams.spare1 = type;\n      var col = [];\n      this.columns.forEach((item) => {\n        if (item.visible) {\n          col.push(item.label);\n        }\n      });\n      this.queryParams.spare2 = col.join(\",\");\n      const queryParams = this.queryParams;\n      this.$confirm(\n        \"是否确认导出项目报备搜索结果\" +\n        (type == 0 ? \"本页\" : \"全部\") +\n        \"数据项?\",\n        \"警告\",\n        {\n          confirmButtonText: \"确定\",\n          cancelButtonText: \"取消\",\n          type: \"warning\",\n        }\n      )\n        .then(function () {\n          loadingwin = that.$loading({\n            lock: true, //lock的修改符--默认是false\n            text: \"导出中...\", //显示在加载图标下方的加载文案\n            spinner: \"el-icon-loading\", //自定义加载图标类名\n            background: \"rgba(0, 0, 0, 0.7)\", //遮罩层颜色\n            target: document.querySelector(\".app-wrapper\"), //loadin覆盖的dom元素节点\n          });\n          document.documentElement.style.overflowY = \"hidden\"; //禁止底层div滚动\n          return exportReport(queryParams);\n        })\n        .then((response) => {\n          this.download(response.msg);\n          this.showExport = false;\n          loadingwin.close();\n          document.documentElement.style.overflowY = \"auto\"; //允许底层div滚动\n        })\n        .catch(() => {\n          this.showExport = false;\n          loadingwin.close();\n          document.documentElement.style.overflowY = \"auto\"; //允许底层div滚动\n        });\n    },\n    /** 导入按钮操作 */\n    handleImport() {\n      this.upload.title = \"项目导入\";\n      this.upload.open = true;\n    },\n    /** 下载模板操作 */\n    importTemplate() {\n      importTemplate().then((response) => {\n        this.download(response.msg);\n      });\n    },\n    downloadSQS() {\n      this.download(\"海佳集团-授权书.docx\", false);\n    },\n    downloadCRH() {\n      this.download(\"海佳集团-售后服务承诺函.doc\", false);\n    },\n    // 文件上传中处理\n    handleFileUploadProgress(event, file, fileList) {\n      this.upload.isUploading = true;\n    },\n    // 文件上传成功处理\n    handleFileSuccess(response, file, fileList) {\n      this.upload.open = false;\n      this.upload.isUploading = false;\n      this.$refs.upload.clearFiles();\n      this.$alert(response.msg, \"导入结果\", { dangerouslyUseHTMLString: true });\n      this.getList();\n    },\n    // 提交上传文件\n    submitFileForm() {\n      this.$refs.upload.submit();\n    },\n    handleChange(value) {\n      if (!value || value.length == 0) {\n        this.selectedOptions = null;\n        this.form.province = undefined;\n        this.form.district = undefined;\n        return;\n      }\n      this.selectedOptions = value;\n      var txt = \"\";\n      value.forEach(function (item) {\n        txt += CodeToText[item] + \"/\";\n      });\n      if (txt.length > 1) {\n        txt = txt.substring(0, txt.length - 1);\n        this.form.district = this.$store.state.user.province;\n        this.form.province = txt;\n      } else {\n        this.form.province = undefined;\n        this.form.district = undefined;\n      }\n    },\n    handleQueryCityChange(value) {\n      this.queryArea = value;\n      var txt = \"\";\n      value.forEach(function (item) {\n        txt += CodeToText[item] + \"/\";\n      });\n      if (txt.length > 1) {\n        txt = txt.substring(0, txt.length - 1);\n        this.queryParams.province = txt;\n      } else {\n        this.queryParams.province = undefined;\n      }\n    },\n    handlePrint(type) {\n      this.queryParams.spare1 = type;\n      var properties = [];\n      //properties.push({field: 'index', displayName: '序号'});\n      properties.push({ field: \"projectId\", displayName: \"项目ID\" });\n      this.columns.forEach((item) => {\n        if (item.visible) {\n          properties.push({ field: item.key, displayName: item.label });\n        }\n      });\n      printReport(this.queryParams).then((response) => {\n        printJS({\n          printable: response.data,\n          type: \"json\",\n          properties: properties,\n          header: '<div style=\"text-align: center\"><h3>项目报备列表</h3></div>',\n          targetStyles: [\"*\"],\n          gridHeaderStyle:\n            \"margin-top:20px;border: 1px solid #000;text-align:center\",\n          gridStyle: \"border: 1px solid #000;text-align:center;min-width:50px;\",\n          style: \"@page {margin:0 10mm;margin-top:10mm;}\",\n        });\n      });\n      // printJS({\n      //   printable: \"printArea\",\n      //   type:'html',\n      //   header:null,\n      //   targetStyles:['*'],\n      //   style:\"@page {margin:0 10mm}\"\n      // })\n    },\n    // 删除 showNameCorlor 和 showNoCorlor 方法，新增高亮方法\n    highlightText(text, keyword) {\n      if (!keyword) return text;\n      // 全部高亮\n      return text ? text.replace(new RegExp(keyword, 'g'), `<font color=\"#f00\">${keyword}</font>`) : text;\n    },\n    highlightCell(field, text) {\n      if (this.searchField === 'all' && this.searchValue && this.highlightFields.includes(field)) {\n        return this.highlightText(text, this.searchValue);\n      }\n      if (this.searchField === field && this.searchValue) {\n        return this.highlightText(text, this.searchValue);\n      }\n      return text;\n    },\n    removeDomain(index) {\n      if (index !== -1) {\n        this.authCompanys.splice(index, 1);\n      }\n    },\n    addDomain() {\n      this.authCompanys.push({\n        value: \"\",\n        key: Date.now(),\n      });\n    },\n    userSearch(createBy) {\n      this.queryParams.belongUser = createBy;\n      this.handleQuery();\n    },\n    optTypeSearch(type) {\n      this.queryParams.operationType = type;\n      this.handleQuery();\n    },\n    optTypeChange(e) {\n      console.log(e);\n    },\n  },\n};\n</script>\n<style>\n@media screen and (min-width: 600px) {\n  .view-dialog {\n    width: 80% !important;\n    float: right;\n  }\n}\n\n@media screen and (max-width: 599px) {\n  .view-dialog {\n    width: 100% !important;\n    float: right;\n  }\n\n  .edit-dialog {\n    width: 100% !important;\n\n    margin-bottom: 0;\n    height: 100%;\n    overflow: auto;\n  }\n\n  .el-dialog:not(.is-fullscreen) {\n    margin-top: 0 !important;\n  }\n\n  .el-form .el-col {\n    width: 100% !important;\n  }\n\n  .info-type .el-row {\n    flex-direction: column;\n  }\n\n  .info-type .el-input {\n    width: 100% !important;\n  }\n\n  .mobile-width {\n    width: 100% !important;\n  }\n}\n\n.likeTip {\n  color: rgb(247, 11, 11);\n  font-size: 12px;\n}\n\n.el-loading-mask {\n  z-index: 2001 !important;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmvBA,SACAA,UAAA,EACAC,SAAA,EACAC,SAAA,EACAC,SAAA,EACAC,YAAA,EACAC,YAAA,EACAC,cAAA,IAAAA,eAAA,EACAC,WAAA,EACAC,eAAA,EACAC,WAAA,EACAC,UAAA,QACA;AACA,SAAAC,QAAA;AACA,OAAAC,UAAA;AACA,OAAAC,QAAA;AACA,SAAAC,gBAAA;AACA,SAAAC,UAAA,EAAAC,UAAA,EAAAC,UAAA;AACA,OAAAC,KAAA;AACA;EACAC,IAAA;EACAC,UAAA;IACAR,UAAA,EAAAA,UAAA;IACAM,KAAA,EAAAA,KAAA;IACAL,QAAA,EAAAA;EACA;EACAQ,IAAA,WAAAA,KAAA;IAAA,IAAAC,KAAA;IACA,IAAAC,iBAAA,YAAAA,kBAAAC,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,IAAAJ,KAAA,CAAAK,IAAA,CAAAC,QAAA,CAAAC,OAAA,eAAAP,KAAA,CAAAK,IAAA,CAAAG,QAAA;QACAJ,QAAA,KAAAK,KAAA;QACA;MACA;MACAL,QAAA;IACA;IACA,IAAAM,kBAAA,YAAAA,mBAAAR,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,IAAAJ,KAAA,CAAAK,IAAA,CAAAC,QAAA,CAAAC,OAAA,eAAAP,KAAA,CAAAK,IAAA,CAAAM,WAAA;QACAP,QAAA,KAAAK,KAAA;QACA;MACA;MACAL,QAAA;IACA;IACA,IAAAQ,QAAA,YAAAA,SAAAV,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,KAAAJ,KAAA,CAAAK,IAAA,CAAAQ,WAAA;QACAT,QAAA,KAAAK,KAAA;MACA;QACA,WAAAK,IAAA,CAAAd,KAAA,CAAAK,IAAA,CAAAQ,WAAA;UACAT,QAAA,KAAAK,KAAA;UACA;QACA;QACAvB,eAAA;UACA2B,WAAA,EAAAb,KAAA,CAAAK,IAAA,CAAAQ,WAAA;UACAE,SAAA,EAAAf,KAAA,CAAAK,IAAA,CAAAU;QACA,GAAAC,IAAA,WAAAC,QAAA;UACA,IAAAA,QAAA,CAAAlB,IAAA;YACAK,QAAA;UACA;YACAA,QAAA,KAAAK,KAAA;UACA;QACA;MACA;IACA;IACA,IAAAS,QAAA,YAAAA,SAAAhB,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,KAAAe,IAAA,CAAAd,IAAA,CAAAe,SAAA;QACAhB,QAAA,KAAAK,KAAA;MACA,kBAAAK,IAAA,CAAAK,IAAA,CAAAd,IAAA,CAAAe,SAAA;QACAhB,QAAA,KAAAK,KAAA;QACA;MACA;MACAL,QAAA;IACA;IACA,IAAAiB,iBAAA,YAAAA,kBAAAnB,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,IAAAJ,KAAA,CAAAK,IAAA,CAAAiB,aAAA,UAAAtB,KAAA,CAAAK,IAAA,CAAAkB,QAAA;QACAnB,QAAA,KAAAK,KAAA;MACA;MACAL,QAAA;IACA;IACA,IAAAoB,YAAA,YAAAA,aAAAtB,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACAqB,OAAA,CAAAC,GAAA;MACA,KAAAP,IAAA,CAAAd,IAAA,CAAAsB,QAAA;QACAvB,QAAA,KAAAK,KAAA;QACA;MACA,WAAAN,KAAA;QACAC,QAAA;QACA;MACA,kCAAAU,IAAA,CAAAX,KAAA;QACAC,QAAA,KAAAK,KAAA;QACA;MACA;MACAL,QAAA;IACA;IACA,IAAAwB,YAAA,YAAAA,aAAA1B,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,KAAAe,IAAA,CAAAd,IAAA,CAAAwB,QAAA;QACAzB,QAAA,KAAAK,KAAA;QACA;MACA,WAAAN,KAAA;QACAC,QAAA;QACA;MACA,kCAAAU,IAAA,CAAAX,KAAA;QACAC,QAAA,KAAAK,KAAA;QACA;MACA;MACAL,QAAA;IACA;IACA;MACA0B,QAAA;MACAC,UAAA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACAC,UAAA;MACAC,SAAA;MACA;MACAC,KAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,oBAAA;MACA;MACAC,kBAAA;MACA;MACAC,iBAAA;MACA;MACAC,kBAAA;MACA;MACAC,YAAA;MACAC,YAAA;MACA;MACAC,kBAAA;MACAC,kBAAA;MACA;MACAC,eAAA;MACAC,WAAA;MACAC,WAAA;MACA;MACAC,qBAAA;MACAC,sBAAA;MACA;MACAC,oBAAA;MACAC,qBAAA;MACA;MACAC,MAAA;QACA;QACAhB,IAAA;QACA;QACAD,KAAA;QACA;QACAkB,WAAA;QACA;QACAC,aAAA;QACA;QACAC,OAAA;UAAAC,aAAA,cAAAzE,QAAA;QAAA;QACA;QACA0E,GAAA,EAAAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA;MACA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAjD,SAAA;QACAP,WAAA;QACAS,aAAA;QACAgD,WAAA;QACAC,QAAA;QACAC,QAAA;QACAC,UAAA;QACAC,aAAA;QACAC,MAAA;QACAC,OAAA;QACAC,cAAA;QACAC,WAAA;QACAC,SAAA;MACA;MACA;MACA1E,IAAA;MACA;MACA;MACA2E,KAAA;QACA1D,aAAA;UAAA2D,QAAA;UAAAC,OAAA;QAAA;QACA9D,SAAA;UAAA6D,QAAA;UAAAE,QAAA,EAAAjE,QAAA;UAAAkE,OAAA;QAAA;QACAvE,WAAA;UAAAoE,QAAA;UAAAE,QAAA,EAAAvE,QAAA;UAAAwE,OAAA;QAAA;QACAR,OAAA,GACA;UAAAK,QAAA;UAAAC,OAAA;UAAAE,OAAA;QAAA,EACA;QACAP,cAAA,GACA;UAAAI,QAAA;UAAAC,OAAA;UAAAE,OAAA;QAAA,EACA;QACAzD,QAAA,GACA;UAAAsD,QAAA;UAAAE,QAAA,EAAA3D,YAAA;UAAA4D,OAAA;QAAA,EACA;QACAC,aAAA,GACA;UAAAJ,QAAA;UAAAC,OAAA;UAAAE,OAAA;QAAA,EACA;QACAvD,QAAA,GACA;UAAAoD,QAAA;UAAAE,QAAA,EAAAvD,YAAA;UAAAwD,OAAA;QAAA,EACA;QACAE,cAAA,GACA;UAAAL,QAAA;UAAAC,OAAA;UAAAE,OAAA;QAAA,EACA;QACAG,WAAA,GACA;UAAAN,QAAA;UAAAC,OAAA;UAAAE,OAAA;QAAA,EACA;QACA5E,QAAA;UAAAgF,SAAA,EAAAvF,iBAAA;UAAAmF,OAAA;QAAA;QACAzE,WAAA;UAAA6E,SAAA,EAAA9E,kBAAA;UAAA0E,OAAA;QAAA;QACAK,KAAA;UAAAR,QAAA;UAAAC,OAAA;QAAA;QACAQ,IAAA;UAAAT,QAAA;UAAAC,OAAA;QAAA;QACAX,QAAA;UAAAU,QAAA;UAAAC,OAAA;QAAA;QACA5E,QAAA,GACA;UAAA2E,QAAA;UAAAC,OAAA;UAAAE,OAAA;QAAA,EACA;QACA7D,QAAA;UAAAiE,SAAA,EAAAnE,iBAAA;UAAA+D,OAAA;QAAA;QACAO,cAAA,GACA;UAAAV,QAAA;UAAAC,OAAA;QAAA,EACA;QACAU,WAAA,GACA;UAAAX,QAAA;UAAAC,OAAA;QAAA;MAEA;MACA;MACAW,OAAA,GACA;QAAAC,GAAA;QAAAC,KAAA;QAAAC,KAAA;QAAAC,OAAA;MAAA,GACA;QAAAH,GAAA;QAAAC,KAAA;QAAAC,KAAA;QAAAC,OAAA;MAAA,GACA;QAAAH,GAAA;QAAAC,KAAA;QAAAC,KAAA;QAAAC,OAAA;MAAA,GACA;QAAAH,GAAA;QAAAC,KAAA;QAAAC,KAAA;QAAAC,OAAA;MAAA,GACA;QAAAH,GAAA;QAAAC,KAAA;QAAAC,KAAA;QAAAC,OAAA;MAAA,GACA;QAAAH,GAAA;QAAAC,KAAA;QAAAC,KAAA;QAAAC,OAAA;MAAA,GACA;QAAAH,GAAA;QAAAC,KAAA;QAAAC,KAAA;QAAAC,OAAA;MAAA,GACA;QAAAH,GAAA;QAAAC,KAAA;QAAAC,KAAA;QAAAC,OAAA;MAAA,GACA;QAAAH,GAAA;QAAAC,KAAA;QAAAC,KAAA;QAAAC,OAAA;MAAA,GACA;QAAAH,GAAA;QAAAC,KAAA;QAAAC,KAAA;QAAAC,OAAA;MAAA,GACA;QAAAH,GAAA;QAAAC,KAAA;QAAAC,KAAA;QAAAC,OAAA;MAAA,GACA;QAAAH,GAAA;QAAAC,KAAA;QAAAC,KAAA;QAAAC,OAAA;MAAA,GACA;QAAAH,GAAA;QAAAC,KAAA;QAAAC,KAAA;QAAAC,OAAA;MAAA,GACA;QAAAH,GAAA;QAAAC,KAAA;QAAAC,KAAA;QAAAC,OAAA;MAAA,GACA;QAAAH,GAAA;QAAAC,KAAA;QAAAC,KAAA;QAAAC,OAAA;MAAA,GACA;QAAAH,GAAA;QAAAC,KAAA;QAAAC,KAAA;QAAAC,OAAA;MAAA,GACA;QAAAH,GAAA;QAAAC,KAAA;QAAAC,KAAA;QAAAC,OAAA;MAAA,GACA;QAAAH,GAAA;QAAAC,KAAA;QAAAC,KAAA;QAAAC,OAAA;MAAA,GACA;QAAAH,GAAA;QAAAC,KAAA;QAAAC,KAAA;QAAAC,OAAA;MAAA,GACA;QAAAH,GAAA;QAAAC,KAAA;QAAAC,KAAA;QAAAC,OAAA;MAAA,GACA;QAAAH,GAAA;QAAAC,KAAA;QAAAC,KAAA;QAAAC,OAAA;MAAA,GACA;QAAAH,GAAA;QAAAC,KAAA;QAAAC,KAAA;QAAAC,OAAA;MAAA;MACA;MACA;MACA;MACA;MAAA,CACA;MACAC,OAAA,EAAAzG,UAAA;MACA0G,eAAA;MACAC,SAAA;MACAC,QAAA;MACAC,IAAA;MACAC,SAAA;MACAC,SAAA;MACAC,MAAA;MACAC,SAAA,EAAAC,SAAA;MACAC,MAAA,EAAAD,SAAA;MACAE,QAAA;MACAC,MAAA,EAAAH,SAAA;MACAI,eAAA;MACAC,iBAAA;MACAC,YAAA;MACAC,YAAA;QACAC,QAAA;QACAnB,KAAA;MACA;MACAoB,gBAAA,EAAAT,SAAA;MACAU,SAAA;MACAC,aAAA,EAAAX,SAAA;MACAY,iBAAA,EAAAZ,SAAA;MACAa,YAAA,EAAAb,SAAA;MACAc,gBAAA,EAAAd,SAAA;MACAe,YAAA,EAAAf,SAAA;MACAgB,QAAA,EAAAhB,SAAA;MACAiB,SAAA,EAAAjB,SAAA;MACAkB,YAAA;MACAC,OAAA;MACAC,eAAA;MACAC,YAAA;MACAC,mBAAA;QACAC,SAAA;UACAC,IAAA;UACAC,OAAA,WAAAA,QAAAC,MAAA;YACA,IAAAC,GAAA,OAAAC,IAAA;YACA,IAAAC,KAAA,OAAAD,IAAA;YACAC,KAAA,CAAAC,OAAA,CAAAD,KAAA,CAAAE,OAAA;YACAL,MAAA,CAAAM,KAAA,UAAAH,KAAA,EAAAF,GAAA;UACA;QACA;UACAH,IAAA;UACAC,OAAA,WAAAA,QAAAC,MAAA;YACA,IAAAC,GAAA,OAAAC,IAAA;YACA,IAAAC,KAAA,OAAAD,IAAA;YACAC,KAAA,CAAAC,OAAA,CAAAD,KAAA,CAAAE,OAAA;YACAL,MAAA,CAAAM,KAAA,UAAAH,KAAA,EAAAF,GAAA;UACA;QACA;UACAH,IAAA;UACAC,OAAA,WAAAA,QAAAC,MAAA;YACA,IAAAC,GAAA,OAAAC,IAAA;YACA,IAAAC,KAAA,OAAAD,IAAA;YACAC,KAAA,CAAAC,OAAA,CAAAD,KAAA,CAAAE,OAAA;YACAL,MAAA,CAAAM,KAAA,UAAAH,KAAA,EAAAF,GAAA;UACA;QACA;MACA;MACAM,aAAA;QACAC,YAAA,WAAAA,aAAAC,IAAA;UACA,OAAAA,IAAA,CAAAJ,OAAA,KAAAH,IAAA,CAAAQ,GAAA;QACA;QACAb,SAAA,GACA;UACAC,IAAA;UACAC,OAAA,WAAAA,QAAAC,MAAA;YACAA,MAAA,CAAAM,KAAA,aAAAJ,IAAA;UACA;QACA,GACA;UACAJ,IAAA;UACAC,OAAA,WAAAA,QAAAC,MAAA;YACA,IAAAW,IAAA,OAAAT,IAAA;YACAS,IAAA,CAAAP,OAAA,CAAAO,IAAA,CAAAN,OAAA;YACAL,MAAA,CAAAM,KAAA,SAAAK,IAAA;UACA;QACA,GACA;UACAb,IAAA;UACAC,OAAA,WAAAA,QAAAC,MAAA;YACA,IAAAW,IAAA,OAAAT,IAAA;YACAS,IAAA,CAAAP,OAAA,CAAAO,IAAA,CAAAN,OAAA;YACAL,MAAA,CAAAM,KAAA,SAAAK,IAAA;UACA;QACA;MAEA;MACAC,WAAA;MAAA;MACAC,kBAAA,GACA;QAAAlD,KAAA;QAAA7F,KAAA;MAAA,GACA;QAAA6F,KAAA;QAAA7F,KAAA;MAAA,GACA;QAAA6F,KAAA;QAAA7F,KAAA;MAAA,GACA;QAAA6F,KAAA;QAAA7F,KAAA;MAAA,GACA;QAAA6F,KAAA;QAAA7F,KAAA;MAAA,GACA;QAAA6F,KAAA;QAAA7F,KAAA;MAAA,EACA;MACAgJ,WAAA;MAAA;MACAC,eAAA;IACA;EACA;EACAC,SAAA,WAAAA,UAAA;IACA5H,OAAA,CAAAC,GAAA;IACA,KAAA4H,OAAA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,MAAA;IACA,KAAAF,OAAA;IACA,KAAAG,QAAA,sBAAAzI,IAAA,WAAAC,QAAA;MACAuI,MAAA,CAAA7G,oBAAA,GAAA1B,QAAA,CAAAlB,IAAA;MACA,IAAA2J,GAAA;MACAA,GAAA,CAAAC,IAAA;QAAAC,EAAA;QAAA5D,KAAA;MAAA;MACA/E,QAAA,CAAAlB,IAAA,CAAA8J,OAAA,WAAAC,IAAA,EAAA/D,KAAA;QACA,IAAAgE,GAAA;QACAA,GAAA,CAAA/D,KAAA,GAAA8D,IAAA,CAAAE,SAAA;QACAD,GAAA,CAAAH,EAAA,GAAAE,IAAA,CAAAG,SAAA;QACAP,GAAA,CAAAC,IAAA,CAAAI,GAAA;MACA;MACA,IAAAzI,aAAA;MACAA,aAAA,CAAA0E,KAAA;MACA1E,aAAA,CAAA6F,QAAA,GAAAuC,GAAA;MACA,IAAAQ,cAAA;MACAA,cAAA,CAAAP,IAAA,CAAArI,aAAA;MACAkI,MAAA,CAAAxC,iBAAA,GAAAkD,cAAA;IACA;IACA,KAAAT,QAAA,oBAAAzI,IAAA,WAAAC,QAAA;MACA,IAAAkJ,IAAA;MACA,IAAAX,MAAA,CAAAY,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,KAAA;QACA,IAAAf,MAAA,CAAAY,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,KAAA,CAAAC,QAAA;UACAL,IAAA;QACA;QACA,IAAAX,MAAA,CAAAY,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,KAAA,CAAAC,QAAA;UACAL,IAAA;QACA;QACA,IAAAX,MAAA,CAAAY,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,KAAA,CAAAC,QAAA;UACAL,IAAA;QACA;MACA;MACAX,MAAA,CAAA5G,kBAAA,GAAA3B,QAAA,CAAAlB,IAAA;MACA,IAAA2J,GAAA;MACAA,GAAA,CAAAC,IAAA;QAAAC,EAAA;QAAA5D,KAAA;MAAA;MACA,IAAAmE,IAAA,SAAAA,IAAA;QACAT,GAAA,CAAAC,IAAA;UAAAC,EAAA;UAAA5D,KAAA;QAAA;MACA;MACA/E,QAAA,CAAAlB,IAAA,CAAA8J,OAAA,WAAAC,IAAA,EAAA/D,KAAA;QACA,IAAAgE,GAAA;QACAA,GAAA,CAAA/D,KAAA,GAAA8D,IAAA,CAAAE,SAAA;QACAD,GAAA,CAAAH,EAAA,GAAAE,IAAA,CAAAG,SAAA;QACAP,GAAA,CAAAC,IAAA,CAAAI,GAAA;MACA;MACA,IAAAhD,eAAA;MACAA,eAAA,CAAAf,KAAA;MACAe,eAAA,CAAAI,QAAA,GAAAuC,GAAA;MACA,IAAAe,gBAAA;MACAA,gBAAA,CAAAd,IAAA,CAAA5C,eAAA;MACAyC,MAAA,CAAAzC,eAAA,GAAA0D,gBAAA;IACA;IACA,KAAAhB,QAAA,mBAAAzI,IAAA,WAAAC,QAAA;MACAuI,MAAA,CAAA3G,iBAAA,GAAA5B,QAAA,CAAAlB,IAAA;IACA;IACA;IACA;IACA;IACA,KAAA0J,QAAA,aAAAzI,IAAA,WAAAC,QAAA;MACAuI,MAAA,CAAAxG,YAAA,GAAA/B,QAAA,CAAAlB,IAAA;MACA,IAAA2J,GAAA;MACAzI,QAAA,CAAAlB,IAAA,CAAA8J,OAAA,WAAAC,IAAA,EAAA/D,KAAA;QACA,IAAAgE,GAAA;QACAA,GAAA,CAAA/D,KAAA,GAAA8D,IAAA,CAAAE,SAAA;QACAD,GAAA,CAAA5J,KAAA,GAAA2J,IAAA,CAAAG,SAAA;QACAP,GAAA,CAAAC,IAAA,CAAAI,GAAA;MACA;MACAP,MAAA,CAAAzG,YAAA,GAAA2G,GAAA;IACA;IACA,KAAAD,QAAA,YAAAzI,IAAA,WAAAC,QAAA;MACAuI,MAAA,CAAAnG,WAAA,GAAApC,QAAA,CAAAlB,IAAA;MACA,IAAA2J,GAAA;MACAzI,QAAA,CAAAlB,IAAA,CAAA8J,OAAA,WAAAC,IAAA,EAAA/D,KAAA;QACA,IAAAgE,GAAA;QACAA,GAAA,CAAA/D,KAAA,GAAA8D,IAAA,CAAAE,SAAA;QACAD,GAAA,CAAA5J,KAAA,GAAA2J,IAAA,CAAAG,SAAA;QACAP,GAAA,CAAAC,IAAA,CAAAI,GAAA;MACA;MACAP,MAAA,CAAApG,WAAA,GAAAsG,GAAA;IACA;IACA,KAAAD,QAAA,YAAAzI,IAAA,WAAAC,QAAA;MACAuI,MAAA,CAAAtG,kBAAA,GAAAjC,QAAA,CAAAlB,IAAA;MACA,IAAA2J,GAAA;MACAzI,QAAA,CAAAlB,IAAA,CAAA8J,OAAA,WAAAC,IAAA,EAAA/D,KAAA;QACA,IAAAgE,GAAA;QACAA,GAAA,CAAA/D,KAAA,GAAA8D,IAAA,CAAAE,SAAA;QACAD,GAAA,CAAA5J,KAAA,GAAA2J,IAAA,CAAAG,SAAA;QACAP,GAAA,CAAAC,IAAA,CAAAI,GAAA;MACA;MACAP,MAAA,CAAAvG,kBAAA,GAAAyG,GAAA;IACA;IACA,KAAAD,QAAA,gBAAAzI,IAAA,WAAAC,QAAA;MACAuI,MAAA,CAAAjG,sBAAA,GAAAtC,QAAA,CAAAlB,IAAA;MACA,IAAA2J,GAAA;MACAzI,QAAA,CAAAlB,IAAA,CAAA8J,OAAA,WAAAC,IAAA,EAAA/D,KAAA;QACA,IAAAgE,GAAA;QACAA,GAAA,CAAA/D,KAAA,GAAA8D,IAAA,CAAAE,SAAA;QACAD,GAAA,CAAA5J,KAAA,GAAA2J,IAAA,CAAAG,SAAA;QACAP,GAAA,CAAAC,IAAA,CAAAI,GAAA;MACA;MACAP,MAAA,CAAAlG,qBAAA,GAAAoG,GAAA;IACA;IACA,KAAAD,QAAA,uBAAAzI,IAAA,WAAAC,QAAA;MACAuI,MAAA,CAAA/F,qBAAA,GAAAxC,QAAA,CAAAlB,IAAA;MACA,IAAA2J,GAAA;MACAzI,QAAA,CAAAlB,IAAA,CAAA8J,OAAA,WAAAC,IAAA,EAAA/D,KAAA;QACA,IAAAgE,GAAA;QACAA,GAAA,CAAA/D,KAAA,GAAA8D,IAAA,CAAAE,SAAA;QACAD,GAAA,CAAA5J,KAAA,GAAA2J,IAAA,CAAAG,SAAA;QACAP,GAAA,CAAAC,IAAA,CAAAI,GAAA;MACA;MACAP,MAAA,CAAAhG,oBAAA,GAAAkG,GAAA;IACA;IACA,KAAAD,QAAA,iBAAAzI,IAAA,WAAAC,QAAA;MACAuI,MAAA,CAAArG,eAAA,GAAAlC,QAAA,CAAAlB,IAAA;IACA;IAEA,IAAA2J,GAAA;IACAA,GAAA,CAAAC,IAAA;MAAAC,EAAA;MAAA5D,KAAA;IAAA;IACA0D,GAAA,CAAAC,IAAA;MAAAC,EAAA;MAAA5D,KAAA;IAAA;IACA0D,GAAA,CAAAC,IAAA;MAAAC,EAAA;MAAA5D,KAAA;IAAA;IAEA,IAAAxB,QAAA;IACAA,QAAA,CAAAwB,KAAA;IACAxB,QAAA,CAAA2C,QAAA,GAAAuC,GAAA;IACA,IAAAgB,SAAA;IACAA,SAAA,CAAAf,IAAA,CAAAnF,QAAA;IACA,KAAAyC,YAAA,GAAAyD,SAAA;IACA,IACA,KAAAN,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,KAAA,IACA,KAAAH,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,KAAA,CAAAC,QAAA,YACA;MACA,KAAAnD,SAAA;IACA;IACA,SAAAsD,SAAA;MACA,KAAA7I,QAAA;MACA,KAAAC,UAAA;IACA;IACA,IACA,KAAAqI,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,KAAA,IACA,KAAAH,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,KAAA,CAAAC,QAAA,kBACA;MACA,KAAA1C,OAAA;IACA;EACA;EACA8C,OAAA;IACAD,SAAA,WAAAA,UAAA;MACA,IAAAE,IAAA,GAAAC,SAAA,CAAAC,SAAA,CAAAC,KAAA,CACA,iJACA;MACA,OAAAH,IAAA;IACA;IACA,eACAvB,OAAA,WAAAA,QAAA;MAAA,IAAA2B,MAAA;MACA,KAAAjJ,OAAA;MACAtD,UAAA,MAAAyF,WAAA,EAAAnD,IAAA,WAAAC,QAAA;QACAgK,MAAA,CAAAzI,UAAA,GAAAvB,QAAA,CAAAiK,IAAA;QACAD,MAAA,CAAA1I,KAAA,GAAAtB,QAAA,CAAAsB,KAAA;QACA0I,MAAA,CAAAjJ,OAAA;MACA;IACA;IACAmJ,cAAA,WAAAA,eAAApH,GAAA;MACA,IAAAqH,CAAA,GAAAC,QAAA,CAAAC,aAAA;MACAF,CAAA,CAAAG,YAAA;MACAH,CAAA,CAAAG,YAAA;MACA,IAAAC,IAAA,GACA,yEACAzH,GAAA;MACAtC,OAAA,CAAAC,GAAA,CAAA8J,IAAA;MACAJ,CAAA,CAAAG,YAAA,SAAAC,IAAA;MACAJ,CAAA,CAAAK,KAAA;IACA;IACA;IACAC,oBAAA,WAAAA,qBAAA3L,IAAA;MACA,IAAAA,IAAA,CAAA6J,EAAA;QACA,KAAAzF,WAAA,CAAAG,WAAA,GAAAqC,SAAA;QACA,KAAAxC,WAAA,CAAAwH,IAAA,GAAAhF,SAAA;QACA,KAAAxC,WAAA,CAAAQ,MAAA,GAAAgC,SAAA;MACA;QACA,IAAA5G,IAAA,CAAA6J,EAAA,SAAA7J,IAAA,CAAA6J,EAAA;UACA,KAAAzF,WAAA,CAAAG,WAAA;UACA,SAAA8F,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,KAAA;YACA,SAAAH,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,KAAA,CAAAC,QAAA;cACA,KAAArG,WAAA,CAAAwH,IAAA;cACA,IAAA5L,IAAA,CAAA6J,EAAA;gBACA,KAAAzF,WAAA,CAAAQ,MAAA;cACA;gBACA,KAAAR,WAAA,CAAAQ,MAAA;cACA;YACA;YACA,SAAAyF,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,KAAA,CAAAC,QAAA;cACA,KAAArG,WAAA,CAAAwH,IAAA;cACA,IAAA5L,IAAA,CAAA6J,EAAA;gBACA,KAAAzF,WAAA,CAAAQ,MAAA;cACA;gBACA,KAAAR,WAAA,CAAAQ,MAAA;cACA;YACA;UACA;QACA;UACA,KAAAR,WAAA,CAAAG,WAAA,GAAAvE,IAAA,CAAA6J,EAAA;UACA,KAAAzF,WAAA,CAAAwH,IAAA,GAAAhF,SAAA;UACA,KAAAxC,WAAA,CAAAQ,MAAA,GAAAgC,SAAA;QACA;MACA;MACA,KAAA2C,OAAA;IACA;IACA;IACAsC,kBAAA,WAAAA,mBAAA7L,IAAA;MACA,IAAAA,IAAA,CAAA6J,EAAA;QACA,KAAAzF,WAAA,CAAA7C,aAAA,GAAAqF,SAAA;MACA;QACA,KAAAxC,WAAA,CAAA7C,aAAA,GAAAvB,IAAA,CAAA6J,EAAA;MACA;MACA,KAAAN,OAAA;IACA;IACA;IACAuC,mBAAA,WAAAA,oBAAA9L,IAAA;MACA,IAAAA,IAAA,CAAA6J,EAAA;QACA,KAAAzF,WAAA,CAAAK,QAAA,GAAAmC,SAAA;MACA;QACA,KAAAxC,WAAA,CAAAK,QAAA,GAAAzE,IAAA,CAAA6J,EAAA;MACA;MACA,KAAAN,OAAA;IACA;IACA;IACAwC,UAAA,WAAAA,WAAA3L,KAAA,EAAAJ,IAAA;MACA,KAAAI,KAAA;MACA,OAAAJ,IAAA,CAAAiG,KAAA,CAAAzF,OAAA,CAAAJ,KAAA;IACA;IACA4L,YAAA,WAAAA,aAAAC,GAAA,EAAAC,MAAA;MACA,IACAD,GAAA,CAAAzL,OAAA,MAAA4D,WAAA,CAAAQ,MAAA,YACA,KAAAR,WAAA,CAAAQ,MAAA,SACA;QACA,OAAAqH,GAAA,CAAAE,OAAA,CACA,KAAA/H,WAAA,CAAAQ,MAAA,EACA,6BAAAR,WAAA,CAAAQ,MAAA,YACA;MACA;QACA,OAAAqH,GAAA;MACA;IACA;IACA;IACAG,mBAAA,WAAAA,oBAAAH,GAAA,EAAAC,MAAA;MACA,YAAAG,eAAA,MAAAzJ,oBAAA,EAAAqJ,GAAA,CAAA1K,aAAA;IACA;IACA;IACA+K,iBAAA,WAAAA,kBAAAL,GAAA,EAAAC,MAAA;MACA,YAAAG,eAAA,MAAAxJ,kBAAA,EAAAoJ,GAAA,CAAA1H,WAAA;IACA;IACA;IACAgI,gBAAA,WAAAA,iBAAAN,GAAA,EAAAC,MAAA;MACA,YAAAG,eAAA,MAAAvJ,iBAAA,EAAAmJ,GAAA,CAAAO,UAAA;IACA;IACA;IACAC,iBAAA,WAAAA,kBAAAR,GAAA,EAAAC,MAAA;MACA,YAAAG,eAAA,MAAAtJ,kBAAA,EAAAkJ,GAAA,CAAAS,WAAA;IACA;IACA;IACAC,WAAA,WAAAA,YAAAV,GAAA,EAAAC,MAAA;MACA,YAAAU,gBAAA,MAAA3J,YAAA,EAAAgJ,GAAA,CAAAvG,KAAA;IACA;IACA;IACAmH,UAAA,WAAAA,WAAAZ,GAAA,EAAAC,MAAA;MACA,YAAAU,gBAAA,MAAAtJ,WAAA,EAAA2I,GAAA,CAAAtG,IAAA;IACA;IACA;IACAmH,iBAAA,WAAAA,kBAAAb,GAAA,EAAAC,MAAA;MACA,IAAAD,GAAA,CAAAc,WAAA;QACA,YAAAH,gBAAA,MAAAzJ,kBAAA,EAAA8I,GAAA,CAAAc,WAAA;MACA;IACA;IACA;IACAC,cAAA,WAAAA,eAAAf,GAAA,EAAAC,MAAA;MACA,YAAAU,gBAAA,MAAAxJ,eAAA,EAAA6I,GAAA,CAAA1L,QAAA;IACA;IACA;IACA0M,oBAAA,WAAAA,qBAAAhB,GAAA,EAAAC,MAAA;MACA,YAAAU,gBAAA,MAAApJ,sBAAA,EAAAyI,GAAA,CAAA1G,cAAA;IACA;IACA;IACA2H,mBAAA,WAAAA,oBAAAjB,GAAA,EAAAC,MAAA;MACA,YAAAU,gBAAA,MAAAlJ,qBAAA,EAAAuI,GAAA,CAAA3G,aAAA;IACA;IACA;IACA6H,MAAA,WAAAA,OAAA;MACA,KAAAxK,IAAA;MACA,KAAAyK,KAAA;IACA;IACAC,MAAA,WAAAA,OAAA;MACA,KAAA/G,QAAA;IACA;IACA;IACA8G,KAAA,WAAAA,MAAA;MACA,KAAA9M,IAAA;QACAU,SAAA;QACAK,SAAA;QACAP,WAAA;QACAS,aAAA;QACAgD,WAAA;QACA+I,YAAA;QACA9I,QAAA;QACA+I,IAAA;QACAC,QAAA;QACA3I,OAAA;QACA2H,UAAA;QACA9H,UAAA;QACAI,cAAA;QACAlD,QAAA;QACA8K,WAAA;QACAe,WAAA;QACA1I,WAAA;QACA2I,UAAA;QACAlI,WAAA;QACAE,KAAA;QACAC,IAAA;QACAgI,IAAA;QACAnM,QAAA;QACAoM,aAAA;QACAb,WAAA;QACAxM,QAAA;QACAE,QAAA;QACAG,WAAA;QACAiN,QAAA;QACAC,WAAA;QACAC,MAAA;QACAnJ,MAAA;QACAoJ,MAAA;MACA;MACA,KAAAC,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAA9J,WAAA,CAAAC,OAAA;MACA;MACA,KAAAD,WAAA,CAAA/C,SAAA;MACA,KAAA+C,WAAA,CAAAtD,WAAA;MACA,KAAAsD,WAAA,CAAAS,OAAA;MACA,KAAAT,WAAA,CAAAU,cAAA;MACA,KAAAV,WAAA,CAAAW,WAAA;MACA,KAAAX,WAAA,CAAAY,SAAA;MAEA,SAAAkE,WAAA;QACA,KAAA9E,WAAA,CAAAY,SAAA,QAAAoE,WAAA;MACA;QACA,KAAAhF,WAAA,MAAA8E,WAAA,SAAAE,WAAA;MACA;MACA,KAAAG,OAAA;IACA;IACA,aACA4E,UAAA,WAAAA,WAAA;MACA,KAAA9H,SAAA;MACA,KAAAjC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAjD,SAAA;QACAP,WAAA;QACAS,aAAA;QACAgD,WAAA;QACAC,QAAA;QACAC,QAAA;QACAC,UAAA;QACAE,MAAA;QACAC,OAAA;QACAC,cAAA;QACAC,WAAA;QACAC,SAAA;MACA;MACA,KAAAkE,WAAA;MACA,KAAAE,WAAA;MACA,KAAA8E,WAAA;IACA;IACA;IACAE,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAnM,GAAA,GAAAmM,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAvN,SAAA;MAAA;MACA,KAAAmB,MAAA,GAAAkM,SAAA,CAAAG,MAAA;MACA,KAAApM,QAAA,IAAAiM,SAAA,CAAAG,MAAA;MACA,KAAAjH,aAAA,GAAA8G,SAAA,IAAA9M,aAAA;MACA,KAAAiG,iBAAA,GAAA6G,SAAA,IAAA9J,WAAA;MACA,KAAAkD,YAAA,GAAA4G,SAAA,IAAAI,MAAA;MACA,KAAA/G,gBAAA,GAAA2G,SAAA,IAAA7B,UAAA;MACA,KAAA7E,YAAA,GAAA0G,SAAA,IAAAL,MAAA;IACA;IACA,aACAU,SAAA,WAAAA,UAAA;MACA;MACA;MACA;MACA,KAAAC,OAAA,CAAA/E,IAAA;QACAgF,IAAA;QACAC,KAAA;UACAC,WAAA,EAAAlI,SAAA;UACAmI,QAAA;QACA;MACA;IACA;IACAC,UAAA,WAAAA,WAAA/C,GAAA;MAAA,IAAAgD,MAAA;MACA,IAAA7N,IAAA;MACA,KAAAmF,IAAA,GAAA2I,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,CAAAnD,GAAA;MACA,KAAA1F,IAAA,CAAAhF,aAAA,QAAA6K,mBAAA,CAAAH,GAAA;MACA,KAAA1F,IAAA,CAAAhC,WAAA,QAAA+H,iBAAA,CAAAL,GAAA;MACA,KAAA1F,IAAA,CAAAiG,UAAA,QAAAD,gBAAA,CAAAN,GAAA;MACA,KAAA1F,IAAA,CAAAmG,WAAA,QAAAD,iBAAA,CAAAR,GAAA;MACA,KAAA1F,IAAA,CAAAb,KAAA,QAAAiH,WAAA,CAAAV,GAAA;MACA,KAAA1F,IAAA,CAAAZ,IAAA,QAAAkH,UAAA,CAAAZ,GAAA;MACA,KAAA1F,IAAA,CAAAwG,WAAA,QAAAD,iBAAA,CAAAb,GAAA;MACA,KAAA1F,IAAA,CAAAhG,QAAA,QAAAyM,cAAA,CAAAf,GAAA;MACA,KAAA1F,IAAA,CAAAhB,cAAA,QAAA0H,oBAAA,CAAAhB,GAAA;MACA,KAAA1F,IAAA,CAAAjB,aAAA,QAAA4H,mBAAA,CAAAjB,GAAA;MACA,IAAAA,GAAA,CAAAoD,YAAA;QACA,KAAAzC,gBAAA,MAAAzJ,kBAAA,EAAA8I,GAAA,CAAAc,WAAA;QACA;QACA;QACA,IAAAuC,IAAA,GAAAC,IAAA,CAAAC,IAAA,CAAAvD,GAAA,CAAAoD,YAAA,CAAAb,MAAA;QAEA,KAAAhI,SAAA,GAAAyF,GAAA,CAAAoD,YAAA,CAAAI,MAAA,IAAAH,IAAA;QACA,KAAA7I,SAAA,GAAAwF,GAAA,CAAAoD,YAAA,CAAAI,MAAA,EAAAH,IAAA;;QAEA;QACA;QACA;QACA;QACA;QACA;MACA;QACA,KAAA9I,SAAA;QACA,KAAAC,SAAA;MACA;MAEA,IAAAwF,GAAA,CAAA1K,aAAA,WAAA0K,GAAA,CAAArH,MAAA;QACA,KAAA8B,MAAA;QACA,KAAAhE,KAAA;MACA;QACA,KAAAgE,MAAA;QACA,KAAAhE,KAAA;MACA;MACA,IAAAgN,MAAA;QAAA3I,MAAA,EAAAkF,GAAA,CAAAjL,SAAA;QAAA0F,MAAA,OAAAA;MAAA;MACAjH,gBAAA,CAAAiQ,MAAA,EAAAzO,IAAA,WAAA0O,IAAA;QACAV,MAAA,CAAAlI,MAAA,GAAAkF,GAAA,CAAAjL,SAAA;QACA,IAAA2O,IAAA,CAAA3P,IAAA,IAAA2P,IAAA,CAAA3P,IAAA,CAAA4P,UAAA;UACAX,MAAA,CAAAtI,SAAA,GAAAgJ,IAAA,CAAA3P,IAAA,CAAA4P,UAAA;UACAX,MAAA,CAAApI,MAAA,GAAA8I,IAAA,CAAA3P,IAAA,CAAA6G,MAAA;UACA;UACA;UACA,IACA8I,IAAA,CAAA3P,IAAA,CAAA4P,UAAA,IACA,CAAAD,IAAA,CAAA3P,IAAA,CAAA6P,OAAA,IACAF,IAAA,CAAA3P,IAAA,CAAA8P,QAAA,IAAAb,MAAA,CAAA5E,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAkE,MAAA,EACA;YACA,IACAQ,MAAA,CAAA5E,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,KAAA,IACAyE,MAAA,CAAA5E,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,KAAA,CAAAC,QAAA,oBACAwB,GAAA,CAAA1K,aAAA,SACA;cACA0N,MAAA,CAAAhH,YAAA;YACA;YACAgH,MAAA,CAAAnI,QAAA;UACA,WACAmI,MAAA,CAAA5E,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,KAAA,IACAyE,MAAA,CAAA5E,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,KAAA,CAAAC,QAAA,oBACAwB,GAAA,CAAAL,IAAA,WACA;YACA,IAAAK,GAAA,CAAA1K,aAAA;cACA0N,MAAA,CAAAhH,YAAA;YACA;YACA;YACAgH,MAAA,CAAAnI,QAAA;UACA;YACAmI,MAAA,CAAAnI,QAAA;UACA;QACA;UACAmI,MAAA,CAAAnI,QAAA;UACAmI,MAAA,CAAAtI,SAAA,GAAAC,SAAA;UACAqI,MAAA,CAAApI,MAAA,GAAAD,SAAA;QACA;;QAEA;QACA;QACA;QACA;QACA;QACA;;QAEAqI,MAAA,CAAA3I,QAAA;MACA;MACAlH,WAAA;QACA0B,WAAA,EAAAmL,GAAA,CAAAnL,WAAA;QACAE,SAAA,EAAAiL,GAAA,CAAAjL;MACA,GAAAC,IAAA,WAAA0O,IAAA;QACA;QACA,IAAAA,IAAA,CAAA3P,IAAA,IAAA2P,IAAA,CAAA3P,IAAA,CAAAwO,MAAA;UACAS,MAAA,CAAArH,QAAA,GAAA+H,IAAA,CAAA3P,IAAA;UACAoB,IAAA,CAAA2O,SAAA,GAAAJ,IAAA,CAAA3P,IAAA,CAAAwO,MAAA;QACA;UACAS,MAAA,CAAArH,QAAA,GAAAhB,SAAA;UACAxF,IAAA,CAAA2O,SAAA,GAAAnJ,SAAA;QACA;MACA;IACA;IACA,aACAoJ,UAAA,WAAAA,WAAA/D,GAAA;MAAA,IAAAgE,MAAA;MACA,IAAAhO,OAAA,QAAAiO,QAAA;QACAC,IAAA;QACA/H,IAAA;QACAgI,OAAA;QACAC,UAAA;MACA;MACAhR,UAAA,CAAA4M,GAAA,EACAhL,IAAA,WAAA0O,IAAA;QACA1N,OAAA,CAAAqO,KAAA;QACAL,MAAA,CAAAM,UAAA,CAAAZ,IAAA,CAAAa,GAAA;QACAP,MAAA,CAAAtB,OAAA,CAAA8B,EAAA;MACA,GACAC,KAAA,WAAAC,CAAA;QACA1O,OAAA,CAAAqO,KAAA;MACA;IACA;IACA,aACAM,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,IAAAzP,IAAA;MACA;MACA;MACA;MACA;MACA;MACA,IAAA0P,OAAA,GACA,KAAAzG,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,KAAA,KACA,KAAAH,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,KAAA,CAAAC,QAAA,cACA,KAAAJ,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,KAAA,CAAAC,QAAA;MAEA,IAAAqG,OAAA,SAAArJ,YAAA,SAAA4C,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAkE,MAAA;QACA,KAAAsC,QAAA;QACA;MACA;MAEA,SAAAxJ,aAAA;QACA,IAAAuJ,OAAA,SAAAnJ,YAAA;UACA,KAAAoJ,QAAA;UACA;QACA;MACA;MACA,SAAAvJ,iBAAA,SAAAsJ,OAAA;QACA,SAAApJ,gBAAA;UACA,KAAAqJ,QAAA;UACA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,KAAA3D,KAAA;MACA,IAAApM,SAAA,QAAAkB,GAAA;MACAtD,SAAA,CAAAoC,SAAA,EAAAC,IAAA,WAAAC,QAAA;QACA2P,MAAA,CAAAvQ,IAAA,GAAAY,QAAA,CAAAlB,IAAA;QACA,IAAA6Q,MAAA,CAAAvQ,IAAA,CAAAoF,KAAA,EAAAmL,MAAA,CAAAvQ,IAAA,CAAAoF,KAAA,GAAAmL,MAAA,CAAAvQ,IAAA,CAAAoF,KAAA,CAAAsL,KAAA,WACAH,MAAA,CAAAvQ,IAAA,CAAAoF,KAAA;QACA,IAAAmL,MAAA,CAAAvQ,IAAA,CAAAyM,WAAA,EACA8D,MAAA,CAAAvQ,IAAA,CAAAyM,WAAA,GAAA8D,MAAA,CAAAvQ,IAAA,CAAAyM,WAAA,CAAAiE,KAAA,WACAH,MAAA,CAAAvQ,IAAA,CAAAyM,WAAA;QACA,IAAA8D,MAAA,CAAAvQ,IAAA,CAAAC,QAAA,EACAsQ,MAAA,CAAAvQ,IAAA,CAAAC,QAAA,GAAAsQ,MAAA,CAAAvQ,IAAA,CAAAC,QAAA,CAAAyQ,KAAA,WACAH,MAAA,CAAAvQ,IAAA,CAAAC,QAAA;QACA,IAAAsQ,MAAA,CAAAvQ,IAAA,CAAAqF,IAAA,EAAAkL,MAAA,CAAAvQ,IAAA,CAAAqF,IAAA,GAAAkL,MAAA,CAAAvQ,IAAA,CAAAqF,IAAA,CAAAqL,KAAA,WACAH,MAAA,CAAAvQ,IAAA,CAAAqF,IAAA;QAEA,IAAAkL,MAAA,CAAAvQ,IAAA,CAAAyE,WAAA;UACA3D,IAAA,CAAA0G,YAAA;UACA,IAAAmJ,KAAA,GAAAJ,MAAA,CAAAvQ,IAAA,CAAAyE,WAAA,CAAAiM,KAAA;UACAC,KAAA,CAAAnH,OAAA,WAAA6G,CAAA;YACAvP,IAAA,CAAA0G,YAAA,CAAA8B,IAAA;cACAxJ,KAAA,EAAAuQ,CAAA;cACA5K,GAAA,EAAAyC,IAAA,CAAAQ,GAAA;YACA;UACA;UACA5H,IAAA,CAAAd,IAAA,CAAAyE,WAAA,GAAA3D,IAAA,CAAA0G,YAAA,IAAA1H,KAAA;UACAgB,IAAA,CAAA0G,YAAA,CAAA2H,MAAA;UACA;QACA,OAAAoB,MAAA,CAAA/I,YAAA;QAEA+I,MAAA,CAAAxJ,gBAAA,GAAAnG,QAAA,CAAAlB,IAAA,CAAAuB,aAAA;QAEAsP,MAAA,CAAAlO,IAAA;QACAkO,MAAA,CAAAnO,KAAA;QACA,IAAAwO,SAAA,GAAAhQ,QAAA,CAAAlB,IAAA,CAAAwE,QAAA;QACA,IAAA0M,SAAA,CAAA1C,MAAA;UACA,IAAA3J,OAAA,GAAAqM,SAAA,CAAAF,KAAA;UACA,IAAAG,KAAA;UACA;UACA,IAAAtM,OAAA,CAAA2J,MAAA,MAAA2C,KAAA,CAAAvH,IAAA,CAAAhK,UAAA,CAAAiF,OAAA,KAAAuM,IAAA;UACA;UACA,IAAAvM,OAAA,CAAA2J,MAAA,MACA2C,KAAA,CAAAvH,IAAA,CAAAhK,UAAA,CAAAiF,OAAA,KAAAA,OAAA,KAAAuM,IAAA;UACA;UACA,IAAAvM,OAAA,CAAA2J,MAAA,MACA2C,KAAA,CAAAvH,IAAA,CAAAhK,UAAA,CAAAiF,OAAA,KAAAA,OAAA,KAAAA,OAAA,KAAAuM,IAAA;UAEAP,MAAA,CAAAzK,eAAA,GAAA+K,KAAA;QACA;MACA;IACA;IACA,WACAE,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAnM,QAAA,WAAAoM,KAAA;QACA,IAAAA,KAAA;UACA,IAAAF,MAAA,CAAAhR,IAAA,CAAAC,QAAA,CAAAC,OAAA,cAAA8Q,MAAA,CAAAhR,IAAA,CAAAG,QAAA;YACA,IAAAgR,QAAA;YACA,KAAAA,QAAA,CAAA1Q,IAAA,CAAAuQ,MAAA,CAAAhR,IAAA,CAAAG,QAAA;cACA6Q,MAAA,CAAAI,QAAA,CAAAC,KAAA;cACA;YACA;UACA;UACA,IAAAL,MAAA,CAAAhR,IAAA,CAAAiB,aAAA;YACA+P,MAAA,CAAAhR,IAAA,CAAAkB,QAAA;YACA8P,MAAA,CAAAhR,IAAA,CAAAsN,aAAA;UACA;UACA,IAAAgE,OAAA,GAAA1C,IAAA,CAAAE,SAAA,CAAAkC,MAAA,CAAAhR,IAAA;UACA,IAAAuR,QAAA,GAAA3C,IAAA,CAAAC,KAAA,CAAAyC,OAAA;UACA,IAAAC,QAAA,CAAAnM,KAAA,IAAAmM,QAAA,CAAAnM,KAAA,CAAA8I,MAAA,MACAqD,QAAA,CAAAnM,KAAA,GAAAmM,QAAA,CAAAnM,KAAA,CAAAoM,IAAA,WACAD,QAAA,CAAAnM,KAAA,GAAAkB,SAAA;UACA,IAAAiL,QAAA,CAAA9E,WAAA,IAAA8E,QAAA,CAAA9E,WAAA,CAAAyB,MAAA,MACAqD,QAAA,CAAA9E,WAAA,GAAA8E,QAAA,CAAA9E,WAAA,CAAA+E,IAAA,WACAD,QAAA,CAAA9E,WAAA,GAAAnG,SAAA;UACA,IAAAiL,QAAA,CAAAtR,QAAA,IAAAsR,QAAA,CAAAtR,QAAA,CAAAiO,MAAA,MACAqD,QAAA,CAAAtR,QAAA,GAAAsR,QAAA,CAAAtR,QAAA,CAAAuR,IAAA,WACAD,QAAA,CAAAtR,QAAA,GAAAqG,SAAA;UACA,IAAAiL,QAAA,CAAAlM,IAAA,IAAAkM,QAAA,CAAAlM,IAAA,CAAA6I,MAAA,MACAqD,QAAA,CAAAlM,IAAA,GAAAkM,QAAA,CAAAlM,IAAA,CAAAmM,IAAA,WACAD,QAAA,CAAAlM,IAAA,GAAAiB,SAAA;;UAEA;UACA,IAAA0K,MAAA,CAAAxJ,YAAA,CAAA0G,MAAA;YACA,IAAAyC,KAAA,OAAAc,KAAA;YACAT,MAAA,CAAAxJ,YAAA,CAAAgC,OAAA,WAAA6G,CAAA;cACAM,KAAA,CAAArH,IAAA,CAAA+G,CAAA,CAAAvQ,KAAA;YACA;YACAyR,QAAA,CAAA9M,WAAA,UAAAkM,KAAA,CAAAa,IAAA;UACA;UAEA,IAAAD,QAAA,CAAA7Q,SAAA;YACAjC,YAAA,CAAA8S,QAAA,EAAA5Q,IAAA,WAAAC,QAAA;cACAoQ,MAAA,CAAAf,UAAA;cAEA,IAAAe,MAAA,CAAAjK,gBAAA,SAAAwK,QAAA,CAAAtQ,aAAA;gBACAG,OAAA,CAAAC,GAAA;cACA;cAEA2P,MAAA,CAAA3O,IAAA;cACA2O,MAAA,CAAA/H,OAAA;YACA;UACA;YACAzK,SAAA,CAAA+S,QAAA,EAAA5Q,IAAA,WAAAC,QAAA;cACAoQ,MAAA,CAAAf,UAAA;cACAe,MAAA,CAAA3O,IAAA;cACA2O,MAAA,CAAA/H,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACAyI,YAAA,WAAAA,aAAA/F,GAAA;MAAA,IAAAgG,MAAA;MACA,IAAAC,UAAA,GAAAjG,GAAA,CAAAjL,SAAA,SAAAkB,GAAA;MACA,KAAAiQ,QAAA,CACA,mBAAAD,UAAA,aACA,MACA;QACAE,iBAAA;QACAC,gBAAA;QACAjI,IAAA;MACA,CACA,EACAnJ,IAAA;QACA,OAAApC,SAAA,CAAAqT,UAAA;MACA,GACAjR,IAAA;QACAgR,MAAA,CAAA1I,OAAA;QACA0I,MAAA,CAAA1B,UAAA;MACA;IACA;IACA+B,WAAA,WAAAA,YAAA;MACA,KAAAhQ,UAAA;IACA;IACAiQ,UAAA,WAAAA,WAAA;MACA,KAAAhQ,SAAA;IACA;IACA,aACAiQ,YAAA,WAAAA,aAAApI,IAAA;MAAA,IAAAqI,MAAA;MACA,IAAAC,UAAA;MACA,IAAAtR,IAAA;MACA,KAAAgD,WAAA,CAAAQ,MAAA,GAAAwF,IAAA;MACA,IAAAuI,GAAA;MACA,KAAA7M,OAAA,CAAAgE,OAAA,WAAAyE,IAAA;QACA,IAAAA,IAAA,CAAArI,OAAA;UACAyM,GAAA,CAAA/I,IAAA,CAAA2E,IAAA,CAAAtI,KAAA;QACA;MACA;MACA,KAAA7B,WAAA,CAAA4J,MAAA,GAAA2E,GAAA,CAAAb,IAAA;MACA,IAAA1N,WAAA,QAAAA,WAAA;MACA,KAAA+N,QAAA,CACA,oBACA/H,IAAA,uBACA,QACA,MACA;QACAgI,iBAAA;QACAC,gBAAA;QACAjI,IAAA;MACA,CACA,EACAnJ,IAAA;QACAyR,UAAA,GAAAtR,IAAA,CAAA8O,QAAA;UACAC,IAAA;UAAA;UACA/H,IAAA;UAAA;UACAgI,OAAA;UAAA;UACAC,UAAA;UAAA;UACAuC,MAAA,EAAAtH,QAAA,CAAAuH,aAAA;QACA;QACAvH,QAAA,CAAAwH,eAAA,CAAAC,KAAA,CAAAC,SAAA;QACA,OAAAhU,YAAA,CAAAoF,WAAA;MACA,GACAnD,IAAA,WAAAC,QAAA;QACAuR,MAAA,CAAAQ,QAAA,CAAA/R,QAAA,CAAAsP,GAAA;QACAiC,MAAA,CAAAnQ,UAAA;QACAoQ,UAAA,CAAApC,KAAA;QACAhF,QAAA,CAAAwH,eAAA,CAAAC,KAAA,CAAAC,SAAA;MACA,GACAtC,KAAA;QACA+B,MAAA,CAAAnQ,UAAA;QACAoQ,UAAA,CAAApC,KAAA;QACAhF,QAAA,CAAAwH,eAAA,CAAAC,KAAA,CAAAC,SAAA;MACA;IACA;IACA,aACAE,YAAA,WAAAA,aAAA;MACA,KAAAvP,MAAA,CAAAjB,KAAA;MACA,KAAAiB,MAAA,CAAAhB,IAAA;IACA;IACA,aACA1D,cAAA,WAAAA,eAAA;MAAA,IAAAkU,OAAA;MACAlU,eAAA,GAAAgC,IAAA,WAAAC,QAAA;QACAiS,OAAA,CAAAF,QAAA,CAAA/R,QAAA,CAAAsP,GAAA;MACA;IACA;IACA4C,WAAA,WAAAA,YAAA;MACA,KAAAH,QAAA;IACA;IACAI,WAAA,WAAAA,YAAA;MACA,KAAAJ,QAAA;IACA;IACA;IACAK,wBAAA,WAAAA,yBAAAC,KAAA,EAAAC,IAAA,EAAAC,QAAA;MACA,KAAA9P,MAAA,CAAAC,WAAA;IACA;IACA;IACA8P,iBAAA,WAAAA,kBAAAxS,QAAA,EAAAsS,IAAA,EAAAC,QAAA;MACA,KAAA9P,MAAA,CAAAhB,IAAA;MACA,KAAAgB,MAAA,CAAAC,WAAA;MACA,KAAA2N,KAAA,CAAA5N,MAAA,CAAAgQ,UAAA;MACA,KAAAC,MAAA,CAAA1S,QAAA,CAAAsP,GAAA;QAAAqD,wBAAA;MAAA;MACA,KAAAtK,OAAA;IACA;IACA;IACAuK,cAAA,WAAAA,eAAA;MACA,KAAAvC,KAAA,CAAA5N,MAAA,CAAAoQ,MAAA;IACA;IACAC,YAAA,WAAAA,aAAA5T,KAAA;MACA,KAAAA,KAAA,IAAAA,KAAA,CAAAoO,MAAA;QACA,KAAApI,eAAA;QACA,KAAA9F,IAAA,CAAAkE,QAAA,GAAAoC,SAAA;QACA,KAAAtG,IAAA,CAAAkN,QAAA,GAAA5G,SAAA;QACA;MACA;MACA,KAAAR,eAAA,GAAAhG,KAAA;MACA,IAAA6T,GAAA;MACA7T,KAAA,CAAA0J,OAAA,WAAAyE,IAAA;QACA0F,GAAA,IAAAtU,UAAA,CAAA4O,IAAA;MACA;MACA,IAAA0F,GAAA,CAAAzF,MAAA;QACAyF,GAAA,GAAAA,GAAA,CAAAC,SAAA,IAAAD,GAAA,CAAAzF,MAAA;QACA,KAAAlO,IAAA,CAAAkN,QAAA,QAAAnD,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAA/F,QAAA;QACA,KAAAlE,IAAA,CAAAkE,QAAA,GAAAyP,GAAA;MACA;QACA,KAAA3T,IAAA,CAAAkE,QAAA,GAAAoC,SAAA;QACA,KAAAtG,IAAA,CAAAkN,QAAA,GAAA5G,SAAA;MACA;IACA;IACAuN,qBAAA,WAAAA,sBAAA/T,KAAA;MACA,KAAAiG,SAAA,GAAAjG,KAAA;MACA,IAAA6T,GAAA;MACA7T,KAAA,CAAA0J,OAAA,WAAAyE,IAAA;QACA0F,GAAA,IAAAtU,UAAA,CAAA4O,IAAA;MACA;MACA,IAAA0F,GAAA,CAAAzF,MAAA;QACAyF,GAAA,GAAAA,GAAA,CAAAC,SAAA,IAAAD,GAAA,CAAAzF,MAAA;QACA,KAAApK,WAAA,CAAAI,QAAA,GAAAyP,GAAA;MACA;QACA,KAAA7P,WAAA,CAAAI,QAAA,GAAAoC,SAAA;MACA;IACA;IACAwN,WAAA,WAAAA,YAAAhK,IAAA;MACA,KAAAhG,WAAA,CAAAQ,MAAA,GAAAwF,IAAA;MACA,IAAAiK,UAAA;MACA;MACAA,UAAA,CAAAzK,IAAA;QAAA0K,KAAA;QAAAC,WAAA;MAAA;MACA,KAAAzO,OAAA,CAAAgE,OAAA,WAAAyE,IAAA;QACA,IAAAA,IAAA,CAAArI,OAAA;UACAmO,UAAA,CAAAzK,IAAA;YAAA0K,KAAA,EAAA/F,IAAA,CAAAxI,GAAA;YAAAwO,WAAA,EAAAhG,IAAA,CAAAtI;UAAA;QACA;MACA;MACA/G,WAAA,MAAAkF,WAAA,EAAAnD,IAAA,WAAAC,QAAA;QACAsT,OAAA;UACAC,SAAA,EAAAvT,QAAA,CAAAlB,IAAA;UACAoK,IAAA;UACAiK,UAAA,EAAAA,UAAA;UACAK,MAAA;UACAC,YAAA;UACAC,eAAA,EACA;UACAC,SAAA;UACA9B,KAAA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACA;IACA+B,aAAA,WAAAA,cAAA1M,IAAA,EAAA2M,OAAA;MACA,KAAAA,OAAA,SAAA3M,IAAA;MACA;MACA,OAAAA,IAAA,GAAAA,IAAA,CAAA+D,OAAA,KAAA6I,MAAA,CAAAD,OAAA,gCAAAE,MAAA,CAAAF,OAAA,gBAAA3M,IAAA;IACA;IACA8M,aAAA,WAAAA,cAAAZ,KAAA,EAAAlM,IAAA;MACA,SAAAc,WAAA,mBAAAE,WAAA,SAAAC,eAAA,CAAAoB,QAAA,CAAA6J,KAAA;QACA,YAAAQ,aAAA,CAAA1M,IAAA,OAAAgB,WAAA;MACA;MACA,SAAAF,WAAA,KAAAoL,KAAA,SAAAlL,WAAA;QACA,YAAA0L,aAAA,CAAA1M,IAAA,OAAAgB,WAAA;MACA;MACA,OAAAhB,IAAA;IACA;IACA+M,YAAA,WAAAA,aAAAnP,KAAA;MACA,IAAAA,KAAA;QACA,KAAA8B,YAAA,CAAA2H,MAAA,CAAAzJ,KAAA;MACA;IACA;IACAoP,SAAA,WAAAA,UAAA;MACA,KAAAtN,YAAA,CAAA8B,IAAA;QACAxJ,KAAA;QACA2F,GAAA,EAAAyC,IAAA,CAAAQ,GAAA;MACA;IACA;IACAqM,UAAA,WAAAA,WAAAC,QAAA;MACA,KAAAlR,WAAA,CAAAM,UAAA,GAAA4Q,QAAA;MACA,KAAApH,WAAA;IACA;IACAqH,aAAA,WAAAA,cAAAnL,IAAA;MACA,KAAAhG,WAAA,CAAA7C,aAAA,GAAA6I,IAAA;MACA,KAAA8D,WAAA;IACA;IACAsH,aAAA,WAAAA,cAAA7E,CAAA;MACAjP,OAAA,CAAAC,GAAA,CAAAgP,CAAA;IACA;EACA;AACA", "ignoreList": []}]}