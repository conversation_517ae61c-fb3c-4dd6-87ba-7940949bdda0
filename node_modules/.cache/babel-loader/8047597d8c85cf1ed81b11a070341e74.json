{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/api/system/config.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/api/system/config.js", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QgZnJvbSAnQC91dGlscy9yZXF1ZXN0JzsKCi8vIOafpeivouWPguaVsOWIl+ihqApleHBvcnQgZnVuY3Rpb24gbGlzdENvbmZpZyhxdWVyeSkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9zeXN0ZW0vY29uZmlnL2xpc3QnLAogICAgbWV0aG9kOiAnZ2V0JywKICAgIHBhcmFtczogcXVlcnkKICB9KTsKfQoKLy8g5p+l6K+i5Y+C5pWw6K+m57uGCmV4cG9ydCBmdW5jdGlvbiBnZXRDb25maWcoY29uZmlnSWQpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvc3lzdGVtL2NvbmZpZy8nICsgY29uZmlnSWQsCiAgICBtZXRob2Q6ICdnZXQnCiAgfSk7Cn0KCi8vIOagueaNruWPguaVsOmUruWQjeafpeivouWPguaVsOWAvApleHBvcnQgZnVuY3Rpb24gZ2V0Q29uZmlnS2V5KGNvbmZpZ0tleSkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9zeXN0ZW0vY29uZmlnL2NvbmZpZ0tleS8nICsgY29uZmlnS2V5LAogICAgbWV0aG9kOiAnZ2V0JwogIH0pOwp9CgovLyDmlrDlop7lj4LmlbDphY3nva4KZXhwb3J0IGZ1bmN0aW9uIGFkZENvbmZpZyhkYXRhKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL3N5c3RlbS9jb25maWcnLAogICAgbWV0aG9kOiAncG9zdCcsCiAgICBkYXRhOiBkYXRhCiAgfSk7Cn0KCi8vIOS/ruaUueWPguaVsOmFjee9rgpleHBvcnQgZnVuY3Rpb24gdXBkYXRlQ29uZmlnKGRhdGEpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvc3lzdGVtL2NvbmZpZycsCiAgICBtZXRob2Q6ICdwdXQnLAogICAgZGF0YTogZGF0YQogIH0pOwp9CgovLyDliKDpmaTlj4LmlbDphY3nva4KZXhwb3J0IGZ1bmN0aW9uIGRlbENvbmZpZyhjb25maWdJZCkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9zeXN0ZW0vY29uZmlnLycgKyBjb25maWdJZCwKICAgIG1ldGhvZDogJ2RlbGV0ZScKICB9KTsKfQoKLy8g5riF55CG5Y+C5pWw57yT5a2YCmV4cG9ydCBmdW5jdGlvbiBjbGVhckNhY2hlKCkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9zeXN0ZW0vY29uZmlnL2NsZWFyQ2FjaGUnLAogICAgbWV0aG9kOiAnZGVsZXRlJwogIH0pOwp9CgovLyDlr7zlh7rlj4LmlbAKZXhwb3J0IGZ1bmN0aW9uIGV4cG9ydENvbmZpZyhxdWVyeSkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9zeXN0ZW0vY29uZmlnL2V4cG9ydCcsCiAgICBtZXRob2Q6ICdnZXQnLAogICAgcGFyYW1zOiBxdWVyeQogIH0pOwp9"}, {"version": 3, "names": ["request", "listConfig", "query", "url", "method", "params", "getConfig", "configId", "getConfigKey", "config<PERSON><PERSON>", "addConfig", "data", "updateConfig", "delConfig", "clearCache", "exportConfig"], "sources": ["/Users/<USER>/develop/code/project/haijia/adminweb/src/api/system/config.js"], "sourcesContent": ["import request from '@/utils/request'\n\n// 查询参数列表\nexport function listConfig(query) {\n  return request({\n    url: '/system/config/list',\n    method: 'get',\n    params: query\n  })\n}\n\n// 查询参数详细\nexport function getConfig(configId) {\n  return request({\n    url: '/system/config/' + configId,\n    method: 'get'\n  })\n}\n\n// 根据参数键名查询参数值\nexport function getConfigKey(configKey) {\n  return request({\n    url: '/system/config/configKey/' + configKey,\n    method: 'get'\n  })\n}\n\n// 新增参数配置\nexport function addConfig(data) {\n  return request({\n    url: '/system/config',\n    method: 'post',\n    data: data\n  })\n}\n\n// 修改参数配置\nexport function updateConfig(data) {\n  return request({\n    url: '/system/config',\n    method: 'put',\n    data: data\n  })\n}\n\n// 删除参数配置\nexport function delConfig(configId) {\n  return request({\n    url: '/system/config/' + configId,\n    method: 'delete'\n  })\n}\n\n// 清理参数缓存\nexport function clearCache() {\n  return request({\n    url: '/system/config/clearCache',\n    method: 'delete'\n  })\n}\n\n// 导出参数\nexport function exportConfig(query) {\n  return request({\n    url: '/system/config/export',\n    method: 'get',\n    params: query\n  })\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;;AAErC;AACA,OAAO,SAASC,UAAUA,CAACC,KAAK,EAAE;EAChC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,qBAAqB;IAC1BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEH;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASI,SAASA,CAACC,QAAQ,EAAE;EAClC,OAAOP,OAAO,CAAC;IACbG,GAAG,EAAE,iBAAiB,GAAGI,QAAQ;IACjCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASI,YAAYA,CAACC,SAAS,EAAE;EACtC,OAAOT,OAAO,CAAC;IACbG,GAAG,EAAE,2BAA2B,GAAGM,SAAS;IAC5CL,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASM,SAASA,CAACC,IAAI,EAAE;EAC9B,OAAOX,OAAO,CAAC;IACbG,GAAG,EAAE,gBAAgB;IACrBC,MAAM,EAAE,MAAM;IACdO,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASC,YAAYA,CAACD,IAAI,EAAE;EACjC,OAAOX,OAAO,CAAC;IACbG,GAAG,EAAE,gBAAgB;IACrBC,MAAM,EAAE,KAAK;IACbO,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASE,SAASA,CAACN,QAAQ,EAAE;EAClC,OAAOP,OAAO,CAAC;IACbG,GAAG,EAAE,iBAAiB,GAAGI,QAAQ;IACjCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASU,UAAUA,CAAA,EAAG;EAC3B,OAAOd,OAAO,CAAC;IACbG,GAAG,EAAE,2BAA2B;IAChCC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASW,YAAYA,CAACb,KAAK,EAAE;EAClC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEH;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}