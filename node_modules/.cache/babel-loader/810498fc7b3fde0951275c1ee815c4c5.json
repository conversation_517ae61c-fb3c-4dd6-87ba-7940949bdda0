{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Process/components/nodePanel/gateway.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Process/components/nodePanel/gateway.vue", "mtime": 1641277326000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBtaXhpblBhbmVsIGZyb20gJy4uLy4uL2NvbW1vbi9taXhpblBhbmVsJzsKaW1wb3J0IG1peGluRXhlY3V0aW9uTGlzdGVuZXIgZnJvbSAnLi4vLi4vY29tbW9uL21peGluRXhlY3V0aW9uTGlzdGVuZXInOwppbXBvcnQgeyBjb21tb25QYXJzZSB9IGZyb20gJy4uLy4uL2NvbW1vbi9wYXJzZUVsZW1lbnQnOwpleHBvcnQgZGVmYXVsdCB7CiAgbWl4aW5zOiBbbWl4aW5QYW5lbCwgbWl4aW5FeGVjdXRpb25MaXN0ZW5lcl0sCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGZvcm1EYXRhOiB7fQogICAgfTsKICB9LAogIGNvbXB1dGVkOiB7CiAgICBmb3JtQ29uZmlnOiBmdW5jdGlvbiBmb3JtQ29uZmlnKCkgewogICAgICByZXR1cm4gewogICAgICAgIGlubGluZTogZmFsc2UsCiAgICAgICAgaXRlbTogW3sKICAgICAgICAgIHhUeXBlOiAnaW5wdXQnLAogICAgICAgICAgbmFtZTogJ2lkJywKICAgICAgICAgIGxhYmVsOiAn6IqC54K5IGlkJywKICAgICAgICAgIHJ1bGVzOiBbewogICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgICAgbWVzc2FnZTogJ0lkIOS4jeiDveS4uuepuicKICAgICAgICAgIH1dCiAgICAgICAgfSwgewogICAgICAgICAgeFR5cGU6ICdpbnB1dCcsCiAgICAgICAgICBuYW1lOiAnbmFtZScsCiAgICAgICAgICBsYWJlbDogJ+iKgueCueWQjeensCcKICAgICAgICB9LCB7CiAgICAgICAgICB4VHlwZTogJ2lucHV0JywKICAgICAgICAgIG5hbWU6ICdkb2N1bWVudGF0aW9uJywKICAgICAgICAgIGxhYmVsOiAn6IqC54K55o+P6L+wJwogICAgICAgIH0sIHsKICAgICAgICAgIHhUeXBlOiAnc2xvdCcsCiAgICAgICAgICBuYW1lOiAnZXhlY3V0aW9uTGlzdGVuZXInLAogICAgICAgICAgbGFiZWw6ICfmiafooYznm5HlkKzlmagnCiAgICAgICAgfSwgewogICAgICAgICAgeFR5cGU6ICdzd2l0Y2gnLAogICAgICAgICAgbmFtZTogJ2FzeW5jJywKICAgICAgICAgIGxhYmVsOiAn5byC5q2lJywKICAgICAgICAgIGFjdGl2ZVRleHQ6ICfmmK8nLAogICAgICAgICAgaW5hY3RpdmVUZXh0OiAn5ZCmJwogICAgICAgIH1dCiAgICAgIH07CiAgICB9CiAgfSwKICB3YXRjaDogewogICAgJ2Zvcm1EYXRhLmFzeW5jJzogZnVuY3Rpb24gZm9ybURhdGFBc3luYyh2YWwpIHsKICAgICAgaWYgKHZhbCA9PT0gJycpIHZhbCA9IG51bGw7CiAgICAgIHRoaXMudXBkYXRlUHJvcGVydGllcyh7CiAgICAgICAgJ2Zsb3dhYmxlOmFzeW5jJzogdmFsCiAgICAgIH0pOwogICAgfQogIH0sCiAgY3JlYXRlZDogZnVuY3Rpb24gY3JlYXRlZCgpIHsKICAgIHRoaXMuZm9ybURhdGEgPSBjb21tb25QYXJzZSh0aGlzLmVsZW1lbnQpOwogIH0KfTs="}, {"version": 3, "names": ["mixinPanel", "mixinExecutionListener", "commonParse", "mixins", "data", "formData", "computed", "formConfig", "inline", "item", "xType", "name", "label", "rules", "required", "message", "activeText", "inactiveText", "watch", "formDataAsync", "val", "updateProperties", "created", "element"], "sources": ["src/components/Process/components/nodePanel/gateway.vue"], "sourcesContent": ["<template>\n  <div>\n    <x-form ref=\"xForm\" v-model=\"formData\" :config=\"formConfig\">\n      <template #executionListener>\n        <el-badge :value=\"executionListenerLength\">\n          <el-button size=\"small\" @click=\"dialogName = 'executionListenerDialog'\">编辑</el-button>\n        </el-badge>\n      </template>\n    </x-form>\n    <executionListenerDialog\n      v-if=\"dialogName === 'executionListenerDialog'\"\n      :element=\"element\"\n      :modeler=\"modeler\"\n      @close=\"finishExecutionListener\"\n    />\n  </div>\n</template>\n\n<script>\nimport mixinPanel from '../../common/mixinPanel'\nimport mixinExecutionListener from '../../common/mixinExecutionListener'\nimport { commonParse } from '../../common/parseElement'\nexport default {\n  mixins: [mixinPanel, mixinExecutionListener],\n  data() {\n    return {\n      formData: {}\n    }\n  },\n  computed: {\n    formConfig() {\n      return {\n        inline: false,\n        item: [\n          {\n            xType: 'input',\n            name: 'id',\n            label: '节点 id',\n            rules: [{ required: true, message: 'Id 不能为空' }]\n          },\n          {\n            xType: 'input',\n            name: 'name',\n            label: '节点名称'\n          },\n          {\n            xType: 'input',\n            name: 'documentation',\n            label: '节点描述'\n          },\n          {\n            xType: 'slot',\n            name: 'executionListener',\n            label: '执行监听器'\n          },\n          {\n            xType: 'switch',\n            name: 'async',\n            label: '异步',\n            activeText: '是',\n            inactiveText: '否'\n          }\n        ]\n      }\n    }\n  },\n  watch: {\n    'formData.async': function(val) {\n      if (val === '') val = null\n      this.updateProperties({ 'flowable:async': val })\n    }\n  },\n  created() {\n    this.formData = commonParse(this.element)\n  }\n}\n</script>\n\n<style>\n\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAmBA,OAAAA,UAAA;AACA,OAAAC,sBAAA;AACA,SAAAC,WAAA;AACA;EACAC,MAAA,GAAAH,UAAA,EAAAC,sBAAA;EACAG,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;IACA;EACA;EACAC,QAAA;IACAC,UAAA,WAAAA,WAAA;MACA;QACAC,MAAA;QACAC,IAAA,GACA;UACAC,KAAA;UACAC,IAAA;UACAC,KAAA;UACAC,KAAA;YAAAC,QAAA;YAAAC,OAAA;UAAA;QACA,GACA;UACAL,KAAA;UACAC,IAAA;UACAC,KAAA;QACA,GACA;UACAF,KAAA;UACAC,IAAA;UACAC,KAAA;QACA,GACA;UACAF,KAAA;UACAC,IAAA;UACAC,KAAA;QACA,GACA;UACAF,KAAA;UACAC,IAAA;UACAC,KAAA;UACAI,UAAA;UACAC,YAAA;QACA;MAEA;IACA;EACA;EACAC,KAAA;IACA,2BAAAC,cAAAC,GAAA;MACA,IAAAA,GAAA,SAAAA,GAAA;MACA,KAAAC,gBAAA;QAAA,kBAAAD;MAAA;IACA;EACA;EACAE,OAAA,WAAAA,QAAA;IACA,KAAAjB,QAAA,GAAAH,WAAA,MAAAqB,OAAA;EACA;AACA", "ignoreList": []}]}