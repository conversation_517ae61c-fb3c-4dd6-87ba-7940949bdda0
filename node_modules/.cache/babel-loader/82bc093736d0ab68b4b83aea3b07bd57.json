{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/utils/generator/drawingDefalut.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/utils/generator/drawingDefalut.js", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:ZXhwb3J0IGRlZmF1bHQgW3sKICBsYXlvdXQ6ICdjb2xGb3JtSXRlbScsCiAgdGFnSWNvbjogJ2lucHV0JywKICBsYWJlbDogJ+aJi+acuuWPtycsCiAgdk1vZGVsOiAnbW9iaWxlJywKICBmb3JtSWQ6IDYsCiAgdGFnOiAnZWwtaW5wdXQnLAogIHBsYWNlaG9sZGVyOiAn6K+36L6T5YWl5omL5py65Y+3JywKICBkZWZhdWx0VmFsdWU6ICcnLAogIHNwYW46IDI0LAogIHN0eWxlOiB7CiAgICB3aWR0aDogJzEwMCUnCiAgfSwKICBjbGVhcmFibGU6IHRydWUsCiAgcHJlcGVuZDogJycsCiAgYXBwZW5kOiAnJywKICAncHJlZml4LWljb24nOiAnZWwtaWNvbi1tb2JpbGUnLAogICdzdWZmaXgtaWNvbic6ICcnLAogIG1heGxlbmd0aDogMTEsCiAgJ3Nob3ctd29yZC1saW1pdCc6IHRydWUsCiAgcmVhZG9ubHk6IGZhbHNlLAogIGRpc2FibGVkOiBmYWxzZSwKICByZXF1aXJlZDogdHJ1ZSwKICBjaGFuZ2VUYWc6IHRydWUsCiAgcmVnTGlzdDogW3sKICAgIHBhdHRlcm46ICcvXjEoM3w0fDV8N3w4fDkpXFxkezl9JC8nLAogICAgbWVzc2FnZTogJ+aJi+acuuWPt+agvOW8j+mUmeivrycKICB9XQp9XTs="}, {"version": 3, "names": ["layout", "tagIcon", "label", "vModel", "formId", "tag", "placeholder", "defaultValue", "span", "style", "width", "clearable", "prepend", "append", "maxlength", "readonly", "disabled", "required", "changeTag", "regList", "pattern", "message"], "sources": ["/Users/<USER>/develop/code/project/haijia/adminweb/src/utils/generator/drawingDefalut.js"], "sourcesContent": ["export default [\n  {\n    layout: 'colFormItem',\n    tagIcon: 'input',\n    label: '手机号',\n    vModel: 'mobile',\n    formId: 6,\n    tag: 'el-input',\n    placeholder: '请输入手机号',\n    defaultValue: '',\n    span: 24,\n    style: { width: '100%' },\n    clearable: true,\n    prepend: '',\n    append: '',\n    'prefix-icon': 'el-icon-mobile',\n    'suffix-icon': '',\n    maxlength: 11,\n    'show-word-limit': true,\n    readonly: false,\n    disabled: false,\n    required: true,\n    changeTag: true,\n    regList: [{\n      pattern: '/^1(3|4|5|7|8|9)\\\\d{9}$/',\n      message: '手机号格式错误'\n    }]\n  }\n]\n"], "mappings": "AAAA,eAAe,CACb;EACEA,MAAM,EAAE,aAAa;EACrBC,OAAO,EAAE,OAAO;EAChBC,KAAK,EAAE,KAAK;EACZC,MAAM,EAAE,QAAQ;EAChBC,MAAM,EAAE,CAAC;EACTC,GAAG,EAAE,UAAU;EACfC,WAAW,EAAE,QAAQ;EACrBC,YAAY,EAAE,EAAE;EAChBC,IAAI,EAAE,EAAE;EACRC,KAAK,EAAE;IAAEC,KAAK,EAAE;EAAO,CAAC;EACxBC,SAAS,EAAE,IAAI;EACfC,OAAO,EAAE,EAAE;EACXC,MAAM,EAAE,EAAE;EACV,aAAa,EAAE,gBAAgB;EAC/B,aAAa,EAAE,EAAE;EACjBC,SAAS,EAAE,EAAE;EACb,iBAAiB,EAAE,IAAI;EACvBC,QAAQ,EAAE,KAAK;EACfC,QAAQ,EAAE,KAAK;EACfC,QAAQ,EAAE,IAAI;EACdC,SAAS,EAAE,IAAI;EACfC,OAAO,EAAE,CAAC;IACRC,OAAO,EAAE,0BAA0B;IACnCC,OAAO,EAAE;EACX,CAAC;AACH,CAAC,CACF", "ignoreList": []}]}