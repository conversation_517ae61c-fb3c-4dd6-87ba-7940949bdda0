{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Process/components/nodePanel/task.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Process/components/nodePanel/task.vue", "mtime": 1650105580000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js", "mtime": 1751171659043}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}