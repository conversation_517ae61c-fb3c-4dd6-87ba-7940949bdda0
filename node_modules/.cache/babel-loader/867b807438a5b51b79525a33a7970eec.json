{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/api/system/notice.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/api/system/notice.js", "mtime": 1654609628000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QgZnJvbSAnQC91dGlscy9yZXF1ZXN0JzsKCi8vIOafpeivouWFrOWRiuWIl+ihqApleHBvcnQgZnVuY3Rpb24gbGlzdE5vdGljZShxdWVyeSkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9zeXN0ZW0vbm90aWNlL2xpc3QnLAogICAgbWV0aG9kOiAnZ2V0JywKICAgIHBhcmFtczogcXVlcnkKICB9KTsKfQoKLy8g5p+l6K+i5YWs5ZGK6K+m57uGCmV4cG9ydCBmdW5jdGlvbiBnZXROb3RpY2Uobm90aWNlSWQpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvc3lzdGVtL25vdGljZS8nICsgbm90aWNlSWQsCiAgICBtZXRob2Q6ICdnZXQnCiAgfSk7Cn0KCi8vIOaWsOWinuWFrOWRigpleHBvcnQgZnVuY3Rpb24gYWRkTm90aWNlKGRhdGEpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvc3lzdGVtL25vdGljZScsCiAgICBtZXRob2Q6ICdwb3N0JywKICAgIGRhdGE6IGRhdGEKICB9KTsKfQoKLy8g5L+u5pS55YWs5ZGKCmV4cG9ydCBmdW5jdGlvbiB1cGRhdGVOb3RpY2UoZGF0YSkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9zeXN0ZW0vbm90aWNlJywKICAgIG1ldGhvZDogJ3B1dCcsCiAgICBkYXRhOiBkYXRhCiAgfSk7Cn0KCi8vIOWIoOmZpOWFrOWRigpleHBvcnQgZnVuY3Rpb24gZGVsTm90aWNlKG5vdGljZUlkKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL3N5c3RlbS9ub3RpY2UvJyArIG5vdGljZUlkLAogICAgbWV0aG9kOiAnZGVsZXRlJwogIH0pOwp9CmV4cG9ydCBmdW5jdGlvbiByZWFkTm90aWNlKG5vdGljZUlkKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL3N5c3RlbS9ub3RpY2UvcmVhZC8nICsgbm90aWNlSWQsCiAgICBtZXRob2Q6ICdwdXQnCiAgfSk7Cn0KZXhwb3J0IGZ1bmN0aW9uIG5Ob3RpY2UoKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL3N5c3RlbS9ub3RpY2UvY291bnQnLAogICAgbWV0aG9kOiAnZ2V0JwogIH0pOwp9"}, {"version": 3, "names": ["request", "listNotice", "query", "url", "method", "params", "getNotice", "noticeId", "addNotice", "data", "updateNotice", "delNotice", "readNotice", "nNotice"], "sources": ["/Users/<USER>/develop/code/project/haijia/adminweb/src/api/system/notice.js"], "sourcesContent": ["import request from '@/utils/request'\n\n// 查询公告列表\nexport function listNotice(query) {\n  return request({\n    url: '/system/notice/list',\n    method: 'get',\n    params: query\n  })\n}\n\n// 查询公告详细\nexport function getNotice(noticeId) {\n  return request({\n    url: '/system/notice/' + noticeId,\n    method: 'get'\n  })\n}\n\n// 新增公告\nexport function addNotice(data) {\n  return request({\n    url: '/system/notice',\n    method: 'post',\n    data: data\n  })\n}\n\n// 修改公告\nexport function updateNotice(data) {\n  return request({\n    url: '/system/notice',\n    method: 'put',\n    data: data\n  })\n}\n\n// 删除公告\nexport function delNotice(noticeId) {\n  return request({\n    url: '/system/notice/' + noticeId,\n    method: 'delete'\n  })\n}\n\nexport function readNotice(noticeId) {\n  return request({\n    url: '/system/notice/read/' + noticeId,\n    method: 'put'\n  })\n}\n\nexport function nNotice() {\n  return request({\n    url: '/system/notice/count',\n    method: 'get'\n  })\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;;AAErC;AACA,OAAO,SAASC,UAAUA,CAACC,KAAK,EAAE;EAChC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,qBAAqB;IAC1BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEH;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASI,SAASA,CAACC,QAAQ,EAAE;EAClC,OAAOP,OAAO,CAAC;IACbG,GAAG,EAAE,iBAAiB,GAAGI,QAAQ;IACjCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASI,SAASA,CAACC,IAAI,EAAE;EAC9B,OAAOT,OAAO,CAAC;IACbG,GAAG,EAAE,gBAAgB;IACrBC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASC,YAAYA,CAACD,IAAI,EAAE;EACjC,OAAOT,OAAO,CAAC;IACbG,GAAG,EAAE,gBAAgB;IACrBC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASE,SAASA,CAACJ,QAAQ,EAAE;EAClC,OAAOP,OAAO,CAAC;IACbG,GAAG,EAAE,iBAAiB,GAAGI,QAAQ;IACjCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;AAEA,OAAO,SAASQ,UAAUA,CAACL,QAAQ,EAAE;EACnC,OAAOP,OAAO,CAAC;IACbG,GAAG,EAAE,sBAAsB,GAAGI,QAAQ;IACtCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;AAEA,OAAO,SAASS,OAAOA,CAAA,EAAG;EACxB,OAAOb,OAAO,CAAC;IACbG,GAAG,EAAE,sBAAsB;IAC3BC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}