{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Process/common/mixinPanel.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Process/common/mixinPanel.js", "mtime": 1641277326000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["xcrud", "golbalConfig", "showConfig", "set", "input", "select", "colorPicker", "showAlpha", "xform", "form", "labelWidth", "components", "xForm", "props", "modeler", "type", "Object", "required", "element", "categorys", "Array", "default", "_default", "watch", "formDataId", "val", "updateProperties", "id", "formDataName", "name", "formDataDocumentation", "documentation", "documentationElement", "get", "create", "text", "methods", "properties", "modeling", "computed", "elementType", "bizObj", "businessObject", "eventDefinitions", "$type"], "sources": ["/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Process/common/mixinPanel.js"], "sourcesContent": ["import xcrud from 'xcrud'\nimport golbalConfig from 'xcrud/package/common/config'\nimport showConfig from '../flowable/showConfig'\ngolbalConfig.set({\n  input: {\n    // size: 'mini'\n  },\n  select: {\n    // size: 'mini'\n  },\n  colorPicker: {\n    showAlpha: true\n  },\n  xform: {\n    form: {\n      labelWidth: 'auto'\n      // size: 'mini'\n    }\n  }\n})\nexport default {\n  components: { xForm: xcrud.xForm },\n  props: {\n    modeler: {\n      type: Object,\n      required: true\n    },\n    element: {\n      type: Object,\n      required: true\n    },\n    categorys: {\n      type: Array,\n      default: () => []\n    }\n  },\n  watch: {\n    'formData.id': function(val) {\n      this.updateProperties({ id: val })\n    },\n    'formData.name': function(val) {\n      this.updateProperties({ name: val })\n    },\n    'formData.documentation': function(val) {\n      if (!val) {\n        this.updateProperties({ documentation: [] })\n        return\n      }\n      const documentationElement = this.modeler.get('moddle').create('bpmn:Documentation', { text: val })\n      this.updateProperties({ documentation: [documentationElement] })\n    }\n  },\n  methods: {\n    updateProperties(properties) {\n      const modeling = this.modeler.get('modeling')\n      modeling.updateProperties(this.element, properties)\n    }\n  },\n  computed: {\n    elementType() {\n      const bizObj = this.element.businessObject\n      return bizObj.eventDefinitions\n        ? bizObj.eventDefinitions[0].$type\n        : bizObj.$type\n    },\n    showConfig() {\n      return showConfig[this.elementType] || {}\n    }\n  }\n}\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,YAAY,MAAM,6BAA6B;AACtD,OAAOC,WAAU,MAAM,wBAAwB;AAC/CD,YAAY,CAACE,GAAG,CAAC;EACfC,KAAK,EAAE;IACL;EAAA,CACD;EACDC,MAAM,EAAE;IACN;EAAA,CACD;EACDC,WAAW,EAAE;IACXC,SAAS,EAAE;EACb,CAAC;EACDC,KAAK,EAAE;IACLC,IAAI,EAAE;MACJC,UAAU,EAAE;MACZ;IACF;EACF;AACF,CAAC,CAAC;AACF,eAAe;EACbC,UAAU,EAAE;IAAEC,KAAK,EAAEZ,KAAK,CAACY;EAAM,CAAC;EAClCC,KAAK,EAAE;IACLC,OAAO,EAAE;MACPC,IAAI,EAAEC,MAAM;MACZC,QAAQ,EAAE;IACZ,CAAC;IACDC,OAAO,EAAE;MACPH,IAAI,EAAEC,MAAM;MACZC,QAAQ,EAAE;IACZ,CAAC;IACDE,SAAS,EAAE;MACTJ,IAAI,EAAEK,KAAK;MACXC,OAAO,EAAE,SAAAC,SAAA;QAAA,OAAM,EAAE;MAAA;IACnB;EACF,CAAC;EACDC,KAAK,EAAE;IACL,aAAa,EAAE,SAAAC,WAASC,GAAG,EAAE;MAC3B,IAAI,CAACC,gBAAgB,CAAC;QAAEC,EAAE,EAAEF;MAAI,CAAC,CAAC;IACpC,CAAC;IACD,eAAe,EAAE,SAAAG,aAASH,GAAG,EAAE;MAC7B,IAAI,CAACC,gBAAgB,CAAC;QAAEG,IAAI,EAAEJ;MAAI,CAAC,CAAC;IACtC,CAAC;IACD,wBAAwB,EAAE,SAAAK,sBAASL,GAAG,EAAE;MACtC,IAAI,CAACA,GAAG,EAAE;QACR,IAAI,CAACC,gBAAgB,CAAC;UAAEK,aAAa,EAAE;QAAG,CAAC,CAAC;QAC5C;MACF;MACA,IAAMC,oBAAoB,GAAG,IAAI,CAAClB,OAAO,CAACmB,GAAG,CAAC,QAAQ,CAAC,CAACC,MAAM,CAAC,oBAAoB,EAAE;QAAEC,IAAI,EAAEV;MAAI,CAAC,CAAC;MACnG,IAAI,CAACC,gBAAgB,CAAC;QAAEK,aAAa,EAAE,CAACC,oBAAoB;MAAE,CAAC,CAAC;IAClE;EACF,CAAC;EACDI,OAAO,EAAE;IACPV,gBAAgB,WAAAA,iBAACW,UAAU,EAAE;MAC3B,IAAMC,QAAQ,GAAG,IAAI,CAACxB,OAAO,CAACmB,GAAG,CAAC,UAAU,CAAC;MAC7CK,QAAQ,CAACZ,gBAAgB,CAAC,IAAI,CAACR,OAAO,EAAEmB,UAAU,CAAC;IACrD;EACF,CAAC;EACDE,QAAQ,EAAE;IACRC,WAAW,WAAAA,YAAA,EAAG;MACZ,IAAMC,MAAM,GAAG,IAAI,CAACvB,OAAO,CAACwB,cAAc;MAC1C,OAAOD,MAAM,CAACE,gBAAgB,GAC1BF,MAAM,CAACE,gBAAgB,CAAC,CAAC,CAAC,CAACC,KAAK,GAChCH,MAAM,CAACG,KAAK;IAClB,CAAC;IACD1C,UAAU,WAAAA,WAAA,EAAG;MACX,OAAOA,WAAU,CAAC,IAAI,CAACsC,WAAW,CAAC,IAAI,CAAC,CAAC;IAC3C;EACF;AACF,CAAC", "ignoreList": []}]}