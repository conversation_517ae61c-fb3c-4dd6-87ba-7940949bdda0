{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/dashboard/PieChart.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/dashboard/PieChart.vue", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["echarts", "require", "resize", "mixins", "props", "className", "type", "String", "default", "width", "height", "data", "chart", "mounted", "_this", "$nextTick", "initChart", "<PERSON><PERSON><PERSON><PERSON>", "dispose", "methods", "init", "$el", "setOption", "tooltip", "trigger", "formatter", "legend", "left", "bottom", "series", "name", "roseType", "radius", "center", "value", "animationEasing", "animationDuration"], "sources": ["src/views/dashboard/PieChart.vue"], "sourcesContent": ["<template>\n  <div :class=\"className\" :style=\"{height:height,width:width}\" />\n</template>\n\n<script>\nimport echarts from 'echarts'\nrequire('echarts/theme/macarons') // echarts theme\nimport resize from './mixins/resize'\n\nexport default {\n  mixins: [resize],\n  props: {\n    className: {\n      type: String,\n      default: 'chart'\n    },\n    width: {\n      type: String,\n      default: '100%'\n    },\n    height: {\n      type: String,\n      default: '300px'\n    }\n  },\n  data() {\n    return {\n      chart: null\n    }\n  },\n  mounted() {\n    this.$nextTick(() => {\n      this.initChart()\n    })\n  },\n  beforeDestroy() {\n    if (!this.chart) {\n      return\n    }\n    this.chart.dispose()\n    this.chart = null\n  },\n  methods: {\n    initChart() {\n      this.chart = echarts.init(this.$el, 'macarons')\n\n      this.chart.setOption({\n        tooltip: {\n          trigger: 'item',\n          formatter: '{a} <br/>{b} : {c} ({d}%)'\n        },\n        legend: {\n          left: 'center',\n          bottom: '10',\n          data: ['Industries', 'Technology', 'Forex', 'Gold', 'Forecasts']\n        },\n        series: [\n          {\n            name: 'WEEKLY WRITE ARTICLES',\n            type: 'pie',\n            roseType: 'radius',\n            radius: [15, 95],\n            center: ['50%', '38%'],\n            data: [\n              { value: 320, name: 'Industries' },\n              { value: 240, name: 'Technology' },\n              { value: 149, name: 'Forex' },\n              { value: 100, name: 'Gold' },\n              { value: 59, name: 'Forecasts' }\n            ],\n            animationEasing: 'cubicInOut',\n            animationDuration: 2600\n          }\n        ]\n      })\n    }\n  }\n}\n</script>\n"], "mappings": ";;;;;AAKA,OAAAA,OAAA;AACAC,OAAA;AACA,OAAAC,MAAA;AAEA;EACAC,MAAA,GAAAD,MAAA;EACAE,KAAA;IACAC,SAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAC,KAAA;MACAH,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAE,MAAA;MACAJ,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;EACA;EACAG,IAAA,WAAAA,KAAA;IACA;MACAC,KAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA,KAAAC,SAAA;MACAD,KAAA,CAAAE,SAAA;IACA;EACA;EACAC,aAAA,WAAAA,cAAA;IACA,UAAAL,KAAA;MACA;IACA;IACA,KAAAA,KAAA,CAAAM,OAAA;IACA,KAAAN,KAAA;EACA;EACAO,OAAA;IACAH,SAAA,WAAAA,UAAA;MACA,KAAAJ,KAAA,GAAAZ,OAAA,CAAAoB,IAAA,MAAAC,GAAA;MAEA,KAAAT,KAAA,CAAAU,SAAA;QACAC,OAAA;UACAC,OAAA;UACAC,SAAA;QACA;QACAC,MAAA;UACAC,IAAA;UACAC,MAAA;UACAjB,IAAA;QACA;QACAkB,MAAA,GACA;UACAC,IAAA;UACAxB,IAAA;UACAyB,QAAA;UACAC,MAAA;UACAC,MAAA;UACAtB,IAAA,GACA;YAAAuB,KAAA;YAAAJ,IAAA;UAAA,GACA;YAAAI,KAAA;YAAAJ,IAAA;UAAA,GACA;YAAAI,KAAA;YAAAJ,IAAA;UAAA,GACA;YAAAI,KAAA;YAAAJ,IAAA;UAAA,GACA;YAAAI,KAAA;YAAAJ,IAAA;UAAA,EACA;UACAK,eAAA;UACAC,iBAAA;QACA;MAEA;IACA;EACA;AACA", "ignoreList": []}]}