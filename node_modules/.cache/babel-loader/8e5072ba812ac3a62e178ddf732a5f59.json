{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/api/project/report.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/api/project/report.js", "mtime": 1663466452000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QgZnJvbSAnQC91dGlscy9yZXF1ZXN0JzsKZXhwb3J0IGZ1bmN0aW9uIGluZGV4UmVwb3J0Q291bnQocXVlcnkpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvcHJvamVjdC9yZXBvcnQvaW5kZXgnLAogICAgbWV0aG9kOiAnZ2V0JywKICAgIHBhcmFtczogcXVlcnkKICB9KTsKfQoKLy8g5p+l6K+i6aG555uu5oql5aSH5YiX6KGoCmV4cG9ydCBmdW5jdGlvbiBsaXN0UmVwb3J0KHF1ZXJ5KSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL3Byb2plY3QvcmVwb3J0L2xpc3QnLAogICAgbWV0aG9kOiAnZ2V0JywKICAgIHBhcmFtczogcXVlcnkKICB9KTsKfQoKLy8g5p+l6K+i6aG555uu5oql5aSH6K+m57uGCmV4cG9ydCBmdW5jdGlvbiBnZXRSZXBvcnQocHJvamVjdElkKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL3Byb2plY3QvcmVwb3J0LycgKyBwcm9qZWN0SWQsCiAgICBtZXRob2Q6ICdnZXQnCiAgfSk7Cn0KCi8vIOaWsOWinumhueebruaKpeWkhwpleHBvcnQgZnVuY3Rpb24gYWRkUmVwb3J0KGRhdGEpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvcHJvamVjdC9yZXBvcnQnLAogICAgbWV0aG9kOiAncG9zdCcsCiAgICBkYXRhOiBkYXRhCiAgfSk7Cn0KCi8vIOS/ruaUuemhueebruaKpeWkhwpleHBvcnQgZnVuY3Rpb24gdXBkYXRlUmVwb3J0KGRhdGEpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvcHJvamVjdC9yZXBvcnQnLAogICAgbWV0aG9kOiAncHV0JywKICAgIGRhdGE6IGRhdGEKICB9KTsKfQoKLy8g5Yig6Zmk6aG555uu5oql5aSHCmV4cG9ydCBmdW5jdGlvbiBkZWxSZXBvcnQocHJvamVjdElkKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL3Byb2plY3QvcmVwb3J0LycgKyBwcm9qZWN0SWQsCiAgICBtZXRob2Q6ICdkZWxldGUnCiAgfSk7Cn0KCi8vIOWvvOWHuumhueebruaKpeWkhwpleHBvcnQgZnVuY3Rpb24gZXhwb3J0UmVwb3J0KHF1ZXJ5KSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL3Byb2plY3QvcmVwb3J0L2V4cG9ydCcsCiAgICBtZXRob2Q6ICdnZXQnLAogICAgcGFyYW1zOiBxdWVyeSwKICAgIHRpbWVvdXQ6IDEwICogNjAgKiAxMDAwCiAgfSk7Cn0KCi8vIOaJk+WNsOmhueebruaKpeWkhwpleHBvcnQgZnVuY3Rpb24gcHJpbnRSZXBvcnQocXVlcnkpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvcHJvamVjdC9yZXBvcnQvcHJpbnQnLAogICAgbWV0aG9kOiAnZ2V0JywKICAgIHBhcmFtczogcXVlcnkKICB9KTsKfQoKLy8g5LiL6L2955So5oi35a+85YWl5qih5p2/CmV4cG9ydCBmdW5jdGlvbiBpbXBvcnRUZW1wbGF0ZSgpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvcHJvamVjdC9yZXBvcnQvaW1wb3J0VGVtcGxhdGUnLAogICAgbWV0aG9kOiAnZ2V0JwogIH0pOwp9CgovLyDpobnnm67lkI3np7DllK/kuIDmoKHpqowKZXhwb3J0IGZ1bmN0aW9uIGNoZWNrTmFtZVVuaXF1ZShkYXRhKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL3Byb2plY3QvcmVwb3J0L2NoZWNrTmFtZVVuaXF1ZScsCiAgICBtZXRob2Q6ICdnZXQnLAogICAgcGFyYW1zOiBkYXRhCiAgfSk7Cn0KCi8vIOmhueebruWQjeensOebuOS8vApleHBvcnQgZnVuY3Rpb24gZ2V0TGlrZUxpc3QoZGF0YSkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9wcm9qZWN0L3JlcG9ydC9nZXRMaWtlTGlzdCcsCiAgICBtZXRob2Q6ICdnZXQnLAogICAgcGFyYW1zOiBkYXRhCiAgfSk7Cn0KCi8vIOmhueebruaOiOadgwpleHBvcnQgZnVuY3Rpb24gYXV0aFJlcG9ydChkYXRhKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL3Byb2plY3QvcmVwb3J0L2F1dGgnLAogICAgbWV0aG9kOiAncHV0JywKICAgIGRhdGE6IGRhdGEKICB9KTsKfQ=="}, {"version": 3, "names": ["request", "indexReportCount", "query", "url", "method", "params", "listReport", "getReport", "projectId", "addReport", "data", "updateReport", "delReport", "exportReport", "timeout", "printReport", "importTemplate", "checkNameUnique", "getLikeList", "authReport"], "sources": ["/Users/<USER>/develop/code/project/haijia/adminweb/src/api/project/report.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\nexport function indexReportCount(query) {\r\n  return request({\r\n    url: '/project/report/index',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询项目报备列表\r\nexport function listReport(query) {\r\n  return request({\r\n    url: '/project/report/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询项目报备详细\r\nexport function getReport(projectId) {\r\n  return request({\r\n    url: '/project/report/' + projectId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增项目报备\r\nexport function addReport(data) {\r\n  return request({\r\n    url: '/project/report',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改项目报备\r\nexport function updateReport(data) {\r\n  return request({\r\n    url: '/project/report',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除项目报备\r\nexport function delReport(projectId) {\r\n  return request({\r\n    url: '/project/report/' + projectId,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 导出项目报备\r\nexport function exportReport(query) {\r\n  return request({\r\n    url: '/project/report/export',\r\n    method: 'get',\r\n    params: query,\r\n    timeout: 10 * 60 * 1000,\r\n  })\r\n}\r\n\r\n// 打印项目报备\r\nexport function printReport(query) {\r\n  return request({\r\n    url: '/project/report/print',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 下载用户导入模板\r\nexport function importTemplate() {\r\n  return request({\r\n    url: '/project/report/importTemplate',\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 项目名称唯一校验\r\nexport function checkNameUnique(data) {\r\n  return request({\r\n    url: '/project/report/checkNameUnique',\r\n    method: 'get',\r\n    params: data\r\n  })\r\n}\r\n\r\n// 项目名称相似\r\nexport function getLikeList(data) {\r\n  return request({\r\n    url: '/project/report/getLikeList',\r\n    method: 'get',\r\n    params: data\r\n  })\r\n}\r\n\r\n// 项目授权\r\nexport function authReport(data) {\r\n  return request({\r\n    url: '/project/report/auth',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;AAErC,OAAO,SAASC,gBAAgBA,CAACC,KAAK,EAAE;EACtC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEH;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASI,UAAUA,CAACJ,KAAK,EAAE;EAChC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,sBAAsB;IAC3BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEH;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASK,SAASA,CAACC,SAAS,EAAE;EACnC,OAAOR,OAAO,CAAC;IACbG,GAAG,EAAE,kBAAkB,GAAGK,SAAS;IACnCJ,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASK,SAASA,CAACC,IAAI,EAAE;EAC9B,OAAOV,OAAO,CAAC;IACbG,GAAG,EAAE,iBAAiB;IACtBC,MAAM,EAAE,MAAM;IACdM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASC,YAAYA,CAACD,IAAI,EAAE;EACjC,OAAOV,OAAO,CAAC;IACbG,GAAG,EAAE,iBAAiB;IACtBC,MAAM,EAAE,KAAK;IACbM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASE,SAASA,CAACJ,SAAS,EAAE;EACnC,OAAOR,OAAO,CAAC;IACbG,GAAG,EAAE,kBAAkB,GAAGK,SAAS;IACnCJ,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASS,YAAYA,CAACX,KAAK,EAAE;EAClC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,wBAAwB;IAC7BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEH,KAAK;IACbY,OAAO,EAAE,EAAE,GAAG,EAAE,GAAG;EACrB,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASC,WAAWA,CAACb,KAAK,EAAE;EACjC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEH;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASc,cAAcA,CAAA,EAAG;EAC/B,OAAOhB,OAAO,CAAC;IACbG,GAAG,EAAE,gCAAgC;IACrCC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASa,eAAeA,CAACP,IAAI,EAAE;EACpC,OAAOV,OAAO,CAAC;IACbG,GAAG,EAAE,iCAAiC;IACtCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEK;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASQ,WAAWA,CAACR,IAAI,EAAE;EAChC,OAAOV,OAAO,CAAC;IACbG,GAAG,EAAE,6BAA6B;IAClCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEK;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASS,UAAUA,CAACT,IAAI,EAAE;EAC/B,OAAOV,OAAO,CAAC;IACbG,GAAG,EAAE,sBAAsB;IAC3BC,MAAM,EAAE,KAAK;IACbM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ", "ignoreList": []}]}