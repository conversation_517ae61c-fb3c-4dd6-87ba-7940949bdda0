{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/@babel/runtime/helpers/arrayWithoutHoles.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/@babel/runtime/helpers/arrayWithoutHoles.js", "mtime": 1751171659853}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:dmFyIGFycmF5TGlrZVRvQXJyYXkgPSByZXF1aXJlKCIuL2FycmF5TGlrZVRvQXJyYXkuanMiKTsKZnVuY3Rpb24gX2FycmF5V2l0aG91dEhvbGVzKHIpIHsKICBpZiAoQXJyYXkuaXNBcnJheShyKSkgcmV0dXJuIGFycmF5TGlrZVRvQXJyYXkocik7Cn0KbW9kdWxlLmV4cG9ydHMgPSBfYXJyYXlXaXRob3V0SG9sZXMsIG1vZHVsZS5leHBvcnRzLl9fZXNNb2R1bGUgPSB0cnVlLCBtb2R1bGUuZXhwb3J0c1siZGVmYXVsdCJdID0gbW9kdWxlLmV4cG9ydHM7"}, {"version": 3, "names": ["arrayLikeToArray", "require", "_arrayWithoutHoles", "r", "Array", "isArray", "module", "exports", "__esModule"], "sources": ["/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/@babel/runtime/helpers/arrayWithoutHoles.js"], "sourcesContent": ["var arrayLikeToArray = require(\"./arrayLikeToArray.js\");\nfunction _arrayWithoutHoles(r) {\n  if (Array.isArray(r)) return arrayLikeToArray(r);\n}\nmodule.exports = _arrayWithoutHoles, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "mappings": "AAAA,IAAIA,gBAAgB,GAAGC,OAAO,CAAC,uBAAuB,CAAC;AACvD,SAASC,kBAAkBA,CAACC,CAAC,EAAE;EAC7B,IAAIC,KAAK,CAACC,OAAO,CAACF,CAAC,CAAC,EAAE,OAAOH,gBAAgB,CAACG,CAAC,CAAC;AAClD;AACAG,MAAM,CAACC,OAAO,GAAGL,kBAAkB,EAAEI,MAAM,CAACC,OAAO,CAACC,UAAU,GAAG,IAAI,EAAEF,MAAM,CAACC,OAAO,CAAC,SAAS,CAAC,GAAGD,MAAM,CAACC,OAAO", "ignoreList": []}]}