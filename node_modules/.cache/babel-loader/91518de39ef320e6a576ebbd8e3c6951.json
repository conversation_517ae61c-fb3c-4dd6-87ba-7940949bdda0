{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/system/role/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/system/role/index.vue", "mtime": 1662389786000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js", "mtime": 1751171659043}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGxpc3RSb2xlLCBnZXRSb2xlLCBkZWxSb2xlLCBhZGRSb2xlLCB1cGRhdGVSb2xlLCBleHBvcnRSb2xlLCBkYXRhU2NvcGUsIGNoYW5nZVJvbGVTdGF0dXMgfSBmcm9tICJAL2FwaS9zeXN0ZW0vcm9sZSI7CmltcG9ydCB7IHRyZWVzZWxlY3QgYXMgbWVudVRyZWVzZWxlY3QsIHJvbGVNZW51VHJlZXNlbGVjdCB9IGZyb20gIkAvYXBpL3N5c3RlbS9tZW51IjsKaW1wb3J0IHsgdHJlZXNlbGVjdCBhcyBkZXB0VHJlZXNlbGVjdCwgcm9sZURlcHRUcmVlc2VsZWN0IH0gZnJvbSAiQC9hcGkvc3lzdGVtL2RlcHQiOwppbXBvcnQgeyBwcm92aW5jZUFuZENpdHlEYXRhIH0gZnJvbSAiZWxlbWVudC1jaGluYS1hcmVhLWRhdGEiOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogIlJvbGUiLAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICAvLyDpga7nvanlsYIKICAgICAgbG9hZGluZzogdHJ1ZSwKICAgICAgLy8g6YCJ5Lit5pWw57uECiAgICAgIGlkczogW10sCiAgICAgIC8vIOmdnuWNleS4quemgeeUqAogICAgICBzaW5nbGU6IHRydWUsCiAgICAgIC8vIOmdnuWkmuS4quemgeeUqAogICAgICBtdWx0aXBsZTogdHJ1ZSwKICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2CiAgICAgIHNob3dTZWFyY2g6IHRydWUsCiAgICAgIC8vIOaAu+adoeaVsAogICAgICB0b3RhbDogMCwKICAgICAgLy8g6KeS6Imy6KGo5qC85pWw5o2uCiAgICAgIHJvbGVMaXN0OiBbXSwKICAgICAgLy8g5by55Ye65bGC5qCH6aKYCiAgICAgIHRpdGxlOiAiIiwKICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGCCiAgICAgIG9wZW46IGZhbHNlLAogICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYLvvIjmlbDmja7mnYPpmZDvvIkKICAgICAgb3BlbkRhdGFTY29wZTogZmFsc2UsCiAgICAgIG1lbnVFeHBhbmQ6IGZhbHNlLAogICAgICBtZW51Tm9kZUFsbDogZmFsc2UsCiAgICAgIGRlcHRFeHBhbmQ6IHRydWUsCiAgICAgIGRlcHROb2RlQWxsOiBmYWxzZSwKICAgICAgLy8g5pel5pyf6IyD5Zu0CiAgICAgIGRhdGVSYW5nZTogW10sCiAgICAgIC8vIOeKtuaAgeaVsOaNruWtl+WFuAogICAgICBzdGF0dXNPcHRpb25zOiBbXSwKICAgICAgLy8g5pWw5o2u6IyD5Zu06YCJ6aG5CiAgICAgIGRhdGFTY29wZU9wdGlvbnM6IFt7CiAgICAgICAgdmFsdWU6ICIxIiwKICAgICAgICBsYWJlbDogIuWFqOmDqOaVsOaNruadg+mZkCIKICAgICAgfSwgewogICAgICAgIHZhbHVlOiAiMiIsCiAgICAgICAgbGFiZWw6ICLoh6rlrprmlbDmja7mnYPpmZAiCiAgICAgIH0sCiAgICAgIC8vIHsKICAgICAgLy8gICB2YWx1ZTogIjMiLAogICAgICAvLyAgIGxhYmVsOiAi5pys6YOo6Zeo5pWw5o2u5p2D6ZmQIgogICAgICAvLyB9LAogICAgICAvLyB7CiAgICAgIC8vICAgdmFsdWU6ICI0IiwKICAgICAgLy8gICBsYWJlbDogIuacrOmDqOmXqOWPiuS7peS4i+aVsOaNruadg+mZkCIKICAgICAgLy8gfSwKICAgICAgewogICAgICAgIHZhbHVlOiAiNSIsCiAgICAgICAgbGFiZWw6ICLku4XmnKzkurrmlbDmja7mnYPpmZAiCiAgICAgIH0sIHsKICAgICAgICB2YWx1ZTogIjYiLAogICAgICAgIGxhYmVsOiAi6Ieq5a6a5LmJ5Yy65Z+f5pWw5o2u5p2D6ZmQIgogICAgICB9XSwKICAgICAgLy8g6I+c5Y2V5YiX6KGoCiAgICAgIG1lbnVPcHRpb25zOiBbXSwKICAgICAgLy8g6YOo6Zeo5YiX6KGoCiAgICAgIGRlcHRPcHRpb25zOiBbXSwKICAgICAgLy8g5p+l6K+i5Y+C5pWwCiAgICAgIHF1ZXJ5UGFyYW1zOiB7CiAgICAgICAgcGFnZU51bTogMSwKICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgcm9sZU5hbWU6IHVuZGVmaW5lZCwKICAgICAgICByb2xlS2V5OiB1bmRlZmluZWQsCiAgICAgICAgc3RhdHVzOiB1bmRlZmluZWQKICAgICAgfSwKICAgICAgLy8g6KGo5Y2V5Y+C5pWwCiAgICAgIGZvcm06IHt9LAogICAgICBkZWZhdWx0UHJvcHM6IHsKICAgICAgICBjaGlsZHJlbjogImNoaWxkcmVuIiwKICAgICAgICBsYWJlbDogImxhYmVsIgogICAgICB9LAogICAgICAvLyDooajljZXmoKHpqowKICAgICAgcnVsZXM6IHsKICAgICAgICByb2xlTmFtZTogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogIuinkuiJsuWQjeensOS4jeiDveS4uuepuiIsCiAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIKICAgICAgICB9XSwKICAgICAgICByb2xlS2V5OiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAi5p2D6ZmQ5a2X56ym5LiN6IO95Li656m6IiwKICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgIH1dLAogICAgICAgIHJvbGVTb3J0OiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAi6KeS6Imy6aG65bqP5LiN6IO95Li656m6IiwKICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgIH1dCiAgICAgIH0sCiAgICAgIHByb3ZpbmNlOiBwcm92aW5jZUFuZENpdHlEYXRhCiAgICB9OwogIH0sCiAgY3JlYXRlZDogZnVuY3Rpb24gY3JlYXRlZCgpIHsKICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICB0aGlzLmdldExpc3QoKTsKICAgIHRoaXMuZ2V0RGljdHMoInN5c19ub3JtYWxfZGlzYWJsZSIpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgIF90aGlzLnN0YXR1c09wdGlvbnMgPSByZXNwb25zZS5kYXRhOwogICAgfSk7CiAgfSwKICBtZXRob2RzOiB7CiAgICAvKiog5p+l6K+i6KeS6Imy5YiX6KGoICovZ2V0TGlzdDogZnVuY3Rpb24gZ2V0TGlzdCgpIHsKICAgICAgdmFyIF90aGlzMiA9IHRoaXM7CiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7CiAgICAgIGxpc3RSb2xlKHRoaXMuYWRkRGF0ZVJhbmdlKHRoaXMucXVlcnlQYXJhbXMsIHRoaXMuZGF0ZVJhbmdlKSkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICBfdGhpczIucm9sZUxpc3QgPSByZXNwb25zZS5yb3dzOwogICAgICAgIF90aGlzMi50b3RhbCA9IHJlc3BvbnNlLnRvdGFsOwogICAgICAgIF90aGlzMi5sb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDmn6Xor6Loj5zljZXmoJHnu5PmnoQgKi9nZXRNZW51VHJlZXNlbGVjdDogZnVuY3Rpb24gZ2V0TWVudVRyZWVzZWxlY3QoKSB7CiAgICAgIHZhciBfdGhpczMgPSB0aGlzOwogICAgICBtZW51VHJlZXNlbGVjdCgpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgX3RoaXMzLm1lbnVPcHRpb25zID0gcmVzcG9uc2UuZGF0YTsKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOafpeivoumDqOmXqOagkee7k+aehCAqL2dldERlcHRUcmVlc2VsZWN0OiBmdW5jdGlvbiBnZXREZXB0VHJlZXNlbGVjdCgpIHsKICAgICAgdmFyIF90aGlzNCA9IHRoaXM7CiAgICAgIGRlcHRUcmVlc2VsZWN0KCkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICBfdGhpczQuZGVwdE9wdGlvbnMgPSByZXNwb25zZS5kYXRhOwogICAgICB9KTsKICAgIH0sCiAgICAvLyDmiYDmnInoj5zljZXoioLngrnmlbDmja4KICAgIGdldE1lbnVBbGxDaGVja2VkS2V5czogZnVuY3Rpb24gZ2V0TWVudUFsbENoZWNrZWRLZXlzKCkgewogICAgICAvLyDnm67liY3ooqvpgInkuK3nmoToj5zljZXoioLngrkKICAgICAgdmFyIGNoZWNrZWRLZXlzID0gdGhpcy4kcmVmcy5tZW51LmdldENoZWNrZWRLZXlzKCk7CiAgICAgIC8vIOWNiumAieS4reeahOiPnOWNleiKgueCuQogICAgICB2YXIgaGFsZkNoZWNrZWRLZXlzID0gdGhpcy4kcmVmcy5tZW51LmdldEhhbGZDaGVja2VkS2V5cygpOwogICAgICBjaGVja2VkS2V5cy51bnNoaWZ0LmFwcGx5KGNoZWNrZWRLZXlzLCBoYWxmQ2hlY2tlZEtleXMpOwogICAgICByZXR1cm4gY2hlY2tlZEtleXM7CiAgICB9LAogICAgLy8g5omA5pyJ6YOo6Zeo6IqC54K55pWw5o2uCiAgICBnZXREZXB0QWxsQ2hlY2tlZEtleXM6IGZ1bmN0aW9uIGdldERlcHRBbGxDaGVja2VkS2V5cygpIHsKICAgICAgLy8g55uu5YmN6KKr6YCJ5Lit55qE6YOo6Zeo6IqC54K5CiAgICAgIHZhciBjaGVja2VkS2V5cyA9IHRoaXMuJHJlZnMuZGVwdC5nZXRDaGVja2VkS2V5cygpOwogICAgICAvLyDljYrpgInkuK3nmoTpg6jpl6joioLngrkKICAgICAgdmFyIGhhbGZDaGVja2VkS2V5cyA9IHRoaXMuJHJlZnMuZGVwdC5nZXRIYWxmQ2hlY2tlZEtleXMoKTsKICAgICAgY2hlY2tlZEtleXMudW5zaGlmdC5hcHBseShjaGVja2VkS2V5cywgaGFsZkNoZWNrZWRLZXlzKTsKICAgICAgcmV0dXJuIGNoZWNrZWRLZXlzOwogICAgfSwKICAgIC8qKiDmoLnmja7op5LoibJJROafpeivouiPnOWNleagkee7k+aehCAqL2dldFJvbGVNZW51VHJlZXNlbGVjdDogZnVuY3Rpb24gZ2V0Um9sZU1lbnVUcmVlc2VsZWN0KHJvbGVJZCkgewogICAgICB2YXIgX3RoaXM1ID0gdGhpczsKICAgICAgcmV0dXJuIHJvbGVNZW51VHJlZXNlbGVjdChyb2xlSWQpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgX3RoaXM1Lm1lbnVPcHRpb25zID0gcmVzcG9uc2UubWVudXM7CiAgICAgICAgcmV0dXJuIHJlc3BvbnNlOwogICAgICB9KTsKICAgIH0sCiAgICAvKiog5qC55o2u6KeS6ImySUTmn6Xor6Lpg6jpl6jmoJHnu5PmnoQgKi9nZXRSb2xlRGVwdFRyZWVzZWxlY3Q6IGZ1bmN0aW9uIGdldFJvbGVEZXB0VHJlZXNlbGVjdChyb2xlSWQpIHsKICAgICAgdmFyIF90aGlzNiA9IHRoaXM7CiAgICAgIHJldHVybiByb2xlRGVwdFRyZWVzZWxlY3Qocm9sZUlkKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgIF90aGlzNi5kZXB0T3B0aW9ucyA9IHJlc3BvbnNlLmRlcHRzOwogICAgICAgIHJldHVybiByZXNwb25zZTsKICAgICAgfSk7CiAgICB9LAogICAgLy8g6KeS6Imy54q25oCB5L+u5pS5CiAgICBoYW5kbGVTdGF0dXNDaGFuZ2U6IGZ1bmN0aW9uIGhhbmRsZVN0YXR1c0NoYW5nZShyb3cpIHsKICAgICAgdmFyIF90aGlzNyA9IHRoaXM7CiAgICAgIHZhciB0ZXh0ID0gcm93LnN0YXR1cyA9PT0gIjAiID8gIuWQr+eUqCIgOiAi5YGc55SoIjsKICAgICAgdGhpcy4kY29uZmlybSgn56Gu6K6k6KaBIicgKyB0ZXh0ICsgJyIiJyArIHJvdy5yb2xlTmFtZSArICci6KeS6Imy5ZCXPycsICLorablkYoiLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLAogICAgICAgIHR5cGU6ICJ3YXJuaW5nIgogICAgICB9KS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICByZXR1cm4gY2hhbmdlUm9sZVN0YXR1cyhyb3cucm9sZUlkLCByb3cuc3RhdHVzKTsKICAgICAgfSkudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgX3RoaXM3Lm1zZ1N1Y2Nlc3ModGV4dCArICLmiJDlip8iKTsKICAgICAgfSkuY2F0Y2goZnVuY3Rpb24gKCkgewogICAgICAgIHJvdy5zdGF0dXMgPSByb3cuc3RhdHVzID09PSAiMCIgPyAiMSIgOiAiMCI7CiAgICAgIH0pOwogICAgfSwKICAgIC8vIOWPlua2iOaMiemSrgogICAgY2FuY2VsOiBmdW5jdGlvbiBjYW5jZWwoKSB7CiAgICAgIHRoaXMub3BlbiA9IGZhbHNlOwogICAgICB0aGlzLnJlc2V0KCk7CiAgICB9LAogICAgLy8g5Y+W5raI5oyJ6ZKu77yI5pWw5o2u5p2D6ZmQ77yJCiAgICBjYW5jZWxEYXRhU2NvcGU6IGZ1bmN0aW9uIGNhbmNlbERhdGFTY29wZSgpIHsKICAgICAgdGhpcy5vcGVuRGF0YVNjb3BlID0gZmFsc2U7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgIH0sCiAgICAvLyDooajljZXph43nva4KICAgIHJlc2V0OiBmdW5jdGlvbiByZXNldCgpIHsKICAgICAgaWYgKHRoaXMuJHJlZnMubWVudSAhPSB1bmRlZmluZWQpIHsKICAgICAgICB0aGlzLiRyZWZzLm1lbnUuc2V0Q2hlY2tlZEtleXMoW10pOwogICAgICB9CiAgICAgIHRoaXMubWVudUV4cGFuZCA9IGZhbHNlLCB0aGlzLm1lbnVOb2RlQWxsID0gZmFsc2UsIHRoaXMuZGVwdEV4cGFuZCA9IHRydWUsIHRoaXMuZGVwdE5vZGVBbGwgPSBmYWxzZSwgdGhpcy5mb3JtID0gewogICAgICAgIHJvbGVJZDogdW5kZWZpbmVkLAogICAgICAgIHJvbGVOYW1lOiB1bmRlZmluZWQsCiAgICAgICAgcm9sZUtleTogdW5kZWZpbmVkLAogICAgICAgIHJvbGVTb3J0OiAwLAogICAgICAgIHN0YXR1czogIjAiLAogICAgICAgIG1lbnVJZHM6IFtdLAogICAgICAgIGRlcHRJZHM6IFtdLAogICAgICAgIG1lbnVDaGVja1N0cmljdGx5OiB0cnVlLAogICAgICAgIGRlcHRDaGVja1N0cmljdGx5OiB0cnVlLAogICAgICAgIHJlbWFyazogdW5kZWZpbmVkLAogICAgICAgIGFyZWE6IHVuZGVmaW5lZAogICAgICB9OwogICAgICB0aGlzLnJlc2V0Rm9ybSgiZm9ybSIpOwogICAgfSwKICAgIC8qKiDmkJzntKLmjInpkq7mk43kvZwgKi9oYW5kbGVRdWVyeTogZnVuY3Rpb24gaGFuZGxlUXVlcnkoKSB7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDE7CiAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgfSwKICAgIC8qKiDph43nva7mjInpkq7mk43kvZwgKi9yZXNldFF1ZXJ5OiBmdW5jdGlvbiByZXNldFF1ZXJ5KCkgewogICAgICB0aGlzLmRhdGVSYW5nZSA9IFtdOwogICAgICB0aGlzLnJlc2V0Rm9ybSgicXVlcnlGb3JtIik7CiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsKICAgIH0sCiAgICAvLyDlpJrpgInmoYbpgInkuK3mlbDmja4KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZTogZnVuY3Rpb24gaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgewogICAgICB0aGlzLmlkcyA9IHNlbGVjdGlvbi5tYXAoZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICByZXR1cm4gaXRlbS5yb2xlSWQ7CiAgICAgIH0pOwogICAgICB0aGlzLnNpbmdsZSA9IHNlbGVjdGlvbi5sZW5ndGggIT0gMTsKICAgICAgdGhpcy5tdWx0aXBsZSA9ICFzZWxlY3Rpb24ubGVuZ3RoOwogICAgfSwKICAgIC8vIOagkeadg+mZkO+8iOWxleW8gC/mipjlj6DvvIkKICAgIGhhbmRsZUNoZWNrZWRUcmVlRXhwYW5kOiBmdW5jdGlvbiBoYW5kbGVDaGVja2VkVHJlZUV4cGFuZCh2YWx1ZSwgdHlwZSkgewogICAgICBpZiAodHlwZSA9PSAnbWVudScpIHsKICAgICAgICB2YXIgdHJlZUxpc3QgPSB0aGlzLm1lbnVPcHRpb25zOwogICAgICAgIGZvciAodmFyIGkgPSAwOyBpIDwgdHJlZUxpc3QubGVuZ3RoOyBpKyspIHsKICAgICAgICAgIHRoaXMuJHJlZnMubWVudS5zdG9yZS5ub2Rlc01hcFt0cmVlTGlzdFtpXS5pZF0uZXhwYW5kZWQgPSB2YWx1ZTsKICAgICAgICB9CiAgICAgIH0gZWxzZSBpZiAodHlwZSA9PSAnZGVwdCcpIHsKICAgICAgICB2YXIgX3RyZWVMaXN0ID0gdGhpcy5kZXB0T3B0aW9uczsKICAgICAgICBmb3IgKHZhciBfaSA9IDA7IF9pIDwgX3RyZWVMaXN0Lmxlbmd0aDsgX2krKykgewogICAgICAgICAgdGhpcy4kcmVmcy5kZXB0LnN0b3JlLm5vZGVzTWFwW190cmVlTGlzdFtfaV0uaWRdLmV4cGFuZGVkID0gdmFsdWU7CiAgICAgICAgfQogICAgICB9CiAgICB9LAogICAgLy8g5qCR5p2D6ZmQ77yI5YWo6YCJL+WFqOS4jemAie+8iQogICAgaGFuZGxlQ2hlY2tlZFRyZWVOb2RlQWxsOiBmdW5jdGlvbiBoYW5kbGVDaGVja2VkVHJlZU5vZGVBbGwodmFsdWUsIHR5cGUpIHsKICAgICAgaWYgKHR5cGUgPT0gJ21lbnUnKSB7CiAgICAgICAgdGhpcy4kcmVmcy5tZW51LnNldENoZWNrZWROb2Rlcyh2YWx1ZSA/IHRoaXMubWVudU9wdGlvbnMgOiBbXSk7CiAgICAgIH0gZWxzZSBpZiAodHlwZSA9PSAnZGVwdCcpIHsKICAgICAgICB0aGlzLiRyZWZzLmRlcHQuc2V0Q2hlY2tlZE5vZGVzKHZhbHVlID8gdGhpcy5kZXB0T3B0aW9ucyA6IFtdKTsKICAgICAgfQogICAgfSwKICAgIC8vIOagkeadg+mZkO+8iOeItuWtkOiBlOWKqO+8iQogICAgaGFuZGxlQ2hlY2tlZFRyZWVDb25uZWN0OiBmdW5jdGlvbiBoYW5kbGVDaGVja2VkVHJlZUNvbm5lY3QodmFsdWUsIHR5cGUpIHsKICAgICAgaWYgKHR5cGUgPT0gJ21lbnUnKSB7CiAgICAgICAgdGhpcy5mb3JtLm1lbnVDaGVja1N0cmljdGx5ID0gdmFsdWUgPyB0cnVlIDogZmFsc2U7CiAgICAgIH0gZWxzZSBpZiAodHlwZSA9PSAnZGVwdCcpIHsKICAgICAgICB0aGlzLmZvcm0uZGVwdENoZWNrU3RyaWN0bHkgPSB2YWx1ZSA/IHRydWUgOiBmYWxzZTsKICAgICAgfQogICAgfSwKICAgIC8qKiDmlrDlop7mjInpkq7mk43kvZwgKi9oYW5kbGVBZGQ6IGZ1bmN0aW9uIGhhbmRsZUFkZCgpIHsKICAgICAgdGhpcy5yZXNldCgpOwogICAgICB0aGlzLmdldE1lbnVUcmVlc2VsZWN0KCk7CiAgICAgIHRoaXMub3BlbiA9IHRydWU7CiAgICAgIHRoaXMudGl0bGUgPSAi5re75Yqg6KeS6ImyIjsKICAgIH0sCiAgICAvKiog5L+u5pS55oyJ6ZKu5pON5L2cICovaGFuZGxlVXBkYXRlOiBmdW5jdGlvbiBoYW5kbGVVcGRhdGUocm93KSB7CiAgICAgIHZhciBfdGhpczggPSB0aGlzOwogICAgICB0aGlzLnJlc2V0KCk7CiAgICAgIHZhciByb2xlSWQgPSByb3cucm9sZUlkIHx8IHRoaXMuaWRzOwogICAgICB2YXIgcm9sZU1lbnUgPSB0aGlzLmdldFJvbGVNZW51VHJlZXNlbGVjdChyb2xlSWQpOwogICAgICBnZXRSb2xlKHJvbGVJZCkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICBfdGhpczguZm9ybSA9IHJlc3BvbnNlLmRhdGE7CiAgICAgICAgX3RoaXM4Lm9wZW4gPSB0cnVlOwogICAgICAgIF90aGlzOC4kbmV4dFRpY2soZnVuY3Rpb24gKCkgewogICAgICAgICAgcm9sZU1lbnUudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgICAgIF90aGlzOC4kcmVmcy5tZW51LnNldENoZWNrZWRLZXlzKHJlcy5jaGVja2VkS2V5cyk7CiAgICAgICAgICB9KTsKICAgICAgICB9KTsKICAgICAgICBfdGhpczgudGl0bGUgPSAi5L+u5pS56KeS6ImyIjsKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOWIhumFjeaVsOaNruadg+mZkOaTjeS9nCAqL2hhbmRsZURhdGFTY29wZTogZnVuY3Rpb24gaGFuZGxlRGF0YVNjb3BlKHJvdykgewogICAgICB2YXIgX3RoaXM5ID0gdGhpczsKICAgICAgdGhpcy5yZXNldCgpOwogICAgICB2YXIgcm9sZURlcHRUcmVlc2VsZWN0ID0gdGhpcy5nZXRSb2xlRGVwdFRyZWVzZWxlY3Qocm93LnJvbGVJZCk7CiAgICAgIGdldFJvbGUocm93LnJvbGVJZCkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICBfdGhpczkuZm9ybSA9IHJlc3BvbnNlLmRhdGE7CiAgICAgICAgX3RoaXM5Lm9wZW5EYXRhU2NvcGUgPSB0cnVlOwogICAgICAgIF90aGlzOS4kbmV4dFRpY2soZnVuY3Rpb24gKCkgewogICAgICAgICAgcm9sZURlcHRUcmVlc2VsZWN0LnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgICAgICBfdGhpczkuJHJlZnMuZGVwdC5zZXRDaGVja2VkS2V5cyhyZXMuY2hlY2tlZEtleXMpOwogICAgICAgICAgfSk7CiAgICAgICAgfSk7CiAgICAgICAgX3RoaXM5LnRpdGxlID0gIuWIhumFjeaVsOaNruadg+mZkCI7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDmj5DkuqTmjInpkq4gKi8KICAgIHN1Ym1pdEZvcm06IGZ1bmN0aW9uIHN1Ym1pdEZvcm0oKSB7CiAgICAgIHZhciBfdGhpczEwID0gdGhpczsKICAgICAgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKGZ1bmN0aW9uICh2YWxpZCkgewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgaWYgKF90aGlzMTAuZm9ybS5yb2xlSWQgIT0gdW5kZWZpbmVkKSB7CiAgICAgICAgICAgIF90aGlzMTAuZm9ybS5tZW51SWRzID0gX3RoaXMxMC5nZXRNZW51QWxsQ2hlY2tlZEtleXMoKTsKICAgICAgICAgICAgdXBkYXRlUm9sZShfdGhpczEwLmZvcm0pLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgICAgICAgX3RoaXMxMC5tc2dTdWNjZXNzKCLkv67mlLnmiJDlip8iKTsKICAgICAgICAgICAgICBfdGhpczEwLm9wZW4gPSBmYWxzZTsKICAgICAgICAgICAgICBfdGhpczEwLmdldExpc3QoKTsKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICBfdGhpczEwLmZvcm0ubWVudUlkcyA9IF90aGlzMTAuZ2V0TWVudUFsbENoZWNrZWRLZXlzKCk7CiAgICAgICAgICAgIGFkZFJvbGUoX3RoaXMxMC5mb3JtKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgICAgICAgIF90aGlzMTAubXNnU3VjY2Vzcygi5paw5aKe5oiQ5YqfIik7CiAgICAgICAgICAgICAgX3RoaXMxMC5vcGVuID0gZmFsc2U7CiAgICAgICAgICAgICAgX3RoaXMxMC5nZXRMaXN0KCk7CiAgICAgICAgICAgIH0pOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLyoqIOaPkOS6pOaMiemSru+8iOaVsOaNruadg+mZkO+8iSAqLwogICAgc3VibWl0RGF0YVNjb3BlOiBmdW5jdGlvbiBzdWJtaXREYXRhU2NvcGUoKSB7CiAgICAgIHZhciBfdGhpczExID0gdGhpczsKICAgICAgLy9jb25zb2xlLmxvZyh0aGlzLmZvcm0pOwogICAgICBpZiAodGhpcy5mb3JtLnJvbGVJZCAhPSB1bmRlZmluZWQpIHsKICAgICAgICB0aGlzLmZvcm0uZGVwdElkcyA9IHRoaXMuZ2V0RGVwdEFsbENoZWNrZWRLZXlzKCk7CiAgICAgICAgZGF0YVNjb3BlKHRoaXMuZm9ybSkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICAgIF90aGlzMTEubXNnU3VjY2Vzcygi5L+u5pS55oiQ5YqfIik7CiAgICAgICAgICBfdGhpczExLm9wZW5EYXRhU2NvcGUgPSBmYWxzZTsKICAgICAgICAgIF90aGlzMTEuZ2V0TGlzdCgpOwogICAgICAgIH0pOwogICAgICB9CiAgICB9LAogICAgLyoqIOWIoOmZpOaMiemSruaTjeS9nCAqL2hhbmRsZURlbGV0ZTogZnVuY3Rpb24gaGFuZGxlRGVsZXRlKHJvdykgewogICAgICB2YXIgX3RoaXMxMiA9IHRoaXM7CiAgICAgIHZhciByb2xlSWRzID0gcm93LnJvbGVJZCB8fCB0aGlzLmlkczsKICAgICAgdGhpcy4kY29uZmlybSgn5piv5ZCm56Gu6K6k5Yig6Zmk6KeS6Imy57yW5Y+35Li6IicgKyByb2xlSWRzICsgJyLnmoTmlbDmja7pobk/JywgIuitpuWRiiIsIHsKICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogIuehruWumiIsCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogIuWPlua2iCIsCiAgICAgICAgdHlwZTogIndhcm5pbmciCiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgIHJldHVybiBkZWxSb2xlKHJvbGVJZHMpOwogICAgICB9KS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICBfdGhpczEyLmdldExpc3QoKTsKICAgICAgICBfdGhpczEyLm1zZ1N1Y2Nlc3MoIuWIoOmZpOaIkOWKnyIpOwogICAgICB9KTsKICAgIH0sCiAgICAvKiog5a+85Ye65oyJ6ZKu5pON5L2cICovaGFuZGxlRXhwb3J0OiBmdW5jdGlvbiBoYW5kbGVFeHBvcnQoKSB7CiAgICAgIHZhciBfdGhpczEzID0gdGhpczsKICAgICAgdmFyIHF1ZXJ5UGFyYW1zID0gdGhpcy5xdWVyeVBhcmFtczsKICAgICAgdGhpcy4kY29uZmlybSgn5piv5ZCm56Gu6K6k5a+85Ye65omA5pyJ6KeS6Imy5pWw5o2u6aG5PycsICLorablkYoiLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLAogICAgICAgIHR5cGU6ICJ3YXJuaW5nIgogICAgICB9KS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICByZXR1cm4gZXhwb3J0Um9sZShxdWVyeVBhcmFtcyk7CiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgX3RoaXMxMy5kb3dubG9hZChyZXNwb25zZS5tc2cpOwogICAgICB9KTsKICAgIH0KICB9Cn07"}, null]}