{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/settings.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/settings.js", "mtime": 1660747928000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:bW9kdWxlLmV4cG9ydHMgPSB7CiAgdGl0bGU6ICfpobnnm67nrqHnkIbns7vnu58nLAogIC8qKgogICAqIOS+p+i+ueagj+S4u+mimCDmt7HoibLkuLvpoph0aGVtZS1kYXJr77yM5rWF6Imy5Li76aKYdGhlbWUtbGlnaHQKICAgKi8KICBzaWRlVGhlbWU6ICd0aGVtZS1kYXJrJywKICAvKioKICAgKiDmmK/lkKbns7vnu5/luIPlsYDphY3nva4KICAgKi8KICBzaG93U2V0dGluZ3M6IGZhbHNlLAogIC8qKgogICAqIOaYr+WQpuaYvuekuiB0YWdzVmlldwogICAqLwogIHRhZ3NWaWV3OiB0cnVlLAogIC8qKgogICAqIOaYr+WQpuWbuuWumuWktOmDqAogICAqLwogIGZpeGVkSGVhZGVyOiBmYWxzZSwKICAvKioKICAgKiDmmK/lkKbmmL7npLpsb2dvCiAgICovCiAgc2lkZWJhckxvZ286IHRydWUsCiAgLyoqCiAgICogQHR5cGUge3N0cmluZyB8IGFycmF5fSAncHJvZHVjdGlvbicgfCBbJ3Byb2R1Y3Rpb24nLCAnZGV2ZWxvcG1lbnQnXQogICAqIEBkZXNjcmlwdGlvbiBOZWVkIHNob3cgZXJyIGxvZ3MgY29tcG9uZW50LgogICAqIFRoZSBkZWZhdWx0IGlzIG9ubHkgdXNlZCBpbiB0aGUgcHJvZHVjdGlvbiBlbnYKICAgKiBJZiB5b3Ugd2FudCB0byBhbHNvIHVzZSBpdCBpbiBkZXYsIHlvdSBjYW4gcGFzcyBbJ3Byb2R1Y3Rpb24nLCAnZGV2ZWxvcG1lbnQnXQogICAqLwogIGVycm9yTG9nOiAncHJvZHVjdGlvbicKfTs="}, {"version": 3, "names": ["module", "exports", "title", "sideTheme", "showSettings", "tagsView", "fixedHeader", "sidebarLogo", "errorLog"], "sources": ["/Users/<USER>/develop/code/project/haijia/adminweb/src/settings.js"], "sourcesContent": ["module.exports = {\n  title: '项目管理系统',\n\n  /**\n   * 侧边栏主题 深色主题theme-dark，浅色主题theme-light\n   */\n  sideTheme: 'theme-dark',\n\n  /**\n   * 是否系统布局配置\n   */\n  showSettings: false,\n\n  /**\n   * 是否显示 tagsView\n   */\n  tagsView: true,\n\n  /**\n   * 是否固定头部\n   */\n  fixedHeader: false,\n\n  /**\n   * 是否显示logo\n   */\n  sidebarLogo: true,\n\n  /**\n   * @type {string | array} 'production' | ['production', 'development']\n   * @description Need show err logs component.\n   * The default is only used in the production env\n   * If you want to also use it in dev, you can pass ['production', 'development']\n   */\n  errorLog: 'production'\n}\n"], "mappings": "AAAAA,MAAM,CAACC,OAAO,GAAG;EACfC,KAAK,EAAE,QAAQ;EAEf;AACF;AACA;EACEC,SAAS,EAAE,YAAY;EAEvB;AACF;AACA;EACEC,YAAY,EAAE,KAAK;EAEnB;AACF;AACA;EACEC,QAAQ,EAAE,IAAI;EAEd;AACF;AACA;EACEC,WAAW,EAAE,KAAK;EAElB;AACF;AACA;EACEC,WAAW,EAAE,IAAI;EAEjB;AACF;AACA;AACA;AACA;AACA;EACEC,QAAQ,EAAE;AACZ,CAAC", "ignoreList": []}]}