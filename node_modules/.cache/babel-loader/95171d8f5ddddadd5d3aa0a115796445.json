{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Process/PropertyPanel.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Process/PropertyPanel.vue", "mtime": 1641277326000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["taskPanel", "startEndPanel", "processPanel", "sequenceFlowPanel", "gatewayPanel", "NodeName", "name", "components", "props", "users", "type", "Array", "required", "groups", "categorys", "modeler", "Object", "data", "element", "form", "id", "color", "roles", "value", "label", "computed", "getComponent", "_this$element", "includes", "nodeName", "bizObj", "businessObject", "eventDefinitions", "$type", "mounted", "handleModeler", "methods", "_this", "on", "e", "$nextTick", "then", "console", "log", "newSelection", "dataType", "$emit"], "sources": ["src/components/Process/PropertyPanel.vue"], "sourcesContent": ["<template>\n  <div ref=\"propertyPanel\" class=\"property-panel\">\n    <div v-if=\"nodeName\" class=\"node-name\">{{ nodeName }}</div>\n    <component\n      :is=\"getComponent\"\n      v-if=\"element\"\n      :element=\"element\"\n      :modeler=\"modeler\"\n      :users=\"users\"\n      :groups=\"groups\"\n      :categorys=\"categorys\"\n      @dataType=\"dataType\"\n    />\n  </div>\n</template>\n\n<script>\nimport taskPanel from './components/nodePanel/task'\nimport startEndPanel from './components/nodePanel/startEnd'\nimport processPanel from './components/nodePanel/process'\nimport sequenceFlowPanel from './components/nodePanel/sequenceFlow'\nimport gatewayPanel from './components/nodePanel/gateway'\nimport { NodeName } from './lang/zh'\n\nexport default {\n  name: 'PropertyPanel',\n  components: { processPanel, taskPanel, startEndPanel, sequenceFlowPanel, gatewayPanel },\n  props: {\n    users: {\n      type: Array,\n      required: true\n    },\n    groups: {\n      type: Array,\n      required: true\n    },\n    categorys: {\n      type: Array,\n      required: true\n    },\n    modeler: {\n      type: Object,\n      required: true\n    }\n  },\n  data() {\n    return {\n      element: null,\n      form: {\n        id: '',\n        name: '',\n        color: null\n      },\n      roles: [\n        { value: 'manager', label: '经理' },\n        { value: 'personnel', label: '人事' },\n        { value: 'charge', label: '主管' }\n      ]\n    }\n  },\n  computed: {\n    getComponent() {\n      const type = this.element?.type\n      if (['bpmn:IntermediateThrowEvent', 'bpmn:StartEvent', 'bpmn:EndEvent'].includes(type)) {\n        return 'startEndPanel'\n      }\n      if ([\n        'bpmn:UserTask',\n        'bpmn:Task',\n        'bpmn:SendTask',\n        'bpmn:ReceiveTask',\n        'bpmn:ManualTask',\n        'bpmn:BusinessRuleTask',\n        'bpmn:ServiceTask',\n        'bpmn:ScriptTask'\n        // 'bpmn:CallActivity',\n        // 'bpmn:SubProcess'\n      ].includes(type)) {\n        return 'taskPanel'\n      }\n      if (type === 'bpmn:SequenceFlow') {\n        return 'sequenceFlowPanel'\n      }\n      if ([\n        'bpmn:InclusiveGateway',\n        'bpmn:ExclusiveGateway',\n        'bpmn:ParallelGateway',\n        'bpmn:EventBasedGateway'\n      ].includes(type)) {\n        return 'gatewayPanel'\n      }\n      if (type === 'bpmn:Process') {\n        return 'processPanel'\n      }\n      return null\n    },\n    nodeName() {\n      if (this.element) {\n        const bizObj = this.element.businessObject\n        const type = bizObj?.eventDefinitions\n          ? bizObj.eventDefinitions[0].$type\n          : bizObj.$type\n        return NodeName[type] || type\n      }\n      return ''\n    }\n  },\n  mounted() {\n    this.handleModeler()\n  },\n  methods: {\n    handleModeler() {\n      this.modeler.on('root.added', e => {\n        if (e.element.type === 'bpmn:Process') {\n          this.element = null\n          this.$nextTick().then(() => {\n            this.element = e.element\n          })\n        }\n      })\n      this.modeler.on('element.click', e => {\n        const { element } = e\n        console.log(element)\n        if (element.type === 'bpmn:Process') {\n          this.element = element\n        }\n      })\n      this.modeler.on('selection.changed', e => {\n        // hack 同类型面板不刷新\n        this.element = null\n        const element = e.newSelection[0]\n        if (element) {\n          this.$nextTick().then(() => {\n            this.element = element\n          })\n        }\n      })\n    },\n    /** 获取数据类型 */\n    dataType(data){\n      this.$emit('dataType', data)\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\">\n.property-panel {\n  padding: 20px 20px;\n  // reset element css\n  .el-form--label-top .el-form-item__label {\n    padding: 0;\n  }\n  .el-form-item {\n    margin-bottom: 6px;\n  }\n  .tab-table .el-form-item {\n    margin-bottom: 16px;\n  }\n  .node-name{\n    border-bottom: 1px solid #ccc;\n    padding: 0 0 10px 20px;\n    margin-bottom: 10px;\n    font-size: 16px;\n    font-weight: bold;\n    color: #444;\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;AAiBA,OAAAA,SAAA;AACA,OAAAC,aAAA;AACA,OAAAC,YAAA;AACA,OAAAC,iBAAA;AACA,OAAAC,YAAA;AACA,SAAAC,QAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IAAAL,YAAA,EAAAA,YAAA;IAAAF,SAAA,EAAAA,SAAA;IAAAC,aAAA,EAAAA,aAAA;IAAAE,iBAAA,EAAAA,iBAAA;IAAAC,YAAA,EAAAA;EAAA;EACAI,KAAA;IACAC,KAAA;MACAC,IAAA,EAAAC,KAAA;MACAC,QAAA;IACA;IACAC,MAAA;MACAH,IAAA,EAAAC,KAAA;MACAC,QAAA;IACA;IACAE,SAAA;MACAJ,IAAA,EAAAC,KAAA;MACAC,QAAA;IACA;IACAG,OAAA;MACAL,IAAA,EAAAM,MAAA;MACAJ,QAAA;IACA;EACA;EACAK,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,IAAA;QACAC,EAAA;QACAd,IAAA;QACAe,KAAA;MACA;MACAC,KAAA,GACA;QAAAC,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA;IAEA;EACA;EACAC,QAAA;IACAC,YAAA,WAAAA,aAAA;MAAA,IAAAC,aAAA;MACA,IAAAjB,IAAA,IAAAiB,aAAA,QAAAT,OAAA,cAAAS,aAAA,uBAAAA,aAAA,CAAAjB,IAAA;MACA,wEAAAkB,QAAA,CAAAlB,IAAA;QACA;MACA;MACA,KACA,iBACA,aACA,iBACA,oBACA,mBACA,yBACA,oBACA;MACA;MACA;MAAA,CACA,CAAAkB,QAAA,CAAAlB,IAAA;QACA;MACA;MACA,IAAAA,IAAA;QACA;MACA;MACA,KACA,yBACA,yBACA,wBACA,yBACA,CAAAkB,QAAA,CAAAlB,IAAA;QACA;MACA;MACA,IAAAA,IAAA;QACA;MACA;MACA;IACA;IACAmB,QAAA,WAAAA,SAAA;MACA,SAAAX,OAAA;QACA,IAAAY,MAAA,QAAAZ,OAAA,CAAAa,cAAA;QACA,IAAArB,IAAA,GAAAoB,MAAA,aAAAA,MAAA,eAAAA,MAAA,CAAAE,gBAAA,GACAF,MAAA,CAAAE,gBAAA,IAAAC,KAAA,GACAH,MAAA,CAAAG,KAAA;QACA,OAAA5B,QAAA,CAAAK,IAAA,KAAAA,IAAA;MACA;MACA;IACA;EACA;EACAwB,OAAA,WAAAA,QAAA;IACA,KAAAC,aAAA;EACA;EACAC,OAAA;IACAD,aAAA,WAAAA,cAAA;MAAA,IAAAE,KAAA;MACA,KAAAtB,OAAA,CAAAuB,EAAA,yBAAAC,CAAA;QACA,IAAAA,CAAA,CAAArB,OAAA,CAAAR,IAAA;UACA2B,KAAA,CAAAnB,OAAA;UACAmB,KAAA,CAAAG,SAAA,GAAAC,IAAA;YACAJ,KAAA,CAAAnB,OAAA,GAAAqB,CAAA,CAAArB,OAAA;UACA;QACA;MACA;MACA,KAAAH,OAAA,CAAAuB,EAAA,4BAAAC,CAAA;QACA,IAAArB,OAAA,GAAAqB,CAAA,CAAArB,OAAA;QACAwB,OAAA,CAAAC,GAAA,CAAAzB,OAAA;QACA,IAAAA,OAAA,CAAAR,IAAA;UACA2B,KAAA,CAAAnB,OAAA,GAAAA,OAAA;QACA;MACA;MACA,KAAAH,OAAA,CAAAuB,EAAA,gCAAAC,CAAA;QACA;QACAF,KAAA,CAAAnB,OAAA;QACA,IAAAA,OAAA,GAAAqB,CAAA,CAAAK,YAAA;QACA,IAAA1B,OAAA;UACAmB,KAAA,CAAAG,SAAA,GAAAC,IAAA;YACAJ,KAAA,CAAAnB,OAAA,GAAAA,OAAA;UACA;QACA;MACA;IACA;IACA,aACA2B,QAAA,WAAAA,SAAA5B,IAAA;MACA,KAAA6B,KAAA,aAAA7B,IAAA;IACA;EACA;AACA", "ignoreList": []}]}