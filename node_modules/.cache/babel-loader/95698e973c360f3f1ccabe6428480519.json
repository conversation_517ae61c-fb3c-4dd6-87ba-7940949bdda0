{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/api/tool/gen.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/api/tool/gen.js", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QgZnJvbSAnQC91dGlscy9yZXF1ZXN0JzsKCi8vIOafpeivoueUn+aIkOihqOaVsOaNrgpleHBvcnQgZnVuY3Rpb24gbGlzdFRhYmxlKHF1ZXJ5KSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL3Rvb2wvZ2VuL2xpc3QnLAogICAgbWV0aG9kOiAnZ2V0JywKICAgIHBhcmFtczogcXVlcnkKICB9KTsKfQovLyDmn6Xor6JkYuaVsOaNruW6k+WIl+ihqApleHBvcnQgZnVuY3Rpb24gbGlzdERiVGFibGUocXVlcnkpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvdG9vbC9nZW4vZGIvbGlzdCcsCiAgICBtZXRob2Q6ICdnZXQnLAogICAgcGFyYW1zOiBxdWVyeQogIH0pOwp9CgovLyDmn6Xor6Looajor6bnu4bkv6Hmga8KZXhwb3J0IGZ1bmN0aW9uIGdldEdlblRhYmxlKHRhYmxlSWQpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvdG9vbC9nZW4vJyArIHRhYmxlSWQsCiAgICBtZXRob2Q6ICdnZXQnCiAgfSk7Cn0KCi8vIOS/ruaUueS7o+eggeeUn+aIkOS/oeaBrwpleHBvcnQgZnVuY3Rpb24gdXBkYXRlR2VuVGFibGUoZGF0YSkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy90b29sL2dlbicsCiAgICBtZXRob2Q6ICdwdXQnLAogICAgZGF0YTogZGF0YQogIH0pOwp9CgovLyDlr7zlhaXooagKZXhwb3J0IGZ1bmN0aW9uIGltcG9ydFRhYmxlKGRhdGEpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvdG9vbC9nZW4vaW1wb3J0VGFibGUnLAogICAgbWV0aG9kOiAncG9zdCcsCiAgICBwYXJhbXM6IGRhdGEKICB9KTsKfQoKLy8g6aKE6KeI55Sf5oiQ5Luj56CBCmV4cG9ydCBmdW5jdGlvbiBwcmV2aWV3VGFibGUodGFibGVJZCkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy90b29sL2dlbi9wcmV2aWV3LycgKyB0YWJsZUlkLAogICAgbWV0aG9kOiAnZ2V0JwogIH0pOwp9CgovLyDliKDpmaTooajmlbDmja4KZXhwb3J0IGZ1bmN0aW9uIGRlbFRhYmxlKHRhYmxlSWQpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvdG9vbC9nZW4vJyArIHRhYmxlSWQsCiAgICBtZXRob2Q6ICdkZWxldGUnCiAgfSk7Cn0KCi8vIOeUn+aIkOS7o+egge+8iOiHquWumuS5iei3r+W+hO+8iQpleHBvcnQgZnVuY3Rpb24gZ2VuQ29kZSh0YWJsZU5hbWUpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvdG9vbC9nZW4vZ2VuQ29kZS8nICsgdGFibGVOYW1lLAogICAgbWV0aG9kOiAnZ2V0JwogIH0pOwp9CgovLyDlkIzmraXmlbDmja7lupMKZXhwb3J0IGZ1bmN0aW9uIHN5bmNoRGIodGFibGVOYW1lKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL3Rvb2wvZ2VuL3N5bmNoRGIvJyArIHRhYmxlTmFtZSwKICAgIG1ldGhvZDogJ2dldCcKICB9KTsKfQ=="}, {"version": 3, "names": ["request", "listTable", "query", "url", "method", "params", "listDbTable", "getGenTable", "tableId", "updateGenTable", "data", "importTable", "previewTable", "delTable", "genCode", "tableName", "synchDb"], "sources": ["/Users/<USER>/develop/code/project/haijia/adminweb/src/api/tool/gen.js"], "sourcesContent": ["import request from '@/utils/request'\n\n// 查询生成表数据\nexport function listTable(query) {\n  return request({\n    url: '/tool/gen/list',\n    method: 'get',\n    params: query\n  })\n}\n// 查询db数据库列表\nexport function listDbTable(query) {\n  return request({\n    url: '/tool/gen/db/list',\n    method: 'get',\n    params: query\n  })\n}\n\n// 查询表详细信息\nexport function getGenTable(tableId) {\n  return request({\n    url: '/tool/gen/' + tableId,\n    method: 'get'\n  })\n}\n\n// 修改代码生成信息\nexport function updateGenTable(data) {\n  return request({\n    url: '/tool/gen',\n    method: 'put',\n    data: data\n  })\n}\n\n// 导入表\nexport function importTable(data) {\n  return request({\n    url: '/tool/gen/importTable',\n    method: 'post',\n    params: data\n  })\n}\n\n// 预览生成代码\nexport function previewTable(tableId) {\n  return request({\n    url: '/tool/gen/preview/' + tableId,\n    method: 'get'\n  })\n}\n\n// 删除表数据\nexport function delTable(tableId) {\n  return request({\n    url: '/tool/gen/' + tableId,\n    method: 'delete'\n  })\n}\n\n// 生成代码（自定义路径）\nexport function genCode(tableName) {\n  return request({\n    url: '/tool/gen/genCode/' + tableName,\n    method: 'get'\n  })\n}\n\n// 同步数据库\nexport function synchDb(tableName) {\n  return request({\n    url: '/tool/gen/synchDb/' + tableName,\n    method: 'get'\n  })\n}\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;;AAErC;AACA,OAAO,SAASC,SAASA,CAACC,KAAK,EAAE;EAC/B,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,gBAAgB;IACrBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEH;EACV,CAAC,CAAC;AACJ;AACA;AACA,OAAO,SAASI,WAAWA,CAACJ,KAAK,EAAE;EACjC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEH;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASK,WAAWA,CAACC,OAAO,EAAE;EACnC,OAAOR,OAAO,CAAC;IACbG,GAAG,EAAE,YAAY,GAAGK,OAAO;IAC3BJ,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASK,cAAcA,CAACC,IAAI,EAAE;EACnC,OAAOV,OAAO,CAAC;IACbG,GAAG,EAAE,WAAW;IAChBC,MAAM,EAAE,KAAK;IACbM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASC,WAAWA,CAACD,IAAI,EAAE;EAChC,OAAOV,OAAO,CAAC;IACbG,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE,MAAM;IACdC,MAAM,EAAEK;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASE,YAAYA,CAACJ,OAAO,EAAE;EACpC,OAAOR,OAAO,CAAC;IACbG,GAAG,EAAE,oBAAoB,GAAGK,OAAO;IACnCJ,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASS,QAAQA,CAACL,OAAO,EAAE;EAChC,OAAOR,OAAO,CAAC;IACbG,GAAG,EAAE,YAAY,GAAGK,OAAO;IAC3BJ,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASU,OAAOA,CAACC,SAAS,EAAE;EACjC,OAAOf,OAAO,CAAC;IACbG,GAAG,EAAE,oBAAoB,GAAGY,SAAS;IACrCX,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASY,OAAOA,CAACD,SAAS,EAAE;EACjC,OAAOf,OAAO,CAAC;IACbG,GAAG,EAAE,oBAAoB,GAAGY,SAAS;IACrCX,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}