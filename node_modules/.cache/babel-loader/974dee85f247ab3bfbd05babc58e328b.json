{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Process/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Process/index.vue", "mtime": 1661782128000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js", "mtime": 1751171659043}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}