{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/api/monitor/operlog.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/api/monitor/operlog.js", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QgZnJvbSAnQC91dGlscy9yZXF1ZXN0JzsKCi8vIOafpeivouaTjeS9nOaXpeW/l+WIl+ihqApleHBvcnQgZnVuY3Rpb24gbGlzdChxdWVyeSkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9tb25pdG9yL29wZXJsb2cvbGlzdCcsCiAgICBtZXRob2Q6ICdnZXQnLAogICAgcGFyYW1zOiBxdWVyeQogIH0pOwp9CgovLyDliKDpmaTmk43kvZzml6Xlv5cKZXhwb3J0IGZ1bmN0aW9uIGRlbE9wZXJsb2cob3BlcklkKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL21vbml0b3Ivb3BlcmxvZy8nICsgb3BlcklkLAogICAgbWV0aG9kOiAnZGVsZXRlJwogIH0pOwp9CgovLyDmuIXnqbrmk43kvZzml6Xlv5cKZXhwb3J0IGZ1bmN0aW9uIGNsZWFuT3BlcmxvZygpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvbW9uaXRvci9vcGVybG9nL2NsZWFuJywKICAgIG1ldGhvZDogJ2RlbGV0ZScKICB9KTsKfQoKLy8g5a+85Ye65pON5L2c5pel5b+XCmV4cG9ydCBmdW5jdGlvbiBleHBvcnRPcGVybG9nKHF1ZXJ5KSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL21vbml0b3Ivb3BlcmxvZy9leHBvcnQnLAogICAgbWV0aG9kOiAnZ2V0JywKICAgIHBhcmFtczogcXVlcnkKICB9KTsKfQ=="}, {"version": 3, "names": ["request", "list", "query", "url", "method", "params", "delOperlog", "operId", "cleanOperlog", "exportOperlog"], "sources": ["/Users/<USER>/develop/code/project/haijia/adminweb/src/api/monitor/operlog.js"], "sourcesContent": ["import request from '@/utils/request'\n\n// 查询操作日志列表\nexport function list(query) {\n  return request({\n    url: '/monitor/operlog/list',\n    method: 'get',\n    params: query\n  })\n}\n\n// 删除操作日志\nexport function delOperlog(operId) {\n  return request({\n    url: '/monitor/operlog/' + operId,\n    method: 'delete'\n  })\n}\n\n// 清空操作日志\nexport function cleanOperlog() {\n  return request({\n    url: '/monitor/operlog/clean',\n    method: 'delete'\n  })\n}\n\n// 导出操作日志\nexport function exportOperlog(query) {\n  return request({\n    url: '/monitor/operlog/export',\n    method: 'get',\n    params: query\n  })\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;;AAErC;AACA,OAAO,SAASC,IAAIA,CAACC,KAAK,EAAE;EAC1B,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEH;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASI,UAAUA,CAACC,MAAM,EAAE;EACjC,OAAOP,OAAO,CAAC;IACbG,GAAG,EAAE,mBAAmB,GAAGI,MAAM;IACjCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASI,YAAYA,CAAA,EAAG;EAC7B,OAAOR,OAAO,CAAC;IACbG,GAAG,EAAE,wBAAwB;IAC7BC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASK,aAAaA,CAACP,KAAK,EAAE;EACnC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,yBAAyB;IAC9BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEH;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}