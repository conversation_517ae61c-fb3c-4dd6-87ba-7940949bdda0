{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/store/modules/user.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/store/modules/user.js", "mtime": 1655042714000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["login", "logout", "getInfo", "getToken", "setToken", "removeToken", "user", "state", "token", "name", "userId", "avatar", "roles", "permissions", "province", "mutations", "SET_TOKEN", "SET_NAME", "SET_ID", "SET_AVATAR", "SET_ROLES", "SET_PERMISSIONS", "SET_PROVINCE", "actions", "<PERSON><PERSON>", "_ref", "userInfo", "commit", "username", "trim", "password", "code", "uuid", "Promise", "resolve", "reject", "then", "res", "catch", "error", "GetInfo", "_ref2", "require", "process", "env", "VUE_APP_BASE_API", "length", "userName", "district", "LogOut", "_ref3", "FedLogOut", "_ref4"], "sources": ["/Users/<USER>/develop/code/project/haijia/adminweb/src/store/modules/user.js"], "sourcesContent": ["import { login, logout, getInfo } from '@/api/login'\nimport { getToken, setToken, removeToken } from '@/utils/auth'\n\nconst user = {\n  state: {\n    token: getToken(),\n    name: '',\n    userId: '',\n    avatar: '',\n    roles: [],\n    permissions: [],\n    province: ''\n  },\n\n  mutations: {\n    SET_TOKEN: (state, token) => {\n      state.token = token\n    },\n    SET_NAME: (state, name) => {\n      state.name = name\n    },\n    SET_ID: (state, userId) => {\n      state.userId = userId\n    },\n    SET_AVATAR: (state, avatar) => {\n      state.avatar = avatar\n    },\n    SET_ROLES: (state, roles) => {\n      state.roles = roles\n    },\n    SET_PERMISSIONS: (state, permissions) => {\n      state.permissions = permissions\n    },\n    SET_PROVINCE: (state, province) => {\n      state.province = province\n    },\n  },\n\n  actions: {\n    // 登录\n    Login({ commit }, userInfo) {\n      const username = userInfo.username.trim()\n      const password = userInfo.password\n      const code = userInfo.code\n      const uuid = userInfo.uuid\n      return new Promise((resolve, reject) => {\n        login(username, password, code, uuid).then(res => {\n          setToken(res.token)\n          commit('SET_TOKEN', res.token)\n          resolve()\n        }).catch(error => {\n          reject(error)\n        })\n      })\n    },\n\n    // 获取用户信息\n    GetInfo({ commit, state }) {\n      return new Promise((resolve, reject) => {\n        getInfo(state.token).then(res => {\n          const user = res.user\n          const avatar = user.avatar == \"\" ? require(\"@/assets/images/profile.png\") : process.env.VUE_APP_BASE_API + user.avatar;\n          if (res.roles && res.roles.length > 0) { // 验证返回的roles是否是一个非空数组\n            commit('SET_ROLES', res.roles)\n            commit('SET_PERMISSIONS', res.permissions)\n          } else {\n            commit('SET_ROLES', ['ROLE_DEFAULT'])\n          }\n          commit('SET_NAME', user.userName)\n          commit('SET_ID', user.userId)\n          commit('SET_AVATAR', avatar)\n          commit('SET_PROVINCE', user.district)\n          resolve(res)\n        }).catch(error => {\n          reject(error)\n        })\n      })\n    },\n    \n    // 退出系统\n    LogOut({ commit, state }) {\n      return new Promise((resolve, reject) => {\n        logout(state.token).then(() => {\n          commit('SET_TOKEN', '')\n          commit('SET_ROLES', [])\n          commit('SET_PERMISSIONS', [])\n          removeToken()\n          resolve()\n        }).catch(error => {\n          reject(error)\n        })\n      })\n    },\n\n    // 前端 登出\n    FedLogOut({ commit }) {\n      return new Promise(resolve => {\n        commit('SET_TOKEN', '')\n        removeToken()\n        resolve()\n      })\n    }\n  }\n}\n\nexport default user\n"], "mappings": "AAAA,SAASA,KAAK,EAAEC,MAAM,EAAEC,OAAO,QAAQ,aAAa;AACpD,SAASC,QAAQ,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,cAAc;AAE9D,IAAMC,IAAI,GAAG;EACXC,KAAK,EAAE;IACLC,KAAK,EAAEL,QAAQ,CAAC,CAAC;IACjBM,IAAI,EAAE,EAAE;IACRC,MAAM,EAAE,EAAE;IACVC,MAAM,EAAE,EAAE;IACVC,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,QAAQ,EAAE;EACZ,CAAC;EAEDC,SAAS,EAAE;IACTC,SAAS,EAAE,SAAAA,UAACT,KAAK,EAAEC,KAAK,EAAK;MAC3BD,KAAK,CAACC,KAAK,GAAGA,KAAK;IACrB,CAAC;IACDS,QAAQ,EAAE,SAAAA,SAACV,KAAK,EAAEE,IAAI,EAAK;MACzBF,KAAK,CAACE,IAAI,GAAGA,IAAI;IACnB,CAAC;IACDS,MAAM,EAAE,SAAAA,OAACX,KAAK,EAAEG,MAAM,EAAK;MACzBH,KAAK,CAACG,MAAM,GAAGA,MAAM;IACvB,CAAC;IACDS,UAAU,EAAE,SAAAA,WAACZ,KAAK,EAAEI,MAAM,EAAK;MAC7BJ,KAAK,CAACI,MAAM,GAAGA,MAAM;IACvB,CAAC;IACDS,SAAS,EAAE,SAAAA,UAACb,KAAK,EAAEK,KAAK,EAAK;MAC3BL,KAAK,CAACK,KAAK,GAAGA,KAAK;IACrB,CAAC;IACDS,eAAe,EAAE,SAAAA,gBAACd,KAAK,EAAEM,WAAW,EAAK;MACvCN,KAAK,CAACM,WAAW,GAAGA,WAAW;IACjC,CAAC;IACDS,YAAY,EAAE,SAAAA,aAACf,KAAK,EAAEO,QAAQ,EAAK;MACjCP,KAAK,CAACO,QAAQ,GAAGA,QAAQ;IAC3B;EACF,CAAC;EAEDS,OAAO,EAAE;IACP;IACAC,KAAK,WAAAA,MAAAC,IAAA,EAAaC,QAAQ,EAAE;MAAA,IAApBC,MAAM,GAAAF,IAAA,CAANE,MAAM;MACZ,IAAMC,QAAQ,GAAGF,QAAQ,CAACE,QAAQ,CAACC,IAAI,CAAC,CAAC;MACzC,IAAMC,QAAQ,GAAGJ,QAAQ,CAACI,QAAQ;MAClC,IAAMC,IAAI,GAAGL,QAAQ,CAACK,IAAI;MAC1B,IAAMC,IAAI,GAAGN,QAAQ,CAACM,IAAI;MAC1B,OAAO,IAAIC,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;QACtCnC,KAAK,CAAC4B,QAAQ,EAAEE,QAAQ,EAAEC,IAAI,EAAEC,IAAI,CAAC,CAACI,IAAI,CAAC,UAAAC,GAAG,EAAI;UAChDjC,QAAQ,CAACiC,GAAG,CAAC7B,KAAK,CAAC;UACnBmB,MAAM,CAAC,WAAW,EAAEU,GAAG,CAAC7B,KAAK,CAAC;UAC9B0B,OAAO,CAAC,CAAC;QACX,CAAC,CAAC,CAACI,KAAK,CAAC,UAAAC,KAAK,EAAI;UAChBJ,MAAM,CAACI,KAAK,CAAC;QACf,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC;IAED;IACAC,OAAO,WAAAA,QAAAC,KAAA,EAAoB;MAAA,IAAjBd,MAAM,GAAAc,KAAA,CAANd,MAAM;QAAEpB,KAAK,GAAAkC,KAAA,CAALlC,KAAK;MACrB,OAAO,IAAI0B,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;QACtCjC,OAAO,CAACK,KAAK,CAACC,KAAK,CAAC,CAAC4B,IAAI,CAAC,UAAAC,GAAG,EAAI;UAC/B,IAAM/B,IAAI,GAAG+B,GAAG,CAAC/B,IAAI;UACrB,IAAMK,MAAM,GAAGL,IAAI,CAACK,MAAM,IAAI,EAAE,GAAG+B,OAAO,CAAC,6BAA6B,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,gBAAgB,GAAGvC,IAAI,CAACK,MAAM;UACtH,IAAI0B,GAAG,CAACzB,KAAK,IAAIyB,GAAG,CAACzB,KAAK,CAACkC,MAAM,GAAG,CAAC,EAAE;YAAE;YACvCnB,MAAM,CAAC,WAAW,EAAEU,GAAG,CAACzB,KAAK,CAAC;YAC9Be,MAAM,CAAC,iBAAiB,EAAEU,GAAG,CAACxB,WAAW,CAAC;UAC5C,CAAC,MAAM;YACLc,MAAM,CAAC,WAAW,EAAE,CAAC,cAAc,CAAC,CAAC;UACvC;UACAA,MAAM,CAAC,UAAU,EAAErB,IAAI,CAACyC,QAAQ,CAAC;UACjCpB,MAAM,CAAC,QAAQ,EAAErB,IAAI,CAACI,MAAM,CAAC;UAC7BiB,MAAM,CAAC,YAAY,EAAEhB,MAAM,CAAC;UAC5BgB,MAAM,CAAC,cAAc,EAAErB,IAAI,CAAC0C,QAAQ,CAAC;UACrCd,OAAO,CAACG,GAAG,CAAC;QACd,CAAC,CAAC,CAACC,KAAK,CAAC,UAAAC,KAAK,EAAI;UAChBJ,MAAM,CAACI,KAAK,CAAC;QACf,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC;IAED;IACAU,MAAM,WAAAA,OAAAC,KAAA,EAAoB;MAAA,IAAjBvB,MAAM,GAAAuB,KAAA,CAANvB,MAAM;QAAEpB,KAAK,GAAA2C,KAAA,CAAL3C,KAAK;MACpB,OAAO,IAAI0B,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;QACtClC,MAAM,CAACM,KAAK,CAACC,KAAK,CAAC,CAAC4B,IAAI,CAAC,YAAM;UAC7BT,MAAM,CAAC,WAAW,EAAE,EAAE,CAAC;UACvBA,MAAM,CAAC,WAAW,EAAE,EAAE,CAAC;UACvBA,MAAM,CAAC,iBAAiB,EAAE,EAAE,CAAC;UAC7BtB,WAAW,CAAC,CAAC;UACb6B,OAAO,CAAC,CAAC;QACX,CAAC,CAAC,CAACI,KAAK,CAAC,UAAAC,KAAK,EAAI;UAChBJ,MAAM,CAACI,KAAK,CAAC;QACf,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC;IAED;IACAY,SAAS,WAAAA,UAAAC,KAAA,EAAa;MAAA,IAAVzB,MAAM,GAAAyB,KAAA,CAANzB,MAAM;MAChB,OAAO,IAAIM,OAAO,CAAC,UAAAC,OAAO,EAAI;QAC5BP,MAAM,CAAC,WAAW,EAAE,EAAE,CAAC;QACvBtB,WAAW,CAAC,CAAC;QACb6B,OAAO,CAAC,CAAC;MACX,CAAC,CAAC;IACJ;EACF;AACF,CAAC;AAED,eAAe5B,IAAI", "ignoreList": []}]}