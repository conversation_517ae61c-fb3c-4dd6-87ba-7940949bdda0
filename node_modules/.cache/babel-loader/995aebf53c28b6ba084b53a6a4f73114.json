{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/components/render/slots/el-select.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/components/render/slots/el-select.js", "mtime": 1641277326000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:ZXhwb3J0IGRlZmF1bHQgewogIG9wdGlvbnM6IGZ1bmN0aW9uIG9wdGlvbnMoaCwgY29uZiwga2V5KSB7CiAgICB2YXIgbGlzdCA9IFtdOwogICAgY29uZi5fX3Nsb3RfXy5vcHRpb25zLmZvckVhY2goZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgbGlzdC5wdXNoKGgoImVsLW9wdGlvbiIsIHsKICAgICAgICAiYXR0cnMiOiB7CiAgICAgICAgICAibGFiZWwiOiBpdGVtLmxhYmVsLAogICAgICAgICAgInZhbHVlIjogaXRlbS52YWx1ZSwKICAgICAgICAgICJkaXNhYmxlZCI6IGl0ZW0uZGlzYWJsZWQKICAgICAgICB9CiAgICAgIH0pKTsKICAgIH0pOwogICAgcmV0dXJuIGxpc3Q7CiAgfQp9Ow=="}, {"version": 3, "names": ["options", "h", "conf", "key", "list", "__slot__", "for<PERSON>ach", "item", "push", "label", "value", "disabled"], "sources": ["/Users/<USER>/develop/code/project/haijia/adminweb/src/components/render/slots/el-select.js"], "sourcesContent": ["export default {\n  options(h, conf, key) {\n    const list = []\n    conf.__slot__.options.forEach(item => {\n      list.push(<el-option label={item.label} value={item.value} disabled={item.disabled}></el-option>)\n    })\n    return list\n  }\n}\n"], "mappings": "AAAA,eAAe;EACbA,OAAO,WAAAA,QAACC,CAAC,EAAEC,IAAI,EAAEC,GAAG,EAAE;IACpB,IAAMC,IAAI,GAAG,EAAE;IACfF,IAAI,CAACG,QAAQ,CAACL,OAAO,CAACM,OAAO,CAAC,UAAAC,IAAI,EAAI;MACpCH,IAAI,CAACI,IAAI,CAAAP,CAAA;QAAA;UAAA,SAAmBM,IAAI,CAACE,KAAK;UAAA,SAASF,IAAI,CAACG,KAAK;UAAA,YAAYH,IAAI,CAACI;QAAQ;MAAA,EAAc,CAAC;IACnG,CAAC,CAAC;IACF,OAAOP,IAAI;EACb;AACF,CAAC", "ignoreList": []}]}