{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/utils/generator/html.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/utils/generator/html.js", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js", "mtime": 1751171659043}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}