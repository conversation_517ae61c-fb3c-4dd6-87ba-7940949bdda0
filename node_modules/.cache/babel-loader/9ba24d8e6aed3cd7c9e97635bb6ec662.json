{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/monitor/job/log.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/monitor/job/log.vue", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGxpc3RKb2JMb2csIGRlbEpvYkxvZywgZXhwb3J0Sm9iTG9nLCBjbGVhbkpvYkxvZyB9IGZyb20gIkAvYXBpL21vbml0b3Ivam9iTG9nIjsKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICJKb2JMb2ciLAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICAvLyDpga7nvanlsYIKICAgICAgbG9hZGluZzogdHJ1ZSwKICAgICAgLy8g6YCJ5Lit5pWw57uECiAgICAgIGlkczogW10sCiAgICAgIC8vIOmdnuWkmuS4quemgeeUqAogICAgICBtdWx0aXBsZTogdHJ1ZSwKICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2CiAgICAgIHNob3dTZWFyY2g6IHRydWUsCiAgICAgIC8vIOaAu+adoeaVsAogICAgICB0b3RhbDogMCwKICAgICAgLy8g6LCD5bqm5pel5b+X6KGo5qC85pWw5o2uCiAgICAgIGpvYkxvZ0xpc3Q6IFtdLAogICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYIKICAgICAgb3BlbjogZmFsc2UsCiAgICAgIC8vIOaXpeacn+iMg+WbtAogICAgICBkYXRlUmFuZ2U6IFtdLAogICAgICAvLyDooajljZXlj4LmlbAKICAgICAgZm9ybToge30sCiAgICAgIC8vIOaJp+ihjOeKtuaAgeWtl+WFuAogICAgICBzdGF0dXNPcHRpb25zOiBbXSwKICAgICAgLy8g5Lu75Yqh57uE5ZCN5a2X5YW4CiAgICAgIGpvYkdyb3VwT3B0aW9uczogW10sCiAgICAgIC8vIOafpeivouWPguaVsAogICAgICBxdWVyeVBhcmFtczogewogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgIGpvYk5hbWU6IHVuZGVmaW5lZCwKICAgICAgICBqb2JHcm91cDogdW5kZWZpbmVkLAogICAgICAgIHN0YXR1czogdW5kZWZpbmVkCiAgICAgIH0KICAgIH07CiAgfSwKICBjcmVhdGVkOiBmdW5jdGlvbiBjcmVhdGVkKCkgewogICAgdmFyIF90aGlzID0gdGhpczsKICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgdGhpcy5nZXREaWN0cygic3lzX2pvYl9zdGF0dXMiKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICBfdGhpcy5zdGF0dXNPcHRpb25zID0gcmVzcG9uc2UuZGF0YTsKICAgIH0pOwogICAgdGhpcy5nZXREaWN0cygic3lzX2pvYl9ncm91cCIpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgIF90aGlzLmpvYkdyb3VwT3B0aW9ucyA9IHJlc3BvbnNlLmRhdGE7CiAgICB9KTsKICB9LAogIG1ldGhvZHM6IHsKICAgIC8qKiDmn6Xor6LosIPluqbml6Xlv5fliJfooaggKi9nZXRMaXN0OiBmdW5jdGlvbiBnZXRMaXN0KCkgewogICAgICB2YXIgX3RoaXMyID0gdGhpczsKICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsKICAgICAgbGlzdEpvYkxvZyh0aGlzLmFkZERhdGVSYW5nZSh0aGlzLnF1ZXJ5UGFyYW1zLCB0aGlzLmRhdGVSYW5nZSkpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgX3RoaXMyLmpvYkxvZ0xpc3QgPSByZXNwb25zZS5yb3dzOwogICAgICAgIF90aGlzMi50b3RhbCA9IHJlc3BvbnNlLnRvdGFsOwogICAgICAgIF90aGlzMi5sb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKICAgIC8vIOaJp+ihjOeKtuaAgeWtl+WFuOe/u+ivkQogICAgc3RhdHVzRm9ybWF0OiBmdW5jdGlvbiBzdGF0dXNGb3JtYXQocm93LCBjb2x1bW4pIHsKICAgICAgcmV0dXJuIHRoaXMuc2VsZWN0RGljdExhYmVsKHRoaXMuc3RhdHVzT3B0aW9ucywgcm93LnN0YXR1cyk7CiAgICB9LAogICAgLy8g5Lu75Yqh57uE5ZCN5a2X5YW457+76K+RCiAgICBqb2JHcm91cEZvcm1hdDogZnVuY3Rpb24gam9iR3JvdXBGb3JtYXQocm93LCBjb2x1bW4pIHsKICAgICAgcmV0dXJuIHRoaXMuc2VsZWN0RGljdExhYmVsKHRoaXMuam9iR3JvdXBPcHRpb25zLCByb3cuam9iR3JvdXApOwogICAgfSwKICAgIC8qKiDmkJzntKLmjInpkq7mk43kvZwgKi9oYW5kbGVRdWVyeTogZnVuY3Rpb24gaGFuZGxlUXVlcnkoKSB7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDE7CiAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgfSwKICAgIC8qKiDph43nva7mjInpkq7mk43kvZwgKi9yZXNldFF1ZXJ5OiBmdW5jdGlvbiByZXNldFF1ZXJ5KCkgewogICAgICB0aGlzLmRhdGVSYW5nZSA9IFtdOwogICAgICB0aGlzLnJlc2V0Rm9ybSgicXVlcnlGb3JtIik7CiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsKICAgIH0sCiAgICAvLyDlpJrpgInmoYbpgInkuK3mlbDmja4KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZTogZnVuY3Rpb24gaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgewogICAgICB0aGlzLmlkcyA9IHNlbGVjdGlvbi5tYXAoZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICByZXR1cm4gaXRlbS5qb2JMb2dJZDsKICAgICAgfSk7CiAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aDsKICAgIH0sCiAgICAvKiog6K+m57uG5oyJ6ZKu5pON5L2cICovaGFuZGxlVmlldzogZnVuY3Rpb24gaGFuZGxlVmlldyhyb3cpIHsKICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsKICAgICAgdGhpcy5mb3JtID0gcm93OwogICAgfSwKICAgIC8qKiDliKDpmaTmjInpkq7mk43kvZwgKi9oYW5kbGVEZWxldGU6IGZ1bmN0aW9uIGhhbmRsZURlbGV0ZShyb3cpIHsKICAgICAgdmFyIF90aGlzMyA9IHRoaXM7CiAgICAgIHZhciBqb2JMb2dJZHMgPSB0aGlzLmlkczsKICAgICAgdGhpcy4kY29uZmlybSgn5piv5ZCm56Gu6K6k5Yig6Zmk6LCD5bqm5pel5b+X57yW5Y+35Li6IicgKyBqb2JMb2dJZHMgKyAnIueahOaVsOaNrumhuT8nLCAi6K2m5ZGKIiwgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAi56Gu5a6aIiwKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAi5Y+W5raIIiwKICAgICAgICB0eXBlOiAid2FybmluZyIKICAgICAgfSkudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgcmV0dXJuIGRlbEpvYkxvZyhqb2JMb2dJZHMpOwogICAgICB9KS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICBfdGhpczMuZ2V0TGlzdCgpOwogICAgICAgIF90aGlzMy5tc2dTdWNjZXNzKCLliKDpmaTmiJDlip8iKTsKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOa4heepuuaMiemSruaTjeS9nCAqL2hhbmRsZUNsZWFuOiBmdW5jdGlvbiBoYW5kbGVDbGVhbigpIHsKICAgICAgdmFyIF90aGlzNCA9IHRoaXM7CiAgICAgIHRoaXMuJGNvbmZpcm0oIuaYr+WQpuehruiupOa4heepuuaJgOacieiwg+W6puaXpeW/l+aVsOaNrumhuT8iLCAi6K2m5ZGKIiwgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAi56Gu5a6aIiwKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAi5Y+W5raIIiwKICAgICAgICB0eXBlOiAid2FybmluZyIKICAgICAgfSkudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgcmV0dXJuIGNsZWFuSm9iTG9nKCk7CiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgIF90aGlzNC5nZXRMaXN0KCk7CiAgICAgICAgX3RoaXM0Lm1zZ1N1Y2Nlc3MoIua4heepuuaIkOWKnyIpOwogICAgICB9KTsKICAgIH0sCiAgICAvKiog5a+85Ye65oyJ6ZKu5pON5L2cICovaGFuZGxlRXhwb3J0OiBmdW5jdGlvbiBoYW5kbGVFeHBvcnQoKSB7CiAgICAgIHZhciBfdGhpczUgPSB0aGlzOwogICAgICB2YXIgcXVlcnlQYXJhbXMgPSB0aGlzLnF1ZXJ5UGFyYW1zOwogICAgICB0aGlzLiRjb25maXJtKCLmmK/lkKbnoa7orqTlr7zlh7rmiYDmnInosIPluqbml6Xlv5fmlbDmja7pobk/IiwgIuitpuWRiiIsIHsKICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogIuehruWumiIsCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogIuWPlua2iCIsCiAgICAgICAgdHlwZTogIndhcm5pbmciCiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgIHJldHVybiBleHBvcnRKb2JMb2cocXVlcnlQYXJhbXMpOwogICAgICB9KS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgIF90aGlzNS5kb3dubG9hZChyZXNwb25zZS5tc2cpOwogICAgICB9KTsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["listJobLog", "delJobLog", "exportJobLog", "cleanJobLog", "name", "data", "loading", "ids", "multiple", "showSearch", "total", "jobLogList", "open", "date<PERSON><PERSON><PERSON>", "form", "statusOptions", "jobGroupOptions", "queryParams", "pageNum", "pageSize", "job<PERSON>ame", "undefined", "jobGroup", "status", "created", "_this", "getList", "getDicts", "then", "response", "methods", "_this2", "addDateRange", "rows", "statusFormat", "row", "column", "selectDictLabel", "jobGroupFormat", "handleQuery", "reset<PERSON><PERSON>y", "resetForm", "handleSelectionChange", "selection", "map", "item", "jobLogId", "length", "handleView", "handleDelete", "_this3", "jobLogIds", "$confirm", "confirmButtonText", "cancelButtonText", "type", "msgSuccess", "handleClean", "_this4", "handleExport", "_this5", "download", "msg"], "sources": ["src/views/monitor/job/log.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"任务名称\" prop=\"jobName\">\n        <el-input\n          v-model=\"queryParams.jobName\"\n          placeholder=\"请输入任务名称\"\n          clearable\n          size=\"small\"\n          style=\"width: 240px\"\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"任务组名\" prop=\"jobGroup\">\n        <el-select\n          v-model=\"queryParams.jobGroup\"\n          placeholder=\"请任务组名\"\n          clearable\n          size=\"small\"\n          style=\"width: 240px\"\n        >\n          <el-option\n            v-for=\"dict in jobGroupOptions\"\n            :key=\"dict.dictValue\"\n            :label=\"dict.dictLabel\"\n            :value=\"dict.dictValue\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"执行状态\" prop=\"status\">\n        <el-select\n          v-model=\"queryParams.status\"\n          placeholder=\"请选择执行状态\"\n          clearable\n          size=\"small\"\n          style=\"width: 240px\"\n        >\n          <el-option\n            v-for=\"dict in statusOptions\"\n            :key=\"dict.dictValue\"\n            :label=\"dict.dictLabel\"\n            :value=\"dict.dictValue\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"执行时间\">\n        <el-date-picker\n          v-model=\"dateRange\"\n          size=\"small\"\n          style=\"width: 240px\"\n          value-format=\"yyyy-MM-dd\"\n          type=\"daterange\"\n          range-separator=\"-\"\n          start-placeholder=\"开始日期\"\n          end-placeholder=\"结束日期\"\n        ></el-date-picker>\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"danger\"\n          plain\n          icon=\"el-icon-delete\"\n          size=\"mini\"\n          :disabled=\"multiple\"\n          @click=\"handleDelete\"\n          v-hasPermi=\"['monitor:job:remove']\"\n        >删除</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"danger\"\n          plain\n          icon=\"el-icon-delete\"\n          size=\"mini\"\n          @click=\"handleClean\"\n          v-hasPermi=\"['monitor:job:remove']\"\n        >清空</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"warning\"\n          plain\n          icon=\"el-icon-download\"\n          size=\"mini\"\n          @click=\"handleExport\"\n          v-hasPermi=\"['monitor:job:export']\"\n        >导出</el-button>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <el-table v-loading=\"loading\" :data=\"jobLogList\" @selection-change=\"handleSelectionChange\">\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column label=\"日志编号\" width=\"80\" align=\"center\" prop=\"jobLogId\" />\n      <el-table-column label=\"任务名称\" align=\"center\" prop=\"jobName\" :show-overflow-tooltip=\"true\" />\n      <el-table-column label=\"任务组名\" align=\"center\" prop=\"jobGroup\" :formatter=\"jobGroupFormat\" :show-overflow-tooltip=\"true\" />\n      <el-table-column label=\"调用目标字符串\" align=\"center\" prop=\"invokeTarget\" :show-overflow-tooltip=\"true\" />\n      <el-table-column label=\"日志信息\" align=\"center\" prop=\"jobMessage\" :show-overflow-tooltip=\"true\" />\n      <el-table-column label=\"执行状态\" align=\"center\" prop=\"status\" :formatter=\"statusFormat\" />\n      <el-table-column label=\"执行时间\" align=\"center\" prop=\"createTime\" width=\"180\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.createTime) }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-view\"\n            @click=\"handleView(scope.row)\"\n            v-hasPermi=\"['monitor:job:query']\"\n          >详细</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 调度日志详细 -->\n    <el-dialog title=\"调度日志详细\" :visible.sync=\"open\" width=\"700px\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" label-width=\"100px\" size=\"mini\">\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"日志序号：\">{{ form.jobLogId }}</el-form-item>\n            <el-form-item label=\"任务名称：\">{{ form.jobName }}</el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"任务分组：\">{{ form.jobGroup }}</el-form-item>\n            <el-form-item label=\"执行时间：\">{{ form.createTime }}</el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"调用方法：\">{{ form.invokeTarget }}</el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"日志信息：\">{{ form.jobMessage }}</el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"执行状态：\">\n              <div v-if=\"form.status == 0\">正常</div>\n              <div v-else-if=\"form.status == 1\">失败</div>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"异常信息：\" v-if=\"form.status == 1\">{{ form.exceptionInfo }}</el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"open = false\">关 闭</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { listJobLog, delJobLog, exportJobLog, cleanJobLog } from \"@/api/monitor/jobLog\";\n\nexport default {\n  name: \"JobLog\",\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 调度日志表格数据\n      jobLogList: [],\n      // 是否显示弹出层\n      open: false,\n      // 日期范围\n      dateRange: [],\n      // 表单参数\n      form: {},\n      // 执行状态字典\n      statusOptions: [],\n      // 任务组名字典\n      jobGroupOptions: [],\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        jobName: undefined,\n        jobGroup: undefined,\n        status: undefined\n      }\n    };\n  },\n  created() {\n    this.getList();\n    this.getDicts(\"sys_job_status\").then(response => {\n      this.statusOptions = response.data;\n    });\n    this.getDicts(\"sys_job_group\").then(response => {\n      this.jobGroupOptions = response.data;\n    });\n  },\n  methods: {\n    /** 查询调度日志列表 */\n    getList() {\n      this.loading = true;\n      listJobLog(this.addDateRange(this.queryParams, this.dateRange)).then(response => {\n          this.jobLogList = response.rows;\n          this.total = response.total;\n          this.loading = false;\n        }\n      );\n    },\n    // 执行状态字典翻译\n    statusFormat(row, column) {\n      return this.selectDictLabel(this.statusOptions, row.status);\n    },\n    // 任务组名字典翻译\n    jobGroupFormat(row, column) {\n      return this.selectDictLabel(this.jobGroupOptions, row.jobGroup);\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.dateRange = [];\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.jobLogId);\n      this.multiple = !selection.length;\n    },\n    /** 详细按钮操作 */\n    handleView(row) {\n      this.open = true;\n      this.form = row;\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const jobLogIds = this.ids;\n      this.$confirm('是否确认删除调度日志编号为\"' + jobLogIds + '\"的数据项?', \"警告\", {\n          confirmButtonText: \"确定\",\n          cancelButtonText: \"取消\",\n          type: \"warning\"\n        }).then(function() {\n          return delJobLog(jobLogIds);\n        }).then(() => {\n          this.getList();\n          this.msgSuccess(\"删除成功\");\n        })\n    },\n    /** 清空按钮操作 */\n    handleClean() {\n      this.$confirm(\"是否确认清空所有调度日志数据项?\", \"警告\", {\n          confirmButtonText: \"确定\",\n          cancelButtonText: \"取消\",\n          type: \"warning\"\n        }).then(function() {\n          return cleanJobLog();\n        }).then(() => {\n          this.getList();\n          this.msgSuccess(\"清空成功\");\n        })\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      const queryParams = this.queryParams;\n      this.$confirm(\"是否确认导出所有调度日志数据项?\", \"警告\", {\n          confirmButtonText: \"确定\",\n          cancelButtonText: \"取消\",\n          type: \"warning\"\n        }).then(function() {\n          return exportJobLog(queryParams);\n        }).then(response => {\n          this.download(response.msg);\n        })\n    }\n  }\n};\n</script>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyKA,SAAAA,UAAA,EAAAC,SAAA,EAAAC,YAAA,EAAAC,WAAA;AAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,UAAA;MACA;MACAC,IAAA;MACA;MACAC,SAAA;MACA;MACAC,IAAA;MACA;MACAC,aAAA;MACA;MACAC,eAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,OAAA,EAAAC,SAAA;QACAC,QAAA,EAAAD,SAAA;QACAE,MAAA,EAAAF;MACA;IACA;EACA;EACAG,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA,KAAAC,OAAA;IACA,KAAAC,QAAA,mBAAAC,IAAA,WAAAC,QAAA;MACAJ,KAAA,CAAAV,aAAA,GAAAc,QAAA,CAAAxB,IAAA;IACA;IACA,KAAAsB,QAAA,kBAAAC,IAAA,WAAAC,QAAA;MACAJ,KAAA,CAAAT,eAAA,GAAAa,QAAA,CAAAxB,IAAA;IACA;EACA;EACAyB,OAAA;IACA,eACAJ,OAAA,WAAAA,QAAA;MAAA,IAAAK,MAAA;MACA,KAAAzB,OAAA;MACAN,UAAA,MAAAgC,YAAA,MAAAf,WAAA,OAAAJ,SAAA,GAAAe,IAAA,WAAAC,QAAA;QACAE,MAAA,CAAApB,UAAA,GAAAkB,QAAA,CAAAI,IAAA;QACAF,MAAA,CAAArB,KAAA,GAAAmB,QAAA,CAAAnB,KAAA;QACAqB,MAAA,CAAAzB,OAAA;MACA,CACA;IACA;IACA;IACA4B,YAAA,WAAAA,aAAAC,GAAA,EAAAC,MAAA;MACA,YAAAC,eAAA,MAAAtB,aAAA,EAAAoB,GAAA,CAAAZ,MAAA;IACA;IACA;IACAe,cAAA,WAAAA,eAAAH,GAAA,EAAAC,MAAA;MACA,YAAAC,eAAA,MAAArB,eAAA,EAAAmB,GAAA,CAAAb,QAAA;IACA;IACA,aACAiB,WAAA,WAAAA,YAAA;MACA,KAAAtB,WAAA,CAAAC,OAAA;MACA,KAAAQ,OAAA;IACA;IACA,aACAc,UAAA,WAAAA,WAAA;MACA,KAAA3B,SAAA;MACA,KAAA4B,SAAA;MACA,KAAAF,WAAA;IACA;IACA;IACAG,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAApC,GAAA,GAAAoC,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,QAAA;MAAA;MACA,KAAAtC,QAAA,IAAAmC,SAAA,CAAAI,MAAA;IACA;IACA,aACAC,UAAA,WAAAA,WAAAb,GAAA;MACA,KAAAvB,IAAA;MACA,KAAAE,IAAA,GAAAqB,GAAA;IACA;IACA,aACAc,YAAA,WAAAA,aAAAd,GAAA;MAAA,IAAAe,MAAA;MACA,IAAAC,SAAA,QAAA5C,GAAA;MACA,KAAA6C,QAAA,oBAAAD,SAAA;QACAE,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAA3B,IAAA;QACA,OAAA3B,SAAA,CAAAkD,SAAA;MACA,GAAAvB,IAAA;QACAsB,MAAA,CAAAxB,OAAA;QACAwB,MAAA,CAAAM,UAAA;MACA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,KAAAN,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAA3B,IAAA;QACA,OAAAzB,WAAA;MACA,GAAAyB,IAAA;QACA8B,MAAA,CAAAhC,OAAA;QACAgC,MAAA,CAAAF,UAAA;MACA;IACA;IACA,aACAG,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,IAAA3C,WAAA,QAAAA,WAAA;MACA,KAAAmC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAA3B,IAAA;QACA,OAAA1B,YAAA,CAAAe,WAAA;MACA,GAAAW,IAAA,WAAAC,QAAA;QACA+B,MAAA,CAAAC,QAAA,CAAAhC,QAAA,CAAAiC,GAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}