{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Process/components/nodePanel/property/listenerParam.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Process/components/nodePanel/property/listenerParam.vue", "mtime": 1641277326000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["mixinXcrud", "mixins", "props", "value", "type", "Array", "default", "_default", "data", "dialogVisible", "formData", "paramList", "computed", "formConfig", "inline", "item", "xType", "tabs", "label", "name", "column", "width", "rules", "required", "message", "trigger", "dic", "methods", "closeDialog", "_this", "$refs", "xForm", "validate", "then", "catch", "e", "console", "error"], "sources": ["src/components/Process/components/nodePanel/property/listenerParam.vue"], "sourcesContent": ["<template>\n  <div>\n    <el-dialog\n      title=\"监听器参数\"\n      :visible.sync=\"dialogVisible\"\n      width=\"700px\"\n      :close-on-click-modal=\"false\"\n      :close-on-press-escape=\"false\"\n      :show-close=\"false\"\n      @closed=\"$emit('close', formData.paramList)\"\n    >\n      <x-form ref=\"xForm\" v-model=\"formData\" :config=\"formConfig\" />\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" size=\"medium\" @click=\"closeDialog\">确 定</el-button>\n      </span>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport mixinXcrud from '../../../common/mixinXcrud'\nexport default {\n  mixins: [mixinXcrud],\n  props: {\n    value: {\n      type: Array,\n      default: () => []\n    }\n  },\n  data() {\n    return {\n      dialogVisible: true,\n      formData: {\n        paramList: this.value\n      }\n    }\n  },\n  computed: {\n    formConfig() {\n      return {\n        inline: false,\n        item: [\n          {\n            xType: 'tabs',\n            tabs: [\n              {\n                label: '监听器参数',\n                name: 'paramList',\n                column: [\n                  {\n                    label: '类型',\n                    name: 'type',\n                    width: 180,\n                    rules: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],\n                    xType: 'select',\n                    dic: [\n                      { label: '字符串', value: 'stringValue' },\n                      { label: '表达式', value: 'expression' }\n                    ]\n                  },\n                  {\n                    label: '名称',\n                    name: 'name',\n                    width: 180,\n                    rules: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],\n                    xType: 'input'\n                  },\n                  {\n                    label: '值',\n                    name: 'value',\n                    xType: 'input',\n                    rules: [{ required: true, message: '请输入', trigger: ['blur', 'change'] }]\n                  }\n                ]\n              }\n            ]\n          }\n        ]\n      }\n    }\n  },\n  methods: {\n    closeDialog() {\n      this.$refs.xForm.validate().then(() => {\n        this.dialogVisible = false\n      }).catch(e => console.error(e))\n    }\n  }\n}\n</script>\n\n<style>\n.flow-containers  .el-badge__content.is-fixed {\n    top: 18px;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAoBA,OAAAA,UAAA;AACA;EACAC,MAAA,GAAAD,UAAA;EACAE,KAAA;IACAC,KAAA;MACAC,IAAA,EAAAC,KAAA;MACAC,OAAA,WAAAC,SAAA;QAAA;MAAA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,aAAA;MACAC,QAAA;QACAC,SAAA,OAAAR;MACA;IACA;EACA;EACAS,QAAA;IACAC,UAAA,WAAAA,WAAA;MACA;QACAC,MAAA;QACAC,IAAA,GACA;UACAC,KAAA;UACAC,IAAA,GACA;YACAC,KAAA;YACAC,IAAA;YACAC,MAAA,GACA;cACAF,KAAA;cACAC,IAAA;cACAE,KAAA;cACAC,KAAA;gBAAAC,QAAA;gBAAAC,OAAA;gBAAAC,OAAA;cAAA;cACAT,KAAA;cACAU,GAAA,GACA;gBAAAR,KAAA;gBAAAf,KAAA;cAAA,GACA;gBAAAe,KAAA;gBAAAf,KAAA;cAAA;YAEA,GACA;cACAe,KAAA;cACAC,IAAA;cACAE,KAAA;cACAC,KAAA;gBAAAC,QAAA;gBAAAC,OAAA;gBAAAC,OAAA;cAAA;cACAT,KAAA;YACA,GACA;cACAE,KAAA;cACAC,IAAA;cACAH,KAAA;cACAM,KAAA;gBAAAC,QAAA;gBAAAC,OAAA;gBAAAC,OAAA;cAAA;YACA;UAEA;QAEA;MAEA;IACA;EACA;EACAE,OAAA;IACAC,WAAA,WAAAA,YAAA;MAAA,IAAAC,KAAA;MACA,KAAAC,KAAA,CAAAC,KAAA,CAAAC,QAAA,GAAAC,IAAA;QACAJ,KAAA,CAAApB,aAAA;MACA,GAAAyB,KAAA,WAAAC,CAAA;QAAA,OAAAC,OAAA,CAAAC,KAAA,CAAAF,CAAA;MAAA;IACA;EACA;AACA", "ignoreList": []}]}