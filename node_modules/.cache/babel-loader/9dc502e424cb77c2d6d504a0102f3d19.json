{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/api/flowable/definition.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/api/flowable/definition.js", "mtime": 1650713280000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["request", "listDefinition", "query", "url", "method", "params", "definitionStart", "procDefId", "data", "definitionStartByKey", "getProcessVariables", "taskId", "updateState", "userList", "roleList", "readXml", "deployId", "readImage", "readXmlByKey", "deployKey", "readImageByKey", "getFlowViewer", "procInsId", "saveXml", "addDeployment", "updateDeployment", "delDeployment", "exportDeployment"], "sources": ["/Users/<USER>/develop/code/project/haijia/adminweb/src/api/flowable/definition.js"], "sourcesContent": ["import request from '@/utils/request'\n\n// 查询流程定义列表\nexport function listDefinition(query) {\n  return request({\n    url: '/flowable/definition/list',\n    method: 'get',\n    params: query\n  })\n}\n\n// 部署流程实例\nexport function definitionStart(procDefId,data) {\n  return request({\n    url: '/flowable/definition/start/' + procDefId,\n    method: 'post',\n    data: data\n  })\n}\n\n// 启动流程实例\nexport function definitionStartByKey(data) {\n  return request({\n    url: '/flowable/definition/startByKey',\n    method: 'post',\n    data: data\n  })\n}\n\n// 获取流程变量\nexport function getProcessVariables(taskId) {\n  return request({\n    url: '/flowable/task/processVariables/' + taskId,\n    method: 'get'\n  })\n}\n\n// 激活/挂起流程\nexport function updateState(params) {\n  return request({\n    url: '/flowable/definition/updateState',\n    method: 'put',\n    params: params\n  })\n}\n\n// 指定流程办理人员列表\nexport function userList(query) {\n  return request({\n    url: '/flowable/definition/userList',\n    method: 'get',\n    params: query\n  })\n}\n\n// 指定流程办理组列表\nexport function roleList(query) {\n  return request({\n    url: '/flowable/definition/roleList',\n    method: 'get',\n    params: query\n  })\n}\n\n// 读取xml文件\nexport function readXml(deployId) {\n  return request({\n    url: '/flowable/definition/readXml/' + deployId,\n    method: 'get'\n  })\n}\n// 读取image文件\nexport function readImage(deployId) {\n  return request({\n    url: '/flowable/definition/readImage/' + deployId,\n    method: 'get'\n  })\n}\n\n// 读取xml文件\nexport function readXmlByKey(deployKey) {\n  return request({\n    url: '/flowable/definition/readXmlByKey/' + deployKey,\n    method: 'get'\n  })\n}\n// 读取image文件\nexport function readImageByKey(deployKey) {\n  return request({\n    url: '/flowable/definition/readImageByKey/' + deployKey,\n    method: 'get'\n  })\n}\n\n// 读取image文件\nexport function getFlowViewer(procInsId) {\n  return request({\n    url: '/flowable/task/flowViewer/' + procInsId,\n    method: 'get'\n  })\n}\n\n// 读取xml文件\nexport function saveXml(data) {\n  return request({\n    url: '/flowable/definition/save',\n    method: 'post',\n    data: data\n  })\n}\n\n// 新增流程定义\nexport function addDeployment(data) {\n  return request({\n    url: '/system/deployment',\n    method: 'post',\n    data: data\n  })\n}\n\n// 修改流程定义\nexport function updateDeployment(data) {\n  return request({\n    url: '/system/deployment',\n    method: 'put',\n    data: data\n  })\n}\n\n// 删除流程定义\nexport function delDeployment(query) {\n  return request({\n    url: '/flowable/definition/delete/',\n    method: 'delete',\n    params: query\n  })\n}\n\n// 导出流程定义\nexport function exportDeployment(query) {\n  return request({\n    url: '/system/deployment/export',\n    method: 'get',\n    params: query\n  })\n}\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;;AAErC;AACA,OAAO,SAASC,cAAcA,CAACC,KAAK,EAAE;EACpC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,2BAA2B;IAChCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEH;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASI,eAAeA,CAACC,SAAS,EAACC,IAAI,EAAE;EAC9C,OAAOR,OAAO,CAAC;IACbG,GAAG,EAAE,6BAA6B,GAAGI,SAAS;IAC9CH,MAAM,EAAE,MAAM;IACdI,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASC,oBAAoBA,CAACD,IAAI,EAAE;EACzC,OAAOR,OAAO,CAAC;IACbG,GAAG,EAAE,iCAAiC;IACtCC,MAAM,EAAE,MAAM;IACdI,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASE,mBAAmBA,CAACC,MAAM,EAAE;EAC1C,OAAOX,OAAO,CAAC;IACbG,GAAG,EAAE,kCAAkC,GAAGQ,MAAM;IAChDP,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASQ,WAAWA,CAACP,MAAM,EAAE;EAClC,OAAOL,OAAO,CAAC;IACbG,GAAG,EAAE,kCAAkC;IACvCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEA;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASQ,QAAQA,CAACX,KAAK,EAAE;EAC9B,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,+BAA+B;IACpCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEH;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASY,QAAQA,CAACZ,KAAK,EAAE;EAC9B,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,+BAA+B;IACpCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEH;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASa,OAAOA,CAACC,QAAQ,EAAE;EAChC,OAAOhB,OAAO,CAAC;IACbG,GAAG,EAAE,+BAA+B,GAAGa,QAAQ;IAC/CZ,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;AACA;AACA,OAAO,SAASa,SAASA,CAACD,QAAQ,EAAE;EAClC,OAAOhB,OAAO,CAAC;IACbG,GAAG,EAAE,iCAAiC,GAAGa,QAAQ;IACjDZ,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASc,YAAYA,CAACC,SAAS,EAAE;EACtC,OAAOnB,OAAO,CAAC;IACbG,GAAG,EAAE,oCAAoC,GAAGgB,SAAS;IACrDf,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;AACA;AACA,OAAO,SAASgB,cAAcA,CAACD,SAAS,EAAE;EACxC,OAAOnB,OAAO,CAAC;IACbG,GAAG,EAAE,sCAAsC,GAAGgB,SAAS;IACvDf,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASiB,aAAaA,CAACC,SAAS,EAAE;EACvC,OAAOtB,OAAO,CAAC;IACbG,GAAG,EAAE,4BAA4B,GAAGmB,SAAS;IAC7ClB,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASmB,OAAOA,CAACf,IAAI,EAAE;EAC5B,OAAOR,OAAO,CAAC;IACbG,GAAG,EAAE,2BAA2B;IAChCC,MAAM,EAAE,MAAM;IACdI,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASgB,aAAaA,CAAChB,IAAI,EAAE;EAClC,OAAOR,OAAO,CAAC;IACbG,GAAG,EAAE,oBAAoB;IACzBC,MAAM,EAAE,MAAM;IACdI,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASiB,gBAAgBA,CAACjB,IAAI,EAAE;EACrC,OAAOR,OAAO,CAAC;IACbG,GAAG,EAAE,oBAAoB;IACzBC,MAAM,EAAE,KAAK;IACbI,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASkB,aAAaA,CAACxB,KAAK,EAAE;EACnC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,8BAA8B;IACnCC,MAAM,EAAE,QAAQ;IAChBC,MAAM,EAAEH;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASyB,gBAAgBA,CAACzB,KAAK,EAAE;EACtC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,2BAA2B;IAChCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEH;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}