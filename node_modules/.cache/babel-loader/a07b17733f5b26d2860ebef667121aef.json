{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/flowable/task/record/flow.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/flowable/task/record/flow.vue", "mtime": 1641277326000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBicG1uTW9kZWxlciBmcm9tICdAL2NvbXBvbmVudHMvUHJvY2Vzcy9pbmRleCc7CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiRmxvdyIsCiAgY29tcG9uZW50czogewogICAgYnBtbk1vZGVsZXI6IGJwbW5Nb2RlbGVyCiAgfSwKICBwcm9wczogewogICAgeG1sRGF0YTogewogICAgICB0eXBlOiBTdHJpbmcsCiAgICAgIGRlZmF1bHQ6ICcnCiAgICB9LAogICAgdGFza0RhdGE6IHsKICAgICAgdHlwZTogQXJyYXksCiAgICAgIGRlZmF1bHQ6IGZ1bmN0aW9uIF9kZWZhdWx0KCkgewogICAgICAgIHJldHVybiBbXTsKICAgICAgfQogICAgfQogIH0sCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7fTsKICB9Cn07"}, {"version": 3, "names": ["bpmnModeler", "name", "components", "props", "xmlData", "type", "String", "default", "taskData", "Array", "_default", "data"], "sources": ["src/views/flowable/task/record/flow.vue"], "sourcesContent": ["<template>\n  <div>\n    <bpmn-modeler\n      ref=\"refNode\"\n      :xml=\"xmlData\"\n      :is-view=\"true\"\n      :taskList=\"taskData\"\n    />\n  </div>\n</template>\n<script>\nimport bpmnModeler from '@/components/Process/index'\nexport default {\n  name: \"Flow\",\n  components: {\n    bpmnModeler\n  },\n  props: {\n    xmlData: {\n      type: String,\n      default: ''\n    },\n    taskData: {\n      type: Array,\n      default: () => []\n    }\n  },\n  data() {\n    return {};\n  }\n};\n</script>\n"], "mappings": ";;;;;;;;;;;AAWA,OAAAA,WAAA;AACA;EACAC,IAAA;EACAC,UAAA;IACAF,WAAA,EAAAA;EACA;EACAG,KAAA;IACAC,OAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAC,QAAA;MACAH,IAAA,EAAAI,KAAA;MACAF,OAAA,WAAAG,SAAA;QAAA;MAAA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;EACA;AACA", "ignoreList": []}]}