{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/components/HeaderSearch/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/components/HeaderSearch/index.vue", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js", "mtime": 1751171659043}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}