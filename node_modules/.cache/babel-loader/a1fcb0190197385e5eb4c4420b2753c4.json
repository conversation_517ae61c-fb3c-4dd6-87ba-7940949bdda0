{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/layout/components/TagsView/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/layout/components/TagsView/index.vue", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:ZnVuY3Rpb24gX3R5cGVvZihvKSB7ICJAYmFiZWwvaGVscGVycyAtIHR5cGVvZiI7IHJldHVybiBfdHlwZW9mID0gImZ1bmN0aW9uIiA9PSB0eXBlb2YgU3ltYm9sICYmICJzeW1ib2wiID09IHR5cGVvZiBTeW1ib2wuaXRlcmF0b3IgPyBmdW5jdGlvbiAobykgeyByZXR1cm4gdHlwZW9mIG87IH0gOiBmdW5jdGlvbiAobykgeyByZXR1cm4gbyAmJiAiZnVuY3Rpb24iID09IHR5cGVvZiBTeW1ib2wgJiYgby5jb25zdHJ1Y3RvciA9PT0gU3ltYm9sICYmIG8gIT09IFN5bWJvbC5wcm90b3R5cGUgPyAic3ltYm9sIiA6IHR5cGVvZiBvOyB9LCBfdHlwZW9mKG8pOyB9CmZ1bmN0aW9uIF9jcmVhdGVGb3JPZkl0ZXJhdG9ySGVscGVyKHIsIGUpIHsgdmFyIHQgPSAidW5kZWZpbmVkIiAhPSB0eXBlb2YgU3ltYm9sICYmIHJbU3ltYm9sLml0ZXJhdG9yXSB8fCByWyJAQGl0ZXJhdG9yIl07IGlmICghdCkgeyBpZiAoQXJyYXkuaXNBcnJheShyKSB8fCAodCA9IF91bnN1cHBvcnRlZEl0ZXJhYmxlVG9BcnJheShyKSkgfHwgZSAmJiByICYmICJudW1iZXIiID09IHR5cGVvZiByLmxlbmd0aCkgeyB0ICYmIChyID0gdCk7IHZhciBfbiA9IDAsIEYgPSBmdW5jdGlvbiBGKCkge307IHJldHVybiB7IHM6IEYsIG46IGZ1bmN0aW9uIG4oKSB7IHJldHVybiBfbiA+PSByLmxlbmd0aCA/IHsgZG9uZTogITAgfSA6IHsgZG9uZTogITEsIHZhbHVlOiByW19uKytdIH07IH0sIGU6IGZ1bmN0aW9uIGUocikgeyB0aHJvdyByOyB9LCBmOiBGIH07IH0gdGhyb3cgbmV3IFR5cGVFcnJvcigiSW52YWxpZCBhdHRlbXB0IHRvIGl0ZXJhdGUgbm9uLWl0ZXJhYmxlIGluc3RhbmNlLlxuSW4gb3JkZXIgdG8gYmUgaXRlcmFibGUsIG5vbi1hcnJheSBvYmplY3RzIG11c3QgaGF2ZSBhIFtTeW1ib2wuaXRlcmF0b3JdKCkgbWV0aG9kLiIpOyB9IHZhciBvLCBhID0gITAsIHUgPSAhMTsgcmV0dXJuIHsgczogZnVuY3Rpb24gcygpIHsgdCA9IHQuY2FsbChyKTsgfSwgbjogZnVuY3Rpb24gbigpIHsgdmFyIHIgPSB0Lm5leHQoKTsgcmV0dXJuIGEgPSByLmRvbmUsIHI7IH0sIGU6IGZ1bmN0aW9uIGUocikgeyB1ID0gITAsIG8gPSByOyB9LCBmOiBmdW5jdGlvbiBmKCkgeyB0cnkgeyBhIHx8IG51bGwgPT0gdC5yZXR1cm4gfHwgdC5yZXR1cm4oKTsgfSBmaW5hbGx5IHsgaWYgKHUpIHRocm93IG87IH0gfSB9OyB9CmZ1bmN0aW9uIF90b0NvbnN1bWFibGVBcnJheShyKSB7IHJldHVybiBfYXJyYXlXaXRob3V0SG9sZXMocikgfHwgX2l0ZXJhYmxlVG9BcnJheShyKSB8fCBfdW5zdXBwb3J0ZWRJdGVyYWJsZVRvQXJyYXkocikgfHwgX25vbkl0ZXJhYmxlU3ByZWFkKCk7IH0KZnVuY3Rpb24gX25vbkl0ZXJhYmxlU3ByZWFkKCkgeyB0aHJvdyBuZXcgVHlwZUVycm9yKCJJbnZhbGlkIGF0dGVtcHQgdG8gc3ByZWFkIG5vbi1pdGVyYWJsZSBpbnN0YW5jZS5cbkluIG9yZGVyIHRvIGJlIGl0ZXJhYmxlLCBub24tYXJyYXkgb2JqZWN0cyBtdXN0IGhhdmUgYSBbU3ltYm9sLml0ZXJhdG9yXSgpIG1ldGhvZC4iKTsgfQpmdW5jdGlvbiBfdW5zdXBwb3J0ZWRJdGVyYWJsZVRvQXJyYXkociwgYSkgeyBpZiAocikgeyBpZiAoInN0cmluZyIgPT0gdHlwZW9mIHIpIHJldHVybiBfYXJyYXlMaWtlVG9BcnJheShyLCBhKTsgdmFyIHQgPSB7fS50b1N0cmluZy5jYWxsKHIpLnNsaWNlKDgsIC0xKTsgcmV0dXJuICJPYmplY3QiID09PSB0ICYmIHIuY29uc3RydWN0b3IgJiYgKHQgPSByLmNvbnN0cnVjdG9yLm5hbWUpLCAiTWFwIiA9PT0gdCB8fCAiU2V0IiA9PT0gdCA/IEFycmF5LmZyb20ocikgOiAiQXJndW1lbnRzIiA9PT0gdCB8fCAvXig/OlVpfEkpbnQoPzo4fDE2fDMyKSg/OkNsYW1wZWQpP0FycmF5JC8udGVzdCh0KSA/IF9hcnJheUxpa2VUb0FycmF5KHIsIGEpIDogdm9pZCAwOyB9IH0KZnVuY3Rpb24gX2l0ZXJhYmxlVG9BcnJheShyKSB7IGlmICgidW5kZWZpbmVkIiAhPSB0eXBlb2YgU3ltYm9sICYmIG51bGwgIT0gcltTeW1ib2wuaXRlcmF0b3JdIHx8IG51bGwgIT0gclsiQEBpdGVyYXRvciJdKSByZXR1cm4gQXJyYXkuZnJvbShyKTsgfQpmdW5jdGlvbiBfYXJyYXlXaXRob3V0SG9sZXMocikgeyBpZiAoQXJyYXkuaXNBcnJheShyKSkgcmV0dXJuIF9hcnJheUxpa2VUb0FycmF5KHIpOyB9CmZ1bmN0aW9uIF9hcnJheUxpa2VUb0FycmF5KHIsIGEpIHsgKG51bGwgPT0gYSB8fCBhID4gci5sZW5ndGgpICYmIChhID0gci5sZW5ndGgpOyBmb3IgKHZhciBlID0gMCwgbiA9IEFycmF5KGEpOyBlIDwgYTsgZSsrKSBuW2VdID0gcltlXTsgcmV0dXJuIG47IH0KZnVuY3Rpb24gb3duS2V5cyhlLCByKSB7IHZhciB0ID0gT2JqZWN0LmtleXMoZSk7IGlmIChPYmplY3QuZ2V0T3duUHJvcGVydHlTeW1ib2xzKSB7IHZhciBvID0gT2JqZWN0LmdldE93blByb3BlcnR5U3ltYm9scyhlKTsgciAmJiAobyA9IG8uZmlsdGVyKGZ1bmN0aW9uIChyKSB7IHJldHVybiBPYmplY3QuZ2V0T3duUHJvcGVydHlEZXNjcmlwdG9yKGUsIHIpLmVudW1lcmFibGU7IH0pKSwgdC5wdXNoLmFwcGx5KHQsIG8pOyB9IHJldHVybiB0OyB9CmZ1bmN0aW9uIF9vYmplY3RTcHJlYWQoZSkgeyBmb3IgKHZhciByID0gMTsgciA8IGFyZ3VtZW50cy5sZW5ndGg7IHIrKykgeyB2YXIgdCA9IG51bGwgIT0gYXJndW1lbnRzW3JdID8gYXJndW1lbnRzW3JdIDoge307IHIgJSAyID8gb3duS2V5cyhPYmplY3QodCksICEwKS5mb3JFYWNoKGZ1bmN0aW9uIChyKSB7IF9kZWZpbmVQcm9wZXJ0eShlLCByLCB0W3JdKTsgfSkgOiBPYmplY3QuZ2V0T3duUHJvcGVydHlEZXNjcmlwdG9ycyA/IE9iamVjdC5kZWZpbmVQcm9wZXJ0aWVzKGUsIE9iamVjdC5nZXRPd25Qcm9wZXJ0eURlc2NyaXB0b3JzKHQpKSA6IG93bktleXMoT2JqZWN0KHQpKS5mb3JFYWNoKGZ1bmN0aW9uIChyKSB7IE9iamVjdC5kZWZpbmVQcm9wZXJ0eShlLCByLCBPYmplY3QuZ2V0T3duUHJvcGVydHlEZXNjcmlwdG9yKHQsIHIpKTsgfSk7IH0gcmV0dXJuIGU7IH0KZnVuY3Rpb24gX2RlZmluZVByb3BlcnR5KGUsIHIsIHQpIHsgcmV0dXJuIChyID0gX3RvUHJvcGVydHlLZXkocikpIGluIGUgPyBPYmplY3QuZGVmaW5lUHJvcGVydHkoZSwgciwgeyB2YWx1ZTogdCwgZW51bWVyYWJsZTogITAsIGNvbmZpZ3VyYWJsZTogITAsIHdyaXRhYmxlOiAhMCB9KSA6IGVbcl0gPSB0LCBlOyB9CmZ1bmN0aW9uIF90b1Byb3BlcnR5S2V5KHQpIHsgdmFyIGkgPSBfdG9QcmltaXRpdmUodCwgInN0cmluZyIpOyByZXR1cm4gInN5bWJvbCIgPT0gX3R5cGVvZihpKSA/IGkgOiBpICsgIiI7IH0KZnVuY3Rpb24gX3RvUHJpbWl0aXZlKHQsIHIpIHsgaWYgKCJvYmplY3QiICE9IF90eXBlb2YodCkgfHwgIXQpIHJldHVybiB0OyB2YXIgZSA9IHRbU3ltYm9sLnRvUHJpbWl0aXZlXTsgaWYgKHZvaWQgMCAhPT0gZSkgeyB2YXIgaSA9IGUuY2FsbCh0LCByIHx8ICJkZWZhdWx0Iik7IGlmICgib2JqZWN0IiAhPSBfdHlwZW9mKGkpKSByZXR1cm4gaTsgdGhyb3cgbmV3IFR5cGVFcnJvcigiQEB0b1ByaW1pdGl2ZSBtdXN0IHJldHVybiBhIHByaW1pdGl2ZSB2YWx1ZS4iKTsgfSByZXR1cm4gKCJzdHJpbmciID09PSByID8gU3RyaW5nIDogTnVtYmVyKSh0KTsgfQovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwoKaW1wb3J0IFNjcm9sbFBhbmUgZnJvbSAnLi9TY3JvbGxQYW5lJzsKaW1wb3J0IHBhdGggZnJvbSAncGF0aCc7CmV4cG9ydCBkZWZhdWx0IHsKICBjb21wb25lbnRzOiB7CiAgICBTY3JvbGxQYW5lOiBTY3JvbGxQYW5lCiAgfSwKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgdmlzaWJsZTogZmFsc2UsCiAgICAgIHRvcDogMCwKICAgICAgbGVmdDogMCwKICAgICAgc2VsZWN0ZWRUYWc6IHt9LAogICAgICBhZmZpeFRhZ3M6IFtdCiAgICB9OwogIH0sCiAgY29tcHV0ZWQ6IHsKICAgIHZpc2l0ZWRWaWV3czogZnVuY3Rpb24gdmlzaXRlZFZpZXdzKCkgewogICAgICByZXR1cm4gdGhpcy4kc3RvcmUuc3RhdGUudGFnc1ZpZXcudmlzaXRlZFZpZXdzOwogICAgfSwKICAgIHJvdXRlczogZnVuY3Rpb24gcm91dGVzKCkgewogICAgICByZXR1cm4gdGhpcy4kc3RvcmUuc3RhdGUucGVybWlzc2lvbi5yb3V0ZXM7CiAgICB9LAogICAgdGhlbWU6IGZ1bmN0aW9uIHRoZW1lKCkgewogICAgICByZXR1cm4gdGhpcy4kc3RvcmUuc3RhdGUuc2V0dGluZ3MudGhlbWU7CiAgICB9CiAgfSwKICB3YXRjaDogewogICAgJHJvdXRlOiBmdW5jdGlvbiAkcm91dGUoKSB7CiAgICAgIHRoaXMuYWRkVGFncygpOwogICAgICB0aGlzLm1vdmVUb0N1cnJlbnRUYWcoKTsKICAgIH0sCiAgICB2aXNpYmxlOiBmdW5jdGlvbiB2aXNpYmxlKHZhbHVlKSB7CiAgICAgIGlmICh2YWx1ZSkgewogICAgICAgIGRvY3VtZW50LmJvZHkuYWRkRXZlbnRMaXN0ZW5lcignY2xpY2snLCB0aGlzLmNsb3NlTWVudSk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgZG9jdW1lbnQuYm9keS5yZW1vdmVFdmVudExpc3RlbmVyKCdjbGljaycsIHRoaXMuY2xvc2VNZW51KTsKICAgICAgfQogICAgfQogIH0sCiAgbW91bnRlZDogZnVuY3Rpb24gbW91bnRlZCgpIHsKICAgIHRoaXMuaW5pdFRhZ3MoKTsKICAgIHRoaXMuYWRkVGFncygpOwogIH0sCiAgbWV0aG9kczogewogICAgaXNBY3RpdmU6IGZ1bmN0aW9uIGlzQWN0aXZlKHJvdXRlKSB7CiAgICAgIHJldHVybiByb3V0ZS5wYXRoID09PSB0aGlzLiRyb3V0ZS5wYXRoOwogICAgfSwKICAgIGFjdGl2ZVN0eWxlOiBmdW5jdGlvbiBhY3RpdmVTdHlsZSh0YWcpIHsKICAgICAgaWYgKCF0aGlzLmlzQWN0aXZlKHRhZykpIHJldHVybiB7fTsKICAgICAgcmV0dXJuIHsKICAgICAgICAiYmFja2dyb3VuZC1jb2xvciI6IHRoaXMudGhlbWUsCiAgICAgICAgImJvcmRlci1jb2xvciI6IHRoaXMudGhlbWUKICAgICAgfTsKICAgIH0sCiAgICBpc0FmZml4OiBmdW5jdGlvbiBpc0FmZml4KHRhZykgewogICAgICByZXR1cm4gdGFnLm1ldGEgJiYgdGFnLm1ldGEuYWZmaXg7CiAgICB9LAogICAgZmlsdGVyQWZmaXhUYWdzOiBmdW5jdGlvbiBmaWx0ZXJBZmZpeFRhZ3Mocm91dGVzKSB7CiAgICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICAgIHZhciBiYXNlUGF0aCA9IGFyZ3VtZW50cy5sZW5ndGggPiAxICYmIGFyZ3VtZW50c1sxXSAhPT0gdW5kZWZpbmVkID8gYXJndW1lbnRzWzFdIDogJy8nOwogICAgICB2YXIgdGFncyA9IFtdOwogICAgICByb3V0ZXMuZm9yRWFjaChmdW5jdGlvbiAocm91dGUpIHsKICAgICAgICBpZiAocm91dGUubWV0YSAmJiByb3V0ZS5tZXRhLmFmZml4KSB7CiAgICAgICAgICB2YXIgdGFnUGF0aCA9IHBhdGgucmVzb2x2ZShiYXNlUGF0aCwgcm91dGUucGF0aCk7CiAgICAgICAgICB0YWdzLnB1c2goewogICAgICAgICAgICBmdWxsUGF0aDogdGFnUGF0aCwKICAgICAgICAgICAgcGF0aDogdGFnUGF0aCwKICAgICAgICAgICAgbmFtZTogcm91dGUubmFtZSwKICAgICAgICAgICAgbWV0YTogX29iamVjdFNwcmVhZCh7fSwgcm91dGUubWV0YSkKICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgICBpZiAocm91dGUuY2hpbGRyZW4pIHsKICAgICAgICAgIHZhciB0ZW1wVGFncyA9IF90aGlzLmZpbHRlckFmZml4VGFncyhyb3V0ZS5jaGlsZHJlbiwgcm91dGUucGF0aCk7CiAgICAgICAgICBpZiAodGVtcFRhZ3MubGVuZ3RoID49IDEpIHsKICAgICAgICAgICAgdGFncyA9IFtdLmNvbmNhdChfdG9Db25zdW1hYmxlQXJyYXkodGFncyksIF90b0NvbnN1bWFibGVBcnJheSh0ZW1wVGFncykpOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSk7CiAgICAgIHJldHVybiB0YWdzOwogICAgfSwKICAgIGluaXRUYWdzOiBmdW5jdGlvbiBpbml0VGFncygpIHsKICAgICAgdmFyIGFmZml4VGFncyA9IHRoaXMuYWZmaXhUYWdzID0gdGhpcy5maWx0ZXJBZmZpeFRhZ3ModGhpcy5yb3V0ZXMpOwogICAgICB2YXIgX2l0ZXJhdG9yID0gX2NyZWF0ZUZvck9mSXRlcmF0b3JIZWxwZXIoYWZmaXhUYWdzKSwKICAgICAgICBfc3RlcDsKICAgICAgdHJ5IHsKICAgICAgICBmb3IgKF9pdGVyYXRvci5zKCk7ICEoX3N0ZXAgPSBfaXRlcmF0b3IubigpKS5kb25lOykgewogICAgICAgICAgdmFyIHRhZyA9IF9zdGVwLnZhbHVlOwogICAgICAgICAgLy8gTXVzdCBoYXZlIHRhZyBuYW1lCiAgICAgICAgICBpZiAodGFnLm5hbWUpIHsKICAgICAgICAgICAgdGhpcy4kc3RvcmUuZGlzcGF0Y2goJ3RhZ3NWaWV3L2FkZFZpc2l0ZWRWaWV3JywgdGFnKTsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0gY2F0Y2ggKGVycikgewogICAgICAgIF9pdGVyYXRvci5lKGVycik7CiAgICAgIH0gZmluYWxseSB7CiAgICAgICAgX2l0ZXJhdG9yLmYoKTsKICAgICAgfQogICAgfSwKICAgIGFkZFRhZ3M6IGZ1bmN0aW9uIGFkZFRhZ3MoKSB7CiAgICAgIHZhciBuYW1lID0gdGhpcy4kcm91dGUubmFtZTsKICAgICAgaWYgKG5hbWUpIHsKICAgICAgICB0aGlzLiRzdG9yZS5kaXNwYXRjaCgndGFnc1ZpZXcvYWRkVmlldycsIHRoaXMuJHJvdXRlKTsKICAgICAgfQogICAgICByZXR1cm4gZmFsc2U7CiAgICB9LAogICAgbW92ZVRvQ3VycmVudFRhZzogZnVuY3Rpb24gbW92ZVRvQ3VycmVudFRhZygpIHsKICAgICAgdmFyIF90aGlzMiA9IHRoaXM7CiAgICAgIHZhciB0YWdzID0gdGhpcy4kcmVmcy50YWc7CiAgICAgIHRoaXMuJG5leHRUaWNrKGZ1bmN0aW9uICgpIHsKICAgICAgICB2YXIgX2l0ZXJhdG9yMiA9IF9jcmVhdGVGb3JPZkl0ZXJhdG9ySGVscGVyKHRhZ3MpLAogICAgICAgICAgX3N0ZXAyOwogICAgICAgIHRyeSB7CiAgICAgICAgICBmb3IgKF9pdGVyYXRvcjIucygpOyAhKF9zdGVwMiA9IF9pdGVyYXRvcjIubigpKS5kb25lOykgewogICAgICAgICAgICB2YXIgdGFnID0gX3N0ZXAyLnZhbHVlOwogICAgICAgICAgICBpZiAodGFnLnRvLnBhdGggPT09IF90aGlzMi4kcm91dGUucGF0aCkgewogICAgICAgICAgICAgIF90aGlzMi4kcmVmcy5zY3JvbGxQYW5lLm1vdmVUb1RhcmdldCh0YWcpOwogICAgICAgICAgICAgIC8vIHdoZW4gcXVlcnkgaXMgZGlmZmVyZW50IHRoZW4gdXBkYXRlCiAgICAgICAgICAgICAgaWYgKHRhZy50by5mdWxsUGF0aCAhPT0gX3RoaXMyLiRyb3V0ZS5mdWxsUGF0aCkgewogICAgICAgICAgICAgICAgX3RoaXMyLiRzdG9yZS5kaXNwYXRjaCgndGFnc1ZpZXcvdXBkYXRlVmlzaXRlZFZpZXcnLCBfdGhpczIuJHJvdXRlKTsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICB9IGNhdGNoIChlcnIpIHsKICAgICAgICAgIF9pdGVyYXRvcjIuZShlcnIpOwogICAgICAgIH0gZmluYWxseSB7CiAgICAgICAgICBfaXRlcmF0b3IyLmYoKTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIHJlZnJlc2hTZWxlY3RlZFRhZzogZnVuY3Rpb24gcmVmcmVzaFNlbGVjdGVkVGFnKHZpZXcpIHsKICAgICAgdmFyIF90aGlzMyA9IHRoaXM7CiAgICAgIHRoaXMuJHN0b3JlLmRpc3BhdGNoKCd0YWdzVmlldy9kZWxDYWNoZWRWaWV3JywgdmlldykudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgdmFyIGZ1bGxQYXRoID0gdmlldy5mdWxsUGF0aDsKICAgICAgICBfdGhpczMuJG5leHRUaWNrKGZ1bmN0aW9uICgpIHsKICAgICAgICAgIF90aGlzMy4kcm91dGVyLnJlcGxhY2UoewogICAgICAgICAgICBwYXRoOiAnL3JlZGlyZWN0JyArIGZ1bGxQYXRoCiAgICAgICAgICB9KTsKICAgICAgICB9KTsKICAgICAgfSk7CiAgICB9LAogICAgY2xvc2VTZWxlY3RlZFRhZzogZnVuY3Rpb24gY2xvc2VTZWxlY3RlZFRhZyh2aWV3KSB7CiAgICAgIHZhciBfdGhpczQgPSB0aGlzOwogICAgICB0aGlzLiRzdG9yZS5kaXNwYXRjaCgndGFnc1ZpZXcvZGVsVmlldycsIHZpZXcpLnRoZW4oZnVuY3Rpb24gKF9yZWYpIHsKICAgICAgICB2YXIgdmlzaXRlZFZpZXdzID0gX3JlZi52aXNpdGVkVmlld3M7CiAgICAgICAgaWYgKF90aGlzNC5pc0FjdGl2ZSh2aWV3KSkgewogICAgICAgICAgX3RoaXM0LnRvTGFzdFZpZXcodmlzaXRlZFZpZXdzLCB2aWV3KTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIGNsb3NlT3RoZXJzVGFnczogZnVuY3Rpb24gY2xvc2VPdGhlcnNUYWdzKCkgewogICAgICB2YXIgX3RoaXM1ID0gdGhpczsKICAgICAgdGhpcy4kcm91dGVyLnB1c2godGhpcy5zZWxlY3RlZFRhZykuY2F0Y2goZnVuY3Rpb24gKCkge30pOwogICAgICB0aGlzLiRzdG9yZS5kaXNwYXRjaCgndGFnc1ZpZXcvZGVsT3RoZXJzVmlld3MnLCB0aGlzLnNlbGVjdGVkVGFnKS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICBfdGhpczUubW92ZVRvQ3VycmVudFRhZygpOwogICAgICB9KTsKICAgIH0sCiAgICBjbG9zZUFsbFRhZ3M6IGZ1bmN0aW9uIGNsb3NlQWxsVGFncyh2aWV3KSB7CiAgICAgIHZhciBfdGhpczYgPSB0aGlzOwogICAgICB0aGlzLiRzdG9yZS5kaXNwYXRjaCgndGFnc1ZpZXcvZGVsQWxsVmlld3MnKS50aGVuKGZ1bmN0aW9uIChfcmVmMikgewogICAgICAgIHZhciB2aXNpdGVkVmlld3MgPSBfcmVmMi52aXNpdGVkVmlld3M7CiAgICAgICAgaWYgKF90aGlzNi5hZmZpeFRhZ3Muc29tZShmdW5jdGlvbiAodGFnKSB7CiAgICAgICAgICByZXR1cm4gdGFnLnBhdGggPT09IF90aGlzNi4kcm91dGUucGF0aDsKICAgICAgICB9KSkgewogICAgICAgICAgcmV0dXJuOwogICAgICAgIH0KICAgICAgICBfdGhpczYudG9MYXN0Vmlldyh2aXNpdGVkVmlld3MsIHZpZXcpOwogICAgICB9KTsKICAgIH0sCiAgICB0b0xhc3RWaWV3OiBmdW5jdGlvbiB0b0xhc3RWaWV3KHZpc2l0ZWRWaWV3cywgdmlldykgewogICAgICB2YXIgbGF0ZXN0VmlldyA9IHZpc2l0ZWRWaWV3cy5zbGljZSgtMSlbMF07CiAgICAgIGlmIChsYXRlc3RWaWV3KSB7CiAgICAgICAgdGhpcy4kcm91dGVyLnB1c2gobGF0ZXN0Vmlldy5mdWxsUGF0aCk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgLy8gbm93IHRoZSBkZWZhdWx0IGlzIHRvIHJlZGlyZWN0IHRvIHRoZSBob21lIHBhZ2UgaWYgdGhlcmUgaXMgbm8gdGFncy12aWV3LAogICAgICAgIC8vIHlvdSBjYW4gYWRqdXN0IGl0IGFjY29yZGluZyB0byB5b3VyIG5lZWRzLgogICAgICAgIGlmICh2aWV3Lm5hbWUgPT09ICdEYXNoYm9hcmQnKSB7CiAgICAgICAgICAvLyB0byByZWxvYWQgaG9tZSBwYWdlCiAgICAgICAgICB0aGlzLiRyb3V0ZXIucmVwbGFjZSh7CiAgICAgICAgICAgIHBhdGg6ICcvcmVkaXJlY3QnICsgdmlldy5mdWxsUGF0aAogICAgICAgICAgfSk7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuJHJvdXRlci5wdXNoKCcvJyk7CiAgICAgICAgfQogICAgICB9CiAgICB9LAogICAgb3Blbk1lbnU6IGZ1bmN0aW9uIG9wZW5NZW51KHRhZywgZSkgewogICAgICB2YXIgbWVudU1pbldpZHRoID0gMTA1OwogICAgICB2YXIgb2Zmc2V0TGVmdCA9IHRoaXMuJGVsLmdldEJvdW5kaW5nQ2xpZW50UmVjdCgpLmxlZnQ7IC8vIGNvbnRhaW5lciBtYXJnaW4gbGVmdAogICAgICB2YXIgb2Zmc2V0V2lkdGggPSB0aGlzLiRlbC5vZmZzZXRXaWR0aDsgLy8gY29udGFpbmVyIHdpZHRoCiAgICAgIHZhciBtYXhMZWZ0ID0gb2Zmc2V0V2lkdGggLSBtZW51TWluV2lkdGg7IC8vIGxlZnQgYm91bmRhcnkKICAgICAgdmFyIGxlZnQgPSBlLmNsaWVudFggLSBvZmZzZXRMZWZ0ICsgMTU7IC8vIDE1OiBtYXJnaW4gcmlnaHQKCiAgICAgIGlmIChsZWZ0ID4gbWF4TGVmdCkgewogICAgICAgIHRoaXMubGVmdCA9IG1heExlZnQ7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy5sZWZ0ID0gbGVmdDsKICAgICAgfQogICAgICB0aGlzLnRvcCA9IGUuY2xpZW50WTsKICAgICAgdGhpcy52aXNpYmxlID0gdHJ1ZTsKICAgICAgdGhpcy5zZWxlY3RlZFRhZyA9IHRhZzsKICAgIH0sCiAgICBjbG9zZU1lbnU6IGZ1bmN0aW9uIGNsb3NlTWVudSgpIHsKICAgICAgdGhpcy52aXNpYmxlID0gZmFsc2U7CiAgICB9LAogICAgaGFuZGxlU2Nyb2xsOiBmdW5jdGlvbiBoYW5kbGVTY3JvbGwoKSB7CiAgICAgIHRoaXMuY2xvc2VNZW51KCk7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["ScrollPane", "path", "components", "data", "visible", "top", "left", "selectedTag", "affixTags", "computed", "visitedViews", "$store", "state", "tagsView", "routes", "permission", "theme", "settings", "watch", "$route", "addTags", "moveToCurrentTag", "value", "document", "body", "addEventListener", "closeMenu", "removeEventListener", "mounted", "initTags", "methods", "isActive", "route", "activeStyle", "tag", "isAffix", "meta", "affix", "filterAffixTags", "_this", "basePath", "arguments", "length", "undefined", "tags", "for<PERSON>ach", "tagPath", "resolve", "push", "fullPath", "name", "_objectSpread", "children", "tempTags", "concat", "_toConsumableArray", "_iterator", "_createForOfIteratorHelper", "_step", "s", "n", "done", "dispatch", "err", "e", "f", "_this2", "$refs", "$nextTick", "_iterator2", "_step2", "to", "scrollPane", "move<PERSON><PERSON><PERSON>arget", "refreshSelectedTag", "view", "_this3", "then", "$router", "replace", "closeSelectedTag", "_this4", "_ref", "toLastView", "closeOthersTags", "_this5", "catch", "closeAllTags", "_this6", "_ref2", "some", "latestView", "slice", "openMenu", "menu<PERSON>in<PERSON>idth", "offsetLeft", "$el", "getBoundingClientRect", "offsetWidth", "maxLeft", "clientX", "clientY", "handleScroll"], "sources": ["src/layout/components/TagsView/index.vue"], "sourcesContent": ["<template>\n  <div id=\"tags-view-container\" class=\"tags-view-container\">\n    <scroll-pane ref=\"scrollPane\" class=\"tags-view-wrapper\" @scroll=\"handleScroll\">\n      <router-link\n        v-for=\"tag in visitedViews\"\n        ref=\"tag\"\n        :key=\"tag.path\"\n        :class=\"isActive(tag)?'active':''\"\n        :to=\"{ path: tag.path, query: tag.query, fullPath: tag.fullPath }\"\n        tag=\"span\"\n        class=\"tags-view-item\"\n        :style=\"activeStyle(tag)\"\n        @click.middle.native=\"!isAffix(tag)?closeSelectedTag(tag):''\"\n        @contextmenu.prevent.native=\"openMenu(tag,$event)\"\n      >\n        {{ tag.title }}\n        <span v-if=\"!isAffix(tag)\" class=\"el-icon-close\" @click.prevent.stop=\"closeSelectedTag(tag)\" />\n      </router-link>\n    </scroll-pane>\n    <ul v-show=\"visible\" :style=\"{left:left+'px',top:top+'px'}\" class=\"contextmenu\">\n      <li @click=\"refreshSelectedTag(selectedTag)\">刷新页面</li>\n      <li v-if=\"!isAffix(selectedTag)\" @click=\"closeSelectedTag(selectedTag)\">关闭当前</li>\n      <li @click=\"closeOthersTags\">关闭其他</li>\n      <li @click=\"closeAllTags(selectedTag)\">关闭所有</li>\n    </ul>\n  </div>\n</template>\n\n<script>\nimport ScrollPane from './ScrollPane'\nimport path from 'path'\n\nexport default {\n  components: { ScrollPane },\n  data() {\n    return {\n      visible: false,\n      top: 0,\n      left: 0,\n      selectedTag: {},\n      affixTags: []\n    }\n  },\n  computed: {\n    visitedViews() {\n      return this.$store.state.tagsView.visitedViews\n    },\n    routes() {\n      return this.$store.state.permission.routes\n    },\n    theme() {\n      return this.$store.state.settings.theme;\n    }\n  },\n  watch: {\n    $route() {\n      this.addTags()\n      this.moveToCurrentTag()\n    },\n    visible(value) {\n      if (value) {\n        document.body.addEventListener('click', this.closeMenu)\n      } else {\n        document.body.removeEventListener('click', this.closeMenu)\n      }\n    }\n  },\n  mounted() {\n    this.initTags()\n    this.addTags()\n  },\n  methods: {\n    isActive(route) {\n      return route.path === this.$route.path\n    },\n    activeStyle(tag) {\n      if (!this.isActive(tag)) return {};\n      return {\n        \"background-color\": this.theme,\n        \"border-color\": this.theme\n      };\n    },\n    isAffix(tag) {\n      return tag.meta && tag.meta.affix\n    },\n    filterAffixTags(routes, basePath = '/') {\n      let tags = []\n      routes.forEach(route => {\n        if (route.meta && route.meta.affix) {\n          const tagPath = path.resolve(basePath, route.path)\n          tags.push({\n            fullPath: tagPath,\n            path: tagPath,\n            name: route.name,\n            meta: { ...route.meta }\n          })\n        }\n        if (route.children) {\n          const tempTags = this.filterAffixTags(route.children, route.path)\n          if (tempTags.length >= 1) {\n            tags = [...tags, ...tempTags]\n          }\n        }\n      })\n      return tags\n    },\n    initTags() {\n      const affixTags = this.affixTags = this.filterAffixTags(this.routes)\n      for (const tag of affixTags) {\n        // Must have tag name\n        if (tag.name) {\n          this.$store.dispatch('tagsView/addVisitedView', tag)\n        }\n      }\n    },\n    addTags() {\n      const { name } = this.$route\n      if (name) {\n        this.$store.dispatch('tagsView/addView', this.$route)\n      }\n      return false\n    },\n    moveToCurrentTag() {\n      const tags = this.$refs.tag\n      this.$nextTick(() => {\n        for (const tag of tags) {\n          if (tag.to.path === this.$route.path) {\n            this.$refs.scrollPane.moveToTarget(tag)\n            // when query is different then update\n            if (tag.to.fullPath !== this.$route.fullPath) {\n              this.$store.dispatch('tagsView/updateVisitedView', this.$route)\n            }\n            break\n          }\n        }\n      })\n    },\n    refreshSelectedTag(view) {\n      this.$store.dispatch('tagsView/delCachedView', view).then(() => {\n        const { fullPath } = view\n        this.$nextTick(() => {\n          this.$router.replace({\n            path: '/redirect' + fullPath\n          })\n        })\n      })\n    },\n    closeSelectedTag(view) {\n      this.$store.dispatch('tagsView/delView', view).then(({ visitedViews }) => {\n        if (this.isActive(view)) {\n          this.toLastView(visitedViews, view)\n        }\n      })\n    },\n    closeOthersTags() {\n      this.$router.push(this.selectedTag).catch(()=>{});\n      this.$store.dispatch('tagsView/delOthersViews', this.selectedTag).then(() => {\n        this.moveToCurrentTag()\n      })\n    },\n    closeAllTags(view) {\n      this.$store.dispatch('tagsView/delAllViews').then(({ visitedViews }) => {\n        if (this.affixTags.some(tag => tag.path === this.$route.path)) {\n          return\n        }\n        this.toLastView(visitedViews, view)\n      })\n    },\n    toLastView(visitedViews, view) {\n      const latestView = visitedViews.slice(-1)[0]\n      if (latestView) {\n        this.$router.push(latestView.fullPath)\n      } else {\n        // now the default is to redirect to the home page if there is no tags-view,\n        // you can adjust it according to your needs.\n        if (view.name === 'Dashboard') {\n          // to reload home page\n          this.$router.replace({ path: '/redirect' + view.fullPath })\n        } else {\n          this.$router.push('/')\n        }\n      }\n    },\n    openMenu(tag, e) {\n      const menuMinWidth = 105\n      const offsetLeft = this.$el.getBoundingClientRect().left // container margin left\n      const offsetWidth = this.$el.offsetWidth // container width\n      const maxLeft = offsetWidth - menuMinWidth // left boundary\n      const left = e.clientX - offsetLeft + 15 // 15: margin right\n\n      if (left > maxLeft) {\n        this.left = maxLeft\n      } else {\n        this.left = left\n      }\n\n      this.top = e.clientY\n      this.visible = true\n      this.selectedTag = tag\n    },\n    closeMenu() {\n      this.visible = false\n    },\n    handleScroll() {\n      this.closeMenu()\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.tags-view-container {\n  height: 34px;\n  width: 100%;\n  background: #fff;\n  border-bottom: 1px solid #d8dce5;\n  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, .12), 0 0 3px 0 rgba(0, 0, 0, .04);\n  .tags-view-wrapper {\n    .tags-view-item {\n      display: inline-block;\n      position: relative;\n      cursor: pointer;\n      height: 26px;\n      line-height: 26px;\n      border: 1px solid #d8dce5;\n      color: #495060;\n      background: #fff;\n      padding: 0 8px;\n      font-size: 12px;\n      margin-left: 5px;\n      margin-top: 4px;\n      &:first-of-type {\n        margin-left: 15px;\n      }\n      &:last-of-type {\n        margin-right: 15px;\n      }\n      &.active {\n        background-color: #42b983;\n        color: #fff;\n        border-color: #42b983;\n        &::before {\n          content: '';\n          background: #fff;\n          display: inline-block;\n          width: 8px;\n          height: 8px;\n          border-radius: 50%;\n          position: relative;\n          margin-right: 2px;\n        }\n      }\n    }\n  }\n  .contextmenu {\n    margin: 0;\n    background: #fff;\n    z-index: 3000;\n    position: absolute;\n    list-style-type: none;\n    padding: 5px 0;\n    border-radius: 4px;\n    font-size: 12px;\n    font-weight: 400;\n    color: #333;\n    box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, .3);\n    li {\n      margin: 0;\n      padding: 7px 16px;\n      cursor: pointer;\n      &:hover {\n        background: #eee;\n      }\n    }\n  }\n}\n</style>\n\n<style lang=\"scss\">\n//reset element css of el-icon-close\n.tags-view-wrapper {\n  .tags-view-item {\n    .el-icon-close {\n      width: 16px;\n      height: 16px;\n      vertical-align: 2px;\n      border-radius: 50%;\n      text-align: center;\n      transition: all .3s cubic-bezier(.645, .045, .355, 1);\n      transform-origin: 100% 50%;\n      &:before {\n        transform: scale(.6);\n        display: inline-block;\n        vertical-align: -3px;\n      }\n      &:hover {\n        background-color: #b4bccc;\n        color: #fff;\n      }\n    }\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BA,OAAAA,UAAA;AACA,OAAAC,IAAA;AAEA;EACAC,UAAA;IAAAF,UAAA,EAAAA;EAAA;EACAG,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,GAAA;MACAC,IAAA;MACAC,WAAA;MACAC,SAAA;IACA;EACA;EACAC,QAAA;IACAC,YAAA,WAAAA,aAAA;MACA,YAAAC,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAH,YAAA;IACA;IACAI,MAAA,WAAAA,OAAA;MACA,YAAAH,MAAA,CAAAC,KAAA,CAAAG,UAAA,CAAAD,MAAA;IACA;IACAE,KAAA,WAAAA,MAAA;MACA,YAAAL,MAAA,CAAAC,KAAA,CAAAK,QAAA,CAAAD,KAAA;IACA;EACA;EACAE,KAAA;IACAC,MAAA,WAAAA,OAAA;MACA,KAAAC,OAAA;MACA,KAAAC,gBAAA;IACA;IACAjB,OAAA,WAAAA,QAAAkB,KAAA;MACA,IAAAA,KAAA;QACAC,QAAA,CAAAC,IAAA,CAAAC,gBAAA,eAAAC,SAAA;MACA;QACAH,QAAA,CAAAC,IAAA,CAAAG,mBAAA,eAAAD,SAAA;MACA;IACA;EACA;EACAE,OAAA,WAAAA,QAAA;IACA,KAAAC,QAAA;IACA,KAAAT,OAAA;EACA;EACAU,OAAA;IACAC,QAAA,WAAAA,SAAAC,KAAA;MACA,OAAAA,KAAA,CAAA/B,IAAA,UAAAkB,MAAA,CAAAlB,IAAA;IACA;IACAgC,WAAA,WAAAA,YAAAC,GAAA;MACA,UAAAH,QAAA,CAAAG,GAAA;MACA;QACA,yBAAAlB,KAAA;QACA,qBAAAA;MACA;IACA;IACAmB,OAAA,WAAAA,QAAAD,GAAA;MACA,OAAAA,GAAA,CAAAE,IAAA,IAAAF,GAAA,CAAAE,IAAA,CAAAC,KAAA;IACA;IACAC,eAAA,WAAAA,gBAAAxB,MAAA;MAAA,IAAAyB,KAAA;MAAA,IAAAC,QAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA;MACA,IAAAG,IAAA;MACA9B,MAAA,CAAA+B,OAAA,WAAAb,KAAA;QACA,IAAAA,KAAA,CAAAI,IAAA,IAAAJ,KAAA,CAAAI,IAAA,CAAAC,KAAA;UACA,IAAAS,OAAA,GAAA7C,IAAA,CAAA8C,OAAA,CAAAP,QAAA,EAAAR,KAAA,CAAA/B,IAAA;UACA2C,IAAA,CAAAI,IAAA;YACAC,QAAA,EAAAH,OAAA;YACA7C,IAAA,EAAA6C,OAAA;YACAI,IAAA,EAAAlB,KAAA,CAAAkB,IAAA;YACAd,IAAA,EAAAe,aAAA,KAAAnB,KAAA,CAAAI,IAAA;UACA;QACA;QACA,IAAAJ,KAAA,CAAAoB,QAAA;UACA,IAAAC,QAAA,GAAAd,KAAA,CAAAD,eAAA,CAAAN,KAAA,CAAAoB,QAAA,EAAApB,KAAA,CAAA/B,IAAA;UACA,IAAAoD,QAAA,CAAAX,MAAA;YACAE,IAAA,MAAAU,MAAA,CAAAC,kBAAA,CAAAX,IAAA,GAAAW,kBAAA,CAAAF,QAAA;UACA;QACA;MACA;MACA,OAAAT,IAAA;IACA;IACAf,QAAA,WAAAA,SAAA;MACA,IAAArB,SAAA,QAAAA,SAAA,QAAA8B,eAAA,MAAAxB,MAAA;MAAA,IAAA0C,SAAA,GAAAC,0BAAA,CACAjD,SAAA;QAAAkD,KAAA;MAAA;QAAA,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAAI,CAAA,IAAAC,IAAA;UAAA,IAAA3B,GAAA,GAAAwB,KAAA,CAAApC,KAAA;UACA;UACA,IAAAY,GAAA,CAAAgB,IAAA;YACA,KAAAvC,MAAA,CAAAmD,QAAA,4BAAA5B,GAAA;UACA;QACA;MAAA,SAAA6B,GAAA;QAAAP,SAAA,CAAAQ,CAAA,CAAAD,GAAA;MAAA;QAAAP,SAAA,CAAAS,CAAA;MAAA;IACA;IACA7C,OAAA,WAAAA,QAAA;MACA,IAAA8B,IAAA,QAAA/B,MAAA,CAAA+B,IAAA;MACA,IAAAA,IAAA;QACA,KAAAvC,MAAA,CAAAmD,QAAA,0BAAA3C,MAAA;MACA;MACA;IACA;IACAE,gBAAA,WAAAA,iBAAA;MAAA,IAAA6C,MAAA;MACA,IAAAtB,IAAA,QAAAuB,KAAA,CAAAjC,GAAA;MACA,KAAAkC,SAAA;QAAA,IAAAC,UAAA,GAAAZ,0BAAA,CACAb,IAAA;UAAA0B,MAAA;QAAA;UAAA,KAAAD,UAAA,CAAAV,CAAA,MAAAW,MAAA,GAAAD,UAAA,CAAAT,CAAA,IAAAC,IAAA;YAAA,IAAA3B,GAAA,GAAAoC,MAAA,CAAAhD,KAAA;YACA,IAAAY,GAAA,CAAAqC,EAAA,CAAAtE,IAAA,KAAAiE,MAAA,CAAA/C,MAAA,CAAAlB,IAAA;cACAiE,MAAA,CAAAC,KAAA,CAAAK,UAAA,CAAAC,YAAA,CAAAvC,GAAA;cACA;cACA,IAAAA,GAAA,CAAAqC,EAAA,CAAAtB,QAAA,KAAAiB,MAAA,CAAA/C,MAAA,CAAA8B,QAAA;gBACAiB,MAAA,CAAAvD,MAAA,CAAAmD,QAAA,+BAAAI,MAAA,CAAA/C,MAAA;cACA;cACA;YACA;UACA;QAAA,SAAA4C,GAAA;UAAAM,UAAA,CAAAL,CAAA,CAAAD,GAAA;QAAA;UAAAM,UAAA,CAAAJ,CAAA;QAAA;MACA;IACA;IACAS,kBAAA,WAAAA,mBAAAC,IAAA;MAAA,IAAAC,MAAA;MACA,KAAAjE,MAAA,CAAAmD,QAAA,2BAAAa,IAAA,EAAAE,IAAA;QACA,IAAA5B,QAAA,GAAA0B,IAAA,CAAA1B,QAAA;QACA2B,MAAA,CAAAR,SAAA;UACAQ,MAAA,CAAAE,OAAA,CAAAC,OAAA;YACA9E,IAAA,gBAAAgD;UACA;QACA;MACA;IACA;IACA+B,gBAAA,WAAAA,iBAAAL,IAAA;MAAA,IAAAM,MAAA;MACA,KAAAtE,MAAA,CAAAmD,QAAA,qBAAAa,IAAA,EAAAE,IAAA,WAAAK,IAAA;QAAA,IAAAxE,YAAA,GAAAwE,IAAA,CAAAxE,YAAA;QACA,IAAAuE,MAAA,CAAAlD,QAAA,CAAA4C,IAAA;UACAM,MAAA,CAAAE,UAAA,CAAAzE,YAAA,EAAAiE,IAAA;QACA;MACA;IACA;IACAS,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACA,KAAAP,OAAA,CAAA9B,IAAA,MAAAzC,WAAA,EAAA+E,KAAA;MACA,KAAA3E,MAAA,CAAAmD,QAAA,iCAAAvD,WAAA,EAAAsE,IAAA;QACAQ,MAAA,CAAAhE,gBAAA;MACA;IACA;IACAkE,YAAA,WAAAA,aAAAZ,IAAA;MAAA,IAAAa,MAAA;MACA,KAAA7E,MAAA,CAAAmD,QAAA,yBAAAe,IAAA,WAAAY,KAAA;QAAA,IAAA/E,YAAA,GAAA+E,KAAA,CAAA/E,YAAA;QACA,IAAA8E,MAAA,CAAAhF,SAAA,CAAAkF,IAAA,WAAAxD,GAAA;UAAA,OAAAA,GAAA,CAAAjC,IAAA,KAAAuF,MAAA,CAAArE,MAAA,CAAAlB,IAAA;QAAA;UACA;QACA;QACAuF,MAAA,CAAAL,UAAA,CAAAzE,YAAA,EAAAiE,IAAA;MACA;IACA;IACAQ,UAAA,WAAAA,WAAAzE,YAAA,EAAAiE,IAAA;MACA,IAAAgB,UAAA,GAAAjF,YAAA,CAAAkF,KAAA;MACA,IAAAD,UAAA;QACA,KAAAb,OAAA,CAAA9B,IAAA,CAAA2C,UAAA,CAAA1C,QAAA;MACA;QACA;QACA;QACA,IAAA0B,IAAA,CAAAzB,IAAA;UACA;UACA,KAAA4B,OAAA,CAAAC,OAAA;YAAA9E,IAAA,gBAAA0E,IAAA,CAAA1B;UAAA;QACA;UACA,KAAA6B,OAAA,CAAA9B,IAAA;QACA;MACA;IACA;IACA6C,QAAA,WAAAA,SAAA3D,GAAA,EAAA8B,CAAA;MACA,IAAA8B,YAAA;MACA,IAAAC,UAAA,QAAAC,GAAA,CAAAC,qBAAA,GAAA3F,IAAA;MACA,IAAA4F,WAAA,QAAAF,GAAA,CAAAE,WAAA;MACA,IAAAC,OAAA,GAAAD,WAAA,GAAAJ,YAAA;MACA,IAAAxF,IAAA,GAAA0D,CAAA,CAAAoC,OAAA,GAAAL,UAAA;;MAEA,IAAAzF,IAAA,GAAA6F,OAAA;QACA,KAAA7F,IAAA,GAAA6F,OAAA;MACA;QACA,KAAA7F,IAAA,GAAAA,IAAA;MACA;MAEA,KAAAD,GAAA,GAAA2D,CAAA,CAAAqC,OAAA;MACA,KAAAjG,OAAA;MACA,KAAAG,WAAA,GAAA2B,GAAA;IACA;IACAR,SAAA,WAAAA,UAAA;MACA,KAAAtB,OAAA;IACA;IACAkG,YAAA,WAAAA,aAAA;MACA,KAAA5E,SAAA;IACA;EACA;AACA", "ignoreList": []}]}