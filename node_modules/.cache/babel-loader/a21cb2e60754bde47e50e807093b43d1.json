{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/assets/icons/index.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/assets/icons/index.js", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IFZ1ZSBmcm9tICd2dWUnOwppbXBvcnQgU3ZnSWNvbiBmcm9tICdAL2NvbXBvbmVudHMvU3ZnSWNvbic7IC8vIHN2ZyBjb21wb25lbnQKCi8vIHJlZ2lzdGVyIGdsb2JhbGx5ClZ1ZS5jb21wb25lbnQoJ3N2Zy1pY29uJywgU3ZnSWNvbik7CnZhciByZXEgPSByZXF1aXJlLmNvbnRleHQoJy4vc3ZnJywgZmFsc2UsIC9cLnN2ZyQvKTsKdmFyIHJlcXVpcmVBbGwgPSBmdW5jdGlvbiByZXF1aXJlQWxsKHJlcXVpcmVDb250ZXh0KSB7CiAgcmV0dXJuIHJlcXVpcmVDb250ZXh0LmtleXMoKS5tYXAocmVxdWlyZUNvbnRleHQpOwp9OwpyZXF1aXJlQWxsKHJlcSk7"}, {"version": 3, "names": ["<PERSON><PERSON>", "SvgIcon", "component", "req", "require", "context", "requireAll", "requireContext", "keys", "map"], "sources": ["/Users/<USER>/develop/code/project/haijia/adminweb/src/assets/icons/index.js"], "sourcesContent": ["import Vue from 'vue'\nimport SvgIcon from '@/components/SvgIcon'// svg component\n\n// register globally\nVue.component('svg-icon', SvgIcon)\n\nconst req = require.context('./svg', false, /\\.svg$/)\nconst requireAll = requireContext => requireContext.keys().map(requireContext)\nrequireAll(req)\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,OAAO,MAAM,sBAAsB;;AAE1C;AACAD,GAAG,CAACE,SAAS,CAAC,UAAU,EAAED,OAAO,CAAC;AAElC,IAAME,GAAG,GAAGC,OAAO,CAACC,OAAO,CAAC,OAAO,EAAE,KAAK,EAAE,QAAQ,CAAC;AACrD,IAAMC,UAAU,GAAG,SAAbA,UAAUA,CAAGC,cAAc;EAAA,OAAIA,cAAc,CAACC,IAAI,CAAC,CAAC,CAACC,GAAG,CAACF,cAAc,CAAC;AAAA;AAC9ED,UAAU,CAACH,GAAG,CAAC", "ignoreList": []}]}