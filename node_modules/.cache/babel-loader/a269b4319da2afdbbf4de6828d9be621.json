{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/dashboard/LineChart.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/dashboard/LineChart.vue", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["echarts", "require", "resize", "mixins", "props", "className", "type", "String", "default", "width", "height", "autoResize", "Boolean", "chartData", "Object", "required", "data", "chart", "watch", "deep", "handler", "val", "setOptions", "mounted", "_this", "$nextTick", "initChart", "<PERSON><PERSON><PERSON><PERSON>", "dispose", "methods", "init", "$el", "_ref", "arguments", "length", "undefined", "expectedData", "actualData", "setOption", "xAxis", "boundaryGap", "axisTick", "show", "grid", "left", "right", "bottom", "top", "containLabel", "tooltip", "trigger", "axisPointer", "padding", "yAxis", "legend", "series", "name", "itemStyle", "normal", "color", "lineStyle", "smooth", "animationDuration", "animationEasing", "areaStyle"], "sources": ["src/views/dashboard/LineChart.vue"], "sourcesContent": ["<template>\n  <div :class=\"className\" :style=\"{height:height,width:width}\" />\n</template>\n\n<script>\nimport echarts from 'echarts'\nrequire('echarts/theme/macarons') // echarts theme\nimport resize from './mixins/resize'\n\nexport default {\n  mixins: [resize],\n  props: {\n    className: {\n      type: String,\n      default: 'chart'\n    },\n    width: {\n      type: String,\n      default: '100%'\n    },\n    height: {\n      type: String,\n      default: '350px'\n    },\n    autoResize: {\n      type: Boolean,\n      default: true\n    },\n    chartData: {\n      type: Object,\n      required: true\n    }\n  },\n  data() {\n    return {\n      chart: null\n    }\n  },\n  watch: {\n    chartData: {\n      deep: true,\n      handler(val) {\n        this.setOptions(val)\n      }\n    }\n  },\n  mounted() {\n    this.$nextTick(() => {\n      this.initChart()\n    })\n  },\n  beforeDestroy() {\n    if (!this.chart) {\n      return\n    }\n    this.chart.dispose()\n    this.chart = null\n  },\n  methods: {\n    initChart() {\n      this.chart = echarts.init(this.$el, 'macarons')\n      this.setOptions(this.chartData)\n    },\n    setOptions({ expectedData, actualData } = {}) {\n      this.chart.setOption({\n        xAxis: {\n          data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],\n          boundaryGap: false,\n          axisTick: {\n            show: false\n          }\n        },\n        grid: {\n          left: 10,\n          right: 10,\n          bottom: 20,\n          top: 30,\n          containLabel: true\n        },\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'cross'\n          },\n          padding: [5, 10]\n        },\n        yAxis: {\n          axisTick: {\n            show: false\n          }\n        },\n        legend: {\n          data: ['expected', 'actual']\n        },\n        series: [{\n          name: 'expected', itemStyle: {\n            normal: {\n              color: '#FF005A',\n              lineStyle: {\n                color: '#FF005A',\n                width: 2\n              }\n            }\n          },\n          smooth: true,\n          type: 'line',\n          data: expectedData,\n          animationDuration: 2800,\n          animationEasing: 'cubicInOut'\n        },\n        {\n          name: 'actual',\n          smooth: true,\n          type: 'line',\n          itemStyle: {\n            normal: {\n              color: '#3888fa',\n              lineStyle: {\n                color: '#3888fa',\n                width: 2\n              },\n              areaStyle: {\n                color: '#f3f8ff'\n              }\n            }\n          },\n          data: actualData,\n          animationDuration: 2800,\n          animationEasing: 'quadraticOut'\n        }]\n      })\n    }\n  }\n}\n</script>\n"], "mappings": ";;;;;AAKA,OAAAA,OAAA;AACAC,OAAA;AACA,OAAAC,MAAA;AAEA;EACAC,MAAA,GAAAD,MAAA;EACAE,KAAA;IACAC,SAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAC,KAAA;MACAH,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAE,MAAA;MACAJ,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAG,UAAA;MACAL,IAAA,EAAAM,OAAA;MACAJ,OAAA;IACA;IACAK,SAAA;MACAP,IAAA,EAAAQ,MAAA;MACAC,QAAA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,KAAA;IACA;EACA;EACAC,KAAA;IACAL,SAAA;MACAM,IAAA;MACAC,OAAA,WAAAA,QAAAC,GAAA;QACA,KAAAC,UAAA,CAAAD,GAAA;MACA;IACA;EACA;EACAE,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA,KAAAC,SAAA;MACAD,KAAA,CAAAE,SAAA;IACA;EACA;EACAC,aAAA,WAAAA,cAAA;IACA,UAAAV,KAAA;MACA;IACA;IACA,KAAAA,KAAA,CAAAW,OAAA;IACA,KAAAX,KAAA;EACA;EACAY,OAAA;IACAH,SAAA,WAAAA,UAAA;MACA,KAAAT,KAAA,GAAAjB,OAAA,CAAA8B,IAAA,MAAAC,GAAA;MACA,KAAAT,UAAA,MAAAT,SAAA;IACA;IACAS,UAAA,WAAAA,WAAA;MAAA,IAAAU,IAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA;QAAAG,YAAA,GAAAJ,IAAA,CAAAI,YAAA;QAAAC,UAAA,GAAAL,IAAA,CAAAK,UAAA;MACA,KAAApB,KAAA,CAAAqB,SAAA;QACAC,KAAA;UACAvB,IAAA;UACAwB,WAAA;UACAC,QAAA;YACAC,IAAA;UACA;QACA;QACAC,IAAA;UACAC,IAAA;UACAC,KAAA;UACAC,MAAA;UACAC,GAAA;UACAC,YAAA;QACA;QACAC,OAAA;UACAC,OAAA;UACAC,WAAA;YACA7C,IAAA;UACA;UACA8C,OAAA;QACA;QACAC,KAAA;UACAZ,QAAA;YACAC,IAAA;UACA;QACA;QACAY,MAAA;UACAtC,IAAA;QACA;QACAuC,MAAA;UACAC,IAAA;UAAAC,SAAA;YACAC,MAAA;cACAC,KAAA;cACAC,SAAA;gBACAD,KAAA;gBACAlD,KAAA;cACA;YACA;UACA;UACAoD,MAAA;UACAvD,IAAA;UACAU,IAAA,EAAAoB,YAAA;UACA0B,iBAAA;UACAC,eAAA;QACA,GACA;UACAP,IAAA;UACAK,MAAA;UACAvD,IAAA;UACAmD,SAAA;YACAC,MAAA;cACAC,KAAA;cACAC,SAAA;gBACAD,KAAA;gBACAlD,KAAA;cACA;cACAuD,SAAA;gBACAL,KAAA;cACA;YACA;UACA;UACA3C,IAAA,EAAAqB,UAAA;UACAyB,iBAAA;UACAC,eAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}