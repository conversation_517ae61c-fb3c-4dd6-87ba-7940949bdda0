{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Process/components/nodePanel/property/taskListener.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Process/components/nodePanel/property/taskListener.vue", "mtime": 1641277326000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["mixinPanel", "listenerParam", "components", "mixins", "data", "dialogVisible", "showParamDialog", "nowIndex", "formData", "taskListener", "computed", "formConfig", "inline", "item", "xType", "tabs", "label", "name", "column", "width", "rules", "required", "message", "trigger", "dic", "value", "tooltip", "slot", "mounted", "_this$element$busines", "_this$element$busines2", "element", "businessObject", "extensionElements", "values", "filter", "$type", "map", "_item$fields$map", "_item$fields", "type", "event", "className", "params", "fields", "field", "fieldType", "methods", "config<PERSON><PERSON><PERSON>", "index", "nowObj", "finishConfigParam", "param", "cache", "$set", "updateElement", "_this$formData$taskLi", "_this", "length", "_extensionElements$va", "_extensionElements$va2", "get", "modeler", "create", "for<PERSON>ach", "fieldElement", "push", "updateProperties", "_extensionElements$va3", "_extensionElements$va4", "closeDialog", "_this2", "$refs", "xForm", "validate", "then", "catch", "e", "console", "error"], "sources": ["src/components/Process/components/nodePanel/property/taskListener.vue"], "sourcesContent": ["<template>\n  <div>\n    <el-dialog\n      title=\"任务监听器\"\n      :visible.sync=\"dialogVisible\"\n      width=\"900px\"\n      :close-on-click-modal=\"false\"\n      :close-on-press-escape=\"false\"\n      :show-close=\"false\"\n      @closed=\"$emit('close')\"\n    >\n      <x-form ref=\"xForm\" v-model=\"formData\" :config=\"formConfig\">\n        <template #params=\"scope\">\n          <el-badge :value=\"scope.row.params ? scope.row.params.length : 0\" type=\"primary\">\n            <el-button size=\"small\" @click=\"configParam(scope.$index)\">配置</el-button>\n          </el-badge>\n        </template>\n      </x-form>\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" size=\"medium\" @click=\"closeDialog\">确 定</el-button>\n      </span>\n    </el-dialog>\n    <listenerParam v-if=\"showParamDialog\" :value=\"formData.taskListener[nowIndex].params\" @close=\"finishConfigParam\" />\n  </div>\n</template>\n\n<script>\nimport mixinPanel from '../../../common/mixinPanel'\nimport listenerParam from './listenerParam'\nexport default {\n  components: { listenerParam },\n  mixins: [mixinPanel],\n  data() {\n    return {\n      dialogVisible: true,\n      showParamDialog: false,\n      nowIndex: null,\n      formData: {\n        taskListener: []\n      }\n    }\n  },\n  computed: {\n    formConfig() {\n    //   const _this = this\n      return {\n        inline: false,\n        item: [\n          {\n            xType: 'tabs',\n            tabs: [\n              {\n                label: '任务监听器',\n                name: 'taskListener',\n                column: [\n                  {\n                    label: '事件',\n                    name: 'event',\n                    width: 180,\n                    rules: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],\n                    xType: 'select',\n                    dic: [\n                      { label: 'create', value: 'create' },\n                      { label: 'assignment', value: 'assignment' },\n                      { label: 'complete', value: 'complete' },\n                      { label: 'delete', value: 'delete' }\n                    ],\n                    tooltip: `create（创建）：当任务已经创建，并且所有任务参数都已经设置时触发。<br />\n                              assignment（指派）：当任务已经指派给某人时触发。请注意：当流程执行到达用户任务时，在触发create事件之前，会首先触发assignment事件。<br />\n                              complete（完成）：当任务已经完成，从运行时数据中删除前触发。<br />\n                              delete（删除）：在任务即将被删除前触发。请注意任务由completeTask正常完成时也会触发。\n                    `\n                  },\n                  {\n                    label: '类型',\n                    name: 'type',\n                    width: 180,\n                    rules: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],\n                    xType: 'select',\n                    dic: [\n                      { label: '类', value: 'class' },\n                      { label: '表达式', value: 'expression' },\n                      { label: '委托表达式', value: 'delegateExpression' }\n                    ]\n                  },\n                  {\n                    label: 'java 类名',\n                    name: 'className',\n                    xType: 'input',\n                    rules: [{ required: true, message: '请输入', trigger: ['blur', 'change'] }]\n                  },\n                  {\n                    xType: 'slot',\n                    label: '参数',\n                    width: 120,\n                    slot: true,\n                    name: 'params'\n                  }\n                ]\n              }\n            ]\n          }\n        ]\n      }\n    }\n  },\n  mounted() {\n    this.formData.taskListener = this.element.businessObject.extensionElements?.values\n      .filter(item => item.$type === 'flowable:TaskListener')\n      .map(item => {\n        let type\n        if ('class' in item) type = 'class'\n        if ('expression' in item) type = 'expression'\n        if ('delegateExpression' in item) type = 'delegateExpression'\n        return {\n          event: item.event,\n          type: type,\n          className: item[type],\n          params: item.fields?.map(field => {\n            let fieldType\n            if ('stringValue' in field) fieldType = 'stringValue'\n            if ('expression' in field) fieldType = 'expression'\n            return {\n              name: field.name,\n              type: fieldType,\n              value: field[fieldType]\n            }\n          }) ?? []\n        }\n      }) ?? []\n  },\n  methods: {\n    configParam(index) {\n      this.nowIndex = index\n      const nowObj = this.formData.taskListener[index]\n      if (!nowObj.params) {\n        nowObj.params = []\n      }\n      this.showParamDialog = true\n    },\n    finishConfigParam(param) {\n      this.showParamDialog = false\n      // hack 数量不更新问题\n      const cache = this.formData.taskListener[this.nowIndex]\n      cache.params = param\n      this.$set(this.formData.taskListener[this.nowIndex], this.nowIndex, cache)\n      this.nowIndex = null\n    },\n    updateElement() {\n      if (this.formData.taskListener?.length) {\n        let extensionElements = this.element.businessObject.get('extensionElements')\n        if (!extensionElements) {\n          extensionElements = this.modeler.get('moddle').create('bpmn:ExtensionElements')\n        }\n        // 清除旧值\n        extensionElements.values = extensionElements.values?.filter(item => item.$type !== 'flowable:TaskListener') ?? []\n        this.formData.taskListener.forEach(item => {\n          const taskListener = this.modeler.get('moddle').create('flowable:TaskListener')\n          taskListener['event'] = item.event\n          taskListener[item.type] = item.className\n          if (item.params && item.params.length) {\n            item.params.forEach(field => {\n              const fieldElement = this.modeler.get('moddle').create('flowable:Field')\n              fieldElement['name'] = field.name\n              fieldElement[field.type] = field.value\n              // 注意：flowable.json 中定义的string和expression类为小写，不然会和原生的String类冲突，此处为hack\n              // const valueElement = this.modeler.get('moddle').create(`flowable:${field.type}`, { body: field.value })\n              // fieldElement[field.type] = valueElement\n              taskListener.get('fields').push(fieldElement)\n            })\n          }\n          extensionElements.get('values').push(taskListener)\n        })\n        this.updateProperties({ extensionElements: extensionElements })\n      } else {\n        const extensionElements = this.element.businessObject[`extensionElements`]\n        if (extensionElements) {\n          extensionElements.values = extensionElements.values?.filter(item => item.$type !== 'flowable:TaskListener') ?? []\n        }\n      }\n    },\n    closeDialog() {\n      this.$refs.xForm.validate().then(() => {\n        this.updateElement()\n        this.dialogVisible = false\n      }).catch(e => console.error(e))\n    }\n  }\n}\n</script>\n\n<style>\n.flow-containers  .el-badge__content.is-fixed {\n    top: 18px;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BA,OAAAA,UAAA;AACA,OAAAC,aAAA;AACA;EACAC,UAAA;IAAAD,aAAA,EAAAA;EAAA;EACAE,MAAA,GAAAH,UAAA;EACAI,IAAA,WAAAA,KAAA;IACA;MACAC,aAAA;MACAC,eAAA;MACAC,QAAA;MACAC,QAAA;QACAC,YAAA;MACA;IACA;EACA;EACAC,QAAA;IACAC,UAAA,WAAAA,WAAA;MACA;MACA;QACAC,MAAA;QACAC,IAAA,GACA;UACAC,KAAA;UACAC,IAAA,GACA;YACAC,KAAA;YACAC,IAAA;YACAC,MAAA,GACA;cACAF,KAAA;cACAC,IAAA;cACAE,KAAA;cACAC,KAAA;gBAAAC,QAAA;gBAAAC,OAAA;gBAAAC,OAAA;cAAA;cACAT,KAAA;cACAU,GAAA,GACA;gBAAAR,KAAA;gBAAAS,KAAA;cAAA,GACA;gBAAAT,KAAA;gBAAAS,KAAA;cAAA,GACA;gBAAAT,KAAA;gBAAAS,KAAA;cAAA,GACA;gBAAAT,KAAA;gBAAAS,KAAA;cAAA,EACA;cACAC,OAAA;YAKA,GACA;cACAV,KAAA;cACAC,IAAA;cACAE,KAAA;cACAC,KAAA;gBAAAC,QAAA;gBAAAC,OAAA;gBAAAC,OAAA;cAAA;cACAT,KAAA;cACAU,GAAA,GACA;gBAAAR,KAAA;gBAAAS,KAAA;cAAA,GACA;gBAAAT,KAAA;gBAAAS,KAAA;cAAA,GACA;gBAAAT,KAAA;gBAAAS,KAAA;cAAA;YAEA,GACA;cACAT,KAAA;cACAC,IAAA;cACAH,KAAA;cACAM,KAAA;gBAAAC,QAAA;gBAAAC,OAAA;gBAAAC,OAAA;cAAA;YACA,GACA;cACAT,KAAA;cACAE,KAAA;cACAG,KAAA;cACAQ,IAAA;cACAV,IAAA;YACA;UAEA;QAEA;MAEA;IACA;EACA;EACAW,OAAA,WAAAA,QAAA;IAAA,IAAAC,qBAAA,EAAAC,sBAAA;IACA,KAAAtB,QAAA,CAAAC,YAAA,IAAAoB,qBAAA,IAAAC,sBAAA,QAAAC,OAAA,CAAAC,cAAA,CAAAC,iBAAA,cAAAH,sBAAA,uBAAAA,sBAAA,CAAAI,MAAA,CACAC,MAAA,WAAAtB,IAAA;MAAA,OAAAA,IAAA,CAAAuB,KAAA;IAAA,GACAC,GAAA,WAAAxB,IAAA;MAAA,IAAAyB,gBAAA,EAAAC,YAAA;MACA,IAAAC,IAAA;MACA,eAAA3B,IAAA,EAAA2B,IAAA;MACA,oBAAA3B,IAAA,EAAA2B,IAAA;MACA,4BAAA3B,IAAA,EAAA2B,IAAA;MACA;QACAC,KAAA,EAAA5B,IAAA,CAAA4B,KAAA;QACAD,IAAA,EAAAA,IAAA;QACAE,SAAA,EAAA7B,IAAA,CAAA2B,IAAA;QACAG,MAAA,GAAAL,gBAAA,IAAAC,YAAA,GAAA1B,IAAA,CAAA+B,MAAA,cAAAL,YAAA,uBAAAA,YAAA,CAAAF,GAAA,WAAAQ,KAAA;UACA,IAAAC,SAAA;UACA,qBAAAD,KAAA,EAAAC,SAAA;UACA,oBAAAD,KAAA,EAAAC,SAAA;UACA;YACA7B,IAAA,EAAA4B,KAAA,CAAA5B,IAAA;YACAuB,IAAA,EAAAM,SAAA;YACArB,KAAA,EAAAoB,KAAA,CAAAC,SAAA;UACA;QACA,gBAAAR,gBAAA,cAAAA,gBAAA;MACA;IACA,gBAAAT,qBAAA,cAAAA,qBAAA;EACA;EACAkB,OAAA;IACAC,WAAA,WAAAA,YAAAC,KAAA;MACA,KAAA1C,QAAA,GAAA0C,KAAA;MACA,IAAAC,MAAA,QAAA1C,QAAA,CAAAC,YAAA,CAAAwC,KAAA;MACA,KAAAC,MAAA,CAAAP,MAAA;QACAO,MAAA,CAAAP,MAAA;MACA;MACA,KAAArC,eAAA;IACA;IACA6C,iBAAA,WAAAA,kBAAAC,KAAA;MACA,KAAA9C,eAAA;MACA;MACA,IAAA+C,KAAA,QAAA7C,QAAA,CAAAC,YAAA,MAAAF,QAAA;MACA8C,KAAA,CAAAV,MAAA,GAAAS,KAAA;MACA,KAAAE,IAAA,MAAA9C,QAAA,CAAAC,YAAA,MAAAF,QAAA,QAAAA,QAAA,EAAA8C,KAAA;MACA,KAAA9C,QAAA;IACA;IACAgD,aAAA,WAAAA,cAAA;MAAA,IAAAC,qBAAA;QAAAC,KAAA;MACA,KAAAD,qBAAA,QAAAhD,QAAA,CAAAC,YAAA,cAAA+C,qBAAA,eAAAA,qBAAA,CAAAE,MAAA;QAAA,IAAAC,qBAAA,EAAAC,sBAAA;QACA,IAAA3B,iBAAA,QAAAF,OAAA,CAAAC,cAAA,CAAA6B,GAAA;QACA,KAAA5B,iBAAA;UACAA,iBAAA,QAAA6B,OAAA,CAAAD,GAAA,WAAAE,MAAA;QACA;QACA;QACA9B,iBAAA,CAAAC,MAAA,IAAAyB,qBAAA,IAAAC,sBAAA,GAAA3B,iBAAA,CAAAC,MAAA,cAAA0B,sBAAA,uBAAAA,sBAAA,CAAAzB,MAAA,WAAAtB,IAAA;UAAA,OAAAA,IAAA,CAAAuB,KAAA;QAAA,gBAAAuB,qBAAA,cAAAA,qBAAA;QACA,KAAAnD,QAAA,CAAAC,YAAA,CAAAuD,OAAA,WAAAnD,IAAA;UACA,IAAAJ,YAAA,GAAAgD,KAAA,CAAAK,OAAA,CAAAD,GAAA,WAAAE,MAAA;UACAtD,YAAA,YAAAI,IAAA,CAAA4B,KAAA;UACAhC,YAAA,CAAAI,IAAA,CAAA2B,IAAA,IAAA3B,IAAA,CAAA6B,SAAA;UACA,IAAA7B,IAAA,CAAA8B,MAAA,IAAA9B,IAAA,CAAA8B,MAAA,CAAAe,MAAA;YACA7C,IAAA,CAAA8B,MAAA,CAAAqB,OAAA,WAAAnB,KAAA;cACA,IAAAoB,YAAA,GAAAR,KAAA,CAAAK,OAAA,CAAAD,GAAA,WAAAE,MAAA;cACAE,YAAA,WAAApB,KAAA,CAAA5B,IAAA;cACAgD,YAAA,CAAApB,KAAA,CAAAL,IAAA,IAAAK,KAAA,CAAApB,KAAA;cACA;cACA;cACA;cACAhB,YAAA,CAAAoD,GAAA,WAAAK,IAAA,CAAAD,YAAA;YACA;UACA;UACAhC,iBAAA,CAAA4B,GAAA,WAAAK,IAAA,CAAAzD,YAAA;QACA;QACA,KAAA0D,gBAAA;UAAAlC,iBAAA,EAAAA;QAAA;MACA;QACA,IAAAA,kBAAA,QAAAF,OAAA,CAAAC,cAAA;QACA,IAAAC,kBAAA;UAAA,IAAAmC,sBAAA,EAAAC,sBAAA;UACApC,kBAAA,CAAAC,MAAA,IAAAkC,sBAAA,IAAAC,sBAAA,GAAApC,kBAAA,CAAAC,MAAA,cAAAmC,sBAAA,uBAAAA,sBAAA,CAAAlC,MAAA,WAAAtB,IAAA;YAAA,OAAAA,IAAA,CAAAuB,KAAA;UAAA,gBAAAgC,sBAAA,cAAAA,sBAAA;QACA;MACA;IACA;IACAE,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,CAAAC,KAAA,CAAAC,QAAA,GAAAC,IAAA;QACAJ,MAAA,CAAAhB,aAAA;QACAgB,MAAA,CAAAlE,aAAA;MACA,GAAAuE,KAAA,WAAAC,CAAA;QAAA,OAAAC,OAAA,CAAAC,KAAA,CAAAF,CAAA;MAAA;IACA;EACA;AACA", "ignoreList": []}]}