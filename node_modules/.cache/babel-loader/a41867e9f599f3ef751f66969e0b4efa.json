{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/project/dict/data.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/project/dict/data.vue", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGxpc3REYXRhLCBnZXREYXRhLCBkZWxEYXRhLCBhZGREYXRhLCB1cGRhdGVEYXRhLCBleHBvcnREYXRhIH0gZnJvbSAiQC9hcGkvc3lzdGVtL2RpY3QvZGF0YSI7CmltcG9ydCB7IGxpc3RUeXBlLCBnZXRUeXBlIGFzIF9nZXRUeXBlIH0gZnJvbSAiQC9hcGkvc3lzdGVtL2RpY3QvdHlwZSI7CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiRGF0YSIsCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIC8vIOmBrue9qeWxggogICAgICBsb2FkaW5nOiB0cnVlLAogICAgICAvLyDpgInkuK3mlbDnu4QKICAgICAgaWRzOiBbXSwKICAgICAgLy8g6Z2e5Y2V5Liq56aB55SoCiAgICAgIHNpbmdsZTogdHJ1ZSwKICAgICAgLy8g6Z2e5aSa5Liq56aB55SoCiAgICAgIG11bHRpcGxlOiB0cnVlLAogICAgICAvLyDmmL7npLrmkJzntKLmnaHku7YKICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwKICAgICAgLy8g5oC75p2h5pWwCiAgICAgIHRvdGFsOiAwLAogICAgICAvLyDlrZflhbjooajmoLzmlbDmja4KICAgICAgZGF0YUxpc3Q6IFtdLAogICAgICAvLyDpu5jorqTlrZflhbjnsbvlnosKICAgICAgZGVmYXVsdERpY3RUeXBlOiAiIiwKICAgICAgLy8g5by55Ye65bGC5qCH6aKYCiAgICAgIHRpdGxlOiAiIiwKICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGCCiAgICAgIG9wZW46IGZhbHNlLAogICAgICAvLyDnirbmgIHmlbDmja7lrZflhbgKICAgICAgc3RhdHVzT3B0aW9uczogW10sCiAgICAgIC8vIOexu+Wei+aVsOaNruWtl+WFuAogICAgICB0eXBlT3B0aW9uczogW10sCiAgICAgIC8vIOafpeivouWPguaVsAogICAgICBxdWVyeVBhcmFtczogewogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgIGRpY3ROYW1lOiB1bmRlZmluZWQsCiAgICAgICAgZGljdFR5cGU6IHVuZGVmaW5lZCwKICAgICAgICBzdGF0dXM6IHVuZGVmaW5lZAogICAgICB9LAogICAgICAvLyDooajljZXlj4LmlbAKICAgICAgZm9ybToge30sCiAgICAgIC8vIOihqOWNleagoemqjAogICAgICBydWxlczogewogICAgICAgIGRpY3RMYWJlbDogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogIuaVsOaNruagh+etvuS4jeiDveS4uuepuiIsCiAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIKICAgICAgICB9XSwKICAgICAgICBkaWN0VmFsdWU6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICLmlbDmja7plK7lgLzkuI3og73kuLrnqboiLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgfV0sCiAgICAgICAgZGljdFNvcnQ6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICLmlbDmja7pobrluo/kuI3og73kuLrnqboiLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgfV0KICAgICAgfQogICAgfTsKICB9LAogIGNyZWF0ZWQ6IGZ1bmN0aW9uIGNyZWF0ZWQoKSB7CiAgICB2YXIgX3RoaXMgPSB0aGlzOwogICAgdmFyIGRpY3RJZCA9IHRoaXMuJHJvdXRlLnBhcmFtcyAmJiB0aGlzLiRyb3V0ZS5wYXJhbXMuZGljdElkOwogICAgdGhpcy5nZXRUeXBlKGRpY3RJZCk7CiAgICB0aGlzLmdldFR5cGVMaXN0KCk7CiAgICB0aGlzLmdldERpY3RzKCJzeXNfbm9ybWFsX2Rpc2FibGUiKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICBfdGhpcy5zdGF0dXNPcHRpb25zID0gcmVzcG9uc2UuZGF0YTsKICAgIH0pOwogIH0sCiAgbWV0aG9kczogewogICAgLyoqIOafpeivouWtl+WFuOexu+Wei+ivpue7hiAqL2dldFR5cGU6IGZ1bmN0aW9uIGdldFR5cGUoZGljdElkKSB7CiAgICAgIHZhciBfdGhpczIgPSB0aGlzOwogICAgICBfZ2V0VHlwZShkaWN0SWQpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgX3RoaXMyLnF1ZXJ5UGFyYW1zLmRpY3RUeXBlID0gcmVzcG9uc2UuZGF0YS5kaWN0VHlwZTsKICAgICAgICBfdGhpczIuZGVmYXVsdERpY3RUeXBlID0gcmVzcG9uc2UuZGF0YS5kaWN0VHlwZTsKICAgICAgICBfdGhpczIuZ2V0TGlzdCgpOwogICAgICB9KTsKICAgIH0sCiAgICAvKiog5p+l6K+i5a2X5YW457G75Z6L5YiX6KGoICovZ2V0VHlwZUxpc3Q6IGZ1bmN0aW9uIGdldFR5cGVMaXN0KCkgewogICAgICB2YXIgX3RoaXMzID0gdGhpczsKICAgICAgbGlzdFR5cGUoKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgIF90aGlzMy50eXBlT3B0aW9ucyA9IHJlc3BvbnNlLnJvd3M7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDmn6Xor6LlrZflhbjmlbDmja7liJfooaggKi9nZXRMaXN0OiBmdW5jdGlvbiBnZXRMaXN0KCkgewogICAgICB2YXIgX3RoaXM0ID0gdGhpczsKICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsKICAgICAgbGlzdERhdGEodGhpcy5xdWVyeVBhcmFtcykudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICBfdGhpczQuZGF0YUxpc3QgPSByZXNwb25zZS5yb3dzOwogICAgICAgIF90aGlzNC50b3RhbCA9IHJlc3BvbnNlLnRvdGFsOwogICAgICAgIF90aGlzNC5sb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKICAgIC8vIOaVsOaNrueKtuaAgeWtl+WFuOe/u+ivkQogICAgc3RhdHVzRm9ybWF0OiBmdW5jdGlvbiBzdGF0dXNGb3JtYXQocm93LCBjb2x1bW4pIHsKICAgICAgcmV0dXJuIHRoaXMuc2VsZWN0RGljdExhYmVsKHRoaXMuc3RhdHVzT3B0aW9ucywgcm93LnN0YXR1cyk7CiAgICB9LAogICAgLy8g5Y+W5raI5oyJ6ZKuCiAgICBjYW5jZWw6IGZ1bmN0aW9uIGNhbmNlbCgpIHsKICAgICAgdGhpcy5vcGVuID0gZmFsc2U7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgIH0sCiAgICAvLyDooajljZXph43nva4KICAgIHJlc2V0OiBmdW5jdGlvbiByZXNldCgpIHsKICAgICAgdGhpcy5mb3JtID0gewogICAgICAgIGRpY3RDb2RlOiB1bmRlZmluZWQsCiAgICAgICAgZGljdExhYmVsOiB1bmRlZmluZWQsCiAgICAgICAgZGljdFZhbHVlOiB1bmRlZmluZWQsCiAgICAgICAgZGljdFNvcnQ6IDAsCiAgICAgICAgc3RhdHVzOiAiMCIsCiAgICAgICAgcmVtYXJrOiB1bmRlZmluZWQKICAgICAgfTsKICAgICAgdGhpcy5yZXNldEZvcm0oImZvcm0iKTsKICAgIH0sCiAgICAvKiog5pCc57Si5oyJ6ZKu5pON5L2cICovaGFuZGxlUXVlcnk6IGZ1bmN0aW9uIGhhbmRsZVF1ZXJ5KCkgewogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOwogICAgICB0aGlzLmdldExpc3QoKTsKICAgIH0sCiAgICAvKiog6YeN572u5oyJ6ZKu5pON5L2cICovcmVzZXRRdWVyeTogZnVuY3Rpb24gcmVzZXRRdWVyeSgpIHsKICAgICAgdGhpcy5yZXNldEZvcm0oInF1ZXJ5Rm9ybSIpOwogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmRpY3RUeXBlID0gdGhpcy5kZWZhdWx0RGljdFR5cGU7CiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsKICAgIH0sCiAgICAvKiog5paw5aKe5oyJ6ZKu5pON5L2cICovaGFuZGxlQWRkOiBmdW5jdGlvbiBoYW5kbGVBZGQoKSB7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsKICAgICAgdGhpcy50aXRsZSA9ICLmt7vliqDlrZflhbjmlbDmja4iOwogICAgICB0aGlzLmZvcm0uZGljdFR5cGUgPSB0aGlzLnF1ZXJ5UGFyYW1zLmRpY3RUeXBlOwogICAgfSwKICAgIC8vIOWkmumAieahhumAieS4reaVsOaNrgogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlOiBmdW5jdGlvbiBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7CiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgIHJldHVybiBpdGVtLmRpY3RDb2RlOwogICAgICB9KTsKICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoICE9IDE7CiAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aDsKICAgIH0sCiAgICAvKiog5L+u5pS55oyJ6ZKu5pON5L2cICovaGFuZGxlVXBkYXRlOiBmdW5jdGlvbiBoYW5kbGVVcGRhdGUocm93KSB7CiAgICAgIHZhciBfdGhpczUgPSB0aGlzOwogICAgICB0aGlzLnJlc2V0KCk7CiAgICAgIHZhciBkaWN0Q29kZSA9IHJvdy5kaWN0Q29kZSB8fCB0aGlzLmlkczsKICAgICAgZ2V0RGF0YShkaWN0Q29kZSkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICBfdGhpczUuZm9ybSA9IHJlc3BvbnNlLmRhdGE7CiAgICAgICAgX3RoaXM1Lm9wZW4gPSB0cnVlOwogICAgICAgIF90aGlzNS50aXRsZSA9ICLkv67mlLnlrZflhbjmlbDmja4iOwogICAgICB9KTsKICAgIH0sCiAgICAvKiog5o+Q5Lqk5oyJ6ZKuICovCiAgICBzdWJtaXRGb3JtOiBmdW5jdGlvbiBzdWJtaXRGb3JtKCkgewogICAgICB2YXIgX3RoaXM2ID0gdGhpczsKICAgICAgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKGZ1bmN0aW9uICh2YWxpZCkgewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgaWYgKF90aGlzNi5mb3JtLmRpY3RDb2RlICE9IHVuZGVmaW5lZCkgewogICAgICAgICAgICB1cGRhdGVEYXRhKF90aGlzNi5mb3JtKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgICAgICAgIF90aGlzNi5tc2dTdWNjZXNzKCLkv67mlLnmiJDlip8iKTsKICAgICAgICAgICAgICBfdGhpczYub3BlbiA9IGZhbHNlOwogICAgICAgICAgICAgIF90aGlzNi5nZXRMaXN0KCk7CiAgICAgICAgICAgIH0pOwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgYWRkRGF0YShfdGhpczYuZm9ybSkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICAgICAgICBfdGhpczYubXNnU3VjY2Vzcygi5paw5aKe5oiQ5YqfIik7CiAgICAgICAgICAgICAgX3RoaXM2Lm9wZW4gPSBmYWxzZTsKICAgICAgICAgICAgICBfdGhpczYuZ2V0TGlzdCgpOwogICAgICAgICAgICB9KTsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDliKDpmaTmjInpkq7mk43kvZwgKi9oYW5kbGVEZWxldGU6IGZ1bmN0aW9uIGhhbmRsZURlbGV0ZShyb3cpIHsKICAgICAgdmFyIF90aGlzNyA9IHRoaXM7CiAgICAgIHZhciBkaWN0Q29kZXMgPSByb3cuZGljdENvZGUgfHwgdGhpcy5pZHM7CiAgICAgIHRoaXMuJGNvbmZpcm0oJ+aYr+WQpuehruiupOWIoOmZpOWtl+WFuOe8lueggeS4uiInICsgZGljdENvZGVzICsgJyLnmoTmlbDmja7pobk/JywgIuitpuWRiiIsIHsKICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogIuehruWumiIsCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogIuWPlua2iCIsCiAgICAgICAgdHlwZTogIndhcm5pbmciCiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgIHJldHVybiBkZWxEYXRhKGRpY3RDb2Rlcyk7CiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgIF90aGlzNy5nZXRMaXN0KCk7CiAgICAgICAgX3RoaXM3Lm1zZ1N1Y2Nlc3MoIuWIoOmZpOaIkOWKnyIpOwogICAgICB9KTsKICAgIH0sCiAgICAvKiog5a+85Ye65oyJ6ZKu5pON5L2cICovaGFuZGxlRXhwb3J0OiBmdW5jdGlvbiBoYW5kbGVFeHBvcnQoKSB7CiAgICAgIHZhciBfdGhpczggPSB0aGlzOwogICAgICB2YXIgcXVlcnlQYXJhbXMgPSB0aGlzLnF1ZXJ5UGFyYW1zOwogICAgICB0aGlzLiRjb25maXJtKCfmmK/lkKbnoa7orqTlr7zlh7rmiYDmnInmlbDmja7pobk/JywgIuitpuWRiiIsIHsKICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogIuehruWumiIsCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogIuWPlua2iCIsCiAgICAgICAgdHlwZTogIndhcm5pbmciCiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgIHJldHVybiBleHBvcnREYXRhKHF1ZXJ5UGFyYW1zKTsKICAgICAgfSkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICBfdGhpczguZG93bmxvYWQocmVzcG9uc2UubXNnKTsKICAgICAgfSk7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["listData", "getData", "delData", "addData", "updateData", "exportData", "listType", "getType", "name", "data", "loading", "ids", "single", "multiple", "showSearch", "total", "dataList", "defaultDictType", "title", "open", "statusOptions", "typeOptions", "queryParams", "pageNum", "pageSize", "dictName", "undefined", "dictType", "status", "form", "rules", "dict<PERSON><PERSON>l", "required", "message", "trigger", "dict<PERSON><PERSON>ue", "dictSort", "created", "_this", "dictId", "$route", "params", "getTypeList", "getDicts", "then", "response", "methods", "_this2", "getList", "_this3", "rows", "_this4", "statusFormat", "row", "column", "selectDictLabel", "cancel", "reset", "dictCode", "remark", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleAdd", "handleSelectionChange", "selection", "map", "item", "length", "handleUpdate", "_this5", "submitForm", "_this6", "$refs", "validate", "valid", "msgSuccess", "handleDelete", "_this7", "dictCodes", "$confirm", "confirmButtonText", "cancelButtonText", "type", "handleExport", "_this8", "download", "msg"], "sources": ["src/views/project/dict/data.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"字典名称\" prop=\"dictType\">\n        <el-select v-model=\"queryParams.dictType\" size=\"small\">\n          <el-option\n            v-for=\"item in typeOptions\"\n            :key=\"item.dictId\"\n            :label=\"item.dictName\"\n            :value=\"item.dictType\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"字典标签\" prop=\"dictLabel\">\n        <el-input\n          v-model=\"queryParams.dictLabel\"\n          placeholder=\"请输入字典标签\"\n          clearable\n          size=\"small\"\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"状态\" prop=\"status\">\n        <el-select v-model=\"queryParams.status\" placeholder=\"数据状态\" clearable size=\"small\">\n          <el-option\n            v-for=\"dict in statusOptions\"\n            :key=\"dict.dictValue\"\n            :label=\"dict.dictLabel\"\n            :value=\"dict.dictValue\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"primary\"\n          plain\n          icon=\"el-icon-plus\"\n          size=\"mini\"\n          @click=\"handleAdd\"\n          v-hasPermi=\"['system:dict:add']\"\n        >新增</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"success\"\n          plain\n          icon=\"el-icon-edit\"\n          size=\"mini\"\n          :disabled=\"single\"\n          @click=\"handleUpdate\"\n          v-hasPermi=\"['system:dict:edit']\"\n        >修改</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"danger\"\n          plain\n          icon=\"el-icon-delete\"\n          size=\"mini\"\n          :disabled=\"multiple\"\n          @click=\"handleDelete\"\n          v-hasPermi=\"['system:dict:remove']\"\n        >删除</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"warning\"\n          plain\n          icon=\"el-icon-download\"\n          size=\"mini\"\n          @click=\"handleExport\"\n          v-hasPermi=\"['system:dict:export']\"\n        >导出</el-button>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <el-table v-loading=\"loading\" :data=\"dataList\" @selection-change=\"handleSelectionChange\">\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column label=\"字典编码\" align=\"center\" prop=\"dictCode\" />\n      <el-table-column label=\"字典标签\" align=\"center\" prop=\"dictLabel\" />\n      <el-table-column label=\"字典键值\" align=\"center\" prop=\"dictValue\" />\n      <el-table-column label=\"字典排序\" align=\"center\" prop=\"dictSort\" />\n      <el-table-column label=\"状态\" align=\"center\" prop=\"status\" :formatter=\"statusFormat\" />\n      <el-table-column label=\"备注\" align=\"center\" prop=\"remark\" :show-overflow-tooltip=\"true\" />\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"180\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.createTime) }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-edit\"\n            @click=\"handleUpdate(scope.row)\"\n            v-hasPermi=\"['system:dict:edit']\"\n          >修改</el-button>\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-delete\"\n            @click=\"handleDelete(scope.row)\"\n            v-hasPermi=\"['system:dict:remove']\"\n          >删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 添加或修改参数配置对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"500px\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\n        <el-form-item label=\"字典类型\">\n          <el-input v-model=\"form.dictType\" :disabled=\"true\" />\n        </el-form-item>\n        <el-form-item label=\"数据标签\" prop=\"dictLabel\">\n          <el-input v-model=\"form.dictLabel\" placeholder=\"请输入数据标签\" />\n        </el-form-item>\n        <el-form-item label=\"数据键值\" prop=\"dictValue\">\n          <el-input v-model=\"form.dictValue\" placeholder=\"请输入数据键值\" />\n        </el-form-item>\n        <el-form-item label=\"显示排序\" prop=\"dictSort\">\n          <el-input-number v-model=\"form.dictSort\" controls-position=\"right\" :min=\"0\" />\n        </el-form-item>\n        <el-form-item label=\"状态\" prop=\"status\">\n          <el-radio-group v-model=\"form.status\">\n            <el-radio\n              v-for=\"dict in statusOptions\"\n              :key=\"dict.dictValue\"\n              :label=\"dict.dictValue\"\n            >{{dict.dictLabel}}</el-radio>\n          </el-radio-group>\n        </el-form-item>\n        <el-form-item label=\"备注\" prop=\"remark\">\n          <el-input v-model=\"form.remark\" type=\"textarea\" placeholder=\"请输入内容\"></el-input>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { listData, getData, delData, addData, updateData, exportData } from \"@/api/system/dict/data\";\nimport { listType, getType } from \"@/api/system/dict/type\";\n\nexport default {\n  name: \"Data\",\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 字典表格数据\n      dataList: [],\n      // 默认字典类型\n      defaultDictType: \"\",\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      open: false,\n      // 状态数据字典\n      statusOptions: [],\n      // 类型数据字典\n      typeOptions: [],\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        dictName: undefined,\n        dictType: undefined,\n        status: undefined\n      },\n      // 表单参数\n      form: {},\n      // 表单校验\n      rules: {\n        dictLabel: [\n          { required: true, message: \"数据标签不能为空\", trigger: \"blur\" }\n        ],\n        dictValue: [\n          { required: true, message: \"数据键值不能为空\", trigger: \"blur\" }\n        ],\n        dictSort: [\n          { required: true, message: \"数据顺序不能为空\", trigger: \"blur\" }\n        ]\n      }\n    };\n  },\n  created() {\n    const dictId = this.$route.params && this.$route.params.dictId;\n    this.getType(dictId);\n    this.getTypeList();\n    this.getDicts(\"sys_normal_disable\").then(response => {\n      this.statusOptions = response.data;\n    });\n  },\n  methods: {\n    /** 查询字典类型详细 */\n    getType(dictId) {\n      getType(dictId).then(response => {\n        this.queryParams.dictType = response.data.dictType;\n        this.defaultDictType = response.data.dictType;\n        this.getList();\n      });\n    },\n    /** 查询字典类型列表 */\n    getTypeList() {\n      listType().then(response => {\n        this.typeOptions = response.rows;\n      });\n    },\n    /** 查询字典数据列表 */\n    getList() {\n      this.loading = true;\n      listData(this.queryParams).then(response => {\n        this.dataList = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    // 数据状态字典翻译\n    statusFormat(row, column) {\n      return this.selectDictLabel(this.statusOptions, row.status);\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        dictCode: undefined,\n        dictLabel: undefined,\n        dictValue: undefined,\n        dictSort: 0,\n        status: \"0\",\n        remark: undefined\n      };\n      this.resetForm(\"form\");\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.queryParams.dictType = this.defaultDictType;\n      this.handleQuery();\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.reset();\n      this.open = true;\n      this.title = \"添加字典数据\";\n      this.form.dictType = this.queryParams.dictType;\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.dictCode)\n      this.single = selection.length!=1\n      this.multiple = !selection.length\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset();\n      const dictCode = row.dictCode || this.ids\n      getData(dictCode).then(response => {\n        this.form = response.data;\n        this.open = true;\n        this.title = \"修改字典数据\";\n      });\n    },\n    /** 提交按钮 */\n    submitForm: function() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          if (this.form.dictCode != undefined) {\n            updateData(this.form).then(response => {\n              this.msgSuccess(\"修改成功\");\n              this.open = false;\n              this.getList();\n            });\n          } else {\n            addData(this.form).then(response => {\n              this.msgSuccess(\"新增成功\");\n              this.open = false;\n              this.getList();\n            });\n          }\n        }\n      });\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const dictCodes = row.dictCode || this.ids;\n      this.$confirm('是否确认删除字典编码为\"' + dictCodes + '\"的数据项?', \"警告\", {\n          confirmButtonText: \"确定\",\n          cancelButtonText: \"取消\",\n          type: \"warning\"\n        }).then(function() {\n          return delData(dictCodes);\n        }).then(() => {\n          this.getList();\n          this.msgSuccess(\"删除成功\");\n        })\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      const queryParams = this.queryParams;\n      this.$confirm('是否确认导出所有数据项?', \"警告\", {\n          confirmButtonText: \"确定\",\n          cancelButtonText: \"取消\",\n          type: \"warning\"\n        }).then(function() {\n          return exportData(queryParams);\n        }).then(response => {\n          this.download(response.msg);\n        })\n    }\n  }\n};\n</script>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkKA,SAAAA,QAAA,EAAAC,OAAA,EAAAC,OAAA,EAAAC,OAAA,EAAAC,UAAA,EAAAC,UAAA;AACA,SAAAC,QAAA,EAAAC,OAAA,IAAAA,QAAA;AAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,QAAA;MACA;MACAC,eAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,aAAA;MACA;MACAC,WAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,QAAA,EAAAC,SAAA;QACAC,QAAA,EAAAD,SAAA;QACAE,MAAA,EAAAF;MACA;MACA;MACAG,IAAA;MACA;MACAC,KAAA;QACAC,SAAA,GACA;UAAAC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAC,SAAA,GACA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAE,QAAA,GACA;UAAAJ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAG,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA,IAAAC,MAAA,QAAAC,MAAA,CAAAC,MAAA,SAAAD,MAAA,CAAAC,MAAA,CAAAF,MAAA;IACA,KAAAhC,OAAA,CAAAgC,MAAA;IACA,KAAAG,WAAA;IACA,KAAAC,QAAA,uBAAAC,IAAA,WAAAC,QAAA;MACAP,KAAA,CAAAlB,aAAA,GAAAyB,QAAA,CAAApC,IAAA;IACA;EACA;EACAqC,OAAA;IACA,eACAvC,OAAA,WAAAA,QAAAgC,MAAA;MAAA,IAAAQ,MAAA;MACAxC,QAAA,CAAAgC,MAAA,EAAAK,IAAA,WAAAC,QAAA;QACAE,MAAA,CAAAzB,WAAA,CAAAK,QAAA,GAAAkB,QAAA,CAAApC,IAAA,CAAAkB,QAAA;QACAoB,MAAA,CAAA9B,eAAA,GAAA4B,QAAA,CAAApC,IAAA,CAAAkB,QAAA;QACAoB,MAAA,CAAAC,OAAA;MACA;IACA;IACA,eACAN,WAAA,WAAAA,YAAA;MAAA,IAAAO,MAAA;MACA3C,QAAA,GAAAsC,IAAA,WAAAC,QAAA;QACAI,MAAA,CAAA5B,WAAA,GAAAwB,QAAA,CAAAK,IAAA;MACA;IACA;IACA,eACAF,OAAA,WAAAA,QAAA;MAAA,IAAAG,MAAA;MACA,KAAAzC,OAAA;MACAV,QAAA,MAAAsB,WAAA,EAAAsB,IAAA,WAAAC,QAAA;QACAM,MAAA,CAAAnC,QAAA,GAAA6B,QAAA,CAAAK,IAAA;QACAC,MAAA,CAAApC,KAAA,GAAA8B,QAAA,CAAA9B,KAAA;QACAoC,MAAA,CAAAzC,OAAA;MACA;IACA;IACA;IACA0C,YAAA,WAAAA,aAAAC,GAAA,EAAAC,MAAA;MACA,YAAAC,eAAA,MAAAnC,aAAA,EAAAiC,GAAA,CAAAzB,MAAA;IACA;IACA;IACA4B,MAAA,WAAAA,OAAA;MACA,KAAArC,IAAA;MACA,KAAAsC,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAA5B,IAAA;QACA6B,QAAA,EAAAhC,SAAA;QACAK,SAAA,EAAAL,SAAA;QACAS,SAAA,EAAAT,SAAA;QACAU,QAAA;QACAR,MAAA;QACA+B,MAAA,EAAAjC;MACA;MACA,KAAAkC,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAAvC,WAAA,CAAAC,OAAA;MACA,KAAAyB,OAAA;IACA;IACA,aACAc,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAAtC,WAAA,CAAAK,QAAA,QAAAV,eAAA;MACA,KAAA4C,WAAA;IACA;IACA,aACAE,SAAA,WAAAA,UAAA;MACA,KAAAN,KAAA;MACA,KAAAtC,IAAA;MACA,KAAAD,KAAA;MACA,KAAAW,IAAA,CAAAF,QAAA,QAAAL,WAAA,CAAAK,QAAA;IACA;IACA;IACAqC,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAtD,GAAA,GAAAsD,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAT,QAAA;MAAA;MACA,KAAA9C,MAAA,GAAAqD,SAAA,CAAAG,MAAA;MACA,KAAAvD,QAAA,IAAAoD,SAAA,CAAAG,MAAA;IACA;IACA,aACAC,YAAA,WAAAA,aAAAhB,GAAA;MAAA,IAAAiB,MAAA;MACA,KAAAb,KAAA;MACA,IAAAC,QAAA,GAAAL,GAAA,CAAAK,QAAA,SAAA/C,GAAA;MACAV,OAAA,CAAAyD,QAAA,EAAAd,IAAA,WAAAC,QAAA;QACAyB,MAAA,CAAAzC,IAAA,GAAAgB,QAAA,CAAApC,IAAA;QACA6D,MAAA,CAAAnD,IAAA;QACAmD,MAAA,CAAApD,KAAA;MACA;IACA;IACA;IACAqD,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAH,MAAA,CAAA3C,IAAA,CAAA6B,QAAA,IAAAhC,SAAA;YACAtB,UAAA,CAAAoE,MAAA,CAAA3C,IAAA,EAAAe,IAAA,WAAAC,QAAA;cACA2B,MAAA,CAAAI,UAAA;cACAJ,MAAA,CAAArD,IAAA;cACAqD,MAAA,CAAAxB,OAAA;YACA;UACA;YACA7C,OAAA,CAAAqE,MAAA,CAAA3C,IAAA,EAAAe,IAAA,WAAAC,QAAA;cACA2B,MAAA,CAAAI,UAAA;cACAJ,MAAA,CAAArD,IAAA;cACAqD,MAAA,CAAAxB,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACA6B,YAAA,WAAAA,aAAAxB,GAAA;MAAA,IAAAyB,MAAA;MACA,IAAAC,SAAA,GAAA1B,GAAA,CAAAK,QAAA,SAAA/C,GAAA;MACA,KAAAqE,QAAA,kBAAAD,SAAA;QACAE,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAAvC,IAAA;QACA,OAAA1C,OAAA,CAAA6E,SAAA;MACA,GAAAnC,IAAA;QACAkC,MAAA,CAAA9B,OAAA;QACA8B,MAAA,CAAAF,UAAA;MACA;IACA;IACA,aACAQ,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,IAAA/D,WAAA,QAAAA,WAAA;MACA,KAAA0D,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAAvC,IAAA;QACA,OAAAvC,UAAA,CAAAiB,WAAA;MACA,GAAAsB,IAAA,WAAAC,QAAA;QACAwC,MAAA,CAAAC,QAAA,CAAAzC,QAAA,CAAA0C,GAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}