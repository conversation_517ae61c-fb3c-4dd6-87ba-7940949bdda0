{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/layout/components/Settings/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/layout/components/Settings/index.vue", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBUaGVtZVBpY2tlciBmcm9tICdAL2NvbXBvbmVudHMvVGhlbWVQaWNrZXInOwpleHBvcnQgZGVmYXVsdCB7CiAgY29tcG9uZW50czogewogICAgVGhlbWVQaWNrZXI6IFRoZW1lUGlja2VyCiAgfSwKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHt9OwogIH0sCiAgY29tcHV0ZWQ6IHsKICAgIHRoZW1lOiBmdW5jdGlvbiB0aGVtZSgpIHsKICAgICAgcmV0dXJuIHRoaXMuJHN0b3JlLnN0YXRlLnNldHRpbmdzLnRoZW1lOwogICAgfSwKICAgIHNpZGVUaGVtZTogZnVuY3Rpb24gc2lkZVRoZW1lKCkgewogICAgICByZXR1cm4gdGhpcy4kc3RvcmUuc3RhdGUuc2V0dGluZ3Muc2lkZVRoZW1lOwogICAgfSwKICAgIGZpeGVkSGVhZGVyOiB7CiAgICAgIGdldDogZnVuY3Rpb24gZ2V0KCkgewogICAgICAgIHJldHVybiB0aGlzLiRzdG9yZS5zdGF0ZS5zZXR0aW5ncy5maXhlZEhlYWRlcjsKICAgICAgfSwKICAgICAgc2V0OiBmdW5jdGlvbiBzZXQodmFsKSB7CiAgICAgICAgdGhpcy4kc3RvcmUuZGlzcGF0Y2goJ3NldHRpbmdzL2NoYW5nZVNldHRpbmcnLCB7CiAgICAgICAgICBrZXk6ICdmaXhlZEhlYWRlcicsCiAgICAgICAgICB2YWx1ZTogdmFsCiAgICAgICAgfSk7CiAgICAgIH0KICAgIH0sCiAgICB0YWdzVmlldzogewogICAgICBnZXQ6IGZ1bmN0aW9uIGdldCgpIHsKICAgICAgICByZXR1cm4gdGhpcy4kc3RvcmUuc3RhdGUuc2V0dGluZ3MudGFnc1ZpZXc7CiAgICAgIH0sCiAgICAgIHNldDogZnVuY3Rpb24gc2V0KHZhbCkgewogICAgICAgIHRoaXMuJHN0b3JlLmRpc3BhdGNoKCdzZXR0aW5ncy9jaGFuZ2VTZXR0aW5nJywgewogICAgICAgICAga2V5OiAndGFnc1ZpZXcnLAogICAgICAgICAgdmFsdWU6IHZhbAogICAgICAgIH0pOwogICAgICB9CiAgICB9LAogICAgc2lkZWJhckxvZ286IHsKICAgICAgZ2V0OiBmdW5jdGlvbiBnZXQoKSB7CiAgICAgICAgcmV0dXJuIHRoaXMuJHN0b3JlLnN0YXRlLnNldHRpbmdzLnNpZGViYXJMb2dvOwogICAgICB9LAogICAgICBzZXQ6IGZ1bmN0aW9uIHNldCh2YWwpIHsKICAgICAgICB0aGlzLiRzdG9yZS5kaXNwYXRjaCgnc2V0dGluZ3MvY2hhbmdlU2V0dGluZycsIHsKICAgICAgICAgIGtleTogJ3NpZGViYXJMb2dvJywKICAgICAgICAgIHZhbHVlOiB2YWwKICAgICAgICB9KTsKICAgICAgfQogICAgfQogIH0sCiAgbWV0aG9kczogewogICAgdGhlbWVDaGFuZ2U6IGZ1bmN0aW9uIHRoZW1lQ2hhbmdlKHZhbCkgewogICAgICB0aGlzLiRzdG9yZS5kaXNwYXRjaCgnc2V0dGluZ3MvY2hhbmdlU2V0dGluZycsIHsKICAgICAgICBrZXk6ICd0aGVtZScsCiAgICAgICAgdmFsdWU6IHZhbAogICAgICB9KTsKICAgIH0sCiAgICBoYW5kbGVUaGVtZTogZnVuY3Rpb24gaGFuZGxlVGhlbWUodmFsKSB7CiAgICAgIHRoaXMuJHN0b3JlLmRpc3BhdGNoKCdzZXR0aW5ncy9jaGFuZ2VTZXR0aW5nJywgewogICAgICAgIGtleTogJ3NpZGVUaGVtZScsCiAgICAgICAgdmFsdWU6IHZhbAogICAgICB9KTsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["ThemePicker", "components", "data", "computed", "theme", "$store", "state", "settings", "sideTheme", "fixedHeader", "get", "set", "val", "dispatch", "key", "value", "tagsView", "sidebarLogo", "methods", "themeChange", "handleTheme"], "sources": ["src/layout/components/Settings/index.vue"], "sourcesContent": ["<template>\n  <div class=\"drawer-container\">\n    <div>\n      <div class=\"setting-drawer-content\">\n        <div class=\"setting-drawer-title\">\n          <h3 class=\"drawer-title\">主题风格设置</h3>\n        </div>\n        <div class=\"setting-drawer-block-checbox\">\n          <div class=\"setting-drawer-block-checbox-item\" @click=\"handleTheme('theme-dark')\">\n            <img src=\"@/assets/images/dark.svg\" alt=\"dark\">\n            <div v-if=\"sideTheme === 'theme-dark'\" class=\"setting-drawer-block-checbox-selectIcon\" style=\"display: block;\">\n              <i aria-label=\"图标: check\" class=\"anticon anticon-check\">\n                <svg viewBox=\"64 64 896 896\" data-icon=\"check\" width=\"1em\" height=\"1em\" :fill=\"theme\" aria-hidden=\"true\"\n                     focusable=\"false\" class=\"\">\n                  <path\n                    d=\"M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 0 0-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z\"/>\n                </svg>\n              </i>\n            </div>\n          </div>\n          <div class=\"setting-drawer-block-checbox-item\" @click=\"handleTheme('theme-light')\">\n            <img src=\"@/assets/images/light.svg\" alt=\"light\">\n            <div v-if=\"sideTheme === 'theme-light'\" class=\"setting-drawer-block-checbox-selectIcon\" style=\"display: block;\">\n              <i aria-label=\"图标: check\" class=\"anticon anticon-check\">\n                <svg viewBox=\"64 64 896 896\" data-icon=\"check\" width=\"1em\" height=\"1em\" :fill=\"theme\" aria-hidden=\"true\"\n                     focusable=\"false\" class=\"\">\n                  <path\n                    d=\"M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 0 0-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z\"/>\n                </svg>\n              </i>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"drawer-item\">\n          <span>主题颜色</span>\n          <theme-picker style=\"float: right;height: 26px;margin: -3px 8px 0 0;\" @change=\"themeChange\" />\n        </div>\n      </div>\n\n      <el-divider/>\n\n      <h3 class=\"drawer-title\">系统布局配置</h3>\n\n      <div class=\"drawer-item\">\n        <span>开启 Tags-Views</span>\n        <el-switch v-model=\"tagsView\" class=\"drawer-switch\" />\n      </div>\n\n      <div class=\"drawer-item\">\n        <span>固定 Header</span>\n        <el-switch v-model=\"fixedHeader\" class=\"drawer-switch\" />\n      </div>\n\n      <div class=\"drawer-item\">\n        <span>显示 Logo</span>\n        <el-switch v-model=\"sidebarLogo\" class=\"drawer-switch\" />\n      </div>\n\n    </div>\n  </div>\n</template>\n\n<script>\nimport ThemePicker from '@/components/ThemePicker'\n\nexport default {\n  components: { ThemePicker },\n  data() {\n    return {}\n  },\n  computed: {\n    theme() {\n      return this.$store.state.settings.theme\n    },\n    sideTheme() {\n      return this.$store.state.settings.sideTheme\n    },\n    fixedHeader: {\n      get() {\n        return this.$store.state.settings.fixedHeader\n      },\n      set(val) {\n        this.$store.dispatch('settings/changeSetting', {\n          key: 'fixedHeader',\n          value: val\n        })\n      }\n    },\n    tagsView: {\n      get() {\n        return this.$store.state.settings.tagsView\n      },\n      set(val) {\n        this.$store.dispatch('settings/changeSetting', {\n          key: 'tagsView',\n          value: val\n        })\n      }\n    },\n    sidebarLogo: {\n      get() {\n        return this.$store.state.settings.sidebarLogo\n      },\n      set(val) {\n        this.$store.dispatch('settings/changeSetting', {\n          key: 'sidebarLogo',\n          value: val\n        })\n      }\n    },\n  },\n  methods: {\n    themeChange(val) {\n      this.$store.dispatch('settings/changeSetting', {\n        key: 'theme',\n        value: val\n      })\n    },\n    handleTheme(val) {\n      this.$store.dispatch('settings/changeSetting', {\n        key: 'sideTheme',\n        value: val\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n  .setting-drawer-content {\n    .setting-drawer-title {\n      margin-bottom: 12px;\n      color: rgba(0, 0, 0, .85);\n      font-size: 14px;\n      line-height: 22px;\n      font-weight: bold;\n    }\n\n    .setting-drawer-block-checbox {\n      display: flex;\n      justify-content: flex-start;\n      align-items: center;\n      margin-top: 10px;\n      margin-bottom: 20px;\n\n      .setting-drawer-block-checbox-item {\n        position: relative;\n        margin-right: 16px;\n        border-radius: 2px;\n        cursor: pointer;\n\n        img {\n          width: 48px;\n          height: 48px;\n        }\n\n        .setting-drawer-block-checbox-selectIcon {\n          position: absolute;\n          top: 0;\n          right: 0;\n          width: 100%;\n          height: 100%;\n          padding-top: 15px;\n          padding-left: 24px;\n          color: #1890ff;\n          font-weight: 700;\n          font-size: 14px;\n        }\n      }\n    }\n  }\n\n  .drawer-container {\n    padding: 24px;\n    font-size: 14px;\n    line-height: 1.5;\n    word-wrap: break-word;\n\n    .drawer-title {\n      margin-bottom: 12px;\n      color: rgba(0, 0, 0, .85);\n      font-size: 14px;\n      line-height: 22px;\n    }\n\n    .drawer-item {\n      color: rgba(0, 0, 0, .65);\n      font-size: 14px;\n      padding: 12px 0;\n    }\n\n    .drawer-switch {\n      float: right\n    }\n  }\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgEA,OAAAA,WAAA;AAEA;EACAC,UAAA;IAAAD,WAAA,EAAAA;EAAA;EACAE,IAAA,WAAAA,KAAA;IACA;EACA;EACAC,QAAA;IACAC,KAAA,WAAAA,MAAA;MACA,YAAAC,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAH,KAAA;IACA;IACAI,SAAA,WAAAA,UAAA;MACA,YAAAH,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAC,SAAA;IACA;IACAC,WAAA;MACAC,GAAA,WAAAA,IAAA;QACA,YAAAL,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAE,WAAA;MACA;MACAE,GAAA,WAAAA,IAAAC,GAAA;QACA,KAAAP,MAAA,CAAAQ,QAAA;UACAC,GAAA;UACAC,KAAA,EAAAH;QACA;MACA;IACA;IACAI,QAAA;MACAN,GAAA,WAAAA,IAAA;QACA,YAAAL,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAS,QAAA;MACA;MACAL,GAAA,WAAAA,IAAAC,GAAA;QACA,KAAAP,MAAA,CAAAQ,QAAA;UACAC,GAAA;UACAC,KAAA,EAAAH;QACA;MACA;IACA;IACAK,WAAA;MACAP,GAAA,WAAAA,IAAA;QACA,YAAAL,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAU,WAAA;MACA;MACAN,GAAA,WAAAA,IAAAC,GAAA;QACA,KAAAP,MAAA,CAAAQ,QAAA;UACAC,GAAA;UACAC,KAAA,EAAAH;QACA;MACA;IACA;EACA;EACAM,OAAA;IACAC,WAAA,WAAAA,YAAAP,GAAA;MACA,KAAAP,MAAA,CAAAQ,QAAA;QACAC,GAAA;QACAC,KAAA,EAAAH;MACA;IACA;IACAQ,WAAA,WAAAA,YAAAR,GAAA;MACA,KAAAP,MAAA,CAAAQ,QAAA;QACAC,GAAA;QACAC,KAAA,EAAAH;MACA;IACA;EACA;AACA", "ignoreList": []}]}