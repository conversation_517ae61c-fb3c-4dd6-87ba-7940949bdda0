{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/utils/generator/config.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/utils/generator/config.js", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["formConf", "formRef", "formModel", "size", "labelPosition", "labelWidth", "formRules", "gutter", "disabled", "span", "formBtns", "inputComponents", "label", "tag", "tagIcon", "placeholder", "defaultValue", "undefined", "style", "width", "clearable", "prepend", "append", "maxlength", "readonly", "required", "regList", "changeTag", "document", "type", "autosize", "minRows", "maxRows", "min", "max", "step", "precision", "selectComponents", "filterable", "multiple", "options", "value", "props", "id", "children", "dataType", "labelKey", "valueKey", "<PERSON><PERSON><PERSON>", "separator", "optionType", "border", "range", "selectableRange", "format", "action", "accept", "name", "showTip", "buttonText", "fileSize", "sizeUnit", "layoutComponents", "layout", "justify", "align", "layoutTree", "default", "icon", "trigger"], "sources": ["/Users/<USER>/develop/code/project/haijia/adminweb/src/utils/generator/config.js"], "sourcesContent": ["export const formConf = {\n  formRef: 'elForm',\n  formModel: 'formData',\n  size: 'medium',\n  labelPosition: 'right',\n  labelWidth: 100,\n  formRules: 'rules',\n  gutter: 15,\n  disabled: false,\n  span: 24,\n  formBtns: true\n}\n\nexport const inputComponents = [\n  {\n    label: '单行文本',\n    tag: 'el-input',\n    tagIcon: 'input',\n    placeholder: '请输入',\n    defaultValue: undefined,\n    span: 24,\n    labelWidth: null,\n    style: { width: '100%' },\n    clearable: true,\n    prepend: '',\n    append: '',\n    'prefix-icon': '',\n    'suffix-icon': '',\n    maxlength: null,\n    'show-word-limit': false,\n    readonly: false,\n    disabled: false,\n    required: true,\n    regList: [],\n    changeTag: true,\n    document: 'https://element.eleme.cn/#/zh-CN/component/input'\n  },\n  {\n    label: '多行文本',\n    tag: 'el-input',\n    tagIcon: 'textarea',\n    type: 'textarea',\n    placeholder: '请输入',\n    defaultValue: undefined,\n    span: 24,\n    labelWidth: null,\n    autosize: {\n      minRows: 4,\n      maxRows: 4\n    },\n    style: { width: '100%' },\n    maxlength: null,\n    'show-word-limit': false,\n    readonly: false,\n    disabled: false,\n    required: true,\n    regList: [],\n    changeTag: true,\n    document: 'https://element.eleme.cn/#/zh-CN/component/input'\n  },\n  {\n    label: '密码',\n    tag: 'el-input',\n    tagIcon: 'password',\n    placeholder: '请输入',\n    defaultValue: undefined,\n    span: 24,\n    'show-password': true,\n    labelWidth: null,\n    style: { width: '100%' },\n    clearable: true,\n    prepend: '',\n    append: '',\n    'prefix-icon': '',\n    'suffix-icon': '',\n    maxlength: null,\n    'show-word-limit': false,\n    readonly: false,\n    disabled: false,\n    required: true,\n    regList: [],\n    changeTag: true,\n    document: 'https://element.eleme.cn/#/zh-CN/component/input'\n  },\n  {\n    label: '计数器',\n    tag: 'el-input-number',\n    tagIcon: 'number',\n    placeholder: '',\n    defaultValue: undefined,\n    span: 24,\n    labelWidth: null,\n    min: undefined,\n    max: undefined,\n    step: undefined,\n    'step-strictly': false,\n    precision: undefined,\n    'controls-position': '',\n    disabled: false,\n    required: true,\n    regList: [],\n    changeTag: true,\n    document: 'https://element.eleme.cn/#/zh-CN/component/input-number'\n  }\n]\n\nexport const selectComponents = [\n  {\n    label: '下拉选择',\n    tag: 'el-select',\n    tagIcon: 'select',\n    placeholder: '请选择',\n    defaultValue: undefined,\n    span: 24,\n    labelWidth: null,\n    style: { width: '100%' },\n    clearable: true,\n    disabled: false,\n    required: true,\n    filterable: false,\n    multiple: false,\n    options: [{\n      label: '选项一',\n      value: 1\n    }, {\n      label: '选项二',\n      value: 2\n    }],\n    regList: [],\n    changeTag: true,\n    document: 'https://element.eleme.cn/#/zh-CN/component/select'\n  },\n  {\n    label: '级联选择',\n    tag: 'el-cascader',\n    tagIcon: 'cascader',\n    placeholder: '请选择',\n    defaultValue: [],\n    span: 24,\n    labelWidth: null,\n    style: { width: '100%' },\n    props: {\n      props: {\n        multiple: false\n      }\n    },\n    'show-all-levels': true,\n    disabled: false,\n    clearable: true,\n    filterable: false,\n    required: true,\n    options: [{\n      id: 1,\n      value: 1,\n      label: '选项1',\n      children: [{\n        id: 2,\n        value: 2,\n        label: '选项1-1'\n      }]\n    }],\n    dataType: 'dynamic',\n    labelKey: 'label',\n    valueKey: 'value',\n    childrenKey: 'children',\n    separator: '/',\n    regList: [],\n    changeTag: true,\n    document: 'https://element.eleme.cn/#/zh-CN/component/cascader'\n  },\n  {\n    label: '单选框组',\n    tag: 'el-radio-group',\n    tagIcon: 'radio',\n    defaultValue: undefined,\n    span: 24,\n    labelWidth: null,\n    style: {},\n    optionType: 'default',\n    border: false,\n    size: 'medium',\n    disabled: false,\n    required: true,\n    options: [{\n      label: '选项一',\n      value: 1\n    }, {\n      label: '选项二',\n      value: 2\n    }],\n    regList: [],\n    changeTag: true,\n    document: 'https://element.eleme.cn/#/zh-CN/component/radio'\n  },\n  {\n    label: '多选框组',\n    tag: 'el-checkbox-group',\n    tagIcon: 'checkbox',\n    defaultValue: [],\n    span: 24,\n    labelWidth: null,\n    style: {},\n    optionType: 'default',\n    border: false,\n    size: 'medium',\n    disabled: false,\n    required: true,\n    options: [{\n      label: '选项一',\n      value: 1\n    }, {\n      label: '选项二',\n      value: 2\n    }],\n    regList: [],\n    changeTag: true,\n    document: 'https://element.eleme.cn/#/zh-CN/component/checkbox'\n  },\n  {\n    label: '开关',\n    tag: 'el-switch',\n    tagIcon: 'switch',\n    defaultValue: false,\n    span: 24,\n    labelWidth: null,\n    style: {},\n    disabled: false,\n    required: true,\n    'active-text': '',\n    'inactive-text': '',\n    'active-color': null,\n    'inactive-color': null,\n    'active-value': true,\n    'inactive-value': false,\n    regList: [],\n    changeTag: true,\n    document: 'https://element.eleme.cn/#/zh-CN/component/switch'\n  },\n  {\n    label: '滑块',\n    tag: 'el-slider',\n    tagIcon: 'slider',\n    defaultValue: null,\n    span: 24,\n    labelWidth: null,\n    disabled: false,\n    required: true,\n    min: 0,\n    max: 100,\n    step: 1,\n    'show-stops': false,\n    range: false,\n    regList: [],\n    changeTag: true,\n    document: 'https://element.eleme.cn/#/zh-CN/component/slider'\n  },\n  {\n    label: '时间选择',\n    tag: 'el-time-picker',\n    tagIcon: 'time',\n    placeholder: '请选择',\n    defaultValue: null,\n    span: 24,\n    labelWidth: null,\n    style: { width: '100%' },\n    disabled: false,\n    clearable: true,\n    required: true,\n    'picker-options': {\n      selectableRange: '00:00:00-23:59:59'\n    },\n    format: 'HH:mm:ss',\n    'value-format': 'HH:mm:ss',\n    regList: [],\n    changeTag: true,\n    document: 'https://element.eleme.cn/#/zh-CN/component/time-picker'\n  },\n  {\n    label: '时间范围',\n    tag: 'el-time-picker',\n    tagIcon: 'time-range',\n    defaultValue: null,\n    span: 24,\n    labelWidth: null,\n    style: { width: '100%' },\n    disabled: false,\n    clearable: true,\n    required: true,\n    'is-range': true,\n    'range-separator': '至',\n    'start-placeholder': '开始时间',\n    'end-placeholder': '结束时间',\n    format: 'HH:mm:ss',\n    'value-format': 'HH:mm:ss',\n    regList: [],\n    changeTag: true,\n    document: 'https://element.eleme.cn/#/zh-CN/component/time-picker'\n  },\n  {\n    label: '日期选择',\n    tag: 'el-date-picker',\n    tagIcon: 'date',\n    placeholder: '请选择',\n    defaultValue: null,\n    type: 'date',\n    span: 24,\n    labelWidth: null,\n    style: { width: '100%' },\n    disabled: false,\n    clearable: true,\n    required: true,\n    format: 'yyyy-MM-dd',\n    'value-format': 'yyyy-MM-dd',\n    readonly: false,\n    regList: [],\n    changeTag: true,\n    document: 'https://element.eleme.cn/#/zh-CN/component/date-picker'\n  },\n  {\n    label: '日期范围',\n    tag: 'el-date-picker',\n    tagIcon: 'date-range',\n    defaultValue: null,\n    span: 24,\n    labelWidth: null,\n    style: { width: '100%' },\n    type: 'daterange',\n    'range-separator': '至',\n    'start-placeholder': '开始日期',\n    'end-placeholder': '结束日期',\n    disabled: false,\n    clearable: true,\n    required: true,\n    format: 'yyyy-MM-dd',\n    'value-format': 'yyyy-MM-dd',\n    readonly: false,\n    regList: [],\n    changeTag: true,\n    document: 'https://element.eleme.cn/#/zh-CN/component/date-picker'\n  },\n  {\n    label: '评分',\n    tag: 'el-rate',\n    tagIcon: 'rate',\n    defaultValue: 0,\n    span: 24,\n    labelWidth: null,\n    style: {},\n    max: 5,\n    'allow-half': false,\n    'show-text': false,\n    'show-score': false,\n    disabled: false,\n    required: true,\n    regList: [],\n    changeTag: true,\n    document: 'https://element.eleme.cn/#/zh-CN/component/rate'\n  },\n  {\n    label: '颜色选择',\n    tag: 'el-color-picker',\n    tagIcon: 'color',\n    defaultValue: null,\n    labelWidth: null,\n    'show-alpha': false,\n    'color-format': '',\n    disabled: false,\n    required: true,\n    size: 'medium',\n    regList: [],\n    changeTag: true,\n    document: 'https://element.eleme.cn/#/zh-CN/component/color-picker'\n  },\n  {\n    label: '上传',\n    tag: 'el-upload',\n    tagIcon: 'upload',\n    action: 'https://jsonplaceholder.typicode.com/posts/',\n    defaultValue: null,\n    labelWidth: null,\n    disabled: false,\n    required: true,\n    accept: '',\n    name: 'file',\n    'auto-upload': true,\n    showTip: false,\n    buttonText: '点击上传',\n    fileSize: 2,\n    sizeUnit: 'MB',\n    'list-type': 'text',\n    multiple: false,\n    regList: [],\n    changeTag: true,\n    document: 'https://element.eleme.cn/#/zh-CN/component/upload'\n  }\n]\n\nexport const layoutComponents = [\n  {\n    layout: 'rowFormItem',\n    tagIcon: 'row',\n    type: 'default',\n    justify: 'start',\n    align: 'top',\n    label: '行容器',\n    layoutTree: true,\n    children: [],\n    document: 'https://element.eleme.cn/#/zh-CN/component/layout'\n  },\n  {\n    layout: 'colFormItem',\n    label: '按钮',\n    changeTag: true,\n    labelWidth: null,\n    tag: 'el-button',\n    tagIcon: 'button',\n    span: 24,\n    default: '主要按钮',\n    type: 'primary',\n    icon: 'el-icon-search',\n    size: 'medium',\n    disabled: false,\n    document: 'https://element.eleme.cn/#/zh-CN/component/button'\n  }\n]\n\n// 组件rule的触发方式，无触发方式的组件不生成rule\nexport const trigger = {\n  'el-input': 'blur',\n  'el-input-number': 'blur',\n  'el-select': 'change',\n  'el-radio-group': 'change',\n  'el-checkbox-group': 'change',\n  'el-cascader': 'change',\n  'el-time-picker': 'change',\n  'el-date-picker': 'change',\n  'el-rate': 'change'\n}\n"], "mappings": "AAAA,OAAO,IAAMA,QAAQ,GAAG;EACtBC,OAAO,EAAE,QAAQ;EACjBC,SAAS,EAAE,UAAU;EACrBC,IAAI,EAAE,QAAQ;EACdC,aAAa,EAAE,OAAO;EACtBC,UAAU,EAAE,GAAG;EACfC,SAAS,EAAE,OAAO;EAClBC,MAAM,EAAE,EAAE;EACVC,QAAQ,EAAE,KAAK;EACfC,IAAI,EAAE,EAAE;EACRC,QAAQ,EAAE;AACZ,CAAC;AAED,OAAO,IAAMC,eAAe,GAAG,CAC7B;EACEC,KAAK,EAAE,MAAM;EACbC,GAAG,EAAE,UAAU;EACfC,OAAO,EAAE,OAAO;EAChBC,WAAW,EAAE,KAAK;EAClBC,YAAY,EAAEC,SAAS;EACvBR,IAAI,EAAE,EAAE;EACRJ,UAAU,EAAE,IAAI;EAChBa,KAAK,EAAE;IAAEC,KAAK,EAAE;EAAO,CAAC;EACxBC,SAAS,EAAE,IAAI;EACfC,OAAO,EAAE,EAAE;EACXC,MAAM,EAAE,EAAE;EACV,aAAa,EAAE,EAAE;EACjB,aAAa,EAAE,EAAE;EACjBC,SAAS,EAAE,IAAI;EACf,iBAAiB,EAAE,KAAK;EACxBC,QAAQ,EAAE,KAAK;EACfhB,QAAQ,EAAE,KAAK;EACfiB,QAAQ,EAAE,IAAI;EACdC,OAAO,EAAE,EAAE;EACXC,SAAS,EAAE,IAAI;EACfC,QAAQ,EAAE;AACZ,CAAC,EACD;EACEhB,KAAK,EAAE,MAAM;EACbC,GAAG,EAAE,UAAU;EACfC,OAAO,EAAE,UAAU;EACnBe,IAAI,EAAE,UAAU;EAChBd,WAAW,EAAE,KAAK;EAClBC,YAAY,EAAEC,SAAS;EACvBR,IAAI,EAAE,EAAE;EACRJ,UAAU,EAAE,IAAI;EAChByB,QAAQ,EAAE;IACRC,OAAO,EAAE,CAAC;IACVC,OAAO,EAAE;EACX,CAAC;EACDd,KAAK,EAAE;IAAEC,KAAK,EAAE;EAAO,CAAC;EACxBI,SAAS,EAAE,IAAI;EACf,iBAAiB,EAAE,KAAK;EACxBC,QAAQ,EAAE,KAAK;EACfhB,QAAQ,EAAE,KAAK;EACfiB,QAAQ,EAAE,IAAI;EACdC,OAAO,EAAE,EAAE;EACXC,SAAS,EAAE,IAAI;EACfC,QAAQ,EAAE;AACZ,CAAC,EACD;EACEhB,KAAK,EAAE,IAAI;EACXC,GAAG,EAAE,UAAU;EACfC,OAAO,EAAE,UAAU;EACnBC,WAAW,EAAE,KAAK;EAClBC,YAAY,EAAEC,SAAS;EACvBR,IAAI,EAAE,EAAE;EACR,eAAe,EAAE,IAAI;EACrBJ,UAAU,EAAE,IAAI;EAChBa,KAAK,EAAE;IAAEC,KAAK,EAAE;EAAO,CAAC;EACxBC,SAAS,EAAE,IAAI;EACfC,OAAO,EAAE,EAAE;EACXC,MAAM,EAAE,EAAE;EACV,aAAa,EAAE,EAAE;EACjB,aAAa,EAAE,EAAE;EACjBC,SAAS,EAAE,IAAI;EACf,iBAAiB,EAAE,KAAK;EACxBC,QAAQ,EAAE,KAAK;EACfhB,QAAQ,EAAE,KAAK;EACfiB,QAAQ,EAAE,IAAI;EACdC,OAAO,EAAE,EAAE;EACXC,SAAS,EAAE,IAAI;EACfC,QAAQ,EAAE;AACZ,CAAC,EACD;EACEhB,KAAK,EAAE,KAAK;EACZC,GAAG,EAAE,iBAAiB;EACtBC,OAAO,EAAE,QAAQ;EACjBC,WAAW,EAAE,EAAE;EACfC,YAAY,EAAEC,SAAS;EACvBR,IAAI,EAAE,EAAE;EACRJ,UAAU,EAAE,IAAI;EAChB4B,GAAG,EAAEhB,SAAS;EACdiB,GAAG,EAAEjB,SAAS;EACdkB,IAAI,EAAElB,SAAS;EACf,eAAe,EAAE,KAAK;EACtBmB,SAAS,EAAEnB,SAAS;EACpB,mBAAmB,EAAE,EAAE;EACvBT,QAAQ,EAAE,KAAK;EACfiB,QAAQ,EAAE,IAAI;EACdC,OAAO,EAAE,EAAE;EACXC,SAAS,EAAE,IAAI;EACfC,QAAQ,EAAE;AACZ,CAAC,CACF;AAED,OAAO,IAAMS,gBAAgB,GAAG,CAC9B;EACEzB,KAAK,EAAE,MAAM;EACbC,GAAG,EAAE,WAAW;EAChBC,OAAO,EAAE,QAAQ;EACjBC,WAAW,EAAE,KAAK;EAClBC,YAAY,EAAEC,SAAS;EACvBR,IAAI,EAAE,EAAE;EACRJ,UAAU,EAAE,IAAI;EAChBa,KAAK,EAAE;IAAEC,KAAK,EAAE;EAAO,CAAC;EACxBC,SAAS,EAAE,IAAI;EACfZ,QAAQ,EAAE,KAAK;EACfiB,QAAQ,EAAE,IAAI;EACda,UAAU,EAAE,KAAK;EACjBC,QAAQ,EAAE,KAAK;EACfC,OAAO,EAAE,CAAC;IACR5B,KAAK,EAAE,KAAK;IACZ6B,KAAK,EAAE;EACT,CAAC,EAAE;IACD7B,KAAK,EAAE,KAAK;IACZ6B,KAAK,EAAE;EACT,CAAC,CAAC;EACFf,OAAO,EAAE,EAAE;EACXC,SAAS,EAAE,IAAI;EACfC,QAAQ,EAAE;AACZ,CAAC,EACD;EACEhB,KAAK,EAAE,MAAM;EACbC,GAAG,EAAE,aAAa;EAClBC,OAAO,EAAE,UAAU;EACnBC,WAAW,EAAE,KAAK;EAClBC,YAAY,EAAE,EAAE;EAChBP,IAAI,EAAE,EAAE;EACRJ,UAAU,EAAE,IAAI;EAChBa,KAAK,EAAE;IAAEC,KAAK,EAAE;EAAO,CAAC;EACxBuB,KAAK,EAAE;IACLA,KAAK,EAAE;MACLH,QAAQ,EAAE;IACZ;EACF,CAAC;EACD,iBAAiB,EAAE,IAAI;EACvB/B,QAAQ,EAAE,KAAK;EACfY,SAAS,EAAE,IAAI;EACfkB,UAAU,EAAE,KAAK;EACjBb,QAAQ,EAAE,IAAI;EACde,OAAO,EAAE,CAAC;IACRG,EAAE,EAAE,CAAC;IACLF,KAAK,EAAE,CAAC;IACR7B,KAAK,EAAE,KAAK;IACZgC,QAAQ,EAAE,CAAC;MACTD,EAAE,EAAE,CAAC;MACLF,KAAK,EAAE,CAAC;MACR7B,KAAK,EAAE;IACT,CAAC;EACH,CAAC,CAAC;EACFiC,QAAQ,EAAE,SAAS;EACnBC,QAAQ,EAAE,OAAO;EACjBC,QAAQ,EAAE,OAAO;EACjBC,WAAW,EAAE,UAAU;EACvBC,SAAS,EAAE,GAAG;EACdvB,OAAO,EAAE,EAAE;EACXC,SAAS,EAAE,IAAI;EACfC,QAAQ,EAAE;AACZ,CAAC,EACD;EACEhB,KAAK,EAAE,MAAM;EACbC,GAAG,EAAE,gBAAgB;EACrBC,OAAO,EAAE,OAAO;EAChBE,YAAY,EAAEC,SAAS;EACvBR,IAAI,EAAE,EAAE;EACRJ,UAAU,EAAE,IAAI;EAChBa,KAAK,EAAE,CAAC,CAAC;EACTgC,UAAU,EAAE,SAAS;EACrBC,MAAM,EAAE,KAAK;EACbhD,IAAI,EAAE,QAAQ;EACdK,QAAQ,EAAE,KAAK;EACfiB,QAAQ,EAAE,IAAI;EACde,OAAO,EAAE,CAAC;IACR5B,KAAK,EAAE,KAAK;IACZ6B,KAAK,EAAE;EACT,CAAC,EAAE;IACD7B,KAAK,EAAE,KAAK;IACZ6B,KAAK,EAAE;EACT,CAAC,CAAC;EACFf,OAAO,EAAE,EAAE;EACXC,SAAS,EAAE,IAAI;EACfC,QAAQ,EAAE;AACZ,CAAC,EACD;EACEhB,KAAK,EAAE,MAAM;EACbC,GAAG,EAAE,mBAAmB;EACxBC,OAAO,EAAE,UAAU;EACnBE,YAAY,EAAE,EAAE;EAChBP,IAAI,EAAE,EAAE;EACRJ,UAAU,EAAE,IAAI;EAChBa,KAAK,EAAE,CAAC,CAAC;EACTgC,UAAU,EAAE,SAAS;EACrBC,MAAM,EAAE,KAAK;EACbhD,IAAI,EAAE,QAAQ;EACdK,QAAQ,EAAE,KAAK;EACfiB,QAAQ,EAAE,IAAI;EACde,OAAO,EAAE,CAAC;IACR5B,KAAK,EAAE,KAAK;IACZ6B,KAAK,EAAE;EACT,CAAC,EAAE;IACD7B,KAAK,EAAE,KAAK;IACZ6B,KAAK,EAAE;EACT,CAAC,CAAC;EACFf,OAAO,EAAE,EAAE;EACXC,SAAS,EAAE,IAAI;EACfC,QAAQ,EAAE;AACZ,CAAC,EACD;EACEhB,KAAK,EAAE,IAAI;EACXC,GAAG,EAAE,WAAW;EAChBC,OAAO,EAAE,QAAQ;EACjBE,YAAY,EAAE,KAAK;EACnBP,IAAI,EAAE,EAAE;EACRJ,UAAU,EAAE,IAAI;EAChBa,KAAK,EAAE,CAAC,CAAC;EACTV,QAAQ,EAAE,KAAK;EACfiB,QAAQ,EAAE,IAAI;EACd,aAAa,EAAE,EAAE;EACjB,eAAe,EAAE,EAAE;EACnB,cAAc,EAAE,IAAI;EACpB,gBAAgB,EAAE,IAAI;EACtB,cAAc,EAAE,IAAI;EACpB,gBAAgB,EAAE,KAAK;EACvBC,OAAO,EAAE,EAAE;EACXC,SAAS,EAAE,IAAI;EACfC,QAAQ,EAAE;AACZ,CAAC,EACD;EACEhB,KAAK,EAAE,IAAI;EACXC,GAAG,EAAE,WAAW;EAChBC,OAAO,EAAE,QAAQ;EACjBE,YAAY,EAAE,IAAI;EAClBP,IAAI,EAAE,EAAE;EACRJ,UAAU,EAAE,IAAI;EAChBG,QAAQ,EAAE,KAAK;EACfiB,QAAQ,EAAE,IAAI;EACdQ,GAAG,EAAE,CAAC;EACNC,GAAG,EAAE,GAAG;EACRC,IAAI,EAAE,CAAC;EACP,YAAY,EAAE,KAAK;EACnBiB,KAAK,EAAE,KAAK;EACZ1B,OAAO,EAAE,EAAE;EACXC,SAAS,EAAE,IAAI;EACfC,QAAQ,EAAE;AACZ,CAAC,EACD;EACEhB,KAAK,EAAE,MAAM;EACbC,GAAG,EAAE,gBAAgB;EACrBC,OAAO,EAAE,MAAM;EACfC,WAAW,EAAE,KAAK;EAClBC,YAAY,EAAE,IAAI;EAClBP,IAAI,EAAE,EAAE;EACRJ,UAAU,EAAE,IAAI;EAChBa,KAAK,EAAE;IAAEC,KAAK,EAAE;EAAO,CAAC;EACxBX,QAAQ,EAAE,KAAK;EACfY,SAAS,EAAE,IAAI;EACfK,QAAQ,EAAE,IAAI;EACd,gBAAgB,EAAE;IAChB4B,eAAe,EAAE;EACnB,CAAC;EACDC,MAAM,EAAE,UAAU;EAClB,cAAc,EAAE,UAAU;EAC1B5B,OAAO,EAAE,EAAE;EACXC,SAAS,EAAE,IAAI;EACfC,QAAQ,EAAE;AACZ,CAAC,EACD;EACEhB,KAAK,EAAE,MAAM;EACbC,GAAG,EAAE,gBAAgB;EACrBC,OAAO,EAAE,YAAY;EACrBE,YAAY,EAAE,IAAI;EAClBP,IAAI,EAAE,EAAE;EACRJ,UAAU,EAAE,IAAI;EAChBa,KAAK,EAAE;IAAEC,KAAK,EAAE;EAAO,CAAC;EACxBX,QAAQ,EAAE,KAAK;EACfY,SAAS,EAAE,IAAI;EACfK,QAAQ,EAAE,IAAI;EACd,UAAU,EAAE,IAAI;EAChB,iBAAiB,EAAE,GAAG;EACtB,mBAAmB,EAAE,MAAM;EAC3B,iBAAiB,EAAE,MAAM;EACzB6B,MAAM,EAAE,UAAU;EAClB,cAAc,EAAE,UAAU;EAC1B5B,OAAO,EAAE,EAAE;EACXC,SAAS,EAAE,IAAI;EACfC,QAAQ,EAAE;AACZ,CAAC,EACD;EACEhB,KAAK,EAAE,MAAM;EACbC,GAAG,EAAE,gBAAgB;EACrBC,OAAO,EAAE,MAAM;EACfC,WAAW,EAAE,KAAK;EAClBC,YAAY,EAAE,IAAI;EAClBa,IAAI,EAAE,MAAM;EACZpB,IAAI,EAAE,EAAE;EACRJ,UAAU,EAAE,IAAI;EAChBa,KAAK,EAAE;IAAEC,KAAK,EAAE;EAAO,CAAC;EACxBX,QAAQ,EAAE,KAAK;EACfY,SAAS,EAAE,IAAI;EACfK,QAAQ,EAAE,IAAI;EACd6B,MAAM,EAAE,YAAY;EACpB,cAAc,EAAE,YAAY;EAC5B9B,QAAQ,EAAE,KAAK;EACfE,OAAO,EAAE,EAAE;EACXC,SAAS,EAAE,IAAI;EACfC,QAAQ,EAAE;AACZ,CAAC,EACD;EACEhB,KAAK,EAAE,MAAM;EACbC,GAAG,EAAE,gBAAgB;EACrBC,OAAO,EAAE,YAAY;EACrBE,YAAY,EAAE,IAAI;EAClBP,IAAI,EAAE,EAAE;EACRJ,UAAU,EAAE,IAAI;EAChBa,KAAK,EAAE;IAAEC,KAAK,EAAE;EAAO,CAAC;EACxBU,IAAI,EAAE,WAAW;EACjB,iBAAiB,EAAE,GAAG;EACtB,mBAAmB,EAAE,MAAM;EAC3B,iBAAiB,EAAE,MAAM;EACzBrB,QAAQ,EAAE,KAAK;EACfY,SAAS,EAAE,IAAI;EACfK,QAAQ,EAAE,IAAI;EACd6B,MAAM,EAAE,YAAY;EACpB,cAAc,EAAE,YAAY;EAC5B9B,QAAQ,EAAE,KAAK;EACfE,OAAO,EAAE,EAAE;EACXC,SAAS,EAAE,IAAI;EACfC,QAAQ,EAAE;AACZ,CAAC,EACD;EACEhB,KAAK,EAAE,IAAI;EACXC,GAAG,EAAE,SAAS;EACdC,OAAO,EAAE,MAAM;EACfE,YAAY,EAAE,CAAC;EACfP,IAAI,EAAE,EAAE;EACRJ,UAAU,EAAE,IAAI;EAChBa,KAAK,EAAE,CAAC,CAAC;EACTgB,GAAG,EAAE,CAAC;EACN,YAAY,EAAE,KAAK;EACnB,WAAW,EAAE,KAAK;EAClB,YAAY,EAAE,KAAK;EACnB1B,QAAQ,EAAE,KAAK;EACfiB,QAAQ,EAAE,IAAI;EACdC,OAAO,EAAE,EAAE;EACXC,SAAS,EAAE,IAAI;EACfC,QAAQ,EAAE;AACZ,CAAC,EACD;EACEhB,KAAK,EAAE,MAAM;EACbC,GAAG,EAAE,iBAAiB;EACtBC,OAAO,EAAE,OAAO;EAChBE,YAAY,EAAE,IAAI;EAClBX,UAAU,EAAE,IAAI;EAChB,YAAY,EAAE,KAAK;EACnB,cAAc,EAAE,EAAE;EAClBG,QAAQ,EAAE,KAAK;EACfiB,QAAQ,EAAE,IAAI;EACdtB,IAAI,EAAE,QAAQ;EACduB,OAAO,EAAE,EAAE;EACXC,SAAS,EAAE,IAAI;EACfC,QAAQ,EAAE;AACZ,CAAC,EACD;EACEhB,KAAK,EAAE,IAAI;EACXC,GAAG,EAAE,WAAW;EAChBC,OAAO,EAAE,QAAQ;EACjByC,MAAM,EAAE,6CAA6C;EACrDvC,YAAY,EAAE,IAAI;EAClBX,UAAU,EAAE,IAAI;EAChBG,QAAQ,EAAE,KAAK;EACfiB,QAAQ,EAAE,IAAI;EACd+B,MAAM,EAAE,EAAE;EACVC,IAAI,EAAE,MAAM;EACZ,aAAa,EAAE,IAAI;EACnBC,OAAO,EAAE,KAAK;EACdC,UAAU,EAAE,MAAM;EAClBC,QAAQ,EAAE,CAAC;EACXC,QAAQ,EAAE,IAAI;EACd,WAAW,EAAE,MAAM;EACnBtB,QAAQ,EAAE,KAAK;EACfb,OAAO,EAAE,EAAE;EACXC,SAAS,EAAE,IAAI;EACfC,QAAQ,EAAE;AACZ,CAAC,CACF;AAED,OAAO,IAAMkC,gBAAgB,GAAG,CAC9B;EACEC,MAAM,EAAE,aAAa;EACrBjD,OAAO,EAAE,KAAK;EACde,IAAI,EAAE,SAAS;EACfmC,OAAO,EAAE,OAAO;EAChBC,KAAK,EAAE,KAAK;EACZrD,KAAK,EAAE,KAAK;EACZsD,UAAU,EAAE,IAAI;EAChBtB,QAAQ,EAAE,EAAE;EACZhB,QAAQ,EAAE;AACZ,CAAC,EACD;EACEmC,MAAM,EAAE,aAAa;EACrBnD,KAAK,EAAE,IAAI;EACXe,SAAS,EAAE,IAAI;EACftB,UAAU,EAAE,IAAI;EAChBQ,GAAG,EAAE,WAAW;EAChBC,OAAO,EAAE,QAAQ;EACjBL,IAAI,EAAE,EAAE;EACR0D,OAAO,EAAE,MAAM;EACftC,IAAI,EAAE,SAAS;EACfuC,IAAI,EAAE,gBAAgB;EACtBjE,IAAI,EAAE,QAAQ;EACdK,QAAQ,EAAE,KAAK;EACfoB,QAAQ,EAAE;AACZ,CAAC,CACF;;AAED;AACA,OAAO,IAAMyC,OAAO,GAAG;EACrB,UAAU,EAAE,MAAM;EAClB,iBAAiB,EAAE,MAAM;EACzB,WAAW,EAAE,QAAQ;EACrB,gBAAgB,EAAE,QAAQ;EAC1B,mBAAmB,EAAE,QAAQ;EAC7B,aAAa,EAAE,QAAQ;EACvB,gBAAgB,EAAE,QAAQ;EAC1B,gBAAgB,EAAE,QAAQ;EAC1B,SAAS,EAAE;AACb,CAAC", "ignoreList": []}]}