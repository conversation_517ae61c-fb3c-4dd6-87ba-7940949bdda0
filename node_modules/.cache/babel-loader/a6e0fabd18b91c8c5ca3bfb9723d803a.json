{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/monitor/operlog/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/monitor/operlog/index.vue", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGxpc3QsIGRlbE9wZXJsb2csIGNsZWFuT3BlcmxvZywgZXhwb3J0T3BlcmxvZyB9IGZyb20gIkAvYXBpL21vbml0b3Ivb3BlcmxvZyI7CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiT3BlcmxvZyIsCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIC8vIOmBrue9qeWxggogICAgICBsb2FkaW5nOiB0cnVlLAogICAgICAvLyDpgInkuK3mlbDnu4QKICAgICAgaWRzOiBbXSwKICAgICAgLy8g6Z2e5aSa5Liq56aB55SoCiAgICAgIG11bHRpcGxlOiB0cnVlLAogICAgICAvLyDmmL7npLrmkJzntKLmnaHku7YKICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwKICAgICAgLy8g5oC75p2h5pWwCiAgICAgIHRvdGFsOiAwLAogICAgICAvLyDooajmoLzmlbDmja4KICAgICAgbGlzdDogW10sCiAgICAgIC8vIOaYr+WQpuaYvuekuuW8ueWHuuWxggogICAgICBvcGVuOiBmYWxzZSwKICAgICAgLy8g57G75Z6L5pWw5o2u5a2X5YW4CiAgICAgIHR5cGVPcHRpb25zOiBbXSwKICAgICAgLy8g57G75Z6L5pWw5o2u5a2X5YW4CiAgICAgIHN0YXR1c09wdGlvbnM6IFtdLAogICAgICAvLyDml6XmnJ/ojIPlm7QKICAgICAgZGF0ZVJhbmdlOiBbXSwKICAgICAgLy8g6KGo5Y2V5Y+C5pWwCiAgICAgIGZvcm06IHt9LAogICAgICAvLyDmn6Xor6Llj4LmlbAKICAgICAgcXVlcnlQYXJhbXM6IHsKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICB0aXRsZTogdW5kZWZpbmVkLAogICAgICAgIG9wZXJOYW1lOiB1bmRlZmluZWQsCiAgICAgICAgYnVzaW5lc3NUeXBlOiB1bmRlZmluZWQsCiAgICAgICAgc3RhdHVzOiB1bmRlZmluZWQKICAgICAgfQogICAgfTsKICB9LAogIGNyZWF0ZWQ6IGZ1bmN0aW9uIGNyZWF0ZWQoKSB7CiAgICB2YXIgX3RoaXMgPSB0aGlzOwogICAgdGhpcy5nZXRMaXN0KCk7CiAgICB0aGlzLmdldERpY3RzKCJzeXNfb3Blcl90eXBlIikudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgX3RoaXMudHlwZU9wdGlvbnMgPSByZXNwb25zZS5kYXRhOwogICAgfSk7CiAgICB0aGlzLmdldERpY3RzKCJzeXNfY29tbW9uX3N0YXR1cyIpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgIF90aGlzLnN0YXR1c09wdGlvbnMgPSByZXNwb25zZS5kYXRhOwogICAgfSk7CiAgfSwKICBtZXRob2RzOiB7CiAgICAvKiog5p+l6K+i55m75b2V5pel5b+XICovZ2V0TGlzdDogZnVuY3Rpb24gZ2V0TGlzdCgpIHsKICAgICAgdmFyIF90aGlzMiA9IHRoaXM7CiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7CiAgICAgIGxpc3QodGhpcy5hZGREYXRlUmFuZ2UodGhpcy5xdWVyeVBhcmFtcywgdGhpcy5kYXRlUmFuZ2UpKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgIF90aGlzMi5saXN0ID0gcmVzcG9uc2Uucm93czsKICAgICAgICBfdGhpczIudG90YWwgPSByZXNwb25zZS50b3RhbDsKICAgICAgICBfdGhpczIubG9hZGluZyA9IGZhbHNlOwogICAgICB9KTsKICAgIH0sCiAgICAvLyDmk43kvZzml6Xlv5fnirbmgIHlrZflhbjnv7vor5EKICAgIHN0YXR1c0Zvcm1hdDogZnVuY3Rpb24gc3RhdHVzRm9ybWF0KHJvdywgY29sdW1uKSB7CiAgICAgIHJldHVybiB0aGlzLnNlbGVjdERpY3RMYWJlbCh0aGlzLnN0YXR1c09wdGlvbnMsIHJvdy5zdGF0dXMpOwogICAgfSwKICAgIC8vIOaTjeS9nOaXpeW/l+exu+Wei+Wtl+WFuOe/u+ivkQogICAgdHlwZUZvcm1hdDogZnVuY3Rpb24gdHlwZUZvcm1hdChyb3csIGNvbHVtbikgewogICAgICByZXR1cm4gdGhpcy5zZWxlY3REaWN0TGFiZWwodGhpcy50eXBlT3B0aW9ucywgcm93LmJ1c2luZXNzVHlwZSk7CiAgICB9LAogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqL2hhbmRsZVF1ZXJ5OiBmdW5jdGlvbiBoYW5kbGVRdWVyeSgpIHsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsKICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICB9LAogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqL3Jlc2V0UXVlcnk6IGZ1bmN0aW9uIHJlc2V0UXVlcnkoKSB7CiAgICAgIHRoaXMuZGF0ZVJhbmdlID0gW107CiAgICAgIHRoaXMucmVzZXRGb3JtKCJxdWVyeUZvcm0iKTsKICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOwogICAgfSwKICAgIC8vIOWkmumAieahhumAieS4reaVsOaNrgogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlOiBmdW5jdGlvbiBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7CiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgIHJldHVybiBpdGVtLm9wZXJJZDsKICAgICAgfSk7CiAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aDsKICAgIH0sCiAgICAvKiog6K+m57uG5oyJ6ZKu5pON5L2cICovaGFuZGxlVmlldzogZnVuY3Rpb24gaGFuZGxlVmlldyhyb3cpIHsKICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsKICAgICAgdGhpcy5mb3JtID0gcm93OwogICAgfSwKICAgIC8qKiDliKDpmaTmjInpkq7mk43kvZwgKi9oYW5kbGVEZWxldGU6IGZ1bmN0aW9uIGhhbmRsZURlbGV0ZShyb3cpIHsKICAgICAgdmFyIF90aGlzMyA9IHRoaXM7CiAgICAgIHZhciBvcGVySWRzID0gcm93Lm9wZXJJZCB8fCB0aGlzLmlkczsKICAgICAgdGhpcy4kY29uZmlybSgn5piv5ZCm56Gu6K6k5Yig6Zmk5pel5b+X57yW5Y+35Li6IicgKyBvcGVySWRzICsgJyLnmoTmlbDmja7pobk/JywgIuitpuWRiiIsIHsKICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogIuehruWumiIsCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogIuWPlua2iCIsCiAgICAgICAgdHlwZTogIndhcm5pbmciCiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgIHJldHVybiBkZWxPcGVybG9nKG9wZXJJZHMpOwogICAgICB9KS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICBfdGhpczMuZ2V0TGlzdCgpOwogICAgICAgIF90aGlzMy5tc2dTdWNjZXNzKCLliKDpmaTmiJDlip8iKTsKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOa4heepuuaMiemSruaTjeS9nCAqL2hhbmRsZUNsZWFuOiBmdW5jdGlvbiBoYW5kbGVDbGVhbigpIHsKICAgICAgdmFyIF90aGlzNCA9IHRoaXM7CiAgICAgIHRoaXMuJGNvbmZpcm0oJ+aYr+WQpuehruiupOa4heepuuaJgOacieaTjeS9nOaXpeW/l+aVsOaNrumhuT8nLCAi6K2m5ZGKIiwgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAi56Gu5a6aIiwKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAi5Y+W5raIIiwKICAgICAgICB0eXBlOiAid2FybmluZyIKICAgICAgfSkudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgcmV0dXJuIGNsZWFuT3BlcmxvZygpOwogICAgICB9KS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICBfdGhpczQuZ2V0TGlzdCgpOwogICAgICAgIF90aGlzNC5tc2dTdWNjZXNzKCLmuIXnqbrmiJDlip8iKTsKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOWvvOWHuuaMiemSruaTjeS9nCAqL2hhbmRsZUV4cG9ydDogZnVuY3Rpb24gaGFuZGxlRXhwb3J0KCkgewogICAgICB2YXIgX3RoaXM1ID0gdGhpczsKICAgICAgdmFyIHF1ZXJ5UGFyYW1zID0gdGhpcy5xdWVyeVBhcmFtczsKICAgICAgdGhpcy4kY29uZmlybSgn5piv5ZCm56Gu6K6k5a+85Ye65omA5pyJ5pON5L2c5pel5b+X5pWw5o2u6aG5PycsICLorablkYoiLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLAogICAgICAgIHR5cGU6ICJ3YXJuaW5nIgogICAgICB9KS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICByZXR1cm4gZXhwb3J0T3BlcmxvZyhxdWVyeVBhcmFtcyk7CiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgX3RoaXM1LmRvd25sb2FkKHJlc3BvbnNlLm1zZyk7CiAgICAgIH0pOwogICAgfQogIH0KfTs="}, {"version": 3, "names": ["list", "delOperlog", "cleanOperlog", "exportOperlog", "name", "data", "loading", "ids", "multiple", "showSearch", "total", "open", "typeOptions", "statusOptions", "date<PERSON><PERSON><PERSON>", "form", "queryParams", "pageNum", "pageSize", "title", "undefined", "operName", "businessType", "status", "created", "_this", "getList", "getDicts", "then", "response", "methods", "_this2", "addDateRange", "rows", "statusFormat", "row", "column", "selectDictLabel", "typeFormat", "handleQuery", "reset<PERSON><PERSON>y", "resetForm", "handleSelectionChange", "selection", "map", "item", "operId", "length", "handleView", "handleDelete", "_this3", "operIds", "$confirm", "confirmButtonText", "cancelButtonText", "type", "msgSuccess", "handleClean", "_this4", "handleExport", "_this5", "download", "msg"], "sources": ["src/views/monitor/operlog/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"系统模块\" prop=\"title\">\n        <el-input\n          v-model=\"queryParams.title\"\n          placeholder=\"请输入系统模块\"\n          clearable\n          style=\"width: 240px;\"\n          size=\"small\"\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"操作人员\" prop=\"operName\">\n        <el-input\n          v-model=\"queryParams.operName\"\n          placeholder=\"请输入操作人员\"\n          clearable\n          style=\"width: 240px;\"\n          size=\"small\"\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"类型\" prop=\"businessType\">\n        <el-select\n          v-model=\"queryParams.businessType\"\n          placeholder=\"操作类型\"\n          clearable\n          size=\"small\"\n          style=\"width: 240px\"\n        >\n          <el-option\n            v-for=\"dict in typeOptions\"\n            :key=\"dict.dictValue\"\n            :label=\"dict.dictLabel\"\n            :value=\"dict.dictValue\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"状态\" prop=\"status\">\n        <el-select\n          v-model=\"queryParams.status\"\n          placeholder=\"操作状态\"\n          clearable\n          size=\"small\"\n          style=\"width: 240px\"\n        >\n          <el-option\n            v-for=\"dict in statusOptions\"\n            :key=\"dict.dictValue\"\n            :label=\"dict.dictLabel\"\n            :value=\"dict.dictValue\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"操作时间\">\n        <el-date-picker\n          v-model=\"dateRange\"\n          size=\"small\"\n          style=\"width: 240px\"\n          value-format=\"yyyy-MM-dd\"\n          type=\"daterange\"\n          range-separator=\"-\"\n          start-placeholder=\"开始日期\"\n          end-placeholder=\"结束日期\"\n        ></el-date-picker>\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"danger\"\n          plain\n          icon=\"el-icon-delete\"\n          size=\"mini\"\n          :disabled=\"multiple\"\n          @click=\"handleDelete\"\n          v-hasPermi=\"['monitor:operlog:remove']\"\n        >删除</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"danger\"\n          plain\n          icon=\"el-icon-delete\"\n          size=\"mini\"\n          @click=\"handleClean\"\n          v-hasPermi=\"['monitor:operlog:remove']\"\n        >清空</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"warning\"\n          plain\n          icon=\"el-icon-download\"\n          size=\"mini\"\n          @click=\"handleExport\"\n          v-hasPermi=\"['system:config:export']\"\n        >导出</el-button>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <el-table v-loading=\"loading\" :data=\"list\" @selection-change=\"handleSelectionChange\">\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column label=\"日志编号\" align=\"center\" prop=\"operId\" />\n      <el-table-column label=\"系统模块\" align=\"center\" prop=\"title\" />\n      <el-table-column label=\"操作类型\" align=\"center\" prop=\"businessType\" :formatter=\"typeFormat\" />\n      <el-table-column label=\"请求方式\" align=\"center\" prop=\"requestMethod\" />\n      <el-table-column label=\"操作人员\" align=\"center\" prop=\"operName\" />\n      <el-table-column label=\"主机\" align=\"center\" prop=\"operIp\" width=\"130\" :show-overflow-tooltip=\"true\" />\n      <el-table-column label=\"操作地点\" align=\"center\" prop=\"operLocation\" :show-overflow-tooltip=\"true\" />\n      <el-table-column label=\"操作状态\" align=\"center\" prop=\"status\" :formatter=\"statusFormat\" />\n      <el-table-column label=\"操作日期\" align=\"center\" prop=\"operTime\" width=\"180\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.operTime) }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-view\"\n            @click=\"handleView(scope.row,scope.index)\"\n            v-hasPermi=\"['monitor:operlog:query']\"\n          >详细</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 操作日志详细 -->\n    <el-dialog title=\"操作日志详细\" :visible.sync=\"open\" width=\"700px\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" label-width=\"100px\" size=\"mini\">\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"操作模块：\">{{ form.title }} / {{ typeFormat(form) }}</el-form-item>\n            <el-form-item\n              label=\"登录信息：\"\n            >{{ form.operName }} / {{ form.operIp }} / {{ form.operLocation }}</el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"请求地址：\">{{ form.operUrl }}</el-form-item>\n            <el-form-item label=\"请求方式：\">{{ form.requestMethod }}</el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"操作方法：\">{{ form.method }}</el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"请求参数：\">{{ form.operParam }}</el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"返回参数：\">{{ form.jsonResult }}</el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"操作状态：\">\n              <div v-if=\"form.status === 0\">正常</div>\n              <div v-else-if=\"form.status === 1\">失败</div>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"操作时间：\">{{ parseTime(form.operTime) }}</el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"异常信息：\" v-if=\"form.status === 1\">{{ form.errorMsg }}</el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"open = false\">关 闭</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { list, delOperlog, cleanOperlog, exportOperlog } from \"@/api/monitor/operlog\";\n\nexport default {\n  name: \"Operlog\",\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 表格数据\n      list: [],\n      // 是否显示弹出层\n      open: false,\n      // 类型数据字典\n      typeOptions: [],\n      // 类型数据字典\n      statusOptions: [],\n      // 日期范围\n      dateRange: [],\n      // 表单参数\n      form: {},\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        title: undefined,\n        operName: undefined,\n        businessType: undefined,\n        status: undefined\n      }\n    };\n  },\n  created() {\n    this.getList();\n    this.getDicts(\"sys_oper_type\").then(response => {\n      this.typeOptions = response.data;\n    });\n    this.getDicts(\"sys_common_status\").then(response => {\n      this.statusOptions = response.data;\n    });\n  },\n  methods: {\n    /** 查询登录日志 */\n    getList() {\n      this.loading = true;\n      list(this.addDateRange(this.queryParams, this.dateRange)).then( response => {\n          this.list = response.rows;\n          this.total = response.total;\n          this.loading = false;\n        }\n      );\n    },\n    // 操作日志状态字典翻译\n    statusFormat(row, column) {\n      return this.selectDictLabel(this.statusOptions, row.status);\n    },\n    // 操作日志类型字典翻译\n    typeFormat(row, column) {\n      return this.selectDictLabel(this.typeOptions, row.businessType);\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.dateRange = [];\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.operId)\n      this.multiple = !selection.length\n    },\n    /** 详细按钮操作 */\n    handleView(row) {\n      this.open = true;\n      this.form = row;\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const operIds = row.operId || this.ids;\n      this.$confirm('是否确认删除日志编号为\"' + operIds + '\"的数据项?', \"警告\", {\n          confirmButtonText: \"确定\",\n          cancelButtonText: \"取消\",\n          type: \"warning\"\n        }).then(function() {\n          return delOperlog(operIds);\n        }).then(() => {\n          this.getList();\n          this.msgSuccess(\"删除成功\");\n        })\n    },\n    /** 清空按钮操作 */\n    handleClean() {\n        this.$confirm('是否确认清空所有操作日志数据项?', \"警告\", {\n          confirmButtonText: \"确定\",\n          cancelButtonText: \"取消\",\n          type: \"warning\"\n        }).then(function() {\n          return cleanOperlog();\n        }).then(() => {\n          this.getList();\n          this.msgSuccess(\"清空成功\");\n        })\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      const queryParams = this.queryParams;\n      this.$confirm('是否确认导出所有操作日志数据项?', \"警告\", {\n          confirmButtonText: \"确定\",\n          cancelButtonText: \"取消\",\n          type: \"warning\"\n        }).then(function() {\n          return exportOperlog(queryParams);\n        }).then(response => {\n          this.download(response.msg);\n        })\n    }\n  }\n};\n</script>\n\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6LA,SAAAA,IAAA,EAAAC,UAAA,EAAAC,YAAA,EAAAC,aAAA;AAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAV,IAAA;MACA;MACAW,IAAA;MACA;MACAC,WAAA;MACA;MACAC,aAAA;MACA;MACAC,SAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,KAAA,EAAAC,SAAA;QACAC,QAAA,EAAAD,SAAA;QACAE,YAAA,EAAAF,SAAA;QACAG,MAAA,EAAAH;MACA;IACA;EACA;EACAI,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA,KAAAC,OAAA;IACA,KAAAC,QAAA,kBAAAC,IAAA,WAAAC,QAAA;MACAJ,KAAA,CAAAb,WAAA,GAAAiB,QAAA,CAAAxB,IAAA;IACA;IACA,KAAAsB,QAAA,sBAAAC,IAAA,WAAAC,QAAA;MACAJ,KAAA,CAAAZ,aAAA,GAAAgB,QAAA,CAAAxB,IAAA;IACA;EACA;EACAyB,OAAA;IACA,aACAJ,OAAA,WAAAA,QAAA;MAAA,IAAAK,MAAA;MACA,KAAAzB,OAAA;MACAN,IAAA,MAAAgC,YAAA,MAAAhB,WAAA,OAAAF,SAAA,GAAAc,IAAA,WAAAC,QAAA;QACAE,MAAA,CAAA/B,IAAA,GAAA6B,QAAA,CAAAI,IAAA;QACAF,MAAA,CAAArB,KAAA,GAAAmB,QAAA,CAAAnB,KAAA;QACAqB,MAAA,CAAAzB,OAAA;MACA,CACA;IACA;IACA;IACA4B,YAAA,WAAAA,aAAAC,GAAA,EAAAC,MAAA;MACA,YAAAC,eAAA,MAAAxB,aAAA,EAAAsB,GAAA,CAAAZ,MAAA;IACA;IACA;IACAe,UAAA,WAAAA,WAAAH,GAAA,EAAAC,MAAA;MACA,YAAAC,eAAA,MAAAzB,WAAA,EAAAuB,GAAA,CAAAb,YAAA;IACA;IACA,aACAiB,WAAA,WAAAA,YAAA;MACA,KAAAvB,WAAA,CAAAC,OAAA;MACA,KAAAS,OAAA;IACA;IACA,aACAc,UAAA,WAAAA,WAAA;MACA,KAAA1B,SAAA;MACA,KAAA2B,SAAA;MACA,KAAAF,WAAA;IACA;IACA;IACAG,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAApC,GAAA,GAAAoC,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,MAAA;MAAA;MACA,KAAAtC,QAAA,IAAAmC,SAAA,CAAAI,MAAA;IACA;IACA,aACAC,UAAA,WAAAA,WAAAb,GAAA;MACA,KAAAxB,IAAA;MACA,KAAAI,IAAA,GAAAoB,GAAA;IACA;IACA,aACAc,YAAA,WAAAA,aAAAd,GAAA;MAAA,IAAAe,MAAA;MACA,IAAAC,OAAA,GAAAhB,GAAA,CAAAW,MAAA,SAAAvC,GAAA;MACA,KAAA6C,QAAA,kBAAAD,OAAA;QACAE,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAA3B,IAAA;QACA,OAAA3B,UAAA,CAAAkD,OAAA;MACA,GAAAvB,IAAA;QACAsB,MAAA,CAAAxB,OAAA;QACAwB,MAAA,CAAAM,UAAA;MACA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,KAAAN,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAA3B,IAAA;QACA,OAAA1B,YAAA;MACA,GAAA0B,IAAA;QACA8B,MAAA,CAAAhC,OAAA;QACAgC,MAAA,CAAAF,UAAA;MACA;IACA;IACA,aACAG,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,IAAA5C,WAAA,QAAAA,WAAA;MACA,KAAAoC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAA3B,IAAA;QACA,OAAAzB,aAAA,CAAAa,WAAA;MACA,GAAAY,IAAA,WAAAC,QAAA;QACA+B,MAAA,CAAAC,QAAA,CAAAhC,QAAA,CAAAiC,GAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}