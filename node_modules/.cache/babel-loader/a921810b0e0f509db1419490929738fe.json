{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/utils/generator/config.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/utils/generator/config.js", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js", "mtime": 1751171659043}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}