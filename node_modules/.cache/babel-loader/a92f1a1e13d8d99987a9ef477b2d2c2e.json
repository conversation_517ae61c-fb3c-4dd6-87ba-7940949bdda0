{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/project/report/form.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/project/report/form.vue", "mtime": 1752653873866}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js", "mtime": 1751171659043}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldFJlcG9ydCwgYWRkUmVwb3J0LCB1cGRhdGVSZXBvcnQsIGNoZWNrTmFtZVVuaXF1ZSB9IGZyb20gIkAvYXBpL3Byb2plY3QvcmVwb3J0IjsKaW1wb3J0IEZpbGVVcGxvYWQgZnJvbSAiQC9jb21wb25lbnRzL0ZpbGVVcGxvYWQiOwppbXBvcnQgZmxvd2FibGUgZnJvbSAnQC92aWV3cy9mbG93YWJsZS90YXNrL3JlY29yZC9pbmRleCc7CmltcG9ydCB7IHJlZ2lvbkRhdGEsIENvZGVUb1RleHQsIFRleHRUb0NvZGUgfSBmcm9tICJlbGVtZW50LWNoaW5hLWFyZWEtZGF0YSI7CmltcG9ydCBwcmludCBmcm9tICJwcmludC1qcyI7CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiUmVwb3J0IiwKICBjb21wb25lbnRzOiB7CiAgICBmbG93YWJsZTogZmxvd2FibGUsCiAgICBGaWxlVXBsb2FkOiBGaWxlVXBsb2FkLAogICAgcHJpbnQ6IHByaW50CiAgfSwKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgdmFyIHRoYXQgPSB0aGlzOwogICAgdmFyIGluZm9UeXBlVmFsdWVWYWxpID0gZnVuY3Rpb24gaW5mb1R5cGVWYWx1ZVZhbGkocnVsZSwgdmFsdWUsIGNhbGxiYWNrKSB7CiAgICAgIGlmICh0aGF0LmZvcm0uaW5mb1R5cGUuaW5kZXhPZignMScpID49IDAgJiYgIXRoYXQuZm9ybS5zY2FuRmlsZSkgewogICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcigi6YKu566x5Zyw5Z2A5b+F5aGrIikpOwogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICBjYWxsYmFjaygpOwogICAgfTsKICAgIHZhciBpbmZvVHlwZVZhbHVlVmFsaTIgPSBmdW5jdGlvbiBpbmZvVHlwZVZhbHVlVmFsaTIocnVsZSwgdmFsdWUsIGNhbGxiYWNrKSB7CiAgICAgIGlmICh0aGF0LmZvcm0uaW5mb1R5cGUuaW5kZXhPZignMicpID49IDAgJiYgIXRoYXQuZm9ybS5zZW5kQWRkcmVzcykgewogICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcigi5pS25Lu25Zyw5Z2A5b+F5aGrIikpOwogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICBjYWxsYmFjaygpOwogICAgfTsKICAgIHZhciBuYW1lVmFsaSA9IGZ1bmN0aW9uIG5hbWVWYWxpKHJ1bGUsIHZhbHVlLCBjYWxsYmFjaykgewogICAgICBpZiAoIXRoYXQuZm9ybS5wcm9qZWN0TmFtZSkgewogICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcigi6aG555uu5ZCN56ew5b+F5aGrIikpOwogICAgICB9IGVsc2UgewogICAgICAgIGlmICgvXHMrL2cudGVzdCh0aGF0LmZvcm0ucHJvamVjdE5hbWUpKSB7CiAgICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoIumhueebruWQjeensOS4jeinhOiMgyIpKTsKICAgICAgICAgIHJldHVybjsKICAgICAgICB9CiAgICAgICAgY2hlY2tOYW1lVW5pcXVlKHsKICAgICAgICAgIHByb2plY3ROYW1lOiB0aGF0LmZvcm0ucHJvamVjdE5hbWUsCiAgICAgICAgICBwcm9qZWN0SWQ6IHRoYXQuZm9ybS5wcm9qZWN0SWQKICAgICAgICB9KS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEgPT0gMCkgewogICAgICAgICAgICBjYWxsYmFjaygpOwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCLpobnnm67lkI3np7Dlt7LlrZjlnKgiKSk7CiAgICAgICAgICB9CiAgICAgICAgfSk7CiAgICAgIH0KICAgIH07CiAgICB2YXIgY29kZVZhbGkgPSBmdW5jdGlvbiBjb2RlVmFsaShydWxlLCB2YWx1ZSwgY2FsbGJhY2spIHsKICAgICAgaWYgKCF0aGF0LmZvcm0ucHJvamVjdE5vKSB7CiAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCLpobnnm67nvJblj7flv4XloasiKSk7CiAgICAgIH0gZWxzZSBpZiAoL1xzKy9nLnRlc3QodGhhdC5mb3JtLnByb2plY3RObykpIHsKICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoIumhueebrue8luWPt+S4jeinhOiMgyIpKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgY2FsbGJhY2soKTsKICAgIH07CiAgICByZXR1cm4gewogICAgICAvLyDpga7nvanlsYIKICAgICAgbG9hZGluZzogdHJ1ZSwKICAgICAgLy8g5pON5L2c57G75Z6L5a2X5YW4CiAgICAgIG9wZXJhdGlvblR5cGVPcHRpb25zOiBbXSwKICAgICAgLy8g5a6h5qC454q25oCB5a2X5YW4CiAgICAgIGF1ZGl0U3RhdHVzT3B0aW9uczogW10sCiAgICAgIC8vIOe8lui+keeKtuaAgeWtl+WFuAogICAgICBlZGl0U3RhdHVzT3B0aW9uczogW10sCiAgICAgIC8vIOaLm+agh+aWueW8j+Wtl+WFuAogICAgICBiaWRkaW5nVHlwZU9wdGlvbnM6IFtdLAogICAgICAvLyDmipXmoIfkuqflk4Hlnovlj7flrZflhbgKICAgICAgbW9kZWxPcHRpb25zOiBbXSwKICAgICAgbW9kZWxPcHRpb24xOiBbXSwKICAgICAgLy8g5omA6ZyA6LWE5paZ5a2X5YW4CiAgICAgIHJlcXVpcmVJbmZvT3B0aW9uczogW10sCiAgICAgIHJlcXVpcmVJbmZvT3B0aW9uMTogW10sCiAgICAgIC8vIOi1hOaWmeexu+Wei+Wtl+WFuAogICAgICBpbmZvVHlwZU9wdGlvbnM6IFtdLAogICAgICAvLyDmiYDlsZ7nnIHku73lrZflhbgKICAgICAgYmVsb25nUHJvdmluY2VPcHRpb25zOiBbXSwKICAgICAgYmVsb25nUHJvdmluY2VPcHRpb25zMTogW10sCiAgICAgIC8vIOWUruWQjuW5tOmZkAogICAgICBhZnRlclNhbGVZZWFyT3B0aW9uczogW10sCiAgICAgIGFmdGVyU2FsZVllYXJPcHRpb25zMTogW10sCiAgICAgIHNwZWNPcHRpb25zOiBbXSwKICAgICAgc3BlY09wdGlvbjE6IFtdLAogICAgICAvLyDooajljZXlj4LmlbAKICAgICAgZm9ybToge30sCiAgICAgIC8vIOihqOWNleagoemqjAogICAgICAvLyDooajljZXmoKHpqowKICAgICAgcnVsZXM6IHsKICAgICAgICBvcGVyYXRpb25UeXBlOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAi5pON5L2c57G75Z6L5b+F6YCJIgogICAgICAgIH1dLAogICAgICAgIHByb2plY3RObzogW3sKICAgICAgICAgIHZhbGlkYXRvcjogY29kZVZhbGksCiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgIH1dLAogICAgICAgIHByb2plY3ROYW1lOiBbewogICAgICAgICAgdmFsaWRhdG9yOiBuYW1lVmFsaSwKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgfV0sCiAgICAgICAgYWRkcmVzczogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogIuivpue7huWcsOWdgOS4jeiDveS4uuepuiIsCiAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIKICAgICAgICB9XSwKICAgICAgICBiaWRkaW5nQ29tcGFueTogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogIuaLm+agh+WNleS9jeS4jeiDveS4uuepuiIsCiAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIKICAgICAgICB9XSwKICAgICAgICBvcGVuRGF0ZTogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogIuW8gOagh+aXpeacn+S4jeiDveS4uuepuiIsCiAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIKICAgICAgICB9XSwKICAgICAgICBhZnRlclNhbGVZZWFyOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAi5ZSu5ZCO5bm06ZmQ5LiN6IO95Li656m6IiwKICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgIH1dLAogICAgICAgIGhhbmdEYXRlOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAi5oyC572R5pel5pyf5LiN6IO95Li656m6IiwKICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgIH1dLAogICAgICAgIGJlbG9uZ1Byb3ZpbmNlOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAi5ZSu5ZCO5bm06ZmQ5LiN6IO95Li656m6IiwKICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgIH1dLAogICAgICAgIGRpc3RyaWJ1dG9yOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAi5omA5bGe57uP6ZSA5ZWG5LiN6IO95Li656m6IiwKICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgIH1dLAogICAgICAgIHNjYW5GaWxlOiBbewogICAgICAgICAgdmFsaWRhdG9yOiBpbmZvVHlwZVZhbHVlVmFsaSwKICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgIH1dLAogICAgICAgIHNlbmRBZGRyZXNzOiBbewogICAgICAgICAgdmFsaWRhdG9yOiBpbmZvVHlwZVZhbHVlVmFsaTIsCiAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIKICAgICAgICB9XSwKICAgICAgICBtb2RlbDogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogIuaKleagh+S6p+WTgeWei+WPt+W/hemAiSIKICAgICAgICB9XSwKICAgICAgICBzcGVjOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAi5oqV5qCH5Lqn5ZOB6KeE5qC85b+F6YCJIgogICAgICAgIH1dLAogICAgICAgIHByb3ZpbmNlOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAi6aG555uu5omA5Zyo5Zyw5b+F6YCJIgogICAgICAgIH1dLAogICAgICAgIGluZm9UeXBlOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAi6LWE5paZ5o6l5pS25pa55byP5b+F6YCJIiwKICAgICAgICAgIHRyaWdnZXI6ICJjaGFuZ2UiCiAgICAgICAgfV0sCiAgICAgICAgYmlkZGluZ0NvbnRhY3Q6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICLmi5vmoIfljZXkvY3ogZTns7vkurov6IGU57O755S16K+d5b+F5aGrIgogICAgICAgIH1dLAogICAgICAgIGF1dGhDb250YWN0OiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAi5o6I5p2D5YWs5Y+46IGU57O75Lq6L+iBlOezu+eUteivneW/heWhqyIKICAgICAgICB9XQogICAgICB9LAogICAgICBvcHRpb25zOiByZWdpb25EYXRhLAogICAgICBzZWxlY3RlZE9wdGlvbnM6IFtdLAogICAgICBxdWVyeUFyZWE6IFtdLAogICAgICAvL+W3peS9nOa1geWPguaVsAogICAgICBmaW5pc2hlZDogJ2ZhbHNlJywKICAgICAgdGFza0lkOiB1bmRlZmluZWQsCiAgICAgIHByb2NJbnNJZDogdW5kZWZpbmVkLAogICAgICBidXNpbmVzc0tleTogdW5kZWZpbmVkLAogICAgICBhdWRpdDogZmFsc2UsCiAgICAgIGZvcm1FZGl0OiBmYWxzZSwKICAgICAgLy/lt6XkvZzmtYHlj4LmlbBlbmQKICAgICAgYXV0aENvbXBhbnlzOiBbXQogICAgfTsKICB9LAogIGFjdGl2YXRlZDogZnVuY3Rpb24gYWN0aXZhdGVkKCkgewogICAgdGhpcy5yZXNldCgpOwogICAgLy90aGlzLmZvcm0ucHJvamVjdE5vID0gbW9tZW50KG5ldyBEYXRlKCkpLmZvcm1hdCgnWVlZWU1NRERISG1tJyk7CiAgICB0aGlzLnRhc2tJZCA9IHRoaXMuJHJvdXRlLnF1ZXJ5ICYmIHRoaXMuJHJvdXRlLnF1ZXJ5LnRhc2tJZDsKICAgIHRoaXMucHJvY0luc0lkID0gdGhpcy4kcm91dGUucXVlcnkgJiYgdGhpcy4kcm91dGUucXVlcnkucHJvY0luc0lkOwogICAgdGhpcy5maW5pc2hlZCA9IHRoaXMuJHJvdXRlLnF1ZXJ5ICYmIHRoaXMuJHJvdXRlLnF1ZXJ5LmZpbmlzaGVkOwogICAgdGhpcy5idXNpbmVzc0tleSA9IHRoaXMuJHJvdXRlLnF1ZXJ5ICYmIHRoaXMuJHJvdXRlLnF1ZXJ5LmJ1c2luZXNzS2V5OwogICAgdmFyIGVkaXQgPSB0aGlzLiRyb3V0ZS5xdWVyeSAmJiB0aGlzLiRyb3V0ZS5xdWVyeS5mb3JtRWRpdDsKICAgIGlmIChlZGl0ID09ICJ0cnVlIikgewogICAgICB0aGlzLmZvcm1FZGl0ID0gdHJ1ZTsKICAgIH0gZWxzZSB7CiAgICAgIHRoaXMuZm9ybUVkaXQgPSBmYWxzZTsKICAgIH0KICAgIGlmICh0aGlzLmJ1c2luZXNzS2V5KSB7CiAgICAgIGlmICh0aGlzLmZpbmlzaGVkID09ICJmYWxzZSIgJiYgIXRoaXMuZm9ybUVkaXQpIHsKICAgICAgICB0aGlzLmF1ZGl0ID0gdHJ1ZTsKICAgICAgfQogICAgICB0aGlzLmdldFJlcG9ydEluZm8odGhpcy5idXNpbmVzc0tleSk7CiAgICB9CiAgICBjb25zb2xlLmxvZygiPT09PT09PT1wcm9qZWN0PT09PT09PT09PmFjdGl2YXRlZD5mb3JtRWRpdD4+IiArIHRoaXMuZm9ybUVkaXQpOwogIH0sCiAgY3JlYXRlZDogZnVuY3Rpb24gY3JlYXRlZCgpIHsKICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICB0aGlzLnJlc2V0KCk7CiAgICAvL3RoaXMuZm9ybS5wcm9qZWN0Tm8gPSBtb21lbnQobmV3IERhdGUoKSkuZm9ybWF0KCdZWVlZTU1EREhIbW0nKTsKICAgIHRoaXMudGFza0lkID0gdGhpcy4kcm91dGUucXVlcnkgJiYgdGhpcy4kcm91dGUucXVlcnkudGFza0lkOwogICAgdGhpcy5wcm9jSW5zSWQgPSB0aGlzLiRyb3V0ZS5xdWVyeSAmJiB0aGlzLiRyb3V0ZS5xdWVyeS5wcm9jSW5zSWQ7CiAgICB0aGlzLmZpbmlzaGVkID0gdGhpcy4kcm91dGUucXVlcnkgJiYgdGhpcy4kcm91dGUucXVlcnkuZmluaXNoZWQ7CiAgICB0aGlzLmJ1c2luZXNzS2V5ID0gdGhpcy4kcm91dGUucXVlcnkgJiYgdGhpcy4kcm91dGUucXVlcnkuYnVzaW5lc3NLZXk7CiAgICB2YXIgZWRpdCA9IHRoaXMuJHJvdXRlLnF1ZXJ5ICYmIHRoaXMuJHJvdXRlLnF1ZXJ5LmZvcm1FZGl0OwogICAgaWYgKGVkaXQgPT0gInRydWUiKSB7CiAgICAgIHRoaXMuZm9ybUVkaXQgPSB0cnVlOwogICAgfSBlbHNlIHsKICAgICAgdGhpcy5mb3JtRWRpdCA9IGZhbHNlOwogICAgfQogICAgaWYgKHRoaXMuYnVzaW5lc3NLZXkpIHsKICAgICAgaWYgKHRoaXMuZmluaXNoZWQgPT0gImZhbHNlIiAmJiAhdGhpcy5mb3JtRWRpdCkgewogICAgICAgIHRoaXMuYXVkaXQgPSB0cnVlOwogICAgICB9CiAgICAgIHRoaXMuZ2V0UmVwb3J0SW5mbyh0aGlzLmJ1c2luZXNzS2V5KTsKICAgIH0KICAgIC8vIHRoaXMuYXVkaXQgPSB0cnVlOwogICAgY29uc29sZS5sb2coIj09PT09PT09PXByb2plY3Q9PT09PT09PT5jcmVhdGVkPj5mb3JtRWRpdD4iICsgdGhpcy5mb3JtRWRpdCk7CiAgICB0aGlzLmdldERpY3RzKCJwcl9vcGVyYXRpb25fdHlwZSIpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgIF90aGlzLm9wZXJhdGlvblR5cGVPcHRpb25zID0gcmVzcG9uc2UuZGF0YTsKICAgIH0pOwogICAgdGhpcy5nZXREaWN0cygicHJfYXVkaXRfc3RhdHVzIikudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgX3RoaXMuYXVkaXRTdGF0dXNPcHRpb25zID0gcmVzcG9uc2UuZGF0YTsKICAgIH0pOwogICAgdGhpcy5nZXREaWN0cygicHJfZWRpdF9zdGF0dXMiKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICBfdGhpcy5lZGl0U3RhdHVzT3B0aW9ucyA9IHJlc3BvbnNlLmRhdGE7CiAgICB9KTsKICAgIC8vIHRoaXMuZ2V0RGljdHMoInByX2JpZGRpbmdfdHlwZSIpLnRoZW4oKHJlc3BvbnNlKSA9PiB7CiAgICAvLyAgIHRoaXMuYmlkZGluZ1R5cGVPcHRpb25zID0gcmVzcG9uc2UuZGF0YTsKICAgIC8vIH0pOwogICAgdGhpcy5nZXREaWN0cygicHJfbW9kZWwiKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICBfdGhpcy5tb2RlbE9wdGlvbjEgPSByZXNwb25zZS5kYXRhOwogICAgICB2YXIgb3B0ID0gW107CiAgICAgIHJlc3BvbnNlLmRhdGEuZm9yRWFjaChmdW5jdGlvbiAoZWxlbSwgaW5kZXgpIHsKICAgICAgICB2YXIgb2JqID0ge307CiAgICAgICAgb2JqLmxhYmVsID0gZWxlbS5kaWN0TGFiZWw7CiAgICAgICAgb2JqLnZhbHVlID0gZWxlbS5kaWN0VmFsdWU7CiAgICAgICAgb3B0LnB1c2gob2JqKTsKICAgICAgfSk7CiAgICAgIF90aGlzLm1vZGVsT3B0aW9ucyA9IG9wdDsKICAgIH0pOwogICAgdGhpcy5nZXREaWN0cygicHJfc3BlYyIpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgIF90aGlzLnNwZWNPcHRpb24xID0gcmVzcG9uc2UuZGF0YTsKICAgICAgdmFyIG9wdCA9IFtdOwogICAgICByZXNwb25zZS5kYXRhLmZvckVhY2goZnVuY3Rpb24gKGVsZW0sIGluZGV4KSB7CiAgICAgICAgdmFyIG9iaiA9IHt9OwogICAgICAgIG9iai5sYWJlbCA9IGVsZW0uZGljdExhYmVsOwogICAgICAgIG9iai52YWx1ZSA9IGVsZW0uZGljdFZhbHVlOwogICAgICAgIG9wdC5wdXNoKG9iaik7CiAgICAgIH0pOwogICAgICBfdGhpcy5zcGVjT3B0aW9ucyA9IG9wdDsKICAgIH0pOwogICAgdGhpcy5nZXREaWN0cygicHJfaW5mbyIpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgIF90aGlzLnJlcXVpcmVJbmZvT3B0aW9uMSA9IHJlc3BvbnNlLmRhdGE7CiAgICAgIHZhciBvcHQgPSBbXTsKICAgICAgcmVzcG9uc2UuZGF0YS5mb3JFYWNoKGZ1bmN0aW9uIChlbGVtLCBpbmRleCkgewogICAgICAgIHZhciBvYmogPSB7fTsKICAgICAgICBvYmoubGFiZWwgPSBlbGVtLmRpY3RMYWJlbDsKICAgICAgICBvYmoudmFsdWUgPSBlbGVtLmRpY3RWYWx1ZTsKICAgICAgICBvcHQucHVzaChvYmopOwogICAgICB9KTsKICAgICAgX3RoaXMucmVxdWlyZUluZm9PcHRpb25zID0gb3B0OwogICAgfSk7CiAgICB0aGlzLmdldERpY3RzKCJwcl9wcm92aW5jZSIpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgIF90aGlzLmJlbG9uZ1Byb3ZpbmNlT3B0aW9uczEgPSByZXNwb25zZS5kYXRhOwogICAgICB2YXIgb3B0ID0gW107CiAgICAgIHJlc3BvbnNlLmRhdGEuZm9yRWFjaChmdW5jdGlvbiAoZWxlbSwgaW5kZXgpIHsKICAgICAgICB2YXIgb2JqID0ge307CiAgICAgICAgb2JqLmxhYmVsID0gZWxlbS5kaWN0TGFiZWw7CiAgICAgICAgb2JqLnZhbHVlID0gZWxlbS5kaWN0VmFsdWU7CiAgICAgICAgb3B0LnB1c2gob2JqKTsKICAgICAgfSk7CiAgICAgIF90aGlzLmJlbG9uZ1Byb3ZpbmNlT3B0aW9ucyA9IG9wdDsKICAgIH0pOwogICAgdGhpcy5nZXREaWN0cygicHJfYWZ0ZXJfc2FsZV95ZWFyIikudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgX3RoaXMuYWZ0ZXJTYWxlWWVhck9wdGlvbnMxID0gcmVzcG9uc2UuZGF0YTsKICAgICAgdmFyIG9wdCA9IFtdOwogICAgICByZXNwb25zZS5kYXRhLmZvckVhY2goZnVuY3Rpb24gKGVsZW0sIGluZGV4KSB7CiAgICAgICAgdmFyIG9iaiA9IHt9OwogICAgICAgIG9iai5sYWJlbCA9IGVsZW0uZGljdExhYmVsOwogICAgICAgIG9iai52YWx1ZSA9IGVsZW0uZGljdFZhbHVlOwogICAgICAgIG9wdC5wdXNoKG9iaik7CiAgICAgIH0pOwogICAgICBfdGhpcy5hZnRlclNhbGVZZWFyT3B0aW9ucyA9IG9wdDsKICAgIH0pOwogICAgdGhpcy5nZXREaWN0cygicHJfZGF0YV90eXBlIikudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgX3RoaXMuaW5mb1R5cGVPcHRpb25zID0gcmVzcG9uc2UuZGF0YTsKICAgIH0pOwogICAgLy/pu5jorqTmiqXlpIcKICAgIHRoaXMuZm9ybS5vcGVyYXRpb25UeXBlID0gJzEnOwogIH0sCiAgbWV0aG9kczogewogICAgLy8g6KGo5Y2V6YeN572uCiAgICByZXNldDogZnVuY3Rpb24gcmVzZXQoKSB7CiAgICAgIHRoaXMuZm9ybSA9IHsKICAgICAgICBwcm9qZWN0SWQ6IG51bGwsCiAgICAgICAgcHJvamVjdE5vOiBudWxsLAogICAgICAgIHByb2plY3ROYW1lOiBudWxsLAogICAgICAgIG9wZXJhdGlvblR5cGU6IDEsCiAgICAgICAgYXVkaXRTdGF0dXM6ICIxIiwKICAgICAgICByZWplY3RSZWFzb246IG51bGwsCiAgICAgICAgcHJvdmluY2U6IG51bGwsCiAgICAgICAgY2l0eTogbnVsbCwKICAgICAgICBkaXN0cmljdDogbnVsbCwKICAgICAgICBhZGRyZXNzOiBudWxsLAogICAgICAgIGVkaXRTdGF0dXM6ICIwIiwKICAgICAgICBiZWxvbmdVc2VyOiBudWxsLAogICAgICAgIGJpZGRpbmdDb21wYW55OiBudWxsLAogICAgICAgIG9wZW5EYXRlOiBudWxsLAogICAgICAgIGJlbG9uZ1Byb3ZpbmNlOiBudWxsLAogICAgICAgIGFmdGVyU2FsZVllYXI6IG51bGwsCiAgICAgICAgaGFuZ0RhdGU6IG51bGwsCiAgICAgICAgYmlkZGluZ1R5cGU6IG51bGwsCiAgICAgICAgYnVkZ2V0TW9uZXk6IG51bGwsCiAgICAgICAgYXV0aENvbXBhbnk6IG51bGwsCiAgICAgICAgYmlkZGluZ05ldDogbnVsbCwKICAgICAgICBkaXN0cmlidXRvcjogbnVsbCwKICAgICAgICBtb2RlbDogW10sCiAgICAgICAgc3BlYzogW10sCiAgICAgICAgYXJlYTogbnVsbCwKICAgICAgICBhdXRoRmlsZTogbnVsbCwKICAgICAgICBhZnRlclNhbGVGaWxlOiBudWxsLAogICAgICAgIHJlcXVpcmVJbmZvOiBbXSwKICAgICAgICBpbmZvVHlwZTogW10sCiAgICAgICAgc2NhbkZpbGU6IG51bGwsCiAgICAgICAgc2VuZEFkZHJlc3M6IG51bGwsCiAgICAgICAgbWFpbEluZm86IG51bGwsCiAgICAgICAgZXhwcmVzc0luZm86IG51bGwsCiAgICAgICAgcmVtYXJrOiBudWxsLAogICAgICAgIHNwYXJlMTogbnVsbCwKICAgICAgICBzcGFyZTI6IG51bGwKICAgICAgfTsKICAgICAgdGhpcy5yZXNldEZvcm0oImZvcm0iKTsKICAgIH0sCiAgICAvLyDlpJrpgInmoYbpgInkuK3mlbDmja4KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZTogZnVuY3Rpb24gaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgewogICAgICB0aGlzLmlkcyA9IHNlbGVjdGlvbi5tYXAoZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICByZXR1cm4gaXRlbS5wcm9qZWN0SWQ7CiAgICAgIH0pOwogICAgICB0aGlzLnNpbmdsZSA9IHNlbGVjdGlvbi5sZW5ndGggIT09IDE7CiAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aDsKICAgICAgY29uc29sZS5pbmZvKHNlbGVjdGlvbik7CiAgICB9LAogICAgLyoqIOS/ruaUueaMiemSruaTjeS9nCAqL2dldFJlcG9ydEluZm86IGZ1bmN0aW9uIGdldFJlcG9ydEluZm8ocHJvamVjdElkKSB7CiAgICAgIHZhciBfdGhpczIgPSB0aGlzOwogICAgICBnZXRSZXBvcnQocHJvamVjdElkKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgIF90aGlzMi5mb3JtID0gcmVzcG9uc2UuZGF0YTsKICAgICAgICBpZiAoX3RoaXMyLmZvcm0ubW9kZWwpIF90aGlzMi5mb3JtLm1vZGVsID0gX3RoaXMyLmZvcm0ubW9kZWwuc3BsaXQoIiwiKTtlbHNlIF90aGlzMi5mb3JtLm1vZGVsID0gW107CiAgICAgICAgaWYgKF90aGlzMi5mb3JtLnJlcXVpcmVJbmZvKSBfdGhpczIuZm9ybS5yZXF1aXJlSW5mbyA9IF90aGlzMi5mb3JtLnJlcXVpcmVJbmZvLnNwbGl0KCIsIik7ZWxzZSBfdGhpczIuZm9ybS5yZXF1aXJlSW5mbyA9IFtdOwogICAgICAgIGlmIChfdGhpczIuZm9ybS5pbmZvVHlwZSkgX3RoaXMyLmZvcm0uaW5mb1R5cGUgPSBfdGhpczIuZm9ybS5pbmZvVHlwZS5zcGxpdCgiLCIpO2Vsc2UgX3RoaXMyLmZvcm0uaW5mb1R5cGUgPSBbXTsKICAgICAgICBpZiAoX3RoaXMyLmZvcm0uc3BlYykgX3RoaXMyLmZvcm0uc3BlYyA9IF90aGlzMi5mb3JtLnNwZWMuc3BsaXQoIiwiKTtlbHNlIF90aGlzMi5mb3JtLnNwZWMgPSBbXTsKICAgICAgICB2YXIgcHJvdmluY2VzID0gcmVzcG9uc2UuZGF0YS5wcm92aW5jZTsKICAgICAgICBpZiAocHJvdmluY2VzLmxlbmd0aCA+IDApIHsKICAgICAgICAgIHZhciBhZGRyZXNzID0gcHJvdmluY2VzLnNwbGl0KCIvIik7CiAgICAgICAgICB2YXIgY2l0eXMgPSBbXTsKICAgICAgICAgIC8vIOecgeS7vQogICAgICAgICAgaWYgKGFkZHJlc3MubGVuZ3RoID4gMCkgY2l0eXMucHVzaChUZXh0VG9Db2RlW2FkZHJlc3NbMF1dLmNvZGUpOwogICAgICAgICAgLy8g5Z+O5biCCiAgICAgICAgICBpZiAoYWRkcmVzcy5sZW5ndGggPiAxKSBjaXR5cy5wdXNoKFRleHRUb0NvZGVbYWRkcmVzc1swXV1bYWRkcmVzc1sxXV0uY29kZSk7CiAgICAgICAgICAvLyDlnLDljLoKICAgICAgICAgIGlmIChhZGRyZXNzLmxlbmd0aCA+IDIpIGNpdHlzLnB1c2goVGV4dFRvQ29kZVthZGRyZXNzWzBdXVthZGRyZXNzWzFdXVthZGRyZXNzWzJdXS5jb2RlKTsKICAgICAgICAgIF90aGlzMi5zZWxlY3RlZE9wdGlvbnMgPSBjaXR5czsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDmj5DkuqTmjInpkq4gKi9zdWJtaXRGb3JtOiBmdW5jdGlvbiBzdWJtaXRGb3JtKCkgewogICAgICB2YXIgX3RoaXMzID0gdGhpczsKICAgICAgdmFyIHRoYXQgPSB0aGlzOwogICAgICB0aGlzLiRyZWZzWyJmb3JtIl0udmFsaWRhdGUoZnVuY3Rpb24gKHZhbGlkKSB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICBpZiAoX3RoaXMzLmZvcm0uaW5mb1R5cGUuaW5kZXhPZignMScpID49IDAgJiYgX3RoaXMzLmZvcm0uc2NhbkZpbGUpIHsKICAgICAgICAgICAgdmFyIGVtYWlsUmVnID0gL15bYS16QS1aMC05Xy1dK0BbYS16QS1aMC05Xy1dKyhcLlthLXpBLVowLTlfLV0rKSskLzsKICAgICAgICAgICAgaWYgKCFlbWFpbFJlZy50ZXN0KF90aGlzMy5mb3JtLnNjYW5GaWxlKSkgewogICAgICAgICAgICAgIF90aGlzMy4kbWVzc2FnZS5lcnJvcigi6LWE5paZ5o6l5pS25pa55byP6YKu566x5qC85byP6ZSZ6K+vIik7CiAgICAgICAgICAgICAgcmV0dXJuOwogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgICBpZiAoX3RoaXMzLmZvcm0ub3BlcmF0aW9uVHlwZSA9PSAyICYmICFfdGhpczMuZm9ybS5hdXRoRmlsZSkgewogICAgICAgICAgICBfdGhpczMuJG1lc3NhZ2UuZXJyb3IoIuaOiOadg+exu+Wei+W/hemcgOS4iuS8oOaOiOadg+S5piIpOwogICAgICAgICAgICByZXR1cm47CiAgICAgICAgICB9CiAgICAgICAgICB2YXIgZm9ybVN0ciA9IEpTT04uc3RyaW5naWZ5KF90aGlzMy5mb3JtKTsKICAgICAgICAgIHZhciBmb3JtRGF0YSA9IEpTT04ucGFyc2UoZm9ybVN0cik7CiAgICAgICAgICBpZiAoZm9ybURhdGEubW9kZWwgJiYgZm9ybURhdGEubW9kZWwubGVuZ3RoID4gMCkgZm9ybURhdGEubW9kZWwgPSBmb3JtRGF0YS5tb2RlbC5qb2luKCIsIik7ZWxzZSBmb3JtRGF0YS5tb2RlbCA9IHVuZGVmaW5lZDsKICAgICAgICAgIGlmIChmb3JtRGF0YS5yZXF1aXJlSW5mbyAmJiBmb3JtRGF0YS5yZXF1aXJlSW5mby5sZW5ndGggPiAwKSBmb3JtRGF0YS5yZXF1aXJlSW5mbyA9IGZvcm1EYXRhLnJlcXVpcmVJbmZvLmpvaW4oIiwiKTtlbHNlIGZvcm1EYXRhLnJlcXVpcmVJbmZvID0gdW5kZWZpbmVkOwogICAgICAgICAgaWYgKGZvcm1EYXRhLmluZm9UeXBlICYmIGZvcm1EYXRhLmluZm9UeXBlLmxlbmd0aCA+IDApIGZvcm1EYXRhLmluZm9UeXBlID0gZm9ybURhdGEuaW5mb1R5cGUuam9pbigiLCIpO2Vsc2UgZm9ybURhdGEuaW5mb1R5cGUgPSB1bmRlZmluZWQ7CiAgICAgICAgICBpZiAoZm9ybURhdGEuc3BlYyAmJiBmb3JtRGF0YS5zcGVjLmxlbmd0aCA+IDApIGZvcm1EYXRhLnNwZWMgPSBmb3JtRGF0YS5zcGVjLmpvaW4oIiwiKTtlbHNlIGZvcm1EYXRhLnNwZWMgPSB1bmRlZmluZWQ7CgogICAgICAgICAgLy/mjojmnYPlhazlj7gKICAgICAgICAgIGlmIChfdGhpczMuYXV0aENvbXBhbnlzLmxlbmd0aCA+IDApIHsKICAgICAgICAgICAgdmFyIGFycmF5ID0gbmV3IEFycmF5KCk7CiAgICAgICAgICAgIF90aGlzMy5hdXRoQ29tcGFueXMuZm9yRWFjaChmdW5jdGlvbiAoZSkgewogICAgICAgICAgICAgIGFycmF5LnB1c2goZS52YWx1ZSk7CiAgICAgICAgICAgIH0pOwogICAgICAgICAgICBmb3JtRGF0YS5hdXRoQ29tcGFueSArPSAiLCIgKyBhcnJheS5qb2luKCIsIik7CiAgICAgICAgICB9CiAgICAgICAgICB2YXIgbG9hZGluZyA9IF90aGlzMy4kbG9hZGluZyh7CiAgICAgICAgICAgIGxvY2s6IHRydWUsCiAgICAgICAgICAgIC8vbG9ja+eahOS/ruaUueespi0t6buY6K6k5pivZmFsc2UKICAgICAgICAgICAgdGV4dDogJ0xvYWRpbmcnLAogICAgICAgICAgICAvL+aYvuekuuWcqOWKoOi9veWbvuagh+S4i+aWueeahOWKoOi9veaWh+ahiAogICAgICAgICAgICBzcGlubmVyOiAnZWwtaWNvbi1sb2FkaW5nJywKICAgICAgICAgICAgLy/oh6rlrprkuYnliqDovb3lm77moIfnsbvlkI0KICAgICAgICAgICAgYmFja2dyb3VuZDogJ3JnYmEoMCwgMCwgMCwgMC43KScsCiAgICAgICAgICAgIC8v6YGu572p5bGC6aKc6ImyCiAgICAgICAgICAgIHRhcmdldDogZG9jdW1lbnQucXVlcnlTZWxlY3RvcignLm1haW4tY29udGFpbmVyJykgLy9sb2FkaW7opobnm5bnmoRkb23lhYPntKDoioLngrkKICAgICAgICAgIH0pOwogICAgICAgICAgZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50LnN0eWxlLm92ZXJmbG93WSA9ICdoaWRkZW4nOyAvL+emgeatouW6leWxgmRpdua7muWKqAoKICAgICAgICAgIGlmIChmb3JtRGF0YS5wcm9qZWN0SWQgIT0gbnVsbCkgewogICAgICAgICAgICB1cGRhdGVSZXBvcnQoZm9ybURhdGEpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgICAgICAgLy90aGlzLm1zZ1N1Y2Nlc3MoIuS/ruaUueaIkOWKnyIpOwogICAgICAgICAgICAgIGlmICh0aGF0LmJ1c2luZXNzS2V5KSB7CiAgICAgICAgICAgICAgICB0aGF0LiRyZWZzWydmbG93J10udGFza0NvbXBsZXRlKCLph43mlrDmj5DkuqQiKTsKICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgdGhhdC5zdGFydEZsb3coZm9ybURhdGEpOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICBzZXRUaW1lb3V0KGZ1bmN0aW9uICgpIHsKICAgICAgICAgICAgICAgIGxvYWRpbmcuY2xvc2UoKTsKICAgICAgICAgICAgICAgIGRvY3VtZW50LmRvY3VtZW50RWxlbWVudC5zdHlsZS5vdmVyZmxvd1kgPSAnYXV0byc7IC8v5YWB6K645bqV5bGCZGl25rua5YqoCiAgICAgICAgICAgICAgfSwgMTAwMCk7CiAgICAgICAgICAgIH0pLmNhdGNoKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICAgICAgICBjb25zb2xlLmxvZyhyZXMpOwogICAgICAgICAgICAgIGxvYWRpbmcuY2xvc2UoKTsKICAgICAgICAgICAgICBkb2N1bWVudC5kb2N1bWVudEVsZW1lbnQuc3R5bGUub3ZlcmZsb3dZID0gJ2F1dG8nOyAvL+WFgeiuuOW6leWxgmRpdua7muWKqAogICAgICAgICAgICB9KTsKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIGFkZFJlcG9ydChmb3JtRGF0YSkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICAgICAgICBjb25zb2xlLmxvZygiPT09YWRkUmVwb3J0PT4+PiIpOwogICAgICAgICAgICAgIC8vdGhpcy5tc2dTdWNjZXNzKCLmlrDlop7miJDlip8iKTsKICAgICAgICAgICAgICBmb3JtRGF0YS5wcm9qZWN0SWQgPSByZXNwb25zZS5kYXRhOwogICAgICAgICAgICAgIHRoYXQuZm9ybS5wcm9qZWN0SWQgPSByZXNwb25zZS5kYXRhOwogICAgICAgICAgICAgIHRoYXQuc3RhcnRGbG93KGZvcm1EYXRhKTsKICAgICAgICAgICAgICBzZXRUaW1lb3V0KGZ1bmN0aW9uICgpIHsKICAgICAgICAgICAgICAgIGxvYWRpbmcuY2xvc2UoKTsKICAgICAgICAgICAgICAgIGRvY3VtZW50LmRvY3VtZW50RWxlbWVudC5zdHlsZS5vdmVyZmxvd1kgPSAnYXV0byc7IC8v5YWB6K645bqV5bGCZGl25rua5YqoCiAgICAgICAgICAgICAgfSwgMTAwMCk7CiAgICAgICAgICAgIH0pLmNhdGNoKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICAgICAgICBjb25zb2xlLmxvZyhyZXMpOwogICAgICAgICAgICAgIGxvYWRpbmcuY2xvc2UoKTsKICAgICAgICAgICAgICBkb2N1bWVudC5kb2N1bWVudEVsZW1lbnQuc3R5bGUub3ZlcmZsb3dZID0gJ2F1dG8nOyAvL+WFgeiuuOW6leWxgmRpdua7muWKqAogICAgICAgICAgICB9KTsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIHN0YXJ0RmxvdzogZnVuY3Rpb24gc3RhcnRGbG93KGZvcm1EYXRhKSB7CiAgICAgIC8v6aG555uu5Yy65Z+fCiAgICAgIC8vdmFyIGFyZWEgPSBmb3JtRGF0YS5kaXN0cmljdDsKICAgICAgLy/nlKjmiLfljLrln58gNi0xMeS/ruaUue+8jOagueaNrueUqOaIt+aJgOWcqOWMuuWfn+WIpOaWrQogICAgICB2YXIgdmFyaWFibGVzID0ge307CiAgICAgIHZhcmlhYmxlcy5QUk9DRVNTX0FSRUEgPSB0aGlzLiRzdG9yZS5zdGF0ZS51c2VyLnByb3ZpbmNlOwogICAgICAvL+aYr+WQpuecgei0n+i0o+S6uuinkuiJsgogICAgICBpZiAodGhpcy4kc3RvcmUuc3RhdGUudXNlci5yb2xlcyAmJiB0aGlzLiRzdG9yZS5zdGF0ZS51c2VyLnJvbGVzLmluY2x1ZGVzKCJwcm92aW5jZV9hZG1pbiIpKSB7CiAgICAgICAgdmFyaWFibGVzLmlzTWFuYWdlID0gMTsKICAgICAgfSBlbHNlIHsKICAgICAgICB2YXJpYWJsZXMuaXNNYW5hZ2UgPSAwOwogICAgICB9CiAgICAgIC8v5paw5aKe5YWo6LWw5oql5aSH5a6h5om55rWB56iLCiAgICAgIC8vIGlmKGZvcm1EYXRhLm9wZXJhdGlvblR5cGUgPT0gJzInKXsKICAgICAgLy8gICB2YXJpYWJsZXMuaXNBdXRoID0gdHJ1ZTsKICAgICAgLy8gfWVsc2V7CiAgICAgIC8vICAgdmFyaWFibGVzLmlzQXV0aCA9IGZhbHNlOwogICAgICAvLyB9CiAgICAgIC8vdmFyaWFibGVzLmlzQXV0aCA9IGZhbHNlOwogICAgICB2YXJpYWJsZXMuQlVTSU5FU1NLRVkgPSBmb3JtRGF0YS5wcm9qZWN0SWQ7CiAgICAgIHZhciB0YXNrTmFtZSA9ICLpobnnm67miqXlpIciOwogICAgICBpZiAoZm9ybURhdGEub3BlcmF0aW9uVHlwZSA9PSAnMicpIHsKICAgICAgICB0YXNrTmFtZSA9ICLpobnnm67mjojmnYMiOwogICAgICB9CiAgICAgIHRoaXMuJHJlZnNbJ2Zsb3cnXS5zdGFydEZsb3coZm9ybURhdGEucHJvamVjdElkLCB0YXNrTmFtZSwgdmFyaWFibGVzKTsKICAgIH0sCiAgICBkb3dubG9hZFNRUzogZnVuY3Rpb24gZG93bmxvYWRTUVMoKSB7CiAgICAgIHRoaXMuZG93bmxvYWQoIua1t+S9s+mbhuWboi3mjojmnYPkuaYuZG9jeCIsIGZhbHNlKTsKICAgIH0sCiAgICBkb3dubG9hZENSSDogZnVuY3Rpb24gZG93bmxvYWRDUkgoKSB7CiAgICAgIHRoaXMuZG93bmxvYWQoIua1t+S9s+mbhuWboi3llK7lkI7mnI3liqHmib/or7rlh70uZG9jIiwgZmFsc2UpOwogICAgfSwKICAgIGhhbmRsZUNoYW5nZTogZnVuY3Rpb24gaGFuZGxlQ2hhbmdlKHZhbHVlKSB7CiAgICAgIGlmICghdmFsdWUgfHwgdmFsdWUubGVuZ3RoID09IDApIHsKICAgICAgICB0aGlzLnNlbGVjdGVkT3B0aW9ucyA9IG51bGw7CiAgICAgICAgdGhpcy5mb3JtLnByb3ZpbmNlID0gdW5kZWZpbmVkOwogICAgICAgIHRoaXMuZm9ybS5kaXN0cmljdCA9IHVuZGVmaW5lZDsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgdGhpcy5zZWxlY3RlZE9wdGlvbnMgPSB2YWx1ZTsKICAgICAgdmFyIHR4dCA9ICIiOwogICAgICB2YWx1ZS5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgdHh0ICs9IENvZGVUb1RleHRbaXRlbV0gKyAiLyI7CiAgICAgIH0pOwogICAgICBpZiAodHh0Lmxlbmd0aCA+IDEpIHsKICAgICAgICB0eHQgPSB0eHQuc3Vic3RyaW5nKDAsIHR4dC5sZW5ndGggLSAxKTsKICAgICAgICB0aGlzLmZvcm0ucHJvdmluY2UgPSB0eHQ7CiAgICAgICAgdGhpcy5mb3JtLmRpc3RyaWN0ID0gdGhpcy4kc3RvcmUuc3RhdGUudXNlci5wcm92aW5jZTsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLmZvcm0ucHJvdmluY2UgPSB1bmRlZmluZWQ7CiAgICAgICAgdGhpcy5mb3JtLmRpc3RyaWN0ID0gdW5kZWZpbmVkOwogICAgICB9CiAgICB9LAogICAgaGFuZGxlUXVlcnlDaXR5Q2hhbmdlOiBmdW5jdGlvbiBoYW5kbGVRdWVyeUNpdHlDaGFuZ2UodmFsdWUpIHsKICAgICAgdGhpcy5xdWVyeUFyZWEgPSB2YWx1ZTsKICAgICAgdmFyIHR4dCA9ICIiOwogICAgICB2YWx1ZS5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgdHh0ICs9IENvZGVUb1RleHRbaXRlbV0gKyAiLyI7CiAgICAgIH0pOwogICAgICBpZiAodHh0Lmxlbmd0aCA+IDEpIHsKICAgICAgICB0eHQgPSB0eHQuc3Vic3RyaW5nKDAsIHR4dC5sZW5ndGggLSAxKTsKICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnByb3ZpbmNlID0gdHh0OwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMucXVlcnlQYXJhbXMucHJvdmluY2UgPSB1bmRlZmluZWQ7CiAgICAgIH0KICAgIH0sCiAgICAvKiog5a6h5om5ICovaGFuZGxlQ29tcGxldGU6IGZ1bmN0aW9uIGhhbmRsZUNvbXBsZXRlKCkgewogICAgICB0aGlzLiRyZWZzWydmbG93J10uaGFuZGxlQ29tcGxldGUoKTsKICAgIH0sCiAgICAvKiog6YCA5ZueICovaGFuZGxlUmV0dXJuOiBmdW5jdGlvbiBoYW5kbGVSZXR1cm4oKSB7CiAgICAgIHRoaXMuJHJlZnNbJ2Zsb3cnXS5oYW5kbGVSZXR1cm4oKTsKICAgIH0sCiAgICAvKiog6L+U5Zue6aG16Z2iICovZ29CYWNrOiBmdW5jdGlvbiBnb0JhY2soKSB7CiAgICAgIC8vIOWFs+mXreW9k+WJjeagh+etvumhteW5tui/lOWbnuS4iuS4qumhtemdogogICAgICB0aGlzLiRzdG9yZS5kaXNwYXRjaCgidGFnc1ZpZXcvZGVsVmlldyIsIHRoaXMuJHJvdXRlKTsKICAgICAgdGhpcy4kcm91dGVyLmdvKC0xKTsKICAgIH0sCiAgICByZW1vdmVEb21haW46IGZ1bmN0aW9uIHJlbW92ZURvbWFpbihpbmRleCkgewogICAgICBpZiAoaW5kZXggIT09IC0xKSB7CiAgICAgICAgdGhpcy5hdXRoQ29tcGFueXMuc3BsaWNlKGluZGV4LCAxKTsKICAgICAgfQogICAgfSwKICAgIGFkZERvbWFpbjogZnVuY3Rpb24gYWRkRG9tYWluKCkgewogICAgICB0aGlzLmF1dGhDb21wYW55cy5wdXNoKHsKICAgICAgICB2YWx1ZTogJycsCiAgICAgICAga2V5OiBEYXRlLm5vdygpCiAgICAgIH0pOwogICAgfQogIH0KfTs="}, null]}