{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Breadcrumb/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Breadcrumb/index.vue", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js", "mtime": 1751171659043}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmV4cG9ydCBkZWZhdWx0IHsKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgbGV2ZWxMaXN0OiBudWxsCiAgICB9OwogIH0sCiAgd2F0Y2g6IHsKICAgICRyb3V0ZTogZnVuY3Rpb24gJHJvdXRlKHJvdXRlKSB7CiAgICAgIC8vIGlmIHlvdSBnbyB0byB0aGUgcmVkaXJlY3QgcGFnZSwgZG8gbm90IHVwZGF0ZSB0aGUgYnJlYWRjcnVtYnMKICAgICAgaWYgKHJvdXRlLnBhdGguc3RhcnRzV2l0aCgnL3JlZGlyZWN0LycpKSB7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIHRoaXMuZ2V0QnJlYWRjcnVtYigpOwogICAgfQogIH0sCiAgY3JlYXRlZDogZnVuY3Rpb24gY3JlYXRlZCgpIHsKICAgIHRoaXMuZ2V0QnJlYWRjcnVtYigpOwogIH0sCiAgbWV0aG9kczogewogICAgZ2V0QnJlYWRjcnVtYjogZnVuY3Rpb24gZ2V0QnJlYWRjcnVtYigpIHsKICAgICAgLy8gb25seSBzaG93IHJvdXRlcyB3aXRoIG1ldGEudGl0bGUKICAgICAgdmFyIG1hdGNoZWQgPSB0aGlzLiRyb3V0ZS5tYXRjaGVkLmZpbHRlcihmdW5jdGlvbiAoaXRlbSkgewogICAgICAgIHJldHVybiBpdGVtLm1ldGEgJiYgaXRlbS5tZXRhLnRpdGxlOwogICAgICB9KTsKICAgICAgdmFyIGZpcnN0ID0gbWF0Y2hlZFswXTsKICAgICAgaWYgKCF0aGlzLmlzRGFzaGJvYXJkKGZpcnN0KSkgewogICAgICAgIG1hdGNoZWQgPSBbewogICAgICAgICAgcGF0aDogJy9pbmRleCcsCiAgICAgICAgICBtZXRhOiB7CiAgICAgICAgICAgIHRpdGxlOiAn6aaW6aG1JwogICAgICAgICAgfQogICAgICAgIH1dLmNvbmNhdChtYXRjaGVkKTsKICAgICAgfQogICAgICB0aGlzLmxldmVsTGlzdCA9IG1hdGNoZWQuZmlsdGVyKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgcmV0dXJuIGl0ZW0ubWV0YSAmJiBpdGVtLm1ldGEudGl0bGUgJiYgaXRlbS5tZXRhLmJyZWFkY3J1bWIgIT09IGZhbHNlOwogICAgICB9KTsKICAgIH0sCiAgICBpc0Rhc2hib2FyZDogZnVuY3Rpb24gaXNEYXNoYm9hcmQocm91dGUpIHsKICAgICAgdmFyIG5hbWUgPSByb3V0ZSAmJiByb3V0ZS5uYW1lOwogICAgICBpZiAoIW5hbWUpIHsKICAgICAgICByZXR1cm4gZmFsc2U7CiAgICAgIH0KICAgICAgcmV0dXJuIG5hbWUudHJpbSgpID09PSAn6aaW6aG1JzsKICAgIH0sCiAgICBoYW5kbGVMaW5rOiBmdW5jdGlvbiBoYW5kbGVMaW5rKGl0ZW0pIHsKICAgICAgdmFyIHJlZGlyZWN0ID0gaXRlbS5yZWRpcmVjdCwKICAgICAgICBwYXRoID0gaXRlbS5wYXRoOwogICAgICBpZiAocmVkaXJlY3QpIHsKICAgICAgICB0aGlzLiRyb3V0ZXIucHVzaChyZWRpcmVjdCk7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHBhdGgpOwogICAgfQogIH0KfTs="}, null]}