{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/login.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/login.vue", "mtime": 1716984016441}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js", "mtime": 1751171659043}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IHJlZ2lzdGVyIH0gZnJvbSAiQC9hcGkvc3lzdGVtL3VzZXIiOwppbXBvcnQgeyBnZXRDb2RlSW1nLCByZXNldENhcHRjaGEsIHJlc2V0UHdkQnlQaG9uZSB9IGZyb20gIkAvYXBpL2xvZ2luIjsKaW1wb3J0IENvb2tpZXMgZnJvbSAianMtY29va2llIjsKaW1wb3J0IHsgZW5jcnlwdCwgZGVjcnlwdCB9IGZyb20gIkAvdXRpbHMvanNlbmNyeXB0IjsKaW1wb3J0IEltYWdlVXBsb2FkIGZyb20gIkAvY29tcG9uZW50cy9JbWFnZVVwbG9hZCI7CmltcG9ydCB7IHJlZ2lvbkRhdGEsIENvZGVUb1RleHQgfSBmcm9tICJlbGVtZW50LWNoaW5hLWFyZWEtZGF0YSI7CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiTG9naW4iLAogIGNvbXBvbmVudHM6IHsKICAgIEltYWdlVXBsb2FkOiBJbWFnZVVwbG9hZAogIH0sCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICAvLyDoh6rlrprkuYnmoKHpqozop4TliJkKICAgIHZhciBiYXJnYWluUGljID0gZnVuY3Rpb24gYmFyZ2FpblBpYyhydWxlLCB2YWx1ZSwgY2FsbGJhY2spIHsKICAgICAgaWYgKCFfdGhpcy5pbWdOYW1lKSB7CiAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCLokKXkuJrmiafnhafnhafniYflv4XkvKAiKSk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgY2FsbGJhY2soKTsKICAgICAgfQogICAgfTsKICAgIHJldHVybiB7CiAgICAgIGNvZGVVcmw6ICIiLAogICAgICBjb29raWVQYXNzd29yZDogIiIsCiAgICAgIGxvZ2luRm9ybTogewogICAgICAgIHVzZXJuYW1lOiAiIiwKICAgICAgICBwYXNzd29yZDogIiIsCiAgICAgICAgcmVtZW1iZXJNZTogZmFsc2UsCiAgICAgICAgY29kZTogIiIsCiAgICAgICAgdXVpZDogIiIKICAgICAgfSwKICAgICAgbG9naW5SdWxlczogewogICAgICAgIHVzZXJuYW1lOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIsCiAgICAgICAgICBtZXNzYWdlOiAi55So5oi35ZCN5LiN6IO95Li656m6IgogICAgICAgIH1dLAogICAgICAgIHBhc3N3b3JkOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIsCiAgICAgICAgICBtZXNzYWdlOiAi5a+G56CB5LiN6IO95Li656m6IgogICAgICAgIH1dLAogICAgICAgIGNvZGU6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIHRyaWdnZXI6ICJjaGFuZ2UiLAogICAgICAgICAgbWVzc2FnZTogIumqjOivgeeggeS4jeiDveS4uuepuiIKICAgICAgICB9XQogICAgICB9LAogICAgICBsb2FkaW5nOiBmYWxzZSwKICAgICAgcmVkaXJlY3Q6IHVuZGVmaW5lZCwKICAgICAgLy8g5by55Ye65bGC5qCH6aKYCiAgICAgIHRpdGxlOiAiIiwKICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGCCiAgICAgIG9wZW46IGZhbHNlLAogICAgICAvLyDooajljZXmoKHpqowKICAgICAgcnVsZXM6IHsKICAgICAgICB1c2VyTmFtZTogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogIueUqOaIt+WQjeS4jeiDveS4uuepuiIsCiAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIKICAgICAgICB9XSwKICAgICAgICBjb21wYW55OiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAi5YWs5Y+45YWo56ew5LiN6IO95Li656m6IiwKICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgIH1dLAogICAgICAgIGJ1c2luZXNzTm86IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICLokKXkuJrmiafnhaflj7fnoIHkuI3og73kuLrnqboiLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgfV0sCiAgICAgICAgcHJvdmluY2U6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICLmiYDlnKjljLrln5/lv4XpgIkiLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgfV0sCiAgICAgICAgaW1nTmFtZTogW3sKICAgICAgICAgIHZhbGlkYXRvcjogYmFyZ2FpblBpYywKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogIuiQpeS4muaJp+eFp+eFp+eJh+W/heS8oCIsCiAgICAgICAgICB0cmlnZ2VyOiAiY2hhbmdlIgogICAgICAgIH1dLAogICAgICAgIHBhc3N3b3JkOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIsCiAgICAgICAgICBtZXNzYWdlOiAi5a+G56CB5LiN6IO95Li656m6IgogICAgICAgIH1dLAogICAgICAgIHBhc3N3b3JkMjogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiLAogICAgICAgICAgbWVzc2FnZTogIuehruiupOWvhueggeS4jeiDveS4uuepuiIKICAgICAgICB9XSwKICAgICAgICBlbWFpbDogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgdHlwZTogImVtYWlsIiwKICAgICAgICAgIG1lc3NhZ2U6ICIn6K+36L6T5YWl5q2j56Gu55qE6YKu566x5Zyw5Z2AIiwKICAgICAgICAgIHRyaWdnZXI6IFsiYmx1ciIsICJjaGFuZ2UiXQogICAgICAgIH1dLAogICAgICAgIHBob25lbnVtYmVyOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBwYXR0ZXJuOiAvXjFbM3w0fDV8Nnw3fDh8OV1bMC05XVxkezh9JC8sCiAgICAgICAgICBtZXNzYWdlOiAi6K+36L6T5YWl5q2j56Gu55qE5omL5py65Y+356CBIiwKICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgIH1dLAogICAgICAgIG5pY2tOYW1lOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAi5oql5aSH5Lq65aeT5ZCN5LiN6IO95Li656m6IiwKICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgIH1dLAogICAgICAgIGFkZHJlc3M6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICLotYTmlpnpgq7lr4TlnLDlnYDkuI3og73kuLrnqboiLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgfV0sCiAgICAgICAgY29kZTogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgdHJpZ2dlcjogImNoYW5nZSIsCiAgICAgICAgICBtZXNzYWdlOiAi6aqM6K+B56CB5LiN6IO95Li656m6IgogICAgICAgIH1dCiAgICAgIH0sCiAgICAgIC8vIOaAp+WIq+eKtuaAgeWtl+WFuAogICAgICBzZXhPcHRpb25zOiBbXSwKICAgICAgLy8g55+t5L+h6YCa55+l5a2X5YW4CiAgICAgIHNtc1NlbmRPcHRpb25zOiBbXSwKICAgICAgLy8g5a6h5qC454q25oCB5a2X5YW4CiAgICAgIGF1ZGl0U3RhdHVzT3B0aW9uczogW10sCiAgICAgIC8vIOinkuiJsumAiemhuQogICAgICByb2xlT3B0aW9uczogW10sCiAgICAgIC8vIOihqOWNleWPguaVsAogICAgICBmb3JtOiB7fSwKICAgICAgcHJvdmluY2VBbmRDaXR5RGF0YTogcmVnaW9uRGF0YSwKICAgICAgY2l0eU9wdGlvbnM6IFtdLAogICAgICBpbWc6IHVuZGVmaW5lZCwKICAgICAgaW1nTmFtZTogdW5kZWZpbmVkLAogICAgICBpbWdGaWxlOiB1bmRlZmluZWQsCiAgICAgIHJlc3RPcGVuOiBmYWxzZSwKICAgICAgcmVzZXRDb2RlVHh0OiAi6I635Y+W6aqM6K+B56CBIiwKICAgICAgcmVzZXRQd2RGb3JtOiB7CiAgICAgICAgdXNlcm5hbWU6ICIiLAogICAgICAgIHBhc3N3b3JkOiAiIiwKICAgICAgICBjb2RlOiAiIgogICAgICB9LAogICAgICByZXNldFJ1bGVzOiB7CiAgICAgICAgdXNlcm5hbWU6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIHBhdHRlcm46IC9eMVszfDR8NXw2fDd8OHw5XVswLTldXGR7OH0kLywKICAgICAgICAgIG1lc3NhZ2U6ICLor7fovpPlhaXmraPnoa7nmoTmiYvmnLrlj7fnoIEiLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgfV0sCiAgICAgICAgcGFzc3dvcmQ6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIHRyaWdnZXI6ICJibHVyIiwKICAgICAgICAgIG1lc3NhZ2U6ICLlr4bnoIHkuI3og73kuLrnqboiCiAgICAgICAgfV0sCiAgICAgICAgY29kZTogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgdHJpZ2dlcjogImNoYW5nZSIsCiAgICAgICAgICBtZXNzYWdlOiAi6aqM6K+B56CB5LiN6IO95Li656m6IgogICAgICAgIH1dCiAgICAgIH0KICAgIH07CiAgfSwKICB3YXRjaDogewogICAgJHJvdXRlOiB7CiAgICAgIGhhbmRsZXI6IGZ1bmN0aW9uIGhhbmRsZXIocm91dGUpIHsKICAgICAgICB0aGlzLnJlZGlyZWN0ID0gcm91dGUucXVlcnkgJiYgcm91dGUucXVlcnkucmVkaXJlY3Q7CiAgICAgIH0sCiAgICAgIGltbWVkaWF0ZTogdHJ1ZQogICAgfQogIH0sCiAgY3JlYXRlZDogZnVuY3Rpb24gY3JlYXRlZCgpIHsKICAgIHRoaXMuZ2V0Q29kZSgpOwogICAgdGhpcy5nZXRDb29raWUoKTsKICB9LAogIG1ldGhvZHM6IHsKICAgIGdldENvZGU6IGZ1bmN0aW9uIGdldENvZGUoKSB7CiAgICAgIHZhciBfdGhpczIgPSB0aGlzOwogICAgICBnZXRDb2RlSW1nKCkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgX3RoaXMyLmNvZGVVcmwgPSAiZGF0YTppbWFnZS9naWY7YmFzZTY0LCIgKyByZXMuaW1nOwogICAgICAgIF90aGlzMi5sb2dpbkZvcm0udXVpZCA9IHJlcy51dWlkOwogICAgICB9KTsKICAgIH0sCiAgICBnZXRDb29raWU6IGZ1bmN0aW9uIGdldENvb2tpZSgpIHsKICAgICAgdmFyIHVzZXJuYW1lID0gQ29va2llcy5nZXQoInVzZXJuYW1lIik7CiAgICAgIHZhciBwYXNzd29yZCA9IENvb2tpZXMuZ2V0KCJwYXNzd29yZCIpOwogICAgICB2YXIgcmVtZW1iZXJNZSA9IENvb2tpZXMuZ2V0KCJyZW1lbWJlck1lIik7CiAgICAgIHRoaXMubG9naW5Gb3JtID0gewogICAgICAgIHVzZXJuYW1lOiB1c2VybmFtZSA9PT0gdW5kZWZpbmVkID8gdGhpcy5sb2dpbkZvcm0udXNlcm5hbWUgOiB1c2VybmFtZSwKICAgICAgICBwYXNzd29yZDogcGFzc3dvcmQgPT09IHVuZGVmaW5lZCA/IHRoaXMubG9naW5Gb3JtLnBhc3N3b3JkIDogZGVjcnlwdChwYXNzd29yZCksCiAgICAgICAgcmVtZW1iZXJNZTogcmVtZW1iZXJNZSA9PT0gdW5kZWZpbmVkID8gZmFsc2UgOiBCb29sZWFuKHJlbWVtYmVyTWUpCiAgICAgIH07CiAgICB9LAogICAgaGFuZGxlTG9naW46IGZ1bmN0aW9uIGhhbmRsZUxvZ2luKCkgewogICAgICB2YXIgX3RoaXMzID0gdGhpczsKICAgICAgdGhpcy4kcmVmcy5sb2dpbkZvcm0udmFsaWRhdGUoZnVuY3Rpb24gKHZhbGlkKSB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICBfdGhpczMubG9hZGluZyA9IHRydWU7CiAgICAgICAgICBpZiAoX3RoaXMzLmxvZ2luRm9ybS5yZW1lbWJlck1lKSB7CiAgICAgICAgICAgIENvb2tpZXMuc2V0KCJ1c2VybmFtZSIsIF90aGlzMy5sb2dpbkZvcm0udXNlcm5hbWUsIHsKICAgICAgICAgICAgICBleHBpcmVzOiAzMAogICAgICAgICAgICB9KTsKICAgICAgICAgICAgQ29va2llcy5zZXQoInBhc3N3b3JkIiwgZW5jcnlwdChfdGhpczMubG9naW5Gb3JtLnBhc3N3b3JkKSwgewogICAgICAgICAgICAgIGV4cGlyZXM6IDMwCiAgICAgICAgICAgIH0pOwogICAgICAgICAgICBDb29raWVzLnNldCgicmVtZW1iZXJNZSIsIF90aGlzMy5sb2dpbkZvcm0ucmVtZW1iZXJNZSwgewogICAgICAgICAgICAgIGV4cGlyZXM6IDMwCiAgICAgICAgICAgIH0pOwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgQ29va2llcy5yZW1vdmUoInVzZXJuYW1lIik7CiAgICAgICAgICAgIENvb2tpZXMucmVtb3ZlKCJwYXNzd29yZCIpOwogICAgICAgICAgICBDb29raWVzLnJlbW92ZSgicmVtZW1iZXJNZSIpOwogICAgICAgICAgfQogICAgICAgICAgX3RoaXMzLiRzdG9yZS5kaXNwYXRjaCgiTG9naW4iLCBfdGhpczMubG9naW5Gb3JtKS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICAgICAgX3RoaXMzLiRyb3V0ZXIucHVzaCh7CiAgICAgICAgICAgICAgcGF0aDogX3RoaXMzLnJlZGlyZWN0IHx8ICIvIgogICAgICAgICAgICB9KS5jYXRjaChmdW5jdGlvbiAoKSB7fSk7CiAgICAgICAgICB9KS5jYXRjaChmdW5jdGlvbiAoKSB7CiAgICAgICAgICAgIF90aGlzMy5sb2FkaW5nID0gZmFsc2U7CiAgICAgICAgICAgIF90aGlzMy5nZXRDb2RlKCk7CiAgICAgICAgICB9KTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIC8vIOihqOWNlemHjee9rgogICAgcmVzZXQ6IGZ1bmN0aW9uIHJlc2V0KCkgewogICAgICB0aGlzLmZvcm0gPSB7CiAgICAgICAgdXNlcklkOiB1bmRlZmluZWQsCiAgICAgICAgdXNlck5hbWU6IHVuZGVmaW5lZCwKICAgICAgICBuaWNrTmFtZTogdW5kZWZpbmVkLAogICAgICAgIHVzZXJUeXBlOiB1bmRlZmluZWQsCiAgICAgICAgZW1haWw6IHVuZGVmaW5lZCwKICAgICAgICBwaG9uZW51bWJlcjogdW5kZWZpbmVkLAogICAgICAgIHNleDogIjIiLAogICAgICAgIGF2YXRhcjogdW5kZWZpbmVkLAogICAgICAgIHBhc3N3b3JkOiB1bmRlZmluZWQsCiAgICAgICAgY29tcGFueTogdW5kZWZpbmVkLAogICAgICAgIGJ1c2luZXNzTm86IHVuZGVmaW5lZCwKICAgICAgICBidXNpbmVzc05vUGljOiB1bmRlZmluZWQsCiAgICAgICAgcHJvdmluY2U6IHVuZGVmaW5lZCwKICAgICAgICBhZGRyZXNzOiB1bmRlZmluZWQsCiAgICAgICAgZGVhbGVyOiB1bmRlZmluZWQKICAgICAgfTsKICAgICAgdGhpcy5pbWcgPSB1bmRlZmluZWQ7CiAgICAgIHRoaXMuaW1nTmFtZSA9IHVuZGVmaW5lZDsKICAgICAgdGhpcy5pbWdGaWxlID0gdW5kZWZpbmVkOwogICAgICB0aGlzLnJlc2V0Rm9ybSgiZm9ybSIpOwogICAgfSwKICAgIC8vIOWPlua2iOaMiemSrgogICAgY2FuY2VsOiBmdW5jdGlvbiBjYW5jZWwoKSB7CiAgICAgIHRoaXMub3BlbiA9IGZhbHNlOwogICAgICB0aGlzLnJlc2V0KCk7CiAgICB9LAogICAgLyoqIOaWsOWinuaMiemSruaTjeS9nCAqL2hhbmRsZUFkZDogZnVuY3Rpb24gaGFuZGxlQWRkKCkgewogICAgICB0aGlzLnJlc2V0KCk7CiAgICAgIC8vdGhpcy5nZXRUcmVlc2VsZWN0KCk7CiAgICAgIHRoaXMub3BlbiA9IHRydWU7CiAgICAgIHRoaXMudGl0bGUgPSAi55So5oi35rOo5YaMIjsKICAgIH0sCiAgICBoYW5kbGVSZXN0OiBmdW5jdGlvbiBoYW5kbGVSZXN0KCkgewogICAgICAvL3RoaXMuZ2V0VHJlZXNlbGVjdCgpOwogICAgICB0aGlzLnJlc3RPcGVuID0gdHJ1ZTsKICAgIH0sCiAgICAvKiog5o+Q5Lqk5oyJ6ZKuICovCiAgICBzdWJtaXRGb3JtOiBmdW5jdGlvbiBzdWJtaXRGb3JtKCkgewogICAgICB2YXIgX3RoaXM0ID0gdGhpczsKICAgICAgaWYgKHRoaXMuZm9ybS5wYXNzd29yZCAhPSB0aGlzLmZvcm0ucGFzc3dvcmQyKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi5Lik5qyh6L6T5YWl5a+G56CB5LiN5LiA6Ie0Iik7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIC8vIHRoaXMuZm9ybS5maWxlID0gdGhpcy5pbWdGaWxlOwogICAgICAvLyBjb25zb2xlLmxvZyh0aGlzLmZvcm0pCgogICAgICB0aGlzLiRyZWZzWyJmb3JtIl0udmFsaWRhdGUoZnVuY3Rpb24gKHZhbGlkKSB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICB2YXIgcGFyYW0gPSBuZXcgRm9ybURhdGEoKTsKICAgICAgICAgIHBhcmFtLmFwcGVuZCgiZmlsZSIsIF90aGlzNC5pbWdGaWxlKTsKICAgICAgICAgIHBhcmFtLmFwcGVuZCgidXNlck5hbWUiLCBfdGhpczQuZm9ybS51c2VyTmFtZSk7CiAgICAgICAgICBwYXJhbS5hcHBlbmQoIm5pY2tOYW1lIiwgX3RoaXM0LmZvcm0ubmlja05hbWUpOwogICAgICAgICAgcGFyYW0uYXBwZW5kKCJlbWFpbCIsIF90aGlzNC5mb3JtLmVtYWlsKTsKICAgICAgICAgIHBhcmFtLmFwcGVuZCgicGhvbmVudW1iZXIiLCBfdGhpczQuZm9ybS5waG9uZW51bWJlcik7CiAgICAgICAgICBwYXJhbS5hcHBlbmQoInBhc3N3b3JkIiwgX3RoaXM0LmZvcm0ucGFzc3dvcmQpOwogICAgICAgICAgcGFyYW0uYXBwZW5kKCJjb21wYW55IiwgX3RoaXM0LmZvcm0uY29tcGFueSk7CiAgICAgICAgICBwYXJhbS5hcHBlbmQoImJ1c2luZXNzTm8iLCBfdGhpczQuZm9ybS5idXNpbmVzc05vKTsKICAgICAgICAgIHBhcmFtLmFwcGVuZCgicHJvdmluY2UiLCBfdGhpczQuZm9ybS5wcm92aW5jZSk7CiAgICAgICAgICBwYXJhbS5hcHBlbmQoImFkZHJlc3MiLCBfdGhpczQuZm9ybS5hZGRyZXNzKTsKICAgICAgICAgIHBhcmFtLmFwcGVuZCgiZGVhbGVyIiwgX3RoaXM0LmZvcm0uZGVhbGVyKTsKICAgICAgICAgICIiOwogICAgICAgICAgcmVnaXN0ZXIocGFyYW0pLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgICAgIF90aGlzNC5tc2dTdWNjZXNzKCLmj5DkuqTmiJDlip8iKTsKICAgICAgICAgICAgX3RoaXM0Lm9wZW4gPSBmYWxzZTsKICAgICAgICAgICAgX3RoaXM0LnJlc2V0KCk7CiAgICAgICAgICB9KTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIGhhbmRsZUNpdHlDaGFuZ2U6IGZ1bmN0aW9uIGhhbmRsZUNpdHlDaGFuZ2UodmFsdWUpIHsKICAgICAgdGhpcy5jaXR5T3B0aW9ucyA9IHZhbHVlOwogICAgICB2YXIgdHh0ID0gIiI7CiAgICAgIHZhbHVlLmZvckVhY2goZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICB0eHQgKz0gQ29kZVRvVGV4dFtpdGVtXSArICIvIjsKICAgICAgfSk7CiAgICAgIGlmICh0eHQubGVuZ3RoID4gMSkgewogICAgICAgIHR4dCA9IHR4dC5zdWJzdHJpbmcoMCwgdHh0Lmxlbmd0aCAtIDEpOwogICAgICAgIHRoaXMuZm9ybS5wcm92aW5jZSA9IHR4dDsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLmZvcm0ucHJvdmluY2UgPSB1bmRlZmluZWQ7CiAgICAgIH0KICAgIH0sCiAgICBhZGRJbWc6IGZ1bmN0aW9uIGFkZEltZyhmaWxlMSkgewogICAgICB2YXIgZmlsZSA9IGZpbGUxLnJhdzsKICAgICAgdmFyIGlzSlBHID0gZmlsZS50eXBlID09PSAiaW1hZ2UvanBlZyI7CiAgICAgIHZhciBpc1BORyA9IGZpbGUudHlwZSA9PT0gImltYWdlL3BuZyI7CiAgICAgIHZhciBpc1dFQlAgPSBmaWxlLnR5cGUgPT09ICJpbWFnZS93ZWJwIjsKICAgICAgdmFyIGlzTHQxTSA9IGZpbGUuc2l6ZSAvIDEwMjQgLyAxMDI0IDwgMTsKICAgICAgaWYgKCFpc0pQRyAmJiAhaXNQTkcgJiYgIWlzV0VCUCkgewogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuS4iuS8oOWbvueJh+WPquiDveaYryBKUEfjgIFQTkfjgIFXRUJQIOagvOW8jyEiKTsKICAgICAgfSBlbHNlIGlmICghaXNMdDFNKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi5LiK5Lyg5Zu+54mH5aSn5bCP5LiN6IO96LaF6L+HIDFNQiEiKTsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLmltZ0ZpbGUgPSBmaWxlOwogICAgICAgIHRoaXMuaW1nTmFtZSA9IGZpbGUubmFtZTsKICAgICAgICB2YXIgc2VsZiA9IHRoaXM7CiAgICAgICAgLy/lrprkuYnkuIDkuKrmlofku7bpmIXor7vlmagKICAgICAgICB2YXIgcmVhZGVyID0gbmV3IEZpbGVSZWFkZXIoKTsKICAgICAgICAvL+aWh+S7tuijhei9veWQjuWwhuWFtuaYvuekuuWcqOWbvueJh+mihOiniOmHjAogICAgICAgIHJlYWRlci5vbmxvYWQgPSBmdW5jdGlvbiAoZSkgewogICAgICAgICAgLy/lsIZiYWRlNjTkvY3lm77niYfkv53lrZjoh7PmlbDnu4Tph4zkvpvkuIrpnaLlm77niYfmmL7npLoKICAgICAgICAgIHNlbGYuaW1nID0gZS50YXJnZXQucmVzdWx0OwogICAgICAgIH07CiAgICAgICAgcmVhZGVyLnJlYWRBc0RhdGFVUkwoZmlsZSk7CiAgICAgIH0KICAgICAgdGhpcy4kcmVmcy5mb3JtLnZhbGlkYXRlRmllbGQoImltZ05hbWUiKTsKICAgIH0sCiAgICByZW1vdmVJbWFnZTogZnVuY3Rpb24gcmVtb3ZlSW1hZ2UoKSB7CiAgICAgIHRoaXMuaW1nRmlsZSA9IHVuZGVmaW5lZDsKICAgICAgdGhpcy5pbWcgPSB1bmRlZmluZWQ7CiAgICAgIHRoaXMuaW1nTmFtZSA9IHVuZGVmaW5lZDsKICAgIH0sCiAgICBnZXRQaG9uZUNvZGU6IGZ1bmN0aW9uIGdldFBob25lQ29kZSgpIHsKICAgICAgdmFyIHRoYXQgPSB0aGlzOwogICAgICBpZiAodGhpcy5yZXNldFB3ZEZvcm0udXNlcm5hbWUgPT0gIiIpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLor7fovpPlhaXmiYvmnLrlj7ciKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgcmVzZXRDYXB0Y2hhKHRoaXMucmVzZXRQd2RGb3JtKS50aGVuKGZ1bmN0aW9uIChyZXNwKSB7CiAgICAgICAgdGhhdC5yZXNldENvZGVUeHQgPSAiNjBz5ZCO6YeN5paw6I635Y+WIjsKICAgICAgICBzZXRUaW1lb3V0KGZ1bmN0aW9uICgpIHsKICAgICAgICAgIHRoYXQucmVzZXRDb2RlVHh0ID0gIuiOt+WPlumqjOivgeeggSI7CiAgICAgICAgfSwgNjAgKiAxMDAwKTsKICAgICAgfSk7CiAgICB9LAogICAgaGFuZFJlc2V0UHdkOiBmdW5jdGlvbiBoYW5kUmVzZXRQd2QoKSB7CiAgICAgIHZhciBfdGhpczUgPSB0aGlzOwogICAgICB2YXIgdGhhdCA9IHRoaXM7CiAgICAgIHRoaXMuJHJlZnNbInJlc2V0UHdkRm9ybSJdLnZhbGlkYXRlKGZ1bmN0aW9uICh2YWxpZCkgewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgaWYgKF90aGlzNS5yZXNldFB3ZEZvcm0ucGFzc3dvcmQgPT0gIiIgfHwgX3RoaXM1LnJlc2V0UHdkRm9ybS5wYXNzd29yZCAhPSBfdGhpczUucmVzZXRQd2RGb3JtLnBhc3N3b3JkMikgewogICAgICAgICAgICBfdGhpczUuJG1lc3NhZ2UuZXJyb3IoIuS4pOasoei+k+WFpeWvhueggeS4jeS4gOiHtCIpOwogICAgICAgICAgICByZXR1cm47CiAgICAgICAgICB9CiAgICAgICAgICBpZiAoX3RoaXM1LnJlc2V0UHdkRm9ybS5wYXNzd29yZC5sZW5ndGggPCA2KSB7CiAgICAgICAgICAgIF90aGlzNS4kbWVzc2FnZS5lcnJvcigi5a+G56CB6ZW/5bqm5LiN6IO95bCP5LqOIik7CiAgICAgICAgICAgIHJldHVybjsKICAgICAgICAgIH0KICAgICAgICAgIHJlc2V0UHdkQnlQaG9uZShfdGhpczUucmVzZXRQd2RGb3JtKS50aGVuKGZ1bmN0aW9uIChyZXNwKSB7CiAgICAgICAgICAgIF90aGlzNS5tc2dTdWNjZXNzKCLph43nva7miJDlip8iKTsKICAgICAgICAgICAgdGhhdC5yZXN0T3BlbiA9IGZhbHNlOwogICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0KICB9Cn07"}, null]}