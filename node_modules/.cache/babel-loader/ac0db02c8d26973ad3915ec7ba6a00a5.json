{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Process/index.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Process/index.js", "mtime": 1641277326000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHdvcmtmbG93QnBtbk1vZGVsZXIgZnJvbSAnLi9pbmRleC52dWUnOwp3b3JrZmxvd0JwbW5Nb2RlbGVyLmluc3RhbGwgPSBmdW5jdGlvbiAoVnVlKSB7CiAgcmV0dXJuIFZ1ZS5jb21wb25lbnQod29ya2Zsb3dCcG1uTW9kZWxlci5uYW1lLCB3b3JrZmxvd0JwbW5Nb2RlbGVyKTsKfTsgLy8g57uZ57uE5Lu26YWN572uaW5zdGFsbOaWueazlQoKZXhwb3J0IGRlZmF1bHQgd29ya2Zsb3dCcG1uTW9kZWxlcjs="}, {"version": 3, "names": ["workflowBpmnModeler", "install", "<PERSON><PERSON>", "component", "name"], "sources": ["/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Process/index.js"], "sourcesContent": ["import workflowBpmnModeler from './index.vue'\n\nworkflowBpmnModeler.install = Vue => Vue.component(workflowBpmnModeler.name, workflowBpmnModeler) // 给组件配置install方法\n\nexport default workflowBpmnModeler\n"], "mappings": "AAAA,OAAOA,mBAAmB,MAAM,aAAa;AAE7CA,mBAAmB,CAACC,OAAO,GAAG,UAAAC,GAAG;EAAA,OAAIA,GAAG,CAACC,SAAS,CAACH,mBAAmB,CAACI,IAAI,EAAEJ,mBAAmB,CAAC;AAAA,GAAC;;AAElG,eAAeA,mBAAmB", "ignoreList": []}]}