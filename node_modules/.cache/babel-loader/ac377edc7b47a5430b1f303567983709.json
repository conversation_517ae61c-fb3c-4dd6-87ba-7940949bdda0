{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/utils/validate.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/utils/validate.js", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["isExternal", "path", "test", "validUsername", "str", "valid_map", "indexOf", "trim", "validURL", "url", "reg", "validLowerCase", "validUpperCase", "validAlphabets", "validEmail", "email", "isString", "String", "isArray", "arg", "Array", "Object", "prototype", "toString", "call"], "sources": ["/Users/<USER>/develop/code/project/haijia/adminweb/src/utils/validate.js"], "sourcesContent": ["/**\n * @param {string} path\n * @returns {Boolean}\n */\nexport function isExternal(path) {\n  return /^(https?:|mailto:|tel:)/.test(path)\n}\n\n/**\n * @param {string} str\n * @returns {Boolean}\n */\nexport function validUsername(str) {\n  const valid_map = ['admin', 'editor']\n  return valid_map.indexOf(str.trim()) >= 0\n}\n\n/**\n * @param {string} url\n * @returns {Boolean}\n */\nexport function validURL(url) {\n  const reg = /^(https?|ftp):\\/\\/([a-zA-Z0-9.-]+(:[a-zA-Z0-9.&%$-]+)*@)*((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]?)(\\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}|([a-zA-Z0-9-]+\\.)*[a-zA-Z0-9-]+\\.(com|edu|gov|int|mil|net|org|biz|arpa|info|name|pro|aero|coop|museum|[a-zA-Z]{2}))(:[0-9]+)*(\\/($|[a-zA-Z0-9.,?'\\\\+&%$#=~_-]+))*$/\n  return reg.test(url)\n}\n\n/**\n * @param {string} str\n * @returns {Boolean}\n */\nexport function validLowerCase(str) {\n  const reg = /^[a-z]+$/\n  return reg.test(str)\n}\n\n/**\n * @param {string} str\n * @returns {Boolean}\n */\nexport function validUpperCase(str) {\n  const reg = /^[A-Z]+$/\n  return reg.test(str)\n}\n\n/**\n * @param {string} str\n * @returns {Boolean}\n */\nexport function validAlphabets(str) {\n  const reg = /^[A-Za-z]+$/\n  return reg.test(str)\n}\n\n/**\n * @param {string} email\n * @returns {Boolean}\n */\nexport function validEmail(email) {\n  const reg = /^(([^<>()\\[\\]\\\\.,;:\\s@\"]+(\\.[^<>()\\[\\]\\\\.,;:\\s@\"]+)*)|(\".+\"))@((\\[[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\])|(([a-zA-Z\\-0-9]+\\.)+[a-zA-Z]{2,}))$/\n  return reg.test(email)\n}\n\n/**\n * @param {string} str\n * @returns {Boolean}\n */\nexport function isString(str) {\n  if (typeof str === 'string' || str instanceof String) {\n    return true\n  }\n  return false\n}\n\n/**\n * @param {Array} arg\n * @returns {Boolean}\n */\nexport function isArray(arg) {\n  if (typeof Array.isArray === 'undefined') {\n    return Object.prototype.toString.call(arg) === '[object Array]'\n  }\n  return Array.isArray(arg)\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,SAASA,UAAUA,CAACC,IAAI,EAAE;EAC/B,OAAO,yBAAyB,CAACC,IAAI,CAACD,IAAI,CAAC;AAC7C;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASE,aAAaA,CAACC,GAAG,EAAE;EACjC,IAAMC,SAAS,GAAG,CAAC,OAAO,EAAE,QAAQ,CAAC;EACrC,OAAOA,SAAS,CAACC,OAAO,CAACF,GAAG,CAACG,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;AAC3C;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASC,QAAQA,CAACC,GAAG,EAAE;EAC5B,IAAMC,GAAG,GAAG,4TAA4T;EACxU,OAAOA,GAAG,CAACR,IAAI,CAACO,GAAG,CAAC;AACtB;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASE,cAAcA,CAACP,GAAG,EAAE;EAClC,IAAMM,GAAG,GAAG,UAAU;EACtB,OAAOA,GAAG,CAACR,IAAI,CAACE,GAAG,CAAC;AACtB;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASQ,cAAcA,CAACR,GAAG,EAAE;EAClC,IAAMM,GAAG,GAAG,UAAU;EACtB,OAAOA,GAAG,CAACR,IAAI,CAACE,GAAG,CAAC;AACtB;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASS,cAAcA,CAACT,GAAG,EAAE;EAClC,IAAMM,GAAG,GAAG,aAAa;EACzB,OAAOA,GAAG,CAACR,IAAI,CAACE,GAAG,CAAC;AACtB;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASU,UAAUA,CAACC,KAAK,EAAE;EAChC,IAAML,GAAG,GAAG,yJAAyJ;EACrK,OAAOA,GAAG,CAACR,IAAI,CAACa,KAAK,CAAC;AACxB;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASC,QAAQA,CAACZ,GAAG,EAAE;EAC5B,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAIA,GAAG,YAAYa,MAAM,EAAE;IACpD,OAAO,IAAI;EACb;EACA,OAAO,KAAK;AACd;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASC,OAAOA,CAACC,GAAG,EAAE;EAC3B,IAAI,OAAOC,KAAK,CAACF,OAAO,KAAK,WAAW,EAAE;IACxC,OAAOG,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACL,GAAG,CAAC,KAAK,gBAAgB;EACjE;EACA,OAAOC,KAAK,CAACF,OAAO,CAACC,GAAG,CAAC;AAC3B", "ignoreList": []}]}