{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/flowable/definition/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/flowable/definition/index.vue", "mtime": 1655049558000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGxpc3REZWZpbml0aW9uLCB1cGRhdGVTdGF0ZSwgZGVsRGVwbG95bWVudCwgYWRkRGVwbG95bWVudCwgdXBkYXRlRGVwbG95bWVudCwgZXhwb3J0RGVwbG95bWVudCwgZGVmaW5pdGlvblN0YXJ0LCByZWFkWG1sIH0gZnJvbSAiQC9hcGkvZmxvd2FibGUvZGVmaW5pdGlvbiI7CmltcG9ydCB7IGdldFRva2VuIH0gZnJvbSAiQC91dGlscy9hdXRoIjsKaW1wb3J0IHsgZ2V0Rm9ybSwgYWRkRGVwbG95Rm9ybSwgbGlzdEZvcm0gfSBmcm9tICJAL2FwaS9mbG93YWJsZS9mb3JtIjsKaW1wb3J0IFBhcnNlciBmcm9tICdAL2NvbXBvbmVudHMvcGFyc2VyL1BhcnNlcic7CmltcG9ydCBmbG93IGZyb20gJ0Avdmlld3MvZmxvd2FibGUvdGFzay9yZWNvcmQvZmxvdyc7CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiRGVmaW5pdGlvbiIsCiAgY29tcG9uZW50czogewogICAgUGFyc2VyOiBQYXJzZXIsCiAgICBmbG93OiBmbG93CiAgfSwKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgLy8g6YGu572p5bGCCiAgICAgIGxvYWRpbmc6IHRydWUsCiAgICAgIC8vIOmAieS4reaVsOe7hAogICAgICBpZHM6IFtdLAogICAgICAvLyDpnZ7ljZXkuKrnpoHnlKgKICAgICAgc2luZ2xlOiB0cnVlLAogICAgICAvLyDpnZ7lpJrkuKrnpoHnlKgKICAgICAgbXVsdGlwbGU6IHRydWUsCiAgICAgIC8vIOaYvuekuuaQnOe0ouadoeS7tgogICAgICBzaG93U2VhcmNoOiB0cnVlLAogICAgICAvLyDmgLvmnaHmlbAKICAgICAgdG90YWw6IDAsCiAgICAgIC8vIOa1geeoi+WumuS5ieihqOagvOaVsOaNrgogICAgICBkZWZpbml0aW9uTGlzdDogW10sCiAgICAgIC8vIOW8ueWHuuWxguagh+mimAogICAgICB0aXRsZTogIiIsCiAgICAgIC8vIOaYr+WQpuaYvuekuuW8ueWHuuWxggogICAgICBvcGVuOiBmYWxzZSwKICAgICAgZm9ybUNvbmZPcGVuOiBmYWxzZSwKICAgICAgZm9ybVRpdGxlOiAiIiwKICAgICAgZm9ybURlcGxveU9wZW46IGZhbHNlLAogICAgICBmb3JtRGVwbG95VGl0bGU6ICIiLAogICAgICBmb3JtTGlzdDogW10sCiAgICAgIGZvcm1Ub3RhbDogMCwKICAgICAgZm9ybUNvbmY6IHt9LAogICAgICAvLyDpu5jorqTooajljZXmlbDmja4KICAgICAgcmVhZEltYWdlOiB7CiAgICAgICAgb3BlbjogZmFsc2UsCiAgICAgICAgc3JjOiAiIgogICAgICB9LAogICAgICAvLyBicG1uLnhtbCDlr7zlhaUKICAgICAgdXBsb2FkOiB7CiAgICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGC77yIeG1s5a+85YWl77yJCiAgICAgICAgb3BlbjogZmFsc2UsCiAgICAgICAgLy8g5by55Ye65bGC5qCH6aKY77yIeG1s5a+85YWl77yJCiAgICAgICAgdGl0bGU6ICIiLAogICAgICAgIC8vIOaYr+WQpuemgeeUqOS4iuS8oAogICAgICAgIGlzVXBsb2FkaW5nOiBmYWxzZSwKICAgICAgICBuYW1lOiBudWxsLAogICAgICAgIGNhdGVnb3J5OiBudWxsLAogICAgICAgIC8vIOiuvue9ruS4iuS8oOeahOivt+axguWktOmDqAogICAgICAgIGhlYWRlcnM6IHsKICAgICAgICAgIEF1dGhvcml6YXRpb246ICJCZWFyZXIgIiArIGdldFRva2VuKCkKICAgICAgICB9LAogICAgICAgIC8vIOS4iuS8oOeahOWcsOWdgAogICAgICAgIHVybDogcHJvY2Vzcy5lbnYuVlVFX0FQUF9CQVNFX0FQSSArICIvZmxvd2FibGUvZGVmaW5pdGlvbi9pbXBvcnQiCiAgICAgIH0sCiAgICAgIC8vIOafpeivouWPguaVsAogICAgICBxdWVyeVBhcmFtczogewogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgIG5hbWU6IG51bGwsCiAgICAgICAgY2F0ZWdvcnk6IG51bGwsCiAgICAgICAga2V5OiBudWxsLAogICAgICAgIHRlbmFudElkOiBudWxsLAogICAgICAgIGRlcGxveVRpbWU6IG51bGwsCiAgICAgICAgZGVyaXZlZEZyb206IG51bGwsCiAgICAgICAgZGVyaXZlZEZyb21Sb290OiBudWxsLAogICAgICAgIHBhcmVudERlcGxveW1lbnRJZDogbnVsbCwKICAgICAgICBlbmdpbmVWZXJzaW9uOiBudWxsCiAgICAgIH0sCiAgICAgIGZvcm1RdWVyeVBhcmFtczogewogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDEwCiAgICAgIH0sCiAgICAgIC8vIOaMgui9veihqOWNleWIsOa1geeoi+WunuS+iwogICAgICBmb3JtRGVwbG95UGFyYW06IHsKICAgICAgICBmb3JtSWQ6IG51bGwsCiAgICAgICAgZGVwbG95SWQ6IG51bGwKICAgICAgfSwKICAgICAgY3VycmVudFJvdzogbnVsbCwKICAgICAgLy8geG1sCiAgICAgIHhtbERhdGE6ICIiLAogICAgICAvLyDooajljZXlj4LmlbAKICAgICAgZm9ybToge30sCiAgICAgIC8vIOihqOWNleagoemqjAogICAgICBydWxlczoge30sCiAgICAgIHByb2Nlc3NDYXRlZ29yeU9wdGlvbnM6IFtdCiAgICB9OwogIH0sCiAgYWN0aXZhdGVkOiBmdW5jdGlvbiBhY3RpdmF0ZWQoKSB7CiAgICB0aGlzLmdldExpc3QoKTsKICB9LAogIGNyZWF0ZWQ6IGZ1bmN0aW9uIGNyZWF0ZWQoKSB7CiAgICB2YXIgX3RoaXMgPSB0aGlzOwogICAgdGhpcy5nZXRMaXN0KCk7CiAgICB0aGlzLmdldERpY3RzKCJzeXNfcHJvY2Vzc19jYXRlZ29yeSIpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgIF90aGlzLnByb2Nlc3NDYXRlZ29yeU9wdGlvbnMgPSByZXNwb25zZS5kYXRhOwogICAgfSk7CiAgfSwKICBtZXRob2RzOiB7CiAgICAvKiog5p+l6K+i5rWB56iL5a6a5LmJ5YiX6KGoICovZ2V0TGlzdDogZnVuY3Rpb24gZ2V0TGlzdCgpIHsKICAgICAgdmFyIF90aGlzMiA9IHRoaXM7CiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7CiAgICAgIGxpc3REZWZpbml0aW9uKHRoaXMucXVlcnlQYXJhbXMpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgX3RoaXMyLmRlZmluaXRpb25MaXN0ID0gcmVzcG9uc2UuZGF0YS5yZWNvcmRzOwogICAgICAgIF90aGlzMi50b3RhbCA9IHJlc3BvbnNlLmRhdGEudG90YWw7CiAgICAgICAgX3RoaXMyLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgfSk7CiAgICB9LAogICAgLy8g57yW6L6R54q25oCB5a2X5YW457+76K+RCiAgICBwcm9jZXNzQ2F0ZWdvcnlGb3JtYXQ6IGZ1bmN0aW9uIHByb2Nlc3NDYXRlZ29yeUZvcm1hdChyb3csIGNvbHVtbikgewogICAgICByZXR1cm4gdGhpcy5zZWxlY3REaWN0TGFiZWwodGhpcy5wcm9jZXNzQ2F0ZWdvcnlPcHRpb25zLCByb3cuY2F0ZWdvcnkpOwogICAgfSwKICAgIC8vIOWPlua2iOaMiemSrgogICAgY2FuY2VsOiBmdW5jdGlvbiBjYW5jZWwoKSB7CiAgICAgIHRoaXMub3BlbiA9IGZhbHNlOwogICAgICB0aGlzLnJlc2V0KCk7CiAgICB9LAogICAgLy8g6KGo5Y2V6YeN572uCiAgICByZXNldDogZnVuY3Rpb24gcmVzZXQoKSB7CiAgICAgIHRoaXMuZm9ybSA9IHsKICAgICAgICBpZDogbnVsbCwKICAgICAgICBuYW1lOiBudWxsLAogICAgICAgIGNhdGVnb3J5OiBudWxsLAogICAgICAgIGtleTogbnVsbCwKICAgICAgICB0ZW5hbnRJZDogbnVsbCwKICAgICAgICBkZXBsb3lUaW1lOiBudWxsLAogICAgICAgIGRlcml2ZWRGcm9tOiBudWxsLAogICAgICAgIGRlcml2ZWRGcm9tUm9vdDogbnVsbCwKICAgICAgICBwYXJlbnREZXBsb3ltZW50SWQ6IG51bGwsCiAgICAgICAgZW5naW5lVmVyc2lvbjogbnVsbAogICAgICB9OwogICAgICB0aGlzLnJlc2V0Rm9ybSgiZm9ybSIpOwogICAgfSwKICAgIC8qKiDmkJzntKLmjInpkq7mk43kvZwgKi9oYW5kbGVRdWVyeTogZnVuY3Rpb24gaGFuZGxlUXVlcnkoKSB7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDE7CiAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgfSwKICAgIC8qKiDph43nva7mjInpkq7mk43kvZwgKi9yZXNldFF1ZXJ5OiBmdW5jdGlvbiByZXNldFF1ZXJ5KCkgewogICAgICB0aGlzLnJlc2V0Rm9ybSgicXVlcnlGb3JtIik7CiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsKICAgIH0sCiAgICAvLyDlpJrpgInmoYbpgInkuK3mlbDmja4KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZTogZnVuY3Rpb24gaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgewogICAgICB0aGlzLmlkcyA9IHNlbGVjdGlvbi5tYXAoZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICByZXR1cm4gaXRlbS5pZDsKICAgICAgfSk7CiAgICAgIHRoaXMuc2luZ2xlID0gc2VsZWN0aW9uLmxlbmd0aCAhPT0gMTsKICAgICAgdGhpcy5tdWx0aXBsZSA9ICFzZWxlY3Rpb24ubGVuZ3RoOwogICAgfSwKICAgIC8qKiDmlrDlop7mjInpkq7mk43kvZwgKi9oYW5kbGVBZGQ6IGZ1bmN0aW9uIGhhbmRsZUFkZCgpIHsKICAgICAgdGhpcy5yZXNldCgpOwogICAgICB0aGlzLm9wZW4gPSB0cnVlOwogICAgICB0aGlzLnRpdGxlID0gIua3u+WKoOa1geeoi+WumuS5iSI7CiAgICB9LAogICAgLyoqIOi3s+i9rOWIsOa1geeoi+iuvuiuoemhtemdoiAqL2hhbmRsZUxvYWRYbWw6IGZ1bmN0aW9uIGhhbmRsZUxvYWRYbWwocm93KSB7CiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHsKICAgICAgICBwYXRoOiAnL2Zsb3dhYmxlL2RlZmluaXRpb24vbW9kZWwnLAogICAgICAgIHF1ZXJ5OiB7CiAgICAgICAgICBkZXBsb3lJZDogcm93LmRlcGxveW1lbnRJZAogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLyoqIOa1geeoi+WbvuafpeeciyAqL2hhbmRsZVJlYWRJbWFnZTogZnVuY3Rpb24gaGFuZGxlUmVhZEltYWdlKGRlcGxveW1lbnRJZCkgewogICAgICB2YXIgX3RoaXMzID0gdGhpczsKICAgICAgdGhpcy5yZWFkSW1hZ2UudGl0bGUgPSAi5rWB56iL5Zu+IjsKICAgICAgdGhpcy5yZWFkSW1hZ2Uub3BlbiA9IHRydWU7CiAgICAgIC8vIHRoaXMucmVhZEltYWdlLnNyYyA9IHByb2Nlc3MuZW52LlZVRV9BUFBfQkFTRV9BUEkgKyAiL2Zsb3dhYmxlL2RlZmluaXRpb24vcmVhZEltYWdlLyIgKyBkZXBsb3ltZW50SWQ7CiAgICAgIC8vIOWPkemAgeivt+axgu+8jOiOt+WPlnhtbAogICAgICByZWFkWG1sKGRlcGxveW1lbnRJZCkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgX3RoaXMzLnhtbERhdGEgPSByZXMuZGF0YTsKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOihqOWNleafpeeciyAqL2hhbmRsZUZvcm06IGZ1bmN0aW9uIGhhbmRsZUZvcm0oZm9ybUlkKSB7CiAgICAgIHZhciBfdGhpczQgPSB0aGlzOwogICAgICBnZXRGb3JtKGZvcm1JZCkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgX3RoaXM0LmZvcm1UaXRsZSA9ICLooajljZXor6bmg4UiOwogICAgICAgIF90aGlzNC5mb3JtQ29uZk9wZW4gPSB0cnVlOwogICAgICAgIF90aGlzNC5mb3JtQ29uZiA9IEpTT04ucGFyc2UocmVzLmRhdGEuZm9ybUNvbnRlbnQpOwogICAgICB9KTsKICAgIH0sCiAgICAvKiog5ZCv5Yqo5rWB56iLICovaGFuZGxlRGVmaW5pdGlvblN0YXJ0OiBmdW5jdGlvbiBoYW5kbGVEZWZpbml0aW9uU3RhcnQocm93KSB7CiAgICAgIHZhciBfdGhpczUgPSB0aGlzOwogICAgICBkZWZpbml0aW9uU3RhcnQocm93LmlkKS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICBfdGhpczUubXNnU3VjY2VzcyhyZXMubXNnKTsKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOaMgui9veihqOWNleW8ueahhiAqL2hhbmRsZUFkZEZvcm06IGZ1bmN0aW9uIGhhbmRsZUFkZEZvcm0ocm93KSB7CiAgICAgIHRoaXMuZm9ybURlcGxveVBhcmFtLmRlcGxveUlkID0gcm93LmRlcGxveW1lbnRJZDsKICAgICAgdGhpcy5MaXN0Rm9ybURlcGxveSgpOwogICAgfSwKICAgIC8qKiDmjILovb3ooajljZXliJfooaggKi9MaXN0Rm9ybURlcGxveTogZnVuY3Rpb24gTGlzdEZvcm1EZXBsb3koKSB7CiAgICAgIHZhciBfdGhpczYgPSB0aGlzOwogICAgICBsaXN0Rm9ybSh0aGlzLmZvcm1RdWVyeVBhcmFtcykudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgX3RoaXM2LmZvcm1MaXN0ID0gcmVzLnJvd3M7CiAgICAgICAgX3RoaXM2LmZvcm1Ub3RhbCA9IHJlcy50b3RhbDsKICAgICAgICBfdGhpczYuZm9ybURlcGxveU9wZW4gPSB0cnVlOwogICAgICAgIF90aGlzNi5mb3JtRGVwbG95VGl0bGUgPSAi5oyC6L296KGo5Y2VIjsKICAgICAgfSk7CiAgICB9LAogICAgLy8gLyoqIOabtOaUueaMgui9veihqOWNleW8ueahhiAqLwogICAgLy8gaGFuZGxlRWRpdEZvcm0ocm93KXsKICAgIC8vICAgdGhpcy5mb3JtRGVwbG95UGFyYW0uZGVwbG95SWQgPSByb3cuZGVwbG95bWVudElkCiAgICAvLyAgIGNvbnN0IHF1ZXJ5UGFyYW1zID0gewogICAgLy8gICAgIHBhZ2VOdW06IDEsCiAgICAvLyAgICAgcGFnZVNpemU6IDEwCiAgICAvLyAgIH0KICAgIC8vICAgbGlzdEZvcm0ocXVlcnlQYXJhbXMpLnRoZW4ocmVzID0+ewogICAgLy8gICAgIHRoaXMuZm9ybUxpc3QgPSByZXMucm93czsKICAgIC8vICAgICB0aGlzLmZvcm1EZXBsb3lPcGVuID0gdHJ1ZTsKICAgIC8vICAgICB0aGlzLmZvcm1EZXBsb3lUaXRsZSA9ICLmjILovb3ooajljZUiOwogICAgLy8gICB9KQogICAgLy8gfSwKICAgIC8qKiDmjILovb3ooajljZUgKi8KICAgIHN1Ym1pdEZvcm1EZXBsb3k6IGZ1bmN0aW9uIHN1Ym1pdEZvcm1EZXBsb3kocm93KSB7CiAgICAgIHZhciBfdGhpczcgPSB0aGlzOwogICAgICB0aGlzLmZvcm1EZXBsb3lQYXJhbS5mb3JtSWQgPSByb3cuZm9ybUlkOwogICAgICBhZGREZXBsb3lGb3JtKHRoaXMuZm9ybURlcGxveVBhcmFtKS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICBfdGhpczcubXNnU3VjY2VzcyhyZXMubXNnKTsKICAgICAgICBfdGhpczcuZm9ybURlcGxveU9wZW4gPSBmYWxzZTsKICAgICAgICBfdGhpczcuZ2V0TGlzdCgpOwogICAgICB9KTsKICAgIH0sCiAgICBoYW5kbGVDdXJyZW50Q2hhbmdlOiBmdW5jdGlvbiBoYW5kbGVDdXJyZW50Q2hhbmdlKGRhdGEpIHsKICAgICAgaWYgKGRhdGEpIHsKICAgICAgICB0aGlzLmN1cnJlbnRSb3cgPSBKU09OLnBhcnNlKGRhdGEuZm9ybUNvbnRlbnQpOwogICAgICB9CiAgICB9LAogICAgLyoqIOaMgui1ty/mv4DmtLvmtYHnqIsgKi9oYW5kbGVVcGRhdGVTdXNwZW5zaW9uU3RhdGU6IGZ1bmN0aW9uIGhhbmRsZVVwZGF0ZVN1c3BlbnNpb25TdGF0ZShyb3cpIHsKICAgICAgdmFyIF90aGlzOCA9IHRoaXM7CiAgICAgIHZhciBzdGF0ZSA9IDE7CiAgICAgIGlmIChyb3cuc3VzcGVuc2lvblN0YXRlID09PSAxKSB7CiAgICAgICAgc3RhdGUgPSAyOwogICAgICB9CiAgICAgIHZhciBwYXJhbXMgPSB7CiAgICAgICAgZGVwbG95SWQ6IHJvdy5kZXBsb3ltZW50SWQsCiAgICAgICAgc3RhdGU6IHN0YXRlCiAgICAgIH07CiAgICAgIHVwZGF0ZVN0YXRlKHBhcmFtcykudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgX3RoaXM4Lm1zZ1N1Y2Nlc3MocmVzLm1zZyk7CiAgICAgICAgX3RoaXM4LmdldExpc3QoKTsKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOS/ruaUueaMiemSruaTjeS9nCAqL2hhbmRsZVVwZGF0ZTogZnVuY3Rpb24gaGFuZGxlVXBkYXRlKHJvdykgewogICAgICB2YXIgX3RoaXM5ID0gdGhpczsKICAgICAgdGhpcy5yZXNldCgpOwogICAgICB2YXIgaWQgPSByb3cuaWQgfHwgdGhpcy5pZHM7CiAgICAgIGdldERlcGxveW1lbnQoaWQpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgX3RoaXM5LmZvcm0gPSByZXNwb25zZS5kYXRhOwogICAgICAgIF90aGlzOS5vcGVuID0gdHJ1ZTsKICAgICAgICBfdGhpczkudGl0bGUgPSAi5L+u5pS55rWB56iL5a6a5LmJIjsKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOaPkOS6pOaMiemSriAqL3N1Ym1pdEZvcm06IGZ1bmN0aW9uIHN1Ym1pdEZvcm0oKSB7CiAgICAgIHZhciBfdGhpczEwID0gdGhpczsKICAgICAgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKGZ1bmN0aW9uICh2YWxpZCkgewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgaWYgKF90aGlzMTAuZm9ybS5pZCAhPSBudWxsKSB7CiAgICAgICAgICAgIHVwZGF0ZURlcGxveW1lbnQoX3RoaXMxMC5mb3JtKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgICAgICAgIF90aGlzMTAubXNnU3VjY2Vzcygi5L+u5pS55oiQ5YqfIik7CiAgICAgICAgICAgICAgX3RoaXMxMC5vcGVuID0gZmFsc2U7CiAgICAgICAgICAgICAgX3RoaXMxMC5nZXRMaXN0KCk7CiAgICAgICAgICAgIH0pOwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgYWRkRGVwbG95bWVudChfdGhpczEwLmZvcm0pLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgICAgICAgX3RoaXMxMC5tc2dTdWNjZXNzKCLmlrDlop7miJDlip8iKTsKICAgICAgICAgICAgICBfdGhpczEwLm9wZW4gPSBmYWxzZTsKICAgICAgICAgICAgICBfdGhpczEwLmdldExpc3QoKTsKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvKiog5Yig6Zmk5oyJ6ZKu5pON5L2cICovaGFuZGxlRGVsZXRlOiBmdW5jdGlvbiBoYW5kbGVEZWxldGUocm93KSB7CiAgICAgIHZhciBfdGhpczExID0gdGhpczsKICAgICAgLy8gY29uc3QgaWRzID0gcm93LmRlcGxveW1lbnRJZCB8fCB0aGlzLmlkczsKICAgICAgdmFyIHBhcmFtcyA9IHsKICAgICAgICBkZXBsb3lJZDogcm93LmRlcGxveW1lbnRJZAogICAgICB9OwogICAgICB0aGlzLiRjb25maXJtKCfmmK/lkKbnoa7orqTliKDpmaTmtYHnqIvlrprkuYnnvJblj7fkuLoiJyArIHBhcmFtcy5kZXBsb3lJZCArICci55qE5pWw5o2u6aG5PycsICLorablkYoiLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLAogICAgICAgIHR5cGU6ICJ3YXJuaW5nIgogICAgICB9KS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICByZXR1cm4gZGVsRGVwbG95bWVudChwYXJhbXMpOwogICAgICB9KS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICBfdGhpczExLmdldExpc3QoKTsKICAgICAgICBfdGhpczExLm1zZ1N1Y2Nlc3MoIuWIoOmZpOaIkOWKnyIpOwogICAgICB9KTsKICAgIH0sCiAgICAvKiog5a+85Ye65oyJ6ZKu5pON5L2cICovaGFuZGxlRXhwb3J0OiBmdW5jdGlvbiBoYW5kbGVFeHBvcnQoKSB7CiAgICAgIHZhciBfdGhpczEyID0gdGhpczsKICAgICAgdmFyIHF1ZXJ5UGFyYW1zID0gdGhpcy5xdWVyeVBhcmFtczsKICAgICAgdGhpcy4kY29uZmlybSgn5piv5ZCm56Gu6K6k5a+85Ye65omA5pyJ5rWB56iL5a6a5LmJ5pWw5o2u6aG5PycsICLorablkYoiLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLAogICAgICAgIHR5cGU6ICJ3YXJuaW5nIgogICAgICB9KS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICByZXR1cm4gZXhwb3J0RGVwbG95bWVudChxdWVyeVBhcmFtcyk7CiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgX3RoaXMxMi5kb3dubG9hZChyZXNwb25zZS5tc2cpOwogICAgICB9KTsKICAgIH0sCiAgICAvKiog5a+85YWlYnBtbi54bWzmlofku7YgKi9oYW5kbGVJbXBvcnQ6IGZ1bmN0aW9uIGhhbmRsZUltcG9ydCgpIHsKICAgICAgdGhpcy51cGxvYWQudGl0bGUgPSAiYnBtbjIwLnhtbOaWh+S7tuWvvOWFpSI7CiAgICAgIHRoaXMudXBsb2FkLm9wZW4gPSB0cnVlOwogICAgfSwKICAgIC8vIOaWh+S7tuS4iuS8oOS4reWkhOeQhgogICAgaGFuZGxlRmlsZVVwbG9hZFByb2dyZXNzOiBmdW5jdGlvbiBoYW5kbGVGaWxlVXBsb2FkUHJvZ3Jlc3MoZXZlbnQsIGZpbGUsIGZpbGVMaXN0KSB7CiAgICAgIHRoaXMudXBsb2FkLmlzVXBsb2FkaW5nID0gdHJ1ZTsKICAgIH0sCiAgICAvLyDmlofku7bkuIrkvKDmiJDlip/lpITnkIYKICAgIGhhbmRsZUZpbGVTdWNjZXNzOiBmdW5jdGlvbiBoYW5kbGVGaWxlU3VjY2VzcyhyZXNwb25zZSwgZmlsZSwgZmlsZUxpc3QpIHsKICAgICAgdGhpcy51cGxvYWQub3BlbiA9IGZhbHNlOwogICAgICB0aGlzLnVwbG9hZC5pc1VwbG9hZGluZyA9IGZhbHNlOwogICAgICB0aGlzLiRyZWZzLnVwbG9hZC5jbGVhckZpbGVzKCk7CiAgICAgIHRoaXMuJG1lc3NhZ2UocmVzcG9uc2UubXNnKTsKICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICB9LAogICAgLy8g5o+Q5Lqk5LiK5Lyg5paH5Lu2CiAgICBzdWJtaXRGaWxlRm9ybTogZnVuY3Rpb24gc3VibWl0RmlsZUZvcm0oKSB7CiAgICAgIHRoaXMuJHJlZnMudXBsb2FkLnN1Ym1pdCgpOwogICAgfQogIH0KfTs="}, {"version": 3, "names": ["listDefinition", "updateState", "delDeployment", "addDeployment", "updateDeployment", "exportDeployment", "definitionStart", "readXml", "getToken", "getForm", "addDeployForm", "listForm", "<PERSON><PERSON><PERSON>", "flow", "name", "components", "data", "loading", "ids", "single", "multiple", "showSearch", "total", "definitionList", "title", "open", "formConfOpen", "formTitle", "formDeployOpen", "formDeployTitle", "formList", "formTotal", "formConf", "readImage", "src", "upload", "isUploading", "category", "headers", "Authorization", "url", "process", "env", "VUE_APP_BASE_API", "queryParams", "pageNum", "pageSize", "key", "tenantId", "deployTime", "derivedFrom", "derivedFromRoot", "parentDeploymentId", "engineVersion", "formQueryParams", "formDeployParam", "formId", "deployId", "currentRow", "xmlData", "form", "rules", "processCategoryOptions", "activated", "getList", "created", "_this", "getDicts", "then", "response", "methods", "_this2", "records", "processCategoryFormat", "row", "column", "selectDictLabel", "cancel", "reset", "id", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "map", "item", "length", "handleAdd", "handleLoadXml", "$router", "push", "path", "query", "deploymentId", "handleReadImage", "_this3", "res", "handleForm", "_this4", "JSON", "parse", "formContent", "handleDefinitionStart", "_this5", "msgSuccess", "msg", "handleAddForm", "ListFormDeploy", "_this6", "rows", "submitFormDeploy", "_this7", "handleCurrentChange", "handleUpdateSuspensionState", "_this8", "state", "suspensionState", "params", "handleUpdate", "_this9", "getDeployment", "submitForm", "_this10", "$refs", "validate", "valid", "handleDelete", "_this11", "$confirm", "confirmButtonText", "cancelButtonText", "type", "handleExport", "_this12", "download", "handleImport", "handleFileUploadProgress", "event", "file", "fileList", "handleFileSuccess", "clearFiles", "$message", "submitFileForm", "submit"], "sources": ["src/views/flowable/definition/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"名称\" prop=\"name\">\n        <el-input\n          v-model=\"queryParams.name\"\n          placeholder=\"请输入名称\"\n          clearable\n          size=\"small\"\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"开始时间\" prop=\"deployTime\">\n        <el-date-picker clearable size=\"small\"\n                        v-model=\"queryParams.deployTime\"\n                        type=\"date\"\n                        value-format=\"yyyy-MM-dd\"\n                        placeholder=\"选择时间\">\n        </el-date-picker>\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"primary\"\n          plain\n          icon=\"el-icon-upload\"\n          size=\"mini\"\n          @click=\"handleImport\"\n        >导入</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"success\"\n          plain\n          icon=\"el-icon-plus\"\n          size=\"mini\"\n          @click=\"handleLoadXml\"\n        >新增</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"danger\"\n          plain\n          icon=\"el-icon-delete\"\n          size=\"mini\"\n          :disabled=\"multiple\"\n          @click=\"handleDelete\"\n          v-hasPermi=\"['system:deployment:remove']\"\n        >删除</el-button>\n      </el-col>\n      <!-- <el-col :span=\"1.5\">\n        <el-button\n          type=\"warning\"\n          plain\n          icon=\"el-icon-download\"\n          size=\"mini\"\n          @click=\"handleExport\"\n          v-hasPermi=\"['system:deployment:export']\"\n        >导出</el-button>\n      </el-col> -->\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <el-table v-loading=\"loading\" fit :data=\"definitionList\" border   @selection-change=\"handleSelectionChange\">\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column label=\"流程编号\" align=\"center\" prop=\"deploymentId\" :show-overflow-tooltip=\"true\"/>\n      <el-table-column label=\"流程标识\" align=\"center\" prop=\"key\" :show-overflow-tooltip=\"true\" />\n      <el-table-column label=\"流程分类\" align=\"center\" prop=\"category\" :formatter=\"processCategoryFormat\" />\n      <el-table-column label=\"流程名称\" align=\"center\" :show-overflow-tooltip=\"true\">\n        <template slot-scope=\"scope\">\n          <el-button type=\"text\" @click=\"handleReadImage(scope.row.deploymentId)\">\n            <span>{{ scope.row.name }}</span>\n          </el-button>\n        </template>\n      </el-table-column>\n      <!-- <el-table-column label=\"业务表单\" align=\"center\" :show-overflow-tooltip=\"true\">\n        <template slot-scope=\"scope\">\n          <el-button v-if=\"scope.row.formId\" type=\"text\" @click=\"handleForm(scope.row.formId)\">\n            <span>{{ scope.row.formName }}</span>\n          </el-button>\n          <label v-else>暂无表单</label>\n        </template>\n      </el-table-column> -->\n      <el-table-column label=\"流程版本\" align=\"center\">\n        <template slot-scope=\"scope\">\n          <el-tag size=\"medium\" >v{{ scope.row.version }}</el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"状态\" align=\"center\">\n        <template slot-scope=\"scope\">\n          <el-tag type=\"success\" v-if=\"scope.row.suspensionState === 1\">激活</el-tag>\n          <el-tag type=\"warning\" v-if=\"scope.row.suspensionState === 2\">挂起</el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"部署时间\" align=\"center\" prop=\"deploymentTime\" width=\"180\"/>\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"scope\">\n          <el-dropdown>\n            <span class=\"el-dropdown-link\">\n              更多操作<i class=\"el-icon-arrow-down el-icon--right\"></i>\n            </span>\n            <el-dropdown-menu slot=\"dropdown\">\n              <el-dropdown-item icon=\"el-icon-edit-outline\" @click.native=\"handleLoadXml(scope.row)\">\n                编辑\n              </el-dropdown-item>\n              <!-- <el-dropdown-item icon=\"el-icon-connection\" @click.native=\"handleAddForm(scope.row)\" v if=\"scope.row.formId == null\">\n                配置表单\n              </el-dropdown-item> -->\n              <el-dropdown-item icon=\"el-icon-video-pause\" @click.native=\"handleUpdateSuspensionState(scope.row)\" v-if=\"scope.row.suspensionState === 1\">\n                挂起\n              </el-dropdown-item>\n              <el-dropdown-item icon=\"el-icon-video-play\" @click.native=\"handleUpdateSuspensionState(scope.row)\" v-if=\"scope.row.suspensionState === 2\">\n                激活\n              </el-dropdown-item>\n              <el-dropdown-item icon=\"el-icon-delete\" @click.native=\"handleDelete(scope.row)\" v-hasPermi=\"['system:deployment:remove']\">\n                删除\n              </el-dropdown-item>\n            </el-dropdown-menu>\n          </el-dropdown>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 添加或修改流程定义对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"500px\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\n        <el-form-item label=\"看看\" prop=\"name\">\n          <el-input v-model=\"form.name\" placeholder=\"请输入看看\" />\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n\n    <!-- bpmn20.xml导入对话框 -->\n    <el-dialog :title=\"upload.title\" :visible.sync=\"upload.open\" width=\"400px\" append-to-body>\n      <el-upload\n        ref=\"upload\"\n        :limit=\"1\"\n        accept=\".xml\"\n        :headers=\"upload.headers\"\n        :action=\"upload.url + '?name=' + upload.name+'&category='+ upload.category\"\n        :disabled=\"upload.isUploading\"\n        :on-progress=\"handleFileUploadProgress\"\n        :on-success=\"handleFileSuccess\"\n        :auto-upload=\"false\"\n        drag\n      >\n        <i class=\"el-icon-upload\"></i>\n        <div class=\"el-upload__text\">\n          将文件拖到此处，或\n          <em>点击上传</em>\n        </div>\n        <div class=\"el-upload__tip\" slot=\"tip\">\n          流程名称：<el-input v-model=\"upload.name\"/>\n          流程分类：<el-input v-model=\"upload.category\"/>\n        </div>\n        <div class=\"el-upload__tip\" style=\"color:red\" slot=\"tip\">提示：仅允许导入“bpmn20.xml”格式文件！</div>\n      </el-upload>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitFileForm\">确 定</el-button>\n        <el-button @click=\"upload.open = false\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 流程图 -->\n    <el-dialog :title=\"readImage.title\" :visible.sync=\"readImage.open\" width=\"70%\" append-to-body>\n      <!-- <el-image :src=\"readImage.src\"></el-image> -->\n       <flow :xmlData=\"xmlData\"/>\n    </el-dialog>\n\n    <!--表单配置详情-->\n    <el-dialog :title=\"formTitle\" :visible.sync=\"formConfOpen\" width=\"50%\" append-to-body>\n      <div class=\"test-form\">\n        <parser :key=\"new Date().getTime()\"  :form-conf=\"formConf\" />\n      </div>\n    </el-dialog>\n\n    <!--挂载表单-->\n    <el-dialog :title=\"formDeployTitle\" :visible.sync=\"formDeployOpen\" width=\"60%\" append-to-body>\n      <el-row :gutter=\"24\">\n        <el-col :span=\"10\" :xs=\"24\">\n          <el-table\n            ref=\"singleTable\"\n            :data=\"formList\"\n            border\n            highlight-current-row\n            @current-change=\"handleCurrentChange\"\n            style=\"width: 100%\">\n            <el-table-column label=\"表单编号\" align=\"center\" prop=\"formId\" />\n            <el-table-column label=\"表单名称\" align=\"center\" prop=\"formName\" />\n            <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\n              <template slot-scope=\"scope\">\n                <el-button size=\"mini\" type=\"text\" @click=\"submitFormDeploy(scope.row)\">确定</el-button>\n              </template>\n            </el-table-column>\n          </el-table>\n\n          <pagination\n            small\n            layout=\"prev, pager, next\"\n            v-show=\"formTotal>0\"\n            :total=\"formTotal\"\n            :page.sync=\"formQueryParams.pageNum\"\n            :limit.sync=\"formQueryParams.pageSize\"\n            @pagination=\"ListFormDeploy\"\n          />\n        </el-col>\n        <el-col :span=\"14\" :xs=\"24\">\n          <div v-if=\"currentRow\">\n            <parser :key=\"new Date().getTime()\" :form-conf=\"currentRow\" />\n          </div>\n        </el-col>\n      </el-row>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { listDefinition, updateState, delDeployment, addDeployment, updateDeployment, exportDeployment, definitionStart, readXml} from \"@/api/flowable/definition\";\nimport { getToken } from \"@/utils/auth\";\nimport { getForm, addDeployForm ,listForm } from \"@/api/flowable/form\";\nimport Parser from '@/components/parser/Parser'\nimport flow from '@/views/flowable/task/record/flow'\n\nexport default {\n  name: \"Definition\",\n  components: {\n    Parser,\n    flow\n  },\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 流程定义表格数据\n      definitionList: [],\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      open: false,\n      formConfOpen: false,\n      formTitle: \"\",\n      formDeployOpen: false,\n      formDeployTitle: \"\",\n      formList: [],\n      formTotal:0,\n      formConf: {}, // 默认表单数据\n      readImage:{\n        open: false,\n        src: \"\",\n      },\n      // bpmn.xml 导入\n      upload: {\n        // 是否显示弹出层（xml导入）\n        open: false,\n        // 弹出层标题（xml导入）\n        title: \"\",\n        // 是否禁用上传\n        isUploading: false,\n        name: null,\n        category: null,\n        // 设置上传的请求头部\n        headers: { Authorization: \"Bearer \" + getToken() },\n        // 上传的地址\n        url: process.env.VUE_APP_BASE_API + \"/flowable/definition/import\"\n      },\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        name: null,\n        category: null,\n        key: null,\n        tenantId: null,\n        deployTime: null,\n        derivedFrom: null,\n        derivedFromRoot: null,\n        parentDeploymentId: null,\n        engineVersion: null\n      },\n      formQueryParams:{\n        pageNum: 1,\n        pageSize: 10,\n      },\n      // 挂载表单到流程实例\n      formDeployParam:{\n        formId: null,\n        deployId: null\n      },\n      currentRow: null,\n      // xml\n      xmlData:\"\",\n      // 表单参数\n      form: {},\n      // 表单校验\n      rules: {\n      },\n      processCategoryOptions: []\n    };\n  },\n  activated(){\n    this.getList();\n  },\n  created() {\n    this.getList();\n    this.getDicts(\"sys_process_category\").then((response) => {\n      this.processCategoryOptions = response.data;\n    });\n  },\n  methods: {\n    /** 查询流程定义列表 */\n    getList() {\n      this.loading = true;\n      listDefinition(this.queryParams).then(response => {\n        this.definitionList = response.data.records;\n        this.total = response.data.total;\n        this.loading = false;\n      });\n    },\n    // 编辑状态字典翻译\n    processCategoryFormat(row, column) {\n      return this.selectDictLabel(this.processCategoryOptions, row.category);\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        id: null,\n        name: null,\n        category: null,\n        key: null,\n        tenantId: null,\n        deployTime: null,\n        derivedFrom: null,\n        derivedFromRoot: null,\n        parentDeploymentId: null,\n        engineVersion: null\n      };\n      this.resetForm(\"form\");\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.id)\n      this.single = selection.length!==1\n      this.multiple = !selection.length\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.reset();\n      this.open = true;\n      this.title = \"添加流程定义\";\n    },\n    /** 跳转到流程设计页面 */\n    handleLoadXml(row){\n      this.$router.push({ path: '/flowable/definition/model',query: { deployId: row.deploymentId }})\n    },\n    /** 流程图查看 */\n    handleReadImage(deploymentId){\n      this.readImage.title = \"流程图\";\n      this.readImage.open = true;\n      // this.readImage.src = process.env.VUE_APP_BASE_API + \"/flowable/definition/readImage/\" + deploymentId;\n       // 发送请求，获取xml\n      readXml(deploymentId).then(res =>{\n        this.xmlData = res.data\n      })\n    },\n    /** 表单查看 */\n    handleForm(formId){\n      getForm(formId).then(res =>{\n        this.formTitle = \"表单详情\";\n        this.formConfOpen = true;\n        this.formConf = JSON.parse(res.data.formContent)\n      })\n    },\n    /** 启动流程 */\n    handleDefinitionStart(row){\n      definitionStart(row.id).then(res =>{\n        this.msgSuccess(res.msg);\n      })\n    },\n    /** 挂载表单弹框 */\n    handleAddForm(row){\n      this.formDeployParam.deployId = row.deploymentId\n      this.ListFormDeploy()\n    },\n    /** 挂载表单列表 */\n    ListFormDeploy(){\n      listForm(this.formQueryParams).then(res =>{\n        this.formList = res.rows;\n        this.formTotal = res.total;\n        this.formDeployOpen = true;\n        this.formDeployTitle = \"挂载表单\";\n      })\n    },\n    // /** 更改挂载表单弹框 */\n    // handleEditForm(row){\n    //   this.formDeployParam.deployId = row.deploymentId\n    //   const queryParams = {\n    //     pageNum: 1,\n    //     pageSize: 10\n    //   }\n    //   listForm(queryParams).then(res =>{\n    //     this.formList = res.rows;\n    //     this.formDeployOpen = true;\n    //     this.formDeployTitle = \"挂载表单\";\n    //   })\n    // },\n    /** 挂载表单 */\n    submitFormDeploy(row){\n      this.formDeployParam.formId = row.formId;\n      addDeployForm(this.formDeployParam).then(res =>{\n        this.msgSuccess(res.msg);\n        this.formDeployOpen = false;\n        this.getList();\n      })\n    },\n    handleCurrentChange(data) {\n      if (data) {\n        this.currentRow = JSON.parse(data.formContent);\n      }\n    },\n    /** 挂起/激活流程 */\n    handleUpdateSuspensionState(row){\n      let state = 1;\n      if (row.suspensionState === 1) {\n          state = 2\n      }\n      const params = {\n        deployId: row.deploymentId,\n        state: state\n      }\n      updateState(params).then(res => {\n        this.msgSuccess(res.msg);\n        this.getList();\n      });\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset();\n      const id = row.id || this.ids\n      getDeployment(id).then(response => {\n        this.form = response.data;\n        this.open = true;\n        this.title = \"修改流程定义\";\n      });\n    },\n    /** 提交按钮 */\n    submitForm() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          if (this.form.id != null) {\n            updateDeployment(this.form).then(response => {\n              this.msgSuccess(\"修改成功\");\n              this.open = false;\n              this.getList();\n            });\n          } else {\n            addDeployment(this.form).then(response => {\n              this.msgSuccess(\"新增成功\");\n              this.open = false;\n              this.getList();\n            });\n          }\n        }\n      });\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      // const ids = row.deploymentId || this.ids;\n      const params = {\n        deployId: row.deploymentId\n      }\n      this.$confirm('是否确认删除流程定义编号为\"' + params.deployId + '\"的数据项?', \"警告\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      }).then(function() {\n        return delDeployment(params);\n      }).then(() => {\n        this.getList();\n        this.msgSuccess(\"删除成功\");\n      })\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      const queryParams = this.queryParams;\n      this.$confirm('是否确认导出所有流程定义数据项?', \"警告\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      }).then(function() {\n        return exportDeployment(queryParams);\n      }).then(response => {\n        this.download(response.msg);\n      })\n    },\n    /** 导入bpmn.xml文件 */\n    handleImport(){\n      this.upload.title = \"bpmn20.xml文件导入\";\n      this.upload.open = true;\n    },\n    // 文件上传中处理\n    handleFileUploadProgress(event, file, fileList) {\n      this.upload.isUploading = true;\n    },\n    // 文件上传成功处理\n    handleFileSuccess(response, file, fileList) {\n      this.upload.open = false;\n      this.upload.isUploading = false;\n      this.$refs.upload.clearFiles();\n      this.$message(response.msg);\n      this.getList();\n    },\n    // 提交上传文件\n    submitFileForm() {\n      this.$refs.upload.submit();\n    }\n  }\n};\n</script>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4OA,SAAAA,cAAA,EAAAC,WAAA,EAAAC,aAAA,EAAAC,aAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,eAAA,EAAAC,OAAA;AACA,SAAAC,QAAA;AACA,SAAAC,OAAA,EAAAC,aAAA,EAAAC,QAAA;AACA,OAAAC,MAAA;AACA,OAAAC,IAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAH,MAAA,EAAAA,MAAA;IACAC,IAAA,EAAAA;EACA;EACAG,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,cAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACAC,YAAA;MACAC,SAAA;MACAC,cAAA;MACAC,eAAA;MACAC,QAAA;MACAC,SAAA;MACAC,QAAA;MAAA;MACAC,SAAA;QACAR,IAAA;QACAS,GAAA;MACA;MACA;MACAC,MAAA;QACA;QACAV,IAAA;QACA;QACAD,KAAA;QACA;QACAY,WAAA;QACAtB,IAAA;QACAuB,QAAA;QACA;QACAC,OAAA;UAAAC,aAAA,cAAA/B,QAAA;QAAA;QACA;QACAgC,GAAA,EAAAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA;MACA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAhC,IAAA;QACAuB,QAAA;QACAU,GAAA;QACAC,QAAA;QACAC,UAAA;QACAC,WAAA;QACAC,eAAA;QACAC,kBAAA;QACAC,aAAA;MACA;MACAC,eAAA;QACAT,OAAA;QACAC,QAAA;MACA;MACA;MACAS,eAAA;QACAC,MAAA;QACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,OAAA;MACA;MACAC,IAAA;MACA;MACAC,KAAA,GACA;MACAC,sBAAA;IACA;EACA;EACAC,SAAA,WAAAA,UAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA,KAAAF,OAAA;IACA,KAAAG,QAAA,yBAAAC,IAAA,WAAAC,QAAA;MACAH,KAAA,CAAAJ,sBAAA,GAAAO,QAAA,CAAArD,IAAA;IACA;EACA;EACAsD,OAAA;IACA,eACAN,OAAA,WAAAA,QAAA;MAAA,IAAAO,MAAA;MACA,KAAAtD,OAAA;MACAjB,cAAA,MAAA4C,WAAA,EAAAwB,IAAA,WAAAC,QAAA;QACAE,MAAA,CAAAhD,cAAA,GAAA8C,QAAA,CAAArD,IAAA,CAAAwD,OAAA;QACAD,MAAA,CAAAjD,KAAA,GAAA+C,QAAA,CAAArD,IAAA,CAAAM,KAAA;QACAiD,MAAA,CAAAtD,OAAA;MACA;IACA;IACA;IACAwD,qBAAA,WAAAA,sBAAAC,GAAA,EAAAC,MAAA;MACA,YAAAC,eAAA,MAAAd,sBAAA,EAAAY,GAAA,CAAArC,QAAA;IACA;IACA;IACAwC,MAAA,WAAAA,OAAA;MACA,KAAApD,IAAA;MACA,KAAAqD,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAAlB,IAAA;QACAmB,EAAA;QACAjE,IAAA;QACAuB,QAAA;QACAU,GAAA;QACAC,QAAA;QACAC,UAAA;QACAC,WAAA;QACAC,eAAA;QACAC,kBAAA;QACAC,aAAA;MACA;MACA,KAAA2B,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAArC,WAAA,CAAAC,OAAA;MACA,KAAAmB,OAAA;IACA;IACA,aACAkB,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAAC,WAAA;IACA;IACA;IACAE,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAlE,GAAA,GAAAkE,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAP,EAAA;MAAA;MACA,KAAA5D,MAAA,GAAAiE,SAAA,CAAAG,MAAA;MACA,KAAAnE,QAAA,IAAAgE,SAAA,CAAAG,MAAA;IACA;IACA,aACAC,SAAA,WAAAA,UAAA;MACA,KAAAV,KAAA;MACA,KAAArD,IAAA;MACA,KAAAD,KAAA;IACA;IACA,gBACAiE,aAAA,WAAAA,cAAAf,GAAA;MACA,KAAAgB,OAAA,CAAAC,IAAA;QAAAC,IAAA;QAAAC,KAAA;UAAApC,QAAA,EAAAiB,GAAA,CAAAoB;QAAA;MAAA;IACA;IACA,YACAC,eAAA,WAAAA,gBAAAD,YAAA;MAAA,IAAAE,MAAA;MACA,KAAA/D,SAAA,CAAAT,KAAA;MACA,KAAAS,SAAA,CAAAR,IAAA;MACA;MACA;MACAlB,OAAA,CAAAuF,YAAA,EAAA1B,IAAA,WAAA6B,GAAA;QACAD,MAAA,CAAArC,OAAA,GAAAsC,GAAA,CAAAjF,IAAA;MACA;IACA;IACA,WACAkF,UAAA,WAAAA,WAAA1C,MAAA;MAAA,IAAA2C,MAAA;MACA1F,OAAA,CAAA+C,MAAA,EAAAY,IAAA,WAAA6B,GAAA;QACAE,MAAA,CAAAxE,SAAA;QACAwE,MAAA,CAAAzE,YAAA;QACAyE,MAAA,CAAAnE,QAAA,GAAAoE,IAAA,CAAAC,KAAA,CAAAJ,GAAA,CAAAjF,IAAA,CAAAsF,WAAA;MACA;IACA;IACA,WACAC,qBAAA,WAAAA,sBAAA7B,GAAA;MAAA,IAAA8B,MAAA;MACAlG,eAAA,CAAAoE,GAAA,CAAAK,EAAA,EAAAX,IAAA,WAAA6B,GAAA;QACAO,MAAA,CAAAC,UAAA,CAAAR,GAAA,CAAAS,GAAA;MACA;IACA;IACA,aACAC,aAAA,WAAAA,cAAAjC,GAAA;MACA,KAAAnB,eAAA,CAAAE,QAAA,GAAAiB,GAAA,CAAAoB,YAAA;MACA,KAAAc,cAAA;IACA;IACA,aACAA,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MACAlG,QAAA,MAAA2C,eAAA,EAAAc,IAAA,WAAA6B,GAAA;QACAY,MAAA,CAAA/E,QAAA,GAAAmE,GAAA,CAAAa,IAAA;QACAD,MAAA,CAAA9E,SAAA,GAAAkE,GAAA,CAAA3E,KAAA;QACAuF,MAAA,CAAAjF,cAAA;QACAiF,MAAA,CAAAhF,eAAA;MACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAkF,gBAAA,WAAAA,iBAAArC,GAAA;MAAA,IAAAsC,MAAA;MACA,KAAAzD,eAAA,CAAAC,MAAA,GAAAkB,GAAA,CAAAlB,MAAA;MACA9C,aAAA,MAAA6C,eAAA,EAAAa,IAAA,WAAA6B,GAAA;QACAe,MAAA,CAAAP,UAAA,CAAAR,GAAA,CAAAS,GAAA;QACAM,MAAA,CAAApF,cAAA;QACAoF,MAAA,CAAAhD,OAAA;MACA;IACA;IACAiD,mBAAA,WAAAA,oBAAAjG,IAAA;MACA,IAAAA,IAAA;QACA,KAAA0C,UAAA,GAAA0C,IAAA,CAAAC,KAAA,CAAArF,IAAA,CAAAsF,WAAA;MACA;IACA;IACA,cACAY,2BAAA,WAAAA,4BAAAxC,GAAA;MAAA,IAAAyC,MAAA;MACA,IAAAC,KAAA;MACA,IAAA1C,GAAA,CAAA2C,eAAA;QACAD,KAAA;MACA;MACA,IAAAE,MAAA;QACA7D,QAAA,EAAAiB,GAAA,CAAAoB,YAAA;QACAsB,KAAA,EAAAA;MACA;MACAnH,WAAA,CAAAqH,MAAA,EAAAlD,IAAA,WAAA6B,GAAA;QACAkB,MAAA,CAAAV,UAAA,CAAAR,GAAA,CAAAS,GAAA;QACAS,MAAA,CAAAnD,OAAA;MACA;IACA;IACA,aACAuD,YAAA,WAAAA,aAAA7C,GAAA;MAAA,IAAA8C,MAAA;MACA,KAAA1C,KAAA;MACA,IAAAC,EAAA,GAAAL,GAAA,CAAAK,EAAA,SAAA7D,GAAA;MACAuG,aAAA,CAAA1C,EAAA,EAAAX,IAAA,WAAAC,QAAA;QACAmD,MAAA,CAAA5D,IAAA,GAAAS,QAAA,CAAArD,IAAA;QACAwG,MAAA,CAAA/F,IAAA;QACA+F,MAAA,CAAAhG,KAAA;MACA;IACA;IACA,WACAkG,UAAA,WAAAA,WAAA;MAAA,IAAAC,OAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAH,OAAA,CAAA/D,IAAA,CAAAmB,EAAA;YACA3E,gBAAA,CAAAuH,OAAA,CAAA/D,IAAA,EAAAQ,IAAA,WAAAC,QAAA;cACAsD,OAAA,CAAAlB,UAAA;cACAkB,OAAA,CAAAlG,IAAA;cACAkG,OAAA,CAAA3D,OAAA;YACA;UACA;YACA7D,aAAA,CAAAwH,OAAA,CAAA/D,IAAA,EAAAQ,IAAA,WAAAC,QAAA;cACAsD,OAAA,CAAAlB,UAAA;cACAkB,OAAA,CAAAlG,IAAA;cACAkG,OAAA,CAAA3D,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACA+D,YAAA,WAAAA,aAAArD,GAAA;MAAA,IAAAsD,OAAA;MACA;MACA,IAAAV,MAAA;QACA7D,QAAA,EAAAiB,GAAA,CAAAoB;MACA;MACA,KAAAmC,QAAA,oBAAAX,MAAA,CAAA7D,QAAA;QACAyE,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAAhE,IAAA;QACA,OAAAlE,aAAA,CAAAoH,MAAA;MACA,GAAAlD,IAAA;QACA4D,OAAA,CAAAhE,OAAA;QACAgE,OAAA,CAAAvB,UAAA;MACA;IACA;IACA,aACA4B,YAAA,WAAAA,aAAA;MAAA,IAAAC,OAAA;MACA,IAAA1F,WAAA,QAAAA,WAAA;MACA,KAAAqF,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAAhE,IAAA;QACA,OAAA/D,gBAAA,CAAAuC,WAAA;MACA,GAAAwB,IAAA,WAAAC,QAAA;QACAiE,OAAA,CAAAC,QAAA,CAAAlE,QAAA,CAAAqC,GAAA;MACA;IACA;IACA,mBACA8B,YAAA,WAAAA,aAAA;MACA,KAAArG,MAAA,CAAAX,KAAA;MACA,KAAAW,MAAA,CAAAV,IAAA;IACA;IACA;IACAgH,wBAAA,WAAAA,yBAAAC,KAAA,EAAAC,IAAA,EAAAC,QAAA;MACA,KAAAzG,MAAA,CAAAC,WAAA;IACA;IACA;IACAyG,iBAAA,WAAAA,kBAAAxE,QAAA,EAAAsE,IAAA,EAAAC,QAAA;MACA,KAAAzG,MAAA,CAAAV,IAAA;MACA,KAAAU,MAAA,CAAAC,WAAA;MACA,KAAAwF,KAAA,CAAAzF,MAAA,CAAA2G,UAAA;MACA,KAAAC,QAAA,CAAA1E,QAAA,CAAAqC,GAAA;MACA,KAAA1C,OAAA;IACA;IACA;IACAgF,cAAA,WAAAA,eAAA;MACA,KAAApB,KAAA,CAAAzF,MAAA,CAAA8G,MAAA;IACA;EACA;AACA", "ignoreList": []}]}