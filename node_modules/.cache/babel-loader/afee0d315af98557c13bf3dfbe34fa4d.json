{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Process/components/nodePanel/property/signal.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Process/components/nodePanel/property/signal.vue", "mtime": 1641277326000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["mixinPanel", "mixins", "data", "dialogVisible", "formData", "signal", "computed", "formConfig", "inline", "item", "xType", "tabs", "label", "name", "column", "width", "rules", "required", "message", "trigger", "dic", "value", "mounted", "methods", "updateElement", "_this$formData$signal", "length", "extensionElements", "element", "businessObject", "get", "modeler", "create", "i", "pop", "updateProperties", "_extensionElements$va", "values", "filter", "$type", "closeDialog", "_this", "$refs", "xForm", "validate", "then", "catch", "e", "console", "error"], "sources": ["src/components/Process/components/nodePanel/property/signal.vue"], "sourcesContent": ["<template>\n  <div>\n    <el-dialog\n      title=\"信号定义\"\n      :visible.sync=\"dialogVisible\"\n      width=\"700px\"\n      :close-on-click-modal=\"false\"\n      :close-on-press-escape=\"false\"\n      :show-close=\"false\"\n      @closed=\"$emit('close')\"\n    >\n      <x-form ref=\"xForm\" v-model=\"formData\" :config=\"formConfig\" />\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" size=\"medium\" @click=\"closeDialog\">确 定</el-button>\n      </span>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport mixinPanel from '../../../common/mixinPanel'\nexport default {\n  mixins: [mixinPanel],\n  data() {\n    return {\n      dialogVisible: true,\n      formData: {\n        signal: []\n      }\n    }\n  },\n  computed: {\n    formConfig() {\n    //   const _this = this\n      return {\n        inline: false,\n        item: [\n          {\n            xType: 'tabs',\n            tabs: [\n              {\n                label: '信号定义',\n                name: 'signal',\n                column: [\n                  {\n                    label: 'scope',\n                    name: 'scope',\n                    width: 180,\n                    rules: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],\n                    xType: 'select',\n                    dic: [\n                      { label: '全局', value: 'start' },\n                      { label: '流程实例', value: 'end' }\n                    ]\n                  },\n                  {\n                    label: 'id',\n                    name: 'id',\n                    width: 200,\n                    rules: [{ required: true, message: '请输入', trigger: ['blur', 'change'] }],\n                    xType: 'input'\n                  },\n                  {\n                    label: '名称',\n                    name: 'name',\n                    xType: 'input',\n                    rules: [{ required: true, message: '请输入', trigger: ['blur', 'change'] }]\n                  }\n                ]\n              }\n            ]\n          }\n        ]\n      }\n    }\n  },\n  mounted() {\n    // this.formData.signal = this.element.businessObject.extensionElements?.values.map(item => {\n    //   let type\n    //   if ('class' in item.$attrs) type = 'class'\n    //   if ('expression' in item.$attrs) type = 'expression'\n    //   if ('delegateExpression' in item.$attrs) type = 'delegateExpression'\n    //   return {\n    //     event: item.$attrs.event,\n    //     type: type,\n    //     className: item.$attrs[type]\n    //   }\n    // }) ?? []\n  },\n  methods: {\n    updateElement() {\n      if (this.formData.signal?.length) {\n        let extensionElements = this.element.businessObject.get('extensionElements')\n        if (!extensionElements) {\n          extensionElements = this.modeler.get('moddle').create('bpmn:signal')\n        }\n        const length = extensionElements.get('values').length\n        for (let i = 0; i < length; i++) {\n          // 清除旧值\n          extensionElements.get('values').pop()\n        }\n        this.updateProperties({ extensionElements: extensionElements })\n      } else {\n        const extensionElements = this.element.businessObject[`extensionElements`]\n        if (extensionElements) {\n          extensionElements.values = extensionElements.values?.filter(item => item.$type !== 'flowable:ExecutionListener')\n        }\n      }\n    },\n    closeDialog() {\n      this.$refs.xForm.validate().then(() => {\n        this.updateElement()\n        this.dialogVisible = false\n      }).catch(e => console.error(e))\n    }\n  }\n}\n</script>\n\n<style>\n.flow-containers  .el-badge__content.is-fixed {\n    top: 18px;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAoBA,OAAAA,UAAA;AACA;EACAC,MAAA,GAAAD,UAAA;EACAE,IAAA,WAAAA,KAAA;IACA;MACAC,aAAA;MACAC,QAAA;QACAC,MAAA;MACA;IACA;EACA;EACAC,QAAA;IACAC,UAAA,WAAAA,WAAA;MACA;MACA;QACAC,MAAA;QACAC,IAAA,GACA;UACAC,KAAA;UACAC,IAAA,GACA;YACAC,KAAA;YACAC,IAAA;YACAC,MAAA,GACA;cACAF,KAAA;cACAC,IAAA;cACAE,KAAA;cACAC,KAAA;gBAAAC,QAAA;gBAAAC,OAAA;gBAAAC,OAAA;cAAA;cACAT,KAAA;cACAU,GAAA,GACA;gBAAAR,KAAA;gBAAAS,KAAA;cAAA,GACA;gBAAAT,KAAA;gBAAAS,KAAA;cAAA;YAEA,GACA;cACAT,KAAA;cACAC,IAAA;cACAE,KAAA;cACAC,KAAA;gBAAAC,QAAA;gBAAAC,OAAA;gBAAAC,OAAA;cAAA;cACAT,KAAA;YACA,GACA;cACAE,KAAA;cACAC,IAAA;cACAH,KAAA;cACAM,KAAA;gBAAAC,QAAA;gBAAAC,OAAA;gBAAAC,OAAA;cAAA;YACA;UAEA;QAEA;MAEA;IACA;EACA;EACAG,OAAA,WAAAA,QAAA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EAAA,CACA;EACAC,OAAA;IACAC,aAAA,WAAAA,cAAA;MAAA,IAAAC,qBAAA;MACA,KAAAA,qBAAA,QAAArB,QAAA,CAAAC,MAAA,cAAAoB,qBAAA,eAAAA,qBAAA,CAAAC,MAAA;QACA,IAAAC,iBAAA,QAAAC,OAAA,CAAAC,cAAA,CAAAC,GAAA;QACA,KAAAH,iBAAA;UACAA,iBAAA,QAAAI,OAAA,CAAAD,GAAA,WAAAE,MAAA;QACA;QACA,IAAAN,MAAA,GAAAC,iBAAA,CAAAG,GAAA,WAAAJ,MAAA;QACA,SAAAO,CAAA,MAAAA,CAAA,GAAAP,MAAA,EAAAO,CAAA;UACA;UACAN,iBAAA,CAAAG,GAAA,WAAAI,GAAA;QACA;QACA,KAAAC,gBAAA;UAAAR,iBAAA,EAAAA;QAAA;MACA;QACA,IAAAA,kBAAA,QAAAC,OAAA,CAAAC,cAAA;QACA,IAAAF,kBAAA;UAAA,IAAAS,qBAAA;UACAT,kBAAA,CAAAU,MAAA,IAAAD,qBAAA,GAAAT,kBAAA,CAAAU,MAAA,cAAAD,qBAAA,uBAAAA,qBAAA,CAAAE,MAAA,WAAA7B,IAAA;YAAA,OAAAA,IAAA,CAAA8B,KAAA;UAAA;QACA;MACA;IACA;IACAC,WAAA,WAAAA,YAAA;MAAA,IAAAC,KAAA;MACA,KAAAC,KAAA,CAAAC,KAAA,CAAAC,QAAA,GAAAC,IAAA;QACAJ,KAAA,CAAAjB,aAAA;QACAiB,KAAA,CAAAtC,aAAA;MACA,GAAA2C,KAAA,WAAAC,CAAA;QAAA,OAAAC,OAAA,CAAAC,KAAA,CAAAF,CAAA;MAAA;IACA;EACA;AACA", "ignoreList": []}]}