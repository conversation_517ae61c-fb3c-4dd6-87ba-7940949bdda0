{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/components/FileUpload/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/components/FileUpload/index.vue", "mtime": 1718676532991}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldFRva2VuIH0gZnJvbSAiQC91dGlscy9hdXRoIjsKZXhwb3J0IGRlZmF1bHQgewogIHByb3BzOiB7CiAgICAvLyDlgLwKICAgIHZhbHVlOiBbU3RyaW5nLCBPYmplY3QsIEFycmF5XSwKICAgIC8vIOWkp+Wwj+mZkOWItihNQikKICAgIGZpbGVTaXplOiB7CiAgICAgIHR5cGU6IE51bWJlciwKICAgICAgZGVmYXVsdDogNQogICAgfSwKICAgIC8vIOaWh+S7tuexu+Weiywg5L6L5aaCWydwbmcnLCAnanBnJywgJ2pwZWcnXQogICAgZmlsZVR5cGU6IHsKICAgICAgdHlwZTogQXJyYXksCiAgICAgIGRlZmF1bHQ6IGZ1bmN0aW9uIF9kZWZhdWx0KCkgewogICAgICAgIHJldHVybiBbImRvYyIsICJ4bHMiLCAicHB0IiwgInR4dCIsICJwZGYiLCAiemlwIiwgInJhciJdOwogICAgICB9CiAgICB9LAogICAgLy8g5piv5ZCm5pi+56S65o+Q56S6CiAgICBpc1Nob3dUaXA6IHsKICAgICAgdHlwZTogQm9vbGVhbiwKICAgICAgZGVmYXVsdDogdHJ1ZQogICAgfQogIH0sCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIHVwbG9hZEZpbGVVcmw6IHByb2Nlc3MuZW52LlZVRV9BUFBfQkFTRV9BUEkgKyAiL2NvbW1vbi91cGxvYWQiLAogICAgICAvLyDkuIrkvKDnmoTlm77niYfmnI3liqHlmajlnLDlnYAKICAgICAgaGVhZGVyczogewogICAgICAgIEF1dGhvcml6YXRpb246ICJCZWFyZXIgIiArIGdldFRva2VuKCkKICAgICAgfSwKICAgICAgZmlsZUxpc3Q6IFtdCiAgICB9OwogIH0sCiAgY29tcHV0ZWQ6IHsKICAgIC8vIOaYr+WQpuaYvuekuuaPkOekugogICAgc2hvd1RpcDogZnVuY3Rpb24gc2hvd1RpcCgpIHsKICAgICAgcmV0dXJuIHRoaXMuaXNTaG93VGlwICYmICh0aGlzLmZpbGVUeXBlIHx8IHRoaXMuZmlsZVNpemUpOwogICAgfSwKICAgIC8vIOWIl+ihqAogICAgbGlzdDogZnVuY3Rpb24gbGlzdCgpIHsKICAgICAgdmFyIHRlbXAgPSAxOwogICAgICBpZiAodGhpcy52YWx1ZSkgewogICAgICAgIC8vIOmmluWFiOWwhuWAvOi9rOS4uuaVsOe7hAogICAgICAgIHZhciBsaXN0ID0gQXJyYXkuaXNBcnJheSh0aGlzLnZhbHVlKSA/IHRoaXMudmFsdWUgOiBbdGhpcy52YWx1ZV07CiAgICAgICAgLy8g54S25ZCO5bCG5pWw57uE6L2s5Li65a+56LGh5pWw57uECiAgICAgICAgcmV0dXJuIGxpc3QubWFwKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgICBpZiAodHlwZW9mIGl0ZW0gPT09ICJzdHJpbmciKSB7CiAgICAgICAgICAgIGl0ZW0gPSB7CiAgICAgICAgICAgICAgbmFtZTogaXRlbSwKICAgICAgICAgICAgICB1cmw6IGl0ZW0KICAgICAgICAgICAgfTsKICAgICAgICAgIH0KICAgICAgICAgIGl0ZW0udWlkID0gaXRlbS51aWQgfHwgbmV3IERhdGUoKS5nZXRUaW1lKCkgKyB0ZW1wKys7CiAgICAgICAgICByZXR1cm4gaXRlbTsKICAgICAgICB9KTsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLmZpbGVMaXN0ID0gW107CiAgICAgICAgcmV0dXJuIFtdOwogICAgICB9CiAgICB9CiAgfSwKICBtZXRob2RzOiB7CiAgICAvLyDkuIrkvKDliY3moKHmo4DmoLzlvI/lkozlpKflsI8KICAgIGhhbmRsZUJlZm9yZVVwbG9hZDogZnVuY3Rpb24gaGFuZGxlQmVmb3JlVXBsb2FkKGZpbGUpIHsKICAgICAgLy8g5qCh5qOA5paH5Lu257G75Z6LCiAgICAgIGlmICh0aGlzLmZpbGVUeXBlKSB7CiAgICAgICAgdmFyIGZpbGVFeHRlbnNpb24gPSAiIjsKICAgICAgICBpZiAoZmlsZS5uYW1lLmxhc3RJbmRleE9mKCIuIikgPiAtMSkgewogICAgICAgICAgZmlsZUV4dGVuc2lvbiA9IGZpbGUubmFtZS5zbGljZShmaWxlLm5hbWUubGFzdEluZGV4T2YoIi4iKSArIDEpOwogICAgICAgIH0KICAgICAgICB2YXIgaXNUeXBlT2sgPSB0aGlzLmZpbGVUeXBlLnNvbWUoZnVuY3Rpb24gKHR5cGUpIHsKICAgICAgICAgIGlmIChmaWxlLnR5cGUuaW5kZXhPZih0eXBlKSA+IC0xKSByZXR1cm4gdHJ1ZTsKICAgICAgICAgIGlmIChmaWxlRXh0ZW5zaW9uICYmIGZpbGVFeHRlbnNpb24uaW5kZXhPZih0eXBlKSA+IC0xKSByZXR1cm4gdHJ1ZTsKICAgICAgICAgIHJldHVybiBmYWxzZTsKICAgICAgICB9KTsKICAgICAgICBpZiAoIWlzVHlwZU9rKSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCJcdTY1ODdcdTRFRjZcdTY4M0NcdTVGMEZcdTRFMERcdTZCNjNcdTc4NkUsIFx1OEJGN1x1NEUwQVx1NEYyMCIuY29uY2F0KHRoaXMuZmlsZVR5cGUuam9pbigiLyIpLCAiXHU2ODNDXHU1RjBGXHU2NTg3XHU0RUY2ISIpKTsKICAgICAgICAgIHJldHVybiBmYWxzZTsKICAgICAgICB9CiAgICAgIH0KICAgICAgLy8g5qCh5qOA5paH5Lu25aSn5bCPCiAgICAgIGlmICh0aGlzLmZpbGVTaXplKSB7CiAgICAgICAgdmFyIGlzTHQgPSBmaWxlLnNpemUgLyAxMDI0IC8gMTAyNCA8IHRoaXMuZmlsZVNpemU7CiAgICAgICAgaWYgKCFpc0x0KSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCJcdTRFMEFcdTRGMjBcdTY1ODdcdTRFRjZcdTU5MjdcdTVDMEZcdTRFMERcdTgwRkRcdThEODVcdThGQzcgIi5jb25jYXQodGhpcy5maWxlU2l6ZSwgIiBNQiEiKSk7CiAgICAgICAgICByZXR1cm4gZmFsc2U7CiAgICAgICAgfQogICAgICB9CiAgICAgIHJldHVybiB0cnVlOwogICAgfSwKICAgIC8vIOaWh+S7tuS4quaVsOi2heWHugogICAgaGFuZGxlRXhjZWVkOiBmdW5jdGlvbiBoYW5kbGVFeGNlZWQoKSB7CiAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIlx1NTNFQVx1NTE0MVx1OEJCOFx1NEUwQVx1NEYyMFx1NTM1NVx1NEUyQVx1NjU4N1x1NEVGNiIpOwogICAgfSwKICAgIC8vIOS4iuS8oOWksei0pQogICAgaGFuZGxlVXBsb2FkRXJyb3I6IGZ1bmN0aW9uIGhhbmRsZVVwbG9hZEVycm9yKGVycikgewogICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLkuIrkvKDlpLHotKUsIOivt+mHjeivlSIpOwogICAgfSwKICAgIC8vIOS4iuS8oOaIkOWKn+WbnuiwgwogICAgaGFuZGxlVXBsb2FkU3VjY2VzczogZnVuY3Rpb24gaGFuZGxlVXBsb2FkU3VjY2VzcyhyZXMsIGZpbGUpIHsKICAgICAgaWYgKHJlcy5jb2RlICE9PSAyMDApIHsKICAgICAgICB0aGlzLmZpbGVMaXN0ID0gW107CiAgICAgICAgcmV0dXJuIHRoaXMuJG1lc3NhZ2UuZXJyb3IocmVzLm1zZyk7CiAgICAgIH0KICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCLkuIrkvKDmiJDlip8iKTsKICAgICAgdGhpcy4kZW1pdCgiaW5wdXQiLCByZXMudXJsKTsKICAgIH0sCiAgICAvLyDliKDpmaTmlofku7YKICAgIGhhbmRsZURlbGV0ZTogZnVuY3Rpb24gaGFuZGxlRGVsZXRlKGluZGV4KSB7CiAgICAgIHRoaXMuZmlsZUxpc3Quc3BsaWNlKGluZGV4LCAxKTsKICAgICAgdGhpcy4kZW1pdCgiaW5wdXQiLCAiIik7CiAgICB9LAogICAgLy8g6I635Y+W5paH5Lu25ZCN56ewCiAgICBnZXRGaWxlTmFtZTogZnVuY3Rpb24gZ2V0RmlsZU5hbWUobmFtZSkgewogICAgICBpZiAobmFtZS5sYXN0SW5kZXhPZigiLyIpID4gLTEpIHsKICAgICAgICByZXR1cm4gbmFtZS5zbGljZShuYW1lLmxhc3RJbmRleE9mKCIvIikgKyAxKS50b0xvd2VyQ2FzZSgpOwogICAgICB9IGVsc2UgewogICAgICAgIHJldHVybiAiIjsKICAgICAgfQogICAgfQogIH0sCiAgY3JlYXRlZDogZnVuY3Rpb24gY3JlYXRlZCgpIHsKICAgIHRoaXMuZmlsZUxpc3QgPSB0aGlzLmxpc3Q7CiAgfQp9Ow=="}, {"version": 3, "names": ["getToken", "props", "value", "String", "Object", "Array", "fileSize", "type", "Number", "default", "fileType", "_default", "isShowTip", "Boolean", "data", "uploadFileUrl", "process", "env", "VUE_APP_BASE_API", "headers", "Authorization", "fileList", "computed", "showTip", "list", "temp", "isArray", "map", "item", "name", "url", "uid", "Date", "getTime", "methods", "handleBeforeUpload", "file", "fileExtension", "lastIndexOf", "slice", "isTypeOk", "some", "indexOf", "$message", "error", "concat", "join", "isLt", "size", "handleExceed", "handleUploadError", "err", "handleUploadSuccess", "res", "code", "msg", "success", "$emit", "handleDelete", "index", "splice", "getFileName", "toLowerCase", "created"], "sources": ["src/components/FileUpload/index.vue"], "sourcesContent": ["<template>\n  <div class=\"upload-file\">\n    <el-upload\n      :action=\"uploadFileUrl\"\n      :before-upload=\"handleBeforeUpload\"\n      :file-list=\"fileList\"\n      :limit=\"1\"\n      :on-error=\"handleUploadError\"\n      :on-exceed=\"handleExceed\"\n      :on-success=\"handleUploadSuccess\"\n      :show-file-list=\"false\"\n      :headers=\"headers\"\n      class=\"upload-file-uploader\"\n      ref=\"upload\"\n    >\n      <!-- 上传按钮 -->\n      <el-button size=\"mini\" type=\"primary\">选取文件</el-button>\n      <!-- 上传提示 -->\n      <div class=\"el-upload__tip\" slot=\"tip\" v-if=\"showTip\">\n        请上传\n        <template v-if=\"fileSize\">\n          大小不超过 <b style=\"color: #f56c6c\">{{ fileSize }}MB</b>\n        </template>\n        <template v-if=\"fileType\">\n          格式为 <b style=\"color: #f56c6c\">{{ fileType.join(\"/\") }}</b>\n        </template>\n        的文件\n      </div>\n    </el-upload>\n\n    <!-- 文件列表 -->\n    <transition-group\n      class=\"upload-file-list el-upload-list el-upload-list--text\"\n      name=\"el-fade-in-linear\"\n      tag=\"ul\"\n    >\n      <li\n        :key=\"file.uid\"\n        class=\"el-upload-list__item ele-upload-list__item-content\"\n        v-for=\"(file, index) in list\"\n      >\n        <el-link :href=\"file.url\" :underline=\"false\" target=\"_blank\">\n          <span class=\"el-icon-document\"> {{ getFileName(file.name) }} </span>\n        </el-link>\n        <div class=\"ele-upload-list__item-content-action\">\n          <el-link :underline=\"false\" @click=\"handleDelete(index)\" type=\"danger\"\n            >删除</el-link\n          >\n        </div>\n      </li>\n    </transition-group>\n  </div>\n</template>\n\n<script>\nimport { getToken } from \"@/utils/auth\";\n\nexport default {\n  props: {\n    // 值\n    value: [String, Object, Array],\n    // 大小限制(MB)\n    fileSize: {\n      type: Number,\n      default: 5,\n    },\n    // 文件类型, 例如['png', 'jpg', 'jpeg']\n    fileType: {\n      type: Array,\n      default: () => [\"doc\", \"xls\", \"ppt\", \"txt\", \"pdf\", \"zip\", \"rar\"],\n    },\n    // 是否显示提示\n    isShowTip: {\n      type: Boolean,\n      default: true,\n    },\n  },\n  data() {\n    return {\n      uploadFileUrl: process.env.VUE_APP_BASE_API + \"/common/upload\", // 上传的图片服务器地址\n      headers: {\n        Authorization: \"Bearer \" + getToken(),\n      },\n      fileList: [],\n    };\n  },\n  computed: {\n    // 是否显示提示\n    showTip() {\n      return this.isShowTip && (this.fileType || this.fileSize);\n    },\n    // 列表\n    list() {\n      let temp = 1;\n      if (this.value) {\n        // 首先将值转为数组\n        const list = Array.isArray(this.value) ? this.value : [this.value];\n        // 然后将数组转为对象数组\n        return list.map((item) => {\n          if (typeof item === \"string\") {\n            item = { name: item, url: item };\n          }\n          item.uid = item.uid || new Date().getTime() + temp++;\n          return item;\n        });\n      } else {\n        this.fileList = [];\n        return [];\n      }\n    },\n  },\n  methods: {\n    // 上传前校检格式和大小\n    handleBeforeUpload(file) {\n      // 校检文件类型\n      if (this.fileType) {\n        let fileExtension = \"\";\n        if (file.name.lastIndexOf(\".\") > -1) {\n          fileExtension = file.name.slice(file.name.lastIndexOf(\".\") + 1);\n        }\n        const isTypeOk = this.fileType.some((type) => {\n          if (file.type.indexOf(type) > -1) return true;\n          if (fileExtension && fileExtension.indexOf(type) > -1) return true;\n          return false;\n        });\n        if (!isTypeOk) {\n          this.$message.error(\n            `文件格式不正确, 请上传${this.fileType.join(\"/\")}格式文件!`\n          );\n          return false;\n        }\n      }\n      // 校检文件大小\n      if (this.fileSize) {\n        const isLt = file.size / 1024 / 1024 < this.fileSize;\n        if (!isLt) {\n          this.$message.error(`上传文件大小不能超过 ${this.fileSize} MB!`);\n          return false;\n        }\n      }\n      return true;\n    },\n    // 文件个数超出\n    handleExceed() {\n      this.$message.error(`只允许上传单个文件`);\n    },\n    // 上传失败\n    handleUploadError(err) {\n      this.$message.error(\"上传失败, 请重试\");\n    },\n    // 上传成功回调\n    handleUploadSuccess(res, file) {\n      if (res.code !== 200) {\n        this.fileList = [];\n        return this.$message.error(res.msg);\n      }\n      this.$message.success(\"上传成功\");\n      this.$emit(\"input\", res.url);\n    },\n    // 删除文件\n    handleDelete(index) {\n      this.fileList.splice(index, 1);\n      this.$emit(\"input\", \"\");\n    },\n    // 获取文件名称\n    getFileName(name) {\n      if (name.lastIndexOf(\"/\") > -1) {\n        return name.slice(name.lastIndexOf(\"/\") + 1).toLowerCase();\n      } else {\n        return \"\";\n      }\n    },\n  },\n  created() {\n    this.fileList = this.list;\n  },\n};\n</script>\n\n<style scoped lang=\"scss\">\n.upload-file-uploader {\n  margin-bottom: 5px;\n}\n.upload-file-list .el-upload-list__item {\n  border: 1px solid #e4e7ed;\n  line-height: 2;\n  margin-bottom: 10px;\n  position: relative;\n}\n.upload-file-list .ele-upload-list__item-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  color: inherit;\n}\n.ele-upload-list__item-content-action .el-link {\n  margin-right: 10px;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuDA,SAAAA,QAAA;AAEA;EACAC,KAAA;IACA;IACAC,KAAA,GAAAC,MAAA,EAAAC,MAAA,EAAAC,KAAA;IACA;IACAC,QAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACA;IACAC,QAAA;MACAH,IAAA,EAAAF,KAAA;MACAI,OAAA,WAAAE,SAAA;QAAA;MAAA;IACA;IACA;IACAC,SAAA;MACAL,IAAA,EAAAM,OAAA;MACAJ,OAAA;IACA;EACA;EACAK,IAAA,WAAAA,KAAA;IACA;MACAC,aAAA,EAAAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA;MAAA;MACAC,OAAA;QACAC,aAAA,cAAApB,QAAA;MACA;MACAqB,QAAA;IACA;EACA;EACAC,QAAA;IACA;IACAC,OAAA,WAAAA,QAAA;MACA,YAAAX,SAAA,UAAAF,QAAA,SAAAJ,QAAA;IACA;IACA;IACAkB,IAAA,WAAAA,KAAA;MACA,IAAAC,IAAA;MACA,SAAAvB,KAAA;QACA;QACA,IAAAsB,IAAA,GAAAnB,KAAA,CAAAqB,OAAA,MAAAxB,KAAA,SAAAA,KAAA,SAAAA,KAAA;QACA;QACA,OAAAsB,IAAA,CAAAG,GAAA,WAAAC,IAAA;UACA,WAAAA,IAAA;YACAA,IAAA;cAAAC,IAAA,EAAAD,IAAA;cAAAE,GAAA,EAAAF;YAAA;UACA;UACAA,IAAA,CAAAG,GAAA,GAAAH,IAAA,CAAAG,GAAA,QAAAC,IAAA,GAAAC,OAAA,KAAAR,IAAA;UACA,OAAAG,IAAA;QACA;MACA;QACA,KAAAP,QAAA;QACA;MACA;IACA;EACA;EACAa,OAAA;IACA;IACAC,kBAAA,WAAAA,mBAAAC,IAAA;MACA;MACA,SAAA1B,QAAA;QACA,IAAA2B,aAAA;QACA,IAAAD,IAAA,CAAAP,IAAA,CAAAS,WAAA;UACAD,aAAA,GAAAD,IAAA,CAAAP,IAAA,CAAAU,KAAA,CAAAH,IAAA,CAAAP,IAAA,CAAAS,WAAA;QACA;QACA,IAAAE,QAAA,QAAA9B,QAAA,CAAA+B,IAAA,WAAAlC,IAAA;UACA,IAAA6B,IAAA,CAAA7B,IAAA,CAAAmC,OAAA,CAAAnC,IAAA;UACA,IAAA8B,aAAA,IAAAA,aAAA,CAAAK,OAAA,CAAAnC,IAAA;UACA;QACA;QACA,KAAAiC,QAAA;UACA,KAAAG,QAAA,CAAAC,KAAA,kEAAAC,MAAA,CACA,KAAAnC,QAAA,CAAAoC,IAAA,mCACA;UACA;QACA;MACA;MACA;MACA,SAAAxC,QAAA;QACA,IAAAyC,IAAA,GAAAX,IAAA,CAAAY,IAAA,sBAAA1C,QAAA;QACA,KAAAyC,IAAA;UACA,KAAAJ,QAAA,CAAAC,KAAA,iEAAAC,MAAA,MAAAvC,QAAA;UACA;QACA;MACA;MACA;IACA;IACA;IACA2C,YAAA,WAAAA,aAAA;MACA,KAAAN,QAAA,CAAAC,KAAA;IACA;IACA;IACAM,iBAAA,WAAAA,kBAAAC,GAAA;MACA,KAAAR,QAAA,CAAAC,KAAA;IACA;IACA;IACAQ,mBAAA,WAAAA,oBAAAC,GAAA,EAAAjB,IAAA;MACA,IAAAiB,GAAA,CAAAC,IAAA;QACA,KAAAjC,QAAA;QACA,YAAAsB,QAAA,CAAAC,KAAA,CAAAS,GAAA,CAAAE,GAAA;MACA;MACA,KAAAZ,QAAA,CAAAa,OAAA;MACA,KAAAC,KAAA,UAAAJ,GAAA,CAAAvB,GAAA;IACA;IACA;IACA4B,YAAA,WAAAA,aAAAC,KAAA;MACA,KAAAtC,QAAA,CAAAuC,MAAA,CAAAD,KAAA;MACA,KAAAF,KAAA;IACA;IACA;IACAI,WAAA,WAAAA,YAAAhC,IAAA;MACA,IAAAA,IAAA,CAAAS,WAAA;QACA,OAAAT,IAAA,CAAAU,KAAA,CAAAV,IAAA,CAAAS,WAAA,WAAAwB,WAAA;MACA;QACA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAA1C,QAAA,QAAAG,IAAA;EACA;AACA", "ignoreList": []}]}