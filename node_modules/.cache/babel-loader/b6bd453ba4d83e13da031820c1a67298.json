{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/tool/build/JsonDrawer.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/tool/build/JsonDrawer.vue", "mtime": 1641277326000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["beautifierConf", "ClipboardJS", "saveAs", "loadMonaco", "loadBeautifier", "beautifier", "monaco", "components", "props", "jsonStr", "type", "String", "required", "data", "computed", "watch", "created", "mounted", "_this", "window", "addEventListener", "preventDefaultSave", "clipboard", "text", "trigger", "$notify", "title", "message", "beautifierJson", "on", "e", "$message", "error", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "methods", "key", "metaKey", "ctrl<PERSON>ey", "preventDefault", "onOpen", "_this2", "btf", "js", "val", "setEditorValue", "onClose", "id", "codeStr", "_this3", "jsonEditor", "setValue", "editor", "create", "document", "getElementById", "value", "theme", "language", "automaticLayout", "onKeyDown", "keyCode", "refresh", "exportJsonFile", "_this4", "$prompt", "inputValue", "concat", "Date", "closeOnClickModal", "inputPlaceholder", "then", "_ref", "getValue", "blob", "Blob", "$emit", "JSON", "parse"], "sources": ["src/views/tool/build/JsonDrawer.vue"], "sourcesContent": ["<template>\n  <div>\n    <el-drawer v-bind=\"$attrs\" v-on=\"$listeners\" @opened=\"onOpen\" @close=\"onClose\">\n      <div class=\"action-bar\" :style=\"{'text-align': 'left'}\">\n        <span class=\"bar-btn\" @click=\"refresh\">\n          <i class=\"el-icon-refresh\" />\n          刷新\n        </span>\n        <span ref=\"copyBtn\" class=\"bar-btn copy-json-btn\">\n          <i class=\"el-icon-document-copy\" />\n          复制JSON\n        </span>\n        <span class=\"bar-btn\" @click=\"exportJsonFile\">\n          <i class=\"el-icon-download\" />\n          导出JSON文件\n        </span>\n        <span class=\"bar-btn delete-btn\" @click=\"$emit('update:visible', false)\">\n          <i class=\"el-icon-circle-close\" />\n          关闭\n        </span>\n      </div>\n      <div id=\"editorJson\" class=\"json-editor\" />\n    </el-drawer>\n  </div>\n</template>\n\n<script>\nimport { beautifierConf } from '@/utils/index'\nimport ClipboardJS from 'clipboard'\nimport { saveAs } from 'file-saver'\nimport loadMonaco from '@/utils/loadMonaco'\nimport loadBeautifier from '@/utils/loadBeautifier'\n\nlet beautifier\nlet monaco\n\nexport default {\n  components: {},\n  props: {\n    jsonStr: {\n      type: String,\n      required: true\n    }\n  },\n  data() {\n    return {}\n  },\n  computed: {},\n  watch: {},\n  created() {},\n  mounted() {\n    window.addEventListener('keydown', this.preventDefaultSave)\n    const clipboard = new ClipboardJS('.copy-json-btn', {\n      text: trigger => {\n        this.$notify({\n          title: '成功',\n          message: '代码已复制到剪切板，可粘贴。',\n          type: 'success'\n        })\n        return this.beautifierJson\n      }\n    })\n    clipboard.on('error', e => {\n      this.$message.error('代码复制失败')\n    })\n  },\n  beforeDestroy() {\n    window.removeEventListener('keydown', this.preventDefaultSave)\n  },\n  methods: {\n    preventDefaultSave(e) {\n      if (e.key === 's' && (e.metaKey || e.ctrlKey)) {\n        e.preventDefault()\n      }\n    },\n    onOpen() {\n      loadBeautifier(btf => {\n        beautifier = btf\n        this.beautifierJson = beautifier.js(this.jsonStr, beautifierConf.js)\n\n        loadMonaco(val => {\n          monaco = val\n          this.setEditorValue('editorJson', this.beautifierJson)\n        })\n      })\n    },\n    onClose() {},\n    setEditorValue(id, codeStr) {\n      if (this.jsonEditor) {\n        this.jsonEditor.setValue(codeStr)\n      } else {\n        this.jsonEditor = monaco.editor.create(document.getElementById(id), {\n          value: codeStr,\n          theme: 'vs-dark',\n          language: 'json',\n          automaticLayout: true\n        })\n        // ctrl + s 刷新\n        this.jsonEditor.onKeyDown(e => {\n          if (e.keyCode === 49 && (e.metaKey || e.ctrlKey)) {\n            this.refresh()\n          }\n        })\n      }\n    },\n    exportJsonFile() {\n      this.$prompt('文件名:', '导出文件', {\n        inputValue: `${+new Date()}.json`,\n        closeOnClickModal: false,\n        inputPlaceholder: '请输入文件名'\n      }).then(({ value }) => {\n        if (!value) value = `${+new Date()}.json`\n        const codeStr = this.jsonEditor.getValue()\n        const blob = new Blob([codeStr], { type: 'text/plain;charset=utf-8' })\n        saveAs(blob, value)\n      })\n    },\n    refresh() {\n      try {\n        this.$emit('refresh', JSON.parse(this.jsonEditor.getValue()))\n      } catch (error) {\n        this.$notify({\n          title: '错误',\n          message: 'JSON格式错误，请检查',\n          type: 'error'\n        })\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import '@/styles/mixin.scss';\n\n::v-deep .el-drawer__header {\n  display: none;\n}\n@include action-bar;\n\n.json-editor{\n  height: calc(100vh - 33px);\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BA,SAAAA,cAAA;AACA,OAAAC,WAAA;AACA,SAAAC,MAAA;AACA,OAAAC,UAAA;AACA,OAAAC,cAAA;AAEA,IAAAC,UAAA;AACA,IAAAC,MAAA;AAEA;EACAC,UAAA;EACAC,KAAA;IACAC,OAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,QAAA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;EACA;EACAC,QAAA;EACAC,KAAA;EACAC,OAAA,WAAAA,QAAA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACAC,MAAA,CAAAC,gBAAA,iBAAAC,kBAAA;IACA,IAAAC,SAAA,OAAArB,WAAA;MACAsB,IAAA,WAAAA,KAAAC,OAAA;QACAN,KAAA,CAAAO,OAAA;UACAC,KAAA;UACAC,OAAA;UACAjB,IAAA;QACA;QACA,OAAAQ,KAAA,CAAAU,cAAA;MACA;IACA;IACAN,SAAA,CAAAO,EAAA,oBAAAC,CAAA;MACAZ,KAAA,CAAAa,QAAA,CAAAC,KAAA;IACA;EACA;EACAC,aAAA,WAAAA,cAAA;IACAd,MAAA,CAAAe,mBAAA,iBAAAb,kBAAA;EACA;EACAc,OAAA;IACAd,kBAAA,WAAAA,mBAAAS,CAAA;MACA,IAAAA,CAAA,CAAAM,GAAA,aAAAN,CAAA,CAAAO,OAAA,IAAAP,CAAA,CAAAQ,OAAA;QACAR,CAAA,CAAAS,cAAA;MACA;IACA;IACAC,MAAA,WAAAA,OAAA;MAAA,IAAAC,MAAA;MACArC,cAAA,WAAAsC,GAAA;QACArC,UAAA,GAAAqC,GAAA;QACAD,MAAA,CAAAb,cAAA,GAAAvB,UAAA,CAAAsC,EAAA,CAAAF,MAAA,CAAAhC,OAAA,EAAAT,cAAA,CAAA2C,EAAA;QAEAxC,UAAA,WAAAyC,GAAA;UACAtC,MAAA,GAAAsC,GAAA;UACAH,MAAA,CAAAI,cAAA,eAAAJ,MAAA,CAAAb,cAAA;QACA;MACA;IACA;IACAkB,OAAA,WAAAA,QAAA;IACAD,cAAA,WAAAA,eAAAE,EAAA,EAAAC,OAAA;MAAA,IAAAC,MAAA;MACA,SAAAC,UAAA;QACA,KAAAA,UAAA,CAAAC,QAAA,CAAAH,OAAA;MACA;QACA,KAAAE,UAAA,GAAA5C,MAAA,CAAA8C,MAAA,CAAAC,MAAA,CAAAC,QAAA,CAAAC,cAAA,CAAAR,EAAA;UACAS,KAAA,EAAAR,OAAA;UACAS,KAAA;UACAC,QAAA;UACAC,eAAA;QACA;QACA;QACA,KAAAT,UAAA,CAAAU,SAAA,WAAA9B,CAAA;UACA,IAAAA,CAAA,CAAA+B,OAAA,YAAA/B,CAAA,CAAAO,OAAA,IAAAP,CAAA,CAAAQ,OAAA;YACAW,MAAA,CAAAa,OAAA;UACA;QACA;MACA;IACA;IACAC,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,OAAA;QACAC,UAAA,KAAAC,MAAA,MAAAC,IAAA;QACAC,iBAAA;QACAC,gBAAA;MACA,GAAAC,IAAA,WAAAC,IAAA;QAAA,IAAAhB,KAAA,GAAAgB,IAAA,CAAAhB,KAAA;QACA,KAAAA,KAAA,EAAAA,KAAA,MAAAW,MAAA,MAAAC,IAAA;QACA,IAAApB,OAAA,GAAAgB,MAAA,CAAAd,UAAA,CAAAuB,QAAA;QACA,IAAAC,IAAA,OAAAC,IAAA,EAAA3B,OAAA;UAAAtC,IAAA;QAAA;QACAR,MAAA,CAAAwE,IAAA,EAAAlB,KAAA;MACA;IACA;IACAM,OAAA,WAAAA,QAAA;MACA;QACA,KAAAc,KAAA,YAAAC,IAAA,CAAAC,KAAA,MAAA5B,UAAA,CAAAuB,QAAA;MACA,SAAAzC,KAAA;QACA,KAAAP,OAAA;UACAC,KAAA;UACAC,OAAA;UACAjB,IAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}