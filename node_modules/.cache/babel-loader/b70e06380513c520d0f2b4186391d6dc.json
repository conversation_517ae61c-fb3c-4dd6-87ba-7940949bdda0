{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/system/user/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/system/user/index.vue", "mtime": 1665234686000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGxpc3RVc2VyLCBnZXRVc2VyLCBkZWxVc2VyLCBhZGRVc2VyLCB1cGRhdGVVc2VyLCBleHBvcnRVc2VyLCByZXNldFVzZXJQd2QsIGNoYW5nZVVzZXJTdGF0dXMsIGltcG9ydFRlbXBsYXRlIGFzIF9pbXBvcnRUZW1wbGF0ZSwgcHJpbnRVc2VyIH0gZnJvbSAiQC9hcGkvc3lzdGVtL3VzZXIiOwppbXBvcnQgeyBnZXRUb2tlbiB9IGZyb20gIkAvdXRpbHMvYXV0aCI7CmltcG9ydCB7IHRyZWVzZWxlY3QgfSBmcm9tICJAL2FwaS9zeXN0ZW0vZGVwdCI7CmltcG9ydCBUcmVlc2VsZWN0IGZyb20gIkByaW9waGFlL3Z1ZS10cmVlc2VsZWN0IjsKaW1wb3J0ICJAcmlvcGhhZS92dWUtdHJlZXNlbGVjdC9kaXN0L3Z1ZS10cmVlc2VsZWN0LmNzcyI7CmltcG9ydCBJbWFnZVVwbG9hZCBmcm9tICJAL2NvbXBvbmVudHMvSW1hZ2VVcGxvYWQiOwppbXBvcnQgeyBwcm92aW5jZUFuZENpdHlEYXRhLCByZWdpb25EYXRhLCBDb2RlVG9UZXh0LCBUZXh0VG9Db2RlIH0gZnJvbSAiZWxlbWVudC1jaGluYS1hcmVhLWRhdGEiOwppbXBvcnQgZmxvd2FibGUgZnJvbSAnQC92aWV3cy9mbG93YWJsZS90YXNrL3JlY29yZC92aWV3JzsKaW1wb3J0IHsgZ2V0SW5zSWRCeUJpektleSB9IGZyb20gIkAvYXBpL2Zsb3dhYmxlL3RvZG8iOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogIlVzZXIiLAogIGNvbXBvbmVudHM6IHsKICAgIFRyZWVzZWxlY3Q6IFRyZWVzZWxlY3QsCiAgICBJbWFnZVVwbG9hZDogSW1hZ2VVcGxvYWQsCiAgICBmbG93YWJsZTogZmxvd2FibGUKICB9LAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBpc01vYmlsZTogZmFsc2UsCiAgICAgIHBhZ2VMYXlvdXQ6ICJ0b3RhbCwgc2l6ZXMsIHByZXYsIHBhZ2VyLCBuZXh0LCBqdW1wZXIiLAogICAgICAvLyDpga7nvanlsYIKICAgICAgbG9hZGluZzogdHJ1ZSwKICAgICAgLy8g6YCJ5Lit5pWw57uECiAgICAgIGlkczogW10sCiAgICAgIC8vIOmdnuWNleS4quemgeeUqAogICAgICBzaW5nbGU6IHRydWUsCiAgICAgIC8vIOmdnuWkmuS4quemgeeUqAogICAgICBtdWx0aXBsZTogdHJ1ZSwKICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2CiAgICAgIHNob3dTZWFyY2g6IHRydWUsCiAgICAgIHNob3dFeHBvcnQ6IGZhbHNlLAogICAgICBzaG93UHJpbnQ6IGZhbHNlLAogICAgICAvLyDmgLvmnaHmlbAKICAgICAgdG90YWw6IDAsCiAgICAgIC8vIOeUqOaIt+ihqOagvOaVsOaNrgogICAgICB1c2VyTGlzdDogbnVsbCwKICAgICAgLy8g5by55Ye65bGC5qCH6aKYCiAgICAgIHRpdGxlOiAiIiwKICAgICAgLy8g6YOo6Zeo5qCR6YCJ6aG5CiAgICAgIGRlcHRPcHRpb25zOiB1bmRlZmluZWQsCiAgICAgIC8vIOaYr+WQpuaYvuekuuW8ueWHuuWxggogICAgICBvcGVuOiBmYWxzZSwKICAgICAgLy8g6YOo6Zeo5ZCN56ewCiAgICAgIGRlcHROYW1lOiB1bmRlZmluZWQsCiAgICAgIC8vIOm7mOiupOWvhueggQogICAgICBpbml0UGFzc3dvcmQ6IHVuZGVmaW5lZCwKICAgICAgLy8g5pel5pyf6IyD5Zu0CiAgICAgIGRhdGVSYW5nZTogW10sCiAgICAgIC8vIOeKtuaAgeaVsOaNruWtl+WFuAogICAgICBzdGF0dXNPcHRpb25zOiBbXSwKICAgICAgLy8g5oCn5Yir54q25oCB5a2X5YW4CiAgICAgIHNleE9wdGlvbnM6IFtdLAogICAgICAvLyDnn63kv6HpgJrnn6XlrZflhbgKICAgICAgc21zU2VuZE9wdGlvbnM6IFtdLAogICAgICAvLyDlrqHmoLjnirbmgIHlrZflhbgKICAgICAgYXVkaXRTdGF0dXNPcHRpb25zOiBbXSwKICAgICAgLy8g6KeS6Imy6YCJ6aG5CiAgICAgIHJvbGVPcHRpb25zOiBbXSwKICAgICAgLy8g6KGo5Y2V5Y+C5pWwCiAgICAgIGZvcm06IHt9LAogICAgICBkZWZhdWx0UHJvcHM6IHsKICAgICAgICBjaGlsZHJlbjogImNoaWxkcmVuIiwKICAgICAgICBsYWJlbDogImxhYmVsIgogICAgICB9LAogICAgICAvLyDnlKjmiLflr7zlhaXlj4LmlbAKICAgICAgdXBsb2FkOiB7CiAgICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGC77yI55So5oi35a+85YWl77yJCiAgICAgICAgb3BlbjogZmFsc2UsCiAgICAgICAgLy8g5by55Ye65bGC5qCH6aKY77yI55So5oi35a+85YWl77yJCiAgICAgICAgdGl0bGU6ICIiLAogICAgICAgIC8vIOaYr+WQpuemgeeUqOS4iuS8oAogICAgICAgIGlzVXBsb2FkaW5nOiBmYWxzZSwKICAgICAgICAvLyDmmK/lkKbmm7TmlrDlt7Lnu4/lrZjlnKjnmoTnlKjmiLfmlbDmja4KICAgICAgICB1cGRhdGVTdXBwb3J0OiAwLAogICAgICAgIC8vIOiuvue9ruS4iuS8oOeahOivt+axguWktOmDqAogICAgICAgIGhlYWRlcnM6IHsKICAgICAgICAgIEF1dGhvcml6YXRpb246ICJCZWFyZXIgIiArIGdldFRva2VuKCkKICAgICAgICB9LAogICAgICAgIC8vIOS4iuS8oOeahOWcsOWdgAogICAgICAgIHVybDogcHJvY2Vzcy5lbnYuVlVFX0FQUF9CQVNFX0FQSSArICIvc3lzdGVtL3VzZXIvaW1wb3J0RGF0YSIKICAgICAgfSwKICAgICAgLy8g5p+l6K+i5Y+C5pWwCiAgICAgIHF1ZXJ5UGFyYW1zOiB7CiAgICAgICAgcGFnZU51bTogMSwKICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgdXNlck5hbWU6IHVuZGVmaW5lZCwKICAgICAgICBwaG9uZW51bWJlcjogdW5kZWZpbmVkLAogICAgICAgIHN0YXR1czogdW5kZWZpbmVkLAogICAgICAgIGRlcHRJZDogdW5kZWZpbmVkLAogICAgICAgIGF1ZGl0U3RhdHVzOiB1bmRlZmluZWQKICAgICAgfSwKICAgICAgLy8g5YiX5L+h5oGvCiAgICAgIGNvbHVtbnM6IFt7CiAgICAgICAga2V5OiAnc3RhdHVzJywKICAgICAgICBpbmRleDogMCwKICAgICAgICBsYWJlbDogIlx1NUUxMFx1NTNGN1x1NzJCNlx1NjAwMSIsCiAgICAgICAgdmlzaWJsZTogdHJ1ZQogICAgICB9LCB7CiAgICAgICAga2V5OiAndXNlcklkJywKICAgICAgICBpbmRleDogMSwKICAgICAgICBsYWJlbDogIlx1NzUyOFx1NjIzN0lEIiwKICAgICAgICB2aXNpYmxlOiB0cnVlCiAgICAgIH0sIHsKICAgICAgICBrZXk6ICd1c2VyTmFtZScsCiAgICAgICAgaW5kZXg6IDIsCiAgICAgICAgbGFiZWw6ICJcdTc1MjhcdTYyMzdcdThEMjZcdTUzRjciLAogICAgICAgIHZpc2libGU6IHRydWUKICAgICAgfSwgewogICAgICAgIGtleTogJ25pY2tOYW1lJywKICAgICAgICBpbmRleDogMywKICAgICAgICBsYWJlbDogIlx1NjJBNVx1NTkwN1x1NEVCQVx1NTlEM1x1NTQwRCIsCiAgICAgICAgdmlzaWJsZTogdHJ1ZQogICAgICB9LCB7CiAgICAgICAga2V5OiAndXNlclR5cGUnLAogICAgICAgIGluZGV4OiA0LAogICAgICAgIGxhYmVsOiAiXHU3NTI4XHU2MjM3XHU3QzdCXHU1NzhCIiwKICAgICAgICB2aXNpYmxlOiB0cnVlCiAgICAgIH0sIHsKICAgICAgICBrZXk6ICdhdWRpdFN0YXR1cycsCiAgICAgICAgaW5kZXg6IDUsCiAgICAgICAgbGFiZWw6ICJcdTVCQTFcdTY4MzhcdTcyQjZcdTYwMDEiLAogICAgICAgIHZpc2libGU6IHRydWUKICAgICAgfSwgewogICAgICAgIGtleTogJ2VtYWlsJywKICAgICAgICBpbmRleDogNiwKICAgICAgICBsYWJlbDogIlx1OEQ0NFx1NjU5OVx1NjNBNVx1NjUzNlx1OTBBRVx1N0JCMSIsCiAgICAgICAgdmlzaWJsZTogdHJ1ZQogICAgICB9LCB7CiAgICAgICAga2V5OiAncGhvbmVudW1iZXInLAogICAgICAgIGluZGV4OiA3LAogICAgICAgIGxhYmVsOiAiXHU2MkE1XHU1OTA3XHU0RUJBXHU3NTM1XHU4QkREIiwKICAgICAgICB2aXNpYmxlOiB0cnVlCiAgICAgIH0sIHsKICAgICAgICBrZXk6ICdzZXgnLAogICAgICAgIGluZGV4OiA4LAogICAgICAgIGxhYmVsOiAiXHU3NTI4XHU2MjM3XHU2MDI3XHU1MjJCIiwKICAgICAgICB2aXNpYmxlOiB0cnVlCiAgICAgIH0sIHsKICAgICAgICBrZXk6ICdhdmF0YXInLAogICAgICAgIGluZGV4OiA5LAogICAgICAgIGxhYmVsOiAiXHU1OTM0XHU1MENGIiwKICAgICAgICB2aXNpYmxlOiB0cnVlCiAgICAgIH0sIHsKICAgICAgICBrZXk6ICdjb21wYW55JywKICAgICAgICBpbmRleDogMTAsCiAgICAgICAgbGFiZWw6ICJcdTUxNkNcdTUzRjhcdTUxNjhcdTc5RjAiLAogICAgICAgIHZpc2libGU6IHRydWUKICAgICAgfSwgewogICAgICAgIGtleTogJ2J1c2luZXNzTm8nLAogICAgICAgIGluZGV4OiAxMSwKICAgICAgICBsYWJlbDogIlx1ODQyNVx1NEUxQVx1NjI2N1x1NzE2N1x1NTNGN1x1NzgwMSIsCiAgICAgICAgdmlzaWJsZTogdHJ1ZQogICAgICB9LCB7CiAgICAgICAga2V5OiAnYnVzaW5lc3NOb1BpYycsCiAgICAgICAgaW5kZXg6IDEyLAogICAgICAgIGxhYmVsOiAiXHU4NDI1XHU0RTFBXHU2MjY3XHU3MTY3XHU1NkZFXHU3MjQ3IiwKICAgICAgICB2aXNpYmxlOiB0cnVlCiAgICAgIH0sIHsKICAgICAgICBrZXk6ICdwcm92aW5jZScsCiAgICAgICAgaW5kZXg6IDEzLAogICAgICAgIGxhYmVsOiAiXHU2MjQwXHU1NzI4XHU1MzNBXHU1N0RGIiwKICAgICAgICB2aXNpYmxlOiB0cnVlCiAgICAgIH0sIHsKICAgICAgICBrZXk6ICdhZGRyZXNzJywKICAgICAgICBpbmRleDogMTQsCiAgICAgICAgbGFiZWw6ICJcdThENDRcdTY1OTlcdTkwQUVcdTVCQzRcdTU3MzBcdTU3NDAiLAogICAgICAgIHZpc2libGU6IHRydWUKICAgICAgfSwgewogICAgICAgIGtleTogJ2RlYWxlcicsCiAgICAgICAgaW5kZXg6IDE1LAogICAgICAgIGxhYmVsOiAiXHU5NkI2XHU1QzVFXHU3RUNGXHU5NTAwXHU1NTQ2IiwKICAgICAgICB2aXNpYmxlOiB0cnVlCiAgICAgIH0sIHsKICAgICAgICBrZXk6ICdjcmVhdGVUaW1lJywKICAgICAgICBpbmRleDogMTYsCiAgICAgICAgbGFiZWw6ICJcdTYzRDBcdTRFQTRcdTY1RjZcdTk1RjQiLAogICAgICAgIHZpc2libGU6IHRydWUKICAgICAgfSwgewogICAgICAgIGtleTogJ3VwZGF0ZVRpbWUnLAogICAgICAgIGluZGV4OiAxNywKICAgICAgICBsYWJlbDogIlx1NjcwMFx1NTQwRVx1NEZFRVx1NjUzOVx1NjVGNlx1OTVGNCIsCiAgICAgICAgdmlzaWJsZTogdHJ1ZQogICAgICB9LCB7CiAgICAgICAga2V5OiAnc21zU2VuZCcsCiAgICAgICAgaW5kZXg6IDE4LAogICAgICAgIGxhYmVsOiAiXHU3N0VEXHU0RkUxXHU5MDFBXHU3N0U1IiwKICAgICAgICB2aXNpYmxlOiB0cnVlCiAgICAgIH1dLAogICAgICAvLyDooajljZXmoKHpqowKICAgICAgcnVsZXM6IHsKICAgICAgICB1c2VyTmFtZTogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogIueUqOaIt+WQjeS4jeiDveS4uuepuiIsCiAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIKICAgICAgICB9XSwKICAgICAgICBjb21wYW55OiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAi5YWs5Y+45YWo56ew5LiN6IO95Li656m6IiwKICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgIH1dLAogICAgICAgIGJ1c2luZXNzTm86IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICLokKXkuJrmiafnhaflj7fnoIHkuI3og73kuLrnqboiLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgfV0sCiAgICAgICAgYnVzaW5lc3NOb1BpYzogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogIuiQpeS4muaJp+eFp+WbvueJh+W/heS8oCIKICAgICAgICB9XSwKICAgICAgICBlbWFpbDogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgdHlwZTogImVtYWlsIiwKICAgICAgICAgIG1lc3NhZ2U6ICIn6K+36L6T5YWl5q2j56Gu55qE6YKu566x5Zyw5Z2AIiwKICAgICAgICAgIHRyaWdnZXI6IFsiYmx1ciIsICJjaGFuZ2UiXQogICAgICAgIH1dLAogICAgICAgIHBob25lbnVtYmVyOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBwYXR0ZXJuOiAvXjFbM3w0fDV8Nnw3fDh8OV1bMC05XVxkezh9JC8sCiAgICAgICAgICBtZXNzYWdlOiAi6K+36L6T5YWl5q2j56Gu55qE5omL5py65Y+356CBIiwKICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgIH1dLAogICAgICAgIG5pY2tOYW1lOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAi5oql5aSH5Lq65aeT5ZCN5LiN6IO95Li656m6IiwKICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgIH1dLAogICAgICAgIHByb3ZpbmNlOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAi5omA5Zyo5Yy65Z+f5LiN6IO95Li656m6IiwKICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgIH1dLAogICAgICAgIGFkZHJlc3M6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICLotYTmlpnpgq7lr4TlnLDlnYDkuI3og73kuLrnqboiLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgfV0KICAgICAgfSwKICAgICAgYXVkaXRTdGF0dXNUcmVlOiBbXSwKICAgICAgcHJvdmluY2VBbmRDaXR5RGF0YTogcmVnaW9uRGF0YSwKICAgICAgY2l0eU9wdGlvbnM6IFtdLAogICAgICBxdWVyeUFyZWE6IFtdLAogICAgICBwcm92aW5jZVRyZWVzOiBbXSwKICAgICAgdmlld09wZW46IGZhbHNlLAogICAgICB2aWV3OiB7fSwKICAgICAgcHJvY0luc0lkOiB1bmRlZmluZWQsCiAgICAgIHRhc2tJZDogdW5kZWZpbmVkLAogICAgICBiaXpLZXk6IHVuZGVmaW5lZCwKICAgICAgc2hvd0FyZWE6IGZhbHNlCiAgICB9OwogIH0sCiAgd2F0Y2g6IHsKICAgIC8vIOagueaNruWQjeensOetm+mAiemDqOmXqOagkQogICAgLy8gZGVwdE5hbWUodmFsKSB7CiAgICAvLyAgIHRoaXMuJHJlZnMudHJlZS5maWx0ZXIodmFsKTsKICAgIC8vIH0KICB9LAogIGNyZWF0ZWQ6IGZ1bmN0aW9uIGNyZWF0ZWQoKSB7CiAgICB2YXIgX3RoaXMgPSB0aGlzOwogICAgaWYgKHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIucm9sZXMgJiYgKHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIucm9sZXMuaW5jbHVkZXMoImFkbWluIikgfHwgdGhpcy4kc3RvcmUuc3RhdGUudXNlci5yb2xlcy5pbmNsdWRlcygidXNlcl9hZG1pbiIpIHx8IHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIucm9sZXMuaW5jbHVkZXMoInJlcG9ydF9hZG1pbiIpIC8vIHx8IHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIucm9sZXMuaW5jbHVkZXMoInByb3ZpbmNlX2FkbWluIikKICAgICkpIHsKICAgICAgdGhpcy5zaG93QXJlYSA9IHRydWU7CiAgICB9CiAgICB0aGlzLmdldExpc3QoKTsKICAgIC8vdGhpcy5nZXRUcmVlc2VsZWN0KCk7CiAgICB0aGlzLmdldERpY3RzKCJzeXNfbm9ybWFsX2Rpc2FibGUiKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICBfdGhpcy5zdGF0dXNPcHRpb25zID0gcmVzcG9uc2UuZGF0YTsKICAgIH0pOwogICAgdGhpcy5nZXREaWN0cygic3lzX3VzZXJfc2V4IikudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgX3RoaXMuc2V4T3B0aW9ucyA9IHJlc3BvbnNlLmRhdGE7CiAgICB9KTsKICAgIHRoaXMuZ2V0Q29uZmlnS2V5KCJzeXMudXNlci5pbml0UGFzc3dvcmQiKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICBfdGhpcy5pbml0UGFzc3dvcmQgPSByZXNwb25zZS5tc2c7CiAgICB9KTsKICAgIHRoaXMuZ2V0RGljdHMoInByX3Ntc19ub3RpZnkiKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICBfdGhpcy5zbXNTZW5kT3B0aW9ucyA9IHJlc3BvbnNlLmRhdGE7CiAgICB9KTsKICAgIHRoaXMuZ2V0RGljdHMoInByX2F1ZGl0X3N0YXR1cyIpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgIHZhciB0eXBlID0gMDsKICAgICAgaWYgKF90aGlzLiRzdG9yZS5zdGF0ZS51c2VyLnJvbGVzKSB7CiAgICAgICAgaWYgKF90aGlzLiRzdG9yZS5zdGF0ZS51c2VyLnJvbGVzLmluY2x1ZGVzKCJjb21tb24iKSkgewogICAgICAgICAgdHlwZSA9IDE7CiAgICAgICAgfQogICAgICAgIGlmIChfdGhpcy4kc3RvcmUuc3RhdGUudXNlci5yb2xlcy5pbmNsdWRlcygicHJvdmluY2VfYWRtaW4iKSkgewogICAgICAgICAgdHlwZSA9IDI7CiAgICAgICAgfQogICAgICAgIGlmIChfdGhpcy4kc3RvcmUuc3RhdGUudXNlci5yb2xlcy5pbmNsdWRlcygicmVwb3J0X2FkbWluIikpIHsKICAgICAgICAgIHR5cGUgPSAzOwogICAgICAgIH0KICAgICAgICBpZiAoX3RoaXMuJHN0b3JlLnN0YXRlLnVzZXIucm9sZXMuaW5jbHVkZXMoInVzZXJfYWRtaW4iKSkgewogICAgICAgICAgdHlwZSA9IDQ7CiAgICAgICAgfQogICAgICB9CiAgICAgIF90aGlzLmF1ZGl0U3RhdHVzT3B0aW9ucyA9IHJlc3BvbnNlLmRhdGE7CiAgICAgIHZhciBvcHQgPSBbXTsKICAgICAgb3B0LnB1c2goewogICAgICAgIGlkOiA5LAogICAgICAgIGxhYmVsOiAn5YWo6YOoJwogICAgICB9KTsKICAgICAgaWYgKHR5cGUgPT0gMiB8fCB0eXBlID09IDMgfHwgdHlwZSA9PSA0KSB7CiAgICAgICAgb3B0LnB1c2goewogICAgICAgICAgaWQ6IDEwLAogICAgICAgICAgbGFiZWw6ICfmnKrlrqHmibknCiAgICAgICAgfSk7CiAgICAgIH0KICAgICAgcmVzcG9uc2UuZGF0YS5mb3JFYWNoKGZ1bmN0aW9uIChlbGVtLCBpbmRleCkgewogICAgICAgIHZhciBvYmogPSB7fTsKICAgICAgICBvYmoubGFiZWwgPSBlbGVtLmRpY3RMYWJlbDsKICAgICAgICBvYmouaWQgPSBlbGVtLmRpY3RWYWx1ZTsKICAgICAgICBvcHQucHVzaChvYmopOwogICAgICB9KTsKICAgICAgdmFyIGF1ZGl0U3RhdHVzVHJlZSA9IHt9OwogICAgICBhdWRpdFN0YXR1c1RyZWUubGFiZWwgPSAi5a6h5qC454q25oCBIjsKICAgICAgYXVkaXRTdGF0dXNUcmVlLmNoaWxkcmVuID0gb3B0OwogICAgICB2YXIgYXVkaXRTdGF0dXNUcmVlcyA9IFtdOwogICAgICBhdWRpdFN0YXR1c1RyZWVzLnB1c2goYXVkaXRTdGF0dXNUcmVlKTsKICAgICAgX3RoaXMuYXVkaXRTdGF0dXNUcmVlID0gYXVkaXRTdGF0dXNUcmVlczsKICAgIH0pOwogICAgLy/miYDlnKjljLrln5/mlbDmja7lpITnkIYKICAgIHZhciBvcHQgPSBbXTsKICAgIG9wdC5wdXNoKHsKICAgICAgaWQ6IDAsCiAgICAgIGxhYmVsOiAn5YWo6YOoJwogICAgfSk7CiAgICBwcm92aW5jZUFuZENpdHlEYXRhLmZvckVhY2goZnVuY3Rpb24gKGVsZW0sIGluZGV4KSB7CiAgICAgIHZhciBvYmogPSB7fTsKICAgICAgb2JqLmxhYmVsID0gZWxlbS5sYWJlbDsKICAgICAgb2JqLmlkID0gZWxlbS5sYWJlbDsKICAgICAgb3B0LnB1c2gob2JqKTsKICAgIH0pOwogICAgdmFyIHByb3ZpbmNlVHJlZSA9IHt9OwogICAgcHJvdmluY2VUcmVlLmxhYmVsID0gIuaJgOWcqOWMuuWfnyI7CiAgICBwcm92aW5jZVRyZWUuY2hpbGRyZW4gPSBvcHQ7CiAgICB2YXIgcHJvdmluY2VUcmVlcyA9IFtdOwogICAgcHJvdmluY2VUcmVlcy5wdXNoKHByb3ZpbmNlVHJlZSk7CiAgICB0aGlzLnByb3ZpbmNlVHJlZXMgPSBwcm92aW5jZVRyZWVzOwogICAgaWYgKHRoaXMuX2lzTW9iaWxlKCkpIHsKICAgICAgdGhpcy5pc01vYmlsZSA9IHRydWU7CiAgICAgIHRoaXMucGFnZUxheW91dCA9ICJ0b3RhbCwgcHJldiwgbmV4dCwganVtcGVyIjsKICAgIH0KICB9LAogIG1ldGhvZHM6IHsKICAgIF9pc01vYmlsZTogZnVuY3Rpb24gX2lzTW9iaWxlKCkgewogICAgICB2YXIgZmxhZyA9IG5hdmlnYXRvci51c2VyQWdlbnQubWF0Y2goLyhwaG9uZXxwYWR8cG9kfGlQaG9uZXxpUG9kfGlvc3xpUGFkfEFuZHJvaWR8TW9iaWxlfEJsYWNrQmVycnl8SUVNb2JpbGV8TVFRQnJvd3NlcnxKVUN8RmVubmVjfHdPU0Jyb3dzZXJ8QnJvd3Nlck5HfFdlYk9TfFN5bWJpYW58V2luZG93cyBQaG9uZSkvaSk7CiAgICAgIHJldHVybiBmbGFnOwogICAgfSwKICAgIC8qKiDmn6Xor6LnlKjmiLfliJfooaggKi9nZXRMaXN0OiBmdW5jdGlvbiBnZXRMaXN0KCkgewogICAgICB2YXIgX3RoaXMyID0gdGhpczsKICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsKICAgICAgbGlzdFVzZXIodGhpcy5hZGREYXRlUmFuZ2UodGhpcy5xdWVyeVBhcmFtcywgdGhpcy5kYXRlUmFuZ2UpKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgIF90aGlzMi51c2VyTGlzdCA9IHJlc3BvbnNlLnJvd3M7CiAgICAgICAgX3RoaXMyLnRvdGFsID0gcmVzcG9uc2UudG90YWw7CiAgICAgICAgX3RoaXMyLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgfSk7CiAgICB9LAogICAgLy8g55So5oi35oCn5Yir5a2X5YW457+76K+RCiAgICBzZXhGb3JtYXQ6IGZ1bmN0aW9uIHNleEZvcm1hdChyb3csIGNvbHVtbikgewogICAgICByZXR1cm4gdGhpcy5zZWxlY3REaWN0TGFiZWwodGhpcy5zZXhPcHRpb25zLCByb3cuc2V4KTsKICAgIH0sCiAgICAvLyDluJDlj7fnirbmgIHlrZflhbjnv7vor5EKICAgIHN0YXR1c0Zvcm1hdDogZnVuY3Rpb24gc3RhdHVzRm9ybWF0KHJvdywgY29sdW1uKSB7CiAgICAgIHJldHVybiB0aGlzLnNlbGVjdERpY3RMYWJlbCh0aGlzLnN0YXR1c09wdGlvbnMsIHJvdy5zdGF0dXMpOwogICAgfSwKICAgIC8vIOefreS/oemAmuefpeWtl+WFuOe/u+ivkQogICAgc21zU2VuZEZvcm1hdDogZnVuY3Rpb24gc21zU2VuZEZvcm1hdChyb3csIGNvbHVtbikgewogICAgICByZXR1cm4gdGhpcy5zZWxlY3REaWN0TGFiZWwodGhpcy5zbXNTZW5kT3B0aW9ucywgcm93LnNtc1NlbmQpOwogICAgfSwKICAgIC8vIOWuoeaguOeKtuaAgeWtl+WFuOe/u+ivkQogICAgYXVkaXRTdGF0dXNGb3JtYXQ6IGZ1bmN0aW9uIGF1ZGl0U3RhdHVzRm9ybWF0KHJvdywgY29sdW1uKSB7CiAgICAgIHJldHVybiB0aGlzLnNlbGVjdERpY3RMYWJlbCh0aGlzLmF1ZGl0U3RhdHVzT3B0aW9ucywgcm93LmF1ZGl0U3RhdHVzKTsKICAgIH0sCiAgICAvKiog5p+l6K+i6YOo6Zeo5LiL5ouJ5qCR57uT5p6EICovZ2V0VHJlZXNlbGVjdDogZnVuY3Rpb24gZ2V0VHJlZXNlbGVjdCgpIHsKICAgICAgdmFyIF90aGlzMyA9IHRoaXM7CiAgICAgIHRyZWVzZWxlY3QoKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgIF90aGlzMy5kZXB0T3B0aW9ucyA9IHJlc3BvbnNlLmRhdGE7CiAgICAgIH0pOwogICAgfSwKICAgIC8vIOetm+mAieiKgueCuQogICAgZmlsdGVyTm9kZTogZnVuY3Rpb24gZmlsdGVyTm9kZSh2YWx1ZSwgZGF0YSkgewogICAgICBpZiAoIXZhbHVlKSByZXR1cm4gdHJ1ZTsKICAgICAgcmV0dXJuIGRhdGEubGFiZWwuaW5kZXhPZih2YWx1ZSkgIT09IC0xOwogICAgfSwKICAgIC8vIOiKgueCueWNleWHu+S6i+S7tgogICAgaGFuZGxlTm9kZUNsaWNrOiBmdW5jdGlvbiBoYW5kbGVOb2RlQ2xpY2soZGF0YSkgewogICAgICBpZiAoZGF0YS5pZCA9PSA5KSB7CiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5hdWRpdFN0YXR1cyA9IHVuZGVmaW5lZDsKICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLm5vZGUgPSB1bmRlZmluZWQ7CiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5zcGFyZTEgPSB1bmRlZmluZWQ7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgaWYgKGRhdGEuaWQgPT0gMSB8fCBkYXRhLmlkID09IDEwKSB7CiAgICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmF1ZGl0U3RhdHVzID0gMTsKICAgICAgICAgIGlmICh0aGlzLiRzdG9yZS5zdGF0ZS51c2VyLnJvbGVzKSB7CiAgICAgICAgICAgIGlmICh0aGlzLiRzdG9yZS5zdGF0ZS51c2VyLnJvbGVzLmluY2x1ZGVzKCJwcm92aW5jZV9hZG1pbiIpKSB7CiAgICAgICAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5ub2RlID0gJ+ecgei0n+i0o+S6uic7CiAgICAgICAgICAgICAgaWYgKGRhdGEuaWQgPT0gMTApIHsKICAgICAgICAgICAgICAgIHRoaXMucXVlcnlQYXJhbXMuc3BhcmUxID0gJz0nOwogICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnNwYXJlMSA9ICchPSc7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9CiAgICAgICAgICAgIGlmICh0aGlzLiRzdG9yZS5zdGF0ZS51c2VyLnJvbGVzLmluY2x1ZGVzKCJyZXBvcnRfYWRtaW4iKSkgewogICAgICAgICAgICAgIHRoaXMucXVlcnlQYXJhbXMubm9kZSA9ICflrqHmoLjlkZgnOwogICAgICAgICAgICAgIGlmIChkYXRhLmlkID09IDEwKSB7CiAgICAgICAgICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnNwYXJlMSA9ICc9JzsKICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5zcGFyZTEgPSAnIT0nOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfQogICAgICAgICAgICBpZiAodGhpcy4kc3RvcmUuc3RhdGUudXNlci5yb2xlcy5pbmNsdWRlcygidXNlcl9hZG1pbiIpKSB7CiAgICAgICAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5ub2RlID0gJ+euoeeQhuWRmCc7CiAgICAgICAgICAgICAgaWYgKGRhdGEuaWQgPT0gMTApIHsKICAgICAgICAgICAgICAgIHRoaXMucXVlcnlQYXJhbXMuc3BhcmUxID0gJz0nOwogICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnNwYXJlMSA9ICchPSc7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMucXVlcnlQYXJhbXMuYXVkaXRTdGF0dXMgPSBkYXRhLmlkOwogICAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5ub2RlID0gdW5kZWZpbmVkOwogICAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5zcGFyZTEgPSB1bmRlZmluZWQ7CiAgICAgICAgfQogICAgICB9CiAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgfSwKICAgIC8vIOWMuuWfn+iKgueCueWNleWHu+S6i+S7tgogICAgaGFuZGxlUHJvdmluY2VDbGljazogZnVuY3Rpb24gaGFuZGxlUHJvdmluY2VDbGljayhkYXRhKSB7CiAgICAgIGlmIChkYXRhLmlkID09IDApIHsKICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnByb3ZpbmNlID0gdW5kZWZpbmVkOwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMucXVlcnlQYXJhbXMucHJvdmluY2UgPSBkYXRhLmlkOwogICAgICB9CiAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgfSwKICAgIC8vIOeUqOaIt+eKtuaAgeS/ruaUuQogICAgaGFuZGxlU3RhdHVzQ2hhbmdlOiBmdW5jdGlvbiBoYW5kbGVTdGF0dXNDaGFuZ2Uocm93KSB7CiAgICAgIHZhciBfdGhpczQgPSB0aGlzOwogICAgICB2YXIgdGV4dCA9IHJvdy5zdGF0dXMgPT09ICIwIiA/ICLlkK/nlKgiIDogIuWBnOeUqCI7CiAgICAgIHRoaXMuJGNvbmZpcm0oJ+ehruiupOimgSInICsgdGV4dCArICciIicgKyByb3cudXNlck5hbWUgKyAnIueUqOaIt+WQlz8nLCAi6K2m5ZGKIiwgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAi56Gu5a6aIiwKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAi5Y+W5raIIiwKICAgICAgICB0eXBlOiAid2FybmluZyIKICAgICAgfSkudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgcmV0dXJuIGNoYW5nZVVzZXJTdGF0dXMocm93LnVzZXJJZCwgcm93LnN0YXR1cyk7CiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgIF90aGlzNC5tc2dTdWNjZXNzKHRleHQgKyAi5oiQ5YqfIik7CiAgICAgIH0pLmNhdGNoKGZ1bmN0aW9uICgpIHsKICAgICAgICByb3cuc3RhdHVzID0gcm93LnN0YXR1cyA9PT0gIjAiID8gIjEiIDogIjAiOwogICAgICB9KTsKICAgIH0sCiAgICAvLyDlj5bmtojmjInpkq4KICAgIGNhbmNlbDogZnVuY3Rpb24gY2FuY2VsKCkgewogICAgICB0aGlzLm9wZW4gPSBmYWxzZTsKICAgICAgdGhpcy5yZXNldCgpOwogICAgfSwKICAgIC8vIOihqOWNlemHjee9rgogICAgcmVzZXQ6IGZ1bmN0aW9uIHJlc2V0KCkgewogICAgICB0aGlzLmZvcm0gPSB7CiAgICAgICAgdXNlcklkOiB1bmRlZmluZWQsCiAgICAgICAgZGVwdElkOiB1bmRlZmluZWQsCiAgICAgICAgdXNlck5hbWU6IHVuZGVmaW5lZCwKICAgICAgICBuaWNrTmFtZTogdW5kZWZpbmVkLAogICAgICAgIHVzZXJUeXBlOiB1bmRlZmluZWQsCiAgICAgICAgZW1haWw6IHVuZGVmaW5lZCwKICAgICAgICBwaG9uZW51bWJlcjogdW5kZWZpbmVkLAogICAgICAgIHNleDogIjAiLAogICAgICAgIGF2YXRhcjogdW5kZWZpbmVkLAogICAgICAgIHBhc3N3b3JkOiB1bmRlZmluZWQsCiAgICAgICAgc3RhdHVzOiAiMCIsCiAgICAgICAgZGVsRmxhZzogdW5kZWZpbmVkLAogICAgICAgIGxvZ2luSXA6IHVuZGVmaW5lZCwKICAgICAgICBsb2dpbkRhdGU6IHVuZGVmaW5lZCwKICAgICAgICBjcmVhdGVCeTogdW5kZWZpbmVkLAogICAgICAgIGNyZWF0ZVRpbWU6IHVuZGVmaW5lZCwKICAgICAgICB1cGRhdGVCeTogdW5kZWZpbmVkLAogICAgICAgIHVwZGF0ZVRpbWU6IHVuZGVmaW5lZCwKICAgICAgICByZW1hcms6IHVuZGVmaW5lZCwKICAgICAgICBjb21wYW55OiB1bmRlZmluZWQsCiAgICAgICAgYnVzaW5lc3NObzogdW5kZWZpbmVkLAogICAgICAgIGJ1c2luZXNzTm9QaWM6IHVuZGVmaW5lZCwKICAgICAgICBwcm92aW5jZTogW10sCiAgICAgICAgYWRkcmVzczogdW5kZWZpbmVkLAogICAgICAgIGRlYWxlcjogdW5kZWZpbmVkLAogICAgICAgIHNtc1NlbmQ6ICIwIiwKICAgICAgICBhdWRpdFN0YXR1czogIjAiLAogICAgICAgIHJvbGVJZHM6IFtdCiAgICAgIH07CiAgICAgIHRoaXMucmVzZXRGb3JtKCJmb3JtIik7CiAgICB9LAogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqL2hhbmRsZVF1ZXJ5OiBmdW5jdGlvbiBoYW5kbGVRdWVyeSgpIHsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlID0gMTsKICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICB9LAogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqL3Jlc2V0UXVlcnk6IGZ1bmN0aW9uIHJlc2V0UXVlcnkoKSB7CiAgICAgIHRoaXMuZGF0ZVJhbmdlID0gW107CiAgICAgIHRoaXMucXVlcnlBcmVhID0gW107CiAgICAgIHRoaXMucmVzZXRGb3JtKCJxdWVyeUZvcm0iKTsKICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOwogICAgfSwKICAgIC8vIOWkmumAieahhumAieS4reaVsOaNrgogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlOiBmdW5jdGlvbiBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7CiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgIHJldHVybiBpdGVtLnVzZXJJZDsKICAgICAgfSk7CiAgICAgIHRoaXMuc2luZ2xlID0gc2VsZWN0aW9uLmxlbmd0aCAhPSAxOwogICAgICB0aGlzLm11bHRpcGxlID0gIXNlbGVjdGlvbi5sZW5ndGg7CiAgICB9LAogICAgaGFuZGxlVmlldzogZnVuY3Rpb24gaGFuZGxlVmlldyhyb3cpIHsKICAgICAgdmFyIF90aGlzNSA9IHRoaXM7CiAgICAgIHRoaXMudmlldyA9IEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkocm93KSk7CiAgICAgIDsKICAgICAgdGhpcy52aWV3LmF1ZGl0U3RhdHVzID0gdGhpcy5hdWRpdFN0YXR1c0Zvcm1hdChyb3cpOwogICAgICB0aGlzLnZpZXcuc3RhdHVzID0gdGhpcy5zdGF0dXNGb3JtYXQocm93KTsKICAgICAgdGhpcy52aWV3LnNtc1NlbmQgPSB0aGlzLnNtc1NlbmRGb3JtYXQocm93KTsKICAgICAgdGhpcy50aXRsZSA9ICLmn6XnnIvnlKjmiLfor6bmg4UiOwogICAgICB2YXIgcGFyYW1zID0gewogICAgICAgIGJpektleTogcm93LnVzZXJJZCwKICAgICAgICBkZWZLZXk6ICdwcm9jZXNzX3VzZXJfcmVnJwogICAgICB9OwogICAgICB0aGlzLmJpektleSA9IHJvdy51c2VySWQ7CiAgICAgIGdldEluc0lkQnlCaXpLZXkocGFyYW1zKS50aGVuKGZ1bmN0aW9uIChyZXNwKSB7CiAgICAgICAgaWYgKHJlc3AuZGF0YSAmJiByZXNwLmRhdGEuaW5zdGFuY2VJZCkgewogICAgICAgICAgX3RoaXM1LnByb2NJbnNJZCA9IHJlc3AuZGF0YS5pbnN0YW5jZUlkOwogICAgICAgICAgX3RoaXM1LnRhc2tJZCA9IHJlc3AuZGF0YS50YXNrSWQ7CiAgICAgICAgICBpZiAocmVzcC5kYXRhLmluc3RhbmNlSWQgJiYgIXJlc3AuZGF0YS5lbmRUaW1lICYmIHJlc3AuZGF0YS5hc3NpZ25lZSA9PSBfdGhpczUuJHN0b3JlLnN0YXRlLnVzZXIudXNlcklkKSB7CiAgICAgICAgICAgIF90aGlzNS5maW5pc2hlZCA9IGZhbHNlOwogICAgICAgICAgfSBlbHNlIGlmIChfdGhpczUuJHN0b3JlLnN0YXRlLnVzZXIucm9sZXMgJiYgX3RoaXM1LiRzdG9yZS5zdGF0ZS51c2VyLnJvbGVzLmluY2x1ZGVzKCJyZXBvcnRfYWRtaW4iKSkgewogICAgICAgICAgICAvL+WuoeaguOWRmOinkuiJsuS4jeaOp+WItuiwgeaTjeS9nAogICAgICAgICAgICBfdGhpczUuZmluaXNoZWQgPSBmYWxzZTsKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIF90aGlzNS5maW5pc2hlZCA9IHRydWU7CiAgICAgICAgICB9CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIF90aGlzNS5maW5pc2hlZCA9IHRydWU7CiAgICAgICAgfQogICAgICAgIF90aGlzNS52aWV3T3BlbiA9IHRydWU7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDmlrDlop7mjInpkq7mk43kvZwgKi9oYW5kbGVBZGQ6IGZ1bmN0aW9uIGhhbmRsZUFkZCgpIHsKICAgICAgLy8gdGhpcy5yZXNldCgpOwogICAgICAvLyAvL3RoaXMuZ2V0VHJlZXNlbGVjdCgpOwogICAgICAvLyBnZXRVc2VyKCkudGhlbigocmVzcG9uc2UpID0+IHsKICAgICAgLy8gICB0aGlzLnBvc3RPcHRpb25zID0gcmVzcG9uc2UucG9zdHM7CiAgICAgIC8vICAgdGhpcy5yb2xlT3B0aW9ucyA9IHJlc3BvbnNlLnJvbGVzOwogICAgICAvLyAgIHRoaXMub3BlbiA9IHRydWU7CiAgICAgIC8vICAgdGhpcy50aXRsZSA9ICLmt7vliqDnlKjmiLciOwogICAgICAvLyAgIHRoaXMuZm9ybS5wYXNzd29yZCA9IHRoaXMuaW5pdFBhc3N3b3JkOwogICAgICAvLyB9KTsKICAgICAgdGhpcy4kcm91dGVyLnB1c2goewogICAgICAgIHBhdGg6ICcvc3lzdGVtL3VzZXIvZm9ybScsCiAgICAgICAgcXVlcnk6IHsKICAgICAgICAgIGJ1c2luZXNzS2V5OiB1bmRlZmluZWQsCiAgICAgICAgICBmb3JtRWRpdDogdHJ1ZQogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLyoqIOS/ruaUueaMiemSruaTjeS9nCAqL2hhbmRsZVVwZGF0ZTogZnVuY3Rpb24gaGFuZGxlVXBkYXRlKHJvdykgewogICAgICB2YXIgX3RoaXM2ID0gdGhpczsKICAgICAgdGhpcy5yZXNldCgpOwogICAgICAvL3RoaXMuZ2V0VHJlZXNlbGVjdCgpOwogICAgICB2YXIgdXNlcklkID0gcm93LnVzZXJJZCB8fCB0aGlzLmlkczsKICAgICAgZ2V0VXNlcih1c2VySWQpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgX3RoaXM2LmZvcm0gPSByZXNwb25zZS5kYXRhOwogICAgICAgIF90aGlzNi5wb3N0T3B0aW9ucyA9IHJlc3BvbnNlLnBvc3RzOwogICAgICAgIF90aGlzNi5yb2xlT3B0aW9ucyA9IHJlc3BvbnNlLnJvbGVzOwogICAgICAgIF90aGlzNi5mb3JtLnBvc3RJZHMgPSByZXNwb25zZS5wb3N0SWRzOwogICAgICAgIF90aGlzNi5mb3JtLnJvbGVJZHMgPSByZXNwb25zZS5yb2xlSWRzOwogICAgICAgIF90aGlzNi5vcGVuID0gdHJ1ZTsKICAgICAgICBfdGhpczYudGl0bGUgPSAi5L+u5pS555So5oi3IjsKICAgICAgICBfdGhpczYuZm9ybS5wYXNzd29yZCA9ICIiOwogICAgICAgIHZhciBwcm92aW5jZXMgPSByZXNwb25zZS5kYXRhLnByb3ZpbmNlOwogICAgICAgIGlmIChwcm92aW5jZXMubGVuZ3RoID4gMCkgewogICAgICAgICAgdmFyIGFkZHJlc3MgPSBwcm92aW5jZXMuc3BsaXQoIi8iKTsKICAgICAgICAgIHZhciBjaXR5cyA9IFtdOwogICAgICAgICAgLy8g55yB5Lu9CiAgICAgICAgICBpZiAoYWRkcmVzcy5sZW5ndGggPiAwKSBjaXR5cy5wdXNoKFRleHRUb0NvZGVbYWRkcmVzc1swXV0uY29kZSk7CiAgICAgICAgICAvLyDln47luIIKICAgICAgICAgIGlmIChhZGRyZXNzLmxlbmd0aCA+IDEpIGNpdHlzLnB1c2goVGV4dFRvQ29kZVthZGRyZXNzWzBdXVthZGRyZXNzWzFdXS5jb2RlKTsKICAgICAgICAgIC8vIOWcsOWMugogICAgICAgICAgaWYgKGFkZHJlc3MubGVuZ3RoID4gMikgY2l0eXMucHVzaChUZXh0VG9Db2RlW2FkZHJlc3NbMF1dW2FkZHJlc3NbMV1dW2FkZHJlc3NbMl1dLmNvZGUpOwogICAgICAgICAgX3RoaXM2LmNpdHlPcHRpb25zID0gY2l0eXM7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvKiog6YeN572u5a+G56CB5oyJ6ZKu5pON5L2cICovaGFuZGxlUmVzZXRQd2Q6IGZ1bmN0aW9uIGhhbmRsZVJlc2V0UHdkKHJvdykgewogICAgICB2YXIgX3RoaXM3ID0gdGhpczsKICAgICAgdGhpcy4kcHJvbXB0KCfor7fovpPlhaUiJyArIHJvdy51c2VyTmFtZSArICci55qE5paw5a+G56CBJywgIuaPkOekuiIsIHsKICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogIuehruWumiIsCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogIuWPlua2iCIKICAgICAgfSkudGhlbihmdW5jdGlvbiAoX3JlZikgewogICAgICAgIHZhciB2YWx1ZSA9IF9yZWYudmFsdWU7CiAgICAgICAgcmVzZXRVc2VyUHdkKHJvdy51c2VySWQsIHZhbHVlKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgICAgX3RoaXM3Lm1zZ1N1Y2Nlc3MoIuS/ruaUueaIkOWKn++8jOaWsOWvhueggeaYr++8miIgKyB2YWx1ZSk7CiAgICAgICAgfSk7CiAgICAgIH0pLmNhdGNoKGZ1bmN0aW9uICgpIHt9KTsKICAgIH0sCiAgICAvKiog5o+Q5Lqk5oyJ6ZKuICovCiAgICBzdWJtaXRGb3JtOiBmdW5jdGlvbiBzdWJtaXRGb3JtKCkgewogICAgICB2YXIgX3RoaXM4ID0gdGhpczsKICAgICAgaWYgKHRoaXMuY2l0eU9wdGlvbnMubGVuZ3RoIDwgMSkge30KICAgICAgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKGZ1bmN0aW9uICh2YWxpZCkgewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgaWYgKF90aGlzOC5mb3JtLnVzZXJJZCAhPSB1bmRlZmluZWQpIHsKICAgICAgICAgICAgdXBkYXRlVXNlcihfdGhpczguZm9ybSkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICAgICAgICBfdGhpczgubXNnU3VjY2Vzcygi5L+u5pS55oiQ5YqfIik7CiAgICAgICAgICAgICAgX3RoaXM4Lm9wZW4gPSBmYWxzZTsKICAgICAgICAgICAgICBfdGhpczguZ2V0TGlzdCgpOwogICAgICAgICAgICB9KTsKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIGFkZFVzZXIoX3RoaXM4LmZvcm0pLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgICAgICAgX3RoaXM4Lm1zZ1N1Y2Nlc3MoIuaWsOWinuaIkOWKnyIpOwogICAgICAgICAgICAgIF90aGlzOC5vcGVuID0gZmFsc2U7CiAgICAgICAgICAgICAgX3RoaXM4LmdldExpc3QoKTsKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvKiog5Yig6Zmk5oyJ6ZKu5pON5L2cICovaGFuZGxlRGVsZXRlOiBmdW5jdGlvbiBoYW5kbGVEZWxldGUocm93KSB7CiAgICAgIHZhciBfdGhpczkgPSB0aGlzOwogICAgICB2YXIgdXNlcklkcyA9IHJvdy51c2VySWQgfHwgdGhpcy5pZHM7CiAgICAgIHRoaXMuJGNvbmZpcm0oJ+aYr+WQpuehruiupOWIoOmZpOeUqOaIt+e8luWPt+S4uiInICsgdXNlcklkcyArICci55qE5pWw5o2u6aG5PycsICLorablkYoiLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLAogICAgICAgIHR5cGU6ICJ3YXJuaW5nIgogICAgICB9KS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICByZXR1cm4gZGVsVXNlcih1c2VySWRzKTsKICAgICAgfSkudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgX3RoaXM5LmdldExpc3QoKTsKICAgICAgICBfdGhpczkubXNnU3VjY2Vzcygi5Yig6Zmk5oiQ5YqfIik7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDlr7zlhaXmjInpkq7mk43kvZwgKi9oYW5kbGVJbXBvcnQ6IGZ1bmN0aW9uIGhhbmRsZUltcG9ydCgpIHsKICAgICAgdGhpcy51cGxvYWQudGl0bGUgPSAi55So5oi35a+85YWlIjsKICAgICAgdGhpcy51cGxvYWQub3BlbiA9IHRydWU7CiAgICB9LAogICAgLyoqIOS4i+i9veaooeadv+aTjeS9nCAqL2ltcG9ydFRlbXBsYXRlOiBmdW5jdGlvbiBpbXBvcnRUZW1wbGF0ZSgpIHsKICAgICAgdmFyIF90aGlzMTAgPSB0aGlzOwogICAgICBfaW1wb3J0VGVtcGxhdGUoKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgIF90aGlzMTAuZG93bmxvYWQocmVzcG9uc2UubXNnKTsKICAgICAgfSk7CiAgICB9LAogICAgLy8g5paH5Lu25LiK5Lyg5Lit5aSE55CGCiAgICBoYW5kbGVGaWxlVXBsb2FkUHJvZ3Jlc3M6IGZ1bmN0aW9uIGhhbmRsZUZpbGVVcGxvYWRQcm9ncmVzcyhldmVudCwgZmlsZSwgZmlsZUxpc3QpIHsKICAgICAgdGhpcy51cGxvYWQuaXNVcGxvYWRpbmcgPSB0cnVlOwogICAgfSwKICAgIC8vIOaWh+S7tuS4iuS8oOaIkOWKn+WkhOeQhgogICAgaGFuZGxlRmlsZVN1Y2Nlc3M6IGZ1bmN0aW9uIGhhbmRsZUZpbGVTdWNjZXNzKHJlc3BvbnNlLCBmaWxlLCBmaWxlTGlzdCkgewogICAgICB0aGlzLnVwbG9hZC5vcGVuID0gZmFsc2U7CiAgICAgIHRoaXMudXBsb2FkLmlzVXBsb2FkaW5nID0gZmFsc2U7CiAgICAgIHRoaXMuJHJlZnMudXBsb2FkLmNsZWFyRmlsZXMoKTsKICAgICAgdGhpcy4kYWxlcnQocmVzcG9uc2UubXNnLCAi5a+85YWl57uT5p6cIiwgewogICAgICAgIGRhbmdlcm91c2x5VXNlSFRNTFN0cmluZzogdHJ1ZQogICAgICB9KTsKICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICB9LAogICAgLy8g5o+Q5Lqk5LiK5Lyg5paH5Lu2CiAgICBzdWJtaXRGaWxlRm9ybTogZnVuY3Rpb24gc3VibWl0RmlsZUZvcm0oKSB7CiAgICAgIHRoaXMuJHJlZnMudXBsb2FkLnN1Ym1pdCgpOwogICAgfSwKICAgIGhhbmRsZUNpdHlDaGFuZ2U6IGZ1bmN0aW9uIGhhbmRsZUNpdHlDaGFuZ2UodmFsdWUpIHsKICAgICAgaWYgKCF2YWx1ZSB8fCB2YWx1ZS5sZW5ndGggPT0gMCkgewogICAgICAgIHRoaXMuY2l0eU9wdGlvbnMgPSBudWxsOwogICAgICAgIHRoaXMuZm9ybS5wcm92aW5jZSA9IHVuZGVmaW5lZDsKICAgICAgICB0aGlzLmZvcm0uZGlzdHJpY3QgPSB1bmRlZmluZWQ7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIHRoaXMuY2l0eU9wdGlvbnMgPSB2YWx1ZTsKICAgICAgdmFyIHR4dCA9ICIiOwogICAgICB2YWx1ZS5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgdHh0ICs9IENvZGVUb1RleHRbaXRlbV0gKyAiLyI7CiAgICAgIH0pOwogICAgICBpZiAodHh0Lmxlbmd0aCA+IDEpIHsKICAgICAgICB0eHQgPSB0eHQuc3Vic3RyaW5nKDAsIHR4dC5sZW5ndGggLSAxKTsKICAgICAgICB0aGlzLmZvcm0ucHJvdmluY2UgPSB0eHQ7CiAgICAgICAgdGhpcy5mb3JtLmRpc3RyaWN0ID0gQ29kZVRvVGV4dFt2YWx1ZVswXV07CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy5mb3JtLnByb3ZpbmNlID0gdW5kZWZpbmVkOwogICAgICB9CiAgICB9LAogICAgaGFuZGxlUXVlcnlDaXR5Q2hhbmdlOiBmdW5jdGlvbiBoYW5kbGVRdWVyeUNpdHlDaGFuZ2UodmFsdWUpIHsKICAgICAgdGhpcy5xdWVyeUFyZWEgPSB2YWx1ZTsKICAgICAgdmFyIHR4dCA9ICIiOwogICAgICB2YWx1ZS5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgdHh0ICs9IENvZGVUb1RleHRbaXRlbV0gKyAiLyI7CiAgICAgIH0pOwogICAgICBpZiAodHh0Lmxlbmd0aCA+IDEpIHsKICAgICAgICB0eHQgPSB0eHQuc3Vic3RyaW5nKDAsIHR4dC5sZW5ndGggLSAxKTsKICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnByb3ZpbmNlID0gdHh0OwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMucXVlcnlQYXJhbXMucHJvdmluY2UgPSB1bmRlZmluZWQ7CiAgICAgIH0KICAgIH0sCiAgICBjbGlja0V4cG9ydDogZnVuY3Rpb24gY2xpY2tFeHBvcnQoKSB7CiAgICAgIHRoaXMuc2hvd0V4cG9ydCA9IHRydWU7CiAgICB9LAogICAgY2xpY2tQcmludDogZnVuY3Rpb24gY2xpY2tQcmludCgpIHsKICAgICAgdGhpcy5zaG93UHJpbnQgPSB0cnVlOwogICAgfSwKICAgIC8qKiDlr7zlh7rmjInpkq7mk43kvZwgKi9oYW5kbGVFeHBvcnQ6IGZ1bmN0aW9uIGhhbmRsZUV4cG9ydCh0eXBlKSB7CiAgICAgIHZhciBfdGhpczExID0gdGhpczsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5zcGFyZTEgPSB0eXBlOwogICAgICB2YXIgY29sID0gW107CiAgICAgIHRoaXMuY29sdW1ucy5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgaWYgKGl0ZW0udmlzaWJsZSkgewogICAgICAgICAgY29sLnB1c2goaXRlbS5sYWJlbCk7CiAgICAgICAgfQogICAgICB9KTsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5zcGFyZTIgPSBjb2wuam9pbignLCcpOwogICAgICB2YXIgcXVlcnlQYXJhbXMgPSB0aGlzLnF1ZXJ5UGFyYW1zOwogICAgICB0aGlzLiRjb25maXJtKCLmmK/lkKbnoa7orqTlr7zlh7rnlKjmiLfmkJzntKLnu5PmnpwiICsgKHR5cGUgPT0gMCA/ICfmnKzpobUnIDogJ+WFqOmDqCcpICsgIuaVsOaNrumhuT8iLCAi6K2m5ZGKIiwgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAi56Gu5a6aIiwKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAi5Y+W5raIIiwKICAgICAgICB0eXBlOiAid2FybmluZyIKICAgICAgfSkudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgcmV0dXJuIGV4cG9ydFVzZXIocXVlcnlQYXJhbXMpOwogICAgICB9KS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgIF90aGlzMTEuZG93bmxvYWQocmVzcG9uc2UubXNnKTsKICAgICAgICBfdGhpczExLnNob3dFeHBvcnQgPSBmYWxzZTsKICAgICAgICBsb2FkaW5nLmNsb3NlKCk7CiAgICAgIH0pOwogICAgfSwKICAgIGhhbmRsZVByaW50OiBmdW5jdGlvbiBoYW5kbGVQcmludCh0eXBlKSB7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMuc3BhcmUxID0gdHlwZTsKICAgICAgdmFyIHByb3BlcnRpZXMgPSBbXTsKICAgICAgcHJvcGVydGllcy5wdXNoKHsKICAgICAgICBmaWVsZDogJ2luZGV4JywKICAgICAgICBkaXNwbGF5TmFtZTogJ+W6j+WPtycKICAgICAgfSk7CiAgICAgIHRoaXMuY29sdW1ucy5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgaWYgKGl0ZW0udmlzaWJsZSkgewogICAgICAgICAgcHJvcGVydGllcy5wdXNoKHsKICAgICAgICAgICAgZmllbGQ6IGl0ZW0ua2V5LAogICAgICAgICAgICBkaXNwbGF5TmFtZTogaXRlbS5sYWJlbAogICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICB9KTsKICAgICAgcHJpbnRVc2VyKHRoaXMucXVlcnlQYXJhbXMpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgcHJpbnRKUyh7CiAgICAgICAgICBwcmludGFibGU6IHJlc3BvbnNlLmRhdGEsCiAgICAgICAgICB0eXBlOiAnanNvbicsCiAgICAgICAgICBwcm9wZXJ0aWVzOiBwcm9wZXJ0aWVzLAogICAgICAgICAgaGVhZGVyOiAnPGRpdiBzdHlsZT0idGV4dC1hbGlnbjogY2VudGVyIj48aDM+55So5oi35YiX6KGo5YiX6KGoPC9oMz48L2Rpdj4nLAogICAgICAgICAgdGFyZ2V0U3R5bGVzOiBbJyonXSwKICAgICAgICAgIGdyaWRIZWFkZXJTdHlsZTogJ2JvcmRlcjogMXB4IHNvbGlkICMwMDA7dGV4dC1hbGlnbjpjZW50ZXInLAogICAgICAgICAgZ3JpZFN0eWxlOiAnYm9yZGVyOiAxcHggc29saWQgIzAwMDt0ZXh0LWFsaWduOmNlbnRlcicsCiAgICAgICAgICBzdHlsZTogIkBwYWdlIHttYXJnaW46MCAxMG1tfSIKICAgICAgICB9KTsKICAgICAgfSk7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["listUser", "getUser", "<PERSON><PERSON><PERSON>", "addUser", "updateUser", "exportUser", "resetUserPwd", "changeUserStatus", "importTemplate", "printUser", "getToken", "treeselect", "Treeselect", "ImageUpload", "provinceAndCityData", "regionData", "CodeToText", "TextToCode", "flowable", "getInsIdByBizKey", "name", "components", "data", "isMobile", "pageLayout", "loading", "ids", "single", "multiple", "showSearch", "showExport", "showPrint", "total", "userList", "title", "deptOptions", "undefined", "open", "deptName", "initPassword", "date<PERSON><PERSON><PERSON>", "statusOptions", "sexOptions", "smsSendOptions", "auditStatusOptions", "roleOptions", "form", "defaultProps", "children", "label", "upload", "isUploading", "updateSupport", "headers", "Authorization", "url", "process", "env", "VUE_APP_BASE_API", "queryParams", "pageNum", "pageSize", "userName", "phonenumber", "status", "deptId", "auditStatus", "columns", "key", "index", "visible", "rules", "required", "message", "trigger", "company", "businessNo", "businessNoPic", "email", "type", "pattern", "nick<PERSON><PERSON>", "province", "address", "auditStatusTree", "cityOptions", "queryArea", "provinceTrees", "viewOpen", "view", "procInsId", "taskId", "bizKey", "showArea", "watch", "created", "_this", "$store", "state", "user", "roles", "includes", "getList", "getDicts", "then", "response", "getConfigKey", "msg", "opt", "push", "id", "for<PERSON>ach", "elem", "obj", "dict<PERSON><PERSON>l", "dict<PERSON><PERSON>ue", "auditStatusTrees", "provinceTree", "_isMobile", "methods", "flag", "navigator", "userAgent", "match", "_this2", "addDateRange", "rows", "sexFormat", "row", "column", "selectDictLabel", "sex", "statusFormat", "smsSendFormat", "smsSend", "auditStatusFormat", "getTreeselect", "_this3", "filterNode", "value", "indexOf", "handleNodeClick", "node", "spare1", "handleProvinceClick", "handleStatusChange", "_this4", "text", "$confirm", "confirmButtonText", "cancelButtonText", "userId", "msgSuccess", "catch", "cancel", "reset", "userType", "avatar", "password", "delFlag", "loginIp", "loginDate", "createBy", "createTime", "updateBy", "updateTime", "remark", "dealer", "roleIds", "resetForm", "handleQuery", "page", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "map", "item", "length", "handleView", "_this5", "JSON", "parse", "stringify", "params", "def<PERSON><PERSON>", "resp", "instanceId", "endTime", "assignee", "finished", "handleAdd", "$router", "path", "query", "businessKey", "formEdit", "handleUpdate", "_this6", "postOptions", "posts", "postIds", "provinces", "split", "citys", "code", "handleResetPwd", "_this7", "$prompt", "_ref", "submitForm", "_this8", "$refs", "validate", "valid", "handleDelete", "_this9", "userIds", "handleImport", "_this10", "download", "handleFileUploadProgress", "event", "file", "fileList", "handleFileSuccess", "clearFiles", "$alert", "dangerouslyUseHTMLString", "submitFileForm", "submit", "handleCityChange", "district", "txt", "substring", "handleQueryCityChange", "clickExport", "clickPrint", "handleExport", "_this11", "col", "spare2", "join", "close", "handlePrint", "properties", "field", "displayName", "printJS", "printable", "header", "targetStyles", "gridHeaderStyle", "gridStyle", "style"], "sources": ["src/views/system/user/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-row :gutter=\"20\">\n      <!--部门数据-->\n      <el-col :span=\"4\" :xs=\"24\">\n        <div class=\"head-container\">\n          <el-tree\n            :data=\"auditStatusTree\"\n            :props=\"defaultProps\"\n            :expand-on-click-node=\"false\"\n            :filter-node-method=\"filterNode\"\n            ref=\"tree\"\n            default-expand-all\n            node-key=\"id\"\n            :current-node-key=\"0\"\n            @node-click=\"handleNodeClick\"\n          />\n          <el-tree\n            :data=\"provinceTrees\"\n            :props=\"defaultProps\"\n            :expand-on-click-node=\"false\"\n            :filter-node-method=\"filterNode\"\n            ref=\"tree\"\n            :default-expand-all=\"isMobile?false:true\"\n            node-key=\"id\"\n            :current-node-key=\"0\"\n            @node-click=\"handleProvinceClick\"\n            v-if=\"showArea\"\n          />\n        </div>\n      </el-col>\n      <!--用户数据-->\n      <el-col :span=\"20\" :xs=\"24\">\n        <el-form\n          :model=\"queryParams\"\n          ref=\"queryForm\"\n          :inline=\"true\"\n          v-show=\"showSearch\"\n          label-width=\"100px\"\n        >\n          <!-- <el-form-item label=\"所在区域\" prop=\"area\">\n            <el-cascader\n              size=\"small\"\n              clearable\n              :options=\"provinceAndCityData\"\n              :props=\"{ checkStrictly: true, expandTrigger: 'hover' }\"\n              @change=\"handleQueryCityChange\"\n            />\n          </el-form-item> -->\n          <el-form-item label=\"用户名称\" prop=\"userName\">\n            <el-input\n              v-model=\"queryParams.userName\"\n              placeholder=\"请输入用户名称\"\n              clearable\n              size=\"small\"\n              style=\"width: 240px\"\n              @keyup.enter.native=\"handleQuery\"\n            />\n          </el-form-item>\n          <el-form-item label=\"手机号码\" prop=\"phonenumber\">\n            <el-input\n              v-model=\"queryParams.phonenumber\"\n              placeholder=\"请输入手机号码\"\n              clearable\n              size=\"small\"\n              style=\"width: 240px\"\n              @keyup.enter.native=\"handleQuery\"\n            />\n          </el-form-item>\n          <el-form-item label=\"状态\" prop=\"status\">\n            <el-select\n              v-model=\"queryParams.status\"              \n              placeholder=\"用户状态\"\n              clearable\n              size=\"small\"\n              style=\"width: 240px\"\n            >\n              <el-option\n                v-for=\"dict in statusOptions\"\n                :key=\"dict.dictValue\"\n                :label=\"dict.dictLabel\"\n                :value=\"dict.dictValue\"\n              />\n            </el-select>\n          </el-form-item>\n          <el-form-item label=\"提交时间\">\n            <el-date-picker\n              v-model=\"dateRange\"\n              size=\"small\"\n              style=\"width: 240px\"\n              value-format=\"yyyy-MM-dd\"\n              type=\"daterange\"\n              range-separator=\"-\"\n              start-placeholder=\"开始日期\"\n              end-placeholder=\"结束日期\"\n            ></el-date-picker>\n          </el-form-item>\n          <el-form-item>\n            <el-button\n              type=\"primary\"\n              icon=\"el-icon-search\"\n              size=\"mini\"\n              @click=\"handleQuery\"\n              >搜索</el-button\n            >\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\"\n              >重置</el-button\n            >\n          </el-form-item>\n        </el-form>\n\n        <el-row :gutter=\"10\" class=\"mb8\">\n          <el-col :span=\"1.5\">\n            <el-button\n              type=\"primary\"\n              plain\n              icon=\"el-icon-plus\"\n              size=\"mini\"\n              @click=\"handleAdd\"\n              v-hasPermi=\"['system:user:add']\"\n              >新增</el-button\n            >\n          </el-col>\n          <el-col :span=\"1.5\">\n            <el-button\n              type=\"success\"\n              plain\n              icon=\"el-icon-edit\"\n              size=\"mini\"\n              :disabled=\"single\"\n              @click=\"handleUpdate\"\n              v-hasPermi=\"['system:user:edit']\"\n              >修改</el-button\n            >\n          </el-col>\n          <el-col :span=\"1.5\">\n            <el-button\n              type=\"danger\"\n              plain\n              icon=\"el-icon-delete\"\n              size=\"mini\"\n              :disabled=\"multiple\"\n              @click=\"handleDelete\"\n              v-hasPermi=\"['system:user:remove']\"\n              >删除</el-button\n            >\n          </el-col>\n          <el-col :span=\"1.5\">\n            <el-button\n              type=\"info\"\n              plain\n              icon=\"el-icon-upload2\"\n              size=\"mini\"\n              @click=\"handleImport\"\n              v-hasPermi=\"['system:user:import']\"\n            >导入</el-button>\n          </el-col>\n          <el-col :span=\"1.5\">\n            <el-button\n              type=\"warning\"\n              plain\n              icon=\"el-icon-download\"\n              size=\"mini\"\n              @click=\"clickExport\"\n              v-hasPermi=\"['system:user:export']\"\n              >导出</el-button\n            >\n          </el-col>\n          <el-col :span=\"1.5\">\n            <el-button icon=\"el-icon-printer\" size=\"mini\" @click=\"clickPrint\" type=\"info\" plain v-hasPermi=\"['system:user:print']\">打印</el-button>\n          </el-col>\n          <right-toolbar\n            :showSearch.sync=\"showSearch\"\n            :showExport.sync=\"showExport\"\n            :showPrint.sync=\"showPrint\"\n            @queryTable=\"getList\"\n            @export=\"handleExport\"\n            @print=\"handlePrint\"\n            :columns=\"columns\"\n          ></right-toolbar>\n        </el-row>\n\n        <el-table\n          border\n          v-loading=\"loading\"\n          :data=\"userList\"\n          @selection-change=\"handleSelectionChange\"\n        >\n          <el-table-column type=\"selection\" width=\"50\" align=\"center\" />\n          <el-table-column type=\"index\" label=\"序号\" width=\"50\" />\n          <el-table-column\n            label=\"操作\"\n            align=\"center\"\n            width=\"60\"\n            class-name=\"small-padding fixed-width\"\n          >\n            <template slot-scope=\"scope\">\n              <el-button\n                size=\"mini\"\n                type=\"text\"\n                icon=\"el-icon-view\"\n                @click=\"handleView(scope.row)\"\n                >查看</el-button>\n              <el-button\n                size=\"mini\"\n                type=\"text\"\n                icon=\"el-icon-edit\"\n                @click=\"handleUpdate(scope.row)\"\n                v-hasPermi=\"['system:user:edit']\"\n              >修改</el-button>\n              <el-button\n                v-if=\"scope.row.userId !== 1\"\n                size=\"mini\"\n                type=\"text\"\n                icon=\"el-icon-delete\"\n                @click=\"handleDelete(scope.row)\"\n                v-hasPermi=\"['system:user:remove']\"\n              >删除</el-button>\n              <el-button\n                size=\"mini\"\n                type=\"text\"\n                icon=\"el-icon-key\"\n                @click=\"handleResetPwd(scope.row)\"\n                v-hasPermi=\"['system:user:resetPwd']\"\n              >重置</el-button>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"帐号状态\" align=\"center\" prop=\"status\" v-if=\"columns[0].visible\" >\n            <template slot-scope=\"scope\">\n              <el-switch\n                :disabled=\"!showArea\"\n                v-model=\"scope.row.status\"\n                active-value=\"0\"\n                inactive-value=\"1\"\n                @change=\"handleStatusChange(scope.row)\"\n              ></el-switch>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"用户ID\" align=\"center\" prop=\"userId\" v-if=\"columns[1].visible\" />\n          <el-table-column label=\"用户账号\" align=\"center\" prop=\"userName\" v-if=\"columns[2].visible\" />\n          <el-table-column label=\"报备人姓名\" align=\"center\" prop=\"nickName\" v-if=\"columns[3].visible\" />\n          <!-- <el-table-column label=\"用户类型\" align=\"center\" prop=\"userType\" v-if=\"columns[4].visible\" /> -->\n          <el-table-column\n            label=\"审核状态\"\n            align=\"center\"\n            prop=\"auditStatus\"\n            :formatter=\"auditStatusFormat\"\n             v-if=\"columns[5].visible\"\n          />\n          <el-table-column label=\"资料接收邮箱\" align=\"center\" prop=\"email\" v-if=\"columns[6].visible\" />\n          <el-table-column\n            label=\"报备人电话\"\n            align=\"center\"\n            prop=\"phonenumber\"\n             v-if=\"columns[7].visible\"\n          />\n          <el-table-column\n            label=\"用户性别\"\n            align=\"center\"\n            prop=\"sex\"\n            :formatter=\"sexFormat\"\n             v-if=\"columns[8].visible\"\n          />\n          <!-- <el-table-column\n            label=\"头像地址\"\n            align=\"center\"\n            prop=\"avatar\"\n            width=\"100\"\n             if=\"columns[9].visible\"\n          \n            <template slot-scope=\"scope\">\n              <img :src=\"scope.row.avatar\" width=\"100\" height=\"100\" />\n            </template>\n          </el-table-column>> -->\n          <el-table-column label=\"公司全称\" align=\"center\" prop=\"company\" v-if=\"columns[10].visible\" />\n          <el-table-column\n            label=\"营业执照号码\"\n            align=\"center\"\n            prop=\"businessNo\"\n             v-if=\"columns[11].visible\"\n          />\n          <el-table-column\n            label=\"营业执照图片\"\n            align=\"center\"\n            prop=\"businessNoPic\"\n            width=\"150\"\n             v-if=\"columns[12].visible\"\n          >\n            <template slot-scope=\"scope\">\n              <img :src=\"scope.row.businessNoPic\" width=\"150\" height=\"100\" />\n            </template>\n          </el-table-column>\n          <el-table-column label=\"所在区域\" align=\"center\" prop=\"province\" v-if=\"columns[13].visible\" />\n          <el-table-column label=\"资料邮寄地址\" align=\"center\" prop=\"address\" v-if=\"columns[14].visible\" />\n          <el-table-column label=\"隶属经销商\" align=\"center\" prop=\"dealer\" v-if=\"columns[15].visible\" />\n          <el-table-column\n            label=\"提交时间\"\n            align=\"center\"\n            prop=\"createTime\"\n            v-if=\"columns[16].visible\"\n            width=\"160\"\n          >\n            <template slot-scope=\"scope\">\n              <span>{{ parseTime(scope.row.createTime) }}</span>\n            </template>\n          </el-table-column>\n          <el-table-column\n            label=\"最后修改时间\"\n            align=\"center\"\n            prop=\"updateTime\"\n            v-if=\"columns[17].visible\"\n            width=\"160\"\n          >\n            <template slot-scope=\"scope\">\n              <span>{{ parseTime(scope.row.updateTime) }}</span>\n            </template>\n          </el-table-column>\n          <el-table-column\n            label=\"短信通知\"\n            align=\"center\"\n            prop=\"smsSend\"\n            :formatter=\"smsSendFormat\"\n             v-if=\"columns[18].visible\"\n          />\n          \n        </el-table>\n\n        <pagination\n          v-show=\"total > 0\"\n          :total=\"total\"\n          :page.sync=\"queryParams.pageNum\"\n          :limit.sync=\"queryParams.pageSize\"\n          :layout=\"pageLayout\"\n          @pagination=\"getList\"\n        />\n      </el-col>\n    </el-row>\n\n    <!-- 添加或修改参数配置对话框 -->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"open\"\n      :close-on-click-modal=\"false\"\n      width=\"80%\"\n      custom-class=\"edit-dialog\"\n      append-to-body\n    >\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"用户账号\" prop=\"userName\">\n              <el-input v-model=\"form.userName\" placeholder=\"请输入用户账号\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"报备人姓名\" prop=\"nickName\">\n              <el-input\n                v-model=\"form.nickName\"\n                placeholder=\"请输入报备人姓名\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"角色\">\n              <el-select v-model=\"form.roleIds\" multiple placeholder=\"请选择\">\n                <el-option\n                  v-for=\"item in roleOptions\"\n                  :key=\"item.roleId\"\n                  :label=\"item.roleName\"\n                  :value=\"item.roleId\"\n                  :disabled=\"item.status == 1\"\n                ></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"资料接收邮箱\" prop=\"email\">\n              <el-input\n                v-model=\"form.email\"\n                placeholder=\"请输入资料接收邮箱\"\n                maxlength=\"50\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <!-- <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"岗位\">\n              <el-select v-model=\"form.postIds\" multiple placeholder=\"请选择\">\n                <el-option\n                  v-for=\"item in postOptions\"\n                  :key=\"item.postId\"\n                  :label=\"item.postName\"\n                  :value=\"item.postId\"\n                  :disabled=\"item.status == 1\"\n                ></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row> -->\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"报备人电话\" prop=\"phonenumber\">\n              <el-input\n                v-model=\"form.phonenumber\"\n                placeholder=\"请输入报备人电话\"\n                maxlength=\"11\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"用户性别\">\n              <el-select v-model=\"form.sex\" placeholder=\"请选择\">\n                <el-option\n                  v-for=\"dict in sexOptions\"\n                  :key=\"dict.dictValue\"\n                  :label=\"dict.dictLabel\"\n                  :value=\"dict.dictValue\"\n                ></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <!-- <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"头像地址\">\n              <imageUpload v-model=\"form.avatar\" />\n            </el-form-item>\n          </el-col>\n           <el-col :span=\"12\">\n            <el-form-item label=\"密码\" prop=\"password\">\n              <el-input\n                v-model=\"form.password\"\n                placeholder=\"请输入密码\"\n                type=\"password\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row> -->\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"帐号状态\">\n              <el-radio-group v-model=\"form.status\">\n                <el-radio\n                  v-for=\"dict in statusOptions\"\n                  :key=\"dict.dictValue\"\n                  :label=\"dict.dictValue\"\n                  >{{ dict.dictLabel }}</el-radio\n                >\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"备注\" prop=\"remark\">\n              <el-input\n                v-model=\"form.remark\"\n                type=\"textarea\"\n                placeholder=\"请输入内容\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"公司全称\" prop=\"company\">\n              <el-input v-model=\"form.company\" placeholder=\"请输入公司全称\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"营业执照号码\" prop=\"businessNo\">\n              <el-input\n                v-model=\"form.businessNo\"\n                placeholder=\"请输入营业执照号码\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"营业执照图片\" prop=\"businessNoPic\" :required=\"true\" error=\"营业执照图片必传\">\n              <imageUpload v-model=\"form.businessNoPic\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            \n            <el-form-item label=\"所在区域\" prop=\"province\">\n              <el-cascader\n                :options=\"provinceAndCityData\"\n                clearable\n                :props=\"{ expandTrigger: 'hover' }\"\n                v-model=\"cityOptions\"\n                @change=\"handleCityChange\"\n              >\n              </el-cascader>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"资料邮寄地址\" prop=\"address\">\n              <el-input\n                v-model=\"form.address\"\n                placeholder=\"请输入资料邮寄地址\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"隶属经销商\" prop=\"dealer\">\n              <el-input v-model=\"form.dealer\" placeholder=\"请输入隶属经销商\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"短信通知\">\n              <el-radio-group v-model=\"form.smsSend\">\n                <el-radio\n                  v-for=\"dict in smsSendOptions\"\n                  :key=\"dict.dictValue\"\n                  :label=\"dict.dictValue\"\n                  >{{ dict.dictLabel }}</el-radio\n                >\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"审核状态\">\n              <el-radio-group v-model=\"form.auditStatus\">\n                <el-radio\n                  v-for=\"dict in auditStatusOptions\"\n                  :key=\"dict.dictValue\"\n                  :label=\"dict.dictValue\"\n                  >{{ dict.dictLabel }}</el-radio\n                >\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 用户导入对话框 -->\n    <el-dialog\n      :title=\"upload.title\"\n      :visible.sync=\"upload.open\"\n      width=\"400px\"\n      append-to-body\n    >\n      <el-upload\n        ref=\"upload\"\n        :limit=\"1\"\n        accept=\".xlsx, .xls\"\n        :headers=\"upload.headers\"\n        :action=\"upload.url + '?updateSupport=' + upload.updateSupport\"\n        :disabled=\"upload.isUploading\"\n        :on-progress=\"handleFileUploadProgress\"\n        :on-success=\"handleFileSuccess\"\n        :auto-upload=\"false\"\n        drag\n      >\n        <i class=\"el-icon-upload\"></i>\n        <div class=\"el-upload__text\">\n          将文件拖到此处，或\n          <em>点击上传</em>\n        </div>\n        <div class=\"el-upload__tip\" slot=\"tip\">\n          <el-checkbox\n            v-model=\"upload.updateSupport\"\n          />是否更新已经存在的用户数据\n          <el-link type=\"info\" style=\"font-size: 12px\" @click=\"importTemplate\"\n            >下载模板</el-link\n          >\n        </div>\n        <div class=\"el-upload__tip\" style=\"color: red\" slot=\"tip\">\n          提示：仅允许导入“xls”或“xlsx”格式文件！\n        </div>\n      </el-upload>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitFileForm\">确 定</el-button>\n        <el-button @click=\"upload.open = false\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 查看项目报备对话框 -->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"viewOpen\"\n      :fullscreen=\"true\"\n      :lock-scroll=\"true\"\n      :destroy-on-close=\"true\"\n      custom-class=\"view-dialog\"\n    >\n      \n      <flowable v-if=\"viewOpen\" ref=\"flow\" procDefKey=\"process_user_reg\" :procInsId=\"procInsId\" :taskId=\"taskId\" :finished=\"finished\" :bizKey=\"bizKey\">\n        <template v-slot:title>用户信息</template>\n        <template v-slot:content>\n          <el-descriptions label-width=\"120px\" :column=\"isMobile?1:3\">\n            <el-descriptions-item label=\"帐号状态\">\n              {{ view.status }}\n            </el-descriptions-item>\n            <el-descriptions-item label=\"审核状态\">\n              {{ view.auditStatus }}\n            </el-descriptions-item>\n            <el-descriptions-item label=\"用户ID\">\n              {{ view.userId }}\n            </el-descriptions-item>\n            <el-descriptions-item label=\"用户账号\">\n              {{ view.userName }}\n            </el-descriptions-item>\n            <el-descriptions-item label=\"报备人姓名\">\n              {{ view.nickName }}\n            </el-descriptions-item>\n            <el-descriptions-item label=\"用户类型\">\n              {{ view.userType }}\n            </el-descriptions-item>\n            <el-descriptions-item label=\"资料接收邮箱\">\n              {{ view.email }}\n            </el-descriptions-item>\n            <el-descriptions-item label=\"报备人电话\">\n              {{ view.phonenumber }}\n            </el-descriptions-item>\n            <el-descriptions-item label=\"公司全称\">\n              {{ view.company }}\n            </el-descriptions-item>\n            <el-descriptions-item label=\"营业执照号码\">\n              {{ view.businessNo }}\n            </el-descriptions-item>\n            <el-descriptions-item label=\"所在区域\">\n              {{ view.province }}\n            </el-descriptions-item>\n            <el-descriptions-item label=\"资料邮寄地址\">\n              {{ view.address }}\n            </el-descriptions-item>\n            <el-descriptions-item label=\"隶属经销商\">\n              {{ view.dealer }}\n            </el-descriptions-item>        \n            <el-descriptions-item label=\"短信通知\">\n              {{ view.smsSend }}\n            </el-descriptions-item>\n            <el-descriptions-item label=\"创建时间\">\n              {{ view.createTime }}\n            </el-descriptions-item>\n            <el-descriptions-item label=\"修改时间\">\n              {{ view.updateTime }}\n            </el-descriptions-item>\n            <el-descriptions-item label=\"备注\" prop=\"remark\">\n              {{ view.remark }}\n            </el-descriptions-item>\n          </el-descriptions>\n        </template>\n      </flowable>\n    </el-dialog>\n\n  </div>\n</template>\n\n<script>\nimport {\n  listUser,\n  getUser,\n  delUser,\n  addUser,\n  updateUser,\n  exportUser,\n  resetUserPwd,\n  changeUserStatus,\n  importTemplate,\n  printUser\n} from \"@/api/system/user\";\nimport { getToken } from \"@/utils/auth\";\nimport { treeselect } from \"@/api/system/dept\";\nimport Treeselect from \"@riophae/vue-treeselect\";\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\";\nimport ImageUpload from \"@/components/ImageUpload\";\nimport { provinceAndCityData,regionData,CodeToText, TextToCode } from \"element-china-area-data\";\nimport flowable from '@/views/flowable/task/record/view';\nimport {getInsIdByBizKey} from \"@/api/flowable/todo\";\nexport default {\n  name: \"User\",\n  components: { Treeselect, ImageUpload,flowable },\n  data() {\n    return {\n      isMobile: false,\n      pageLayout: \"total, sizes, prev, pager, next, jumper\",\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      showExport: false,\n      showPrint: false,\n      // 总条数\n      total: 0,\n      // 用户表格数据\n      userList: null,\n      // 弹出层标题\n      title: \"\",\n      // 部门树选项\n      deptOptions: undefined,\n      // 是否显示弹出层\n      open: false,\n      // 部门名称\n      deptName: undefined,\n      // 默认密码\n      initPassword: undefined,\n      // 日期范围\n      dateRange: [],\n      // 状态数据字典\n      statusOptions: [],\n      // 性别状态字典\n      sexOptions: [],\n      // 短信通知字典\n      smsSendOptions: [],\n      // 审核状态字典\n      auditStatusOptions: [],\n      // 角色选项\n      roleOptions: [],\n      // 表单参数\n      form: {},\n      defaultProps: {\n        children: \"children\",\n        label: \"label\",\n      },\n      // 用户导入参数\n      upload: {\n        // 是否显示弹出层（用户导入）\n        open: false,\n        // 弹出层标题（用户导入）\n        title: \"\",\n        // 是否禁用上传\n        isUploading: false,\n        // 是否更新已经存在的用户数据\n        updateSupport: 0,\n        // 设置上传的请求头部\n        headers: { Authorization: \"Bearer \" + getToken() },\n        // 上传的地址\n        url: process.env.VUE_APP_BASE_API + \"/system/user/importData\",\n      },\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        userName: undefined,\n        phonenumber: undefined,\n        status: undefined,\n        deptId: undefined,\n        auditStatus: undefined,\n      },\n      // 列信息\n      columns: [\n        { key: 'status', index: 0, label: `帐号状态`, visible: true },\n        { key: 'userId', index: 1, label: `用户ID`, visible: true },\n        { key: 'userName', index: 2, label: `用户账号`, visible: true },\n        { key: 'nickName', index: 3, label: `报备人姓名`, visible: true },\n        { key: 'userType', index: 4, label: `用户类型`, visible: true },\n        { key: 'auditStatus', index: 5, label: `审核状态`, visible: true },\n        { key: 'email', index: 6, label: `资料接收邮箱`, visible: true },\n        { key: 'phonenumber', index: 7, label: `报备人电话`, visible: true },\n        { key: 'sex', index: 8, label: `用户性别`, visible: true },\n        { key: 'avatar', index: 9, label: `头像`, visible: true },\n        { key: 'company', index: 10, label: `公司全称`, visible: true },\n        { key: 'businessNo', index: 11, label: `营业执照号码`, visible: true },\n        { key: 'businessNoPic', index: 12, label: `营业执照图片`, visible: true },\n        { key: 'province', index: 13, label: `所在区域`, visible: true },\n        { key: 'address', index: 14, label: `资料邮寄地址`, visible: true },\n        { key: 'dealer', index: 15, label: `隶属经销商`, visible: true },\n        { key: 'createTime', index: 16, label: `提交时间`, visible: true },\n        { key: 'updateTime', index: 17, label: `最后修改时间`, visible: true },\n        { key: 'smsSend', index: 18, label: `短信通知`, visible: true },\n      ],\n      // 表单校验\n      rules: {\n        userName: [\n          { required: true, message: \"用户名不能为空\", trigger: \"blur\" },\n        ],\n        company: [\n          { required: true, message: \"公司全称不能为空\", trigger: \"blur\" },\n        ],\n        businessNo: [\n          { required: true, message: \"营业执照号码不能为空\", trigger: \"blur\" },\n        ],\n        businessNoPic: [\n          { required: true, message: \"营业执照图片必传\" },\n        ],\n        email: [\n          {\n            required: true,\n            type: \"email\",\n            message: \"'请输入正确的邮箱地址\",\n            trigger: [\"blur\", \"change\"],\n          },\n        ],\n        phonenumber: [\n          {\n            required: true,\n            pattern: /^1[3|4|5|6|7|8|9][0-9]\\d{8}$/,\n            message: \"请输入正确的手机号码\",\n            trigger: \"blur\",\n          },\n        ],\n        nickName: [\n          { required: true, message: \"报备人姓名不能为空\", trigger: \"blur\" },\n        ],\n        province: [\n          { required: true, message: \"所在区域不能为空\", trigger: \"blur\" },\n        ],\n        address: [\n          { required: true, message: \"资料邮寄地址不能为空\", trigger: \"blur\" },\n        ],\n      },\n      auditStatusTree: [],\n      provinceAndCityData: regionData,\n      cityOptions: [],\n      queryArea: [],\n      provinceTrees: [],\n      viewOpen: false,\n      view: {},\n      procInsId: undefined,\n      taskId: undefined,\n      bizKey: undefined,\n      showArea: false,\n    };\n  },\n  watch: {\n    // 根据名称筛选部门树\n    // deptName(val) {\n    //   this.$refs.tree.filter(val);\n    // }\n  },\n  created() {\n\n    if(this.$store.state.user.roles && \n      (this.$store.state.user.roles.includes(\"admin\") || this.$store.state.user.roles.includes(\"user_admin\")\n      || this.$store.state.user.roles.includes(\"report_admin\")// || this.$store.state.user.roles.includes(\"province_admin\")\n      )){\n        this.showArea = true;\n    }\n    \n    this.getList();\n    //this.getTreeselect();\n    this.getDicts(\"sys_normal_disable\").then((response) => {\n      this.statusOptions = response.data;\n    });\n    this.getDicts(\"sys_user_sex\").then((response) => {\n      this.sexOptions = response.data;\n    });\n    this.getConfigKey(\"sys.user.initPassword\").then((response) => {\n      this.initPassword = response.msg;\n    });\n    this.getDicts(\"pr_sms_notify\").then((response) => {\n      this.smsSendOptions = response.data;\n    });\n    this.getDicts(\"pr_audit_status\").then((response) => {\n      var type = 0;\n      if(this.$store.state.user.roles){\n        if(this.$store.state.user.roles.includes(\"common\")){\n          type = 1;\n        } \n        if(this.$store.state.user.roles.includes(\"province_admin\")){\n          type = 2;\n        }\n        if(this.$store.state.user.roles.includes(\"report_admin\")){\n          type = 3;\n        }\n        if(this.$store.state.user.roles.includes(\"user_admin\")){\n          type = 4;\n        }\n      }\n      this.auditStatusOptions = response.data;\n      var opt = [];\n      opt.push({id: 9, label:'全部'})\n      if(type == 2 || type == 3 || type == 4){\n        opt.push({id: 10, label:'未审批'})\n      }\n      response.data.forEach((elem, index) => {\n        var obj = {};\n        obj.label = elem.dictLabel;\n        obj.id = elem.dictValue;\n        opt.push(obj);\n      });\n      var auditStatusTree = {};\n      auditStatusTree.label = \"审核状态\";\n      auditStatusTree.children = opt;\n      var auditStatusTrees = [];\n      auditStatusTrees.push(auditStatusTree);\n      this.auditStatusTree = auditStatusTrees;\n    });\n    //所在区域数据处理\n    var opt = [];\n    opt.push({id: 0, label:'全部'})\n    provinceAndCityData.forEach((elem, index) => {\n        var obj = {};\n        obj.label = elem.label;\n        obj.id = elem.label;\n        opt.push(obj);\n    });\n    var provinceTree = {};\n    provinceTree.label = \"所在区域\";\n    provinceTree.children = opt;\n    var provinceTrees = [];\n    provinceTrees.push(provinceTree);\n    this.provinceTrees = provinceTrees;\n    if(this._isMobile()){\n      this.isMobile = true;\n      this.pageLayout = \"total, prev, next, jumper\";\n    }\n  },\n  methods: {\n    _isMobile() {\n      let flag = navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i)\n      return flag;\n    },\n    /** 查询用户列表 */\n    getList() {\n      this.loading = true;\n      listUser(this.addDateRange(this.queryParams, this.dateRange)).then(\n        (response) => {\n          this.userList = response.rows;\n          this.total = response.total;\n          this.loading = false;\n        }\n      );\n    },\n    // 用户性别字典翻译\n    sexFormat(row, column) {\n      return this.selectDictLabel(this.sexOptions, row.sex);\n    },\n    // 帐号状态字典翻译\n    statusFormat(row, column) {\n      return this.selectDictLabel(this.statusOptions, row.status);\n    },\n    // 短信通知字典翻译\n    smsSendFormat(row, column) {\n      return this.selectDictLabel(this.smsSendOptions, row.smsSend);\n    },\n    // 审核状态字典翻译\n    auditStatusFormat(row, column) {\n      return this.selectDictLabel(this.auditStatusOptions, row.auditStatus);\n    },\n    /** 查询部门下拉树结构 */\n    getTreeselect() {\n      treeselect().then((response) => {\n        this.deptOptions = response.data;\n      });\n    },\n    // 筛选节点\n    filterNode(value, data) {\n      if (!value) return true;\n      return data.label.indexOf(value) !== -1;\n    },\n    // 节点单击事件\n    handleNodeClick(data) {\n      if(data.id == 9){\n        this.queryParams.auditStatus = undefined;\n        this.queryParams.node = undefined;\n        this.queryParams.spare1 = undefined;\n      }else{\n        if(data.id == 1 || data.id == 10){\n          this.queryParams.auditStatus = 1;\n          if(this.$store.state.user.roles){\n            if(this.$store.state.user.roles.includes(\"province_admin\")){\n              this.queryParams.node = '省负责人';\n              if(data.id == 10){\n                this.queryParams.spare1 = '=';\n              }else{\n                this.queryParams.spare1 = '!=';\n              }\n            }\n            if(this.$store.state.user.roles.includes(\"report_admin\")){\n              this.queryParams.node = '审核员';\n              if(data.id == 10){\n                this.queryParams.spare1 = '=';\n              }else{\n                this.queryParams.spare1 = '!=';\n              }\n            }\n            if(this.$store.state.user.roles.includes(\"user_admin\")){\n              this.queryParams.node = '管理员';\n              if(data.id == 10){\n                this.queryParams.spare1 = '=';\n              }else{\n                this.queryParams.spare1 = '!=';\n              }\n            }\n          }\n        }else{\n          this.queryParams.auditStatus = data.id;\n          this.queryParams.node = undefined;\n          this.queryParams.spare1 = undefined;\n        }\n      }\n      this.getList();\n    },\n    // 区域节点单击事件\n    handleProvinceClick(data) {\n      if(data.id == 0){\n        this.queryParams.province=undefined;\n      }else{\n        this.queryParams.province = data.id;\n      }\n      this.getList();\n    },\n    // 用户状态修改\n    handleStatusChange(row) {\n      let text = row.status === \"0\" ? \"启用\" : \"停用\";\n      this.$confirm(\n        '确认要\"' + text + '\"\"' + row.userName + '\"用户吗?',\n        \"警告\",\n        {\n          confirmButtonText: \"确定\",\n          cancelButtonText: \"取消\",\n          type: \"warning\",\n        }\n      )\n        .then(function () {\n          return changeUserStatus(row.userId, row.status);\n        })\n        .then(() => {\n          this.msgSuccess(text + \"成功\");\n        })\n        .catch(function () {\n          row.status = row.status === \"0\" ? \"1\" : \"0\";\n        });\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        userId: undefined,\n        deptId: undefined,\n        userName: undefined,\n        nickName: undefined,\n        userType: undefined,\n        email: undefined,\n        phonenumber: undefined,\n        sex: \"0\",\n        avatar: undefined,\n        password: undefined,\n        status: \"0\",\n        delFlag: undefined,\n        loginIp: undefined,\n        loginDate: undefined,\n        createBy: undefined,\n        createTime: undefined,\n        updateBy: undefined,\n        updateTime: undefined,\n        remark: undefined,\n        company: undefined,\n        businessNo: undefined,\n        businessNoPic: undefined,\n        province: [],\n        address: undefined,\n        dealer: undefined,\n        smsSend: \"0\",\n        auditStatus: \"0\",\n        roleIds: [],\n      };\n      this.resetForm(\"form\");\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.page = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.dateRange = [];\n      this.queryArea = [];\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map((item) => item.userId);\n      this.single = selection.length != 1;\n      this.multiple = !selection.length;\n    },\n    handleView(row){\n      this.view = JSON.parse(JSON.stringify(row)); ;\n      this.view.auditStatus = this.auditStatusFormat(row);\n      this.view.status = this.statusFormat(row);\n      this.view.smsSend = this.smsSendFormat(row);\n      this.title = \"查看用户详情\";\n      const params = {bizKey: row.userId, defKey: 'process_user_reg'}\n      this.bizKey = row.userId;\n      getInsIdByBizKey(params).then((resp)=>{\n        if(resp.data && resp.data.instanceId){\n          this.procInsId = resp.data.instanceId;\n          this.taskId = resp.data.taskId;\n          if(resp.data.instanceId && !resp.data.endTime && resp.data.assignee == this.$store.state.user.userId){\n            this.finished = false;          \n          }else if(this.$store.state.user.roles && (this.$store.state.user.roles.includes(\"report_admin\"))){\n            //审核员角色不控制谁操作\n            this.finished = false;\n          }else{\n            this.finished = true;\n          }          \n        }else{\n          this.finished = true;\n        }\n        this.viewOpen = true;\n      })\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      // this.reset();\n      // //this.getTreeselect();\n      // getUser().then((response) => {\n      //   this.postOptions = response.posts;\n      //   this.roleOptions = response.roles;\n      //   this.open = true;\n      //   this.title = \"添加用户\";\n      //   this.form.password = this.initPassword;\n      // });\n      this.$router.push({ path: '/system/user/form',\n        query: {\n          businessKey: undefined,\n          formEdit: true\n      }})\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset();\n      //this.getTreeselect();\n      const userId = row.userId || this.ids;\n      getUser(userId).then((response) => {\n        this.form = response.data;\n        this.postOptions = response.posts;\n        this.roleOptions = response.roles;\n        this.form.postIds = response.postIds;\n        this.form.roleIds = response.roleIds;\n        this.open = true;\n        this.title = \"修改用户\";\n        this.form.password = \"\";\n        var provinces = response.data.province;\n        if(provinces.length > 0){\n          var address = provinces.split(\"/\");\n          var citys = [];\n          // 省份\n          if(address.length > 0)\n          citys.push(TextToCode[address[0]].code);\n          // 城市\n          if(address.length > 1)\n          citys.push(TextToCode[address[0]][address[1]].code);\n          // 地区\n          if(address.length > 2)\n          citys.push(TextToCode[address[0]][address[1]][address[2]].code);\n          \n          this.cityOptions = citys;\n        }\n      });\n    },\n    /** 重置密码按钮操作 */\n    handleResetPwd(row) {\n      this.$prompt('请输入\"' + row.userName + '\"的新密码', \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n      })\n        .then(({ value }) => {\n          resetUserPwd(row.userId, value).then((response) => {\n            this.msgSuccess(\"修改成功，新密码是：\" + value);\n          });\n        })\n        .catch(() => {});\n    },\n    /** 提交按钮 */\n    submitForm: function () {\n      if(this.cityOptions.length < 1){\n        \n      }\n      this.$refs[\"form\"].validate((valid) => {\n        if (valid) {\n          if (this.form.userId != undefined) {\n            updateUser(this.form).then((response) => {\n              this.msgSuccess(\"修改成功\");\n              this.open = false;\n              this.getList();\n            });\n          } else {\n            addUser(this.form).then((response) => {\n              this.msgSuccess(\"新增成功\");\n              this.open = false;\n              this.getList();\n            });\n          }\n        }\n      });\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const userIds = row.userId || this.ids;\n      this.$confirm(\n        '是否确认删除用户编号为\"' + userIds + '\"的数据项?',\n        \"警告\",\n        {\n          confirmButtonText: \"确定\",\n          cancelButtonText: \"取消\",\n          type: \"warning\",\n        }\n      )\n        .then(function () {\n          return delUser(userIds);\n        })\n        .then(() => {\n          this.getList();\n          this.msgSuccess(\"删除成功\");\n        });\n    },\n    /** 导入按钮操作 */\n    handleImport() {\n      this.upload.title = \"用户导入\";\n      this.upload.open = true;\n    },\n    /** 下载模板操作 */\n    importTemplate() {\n      importTemplate().then((response) => {\n        this.download(response.msg);\n      });\n    },\n    // 文件上传中处理\n    handleFileUploadProgress(event, file, fileList) {\n      this.upload.isUploading = true;\n    },\n    // 文件上传成功处理\n    handleFileSuccess(response, file, fileList) {\n      this.upload.open = false;\n      this.upload.isUploading = false;\n      this.$refs.upload.clearFiles();\n      this.$alert(response.msg, \"导入结果\", { dangerouslyUseHTMLString: true });\n      this.getList();\n    },\n    // 提交上传文件\n    submitFileForm() {\n      this.$refs.upload.submit();\n    },\n    handleCityChange(value) {\n      if(!value || value.length == 0 ){\n        this.cityOptions = null;\n        this.form.province = undefined;\n        this.form.district = undefined;\n        return\n      }\n      this.cityOptions = value\n      var txt = \"\";\n      value.forEach(function (item) {\n          txt += CodeToText[item]+\"/\"\n      });\n      if(txt.length > 1){\n        txt = txt.substring(0, txt.length-1);\n        this.form.province=txt;\n        this.form.district = CodeToText[value[0]];\n      }else{\n        this.form.province=undefined;\n      }\n      \n    },\n    handleQueryCityChange(value) {\n      this.queryArea = value\n      var txt = \"\";\n      value.forEach(function (item) {\n          txt += CodeToText[item]+\"/\"\n      });\n      if(txt.length > 1){\n        txt = txt.substring(0, txt.length-1);\n        this.queryParams.province=txt;\n      }else{\n        this.queryParams.province=undefined;\n      }\n      \n    },\n    clickExport(){\n      this.showExport = true;\n    },\n    clickPrint(){\n      this.showPrint = true;\n    },\n    /** 导出按钮操作 */\n    handleExport(type) {\n      this.queryParams.spare1 = type;\n      var col = []\n      this.columns.forEach(item=>{\n        if(item.visible){\n          col.push(item.label);\n        }\n      })\n      this.queryParams.spare2 = col.join(',');\n      const queryParams = this.queryParams;\n      this.$confirm(\"是否确认导出用户搜索结果\" + (type==0?'本页':'全部') + \"数据项?\", \"警告\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\",\n      })\n        .then(function () {\n          return exportUser(queryParams);\n        })\n        .then((response) => {\n          this.download(response.msg);\n          this.showExport = false;\n          loading.close();\n        });\n    },\n    handlePrint(type){\n      this.queryParams.spare1 = type;\n      var properties=[];\n      properties.push({field: 'index', displayName: '序号'});\n      this.columns.forEach(item=>{\n        if(item.visible){\n          properties.push({field: item.key, displayName: item.label});\n        }\n      });\n      printUser(this.queryParams).then((response) => {\n        printJS({\n          printable: response.data,\n          type:'json',\n          properties: properties,\n          header:'<div style=\"text-align: center\"><h3>用户列表列表</h3></div>',\n          targetStyles:['*'],\n          gridHeaderStyle: 'border: 1px solid #000;text-align:center',\n          gridStyle: 'border: 1px solid #000;text-align:center',\n          style:\"@page {margin:0 10mm}\"\n        })\n\n      });\n    },\n  },\n};\n</script>\n<style>\n@media screen and (min-width: 600px) {\n  .view-dialog{\n    width: 80% !important;\n    float: right;\n  }\n}\n@media screen and (max-width: 599px) {\n  .el-form .el-col{\n    width: 100% !important;\n  }\n  .edit-dialog{\n    width: 100% !important;\n    margin-bottom: 0;\n    height: 100%;\n    overflow: auto;\n  }\n  .el-dialog:not(.is-fullscreen) {\n    margin-top: 0 !important;\n  }\n}\n</style>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAupBA,SACAA,QAAA,EACAC,OAAA,EACAC,OAAA,EACAC,OAAA,EACAC,UAAA,EACAC,UAAA,EACAC,YAAA,EACAC,gBAAA,EACAC,cAAA,IAAAA,eAAA,EACAC,SAAA,QACA;AACA,SAAAC,QAAA;AACA,SAAAC,UAAA;AACA,OAAAC,UAAA;AACA;AACA,OAAAC,WAAA;AACA,SAAAC,mBAAA,EAAAC,UAAA,EAAAC,UAAA,EAAAC,UAAA;AACA,OAAAC,QAAA;AACA,SAAAC,gBAAA;AACA;EACAC,IAAA;EACAC,UAAA;IAAAT,UAAA,EAAAA,UAAA;IAAAC,WAAA,EAAAA,WAAA;IAAAK,QAAA,EAAAA;EAAA;EACAI,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;MACAC,UAAA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACAC,UAAA;MACAC,SAAA;MACA;MACAC,KAAA;MACA;MACAC,QAAA;MACA;MACAC,KAAA;MACA;MACAC,WAAA,EAAAC,SAAA;MACA;MACAC,IAAA;MACA;MACAC,QAAA,EAAAF,SAAA;MACA;MACAG,YAAA,EAAAH,SAAA;MACA;MACAI,SAAA;MACA;MACAC,aAAA;MACA;MACAC,UAAA;MACA;MACAC,cAAA;MACA;MACAC,kBAAA;MACA;MACAC,WAAA;MACA;MACAC,IAAA;MACAC,YAAA;QACAC,QAAA;QACAC,KAAA;MACA;MACA;MACAC,MAAA;QACA;QACAb,IAAA;QACA;QACAH,KAAA;QACA;QACAiB,WAAA;QACA;QACAC,aAAA;QACA;QACAC,OAAA;UAAAC,aAAA,cAAA5C,QAAA;QAAA;QACA;QACA6C,GAAA,EAAAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA;MACA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,QAAA,EAAA1B,SAAA;QACA2B,WAAA,EAAA3B,SAAA;QACA4B,MAAA,EAAA5B,SAAA;QACA6B,MAAA,EAAA7B,SAAA;QACA8B,WAAA,EAAA9B;MACA;MACA;MACA+B,OAAA,GACA;QAAAC,GAAA;QAAAC,KAAA;QAAApB,KAAA;QAAAqB,OAAA;MAAA,GACA;QAAAF,GAAA;QAAAC,KAAA;QAAApB,KAAA;QAAAqB,OAAA;MAAA,GACA;QAAAF,GAAA;QAAAC,KAAA;QAAApB,KAAA;QAAAqB,OAAA;MAAA,GACA;QAAAF,GAAA;QAAAC,KAAA;QAAApB,KAAA;QAAAqB,OAAA;MAAA,GACA;QAAAF,GAAA;QAAAC,KAAA;QAAApB,KAAA;QAAAqB,OAAA;MAAA,GACA;QAAAF,GAAA;QAAAC,KAAA;QAAApB,KAAA;QAAAqB,OAAA;MAAA,GACA;QAAAF,GAAA;QAAAC,KAAA;QAAApB,KAAA;QAAAqB,OAAA;MAAA,GACA;QAAAF,GAAA;QAAAC,KAAA;QAAApB,KAAA;QAAAqB,OAAA;MAAA,GACA;QAAAF,GAAA;QAAAC,KAAA;QAAApB,KAAA;QAAAqB,OAAA;MAAA,GACA;QAAAF,GAAA;QAAAC,KAAA;QAAApB,KAAA;QAAAqB,OAAA;MAAA,GACA;QAAAF,GAAA;QAAAC,KAAA;QAAApB,KAAA;QAAAqB,OAAA;MAAA,GACA;QAAAF,GAAA;QAAAC,KAAA;QAAApB,KAAA;QAAAqB,OAAA;MAAA,GACA;QAAAF,GAAA;QAAAC,KAAA;QAAApB,KAAA;QAAAqB,OAAA;MAAA,GACA;QAAAF,GAAA;QAAAC,KAAA;QAAApB,KAAA;QAAAqB,OAAA;MAAA,GACA;QAAAF,GAAA;QAAAC,KAAA;QAAApB,KAAA;QAAAqB,OAAA;MAAA,GACA;QAAAF,GAAA;QAAAC,KAAA;QAAApB,KAAA;QAAAqB,OAAA;MAAA,GACA;QAAAF,GAAA;QAAAC,KAAA;QAAApB,KAAA;QAAAqB,OAAA;MAAA,GACA;QAAAF,GAAA;QAAAC,KAAA;QAAApB,KAAA;QAAAqB,OAAA;MAAA,GACA;QAAAF,GAAA;QAAAC,KAAA;QAAApB,KAAA;QAAAqB,OAAA;MAAA,EACA;MACA;MACAC,KAAA;QACAT,QAAA,GACA;UAAAU,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAC,OAAA,GACA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAE,UAAA,GACA;UAAAJ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAG,aAAA,GACA;UAAAL,QAAA;UAAAC,OAAA;QAAA,EACA;QACAK,KAAA,GACA;UACAN,QAAA;UACAO,IAAA;UACAN,OAAA;UACAC,OAAA;QACA,EACA;QACAX,WAAA,GACA;UACAS,QAAA;UACAQ,OAAA;UACAP,OAAA;UACAC,OAAA;QACA,EACA;QACAO,QAAA,GACA;UAAAT,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAQ,QAAA,GACA;UAAAV,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAS,OAAA,GACA;UAAAX,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAU,eAAA;MACAtE,mBAAA,EAAAC,UAAA;MACAsE,WAAA;MACAC,SAAA;MACAC,aAAA;MACAC,QAAA;MACAC,IAAA;MACAC,SAAA,EAAAtD,SAAA;MACAuD,MAAA,EAAAvD,SAAA;MACAwD,MAAA,EAAAxD,SAAA;MACAyD,QAAA;IACA;EACA;EACAC,KAAA;IACA;IACA;IACA;IACA;EAAA,CACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IAEA,SAAAC,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,KAAA,KACA,KAAAH,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,KAAA,CAAAC,QAAA,kBAAAJ,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,KAAA,CAAAC,QAAA,kBACA,KAAAJ,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,KAAA,CAAAC,QAAA;IAAA,CACA;MACA,KAAAR,QAAA;IACA;IAEA,KAAAS,OAAA;IACA;IACA,KAAAC,QAAA,uBAAAC,IAAA,WAAAC,QAAA;MACAT,KAAA,CAAAvD,aAAA,GAAAgE,QAAA,CAAAnF,IAAA;IACA;IACA,KAAAiF,QAAA,iBAAAC,IAAA,WAAAC,QAAA;MACAT,KAAA,CAAAtD,UAAA,GAAA+D,QAAA,CAAAnF,IAAA;IACA;IACA,KAAAoF,YAAA,0BAAAF,IAAA,WAAAC,QAAA;MACAT,KAAA,CAAAzD,YAAA,GAAAkE,QAAA,CAAAE,GAAA;IACA;IACA,KAAAJ,QAAA,kBAAAC,IAAA,WAAAC,QAAA;MACAT,KAAA,CAAArD,cAAA,GAAA8D,QAAA,CAAAnF,IAAA;IACA;IACA,KAAAiF,QAAA,oBAAAC,IAAA,WAAAC,QAAA;MACA,IAAA1B,IAAA;MACA,IAAAiB,KAAA,CAAAC,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,KAAA;QACA,IAAAJ,KAAA,CAAAC,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,KAAA,CAAAC,QAAA;UACAtB,IAAA;QACA;QACA,IAAAiB,KAAA,CAAAC,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,KAAA,CAAAC,QAAA;UACAtB,IAAA;QACA;QACA,IAAAiB,KAAA,CAAAC,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,KAAA,CAAAC,QAAA;UACAtB,IAAA;QACA;QACA,IAAAiB,KAAA,CAAAC,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,KAAA,CAAAC,QAAA;UACAtB,IAAA;QACA;MACA;MACAiB,KAAA,CAAApD,kBAAA,GAAA6D,QAAA,CAAAnF,IAAA;MACA,IAAAsF,GAAA;MACAA,GAAA,CAAAC,IAAA;QAAAC,EAAA;QAAA7D,KAAA;MAAA;MACA,IAAA8B,IAAA,SAAAA,IAAA,SAAAA,IAAA;QACA6B,GAAA,CAAAC,IAAA;UAAAC,EAAA;UAAA7D,KAAA;QAAA;MACA;MACAwD,QAAA,CAAAnF,IAAA,CAAAyF,OAAA,WAAAC,IAAA,EAAA3C,KAAA;QACA,IAAA4C,GAAA;QACAA,GAAA,CAAAhE,KAAA,GAAA+D,IAAA,CAAAE,SAAA;QACAD,GAAA,CAAAH,EAAA,GAAAE,IAAA,CAAAG,SAAA;QACAP,GAAA,CAAAC,IAAA,CAAAI,GAAA;MACA;MACA,IAAA7B,eAAA;MACAA,eAAA,CAAAnC,KAAA;MACAmC,eAAA,CAAApC,QAAA,GAAA4D,GAAA;MACA,IAAAQ,gBAAA;MACAA,gBAAA,CAAAP,IAAA,CAAAzB,eAAA;MACAY,KAAA,CAAAZ,eAAA,GAAAgC,gBAAA;IACA;IACA;IACA,IAAAR,GAAA;IACAA,GAAA,CAAAC,IAAA;MAAAC,EAAA;MAAA7D,KAAA;IAAA;IACAnC,mBAAA,CAAAiG,OAAA,WAAAC,IAAA,EAAA3C,KAAA;MACA,IAAA4C,GAAA;MACAA,GAAA,CAAAhE,KAAA,GAAA+D,IAAA,CAAA/D,KAAA;MACAgE,GAAA,CAAAH,EAAA,GAAAE,IAAA,CAAA/D,KAAA;MACA2D,GAAA,CAAAC,IAAA,CAAAI,GAAA;IACA;IACA,IAAAI,YAAA;IACAA,YAAA,CAAApE,KAAA;IACAoE,YAAA,CAAArE,QAAA,GAAA4D,GAAA;IACA,IAAArB,aAAA;IACAA,aAAA,CAAAsB,IAAA,CAAAQ,YAAA;IACA,KAAA9B,aAAA,GAAAA,aAAA;IACA,SAAA+B,SAAA;MACA,KAAA/F,QAAA;MACA,KAAAC,UAAA;IACA;EACA;EACA+F,OAAA;IACAD,SAAA,WAAAA,UAAA;MACA,IAAAE,IAAA,GAAAC,SAAA,CAAAC,SAAA,CAAAC,KAAA;MACA,OAAAH,IAAA;IACA;IACA,aACAlB,OAAA,WAAAA,QAAA;MAAA,IAAAsB,MAAA;MACA,KAAAnG,OAAA;MACAzB,QAAA,MAAA6H,YAAA,MAAAlE,WAAA,OAAAnB,SAAA,GAAAgE,IAAA,CACA,UAAAC,QAAA;QACAmB,MAAA,CAAA3F,QAAA,GAAAwE,QAAA,CAAAqB,IAAA;QACAF,MAAA,CAAA5F,KAAA,GAAAyE,QAAA,CAAAzE,KAAA;QACA4F,MAAA,CAAAnG,OAAA;MACA,CACA;IACA;IACA;IACAsG,SAAA,WAAAA,UAAAC,GAAA,EAAAC,MAAA;MACA,YAAAC,eAAA,MAAAxF,UAAA,EAAAsF,GAAA,CAAAG,GAAA;IACA;IACA;IACAC,YAAA,WAAAA,aAAAJ,GAAA,EAAAC,MAAA;MACA,YAAAC,eAAA,MAAAzF,aAAA,EAAAuF,GAAA,CAAAhE,MAAA;IACA;IACA;IACAqE,aAAA,WAAAA,cAAAL,GAAA,EAAAC,MAAA;MACA,YAAAC,eAAA,MAAAvF,cAAA,EAAAqF,GAAA,CAAAM,OAAA;IACA;IACA;IACAC,iBAAA,WAAAA,kBAAAP,GAAA,EAAAC,MAAA;MACA,YAAAC,eAAA,MAAAtF,kBAAA,EAAAoF,GAAA,CAAA9D,WAAA;IACA;IACA,gBACAsE,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MACA9H,UAAA,GAAA6F,IAAA,WAAAC,QAAA;QACAgC,MAAA,CAAAtG,WAAA,GAAAsE,QAAA,CAAAnF,IAAA;MACA;IACA;IACA;IACAoH,UAAA,WAAAA,WAAAC,KAAA,EAAArH,IAAA;MACA,KAAAqH,KAAA;MACA,OAAArH,IAAA,CAAA2B,KAAA,CAAA2F,OAAA,CAAAD,KAAA;IACA;IACA;IACAE,eAAA,WAAAA,gBAAAvH,IAAA;MACA,IAAAA,IAAA,CAAAwF,EAAA;QACA,KAAAnD,WAAA,CAAAO,WAAA,GAAA9B,SAAA;QACA,KAAAuB,WAAA,CAAAmF,IAAA,GAAA1G,SAAA;QACA,KAAAuB,WAAA,CAAAoF,MAAA,GAAA3G,SAAA;MACA;QACA,IAAAd,IAAA,CAAAwF,EAAA,SAAAxF,IAAA,CAAAwF,EAAA;UACA,KAAAnD,WAAA,CAAAO,WAAA;UACA,SAAA+B,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,KAAA;YACA,SAAAH,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,KAAA,CAAAC,QAAA;cACA,KAAA1C,WAAA,CAAAmF,IAAA;cACA,IAAAxH,IAAA,CAAAwF,EAAA;gBACA,KAAAnD,WAAA,CAAAoF,MAAA;cACA;gBACA,KAAApF,WAAA,CAAAoF,MAAA;cACA;YACA;YACA,SAAA9C,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,KAAA,CAAAC,QAAA;cACA,KAAA1C,WAAA,CAAAmF,IAAA;cACA,IAAAxH,IAAA,CAAAwF,EAAA;gBACA,KAAAnD,WAAA,CAAAoF,MAAA;cACA;gBACA,KAAApF,WAAA,CAAAoF,MAAA;cACA;YACA;YACA,SAAA9C,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,KAAA,CAAAC,QAAA;cACA,KAAA1C,WAAA,CAAAmF,IAAA;cACA,IAAAxH,IAAA,CAAAwF,EAAA;gBACA,KAAAnD,WAAA,CAAAoF,MAAA;cACA;gBACA,KAAApF,WAAA,CAAAoF,MAAA;cACA;YACA;UACA;QACA;UACA,KAAApF,WAAA,CAAAO,WAAA,GAAA5C,IAAA,CAAAwF,EAAA;UACA,KAAAnD,WAAA,CAAAmF,IAAA,GAAA1G,SAAA;UACA,KAAAuB,WAAA,CAAAoF,MAAA,GAAA3G,SAAA;QACA;MACA;MACA,KAAAkE,OAAA;IACA;IACA;IACA0C,mBAAA,WAAAA,oBAAA1H,IAAA;MACA,IAAAA,IAAA,CAAAwF,EAAA;QACA,KAAAnD,WAAA,CAAAuB,QAAA,GAAA9C,SAAA;MACA;QACA,KAAAuB,WAAA,CAAAuB,QAAA,GAAA5D,IAAA,CAAAwF,EAAA;MACA;MACA,KAAAR,OAAA;IACA;IACA;IACA2C,kBAAA,WAAAA,mBAAAjB,GAAA;MAAA,IAAAkB,MAAA;MACA,IAAAC,IAAA,GAAAnB,GAAA,CAAAhE,MAAA;MACA,KAAAoF,QAAA,CACA,SAAAD,IAAA,UAAAnB,GAAA,CAAAlE,QAAA,YACA,MACA;QACAuF,iBAAA;QACAC,gBAAA;QACAvE,IAAA;MACA,CACA,EACAyB,IAAA;QACA,OAAAjG,gBAAA,CAAAyH,GAAA,CAAAuB,MAAA,EAAAvB,GAAA,CAAAhE,MAAA;MACA,GACAwC,IAAA;QACA0C,MAAA,CAAAM,UAAA,CAAAL,IAAA;MACA,GACAM,KAAA;QACAzB,GAAA,CAAAhE,MAAA,GAAAgE,GAAA,CAAAhE,MAAA;MACA;IACA;IACA;IACA0F,MAAA,WAAAA,OAAA;MACA,KAAArH,IAAA;MACA,KAAAsH,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAA7G,IAAA;QACAyG,MAAA,EAAAnH,SAAA;QACA6B,MAAA,EAAA7B,SAAA;QACA0B,QAAA,EAAA1B,SAAA;QACA6C,QAAA,EAAA7C,SAAA;QACAwH,QAAA,EAAAxH,SAAA;QACA0C,KAAA,EAAA1C,SAAA;QACA2B,WAAA,EAAA3B,SAAA;QACA+F,GAAA;QACA0B,MAAA,EAAAzH,SAAA;QACA0H,QAAA,EAAA1H,SAAA;QACA4B,MAAA;QACA+F,OAAA,EAAA3H,SAAA;QACA4H,OAAA,EAAA5H,SAAA;QACA6H,SAAA,EAAA7H,SAAA;QACA8H,QAAA,EAAA9H,SAAA;QACA+H,UAAA,EAAA/H,SAAA;QACAgI,QAAA,EAAAhI,SAAA;QACAiI,UAAA,EAAAjI,SAAA;QACAkI,MAAA,EAAAlI,SAAA;QACAuC,OAAA,EAAAvC,SAAA;QACAwC,UAAA,EAAAxC,SAAA;QACAyC,aAAA,EAAAzC,SAAA;QACA8C,QAAA;QACAC,OAAA,EAAA/C,SAAA;QACAmI,MAAA,EAAAnI,SAAA;QACAkG,OAAA;QACApE,WAAA;QACAsG,OAAA;MACA;MACA,KAAAC,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAA/G,WAAA,CAAAgH,IAAA;MACA,KAAArE,OAAA;IACA;IACA,aACAsE,UAAA,WAAAA,WAAA;MACA,KAAApI,SAAA;MACA,KAAA8C,SAAA;MACA,KAAAmF,SAAA;MACA,KAAAC,WAAA;IACA;IACA;IACAG,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAApJ,GAAA,GAAAoJ,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAzB,MAAA;MAAA;MACA,KAAA5H,MAAA,GAAAmJ,SAAA,CAAAG,MAAA;MACA,KAAArJ,QAAA,IAAAkJ,SAAA,CAAAG,MAAA;IACA;IACAC,UAAA,WAAAA,WAAAlD,GAAA;MAAA,IAAAmD,MAAA;MACA,KAAA1F,IAAA,GAAA2F,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,CAAAtD,GAAA;MAAA;MACA,KAAAvC,IAAA,CAAAvB,WAAA,QAAAqE,iBAAA,CAAAP,GAAA;MACA,KAAAvC,IAAA,CAAAzB,MAAA,QAAAoE,YAAA,CAAAJ,GAAA;MACA,KAAAvC,IAAA,CAAA6C,OAAA,QAAAD,aAAA,CAAAL,GAAA;MACA,KAAA9F,KAAA;MACA,IAAAqJ,MAAA;QAAA3F,MAAA,EAAAoC,GAAA,CAAAuB,MAAA;QAAAiC,MAAA;MAAA;MACA,KAAA5F,MAAA,GAAAoC,GAAA,CAAAuB,MAAA;MACApI,gBAAA,CAAAoK,MAAA,EAAA/E,IAAA,WAAAiF,IAAA;QACA,IAAAA,IAAA,CAAAnK,IAAA,IAAAmK,IAAA,CAAAnK,IAAA,CAAAoK,UAAA;UACAP,MAAA,CAAAzF,SAAA,GAAA+F,IAAA,CAAAnK,IAAA,CAAAoK,UAAA;UACAP,MAAA,CAAAxF,MAAA,GAAA8F,IAAA,CAAAnK,IAAA,CAAAqE,MAAA;UACA,IAAA8F,IAAA,CAAAnK,IAAA,CAAAoK,UAAA,KAAAD,IAAA,CAAAnK,IAAA,CAAAqK,OAAA,IAAAF,IAAA,CAAAnK,IAAA,CAAAsK,QAAA,IAAAT,MAAA,CAAAlF,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAoD,MAAA;YACA4B,MAAA,CAAAU,QAAA;UACA,WAAAV,MAAA,CAAAlF,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,KAAA,IAAA+E,MAAA,CAAAlF,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,KAAA,CAAAC,QAAA;YACA;YACA8E,MAAA,CAAAU,QAAA;UACA;YACAV,MAAA,CAAAU,QAAA;UACA;QACA;UACAV,MAAA,CAAAU,QAAA;QACA;QACAV,MAAA,CAAA3F,QAAA;MACA;IACA;IACA,aACAsG,SAAA,WAAAA,UAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,KAAAC,OAAA,CAAAlF,IAAA;QAAAmF,IAAA;QACAC,KAAA;UACAC,WAAA,EAAA9J,SAAA;UACA+J,QAAA;QACA;MAAA;IACA;IACA,aACAC,YAAA,WAAAA,aAAApE,GAAA;MAAA,IAAAqE,MAAA;MACA,KAAA1C,KAAA;MACA;MACA,IAAAJ,MAAA,GAAAvB,GAAA,CAAAuB,MAAA,SAAA7H,GAAA;MACAzB,OAAA,CAAAsJ,MAAA,EAAA/C,IAAA,WAAAC,QAAA;QACA4F,MAAA,CAAAvJ,IAAA,GAAA2D,QAAA,CAAAnF,IAAA;QACA+K,MAAA,CAAAC,WAAA,GAAA7F,QAAA,CAAA8F,KAAA;QACAF,MAAA,CAAAxJ,WAAA,GAAA4D,QAAA,CAAAL,KAAA;QACAiG,MAAA,CAAAvJ,IAAA,CAAA0J,OAAA,GAAA/F,QAAA,CAAA+F,OAAA;QACAH,MAAA,CAAAvJ,IAAA,CAAA0H,OAAA,GAAA/D,QAAA,CAAA+D,OAAA;QACA6B,MAAA,CAAAhK,IAAA;QACAgK,MAAA,CAAAnK,KAAA;QACAmK,MAAA,CAAAvJ,IAAA,CAAAgH,QAAA;QACA,IAAA2C,SAAA,GAAAhG,QAAA,CAAAnF,IAAA,CAAA4D,QAAA;QACA,IAAAuH,SAAA,CAAAxB,MAAA;UACA,IAAA9F,OAAA,GAAAsH,SAAA,CAAAC,KAAA;UACA,IAAAC,KAAA;UACA;UACA,IAAAxH,OAAA,CAAA8F,MAAA,MACA0B,KAAA,CAAA9F,IAAA,CAAA5F,UAAA,CAAAkE,OAAA,KAAAyH,IAAA;UACA;UACA,IAAAzH,OAAA,CAAA8F,MAAA,MACA0B,KAAA,CAAA9F,IAAA,CAAA5F,UAAA,CAAAkE,OAAA,KAAAA,OAAA,KAAAyH,IAAA;UACA;UACA,IAAAzH,OAAA,CAAA8F,MAAA,MACA0B,KAAA,CAAA9F,IAAA,CAAA5F,UAAA,CAAAkE,OAAA,KAAAA,OAAA,KAAAA,OAAA,KAAAyH,IAAA;UAEAP,MAAA,CAAAhH,WAAA,GAAAsH,KAAA;QACA;MACA;IACA;IACA,eACAE,cAAA,WAAAA,eAAA7E,GAAA;MAAA,IAAA8E,MAAA;MACA,KAAAC,OAAA,UAAA/E,GAAA,CAAAlE,QAAA;QACAuF,iBAAA;QACAC,gBAAA;MACA,GACA9C,IAAA,WAAAwG,IAAA;QAAA,IAAArE,KAAA,GAAAqE,IAAA,CAAArE,KAAA;QACArI,YAAA,CAAA0H,GAAA,CAAAuB,MAAA,EAAAZ,KAAA,EAAAnC,IAAA,WAAAC,QAAA;UACAqG,MAAA,CAAAtD,UAAA,gBAAAb,KAAA;QACA;MACA,GACAc,KAAA;IACA;IACA;IACAwD,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,SAAA7H,WAAA,CAAA4F,MAAA,OAEA;MACA,KAAAkC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAH,MAAA,CAAApK,IAAA,CAAAyG,MAAA,IAAAnH,SAAA;YACAhC,UAAA,CAAA8M,MAAA,CAAApK,IAAA,EAAA0D,IAAA,WAAAC,QAAA;cACAyG,MAAA,CAAA1D,UAAA;cACA0D,MAAA,CAAA7K,IAAA;cACA6K,MAAA,CAAA5G,OAAA;YACA;UACA;YACAnG,OAAA,CAAA+M,MAAA,CAAApK,IAAA,EAAA0D,IAAA,WAAAC,QAAA;cACAyG,MAAA,CAAA1D,UAAA;cACA0D,MAAA,CAAA7K,IAAA;cACA6K,MAAA,CAAA5G,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACAgH,YAAA,WAAAA,aAAAtF,GAAA;MAAA,IAAAuF,MAAA;MACA,IAAAC,OAAA,GAAAxF,GAAA,CAAAuB,MAAA,SAAA7H,GAAA;MACA,KAAA0H,QAAA,CACA,iBAAAoE,OAAA,aACA,MACA;QACAnE,iBAAA;QACAC,gBAAA;QACAvE,IAAA;MACA,CACA,EACAyB,IAAA;QACA,OAAAtG,OAAA,CAAAsN,OAAA;MACA,GACAhH,IAAA;QACA+G,MAAA,CAAAjH,OAAA;QACAiH,MAAA,CAAA/D,UAAA;MACA;IACA;IACA,aACAiE,YAAA,WAAAA,aAAA;MACA,KAAAvK,MAAA,CAAAhB,KAAA;MACA,KAAAgB,MAAA,CAAAb,IAAA;IACA;IACA,aACA7B,cAAA,WAAAA,eAAA;MAAA,IAAAkN,OAAA;MACAlN,eAAA,GAAAgG,IAAA,WAAAC,QAAA;QACAiH,OAAA,CAAAC,QAAA,CAAAlH,QAAA,CAAAE,GAAA;MACA;IACA;IACA;IACAiH,wBAAA,WAAAA,yBAAAC,KAAA,EAAAC,IAAA,EAAAC,QAAA;MACA,KAAA7K,MAAA,CAAAC,WAAA;IACA;IACA;IACA6K,iBAAA,WAAAA,kBAAAvH,QAAA,EAAAqH,IAAA,EAAAC,QAAA;MACA,KAAA7K,MAAA,CAAAb,IAAA;MACA,KAAAa,MAAA,CAAAC,WAAA;MACA,KAAAgK,KAAA,CAAAjK,MAAA,CAAA+K,UAAA;MACA,KAAAC,MAAA,CAAAzH,QAAA,CAAAE,GAAA;QAAAwH,wBAAA;MAAA;MACA,KAAA7H,OAAA;IACA;IACA;IACA8H,cAAA,WAAAA,eAAA;MACA,KAAAjB,KAAA,CAAAjK,MAAA,CAAAmL,MAAA;IACA;IACAC,gBAAA,WAAAA,iBAAA3F,KAAA;MACA,KAAAA,KAAA,IAAAA,KAAA,CAAAsC,MAAA;QACA,KAAA5F,WAAA;QACA,KAAAvC,IAAA,CAAAoC,QAAA,GAAA9C,SAAA;QACA,KAAAU,IAAA,CAAAyL,QAAA,GAAAnM,SAAA;QACA;MACA;MACA,KAAAiD,WAAA,GAAAsD,KAAA;MACA,IAAA6F,GAAA;MACA7F,KAAA,CAAA5B,OAAA,WAAAiE,IAAA;QACAwD,GAAA,IAAAxN,UAAA,CAAAgK,IAAA;MACA;MACA,IAAAwD,GAAA,CAAAvD,MAAA;QACAuD,GAAA,GAAAA,GAAA,CAAAC,SAAA,IAAAD,GAAA,CAAAvD,MAAA;QACA,KAAAnI,IAAA,CAAAoC,QAAA,GAAAsJ,GAAA;QACA,KAAA1L,IAAA,CAAAyL,QAAA,GAAAvN,UAAA,CAAA2H,KAAA;MACA;QACA,KAAA7F,IAAA,CAAAoC,QAAA,GAAA9C,SAAA;MACA;IAEA;IACAsM,qBAAA,WAAAA,sBAAA/F,KAAA;MACA,KAAArD,SAAA,GAAAqD,KAAA;MACA,IAAA6F,GAAA;MACA7F,KAAA,CAAA5B,OAAA,WAAAiE,IAAA;QACAwD,GAAA,IAAAxN,UAAA,CAAAgK,IAAA;MACA;MACA,IAAAwD,GAAA,CAAAvD,MAAA;QACAuD,GAAA,GAAAA,GAAA,CAAAC,SAAA,IAAAD,GAAA,CAAAvD,MAAA;QACA,KAAAtH,WAAA,CAAAuB,QAAA,GAAAsJ,GAAA;MACA;QACA,KAAA7K,WAAA,CAAAuB,QAAA,GAAA9C,SAAA;MACA;IAEA;IACAuM,WAAA,WAAAA,YAAA;MACA,KAAA7M,UAAA;IACA;IACA8M,UAAA,WAAAA,WAAA;MACA,KAAA7M,SAAA;IACA;IACA,aACA8M,YAAA,WAAAA,aAAA9J,IAAA;MAAA,IAAA+J,OAAA;MACA,KAAAnL,WAAA,CAAAoF,MAAA,GAAAhE,IAAA;MACA,IAAAgK,GAAA;MACA,KAAA5K,OAAA,CAAA4C,OAAA,WAAAiE,IAAA;QACA,IAAAA,IAAA,CAAA1G,OAAA;UACAyK,GAAA,CAAAlI,IAAA,CAAAmE,IAAA,CAAA/H,KAAA;QACA;MACA;MACA,KAAAU,WAAA,CAAAqL,MAAA,GAAAD,GAAA,CAAAE,IAAA;MACA,IAAAtL,WAAA,QAAAA,WAAA;MACA,KAAAyF,QAAA,mBAAArE,IAAA;QACAsE,iBAAA;QACAC,gBAAA;QACAvE,IAAA;MACA,GACAyB,IAAA;QACA,OAAAnG,UAAA,CAAAsD,WAAA;MACA,GACA6C,IAAA,WAAAC,QAAA;QACAqI,OAAA,CAAAnB,QAAA,CAAAlH,QAAA,CAAAE,GAAA;QACAmI,OAAA,CAAAhN,UAAA;QACAL,OAAA,CAAAyN,KAAA;MACA;IACA;IACAC,WAAA,WAAAA,YAAApK,IAAA;MACA,KAAApB,WAAA,CAAAoF,MAAA,GAAAhE,IAAA;MACA,IAAAqK,UAAA;MACAA,UAAA,CAAAvI,IAAA;QAAAwI,KAAA;QAAAC,WAAA;MAAA;MACA,KAAAnL,OAAA,CAAA4C,OAAA,WAAAiE,IAAA;QACA,IAAAA,IAAA,CAAA1G,OAAA;UACA8K,UAAA,CAAAvI,IAAA;YAAAwI,KAAA,EAAArE,IAAA,CAAA5G,GAAA;YAAAkL,WAAA,EAAAtE,IAAA,CAAA/H;UAAA;QACA;MACA;MACAxC,SAAA,MAAAkD,WAAA,EAAA6C,IAAA,WAAAC,QAAA;QACA8I,OAAA;UACAC,SAAA,EAAA/I,QAAA,CAAAnF,IAAA;UACAyD,IAAA;UACAqK,UAAA,EAAAA,UAAA;UACAK,MAAA;UACAC,YAAA;UACAC,eAAA;UACAC,SAAA;UACAC,KAAA;QACA;MAEA;IACA;EACA;AACA", "ignoreList": []}]}