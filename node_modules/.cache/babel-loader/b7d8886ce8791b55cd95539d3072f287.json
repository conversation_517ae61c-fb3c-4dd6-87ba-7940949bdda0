{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/utils/generator/css.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/utils/generator/css.js", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:dmFyIHN0eWxlcyA9IHsKICAnZWwtcmF0ZSc6ICcuZWwtcmF0ZXtkaXNwbGF5OiBpbmxpbmUtYmxvY2s7IHZlcnRpY2FsLWFsaWduOiB0ZXh0LXRvcDt9JywKICAnZWwtdXBsb2FkJzogJy5lbC11cGxvYWRfX3RpcHtsaW5lLWhlaWdodDogMS4yO30nCn07CmZ1bmN0aW9uIGFkZENzcyhjc3NMaXN0LCBlbCkgewogIHZhciBjc3MgPSBzdHlsZXNbZWwudGFnXTsKICBjc3MgJiYgY3NzTGlzdC5pbmRleE9mKGNzcykgPT09IC0xICYmIGNzc0xpc3QucHVzaChjc3MpOwogIGlmIChlbC5jaGlsZHJlbikgewogICAgZWwuY2hpbGRyZW4uZm9yRWFjaChmdW5jdGlvbiAoZWwyKSB7CiAgICAgIHJldHVybiBhZGRDc3MoY3NzTGlzdCwgZWwyKTsKICAgIH0pOwogIH0KfQpleHBvcnQgZnVuY3Rpb24gbWFrZVVwQ3NzKGNvbmYpIHsKICB2YXIgY3NzTGlzdCA9IFtdOwogIGNvbmYuZmllbGRzLmZvckVhY2goZnVuY3Rpb24gKGVsKSB7CiAgICByZXR1cm4gYWRkQ3NzKGNzc0xpc3QsIGVsKTsKICB9KTsKICByZXR1cm4gY3NzTGlzdC5qb2luKCdcbicpOwp9"}, {"version": 3, "names": ["styles", "addCss", "cssList", "el", "css", "tag", "indexOf", "push", "children", "for<PERSON>ach", "el2", "makeUpCss", "conf", "fields", "join"], "sources": ["/Users/<USER>/develop/code/project/haijia/adminweb/src/utils/generator/css.js"], "sourcesContent": ["const styles = {\n  'el-rate': '.el-rate{display: inline-block; vertical-align: text-top;}',\n  'el-upload': '.el-upload__tip{line-height: 1.2;}'\n}\n\nfunction addCss(cssList, el) {\n  const css = styles[el.tag]\n  css && cssList.indexOf(css) === -1 && cssList.push(css)\n  if (el.children) {\n    el.children.forEach(el2 => addCss(cssList, el2))\n  }\n}\n\nexport function makeUpCss(conf) {\n  const cssList = []\n  conf.fields.forEach(el => addCss(cssList, el))\n  return cssList.join('\\n')\n}\n"], "mappings": "AAAA,IAAMA,MAAM,GAAG;EACb,SAAS,EAAE,4DAA4D;EACvE,WAAW,EAAE;AACf,CAAC;AAED,SAASC,MAAMA,CAACC,OAAO,EAAEC,EAAE,EAAE;EAC3B,IAAMC,GAAG,GAAGJ,MAAM,CAACG,EAAE,CAACE,GAAG,CAAC;EAC1BD,GAAG,IAAIF,OAAO,CAACI,OAAO,CAACF,GAAG,CAAC,KAAK,CAAC,CAAC,IAAIF,OAAO,CAACK,IAAI,CAACH,GAAG,CAAC;EACvD,IAAID,EAAE,CAACK,QAAQ,EAAE;IACfL,EAAE,CAACK,QAAQ,CAACC,OAAO,CAAC,UAAAC,GAAG;MAAA,OAAIT,MAAM,CAACC,OAAO,EAAEQ,GAAG,CAAC;IAAA,EAAC;EAClD;AACF;AAEA,OAAO,SAASC,SAASA,CAACC,IAAI,EAAE;EAC9B,IAAMV,OAAO,GAAG,EAAE;EAClBU,IAAI,CAACC,MAAM,CAACJ,OAAO,CAAC,UAAAN,EAAE;IAAA,OAAIF,MAAM,CAACC,OAAO,EAAEC,EAAE,CAAC;EAAA,EAAC;EAC9C,OAAOD,OAAO,CAACY,IAAI,CAAC,IAAI,CAAC;AAC3B", "ignoreList": []}]}