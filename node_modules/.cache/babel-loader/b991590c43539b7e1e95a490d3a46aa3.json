{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/api/monitor/online.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/api/monitor/online.js", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QgZnJvbSAnQC91dGlscy9yZXF1ZXN0JzsKCi8vIOafpeivouWcqOe6v+eUqOaIt+WIl+ihqApleHBvcnQgZnVuY3Rpb24gbGlzdChxdWVyeSkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9tb25pdG9yL29ubGluZS9saXN0JywKICAgIG1ldGhvZDogJ2dldCcsCiAgICBwYXJhbXM6IHF1ZXJ5CiAgfSk7Cn0KCi8vIOW8uumAgOeUqOaItwpleHBvcnQgZnVuY3Rpb24gZm9yY2VMb2dvdXQodG9rZW5JZCkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9tb25pdG9yL29ubGluZS8nICsgdG9rZW5JZCwKICAgIG1ldGhvZDogJ2RlbGV0ZScKICB9KTsKfQ=="}, {"version": 3, "names": ["request", "list", "query", "url", "method", "params", "forceLogout", "tokenId"], "sources": ["/Users/<USER>/develop/code/project/haijia/adminweb/src/api/monitor/online.js"], "sourcesContent": ["import request from '@/utils/request'\n\n// 查询在线用户列表\nexport function list(query) {\n  return request({\n    url: '/monitor/online/list',\n    method: 'get',\n    params: query\n  })\n}\n\n// 强退用户\nexport function forceLogout(tokenId) {\n  return request({\n    url: '/monitor/online/' + tokenId,\n    method: 'delete'\n  })\n}\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;;AAErC;AACA,OAAO,SAASC,IAAIA,CAACC,KAAK,EAAE;EAC1B,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,sBAAsB;IAC3BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEH;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASI,WAAWA,CAACC,OAAO,EAAE;EACnC,OAAOP,OAAO,CAAC;IACbG,GAAG,EAAE,kBAAkB,GAAGI,OAAO;IACjCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}