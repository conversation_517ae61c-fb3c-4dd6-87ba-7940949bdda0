{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/main.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/main.js", "mtime": 1664290784000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js", "mtime": 1751171659043}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}