{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/components/SvgIcon/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/components/SvgIcon/index.vue", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGlzRXh0ZXJuYWwgYXMgX2lzRXh0ZXJuYWwgfSBmcm9tICdAL3V0aWxzL3ZhbGlkYXRlJzsKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdTdmdJY29uJywKICBwcm9wczogewogICAgaWNvbkNsYXNzOiB7CiAgICAgIHR5cGU6IFN0cmluZywKICAgICAgcmVxdWlyZWQ6IHRydWUKICAgIH0sCiAgICBjbGFzc05hbWU6IHsKICAgICAgdHlwZTogU3RyaW5nLAogICAgICBkZWZhdWx0OiAnJwogICAgfQogIH0sCiAgY29tcHV0ZWQ6IHsKICAgIGlzRXh0ZXJuYWw6IGZ1bmN0aW9uIGlzRXh0ZXJuYWwoKSB7CiAgICAgIHJldHVybiBfaXNFeHRlcm5hbCh0aGlzLmljb25DbGFzcyk7CiAgICB9LAogICAgaWNvbk5hbWU6IGZ1bmN0aW9uIGljb25OYW1lKCkgewogICAgICByZXR1cm4gIiNpY29uLSIuY29uY2F0KHRoaXMuaWNvbkNsYXNzKTsKICAgIH0sCiAgICBzdmdDbGFzczogZnVuY3Rpb24gc3ZnQ2xhc3MoKSB7CiAgICAgIGlmICh0aGlzLmNsYXNzTmFtZSkgewogICAgICAgIHJldHVybiAnc3ZnLWljb24gJyArIHRoaXMuY2xhc3NOYW1lOwogICAgICB9IGVsc2UgewogICAgICAgIHJldHVybiAnc3ZnLWljb24nOwogICAgICB9CiAgICB9LAogICAgc3R5bGVFeHRlcm5hbEljb246IGZ1bmN0aW9uIHN0eWxlRXh0ZXJuYWxJY29uKCkgewogICAgICByZXR1cm4gewogICAgICAgIG1hc2s6ICJ1cmwoIi5jb25jYXQodGhpcy5pY29uQ2xhc3MsICIpIG5vLXJlcGVhdCA1MCUgNTAlIiksCiAgICAgICAgJy13ZWJraXQtbWFzayc6ICJ1cmwoIi5jb25jYXQodGhpcy5pY29uQ2xhc3MsICIpIG5vLXJlcGVhdCA1MCUgNTAlIikKICAgICAgfTsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["isExternal", "name", "props", "iconClass", "type", "String", "required", "className", "default", "computed", "iconName", "concat", "svgClass", "styleExternalIcon", "mask"], "sources": ["src/components/SvgIcon/index.vue"], "sourcesContent": ["<template>\n  <div v-if=\"isExternal\" :style=\"styleExternalIcon\" class=\"svg-external-icon svg-icon\" v-on=\"$listeners\" />\n  <svg v-else :class=\"svgClass\" aria-hidden=\"true\" v-on=\"$listeners\">\n    <use :xlink:href=\"iconName\" />\n  </svg>\n</template>\n\n<script>\nimport { isExternal } from '@/utils/validate'\n\nexport default {\n  name: 'SvgIcon',\n  props: {\n    iconClass: {\n      type: String,\n      required: true\n    },\n    className: {\n      type: String,\n      default: ''\n    }\n  },\n  computed: {\n    isExternal() {\n      return isExternal(this.iconClass)\n    },\n    iconName() {\n      return `#icon-${this.iconClass}`\n    },\n    svgClass() {\n      if (this.className) {\n        return 'svg-icon ' + this.className\n      } else {\n        return 'svg-icon'\n      }\n    },\n    styleExternalIcon() {\n      return {\n        mask: `url(${this.iconClass}) no-repeat 50% 50%`,\n        '-webkit-mask': `url(${this.iconClass}) no-repeat 50% 50%`\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.svg-icon {\n  width: 1em;\n  height: 1em;\n  vertical-align: -0.15em;\n  fill: currentColor;\n  overflow: hidden;\n}\n\n.svg-external-icon {\n  background-color: currentColor;\n  mask-size: cover!important;\n  display: inline-block;\n}\n</style>\n"], "mappings": ";;;;;;;;AAQA,SAAAA,UAAA,IAAAA,WAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACAC,SAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,QAAA;IACA;IACAC,SAAA;MACAH,IAAA,EAAAC,MAAA;MACAG,OAAA;IACA;EACA;EACAC,QAAA;IACAT,UAAA,WAAAA,WAAA;MACA,OAAAA,WAAA,MAAAG,SAAA;IACA;IACAO,QAAA,WAAAA,SAAA;MACA,gBAAAC,MAAA,MAAAR,SAAA;IACA;IACAS,QAAA,WAAAA,SAAA;MACA,SAAAL,SAAA;QACA,0BAAAA,SAAA;MACA;QACA;MACA;IACA;IACAM,iBAAA,WAAAA,kBAAA;MACA;QACAC,IAAA,SAAAH,MAAA,MAAAR,SAAA;QACA,uBAAAQ,MAAA,MAAAR,SAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}