{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/monitor/cache/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/monitor/cache/index.vue", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldENhY2hlIH0gZnJvbSAiQC9hcGkvbW9uaXRvci9jYWNoZSI7CmltcG9ydCBlY2hhcnRzIGZyb20gImVjaGFydHMiOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogIlNlcnZlciIsCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIC8vIOWKoOi9veWxguS/oeaBrwogICAgICBsb2FkaW5nOiBbXSwKICAgICAgLy8g57uf6K6h5ZG95Luk5L+h5oGvCiAgICAgIGNvbW1hbmRzdGF0czogbnVsbCwKICAgICAgLy8g5L2/55So5YaF5a2YCiAgICAgIHVzZWRtZW1vcnk6IG51bGwsCiAgICAgIC8vIGNhY2hl5L+h5oGvCiAgICAgIGNhY2hlOiBbXQogICAgfTsKICB9LAogIGNyZWF0ZWQ6IGZ1bmN0aW9uIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmdldExpc3QoKTsKICAgIHRoaXMub3BlbkxvYWRpbmcoKTsKICB9LAogIG1ldGhvZHM6IHsKICAgIC8qKiDmn6XnvJPlrZjor6Lkv6Hmga8gKi9nZXRMaXN0OiBmdW5jdGlvbiBnZXRMaXN0KCkgewogICAgICB2YXIgX3RoaXMgPSB0aGlzOwogICAgICBnZXRDYWNoZSgpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgX3RoaXMuY2FjaGUgPSByZXNwb25zZS5kYXRhOwogICAgICAgIF90aGlzLmxvYWRpbmcuY2xvc2UoKTsKICAgICAgICBfdGhpcy5jb21tYW5kc3RhdHMgPSBlY2hhcnRzLmluaXQoX3RoaXMuJHJlZnMuY29tbWFuZHN0YXRzLCAibWFjYXJvbnMiKTsKICAgICAgICBfdGhpcy5jb21tYW5kc3RhdHMuc2V0T3B0aW9uKHsKICAgICAgICAgIHRvb2x0aXA6IHsKICAgICAgICAgICAgdHJpZ2dlcjogIml0ZW0iLAogICAgICAgICAgICBmb3JtYXR0ZXI6ICJ7YX0gPGJyLz57Yn0gOiB7Y30gKHtkfSUpIgogICAgICAgICAgfSwKICAgICAgICAgIHNlcmllczogW3sKICAgICAgICAgICAgbmFtZTogIuWRveS7pCIsCiAgICAgICAgICAgIHR5cGU6ICJwaWUiLAogICAgICAgICAgICByb3NlVHlwZTogInJhZGl1cyIsCiAgICAgICAgICAgIHJhZGl1czogWzE1LCA5NV0sCiAgICAgICAgICAgIGNlbnRlcjogWyI1MCUiLCAiMzglIl0sCiAgICAgICAgICAgIGRhdGE6IHJlc3BvbnNlLmRhdGEuY29tbWFuZFN0YXRzLAogICAgICAgICAgICBhbmltYXRpb25FYXNpbmc6ICJjdWJpY0luT3V0IiwKICAgICAgICAgICAgYW5pbWF0aW9uRHVyYXRpb246IDEwMDAKICAgICAgICAgIH1dCiAgICAgICAgfSk7CiAgICAgICAgX3RoaXMudXNlZG1lbW9yeSA9IGVjaGFydHMuaW5pdChfdGhpcy4kcmVmcy51c2VkbWVtb3J5LCAibWFjYXJvbnMiKTsKICAgICAgICBfdGhpcy51c2VkbWVtb3J5LnNldE9wdGlvbih7CiAgICAgICAgICB0b29sdGlwOiB7CiAgICAgICAgICAgIGZvcm1hdHRlcjogIntifSA8YnIvPnthfSA6ICIgKyBfdGhpcy5jYWNoZS5pbmZvLnVzZWRfbWVtb3J5X2h1bWFuCiAgICAgICAgICB9LAogICAgICAgICAgc2VyaWVzOiBbewogICAgICAgICAgICBuYW1lOiAi5bOw5YC8IiwKICAgICAgICAgICAgdHlwZTogImdhdWdlIiwKICAgICAgICAgICAgbWluOiAwLAogICAgICAgICAgICBtYXg6IDEwMDAsCiAgICAgICAgICAgIGRldGFpbDogewogICAgICAgICAgICAgIGZvcm1hdHRlcjogX3RoaXMuY2FjaGUuaW5mby51c2VkX21lbW9yeV9odW1hbgogICAgICAgICAgICB9LAogICAgICAgICAgICBkYXRhOiBbewogICAgICAgICAgICAgIHZhbHVlOiBwYXJzZUZsb2F0KF90aGlzLmNhY2hlLmluZm8udXNlZF9tZW1vcnlfaHVtYW4pLAogICAgICAgICAgICAgIG5hbWU6ICLlhoXlrZjmtojogJciCiAgICAgICAgICAgIH1dCiAgICAgICAgICB9XQogICAgICAgIH0pOwogICAgICB9KTsKICAgIH0sCiAgICAvLyDmiZPlvIDliqDovb3lsYIKICAgIG9wZW5Mb2FkaW5nOiBmdW5jdGlvbiBvcGVuTG9hZGluZygpIHsKICAgICAgdGhpcy5sb2FkaW5nID0gdGhpcy4kbG9hZGluZyh7CiAgICAgICAgbG9jazogdHJ1ZSwKICAgICAgICB0ZXh0OiAi5ou85ZG96K+75Y+W5LitIiwKICAgICAgICBzcGlubmVyOiAiZWwtaWNvbi1sb2FkaW5nIiwKICAgICAgICBiYWNrZ3JvdW5kOiAicmdiYSgwLCAwLCAwLCAwLjcpIgogICAgICB9KTsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["getCache", "echarts", "name", "data", "loading", "commandstats", "usedmemory", "cache", "created", "getList", "openLoading", "methods", "_this", "then", "response", "close", "init", "$refs", "setOption", "tooltip", "trigger", "formatter", "series", "type", "roseType", "radius", "center", "commandStats", "animationEasing", "animationDuration", "info", "used_memory_human", "min", "max", "detail", "value", "parseFloat", "$loading", "lock", "text", "spinner", "background"], "sources": ["src/views/monitor/cache/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-row>\n      <el-col :span=\"24\" class=\"card-box\">\n        <el-card>\n          <div slot=\"header\"><span>基本信息</span></div>\n          <div class=\"el-table el-table--enable-row-hover el-table--medium\">\n            <table cellspacing=\"0\" style=\"width: 100%\">\n              <tbody>\n                <tr>\n                  <td><div class=\"cell\">Redis版本</div></td>\n                  <td><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.redis_version }}</div></td>\n                  <td><div class=\"cell\">运行模式</div></td>\n                  <td><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.redis_mode == \"standalone\" ? \"单机\" : \"集群\" }}</div></td>\n                  <td><div class=\"cell\">端口</div></td>\n                  <td><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.tcp_port }}</div></td>\n                  <td><div class=\"cell\">客户端数</div></td>\n                  <td><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.connected_clients }}</div></td>\n                </tr>\n                <tr>\n                  <td><div class=\"cell\">运行时间(天)</div></td>\n                  <td><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.uptime_in_days }}</div></td>\n                  <td><div class=\"cell\">使用内存</div></td>\n                  <td><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.used_memory_human }}</div></td>\n                  <td><div class=\"cell\">使用CPU</div></td>\n                  <td><div class=\"cell\" v-if=\"cache.info\">{{ parseFloat(cache.info.used_cpu_user_children).toFixed(2) }}</div></td>\n                  <td><div class=\"cell\">内存配置</div></td>\n                  <td><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.maxmemory_human }}</div></td>\n                </tr>\n                <tr>\n                  <td><div class=\"cell\">AOF是否开启</div></td>\n                  <td><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.aof_enabled == \"0\" ? \"否\" : \"是\" }}</div></td>\n                  <td><div class=\"cell\">RDB是否成功</div></td>\n                  <td><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.rdb_last_bgsave_status }}</div></td>\n                  <td><div class=\"cell\">Key数量</div></td>\n                  <td><div class=\"cell\" v-if=\"cache.dbSize\">{{ cache.dbSize }} </div></td>\n                  <td><div class=\"cell\">网络入口/出口</div></td>\n                  <td><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.instantaneous_input_kbps }}kps/{{cache.info.instantaneous_output_kbps}}kps</div></td>\n                </tr>\n              </tbody>\n            </table>\n          </div>\n        </el-card>\n      </el-col>\n\n      <el-col :span=\"12\" class=\"card-box\">\n        <el-card>\n          <div slot=\"header\"><span>命令统计</span></div>\n          <div class=\"el-table el-table--enable-row-hover el-table--medium\">\n            <div ref=\"commandstats\" style=\"height: 420px\" />\n          </div>\n        </el-card>\n      </el-col>\n\n      <el-col :span=\"12\" class=\"card-box\">\n        <el-card>\n          <div slot=\"header\">\n            <span>内存信息</span>\n          </div>\n          <div class=\"el-table el-table--enable-row-hover el-table--medium\">\n            <div ref=\"usedmemory\" style=\"height: 420px\" />\n          </div>\n        </el-card>\n      </el-col>\n    </el-row>\n  </div>\n</template>\n\n<script>\nimport { getCache } from \"@/api/monitor/cache\";\nimport echarts from \"echarts\";\n\nexport default {\n  name: \"Server\",\n  data() {\n    return {\n      // 加载层信息\n      loading: [],\n      // 统计命令信息\n      commandstats: null,\n      // 使用内存\n      usedmemory: null,\n      // cache信息\n      cache: [],\n    };\n  },\n  created() {\n    this.getList();\n    this.openLoading();\n  },\n  methods: {\n    /** 查缓存询信息 */\n    getList() {\n      getCache().then((response) => {\n        this.cache = response.data;\n        this.loading.close();\n\n        this.commandstats = echarts.init(this.$refs.commandstats, \"macarons\");\n        this.commandstats.setOption({\n          tooltip: {\n            trigger: \"item\",\n            formatter: \"{a} <br/>{b} : {c} ({d}%)\",\n          },\n          series: [\n            {\n              name: \"命令\",\n              type: \"pie\",\n              roseType: \"radius\",\n              radius: [15, 95],\n              center: [\"50%\", \"38%\"],\n              data: response.data.commandStats,\n              animationEasing: \"cubicInOut\",\n              animationDuration: 1000,\n            },\n          ],\n        });\n        this.usedmemory = echarts.init(this.$refs.usedmemory, \"macarons\");\n        this.usedmemory.setOption({\n          tooltip: {\n            formatter: \"{b} <br/>{a} : \" + this.cache.info.used_memory_human,\n          },\n          series: [\n            {\n              name: \"峰值\",\n              type: \"gauge\",\n              min: 0,\n              max: 1000,\n              detail: {\n                formatter: this.cache.info.used_memory_human,\n              },\n              data: [\n                {\n                  value: parseFloat(this.cache.info.used_memory_human),\n                  name: \"内存消耗\",\n                },\n              ],\n            },\n          ],\n        });\n      });\n    },\n    // 打开加载层\n    openLoading() {\n      this.loading = this.$loading({\n        lock: true,\n        text: \"拼命读取中\",\n        spinner: \"el-icon-loading\",\n        background: \"rgba(0, 0, 0, 0.7)\",\n      });\n    },\n  },\n};\n</script>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqEA,SAAAA,QAAA;AACA,OAAAC,OAAA;AAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,YAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;IACA,KAAAC,WAAA;EACA;EACAC,OAAA;IACA,aACAF,OAAA,WAAAA,QAAA;MAAA,IAAAG,KAAA;MACAZ,QAAA,GAAAa,IAAA,WAAAC,QAAA;QACAF,KAAA,CAAAL,KAAA,GAAAO,QAAA,CAAAX,IAAA;QACAS,KAAA,CAAAR,OAAA,CAAAW,KAAA;QAEAH,KAAA,CAAAP,YAAA,GAAAJ,OAAA,CAAAe,IAAA,CAAAJ,KAAA,CAAAK,KAAA,CAAAZ,YAAA;QACAO,KAAA,CAAAP,YAAA,CAAAa,SAAA;UACAC,OAAA;YACAC,OAAA;YACAC,SAAA;UACA;UACAC,MAAA,GACA;YACApB,IAAA;YACAqB,IAAA;YACAC,QAAA;YACAC,MAAA;YACAC,MAAA;YACAvB,IAAA,EAAAW,QAAA,CAAAX,IAAA,CAAAwB,YAAA;YACAC,eAAA;YACAC,iBAAA;UACA;QAEA;QACAjB,KAAA,CAAAN,UAAA,GAAAL,OAAA,CAAAe,IAAA,CAAAJ,KAAA,CAAAK,KAAA,CAAAX,UAAA;QACAM,KAAA,CAAAN,UAAA,CAAAY,SAAA;UACAC,OAAA;YACAE,SAAA,sBAAAT,KAAA,CAAAL,KAAA,CAAAuB,IAAA,CAAAC;UACA;UACAT,MAAA,GACA;YACApB,IAAA;YACAqB,IAAA;YACAS,GAAA;YACAC,GAAA;YACAC,MAAA;cACAb,SAAA,EAAAT,KAAA,CAAAL,KAAA,CAAAuB,IAAA,CAAAC;YACA;YACA5B,IAAA,GACA;cACAgC,KAAA,EAAAC,UAAA,CAAAxB,KAAA,CAAAL,KAAA,CAAAuB,IAAA,CAAAC,iBAAA;cACA7B,IAAA;YACA;UAEA;QAEA;MACA;IACA;IACA;IACAQ,WAAA,WAAAA,YAAA;MACA,KAAAN,OAAA,QAAAiC,QAAA;QACAC,IAAA;QACAC,IAAA;QACAC,OAAA;QACAC,UAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}