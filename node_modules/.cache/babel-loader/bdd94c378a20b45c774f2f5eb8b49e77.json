{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/layout/components/Sidebar/Item.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/layout/components/Sidebar/Item.vue", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:ZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdNZW51SXRlbScsCiAgZnVuY3Rpb25hbDogdHJ1ZSwKICBwcm9wczogewogICAgaWNvbjogewogICAgICB0eXBlOiBTdHJpbmcsCiAgICAgIGRlZmF1bHQ6ICcnCiAgICB9LAogICAgdGl0bGU6IHsKICAgICAgdHlwZTogU3RyaW5nLAogICAgICBkZWZhdWx0OiAnJwogICAgfQogIH0sCiAgcmVuZGVyOiBmdW5jdGlvbiByZW5kZXIoaCwgY29udGV4dCkgewogICAgdmFyIF9jb250ZXh0JHByb3BzID0gY29udGV4dC5wcm9wcywKICAgICAgaWNvbiA9IF9jb250ZXh0JHByb3BzLmljb24sCiAgICAgIHRpdGxlID0gX2NvbnRleHQkcHJvcHMudGl0bGU7CiAgICB2YXIgdm5vZGVzID0gW107CiAgICBpZiAoaWNvbikgewogICAgICB2bm9kZXMucHVzaChoKCJzdmctaWNvbiIsIHsKICAgICAgICAiYXR0cnMiOiB7CiAgICAgICAgICAiaWNvbi1jbGFzcyI6IGljb24KICAgICAgICB9CiAgICAgIH0pKTsKICAgIH0KICAgIGlmICh0aXRsZSkgewogICAgICB2bm9kZXMucHVzaChoKCJzcGFuIiwgewogICAgICAgICJzbG90IjogJ3RpdGxlJwogICAgICB9LCBbdGl0bGVdKSk7CiAgICB9CiAgICByZXR1cm4gdm5vZGVzOwogIH0KfTs="}, {"version": 3, "names": ["name", "functional", "props", "icon", "type", "String", "default", "title", "render", "h", "context", "_context$props", "vnodes", "push"], "sources": ["src/layout/components/Sidebar/Item.vue"], "sourcesContent": ["<script>\nexport default {\n  name: 'MenuItem',\n  functional: true,\n  props: {\n    icon: {\n      type: String,\n      default: ''\n    },\n    title: {\n      type: String,\n      default: ''\n    }\n  },\n  render(h, context) {\n    const { icon, title } = context.props\n    const vnodes = []\n\n    if (icon) {\n      vnodes.push(<svg-icon icon-class={icon}/>)\n    }\n\n    if (title) {\n      vnodes.push(<span slot='title'>{(title)}</span>)\n    }\n    return vnodes\n  }\n}\n</script>\n"], "mappings": "AACA;EACAA,IAAA;EACAC,UAAA;EACAC,KAAA;IACAC,IAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAC,KAAA;MACAH,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;EACA;EACAE,MAAA,WAAAA,OAAAC,CAAA,EAAAC,OAAA;IACA,IAAAC,cAAA,GAAAD,OAAA,CAAAR,KAAA;MAAAC,IAAA,GAAAQ,cAAA,CAAAR,IAAA;MAAAI,KAAA,GAAAI,cAAA,CAAAJ,KAAA;IACA,IAAAK,MAAA;IAEA,IAAAT,IAAA;MACAS,MAAA,CAAAC,IAAA,CAAAJ,CAAA;QAAA;UAAA,cAAAN;QAAA;MAAA;IACA;IAEA,IAAAI,KAAA;MACAK,MAAA,CAAAC,IAAA,CAAAJ,CAAA;QAAA;MAAA,IAAAF,KAAA;IACA;IACA,OAAAK,MAAA;EACA;AACA", "ignoreList": []}]}