{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/components/RightToolbar/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/components/RightToolbar/index.vue", "mtime": 1665827762000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["name", "data", "value", "title", "open", "typeOptions", "lable", "type", "checkAll", "columnOptions", "showColumns", "exportColumns", "printColumns", "isIndeterminate", "props", "showSearch", "Boolean", "default", "columns", "Array", "showExport", "showPrint", "watch", "val", "created", "map", "x", "label", "methods", "toggleSearch", "$emit", "refresh", "dataChange", "item", "key", "visible", "includes", "showColumn", "confirm", "cancel", "handleCheckAllChange", "handleshowColumnsChange"], "sources": ["src/components/RightToolbar/index.vue"], "sourcesContent": ["<template>\n  <div class=\"top-right-btn\">\n    <el-row>\n      <el-tooltip class=\"item\" effect=\"dark\" :content=\"showSearch ? '隐藏搜索' : '显示搜索'\" placement=\"top\">\n        <el-button size=\"mini\" circle icon=\"el-icon-search\" @click=\"toggleSearch()\" />\n      </el-tooltip>\n      <el-tooltip class=\"item\" effect=\"dark\" content=\"刷新\" placement=\"top\">\n        <el-button size=\"mini\" circle icon=\"el-icon-refresh\" @click=\"refresh()\" />\n      </el-tooltip>\n      <el-tooltip class=\"item\" effect=\"dark\" content=\"显隐列\" placement=\"top\" v-if=\"columns\">\n        <el-button size=\"mini\" circle icon=\"el-icon-menu\" @click=\"showColumn()\" />\n      </el-tooltip>      \n    </el-row>    \n    <el-dialog :title=\"title\" :visible.sync=\"open\" @close=\"cancel\" append-to-body>\n      \n      <!-- <el-transfer\n        :titles=\"['显示', '隐藏']\"\n        v-model=\"value\"\n        :data=\"columns\"\n        @change=\"dataChange\"\n      ></el-transfer> -->\n      <el-row>\n        <el-checkbox :indeterminate=\"isIndeterminate\" v-model=\"checkAll\" @change=\"handleCheckAllChange\">全选</el-checkbox>\n        <div style=\"margin: 15px 0;\"></div>\n        <el-checkbox-group v-model=\"showColumns\" @change=\"handleshowColumnsChange\">\n          <el-checkbox v-for=\"column in columns\" :label=\"column.label\" :key=\"column.key\">{{column.label}}</el-checkbox>\n        </el-checkbox-group>\n      </el-row>\n      \n      <div v-if=\"showExport||showPrint\" slot=\"footer\" class=\"dialog-footer\">\n        <span style=\"margin-right:150px;\">\n          <span>数据范围:</span>\n          <el-radio-group v-model=\"type\">\n            <el-radio\n              v-for=\"dict in typeOptions\"\n              :key=\"dict.value\"\n              :label=\"dict.value\"\n              >{{ dict.lable }}</el-radio>\n          </el-radio-group>\n        </span>\n        <el-button type=\"primary\" @click=\"confirm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n<script>\nexport default {\n  name: \"RightToolbar\",\n  data() {\n    return {\n      // 显隐数据\n      value: [],\n      // 弹出层标题\n      title: \"显示/隐藏\",\n      // 是否显示弹出层\n      open: false,\n      typeOptions: [\n        {value: 0, lable: '本页'},\n        {value: 1, lable: '全部'},\n      ],\n      type: 0,\n      checkAll: false,\n      columnOptions: [],\n      showColumns: [],\n      exportColumns: [],\n      printColumns: [],\n      isIndeterminate: true\n    };\n  },\n  props: {\n    showSearch: {\n      type: Boolean,\n      default: true,\n    },\n    columns: {\n      type: Array,\n    },\n    showExport: {\n      type: Boolean,\n      default: false,\n    },\n    showPrint: {\n      type: Boolean,\n      default: false,\n    },\n  },\n  watch: {\n    showExport(val){\n      this.open = val;\n      if(val){\n        this.title = \"导出选项\";\n      }else{\n        this.title = \"显示/隐藏\";\n      }\n    },    \n    showPrint(val){\n      this.open = val;\n      if(val){\n        this.title = \"打印选项\";\n      }else{\n        this.title = \"显示/隐藏\";\n      }\n    }\n  },\n  created(){\n    this.showColumns = this.columns.map(x => {return x.label}) // 生成数组\n    this.columnOptions = this.showColumns\n  },\n  methods: {\n    // 搜索\n    toggleSearch() {\n      this.$emit(\"update:showSearch\", !this.showSearch);\n    },\n    // 刷新\n    refresh() {\n      this.$emit(\"queryTable\");\n    },\n    // 右侧列表元素变化\n    dataChange(data) {\n      for (var item in this.columns) {\n        const key = this.columns[item].key;\n        this.columns[item].visible = !data.includes(key);\n      }\n    },\n    // 打开显隐列dialog\n    showColumn() {\n      this.open = true;\n    },\n    confirm(){\n      if(this.showExport){\n        this.$emit(\"export\", this.type);\n      }\n      if(this.showPrint){\n        this.$emit(\"print\", this.type);\n      }\n    },\n    cancel(){\n      if(this.showExport){\n        this.$emit(\"update:showExport\", false);\n      }\n      if(this.showPrint){\n        this.$emit(\"update:showPrint\", false);\n      }\n    },\n    handleCheckAllChange(val) {\n      this.showColumns = val ? this.columnOptions : [];\n      this.isIndeterminate = false;\n      for (var item in this.columns) {\n        this.columns[item].visible = val;\n      }      \n    },\n    handleshowColumnsChange(value) {\n     // console.log(value)\n      // let checkedCount = value.length;\n      // this.checkAll = checkedCount === this.cities.length;\n      // this.isIndeterminate = checkedCount > 0 && checkedCount < this.cities.length;\n      for (var item in this.columns) {\n        const key = this.columns[item].label;\n        this.columns[item].visible = value.includes(key);\n      }\n    }\n  },\n};\n</script>\n<style lang=\"scss\" scoped>\n::v-deep .el-transfer__button {\n  border-radius: 50%;\n  padding: 12px;\n  display: block;\n  margin-left: 0px;\n}\n::v-deep .el-transfer__button:first-child {\n  margin-bottom: 10px;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+CA;EACAA,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,KAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACAC,WAAA,GACA;QAAAH,KAAA;QAAAI,KAAA;MAAA,GACA;QAAAJ,KAAA;QAAAI,KAAA;MAAA,EACA;MACAC,IAAA;MACAC,QAAA;MACAC,aAAA;MACAC,WAAA;MACAC,aAAA;MACAC,YAAA;MACAC,eAAA;IACA;EACA;EACAC,KAAA;IACAC,UAAA;MACAR,IAAA,EAAAS,OAAA;MACAC,OAAA;IACA;IACAC,OAAA;MACAX,IAAA,EAAAY;IACA;IACAC,UAAA;MACAb,IAAA,EAAAS,OAAA;MACAC,OAAA;IACA;IACAI,SAAA;MACAd,IAAA,EAAAS,OAAA;MACAC,OAAA;IACA;EACA;EACAK,KAAA;IACAF,UAAA,WAAAA,WAAAG,GAAA;MACA,KAAAnB,IAAA,GAAAmB,GAAA;MACA,IAAAA,GAAA;QACA,KAAApB,KAAA;MACA;QACA,KAAAA,KAAA;MACA;IACA;IACAkB,SAAA,WAAAA,UAAAE,GAAA;MACA,KAAAnB,IAAA,GAAAmB,GAAA;MACA,IAAAA,GAAA;QACA,KAAApB,KAAA;MACA;QACA,KAAAA,KAAA;MACA;IACA;EACA;EACAqB,OAAA,WAAAA,QAAA;IACA,KAAAd,WAAA,QAAAQ,OAAA,CAAAO,GAAA,WAAAC,CAAA;MAAA,OAAAA,CAAA,CAAAC,KAAA;IAAA;IACA,KAAAlB,aAAA,QAAAC,WAAA;EACA;EACAkB,OAAA;IACA;IACAC,YAAA,WAAAA,aAAA;MACA,KAAAC,KAAA,4BAAAf,UAAA;IACA;IACA;IACAgB,OAAA,WAAAA,QAAA;MACA,KAAAD,KAAA;IACA;IACA;IACAE,UAAA,WAAAA,WAAA/B,IAAA;MACA,SAAAgC,IAAA,SAAAf,OAAA;QACA,IAAAgB,GAAA,QAAAhB,OAAA,CAAAe,IAAA,EAAAC,GAAA;QACA,KAAAhB,OAAA,CAAAe,IAAA,EAAAE,OAAA,IAAAlC,IAAA,CAAAmC,QAAA,CAAAF,GAAA;MACA;IACA;IACA;IACAG,UAAA,WAAAA,WAAA;MACA,KAAAjC,IAAA;IACA;IACAkC,OAAA,WAAAA,QAAA;MACA,SAAAlB,UAAA;QACA,KAAAU,KAAA,gBAAAvB,IAAA;MACA;MACA,SAAAc,SAAA;QACA,KAAAS,KAAA,eAAAvB,IAAA;MACA;IACA;IACAgC,MAAA,WAAAA,OAAA;MACA,SAAAnB,UAAA;QACA,KAAAU,KAAA;MACA;MACA,SAAAT,SAAA;QACA,KAAAS,KAAA;MACA;IACA;IACAU,oBAAA,WAAAA,qBAAAjB,GAAA;MACA,KAAAb,WAAA,GAAAa,GAAA,QAAAd,aAAA;MACA,KAAAI,eAAA;MACA,SAAAoB,IAAA,SAAAf,OAAA;QACA,KAAAA,OAAA,CAAAe,IAAA,EAAAE,OAAA,GAAAZ,GAAA;MACA;IACA;IACAkB,uBAAA,WAAAA,wBAAAvC,KAAA;MACA;MACA;MACA;MACA;MACA,SAAA+B,IAAA,SAAAf,OAAA;QACA,IAAAgB,GAAA,QAAAhB,OAAA,CAAAe,IAAA,EAAAN,KAAA;QACA,KAAAT,OAAA,CAAAe,IAAA,EAAAE,OAAA,GAAAjC,KAAA,CAAAkC,QAAA,CAAAF,GAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}