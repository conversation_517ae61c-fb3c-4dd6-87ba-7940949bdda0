{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/tool/build/CodeTypeDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/tool/build/CodeTypeDialog.vue", "mtime": 1641277326000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["inheritAttrs", "props", "data", "formData", "fileName", "undefined", "type", "rules", "required", "message", "trigger", "typeOptions", "label", "value", "computed", "watch", "mounted", "methods", "onOpen", "showFileName", "concat", "Date", "onClose", "close", "e", "$emit", "handelConfirm", "_this", "$refs", "elForm", "validate", "valid", "_objectSpread"], "sources": ["src/views/tool/build/CodeTypeDialog.vue"], "sourcesContent": ["<template>\n  <div>\n    <el-dialog\n      v-bind=\"$attrs\"\n      width=\"500px\"\n      :close-on-click-modal=\"false\"\n      :modal-append-to-body=\"false\"\n      v-on=\"$listeners\"\n      @open=\"onOpen\"\n      @close=\"onClose\"\n    >\n      <el-row :gutter=\"15\">\n        <el-form\n          ref=\"elForm\"\n          :model=\"formData\"\n          :rules=\"rules\"\n          size=\"medium\"\n          label-width=\"100px\"\n        >\n          <el-col :span=\"24\">\n            <el-form-item label=\"生成类型\" prop=\"type\">\n              <el-radio-group v-model=\"formData.type\">\n                <el-radio-button\n                  v-for=\"(item, index) in typeOptions\"\n                  :key=\"index\"\n                  :label=\"item.value\"\n                  :disabled=\"item.disabled\"\n                >\n                  {{ item.label }}\n                </el-radio-button>\n              </el-radio-group>\n            </el-form-item>\n            <el-form-item v-if=\"showFileName\" label=\"文件名\" prop=\"fileName\">\n              <el-input v-model=\"formData.fileName\" placeholder=\"请输入文件名\" clearable />\n            </el-form-item>\n          </el-col>\n        </el-form>\n      </el-row>\n\n      <div slot=\"footer\">\n        <el-button @click=\"close\">\n          取消\n        </el-button>\n        <el-button type=\"primary\" @click=\"handelConfirm\">\n          确定\n        </el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n<script>\nexport default {\n  inheritAttrs: false,\n  props: ['showFileName'],\n  data() {\n    return {\n      formData: {\n        fileName: undefined,\n        type: 'file'\n      },\n      rules: {\n        fileName: [{\n          required: true,\n          message: '请输入文件名',\n          trigger: 'blur'\n        }],\n        type: [{\n          required: true,\n          message: '生成类型不能为空',\n          trigger: 'change'\n        }]\n      },\n      typeOptions: [{\n        label: '页面',\n        value: 'file'\n      }, {\n        label: '弹窗',\n        value: 'dialog'\n      }]\n    }\n  },\n  computed: {\n  },\n  watch: {},\n  mounted() {},\n  methods: {\n    onOpen() {\n      if (this.showFileName) {\n        this.formData.fileName = `${+new Date()}.vue`\n      }\n    },\n    onClose() {\n    },\n    close(e) {\n      this.$emit('update:visible', false)\n    },\n    handelConfirm() {\n      this.$refs.elForm.validate(valid => {\n        if (!valid) return\n        this.$emit('confirm', { ...this.formData })\n        this.close()\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmDA;EACAA,YAAA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;QACAC,QAAA,EAAAC,SAAA;QACAC,IAAA;MACA;MACAC,KAAA;QACAH,QAAA;UACAI,QAAA;UACAC,OAAA;UACAC,OAAA;QACA;QACAJ,IAAA;UACAE,QAAA;UACAC,OAAA;UACAC,OAAA;QACA;MACA;MACAC,WAAA;QACAC,KAAA;QACAC,KAAA;MACA;QACAD,KAAA;QACAC,KAAA;MACA;IACA;EACA;EACAC,QAAA,GACA;EACAC,KAAA;EACAC,OAAA,WAAAA,QAAA;EACAC,OAAA;IACAC,MAAA,WAAAA,OAAA;MACA,SAAAC,YAAA;QACA,KAAAhB,QAAA,CAAAC,QAAA,MAAAgB,MAAA,MAAAC,IAAA;MACA;IACA;IACAC,OAAA,WAAAA,QAAA,GACA;IACAC,KAAA,WAAAA,MAAAC,CAAA;MACA,KAAAC,KAAA;IACA;IACAC,aAAA,WAAAA,cAAA;MAAA,IAAAC,KAAA;MACA,KAAAC,KAAA,CAAAC,MAAA,CAAAC,QAAA,WAAAC,KAAA;QACA,KAAAA,KAAA;QACAJ,KAAA,CAAAF,KAAA,YAAAO,aAAA,KAAAL,KAAA,CAAAxB,QAAA;QACAwB,KAAA,CAAAJ,KAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}