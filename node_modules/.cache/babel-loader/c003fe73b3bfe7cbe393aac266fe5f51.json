{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/system/role/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/system/role/index.vue", "mtime": 1662389786000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGxpc3RSb2xlLCBnZXRSb2xlLCBkZWxSb2xlLCBhZGRSb2xlLCB1cGRhdGVSb2xlLCBleHBvcnRSb2xlLCBkYXRhU2NvcGUsIGNoYW5nZVJvbGVTdGF0dXMgfSBmcm9tICJAL2FwaS9zeXN0ZW0vcm9sZSI7CmltcG9ydCB7IHRyZWVzZWxlY3QgYXMgbWVudVRyZWVzZWxlY3QsIHJvbGVNZW51VHJlZXNlbGVjdCB9IGZyb20gIkAvYXBpL3N5c3RlbS9tZW51IjsKaW1wb3J0IHsgdHJlZXNlbGVjdCBhcyBkZXB0VHJlZXNlbGVjdCwgcm9sZURlcHRUcmVlc2VsZWN0IH0gZnJvbSAiQC9hcGkvc3lzdGVtL2RlcHQiOwppbXBvcnQgeyBwcm92aW5jZUFuZENpdHlEYXRhIH0gZnJvbSAiZWxlbWVudC1jaGluYS1hcmVhLWRhdGEiOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogIlJvbGUiLAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICAvLyDpga7nvanlsYIKICAgICAgbG9hZGluZzogdHJ1ZSwKICAgICAgLy8g6YCJ5Lit5pWw57uECiAgICAgIGlkczogW10sCiAgICAgIC8vIOmdnuWNleS4quemgeeUqAogICAgICBzaW5nbGU6IHRydWUsCiAgICAgIC8vIOmdnuWkmuS4quemgeeUqAogICAgICBtdWx0aXBsZTogdHJ1ZSwKICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2CiAgICAgIHNob3dTZWFyY2g6IHRydWUsCiAgICAgIC8vIOaAu+adoeaVsAogICAgICB0b3RhbDogMCwKICAgICAgLy8g6KeS6Imy6KGo5qC85pWw5o2uCiAgICAgIHJvbGVMaXN0OiBbXSwKICAgICAgLy8g5by55Ye65bGC5qCH6aKYCiAgICAgIHRpdGxlOiAiIiwKICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGCCiAgICAgIG9wZW46IGZhbHNlLAogICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYLvvIjmlbDmja7mnYPpmZDvvIkKICAgICAgb3BlbkRhdGFTY29wZTogZmFsc2UsCiAgICAgIG1lbnVFeHBhbmQ6IGZhbHNlLAogICAgICBtZW51Tm9kZUFsbDogZmFsc2UsCiAgICAgIGRlcHRFeHBhbmQ6IHRydWUsCiAgICAgIGRlcHROb2RlQWxsOiBmYWxzZSwKICAgICAgLy8g5pel5pyf6IyD5Zu0CiAgICAgIGRhdGVSYW5nZTogW10sCiAgICAgIC8vIOeKtuaAgeaVsOaNruWtl+WFuAogICAgICBzdGF0dXNPcHRpb25zOiBbXSwKICAgICAgLy8g5pWw5o2u6IyD5Zu06YCJ6aG5CiAgICAgIGRhdGFTY29wZU9wdGlvbnM6IFt7CiAgICAgICAgdmFsdWU6ICIxIiwKICAgICAgICBsYWJlbDogIuWFqOmDqOaVsOaNruadg+mZkCIKICAgICAgfSwgewogICAgICAgIHZhbHVlOiAiMiIsCiAgICAgICAgbGFiZWw6ICLoh6rlrprmlbDmja7mnYPpmZAiCiAgICAgIH0sCiAgICAgIC8vIHsKICAgICAgLy8gICB2YWx1ZTogIjMiLAogICAgICAvLyAgIGxhYmVsOiAi5pys6YOo6Zeo5pWw5o2u5p2D6ZmQIgogICAgICAvLyB9LAogICAgICAvLyB7CiAgICAgIC8vICAgdmFsdWU6ICI0IiwKICAgICAgLy8gICBsYWJlbDogIuacrOmDqOmXqOWPiuS7peS4i+aVsOaNruadg+mZkCIKICAgICAgLy8gfSwKICAgICAgewogICAgICAgIHZhbHVlOiAiNSIsCiAgICAgICAgbGFiZWw6ICLku4XmnKzkurrmlbDmja7mnYPpmZAiCiAgICAgIH0sIHsKICAgICAgICB2YWx1ZTogIjYiLAogICAgICAgIGxhYmVsOiAi6Ieq5a6a5LmJ5Yy65Z+f5pWw5o2u5p2D6ZmQIgogICAgICB9XSwKICAgICAgLy8g6I+c5Y2V5YiX6KGoCiAgICAgIG1lbnVPcHRpb25zOiBbXSwKICAgICAgLy8g6YOo6Zeo5YiX6KGoCiAgICAgIGRlcHRPcHRpb25zOiBbXSwKICAgICAgLy8g5p+l6K+i5Y+C5pWwCiAgICAgIHF1ZXJ5UGFyYW1zOiB7CiAgICAgICAgcGFnZU51bTogMSwKICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgcm9sZU5hbWU6IHVuZGVmaW5lZCwKICAgICAgICByb2xlS2V5OiB1bmRlZmluZWQsCiAgICAgICAgc3RhdHVzOiB1bmRlZmluZWQKICAgICAgfSwKICAgICAgLy8g6KGo5Y2V5Y+C5pWwCiAgICAgIGZvcm06IHt9LAogICAgICBkZWZhdWx0UHJvcHM6IHsKICAgICAgICBjaGlsZHJlbjogImNoaWxkcmVuIiwKICAgICAgICBsYWJlbDogImxhYmVsIgogICAgICB9LAogICAgICAvLyDooajljZXmoKHpqowKICAgICAgcnVsZXM6IHsKICAgICAgICByb2xlTmFtZTogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogIuinkuiJsuWQjeensOS4jeiDveS4uuepuiIsCiAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIKICAgICAgICB9XSwKICAgICAgICByb2xlS2V5OiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAi5p2D6ZmQ5a2X56ym5LiN6IO95Li656m6IiwKICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgIH1dLAogICAgICAgIHJvbGVTb3J0OiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAi6KeS6Imy6aG65bqP5LiN6IO95Li656m6IiwKICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgIH1dCiAgICAgIH0sCiAgICAgIHByb3ZpbmNlOiBwcm92aW5jZUFuZENpdHlEYXRhCiAgICB9OwogIH0sCiAgY3JlYXRlZDogZnVuY3Rpb24gY3JlYXRlZCgpIHsKICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICB0aGlzLmdldExpc3QoKTsKICAgIHRoaXMuZ2V0RGljdHMoInN5c19ub3JtYWxfZGlzYWJsZSIpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgIF90aGlzLnN0YXR1c09wdGlvbnMgPSByZXNwb25zZS5kYXRhOwogICAgfSk7CiAgfSwKICBtZXRob2RzOiB7CiAgICAvKiog5p+l6K+i6KeS6Imy5YiX6KGoICovZ2V0TGlzdDogZnVuY3Rpb24gZ2V0TGlzdCgpIHsKICAgICAgdmFyIF90aGlzMiA9IHRoaXM7CiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7CiAgICAgIGxpc3RSb2xlKHRoaXMuYWRkRGF0ZVJhbmdlKHRoaXMucXVlcnlQYXJhbXMsIHRoaXMuZGF0ZVJhbmdlKSkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICBfdGhpczIucm9sZUxpc3QgPSByZXNwb25zZS5yb3dzOwogICAgICAgIF90aGlzMi50b3RhbCA9IHJlc3BvbnNlLnRvdGFsOwogICAgICAgIF90aGlzMi5sb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDmn6Xor6Loj5zljZXmoJHnu5PmnoQgKi9nZXRNZW51VHJlZXNlbGVjdDogZnVuY3Rpb24gZ2V0TWVudVRyZWVzZWxlY3QoKSB7CiAgICAgIHZhciBfdGhpczMgPSB0aGlzOwogICAgICBtZW51VHJlZXNlbGVjdCgpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgX3RoaXMzLm1lbnVPcHRpb25zID0gcmVzcG9uc2UuZGF0YTsKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOafpeivoumDqOmXqOagkee7k+aehCAqL2dldERlcHRUcmVlc2VsZWN0OiBmdW5jdGlvbiBnZXREZXB0VHJlZXNlbGVjdCgpIHsKICAgICAgdmFyIF90aGlzNCA9IHRoaXM7CiAgICAgIGRlcHRUcmVlc2VsZWN0KCkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICBfdGhpczQuZGVwdE9wdGlvbnMgPSByZXNwb25zZS5kYXRhOwogICAgICB9KTsKICAgIH0sCiAgICAvLyDmiYDmnInoj5zljZXoioLngrnmlbDmja4KICAgIGdldE1lbnVBbGxDaGVja2VkS2V5czogZnVuY3Rpb24gZ2V0TWVudUFsbENoZWNrZWRLZXlzKCkgewogICAgICAvLyDnm67liY3ooqvpgInkuK3nmoToj5zljZXoioLngrkKICAgICAgdmFyIGNoZWNrZWRLZXlzID0gdGhpcy4kcmVmcy5tZW51LmdldENoZWNrZWRLZXlzKCk7CiAgICAgIC8vIOWNiumAieS4reeahOiPnOWNleiKgueCuQogICAgICB2YXIgaGFsZkNoZWNrZWRLZXlzID0gdGhpcy4kcmVmcy5tZW51LmdldEhhbGZDaGVja2VkS2V5cygpOwogICAgICBjaGVja2VkS2V5cy51bnNoaWZ0LmFwcGx5KGNoZWNrZWRLZXlzLCBoYWxmQ2hlY2tlZEtleXMpOwogICAgICByZXR1cm4gY2hlY2tlZEtleXM7CiAgICB9LAogICAgLy8g5omA5pyJ6YOo6Zeo6IqC54K55pWw5o2uCiAgICBnZXREZXB0QWxsQ2hlY2tlZEtleXM6IGZ1bmN0aW9uIGdldERlcHRBbGxDaGVja2VkS2V5cygpIHsKICAgICAgLy8g55uu5YmN6KKr6YCJ5Lit55qE6YOo6Zeo6IqC54K5CiAgICAgIHZhciBjaGVja2VkS2V5cyA9IHRoaXMuJHJlZnMuZGVwdC5nZXRDaGVja2VkS2V5cygpOwogICAgICAvLyDljYrpgInkuK3nmoTpg6jpl6joioLngrkKICAgICAgdmFyIGhhbGZDaGVja2VkS2V5cyA9IHRoaXMuJHJlZnMuZGVwdC5nZXRIYWxmQ2hlY2tlZEtleXMoKTsKICAgICAgY2hlY2tlZEtleXMudW5zaGlmdC5hcHBseShjaGVja2VkS2V5cywgaGFsZkNoZWNrZWRLZXlzKTsKICAgICAgcmV0dXJuIGNoZWNrZWRLZXlzOwogICAgfSwKICAgIC8qKiDmoLnmja7op5LoibJJROafpeivouiPnOWNleagkee7k+aehCAqL2dldFJvbGVNZW51VHJlZXNlbGVjdDogZnVuY3Rpb24gZ2V0Um9sZU1lbnVUcmVlc2VsZWN0KHJvbGVJZCkgewogICAgICB2YXIgX3RoaXM1ID0gdGhpczsKICAgICAgcmV0dXJuIHJvbGVNZW51VHJlZXNlbGVjdChyb2xlSWQpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgX3RoaXM1Lm1lbnVPcHRpb25zID0gcmVzcG9uc2UubWVudXM7CiAgICAgICAgcmV0dXJuIHJlc3BvbnNlOwogICAgICB9KTsKICAgIH0sCiAgICAvKiog5qC55o2u6KeS6ImySUTmn6Xor6Lpg6jpl6jmoJHnu5PmnoQgKi9nZXRSb2xlRGVwdFRyZWVzZWxlY3Q6IGZ1bmN0aW9uIGdldFJvbGVEZXB0VHJlZXNlbGVjdChyb2xlSWQpIHsKICAgICAgdmFyIF90aGlzNiA9IHRoaXM7CiAgICAgIHJldHVybiByb2xlRGVwdFRyZWVzZWxlY3Qocm9sZUlkKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgIF90aGlzNi5kZXB0T3B0aW9ucyA9IHJlc3BvbnNlLmRlcHRzOwogICAgICAgIHJldHVybiByZXNwb25zZTsKICAgICAgfSk7CiAgICB9LAogICAgLy8g6KeS6Imy54q25oCB5L+u5pS5CiAgICBoYW5kbGVTdGF0dXNDaGFuZ2U6IGZ1bmN0aW9uIGhhbmRsZVN0YXR1c0NoYW5nZShyb3cpIHsKICAgICAgdmFyIF90aGlzNyA9IHRoaXM7CiAgICAgIHZhciB0ZXh0ID0gcm93LnN0YXR1cyA9PT0gIjAiID8gIuWQr+eUqCIgOiAi5YGc55SoIjsKICAgICAgdGhpcy4kY29uZmlybSgn56Gu6K6k6KaBIicgKyB0ZXh0ICsgJyIiJyArIHJvdy5yb2xlTmFtZSArICci6KeS6Imy5ZCXPycsICLorablkYoiLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLAogICAgICAgIHR5cGU6ICJ3YXJuaW5nIgogICAgICB9KS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICByZXR1cm4gY2hhbmdlUm9sZVN0YXR1cyhyb3cucm9sZUlkLCByb3cuc3RhdHVzKTsKICAgICAgfSkudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgX3RoaXM3Lm1zZ1N1Y2Nlc3ModGV4dCArICLmiJDlip8iKTsKICAgICAgfSkuY2F0Y2goZnVuY3Rpb24gKCkgewogICAgICAgIHJvdy5zdGF0dXMgPSByb3cuc3RhdHVzID09PSAiMCIgPyAiMSIgOiAiMCI7CiAgICAgIH0pOwogICAgfSwKICAgIC8vIOWPlua2iOaMiemSrgogICAgY2FuY2VsOiBmdW5jdGlvbiBjYW5jZWwoKSB7CiAgICAgIHRoaXMub3BlbiA9IGZhbHNlOwogICAgICB0aGlzLnJlc2V0KCk7CiAgICB9LAogICAgLy8g5Y+W5raI5oyJ6ZKu77yI5pWw5o2u5p2D6ZmQ77yJCiAgICBjYW5jZWxEYXRhU2NvcGU6IGZ1bmN0aW9uIGNhbmNlbERhdGFTY29wZSgpIHsKICAgICAgdGhpcy5vcGVuRGF0YVNjb3BlID0gZmFsc2U7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgIH0sCiAgICAvLyDooajljZXph43nva4KICAgIHJlc2V0OiBmdW5jdGlvbiByZXNldCgpIHsKICAgICAgaWYgKHRoaXMuJHJlZnMubWVudSAhPSB1bmRlZmluZWQpIHsKICAgICAgICB0aGlzLiRyZWZzLm1lbnUuc2V0Q2hlY2tlZEtleXMoW10pOwogICAgICB9CiAgICAgIHRoaXMubWVudUV4cGFuZCA9IGZhbHNlLCB0aGlzLm1lbnVOb2RlQWxsID0gZmFsc2UsIHRoaXMuZGVwdEV4cGFuZCA9IHRydWUsIHRoaXMuZGVwdE5vZGVBbGwgPSBmYWxzZSwgdGhpcy5mb3JtID0gewogICAgICAgIHJvbGVJZDogdW5kZWZpbmVkLAogICAgICAgIHJvbGVOYW1lOiB1bmRlZmluZWQsCiAgICAgICAgcm9sZUtleTogdW5kZWZpbmVkLAogICAgICAgIHJvbGVTb3J0OiAwLAogICAgICAgIHN0YXR1czogIjAiLAogICAgICAgIG1lbnVJZHM6IFtdLAogICAgICAgIGRlcHRJZHM6IFtdLAogICAgICAgIG1lbnVDaGVja1N0cmljdGx5OiB0cnVlLAogICAgICAgIGRlcHRDaGVja1N0cmljdGx5OiB0cnVlLAogICAgICAgIHJlbWFyazogdW5kZWZpbmVkLAogICAgICAgIGFyZWE6IHVuZGVmaW5lZAogICAgICB9OwogICAgICB0aGlzLnJlc2V0Rm9ybSgiZm9ybSIpOwogICAgfSwKICAgIC8qKiDmkJzntKLmjInpkq7mk43kvZwgKi9oYW5kbGVRdWVyeTogZnVuY3Rpb24gaGFuZGxlUXVlcnkoKSB7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDE7CiAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgfSwKICAgIC8qKiDph43nva7mjInpkq7mk43kvZwgKi9yZXNldFF1ZXJ5OiBmdW5jdGlvbiByZXNldFF1ZXJ5KCkgewogICAgICB0aGlzLmRhdGVSYW5nZSA9IFtdOwogICAgICB0aGlzLnJlc2V0Rm9ybSgicXVlcnlGb3JtIik7CiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsKICAgIH0sCiAgICAvLyDlpJrpgInmoYbpgInkuK3mlbDmja4KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZTogZnVuY3Rpb24gaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgewogICAgICB0aGlzLmlkcyA9IHNlbGVjdGlvbi5tYXAoZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICByZXR1cm4gaXRlbS5yb2xlSWQ7CiAgICAgIH0pOwogICAgICB0aGlzLnNpbmdsZSA9IHNlbGVjdGlvbi5sZW5ndGggIT0gMTsKICAgICAgdGhpcy5tdWx0aXBsZSA9ICFzZWxlY3Rpb24ubGVuZ3RoOwogICAgfSwKICAgIC8vIOagkeadg+mZkO+8iOWxleW8gC/mipjlj6DvvIkKICAgIGhhbmRsZUNoZWNrZWRUcmVlRXhwYW5kOiBmdW5jdGlvbiBoYW5kbGVDaGVja2VkVHJlZUV4cGFuZCh2YWx1ZSwgdHlwZSkgewogICAgICBpZiAodHlwZSA9PSAnbWVudScpIHsKICAgICAgICB2YXIgdHJlZUxpc3QgPSB0aGlzLm1lbnVPcHRpb25zOwogICAgICAgIGZvciAodmFyIGkgPSAwOyBpIDwgdHJlZUxpc3QubGVuZ3RoOyBpKyspIHsKICAgICAgICAgIHRoaXMuJHJlZnMubWVudS5zdG9yZS5ub2Rlc01hcFt0cmVlTGlzdFtpXS5pZF0uZXhwYW5kZWQgPSB2YWx1ZTsKICAgICAgICB9CiAgICAgIH0gZWxzZSBpZiAodHlwZSA9PSAnZGVwdCcpIHsKICAgICAgICB2YXIgX3RyZWVMaXN0ID0gdGhpcy5kZXB0T3B0aW9uczsKICAgICAgICBmb3IgKHZhciBfaSA9IDA7IF9pIDwgX3RyZWVMaXN0Lmxlbmd0aDsgX2krKykgewogICAgICAgICAgdGhpcy4kcmVmcy5kZXB0LnN0b3JlLm5vZGVzTWFwW190cmVlTGlzdFtfaV0uaWRdLmV4cGFuZGVkID0gdmFsdWU7CiAgICAgICAgfQogICAgICB9CiAgICB9LAogICAgLy8g5qCR5p2D6ZmQ77yI5YWo6YCJL+WFqOS4jemAie+8iQogICAgaGFuZGxlQ2hlY2tlZFRyZWVOb2RlQWxsOiBmdW5jdGlvbiBoYW5kbGVDaGVja2VkVHJlZU5vZGVBbGwodmFsdWUsIHR5cGUpIHsKICAgICAgaWYgKHR5cGUgPT0gJ21lbnUnKSB7CiAgICAgICAgdGhpcy4kcmVmcy5tZW51LnNldENoZWNrZWROb2Rlcyh2YWx1ZSA/IHRoaXMubWVudU9wdGlvbnMgOiBbXSk7CiAgICAgIH0gZWxzZSBpZiAodHlwZSA9PSAnZGVwdCcpIHsKICAgICAgICB0aGlzLiRyZWZzLmRlcHQuc2V0Q2hlY2tlZE5vZGVzKHZhbHVlID8gdGhpcy5kZXB0T3B0aW9ucyA6IFtdKTsKICAgICAgfQogICAgfSwKICAgIC8vIOagkeadg+mZkO+8iOeItuWtkOiBlOWKqO+8iQogICAgaGFuZGxlQ2hlY2tlZFRyZWVDb25uZWN0OiBmdW5jdGlvbiBoYW5kbGVDaGVja2VkVHJlZUNvbm5lY3QodmFsdWUsIHR5cGUpIHsKICAgICAgaWYgKHR5cGUgPT0gJ21lbnUnKSB7CiAgICAgICAgdGhpcy5mb3JtLm1lbnVDaGVja1N0cmljdGx5ID0gdmFsdWUgPyB0cnVlIDogZmFsc2U7CiAgICAgIH0gZWxzZSBpZiAodHlwZSA9PSAnZGVwdCcpIHsKICAgICAgICB0aGlzLmZvcm0uZGVwdENoZWNrU3RyaWN0bHkgPSB2YWx1ZSA/IHRydWUgOiBmYWxzZTsKICAgICAgfQogICAgfSwKICAgIC8qKiDmlrDlop7mjInpkq7mk43kvZwgKi9oYW5kbGVBZGQ6IGZ1bmN0aW9uIGhhbmRsZUFkZCgpIHsKICAgICAgdGhpcy5yZXNldCgpOwogICAgICB0aGlzLmdldE1lbnVUcmVlc2VsZWN0KCk7CiAgICAgIHRoaXMub3BlbiA9IHRydWU7CiAgICAgIHRoaXMudGl0bGUgPSAi5re75Yqg6KeS6ImyIjsKICAgIH0sCiAgICAvKiog5L+u5pS55oyJ6ZKu5pON5L2cICovaGFuZGxlVXBkYXRlOiBmdW5jdGlvbiBoYW5kbGVVcGRhdGUocm93KSB7CiAgICAgIHZhciBfdGhpczggPSB0aGlzOwogICAgICB0aGlzLnJlc2V0KCk7CiAgICAgIHZhciByb2xlSWQgPSByb3cucm9sZUlkIHx8IHRoaXMuaWRzOwogICAgICB2YXIgcm9sZU1lbnUgPSB0aGlzLmdldFJvbGVNZW51VHJlZXNlbGVjdChyb2xlSWQpOwogICAgICBnZXRSb2xlKHJvbGVJZCkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICBfdGhpczguZm9ybSA9IHJlc3BvbnNlLmRhdGE7CiAgICAgICAgX3RoaXM4Lm9wZW4gPSB0cnVlOwogICAgICAgIF90aGlzOC4kbmV4dFRpY2soZnVuY3Rpb24gKCkgewogICAgICAgICAgcm9sZU1lbnUudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgICAgIF90aGlzOC4kcmVmcy5tZW51LnNldENoZWNrZWRLZXlzKHJlcy5jaGVja2VkS2V5cyk7CiAgICAgICAgICB9KTsKICAgICAgICB9KTsKICAgICAgICBfdGhpczgudGl0bGUgPSAi5L+u5pS56KeS6ImyIjsKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOWIhumFjeaVsOaNruadg+mZkOaTjeS9nCAqL2hhbmRsZURhdGFTY29wZTogZnVuY3Rpb24gaGFuZGxlRGF0YVNjb3BlKHJvdykgewogICAgICB2YXIgX3RoaXM5ID0gdGhpczsKICAgICAgdGhpcy5yZXNldCgpOwogICAgICB2YXIgcm9sZURlcHRUcmVlc2VsZWN0ID0gdGhpcy5nZXRSb2xlRGVwdFRyZWVzZWxlY3Qocm93LnJvbGVJZCk7CiAgICAgIGdldFJvbGUocm93LnJvbGVJZCkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICBfdGhpczkuZm9ybSA9IHJlc3BvbnNlLmRhdGE7CiAgICAgICAgX3RoaXM5Lm9wZW5EYXRhU2NvcGUgPSB0cnVlOwogICAgICAgIF90aGlzOS4kbmV4dFRpY2soZnVuY3Rpb24gKCkgewogICAgICAgICAgcm9sZURlcHRUcmVlc2VsZWN0LnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgICAgICBfdGhpczkuJHJlZnMuZGVwdC5zZXRDaGVja2VkS2V5cyhyZXMuY2hlY2tlZEtleXMpOwogICAgICAgICAgfSk7CiAgICAgICAgfSk7CiAgICAgICAgX3RoaXM5LnRpdGxlID0gIuWIhumFjeaVsOaNruadg+mZkCI7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDmj5DkuqTmjInpkq4gKi8KICAgIHN1Ym1pdEZvcm06IGZ1bmN0aW9uIHN1Ym1pdEZvcm0oKSB7CiAgICAgIHZhciBfdGhpczEwID0gdGhpczsKICAgICAgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKGZ1bmN0aW9uICh2YWxpZCkgewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgaWYgKF90aGlzMTAuZm9ybS5yb2xlSWQgIT0gdW5kZWZpbmVkKSB7CiAgICAgICAgICAgIF90aGlzMTAuZm9ybS5tZW51SWRzID0gX3RoaXMxMC5nZXRNZW51QWxsQ2hlY2tlZEtleXMoKTsKICAgICAgICAgICAgdXBkYXRlUm9sZShfdGhpczEwLmZvcm0pLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgICAgICAgX3RoaXMxMC5tc2dTdWNjZXNzKCLkv67mlLnmiJDlip8iKTsKICAgICAgICAgICAgICBfdGhpczEwLm9wZW4gPSBmYWxzZTsKICAgICAgICAgICAgICBfdGhpczEwLmdldExpc3QoKTsKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICBfdGhpczEwLmZvcm0ubWVudUlkcyA9IF90aGlzMTAuZ2V0TWVudUFsbENoZWNrZWRLZXlzKCk7CiAgICAgICAgICAgIGFkZFJvbGUoX3RoaXMxMC5mb3JtKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgICAgICAgIF90aGlzMTAubXNnU3VjY2Vzcygi5paw5aKe5oiQ5YqfIik7CiAgICAgICAgICAgICAgX3RoaXMxMC5vcGVuID0gZmFsc2U7CiAgICAgICAgICAgICAgX3RoaXMxMC5nZXRMaXN0KCk7CiAgICAgICAgICAgIH0pOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLyoqIOaPkOS6pOaMiemSru+8iOaVsOaNruadg+mZkO+8iSAqLwogICAgc3VibWl0RGF0YVNjb3BlOiBmdW5jdGlvbiBzdWJtaXREYXRhU2NvcGUoKSB7CiAgICAgIHZhciBfdGhpczExID0gdGhpczsKICAgICAgLy9jb25zb2xlLmxvZyh0aGlzLmZvcm0pOwogICAgICBpZiAodGhpcy5mb3JtLnJvbGVJZCAhPSB1bmRlZmluZWQpIHsKICAgICAgICB0aGlzLmZvcm0uZGVwdElkcyA9IHRoaXMuZ2V0RGVwdEFsbENoZWNrZWRLZXlzKCk7CiAgICAgICAgZGF0YVNjb3BlKHRoaXMuZm9ybSkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICAgIF90aGlzMTEubXNnU3VjY2Vzcygi5L+u5pS55oiQ5YqfIik7CiAgICAgICAgICBfdGhpczExLm9wZW5EYXRhU2NvcGUgPSBmYWxzZTsKICAgICAgICAgIF90aGlzMTEuZ2V0TGlzdCgpOwogICAgICAgIH0pOwogICAgICB9CiAgICB9LAogICAgLyoqIOWIoOmZpOaMiemSruaTjeS9nCAqL2hhbmRsZURlbGV0ZTogZnVuY3Rpb24gaGFuZGxlRGVsZXRlKHJvdykgewogICAgICB2YXIgX3RoaXMxMiA9IHRoaXM7CiAgICAgIHZhciByb2xlSWRzID0gcm93LnJvbGVJZCB8fCB0aGlzLmlkczsKICAgICAgdGhpcy4kY29uZmlybSgn5piv5ZCm56Gu6K6k5Yig6Zmk6KeS6Imy57yW5Y+35Li6IicgKyByb2xlSWRzICsgJyLnmoTmlbDmja7pobk/JywgIuitpuWRiiIsIHsKICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogIuehruWumiIsCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogIuWPlua2iCIsCiAgICAgICAgdHlwZTogIndhcm5pbmciCiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgIHJldHVybiBkZWxSb2xlKHJvbGVJZHMpOwogICAgICB9KS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICBfdGhpczEyLmdldExpc3QoKTsKICAgICAgICBfdGhpczEyLm1zZ1N1Y2Nlc3MoIuWIoOmZpOaIkOWKnyIpOwogICAgICB9KTsKICAgIH0sCiAgICAvKiog5a+85Ye65oyJ6ZKu5pON5L2cICovaGFuZGxlRXhwb3J0OiBmdW5jdGlvbiBoYW5kbGVFeHBvcnQoKSB7CiAgICAgIHZhciBfdGhpczEzID0gdGhpczsKICAgICAgdmFyIHF1ZXJ5UGFyYW1zID0gdGhpcy5xdWVyeVBhcmFtczsKICAgICAgdGhpcy4kY29uZmlybSgn5piv5ZCm56Gu6K6k5a+85Ye65omA5pyJ6KeS6Imy5pWw5o2u6aG5PycsICLorablkYoiLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLAogICAgICAgIHR5cGU6ICJ3YXJuaW5nIgogICAgICB9KS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICByZXR1cm4gZXhwb3J0Um9sZShxdWVyeVBhcmFtcyk7CiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgX3RoaXMxMy5kb3dubG9hZChyZXNwb25zZS5tc2cpOwogICAgICB9KTsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["listRole", "getRole", "delRole", "addRole", "updateRole", "exportRole", "dataScope", "changeRoleStatus", "treeselect", "menuTreeselect", "roleMenuTreeselect", "deptTreeselect", "roleDeptTreeselect", "provinceAndCityData", "name", "data", "loading", "ids", "single", "multiple", "showSearch", "total", "roleList", "title", "open", "openDataScope", "menuExpand", "menuNodeAll", "deptExpand", "deptNodeAll", "date<PERSON><PERSON><PERSON>", "statusOptions", "dataScopeOptions", "value", "label", "menuOptions", "deptOptions", "queryParams", "pageNum", "pageSize", "<PERSON><PERSON><PERSON>", "undefined", "<PERSON><PERSON><PERSON>", "status", "form", "defaultProps", "children", "rules", "required", "message", "trigger", "roleSort", "province", "created", "_this", "getList", "getDicts", "then", "response", "methods", "_this2", "addDateRange", "rows", "getMenuTreeselect", "_this3", "getDeptTreeselect", "_this4", "getMenuAllCheckedKeys", "checked<PERSON>eys", "$refs", "menu", "getChe<PERSON><PERSON>eys", "halfC<PERSON>cked<PERSON>eys", "getHalfCheckedKeys", "unshift", "apply", "getDeptAllCheckedKeys", "dept", "getRoleMenuTreeselect", "roleId", "_this5", "menus", "getRoleDeptTreeselect", "_this6", "depts", "handleStatusChange", "row", "_this7", "text", "$confirm", "confirmButtonText", "cancelButtonText", "type", "msgSuccess", "catch", "cancel", "reset", "cancelDataScope", "set<PERSON><PERSON><PERSON><PERSON>eys", "menuIds", "deptIds", "menu<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "remark", "area", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "map", "item", "length", "handleCheckedTreeExpand", "treeList", "i", "store", "nodesMap", "id", "expanded", "handleCheckedTreeNodeAll", "setCheckedNodes", "handleCheckedTreeConnect", "handleAdd", "handleUpdate", "_this8", "roleMenu", "$nextTick", "res", "handleDataScope", "_this9", "submitForm", "_this10", "validate", "valid", "submitDataScope", "_this11", "handleDelete", "_this12", "roleIds", "handleExport", "_this13", "download", "msg"], "sources": ["src/views/system/role/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" v-show=\"showSearch\" :inline=\"true\">\n      <el-form-item label=\"角色名称\" prop=\"roleName\">\n        <el-input\n          v-model=\"queryParams.roleName\"\n          placeholder=\"请输入角色名称\"\n          clearable\n          size=\"small\"\n          style=\"width: 240px\"\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"权限字符\" prop=\"roleKey\">\n        <el-input\n          v-model=\"queryParams.roleKey\"\n          placeholder=\"请输入权限字符\"\n          clearable\n          size=\"small\"\n          style=\"width: 240px\"\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"状态\" prop=\"status\">\n        <el-select\n          v-model=\"queryParams.status\"\n          placeholder=\"角色状态\"\n          clearable\n          size=\"small\"\n          style=\"width: 240px\"\n        >\n          <el-option\n            v-for=\"dict in statusOptions\"\n            :key=\"dict.dictValue\"\n            :label=\"dict.dictLabel\"\n            :value=\"dict.dictValue\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"创建时间\">\n        <el-date-picker\n          v-model=\"dateRange\"\n          size=\"small\"\n          style=\"width: 240px\"\n          value-format=\"yyyy-MM-dd\"\n          type=\"daterange\"\n          range-separator=\"-\"\n          start-placeholder=\"开始日期\"\n          end-placeholder=\"结束日期\"\n        ></el-date-picker>\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"primary\"\n          plain\n          icon=\"el-icon-plus\"\n          size=\"mini\"\n          @click=\"handleAdd\"\n          v-hasPermi=\"['system:role:add']\"\n        >新增</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"success\"\n          plain\n          icon=\"el-icon-edit\"\n          size=\"mini\"\n          :disabled=\"single\"\n          @click=\"handleUpdate\"\n          v-hasPermi=\"['system:role:edit']\"\n        >修改</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"danger\"\n          plain\n          icon=\"el-icon-delete\"\n          size=\"mini\"\n          :disabled=\"multiple\"\n          @click=\"handleDelete\"\n          v-hasPermi=\"['system:role:remove']\"\n        >删除</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"warning\"\n          plain\n          icon=\"el-icon-download\"\n          size=\"mini\"\n          @click=\"handleExport\"\n          v-hasPermi=\"['system:role:export']\"\n        >导出</el-button>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <el-table v-loading=\"loading\" :data=\"roleList\" @selection-change=\"handleSelectionChange\">\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column label=\"角色编号\" prop=\"roleId\" width=\"120\" />\n      <el-table-column label=\"角色名称\" prop=\"roleName\" :show-overflow-tooltip=\"true\" width=\"150\" />\n      <el-table-column label=\"权限字符\" prop=\"roleKey\" :show-overflow-tooltip=\"true\" width=\"150\" />\n      <el-table-column label=\"显示顺序\" prop=\"roleSort\" width=\"100\" />\n      <el-table-column label=\"状态\" align=\"center\" width=\"100\">\n        <template slot-scope=\"scope\">\n          <el-switch\n            v-model=\"scope.row.status\"\n            active-value=\"0\"\n            inactive-value=\"1\"\n            @change=\"handleStatusChange(scope.row)\"\n          ></el-switch>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"180\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.createTime) }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-edit\"\n            @click=\"handleUpdate(scope.row)\"\n            v-hasPermi=\"['system:role:edit']\"\n          >修改</el-button>\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-circle-check\"\n            @click=\"handleDataScope(scope.row)\"\n            v-hasPermi=\"['system:role:edit']\"\n          >数据权限</el-button>\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-delete\"\n            @click=\"handleDelete(scope.row)\"\n            v-hasPermi=\"['system:role:remove']\"\n          >删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 添加或修改角色配置对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"500px\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\n        <el-form-item label=\"角色名称\" prop=\"roleName\">\n          <el-input v-model=\"form.roleName\" placeholder=\"请输入角色名称\" />\n        </el-form-item>\n        <el-form-item label=\"权限字符\" prop=\"roleKey\">\n          <el-input v-model=\"form.roleKey\" placeholder=\"请输入权限字符\" />\n        </el-form-item>\n        <el-form-item label=\"角色顺序\" prop=\"roleSort\">\n          <el-input-number v-model=\"form.roleSort\" controls-position=\"right\" :min=\"0\" />\n        </el-form-item>\n        <el-form-item label=\"状态\">\n          <el-radio-group v-model=\"form.status\">\n            <el-radio\n              v-for=\"dict in statusOptions\"\n              :key=\"dict.dictValue\"\n              :label=\"dict.dictValue\"\n            >{{dict.dictLabel}}</el-radio>\n          </el-radio-group>\n        </el-form-item>\n        <el-form-item label=\"菜单权限\">\n          <el-checkbox v-model=\"menuExpand\" @change=\"handleCheckedTreeExpand($event, 'menu')\">展开/折叠</el-checkbox>\n          <el-checkbox v-model=\"menuNodeAll\" @change=\"handleCheckedTreeNodeAll($event, 'menu')\">全选/全不选</el-checkbox>\n          <el-checkbox v-model=\"form.menuCheckStrictly\" @change=\"handleCheckedTreeConnect($event, 'menu')\">父子联动</el-checkbox>\n          <el-tree\n            class=\"tree-border\"\n            :data=\"menuOptions\"\n            show-checkbox\n            ref=\"menu\"\n            node-key=\"id\"\n            :check-strictly=\"!form.menuCheckStrictly\"\n            empty-text=\"加载中，请稍后\"\n            :props=\"defaultProps\"\n          ></el-tree>\n        </el-form-item>\n        <el-form-item label=\"备注\">\n          <el-input v-model=\"form.remark\" type=\"textarea\" placeholder=\"请输入内容\"></el-input>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 分配角色数据权限对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"openDataScope\" width=\"500px\" append-to-body>\n      <el-form :model=\"form\" label-width=\"100px\">\n        <el-form-item label=\"角色名称\">\n          <el-input v-model=\"form.roleName\" :disabled=\"true\" />\n        </el-form-item>\n        <el-form-item label=\"权限字符\">\n          <el-input v-model=\"form.roleKey\" :disabled=\"true\" />\n        </el-form-item>\n        <el-form-item label=\"权限范围\">\n          <el-select v-model=\"form.dataScope\">\n            <el-option\n              v-for=\"item in dataScopeOptions\"\n              :key=\"item.value\"\n              :label=\"item.label\"\n              :value=\"item.value\"\n            ></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"数据权限\" v-show=\"form.dataScope == 2\">\n          <el-checkbox v-model=\"deptExpand\" @change=\"handleCheckedTreeExpand($event, 'dept')\">展开/折叠</el-checkbox>\n          <el-checkbox v-model=\"deptNodeAll\" @change=\"handleCheckedTreeNodeAll($event, 'dept')\">全选/全不选</el-checkbox>\n          <el-checkbox v-model=\"form.deptCheckStrictly\" @change=\"handleCheckedTreeConnect($event, 'dept')\">父子联动</el-checkbox>\n          <el-tree\n            class=\"tree-border\"\n            :data=\"deptOptions\"\n            show-checkbox\n            default-expand-all\n            ref=\"dept\"\n            node-key=\"id\"\n            :check-strictly=\"!form.deptCheckStrictly\"\n            empty-text=\"加载中，请稍后\"\n            :props=\"defaultProps\"\n          ></el-tree>\n        </el-form-item>\n        <el-form-item label=\"区域数据权限\" v-show=\"form.dataScope == 6\">\n          <el-select v-model=\"form.area\" multiple placeholder=\"请选择\">\n            <el-option\n              v-for=\"item in province\"\n              :key=\"item.value\"\n              :label=\"item.label\"\n              :value=\"item.label\">\n            </el-option>\n          </el-select>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitDataScope\">确 定</el-button>\n        <el-button @click=\"cancelDataScope\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { listRole, getRole, delRole, addRole, updateRole, exportRole, dataScope, changeRoleStatus } from \"@/api/system/role\";\nimport { treeselect as menuTreeselect, roleMenuTreeselect } from \"@/api/system/menu\";\nimport { treeselect as deptTreeselect, roleDeptTreeselect } from \"@/api/system/dept\";\nimport { provinceAndCityData } from \"element-china-area-data\";\nexport default {\n  name: \"Role\",\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 角色表格数据\n      roleList: [],\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      open: false,\n      // 是否显示弹出层（数据权限）\n      openDataScope: false,\n      menuExpand: false,\n      menuNodeAll: false,\n      deptExpand: true,\n      deptNodeAll: false,\n      // 日期范围\n      dateRange: [],\n      // 状态数据字典\n      statusOptions: [],\n      // 数据范围选项\n      dataScopeOptions: [\n        {\n          value: \"1\",\n          label: \"全部数据权限\"\n        },\n        {\n          value: \"2\",\n          label: \"自定数据权限\"\n        },\n        // {\n        //   value: \"3\",\n        //   label: \"本部门数据权限\"\n        // },\n        // {\n        //   value: \"4\",\n        //   label: \"本部门及以下数据权限\"\n        // },\n        {\n          value: \"5\",\n          label: \"仅本人数据权限\"\n        },\n        {\n          value: \"6\",\n          label: \"自定义区域数据权限\"\n        }\n      ],\n      // 菜单列表\n      menuOptions: [],\n      // 部门列表\n      deptOptions: [],\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        roleName: undefined,\n        roleKey: undefined,\n        status: undefined\n      },\n      // 表单参数\n      form: {},\n      defaultProps: {\n        children: \"children\",\n        label: \"label\"\n      },\n      // 表单校验\n      rules: {\n        roleName: [\n          { required: true, message: \"角色名称不能为空\", trigger: \"blur\" }\n        ],\n        roleKey: [\n          { required: true, message: \"权限字符不能为空\", trigger: \"blur\" }\n        ],\n        roleSort: [\n          { required: true, message: \"角色顺序不能为空\", trigger: \"blur\" }\n        ]\n      },\n      province: provinceAndCityData\n    };\n  },\n  created() {\n    this.getList();\n    this.getDicts(\"sys_normal_disable\").then(response => {\n      this.statusOptions = response.data;\n    });\n  },\n  methods: {\n    /** 查询角色列表 */\n    getList() {\n      this.loading = true;\n      listRole(this.addDateRange(this.queryParams, this.dateRange)).then(\n        response => {\n          this.roleList = response.rows;\n          this.total = response.total;\n          this.loading = false;\n        }\n      );\n    },\n    /** 查询菜单树结构 */\n    getMenuTreeselect() {\n      menuTreeselect().then(response => {\n        this.menuOptions = response.data;\n      });\n    },\n    /** 查询部门树结构 */\n    getDeptTreeselect() {\n      deptTreeselect().then(response => {\n        this.deptOptions = response.data;\n      });\n    },\n    // 所有菜单节点数据\n    getMenuAllCheckedKeys() {\n      // 目前被选中的菜单节点\n      let checkedKeys = this.$refs.menu.getCheckedKeys();\n      // 半选中的菜单节点\n      let halfCheckedKeys = this.$refs.menu.getHalfCheckedKeys();\n      checkedKeys.unshift.apply(checkedKeys, halfCheckedKeys);\n      return checkedKeys;\n    },\n    // 所有部门节点数据\n    getDeptAllCheckedKeys() {\n      // 目前被选中的部门节点\n      let checkedKeys = this.$refs.dept.getCheckedKeys();\n      // 半选中的部门节点\n      let halfCheckedKeys = this.$refs.dept.getHalfCheckedKeys();\n      checkedKeys.unshift.apply(checkedKeys, halfCheckedKeys);\n      return checkedKeys;\n    },\n    /** 根据角色ID查询菜单树结构 */\n    getRoleMenuTreeselect(roleId) {\n      return roleMenuTreeselect(roleId).then(response => {\n        this.menuOptions = response.menus;\n        return response;\n      });\n    },\n    /** 根据角色ID查询部门树结构 */\n    getRoleDeptTreeselect(roleId) {\n      return roleDeptTreeselect(roleId).then(response => {\n        this.deptOptions = response.depts;\n        return response;\n      });\n    },\n    // 角色状态修改\n    handleStatusChange(row) {\n      let text = row.status === \"0\" ? \"启用\" : \"停用\";\n      this.$confirm('确认要\"' + text + '\"\"' + row.roleName + '\"角色吗?', \"警告\", {\n          confirmButtonText: \"确定\",\n          cancelButtonText: \"取消\",\n          type: \"warning\"\n        }).then(function() {\n          return changeRoleStatus(row.roleId, row.status);\n        }).then(() => {\n          this.msgSuccess(text + \"成功\");\n        }).catch(function() {\n          row.status = row.status === \"0\" ? \"1\" : \"0\";\n        });\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 取消按钮（数据权限）\n    cancelDataScope() {\n      this.openDataScope = false;\n      this.reset();\n    },\n    // 表单重置\n    reset() {\n      if (this.$refs.menu != undefined) {\n        this.$refs.menu.setCheckedKeys([]);\n      }\n      this.menuExpand = false,\n      this.menuNodeAll = false,\n      this.deptExpand = true,\n      this.deptNodeAll = false,\n      this.form = {\n        roleId: undefined,\n        roleName: undefined,\n        roleKey: undefined,\n        roleSort: 0,\n        status: \"0\",\n        menuIds: [],\n        deptIds: [],\n        menuCheckStrictly: true,\n        deptCheckStrictly: true,\n        remark: undefined,\n        area: undefined,\n      };\n      this.resetForm(\"form\");\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.dateRange = [];\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.roleId)\n      this.single = selection.length!=1\n      this.multiple = !selection.length\n    },\n    // 树权限（展开/折叠）\n    handleCheckedTreeExpand(value, type) {\n      if (type == 'menu') {\n        let treeList = this.menuOptions;\n        for (let i = 0; i < treeList.length; i++) {\n          this.$refs.menu.store.nodesMap[treeList[i].id].expanded = value;\n        }\n      } else if (type == 'dept') {\n        let treeList = this.deptOptions;\n        for (let i = 0; i < treeList.length; i++) {\n          this.$refs.dept.store.nodesMap[treeList[i].id].expanded = value;\n        }\n      }\n    },\n    // 树权限（全选/全不选）\n    handleCheckedTreeNodeAll(value, type) {\n      if (type == 'menu') {\n        this.$refs.menu.setCheckedNodes(value ? this.menuOptions: []);\n      } else if (type == 'dept') {\n        this.$refs.dept.setCheckedNodes(value ? this.deptOptions: []);\n      }\n    },\n    // 树权限（父子联动）\n    handleCheckedTreeConnect(value, type) {\n      if (type == 'menu') {\n        this.form.menuCheckStrictly = value ? true: false;\n      } else if (type == 'dept') {\n        this.form.deptCheckStrictly = value ? true: false;\n      }\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.reset();\n      this.getMenuTreeselect();\n      this.open = true;\n      this.title = \"添加角色\";\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset();\n      const roleId = row.roleId || this.ids\n      const roleMenu = this.getRoleMenuTreeselect(roleId);\n      getRole(roleId).then(response => {\n        this.form = response.data;\n        this.open = true;\n        this.$nextTick(() => {\n          roleMenu.then(res => {\n            this.$refs.menu.setCheckedKeys(res.checkedKeys);\n          });\n        });\n        this.title = \"修改角色\";\n      });\n    },\n    /** 分配数据权限操作 */\n    handleDataScope(row) {\n      this.reset();\n      const roleDeptTreeselect = this.getRoleDeptTreeselect(row.roleId);\n      getRole(row.roleId).then(response => {\n        this.form = response.data;\n        this.openDataScope = true;\n        this.$nextTick(() => {\n          roleDeptTreeselect.then(res => {\n            this.$refs.dept.setCheckedKeys(res.checkedKeys);\n          });\n        });\n        this.title = \"分配数据权限\";\n      });\n    },\n    /** 提交按钮 */\n    submitForm: function() {\n      \n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          if (this.form.roleId != undefined) {\n            this.form.menuIds = this.getMenuAllCheckedKeys();\n            updateRole(this.form).then(response => {\n              this.msgSuccess(\"修改成功\");\n              this.open = false;\n              this.getList();\n            });\n          } else {\n            this.form.menuIds = this.getMenuAllCheckedKeys();\n            addRole(this.form).then(response => {\n              this.msgSuccess(\"新增成功\");\n              this.open = false;\n              this.getList();\n            });\n          }\n        }\n      });\n    },\n    /** 提交按钮（数据权限） */\n    submitDataScope: function() {\n      //console.log(this.form);\n      if (this.form.roleId != undefined) {\n        this.form.deptIds = this.getDeptAllCheckedKeys();\n        dataScope(this.form).then(response => {\n          this.msgSuccess(\"修改成功\");\n          this.openDataScope = false;\n          this.getList();\n        });\n      }\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const roleIds = row.roleId || this.ids;\n      this.$confirm('是否确认删除角色编号为\"' + roleIds + '\"的数据项?', \"警告\", {\n          confirmButtonText: \"确定\",\n          cancelButtonText: \"取消\",\n          type: \"warning\"\n        }).then(function() {\n          return delRole(roleIds);\n        }).then(() => {\n          this.getList();\n          this.msgSuccess(\"删除成功\");\n        })\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      const queryParams = this.queryParams;\n      this.$confirm('是否确认导出所有角色数据项?', \"警告\", {\n          confirmButtonText: \"确定\",\n          cancelButtonText: \"取消\",\n          type: \"warning\"\n        }).then(function() {\n          return exportRole(queryParams);\n        }).then(response => {\n          this.download(response.msg);\n        })\n    }\n  }\n};\n</script>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoQA,SAAAA,QAAA,EAAAC,OAAA,EAAAC,OAAA,EAAAC,OAAA,EAAAC,UAAA,EAAAC,UAAA,EAAAC,SAAA,EAAAC,gBAAA;AACA,SAAAC,UAAA,IAAAC,cAAA,EAAAC,kBAAA;AACA,SAAAF,UAAA,IAAAG,cAAA,EAAAC,kBAAA;AACA,SAAAC,mBAAA;AACA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,QAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,aAAA;MACAC,UAAA;MACAC,WAAA;MACAC,UAAA;MACAC,WAAA;MACA;MACAC,SAAA;MACA;MACAC,aAAA;MACA;MACAC,gBAAA,GACA;QACAC,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,EACA;MACA;MACAC,WAAA;MACA;MACAC,WAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,QAAA,EAAAC,SAAA;QACAC,OAAA,EAAAD,SAAA;QACAE,MAAA,EAAAF;MACA;MACA;MACAG,IAAA;MACAC,YAAA;QACAC,QAAA;QACAZ,KAAA;MACA;MACA;MACAa,KAAA;QACAP,QAAA,GACA;UAAAQ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAR,OAAA,GACA;UAAAM,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAC,QAAA,GACA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAE,QAAA,EAAAvC;IACA;EACA;EACAwC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA,KAAAC,OAAA;IACA,KAAAC,QAAA,uBAAAC,IAAA,WAAAC,QAAA;MACAJ,KAAA,CAAAvB,aAAA,GAAA2B,QAAA,CAAA3C,IAAA;IACA;EACA;EACA4C,OAAA;IACA,aACAJ,OAAA,WAAAA,QAAA;MAAA,IAAAK,MAAA;MACA,KAAA5C,OAAA;MACAhB,QAAA,MAAA6D,YAAA,MAAAxB,WAAA,OAAAP,SAAA,GAAA2B,IAAA,CACA,UAAAC,QAAA;QACAE,MAAA,CAAAtC,QAAA,GAAAoC,QAAA,CAAAI,IAAA;QACAF,MAAA,CAAAvC,KAAA,GAAAqC,QAAA,CAAArC,KAAA;QACAuC,MAAA,CAAA5C,OAAA;MACA,CACA;IACA;IACA,cACA+C,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,MAAA;MACAvD,cAAA,GAAAgD,IAAA,WAAAC,QAAA;QACAM,MAAA,CAAA7B,WAAA,GAAAuB,QAAA,CAAA3C,IAAA;MACA;IACA;IACA,cACAkD,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,MAAA;MACAvD,cAAA,GAAA8C,IAAA,WAAAC,QAAA;QACAQ,MAAA,CAAA9B,WAAA,GAAAsB,QAAA,CAAA3C,IAAA;MACA;IACA;IACA;IACAoD,qBAAA,WAAAA,sBAAA;MACA;MACA,IAAAC,WAAA,QAAAC,KAAA,CAAAC,IAAA,CAAAC,cAAA;MACA;MACA,IAAAC,eAAA,QAAAH,KAAA,CAAAC,IAAA,CAAAG,kBAAA;MACAL,WAAA,CAAAM,OAAA,CAAAC,KAAA,CAAAP,WAAA,EAAAI,eAAA;MACA,OAAAJ,WAAA;IACA;IACA;IACAQ,qBAAA,WAAAA,sBAAA;MACA;MACA,IAAAR,WAAA,QAAAC,KAAA,CAAAQ,IAAA,CAAAN,cAAA;MACA;MACA,IAAAC,eAAA,QAAAH,KAAA,CAAAQ,IAAA,CAAAJ,kBAAA;MACAL,WAAA,CAAAM,OAAA,CAAAC,KAAA,CAAAP,WAAA,EAAAI,eAAA;MACA,OAAAJ,WAAA;IACA;IACA,oBACAU,qBAAA,WAAAA,sBAAAC,MAAA;MAAA,IAAAC,MAAA;MACA,OAAAtE,kBAAA,CAAAqE,MAAA,EAAAtB,IAAA,WAAAC,QAAA;QACAsB,MAAA,CAAA7C,WAAA,GAAAuB,QAAA,CAAAuB,KAAA;QACA,OAAAvB,QAAA;MACA;IACA;IACA,oBACAwB,qBAAA,WAAAA,sBAAAH,MAAA;MAAA,IAAAI,MAAA;MACA,OAAAvE,kBAAA,CAAAmE,MAAA,EAAAtB,IAAA,WAAAC,QAAA;QACAyB,MAAA,CAAA/C,WAAA,GAAAsB,QAAA,CAAA0B,KAAA;QACA,OAAA1B,QAAA;MACA;IACA;IACA;IACA2B,kBAAA,WAAAA,mBAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,IAAA,GAAAF,GAAA,CAAA3C,MAAA;MACA,KAAA8C,QAAA,UAAAD,IAAA,UAAAF,GAAA,CAAA9C,QAAA;QACAkD,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAAnC,IAAA;QACA,OAAAlD,gBAAA,CAAA+E,GAAA,CAAAP,MAAA,EAAAO,GAAA,CAAA3C,MAAA;MACA,GAAAc,IAAA;QACA8B,MAAA,CAAAM,UAAA,CAAAL,IAAA;MACA,GAAAM,KAAA;QACAR,GAAA,CAAA3C,MAAA,GAAA2C,GAAA,CAAA3C,MAAA;MACA;IACA;IACA;IACAoD,MAAA,WAAAA,OAAA;MACA,KAAAvE,IAAA;MACA,KAAAwE,KAAA;IACA;IACA;IACAC,eAAA,WAAAA,gBAAA;MACA,KAAAxE,aAAA;MACA,KAAAuE,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,SAAA3B,KAAA,CAAAC,IAAA,IAAA7B,SAAA;QACA,KAAA4B,KAAA,CAAAC,IAAA,CAAA4B,cAAA;MACA;MACA,KAAAxE,UAAA,UACA,KAAAC,WAAA,UACA,KAAAC,UAAA,SACA,KAAAC,WAAA,UACA,KAAAe,IAAA;QACAmC,MAAA,EAAAtC,SAAA;QACAD,QAAA,EAAAC,SAAA;QACAC,OAAA,EAAAD,SAAA;QACAU,QAAA;QACAR,MAAA;QACAwD,OAAA;QACAC,OAAA;QACAC,iBAAA;QACAC,iBAAA;QACAC,MAAA,EAAA9D,SAAA;QACA+D,IAAA,EAAA/D;MACA;MACA,KAAAgE,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAArE,WAAA,CAAAC,OAAA;MACA,KAAAiB,OAAA;IACA;IACA,aACAoD,UAAA,WAAAA,WAAA;MACA,KAAA7E,SAAA;MACA,KAAA2E,SAAA;MACA,KAAAC,WAAA;IACA;IACA;IACAE,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAA5F,GAAA,GAAA4F,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAhC,MAAA;MAAA;MACA,KAAA7D,MAAA,GAAA2F,SAAA,CAAAG,MAAA;MACA,KAAA7F,QAAA,IAAA0F,SAAA,CAAAG,MAAA;IACA;IACA;IACAC,uBAAA,WAAAA,wBAAAhF,KAAA,EAAA2D,IAAA;MACA,IAAAA,IAAA;QACA,IAAAsB,QAAA,QAAA/E,WAAA;QACA,SAAAgF,CAAA,MAAAA,CAAA,GAAAD,QAAA,CAAAF,MAAA,EAAAG,CAAA;UACA,KAAA9C,KAAA,CAAAC,IAAA,CAAA8C,KAAA,CAAAC,QAAA,CAAAH,QAAA,CAAAC,CAAA,EAAAG,EAAA,EAAAC,QAAA,GAAAtF,KAAA;QACA;MACA,WAAA2D,IAAA;QACA,IAAAsB,SAAA,QAAA9E,WAAA;QACA,SAAA+E,EAAA,MAAAA,EAAA,GAAAD,SAAA,CAAAF,MAAA,EAAAG,EAAA;UACA,KAAA9C,KAAA,CAAAQ,IAAA,CAAAuC,KAAA,CAAAC,QAAA,CAAAH,SAAA,CAAAC,EAAA,EAAAG,EAAA,EAAAC,QAAA,GAAAtF,KAAA;QACA;MACA;IACA;IACA;IACAuF,wBAAA,WAAAA,yBAAAvF,KAAA,EAAA2D,IAAA;MACA,IAAAA,IAAA;QACA,KAAAvB,KAAA,CAAAC,IAAA,CAAAmD,eAAA,CAAAxF,KAAA,QAAAE,WAAA;MACA,WAAAyD,IAAA;QACA,KAAAvB,KAAA,CAAAQ,IAAA,CAAA4C,eAAA,CAAAxF,KAAA,QAAAG,WAAA;MACA;IACA;IACA;IACAsF,wBAAA,WAAAA,yBAAAzF,KAAA,EAAA2D,IAAA;MACA,IAAAA,IAAA;QACA,KAAAhD,IAAA,CAAAyD,iBAAA,GAAApE,KAAA;MACA,WAAA2D,IAAA;QACA,KAAAhD,IAAA,CAAA0D,iBAAA,GAAArE,KAAA;MACA;IACA;IACA,aACA0F,SAAA,WAAAA,UAAA;MACA,KAAA3B,KAAA;MACA,KAAAjC,iBAAA;MACA,KAAAvC,IAAA;MACA,KAAAD,KAAA;IACA;IACA,aACAqG,YAAA,WAAAA,aAAAtC,GAAA;MAAA,IAAAuC,MAAA;MACA,KAAA7B,KAAA;MACA,IAAAjB,MAAA,GAAAO,GAAA,CAAAP,MAAA,SAAA9D,GAAA;MACA,IAAA6G,QAAA,QAAAhD,qBAAA,CAAAC,MAAA;MACA9E,OAAA,CAAA8E,MAAA,EAAAtB,IAAA,WAAAC,QAAA;QACAmE,MAAA,CAAAjF,IAAA,GAAAc,QAAA,CAAA3C,IAAA;QACA8G,MAAA,CAAArG,IAAA;QACAqG,MAAA,CAAAE,SAAA;UACAD,QAAA,CAAArE,IAAA,WAAAuE,GAAA;YACAH,MAAA,CAAAxD,KAAA,CAAAC,IAAA,CAAA4B,cAAA,CAAA8B,GAAA,CAAA5D,WAAA;UACA;QACA;QACAyD,MAAA,CAAAtG,KAAA;MACA;IACA;IACA,eACA0G,eAAA,WAAAA,gBAAA3C,GAAA;MAAA,IAAA4C,MAAA;MACA,KAAAlC,KAAA;MACA,IAAApF,kBAAA,QAAAsE,qBAAA,CAAAI,GAAA,CAAAP,MAAA;MACA9E,OAAA,CAAAqF,GAAA,CAAAP,MAAA,EAAAtB,IAAA,WAAAC,QAAA;QACAwE,MAAA,CAAAtF,IAAA,GAAAc,QAAA,CAAA3C,IAAA;QACAmH,MAAA,CAAAzG,aAAA;QACAyG,MAAA,CAAAH,SAAA;UACAnH,kBAAA,CAAA6C,IAAA,WAAAuE,GAAA;YACAE,MAAA,CAAA7D,KAAA,CAAAQ,IAAA,CAAAqB,cAAA,CAAA8B,GAAA,CAAA5D,WAAA;UACA;QACA;QACA8D,MAAA,CAAA3G,KAAA;MACA;IACA;IACA;IACA4G,UAAA,WAAAA,WAAA;MAAA,IAAAC,OAAA;MAEA,KAAA/D,KAAA,SAAAgE,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAF,OAAA,CAAAxF,IAAA,CAAAmC,MAAA,IAAAtC,SAAA;YACA2F,OAAA,CAAAxF,IAAA,CAAAuD,OAAA,GAAAiC,OAAA,CAAAjE,qBAAA;YACA/D,UAAA,CAAAgI,OAAA,CAAAxF,IAAA,EAAAa,IAAA,WAAAC,QAAA;cACA0E,OAAA,CAAAvC,UAAA;cACAuC,OAAA,CAAA5G,IAAA;cACA4G,OAAA,CAAA7E,OAAA;YACA;UACA;YACA6E,OAAA,CAAAxF,IAAA,CAAAuD,OAAA,GAAAiC,OAAA,CAAAjE,qBAAA;YACAhE,OAAA,CAAAiI,OAAA,CAAAxF,IAAA,EAAAa,IAAA,WAAAC,QAAA;cACA0E,OAAA,CAAAvC,UAAA;cACAuC,OAAA,CAAA5G,IAAA;cACA4G,OAAA,CAAA7E,OAAA;YACA;UACA;QACA;MACA;IACA;IACA;IACAgF,eAAA,WAAAA,gBAAA;MAAA,IAAAC,OAAA;MACA;MACA,SAAA5F,IAAA,CAAAmC,MAAA,IAAAtC,SAAA;QACA,KAAAG,IAAA,CAAAwD,OAAA,QAAAxB,qBAAA;QACAtE,SAAA,MAAAsC,IAAA,EAAAa,IAAA,WAAAC,QAAA;UACA8E,OAAA,CAAA3C,UAAA;UACA2C,OAAA,CAAA/G,aAAA;UACA+G,OAAA,CAAAjF,OAAA;QACA;MACA;IACA;IACA,aACAkF,YAAA,WAAAA,aAAAnD,GAAA;MAAA,IAAAoD,OAAA;MACA,IAAAC,OAAA,GAAArD,GAAA,CAAAP,MAAA,SAAA9D,GAAA;MACA,KAAAwE,QAAA,kBAAAkD,OAAA;QACAjD,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAAnC,IAAA;QACA,OAAAvD,OAAA,CAAAyI,OAAA;MACA,GAAAlF,IAAA;QACAiF,OAAA,CAAAnF,OAAA;QACAmF,OAAA,CAAA7C,UAAA;MACA;IACA;IACA,aACA+C,YAAA,WAAAA,aAAA;MAAA,IAAAC,OAAA;MACA,IAAAxG,WAAA,QAAAA,WAAA;MACA,KAAAoD,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAAnC,IAAA;QACA,OAAApD,UAAA,CAAAgC,WAAA;MACA,GAAAoB,IAAA,WAAAC,QAAA;QACAmF,OAAA,CAAAC,QAAA,CAAApF,QAAA,CAAAqF,GAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}