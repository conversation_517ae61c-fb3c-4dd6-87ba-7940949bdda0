{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/system/config/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/system/config/index.vue", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGxpc3RDb25maWcsIGdldENvbmZpZywgZGVsQ29uZmlnLCBhZGRDb25maWcsIHVwZGF0ZUNvbmZpZywgZXhwb3J0Q29uZmlnLCBjbGVhckNhY2hlIH0gZnJvbSAiQC9hcGkvc3lzdGVtL2NvbmZpZyI7CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiQ29uZmlnIiwKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgLy8g6YGu572p5bGCCiAgICAgIGxvYWRpbmc6IHRydWUsCiAgICAgIC8vIOmAieS4reaVsOe7hAogICAgICBpZHM6IFtdLAogICAgICAvLyDpnZ7ljZXkuKrnpoHnlKgKICAgICAgc2luZ2xlOiB0cnVlLAogICAgICAvLyDpnZ7lpJrkuKrnpoHnlKgKICAgICAgbXVsdGlwbGU6IHRydWUsCiAgICAgIC8vIOaYvuekuuaQnOe0ouadoeS7tgogICAgICBzaG93U2VhcmNoOiB0cnVlLAogICAgICAvLyDmgLvmnaHmlbAKICAgICAgdG90YWw6IDAsCiAgICAgIC8vIOWPguaVsOihqOagvOaVsOaNrgogICAgICBjb25maWdMaXN0OiBbXSwKICAgICAgLy8g5by55Ye65bGC5qCH6aKYCiAgICAgIHRpdGxlOiAiIiwKICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGCCiAgICAgIG9wZW46IGZhbHNlLAogICAgICAvLyDnsbvlnovmlbDmja7lrZflhbgKICAgICAgdHlwZU9wdGlvbnM6IFtdLAogICAgICAvLyDml6XmnJ/ojIPlm7QKICAgICAgZGF0ZVJhbmdlOiBbXSwKICAgICAgLy8g5p+l6K+i5Y+C5pWwCiAgICAgIHF1ZXJ5UGFyYW1zOiB7CiAgICAgICAgcGFnZU51bTogMSwKICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgY29uZmlnTmFtZTogdW5kZWZpbmVkLAogICAgICAgIGNvbmZpZ0tleTogdW5kZWZpbmVkLAogICAgICAgIGNvbmZpZ1R5cGU6IHVuZGVmaW5lZAogICAgICB9LAogICAgICAvLyDooajljZXlj4LmlbAKICAgICAgZm9ybToge30sCiAgICAgIC8vIOihqOWNleagoemqjAogICAgICBydWxlczogewogICAgICAgIGNvbmZpZ05hbWU6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICLlj4LmlbDlkI3np7DkuI3og73kuLrnqboiLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgfV0sCiAgICAgICAgY29uZmlnS2V5OiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAi5Y+C5pWw6ZSu5ZCN5LiN6IO95Li656m6IiwKICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgIH1dLAogICAgICAgIGNvbmZpZ1ZhbHVlOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAi5Y+C5pWw6ZSu5YC85LiN6IO95Li656m6IiwKICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgIH1dCiAgICAgIH0KICAgIH07CiAgfSwKICBjcmVhdGVkOiBmdW5jdGlvbiBjcmVhdGVkKCkgewogICAgdmFyIF90aGlzID0gdGhpczsKICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgdGhpcy5nZXREaWN0cygic3lzX3llc19ubyIpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgIF90aGlzLnR5cGVPcHRpb25zID0gcmVzcG9uc2UuZGF0YTsKICAgIH0pOwogIH0sCiAgbWV0aG9kczogewogICAgLyoqIOafpeivouWPguaVsOWIl+ihqCAqL2dldExpc3Q6IGZ1bmN0aW9uIGdldExpc3QoKSB7CiAgICAgIHZhciBfdGhpczIgPSB0aGlzOwogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOwogICAgICBsaXN0Q29uZmlnKHRoaXMuYWRkRGF0ZVJhbmdlKHRoaXMucXVlcnlQYXJhbXMsIHRoaXMuZGF0ZVJhbmdlKSkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICBfdGhpczIuY29uZmlnTGlzdCA9IHJlc3BvbnNlLnJvd3M7CiAgICAgICAgX3RoaXMyLnRvdGFsID0gcmVzcG9uc2UudG90YWw7CiAgICAgICAgX3RoaXMyLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgfSk7CiAgICB9LAogICAgLy8g5Y+C5pWw57O757uf5YaF572u5a2X5YW457+76K+RCiAgICB0eXBlRm9ybWF0OiBmdW5jdGlvbiB0eXBlRm9ybWF0KHJvdywgY29sdW1uKSB7CiAgICAgIHJldHVybiB0aGlzLnNlbGVjdERpY3RMYWJlbCh0aGlzLnR5cGVPcHRpb25zLCByb3cuY29uZmlnVHlwZSk7CiAgICB9LAogICAgLy8g5Y+W5raI5oyJ6ZKuCiAgICBjYW5jZWw6IGZ1bmN0aW9uIGNhbmNlbCgpIHsKICAgICAgdGhpcy5vcGVuID0gZmFsc2U7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgIH0sCiAgICAvLyDooajljZXph43nva4KICAgIHJlc2V0OiBmdW5jdGlvbiByZXNldCgpIHsKICAgICAgdGhpcy5mb3JtID0gewogICAgICAgIGNvbmZpZ0lkOiB1bmRlZmluZWQsCiAgICAgICAgY29uZmlnTmFtZTogdW5kZWZpbmVkLAogICAgICAgIGNvbmZpZ0tleTogdW5kZWZpbmVkLAogICAgICAgIGNvbmZpZ1ZhbHVlOiB1bmRlZmluZWQsCiAgICAgICAgY29uZmlnVHlwZTogIlkiLAogICAgICAgIHJlbWFyazogdW5kZWZpbmVkCiAgICAgIH07CiAgICAgIHRoaXMucmVzZXRGb3JtKCJmb3JtIik7CiAgICB9LAogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqL2hhbmRsZVF1ZXJ5OiBmdW5jdGlvbiBoYW5kbGVRdWVyeSgpIHsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsKICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICB9LAogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqL3Jlc2V0UXVlcnk6IGZ1bmN0aW9uIHJlc2V0UXVlcnkoKSB7CiAgICAgIHRoaXMuZGF0ZVJhbmdlID0gW107CiAgICAgIHRoaXMucmVzZXRGb3JtKCJxdWVyeUZvcm0iKTsKICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOwogICAgfSwKICAgIC8qKiDmlrDlop7mjInpkq7mk43kvZwgKi9oYW5kbGVBZGQ6IGZ1bmN0aW9uIGhhbmRsZUFkZCgpIHsKICAgICAgdGhpcy5yZXNldCgpOwogICAgICB0aGlzLm9wZW4gPSB0cnVlOwogICAgICB0aGlzLnRpdGxlID0gIua3u+WKoOWPguaVsCI7CiAgICB9LAogICAgLy8g5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2U6IGZ1bmN0aW9uIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsKICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgcmV0dXJuIGl0ZW0uY29uZmlnSWQ7CiAgICAgIH0pOwogICAgICB0aGlzLnNpbmdsZSA9IHNlbGVjdGlvbi5sZW5ndGggIT0gMTsKICAgICAgdGhpcy5tdWx0aXBsZSA9ICFzZWxlY3Rpb24ubGVuZ3RoOwogICAgfSwKICAgIC8qKiDkv67mlLnmjInpkq7mk43kvZwgKi9oYW5kbGVVcGRhdGU6IGZ1bmN0aW9uIGhhbmRsZVVwZGF0ZShyb3cpIHsKICAgICAgdmFyIF90aGlzMyA9IHRoaXM7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgICAgdmFyIGNvbmZpZ0lkID0gcm93LmNvbmZpZ0lkIHx8IHRoaXMuaWRzOwogICAgICBnZXRDb25maWcoY29uZmlnSWQpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgX3RoaXMzLmZvcm0gPSByZXNwb25zZS5kYXRhOwogICAgICAgIF90aGlzMy5vcGVuID0gdHJ1ZTsKICAgICAgICBfdGhpczMudGl0bGUgPSAi5L+u5pS55Y+C5pWwIjsKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOaPkOS6pOaMiemSriAqLwogICAgc3VibWl0Rm9ybTogZnVuY3Rpb24gc3VibWl0Rm9ybSgpIHsKICAgICAgdmFyIF90aGlzNCA9IHRoaXM7CiAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZShmdW5jdGlvbiAodmFsaWQpIHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIGlmIChfdGhpczQuZm9ybS5jb25maWdJZCAhPSB1bmRlZmluZWQpIHsKICAgICAgICAgICAgdXBkYXRlQ29uZmlnKF90aGlzNC5mb3JtKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgICAgICAgIF90aGlzNC5tc2dTdWNjZXNzKCLkv67mlLnmiJDlip8iKTsKICAgICAgICAgICAgICBfdGhpczQub3BlbiA9IGZhbHNlOwogICAgICAgICAgICAgIF90aGlzNC5nZXRMaXN0KCk7CiAgICAgICAgICAgIH0pOwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgYWRkQ29uZmlnKF90aGlzNC5mb3JtKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgICAgICAgIF90aGlzNC5tc2dTdWNjZXNzKCLmlrDlop7miJDlip8iKTsKICAgICAgICAgICAgICBfdGhpczQub3BlbiA9IGZhbHNlOwogICAgICAgICAgICAgIF90aGlzNC5nZXRMaXN0KCk7CiAgICAgICAgICAgIH0pOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLyoqIOWIoOmZpOaMiemSruaTjeS9nCAqL2hhbmRsZURlbGV0ZTogZnVuY3Rpb24gaGFuZGxlRGVsZXRlKHJvdykgewogICAgICB2YXIgX3RoaXM1ID0gdGhpczsKICAgICAgdmFyIGNvbmZpZ0lkcyA9IHJvdy5jb25maWdJZCB8fCB0aGlzLmlkczsKICAgICAgdGhpcy4kY29uZmlybSgn5piv5ZCm56Gu6K6k5Yig6Zmk5Y+C5pWw57yW5Y+35Li6IicgKyBjb25maWdJZHMgKyAnIueahOaVsOaNrumhuT8nLCAi6K2m5ZGKIiwgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAi56Gu5a6aIiwKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAi5Y+W5raIIiwKICAgICAgICB0eXBlOiAid2FybmluZyIKICAgICAgfSkudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgcmV0dXJuIGRlbENvbmZpZyhjb25maWdJZHMpOwogICAgICB9KS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICBfdGhpczUuZ2V0TGlzdCgpOwogICAgICAgIF90aGlzNS5tc2dTdWNjZXNzKCLliKDpmaTmiJDlip8iKTsKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOWvvOWHuuaMiemSruaTjeS9nCAqL2hhbmRsZUV4cG9ydDogZnVuY3Rpb24gaGFuZGxlRXhwb3J0KCkgewogICAgICB2YXIgX3RoaXM2ID0gdGhpczsKICAgICAgdmFyIHF1ZXJ5UGFyYW1zID0gdGhpcy5xdWVyeVBhcmFtczsKICAgICAgdGhpcy4kY29uZmlybSgn5piv5ZCm56Gu6K6k5a+85Ye65omA5pyJ5Y+C5pWw5pWw5o2u6aG5PycsICLorablkYoiLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLAogICAgICAgIHR5cGU6ICJ3YXJuaW5nIgogICAgICB9KS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICByZXR1cm4gZXhwb3J0Q29uZmlnKHF1ZXJ5UGFyYW1zKTsKICAgICAgfSkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICBfdGhpczYuZG93bmxvYWQocmVzcG9uc2UubXNnKTsKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOa4heeQhue8k+WtmOaMiemSruaTjeS9nCAqL2hhbmRsZUNsZWFyQ2FjaGU6IGZ1bmN0aW9uIGhhbmRsZUNsZWFyQ2FjaGUoKSB7CiAgICAgIHZhciBfdGhpczcgPSB0aGlzOwogICAgICBjbGVhckNhY2hlKCkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICBfdGhpczcubXNnU3VjY2Vzcygi5riF55CG5oiQ5YqfIik7CiAgICAgIH0pOwogICAgfQogIH0KfTs="}, {"version": 3, "names": ["listConfig", "getConfig", "delConfig", "addConfig", "updateConfig", "exportConfig", "clearCache", "name", "data", "loading", "ids", "single", "multiple", "showSearch", "total", "configList", "title", "open", "typeOptions", "date<PERSON><PERSON><PERSON>", "queryParams", "pageNum", "pageSize", "config<PERSON><PERSON>", "undefined", "config<PERSON><PERSON>", "configType", "form", "rules", "required", "message", "trigger", "config<PERSON><PERSON><PERSON>", "created", "_this", "getList", "getDicts", "then", "response", "methods", "_this2", "addDateRange", "rows", "typeFormat", "row", "column", "selectDictLabel", "cancel", "reset", "configId", "remark", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleAdd", "handleSelectionChange", "selection", "map", "item", "length", "handleUpdate", "_this3", "submitForm", "_this4", "$refs", "validate", "valid", "msgSuccess", "handleDelete", "_this5", "configIds", "$confirm", "confirmButtonText", "cancelButtonText", "type", "handleExport", "_this6", "download", "msg", "handleClearCache", "_this7"], "sources": ["src/views/system/config/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"参数名称\" prop=\"configName\">\n        <el-input\n          v-model=\"queryParams.configName\"\n          placeholder=\"请输入参数名称\"\n          clearable\n          size=\"small\"\n          style=\"width: 240px\"\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"参数键名\" prop=\"configKey\">\n        <el-input\n          v-model=\"queryParams.configKey\"\n          placeholder=\"请输入参数键名\"\n          clearable\n          size=\"small\"\n          style=\"width: 240px\"\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"系统内置\" prop=\"configType\">\n        <el-select v-model=\"queryParams.configType\" placeholder=\"系统内置\" clearable size=\"small\">\n          <el-option\n            v-for=\"dict in typeOptions\"\n            :key=\"dict.dictValue\"\n            :label=\"dict.dictLabel\"\n            :value=\"dict.dictValue\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"创建时间\">\n        <el-date-picker\n          v-model=\"dateRange\"\n          size=\"small\"\n          style=\"width: 240px\"\n          value-format=\"yyyy-MM-dd\"\n          type=\"daterange\"\n          range-separator=\"-\"\n          start-placeholder=\"开始日期\"\n          end-placeholder=\"结束日期\"\n        ></el-date-picker>\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"primary\"\n          plain\n          icon=\"el-icon-plus\"\n          size=\"mini\"\n          @click=\"handleAdd\"\n          v-hasPermi=\"['system:config:add']\"\n        >新增</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"success\"\n          plain\n          icon=\"el-icon-edit\"\n          size=\"mini\"\n          :disabled=\"single\"\n          @click=\"handleUpdate\"\n          v-hasPermi=\"['system:config:edit']\"\n        >修改</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"danger\"\n          plain\n          icon=\"el-icon-delete\"\n          size=\"mini\"\n          :disabled=\"multiple\"\n          @click=\"handleDelete\"\n          v-hasPermi=\"['system:config:remove']\"\n        >删除</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"warning\"\n          plain\n          icon=\"el-icon-download\"\n          size=\"mini\"\n          @click=\"handleExport\"\n          v-hasPermi=\"['system:config:export']\"\n        >导出</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"danger\"\n          plain\n          icon=\"el-icon-refresh\"\n          size=\"mini\"\n          @click=\"handleClearCache\"\n          v-hasPermi=\"['system:config:remove']\"\n        >清理缓存</el-button>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <el-table v-loading=\"loading\" :data=\"configList\" @selection-change=\"handleSelectionChange\">\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column label=\"参数主键\" align=\"center\" prop=\"configId\" />\n      <el-table-column label=\"参数名称\" align=\"center\" prop=\"configName\" :show-overflow-tooltip=\"true\" />\n      <el-table-column label=\"参数键名\" align=\"center\" prop=\"configKey\" :show-overflow-tooltip=\"true\" />\n      <el-table-column label=\"参数键值\" align=\"center\" prop=\"configValue\" />\n      <el-table-column label=\"系统内置\" align=\"center\" prop=\"configType\" :formatter=\"typeFormat\" />\n      <el-table-column label=\"备注\" align=\"center\" prop=\"remark\" :show-overflow-tooltip=\"true\" />\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"180\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.createTime) }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-edit\"\n            @click=\"handleUpdate(scope.row)\"\n            v-hasPermi=\"['system:config:edit']\"\n          >修改</el-button>\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-delete\"\n            @click=\"handleDelete(scope.row)\"\n            v-hasPermi=\"['system:config:remove']\"\n          >删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 添加或修改参数配置对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"500px\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\n        <el-form-item label=\"参数名称\" prop=\"configName\">\n          <el-input v-model=\"form.configName\" placeholder=\"请输入参数名称\" />\n        </el-form-item>\n        <el-form-item label=\"参数键名\" prop=\"configKey\">\n          <el-input v-model=\"form.configKey\" placeholder=\"请输入参数键名\" />\n        </el-form-item>\n        <el-form-item label=\"参数键值\" prop=\"configValue\">\n          <el-input v-model=\"form.configValue\" placeholder=\"请输入参数键值\" />\n        </el-form-item>\n        <el-form-item label=\"系统内置\" prop=\"configType\">\n          <el-radio-group v-model=\"form.configType\">\n            <el-radio\n              v-for=\"dict in typeOptions\"\n              :key=\"dict.dictValue\"\n              :label=\"dict.dictValue\"\n            >{{dict.dictLabel}}</el-radio>\n          </el-radio-group>\n        </el-form-item>\n        <el-form-item label=\"备注\" prop=\"remark\">\n          <el-input v-model=\"form.remark\" type=\"textarea\" placeholder=\"请输入内容\" />\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { listConfig, getConfig, delConfig, addConfig, updateConfig, exportConfig, clearCache } from \"@/api/system/config\";\n\nexport default {\n  name: \"Config\",\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 参数表格数据\n      configList: [],\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      open: false,\n      // 类型数据字典\n      typeOptions: [],\n      // 日期范围\n      dateRange: [],\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        configName: undefined,\n        configKey: undefined,\n        configType: undefined\n      },\n      // 表单参数\n      form: {},\n      // 表单校验\n      rules: {\n        configName: [\n          { required: true, message: \"参数名称不能为空\", trigger: \"blur\" }\n        ],\n        configKey: [\n          { required: true, message: \"参数键名不能为空\", trigger: \"blur\" }\n        ],\n        configValue: [\n          { required: true, message: \"参数键值不能为空\", trigger: \"blur\" }\n        ]\n      }\n    };\n  },\n  created() {\n    this.getList();\n    this.getDicts(\"sys_yes_no\").then(response => {\n      this.typeOptions = response.data;\n    });\n  },\n  methods: {\n    /** 查询参数列表 */\n    getList() {\n      this.loading = true;\n      listConfig(this.addDateRange(this.queryParams, this.dateRange)).then(response => {\n          this.configList = response.rows;\n          this.total = response.total;\n          this.loading = false;\n        }\n      );\n    },\n    // 参数系统内置字典翻译\n    typeFormat(row, column) {\n      return this.selectDictLabel(this.typeOptions, row.configType);\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        configId: undefined,\n        configName: undefined,\n        configKey: undefined,\n        configValue: undefined,\n        configType: \"Y\",\n        remark: undefined\n      };\n      this.resetForm(\"form\");\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.dateRange = [];\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.reset();\n      this.open = true;\n      this.title = \"添加参数\";\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.configId)\n      this.single = selection.length!=1\n      this.multiple = !selection.length\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset();\n      const configId = row.configId || this.ids\n      getConfig(configId).then(response => {\n        this.form = response.data;\n        this.open = true;\n        this.title = \"修改参数\";\n      });\n    },\n    /** 提交按钮 */\n    submitForm: function() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          if (this.form.configId != undefined) {\n            updateConfig(this.form).then(response => {\n              this.msgSuccess(\"修改成功\");\n              this.open = false;\n              this.getList();\n            });\n          } else {\n            addConfig(this.form).then(response => {\n              this.msgSuccess(\"新增成功\");\n              this.open = false;\n              this.getList();\n            });\n          }\n        }\n      });\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const configIds = row.configId || this.ids;\n      this.$confirm('是否确认删除参数编号为\"' + configIds + '\"的数据项?', \"警告\", {\n          confirmButtonText: \"确定\",\n          cancelButtonText: \"取消\",\n          type: \"warning\"\n        }).then(function() {\n          return delConfig(configIds);\n        }).then(() => {\n          this.getList();\n          this.msgSuccess(\"删除成功\");\n        })\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      const queryParams = this.queryParams;\n      this.$confirm('是否确认导出所有参数数据项?', \"警告\", {\n          confirmButtonText: \"确定\",\n          cancelButtonText: \"取消\",\n          type: \"warning\"\n        }).then(function() {\n          return exportConfig(queryParams);\n        }).then(response => {\n          this.download(response.msg);\n        })\n    },\n    /** 清理缓存按钮操作 */\n    handleClearCache() {\n      clearCache().then(response => {\n        this.msgSuccess(\"清理成功\");\n      });\n    }\n  }\n};\n</script>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsLA,SAAAA,UAAA,EAAAC,SAAA,EAAAC,SAAA,EAAAC,SAAA,EAAAC,YAAA,EAAAC,YAAA,EAAAC,UAAA;AAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;MACA;MACAC,SAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,UAAA,EAAAC,SAAA;QACAC,SAAA,EAAAD,SAAA;QACAE,UAAA,EAAAF;MACA;MACA;MACAG,IAAA;MACA;MACAC,KAAA;QACAL,UAAA,GACA;UAAAM,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAN,SAAA,GACA;UAAAI,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAC,WAAA,GACA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAE,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA,KAAAC,OAAA;IACA,KAAAC,QAAA,eAAAC,IAAA,WAAAC,QAAA;MACAJ,KAAA,CAAAhB,WAAA,GAAAoB,QAAA,CAAA9B,IAAA;IACA;EACA;EACA+B,OAAA;IACA,aACAJ,OAAA,WAAAA,QAAA;MAAA,IAAAK,MAAA;MACA,KAAA/B,OAAA;MACAT,UAAA,MAAAyC,YAAA,MAAArB,WAAA,OAAAD,SAAA,GAAAkB,IAAA,WAAAC,QAAA;QACAE,MAAA,CAAAzB,UAAA,GAAAuB,QAAA,CAAAI,IAAA;QACAF,MAAA,CAAA1B,KAAA,GAAAwB,QAAA,CAAAxB,KAAA;QACA0B,MAAA,CAAA/B,OAAA;MACA,CACA;IACA;IACA;IACAkC,UAAA,WAAAA,WAAAC,GAAA,EAAAC,MAAA;MACA,YAAAC,eAAA,MAAA5B,WAAA,EAAA0B,GAAA,CAAAlB,UAAA;IACA;IACA;IACAqB,MAAA,WAAAA,OAAA;MACA,KAAA9B,IAAA;MACA,KAAA+B,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAArB,IAAA;QACAsB,QAAA,EAAAzB,SAAA;QACAD,UAAA,EAAAC,SAAA;QACAC,SAAA,EAAAD,SAAA;QACAQ,WAAA,EAAAR,SAAA;QACAE,UAAA;QACAwB,MAAA,EAAA1B;MACA;MACA,KAAA2B,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAAhC,WAAA,CAAAC,OAAA;MACA,KAAAc,OAAA;IACA;IACA,aACAkB,UAAA,WAAAA,WAAA;MACA,KAAAlC,SAAA;MACA,KAAAgC,SAAA;MACA,KAAAC,WAAA;IACA;IACA,aACAE,SAAA,WAAAA,UAAA;MACA,KAAAN,KAAA;MACA,KAAA/B,IAAA;MACA,KAAAD,KAAA;IACA;IACA;IACAuC,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAA9C,GAAA,GAAA8C,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAT,QAAA;MAAA;MACA,KAAAtC,MAAA,GAAA6C,SAAA,CAAAG,MAAA;MACA,KAAA/C,QAAA,IAAA4C,SAAA,CAAAG,MAAA;IACA;IACA,aACAC,YAAA,WAAAA,aAAAhB,GAAA;MAAA,IAAAiB,MAAA;MACA,KAAAb,KAAA;MACA,IAAAC,QAAA,GAAAL,GAAA,CAAAK,QAAA,SAAAvC,GAAA;MACAT,SAAA,CAAAgD,QAAA,EAAAZ,IAAA,WAAAC,QAAA;QACAuB,MAAA,CAAAlC,IAAA,GAAAW,QAAA,CAAA9B,IAAA;QACAqD,MAAA,CAAA5C,IAAA;QACA4C,MAAA,CAAA7C,KAAA;MACA;IACA;IACA;IACA8C,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAH,MAAA,CAAApC,IAAA,CAAAsB,QAAA,IAAAzB,SAAA;YACApB,YAAA,CAAA2D,MAAA,CAAApC,IAAA,EAAAU,IAAA,WAAAC,QAAA;cACAyB,MAAA,CAAAI,UAAA;cACAJ,MAAA,CAAA9C,IAAA;cACA8C,MAAA,CAAA5B,OAAA;YACA;UACA;YACAhC,SAAA,CAAA4D,MAAA,CAAApC,IAAA,EAAAU,IAAA,WAAAC,QAAA;cACAyB,MAAA,CAAAI,UAAA;cACAJ,MAAA,CAAA9C,IAAA;cACA8C,MAAA,CAAA5B,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACAiC,YAAA,WAAAA,aAAAxB,GAAA;MAAA,IAAAyB,MAAA;MACA,IAAAC,SAAA,GAAA1B,GAAA,CAAAK,QAAA,SAAAvC,GAAA;MACA,KAAA6D,QAAA,kBAAAD,SAAA;QACAE,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAArC,IAAA;QACA,OAAAnC,SAAA,CAAAoE,SAAA;MACA,GAAAjC,IAAA;QACAgC,MAAA,CAAAlC,OAAA;QACAkC,MAAA,CAAAF,UAAA;MACA;IACA;IACA,aACAQ,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,IAAAxD,WAAA,QAAAA,WAAA;MACA,KAAAmD,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAArC,IAAA;QACA,OAAAhC,YAAA,CAAAe,WAAA;MACA,GAAAiB,IAAA,WAAAC,QAAA;QACAsC,MAAA,CAAAC,QAAA,CAAAvC,QAAA,CAAAwC,GAAA;MACA;IACA;IACA,eACAC,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,MAAA;MACA1E,UAAA,GAAA+B,IAAA,WAAAC,QAAA;QACA0C,MAAA,CAAAb,UAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}