{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/directive/permission/index.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/directive/permission/index.js", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IGhhc1JvbGUgZnJvbSAnLi9oYXNSb2xlJzsKaW1wb3J0IGhhc1Blcm1pIGZyb20gJy4vaGFzUGVybWknOwp2YXIgaW5zdGFsbCA9IGZ1bmN0aW9uIGluc3RhbGwoVnVlKSB7CiAgVnVlLmRpcmVjdGl2ZSgnaGFzUm9sZScsIGhhc1JvbGUpOwogIFZ1ZS5kaXJlY3RpdmUoJ2hhc1Blcm1pJywgaGFzUGVybWkpOwp9OwppZiAod2luZG93LlZ1ZSkgewogIHdpbmRvd1snaGFzUm9sZSddID0gaGFzUm9sZTsKICB3aW5kb3dbJ2hhc1Blcm1pJ10gPSBoYXNQZXJtaTsKICBWdWUudXNlKGluc3RhbGwpOyAvLyBlc2xpbnQtZGlzYWJsZS1saW5lCn0KZXhwb3J0IGRlZmF1bHQgaW5zdGFsbDs="}, {"version": 3, "names": ["hasRole", "<PERSON><PERSON><PERSON><PERSON>", "install", "<PERSON><PERSON>", "directive", "window", "use"], "sources": ["/Users/<USER>/develop/code/project/haijia/adminweb/src/directive/permission/index.js"], "sourcesContent": ["import hasRole from './hasRole'\nimport hasPermi from './hasPermi'\n\nconst install = function(Vue) {\n  Vue.directive('hasRole', hasRole)\n  Vue.directive('hasPermi', hasPermi)\n}\n\nif (window.Vue) {\n  window['hasRole'] = hasRole\n  window['hasPermi'] = hasPermi\n  Vue.use(install); // eslint-disable-line\n}\n\nexport default install\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,WAAW;AAC/B,OAAOC,QAAQ,MAAM,YAAY;AAEjC,IAAMC,OAAO,GAAG,SAAVA,OAAOA,CAAYC,GAAG,EAAE;EAC5BA,GAAG,CAACC,SAAS,CAAC,SAAS,EAAEJ,OAAO,CAAC;EACjCG,GAAG,CAACC,SAAS,CAAC,UAAU,EAAEH,QAAQ,CAAC;AACrC,CAAC;AAED,IAAII,MAAM,CAACF,GAAG,EAAE;EACdE,MAAM,CAAC,SAAS,CAAC,GAAGL,OAAO;EAC3BK,MAAM,CAAC,UAAU,CAAC,GAAGJ,QAAQ;EAC7BE,GAAG,CAACG,GAAG,CAACJ,OAAO,CAAC,CAAC,CAAC;AACpB;AAEA,eAAeA,OAAO", "ignoreList": []}]}