{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/api/system/dict/type.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/api/system/dict/type.js", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QgZnJvbSAnQC91dGlscy9yZXF1ZXN0JzsKCi8vIOafpeivouWtl+WFuOexu+Wei+WIl+ihqApleHBvcnQgZnVuY3Rpb24gbGlzdFR5cGUocXVlcnkpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvc3lzdGVtL2RpY3QvdHlwZS9saXN0JywKICAgIG1ldGhvZDogJ2dldCcsCiAgICBwYXJhbXM6IHF1ZXJ5CiAgfSk7Cn0KCi8vIOafpeivouWtl+WFuOexu+Wei+ivpue7hgpleHBvcnQgZnVuY3Rpb24gZ2V0VHlwZShkaWN0SWQpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvc3lzdGVtL2RpY3QvdHlwZS8nICsgZGljdElkLAogICAgbWV0aG9kOiAnZ2V0JwogIH0pOwp9CgovLyDmlrDlop7lrZflhbjnsbvlnosKZXhwb3J0IGZ1bmN0aW9uIGFkZFR5cGUoZGF0YSkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9zeXN0ZW0vZGljdC90eXBlJywKICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgZGF0YTogZGF0YQogIH0pOwp9CgovLyDkv67mlLnlrZflhbjnsbvlnosKZXhwb3J0IGZ1bmN0aW9uIHVwZGF0ZVR5cGUoZGF0YSkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9zeXN0ZW0vZGljdC90eXBlJywKICAgIG1ldGhvZDogJ3B1dCcsCiAgICBkYXRhOiBkYXRhCiAgfSk7Cn0KCi8vIOWIoOmZpOWtl+WFuOexu+WeiwpleHBvcnQgZnVuY3Rpb24gZGVsVHlwZShkaWN0SWQpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvc3lzdGVtL2RpY3QvdHlwZS8nICsgZGljdElkLAogICAgbWV0aG9kOiAnZGVsZXRlJwogIH0pOwp9CgovLyDmuIXnkIblj4LmlbDnvJPlrZgKZXhwb3J0IGZ1bmN0aW9uIGNsZWFyQ2FjaGUoKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL3N5c3RlbS9kaWN0L3R5cGUvY2xlYXJDYWNoZScsCiAgICBtZXRob2Q6ICdkZWxldGUnCiAgfSk7Cn0KCi8vIOWvvOWHuuWtl+WFuOexu+WeiwpleHBvcnQgZnVuY3Rpb24gZXhwb3J0VHlwZShxdWVyeSkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9zeXN0ZW0vZGljdC90eXBlL2V4cG9ydCcsCiAgICBtZXRob2Q6ICdnZXQnLAogICAgcGFyYW1zOiBxdWVyeQogIH0pOwp9CgovLyDojrflj5blrZflhbjpgInmi6nmoYbliJfooagKZXhwb3J0IGZ1bmN0aW9uIG9wdGlvbnNlbGVjdCgpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvc3lzdGVtL2RpY3QvdHlwZS9vcHRpb25zZWxlY3QnLAogICAgbWV0aG9kOiAnZ2V0JwogIH0pOwp9"}, {"version": 3, "names": ["request", "listType", "query", "url", "method", "params", "getType", "dictId", "addType", "data", "updateType", "delType", "clearCache", "exportType", "optionselect"], "sources": ["/Users/<USER>/develop/code/project/haijia/adminweb/src/api/system/dict/type.js"], "sourcesContent": ["import request from '@/utils/request'\n\n// 查询字典类型列表\nexport function listType(query) {\n  return request({\n    url: '/system/dict/type/list',\n    method: 'get',\n    params: query\n  })\n}\n\n// 查询字典类型详细\nexport function getType(dictId) {\n  return request({\n    url: '/system/dict/type/' + dictId,\n    method: 'get'\n  })\n}\n\n// 新增字典类型\nexport function addType(data) {\n  return request({\n    url: '/system/dict/type',\n    method: 'post',\n    data: data\n  })\n}\n\n// 修改字典类型\nexport function updateType(data) {\n  return request({\n    url: '/system/dict/type',\n    method: 'put',\n    data: data\n  })\n}\n\n// 删除字典类型\nexport function delType(dictId) {\n  return request({\n    url: '/system/dict/type/' + dictId,\n    method: 'delete'\n  })\n}\n\n// 清理参数缓存\nexport function clearCache() {\n  return request({\n    url: '/system/dict/type/clearCache',\n    method: 'delete'\n  })\n}\n\n// 导出字典类型\nexport function exportType(query) {\n  return request({\n    url: '/system/dict/type/export',\n    method: 'get',\n    params: query\n  })\n}\n\n// 获取字典选择框列表\nexport function optionselect() {\n  return request({\n    url: '/system/dict/type/optionselect',\n    method: 'get'\n  })\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;;AAErC;AACA,OAAO,SAASC,QAAQA,CAACC,KAAK,EAAE;EAC9B,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,wBAAwB;IAC7BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEH;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASI,OAAOA,CAACC,MAAM,EAAE;EAC9B,OAAOP,OAAO,CAAC;IACbG,GAAG,EAAE,oBAAoB,GAAGI,MAAM;IAClCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASI,OAAOA,CAACC,IAAI,EAAE;EAC5B,OAAOT,OAAO,CAAC;IACbG,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASC,UAAUA,CAACD,IAAI,EAAE;EAC/B,OAAOT,OAAO,CAAC;IACbG,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASE,OAAOA,CAACJ,MAAM,EAAE;EAC9B,OAAOP,OAAO,CAAC;IACbG,GAAG,EAAE,oBAAoB,GAAGI,MAAM;IAClCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASQ,UAAUA,CAAA,EAAG;EAC3B,OAAOZ,OAAO,CAAC;IACbG,GAAG,EAAE,8BAA8B;IACnCC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASS,UAAUA,CAACX,KAAK,EAAE;EAChC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,0BAA0B;IAC/BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEH;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASY,YAAYA,CAAA,EAAG;EAC7B,OAAOd,OAAO,CAAC;IACbG,GAAG,EAAE,gCAAgC;IACrCC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}