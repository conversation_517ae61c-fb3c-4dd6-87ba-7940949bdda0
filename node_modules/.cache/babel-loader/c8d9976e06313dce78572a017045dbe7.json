{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/monitor/druid/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/monitor/druid/index.vue", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiRHJ1aWQiLAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBzcmM6IHByb2Nlc3MuZW52LlZVRV9BUFBfQkFTRV9BUEkgKyAiL2RydWlkL2luZGV4Lmh0bWwiLAogICAgICBoZWlnaHQ6IGRvY3VtZW50LmRvY3VtZW50RWxlbWVudC5jbGllbnRIZWlnaHQgLSA5NC41ICsgInB4OyIsCiAgICAgIGxvYWRpbmc6IHRydWUKICAgIH07CiAgfSwKICBtb3VudGVkOiBmdW5jdGlvbiBtb3VudGVkKCkgewogICAgdmFyIF90aGlzID0gdGhpczsKICAgIHNldFRpbWVvdXQoZnVuY3Rpb24gKCkgewogICAgICBfdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICB9LCAyMzApOwogICAgdmFyIHRoYXQgPSB0aGlzOwogICAgd2luZG93Lm9ucmVzaXplID0gZnVuY3Rpb24gdGVtcCgpIHsKICAgICAgdGhhdC5oZWlnaHQgPSBkb2N1bWVudC5kb2N1bWVudEVsZW1lbnQuY2xpZW50SGVpZ2h0IC0gOTQuNSArICJweDsiOwogICAgfTsKICB9Cn07"}, {"version": 3, "names": ["name", "data", "src", "process", "env", "VUE_APP_BASE_API", "height", "document", "documentElement", "clientHeight", "loading", "mounted", "_this", "setTimeout", "that", "window", "onresize", "temp"], "sources": ["src/views/monitor/druid/index.vue"], "sourcesContent": ["<template>\n  <div v-loading=\"loading\" :style=\"'height:'+ height\">\n    <iframe :src=\"src\" frameborder=\"no\" style=\"width: 100%;height: 100%\" scrolling=\"auto\" />\n  </div>\n</template>\n<script>\nexport default {\n  name: \"Druid\",\n  data() {\n    return {\n      src: process.env.VUE_APP_BASE_API + \"/druid/index.html\",\n      height: document.documentElement.clientHeight - 94.5 + \"px;\",\n      loading: true\n    };\n  },\n  mounted: function() {\n    setTimeout(() => {\n      this.loading = false;\n    }, 230);\n    const that = this;\n    window.onresize = function temp() {\n      that.height = document.documentElement.clientHeight - 94.5 + \"px;\";\n    };\n  }\n};\n</script>\n"], "mappings": ";;;;;;AAMA;EACAA,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,GAAA,EAAAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA;MACAC,MAAA,EAAAC,QAAA,CAAAC,eAAA,CAAAC,YAAA;MACAC,OAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACAC,UAAA;MACAD,KAAA,CAAAF,OAAA;IACA;IACA,IAAAI,IAAA;IACAC,MAAA,CAAAC,QAAA,YAAAC,KAAA;MACAH,IAAA,CAAAR,MAAA,GAAAC,QAAA,CAAAC,eAAA,CAAAC,YAAA;IACA;EACA;AACA", "ignoreList": []}]}