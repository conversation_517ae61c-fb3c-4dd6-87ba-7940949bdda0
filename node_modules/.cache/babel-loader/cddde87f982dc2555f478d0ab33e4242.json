{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/api/system/user.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/api/system/user.js", "mtime": 1663466458000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["request", "praseStrEmpty", "listUser", "query", "url", "method", "params", "getUser", "userId", "addUser", "data", "updateUser", "<PERSON><PERSON><PERSON>", "exportUser", "timeout", "printUser", "resetUserPwd", "password", "changeUserStatus", "status", "getUserProfile", "updateUserProfile", "updateUserPwd", "oldPassword", "newPassword", "uploadAvatar", "importTemplate", "register"], "sources": ["/Users/<USER>/develop/code/project/haijia/adminweb/src/api/system/user.js"], "sourcesContent": ["import request from '@/utils/request'\nimport { praseStrEmpty } from \"@/utils/ruoyi\";\n\n// 查询用户列表\nexport function listUser(query) {\n  return request({\n    url: '/system/user/list',\n    method: 'get',\n    params: query\n  })\n}\n\n// 查询用户详细\nexport function getUser(userId) {\n  return request({\n    url: '/system/user/' + praseStrEmpty(userId),\n    method: 'get'\n  })\n}\n\n// 新增用户\nexport function addUser(data) {\n  return request({\n    url: '/system/user',\n    method: 'post',\n    data: data\n  })\n}\n\n// 修改用户\nexport function updateUser(data) {\n  return request({\n    url: '/system/user',\n    method: 'put',\n    data: data\n  })\n}\n\n// 删除用户\nexport function delUser(userId) {\n  return request({\n    url: '/system/user/' + userId,\n    method: 'delete'\n  })\n}\n\n// 导出用户\nexport function exportUser(query) {\n  return request({\n    url: '/system/user/export',\n    method: 'get',\n    params: query,\n    timeout: 10 * 60 * 1000,\n  })\n}\n\n// 打印用户\nexport function printUser(query) {\n  return request({\n    url: '/system/user/print',\n    method: 'get',\n    params: query\n  })\n}\n\n// 用户密码重置\nexport function resetUserPwd(userId, password) {\n  const data = {\n    userId,\n    password\n  }\n  return request({\n    url: '/system/user/resetPwd',\n    method: 'put',\n    data: data\n  })\n}\n\n// 用户状态修改\nexport function changeUserStatus(userId, status) {\n  const data = {\n    userId,\n    status\n  }\n  return request({\n    url: '/system/user/changeStatus',\n    method: 'put',\n    data: data\n  })\n}\n\n// 查询用户个人信息\nexport function getUserProfile() {\n  return request({\n    url: '/system/user/profile',\n    method: 'get'\n  })\n}\n\n// 修改用户个人信息\nexport function updateUserProfile(data) {\n  return request({\n    url: '/system/user/profile',\n    method: 'put',\n    data: data\n  })\n}\n\n// 用户密码重置\nexport function updateUserPwd(oldPassword, newPassword) {\n  const data = {\n    oldPassword,\n    newPassword\n  }\n  return request({\n    url: '/system/user/profile/updatePwd',\n    method: 'put',\n    params: data\n  })\n}\n\n// 用户头像上传\nexport function uploadAvatar(data) {\n  return request({\n    url: '/system/user/profile/avatar',\n    method: 'post',\n    data: data\n  })\n}\n\n// 下载用户导入模板\nexport function importTemplate() {\n  return request({\n    url: '/system/user/importTemplate',\n    method: 'get'\n  })\n}\n\n// 新增用户\nexport function register(data) {\n  return request({\n    url: '/system/user/register',\n    method: 'post',\n    data: data,\n  })\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;AACrC,SAASC,aAAa,QAAQ,eAAe;;AAE7C;AACA,OAAO,SAASC,QAAQA,CAACC,KAAK,EAAE;EAC9B,OAAOH,OAAO,CAAC;IACbI,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEH;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASI,OAAOA,CAACC,MAAM,EAAE;EAC9B,OAAOR,OAAO,CAAC;IACbI,GAAG,EAAE,eAAe,GAAGH,aAAa,CAACO,MAAM,CAAC;IAC5CH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASI,OAAOA,CAACC,IAAI,EAAE;EAC5B,OAAOV,OAAO,CAAC;IACbI,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASC,UAAUA,CAACD,IAAI,EAAE;EAC/B,OAAOV,OAAO,CAAC;IACbI,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASE,OAAOA,CAACJ,MAAM,EAAE;EAC9B,OAAOR,OAAO,CAAC;IACbI,GAAG,EAAE,eAAe,GAAGI,MAAM;IAC7BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASQ,UAAUA,CAACV,KAAK,EAAE;EAChC,OAAOH,OAAO,CAAC;IACbI,GAAG,EAAE,qBAAqB;IAC1BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEH,KAAK;IACbW,OAAO,EAAE,EAAE,GAAG,EAAE,GAAG;EACrB,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASC,SAASA,CAACZ,KAAK,EAAE;EAC/B,OAAOH,OAAO,CAAC;IACbI,GAAG,EAAE,oBAAoB;IACzBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEH;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASa,YAAYA,CAACR,MAAM,EAAES,QAAQ,EAAE;EAC7C,IAAMP,IAAI,GAAG;IACXF,MAAM,EAANA,MAAM;IACNS,QAAQ,EAARA;EACF,CAAC;EACD,OAAOjB,OAAO,CAAC;IACbI,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASQ,gBAAgBA,CAACV,MAAM,EAAEW,MAAM,EAAE;EAC/C,IAAMT,IAAI,GAAG;IACXF,MAAM,EAANA,MAAM;IACNW,MAAM,EAANA;EACF,CAAC;EACD,OAAOnB,OAAO,CAAC;IACbI,GAAG,EAAE,2BAA2B;IAChCC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASU,cAAcA,CAAA,EAAG;EAC/B,OAAOpB,OAAO,CAAC;IACbI,GAAG,EAAE,sBAAsB;IAC3BC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASgB,iBAAiBA,CAACX,IAAI,EAAE;EACtC,OAAOV,OAAO,CAAC;IACbI,GAAG,EAAE,sBAAsB;IAC3BC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASY,aAAaA,CAACC,WAAW,EAAEC,WAAW,EAAE;EACtD,IAAMd,IAAI,GAAG;IACXa,WAAW,EAAXA,WAAW;IACXC,WAAW,EAAXA;EACF,CAAC;EACD,OAAOxB,OAAO,CAAC;IACbI,GAAG,EAAE,gCAAgC;IACrCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEI;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASe,YAAYA,CAACf,IAAI,EAAE;EACjC,OAAOV,OAAO,CAAC;IACbI,GAAG,EAAE,6BAA6B;IAClCC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASgB,cAAcA,CAAA,EAAG;EAC/B,OAAO1B,OAAO,CAAC;IACbI,GAAG,EAAE,6BAA6B;IAClCC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASsB,QAAQA,CAACjB,IAAI,EAAE;EAC7B,OAAOV,OAAO,CAAC;IACbI,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ", "ignoreList": []}]}