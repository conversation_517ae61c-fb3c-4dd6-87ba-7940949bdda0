{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/tool/build/ResourceDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/tool/build/ResourceDialog.vue", "mtime": 1641277326000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGRlZXBDbG9uZSB9IGZyb20gJ0AvdXRpbHMvaW5kZXgnOwpleHBvcnQgZGVmYXVsdCB7CiAgY29tcG9uZW50czoge30sCiAgaW5oZXJpdEF0dHJzOiBmYWxzZSwKICBwcm9wczogWydvcmlnaW5SZXNvdXJjZSddLAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICByZXNvdXJjZXM6IG51bGwKICAgIH07CiAgfSwKICBjb21wdXRlZDoge30sCiAgd2F0Y2g6IHt9LAogIGNyZWF0ZWQ6IGZ1bmN0aW9uIGNyZWF0ZWQoKSB7fSwKICBtb3VudGVkOiBmdW5jdGlvbiBtb3VudGVkKCkge30sCiAgbWV0aG9kczogewogICAgb25PcGVuOiBmdW5jdGlvbiBvbk9wZW4oKSB7CiAgICAgIHRoaXMucmVzb3VyY2VzID0gdGhpcy5vcmlnaW5SZXNvdXJjZS5sZW5ndGggPyBkZWVwQ2xvbmUodGhpcy5vcmlnaW5SZXNvdXJjZSkgOiBbJyddOwogICAgfSwKICAgIG9uQ2xvc2U6IGZ1bmN0aW9uIG9uQ2xvc2UoKSB7fSwKICAgIGNsb3NlOiBmdW5jdGlvbiBjbG9zZSgpIHsKICAgICAgdGhpcy4kZW1pdCgndXBkYXRlOnZpc2libGUnLCBmYWxzZSk7CiAgICB9LAogICAgaGFuZGVsQ29uZmlybTogZnVuY3Rpb24gaGFuZGVsQ29uZmlybSgpIHsKICAgICAgdmFyIHJlc3VsdHMgPSB0aGlzLnJlc291cmNlcy5maWx0ZXIoZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICByZXR1cm4gISFpdGVtOwogICAgICB9KSB8fCBbXTsKICAgICAgdGhpcy4kZW1pdCgnc2F2ZScsIHJlc3VsdHMpOwogICAgICB0aGlzLmNsb3NlKCk7CiAgICAgIGlmIChyZXN1bHRzLmxlbmd0aCkgewogICAgICAgIHRoaXMucmVzb3VyY2VzID0gcmVzdWx0czsKICAgICAgfQogICAgfSwKICAgIGRlbGV0ZU9uZTogZnVuY3Rpb24gZGVsZXRlT25lKGluZGV4KSB7CiAgICAgIHRoaXMucmVzb3VyY2VzLnNwbGljZShpbmRleCwgMSk7CiAgICB9LAogICAgYWRkT25lOiBmdW5jdGlvbiBhZGRPbmUodXJsKSB7CiAgICAgIGlmICh0aGlzLnJlc291cmNlcy5pbmRleE9mKHVybCkgPiAtMSkgewogICAgICAgIHRoaXMuJG1lc3NhZ2UoJ+i1hOa6kOW3suWtmOWcqCcpOwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMucmVzb3VyY2VzLnB1c2godXJsKTsKICAgICAgfQogICAgfQogIH0KfTs="}, {"version": 3, "names": ["deepClone", "components", "inheritAttrs", "props", "data", "resources", "computed", "watch", "created", "mounted", "methods", "onOpen", "originResource", "length", "onClose", "close", "$emit", "handelConfirm", "results", "filter", "item", "deleteOne", "index", "splice", "addOne", "url", "indexOf", "$message", "push"], "sources": ["src/views/tool/build/ResourceDialog.vue"], "sourcesContent": ["<template>\n  <div>\n    <el-dialog\n      v-bind=\"$attrs\"\n      title=\"外部资源引用\"\n      width=\"600px\"\n      :close-on-click-modal=\"false\"\n      v-on=\"$listeners\"\n      @open=\"onOpen\"\n      @close=\"onClose\"\n    >\n      <el-input\n        v-for=\"(item, index) in resources\"\n        :key=\"index\"\n        v-model=\"resources[index]\"\n        class=\"url-item\"\n        placeholder=\"请输入 css 或 js 资源路径\"\n        prefix-icon=\"el-icon-link\"\n        clearable\n      >\n        <el-button\n          slot=\"append\"\n          icon=\"el-icon-delete\"\n          @click=\"deleteOne(index)\"\n        />\n      </el-input>\n      <el-button-group class=\"add-item\">\n        <el-button\n          plain\n          @click=\"addOne('https://lib.baomitu.com/jquery/1.8.3/jquery.min.js')\"\n        >\n          jQuery1.8.3\n        </el-button>\n        <el-button\n          plain\n          @click=\"addOne('https://unpkg.com/http-vue-loader')\"\n        >\n          http-vue-loader\n        </el-button>\n        <el-button\n          icon=\"el-icon-circle-plus-outline\"\n          plain\n          @click=\"addOne('')\"\n        >\n          添加其他\n        </el-button>\n      </el-button-group>\n      <div slot=\"footer\">\n        <el-button @click=\"close\">\n          取消\n        </el-button>\n        <el-button\n          type=\"primary\"\n          @click=\"handelConfirm\"\n        >\n          确定\n        </el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n<script>\nimport { deepClone } from '@/utils/index'\n\nexport default {\n  components: {},\n  inheritAttrs: false,\n  props: ['originResource'],\n  data() {\n    return {\n      resources: null\n    }\n  },\n  computed: {},\n  watch: {},\n  created() {},\n  mounted() {},\n  methods: {\n    onOpen() {\n      this.resources = this.originResource.length ? deepClone(this.originResource) : ['']\n    },\n    onClose() {\n    },\n    close() {\n      this.$emit('update:visible', false)\n    },\n    handelConfirm() {\n      const results = this.resources.filter(item => !!item) || []\n      this.$emit('save', results)\n      this.close()\n      if (results.length) {\n        this.resources = results\n      }\n    },\n    deleteOne(index) {\n      this.resources.splice(index, 1)\n    },\n    addOne(url) {\n      if (this.resources.indexOf(url) > -1) {\n        this.$message('资源已存在')\n      } else {\n        this.resources.push(url)\n      }\n    }\n  }\n}\n\n</script>\n<style lang=\"scss\" scoped>\n.add-item{\n  margin-top: 8px;\n}\n.url-item{\n  margin-bottom: 12px;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8DA,SAAAA,SAAA;AAEA;EACAC,UAAA;EACAC,YAAA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA;IACA;EACA;EACAC,QAAA;EACAC,KAAA;EACAC,OAAA,WAAAA,QAAA;EACAC,OAAA,WAAAA,QAAA;EACAC,OAAA;IACAC,MAAA,WAAAA,OAAA;MACA,KAAAN,SAAA,QAAAO,cAAA,CAAAC,MAAA,GAAAb,SAAA,MAAAY,cAAA;IACA;IACAE,OAAA,WAAAA,QAAA,GACA;IACAC,KAAA,WAAAA,MAAA;MACA,KAAAC,KAAA;IACA;IACAC,aAAA,WAAAA,cAAA;MACA,IAAAC,OAAA,QAAAb,SAAA,CAAAc,MAAA,WAAAC,IAAA;QAAA,SAAAA,IAAA;MAAA;MACA,KAAAJ,KAAA,SAAAE,OAAA;MACA,KAAAH,KAAA;MACA,IAAAG,OAAA,CAAAL,MAAA;QACA,KAAAR,SAAA,GAAAa,OAAA;MACA;IACA;IACAG,SAAA,WAAAA,UAAAC,KAAA;MACA,KAAAjB,SAAA,CAAAkB,MAAA,CAAAD,KAAA;IACA;IACAE,MAAA,WAAAA,OAAAC,GAAA;MACA,SAAApB,SAAA,CAAAqB,OAAA,CAAAD,GAAA;QACA,KAAAE,QAAA;MACA;QACA,KAAAtB,SAAA,CAAAuB,IAAA,CAAAH,GAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}