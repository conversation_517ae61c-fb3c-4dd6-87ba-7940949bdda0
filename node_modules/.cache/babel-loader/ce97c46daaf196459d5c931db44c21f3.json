{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/components/parser/Parser.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/components/parser/Parser.vue", "mtime": 1650124704000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["deepClone", "render", "ruleTrigger", "layouts", "colFormItem", "h", "scheme", "config", "__config__", "listeners", "buildListeners", "call", "labelWidth", "concat", "showLabel", "span", "__vModel__", "label", "_mergeJSXProps", "rowFormItem", "child", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "apply", "arguments", "type", "justify", "align", "gutter", "renderFrom", "formConfCopy", "_mergeJSXProps2", "size", "labelPosition", "disabled", "formRef", "model", "formModel", "formRules", "renderFormItem", "fields", "formBtns", "submitForm", "resetForm", "elementList", "_this", "map", "layout", "Error", "Array", "isArray", "children", "setValue", "event", "$set", "formConf", "_this2", "methods", "__methods__", "Object", "keys", "for<PERSON>ach", "key", "input", "components", "props", "required", "data", "_defineProperty", "initFormData", "buildRules", "componentList", "formData", "_this3", "cur", "defaultValue", "rules", "_this4", "regList", "message", "placeholder", "undefined", "push", "item", "pattern", "eval", "trigger", "tag", "$refs", "resetFields", "_this5", "validate", "valid", "params", "valData", "$emit", "getData"], "sources": ["src/components/parser/Parser.vue"], "sourcesContent": ["<script>\nimport { deepClone } from '@/utils/index'\nimport render from '@/components/render/render.js'\n\nconst ruleTrigger = {\n  'el-input': 'blur',\n  'el-input-number': 'blur',\n  'el-select': 'change',\n  'el-radio-group': 'change',\n  'el-checkbox-group': 'change',\n  'el-cascader': 'change',\n  'el-time-picker': 'change',\n  'el-date-picker': 'change',\n  'el-rate': 'change'\n}\n\nconst layouts = {\n  colFormItem(h, scheme) {\n    const config = scheme.__config__\n    const listeners = buildListeners.call(this, scheme)\n\n    let labelWidth = config.labelWidth ? `${config.labelWidth}px` : null\n    if (config.showLabel === false) labelWidth = '0'\n    return (\n      <el-col span={config.span}>\n        <el-form-item label-width={labelWidth} prop={scheme.__vModel__}\n          label={config.showLabel ? config.label : ''}>\n          <render conf={scheme} on={listeners} />\n        </el-form-item>\n      </el-col>\n    )\n  },\n  rowFormItem(h, scheme) {\n    let child = renderChildren.apply(this, arguments)\n    if (scheme.type === 'flex') {\n      child = <el-row type={scheme.type} justify={scheme.justify} align={scheme.align}>\n              {child}\n            </el-row>\n    }\n    return (\n      <el-col span={scheme.span}>\n        <el-row gutter={scheme.gutter}>\n          {child}\n        </el-row>\n      </el-col>\n    )\n  }\n}\n\nfunction renderFrom(h) {\n  const { formConfCopy } = this\n\n  return (\n    <el-row gutter={formConfCopy.gutter}>\n      <el-form\n        size={formConfCopy.size}\n        label-position={formConfCopy.labelPosition}\n        disabled={formConfCopy.disabled}\n        label-width={`${formConfCopy.labelWidth}px`}\n        ref={formConfCopy.formRef}\n        // model不能直接赋值 https://github.com/vuejs/jsx/issues/49#issuecomment-472013664\n        props={{ model: this[formConfCopy.formModel] }}\n        rules={this[formConfCopy.formRules]}\n      >\n        {renderFormItem.call(this, h, formConfCopy.fields)}\n        {formConfCopy.formBtns && formBtns.call(this, h)}\n      </el-form>\n    </el-row>\n  )\n}\n\nfunction formBtns(h) {\n  return <el-col>\n    <el-form-item size=\"large\">\n      <el-button type=\"primary\" onClick={this.submitForm}>提交</el-button>\n      <el-button onClick={this.resetForm}>重置</el-button>\n    </el-form-item>\n  </el-col>\n}\n\nfunction renderFormItem(h, elementList) {\n  return elementList.map(scheme => {\n    const config = scheme.__config__\n    const layout = layouts[config.layout]\n\n    if (layout) {\n      return layout.call(this, h, scheme)\n    }\n    throw new Error(`没有与${config.layout}匹配的layout`)\n  })\n}\n\nfunction renderChildren(h, scheme) {\n  const config = scheme.__config__\n  if (!Array.isArray(config.children)) return null\n  return renderFormItem.call(this, h, config.children)\n}\n\nfunction setValue(event, config, scheme) {\n  this.$set(config, 'defaultValue', event)\n  this.$set(this[this.formConf.formModel], scheme.__vModel__, event)\n}\n\nfunction buildListeners(scheme) {\n  const config = scheme.__config__\n  const methods = this.formConf.__methods__ || {}\n  const listeners = {}\n\n  // 给__methods__中的方法绑定this和event\n  Object.keys(methods).forEach(key => {\n    listeners[key] = event => methods[key].call(this, event)\n  })\n  // 响应 render.js 中的 vModel $emit('input', val)\n  listeners.input = event => setValue.call(this, event, config, scheme)\n\n  return listeners\n}\n\nexport default {\n  components: {\n    render\n  },\n  props: {\n    formConf: {\n      type: Object,\n      required: true\n    }\n  },\n  data() {\n    const data = {\n      formConfCopy: deepClone(this.formConf),\n      [this.formConf.formModel]: {},\n      [this.formConf.formRules]: {}\n    }\n    this.initFormData(data.formConfCopy.fields, data[this.formConf.formModel])\n    this.buildRules(data.formConfCopy.fields, data[this.formConf.formRules])\n    return data\n  },\n  methods: {\n    initFormData(componentList, formData) {\n      componentList.forEach(cur => {\n        const config = cur.__config__\n        if (cur.__vModel__) formData[cur.__vModel__] = config.defaultValue\n        if (config.children) this.initFormData(config.children, formData)\n      })\n    },\n    buildRules(componentList, rules) {\n      componentList.forEach(cur => {\n        const config = cur.__config__\n        if (Array.isArray(config.regList)) {\n          if (config.required) {\n            const required = { required: config.required, message: cur.placeholder }\n            if (Array.isArray(config.defaultValue)) {\n              required.type = 'array'\n              required.message = `请至少选择一个${config.label}`\n            }\n            required.message === undefined && (required.message = `${config.label}不能为空`)\n            config.regList.push(required)\n          }\n          rules[cur.__vModel__] = config.regList.map(item => {\n            item.pattern && (item.pattern = eval(item.pattern))\n            item.trigger = ruleTrigger && ruleTrigger[config.tag]\n            return item\n          })\n        }\n        if (config.children) this.buildRules(config.children, rules)\n      })\n    },\n    resetForm() {\n      this.formConfCopy = deepClone(this.formConf)\n      this.$refs[this.formConf.formRef].resetFields()\n    },\n    submitForm() {\n      this.$refs[this.formConf.formRef].validate(valid => {\n        if (!valid) return false\n        // 触发sumit事件\n        // this.$emit('submit', this[this.formConf.formModel])\n        const params = {\n          formData: this.formConfCopy,\n          valData: this[this.formConf.formModel]\n        }\n        this.$emit('submit', params)\n        return true\n      })\n    },\n    // 传值给父组件\n    getData(){\n      this.$emit('getData', this[this.formConf.formModel])\n      // this.$emit('getData',this.formConfCopy)\n    }\n  },\n  render(h) {\n    return renderFrom.call(this, h)\n  }\n}\n</script>\n"], "mappings": ";;;;;AACA,SAAAA,SAAA;AACA,OAAAC,MAAA;AAEA,IAAAC,WAAA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACA;AAEA,IAAAC,OAAA;EACAC,WAAA,WAAAA,YAAAC,CAAA,EAAAC,MAAA;IACA,IAAAC,MAAA,GAAAD,MAAA,CAAAE,UAAA;IACA,IAAAC,SAAA,GAAAC,cAAA,CAAAC,IAAA,OAAAL,MAAA;IAEA,IAAAM,UAAA,GAAAL,MAAA,CAAAK,UAAA,MAAAC,MAAA,CAAAN,MAAA,CAAAK,UAAA;IACA,IAAAL,MAAA,CAAAO,SAAA,YAAAF,UAAA;IACA,OAAAP,CAAA;MAAA;QAAA,QACAE,MAAA,CAAAQ;MAAA;IAAA,IAAAV,CAAA;MAAA;QAAA,eACAO,UAAA;QAAA,QAAAN,MAAA,CAAAU,UAAA;QAAA,SACAT,MAAA,CAAAO,SAAA,GAAAP,MAAA,CAAAU,KAAA;MAAA;IAAA,IAAAZ,CAAA,CAAAJ,MAAA,EAAAiB,cAAA;MAAA;QAAA,QACAZ;MAAA;IAAA;MAAA,MAAAG;IAAA;EAIA;EACAU,WAAA,WAAAA,YAAAd,CAAA,EAAAC,MAAA;IACA,IAAAc,KAAA,GAAAC,cAAA,CAAAC,KAAA,OAAAC,SAAA;IACA,IAAAjB,MAAA,CAAAkB,IAAA;MACAJ,KAAA,GAAAf,CAAA;QAAA;UAAA,QAAAC,MAAA,CAAAkB,IAAA;UAAA,WAAAlB,MAAA,CAAAmB,OAAA;UAAA,SAAAnB,MAAA,CAAAoB;QAAA;MAAA,IACAN,KAAA,EACA;IACA;IACA,OAAAf,CAAA;MAAA;QAAA,QACAC,MAAA,CAAAS;MAAA;IAAA,IAAAV,CAAA;MAAA;QAAA,UACAC,MAAA,CAAAqB;MAAA;IAAA,IACAP,KAAA;EAIA;AACA;AAEA,SAAAQ,WAAAvB,CAAA;EACA,IAAAwB,YAAA,QAAAA,YAAA;EAEA,OAAAxB,CAAA;IAAA;MAAA,UACAwB,YAAA,CAAAF;IAAA;EAAA,IAAAtB,CAAA,YAAAyB,eAAA;IAAA;MAAA,QAEAD,YAAA,CAAAE,IAAA;MAAA,kBACAF,YAAA,CAAAG,aAAA;MAAA,YACAH,YAAA,CAAAI,QAAA;MAAA,kBAAApB,MAAA,CACAgB,YAAA,CAAAjB,UAAA;IAAA;IAAA,OACAiB,YAAA,CAAAK;EAAA;IAAA,SAEA;MAAAC,KAAA,OAAAN,YAAA,CAAAO,SAAA;IAAA;EAAA;IAAA;MAAA,SACA,KAAAP,YAAA,CAAAQ,SAAA;IAAA;EAAA,MAEAC,cAAA,CAAA3B,IAAA,OAAAN,CAAA,EAAAwB,YAAA,CAAAU,MAAA,GACAV,YAAA,CAAAW,QAAA,IAAAA,QAAA,CAAA7B,IAAA,OAAAN,CAAA;AAIA;AAEA,SAAAmC,SAAAnC,CAAA;EACA,OAAAA,CAAA,YAAAA,CAAA;IAAA;MAAA,QACA;IAAA;EAAA,IAAAA,CAAA;IAAA;MAAA,QACA;IAAA;IAAA;MAAA,cAAAoC;IAAA;EAAA,sBAAApC,CAAA;IAAA;MAAA,SACA,KAAAqC;IAAA;EAAA;AAGA;AAEA,SAAAJ,eAAAjC,CAAA,EAAAsC,WAAA;EAAA,IAAAC,KAAA;EACA,OAAAD,WAAA,CAAAE,GAAA,WAAAvC,MAAA;IACA,IAAAC,MAAA,GAAAD,MAAA,CAAAE,UAAA;IACA,IAAAsC,MAAA,GAAA3C,OAAA,CAAAI,MAAA,CAAAuC,MAAA;IAEA,IAAAA,MAAA;MACA,OAAAA,MAAA,CAAAnC,IAAA,CAAAiC,KAAA,EAAAvC,CAAA,EAAAC,MAAA;IACA;IACA,UAAAyC,KAAA,sBAAAlC,MAAA,CAAAN,MAAA,CAAAuC,MAAA;EACA;AACA;AAEA,SAAAzB,eAAAhB,CAAA,EAAAC,MAAA;EACA,IAAAC,MAAA,GAAAD,MAAA,CAAAE,UAAA;EACA,KAAAwC,KAAA,CAAAC,OAAA,CAAA1C,MAAA,CAAA2C,QAAA;EACA,OAAAZ,cAAA,CAAA3B,IAAA,OAAAN,CAAA,EAAAE,MAAA,CAAA2C,QAAA;AACA;AAEA,SAAAC,SAAAC,KAAA,EAAA7C,MAAA,EAAAD,MAAA;EACA,KAAA+C,IAAA,CAAA9C,MAAA,kBAAA6C,KAAA;EACA,KAAAC,IAAA,WAAAC,QAAA,CAAAlB,SAAA,GAAA9B,MAAA,CAAAU,UAAA,EAAAoC,KAAA;AACA;AAEA,SAAA1C,eAAAJ,MAAA;EAAA,IAAAiD,MAAA;EACA,IAAAhD,MAAA,GAAAD,MAAA,CAAAE,UAAA;EACA,IAAAgD,OAAA,QAAAF,QAAA,CAAAG,WAAA;EACA,IAAAhD,SAAA;;EAEA;EACAiD,MAAA,CAAAC,IAAA,CAAAH,OAAA,EAAAI,OAAA,WAAAC,GAAA;IACApD,SAAA,CAAAoD,GAAA,cAAAT,KAAA;MAAA,OAAAI,OAAA,CAAAK,GAAA,EAAAlD,IAAA,CAAA4C,MAAA,EAAAH,KAAA;IAAA;EACA;EACA;EACA3C,SAAA,CAAAqD,KAAA,aAAAV,KAAA;IAAA,OAAAD,QAAA,CAAAxC,IAAA,CAAA4C,MAAA,EAAAH,KAAA,EAAA7C,MAAA,EAAAD,MAAA;EAAA;EAEA,OAAAG,SAAA;AACA;AAEA;EACAsD,UAAA;IACA9D,MAAA,EAAAA;EACA;EACA+D,KAAA;IACAV,QAAA;MACA9B,IAAA,EAAAkC,MAAA;MACAO,QAAA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA,IAAAA,IAAA,GAAAC,eAAA,CAAAA,eAAA;MACAtC,YAAA,EAAA7B,SAAA,MAAAsD,QAAA;IAAA,GACA,KAAAA,QAAA,CAAAlB,SAAA,OACA,KAAAkB,QAAA,CAAAjB,SAAA,KACA;IACA,KAAA+B,YAAA,CAAAF,IAAA,CAAArC,YAAA,CAAAU,MAAA,EAAA2B,IAAA,MAAAZ,QAAA,CAAAlB,SAAA;IACA,KAAAiC,UAAA,CAAAH,IAAA,CAAArC,YAAA,CAAAU,MAAA,EAAA2B,IAAA,MAAAZ,QAAA,CAAAjB,SAAA;IACA,OAAA6B,IAAA;EACA;EACAV,OAAA;IACAY,YAAA,WAAAA,aAAAE,aAAA,EAAAC,QAAA;MAAA,IAAAC,MAAA;MACAF,aAAA,CAAAV,OAAA,WAAAa,GAAA;QACA,IAAAlE,MAAA,GAAAkE,GAAA,CAAAjE,UAAA;QACA,IAAAiE,GAAA,CAAAzD,UAAA,EAAAuD,QAAA,CAAAE,GAAA,CAAAzD,UAAA,IAAAT,MAAA,CAAAmE,YAAA;QACA,IAAAnE,MAAA,CAAA2C,QAAA,EAAAsB,MAAA,CAAAJ,YAAA,CAAA7D,MAAA,CAAA2C,QAAA,EAAAqB,QAAA;MACA;IACA;IACAF,UAAA,WAAAA,WAAAC,aAAA,EAAAK,KAAA;MAAA,IAAAC,MAAA;MACAN,aAAA,CAAAV,OAAA,WAAAa,GAAA;QACA,IAAAlE,MAAA,GAAAkE,GAAA,CAAAjE,UAAA;QACA,IAAAwC,KAAA,CAAAC,OAAA,CAAA1C,MAAA,CAAAsE,OAAA;UACA,IAAAtE,MAAA,CAAA0D,QAAA;YACA,IAAAA,QAAA;cAAAA,QAAA,EAAA1D,MAAA,CAAA0D,QAAA;cAAAa,OAAA,EAAAL,GAAA,CAAAM;YAAA;YACA,IAAA/B,KAAA,CAAAC,OAAA,CAAA1C,MAAA,CAAAmE,YAAA;cACAT,QAAA,CAAAzC,IAAA;cACAyC,QAAA,CAAAa,OAAA,gDAAAjE,MAAA,CAAAN,MAAA,CAAAU,KAAA;YACA;YACAgD,QAAA,CAAAa,OAAA,KAAAE,SAAA,KAAAf,QAAA,CAAAa,OAAA,MAAAjE,MAAA,CAAAN,MAAA,CAAAU,KAAA;YACAV,MAAA,CAAAsE,OAAA,CAAAI,IAAA,CAAAhB,QAAA;UACA;UACAU,KAAA,CAAAF,GAAA,CAAAzD,UAAA,IAAAT,MAAA,CAAAsE,OAAA,CAAAhC,GAAA,WAAAqC,IAAA;YACAA,IAAA,CAAAC,OAAA,KAAAD,IAAA,CAAAC,OAAA,GAAAC,IAAA,CAAAF,IAAA,CAAAC,OAAA;YACAD,IAAA,CAAAG,OAAA,GAAAnF,WAAA,IAAAA,WAAA,CAAAK,MAAA,CAAA+E,GAAA;YACA,OAAAJ,IAAA;UACA;QACA;QACA,IAAA3E,MAAA,CAAA2C,QAAA,EAAA0B,MAAA,CAAAP,UAAA,CAAA9D,MAAA,CAAA2C,QAAA,EAAAyB,KAAA;MACA;IACA;IACAjC,SAAA,WAAAA,UAAA;MACA,KAAAb,YAAA,GAAA7B,SAAA,MAAAsD,QAAA;MACA,KAAAiC,KAAA,MAAAjC,QAAA,CAAApB,OAAA,EAAAsD,WAAA;IACA;IACA/C,UAAA,WAAAA,WAAA;MAAA,IAAAgD,MAAA;MACA,KAAAF,KAAA,MAAAjC,QAAA,CAAApB,OAAA,EAAAwD,QAAA,WAAAC,KAAA;QACA,KAAAA,KAAA;QACA;QACA;QACA,IAAAC,MAAA;UACArB,QAAA,EAAAkB,MAAA,CAAA5D,YAAA;UACAgE,OAAA,EAAAJ,MAAA,CAAAA,MAAA,CAAAnC,QAAA,CAAAlB,SAAA;QACA;QACAqD,MAAA,CAAAK,KAAA,WAAAF,MAAA;QACA;MACA;IACA;IACA;IACAG,OAAA,WAAAA,QAAA;MACA,KAAAD,KAAA,sBAAAxC,QAAA,CAAAlB,SAAA;MACA;IACA;EACA;EACAnC,MAAA,WAAAA,OAAAI,CAAA;IACA,OAAAuB,UAAA,CAAAjB,IAAA,OAAAN,CAAA;EACA;AACA", "ignoreList": []}]}