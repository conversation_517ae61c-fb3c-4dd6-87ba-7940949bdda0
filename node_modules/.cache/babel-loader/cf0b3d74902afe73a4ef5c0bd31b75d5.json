{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Editor/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Editor/index.vue", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js", "mtime": 1751171659043}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}