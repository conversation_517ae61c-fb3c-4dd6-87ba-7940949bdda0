{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Notice/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Notice/index.vue", "mtime": 1655640984000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js", "mtime": 1751171659043}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IG5Ob3RpY2UgfSBmcm9tICdAL2FwaS9zeXN0ZW0vbm90aWNlJzsKZXhwb3J0IGRlZmF1bHQgewogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBoYXNOb3RpY2U6IGZhbHNlLAogICAgICB0aW1lcjogJycsCiAgICAgIGNvdW50OiAwCiAgICB9OwogIH0sCiAgbW91bnRlZDogZnVuY3Rpb24gbW91bnRlZCgpIHsKICAgIC8v5a6a5pe25p+lCiAgICB0aGlzLnRpbWVyID0gc2V0SW50ZXJ2YWwodGhpcy5jaGVja05vdGljZSwgNjAgKiAxMDAwICogMSk7CiAgfSwKICBiZWZvcmVEZXN0cm95OiBmdW5jdGlvbiBiZWZvcmVEZXN0cm95KCkgewogICAgY2xlYXJJbnRlcnZhbCh0aGlzLnRpbWVyKTsKICB9LAogIGNyZWF0ZWQ6IGZ1bmN0aW9uIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmNoZWNrTm90aWNlKCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICBjbGljazogZnVuY3Rpb24gY2xpY2soKSB7CiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHsKICAgICAgICBwYXRoOiAnL3VzZXIvbm90aWNlJwogICAgICB9KTsKICAgIH0sCiAgICBjaGVja05vdGljZTogZnVuY3Rpb24gY2hlY2tOb3RpY2UoKSB7CiAgICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICAgIGlmICh0aGlzLmhhc05vdGljZSkgewogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICBuTm90aWNlKCkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICBfdGhpcy5oYXNOb3RpY2UgPSByZXNwb25zZS5kYXRhID4gMDsKICAgICAgICBfdGhpcy5jb3VudCA9IHJlc3BvbnNlLmRhdGE7CiAgICAgIH0pOwogICAgfQogIH0KfTs="}, null]}