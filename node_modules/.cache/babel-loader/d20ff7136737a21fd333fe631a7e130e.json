{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/tool/build/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/tool/build/index.vue", "mtime": 1641277326000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["draggable", "debounce", "saveAs", "ClipboardJS", "render", "FormDrawer", "JsonDrawer", "RightPanel", "inputComponents", "selectComponents", "layoutComponents", "formConf", "exportDefault", "beautifierConf", "isNumberStr", "titleCase", "deepClone", "isObjectObject", "makeUpHtml", "vueTemplate", "vueScript", "cssStyle", "makeUpJs", "makeUpCss", "drawingDefalut", "logo", "CodeTypeDialog", "DraggableItem", "getDrawingList", "saveDrawingList", "getIdGlobal", "saveIdGlobal", "getFormConf", "loadBeautifier", "getForm", "addForm", "updateForm", "beautifier", "emptyActiveData", "style", "autosize", "oldActiveId", "tempActiveData", "drawingListInDB", "formConfInDB", "idGlobal", "components", "data", "labelWidth", "drawingList", "drawingData", "activeId", "formId", "drawerVisible", "formData", "dialogVisible", "jsonDrawerVisible", "generateConf", "showFileName", "activeData", "saveDrawingListDebounce", "saveIdGlobalDebounce", "leftComponents", "title", "list", "formOpen", "formTitle", "form", "formName", "formContent", "remark", "rules", "computed", "watch", "activeData__config__Label", "val", "oldVal", "placeholder", "undefined", "__config__", "tag", "replace", "handler", "immediate", "length", "deep", "mounted", "_this", "that", "Array", "isArray", "activeFormItem", "$route", "query", "then", "res", "JSON", "parse", "fields", "btf", "clipboard", "text", "trigger", "codeStr", "generateCode", "$notify", "message", "type", "on", "e", "$message", "error", "methods", "setObjectValueReduce", "obj", "str<PERSON><PERSON><PERSON>", "arr", "split", "reduce", "pre", "item", "i", "setRespData", "component", "resp", "_component$__config__", "dataPath", "<PERSON><PERSON><PERSON>", "dataConsumer", "respData", "findIndex", "$set", "fetchData", "_this2", "_component$__config__2", "dataType", "method", "url", "setLoading", "$axios", "directives", "t", "find", "d", "name", "value", "currentItem", "onEnd", "from", "to", "addComponent", "clone", "cloneComponent", "push", "origin", "config", "span", "createIdAndKey", "label", "_this3", "concat", "Date", "layout", "__vModel__", "componentName", "children", "map", "childItem", "AssembleFormData", "_objectSpread", "generate", "func", "operationType", "execRun", "execDownload", "blob", "Blob", "fileName", "execCopy", "document", "getElementById", "click", "empty", "_this4", "$confirm", "drawingItemCopy", "drawingItemDelete", "index", "_this5", "splice", "$nextTick", "len", "script", "html", "css", "showJson", "download", "run", "copy", "tagChange", "newTag", "_this6", "tagIcon", "_typeof", "defaultValue", "Object", "keys", "for<PERSON>ach", "key", "updateDrawingList", "_this7", "refresh<PERSON><PERSON>", "handleForm", "stringify", "reset", "resetForm", "cancel", "submitForm", "_this8", "$refs", "validate", "valid", "response", "msgSuccess", "open", "$store", "dispatch", "$router", "go"], "sources": ["src/views/tool/build/index.vue"], "sourcesContent": ["<template>\n  <div class=\"container\">\n    <div class=\"left-board\">\n      <div class=\"logo-wrapper\">\n        <div class=\"logo\">\n          <img :src=\"logo\" alt=\"logo\"> Form Generator\n        </div>\n      </div>\n      <el-scrollbar class=\"left-scrollbar\">\n        <div class=\"components-list\">\n          <div v-for=\"(item, listIndex) in leftComponents\" :key=\"listIndex\">\n            <div class=\"components-title\">\n              <svg-icon icon-class=\"component\" />\n              {{ item.title }}\n            </div>\n            <draggable\n              class=\"components-draggable\"\n              :list=\"item.list\"\n              :group=\"{ name: 'componentsGroup', pull: 'clone', put: false }\"\n              :clone=\"cloneComponent\"\n              draggable=\".components-item\"\n              :sort=\"false\"\n              @end=\"onEnd\"\n            >\n              <div\n                v-for=\"(element, index) in item.list\"\n                :key=\"index\"\n                class=\"components-item\"\n                @click=\"addComponent(element)\"\n              >\n                <div class=\"components-body\">\n                  <svg-icon :icon-class=\"element.__config__.tagIcon\" />\n                  {{ element.__config__.label }}\n                </div>\n              </div>\n            </draggable>\n          </div>\n        </div>\n      </el-scrollbar>\n    </div>\n\n    <div class=\"center-board\">\n      <div class=\"action-bar\">\n        <el-button icon=\"el-icon-plus\" type=\"text\" @click=\"handleForm\">\n          保存\n        </el-button>\n        <el-button icon=\"el-icon-video-play\" type=\"text\" @click=\"run\">\n          运行\n        </el-button>\n        <el-button icon=\"el-icon-view\" type=\"text\" @click=\"showJson\">\n          查看json\n        </el-button>\n        <el-button icon=\"el-icon-download\" type=\"text\" @click=\"download\">\n          导出vue文件\n        </el-button>\n        <el-button class=\"copy-btn-main\" icon=\"el-icon-document-copy\" type=\"text\" @click=\"copy\">\n          复制代码\n        </el-button>\n        <el-button class=\"delete-btn\" icon=\"el-icon-delete\" type=\"text\" @click=\"empty\">\n          清空\n        </el-button>\n      </div>\n      <el-scrollbar class=\"center-scrollbar\">\n        <el-row class=\"center-board-row\" :gutter=\"formConf.gutter\">\n          <el-form\n            :size=\"formConf.size\"\n            :label-position=\"formConf.labelPosition\"\n            :disabled=\"formConf.disabled\"\n            :label-width=\"formConf.labelWidth + 'px'\"\n          >\n            <draggable class=\"drawing-board\" :list=\"drawingList\" :animation=\"340\" group=\"componentsGroup\">\n              <draggable-item\n                v-for=\"(item, index) in drawingList\"\n                :key=\"item.renderKey\"\n                :drawing-list=\"drawingList\"\n                :current-item=\"item\"\n                :index=\"index\"\n                :active-id=\"activeId\"\n                :form-conf=\"formConf\"\n                @activeItem=\"activeFormItem\"\n                @copyItem=\"drawingItemCopy\"\n                @deleteItem=\"drawingItemDelete\"\n              />\n            </draggable>\n            <div v-show=\"!drawingList.length\" class=\"empty-info\">\n              从左侧拖入或点选组件进行表单设计\n            </div>\n          </el-form>\n        </el-row>\n      </el-scrollbar>\n    </div>\n\n    <right-panel\n      :active-data=\"activeData\"\n      :form-conf=\"formConf\"\n      :show-field=\"!!drawingList.length\"\n      @tag-change=\"tagChange\"\n      @fetch-data=\"fetchData\"\n    />\n\n    <form-drawer\n      :visible.sync=\"drawerVisible\"\n      :form-data=\"formData\"\n      size=\"100%\"\n      :generate-conf=\"generateConf\"\n    />\n    <json-drawer\n      size=\"60%\"\n      :visible.sync=\"jsonDrawerVisible\"\n      :json-str=\"JSON.stringify(formData)\"\n      @refresh=\"refreshJson\"\n    />\n    <code-type-dialog\n      :visible.sync=\"dialogVisible\"\n      title=\"选择生成类型\"\n      :show-file-name=\"showFileName\"\n      @confirm=\"generate\"\n    />\n    <input id=\"copyNode\" type=\"hidden\">\n    <!--表单配置详情-->\n    <el-dialog :title=\"formTitle\" :visible.sync=\"formOpen\" width=\"500px\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\n        <el-form-item label=\"表单名称\" prop=\"formName\">\n          <el-input v-model=\"form.formName\" placeholder=\"请输入表单名称\" />\n        </el-form-item>\n        <el-form-item label=\"备注\" prop=\"remark\">\n          <el-input v-model=\"form.remark\" placeholder=\"请输入备注\" />\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport draggable from 'vuedraggable'\nimport { debounce } from 'throttle-debounce'\nimport { saveAs } from 'file-saver'\nimport ClipboardJS from 'clipboard'\nimport render from '@/components/render/render'\nimport FormDrawer from './FormDrawer'\nimport JsonDrawer from './JsonDrawer'\nimport RightPanel from './RightPanel'\nimport {\n  inputComponents, selectComponents, layoutComponents, formConf\n} from '@/utils/generator/config'\nimport {\n  exportDefault, beautifierConf, isNumberStr, titleCase, deepClone, isObjectObject\n} from '@/utils/index'\nimport {\n  makeUpHtml, vueTemplate, vueScript, cssStyle\n} from '@/utils/generator/html'\nimport { makeUpJs } from '@/utils/generator/js'\nimport { makeUpCss } from '@/utils/generator/css'\nimport drawingDefalut from '@/utils/generator/drawingDefalut'\nimport logo from '@/assets/logo/logo.png'\nimport CodeTypeDialog from './CodeTypeDialog'\nimport DraggableItem from './DraggableItem'\nimport {\n  getDrawingList, saveDrawingList, getIdGlobal, saveIdGlobal, getFormConf\n} from '@/utils/db'\nimport loadBeautifier from '@/utils/loadBeautifier'\nimport {getForm, addForm, updateForm} from \"@/api/flowable/form\";\n\nlet beautifier\nconst emptyActiveData = { style: {}, autosize: {} }\nlet oldActiveId\nlet tempActiveData\nconst drawingListInDB = getDrawingList()\nconst formConfInDB = getFormConf()\nconst idGlobal = getIdGlobal()\n\nexport default {\n  components: {\n    draggable,\n    render,\n    FormDrawer,\n    JsonDrawer,\n    RightPanel,\n    CodeTypeDialog,\n    DraggableItem\n  },\n  data() {\n    return {\n      logo,\n      idGlobal,\n      formConf,\n      inputComponents,\n      selectComponents,\n      layoutComponents,\n      labelWidth: 100,\n      drawingList: drawingDefalut,\n      drawingData: {},\n      activeId: drawingDefalut[0].formId,\n      drawerVisible: false,\n      formData: {},\n      dialogVisible: false,\n      jsonDrawerVisible: false,\n      generateConf: null,\n      showFileName: false,\n      activeData: drawingDefalut[0],\n      saveDrawingListDebounce: debounce(340, saveDrawingList),\n      saveIdGlobalDebounce: debounce(340, saveIdGlobal),\n      leftComponents: [\n        {\n          title: '输入型组件',\n          list: inputComponents\n        },\n        {\n          title: '选择型组件',\n          list: selectComponents\n        },\n        {\n          title: '布局型组件',\n          list: layoutComponents\n        }\n      ],\n      formOpen: false,\n      formTitle: \"\",\n      // 表单参数\n      form: {\n        formId: null,\n        formName: null,\n        formContent: null,\n        remark: null\n      },\n      // 表单校验\n      rules: {}\n    }\n  },\n  computed: {\n  },\n  watch: {\n    // eslint-disable-next-line func-names\n    'activeData.__config__.label': function (val, oldVal) {\n      if (\n        this.activeData.placeholder === undefined\n        || !this.activeData.__config__.tag\n        || oldActiveId !== this.activeId\n      ) {\n        return\n      }\n      this.activeData.placeholder = this.activeData.placeholder.replace(oldVal, '') + val\n    },\n    activeId: {\n      handler(val) {\n        oldActiveId = val\n      },\n      immediate: true\n    },\n    drawingList: {\n      handler(val) {\n        this.saveDrawingListDebounce(val)\n        if (val.length === 0) this.idGlobal = 100\n      },\n      deep: true\n    },\n    idGlobal: {\n      handler(val) {\n        this.saveIdGlobalDebounce(val)\n      },\n      immediate: true\n    }\n  },\n\n  mounted() {\n    const that = this;\n    if (Array.isArray(drawingListInDB) && drawingListInDB.length > 0) {\n      that.drawingList = drawingListInDB\n    } else {\n      that.drawingList = drawingDefalut\n    }\n    this.activeFormItem(that.drawingList[0])\n    // // if (formConfInDB) {\n    // //   this.formConf = formConfInDB\n    // // }\n    that.drawingList = [];\n    const formId =  that.$route.query && that.$route.query.formId;\n    if (formId) {\n      getForm(formId).then(res =>{\n        that.formConf = JSON.parse(res.data.formContent);\n        that.drawingList = that.formConf.fields;\n        that.form = res.data;\n      })\n    }else {\n      if (formConfInDB) {\n        that.formConf = formConfInDB\n      }\n    }\n    loadBeautifier(btf => {\n      beautifier = btf\n    })\n    const clipboard = new ClipboardJS('#copyNode', {\n      text: trigger => {\n        const codeStr = this.generateCode()\n        this.$notify({\n          title: '成功',\n          message: '代码已复制到剪切板，可粘贴。',\n          type: 'success'\n        })\n        return codeStr\n      }\n    })\n    clipboard.on('error', e => {\n      this.$message.error('代码复制失败')\n    })\n  },\n  methods: {\n    setObjectValueReduce(obj, strKeys, data) {\n      const arr = strKeys.split('.')\n      arr.reduce((pre, item, i) => {\n        if (arr.length === i + 1) {\n          pre[item] = data\n        } else if (!isObjectObject(pre[item])) {\n          pre[item] = {}\n        }\n        return pre[item]\n      }, obj)\n    },\n    setRespData(component, resp) {\n      const { dataPath, renderKey, dataConsumer } = component.__config__\n      if (!dataPath || !dataConsumer) return\n      const respData = dataPath.split('.').reduce((pre, item) => pre[item], resp)\n\n      // 将请求回来的数据，赋值到指定属性。\n      // 以el-tabel为例，根据Element文档，应该将数据赋值给el-tabel的data属性，所以dataConsumer的值应为'data';\n      // 此时赋值代码可写成 component[dataConsumer] = respData；\n      // 但为支持更深层级的赋值（如：dataConsumer的值为'options.data'）,使用setObjectValueReduce\n      this.setObjectValueReduce(component, dataConsumer, respData)\n      const i = this.drawingList.findIndex(item => item.__config__.renderKey === renderKey)\n      if (i > -1) this.$set(this.drawingList, i, component)\n    },\n    fetchData(component) {\n      const { dataType, method, url } = component.__config__\n      if (dataType === 'dynamic' && method && url) {\n        this.setLoading(component, true)\n        this.$axios({\n          method,\n          url\n        }).then(resp => {\n          this.setLoading(component, false)\n          this.setRespData(component, resp.data)\n        })\n      }\n    },\n    setLoading(component, val) {\n      const { directives } = component\n      if (Array.isArray(directives)) {\n        const t = directives.find(d => d.name === 'loading')\n        if (t) t.value = val\n      }\n    },\n    activeFormItem(currentItem) {\n      this.activeData = currentItem\n      this.activeId = currentItem.__config__.formId\n    },\n    onEnd(obj) {\n      if (obj.from !== obj.to) {\n        this.fetchData(tempActiveData)\n        this.activeData = tempActiveData\n        this.activeId = this.idGlobal\n      }\n    },\n    addComponent(item) {\n      const clone = this.cloneComponent(item)\n      this.fetchData(clone)\n      this.drawingList.push(clone)\n      this.activeFormItem(clone)\n    },\n    cloneComponent(origin) {\n      const clone = deepClone(origin)\n      const config = clone.__config__\n      config.span = this.formConf.span // 生成代码时，会根据span做精简判断\n      this.createIdAndKey(clone)\n      clone.placeholder !== undefined && (clone.placeholder += config.label)\n      tempActiveData = clone\n      return tempActiveData\n    },\n    createIdAndKey(item) {\n      const config = item.__config__\n      config.formId = ++this.idGlobal\n      config.renderKey = `${config.formId}${+new Date()}` // 改变renderKey后可以实现强制更新组件\n      if (config.layout === 'colFormItem') {\n        item.__vModel__ = `field${this.idGlobal}`\n      } else if (config.layout === 'rowFormItem') {\n        config.componentName = `row${this.idGlobal}`\n        !Array.isArray(config.children) && (config.children = [])\n        delete config.label // rowFormItem无需配置label属性\n      }\n      if (Array.isArray(config.children)) {\n        config.children = config.children.map(childItem => this.createIdAndKey(childItem))\n      }\n      return item\n    },\n    AssembleFormData() {\n      this.formData = {\n        fields: deepClone(this.drawingList),\n        ...this.formConf\n      }\n    },\n    generate(data) {\n      const func = this[`exec${titleCase(this.operationType)}`]\n      this.generateConf = data\n      func && func(data)\n    },\n    execRun(data) {\n      this.AssembleFormData()\n      this.drawerVisible = true\n    },\n    execDownload(data) {\n      const codeStr = this.generateCode()\n      const blob = new Blob([codeStr], { type: 'text/plain;charset=utf-8' })\n      saveAs(blob, data.fileName)\n    },\n    execCopy(data) {\n      document.getElementById('copyNode').click()\n    },\n    empty() {\n      this.$confirm('确定要清空所有组件吗？', '提示', { type: 'warning' }).then(\n        () => {\n          this.drawingList = []\n          this.idGlobal = 100\n        }\n      )\n    },\n    drawingItemCopy(item, list) {\n      let clone = deepClone(item)\n      clone = this.createIdAndKey(clone)\n      list.push(clone)\n      this.activeFormItem(clone)\n    },\n    drawingItemDelete(index, list) {\n      list.splice(index, 1)\n      this.$nextTick(() => {\n        const len = this.drawingList.length\n        if (len) {\n          this.activeFormItem(this.drawingList[len - 1])\n        }\n      })\n    },\n    generateCode() {\n      const { type } = this.generateConf\n      this.AssembleFormData()\n      const script = vueScript(makeUpJs(this.formData, type))\n      const html = vueTemplate(makeUpHtml(this.formData, type))\n      const css = cssStyle(makeUpCss(this.formData))\n      return beautifier.html(html + script + css, beautifierConf.html)\n    },\n    showJson() {\n      this.AssembleFormData()\n      this.jsonDrawerVisible = true\n    },\n    download() {\n      this.dialogVisible = true\n      this.showFileName = true\n      this.operationType = 'download'\n    },\n    run() {\n      this.dialogVisible = true\n      this.showFileName = false\n      this.operationType = 'run'\n    },\n    copy() {\n      this.dialogVisible = true\n      this.showFileName = false\n      this.operationType = 'copy'\n    },\n    tagChange(newTag) {\n      newTag = this.cloneComponent(newTag)\n      const config = newTag.__config__\n      newTag.__vModel__ = this.activeData.__vModel__\n      config.formId = this.activeId\n      config.span = this.activeData.__config__.span\n      this.activeData.__config__.tag = config.tag\n      this.activeData.__config__.tagIcon = config.tagIcon\n      this.activeData.__config__.document = config.document\n      if (typeof this.activeData.__config__.defaultValue === typeof config.defaultValue) {\n        config.defaultValue = this.activeData.__config__.defaultValue\n      }\n      Object.keys(newTag).forEach(key => {\n        if (this.activeData[key] !== undefined) {\n          newTag[key] = this.activeData[key]\n        }\n      })\n      this.activeData = newTag\n      this.updateDrawingList(newTag, this.drawingList)\n    },\n    updateDrawingList(newTag, list) {\n      const index = list.findIndex(item => item.__config__.formId === this.activeId)\n      if (index > -1) {\n        list.splice(index, 1, newTag)\n      } else {\n        list.forEach(item => {\n          if (Array.isArray(item.__config__.children)) this.updateDrawingList(newTag, item.__config__.children)\n        })\n      }\n    },\n    refreshJson(data) {\n      this.drawingList = deepClone(data.fields)\n      delete data.fields\n      this.formConf = data\n    },\n    /** 表单基本信息 */\n    handleForm(){\n      this.formData = {\n        fields: deepClone(this.drawingList),\n        ...this.formConf\n      }\n     this.form.formContent = JSON.stringify(this.formData);\n     this.formOpen = true;\n     this.formTitle = \"添加表单\";\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        formId: null,\n        formName: null,\n        formContent: null,\n        remark: null\n      };\n      this.resetForm(\"form\");\n    },\n    // 取消按钮\n    cancel() {\n      this.formOpen = false;\n      this.reset();\n    },\n    /** 保存表单信息 */\n    submitForm(){\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          if (this.form.formId != null) {\n            updateForm(this.form).then(response => {\n              this.msgSuccess(\"修改成功\");\n            });\n          } else {\n            addForm(this.form).then(response => {\n              this.msgSuccess(\"新增成功\");\n            });\n          }\n          this.drawingList = []\n          this.idGlobal = 100\n          this.open = false;\n          // 关闭当前标签页并返回上个页面\n          this.$store.dispatch(\"tagsView/delView\", this.$route);\n          this.$router.go(-1)\n        }\n      });\n    }\n  }\n}\n</script>\n\n<style lang='scss'>\n@import '@/styles/home';\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0IA,OAAAA,SAAA;AACA,SAAAC,QAAA;AACA,SAAAC,MAAA;AACA,OAAAC,WAAA;AACA,OAAAC,MAAA;AACA,OAAAC,UAAA;AACA,OAAAC,UAAA;AACA,OAAAC,UAAA;AACA,SACAC,eAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,QAAA,QACA;AACA,SACAC,aAAA,EAAAC,cAAA,EAAAC,WAAA,EAAAC,SAAA,EAAAC,SAAA,EAAAC,cAAA,QACA;AACA,SACAC,UAAA,EAAAC,WAAA,EAAAC,SAAA,EAAAC,QAAA,QACA;AACA,SAAAC,QAAA;AACA,SAAAC,SAAA;AACA,OAAAC,cAAA;AACA,OAAAC,IAAA;AACA,OAAAC,cAAA;AACA,OAAAC,aAAA;AACA,SACAC,cAAA,EAAAC,eAAA,EAAAC,WAAA,EAAAC,YAAA,EAAAC,WAAA,QACA;AACA,OAAAC,cAAA;AACA,SAAAC,OAAA,EAAAC,OAAA,EAAAC,UAAA;AAEA,IAAAC,UAAA;AACA,IAAAC,eAAA;EAAAC,KAAA;EAAAC,QAAA;AAAA;AACA,IAAAC,WAAA;AACA,IAAAC,cAAA;AACA,IAAAC,eAAA,GAAAf,cAAA;AACA,IAAAgB,YAAA,GAAAZ,WAAA;AACA,IAAAa,QAAA,GAAAf,WAAA;AAEA;EACAgB,UAAA;IACA9C,SAAA,EAAAA,SAAA;IACAI,MAAA,EAAAA,MAAA;IACAC,UAAA,EAAAA,UAAA;IACAC,UAAA,EAAAA,UAAA;IACAC,UAAA,EAAAA,UAAA;IACAmB,cAAA,EAAAA,cAAA;IACAC,aAAA,EAAAA;EACA;EACAoB,IAAA,WAAAA,KAAA;IACA;MACAtB,IAAA,EAAAA,IAAA;MACAoB,QAAA,EAAAA,QAAA;MACAlC,QAAA,EAAAA,QAAA;MACAH,eAAA,EAAAA,eAAA;MACAC,gBAAA,EAAAA,gBAAA;MACAC,gBAAA,EAAAA,gBAAA;MACAsC,UAAA;MACAC,WAAA,EAAAzB,cAAA;MACA0B,WAAA;MACAC,QAAA,EAAA3B,cAAA,IAAA4B,MAAA;MACAC,aAAA;MACAC,QAAA;MACAC,aAAA;MACAC,iBAAA;MACAC,YAAA;MACAC,YAAA;MACAC,UAAA,EAAAnC,cAAA;MACAoC,uBAAA,EAAA3D,QAAA,MAAA4B,eAAA;MACAgC,oBAAA,EAAA5D,QAAA,MAAA8B,YAAA;MACA+B,cAAA,GACA;QACAC,KAAA;QACAC,IAAA,EAAAxD;MACA,GACA;QACAuD,KAAA;QACAC,IAAA,EAAAvD;MACA,GACA;QACAsD,KAAA;QACAC,IAAA,EAAAtD;MACA,EACA;MACAuD,QAAA;MACAC,SAAA;MACA;MACAC,IAAA;QACAf,MAAA;QACAgB,QAAA;QACAC,WAAA;QACAC,MAAA;MACA;MACA;MACAC,KAAA;IACA;EACA;EACAC,QAAA,GACA;EACAC,KAAA;IACA;IACA,wCAAAC,0BAAAC,GAAA,EAAAC,MAAA;MACA,IACA,KAAAjB,UAAA,CAAAkB,WAAA,KAAAC,SAAA,IACA,MAAAnB,UAAA,CAAAoB,UAAA,CAAAC,GAAA,IACAvC,WAAA,UAAAU,QAAA,EACA;QACA;MACA;MACA,KAAAQ,UAAA,CAAAkB,WAAA,QAAAlB,UAAA,CAAAkB,WAAA,CAAAI,OAAA,CAAAL,MAAA,QAAAD,GAAA;IACA;IACAxB,QAAA;MACA+B,OAAA,WAAAA,QAAAP,GAAA;QACAlC,WAAA,GAAAkC,GAAA;MACA;MACAQ,SAAA;IACA;IACAlC,WAAA;MACAiC,OAAA,WAAAA,QAAAP,GAAA;QACA,KAAAf,uBAAA,CAAAe,GAAA;QACA,IAAAA,GAAA,CAAAS,MAAA,aAAAvC,QAAA;MACA;MACAwC,IAAA;IACA;IACAxC,QAAA;MACAqC,OAAA,WAAAA,QAAAP,GAAA;QACA,KAAAd,oBAAA,CAAAc,GAAA;MACA;MACAQ,SAAA;IACA;EACA;EAEAG,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA,IAAAC,IAAA;IACA,IAAAC,KAAA,CAAAC,OAAA,CAAA/C,eAAA,KAAAA,eAAA,CAAAyC,MAAA;MACAI,IAAA,CAAAvC,WAAA,GAAAN,eAAA;IACA;MACA6C,IAAA,CAAAvC,WAAA,GAAAzB,cAAA;IACA;IACA,KAAAmE,cAAA,CAAAH,IAAA,CAAAvC,WAAA;IACA;IACA;IACA;IACAuC,IAAA,CAAAvC,WAAA;IACA,IAAAG,MAAA,GAAAoC,IAAA,CAAAI,MAAA,CAAAC,KAAA,IAAAL,IAAA,CAAAI,MAAA,CAAAC,KAAA,CAAAzC,MAAA;IACA,IAAAA,MAAA;MACAlB,OAAA,CAAAkB,MAAA,EAAA0C,IAAA,WAAAC,GAAA;QACAP,IAAA,CAAA7E,QAAA,GAAAqF,IAAA,CAAAC,KAAA,CAAAF,GAAA,CAAAhD,IAAA,CAAAsB,WAAA;QACAmB,IAAA,CAAAvC,WAAA,GAAAuC,IAAA,CAAA7E,QAAA,CAAAuF,MAAA;QACAV,IAAA,CAAArB,IAAA,GAAA4B,GAAA,CAAAhD,IAAA;MACA;IACA;MACA,IAAAH,YAAA;QACA4C,IAAA,CAAA7E,QAAA,GAAAiC,YAAA;MACA;IACA;IACAX,cAAA,WAAAkE,GAAA;MACA9D,UAAA,GAAA8D,GAAA;IACA;IACA,IAAAC,SAAA,OAAAjG,WAAA;MACAkG,IAAA,WAAAA,KAAAC,OAAA;QACA,IAAAC,OAAA,GAAAhB,KAAA,CAAAiB,YAAA;QACAjB,KAAA,CAAAkB,OAAA;UACA1C,KAAA;UACA2C,OAAA;UACAC,IAAA;QACA;QACA,OAAAJ,OAAA;MACA;IACA;IACAH,SAAA,CAAAQ,EAAA,oBAAAC,CAAA;MACAtB,KAAA,CAAAuB,QAAA,CAAAC,KAAA;IACA;EACA;EACAC,OAAA;IACAC,oBAAA,WAAAA,qBAAAC,GAAA,EAAAC,OAAA,EAAApE,IAAA;MACA,IAAAqE,GAAA,GAAAD,OAAA,CAAAE,KAAA;MACAD,GAAA,CAAAE,MAAA,WAAAC,GAAA,EAAAC,IAAA,EAAAC,CAAA;QACA,IAAAL,GAAA,CAAAhC,MAAA,KAAAqC,CAAA;UACAF,GAAA,CAAAC,IAAA,IAAAzE,IAAA;QACA,YAAA9B,cAAA,CAAAsG,GAAA,CAAAC,IAAA;UACAD,GAAA,CAAAC,IAAA;QACA;QACA,OAAAD,GAAA,CAAAC,IAAA;MACA,GAAAN,GAAA;IACA;IACAQ,WAAA,WAAAA,YAAAC,SAAA,EAAAC,IAAA;MACA,IAAAC,qBAAA,GAAAF,SAAA,CAAA5C,UAAA;QAAA+C,QAAA,GAAAD,qBAAA,CAAAC,QAAA;QAAAC,SAAA,GAAAF,qBAAA,CAAAE,SAAA;QAAAC,YAAA,GAAAH,qBAAA,CAAAG,YAAA;MACA,KAAAF,QAAA,KAAAE,YAAA;MACA,IAAAC,QAAA,GAAAH,QAAA,CAAAT,KAAA,MAAAC,MAAA,WAAAC,GAAA,EAAAC,IAAA;QAAA,OAAAD,GAAA,CAAAC,IAAA;MAAA,GAAAI,IAAA;;MAEA;MACA;MACA;MACA;MACA,KAAAX,oBAAA,CAAAU,SAAA,EAAAK,YAAA,EAAAC,QAAA;MACA,IAAAR,CAAA,QAAAxE,WAAA,CAAAiF,SAAA,WAAAV,IAAA;QAAA,OAAAA,IAAA,CAAAzC,UAAA,CAAAgD,SAAA,KAAAA,SAAA;MAAA;MACA,IAAAN,CAAA,YAAAU,IAAA,MAAAlF,WAAA,EAAAwE,CAAA,EAAAE,SAAA;IACA;IACAS,SAAA,WAAAA,UAAAT,SAAA;MAAA,IAAAU,MAAA;MACA,IAAAC,sBAAA,GAAAX,SAAA,CAAA5C,UAAA;QAAAwD,QAAA,GAAAD,sBAAA,CAAAC,QAAA;QAAAC,MAAA,GAAAF,sBAAA,CAAAE,MAAA;QAAAC,GAAA,GAAAH,sBAAA,CAAAG,GAAA;MACA,IAAAF,QAAA,kBAAAC,MAAA,IAAAC,GAAA;QACA,KAAAC,UAAA,CAAAf,SAAA;QACA,KAAAgB,MAAA;UACAH,MAAA,EAAAA,MAAA;UACAC,GAAA,EAAAA;QACA,GAAA3C,IAAA,WAAA8B,IAAA;UACAS,MAAA,CAAAK,UAAA,CAAAf,SAAA;UACAU,MAAA,CAAAX,WAAA,CAAAC,SAAA,EAAAC,IAAA,CAAA7E,IAAA;QACA;MACA;IACA;IACA2F,UAAA,WAAAA,WAAAf,SAAA,EAAAhD,GAAA;MACA,IAAAiE,UAAA,GAAAjB,SAAA,CAAAiB,UAAA;MACA,IAAAnD,KAAA,CAAAC,OAAA,CAAAkD,UAAA;QACA,IAAAC,CAAA,GAAAD,UAAA,CAAAE,IAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAAC,IAAA;QAAA;QACA,IAAAH,CAAA,EAAAA,CAAA,CAAAI,KAAA,GAAAtE,GAAA;MACA;IACA;IACAgB,cAAA,WAAAA,eAAAuD,WAAA;MACA,KAAAvF,UAAA,GAAAuF,WAAA;MACA,KAAA/F,QAAA,GAAA+F,WAAA,CAAAnE,UAAA,CAAA3B,MAAA;IACA;IACA+F,KAAA,WAAAA,MAAAjC,GAAA;MACA,IAAAA,GAAA,CAAAkC,IAAA,KAAAlC,GAAA,CAAAmC,EAAA;QACA,KAAAjB,SAAA,CAAA1F,cAAA;QACA,KAAAiB,UAAA,GAAAjB,cAAA;QACA,KAAAS,QAAA,QAAAN,QAAA;MACA;IACA;IACAyG,YAAA,WAAAA,aAAA9B,IAAA;MACA,IAAA+B,KAAA,QAAAC,cAAA,CAAAhC,IAAA;MACA,KAAAY,SAAA,CAAAmB,KAAA;MACA,KAAAtG,WAAA,CAAAwG,IAAA,CAAAF,KAAA;MACA,KAAA5D,cAAA,CAAA4D,KAAA;IACA;IACAC,cAAA,WAAAA,eAAAE,MAAA;MACA,IAAAH,KAAA,GAAAvI,SAAA,CAAA0I,MAAA;MACA,IAAAC,MAAA,GAAAJ,KAAA,CAAAxE,UAAA;MACA4E,MAAA,CAAAC,IAAA,QAAAjJ,QAAA,CAAAiJ,IAAA;MACA,KAAAC,cAAA,CAAAN,KAAA;MACAA,KAAA,CAAA1E,WAAA,KAAAC,SAAA,KAAAyE,KAAA,CAAA1E,WAAA,IAAA8E,MAAA,CAAAG,KAAA;MACApH,cAAA,GAAA6G,KAAA;MACA,OAAA7G,cAAA;IACA;IACAmH,cAAA,WAAAA,eAAArC,IAAA;MAAA,IAAAuC,MAAA;MACA,IAAAJ,MAAA,GAAAnC,IAAA,CAAAzC,UAAA;MACA4E,MAAA,CAAAvG,MAAA,UAAAP,QAAA;MACA8G,MAAA,CAAA5B,SAAA,MAAAiC,MAAA,CAAAL,MAAA,CAAAvG,MAAA,EAAA4G,MAAA,MAAAC,IAAA;MACA,IAAAN,MAAA,CAAAO,MAAA;QACA1C,IAAA,CAAA2C,UAAA,WAAAH,MAAA,MAAAnH,QAAA;MACA,WAAA8G,MAAA,CAAAO,MAAA;QACAP,MAAA,CAAAS,aAAA,SAAAJ,MAAA,MAAAnH,QAAA;QACA,CAAA4C,KAAA,CAAAC,OAAA,CAAAiE,MAAA,CAAAU,QAAA,MAAAV,MAAA,CAAAU,QAAA;QACA,OAAAV,MAAA,CAAAG,KAAA;MACA;MACA,IAAArE,KAAA,CAAAC,OAAA,CAAAiE,MAAA,CAAAU,QAAA;QACAV,MAAA,CAAAU,QAAA,GAAAV,MAAA,CAAAU,QAAA,CAAAC,GAAA,WAAAC,SAAA;UAAA,OAAAR,MAAA,CAAAF,cAAA,CAAAU,SAAA;QAAA;MACA;MACA,OAAA/C,IAAA;IACA;IACAgD,gBAAA,WAAAA,iBAAA;MACA,KAAAlH,QAAA,GAAAmH,aAAA;QACAvE,MAAA,EAAAlF,SAAA,MAAAiC,WAAA;MAAA,GACA,KAAAtC,QAAA,CACA;IACA;IACA+J,QAAA,WAAAA,SAAA3H,IAAA;MACA,IAAA4H,IAAA,eAAAX,MAAA,CAAAjJ,SAAA,MAAA6J,aAAA;MACA,KAAAnH,YAAA,GAAAV,IAAA;MACA4H,IAAA,IAAAA,IAAA,CAAA5H,IAAA;IACA;IACA8H,OAAA,WAAAA,QAAA9H,IAAA;MACA,KAAAyH,gBAAA;MACA,KAAAnH,aAAA;IACA;IACAyH,YAAA,WAAAA,aAAA/H,IAAA;MACA,IAAAwD,OAAA,QAAAC,YAAA;MACA,IAAAuE,IAAA,OAAAC,IAAA,EAAAzE,OAAA;QAAAI,IAAA;MAAA;MACAzG,MAAA,CAAA6K,IAAA,EAAAhI,IAAA,CAAAkI,QAAA;IACA;IACAC,QAAA,WAAAA,SAAAnI,IAAA;MACAoI,QAAA,CAAAC,cAAA,aAAAC,KAAA;IACA;IACAC,KAAA,WAAAA,MAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,QAAA;QAAA7E,IAAA;MAAA,GAAAb,IAAA,CACA;QACAyF,MAAA,CAAAtI,WAAA;QACAsI,MAAA,CAAA1I,QAAA;MACA,CACA;IACA;IACA4I,eAAA,WAAAA,gBAAAjE,IAAA,EAAAxD,IAAA;MACA,IAAAuF,KAAA,GAAAvI,SAAA,CAAAwG,IAAA;MACA+B,KAAA,QAAAM,cAAA,CAAAN,KAAA;MACAvF,IAAA,CAAAyF,IAAA,CAAAF,KAAA;MACA,KAAA5D,cAAA,CAAA4D,KAAA;IACA;IACAmC,iBAAA,WAAAA,kBAAAC,KAAA,EAAA3H,IAAA;MAAA,IAAA4H,MAAA;MACA5H,IAAA,CAAA6H,MAAA,CAAAF,KAAA;MACA,KAAAG,SAAA;QACA,IAAAC,GAAA,GAAAH,MAAA,CAAA3I,WAAA,CAAAmC,MAAA;QACA,IAAA2G,GAAA;UACAH,MAAA,CAAAjG,cAAA,CAAAiG,MAAA,CAAA3I,WAAA,CAAA8I,GAAA;QACA;MACA;IACA;IACAvF,YAAA,WAAAA,aAAA;MACA,IAAAG,IAAA,QAAAlD,YAAA,CAAAkD,IAAA;MACA,KAAA6D,gBAAA;MACA,IAAAwB,MAAA,GAAA5K,SAAA,CAAAE,QAAA,MAAAgC,QAAA,EAAAqD,IAAA;MACA,IAAAsF,IAAA,GAAA9K,WAAA,CAAAD,UAAA,MAAAoC,QAAA,EAAAqD,IAAA;MACA,IAAAuF,GAAA,GAAA7K,QAAA,CAAAE,SAAA,MAAA+B,QAAA;MACA,OAAAjB,UAAA,CAAA4J,IAAA,CAAAA,IAAA,GAAAD,MAAA,GAAAE,GAAA,EAAArL,cAAA,CAAAoL,IAAA;IACA;IACAE,QAAA,WAAAA,SAAA;MACA,KAAA3B,gBAAA;MACA,KAAAhH,iBAAA;IACA;IACA4I,QAAA,WAAAA,SAAA;MACA,KAAA7I,aAAA;MACA,KAAAG,YAAA;MACA,KAAAkH,aAAA;IACA;IACAyB,GAAA,WAAAA,IAAA;MACA,KAAA9I,aAAA;MACA,KAAAG,YAAA;MACA,KAAAkH,aAAA;IACA;IACA0B,IAAA,WAAAA,KAAA;MACA,KAAA/I,aAAA;MACA,KAAAG,YAAA;MACA,KAAAkH,aAAA;IACA;IACA2B,SAAA,WAAAA,UAAAC,MAAA;MAAA,IAAAC,MAAA;MACAD,MAAA,QAAAhD,cAAA,CAAAgD,MAAA;MACA,IAAA7C,MAAA,GAAA6C,MAAA,CAAAzH,UAAA;MACAyH,MAAA,CAAArC,UAAA,QAAAxG,UAAA,CAAAwG,UAAA;MACAR,MAAA,CAAAvG,MAAA,QAAAD,QAAA;MACAwG,MAAA,CAAAC,IAAA,QAAAjG,UAAA,CAAAoB,UAAA,CAAA6E,IAAA;MACA,KAAAjG,UAAA,CAAAoB,UAAA,CAAAC,GAAA,GAAA2E,MAAA,CAAA3E,GAAA;MACA,KAAArB,UAAA,CAAAoB,UAAA,CAAA2H,OAAA,GAAA/C,MAAA,CAAA+C,OAAA;MACA,KAAA/I,UAAA,CAAAoB,UAAA,CAAAoG,QAAA,GAAAxB,MAAA,CAAAwB,QAAA;MACA,IAAAwB,OAAA,MAAAhJ,UAAA,CAAAoB,UAAA,CAAA6H,YAAA,MAAAD,OAAA,CAAAhD,MAAA,CAAAiD,YAAA;QACAjD,MAAA,CAAAiD,YAAA,QAAAjJ,UAAA,CAAAoB,UAAA,CAAA6H,YAAA;MACA;MACAC,MAAA,CAAAC,IAAA,CAAAN,MAAA,EAAAO,OAAA,WAAAC,GAAA;QACA,IAAAP,MAAA,CAAA9I,UAAA,CAAAqJ,GAAA,MAAAlI,SAAA;UACA0H,MAAA,CAAAQ,GAAA,IAAAP,MAAA,CAAA9I,UAAA,CAAAqJ,GAAA;QACA;MACA;MACA,KAAArJ,UAAA,GAAA6I,MAAA;MACA,KAAAS,iBAAA,CAAAT,MAAA,OAAAvJ,WAAA;IACA;IACAgK,iBAAA,WAAAA,kBAAAT,MAAA,EAAAxI,IAAA;MAAA,IAAAkJ,MAAA;MACA,IAAAvB,KAAA,GAAA3H,IAAA,CAAAkE,SAAA,WAAAV,IAAA;QAAA,OAAAA,IAAA,CAAAzC,UAAA,CAAA3B,MAAA,KAAA8J,MAAA,CAAA/J,QAAA;MAAA;MACA,IAAAwI,KAAA;QACA3H,IAAA,CAAA6H,MAAA,CAAAF,KAAA,KAAAa,MAAA;MACA;QACAxI,IAAA,CAAA+I,OAAA,WAAAvF,IAAA;UACA,IAAA/B,KAAA,CAAAC,OAAA,CAAA8B,IAAA,CAAAzC,UAAA,CAAAsF,QAAA,GAAA6C,MAAA,CAAAD,iBAAA,CAAAT,MAAA,EAAAhF,IAAA,CAAAzC,UAAA,CAAAsF,QAAA;QACA;MACA;IACA;IACA8C,WAAA,WAAAA,YAAApK,IAAA;MACA,KAAAE,WAAA,GAAAjC,SAAA,CAAA+B,IAAA,CAAAmD,MAAA;MACA,OAAAnD,IAAA,CAAAmD,MAAA;MACA,KAAAvF,QAAA,GAAAoC,IAAA;IACA;IACA,aACAqK,UAAA,WAAAA,WAAA;MACA,KAAA9J,QAAA,GAAAmH,aAAA;QACAvE,MAAA,EAAAlF,SAAA,MAAAiC,WAAA;MAAA,GACA,KAAAtC,QAAA,CACA;MACA,KAAAwD,IAAA,CAAAE,WAAA,GAAA2B,IAAA,CAAAqH,SAAA,MAAA/J,QAAA;MACA,KAAAW,QAAA;MACA,KAAAC,SAAA;IACA;IACA;IACAoJ,KAAA,WAAAA,MAAA;MACA,KAAAnJ,IAAA;QACAf,MAAA;QACAgB,QAAA;QACAC,WAAA;QACAC,MAAA;MACA;MACA,KAAAiJ,SAAA;IACA;IACA;IACAC,MAAA,WAAAA,OAAA;MACA,KAAAvJ,QAAA;MACA,KAAAqJ,KAAA;IACA;IACA,aACAG,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAH,MAAA,CAAAvJ,IAAA,CAAAf,MAAA;YACAhB,UAAA,CAAAsL,MAAA,CAAAvJ,IAAA,EAAA2B,IAAA,WAAAgI,QAAA;cACAJ,MAAA,CAAAK,UAAA;YACA;UACA;YACA5L,OAAA,CAAAuL,MAAA,CAAAvJ,IAAA,EAAA2B,IAAA,WAAAgI,QAAA;cACAJ,MAAA,CAAAK,UAAA;YACA;UACA;UACAL,MAAA,CAAAzK,WAAA;UACAyK,MAAA,CAAA7K,QAAA;UACA6K,MAAA,CAAAM,IAAA;UACA;UACAN,MAAA,CAAAO,MAAA,CAAAC,QAAA,qBAAAR,MAAA,CAAA9H,MAAA;UACA8H,MAAA,CAAAS,OAAA,CAAAC,EAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}