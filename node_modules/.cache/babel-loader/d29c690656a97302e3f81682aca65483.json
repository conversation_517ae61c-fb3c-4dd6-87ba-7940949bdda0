{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/utils/loadScript.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/utils/loadScript.js", "mtime": 1641277326000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["callbacks", "loadScript", "src", "callback", "existingScript", "document", "getElementById", "cb", "$script", "createElement", "id", "async", "body", "append<PERSON><PERSON><PERSON>", "onEnd", "stdOnEnd", "bind", "ieOnEnd", "push", "script", "_this", "onload", "onerror", "for<PERSON>ach", "item", "Error", "concat", "_this2", "onreadystatechange", "readyState", "loadScriptQueue", "list", "first", "shift", "length"], "sources": ["/Users/<USER>/develop/code/project/haijia/adminweb/src/utils/loadScript.js"], "sourcesContent": ["const callbacks = {}\n\n/**\n * 加载一个远程脚本\n * @param {String} src 一个远程脚本\n * @param {Function} callback 回调\n */\nfunction loadScript(src, callback) {\n  const existingScript = document.getElementById(src)\n  const cb = callback || (() => {})\n  if (!existingScript) {\n    callbacks[src] = []\n    const $script = document.createElement('script')\n    $script.src = src\n    $script.id = src\n    $script.async = 1\n    document.body.appendChild($script)\n    const onEnd = 'onload' in $script ? stdOnEnd.bind($script) : ieOnEnd.bind($script)\n    onEnd($script)\n  }\n\n  callbacks[src].push(cb)\n\n  function stdOnEnd(script) {\n    script.onload = () => {\n      this.onerror = this.onload = null\n      callbacks[src].forEach(item => {\n        item(null, script)\n      })\n      delete callbacks[src]\n    }\n    script.onerror = () => {\n      this.onerror = this.onload = null\n      cb(new Error(`Failed to load ${src}`), script)\n    }\n  }\n\n  function ieOnEnd(script) {\n    script.onreadystatechange = () => {\n      if (this.readyState !== 'complete' && this.readyState !== 'loaded') return\n      this.onreadystatechange = null\n      callbacks[src].forEach(item => {\n        item(null, script)\n      })\n      delete callbacks[src]\n    }\n  }\n}\n\n/**\n * 顺序加载一组远程脚本\n * @param {Array} list 一组远程脚本\n * @param {Function} cb 回调\n */\nexport function loadScriptQueue(list, cb) {\n  const first = list.shift()\n  list.length ? loadScript(first, () => loadScriptQueue(list, cb)) : loadScript(first, cb)\n}\n\nexport default loadScript\n"], "mappings": "AAAA,IAAMA,SAAS,GAAG,CAAC,CAAC;;AAEpB;AACA;AACA;AACA;AACA;AACA,SAASC,UAAUA,CAACC,GAAG,EAAEC,QAAQ,EAAE;EACjC,IAAMC,cAAc,GAAGC,QAAQ,CAACC,cAAc,CAACJ,GAAG,CAAC;EACnD,IAAMK,EAAE,GAAGJ,QAAQ,IAAK,YAAM,CAAC,CAAE;EACjC,IAAI,CAACC,cAAc,EAAE;IACnBJ,SAAS,CAACE,GAAG,CAAC,GAAG,EAAE;IACnB,IAAMM,OAAO,GAAGH,QAAQ,CAACI,aAAa,CAAC,QAAQ,CAAC;IAChDD,OAAO,CAACN,GAAG,GAAGA,GAAG;IACjBM,OAAO,CAACE,EAAE,GAAGR,GAAG;IAChBM,OAAO,CAACG,KAAK,GAAG,CAAC;IACjBN,QAAQ,CAACO,IAAI,CAACC,WAAW,CAACL,OAAO,CAAC;IAClC,IAAMM,KAAK,GAAG,QAAQ,IAAIN,OAAO,GAAGO,QAAQ,CAACC,IAAI,CAACR,OAAO,CAAC,GAAGS,OAAO,CAACD,IAAI,CAACR,OAAO,CAAC;IAClFM,KAAK,CAACN,OAAO,CAAC;EAChB;EAEAR,SAAS,CAACE,GAAG,CAAC,CAACgB,IAAI,CAACX,EAAE,CAAC;EAEvB,SAASQ,QAAQA,CAACI,MAAM,EAAE;IAAA,IAAAC,KAAA;IACxBD,MAAM,CAACE,MAAM,GAAG,YAAM;MACpBD,KAAI,CAACE,OAAO,GAAGF,KAAI,CAACC,MAAM,GAAG,IAAI;MACjCrB,SAAS,CAACE,GAAG,CAAC,CAACqB,OAAO,CAAC,UAAAC,IAAI,EAAI;QAC7BA,IAAI,CAAC,IAAI,EAAEL,MAAM,CAAC;MACpB,CAAC,CAAC;MACF,OAAOnB,SAAS,CAACE,GAAG,CAAC;IACvB,CAAC;IACDiB,MAAM,CAACG,OAAO,GAAG,YAAM;MACrBF,KAAI,CAACE,OAAO,GAAGF,KAAI,CAACC,MAAM,GAAG,IAAI;MACjCd,EAAE,CAAC,IAAIkB,KAAK,mBAAAC,MAAA,CAAmBxB,GAAG,CAAE,CAAC,EAAEiB,MAAM,CAAC;IAChD,CAAC;EACH;EAEA,SAASF,OAAOA,CAACE,MAAM,EAAE;IAAA,IAAAQ,MAAA;IACvBR,MAAM,CAACS,kBAAkB,GAAG,YAAM;MAChC,IAAID,MAAI,CAACE,UAAU,KAAK,UAAU,IAAIF,MAAI,CAACE,UAAU,KAAK,QAAQ,EAAE;MACpEF,MAAI,CAACC,kBAAkB,GAAG,IAAI;MAC9B5B,SAAS,CAACE,GAAG,CAAC,CAACqB,OAAO,CAAC,UAAAC,IAAI,EAAI;QAC7BA,IAAI,CAAC,IAAI,EAAEL,MAAM,CAAC;MACpB,CAAC,CAAC;MACF,OAAOnB,SAAS,CAACE,GAAG,CAAC;IACvB,CAAC;EACH;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS4B,eAAeA,CAACC,IAAI,EAAExB,EAAE,EAAE;EACxC,IAAMyB,KAAK,GAAGD,IAAI,CAACE,KAAK,CAAC,CAAC;EAC1BF,IAAI,CAACG,MAAM,GAAGjC,UAAU,CAAC+B,KAAK,EAAE;IAAA,OAAMF,eAAe,CAACC,IAAI,EAAExB,EAAE,CAAC;EAAA,EAAC,GAAGN,UAAU,CAAC+B,KAAK,EAAEzB,EAAE,CAAC;AAC1F;AAEA,eAAeN,UAAU", "ignoreList": []}]}