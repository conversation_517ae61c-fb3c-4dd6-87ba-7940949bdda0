{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/store/modules/settings.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/store/modules/settings.js", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHZhcmlhYmxlcyBmcm9tICdAL2Fzc2V0cy9zdHlsZXMvZWxlbWVudC12YXJpYWJsZXMuc2Nzcyc7CmltcG9ydCBkZWZhdWx0U2V0dGluZ3MgZnJvbSAnQC9zZXR0aW5ncyc7CnZhciBzaWRlVGhlbWUgPSBkZWZhdWx0U2V0dGluZ3Muc2lkZVRoZW1lLAogIHNob3dTZXR0aW5ncyA9IGRlZmF1bHRTZXR0aW5ncy5zaG93U2V0dGluZ3MsCiAgdGFnc1ZpZXcgPSBkZWZhdWx0U2V0dGluZ3MudGFnc1ZpZXcsCiAgZml4ZWRIZWFkZXIgPSBkZWZhdWx0U2V0dGluZ3MuZml4ZWRIZWFkZXIsCiAgc2lkZWJhckxvZ28gPSBkZWZhdWx0U2V0dGluZ3Muc2lkZWJhckxvZ287CnZhciBzdGF0ZSA9IHsKICB0aGVtZTogdmFyaWFibGVzLnRoZW1lLAogIHNpZGVUaGVtZTogc2lkZVRoZW1lLAogIHNob3dTZXR0aW5nczogc2hvd1NldHRpbmdzLAogIHRhZ3NWaWV3OiB0YWdzVmlldywKICBmaXhlZEhlYWRlcjogZml4ZWRIZWFkZXIsCiAgc2lkZWJhckxvZ286IHNpZGViYXJMb2dvCn07CnZhciBtdXRhdGlvbnMgPSB7CiAgQ0hBTkdFX1NFVFRJTkc6IGZ1bmN0aW9uIENIQU5HRV9TRVRUSU5HKHN0YXRlLCBfcmVmKSB7CiAgICB2YXIga2V5ID0gX3JlZi5rZXksCiAgICAgIHZhbHVlID0gX3JlZi52YWx1ZTsKICAgIGlmIChzdGF0ZS5oYXNPd25Qcm9wZXJ0eShrZXkpKSB7CiAgICAgIHN0YXRlW2tleV0gPSB2YWx1ZTsKICAgIH0KICB9Cn07CnZhciBhY3Rpb25zID0gewogIGNoYW5nZVNldHRpbmc6IGZ1bmN0aW9uIGNoYW5nZVNldHRpbmcoX3JlZjIsIGRhdGEpIHsKICAgIHZhciBjb21taXQgPSBfcmVmMi5jb21taXQ7CiAgICBjb21taXQoJ0NIQU5HRV9TRVRUSU5HJywgZGF0YSk7CiAgfQp9OwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZXNwYWNlZDogdHJ1ZSwKICBzdGF0ZTogc3RhdGUsCiAgbXV0YXRpb25zOiBtdXRhdGlvbnMsCiAgYWN0aW9uczogYWN0aW9ucwp9Ow=="}, {"version": 3, "names": ["variables", "defaultSettings", "sideTheme", "showSettings", "tagsView", "fixedHeader", "sidebarLogo", "state", "theme", "mutations", "CHANGE_SETTING", "_ref", "key", "value", "hasOwnProperty", "actions", "changeSetting", "_ref2", "data", "commit", "namespaced"], "sources": ["/Users/<USER>/develop/code/project/haijia/adminweb/src/store/modules/settings.js"], "sourcesContent": ["import variables from '@/assets/styles/element-variables.scss'\nimport defaultSettings from '@/settings'\n\nconst { sideTheme, showSettings, tagsView, fixedHeader, sidebarLogo } = defaultSettings\n\nconst state = {\n  theme: variables.theme,\n  sideTheme: sideTheme,\n  showSettings: showSettings,\n  tagsView: tagsView,\n  fixedHeader: fixedHeader,\n  sidebarLogo: sidebarLogo\n}\n\nconst mutations = {\n  CHANGE_SETTING: (state, { key, value }) => {\n    if (state.hasOwnProperty(key)) {\n      state[key] = value\n    }\n  }\n}\n\nconst actions = {\n  changeSetting({ commit }, data) {\n    commit('CHANGE_SETTING', data)\n  }\n}\n\nexport default {\n  namespaced: true,\n  state,\n  mutations,\n  actions\n}\n\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,wCAAwC;AAC9D,OAAOC,eAAe,MAAM,YAAY;AAExC,IAAQC,SAAS,GAAuDD,eAAe,CAA/EC,SAAS;EAAEC,YAAY,GAAyCF,eAAe,CAApEE,YAAY;EAAEC,QAAQ,GAA+BH,eAAe,CAAtDG,QAAQ;EAAEC,WAAW,GAAkBJ,eAAe,CAA5CI,WAAW;EAAEC,WAAW,GAAKL,eAAe,CAA/BK,WAAW;AAEnE,IAAMC,KAAK,GAAG;EACZC,KAAK,EAAER,SAAS,CAACQ,KAAK;EACtBN,SAAS,EAAEA,SAAS;EACpBC,YAAY,EAAEA,YAAY;EAC1BC,QAAQ,EAAEA,QAAQ;EAClBC,WAAW,EAAEA,WAAW;EACxBC,WAAW,EAAEA;AACf,CAAC;AAED,IAAMG,SAAS,GAAG;EAChBC,cAAc,EAAE,SAAAA,eAACH,KAAK,EAAAI,IAAA,EAAqB;IAAA,IAAjBC,GAAG,GAAAD,IAAA,CAAHC,GAAG;MAAEC,KAAK,GAAAF,IAAA,CAALE,KAAK;IAClC,IAAIN,KAAK,CAACO,cAAc,CAACF,GAAG,CAAC,EAAE;MAC7BL,KAAK,CAACK,GAAG,CAAC,GAAGC,KAAK;IACpB;EACF;AACF,CAAC;AAED,IAAME,OAAO,GAAG;EACdC,aAAa,WAAAA,cAAAC,KAAA,EAAaC,IAAI,EAAE;IAAA,IAAhBC,MAAM,GAAAF,KAAA,CAANE,MAAM;IACpBA,MAAM,CAAC,gBAAgB,EAAED,IAAI,CAAC;EAChC;AACF,CAAC;AAED,eAAe;EACbE,UAAU,EAAE,IAAI;EAChBb,KAAK,EAALA,KAAK;EACLE,SAAS,EAATA,SAAS;EACTM,OAAO,EAAPA;AACF,CAAC", "ignoreList": []}]}