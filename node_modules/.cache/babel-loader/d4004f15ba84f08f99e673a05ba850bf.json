{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/utils/permission.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/utils/permission.js", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHN0b3JlIGZyb20gJ0Avc3RvcmUnOwoKLyoqCiAqIOWtl+espuadg+mZkOagoemqjAogKiBAcGFyYW0ge0FycmF5fSB2YWx1ZSDmoKHpqozlgLwKICogQHJldHVybnMge0Jvb2xlYW59CiAqLwpleHBvcnQgZnVuY3Rpb24gY2hlY2tQZXJtaSh2YWx1ZSkgewogIGlmICh2YWx1ZSAmJiB2YWx1ZSBpbnN0YW5jZW9mIEFycmF5ICYmIHZhbHVlLmxlbmd0aCA+IDApIHsKICAgIHZhciBwZXJtaXNzaW9ucyA9IHN0b3JlLmdldHRlcnMgJiYgc3RvcmUuZ2V0dGVycy5wZXJtaXNzaW9uczsKICAgIHZhciBwZXJtaXNzaW9uRGF0YXMgPSB2YWx1ZTsKICAgIHZhciBhbGxfcGVybWlzc2lvbiA9ICIqOio6KiI7CiAgICB2YXIgaGFzUGVybWlzc2lvbiA9IHBlcm1pc3Npb25zLnNvbWUoZnVuY3Rpb24gKHBlcm1pc3Npb24pIHsKICAgICAgcmV0dXJuIGFsbF9wZXJtaXNzaW9uID09PSBwZXJtaXNzaW9uIHx8IHBlcm1pc3Npb25EYXRhcy5pbmNsdWRlcyhwZXJtaXNzaW9uKTsKICAgIH0pOwogICAgaWYgKCFoYXNQZXJtaXNzaW9uKSB7CiAgICAgIHJldHVybiBmYWxzZTsKICAgIH0KICAgIHJldHVybiB0cnVlOwogIH0gZWxzZSB7CiAgICBjb25zb2xlLmVycm9yKCJuZWVkIHJvbGVzISBMaWtlIGNoZWNrUGVybWk9XCJbJ3N5c3RlbTp1c2VyOmFkZCcsJ3N5c3RlbTp1c2VyOmVkaXQnXVwiIik7CiAgICByZXR1cm4gZmFsc2U7CiAgfQp9CgovKioKICog6KeS6Imy5p2D6ZmQ5qCh6aqMCiAqIEBwYXJhbSB7QXJyYXl9IHZhbHVlIOagoemqjOWAvAogKiBAcmV0dXJucyB7Qm9vbGVhbn0KICovCmV4cG9ydCBmdW5jdGlvbiBjaGVja1JvbGUodmFsdWUpIHsKICBpZiAodmFsdWUgJiYgdmFsdWUgaW5zdGFuY2VvZiBBcnJheSAmJiB2YWx1ZS5sZW5ndGggPiAwKSB7CiAgICB2YXIgcm9sZXMgPSBzdG9yZS5nZXR0ZXJzICYmIHN0b3JlLmdldHRlcnMucm9sZXM7CiAgICB2YXIgcGVybWlzc2lvblJvbGVzID0gdmFsdWU7CiAgICB2YXIgc3VwZXJfYWRtaW4gPSAiYWRtaW4iOwogICAgdmFyIGhhc1JvbGUgPSByb2xlcy5zb21lKGZ1bmN0aW9uIChyb2xlKSB7CiAgICAgIHJldHVybiBzdXBlcl9hZG1pbiA9PT0gcm9sZSB8fCBwZXJtaXNzaW9uUm9sZXMuaW5jbHVkZXMocm9sZSk7CiAgICB9KTsKICAgIGlmICghaGFzUm9sZSkgewogICAgICByZXR1cm4gZmFsc2U7CiAgICB9CiAgICByZXR1cm4gdHJ1ZTsKICB9IGVsc2UgewogICAgY29uc29sZS5lcnJvcigibmVlZCByb2xlcyEgTGlrZSBjaGVja1JvbGU9XCJbJ2FkbWluJywnZWRpdG9yJ11cIiIpOwogICAgcmV0dXJuIGZhbHNlOwogIH0KfQ=="}, {"version": 3, "names": ["store", "check<PERSON><PERSON><PERSON>", "value", "Array", "length", "permissions", "getters", "permissionDatas", "all_permission", "hasPermission", "some", "permission", "includes", "console", "error", "checkRole", "roles", "permissionRoles", "super_admin", "hasRole", "role"], "sources": ["/Users/<USER>/develop/code/project/haijia/adminweb/src/utils/permission.js"], "sourcesContent": ["import store from '@/store'\n\n/**\n * 字符权限校验\n * @param {Array} value 校验值\n * @returns {Boolean}\n */\nexport function checkPermi(value) {\n  if (value && value instanceof Array && value.length > 0) {\n    const permissions = store.getters && store.getters.permissions\n    const permissionDatas = value\n    const all_permission = \"*:*:*\";\n\n    const hasPermission = permissions.some(permission => {\n      return all_permission === permission || permissionDatas.includes(permission)\n    })\n\n    if (!hasPermission) {\n      return false\n    }\n    return true\n  } else {\n    console.error(`need roles! Like checkPermi=\"['system:user:add','system:user:edit']\"`)\n    return false\n  }\n}\n\n/**\n * 角色权限校验\n * @param {Array} value 校验值\n * @returns {Boolean}\n */\nexport function checkRole(value) {\n  if (value && value instanceof Array && value.length > 0) {\n    const roles = store.getters && store.getters.roles\n    const permissionRoles = value\n    const super_admin = \"admin\";\n\n    const hasRole = roles.some(role => {\n      return super_admin === role || permissionRoles.includes(role)\n    })\n\n    if (!hasRole) {\n      return false\n    }\n    return true\n  } else {\n    console.error(`need roles! Like checkRole=\"['admin','editor']\"`)\n    return false\n  }\n}"], "mappings": "AAAA,OAAOA,KAAK,MAAM,SAAS;;AAE3B;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,UAAUA,CAACC,KAAK,EAAE;EAChC,IAAIA,KAAK,IAAIA,KAAK,YAAYC,KAAK,IAAID,KAAK,CAACE,MAAM,GAAG,CAAC,EAAE;IACvD,IAAMC,WAAW,GAAGL,KAAK,CAACM,OAAO,IAAIN,KAAK,CAACM,OAAO,CAACD,WAAW;IAC9D,IAAME,eAAe,GAAGL,KAAK;IAC7B,IAAMM,cAAc,GAAG,OAAO;IAE9B,IAAMC,aAAa,GAAGJ,WAAW,CAACK,IAAI,CAAC,UAAAC,UAAU,EAAI;MACnD,OAAOH,cAAc,KAAKG,UAAU,IAAIJ,eAAe,CAACK,QAAQ,CAACD,UAAU,CAAC;IAC9E,CAAC,CAAC;IAEF,IAAI,CAACF,aAAa,EAAE;MAClB,OAAO,KAAK;IACd;IACA,OAAO,IAAI;EACb,CAAC,MAAM;IACLI,OAAO,CAACC,KAAK,yEAAuE,CAAC;IACrF,OAAO,KAAK;EACd;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,SAASA,CAACb,KAAK,EAAE;EAC/B,IAAIA,KAAK,IAAIA,KAAK,YAAYC,KAAK,IAAID,KAAK,CAACE,MAAM,GAAG,CAAC,EAAE;IACvD,IAAMY,KAAK,GAAGhB,KAAK,CAACM,OAAO,IAAIN,KAAK,CAACM,OAAO,CAACU,KAAK;IAClD,IAAMC,eAAe,GAAGf,KAAK;IAC7B,IAAMgB,WAAW,GAAG,OAAO;IAE3B,IAAMC,OAAO,GAAGH,KAAK,CAACN,IAAI,CAAC,UAAAU,IAAI,EAAI;MACjC,OAAOF,WAAW,KAAKE,IAAI,IAAIH,eAAe,CAACL,QAAQ,CAACQ,IAAI,CAAC;IAC/D,CAAC,CAAC;IAEF,IAAI,CAACD,OAAO,EAAE;MACZ,OAAO,KAAK;IACd;IACA,OAAO,IAAI;EACb,CAAC,MAAM;IACLN,OAAO,CAACC,KAAK,oDAAkD,CAAC;IAChE,OAAO,KAAK;EACd;AACF", "ignoreList": []}]}