{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/utils/loadMonaco.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/utils/loadMonaco.js", "mtime": 1641277326000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IGxvYWRTY3JpcHQgZnJvbSAnLi9sb2FkU2NyaXB0JzsKaW1wb3J0IEVMRU1FTlQgZnJvbSAnZWxlbWVudC11aSc7CmltcG9ydCBwbHVnaW5zQ29uZmlnIGZyb20gJy4vcGx1Z2luc0NvbmZpZyc7CgovLyBtb25hY28tZWRpdG9y5Y2V5L6LCnZhciBtb25hY29FaWR0b3I7CgovKioKICog5Yqo5oCB5Yqg6L29bW9uYWNvLWVkaXRvciBjZG7otYTmupAKICogQHBhcmFtIHtGdW5jdGlvbn0gY2Ig5Zue6LCD77yM5b+F5aGrCiAqLwpleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBsb2FkTW9uYWNvKGNiKSB7CiAgaWYgKG1vbmFjb0VpZHRvcikgewogICAgY2IobW9uYWNvRWlkdG9yKTsKICAgIHJldHVybjsKICB9CiAgdmFyIHZzID0gcGx1Z2luc0NvbmZpZy5tb25hY29FZGl0b3JVcmw7CgogIC8vIOS9v+eUqGVsZW1lbnQgdWnlrp7njrDliqDovb3mj5DnpLoKICB2YXIgbG9hZGluZyA9IEVMRU1FTlQuTG9hZGluZy5zZXJ2aWNlKHsKICAgIGZ1bGxzY3JlZW46IHRydWUsCiAgICBsb2NrOiB0cnVlLAogICAgdGV4dDogJ+e8lui+keWZqOi1hOa6kOWIneWni+WMluS4rS4uLicsCiAgICBzcGlubmVyOiAnZWwtaWNvbi1sb2FkaW5nJywKICAgIGJhY2tncm91bmQ6ICdyZ2JhKDI1NSwgMjU1LCAyNTUsIDAuNSknCiAgfSk7CiAgIXdpbmRvdy5yZXF1aXJlICYmICh3aW5kb3cucmVxdWlyZSA9IHt9KTsKICAhd2luZG93LnJlcXVpcmUucGF0aHMgJiYgKHdpbmRvdy5yZXF1aXJlLnBhdGhzID0ge30pOwogIHdpbmRvdy5yZXF1aXJlLnBhdGhzLnZzID0gdnM7CiAgbG9hZFNjcmlwdCgiIi5jb25jYXQodnMsICIvbG9hZGVyLmpzIiksIGZ1bmN0aW9uICgpIHsKICAgIHdpbmRvdy5yZXF1aXJlKFsndnMvZWRpdG9yL2VkaXRvci5tYWluJ10sIGZ1bmN0aW9uICgpIHsKICAgICAgbG9hZGluZy5jbG9zZSgpOwogICAgICBtb25hY29FaWR0b3IgPSB3aW5kb3cubW9uYWNvOwogICAgICBjYihtb25hY29FaWR0b3IpOwogICAgfSk7CiAgfSk7Cn0="}, {"version": 3, "names": ["loadScript", "ELEMENT", "pluginsConfig", "monacoEidtor", "loadMonaco", "cb", "vs", "monacoEditorUrl", "loading", "Loading", "service", "fullscreen", "lock", "text", "spinner", "background", "window", "require", "paths", "concat", "close", "monaco"], "sources": ["/Users/<USER>/develop/code/project/haijia/adminweb/src/utils/loadMonaco.js"], "sourcesContent": ["import loadScript from './loadScript'\nimport ELEMENT from 'element-ui'\nimport pluginsConfig from './pluginsConfig'\n\n// monaco-editor单例\nlet monacoEidtor\n\n/**\n * 动态加载monaco-editor cdn资源\n * @param {Function} cb 回调，必填\n */\nexport default function loadMonaco(cb) {\n  if (monacoEidtor) {\n    cb(monacoEidtor)\n    return\n  }\n\n  const { monacoEditorUrl: vs } = pluginsConfig\n\n  // 使用element ui实现加载提示\n  const loading = ELEMENT.Loading.service({\n    fullscreen: true,\n    lock: true,\n    text: '编辑器资源初始化中...',\n    spinner: 'el-icon-loading',\n    background: 'rgba(255, 255, 255, 0.5)'\n  })\n\n  !window.require && (window.require = {})\n  !window.require.paths && (window.require.paths = {})\n  window.require.paths.vs = vs\n\n  loadScript(`${vs}/loader.js`, () => {\n    window.require(['vs/editor/editor.main'], () => {\n      loading.close()\n      monacoEidtor = window.monaco\n      cb(monacoEidtor)\n    })\n  })\n}\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,cAAc;AACrC,OAAOC,OAAO,MAAM,YAAY;AAChC,OAAOC,aAAa,MAAM,iBAAiB;;AAE3C;AACA,IAAIC,YAAY;;AAEhB;AACA;AACA;AACA;AACA,eAAe,SAASC,UAAUA,CAACC,EAAE,EAAE;EACrC,IAAIF,YAAY,EAAE;IAChBE,EAAE,CAACF,YAAY,CAAC;IAChB;EACF;EAEA,IAAyBG,EAAE,GAAKJ,aAAa,CAArCK,eAAe;;EAEvB;EACA,IAAMC,OAAO,GAAGP,OAAO,CAACQ,OAAO,CAACC,OAAO,CAAC;IACtCC,UAAU,EAAE,IAAI;IAChBC,IAAI,EAAE,IAAI;IACVC,IAAI,EAAE,cAAc;IACpBC,OAAO,EAAE,iBAAiB;IAC1BC,UAAU,EAAE;EACd,CAAC,CAAC;EAEF,CAACC,MAAM,CAACC,OAAO,KAAKD,MAAM,CAACC,OAAO,GAAG,CAAC,CAAC,CAAC;EACxC,CAACD,MAAM,CAACC,OAAO,CAACC,KAAK,KAAKF,MAAM,CAACC,OAAO,CAACC,KAAK,GAAG,CAAC,CAAC,CAAC;EACpDF,MAAM,CAACC,OAAO,CAACC,KAAK,CAACZ,EAAE,GAAGA,EAAE;EAE5BN,UAAU,IAAAmB,MAAA,CAAIb,EAAE,iBAAc,YAAM;IAClCU,MAAM,CAACC,OAAO,CAAC,CAAC,uBAAuB,CAAC,EAAE,YAAM;MAC9CT,OAAO,CAACY,KAAK,CAAC,CAAC;MACfjB,YAAY,GAAGa,MAAM,CAACK,MAAM;MAC5BhB,EAAE,CAACF,YAAY,CAAC;IAClB,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ", "ignoreList": []}]}