{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/components/tinymce/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/components/tinymce/index.vue", "mtime": 1641277326000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["loadTinymce", "plugins", "toolbar", "debounce", "num", "props", "id", "type", "String", "default", "_default", "concat", "Date", "value", "data", "tinymceId", "mounted", "_this", "<PERSON><PERSON><PERSON>", "require", "conf", "selector", "language", "menubar", "height", "branding", "object_resizing", "end_container_on_empty_block", "powerpaste_word_import", "code_dialog_height", "code_dialog_width", "advlist_bullet_styles", "advlist_number_styles", "default_link_target", "link_title", "nonbreaking_force_tab", "Object", "assign", "$attrs", "init_instance_callback", "editor", "<PERSON><PERSON><PERSON><PERSON>", "vModel", "init", "destroyed", "destroyTinymce", "methods", "_this2", "debounceSetContent", "$watch", "val", "prevVal", "get<PERSON>ontent", "toString", "call", "on", "$emit", "window", "get", "destroy"], "sources": ["src/components/tinymce/index.vue"], "sourcesContent": ["<template>\n  <textarea :id=\"tinymceId\" style=\"visibility: hidden\" />\n</template>\n\n<script>\nimport loadTinymce from '@/utils/loadTinymce'\nimport { plugins, toolbar } from './config'\nimport { debounce } from 'throttle-debounce'\n\nlet num = 1\n\nexport default {\n  props: {\n    id: {\n      type: String,\n      default: () => {\n        num === 10000 && (num = 1)\n        return `tinymce${+new Date()}${num++}`\n      }\n    },\n    value: {\n      default: ''\n    }\n  },\n  data() {\n    return {\n      tinymceId: this.id\n    }\n  },\n  mounted() {\n    loadTinymce(tinymce => {\n      // eslint-disable-next-line global-require\n      require('./zh_CN')\n      let conf = {\n        selector: `#${this.tinymceId}`,\n        language: 'zh_CN',\n        menubar: 'file edit insert view format table',\n        plugins,\n        toolbar,\n        height: 300,\n        branding: false,\n        object_resizing: false,\n        end_container_on_empty_block: true,\n        powerpaste_word_import: 'clean',\n        code_dialog_height: 450,\n        code_dialog_width: 1000,\n        advlist_bullet_styles: 'square',\n        advlist_number_styles: 'default',\n        default_link_target: '_blank',\n        link_title: false,\n        nonbreaking_force_tab: true\n      }\n      conf = Object.assign(conf, this.$attrs)\n      conf.init_instance_callback = editor => {\n        if (this.value) editor.setContent(this.value)\n        this.vModel(editor)\n      }\n      tinymce.init(conf)\n    })\n  },\n  destroyed() {\n    this.destroyTinymce()\n  },\n  methods: {\n    vModel(editor) {\n      // 控制连续写入时setContent的触发频率\n      const debounceSetContent = debounce(250, editor.setContent)\n      this.$watch('value', (val, prevVal) => {\n        if (editor && val !== prevVal && val !== editor.getContent()) {\n          if (typeof val !== 'string') val = val.toString()\n          debounceSetContent.call(editor, val)\n        }\n      })\n\n      editor.on('change keyup undo redo', () => {\n        this.$emit('input', editor.getContent())\n      })\n    },\n    destroyTinymce() {\n      if (!window.tinymce) return\n      const tinymce = window.tinymce.get(this.tinymceId)\n      if (tinymce) {\n        tinymce.destroy()\n      }\n    }\n  }\n}\n</script>\n"], "mappings": ";;;;;AAKA,OAAAA,WAAA;AACA,SAAAC,OAAA,EAAAC,OAAA;AACA,SAAAC,QAAA;AAEA,IAAAC,GAAA;AAEA;EACAC,KAAA;IACAC,EAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA,WAAAC,SAAA;QACAN,GAAA,eAAAA,GAAA;QACA,iBAAAO,MAAA,MAAAC,IAAA,IAAAD,MAAA,CAAAP,GAAA;MACA;IACA;IACAS,KAAA;MACAJ,OAAA;IACA;EACA;EACAK,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA,OAAAT;IACA;EACA;EACAU,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACAjB,WAAA,WAAAkB,OAAA;MACA;MACAC,OAAA;MACA,IAAAC,IAAA;QACAC,QAAA,MAAAV,MAAA,CAAAM,KAAA,CAAAF,SAAA;QACAO,QAAA;QACAC,OAAA;QACAtB,OAAA,EAAAA,OAAA;QACAC,OAAA,EAAAA,OAAA;QACAsB,MAAA;QACAC,QAAA;QACAC,eAAA;QACAC,4BAAA;QACAC,sBAAA;QACAC,kBAAA;QACAC,iBAAA;QACAC,qBAAA;QACAC,qBAAA;QACAC,mBAAA;QACAC,UAAA;QACAC,qBAAA;MACA;MACAf,IAAA,GAAAgB,MAAA,CAAAC,MAAA,CAAAjB,IAAA,EAAAH,KAAA,CAAAqB,MAAA;MACAlB,IAAA,CAAAmB,sBAAA,aAAAC,MAAA;QACA,IAAAvB,KAAA,CAAAJ,KAAA,EAAA2B,MAAA,CAAAC,UAAA,CAAAxB,KAAA,CAAAJ,KAAA;QACAI,KAAA,CAAAyB,MAAA,CAAAF,MAAA;MACA;MACAtB,OAAA,CAAAyB,IAAA,CAAAvB,IAAA;IACA;EACA;EACAwB,SAAA,WAAAA,UAAA;IACA,KAAAC,cAAA;EACA;EACAC,OAAA;IACAJ,MAAA,WAAAA,OAAAF,MAAA;MAAA,IAAAO,MAAA;MACA;MACA,IAAAC,kBAAA,GAAA7C,QAAA,MAAAqC,MAAA,CAAAC,UAAA;MACA,KAAAQ,MAAA,oBAAAC,GAAA,EAAAC,OAAA;QACA,IAAAX,MAAA,IAAAU,GAAA,KAAAC,OAAA,IAAAD,GAAA,KAAAV,MAAA,CAAAY,UAAA;UACA,WAAAF,GAAA,eAAAA,GAAA,GAAAA,GAAA,CAAAG,QAAA;UACAL,kBAAA,CAAAM,IAAA,CAAAd,MAAA,EAAAU,GAAA;QACA;MACA;MAEAV,MAAA,CAAAe,EAAA;QACAR,MAAA,CAAAS,KAAA,UAAAhB,MAAA,CAAAY,UAAA;MACA;IACA;IACAP,cAAA,WAAAA,eAAA;MACA,KAAAY,MAAA,CAAAvC,OAAA;MACA,IAAAA,OAAA,GAAAuC,MAAA,CAAAvC,OAAA,CAAAwC,GAAA,MAAA3C,SAAA;MACA,IAAAG,OAAA;QACAA,OAAA,CAAAyC,OAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}