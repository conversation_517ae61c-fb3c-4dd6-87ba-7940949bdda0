{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/api/login.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/api/login.js", "mtime": 1664705570000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QgZnJvbSAnQC91dGlscy9yZXF1ZXN0JzsKCi8vIOeZu+W9leaWueazlQpleHBvcnQgZnVuY3Rpb24gbG9naW4odXNlcm5hbWUsIHBhc3N3b3JkLCBjb2RlLCB1dWlkKSB7CiAgdmFyIGRhdGEgPSB7CiAgICB1c2VybmFtZTogdXNlcm5hbWUsCiAgICBwYXNzd29yZDogcGFzc3dvcmQsCiAgICBjb2RlOiBjb2RlLAogICAgdXVpZDogdXVpZAogIH07CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL2xvZ2luJywKICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgZGF0YTogZGF0YQogIH0pOwp9CgovLyDojrflj5bnlKjmiLfor6bnu4bkv6Hmga8KZXhwb3J0IGZ1bmN0aW9uIGdldEluZm8oKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL2dldEluZm8nLAogICAgbWV0aG9kOiAnZ2V0JwogIH0pOwp9CgovLyDpgIDlh7rmlrnms5UKZXhwb3J0IGZ1bmN0aW9uIGxvZ291dCgpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvbG9nb3V0JywKICAgIG1ldGhvZDogJ3Bvc3QnCiAgfSk7Cn0KCi8vIOiOt+WPlumqjOivgeeggQpleHBvcnQgZnVuY3Rpb24gZ2V0Q29kZUltZygpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvY2FwdGNoYUltYWdlJywKICAgIG1ldGhvZDogJ2dldCcKICB9KTsKfQoKLy8g6I635Y+W6aqM6K+B56CBCmV4cG9ydCBmdW5jdGlvbiByZXNldFB3ZChkYXRhKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL3Jlc2V0UHdkJywKICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgZGF0YTogZGF0YQogIH0pOwp9CgovL+mHjee9ruWvhueggQpleHBvcnQgZnVuY3Rpb24gcmVzZXRQd2RCeVBob25lKGRhdGEpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvcmVzZXRQd2RCeVBob25lJywKICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgZGF0YTogZGF0YQogIH0pOwp9Ci8vIOiOt+WPlumqjOivgeeggQpleHBvcnQgZnVuY3Rpb24gcmVzZXRDYXB0Y2hhKGRhdGEpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvcmVzZXRDYXB0Y2hhJywKICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgZGF0YTogZGF0YQogIH0pOwp9"}, {"version": 3, "names": ["request", "login", "username", "password", "code", "uuid", "data", "url", "method", "getInfo", "logout", "getCodeImg", "resetPwd", "resetPwdByPhone", "resetCaptcha"], "sources": ["/Users/<USER>/develop/code/project/haijia/adminweb/src/api/login.js"], "sourcesContent": ["import request from '@/utils/request'\n\n// 登录方法\nexport function login(username, password, code, uuid) {\n  const data = {\n    username,\n    password,\n    code,\n    uuid\n  }\n  return request({\n    url: '/login',\n    method: 'post',\n    data: data\n  })\n}\n\n// 获取用户详细信息\nexport function getInfo() {\n  return request({\n    url: '/getInfo',\n    method: 'get'\n  })\n}\n\n// 退出方法\nexport function logout() {\n  return request({\n    url: '/logout',\n    method: 'post'\n  })\n}\n\n// 获取验证码\nexport function getCodeImg() {\n  return request({\n    url: '/captchaImage',\n    method: 'get'\n  })\n}\n\n// 获取验证码\nexport function resetPwd(data) {\n  return request({\n    url: '/resetPwd',\n    method: 'post',\n    data: data\n  })\n}\n\n//重置密码\nexport function resetPwdByPhone(data) {\n  return request({\n    url: '/resetPwdByPhone',\n    method: 'post',\n    data: data\n  })\n}\n// 获取验证码\nexport function resetCaptcha(data) {\n  return request({\n    url: '/resetCaptcha',\n    method: 'post',\n    data: data\n  })\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;;AAErC;AACA,OAAO,SAASC,KAAKA,CAACC,QAAQ,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,IAAI,EAAE;EACpD,IAAMC,IAAI,GAAG;IACXJ,QAAQ,EAARA,QAAQ;IACRC,QAAQ,EAARA,QAAQ;IACRC,IAAI,EAAJA,IAAI;IACJC,IAAI,EAAJA;EACF,CAAC;EACD,OAAOL,OAAO,CAAC;IACbO,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASG,OAAOA,CAAA,EAAG;EACxB,OAAOT,OAAO,CAAC;IACbO,GAAG,EAAE,UAAU;IACfC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASE,MAAMA,CAAA,EAAG;EACvB,OAAOV,OAAO,CAAC;IACbO,GAAG,EAAE,SAAS;IACdC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASG,UAAUA,CAAA,EAAG;EAC3B,OAAOX,OAAO,CAAC;IACbO,GAAG,EAAE,eAAe;IACpBC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASI,QAAQA,CAACN,IAAI,EAAE;EAC7B,OAAON,OAAO,CAAC;IACbO,GAAG,EAAE,WAAW;IAChBC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASO,eAAeA,CAACP,IAAI,EAAE;EACpC,OAAON,OAAO,CAAC;IACbO,GAAG,EAAE,kBAAkB;IACvBC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;AACA;AACA,OAAO,SAASQ,YAAYA,CAACR,IAAI,EAAE;EACjC,OAAON,OAAO,CAAC;IACbO,GAAG,EAAE,eAAe;IACpBC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ", "ignoreList": []}]}