{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/flowable/task/form/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/flowable/task/form/index.vue", "mtime": 1641277326000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGxpc3RGb3JtLCBnZXRGb3JtLCBkZWxGb3JtLCBhZGRGb3JtLCB1cGRhdGVGb3JtLCBleHBvcnRGb3JtIH0gZnJvbSAiQC9hcGkvZmxvd2FibGUvZm9ybSI7CmltcG9ydCBFZGl0b3IgZnJvbSAnQC9jb21wb25lbnRzL0VkaXRvcic7CmltcG9ydCBQYXJzZXIgZnJvbSAnQC9jb21wb25lbnRzL3BhcnNlci9QYXJzZXInOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogIkZvcm0iLAogIGNvbXBvbmVudHM6IHsKICAgIEVkaXRvcjogRWRpdG9yLAogICAgUGFyc2VyOiBQYXJzZXIKICB9LAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICAvLyDpga7nvanlsYIKICAgICAgbG9hZGluZzogdHJ1ZSwKICAgICAgLy8g6YCJ5Lit5pWw57uECiAgICAgIGlkczogW10sCiAgICAgIC8vIOmdnuWNleS4quemgeeUqAogICAgICBzaW5nbGU6IHRydWUsCiAgICAgIC8vIOmdnuWkmuS4quemgeeUqAogICAgICBtdWx0aXBsZTogdHJ1ZSwKICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2CiAgICAgIHNob3dTZWFyY2g6IHRydWUsCiAgICAgIC8vIOaAu+adoeaVsAogICAgICB0b3RhbDogMCwKICAgICAgLy8g5rWB56iL6KGo5Y2V6KGo5qC85pWw5o2uCiAgICAgIGZvcm1MaXN0OiBbXSwKICAgICAgLy8g5by55Ye65bGC5qCH6aKYCiAgICAgIHRpdGxlOiAiIiwKICAgICAgZm9ybUNvbmY6IHt9LAogICAgICAvLyDpu5jorqTooajljZXmlbDmja4KICAgICAgZm9ybUNvbmZPcGVuOiBmYWxzZSwKICAgICAgZm9ybVRpdGxlOiAiIiwKICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGCCiAgICAgIG9wZW46IGZhbHNlLAogICAgICAvLyDmn6Xor6Llj4LmlbAKICAgICAgcXVlcnlQYXJhbXM6IHsKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICBmb3JtTmFtZTogbnVsbCwKICAgICAgICBmb3JtQ29udGVudDogbnVsbAogICAgICB9LAogICAgICAvLyDooajljZXlj4LmlbAKICAgICAgZm9ybToge30sCiAgICAgIC8vIOihqOWNleagoemqjAogICAgICBydWxlczoge30KICAgIH07CiAgfSwKICBjcmVhdGVkOiBmdW5jdGlvbiBjcmVhdGVkKCkgewogICAgdGhpcy5nZXRMaXN0KCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICAvKiog5p+l6K+i5rWB56iL6KGo5Y2V5YiX6KGoICovZ2V0TGlzdDogZnVuY3Rpb24gZ2V0TGlzdCgpIHsKICAgICAgdmFyIF90aGlzID0gdGhpczsKICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsKICAgICAgbGlzdEZvcm0odGhpcy5xdWVyeVBhcmFtcykudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICBfdGhpcy5mb3JtTGlzdCA9IHJlc3BvbnNlLnJvd3M7CiAgICAgICAgX3RoaXMudG90YWwgPSByZXNwb25zZS50b3RhbDsKICAgICAgICBfdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKICAgIC8vIOWPlua2iOaMiemSrgogICAgY2FuY2VsOiBmdW5jdGlvbiBjYW5jZWwoKSB7CiAgICAgIHRoaXMub3BlbiA9IGZhbHNlOwogICAgICB0aGlzLnJlc2V0KCk7CiAgICB9LAogICAgLy8g6KGo5Y2V6YeN572uCiAgICByZXNldDogZnVuY3Rpb24gcmVzZXQoKSB7CiAgICAgIHRoaXMuZm9ybSA9IHsKICAgICAgICBmb3JtSWQ6IG51bGwsCiAgICAgICAgZm9ybU5hbWU6IG51bGwsCiAgICAgICAgZm9ybUNvbnRlbnQ6IG51bGwsCiAgICAgICAgY3JlYXRlVGltZTogbnVsbCwKICAgICAgICB1cGRhdGVUaW1lOiBudWxsLAogICAgICAgIGNyZWF0ZUJ5OiBudWxsLAogICAgICAgIHVwZGF0ZUJ5OiBudWxsLAogICAgICAgIHJlbWFyazogbnVsbAogICAgICB9OwogICAgICB0aGlzLnJlc2V0Rm9ybSgiZm9ybSIpOwogICAgfSwKICAgIC8qKiDmkJzntKLmjInpkq7mk43kvZwgKi9oYW5kbGVRdWVyeTogZnVuY3Rpb24gaGFuZGxlUXVlcnkoKSB7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDE7CiAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgfSwKICAgIC8qKiDph43nva7mjInpkq7mk43kvZwgKi9yZXNldFF1ZXJ5OiBmdW5jdGlvbiByZXNldFF1ZXJ5KCkgewogICAgICB0aGlzLnJlc2V0Rm9ybSgicXVlcnlGb3JtIik7CiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsKICAgIH0sCiAgICAvLyDlpJrpgInmoYbpgInkuK3mlbDmja4KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZTogZnVuY3Rpb24gaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgewogICAgICB0aGlzLmlkcyA9IHNlbGVjdGlvbi5tYXAoZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICByZXR1cm4gaXRlbS5mb3JtSWQ7CiAgICAgIH0pOwogICAgICB0aGlzLnNpbmdsZSA9IHNlbGVjdGlvbi5sZW5ndGggIT09IDE7CiAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aDsKICAgIH0sCiAgICAvKiog6KGo5Y2V6YWN572u5L+h5oGvICovaGFuZGxlRGV0YWlsOiBmdW5jdGlvbiBoYW5kbGVEZXRhaWwocm93KSB7CiAgICAgIHRoaXMuZm9ybUNvbmZPcGVuID0gdHJ1ZTsKICAgICAgdGhpcy5mb3JtVGl0bGUgPSAi5rWB56iL6KGo5Y2V6YWN572u6K+m57uGIjsKICAgICAgdGhpcy5mb3JtQ29uZiA9IEpTT04ucGFyc2Uocm93LmZvcm1Db250ZW50KTsKICAgIH0sCiAgICAvKiog5paw5aKe5oyJ6ZKu5pON5L2cICovaGFuZGxlQWRkOiBmdW5jdGlvbiBoYW5kbGVBZGQoKSB7CiAgICAgIC8vIHRoaXMucmVzZXQoKTsKICAgICAgLy8gdGhpcy5vcGVuID0gdHJ1ZTsKICAgICAgLy8gdGhpcy50aXRsZSA9ICLmt7vliqDmtYHnqIvooajljZUiOwogICAgICB0aGlzLiRyb3V0ZXIucHVzaCh7CiAgICAgICAgcGF0aDogJy90b29sL2J1aWxkL2luZGV4JywKICAgICAgICBxdWVyeTogewogICAgICAgICAgZm9ybUlkOiBudWxsCiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvKiog5L+u5pS55oyJ6ZKu5pON5L2cICovaGFuZGxlVXBkYXRlOiBmdW5jdGlvbiBoYW5kbGVVcGRhdGUocm93KSB7CiAgICAgIC8vIHRoaXMucmVzZXQoKTsKICAgICAgLy8gY29uc3QgZm9ybUlkID0gcm93LmZvcm1JZCB8fCB0aGlzLmlkcwogICAgICAvLyBnZXRGb3JtKGZvcm1JZCkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgIC8vICAgdGhpcy5mb3JtID0gcmVzcG9uc2UuZGF0YTsKICAgICAgLy8gICB0aGlzLm9wZW4gPSB0cnVlOwogICAgICAvLyAgIHRoaXMudGl0bGUgPSAi5L+u5pS55rWB56iL6KGo5Y2VIjsKICAgICAgLy8gfSk7CiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHsKICAgICAgICBwYXRoOiAnL3Rvb2wvYnVpbGQvaW5kZXgnLAogICAgICAgIHF1ZXJ5OiB7CiAgICAgICAgICBmb3JtSWQ6IHJvdy5mb3JtSWQKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDmj5DkuqTmjInpkq4gKi9zdWJtaXRGb3JtOiBmdW5jdGlvbiBzdWJtaXRGb3JtKCkgewogICAgICB2YXIgX3RoaXMyID0gdGhpczsKICAgICAgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKGZ1bmN0aW9uICh2YWxpZCkgewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgaWYgKF90aGlzMi5mb3JtLmZvcm1JZCAhPSBudWxsKSB7CiAgICAgICAgICAgIHVwZGF0ZUZvcm0oX3RoaXMyLmZvcm0pLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgICAgICAgX3RoaXMyLm1zZ1N1Y2Nlc3MoIuS/ruaUueaIkOWKnyIpOwogICAgICAgICAgICAgIF90aGlzMi5vcGVuID0gZmFsc2U7CiAgICAgICAgICAgICAgX3RoaXMyLmdldExpc3QoKTsKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICBhZGRGb3JtKF90aGlzMi5mb3JtKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgICAgICAgIF90aGlzMi5tc2dTdWNjZXNzKCLmlrDlop7miJDlip8iKTsKICAgICAgICAgICAgICBfdGhpczIub3BlbiA9IGZhbHNlOwogICAgICAgICAgICAgIF90aGlzMi5nZXRMaXN0KCk7CiAgICAgICAgICAgIH0pOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLyoqIOWIoOmZpOaMiemSruaTjeS9nCAqL2hhbmRsZURlbGV0ZTogZnVuY3Rpb24gaGFuZGxlRGVsZXRlKHJvdykgewogICAgICB2YXIgX3RoaXMzID0gdGhpczsKICAgICAgdmFyIGZvcm1JZHMgPSByb3cuZm9ybUlkIHx8IHRoaXMuaWRzOwogICAgICB0aGlzLiRjb25maXJtKCfmmK/lkKbnoa7orqTliKDpmaTmtYHnqIvooajljZXnvJblj7fkuLoiJyArIGZvcm1JZHMgKyAnIueahOaVsOaNrumhuT8nLCAi6K2m5ZGKIiwgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAi56Gu5a6aIiwKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAi5Y+W5raIIiwKICAgICAgICB0eXBlOiAid2FybmluZyIKICAgICAgfSkudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgcmV0dXJuIGRlbEZvcm0oZm9ybUlkcyk7CiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgIF90aGlzMy5nZXRMaXN0KCk7CiAgICAgICAgX3RoaXMzLm1zZ1N1Y2Nlc3MoIuWIoOmZpOaIkOWKnyIpOwogICAgICB9KTsKICAgIH0sCiAgICAvKiog5a+85Ye65oyJ6ZKu5pON5L2cICovaGFuZGxlRXhwb3J0OiBmdW5jdGlvbiBoYW5kbGVFeHBvcnQoKSB7CiAgICAgIHZhciBfdGhpczQgPSB0aGlzOwogICAgICB2YXIgcXVlcnlQYXJhbXMgPSB0aGlzLnF1ZXJ5UGFyYW1zOwogICAgICB0aGlzLiRjb25maXJtKCfmmK/lkKbnoa7orqTlr7zlh7rmiYDmnInmtYHnqIvooajljZXmlbDmja7pobk/JywgIuitpuWRiiIsIHsKICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogIuehruWumiIsCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogIuWPlua2iCIsCiAgICAgICAgdHlwZTogIndhcm5pbmciCiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgIHJldHVybiBleHBvcnRGb3JtKHF1ZXJ5UGFyYW1zKTsKICAgICAgfSkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICBfdGhpczQuZG93bmxvYWQocmVzcG9uc2UubXNnKTsKICAgICAgfSk7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["listForm", "getForm", "delForm", "addForm", "updateForm", "exportForm", "Editor", "<PERSON><PERSON><PERSON>", "name", "components", "data", "loading", "ids", "single", "multiple", "showSearch", "total", "formList", "title", "formConf", "formConfOpen", "formTitle", "open", "queryParams", "pageNum", "pageSize", "formName", "formContent", "form", "rules", "created", "getList", "methods", "_this", "then", "response", "rows", "cancel", "reset", "formId", "createTime", "updateTime", "createBy", "updateBy", "remark", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "map", "item", "length", "handleDetail", "row", "JSON", "parse", "handleAdd", "$router", "push", "path", "query", "handleUpdate", "submitForm", "_this2", "$refs", "validate", "valid", "msgSuccess", "handleDelete", "_this3", "formIds", "$confirm", "confirmButtonText", "cancelButtonText", "type", "handleExport", "_this4", "download", "msg"], "sources": ["src/views/flowable/task/form/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"表单名称\" prop=\"formName\">\n        <el-input\n          v-model=\"queryParams.formName\"\n          placeholder=\"请输入表单名称\"\n          clearable\n          size=\"small\"\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"primary\"\n          plain\n          icon=\"el-icon-plus\"\n          size=\"mini\"\n          @click=\"handleAdd\"\n          v-hasPermi=\"['flowable:form:add']\"\n        >新增</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"success\"\n          plain\n          icon=\"el-icon-edit\"\n          size=\"mini\"\n          :disabled=\"single\"\n          @click=\"handleUpdate\"\n          v-hasPermi=\"['flowable:form:edit']\"\n        >修改</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"danger\"\n          plain\n          icon=\"el-icon-delete\"\n          size=\"mini\"\n          :disabled=\"multiple\"\n          @click=\"handleDelete\"\n          v-hasPermi=\"['flowable:form:remove']\"\n        >删除</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"warning\"\n          plain\n          icon=\"el-icon-download\"\n          size=\"mini\"\n          @click=\"handleExport\"\n          v-hasPermi=\"['flowable:form:export']\"\n        >导出</el-button>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <el-table v-loading=\"loading\" :data=\"formList\" @selection-change=\"handleSelectionChange\">\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column label=\"表单主键\" align=\"center\" prop=\"formId\" />\n      <el-table-column label=\"表单名称\" align=\"center\" prop=\"formName\" />\n      <el-table-column label=\"备注\" align=\"center\" prop=\"remark\" />\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-view\"\n            @click=\"handleDetail(scope.row)\"\n          >详情</el-button>\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-edit\"\n            @click=\"handleUpdate(scope.row)\"\n            v-hasPermi=\"['flowable:form:edit']\"\n          >修改</el-button>\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-delete\"\n            @click=\"handleDelete(scope.row)\"\n            v-hasPermi=\"['flowable:form:remove']\"\n          >删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 添加或修改流程表单对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"500px\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\n        <el-form-item label=\"表单名称\" prop=\"formName\">\n          <el-input v-model=\"form.formName\" placeholder=\"请输入表单名称\" />\n        </el-form-item>\n        <el-form-item label=\"表单内容\">\n          <editor v-model=\"form.formContent\" :min-height=\"192\"/>\n        </el-form-item>\n        <el-form-item label=\"备注\" prop=\"remark\">\n          <el-input v-model=\"form.remark\" placeholder=\"请输入备注\" />\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n    <!--表单配置详情-->\n    <el-dialog :title=\"formTitle\" :visible.sync=\"formConfOpen\" width=\"60%\" append-to-body>\n      <div class=\"test-form\">\n        <parser :key=\"new Date().getTime()\"  :form-conf=\"formConf\" />\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { listForm, getForm, delForm, addForm, updateForm, exportForm } from \"@/api/flowable/form\";\nimport Editor from '@/components/Editor';\nimport Parser from '@/components/parser/Parser'\nexport default {\n  name: \"Form\",\n  components: {\n    Editor,\n    Parser\n  },\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 流程表单表格数据\n      formList: [],\n      // 弹出层标题\n      title: \"\",\n      formConf: {}, // 默认表单数据\n      formConfOpen: false,\n      formTitle: \"\",\n      // 是否显示弹出层\n      open: false,\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        formName: null,\n        formContent: null,\n      },\n      // 表单参数\n      form: {},\n      // 表单校验\n      rules: {\n      }\n    };\n  },\n  created() {\n    this.getList();\n  },\n  methods: {\n    /** 查询流程表单列表 */\n    getList() {\n      this.loading = true;\n      listForm(this.queryParams).then(response => {\n        this.formList = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        formId: null,\n        formName: null,\n        formContent: null,\n        createTime: null,\n        updateTime: null,\n        createBy: null,\n        updateBy: null,\n        remark: null\n      };\n      this.resetForm(\"form\");\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.formId)\n      this.single = selection.length!==1\n      this.multiple = !selection.length\n    },\n    /** 表单配置信息 */\n    handleDetail(row){\n      this.formConfOpen = true;\n      this.formTitle = \"流程表单配置详细\";\n      this.formConf = JSON.parse(row.formContent)\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      // this.reset();\n      // this.open = true;\n      // this.title = \"添加流程表单\";\n      this.$router.push({ path: '/tool/build/index', query: {formId: null }})\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      // this.reset();\n      // const formId = row.formId || this.ids\n      // getForm(formId).then(response => {\n      //   this.form = response.data;\n      //   this.open = true;\n      //   this.title = \"修改流程表单\";\n      // });\n      this.$router.push({ path: '/tool/build/index', query: {formId: row.formId }})\n    },\n    /** 提交按钮 */\n    submitForm() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          if (this.form.formId != null) {\n            updateForm(this.form).then(response => {\n              this.msgSuccess(\"修改成功\");\n              this.open = false;\n              this.getList();\n            });\n          } else {\n            addForm(this.form).then(response => {\n              this.msgSuccess(\"新增成功\");\n              this.open = false;\n              this.getList();\n            });\n          }\n        }\n      });\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const formIds = row.formId || this.ids;\n      this.$confirm('是否确认删除流程表单编号为\"' + formIds + '\"的数据项?', \"警告\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      }).then(function() {\n        return delForm(formIds);\n      }).then(() => {\n        this.getList();\n        this.msgSuccess(\"删除成功\");\n      })\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      const queryParams = this.queryParams;\n      this.$confirm('是否确认导出所有流程表单数据项?', \"警告\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      }).then(function() {\n        return exportForm(queryParams);\n      }).then(response => {\n        this.download(response.msg);\n      })\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.test-form {\n  margin: 15px auto;\n  width: 800px;\n  padding: 15px;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoIA,SAAAA,QAAA,EAAAC,OAAA,EAAAC,OAAA,EAAAC,OAAA,EAAAC,UAAA,EAAAC,UAAA;AACA,OAAAC,MAAA;AACA,OAAAC,MAAA;AACA;EACAC,IAAA;EACAC,UAAA;IACAH,MAAA,EAAAA,MAAA;IACAC,MAAA,EAAAA;EACA;EACAG,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,QAAA;MACA;MACAC,KAAA;MACAC,QAAA;MAAA;MACAC,YAAA;MACAC,SAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,QAAA;QACAC,WAAA;MACA;MACA;MACAC,IAAA;MACA;MACAC,KAAA,GACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA,eACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAAtB,OAAA;MACAX,QAAA,MAAAuB,WAAA,EAAAW,IAAA,WAAAC,QAAA;QACAF,KAAA,CAAAhB,QAAA,GAAAkB,QAAA,CAAAC,IAAA;QACAH,KAAA,CAAAjB,KAAA,GAAAmB,QAAA,CAAAnB,KAAA;QACAiB,KAAA,CAAAtB,OAAA;MACA;IACA;IACA;IACA0B,MAAA,WAAAA,OAAA;MACA,KAAAf,IAAA;MACA,KAAAgB,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAAV,IAAA;QACAW,MAAA;QACAb,QAAA;QACAC,WAAA;QACAa,UAAA;QACAC,UAAA;QACAC,QAAA;QACAC,QAAA;QACAC,MAAA;MACA;MACA,KAAAC,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAAvB,WAAA,CAAAC,OAAA;MACA,KAAAO,OAAA;IACA;IACA,aACAgB,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAAC,WAAA;IACA;IACA;IACAE,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAArC,GAAA,GAAAqC,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAZ,MAAA;MAAA;MACA,KAAA1B,MAAA,GAAAoC,SAAA,CAAAG,MAAA;MACA,KAAAtC,QAAA,IAAAmC,SAAA,CAAAG,MAAA;IACA;IACA,aACAC,YAAA,WAAAA,aAAAC,GAAA;MACA,KAAAlC,YAAA;MACA,KAAAC,SAAA;MACA,KAAAF,QAAA,GAAAoC,IAAA,CAAAC,KAAA,CAAAF,GAAA,CAAA3B,WAAA;IACA;IACA,aACA8B,SAAA,WAAAA,UAAA;MACA;MACA;MACA;MACA,KAAAC,OAAA,CAAAC,IAAA;QAAAC,IAAA;QAAAC,KAAA;UAAAtB,MAAA;QAAA;MAAA;IACA;IACA,aACAuB,YAAA,WAAAA,aAAAR,GAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,KAAAI,OAAA,CAAAC,IAAA;QAAAC,IAAA;QAAAC,KAAA;UAAAtB,MAAA,EAAAe,GAAA,CAAAf;QAAA;MAAA;IACA;IACA,WACAwB,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAH,MAAA,CAAApC,IAAA,CAAAW,MAAA;YACAnC,UAAA,CAAA4D,MAAA,CAAApC,IAAA,EAAAM,IAAA,WAAAC,QAAA;cACA6B,MAAA,CAAAI,UAAA;cACAJ,MAAA,CAAA1C,IAAA;cACA0C,MAAA,CAAAjC,OAAA;YACA;UACA;YACA5B,OAAA,CAAA6D,MAAA,CAAApC,IAAA,EAAAM,IAAA,WAAAC,QAAA;cACA6B,MAAA,CAAAI,UAAA;cACAJ,MAAA,CAAA1C,IAAA;cACA0C,MAAA,CAAAjC,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACAsC,YAAA,WAAAA,aAAAf,GAAA;MAAA,IAAAgB,MAAA;MACA,IAAAC,OAAA,GAAAjB,GAAA,CAAAf,MAAA,SAAA3B,GAAA;MACA,KAAA4D,QAAA,oBAAAD,OAAA;QACAE,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAAzC,IAAA;QACA,OAAAhC,OAAA,CAAAqE,OAAA;MACA,GAAArC,IAAA;QACAoC,MAAA,CAAAvC,OAAA;QACAuC,MAAA,CAAAF,UAAA;MACA;IACA;IACA,aACAQ,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,IAAAtD,WAAA,QAAAA,WAAA;MACA,KAAAiD,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAAzC,IAAA;QACA,OAAA7B,UAAA,CAAAkB,WAAA;MACA,GAAAW,IAAA,WAAAC,QAAA;QACA0C,MAAA,CAAAC,QAAA,CAAA3C,QAAA,CAAA4C,GAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}