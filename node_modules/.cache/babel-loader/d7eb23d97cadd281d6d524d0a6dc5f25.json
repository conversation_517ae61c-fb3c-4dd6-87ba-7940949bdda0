{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/utils/loadBeautifier.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/utils/loadBeautifier.js", "mtime": 1641277326000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IGxvYWRTY3JpcHQgZnJvbSAnLi9sb2FkU2NyaXB0JzsKaW1wb3J0IEVMRU1FTlQgZnJvbSAnZWxlbWVudC11aSc7CmltcG9ydCBwbHVnaW5zQ29uZmlnIGZyb20gJy4vcGx1Z2luc0NvbmZpZyc7CnZhciBiZWF1dGlmaWVyT2JqOwpleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBsb2FkQmVhdXRpZmllcihjYikgewogIHZhciBiZWF1dGlmaWVyVXJsID0gcGx1Z2luc0NvbmZpZy5iZWF1dGlmaWVyVXJsOwogIGlmIChiZWF1dGlmaWVyT2JqKSB7CiAgICBjYihiZWF1dGlmaWVyT2JqKTsKICAgIHJldHVybjsKICB9CiAgdmFyIGxvYWRpbmcgPSBFTEVNRU5ULkxvYWRpbmcuc2VydmljZSh7CiAgICBmdWxsc2NyZWVuOiB0cnVlLAogICAgbG9jazogdHJ1ZSwKICAgIHRleHQ6ICfmoLzlvI/ljJbotYTmupDliqDovb3kuK0uLi4nLAogICAgc3Bpbm5lcjogJ2VsLWljb24tbG9hZGluZycsCiAgICBiYWNrZ3JvdW5kOiAncmdiYSgyNTUsIDI1NSwgMjU1LCAwLjUpJwogIH0pOwogIGxvYWRTY3JpcHQoYmVhdXRpZmllclVybCwgZnVuY3Rpb24gKCkgewogICAgbG9hZGluZy5jbG9zZSgpOwogICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIG5vLXVuZGVmCiAgICBiZWF1dGlmaWVyT2JqID0gYmVhdXRpZmllcjsKICAgIGNiKGJlYXV0aWZpZXJPYmopOwogIH0pOwp9"}, {"version": 3, "names": ["loadScript", "ELEMENT", "pluginsConfig", "beautifierObj", "loadBeautifier", "cb", "beautifierUrl", "loading", "Loading", "service", "fullscreen", "lock", "text", "spinner", "background", "close", "beautifier"], "sources": ["/Users/<USER>/develop/code/project/haijia/adminweb/src/utils/loadBeautifier.js"], "sourcesContent": ["import loadScript from './loadScript'\nimport ELEMENT from 'element-ui'\nimport pluginsConfig from './pluginsConfig'\n\nlet beautifierObj\n\nexport default function loadBeautifier(cb) {\n  const { beautifierUrl } = pluginsConfig\n  if (beautifierObj) {\n    cb(beautifierObj)\n    return\n  }\n\n  const loading = ELEMENT.Loading.service({\n    fullscreen: true,\n    lock: true,\n    text: '格式化资源加载中...',\n    spinner: 'el-icon-loading',\n    background: 'rgba(255, 255, 255, 0.5)'\n  })\n\n  loadScript(beautifierUrl, () => {\n    loading.close()\n    // eslint-disable-next-line no-undef\n    beautifierObj = beautifier\n    cb(beautifierObj)\n  })\n}\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,cAAc;AACrC,OAAOC,OAAO,MAAM,YAAY;AAChC,OAAOC,aAAa,MAAM,iBAAiB;AAE3C,IAAIC,aAAa;AAEjB,eAAe,SAASC,cAAcA,CAACC,EAAE,EAAE;EACzC,IAAQC,aAAa,GAAKJ,aAAa,CAA/BI,aAAa;EACrB,IAAIH,aAAa,EAAE;IACjBE,EAAE,CAACF,aAAa,CAAC;IACjB;EACF;EAEA,IAAMI,OAAO,GAAGN,OAAO,CAACO,OAAO,CAACC,OAAO,CAAC;IACtCC,UAAU,EAAE,IAAI;IAChBC,IAAI,EAAE,IAAI;IACVC,IAAI,EAAE,aAAa;IACnBC,OAAO,EAAE,iBAAiB;IAC1BC,UAAU,EAAE;EACd,CAAC,CAAC;EAEFd,UAAU,CAACM,aAAa,EAAE,YAAM;IAC9BC,OAAO,CAACQ,KAAK,CAAC,CAAC;IACf;IACAZ,aAAa,GAAGa,UAAU;IAC1BX,EAAE,CAACF,aAAa,CAAC;EACnB,CAAC,CAAC;AACJ", "ignoreList": []}]}