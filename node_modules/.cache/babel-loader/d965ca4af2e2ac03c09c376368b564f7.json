{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/flowable/task/record/view.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/flowable/task/record/view.vue", "mtime": 1717760123606}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js", "mtime": 1751171659043}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGZsb3dSZWNvcmQsIGdldEhpc0lucyB9IGZyb20gIkAvYXBpL2Zsb3dhYmxlL2ZpbmlzaGVkIjsKaW1wb3J0IFBhcnNlciBmcm9tICJAL2NvbXBvbmVudHMvcGFyc2VyL1BhcnNlciI7CmltcG9ydCBVcGxvYWQgZnJvbSAiQC9jb21wb25lbnRzL0ZpbGVVcGxvYWQiOwppbXBvcnQgeyBkZWZpbml0aW9uU3RhcnRCeUtleSwgZ2V0UHJvY2Vzc1ZhcmlhYmxlcywgcmVhZFhtbEJ5S2V5LCBnZXRGbG93Vmlld2VyIGFzIF9nZXRGbG93Vmlld2VyIH0gZnJvbSAiQC9hcGkvZmxvd2FibGUvZGVmaW5pdGlvbiI7CmltcG9ydCB7IGNvbXBsZXRlLCByZWplY3RUYXNrLCByZXR1cm5MaXN0LCByZXR1cm5UYXNrLCBnZXROZXh0Rmxvd05vZGUgYXMgX2dldE5leHRGbG93Tm9kZSwgZGVsZWdhdGUsIGVuZFRhc2sgfSBmcm9tICJAL2FwaS9mbG93YWJsZS90b2RvIjsKaW1wb3J0IGZsb3cgZnJvbSAiQC92aWV3cy9mbG93YWJsZS90YXNrL3JlY29yZC9mbG93IjsKaW1wb3J0IHsgdHJlZXNlbGVjdCB9IGZyb20gIkAvYXBpL3N5c3RlbS9kZXB0IjsKaW1wb3J0ICJAcmlvcGhhZS92dWUtdHJlZXNlbGVjdC9kaXN0L3Z1ZS10cmVlc2VsZWN0LmNzcyI7CmltcG9ydCBUcmVlc2VsZWN0IGZyb20gIkByaW9waGFlL3Z1ZS10cmVlc2VsZWN0IjsKaW1wb3J0IHsgbGlzdFVzZXIgfSBmcm9tICJAL2FwaS9zeXN0ZW0vdXNlciI7CmltcG9ydCBtb21lbnQgZnJvbSAibW9tZW50IjsKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICJSZWNvcmQiLAogIGNvbXBvbmVudHM6IHsKICAgIFBhcnNlcjogUGFyc2VyLAogICAgZmxvdzogZmxvdywKICAgIFRyZWVzZWxlY3Q6IFRyZWVzZWxlY3QsCiAgICBVcGxvYWQ6IFVwbG9hZAogIH0sCiAgcHJvcHM6IHsKICAgIHByb2NEZWZLZXk6IHsKICAgICAgdHlwZTogU3RyaW5nLAogICAgICBkZWZhdWx0OiB1bmRlZmluZWQKICAgIH0sCiAgICB0YXNrSWQ6IHsKICAgICAgdHlwZTogU3RyaW5nLAogICAgICBkZWZhdWx0OiB1bmRlZmluZWQKICAgIH0sCiAgICBwcm9jSW5zSWQ6IHsKICAgICAgdHlwZTogU3RyaW5nLAogICAgICBkZWZhdWx0OiB1bmRlZmluZWQKICAgIH0sCiAgICBiaXpLZXk6IHsKICAgICAgdHlwZTogU3RyaW5nLAogICAgICBkZWZhdWx0OiB1bmRlZmluZWQKICAgIH0sCiAgICBmaW5pc2hlZDogewogICAgICB0eXBlOiBCb29sZWFuLAogICAgICBkZWZhdWx0OiB0cnVlCiAgICB9LAogICAgaXNBdXRoSW1hZ2VzOiB7CiAgICAgIHR5cGU6IEJvb2xlYW4sCiAgICAgIGRlZmF1bHQ6IGZhbHNlCiAgICB9LAogICAgdmlld09wZW46IHsKICAgICAgdHlwZTogQm9vbGVhbiwKICAgICAgZGVmYXVsdDogZmFsc2UKICAgIH0KICB9LAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICAvLyDmqKHlnot4bWzmlbDmja4KICAgICAgeG1sRGF0YTogIiIsCiAgICAgIHRhc2tMaXN0OiBbXSwKICAgICAgLy8g6YOo6Zeo5ZCN56ewCiAgICAgIGRlcHROYW1lOiB1bmRlZmluZWQsCiAgICAgIC8vIOmDqOmXqOagkemAiemhuQogICAgICBkZXB0T3B0aW9uczogdW5kZWZpbmVkLAogICAgICAvLyDnlKjmiLfooajmoLzmlbDmja4KICAgICAgdXNlckxpc3Q6IG51bGwsCiAgICAgIGRlZmF1bHRQcm9wczogewogICAgICAgIGNoaWxkcmVuOiAiY2hpbGRyZW4iLAogICAgICAgIGxhYmVsOiAibGFiZWwiCiAgICAgIH0sCiAgICAgIC8vIOafpeivouWPguaVsAogICAgICBxdWVyeVBhcmFtczogewogICAgICAgIGRlcHRJZDogdW5kZWZpbmVkCiAgICAgIH0sCiAgICAgIHR5cGVBcnI6IFsiemlwIiwgInJhciJdLAogICAgICAvLyDpga7nvanlsYIKICAgICAgbG9hZGluZzogdHJ1ZSwKICAgICAgZmxvd1JlY29yZExpc3Q6IFtdLAogICAgICAvLyDmtYHnqIvmtYHovazmlbDmja4KICAgICAgZmxvd1JlY29yZExpc3RzOiB7fSwKICAgICAgZm9ybUNvbmZDb3B5OiB7fSwKICAgICAgc3JjOiBudWxsLAogICAgICBydWxlczoge30sCiAgICAgIC8vIOihqOWNleagoemqjAogICAgICB2YXJpYWJsZXNGb3JtOiB7fSwKICAgICAgLy8g5rWB56iL5Y+Y6YeP5pWw5o2uCiAgICAgIHRhc2tGb3JtOiB7CiAgICAgICAgcmV0dXJuVGFza1Nob3c6IGZhbHNlLAogICAgICAgIC8vIOaYr+WQpuWxleekuuWbnumAgOihqOWNlQogICAgICAgIGRlbGVnYXRlVGFza1Nob3c6IGZhbHNlLAogICAgICAgIC8vIOaYr+WQpuWxleekuuWbnumAgOihqOWNlQogICAgICAgIGRlZmF1bHRUYXNrU2hvdzogdHJ1ZSwKICAgICAgICAvLyDpu5jorqTlpITnkIYKICAgICAgICBzZW5kVXNlclNob3c6IGZhbHNlLAogICAgICAgIC8vIOWuoeaJueeUqOaItwogICAgICAgIG11bHRpcGxlOiBmYWxzZSwKICAgICAgICBjb21tZW50OiAiIiwKICAgICAgICAvLyDmhI/op4HlhoXlrrkKICAgICAgICBwcm9jSW5zSWQ6ICIiLAogICAgICAgIC8vIOa1geeoi+WunuS+i+e8luWPtwogICAgICAgIGluc3RhbmNlSWQ6ICIiLAogICAgICAgIC8vIOa1geeoi+WunuS+i+e8luWPtwogICAgICAgIHRhc2tJZDogIiIsCiAgICAgICAgLy8g5rWB56iL5Lu75Yqh57yW5Y+3CiAgICAgICAgcHJvY0RlZktleTogIiIsCiAgICAgICAgLy8g5rWB56iL57yW5Y+3CiAgICAgICAgdmFyczogIiIsCiAgICAgICAgdGFyZ2V0S2V5OiAiIiwKICAgICAgICBhdXRoSW1hZ2VzOiAiIgogICAgICB9LAogICAgICB1c2VyRGF0YUxpc3Q6IFtdLAogICAgICAvLyDmtYHnqIvlgJnpgInkuroKICAgICAgYXNzaWduZWU6IG51bGwsCiAgICAgIGZvcm1Db25mOiB7fSwKICAgICAgLy8g6buY6K6k6KGo5Y2V5pWw5o2uCiAgICAgIGZvcm1Db25mT3BlbjogZmFsc2UsCiAgICAgIC8vIOaYr+WQpuWKoOi9vem7mOiupOihqOWNleaVsOaNrgogICAgICB2YXJpYWJsZXM6IFtdLAogICAgICAvLyDmtYHnqIvlj5jph4/mlbDmja4KICAgICAgdmFyaWFibGVzRGF0YToge30sCiAgICAgIC8vIOa1geeoi+WPmOmHj+aVsOaNrgogICAgICB2YXJpYWJsZU9wZW46IGZhbHNlLAogICAgICAvLyDmmK/lkKbliqDovb3mtYHnqIvlj5jph4/mlbDmja4KICAgICAgcmV0dXJuVGFza0xpc3Q6IFtdLAogICAgICAvLyDlm57pgIDliJfooajmlbDmja4KICAgICAgY29tcGxldGVUaXRsZTogbnVsbCwKICAgICAgY29tcGxldGVPcGVuOiBmYWxzZSwKICAgICAgcmV0dXJuVGl0bGU6IG51bGwsCiAgICAgIHJldHVybk9wZW46IGZhbHNlLAogICAgICByZWplY3RPcGVuOiBmYWxzZSwKICAgICAgcmVqZWN0VGl0bGU6IG51bGwsCiAgICAgIHJlamVjdE9wZW4xOiBmYWxzZSwKICAgICAgcmVqZWN0VGl0bGUxOiBudWxsLAogICAgICB1c2VyRGF0YTogW10sCiAgICAgIGF1ZGl0OiB0cnVlLAogICAgICBjYW5GaW5pc2g6IGZhbHNlLAogICAgICBmbG93SGlzOiBbXSwKICAgICAgZmxvd0FjdGl2ZTogbnVsbCwKICAgICAgYml6S2V5OiBudWxsLAogICAgICBpc0NvbW1vbjogZmFsc2UsCiAgICAgIGF1dGhJbWFnZXM6IFtdCiAgICB9OwogIH0sCiAgY3JlYXRlZDogZnVuY3Rpb24gY3JlYXRlZCgpIHsKICAgIGlmICh0aGlzLiRzdG9yZS5zdGF0ZS51c2VyLnJvbGVzICYmIHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIucm9sZXMuaW5jbHVkZXMoImNvbW1vbiIpKSB7CiAgICAgIHRoaXMuaXNDb21tb24gPSB0cnVlOwogICAgfQogICAgY29uc29sZS5sb2coIj09PT09PT09cmVjb3JkPT09PT09PT1jcmVhdGVkPT4+PiIpOwogICAgY29uc29sZS5sb2codGhpcy5fcHJvcHMpOwogICAgdmFyIHRoYXQgPSB0aGlzOwogICAgdmFyIF90aGlzJF9wcm9wcyA9IHRoaXMuX3Byb3BzLAogICAgICB0YXNrSWQgPSBfdGhpcyRfcHJvcHMudGFza0lkLAogICAgICBwcm9jRGVmS2V5ID0gX3RoaXMkX3Byb3BzLnByb2NEZWZLZXksCiAgICAgIHByb2NJbnNJZCA9IF90aGlzJF9wcm9wcy5wcm9jSW5zSWQsCiAgICAgIGZpbmlzaGVkID0gX3RoaXMkX3Byb3BzLmZpbmlzaGVkLAogICAgICBiaXpLZXkgPSBfdGhpcyRfcHJvcHMuYml6S2V5OwogICAgLy8gaWYoIXZpZXdPcGVuKXsKICAgIC8vICAgY29uc29sZS5sb2coIj09PT4+PuWFs+mXrSzkuI3muLLmn5MiKQogICAgLy8gICByZXR1cm47CiAgICAvLyB9CiAgICB0aGlzLnRhc2tGb3JtLnRhc2tJZCA9IHRhc2tJZDsKICAgIHRoaXMudGFza0Zvcm0ucHJvY0luc0lkID0gcHJvY0luc0lkOwogICAgdGhpcy50YXNrRm9ybS5pbnN0YW5jZUlkID0gcHJvY0luc0lkOwogICAgLy8g5Yid5aeL5YyW6KGo5Y2VCiAgICB0aGlzLnRhc2tGb3JtLnByb2NEZWZLZXkgPSBwcm9jRGVmS2V5OwogICAgdGhpcy5iaXpLZXkgPSBiaXpLZXk7CiAgICAvL+mHjee9rgogICAgdGhhdC5mbG93SGlzID0gW107CiAgICB0aGF0LmZsb3dSZWNvcmRMaXN0cyA9IG51bGw7CiAgICAvLyDlm57mmL7mtYHnqIvorrDlvZUKICAgIGlmIChwcm9jSW5zSWQpIHsKICAgICAgdGhpcy5nZXRGbG93Vmlld2VyKHRoaXMudGFza0Zvcm0ucHJvY0luc0lkLCB0aGlzLnRhc2tGb3JtLnByb2NEZWZLZXkpOwogICAgICAvLyDmtYHnqIvku7vliqHph43ojrflj5blj5jph4/ooajljZUKICAgICAgaWYgKHRoaXMudGFza0Zvcm0udGFza0lkKSB7CiAgICAgICAgdGhpcy5wcm9jZXNzVmFyaWFibGVzKHRoaXMudGFza0Zvcm0udGFza0lkKTsKICAgICAgICB0aGlzLmdldE5leHRGbG93Tm9kZSh0aGlzLnRhc2tGb3JtLnRhc2tJZCk7CiAgICAgIH0KICAgICAgdGhpcy5nZXRGbG93UmVjb3JkTGlzdCh0aGlzLnRhc2tGb3JtLnByb2NJbnNJZCk7CiAgICAgIHRoYXQuZmxvd0FjdGl2ZSA9IHByb2NJbnNJZDsKICAgICAgZ2V0SGlzSW5zKHsKICAgICAgICBiaXpLZXk6IHRoYXQuYml6S2V5LAogICAgICAgIGRlZktleTogcHJvY0RlZktleQogICAgICB9KS50aGVuKGZ1bmN0aW9uIChyZXNwKSB7CiAgICAgICAgaWYgKHJlc3AuZGF0YSAmJiByZXNwLmRhdGEubGVuZ3RoID4gMCkgewogICAgICAgICAgdGhhdC50YXNrRm9ybS50YXNrTmFtZSA9IHJlc3AuZGF0YVswXS5uYW1lOwogICAgICAgIH0KICAgICAgICBpZiAocmVzcC5kYXRhLmxlbmd0aCA+IDEpIHsKICAgICAgICAgIHRoYXQuZmxvd0hpcyA9IHJlc3AuZGF0YTsKICAgICAgICAgIHRoYXQuZmxvd1JlY29yZExpc3RzID0gbmV3IE1hcCgpOwogICAgICAgICAgdGhhdC5mbG93UmVjb3JkTGlzdHMuc2V0KHByb2NJbnNJZCwgdGhhdC5mbG93UmVjb3JkTGlzdCk7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0gZWxzZSB7CiAgICAgIHRoaXMuZ2V0TW9kZWxEZXRhaWwocHJvY0RlZktleSk7CiAgICB9CiAgICB0aGlzLmZpbmlzaGVkID0gZmluaXNoZWQ7CiAgfSwKICBtb3VudGVkOiBmdW5jdGlvbiBtb3VudGVkKCkgewogICAgLy8gLy8g6KGo5Y2V5pWw5o2u5Zue5aGr77yM5qih5ouf5byC5q2l6K+35rGC5Zy65pmvCiAgICAvLyBzZXRUaW1lb3V0KCgpID0+IHsKICAgIC8vICAgLy8g6K+35rGC5Zue5p2l55qE6KGo5Y2V5pWw5o2uCiAgICAvLyAgIGNvbnN0IGRhdGEgPSB7CiAgICAvLyAgICAgZmllbGQxMDI6ICcxODgzNjY2MjU1NScKICAgIC8vICAgfQogICAgLy8gICAvLyDlm57loavmlbDmja4KICAgIC8vICAgdGhpcy5maWxsRm9ybURhdGEodGhpcy5mb3JtQ29uZiwgZGF0YSkKICAgIC8vICAgLy8g5pu05paw6KGo5Y2VCiAgICAvLyAgIHRoaXMua2V5ID0gK25ldyBEYXRlKCkuZ2V0VGltZSgpCiAgICAvLyB9LCAxMDAwKQogIH0sCiAgbWV0aG9kczogewogICAgLyoqIOafpeivoumDqOmXqOS4i+aLieagkee7k+aehCAqL2dldFRyZWVzZWxlY3Q6IGZ1bmN0aW9uIGdldFRyZWVzZWxlY3QoKSB7CiAgICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICAgIHRyZWVzZWxlY3QoKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgIF90aGlzLmRlcHRPcHRpb25zID0gcmVzcG9uc2UuZGF0YTsKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOafpeivoueUqOaIt+WIl+ihqCAqL2dldExpc3Q6IGZ1bmN0aW9uIGdldExpc3QoKSB7CiAgICAgIHZhciBfdGhpczIgPSB0aGlzOwogICAgICBsaXN0VXNlcih0aGlzLmFkZERhdGVSYW5nZSh0aGlzLnF1ZXJ5UGFyYW1zLCB0aGlzLmRhdGVSYW5nZSkpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgX3RoaXMyLnVzZXJMaXN0ID0gcmVzcG9uc2Uucm93czsKICAgICAgICBfdGhpczIudG90YWwgPSByZXNwb25zZS50b3RhbDsKICAgICAgfSk7CiAgICB9LAogICAgLy8g562b6YCJ6IqC54K5CiAgICBmaWx0ZXJOb2RlOiBmdW5jdGlvbiBmaWx0ZXJOb2RlKHZhbHVlLCBkYXRhKSB7CiAgICAgIGlmICghdmFsdWUpIHJldHVybiB0cnVlOwogICAgICByZXR1cm4gZGF0YS5sYWJlbC5pbmRleE9mKHZhbHVlKSAhPT0gLTE7CiAgICB9LAogICAgLy8g6IqC54K55Y2V5Ye75LqL5Lu2CiAgICBoYW5kbGVOb2RlQ2xpY2s6IGZ1bmN0aW9uIGhhbmRsZU5vZGVDbGljayhkYXRhKSB7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMuZGVwdElkID0gZGF0YS5pZDsKICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICB9LAogICAgLyoqIHhtbCDmlofku7YgKi9nZXRNb2RlbERldGFpbDogZnVuY3Rpb24gZ2V0TW9kZWxEZXRhaWwoZGVwbG95S2V5KSB7CiAgICAgIHZhciBfdGhpczMgPSB0aGlzOwogICAgICAvLyDlj5HpgIHor7fmsYLvvIzojrflj5Z4bWwKICAgICAgcmVhZFhtbEJ5S2V5KGRlcGxveUtleSkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgX3RoaXMzLnhtbERhdGEgPSByZXMuZGF0YTsKICAgICAgfSk7CiAgICB9LAogICAgZ2V0Rmxvd1ZpZXdlcjogZnVuY3Rpb24gZ2V0Rmxvd1ZpZXdlcihwcm9jSW5zSWQsIGRlcGxveUtleSkgewogICAgICB2YXIgX3RoaXM0ID0gdGhpczsKICAgICAgX2dldEZsb3dWaWV3ZXIocHJvY0luc0lkKS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICBfdGhpczQudGFza0xpc3QgPSByZXMuZGF0YTsKICAgICAgICBfdGhpczQuZ2V0TW9kZWxEZXRhaWwoZGVwbG95S2V5KTsKICAgICAgfSk7CiAgICB9LAogICAgc2V0SWNvbjogZnVuY3Rpb24gc2V0SWNvbih2YWwpIHsKICAgICAgaWYgKHZhbCkgewogICAgICAgIHJldHVybiAiZWwtaWNvbi1jaGVjayI7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgcmV0dXJuICJlbC1pY29uLXRpbWUiOwogICAgICB9CiAgICB9LAogICAgc2V0Q29sb3I6IGZ1bmN0aW9uIHNldENvbG9yKHZhbCkgewogICAgICBpZiAodmFsKSB7CiAgICAgICAgcmV0dXJuICIjMmJjNDE4IjsKICAgICAgfSBlbHNlIHsKICAgICAgICByZXR1cm4gIiNiM2JkYmIiOwogICAgICB9CiAgICB9LAogICAgLy8g5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2U6IGZ1bmN0aW9uIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsKICAgICAgdGhpcy51c2VyRGF0YSA9IHNlbGVjdGlvbjsKICAgICAgdmFyIHZhbCA9IHNlbGVjdGlvbi5tYXAoZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICByZXR1cm4gaXRlbS51c2VySWQ7CiAgICAgIH0pWzBdOwogICAgICBpZiAodmFsIGluc3RhbmNlb2YgQXJyYXkpIHsKICAgICAgICB0aGlzLnRhc2tGb3JtLnZhbHVlcyA9IHsKICAgICAgICAgIGFwcHJvdmFsOiB2YWwuam9pbigiLCIpCiAgICAgICAgfTsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLnRhc2tGb3JtLnZhbHVlcyA9IHsKICAgICAgICAgIGFwcHJvdmFsOiB2YWwKICAgICAgICB9OwogICAgICB9CiAgICB9LAogICAgLy8g5YWz6Zet5qCH562+CiAgICBoYW5kbGVDbG9zZTogZnVuY3Rpb24gaGFuZGxlQ2xvc2UodGFnKSB7CiAgICAgIHRoaXMudXNlckRhdGEuc3BsaWNlKHRoaXMudXNlckRhdGEuaW5kZXhPZih0YWcpLCAxKTsKICAgIH0sCiAgICAvKiog5rWB56iL5Y+Y6YeP6LWL5YC8ICovaGFuZGxlQ2hlY2tDaGFuZ2U6IGZ1bmN0aW9uIGhhbmRsZUNoZWNrQ2hhbmdlKHZhbCkgewogICAgICBpZiAodmFsIGluc3RhbmNlb2YgQXJyYXkpIHsKICAgICAgICB0aGlzLnRhc2tGb3JtLnZhbHVlcyA9IHsKICAgICAgICAgIGFwcHJvdmFsOiB2YWwuam9pbigiLCIpCiAgICAgICAgfTsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLnRhc2tGb3JtLnZhbHVlcyA9IHsKICAgICAgICAgIGFwcHJvdmFsOiB2YWwKICAgICAgICB9OwogICAgICB9CiAgICB9LAogICAgLyoqIOa1geeoi+a1gei9rOiusOW9lSAqL2dldEZsb3dSZWNvcmRMaXN0OiBmdW5jdGlvbiBnZXRGbG93UmVjb3JkTGlzdChwcm9jSW5zSWQpIHsKICAgICAgdmFyIHRoYXQgPSB0aGlzOwogICAgICB2YXIgcGFyYW1zID0gewogICAgICAgIHByb2NJbnNJZDogcHJvY0luc0lkCiAgICAgIH07CiAgICAgIGZsb3dSZWNvcmQocGFyYW1zKS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICB0aGF0LmZsb3dSZWNvcmRMaXN0ID0gcmVzLmRhdGEuZmxvd0xpc3Q7CiAgICAgICAgLy8g5rWB56iL6L+H56iL5Lit5LiN5a2Y5Zyo5Yid5aeL5YyW6KGo5Y2VIOebtOaOpeivu+WPlueahOa1geeoi+WPmOmHj+S4reWtmOWCqOeahOihqOWNleWAvAogICAgICAgIGlmIChyZXMuZGF0YS5mb3JtRGF0YSkgewogICAgICAgICAgdGhhdC5mb3JtQ29uZiA9IHJlcy5kYXRhLmZvcm1EYXRhOwogICAgICAgICAgdGhhdC5mb3JtQ29uZk9wZW4gPSB0cnVlOwogICAgICAgIH0KICAgICAgICBpZiAodGhhdC5mbG93UmVjb3JkTGlzdHMpIHsKICAgICAgICAgIHRoYXQuZmxvd1JlY29yZExpc3RzLnNldChwcm9jSW5zSWQsIHRoYXQuZmxvd1JlY29yZExpc3QpOwogICAgICAgIH0KICAgICAgfSkuY2F0Y2goZnVuY3Rpb24gKHJlcykgewogICAgICAgIHRoYXQuJHJvdXRlci5nbygwKTsKICAgICAgfSk7CiAgICB9LAogICAgZmlsbEZvcm1EYXRhOiBmdW5jdGlvbiBmaWxsRm9ybURhdGEoZm9ybSwgZGF0YSkgewogICAgICBmb3JtLmZpZWxkcy5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgdmFyIHZhbCA9IGRhdGFbaXRlbS5fX3ZNb2RlbF9fXTsKICAgICAgICBpZiAodmFsKSB7CiAgICAgICAgICBpdGVtLl9fY29uZmlnX18uZGVmYXVsdFZhbHVlID0gdmFsOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLyoqIOiOt+WPlua1geeoi+WPmOmHj+WGheWuuSAqL3Byb2Nlc3NWYXJpYWJsZXM6IGZ1bmN0aW9uIHByb2Nlc3NWYXJpYWJsZXModGFza0lkKSB7CiAgICAgIHZhciBfdGhpczUgPSB0aGlzOwogICAgICBpZiAodGFza0lkKSB7CiAgICAgICAgLy8g5o+Q5Lqk5rWB56iL55Sz6K+35pe25aGr5YaZ55qE6KGo5Y2V5a2Y5YWl5LqG5rWB56iL5Y+Y6YeP5Lit5ZCO57ut5Lu75Yqh5aSE55CG5pe26ZyA6KaB5bGV56S6CiAgICAgICAgZ2V0UHJvY2Vzc1ZhcmlhYmxlcyh0YXNrSWQpLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgICAgLy8gdGhpcy52YXJpYWJsZXMgPSByZXMuZGF0YS52YXJpYWJsZXM7CiAgICAgICAgICBfdGhpczUudmFyaWFibGVzRGF0YSA9IHJlcy5kYXRhLnZhcmlhYmxlczsKICAgICAgICAgIF90aGlzNS52YXJpYWJsZU9wZW4gPSB0cnVlOwogICAgICAgIH0pOwogICAgICB9CiAgICB9LAogICAgLyoqIOagueaNruW9k+WJjeS7u+WKoeaIluiAhea1geeoi+iuvuiuoemFjee9rueahOS4i+S4gOatpeiKgueCuSAqL2dldE5leHRGbG93Tm9kZTogZnVuY3Rpb24gZ2V0TmV4dEZsb3dOb2RlKHRhc2tJZCkgewogICAgICB2YXIgX3RoaXM2ID0gdGhpczsKICAgICAgLy8g5qC55o2u5b2T5YmN5Lu75Yqh5oiW6ICF5rWB56iL6K6+6K6h6YWN572u55qE5LiL5LiA5q2l6IqC54K5IHRvZG8g5pqC5pe25pyq5raJ5Y+K5Yiw6ICD6JmR572R5YWz44CB6KGo6L6+5byP5ZKM5aSa6IqC54K55oOF5Ya1CiAgICAgIHZhciBwYXJhbXMgPSB7CiAgICAgICAgdGFza0lkOiB0YXNrSWQKICAgICAgfTsKICAgICAgX2dldE5leHRGbG93Tm9kZShwYXJhbXMpLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgIHZhciBkYXRhID0gcmVzLmRhdGE7CiAgICAgICAgaWYgKGRhdGEpIHsKICAgICAgICAgIGlmIChkYXRhLnR5cGUgPT09ICJhc3NpZ25lZSIpIHsKICAgICAgICAgICAgX3RoaXM2LnVzZXJEYXRhTGlzdCA9IHJlcy5kYXRhLnVzZXJMaXN0OwogICAgICAgICAgfSBlbHNlIGlmIChkYXRhLnR5cGUgPT09ICJjYW5kaWRhdGVVc2VycyIpIHsKICAgICAgICAgICAgX3RoaXM2LnVzZXJEYXRhTGlzdCA9IHJlcy5kYXRhLnVzZXJMaXN0OwogICAgICAgICAgICBfdGhpczYudGFza0Zvcm0ubXVsdGlwbGUgPSB0cnVlOwogICAgICAgICAgfSBlbHNlIGlmIChkYXRhLnR5cGUgPT09ICJjYW5kaWRhdGVHcm91cHMiKSB7CiAgICAgICAgICAgIHJlcy5kYXRhLnJvbGVMaXN0LmZvckVhY2goZnVuY3Rpb24gKHJvbGUpIHsKICAgICAgICAgICAgICByb2xlLnVzZXJJZCA9IHJvbGUucm9sZUlkOwogICAgICAgICAgICAgIHJvbGUubmlja05hbWUgPSByb2xlLnJvbGVOYW1lOwogICAgICAgICAgICB9KTsKICAgICAgICAgICAgX3RoaXM2LnVzZXJEYXRhTGlzdCA9IHJlcy5kYXRhLnJvbGVMaXN0OwogICAgICAgICAgICBfdGhpczYudGFza0Zvcm0ubXVsdGlwbGUgPSBmYWxzZTsKICAgICAgICAgIH0gZWxzZSBpZiAoZGF0YS50eXBlID09PSAibXVsdGlJbnN0YW5jZSIpIHsKICAgICAgICAgICAgX3RoaXM2LnVzZXJEYXRhTGlzdCA9IHJlcy5kYXRhLnVzZXJMaXN0OwogICAgICAgICAgICBfdGhpczYudGFza0Zvcm0ubXVsdGlwbGUgPSB0cnVlOwogICAgICAgICAgfQogICAgICAgICAgX3RoaXM2LnRhc2tGb3JtLnNlbmRVc2VyU2hvdyA9IHRydWU7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIF90aGlzNi5jYW5GaW5pc2ggPSB0cnVlOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLyoqIOWuoeaJueS7u+WKoemAieaLqSAqL2hhbmRsZUNvbXBsZXRlOiBmdW5jdGlvbiBoYW5kbGVDb21wbGV0ZSgpIHsKICAgICAgdGhpcy5jb21wbGV0ZU9wZW4gPSB0cnVlOwogICAgICB0aGlzLmNvbXBsZXRlVGl0bGUgPSAi5a6h5om55rWB56iLIjsKICAgICAgLy90aGlzLmdldFRyZWVzZWxlY3QoKTsKICAgIH0sCiAgICAvKiog5a6h5om55Lu75YqhICovdGFza0NvbXBsZXRlOiBmdW5jdGlvbiB0YXNrQ29tcGxldGUoY29tbWVudCkgewogICAgICB2YXIgX3RoaXM3ID0gdGhpczsKICAgICAgLy8gaWYgKCF0aGlzLnRhc2tGb3JtLnZhbHVlcyl7CiAgICAgIC8vICAgdGhpcy5tc2dFcnJvcigi6K+36YCJ5oup5rWB56iL5o6l5pS25Lq65ZGYIik7CiAgICAgIC8vICAgcmV0dXJuOwogICAgICAvLyB9CiAgICAgIGlmIChjb21tZW50ICYmIHR5cGVvZiBjb21tZW50ID09ICJzdHJpbmciICYmIGNvbW1lbnQuY29uc3RydWN0b3IgPT0gU3RyaW5nKSB7CiAgICAgICAgdGhpcy50YXNrRm9ybS5jb21tZW50ID0gY29tbWVudDsKICAgICAgfQogICAgICBpZiAoIXRoaXMudGFza0Zvcm0uY29tbWVudCkgewogICAgICAgIHRoaXMubXNnRXJyb3IoIuivt+i+k+WFpeWuoeaJueaEj+ingSIpOwogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICAvL2lmKHRoaXMuY2FuRmluaXNoKXsKICAgICAgdGhpcy50YXNrRm9ybS52YWx1ZXMgPSB7fTsKICAgICAgdGhpcy50YXNrRm9ybS5iaXpLZXkgPSB0aGlzLmJpektleTsKICAgICAgdGhpcy50YXNrRm9ybS52YWx1ZXMuaXNSZWplY3QgPSBmYWxzZTsKICAgICAgLy99CiAgICAgIGNvbXBsZXRlKHRoaXMudGFza0Zvcm0pLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgX3RoaXM3Lm1zZ1N1Y2Nlc3MocmVzcG9uc2UubXNnKTsKICAgICAgICAvL3RoaXMuZ29CYWNrKCk7CiAgICAgICAgX3RoaXM3LiRyb3V0ZXIuZ28oMCk7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDlp5TmtL7ku7vliqEgKi9oYW5kbGVEZWxlZ2F0ZTogZnVuY3Rpb24gaGFuZGxlRGVsZWdhdGUoKSB7CiAgICAgIHRoaXMudGFza0Zvcm0uZGVsZWdhdGVUYXNrU2hvdyA9IHRydWU7CiAgICAgIHRoaXMudGFza0Zvcm0uZGVmYXVsdFRhc2tTaG93ID0gZmFsc2U7CiAgICB9LAogICAgaGFuZGxlQXNzaWduOiBmdW5jdGlvbiBoYW5kbGVBc3NpZ24oKSB7fSwKICAgIC8qKiDov5Tlm57pobXpnaIgKi9nb0JhY2s6IGZ1bmN0aW9uIGdvQmFjaygpIHsKICAgICAgLy8g5YWz6Zet5b2T5YmN5qCH562+6aG15bm26L+U5Zue5LiK5Liq6aG16Z2iCiAgICAgIHRoaXMuJHN0b3JlLmRpc3BhdGNoKCJ0YWdzVmlldy9kZWxWaWV3IiwgdGhpcy4kcm91dGUpOwogICAgICB0aGlzLiRyb3V0ZXIuZ28oLTEpOwogICAgfSwKICAgIC8qKiDmjqXmlLblrZDnu4Tku7bkvKDnmoTlgLwgKi9nZXREYXRhOiBmdW5jdGlvbiBnZXREYXRhKGRhdGEpIHsKICAgICAgaWYgKGRhdGEpIHsKICAgICAgICB2YXIgdmFyaWFibGVzID0gW107CiAgICAgICAgZGF0YS5maWVsZHMuZm9yRWFjaChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgdmFyIHZhcmlhYmxlRGF0YSA9IHt9OwogICAgICAgICAgdmFyaWFibGVEYXRhLmxhYmVsID0gaXRlbS5fX2NvbmZpZ19fLmxhYmVsOwogICAgICAgICAgLy8g6KGo5Y2V5YC85Li65aSa5Liq6YCJ6aG55pe2CiAgICAgICAgICBpZiAoaXRlbS5fX2NvbmZpZ19fLmRlZmF1bHRWYWx1ZSBpbnN0YW5jZW9mIEFycmF5KSB7CiAgICAgICAgICAgIHZhciBhcnJheSA9IFtdOwogICAgICAgICAgICBpdGVtLl9fY29uZmlnX18uZGVmYXVsdFZhbHVlLmZvckVhY2goZnVuY3Rpb24gKHZhbCkgewogICAgICAgICAgICAgIGFycmF5LnB1c2godmFsKTsKICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIHZhcmlhYmxlRGF0YS52YWwgPSBhcnJheTsKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIHZhcmlhYmxlRGF0YS52YWwgPSBpdGVtLl9fY29uZmlnX18uZGVmYXVsdFZhbHVlOwogICAgICAgICAgfQogICAgICAgICAgdmFyaWFibGVzLnB1c2godmFyaWFibGVEYXRhKTsKICAgICAgICB9KTsKICAgICAgICB0aGlzLnZhcmlhYmxlcyA9IHZhcmlhYmxlczsKICAgICAgfQogICAgfSwKICAgIC8vIOaOpeaUtuWtkOe7hOS7tuWbvueJh+S4iuS8oOeahOWAvAogICAgaW1hZ2VVcmxzOiBmdW5jdGlvbiBpbWFnZVVybHModmFsdWUpIHsKICAgICAgdGhpcy50YXNrRm9ybS5hdXRoSW1hZ2VzID0gdmFsdWU7CiAgICB9LAogICAgLyoqIOeUs+ivt+a1geeoi+ihqOWNleaVsOaNruaPkOS6pCAqLwogICAgLy8gc3VibWl0Rm9ybShkYXRhKSB7CiAgICAvLyAgIGlmIChkYXRhKSB7CiAgICAvLyAgICAgY29uc3QgdmFyaWFibGVzID0gZGF0YS52YWxEYXRhOwogICAgLy8gICAgIGNvbnN0IGZvcm1EYXRhID0gZGF0YS5mb3JtRGF0YTsKICAgIC8vICAgICBmb3JtRGF0YS5kaXNhYmxlZCA9IHRydWU7CiAgICAvLyAgICAgZm9ybURhdGEuZm9ybUJ0bnMgPSBmYWxzZTsKICAgIC8vICAgICBpZiAodGhpcy50YXNrRm9ybS5wcm9jRGVmS2V5KSB7CiAgICAvLyAgICAgICB2YXJpYWJsZXMudmFyaWFibGVzID0gZm9ybURhdGE7CiAgICAvLyAgICAgICB2YXJpYWJsZXMuYnVzaW5lc3NLZXkgPSBkYXRhLmJ1c2luZXNzS2V5OwogICAgLy8gICAgICAgIC8vIOWQr+WKqOa1geeoi+W5tuWwhuihqOWNleaVsOaNruWKoOWFpea1geeoi+WPmOmHjwogICAgLy8gICAgICAgZGVmaW5pdGlvblN0YXJ0QnlLZXkodGhpcy50YXNrRm9ybS5wcm9jRGVmS2V5LCBKU09OLnN0cmluZ2lmeSh2YXJpYWJsZXMpKS50aGVuKHJlcyA9PiB7CiAgICAvLyAgICAgICAgIHRoaXMubXNnU3VjY2VzcyhyZXMubXNnKTsKICAgIC8vICAgICAgICAgdGhpcy5nb0JhY2soKTsKICAgIC8vICAgICAgIH0pCiAgICAvLyAgICAgfQogICAgLy8gICB9CiAgICAvLyB9LAogICAgc3RhcnRGbG93OiBmdW5jdGlvbiBzdGFydEZsb3coYnVzaW5lc3NLZXksIG5hbWUsIHZhcmlhYmxlcykgewogICAgICB2YXIgX3RoaXM4ID0gdGhpczsKICAgICAgdmFyIHN0YXJ0RGF0ZSA9IG1vbWVudChuZXcgRGF0ZSgpKS5mb3JtYXQoIllZWVlNTURESEhtbXNzIik7CiAgICAgIHZhciBkYXRhID0ge307CiAgICAgIGlmICh0aGlzLnRhc2tGb3JtLnByb2NEZWZLZXkpIHsKICAgICAgICBpZiAoIXZhcmlhYmxlcykgewogICAgICAgICAgZGF0YS52YXJpYWJsZXMgPSB7fTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgZGF0YS52YXJpYWJsZXMgPSB2YXJpYWJsZXM7CiAgICAgICAgfQogICAgICAgIGRhdGEuYnVzaW5lc3NLZXkgPSBidXNpbmVzc0tleTsKICAgICAgICBkYXRhLnByb2NEZWZLZXkgPSB0aGlzLnRhc2tGb3JtLnByb2NEZWZLZXk7CiAgICAgICAgZGF0YS50YXNrTmFtZSA9IG5hbWU7CiAgICAgICAgLy8g5ZCv5Yqo5rWB56iL5bm25bCG6KGo5Y2V5pWw5o2u5Yqg5YWl5rWB56iL5Y+Y6YePCiAgICAgICAgZGVmaW5pdGlvblN0YXJ0QnlLZXkoSlNPTi5zdHJpbmdpZnkoZGF0YSkpLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgICAgX3RoaXM4Lm1zZ1N1Y2Nlc3MocmVzLm1zZyk7CiAgICAgICAgICBfdGhpczguZ29CYWNrKCk7CiAgICAgICAgfSk7CiAgICAgIH0KICAgIH0sCiAgICAvKiog6amz5Zue5Lu75YqhICovaGFuZGxlUmVqZWN0OiBmdW5jdGlvbiBoYW5kbGVSZWplY3QoKSB7CiAgICAgIHRoaXMucmVqZWN0T3BlbiA9IHRydWU7CiAgICAgIHRoaXMucmVqZWN0VGl0bGUgPSAi6YCA5Zue5rWB56iLIjsKICAgIH0sCiAgICAvKiog6amz5Zue5Lu75YqhICovdGFza1JlamVjdDogZnVuY3Rpb24gdGFza1JlamVjdCgpIHsKICAgICAgdmFyIF90aGlzOSA9IHRoaXM7CiAgICAgIHRoaXMuJHJlZnNbInRhc2tGb3JtIl0udmFsaWRhdGUoZnVuY3Rpb24gKHZhbGlkKSB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICByZWplY3RUYXNrKF90aGlzOS50YXNrRm9ybSkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgICAgIF90aGlzOS5tc2dTdWNjZXNzKHJlcy5tc2cpOwogICAgICAgICAgICBfdGhpczkuJHJvdXRlci5nbygwKTsKICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLyoqIOWPr+mAgOWbnuS7u+WKoeWIl+ihqCAqL2hhbmRsZVJldHVybjogZnVuY3Rpb24gaGFuZGxlUmV0dXJuKCkgewogICAgICB2YXIgX3RoaXMxMCA9IHRoaXM7CiAgICAgIHRoaXMucmV0dXJuT3BlbiA9IHRydWU7CiAgICAgIHRoaXMucmV0dXJuVGl0bGUgPSAi6YCA5Zue5rWB56iLIjsKICAgICAgcmV0dXJuTGlzdCh0aGlzLnRhc2tGb3JtKS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICBfdGhpczEwLnJldHVyblRhc2tMaXN0ID0gcmVzLmRhdGE7CiAgICAgICAgX3RoaXMxMC50YXNrRm9ybS52YWx1ZXMgPSBudWxsOwogICAgICAgIGlmIChyZXMuZGF0YSAmJiByZXMuZGF0YS5sZW5ndGggPiAwKSB7CiAgICAgICAgICBfdGhpczEwLnRhc2tGb3JtLnRhcmdldEtleSA9IHJlcy5kYXRhW3Jlcy5kYXRhLmxlbmd0aCAtIDFdLmlkOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLyoqIOaPkOS6pOmAgOWbnuS7u+WKoSAqL3Rhc2tSZXR1cm46IGZ1bmN0aW9uIHRhc2tSZXR1cm4oKSB7CiAgICAgIHZhciBfdGhpczExID0gdGhpczsKICAgICAgdGhpcy4kcmVmc1sidGFza0Zvcm0iXS52YWxpZGF0ZShmdW5jdGlvbiAodmFsaWQpIHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIC8v6YCA5Zue5Lia5YqhSUTvvIznlKjkuo7kv67mlLnnirbmgIEKICAgICAgICAgIF90aGlzMTEudGFza0Zvcm0uYml6S2V5ID0gX3RoaXMxMS5iaXpLZXk7CiAgICAgICAgICByZXR1cm5UYXNrKF90aGlzMTEudGFza0Zvcm0pLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgICAgICBfdGhpczExLm1zZ1N1Y2Nlc3MocmVzLm1zZyk7CiAgICAgICAgICAgIF90aGlzMTEuJHJvdXRlci5nbygwKTsKICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLyoqIOWPlua2iOWbnumAgOS7u+WKoeaMiemSriAqL2NhbmNlbFRhc2s6IGZ1bmN0aW9uIGNhbmNlbFRhc2soKSB7CiAgICAgIHRoaXMudGFza0Zvcm0ucmV0dXJuVGFza1Nob3cgPSBmYWxzZTsKICAgICAgdGhpcy50YXNrRm9ybS5kZWZhdWx0VGFza1Nob3cgPSB0cnVlOwogICAgICB0aGlzLnRhc2tGb3JtLnNlbmRVc2VyU2hvdyA9IHRydWU7CiAgICAgIHRoaXMucmV0dXJuVGFza0xpc3QgPSBbXTsKICAgIH0sCiAgICAvKiog5aeU5rS+5Lu75YqhICovc3VibWl0RGVsZXRlVGFzazogZnVuY3Rpb24gc3VibWl0RGVsZXRlVGFzaygpIHsKICAgICAgdmFyIF90aGlzMTIgPSB0aGlzOwogICAgICB0aGlzLiRyZWZzWyJ0YXNrRm9ybSJdLnZhbGlkYXRlKGZ1bmN0aW9uICh2YWxpZCkgewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgZGVsZWdhdGUoX3RoaXMxMi50YXNrRm9ybSkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICAgICAgX3RoaXMxMi5tc2dTdWNjZXNzKHJlc3BvbnNlLm1zZyk7CiAgICAgICAgICAgIF90aGlzMTIuJHJvdXRlci5nbygwKTsKICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLyoqIOWPlua2iOWbnumAgOS7u+WKoeaMiemSriAqL2NhbmNlbERlbGVnYXRlVGFzazogZnVuY3Rpb24gY2FuY2VsRGVsZWdhdGVUYXNrKCkgewogICAgICB0aGlzLnRhc2tGb3JtLmRlbGVnYXRlVGFza1Nob3cgPSBmYWxzZTsKICAgICAgdGhpcy50YXNrRm9ybS5kZWZhdWx0VGFza1Nob3cgPSB0cnVlOwogICAgICB0aGlzLnRhc2tGb3JtLnNlbmRVc2VyU2hvdyA9IHRydWU7CiAgICAgIHRoaXMucmV0dXJuVGFza0xpc3QgPSBbXTsKICAgIH0sCiAgICBoYW5kbGVFbmQ6IGZ1bmN0aW9uIGhhbmRsZUVuZCgpIHsKICAgICAgdGhpcy5yZWplY3RPcGVuMSA9IHRydWU7CiAgICAgIHRoaXMucmVqZWN0VGl0bGUxID0gIumps+Wbnua1geeoiyI7CiAgICB9LAogICAgLyoqIOmps+Wbnue7k+adn+S7u+WKoSAqL3Rhc2tFbmQ6IGZ1bmN0aW9uIHRhc2tFbmQoKSB7CiAgICAgIHZhciBfdGhpczEzID0gdGhpczsKICAgICAgdGhpcy4kcmVmc1sidGFza0Zvcm0iXS52YWxpZGF0ZShmdW5jdGlvbiAodmFsaWQpIHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIF90aGlzMTMudGFza0Zvcm0udmFsdWVzID0ge307CiAgICAgICAgICBfdGhpczEzLnRhc2tGb3JtLnZhbHVlcy5pc1JlamVjdCA9IHRydWU7CiAgICAgICAgICBlbmRUYXNrKF90aGlzMTMudGFza0Zvcm0pLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgICAgICBfdGhpczEzLm1zZ1N1Y2Nlc3MocmVzLm1zZyk7CiAgICAgICAgICAgIF90aGlzMTMuJHJvdXRlci5nbygwKTsKICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgaGFuZGxlQ2xpY2s6IGZ1bmN0aW9uIGhhbmRsZUNsaWNrKHRhYiwgZXZlbnQpIHsKICAgICAgaWYgKHRoaXMuZmxvd1JlY29yZExpc3RzICYmIHRoaXMuZmxvd1JlY29yZExpc3RzLmdldCh0YWIubmFtZSkpIHsKICAgICAgICB0aGlzLmZsb3dSZWNvcmRMaXN0ID0gdGhpcy5mbG93UmVjb3JkTGlzdHMuZ2V0KHRhYi5uYW1lKTsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLmdldEZsb3dSZWNvcmRMaXN0KHRhYi5uYW1lKTsKICAgICAgfQogICAgfQogIH0KfTs="}, null]}