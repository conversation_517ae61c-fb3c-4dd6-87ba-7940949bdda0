{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/system/menu/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/system/menu/index.vue", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGxpc3RNZW51LCBnZXRNZW51LCBkZWxNZW51LCBhZGRNZW51LCB1cGRhdGVNZW51IH0gZnJvbSAiQC9hcGkvc3lzdGVtL21lbnUiOwppbXBvcnQgVHJlZXNlbGVjdCBmcm9tICJAcmlvcGhhZS92dWUtdHJlZXNlbGVjdCI7CmltcG9ydCAiQHJpb3BoYWUvdnVlLXRyZWVzZWxlY3QvZGlzdC92dWUtdHJlZXNlbGVjdC5jc3MiOwppbXBvcnQgSWNvblNlbGVjdCBmcm9tICJAL2NvbXBvbmVudHMvSWNvblNlbGVjdCI7CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiTWVudSIsCiAgY29tcG9uZW50czogewogICAgVHJlZXNlbGVjdDogVHJlZXNlbGVjdCwKICAgIEljb25TZWxlY3Q6IEljb25TZWxlY3QKICB9LAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICAvLyDpga7nvanlsYIKICAgICAgbG9hZGluZzogdHJ1ZSwKICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2CiAgICAgIHNob3dTZWFyY2g6IHRydWUsCiAgICAgIC8vIOiPnOWNleihqOagvOagkeaVsOaNrgogICAgICBtZW51TGlzdDogW10sCiAgICAgIC8vIOiPnOWNleagkemAiemhuQogICAgICBtZW51T3B0aW9uczogW10sCiAgICAgIC8vIOW8ueWHuuWxguagh+mimAogICAgICB0aXRsZTogIiIsCiAgICAgIC8vIOaYr+WQpuaYvuekuuW8ueWHuuWxggogICAgICBvcGVuOiBmYWxzZSwKICAgICAgLy8g5pi+56S654q25oCB5pWw5o2u5a2X5YW4CiAgICAgIHZpc2libGVPcHRpb25zOiBbXSwKICAgICAgLy8g6I+c5Y2V54q25oCB5pWw5o2u5a2X5YW4CiAgICAgIHN0YXR1c09wdGlvbnM6IFtdLAogICAgICAvLyDmn6Xor6Llj4LmlbAKICAgICAgcXVlcnlQYXJhbXM6IHsKICAgICAgICBtZW51TmFtZTogdW5kZWZpbmVkLAogICAgICAgIHZpc2libGU6IHVuZGVmaW5lZAogICAgICB9LAogICAgICAvLyDooajljZXlj4LmlbAKICAgICAgZm9ybToge30sCiAgICAgIC8vIOihqOWNleagoemqjAogICAgICBydWxlczogewogICAgICAgIG1lbnVOYW1lOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAi6I+c5Y2V5ZCN56ew5LiN6IO95Li656m6IiwKICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgIH1dLAogICAgICAgIG9yZGVyTnVtOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAi6I+c5Y2V6aG65bqP5LiN6IO95Li656m6IiwKICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgIH1dLAogICAgICAgIHBhdGg6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICLot6/nlLHlnLDlnYDkuI3og73kuLrnqboiLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgfV0KICAgICAgfQogICAgfTsKICB9LAogIGNyZWF0ZWQ6IGZ1bmN0aW9uIGNyZWF0ZWQoKSB7CiAgICB2YXIgX3RoaXMgPSB0aGlzOwogICAgdGhpcy5nZXRMaXN0KCk7CiAgICB0aGlzLmdldERpY3RzKCJzeXNfc2hvd19oaWRlIikudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgX3RoaXMudmlzaWJsZU9wdGlvbnMgPSByZXNwb25zZS5kYXRhOwogICAgfSk7CiAgICB0aGlzLmdldERpY3RzKCJzeXNfbm9ybWFsX2Rpc2FibGUiKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICBfdGhpcy5zdGF0dXNPcHRpb25zID0gcmVzcG9uc2UuZGF0YTsKICAgIH0pOwogIH0sCiAgbWV0aG9kczogewogICAgLy8g6YCJ5oup5Zu+5qCHCiAgICBzZWxlY3RlZDogZnVuY3Rpb24gc2VsZWN0ZWQobmFtZSkgewogICAgICB0aGlzLmZvcm0uaWNvbiA9IG5hbWU7CiAgICB9LAogICAgLyoqIOafpeivouiPnOWNleWIl+ihqCAqL2dldExpc3Q6IGZ1bmN0aW9uIGdldExpc3QoKSB7CiAgICAgIHZhciBfdGhpczIgPSB0aGlzOwogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOwogICAgICBsaXN0TWVudSh0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgIF90aGlzMi5tZW51TGlzdCA9IF90aGlzMi5oYW5kbGVUcmVlKHJlc3BvbnNlLmRhdGEsICJtZW51SWQiKTsKICAgICAgICBfdGhpczIubG9hZGluZyA9IGZhbHNlOwogICAgICB9KTsKICAgIH0sCiAgICAvKiog6L2s5o2i6I+c5Y2V5pWw5o2u57uT5p6EICovbm9ybWFsaXplcjogZnVuY3Rpb24gbm9ybWFsaXplcihub2RlKSB7CiAgICAgIGlmIChub2RlLmNoaWxkcmVuICYmICFub2RlLmNoaWxkcmVuLmxlbmd0aCkgewogICAgICAgIGRlbGV0ZSBub2RlLmNoaWxkcmVuOwogICAgICB9CiAgICAgIHJldHVybiB7CiAgICAgICAgaWQ6IG5vZGUubWVudUlkLAogICAgICAgIGxhYmVsOiBub2RlLm1lbnVOYW1lLAogICAgICAgIGNoaWxkcmVuOiBub2RlLmNoaWxkcmVuCiAgICAgIH07CiAgICB9LAogICAgLyoqIOafpeivouiPnOWNleS4i+aLieagkee7k+aehCAqL2dldFRyZWVzZWxlY3Q6IGZ1bmN0aW9uIGdldFRyZWVzZWxlY3QoKSB7CiAgICAgIHZhciBfdGhpczMgPSB0aGlzOwogICAgICBsaXN0TWVudSgpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgX3RoaXMzLm1lbnVPcHRpb25zID0gW107CiAgICAgICAgdmFyIG1lbnUgPSB7CiAgICAgICAgICBtZW51SWQ6IDAsCiAgICAgICAgICBtZW51TmFtZTogJ+S4u+exu+ebricsCiAgICAgICAgICBjaGlsZHJlbjogW10KICAgICAgICB9OwogICAgICAgIG1lbnUuY2hpbGRyZW4gPSBfdGhpczMuaGFuZGxlVHJlZShyZXNwb25zZS5kYXRhLCAibWVudUlkIik7CiAgICAgICAgX3RoaXMzLm1lbnVPcHRpb25zLnB1c2gobWVudSk7CiAgICAgIH0pOwogICAgfSwKICAgIC8vIOaYvuekuueKtuaAgeWtl+WFuOe/u+ivkQogICAgdmlzaWJsZUZvcm1hdDogZnVuY3Rpb24gdmlzaWJsZUZvcm1hdChyb3csIGNvbHVtbikgewogICAgICBpZiAocm93Lm1lbnVUeXBlID09ICJGIikgewogICAgICAgIHJldHVybiAiIjsKICAgICAgfQogICAgICByZXR1cm4gdGhpcy5zZWxlY3REaWN0TGFiZWwodGhpcy52aXNpYmxlT3B0aW9ucywgcm93LnZpc2libGUpOwogICAgfSwKICAgIC8vIOiPnOWNleeKtuaAgeWtl+WFuOe/u+ivkQogICAgc3RhdHVzRm9ybWF0OiBmdW5jdGlvbiBzdGF0dXNGb3JtYXQocm93LCBjb2x1bW4pIHsKICAgICAgaWYgKHJvdy5tZW51VHlwZSA9PSAiRiIpIHsKICAgICAgICByZXR1cm4gIiI7CiAgICAgIH0KICAgICAgcmV0dXJuIHRoaXMuc2VsZWN0RGljdExhYmVsKHRoaXMuc3RhdHVzT3B0aW9ucywgcm93LnN0YXR1cyk7CiAgICB9LAogICAgLy8g5Y+W5raI5oyJ6ZKuCiAgICBjYW5jZWw6IGZ1bmN0aW9uIGNhbmNlbCgpIHsKICAgICAgdGhpcy5vcGVuID0gZmFsc2U7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgIH0sCiAgICAvLyDooajljZXph43nva4KICAgIHJlc2V0OiBmdW5jdGlvbiByZXNldCgpIHsKICAgICAgdGhpcy5mb3JtID0gewogICAgICAgIG1lbnVJZDogdW5kZWZpbmVkLAogICAgICAgIHBhcmVudElkOiAwLAogICAgICAgIG1lbnVOYW1lOiB1bmRlZmluZWQsCiAgICAgICAgaWNvbjogdW5kZWZpbmVkLAogICAgICAgIG1lbnVUeXBlOiAiTSIsCiAgICAgICAgb3JkZXJOdW06IHVuZGVmaW5lZCwKICAgICAgICBpc0ZyYW1lOiAiMSIsCiAgICAgICAgaXNDYWNoZTogIjAiLAogICAgICAgIHZpc2libGU6ICIwIiwKICAgICAgICBzdGF0dXM6ICIwIgogICAgICB9OwogICAgICB0aGlzLnJlc2V0Rm9ybSgiZm9ybSIpOwogICAgfSwKICAgIC8qKiDmkJzntKLmjInpkq7mk43kvZwgKi9oYW5kbGVRdWVyeTogZnVuY3Rpb24gaGFuZGxlUXVlcnkoKSB7CiAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgfSwKICAgIC8qKiDph43nva7mjInpkq7mk43kvZwgKi9yZXNldFF1ZXJ5OiBmdW5jdGlvbiByZXNldFF1ZXJ5KCkgewogICAgICB0aGlzLnJlc2V0Rm9ybSgicXVlcnlGb3JtIik7CiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsKICAgIH0sCiAgICAvKiog5paw5aKe5oyJ6ZKu5pON5L2cICovaGFuZGxlQWRkOiBmdW5jdGlvbiBoYW5kbGVBZGQocm93KSB7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgICAgdGhpcy5nZXRUcmVlc2VsZWN0KCk7CiAgICAgIGlmIChyb3cgIT0gbnVsbCAmJiByb3cubWVudUlkKSB7CiAgICAgICAgdGhpcy5mb3JtLnBhcmVudElkID0gcm93Lm1lbnVJZDsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLmZvcm0ucGFyZW50SWQgPSAwOwogICAgICB9CiAgICAgIHRoaXMub3BlbiA9IHRydWU7CiAgICAgIHRoaXMudGl0bGUgPSAi5re75Yqg6I+c5Y2VIjsKICAgIH0sCiAgICAvKiog5L+u5pS55oyJ6ZKu5pON5L2cICovaGFuZGxlVXBkYXRlOiBmdW5jdGlvbiBoYW5kbGVVcGRhdGUocm93KSB7CiAgICAgIHZhciBfdGhpczQgPSB0aGlzOwogICAgICB0aGlzLnJlc2V0KCk7CiAgICAgIHRoaXMuZ2V0VHJlZXNlbGVjdCgpOwogICAgICBnZXRNZW51KHJvdy5tZW51SWQpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgX3RoaXM0LmZvcm0gPSByZXNwb25zZS5kYXRhOwogICAgICAgIF90aGlzNC5vcGVuID0gdHJ1ZTsKICAgICAgICBfdGhpczQudGl0bGUgPSAi5L+u5pS56I+c5Y2VIjsKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOaPkOS6pOaMiemSriAqLwogICAgc3VibWl0Rm9ybTogZnVuY3Rpb24gc3VibWl0Rm9ybSgpIHsKICAgICAgdmFyIF90aGlzNSA9IHRoaXM7CiAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZShmdW5jdGlvbiAodmFsaWQpIHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIGlmIChfdGhpczUuZm9ybS5tZW51SWQgIT0gdW5kZWZpbmVkKSB7CiAgICAgICAgICAgIHVwZGF0ZU1lbnUoX3RoaXM1LmZvcm0pLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgICAgICAgX3RoaXM1Lm1zZ1N1Y2Nlc3MoIuS/ruaUueaIkOWKnyIpOwogICAgICAgICAgICAgIF90aGlzNS5vcGVuID0gZmFsc2U7CiAgICAgICAgICAgICAgX3RoaXM1LmdldExpc3QoKTsKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICBhZGRNZW51KF90aGlzNS5mb3JtKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgICAgICAgIF90aGlzNS5tc2dTdWNjZXNzKCLmlrDlop7miJDlip8iKTsKICAgICAgICAgICAgICBfdGhpczUub3BlbiA9IGZhbHNlOwogICAgICAgICAgICAgIF90aGlzNS5nZXRMaXN0KCk7CiAgICAgICAgICAgIH0pOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLyoqIOWIoOmZpOaMiemSruaTjeS9nCAqL2hhbmRsZURlbGV0ZTogZnVuY3Rpb24gaGFuZGxlRGVsZXRlKHJvdykgewogICAgICB2YXIgX3RoaXM2ID0gdGhpczsKICAgICAgdGhpcy4kY29uZmlybSgn5piv5ZCm56Gu6K6k5Yig6Zmk5ZCN56ew5Li6IicgKyByb3cubWVudU5hbWUgKyAnIueahOaVsOaNrumhuT8nLCAi6K2m5ZGKIiwgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAi56Gu5a6aIiwKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAi5Y+W5raIIiwKICAgICAgICB0eXBlOiAid2FybmluZyIKICAgICAgfSkudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgcmV0dXJuIGRlbE1lbnUocm93Lm1lbnVJZCk7CiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgIF90aGlzNi5nZXRMaXN0KCk7CiAgICAgICAgX3RoaXM2Lm1zZ1N1Y2Nlc3MoIuWIoOmZpOaIkOWKnyIpOwogICAgICB9KTsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["listMenu", "getMenu", "delMenu", "addMenu", "updateMenu", "Treeselect", "IconSelect", "name", "components", "data", "loading", "showSearch", "menuList", "menuOptions", "title", "open", "visibleOptions", "statusOptions", "queryParams", "menuName", "undefined", "visible", "form", "rules", "required", "message", "trigger", "orderNum", "path", "created", "_this", "getList", "getDicts", "then", "response", "methods", "selected", "icon", "_this2", "handleTree", "normalizer", "node", "children", "length", "id", "menuId", "label", "getTreeselect", "_this3", "menu", "push", "visibleFormat", "row", "column", "menuType", "selectDictLabel", "statusFormat", "status", "cancel", "reset", "parentId", "isFrame", "isCache", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleAdd", "handleUpdate", "_this4", "submitForm", "_this5", "$refs", "validate", "valid", "msgSuccess", "handleDelete", "_this6", "$confirm", "confirmButtonText", "cancelButtonText", "type"], "sources": ["src/views/system/menu/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" v-show=\"showSearch\">\n      <el-form-item label=\"菜单名称\" prop=\"menuName\">\n        <el-input\n          v-model=\"queryParams.menuName\"\n          placeholder=\"请输入菜单名称\"\n          clearable\n          size=\"small\"\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"状态\" prop=\"status\">\n        <el-select v-model=\"queryParams.status\" placeholder=\"菜单状态\" clearable size=\"small\">\n          <el-option\n            v-for=\"dict in statusOptions\"\n            :key=\"dict.dictValue\"\n            :label=\"dict.dictLabel\"\n            :value=\"dict.dictValue\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"primary\"\n          plain\n          icon=\"el-icon-plus\"\n          size=\"mini\"\n          @click=\"handleAdd\"\n          v-hasPermi=\"['system:menu:add']\"\n        >新增</el-button>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <el-table\n      v-loading=\"loading\"\n      :data=\"menuList\"\n      row-key=\"menuId\"\n      :tree-props=\"{children: 'children', hasChildren: 'hasChildren'}\"\n    >\n      <el-table-column prop=\"menuName\" label=\"菜单名称\" :show-overflow-tooltip=\"true\" width=\"160\"></el-table-column>\n      <el-table-column prop=\"icon\" label=\"图标\" align=\"center\" width=\"100\">\n        <template slot-scope=\"scope\">\n          <svg-icon :icon-class=\"scope.row.icon\" />\n        </template>\n      </el-table-column>\n      <el-table-column prop=\"orderNum\" label=\"排序\" width=\"60\"></el-table-column>\n      <el-table-column prop=\"perms\" label=\"权限标识\" :show-overflow-tooltip=\"true\"></el-table-column>\n      <el-table-column prop=\"component\" label=\"组件路径\" :show-overflow-tooltip=\"true\"></el-table-column>\n      <el-table-column prop=\"status\" label=\"状态\" :formatter=\"statusFormat\" width=\"80\"></el-table-column>\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.createTime) }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"scope\">\n          <el-button size=\"mini\" \n            type=\"text\" \n            icon=\"el-icon-edit\" \n            @click=\"handleUpdate(scope.row)\"\n            v-hasPermi=\"['system:menu:edit']\"\n          >修改</el-button>\n          <el-button \n            size=\"mini\" \n            type=\"text\" \n            icon=\"el-icon-plus\" \n            @click=\"handleAdd(scope.row)\"\n            v-hasPermi=\"['system:menu:add']\"\n          >新增</el-button>\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-delete\"\n            @click=\"handleDelete(scope.row)\"\n            v-hasPermi=\"['system:menu:remove']\"\n          >删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <!-- 添加或修改菜单对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"600px\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"上级菜单\">\n              <treeselect\n                v-model=\"form.parentId\"\n                :options=\"menuOptions\"\n                :normalizer=\"normalizer\"\n                :show-count=\"true\"\n                placeholder=\"选择上级菜单\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"菜单类型\" prop=\"menuType\">\n              <el-radio-group v-model=\"form.menuType\">\n                <el-radio label=\"M\">目录</el-radio>\n                <el-radio label=\"C\">菜单</el-radio>\n                <el-radio label=\"F\">按钮</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item v-if=\"form.menuType != 'F'\" label=\"菜单图标\">\n              <el-popover\n                placement=\"bottom-start\"\n                width=\"460\"\n                trigger=\"click\"\n                @show=\"$refs['iconSelect'].reset()\"\n              >\n                <IconSelect ref=\"iconSelect\" @selected=\"selected\" />\n                <el-input slot=\"reference\" v-model=\"form.icon\" placeholder=\"点击选择图标\" readonly>\n                  <svg-icon\n                    v-if=\"form.icon\"\n                    slot=\"prefix\"\n                    :icon-class=\"form.icon\"\n                    class=\"el-input__icon\"\n                    style=\"height: 32px;width: 16px;\"\n                  />\n                  <i v-else slot=\"prefix\" class=\"el-icon-search el-input__icon\" />\n                </el-input>\n              </el-popover>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"菜单名称\" prop=\"menuName\">\n              <el-input v-model=\"form.menuName\" placeholder=\"请输入菜单名称\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"显示排序\" prop=\"orderNum\">\n              <el-input-number v-model=\"form.orderNum\" controls-position=\"right\" :min=\"0\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item v-if=\"form.menuType != 'F'\" label=\"是否外链\">\n              <el-radio-group v-model=\"form.isFrame\">\n                <el-radio label=\"0\">是</el-radio>\n                <el-radio label=\"1\">否</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item v-if=\"form.menuType != 'F'\" label=\"路由地址\" prop=\"path\">\n              <el-input v-model=\"form.path\" placeholder=\"请输入路由地址\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\" v-if=\"form.menuType == 'C'\">\n            <el-form-item label=\"组件路径\" prop=\"component\">\n              <el-input v-model=\"form.component\" placeholder=\"请输入组件路径\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item v-if=\"form.menuType != 'M'\" label=\"权限标识\">\n              <el-input v-model=\"form.perms\" placeholder=\"请权限标识\" maxlength=\"50\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item v-if=\"form.menuType != 'F'\" label=\"显示状态\">\n              <el-radio-group v-model=\"form.visible\">\n                <el-radio\n                  v-for=\"dict in visibleOptions\"\n                  :key=\"dict.dictValue\"\n                  :label=\"dict.dictValue\"\n                >{{dict.dictLabel}}</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item v-if=\"form.menuType != 'F'\" label=\"菜单状态\">\n              <el-radio-group v-model=\"form.status\">\n                <el-radio\n                  v-for=\"dict in statusOptions\"\n                  :key=\"dict.dictValue\"\n                  :label=\"dict.dictValue\"\n                >{{dict.dictLabel}}</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item v-if=\"form.menuType == 'C'\" label=\"是否缓存\">\n              <el-radio-group v-model=\"form.isCache\">\n                <el-radio label=\"0\">缓存</el-radio>\n                <el-radio label=\"1\">不缓存</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { listMenu, getMenu, delMenu, addMenu, updateMenu } from \"@/api/system/menu\";\nimport Treeselect from \"@riophae/vue-treeselect\";\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\";\nimport IconSelect from \"@/components/IconSelect\";\n\nexport default {\n  name: \"Menu\",\n  components: { Treeselect, IconSelect },\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 菜单表格树数据\n      menuList: [],\n      // 菜单树选项\n      menuOptions: [],\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      open: false,\n      // 显示状态数据字典\n      visibleOptions: [],\n      // 菜单状态数据字典\n      statusOptions: [],\n      // 查询参数\n      queryParams: {\n        menuName: undefined,\n        visible: undefined\n      },\n      // 表单参数\n      form: {},\n      // 表单校验\n      rules: {\n        menuName: [\n          { required: true, message: \"菜单名称不能为空\", trigger: \"blur\" }\n        ],\n        orderNum: [\n          { required: true, message: \"菜单顺序不能为空\", trigger: \"blur\" }\n        ],\n        path: [\n          { required: true, message: \"路由地址不能为空\", trigger: \"blur\" }\n        ]\n      }\n    };\n  },\n  created() {\n    this.getList();\n    this.getDicts(\"sys_show_hide\").then(response => {\n      this.visibleOptions = response.data;\n    });\n    this.getDicts(\"sys_normal_disable\").then(response => {\n      this.statusOptions = response.data;\n    });\n  },\n  methods: {\n    // 选择图标\n    selected(name) {\n      this.form.icon = name;\n    },\n    /** 查询菜单列表 */\n    getList() {\n      this.loading = true;\n      listMenu(this.queryParams).then(response => {\n        this.menuList = this.handleTree(response.data, \"menuId\");\n        this.loading = false;\n      });\n    },\n    /** 转换菜单数据结构 */\n    normalizer(node) {\n      if (node.children && !node.children.length) {\n        delete node.children;\n      }\n      return {\n        id: node.menuId,\n        label: node.menuName,\n        children: node.children\n      };\n    },\n    /** 查询菜单下拉树结构 */\n    getTreeselect() {\n      listMenu().then(response => {\n        this.menuOptions = [];\n        const menu = { menuId: 0, menuName: '主类目', children: [] };\n        menu.children = this.handleTree(response.data, \"menuId\");\n        this.menuOptions.push(menu);\n      });\n    },\n    // 显示状态字典翻译\n    visibleFormat(row, column) {\n      if (row.menuType == \"F\") {\n        return \"\";\n      }\n      return this.selectDictLabel(this.visibleOptions, row.visible);\n    },\n    // 菜单状态字典翻译\n    statusFormat(row, column) {\n      if (row.menuType == \"F\") {\n        return \"\";\n      }\n      return this.selectDictLabel(this.statusOptions, row.status);\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        menuId: undefined,\n        parentId: 0,\n        menuName: undefined,\n        icon: undefined,\n        menuType: \"M\",\n        orderNum: undefined,\n        isFrame: \"1\",\n        isCache: \"0\",\n        visible: \"0\",\n        status: \"0\"\n      };\n      this.resetForm(\"form\");\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    /** 新增按钮操作 */\n    handleAdd(row) {\n      this.reset();\n      this.getTreeselect();\n      if (row != null && row.menuId) {\n        this.form.parentId = row.menuId;\n      } else {\n        this.form.parentId = 0;\n      }\n      this.open = true;\n      this.title = \"添加菜单\";\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset();\n      this.getTreeselect();\n      getMenu(row.menuId).then(response => {\n        this.form = response.data;\n        this.open = true;\n        this.title = \"修改菜单\";\n      });\n    },\n    /** 提交按钮 */\n    submitForm: function() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          if (this.form.menuId != undefined) {\n            updateMenu(this.form).then(response => {\n              this.msgSuccess(\"修改成功\");\n              this.open = false;\n              this.getList();\n            });\n          } else {\n            addMenu(this.form).then(response => {\n              this.msgSuccess(\"新增成功\");\n              this.open = false;\n              this.getList();\n            });\n          }\n        }\n      });\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      this.$confirm('是否确认删除名称为\"' + row.menuName + '\"的数据项?', \"警告\", {\n          confirmButtonText: \"确定\",\n          cancelButtonText: \"取消\",\n          type: \"warning\"\n        }).then(function() {\n          return delMenu(row.menuId);\n        }).then(() => {\n          this.getList();\n          this.msgSuccess(\"删除成功\");\n        })\n    }\n  }\n};\n</script>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiNA,SAAAA,QAAA,EAAAC,OAAA,EAAAC,OAAA,EAAAC,OAAA,EAAAC,UAAA;AACA,OAAAC,UAAA;AACA;AACA,OAAAC,UAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IAAAH,UAAA,EAAAA,UAAA;IAAAC,UAAA,EAAAA;EAAA;EACAG,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,UAAA;MACA;MACAC,QAAA;MACA;MACAC,WAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,cAAA;MACA;MACAC,aAAA;MACA;MACAC,WAAA;QACAC,QAAA,EAAAC,SAAA;QACAC,OAAA,EAAAD;MACA;MACA;MACAE,IAAA;MACA;MACAC,KAAA;QACAJ,QAAA,GACA;UAAAK,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAC,QAAA,GACA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAE,IAAA,GACA;UAAAJ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAG,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA,KAAAC,OAAA;IACA,KAAAC,QAAA,kBAAAC,IAAA,WAAAC,QAAA;MACAJ,KAAA,CAAAd,cAAA,GAAAkB,QAAA,CAAAzB,IAAA;IACA;IACA,KAAAuB,QAAA,uBAAAC,IAAA,WAAAC,QAAA;MACAJ,KAAA,CAAAb,aAAA,GAAAiB,QAAA,CAAAzB,IAAA;IACA;EACA;EACA0B,OAAA;IACA;IACAC,QAAA,WAAAA,SAAA7B,IAAA;MACA,KAAAe,IAAA,CAAAe,IAAA,GAAA9B,IAAA;IACA;IACA,aACAwB,OAAA,WAAAA,QAAA;MAAA,IAAAO,MAAA;MACA,KAAA5B,OAAA;MACAV,QAAA,MAAAkB,WAAA,EAAAe,IAAA,WAAAC,QAAA;QACAI,MAAA,CAAA1B,QAAA,GAAA0B,MAAA,CAAAC,UAAA,CAAAL,QAAA,CAAAzB,IAAA;QACA6B,MAAA,CAAA5B,OAAA;MACA;IACA;IACA,eACA8B,UAAA,WAAAA,WAAAC,IAAA;MACA,IAAAA,IAAA,CAAAC,QAAA,KAAAD,IAAA,CAAAC,QAAA,CAAAC,MAAA;QACA,OAAAF,IAAA,CAAAC,QAAA;MACA;MACA;QACAE,EAAA,EAAAH,IAAA,CAAAI,MAAA;QACAC,KAAA,EAAAL,IAAA,CAAAtB,QAAA;QACAuB,QAAA,EAAAD,IAAA,CAAAC;MACA;IACA;IACA,gBACAK,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MACAhD,QAAA,GAAAiC,IAAA,WAAAC,QAAA;QACAc,MAAA,CAAAnC,WAAA;QACA,IAAAoC,IAAA;UAAAJ,MAAA;UAAA1B,QAAA;UAAAuB,QAAA;QAAA;QACAO,IAAA,CAAAP,QAAA,GAAAM,MAAA,CAAAT,UAAA,CAAAL,QAAA,CAAAzB,IAAA;QACAuC,MAAA,CAAAnC,WAAA,CAAAqC,IAAA,CAAAD,IAAA;MACA;IACA;IACA;IACAE,aAAA,WAAAA,cAAAC,GAAA,EAAAC,MAAA;MACA,IAAAD,GAAA,CAAAE,QAAA;QACA;MACA;MACA,YAAAC,eAAA,MAAAvC,cAAA,EAAAoC,GAAA,CAAA/B,OAAA;IACA;IACA;IACAmC,YAAA,WAAAA,aAAAJ,GAAA,EAAAC,MAAA;MACA,IAAAD,GAAA,CAAAE,QAAA;QACA;MACA;MACA,YAAAC,eAAA,MAAAtC,aAAA,EAAAmC,GAAA,CAAAK,MAAA;IACA;IACA;IACAC,MAAA,WAAAA,OAAA;MACA,KAAA3C,IAAA;MACA,KAAA4C,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAArC,IAAA;QACAuB,MAAA,EAAAzB,SAAA;QACAwC,QAAA;QACAzC,QAAA,EAAAC,SAAA;QACAiB,IAAA,EAAAjB,SAAA;QACAkC,QAAA;QACA3B,QAAA,EAAAP,SAAA;QACAyC,OAAA;QACAC,OAAA;QACAzC,OAAA;QACAoC,MAAA;MACA;MACA,KAAAM,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAAjC,OAAA;IACA;IACA,aACAkC,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAAC,WAAA;IACA;IACA,aACAE,SAAA,WAAAA,UAAAd,GAAA;MACA,KAAAO,KAAA;MACA,KAAAZ,aAAA;MACA,IAAAK,GAAA,YAAAA,GAAA,CAAAP,MAAA;QACA,KAAAvB,IAAA,CAAAsC,QAAA,GAAAR,GAAA,CAAAP,MAAA;MACA;QACA,KAAAvB,IAAA,CAAAsC,QAAA;MACA;MACA,KAAA7C,IAAA;MACA,KAAAD,KAAA;IACA;IACA,aACAqD,YAAA,WAAAA,aAAAf,GAAA;MAAA,IAAAgB,MAAA;MACA,KAAAT,KAAA;MACA,KAAAZ,aAAA;MACA9C,OAAA,CAAAmD,GAAA,CAAAP,MAAA,EAAAZ,IAAA,WAAAC,QAAA;QACAkC,MAAA,CAAA9C,IAAA,GAAAY,QAAA,CAAAzB,IAAA;QACA2D,MAAA,CAAArD,IAAA;QACAqD,MAAA,CAAAtD,KAAA;MACA;IACA;IACA;IACAuD,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAH,MAAA,CAAAhD,IAAA,CAAAuB,MAAA,IAAAzB,SAAA;YACAhB,UAAA,CAAAkE,MAAA,CAAAhD,IAAA,EAAAW,IAAA,WAAAC,QAAA;cACAoC,MAAA,CAAAI,UAAA;cACAJ,MAAA,CAAAvD,IAAA;cACAuD,MAAA,CAAAvC,OAAA;YACA;UACA;YACA5B,OAAA,CAAAmE,MAAA,CAAAhD,IAAA,EAAAW,IAAA,WAAAC,QAAA;cACAoC,MAAA,CAAAI,UAAA;cACAJ,MAAA,CAAAvD,IAAA;cACAuD,MAAA,CAAAvC,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACA4C,YAAA,WAAAA,aAAAvB,GAAA;MAAA,IAAAwB,MAAA;MACA,KAAAC,QAAA,gBAAAzB,GAAA,CAAAjC,QAAA;QACA2D,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAA/C,IAAA;QACA,OAAA/B,OAAA,CAAAkD,GAAA,CAAAP,MAAA;MACA,GAAAZ,IAAA;QACA2C,MAAA,CAAA7C,OAAA;QACA6C,MAAA,CAAAF,UAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}