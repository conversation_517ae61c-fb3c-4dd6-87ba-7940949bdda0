{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Process/components/custom/customContextPad.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Process/components/custom/customContextPad.js", "mtime": 1650124106000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js", "mtime": 1751171659043}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}