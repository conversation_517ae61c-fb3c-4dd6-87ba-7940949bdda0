{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Notice/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Notice/index.vue", "mtime": 1655640984000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IG5Ob3RpY2UgfSBmcm9tICdAL2FwaS9zeXN0ZW0vbm90aWNlJzsKZXhwb3J0IGRlZmF1bHQgewogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBoYXNOb3RpY2U6IGZhbHNlLAogICAgICB0aW1lcjogJycsCiAgICAgIGNvdW50OiAwCiAgICB9OwogIH0sCiAgbW91bnRlZDogZnVuY3Rpb24gbW91bnRlZCgpIHsKICAgIC8v5a6a5pe25p+lCiAgICB0aGlzLnRpbWVyID0gc2V0SW50ZXJ2YWwodGhpcy5jaGVja05vdGljZSwgNjAgKiAxMDAwICogMSk7CiAgfSwKICBiZWZvcmVEZXN0cm95OiBmdW5jdGlvbiBiZWZvcmVEZXN0cm95KCkgewogICAgY2xlYXJJbnRlcnZhbCh0aGlzLnRpbWVyKTsKICB9LAogIGNyZWF0ZWQ6IGZ1bmN0aW9uIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmNoZWNrTm90aWNlKCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICBjbGljazogZnVuY3Rpb24gY2xpY2soKSB7CiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHsKICAgICAgICBwYXRoOiAnL3VzZXIvbm90aWNlJwogICAgICB9KTsKICAgIH0sCiAgICBjaGVja05vdGljZTogZnVuY3Rpb24gY2hlY2tOb3RpY2UoKSB7CiAgICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICAgIGlmICh0aGlzLmhhc05vdGljZSkgewogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICBuTm90aWNlKCkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICBfdGhpcy5oYXNOb3RpY2UgPSByZXNwb25zZS5kYXRhID4gMDsKICAgICAgICBfdGhpcy5jb3VudCA9IHJlc3BvbnNlLmRhdGE7CiAgICAgIH0pOwogICAgfQogIH0KfTs="}, {"version": 3, "names": ["nNotice", "data", "hasNotice", "timer", "count", "mounted", "setInterval", "checkNotice", "<PERSON><PERSON><PERSON><PERSON>", "clearInterval", "created", "methods", "click", "$router", "push", "path", "_this", "then", "response"], "sources": ["src/components/Notice/index.vue"], "sourcesContent": ["<template>\n  <div>\n    <el-badge :value=\"count\" :max=\"99\">\n      <i class=\"el-icon-bell\" @click=\"click\" />\n    </el-badge>\n  </div>\n</template>\n\n<script>\nimport { nNotice } from '@/api/system/notice'\n\nexport default {\n  data() {\n    return {\n      hasNotice: false,\n      timer: '',\n      count: 0\n    }\n  },\n  mounted() {\n    //定时查\n    this.timer = setInterval(this.checkNotice, 60 * 1000 * 1)\n  },\n  beforeDestroy() {\n    clearInterval(this.timer)\n  },\n  created() {\n    this.checkNotice()\n  },\n  methods: {\n    click() {\n      this.$router.push({ path: '/user/notice' })\n    },\n    checkNotice() {\n      if (this.hasNotice) {\n        return\n      }\n      nNotice().then(response => {\n        this.hasNotice = response.data > 0\n        this.count = response.data\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n::v-deep .el-badge__content.is-fixed.is-dot {\n  right: 5px;\n  top: 10px;\n}\n\n.el-icon-bell {\n  font-size: 20px;\n  cursor: pointer;\n}\n\n\n</style>\n<style>\n.el-badge__content.is-fixed {\n    margin: 10px -2px;\n}\n.el-badge__content{\n  height: 20px;\n}\n</style>"], "mappings": ";;;;;;;;;AASA,SAAAA,OAAA;AAEA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA;MACAC,KAAA;MACAC,KAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA;IACA,KAAAF,KAAA,GAAAG,WAAA,MAAAC,WAAA;EACA;EACAC,aAAA,WAAAA,cAAA;IACAC,aAAA,MAAAN,KAAA;EACA;EACAO,OAAA,WAAAA,QAAA;IACA,KAAAH,WAAA;EACA;EACAI,OAAA;IACAC,KAAA,WAAAA,MAAA;MACA,KAAAC,OAAA,CAAAC,IAAA;QAAAC,IAAA;MAAA;IACA;IACAR,WAAA,WAAAA,YAAA;MAAA,IAAAS,KAAA;MACA,SAAAd,SAAA;QACA;MACA;MACAF,OAAA,GAAAiB,IAAA,WAAAC,QAAA;QACAF,KAAA,CAAAd,SAAA,GAAAgB,QAAA,CAAAjB,IAAA;QACAe,KAAA,CAAAZ,KAAA,GAAAc,QAAA,CAAAjB,IAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}