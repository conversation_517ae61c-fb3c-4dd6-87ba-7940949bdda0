{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/utils/auth.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/utils/auth.js", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IENvb2tpZXMgZnJvbSAnanMtY29va2llJzsKdmFyIFRva2VuS2V5ID0gJ0FkbWluLVRva2VuJzsKZXhwb3J0IGZ1bmN0aW9uIGdldFRva2VuKCkgewogIHJldHVybiBDb29raWVzLmdldChUb2tlbktleSk7Cn0KZXhwb3J0IGZ1bmN0aW9uIHNldFRva2VuKHRva2VuKSB7CiAgcmV0dXJuIENvb2tpZXMuc2V0KFRva2VuS2V5LCB0b2tlbik7Cn0KZXhwb3J0IGZ1bmN0aW9uIHJlbW92ZVRva2VuKCkgewogIHJldHVybiBDb29raWVzLnJlbW92ZShUb2tlbktleSk7Cn0="}, {"version": 3, "names": ["Cookies", "TokenKey", "getToken", "get", "setToken", "token", "set", "removeToken", "remove"], "sources": ["/Users/<USER>/develop/code/project/haijia/adminweb/src/utils/auth.js"], "sourcesContent": ["import Cookies from 'js-cookie'\n\nconst TokenKey = 'Admin-Token'\n\nexport function getToken() {\n  return Cookies.get(TokenKey)\n}\n\nexport function setToken(token) {\n  return Cookies.set(To<PERSON><PERSON><PERSON>, token)\n}\n\nexport function removeToken() {\n  return Cookies.remove(TokenKey)\n}\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,WAAW;AAE/B,IAAMC,QAAQ,GAAG,aAAa;AAE9B,OAAO,SAASC,QAAQA,CAAA,EAAG;EACzB,OAAOF,OAAO,CAACG,GAAG,CAACF,QAAQ,CAAC;AAC9B;AAEA,OAAO,SAASG,QAAQA,CAACC,KAAK,EAAE;EAC9B,OAAOL,OAAO,CAACM,GAAG,CAACL,QAAQ,EAAEI,KAAK,CAAC;AACrC;AAEA,OAAO,SAASE,WAAWA,CAAA,EAAG;EAC5B,OAAOP,OAAO,CAACQ,MAAM,CAACP,QAAQ,CAAC;AACjC", "ignoreList": []}]}