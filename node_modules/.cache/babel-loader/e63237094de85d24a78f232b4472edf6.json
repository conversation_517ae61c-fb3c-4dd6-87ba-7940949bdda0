{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/components/SizeSelect/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/components/SizeSelect/index.vue", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmV4cG9ydCBkZWZhdWx0IHsKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgc2l6ZU9wdGlvbnM6IFt7CiAgICAgICAgbGFiZWw6ICdEZWZhdWx0JywKICAgICAgICB2YWx1ZTogJ2RlZmF1bHQnCiAgICAgIH0sIHsKICAgICAgICBsYWJlbDogJ01lZGl1bScsCiAgICAgICAgdmFsdWU6ICdtZWRpdW0nCiAgICAgIH0sIHsKICAgICAgICBsYWJlbDogJ1NtYWxsJywKICAgICAgICB2YWx1ZTogJ3NtYWxsJwogICAgICB9LCB7CiAgICAgICAgbGFiZWw6ICdNaW5pJywKICAgICAgICB2YWx1ZTogJ21pbmknCiAgICAgIH1dCiAgICB9OwogIH0sCiAgY29tcHV0ZWQ6IHsKICAgIHNpemU6IGZ1bmN0aW9uIHNpemUoKSB7CiAgICAgIHJldHVybiB0aGlzLiRzdG9yZS5nZXR0ZXJzLnNpemU7CiAgICB9CiAgfSwKICBtZXRob2RzOiB7CiAgICBoYW5kbGVTZXRTaXplOiBmdW5jdGlvbiBoYW5kbGVTZXRTaXplKHNpemUpIHsKICAgICAgdGhpcy4kRUxFTUVOVC5zaXplID0gc2l6ZTsKICAgICAgdGhpcy4kc3RvcmUuZGlzcGF0Y2goJ2FwcC9zZXRTaXplJywgc2l6ZSk7CiAgICAgIHRoaXMucmVmcmVzaFZpZXcoKTsKICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgbWVzc2FnZTogJ1N3aXRjaCBTaXplIFN1Y2Nlc3MnLAogICAgICAgIHR5cGU6ICdzdWNjZXNzJwogICAgICB9KTsKICAgIH0sCiAgICByZWZyZXNoVmlldzogZnVuY3Rpb24gcmVmcmVzaFZpZXcoKSB7CiAgICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICAgIC8vIEluIG9yZGVyIHRvIG1ha2UgdGhlIGNhY2hlZCBwYWdlIHJlLXJlbmRlcmVkCiAgICAgIHRoaXMuJHN0b3JlLmRpc3BhdGNoKCd0YWdzVmlldy9kZWxBbGxDYWNoZWRWaWV3cycsIHRoaXMuJHJvdXRlKTsKICAgICAgdmFyIGZ1bGxQYXRoID0gdGhpcy4kcm91dGUuZnVsbFBhdGg7CiAgICAgIHRoaXMuJG5leHRUaWNrKGZ1bmN0aW9uICgpIHsKICAgICAgICBfdGhpcy4kcm91dGVyLnJlcGxhY2UoewogICAgICAgICAgcGF0aDogJy9yZWRpcmVjdCcgKyBmdWxsUGF0aAogICAgICAgIH0pOwogICAgICB9KTsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["data", "sizeOptions", "label", "value", "computed", "size", "$store", "getters", "methods", "handleSetSize", "$ELEMENT", "dispatch", "refresh<PERSON>iew", "$message", "message", "type", "_this", "$route", "fullPath", "$nextTick", "$router", "replace", "path"], "sources": ["src/components/SizeSelect/index.vue"], "sourcesContent": ["<template>\n  <el-dropdown trigger=\"click\" @command=\"handleSetSize\">\n    <div>\n      <svg-icon class-name=\"size-icon\" icon-class=\"size\" />\n    </div>\n    <el-dropdown-menu slot=\"dropdown\">\n      <el-dropdown-item v-for=\"item of sizeOptions\" :key=\"item.value\" :disabled=\"size===item.value\" :command=\"item.value\">\n        {{\n          item.label }}\n      </el-dropdown-item>\n    </el-dropdown-menu>\n  </el-dropdown>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      sizeOptions: [\n        { label: 'Default', value: 'default' },\n        { label: 'Medium', value: 'medium' },\n        { label: 'Small', value: 'small' },\n        { label: 'Mini', value: 'mini' }\n      ]\n    }\n  },\n  computed: {\n    size() {\n      return this.$store.getters.size\n    }\n  },\n  methods: {\n    handleSetSize(size) {\n      this.$ELEMENT.size = size\n      this.$store.dispatch('app/setSize', size)\n      this.refreshView()\n      this.$message({\n        message: 'Switch Size Success',\n        type: 'success'\n      })\n    },\n    refreshView() {\n      // In order to make the cached page re-rendered\n      this.$store.dispatch('tagsView/delAllCachedViews', this.$route)\n\n      const { fullPath } = this.$route\n\n      this.$nextTick(() => {\n        this.$router.replace({\n          path: '/redirect' + fullPath\n        })\n      })\n    }\n  }\n\n}\n</script>\n"], "mappings": ";;;;;;;;;;;;;;;AAeA;EACAA,IAAA,WAAAA,KAAA;IACA;MACAC,WAAA,GACA;QAAAC,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA;IAEA;EACA;EACAC,QAAA;IACAC,IAAA,WAAAA,KAAA;MACA,YAAAC,MAAA,CAAAC,OAAA,CAAAF,IAAA;IACA;EACA;EACAG,OAAA;IACAC,aAAA,WAAAA,cAAAJ,IAAA;MACA,KAAAK,QAAA,CAAAL,IAAA,GAAAA,IAAA;MACA,KAAAC,MAAA,CAAAK,QAAA,gBAAAN,IAAA;MACA,KAAAO,WAAA;MACA,KAAAC,QAAA;QACAC,OAAA;QACAC,IAAA;MACA;IACA;IACAH,WAAA,WAAAA,YAAA;MAAA,IAAAI,KAAA;MACA;MACA,KAAAV,MAAA,CAAAK,QAAA,oCAAAM,MAAA;MAEA,IAAAC,QAAA,QAAAD,MAAA,CAAAC,QAAA;MAEA,KAAAC,SAAA;QACAH,KAAA,CAAAI,OAAA,CAAAC,OAAA;UACAC,IAAA,gBAAAJ;QACA;MACA;IACA;EACA;AAEA", "ignoreList": []}]}