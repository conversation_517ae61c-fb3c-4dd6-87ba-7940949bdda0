{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/flowable/task/record/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/flowable/task/record/index.vue", "mtime": 1668865255000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js", "mtime": 1751171659043}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGZsb3dSZWNvcmQgfSBmcm9tICJAL2FwaS9mbG93YWJsZS9maW5pc2hlZCI7CmltcG9ydCBQYXJzZXIgZnJvbSAnQC9jb21wb25lbnRzL3BhcnNlci9QYXJzZXInOwppbXBvcnQgeyBkZWZpbml0aW9uU3RhcnRCeUtleSwgZ2V0UHJvY2Vzc1ZhcmlhYmxlcywgcmVhZFhtbEJ5S2V5LCBnZXRGbG93Vmlld2VyIGFzIF9nZXRGbG93Vmlld2VyIH0gZnJvbSAiQC9hcGkvZmxvd2FibGUvZGVmaW5pdGlvbiI7CmltcG9ydCB7IGNvbXBsZXRlLCByZWplY3RUYXNrLCByZXR1cm5MaXN0LCByZXR1cm5UYXNrLCBnZXROZXh0Rmxvd05vZGUgYXMgX2dldE5leHRGbG93Tm9kZSwgZGVsZWdhdGUgfSBmcm9tICJAL2FwaS9mbG93YWJsZS90b2RvIjsKaW1wb3J0IGZsb3cgZnJvbSAnQC92aWV3cy9mbG93YWJsZS90YXNrL3JlY29yZC9mbG93JzsKaW1wb3J0IHsgdHJlZXNlbGVjdCB9IGZyb20gIkAvYXBpL3N5c3RlbS9kZXB0IjsKaW1wb3J0ICJAcmlvcGhhZS92dWUtdHJlZXNlbGVjdC9kaXN0L3Z1ZS10cmVlc2VsZWN0LmNzcyI7CmltcG9ydCBUcmVlc2VsZWN0IGZyb20gIkByaW9waGFlL3Z1ZS10cmVlc2VsZWN0IjsKaW1wb3J0IHsgbGlzdFVzZXIgfSBmcm9tICJAL2FwaS9zeXN0ZW0vdXNlciI7CmltcG9ydCBtb21lbnQgZnJvbSAnbW9tZW50JzsKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICJSZWNvcmQiLAogIGNvbXBvbmVudHM6IHsKICAgIFBhcnNlcjogUGFyc2VyLAogICAgZmxvdzogZmxvdywKICAgIFRyZWVzZWxlY3Q6IFRyZWVzZWxlY3QKICB9LAogIHByb3BzOiB7CiAgICBwcm9jRGVmS2V5OiB7CiAgICAgIHR5cGU6IFN0cmluZywKICAgICAgZGVmYXVsdDogdW5kZWZpbmVkCiAgICB9LAogICAgdGFza0lkOiB7CiAgICAgIHR5cGU6IFN0cmluZywKICAgICAgZGVmYXVsdDogdW5kZWZpbmVkCiAgICB9LAogICAgcHJvY0luc0lkOiB7CiAgICAgIHR5cGU6IFN0cmluZywKICAgICAgZGVmYXVsdDogdW5kZWZpbmVkCiAgICB9CiAgfSwKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgLy8g5qih5Z6LeG1s5pWw5o2uCiAgICAgIHhtbERhdGE6ICIiLAogICAgICB0YXNrTGlzdDogW10sCiAgICAgIC8vIOmDqOmXqOWQjeensAogICAgICBkZXB0TmFtZTogdW5kZWZpbmVkLAogICAgICAvLyDpg6jpl6jmoJHpgInpobkKICAgICAgZGVwdE9wdGlvbnM6IHVuZGVmaW5lZCwKICAgICAgLy8g55So5oi36KGo5qC85pWw5o2uCiAgICAgIHVzZXJMaXN0OiBudWxsLAogICAgICBkZWZhdWx0UHJvcHM6IHsKICAgICAgICBjaGlsZHJlbjogImNoaWxkcmVuIiwKICAgICAgICBsYWJlbDogImxhYmVsIgogICAgICB9LAogICAgICAvLyDmn6Xor6Llj4LmlbAKICAgICAgcXVlcnlQYXJhbXM6IHsKICAgICAgICBkZXB0SWQ6IHVuZGVmaW5lZAogICAgICB9LAogICAgICAvLyDpga7nvanlsYIKICAgICAgbG9hZGluZzogdHJ1ZSwKICAgICAgZmxvd1JlY29yZExpc3Q6IFtdLAogICAgICAvLyDmtYHnqIvmtYHovazmlbDmja4KICAgICAgZm9ybUNvbmZDb3B5OiB7fSwKICAgICAgc3JjOiBudWxsLAogICAgICBydWxlczoge30sCiAgICAgIC8vIOihqOWNleagoemqjAogICAgICB2YXJpYWJsZXNGb3JtOiB7fSwKICAgICAgLy8g5rWB56iL5Y+Y6YeP5pWw5o2uCiAgICAgIHRhc2tGb3JtOiB7CiAgICAgICAgcmV0dXJuVGFza1Nob3c6IGZhbHNlLAogICAgICAgIC8vIOaYr+WQpuWxleekuuWbnumAgOihqOWNlQogICAgICAgIGRlbGVnYXRlVGFza1Nob3c6IGZhbHNlLAogICAgICAgIC8vIOaYr+WQpuWxleekuuWbnumAgOihqOWNlQogICAgICAgIGRlZmF1bHRUYXNrU2hvdzogdHJ1ZSwKICAgICAgICAvLyDpu5jorqTlpITnkIYKICAgICAgICBzZW5kVXNlclNob3c6IGZhbHNlLAogICAgICAgIC8vIOWuoeaJueeUqOaItwogICAgICAgIG11bHRpcGxlOiBmYWxzZSwKICAgICAgICBjb21tZW50OiAiIiwKICAgICAgICAvLyDmhI/op4HlhoXlrrkKICAgICAgICBwcm9jSW5zSWQ6ICIiLAogICAgICAgIC8vIOa1geeoi+WunuS+i+e8luWPtwogICAgICAgIGluc3RhbmNlSWQ6ICIiLAogICAgICAgIC8vIOa1geeoi+WunuS+i+e8luWPtwogICAgICAgIHRhc2tJZDogIiIsCiAgICAgICAgLy8g5rWB56iL5Lu75Yqh57yW5Y+3CiAgICAgICAgcHJvY0RlZktleTogIiIsCiAgICAgICAgLy8g5rWB56iL57yW5Y+3CiAgICAgICAgdmFyczogIiIsCiAgICAgICAgdGFyZ2V0S2V5OiAiIgogICAgICB9LAogICAgICB1c2VyRGF0YUxpc3Q6IFtdLAogICAgICAvLyDmtYHnqIvlgJnpgInkuroKICAgICAgYXNzaWduZWU6IG51bGwsCiAgICAgIGZvcm1Db25mOiB7fSwKICAgICAgLy8g6buY6K6k6KGo5Y2V5pWw5o2uCiAgICAgIGZvcm1Db25mT3BlbjogZmFsc2UsCiAgICAgIC8vIOaYr+WQpuWKoOi9vem7mOiupOihqOWNleaVsOaNrgogICAgICB2YXJpYWJsZXM6IFtdLAogICAgICAvLyDmtYHnqIvlj5jph4/mlbDmja4KICAgICAgdmFyaWFibGVzRGF0YToge30sCiAgICAgIC8vIOa1geeoi+WPmOmHj+aVsOaNrgogICAgICB2YXJpYWJsZU9wZW46IGZhbHNlLAogICAgICAvLyDmmK/lkKbliqDovb3mtYHnqIvlj5jph4/mlbDmja4KICAgICAgcmV0dXJuVGFza0xpc3Q6IFtdLAogICAgICAvLyDlm57pgIDliJfooajmlbDmja4KICAgICAgZmluaXNoZWQ6ICdmYWxzZScsCiAgICAgIGNvbXBsZXRlVGl0bGU6IG51bGwsCiAgICAgIGNvbXBsZXRlT3BlbjogZmFsc2UsCiAgICAgIHJldHVyblRpdGxlOiBudWxsLAogICAgICByZXR1cm5PcGVuOiBmYWxzZSwKICAgICAgcmVqZWN0T3BlbjogZmFsc2UsCiAgICAgIHJlamVjdFRpdGxlOiBudWxsLAogICAgICB1c2VyRGF0YTogW10KICAgIH07CiAgfSwKICBjcmVhdGVkOiBmdW5jdGlvbiBjcmVhdGVkKCkgewogICAgY29uc29sZS5sb2coIj09PT09PT09cmVjb3JkPT09PT09PT1jcmVhdGVkPT4+PiIpOwogICAgY29uc29sZS5sb2codGhpcy5fcHJvcHMpOwogICAgdmFyIF90aGlzJF9wcm9wcyA9IHRoaXMuX3Byb3BzLAogICAgICB0YXNrSWQgPSBfdGhpcyRfcHJvcHMudGFza0lkLAogICAgICBwcm9jRGVmS2V5ID0gX3RoaXMkX3Byb3BzLnByb2NEZWZLZXksCiAgICAgIHByb2NJbnNJZCA9IF90aGlzJF9wcm9wcy5wcm9jSW5zSWQsCiAgICAgIGZpbmlzaGVkID0gX3RoaXMkX3Byb3BzLmZpbmlzaGVkOwogICAgdGhpcy50YXNrRm9ybS50YXNrSWQgPSB0YXNrSWQ7CiAgICB0aGlzLnRhc2tGb3JtLnByb2NJbnNJZCA9IHByb2NJbnNJZDsKICAgIHRoaXMudGFza0Zvcm0uaW5zdGFuY2VJZCA9IHByb2NJbnNJZDsKICAgIC8vIOWIneWni+WMluihqOWNlQogICAgdGhpcy50YXNrRm9ybS5wcm9jRGVmS2V5ID0gcHJvY0RlZktleTsKICAgIHRoaXMuZmluaXNoZWQgPSBmaW5pc2hlZDsKICAgIC8vIOWbnuaYvua1geeoi+iusOW9lQogICAgdGhpcy5nZXRGbG93Vmlld2VyKHRoaXMudGFza0Zvcm0ucHJvY0luc0lkLCB0aGlzLnRhc2tGb3JtLnByb2NEZWZLZXkpOwogICAgLy8g5rWB56iL5Lu75Yqh6YeN6I635Y+W5Y+Y6YeP6KGo5Y2VCiAgICBpZiAodGhpcy50YXNrRm9ybS50YXNrSWQpIHsKICAgICAgdGhpcy5wcm9jZXNzVmFyaWFibGVzKHRoaXMudGFza0Zvcm0udGFza0lkKTsKICAgICAgdGhpcy5nZXROZXh0Rmxvd05vZGUodGhpcy50YXNrRm9ybS50YXNrSWQpOwogICAgfQogICAgdGhpcy5nZXRGbG93UmVjb3JkTGlzdCh0aGlzLnRhc2tGb3JtLnByb2NJbnNJZCk7CiAgICB0aGlzLmZpbmlzaGVkID0gdGhpcy4kcm91dGUucXVlcnkgJiYgdGhpcy4kcm91dGUucXVlcnkuZmluaXNoZWQ7CiAgfSwKICBhY3RpdmF0ZWQ6IGZ1bmN0aW9uIGFjdGl2YXRlZCgpIHsKICAgIGNvbnNvbGUubG9nKCI9PT09PT09PXJlY29yZD09PT09PT09YWN0aXZhdGVkPT4+PiIpOwogICAgdmFyIF90aGlzJF9wcm9wczIgPSB0aGlzLl9wcm9wcywKICAgICAgdGFza0lkID0gX3RoaXMkX3Byb3BzMi50YXNrSWQsCiAgICAgIHByb2NEZWZLZXkgPSBfdGhpcyRfcHJvcHMyLnByb2NEZWZLZXksCiAgICAgIHByb2NJbnNJZCA9IF90aGlzJF9wcm9wczIucHJvY0luc0lkLAogICAgICBmaW5pc2hlZCA9IF90aGlzJF9wcm9wczIuZmluaXNoZWQ7CiAgICBjb25zb2xlLmxvZyh0aGlzLl9wcm9wcyk7CiAgICB0aGlzLnRhc2tGb3JtLnRhc2tJZCA9IHRhc2tJZDsKICAgIHRoaXMudGFza0Zvcm0ucHJvY0luc0lkID0gcHJvY0luc0lkOwogICAgdGhpcy50YXNrRm9ybS5pbnN0YW5jZUlkID0gcHJvY0luc0lkOwogICAgLy8g5Yid5aeL5YyW6KGo5Y2VCiAgICB0aGlzLnRhc2tGb3JtLnByb2NEZWZLZXkgPSBwcm9jRGVmS2V5OwogICAgdGhpcy5maW5pc2hlZCA9IGZpbmlzaGVkOwogICAgLy8g5Zue5pi+5rWB56iL6K6w5b2VCiAgICB0aGlzLmdldEZsb3dWaWV3ZXIodGhpcy50YXNrRm9ybS5wcm9jSW5zSWQsIHRoaXMudGFza0Zvcm0ucHJvY0RlZktleSk7CiAgICAvLyDmtYHnqIvku7vliqHph43ojrflj5blj5jph4/ooajljZUKICAgIGlmICh0aGlzLnRhc2tGb3JtLnRhc2tJZCkgewogICAgICB0aGlzLnByb2Nlc3NWYXJpYWJsZXModGhpcy50YXNrRm9ybS50YXNrSWQpOwogICAgICB0aGlzLmdldE5leHRGbG93Tm9kZSh0aGlzLnRhc2tGb3JtLnRhc2tJZCk7CiAgICB9CiAgICB0aGlzLmdldEZsb3dSZWNvcmRMaXN0KHRoaXMudGFza0Zvcm0ucHJvY0luc0lkKTsKICAgIHRoaXMuZmluaXNoZWQgPSB0aGlzLiRyb3V0ZS5xdWVyeSAmJiB0aGlzLiRyb3V0ZS5xdWVyeS5maW5pc2hlZDsKICB9LAogIG1vdW50ZWQ6IGZ1bmN0aW9uIG1vdW50ZWQoKSB7CiAgICAvLyAvLyDooajljZXmlbDmja7lm57loavvvIzmqKHmi5/lvILmraXor7fmsYLlnLrmma8KICAgIC8vIHNldFRpbWVvdXQoKCkgPT4gewogICAgLy8gICAvLyDor7fmsYLlm57mnaXnmoTooajljZXmlbDmja4KICAgIC8vICAgY29uc3QgZGF0YSA9IHsKICAgIC8vICAgICBmaWVsZDEwMjogJzE4ODM2NjYyNTU1JwogICAgLy8gICB9CiAgICAvLyAgIC8vIOWbnuWhq+aVsOaNrgogICAgLy8gICB0aGlzLmZpbGxGb3JtRGF0YSh0aGlzLmZvcm1Db25mLCBkYXRhKQogICAgLy8gICAvLyDmm7TmlrDooajljZUKICAgIC8vICAgdGhpcy5rZXkgPSArbmV3IERhdGUoKS5nZXRUaW1lKCkKICAgIC8vIH0sIDEwMDApCiAgfSwKICBtZXRob2RzOiB7CiAgICAvKiog5p+l6K+i6YOo6Zeo5LiL5ouJ5qCR57uT5p6EICovZ2V0VHJlZXNlbGVjdDogZnVuY3Rpb24gZ2V0VHJlZXNlbGVjdCgpIHsKICAgICAgdmFyIF90aGlzID0gdGhpczsKICAgICAgdHJlZXNlbGVjdCgpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgX3RoaXMuZGVwdE9wdGlvbnMgPSByZXNwb25zZS5kYXRhOwogICAgICB9KTsKICAgIH0sCiAgICAvKiog5p+l6K+i55So5oi35YiX6KGoICovZ2V0TGlzdDogZnVuY3Rpb24gZ2V0TGlzdCgpIHsKICAgICAgdmFyIF90aGlzMiA9IHRoaXM7CiAgICAgIGxpc3RVc2VyKHRoaXMuYWRkRGF0ZVJhbmdlKHRoaXMucXVlcnlQYXJhbXMsIHRoaXMuZGF0ZVJhbmdlKSkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICBfdGhpczIudXNlckxpc3QgPSByZXNwb25zZS5yb3dzOwogICAgICAgIF90aGlzMi50b3RhbCA9IHJlc3BvbnNlLnRvdGFsOwogICAgICB9KTsKICAgIH0sCiAgICAvLyDnrZvpgInoioLngrkKICAgIGZpbHRlck5vZGU6IGZ1bmN0aW9uIGZpbHRlck5vZGUodmFsdWUsIGRhdGEpIHsKICAgICAgaWYgKCF2YWx1ZSkgcmV0dXJuIHRydWU7CiAgICAgIHJldHVybiBkYXRhLmxhYmVsLmluZGV4T2YodmFsdWUpICE9PSAtMTsKICAgIH0sCiAgICAvLyDoioLngrnljZXlh7vkuovku7YKICAgIGhhbmRsZU5vZGVDbGljazogZnVuY3Rpb24gaGFuZGxlTm9kZUNsaWNrKGRhdGEpIHsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5kZXB0SWQgPSBkYXRhLmlkOwogICAgICB0aGlzLmdldExpc3QoKTsKICAgIH0sCiAgICAvKiogeG1sIOaWh+S7tiAqL2dldE1vZGVsRGV0YWlsOiBmdW5jdGlvbiBnZXRNb2RlbERldGFpbChkZXBsb3lLZXkpIHsKICAgICAgdmFyIF90aGlzMyA9IHRoaXM7CiAgICAgIC8vIOWPkemAgeivt+axgu+8jOiOt+WPlnhtbAogICAgICByZWFkWG1sQnlLZXkoZGVwbG95S2V5KS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICBfdGhpczMueG1sRGF0YSA9IHJlcy5kYXRhOwogICAgICB9KTsKICAgIH0sCiAgICBnZXRGbG93Vmlld2VyOiBmdW5jdGlvbiBnZXRGbG93Vmlld2VyKHByb2NJbnNJZCwgZGVwbG95S2V5KSB7CiAgICAgIHZhciBfdGhpczQgPSB0aGlzOwogICAgICBfZ2V0Rmxvd1ZpZXdlcihwcm9jSW5zSWQpLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgIF90aGlzNC50YXNrTGlzdCA9IHJlcy5kYXRhOwogICAgICAgIF90aGlzNC5nZXRNb2RlbERldGFpbChkZXBsb3lLZXkpOwogICAgICB9KTsKICAgIH0sCiAgICBzZXRJY29uOiBmdW5jdGlvbiBzZXRJY29uKHZhbCkgewogICAgICBpZiAodmFsKSB7CiAgICAgICAgcmV0dXJuICJlbC1pY29uLWNoZWNrIjsKICAgICAgfSBlbHNlIHsKICAgICAgICByZXR1cm4gImVsLWljb24tdGltZSI7CiAgICAgIH0KICAgIH0sCiAgICBzZXRDb2xvcjogZnVuY3Rpb24gc2V0Q29sb3IodmFsKSB7CiAgICAgIGlmICh2YWwpIHsKICAgICAgICByZXR1cm4gIiMyYmM0MTgiOwogICAgICB9IGVsc2UgewogICAgICAgIHJldHVybiAiI2IzYmRiYiI7CiAgICAgIH0KICAgIH0sCiAgICAvLyDlpJrpgInmoYbpgInkuK3mlbDmja4KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZTogZnVuY3Rpb24gaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgewogICAgICB0aGlzLnVzZXJEYXRhID0gc2VsZWN0aW9uOwogICAgICB2YXIgdmFsID0gc2VsZWN0aW9uLm1hcChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgIHJldHVybiBpdGVtLnVzZXJJZDsKICAgICAgfSlbMF07CiAgICAgIGlmICh2YWwgaW5zdGFuY2VvZiBBcnJheSkgewogICAgICAgIHRoaXMudGFza0Zvcm0udmFsdWVzID0gewogICAgICAgICAgImFwcHJvdmFsIjogdmFsLmpvaW4oJywnKQogICAgICAgIH07CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy50YXNrRm9ybS52YWx1ZXMgPSB7CiAgICAgICAgICAiYXBwcm92YWwiOiB2YWwKICAgICAgICB9OwogICAgICB9CiAgICB9LAogICAgLy8g5YWz6Zet5qCH562+CiAgICBoYW5kbGVDbG9zZTogZnVuY3Rpb24gaGFuZGxlQ2xvc2UodGFnKSB7CiAgICAgIHRoaXMudXNlckRhdGEuc3BsaWNlKHRoaXMudXNlckRhdGEuaW5kZXhPZih0YWcpLCAxKTsKICAgIH0sCiAgICAvKiog5rWB56iL5Y+Y6YeP6LWL5YC8ICovaGFuZGxlQ2hlY2tDaGFuZ2U6IGZ1bmN0aW9uIGhhbmRsZUNoZWNrQ2hhbmdlKHZhbCkgewogICAgICBpZiAodmFsIGluc3RhbmNlb2YgQXJyYXkpIHsKICAgICAgICB0aGlzLnRhc2tGb3JtLnZhbHVlcyA9IHsKICAgICAgICAgICJhcHByb3ZhbCI6IHZhbC5qb2luKCcsJykKICAgICAgICB9OwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMudGFza0Zvcm0udmFsdWVzID0gewogICAgICAgICAgImFwcHJvdmFsIjogdmFsCiAgICAgICAgfTsKICAgICAgfQogICAgfSwKICAgIC8qKiDmtYHnqIvmtYHovazorrDlvZUgKi9nZXRGbG93UmVjb3JkTGlzdDogZnVuY3Rpb24gZ2V0Rmxvd1JlY29yZExpc3QocHJvY0luc0lkKSB7CiAgICAgIHZhciBfdGhpczUgPSB0aGlzOwogICAgICB2YXIgcGFyYW1zID0gewogICAgICAgIHByb2NJbnNJZDogcHJvY0luc0lkCiAgICAgIH07CiAgICAgIGZsb3dSZWNvcmQocGFyYW1zKS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICBfdGhpczUuZmxvd1JlY29yZExpc3QgPSByZXMuZGF0YS5mbG93TGlzdDsKICAgICAgICAvLyDmtYHnqIvov4fnqIvkuK3kuI3lrZjlnKjliJ3lp4vljJbooajljZUg55u05o6l6K+75Y+W55qE5rWB56iL5Y+Y6YeP5Lit5a2Y5YKo55qE6KGo5Y2V5YC8CiAgICAgICAgaWYgKHJlcy5kYXRhLmZvcm1EYXRhKSB7CiAgICAgICAgICBfdGhpczUuZm9ybUNvbmYgPSByZXMuZGF0YS5mb3JtRGF0YTsKICAgICAgICAgIF90aGlzNS5mb3JtQ29uZk9wZW4gPSB0cnVlOwogICAgICAgIH0KICAgICAgfSkuY2F0Y2goZnVuY3Rpb24gKHJlcykgewogICAgICAgIF90aGlzNS5nb0JhY2soKTsKICAgICAgfSk7CiAgICB9LAogICAgZmlsbEZvcm1EYXRhOiBmdW5jdGlvbiBmaWxsRm9ybURhdGEoZm9ybSwgZGF0YSkgewogICAgICBmb3JtLmZpZWxkcy5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgdmFyIHZhbCA9IGRhdGFbaXRlbS5fX3ZNb2RlbF9fXTsKICAgICAgICBpZiAodmFsKSB7CiAgICAgICAgICBpdGVtLl9fY29uZmlnX18uZGVmYXVsdFZhbHVlID0gdmFsOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLyoqIOiOt+WPlua1geeoi+WPmOmHj+WGheWuuSAqL3Byb2Nlc3NWYXJpYWJsZXM6IGZ1bmN0aW9uIHByb2Nlc3NWYXJpYWJsZXModGFza0lkKSB7CiAgICAgIHZhciBfdGhpczYgPSB0aGlzOwogICAgICBpZiAodGFza0lkKSB7CiAgICAgICAgLy8g5o+Q5Lqk5rWB56iL55Sz6K+35pe25aGr5YaZ55qE6KGo5Y2V5a2Y5YWl5LqG5rWB56iL5Y+Y6YeP5Lit5ZCO57ut5Lu75Yqh5aSE55CG5pe26ZyA6KaB5bGV56S6CiAgICAgICAgZ2V0UHJvY2Vzc1ZhcmlhYmxlcyh0YXNrSWQpLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgICAgLy8gdGhpcy52YXJpYWJsZXMgPSByZXMuZGF0YS52YXJpYWJsZXM7CiAgICAgICAgICBfdGhpczYudmFyaWFibGVzRGF0YSA9IHJlcy5kYXRhLnZhcmlhYmxlczsKICAgICAgICAgIF90aGlzNi52YXJpYWJsZU9wZW4gPSB0cnVlOwogICAgICAgIH0pOwogICAgICB9CiAgICB9LAogICAgLyoqIOagueaNruW9k+WJjeS7u+WKoeaIluiAhea1geeoi+iuvuiuoemFjee9rueahOS4i+S4gOatpeiKgueCuSAqL2dldE5leHRGbG93Tm9kZTogZnVuY3Rpb24gZ2V0TmV4dEZsb3dOb2RlKHRhc2tJZCkgewogICAgICB2YXIgX3RoaXM3ID0gdGhpczsKICAgICAgLy8g5qC55o2u5b2T5YmN5Lu75Yqh5oiW6ICF5rWB56iL6K6+6K6h6YWN572u55qE5LiL5LiA5q2l6IqC54K5IHRvZG8g5pqC5pe25pyq5raJ5Y+K5Yiw6ICD6JmR572R5YWz44CB6KGo6L6+5byP5ZKM5aSa6IqC54K55oOF5Ya1CiAgICAgIHZhciBwYXJhbXMgPSB7CiAgICAgICAgdGFza0lkOiB0YXNrSWQKICAgICAgfTsKICAgICAgX2dldE5leHRGbG93Tm9kZShwYXJhbXMpLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgIHZhciBkYXRhID0gcmVzLmRhdGE7CiAgICAgICAgaWYgKGRhdGEpIHsKICAgICAgICAgIGlmIChkYXRhLnR5cGUgPT09ICdhc3NpZ25lZScpIHsKICAgICAgICAgICAgX3RoaXM3LnVzZXJEYXRhTGlzdCA9IHJlcy5kYXRhLnVzZXJMaXN0OwogICAgICAgICAgfSBlbHNlIGlmIChkYXRhLnR5cGUgPT09ICdjYW5kaWRhdGVVc2VycycpIHsKICAgICAgICAgICAgX3RoaXM3LnVzZXJEYXRhTGlzdCA9IHJlcy5kYXRhLnVzZXJMaXN0OwogICAgICAgICAgICBfdGhpczcudGFza0Zvcm0ubXVsdGlwbGUgPSB0cnVlOwogICAgICAgICAgfSBlbHNlIGlmIChkYXRhLnR5cGUgPT09ICdjYW5kaWRhdGVHcm91cHMnKSB7CiAgICAgICAgICAgIHJlcy5kYXRhLnJvbGVMaXN0LmZvckVhY2goZnVuY3Rpb24gKHJvbGUpIHsKICAgICAgICAgICAgICByb2xlLnVzZXJJZCA9IHJvbGUucm9sZUlkOwogICAgICAgICAgICAgIHJvbGUubmlja05hbWUgPSByb2xlLnJvbGVOYW1lOwogICAgICAgICAgICB9KTsKICAgICAgICAgICAgX3RoaXM3LnVzZXJEYXRhTGlzdCA9IHJlcy5kYXRhLnJvbGVMaXN0OwogICAgICAgICAgICBfdGhpczcudGFza0Zvcm0ubXVsdGlwbGUgPSBmYWxzZTsKICAgICAgICAgIH0gZWxzZSBpZiAoZGF0YS50eXBlID09PSAnbXVsdGlJbnN0YW5jZScpIHsKICAgICAgICAgICAgX3RoaXM3LnVzZXJEYXRhTGlzdCA9IHJlcy5kYXRhLnVzZXJMaXN0OwogICAgICAgICAgICBfdGhpczcudGFza0Zvcm0ubXVsdGlwbGUgPSB0cnVlOwogICAgICAgICAgfQogICAgICAgICAgX3RoaXM3LnRhc2tGb3JtLnNlbmRVc2VyU2hvdyA9IHRydWU7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvKiog5a6h5om55Lu75Yqh6YCJ5oupICovaGFuZGxlQ29tcGxldGU6IGZ1bmN0aW9uIGhhbmRsZUNvbXBsZXRlKCkgewogICAgICB0aGlzLmNvbXBsZXRlT3BlbiA9IHRydWU7CiAgICAgIHRoaXMuY29tcGxldGVUaXRsZSA9ICLlrqHmibnmtYHnqIsiOwogICAgICAvL3RoaXMuZ2V0VHJlZXNlbGVjdCgpOwogICAgfSwKICAgIC8qKiDlrqHmibnku7vliqEgKi90YXNrQ29tcGxldGU6IGZ1bmN0aW9uIHRhc2tDb21wbGV0ZShjb21tZW50KSB7CiAgICAgIHZhciBfdGhpczggPSB0aGlzOwogICAgICAvLyBpZiAoIXRoaXMudGFza0Zvcm0udmFsdWVzKXsKICAgICAgLy8gICB0aGlzLm1zZ0Vycm9yKCLor7fpgInmi6nmtYHnqIvmjqXmlLbkurrlkZgiKTsKICAgICAgLy8gICByZXR1cm47CiAgICAgIC8vIH0KICAgICAgaWYgKGNvbW1lbnQgJiYgdHlwZW9mIGNvbW1lbnQgPT0gJ3N0cmluZycgJiYgY29tbWVudC5jb25zdHJ1Y3RvciA9PSBTdHJpbmcpIHsKICAgICAgICB0aGlzLnRhc2tGb3JtLmNvbW1lbnQgPSBjb21tZW50OwogICAgICB9CiAgICAgIGlmICghdGhpcy50YXNrRm9ybS5jb21tZW50KSB7CiAgICAgICAgdGhpcy5tc2dFcnJvcigi6K+36L6T5YWl5a6h5om55oSP6KeBIik7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIGNvbXBsZXRlKHRoaXMudGFza0Zvcm0pLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgX3RoaXM4Lm1zZ1N1Y2Nlc3MocmVzcG9uc2UubXNnKTsKICAgICAgICBfdGhpczguZ29CYWNrKCk7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDlp5TmtL7ku7vliqEgKi9oYW5kbGVEZWxlZ2F0ZTogZnVuY3Rpb24gaGFuZGxlRGVsZWdhdGUoKSB7CiAgICAgIHRoaXMudGFza0Zvcm0uZGVsZWdhdGVUYXNrU2hvdyA9IHRydWU7CiAgICAgIHRoaXMudGFza0Zvcm0uZGVmYXVsdFRhc2tTaG93ID0gZmFsc2U7CiAgICB9LAogICAgaGFuZGxlQXNzaWduOiBmdW5jdGlvbiBoYW5kbGVBc3NpZ24oKSB7fSwKICAgIC8qKiDov5Tlm57pobXpnaIgKi9nb0JhY2s6IGZ1bmN0aW9uIGdvQmFjaygpIHsKICAgICAgLy8g5YWz6Zet5b2T5YmN5qCH562+6aG15bm26L+U5Zue5LiK5Liq6aG16Z2iCiAgICAgIHRoaXMuJHN0b3JlLmRpc3BhdGNoKCJ0YWdzVmlldy9kZWxWaWV3IiwgdGhpcy4kcm91dGUpOwogICAgICB0aGlzLiRyb3V0ZXIuZ28oLTEpOwogICAgfSwKICAgIC8qKiDmjqXmlLblrZDnu4Tku7bkvKDnmoTlgLwgKi9nZXREYXRhOiBmdW5jdGlvbiBnZXREYXRhKGRhdGEpIHsKICAgICAgaWYgKGRhdGEpIHsKICAgICAgICB2YXIgdmFyaWFibGVzID0gW107CiAgICAgICAgZGF0YS5maWVsZHMuZm9yRWFjaChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgdmFyIHZhcmlhYmxlRGF0YSA9IHt9OwogICAgICAgICAgdmFyaWFibGVEYXRhLmxhYmVsID0gaXRlbS5fX2NvbmZpZ19fLmxhYmVsOwogICAgICAgICAgLy8g6KGo5Y2V5YC85Li65aSa5Liq6YCJ6aG55pe2CiAgICAgICAgICBpZiAoaXRlbS5fX2NvbmZpZ19fLmRlZmF1bHRWYWx1ZSBpbnN0YW5jZW9mIEFycmF5KSB7CiAgICAgICAgICAgIHZhciBhcnJheSA9IFtdOwogICAgICAgICAgICBpdGVtLl9fY29uZmlnX18uZGVmYXVsdFZhbHVlLmZvckVhY2goZnVuY3Rpb24gKHZhbCkgewogICAgICAgICAgICAgIGFycmF5LnB1c2godmFsKTsKICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIHZhcmlhYmxlRGF0YS52YWwgPSBhcnJheTsKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIHZhcmlhYmxlRGF0YS52YWwgPSBpdGVtLl9fY29uZmlnX18uZGVmYXVsdFZhbHVlOwogICAgICAgICAgfQogICAgICAgICAgdmFyaWFibGVzLnB1c2godmFyaWFibGVEYXRhKTsKICAgICAgICB9KTsKICAgICAgICB0aGlzLnZhcmlhYmxlcyA9IHZhcmlhYmxlczsKICAgICAgfQogICAgfSwKICAgIC8qKiDnlLPor7fmtYHnqIvooajljZXmlbDmja7mj5DkuqQtLS0t5rKh55SoICovCiAgICAvLyBzdWJtaXRGb3JtKGRhdGEpIHsKICAgIC8vICAgaWYgKGRhdGEpIHsKICAgIC8vICAgICBjb25zdCB2YXJpYWJsZXMgPSBkYXRhLnZhbERhdGE7CiAgICAvLyAgICAgY29uc3QgZm9ybURhdGEgPSBkYXRhLmZvcm1EYXRhOwogICAgLy8gICAgIGZvcm1EYXRhLmRpc2FibGVkID0gdHJ1ZTsKICAgIC8vICAgICBmb3JtRGF0YS5mb3JtQnRucyA9IGZhbHNlOwogICAgLy8gICAgIGlmICh0aGlzLnRhc2tGb3JtLnByb2NEZWZLZXkpIHsKICAgIC8vICAgICAgIHZhcmlhYmxlcy52YXJpYWJsZXMgPSBmb3JtRGF0YTsKICAgIC8vICAgICAgIHZhcmlhYmxlcy5idXNpbmVzc0tleSA9IGRhdGEuYnVzaW5lc3NLZXk7ICAgICAgICAgIAogICAgLy8gICAgICAgIC8vIOWQr+WKqOa1geeoi+W5tuWwhuihqOWNleaVsOaNruWKoOWFpea1geeoi+WPmOmHjwogICAgLy8gICAgICAgZGVmaW5pdGlvblN0YXJ0QnlLZXkodGhpcy50YXNrRm9ybS5wcm9jRGVmS2V5LCBKU09OLnN0cmluZ2lmeSh2YXJpYWJsZXMpKS50aGVuKHJlcyA9PiB7CiAgICAvLyAgICAgICAgIHRoaXMubXNnU3VjY2VzcyhyZXMubXNnKTsKICAgIC8vICAgICAgICAgdGhpcy5nb0JhY2soKTsKICAgIC8vICAgICAgIH0pCiAgICAvLyAgICAgfQogICAgLy8gICB9CiAgICAvLyB9LAogICAgc3RhcnRGbG93OiBmdW5jdGlvbiBzdGFydEZsb3coYnVzaW5lc3NLZXksIG5hbWUsIHZhcmlhYmxlcykgewogICAgICB2YXIgX3RoaXM5ID0gdGhpczsKICAgICAgdmFyIHN0YXJ0RGF0ZSA9IG1vbWVudChuZXcgRGF0ZSgpKS5mb3JtYXQoJ1lZWVlNTURESEhtbXNzJyk7CiAgICAgIHZhciBkYXRhID0ge307CiAgICAgIGlmICh0aGlzLnRhc2tGb3JtLnByb2NEZWZLZXkpIHsKICAgICAgICBpZiAoIXZhcmlhYmxlcykgewogICAgICAgICAgZGF0YS52YXJpYWJsZXMgPSB7fTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgZGF0YS52YXJpYWJsZXMgPSB2YXJpYWJsZXM7CiAgICAgICAgfQogICAgICAgIGRhdGEuYnVzaW5lc3NLZXkgPSBidXNpbmVzc0tleTsKICAgICAgICBkYXRhLnByb2NEZWZLZXkgPSB0aGlzLnRhc2tGb3JtLnByb2NEZWZLZXk7CiAgICAgICAgZGF0YS50YXNrTmFtZSA9IG5hbWU7CiAgICAgICAgLy8g5ZCv5Yqo5rWB56iL5bm25bCG6KGo5Y2V5pWw5o2u5Yqg5YWl5rWB56iL5Y+Y6YePCiAgICAgICAgZGVmaW5pdGlvblN0YXJ0QnlLZXkoSlNPTi5zdHJpbmdpZnkoZGF0YSkpLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgICAgX3RoaXM5Lm1zZ1N1Y2Nlc3MocmVzLm1zZyk7CiAgICAgICAgICBfdGhpczkuZ29CYWNrKCk7CiAgICAgICAgfSk7CiAgICAgIH0KICAgIH0sCiAgICAvKiog6amz5Zue5Lu75YqhICovaGFuZGxlUmVqZWN0OiBmdW5jdGlvbiBoYW5kbGVSZWplY3QoKSB7CiAgICAgIHRoaXMucmVqZWN0T3BlbiA9IHRydWU7CiAgICAgIHRoaXMucmVqZWN0VGl0bGUgPSAi6amz5Zue5rWB56iLIjsKICAgIH0sCiAgICAvKiog6amz5Zue5Lu75YqhICovdGFza1JlamVjdDogZnVuY3Rpb24gdGFza1JlamVjdCgpIHsKICAgICAgdmFyIF90aGlzMTAgPSB0aGlzOwogICAgICB0aGlzLiRyZWZzWyJ0YXNrRm9ybSJdLnZhbGlkYXRlKGZ1bmN0aW9uICh2YWxpZCkgewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgcmVqZWN0VGFzayhfdGhpczEwLnRhc2tGb3JtKS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICAgICAgX3RoaXMxMC5tc2dTdWNjZXNzKHJlcy5tc2cpOwogICAgICAgICAgICBfdGhpczEwLmdvQmFjaygpOwogICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvKiog5Y+v6YCA5Zue5Lu75Yqh5YiX6KGoICovaGFuZGxlUmV0dXJuOiBmdW5jdGlvbiBoYW5kbGVSZXR1cm4oKSB7CiAgICAgIHZhciBfdGhpczExID0gdGhpczsKICAgICAgdGhpcy5yZXR1cm5PcGVuID0gdHJ1ZTsKICAgICAgdGhpcy5yZXR1cm5UaXRsZSA9ICLpgIDlm57mtYHnqIsiOwogICAgICByZXR1cm5MaXN0KHRoaXMudGFza0Zvcm0pLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgIF90aGlzMTEucmV0dXJuVGFza0xpc3QgPSByZXMuZGF0YTsKICAgICAgICBfdGhpczExLnRhc2tGb3JtLnZhbHVlcyA9IG51bGw7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDmj5DkuqTpgIDlm57ku7vliqEgKi90YXNrUmV0dXJuOiBmdW5jdGlvbiB0YXNrUmV0dXJuKCkgewogICAgICB2YXIgX3RoaXMxMiA9IHRoaXM7CiAgICAgIHRoaXMuJHJlZnNbInRhc2tGb3JtIl0udmFsaWRhdGUoZnVuY3Rpb24gKHZhbGlkKSB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICByZXR1cm5UYXNrKF90aGlzMTIudGFza0Zvcm0pLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgICAgICBfdGhpczEyLm1zZ1N1Y2Nlc3MocmVzLm1zZyk7CiAgICAgICAgICAgIF90aGlzMTIuZ29CYWNrKCk7CiAgICAgICAgICB9KTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDlj5bmtojlm57pgIDku7vliqHmjInpkq4gKi9jYW5jZWxUYXNrOiBmdW5jdGlvbiBjYW5jZWxUYXNrKCkgewogICAgICB0aGlzLnRhc2tGb3JtLnJldHVyblRhc2tTaG93ID0gZmFsc2U7CiAgICAgIHRoaXMudGFza0Zvcm0uZGVmYXVsdFRhc2tTaG93ID0gdHJ1ZTsKICAgICAgdGhpcy50YXNrRm9ybS5zZW5kVXNlclNob3cgPSB0cnVlOwogICAgICB0aGlzLnJldHVyblRhc2tMaXN0ID0gW107CiAgICB9LAogICAgLyoqIOWnlOa0vuS7u+WKoSAqL3N1Ym1pdERlbGV0ZVRhc2s6IGZ1bmN0aW9uIHN1Ym1pdERlbGV0ZVRhc2soKSB7CiAgICAgIHZhciBfdGhpczEzID0gdGhpczsKICAgICAgdGhpcy4kcmVmc1sidGFza0Zvcm0iXS52YWxpZGF0ZShmdW5jdGlvbiAodmFsaWQpIHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIGRlbGVnYXRlKF90aGlzMTMudGFza0Zvcm0pLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgICAgIF90aGlzMTMubXNnU3VjY2VzcyhyZXNwb25zZS5tc2cpOwogICAgICAgICAgICBfdGhpczEzLmdvQmFjaygpOwogICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvKiog5Y+W5raI5Zue6YCA5Lu75Yqh5oyJ6ZKuICovY2FuY2VsRGVsZWdhdGVUYXNrOiBmdW5jdGlvbiBjYW5jZWxEZWxlZ2F0ZVRhc2soKSB7CiAgICAgIHRoaXMudGFza0Zvcm0uZGVsZWdhdGVUYXNrU2hvdyA9IGZhbHNlOwogICAgICB0aGlzLnRhc2tGb3JtLmRlZmF1bHRUYXNrU2hvdyA9IHRydWU7CiAgICAgIHRoaXMudGFza0Zvcm0uc2VuZFVzZXJTaG93ID0gdHJ1ZTsKICAgICAgdGhpcy5yZXR1cm5UYXNrTGlzdCA9IFtdOwogICAgfQogIH0KfTs="}, null]}