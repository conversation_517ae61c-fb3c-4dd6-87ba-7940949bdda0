{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/dashboard/RaddarChart.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/dashboard/RaddarChart.vue", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KCmltcG9ydCBlY2hhcnRzIGZyb20gJ2VjaGFydHMnOwpyZXF1aXJlKCdlY2hhcnRzL3RoZW1lL21hY2Fyb25zJyk7IC8vIGVjaGFydHMgdGhlbWUKaW1wb3J0IHJlc2l6ZSBmcm9tICcuL21peGlucy9yZXNpemUnOwp2YXIgYW5pbWF0aW9uRHVyYXRpb24gPSAzMDAwOwpleHBvcnQgZGVmYXVsdCB7CiAgbWl4aW5zOiBbcmVzaXplXSwKICBwcm9wczogewogICAgY2xhc3NOYW1lOiB7CiAgICAgIHR5cGU6IFN0cmluZywKICAgICAgZGVmYXVsdDogJ2NoYXJ0JwogICAgfSwKICAgIHdpZHRoOiB7CiAgICAgIHR5cGU6IFN0cmluZywKICAgICAgZGVmYXVsdDogJzEwMCUnCiAgICB9LAogICAgaGVpZ2h0OiB7CiAgICAgIHR5cGU6IFN0cmluZywKICAgICAgZGVmYXVsdDogJzMwMHB4JwogICAgfQogIH0sCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGNoYXJ0OiBudWxsCiAgICB9OwogIH0sCiAgbW91bnRlZDogZnVuY3Rpb24gbW91bnRlZCgpIHsKICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICB0aGlzLiRuZXh0VGljayhmdW5jdGlvbiAoKSB7CiAgICAgIF90aGlzLmluaXRDaGFydCgpOwogICAgfSk7CiAgfSwKICBiZWZvcmVEZXN0cm95OiBmdW5jdGlvbiBiZWZvcmVEZXN0cm95KCkgewogICAgaWYgKCF0aGlzLmNoYXJ0KSB7CiAgICAgIHJldHVybjsKICAgIH0KICAgIHRoaXMuY2hhcnQuZGlzcG9zZSgpOwogICAgdGhpcy5jaGFydCA9IG51bGw7CiAgfSwKICBtZXRob2RzOiB7CiAgICBpbml0Q2hhcnQ6IGZ1bmN0aW9uIGluaXRDaGFydCgpIHsKICAgICAgdGhpcy5jaGFydCA9IGVjaGFydHMuaW5pdCh0aGlzLiRlbCwgJ21hY2Fyb25zJyk7CiAgICAgIHRoaXMuY2hhcnQuc2V0T3B0aW9uKHsKICAgICAgICB0b29sdGlwOiB7CiAgICAgICAgICB0cmlnZ2VyOiAnYXhpcycsCiAgICAgICAgICBheGlzUG9pbnRlcjogewogICAgICAgICAgICAvLyDlnZDmoIfovbTmjIfnpLrlmajvvIzlnZDmoIfovbTop6blj5HmnInmlYgKICAgICAgICAgICAgdHlwZTogJ3NoYWRvdycgLy8g6buY6K6k5Li655u057q/77yM5Y+v6YCJ5Li677yaJ2xpbmUnIHwgJ3NoYWRvdycKICAgICAgICAgIH0KICAgICAgICB9LAogICAgICAgIHJhZGFyOiB7CiAgICAgICAgICByYWRpdXM6ICc2NiUnLAogICAgICAgICAgY2VudGVyOiBbJzUwJScsICc0MiUnXSwKICAgICAgICAgIHNwbGl0TnVtYmVyOiA4LAogICAgICAgICAgc3BsaXRBcmVhOiB7CiAgICAgICAgICAgIGFyZWFTdHlsZTogewogICAgICAgICAgICAgIGNvbG9yOiAncmdiYSgxMjcsOTUsMTMyLC4zKScsCiAgICAgICAgICAgICAgb3BhY2l0eTogMSwKICAgICAgICAgICAgICBzaGFkb3dCbHVyOiA0NSwKICAgICAgICAgICAgICBzaGFkb3dDb2xvcjogJ3JnYmEoMCwwLDAsLjUpJywKICAgICAgICAgICAgICBzaGFkb3dPZmZzZXRYOiAwLAogICAgICAgICAgICAgIHNoYWRvd09mZnNldFk6IDE1CiAgICAgICAgICAgIH0KICAgICAgICAgIH0sCiAgICAgICAgICBpbmRpY2F0b3I6IFt7CiAgICAgICAgICAgIG5hbWU6ICdTYWxlcycsCiAgICAgICAgICAgIG1heDogMTAwMDAKICAgICAgICAgIH0sIHsKICAgICAgICAgICAgbmFtZTogJ0FkbWluaXN0cmF0aW9uJywKICAgICAgICAgICAgbWF4OiAyMDAwMAogICAgICAgICAgfSwgewogICAgICAgICAgICBuYW1lOiAnSW5mb3JtYXRpb24gVGVjaG9sb2d5JywKICAgICAgICAgICAgbWF4OiAyMDAwMAogICAgICAgICAgfSwgewogICAgICAgICAgICBuYW1lOiAnQ3VzdG9tZXIgU3VwcG9ydCcsCiAgICAgICAgICAgIG1heDogMjAwMDAKICAgICAgICAgIH0sIHsKICAgICAgICAgICAgbmFtZTogJ0RldmVsb3BtZW50JywKICAgICAgICAgICAgbWF4OiAyMDAwMAogICAgICAgICAgfSwgewogICAgICAgICAgICBuYW1lOiAnTWFya2V0aW5nJywKICAgICAgICAgICAgbWF4OiAyMDAwMAogICAgICAgICAgfV0KICAgICAgICB9LAogICAgICAgIGxlZ2VuZDogewogICAgICAgICAgbGVmdDogJ2NlbnRlcicsCiAgICAgICAgICBib3R0b206ICcxMCcsCiAgICAgICAgICBkYXRhOiBbJ0FsbG9jYXRlZCBCdWRnZXQnLCAnRXhwZWN0ZWQgU3BlbmRpbmcnLCAnQWN0dWFsIFNwZW5kaW5nJ10KICAgICAgICB9LAogICAgICAgIHNlcmllczogW3sKICAgICAgICAgIHR5cGU6ICdyYWRhcicsCiAgICAgICAgICBzeW1ib2xTaXplOiAwLAogICAgICAgICAgYXJlYVN0eWxlOiB7CiAgICAgICAgICAgIG5vcm1hbDogewogICAgICAgICAgICAgIHNoYWRvd0JsdXI6IDEzLAogICAgICAgICAgICAgIHNoYWRvd0NvbG9yOiAncmdiYSgwLDAsMCwuMiknLAogICAgICAgICAgICAgIHNoYWRvd09mZnNldFg6IDAsCiAgICAgICAgICAgICAgc2hhZG93T2Zmc2V0WTogMTAsCiAgICAgICAgICAgICAgb3BhY2l0eTogMQogICAgICAgICAgICB9CiAgICAgICAgICB9LAogICAgICAgICAgZGF0YTogW3sKICAgICAgICAgICAgdmFsdWU6IFs1MDAwLCA3MDAwLCAxMjAwMCwgMTEwMDAsIDE1MDAwLCAxNDAwMF0sCiAgICAgICAgICAgIG5hbWU6ICdBbGxvY2F0ZWQgQnVkZ2V0JwogICAgICAgICAgfSwgewogICAgICAgICAgICB2YWx1ZTogWzQwMDAsIDkwMDAsIDE1MDAwLCAxNTAwMCwgMTMwMDAsIDExMDAwXSwKICAgICAgICAgICAgbmFtZTogJ0V4cGVjdGVkIFNwZW5kaW5nJwogICAgICAgICAgfSwgewogICAgICAgICAgICB2YWx1ZTogWzU1MDAsIDExMDAwLCAxMjAwMCwgMTUwMDAsIDEyMDAwLCAxMjAwMF0sCiAgICAgICAgICAgIG5hbWU6ICdBY3R1YWwgU3BlbmRpbmcnCiAgICAgICAgICB9XSwKICAgICAgICAgIGFuaW1hdGlvbkR1cmF0aW9uOiBhbmltYXRpb25EdXJhdGlvbgogICAgICAgIH1dCiAgICAgIH0pOwogICAgfQogIH0KfTs="}, {"version": 3, "names": ["echarts", "require", "resize", "animationDuration", "mixins", "props", "className", "type", "String", "default", "width", "height", "data", "chart", "mounted", "_this", "$nextTick", "initChart", "<PERSON><PERSON><PERSON><PERSON>", "dispose", "methods", "init", "$el", "setOption", "tooltip", "trigger", "axisPointer", "radar", "radius", "center", "splitNumber", "splitArea", "areaStyle", "color", "opacity", "<PERSON><PERSON><PERSON><PERSON>", "shadowColor", "shadowOffsetX", "shadowOffsetY", "indicator", "name", "max", "legend", "left", "bottom", "series", "symbolSize", "normal", "value"], "sources": ["src/views/dashboard/RaddarChart.vue"], "sourcesContent": ["<template>\n  <div :class=\"className\" :style=\"{height:height,width:width}\" />\n</template>\n\n<script>\nimport echarts from 'echarts'\nrequire('echarts/theme/macarons') // echarts theme\nimport resize from './mixins/resize'\n\nconst animationDuration = 3000\n\nexport default {\n  mixins: [resize],\n  props: {\n    className: {\n      type: String,\n      default: 'chart'\n    },\n    width: {\n      type: String,\n      default: '100%'\n    },\n    height: {\n      type: String,\n      default: '300px'\n    }\n  },\n  data() {\n    return {\n      chart: null\n    }\n  },\n  mounted() {\n    this.$nextTick(() => {\n      this.initChart()\n    })\n  },\n  beforeDestroy() {\n    if (!this.chart) {\n      return\n    }\n    this.chart.dispose()\n    this.chart = null\n  },\n  methods: {\n    initChart() {\n      this.chart = echarts.init(this.$el, 'macarons')\n\n      this.chart.setOption({\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: { // 坐标轴指示器，坐标轴触发有效\n            type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'\n          }\n        },\n        radar: {\n          radius: '66%',\n          center: ['50%', '42%'],\n          splitNumber: 8,\n          splitArea: {\n            areaStyle: {\n              color: 'rgba(127,95,132,.3)',\n              opacity: 1,\n              shadowBlur: 45,\n              shadowColor: 'rgba(0,0,0,.5)',\n              shadowOffsetX: 0,\n              shadowOffsetY: 15\n            }\n          },\n          indicator: [\n            { name: 'Sales', max: 10000 },\n            { name: 'Administration', max: 20000 },\n            { name: 'Information Techology', max: 20000 },\n            { name: 'Customer Support', max: 20000 },\n            { name: 'Development', max: 20000 },\n            { name: 'Marketing', max: 20000 }\n          ]\n        },\n        legend: {\n          left: 'center',\n          bottom: '10',\n          data: ['Allocated Budget', 'Expected Spending', 'Actual Spending']\n        },\n        series: [{\n          type: 'radar',\n          symbolSize: 0,\n          areaStyle: {\n            normal: {\n              shadowBlur: 13,\n              shadowColor: 'rgba(0,0,0,.2)',\n              shadowOffsetX: 0,\n              shadowOffsetY: 10,\n              opacity: 1\n            }\n          },\n          data: [\n            {\n              value: [5000, 7000, 12000, 11000, 15000, 14000],\n              name: 'Allocated Budget'\n            },\n            {\n              value: [4000, 9000, 15000, 15000, 13000, 11000],\n              name: 'Expected Spending'\n            },\n            {\n              value: [5500, 11000, 12000, 15000, 12000, 12000],\n              name: 'Actual Spending'\n            }\n          ],\n          animationDuration: animationDuration\n        }]\n      })\n    }\n  }\n}\n</script>\n"], "mappings": ";;;;;AAKA,OAAAA,OAAA;AACAC,OAAA;AACA,OAAAC,MAAA;AAEA,IAAAC,iBAAA;AAEA;EACAC,MAAA,GAAAF,MAAA;EACAG,KAAA;IACAC,SAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAC,KAAA;MACAH,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAE,MAAA;MACAJ,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;EACA;EACAG,IAAA,WAAAA,KAAA;IACA;MACAC,KAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA,KAAAC,SAAA;MACAD,KAAA,CAAAE,SAAA;IACA;EACA;EACAC,aAAA,WAAAA,cAAA;IACA,UAAAL,KAAA;MACA;IACA;IACA,KAAAA,KAAA,CAAAM,OAAA;IACA,KAAAN,KAAA;EACA;EACAO,OAAA;IACAH,SAAA,WAAAA,UAAA;MACA,KAAAJ,KAAA,GAAAb,OAAA,CAAAqB,IAAA,MAAAC,GAAA;MAEA,KAAAT,KAAA,CAAAU,SAAA;QACAC,OAAA;UACAC,OAAA;UACAC,WAAA;YAAA;YACAnB,IAAA;UACA;QACA;QACAoB,KAAA;UACAC,MAAA;UACAC,MAAA;UACAC,WAAA;UACAC,SAAA;YACAC,SAAA;cACAC,KAAA;cACAC,OAAA;cACAC,UAAA;cACAC,WAAA;cACAC,aAAA;cACAC,aAAA;YACA;UACA;UACAC,SAAA,GACA;YAAAC,IAAA;YAAAC,GAAA;UAAA,GACA;YAAAD,IAAA;YAAAC,GAAA;UAAA,GACA;YAAAD,IAAA;YAAAC,GAAA;UAAA,GACA;YAAAD,IAAA;YAAAC,GAAA;UAAA,GACA;YAAAD,IAAA;YAAAC,GAAA;UAAA,GACA;YAAAD,IAAA;YAAAC,GAAA;UAAA;QAEA;QACAC,MAAA;UACAC,IAAA;UACAC,MAAA;UACAhC,IAAA;QACA;QACAiC,MAAA;UACAtC,IAAA;UACAuC,UAAA;UACAd,SAAA;YACAe,MAAA;cACAZ,UAAA;cACAC,WAAA;cACAC,aAAA;cACAC,aAAA;cACAJ,OAAA;YACA;UACA;UACAtB,IAAA,GACA;YACAoC,KAAA;YACAR,IAAA;UACA,GACA;YACAQ,KAAA;YACAR,IAAA;UACA,GACA;YACAQ,KAAA;YACAR,IAAA;UACA,EACA;UACArC,iBAAA,EAAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}