{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/tool/build/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/tool/build/index.vue", "mtime": 1641277326000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js", "mtime": 1751171659043}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}