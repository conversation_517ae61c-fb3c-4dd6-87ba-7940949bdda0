{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/permission.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/permission.js", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["router", "store", "Message", "NProgress", "getToken", "configure", "showSpinner", "whiteList", "beforeEach", "to", "from", "next", "start", "path", "done", "getters", "roles", "length", "dispatch", "then", "res", "accessRoutes", "addRoutes", "_objectSpread", "replace", "catch", "err", "error", "indexOf", "concat", "fullPath", "after<PERSON>ach"], "sources": ["/Users/<USER>/develop/code/project/haijia/adminweb/src/permission.js"], "sourcesContent": ["import router from './router'\nimport store from './store'\nimport { Message } from 'element-ui'\nimport NProgress from 'nprogress'\nimport 'nprogress/nprogress.css'\nimport { getToken } from '@/utils/auth'\n\nNProgress.configure({ showSpinner: false })\n\nconst whiteList = ['/login', '/auth-redirect', '/bind', '/register']\n\nrouter.beforeEach((to, from, next) => {\n  NProgress.start()\n  if (getToken()) {\n    /* has token*/\n    if (to.path === '/login') {\n      next({ path: '/' })\n      NProgress.done()\n    } else {\n      if (store.getters.roles.length === 0) {\n        // 判断当前用户是否已拉取完user_info信息\n        store.dispatch('GetInfo').then(res => {\n          // 拉取user_info\n          const roles = res.roles\n          store.dispatch('GenerateRoutes', { roles }).then(accessRoutes => {\n            // 根据roles权限生成可访问的路由表\n            router.addRoutes(accessRoutes) // 动态添加可访问路由表\n            next({ ...to, replace: true }) // hack方法 确保addRoutes已完成\n          })\n        }).catch(err => {\n            store.dispatch('LogOut').then(() => {\n              Message.error(err)\n              next({ path: '/' })\n            })\n          })\n      } else {\n        next()\n      }\n    }\n  } else {\n    // 没有token\n    if (whiteList.indexOf(to.path) !== -1) {\n      // 在免登录白名单，直接进入\n      next()\n    } else {\n      next(`/login?redirect=${to.fullPath}`) // 否则全部重定向到登录页\n      NProgress.done()\n    }\n  }\n})\n\nrouter.afterEach(() => {\n  NProgress.done()\n})\n"], "mappings": ";;;;;;AAAA,OAAOA,MAAM,MAAM,UAAU;AAC7B,OAAOC,KAAK,MAAM,SAAS;AAC3B,SAASC,OAAO,QAAQ,YAAY;AACpC,OAAOC,SAAS,MAAM,WAAW;AACjC,OAAO,yBAAyB;AAChC,SAASC,QAAQ,QAAQ,cAAc;AAEvCD,SAAS,CAACE,SAAS,CAAC;EAAEC,WAAW,EAAE;AAAM,CAAC,CAAC;AAE3C,IAAMC,SAAS,GAAG,CAAC,QAAQ,EAAE,gBAAgB,EAAE,OAAO,EAAE,WAAW,CAAC;AAEpEP,MAAM,CAACQ,UAAU,CAAC,UAACC,EAAE,EAAEC,IAAI,EAAEC,IAAI,EAAK;EACpCR,SAAS,CAACS,KAAK,CAAC,CAAC;EACjB,IAAIR,QAAQ,CAAC,CAAC,EAAE;IACd;IACA,IAAIK,EAAE,CAACI,IAAI,KAAK,QAAQ,EAAE;MACxBF,IAAI,CAAC;QAAEE,IAAI,EAAE;MAAI,CAAC,CAAC;MACnBV,SAAS,CAACW,IAAI,CAAC,CAAC;IAClB,CAAC,MAAM;MACL,IAAIb,KAAK,CAACc,OAAO,CAACC,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE;QACpC;QACAhB,KAAK,CAACiB,QAAQ,CAAC,SAAS,CAAC,CAACC,IAAI,CAAC,UAAAC,GAAG,EAAI;UACpC;UACA,IAAMJ,KAAK,GAAGI,GAAG,CAACJ,KAAK;UACvBf,KAAK,CAACiB,QAAQ,CAAC,gBAAgB,EAAE;YAAEF,KAAK,EAALA;UAAM,CAAC,CAAC,CAACG,IAAI,CAAC,UAAAE,YAAY,EAAI;YAC/D;YACArB,MAAM,CAACsB,SAAS,CAACD,YAAY,CAAC,EAAC;YAC/BV,IAAI,CAAAY,aAAA,CAAAA,aAAA,KAAMd,EAAE;cAAEe,OAAO,EAAE;YAAI,EAAE,CAAC,EAAC;UACjC,CAAC,CAAC;QACJ,CAAC,CAAC,CAACC,KAAK,CAAC,UAAAC,GAAG,EAAI;UACZzB,KAAK,CAACiB,QAAQ,CAAC,QAAQ,CAAC,CAACC,IAAI,CAAC,YAAM;YAClCjB,OAAO,CAACyB,KAAK,CAACD,GAAG,CAAC;YAClBf,IAAI,CAAC;cAAEE,IAAI,EAAE;YAAI,CAAC,CAAC;UACrB,CAAC,CAAC;QACJ,CAAC,CAAC;MACN,CAAC,MAAM;QACLF,IAAI,CAAC,CAAC;MACR;IACF;EACF,CAAC,MAAM;IACL;IACA,IAAIJ,SAAS,CAACqB,OAAO,CAACnB,EAAE,CAACI,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;MACrC;MACAF,IAAI,CAAC,CAAC;IACR,CAAC,MAAM;MACLA,IAAI,oBAAAkB,MAAA,CAAoBpB,EAAE,CAACqB,QAAQ,CAAE,CAAC,EAAC;MACvC3B,SAAS,CAACW,IAAI,CAAC,CAAC;IAClB;EACF;AACF,CAAC,CAAC;AAEFd,MAAM,CAAC+B,SAAS,CAAC,YAAM;EACrB5B,SAAS,CAACW,IAAI,CAAC,CAAC;AAClB,CAAC,CAAC", "ignoreList": []}]}