{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/login.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/login.vue", "mtime": 1716984016441}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IHJlZ2lzdGVyIH0gZnJvbSAiQC9hcGkvc3lzdGVtL3VzZXIiOwppbXBvcnQgeyBnZXRDb2RlSW1nLCByZXNldENhcHRjaGEsIHJlc2V0UHdkQnlQaG9uZSB9IGZyb20gIkAvYXBpL2xvZ2luIjsKaW1wb3J0IENvb2tpZXMgZnJvbSAianMtY29va2llIjsKaW1wb3J0IHsgZW5jcnlwdCwgZGVjcnlwdCB9IGZyb20gIkAvdXRpbHMvanNlbmNyeXB0IjsKaW1wb3J0IEltYWdlVXBsb2FkIGZyb20gIkAvY29tcG9uZW50cy9JbWFnZVVwbG9hZCI7CmltcG9ydCB7IHJlZ2lvbkRhdGEsIENvZGVUb1RleHQgfSBmcm9tICJlbGVtZW50LWNoaW5hLWFyZWEtZGF0YSI7CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiTG9naW4iLAogIGNvbXBvbmVudHM6IHsKICAgIEltYWdlVXBsb2FkOiBJbWFnZVVwbG9hZAogIH0sCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICAvLyDoh6rlrprkuYnmoKHpqozop4TliJkKICAgIHZhciBiYXJnYWluUGljID0gZnVuY3Rpb24gYmFyZ2FpblBpYyhydWxlLCB2YWx1ZSwgY2FsbGJhY2spIHsKICAgICAgaWYgKCFfdGhpcy5pbWdOYW1lKSB7CiAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCLokKXkuJrmiafnhafnhafniYflv4XkvKAiKSk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgY2FsbGJhY2soKTsKICAgICAgfQogICAgfTsKICAgIHJldHVybiB7CiAgICAgIGNvZGVVcmw6ICIiLAogICAgICBjb29raWVQYXNzd29yZDogIiIsCiAgICAgIGxvZ2luRm9ybTogewogICAgICAgIHVzZXJuYW1lOiAiIiwKICAgICAgICBwYXNzd29yZDogIiIsCiAgICAgICAgcmVtZW1iZXJNZTogZmFsc2UsCiAgICAgICAgY29kZTogIiIsCiAgICAgICAgdXVpZDogIiIKICAgICAgfSwKICAgICAgbG9naW5SdWxlczogewogICAgICAgIHVzZXJuYW1lOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIsCiAgICAgICAgICBtZXNzYWdlOiAi55So5oi35ZCN5LiN6IO95Li656m6IgogICAgICAgIH1dLAogICAgICAgIHBhc3N3b3JkOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIsCiAgICAgICAgICBtZXNzYWdlOiAi5a+G56CB5LiN6IO95Li656m6IgogICAgICAgIH1dLAogICAgICAgIGNvZGU6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIHRyaWdnZXI6ICJjaGFuZ2UiLAogICAgICAgICAgbWVzc2FnZTogIumqjOivgeeggeS4jeiDveS4uuepuiIKICAgICAgICB9XQogICAgICB9LAogICAgICBsb2FkaW5nOiBmYWxzZSwKICAgICAgcmVkaXJlY3Q6IHVuZGVmaW5lZCwKICAgICAgLy8g5by55Ye65bGC5qCH6aKYCiAgICAgIHRpdGxlOiAiIiwKICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGCCiAgICAgIG9wZW46IGZhbHNlLAogICAgICAvLyDooajljZXmoKHpqowKICAgICAgcnVsZXM6IHsKICAgICAgICB1c2VyTmFtZTogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogIueUqOaIt+WQjeS4jeiDveS4uuepuiIsCiAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIKICAgICAgICB9XSwKICAgICAgICBjb21wYW55OiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAi5YWs5Y+45YWo56ew5LiN6IO95Li656m6IiwKICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgIH1dLAogICAgICAgIGJ1c2luZXNzTm86IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICLokKXkuJrmiafnhaflj7fnoIHkuI3og73kuLrnqboiLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgfV0sCiAgICAgICAgcHJvdmluY2U6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICLmiYDlnKjljLrln5/lv4XpgIkiLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgfV0sCiAgICAgICAgaW1nTmFtZTogW3sKICAgICAgICAgIHZhbGlkYXRvcjogYmFyZ2FpblBpYywKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogIuiQpeS4muaJp+eFp+eFp+eJh+W/heS8oCIsCiAgICAgICAgICB0cmlnZ2VyOiAiY2hhbmdlIgogICAgICAgIH1dLAogICAgICAgIHBhc3N3b3JkOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIsCiAgICAgICAgICBtZXNzYWdlOiAi5a+G56CB5LiN6IO95Li656m6IgogICAgICAgIH1dLAogICAgICAgIHBhc3N3b3JkMjogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiLAogICAgICAgICAgbWVzc2FnZTogIuehruiupOWvhueggeS4jeiDveS4uuepuiIKICAgICAgICB9XSwKICAgICAgICBlbWFpbDogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgdHlwZTogImVtYWlsIiwKICAgICAgICAgIG1lc3NhZ2U6ICIn6K+36L6T5YWl5q2j56Gu55qE6YKu566x5Zyw5Z2AIiwKICAgICAgICAgIHRyaWdnZXI6IFsiYmx1ciIsICJjaGFuZ2UiXQogICAgICAgIH1dLAogICAgICAgIHBob25lbnVtYmVyOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBwYXR0ZXJuOiAvXjFbM3w0fDV8Nnw3fDh8OV1bMC05XVxkezh9JC8sCiAgICAgICAgICBtZXNzYWdlOiAi6K+36L6T5YWl5q2j56Gu55qE5omL5py65Y+356CBIiwKICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgIH1dLAogICAgICAgIG5pY2tOYW1lOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAi5oql5aSH5Lq65aeT5ZCN5LiN6IO95Li656m6IiwKICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgIH1dLAogICAgICAgIGFkZHJlc3M6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICLotYTmlpnpgq7lr4TlnLDlnYDkuI3og73kuLrnqboiLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgfV0sCiAgICAgICAgY29kZTogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgdHJpZ2dlcjogImNoYW5nZSIsCiAgICAgICAgICBtZXNzYWdlOiAi6aqM6K+B56CB5LiN6IO95Li656m6IgogICAgICAgIH1dCiAgICAgIH0sCiAgICAgIC8vIOaAp+WIq+eKtuaAgeWtl+WFuAogICAgICBzZXhPcHRpb25zOiBbXSwKICAgICAgLy8g55+t5L+h6YCa55+l5a2X5YW4CiAgICAgIHNtc1NlbmRPcHRpb25zOiBbXSwKICAgICAgLy8g5a6h5qC454q25oCB5a2X5YW4CiAgICAgIGF1ZGl0U3RhdHVzT3B0aW9uczogW10sCiAgICAgIC8vIOinkuiJsumAiemhuQogICAgICByb2xlT3B0aW9uczogW10sCiAgICAgIC8vIOihqOWNleWPguaVsAogICAgICBmb3JtOiB7fSwKICAgICAgcHJvdmluY2VBbmRDaXR5RGF0YTogcmVnaW9uRGF0YSwKICAgICAgY2l0eU9wdGlvbnM6IFtdLAogICAgICBpbWc6IHVuZGVmaW5lZCwKICAgICAgaW1nTmFtZTogdW5kZWZpbmVkLAogICAgICBpbWdGaWxlOiB1bmRlZmluZWQsCiAgICAgIHJlc3RPcGVuOiBmYWxzZSwKICAgICAgcmVzZXRDb2RlVHh0OiAi6I635Y+W6aqM6K+B56CBIiwKICAgICAgcmVzZXRQd2RGb3JtOiB7CiAgICAgICAgdXNlcm5hbWU6ICIiLAogICAgICAgIHBhc3N3b3JkOiAiIiwKICAgICAgICBjb2RlOiAiIgogICAgICB9LAogICAgICByZXNldFJ1bGVzOiB7CiAgICAgICAgdXNlcm5hbWU6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIHBhdHRlcm46IC9eMVszfDR8NXw2fDd8OHw5XVswLTldXGR7OH0kLywKICAgICAgICAgIG1lc3NhZ2U6ICLor7fovpPlhaXmraPnoa7nmoTmiYvmnLrlj7fnoIEiLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgfV0sCiAgICAgICAgcGFzc3dvcmQ6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIHRyaWdnZXI6ICJibHVyIiwKICAgICAgICAgIG1lc3NhZ2U6ICLlr4bnoIHkuI3og73kuLrnqboiCiAgICAgICAgfV0sCiAgICAgICAgY29kZTogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgdHJpZ2dlcjogImNoYW5nZSIsCiAgICAgICAgICBtZXNzYWdlOiAi6aqM6K+B56CB5LiN6IO95Li656m6IgogICAgICAgIH1dCiAgICAgIH0KICAgIH07CiAgfSwKICB3YXRjaDogewogICAgJHJvdXRlOiB7CiAgICAgIGhhbmRsZXI6IGZ1bmN0aW9uIGhhbmRsZXIocm91dGUpIHsKICAgICAgICB0aGlzLnJlZGlyZWN0ID0gcm91dGUucXVlcnkgJiYgcm91dGUucXVlcnkucmVkaXJlY3Q7CiAgICAgIH0sCiAgICAgIGltbWVkaWF0ZTogdHJ1ZQogICAgfQogIH0sCiAgY3JlYXRlZDogZnVuY3Rpb24gY3JlYXRlZCgpIHsKICAgIHRoaXMuZ2V0Q29kZSgpOwogICAgdGhpcy5nZXRDb29raWUoKTsKICB9LAogIG1ldGhvZHM6IHsKICAgIGdldENvZGU6IGZ1bmN0aW9uIGdldENvZGUoKSB7CiAgICAgIHZhciBfdGhpczIgPSB0aGlzOwogICAgICBnZXRDb2RlSW1nKCkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgX3RoaXMyLmNvZGVVcmwgPSAiZGF0YTppbWFnZS9naWY7YmFzZTY0LCIgKyByZXMuaW1nOwogICAgICAgIF90aGlzMi5sb2dpbkZvcm0udXVpZCA9IHJlcy51dWlkOwogICAgICB9KTsKICAgIH0sCiAgICBnZXRDb29raWU6IGZ1bmN0aW9uIGdldENvb2tpZSgpIHsKICAgICAgdmFyIHVzZXJuYW1lID0gQ29va2llcy5nZXQoInVzZXJuYW1lIik7CiAgICAgIHZhciBwYXNzd29yZCA9IENvb2tpZXMuZ2V0KCJwYXNzd29yZCIpOwogICAgICB2YXIgcmVtZW1iZXJNZSA9IENvb2tpZXMuZ2V0KCJyZW1lbWJlck1lIik7CiAgICAgIHRoaXMubG9naW5Gb3JtID0gewogICAgICAgIHVzZXJuYW1lOiB1c2VybmFtZSA9PT0gdW5kZWZpbmVkID8gdGhpcy5sb2dpbkZvcm0udXNlcm5hbWUgOiB1c2VybmFtZSwKICAgICAgICBwYXNzd29yZDogcGFzc3dvcmQgPT09IHVuZGVmaW5lZCA/IHRoaXMubG9naW5Gb3JtLnBhc3N3b3JkIDogZGVjcnlwdChwYXNzd29yZCksCiAgICAgICAgcmVtZW1iZXJNZTogcmVtZW1iZXJNZSA9PT0gdW5kZWZpbmVkID8gZmFsc2UgOiBCb29sZWFuKHJlbWVtYmVyTWUpCiAgICAgIH07CiAgICB9LAogICAgaGFuZGxlTG9naW46IGZ1bmN0aW9uIGhhbmRsZUxvZ2luKCkgewogICAgICB2YXIgX3RoaXMzID0gdGhpczsKICAgICAgdGhpcy4kcmVmcy5sb2dpbkZvcm0udmFsaWRhdGUoZnVuY3Rpb24gKHZhbGlkKSB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICBfdGhpczMubG9hZGluZyA9IHRydWU7CiAgICAgICAgICBpZiAoX3RoaXMzLmxvZ2luRm9ybS5yZW1lbWJlck1lKSB7CiAgICAgICAgICAgIENvb2tpZXMuc2V0KCJ1c2VybmFtZSIsIF90aGlzMy5sb2dpbkZvcm0udXNlcm5hbWUsIHsKICAgICAgICAgICAgICBleHBpcmVzOiAzMAogICAgICAgICAgICB9KTsKICAgICAgICAgICAgQ29va2llcy5zZXQoInBhc3N3b3JkIiwgZW5jcnlwdChfdGhpczMubG9naW5Gb3JtLnBhc3N3b3JkKSwgewogICAgICAgICAgICAgIGV4cGlyZXM6IDMwCiAgICAgICAgICAgIH0pOwogICAgICAgICAgICBDb29raWVzLnNldCgicmVtZW1iZXJNZSIsIF90aGlzMy5sb2dpbkZvcm0ucmVtZW1iZXJNZSwgewogICAgICAgICAgICAgIGV4cGlyZXM6IDMwCiAgICAgICAgICAgIH0pOwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgQ29va2llcy5yZW1vdmUoInVzZXJuYW1lIik7CiAgICAgICAgICAgIENvb2tpZXMucmVtb3ZlKCJwYXNzd29yZCIpOwogICAgICAgICAgICBDb29raWVzLnJlbW92ZSgicmVtZW1iZXJNZSIpOwogICAgICAgICAgfQogICAgICAgICAgX3RoaXMzLiRzdG9yZS5kaXNwYXRjaCgiTG9naW4iLCBfdGhpczMubG9naW5Gb3JtKS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICAgICAgX3RoaXMzLiRyb3V0ZXIucHVzaCh7CiAgICAgICAgICAgICAgcGF0aDogX3RoaXMzLnJlZGlyZWN0IHx8ICIvIgogICAgICAgICAgICB9KS5jYXRjaChmdW5jdGlvbiAoKSB7fSk7CiAgICAgICAgICB9KS5jYXRjaChmdW5jdGlvbiAoKSB7CiAgICAgICAgICAgIF90aGlzMy5sb2FkaW5nID0gZmFsc2U7CiAgICAgICAgICAgIF90aGlzMy5nZXRDb2RlKCk7CiAgICAgICAgICB9KTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIC8vIOihqOWNlemHjee9rgogICAgcmVzZXQ6IGZ1bmN0aW9uIHJlc2V0KCkgewogICAgICB0aGlzLmZvcm0gPSB7CiAgICAgICAgdXNlcklkOiB1bmRlZmluZWQsCiAgICAgICAgdXNlck5hbWU6IHVuZGVmaW5lZCwKICAgICAgICBuaWNrTmFtZTogdW5kZWZpbmVkLAogICAgICAgIHVzZXJUeXBlOiB1bmRlZmluZWQsCiAgICAgICAgZW1haWw6IHVuZGVmaW5lZCwKICAgICAgICBwaG9uZW51bWJlcjogdW5kZWZpbmVkLAogICAgICAgIHNleDogIjIiLAogICAgICAgIGF2YXRhcjogdW5kZWZpbmVkLAogICAgICAgIHBhc3N3b3JkOiB1bmRlZmluZWQsCiAgICAgICAgY29tcGFueTogdW5kZWZpbmVkLAogICAgICAgIGJ1c2luZXNzTm86IHVuZGVmaW5lZCwKICAgICAgICBidXNpbmVzc05vUGljOiB1bmRlZmluZWQsCiAgICAgICAgcHJvdmluY2U6IHVuZGVmaW5lZCwKICAgICAgICBhZGRyZXNzOiB1bmRlZmluZWQsCiAgICAgICAgZGVhbGVyOiB1bmRlZmluZWQKICAgICAgfTsKICAgICAgdGhpcy5pbWcgPSB1bmRlZmluZWQ7CiAgICAgIHRoaXMuaW1nTmFtZSA9IHVuZGVmaW5lZDsKICAgICAgdGhpcy5pbWdGaWxlID0gdW5kZWZpbmVkOwogICAgICB0aGlzLnJlc2V0Rm9ybSgiZm9ybSIpOwogICAgfSwKICAgIC8vIOWPlua2iOaMiemSrgogICAgY2FuY2VsOiBmdW5jdGlvbiBjYW5jZWwoKSB7CiAgICAgIHRoaXMub3BlbiA9IGZhbHNlOwogICAgICB0aGlzLnJlc2V0KCk7CiAgICB9LAogICAgLyoqIOaWsOWinuaMiemSruaTjeS9nCAqL2hhbmRsZUFkZDogZnVuY3Rpb24gaGFuZGxlQWRkKCkgewogICAgICB0aGlzLnJlc2V0KCk7CiAgICAgIC8vdGhpcy5nZXRUcmVlc2VsZWN0KCk7CiAgICAgIHRoaXMub3BlbiA9IHRydWU7CiAgICAgIHRoaXMudGl0bGUgPSAi55So5oi35rOo5YaMIjsKICAgIH0sCiAgICBoYW5kbGVSZXN0OiBmdW5jdGlvbiBoYW5kbGVSZXN0KCkgewogICAgICAvL3RoaXMuZ2V0VHJlZXNlbGVjdCgpOwogICAgICB0aGlzLnJlc3RPcGVuID0gdHJ1ZTsKICAgIH0sCiAgICAvKiog5o+Q5Lqk5oyJ6ZKuICovCiAgICBzdWJtaXRGb3JtOiBmdW5jdGlvbiBzdWJtaXRGb3JtKCkgewogICAgICB2YXIgX3RoaXM0ID0gdGhpczsKICAgICAgaWYgKHRoaXMuZm9ybS5wYXNzd29yZCAhPSB0aGlzLmZvcm0ucGFzc3dvcmQyKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi5Lik5qyh6L6T5YWl5a+G56CB5LiN5LiA6Ie0Iik7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIC8vIHRoaXMuZm9ybS5maWxlID0gdGhpcy5pbWdGaWxlOwogICAgICAvLyBjb25zb2xlLmxvZyh0aGlzLmZvcm0pCgogICAgICB0aGlzLiRyZWZzWyJmb3JtIl0udmFsaWRhdGUoZnVuY3Rpb24gKHZhbGlkKSB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICB2YXIgcGFyYW0gPSBuZXcgRm9ybURhdGEoKTsKICAgICAgICAgIHBhcmFtLmFwcGVuZCgiZmlsZSIsIF90aGlzNC5pbWdGaWxlKTsKICAgICAgICAgIHBhcmFtLmFwcGVuZCgidXNlck5hbWUiLCBfdGhpczQuZm9ybS51c2VyTmFtZSk7CiAgICAgICAgICBwYXJhbS5hcHBlbmQoIm5pY2tOYW1lIiwgX3RoaXM0LmZvcm0ubmlja05hbWUpOwogICAgICAgICAgcGFyYW0uYXBwZW5kKCJlbWFpbCIsIF90aGlzNC5mb3JtLmVtYWlsKTsKICAgICAgICAgIHBhcmFtLmFwcGVuZCgicGhvbmVudW1iZXIiLCBfdGhpczQuZm9ybS5waG9uZW51bWJlcik7CiAgICAgICAgICBwYXJhbS5hcHBlbmQoInBhc3N3b3JkIiwgX3RoaXM0LmZvcm0ucGFzc3dvcmQpOwogICAgICAgICAgcGFyYW0uYXBwZW5kKCJjb21wYW55IiwgX3RoaXM0LmZvcm0uY29tcGFueSk7CiAgICAgICAgICBwYXJhbS5hcHBlbmQoImJ1c2luZXNzTm8iLCBfdGhpczQuZm9ybS5idXNpbmVzc05vKTsKICAgICAgICAgIHBhcmFtLmFwcGVuZCgicHJvdmluY2UiLCBfdGhpczQuZm9ybS5wcm92aW5jZSk7CiAgICAgICAgICBwYXJhbS5hcHBlbmQoImFkZHJlc3MiLCBfdGhpczQuZm9ybS5hZGRyZXNzKTsKICAgICAgICAgIHBhcmFtLmFwcGVuZCgiZGVhbGVyIiwgX3RoaXM0LmZvcm0uZGVhbGVyKTsKICAgICAgICAgICIiOwogICAgICAgICAgcmVnaXN0ZXIocGFyYW0pLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgICAgIF90aGlzNC5tc2dTdWNjZXNzKCLmj5DkuqTmiJDlip8iKTsKICAgICAgICAgICAgX3RoaXM0Lm9wZW4gPSBmYWxzZTsKICAgICAgICAgICAgX3RoaXM0LnJlc2V0KCk7CiAgICAgICAgICB9KTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIGhhbmRsZUNpdHlDaGFuZ2U6IGZ1bmN0aW9uIGhhbmRsZUNpdHlDaGFuZ2UodmFsdWUpIHsKICAgICAgdGhpcy5jaXR5T3B0aW9ucyA9IHZhbHVlOwogICAgICB2YXIgdHh0ID0gIiI7CiAgICAgIHZhbHVlLmZvckVhY2goZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICB0eHQgKz0gQ29kZVRvVGV4dFtpdGVtXSArICIvIjsKICAgICAgfSk7CiAgICAgIGlmICh0eHQubGVuZ3RoID4gMSkgewogICAgICAgIHR4dCA9IHR4dC5zdWJzdHJpbmcoMCwgdHh0Lmxlbmd0aCAtIDEpOwogICAgICAgIHRoaXMuZm9ybS5wcm92aW5jZSA9IHR4dDsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLmZvcm0ucHJvdmluY2UgPSB1bmRlZmluZWQ7CiAgICAgIH0KICAgIH0sCiAgICBhZGRJbWc6IGZ1bmN0aW9uIGFkZEltZyhmaWxlMSkgewogICAgICB2YXIgZmlsZSA9IGZpbGUxLnJhdzsKICAgICAgdmFyIGlzSlBHID0gZmlsZS50eXBlID09PSAiaW1hZ2UvanBlZyI7CiAgICAgIHZhciBpc1BORyA9IGZpbGUudHlwZSA9PT0gImltYWdlL3BuZyI7CiAgICAgIHZhciBpc1dFQlAgPSBmaWxlLnR5cGUgPT09ICJpbWFnZS93ZWJwIjsKICAgICAgdmFyIGlzTHQxTSA9IGZpbGUuc2l6ZSAvIDEwMjQgLyAxMDI0IDwgMTsKICAgICAgaWYgKCFpc0pQRyAmJiAhaXNQTkcgJiYgIWlzV0VCUCkgewogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuS4iuS8oOWbvueJh+WPquiDveaYryBKUEfjgIFQTkfjgIFXRUJQIOagvOW8jyEiKTsKICAgICAgfSBlbHNlIGlmICghaXNMdDFNKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi5LiK5Lyg5Zu+54mH5aSn5bCP5LiN6IO96LaF6L+HIDFNQiEiKTsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLmltZ0ZpbGUgPSBmaWxlOwogICAgICAgIHRoaXMuaW1nTmFtZSA9IGZpbGUubmFtZTsKICAgICAgICB2YXIgc2VsZiA9IHRoaXM7CiAgICAgICAgLy/lrprkuYnkuIDkuKrmlofku7bpmIXor7vlmagKICAgICAgICB2YXIgcmVhZGVyID0gbmV3IEZpbGVSZWFkZXIoKTsKICAgICAgICAvL+aWh+S7tuijhei9veWQjuWwhuWFtuaYvuekuuWcqOWbvueJh+mihOiniOmHjAogICAgICAgIHJlYWRlci5vbmxvYWQgPSBmdW5jdGlvbiAoZSkgewogICAgICAgICAgLy/lsIZiYWRlNjTkvY3lm77niYfkv53lrZjoh7PmlbDnu4Tph4zkvpvkuIrpnaLlm77niYfmmL7npLoKICAgICAgICAgIHNlbGYuaW1nID0gZS50YXJnZXQucmVzdWx0OwogICAgICAgIH07CiAgICAgICAgcmVhZGVyLnJlYWRBc0RhdGFVUkwoZmlsZSk7CiAgICAgIH0KICAgICAgdGhpcy4kcmVmcy5mb3JtLnZhbGlkYXRlRmllbGQoImltZ05hbWUiKTsKICAgIH0sCiAgICByZW1vdmVJbWFnZTogZnVuY3Rpb24gcmVtb3ZlSW1hZ2UoKSB7CiAgICAgIHRoaXMuaW1nRmlsZSA9IHVuZGVmaW5lZDsKICAgICAgdGhpcy5pbWcgPSB1bmRlZmluZWQ7CiAgICAgIHRoaXMuaW1nTmFtZSA9IHVuZGVmaW5lZDsKICAgIH0sCiAgICBnZXRQaG9uZUNvZGU6IGZ1bmN0aW9uIGdldFBob25lQ29kZSgpIHsKICAgICAgdmFyIHRoYXQgPSB0aGlzOwogICAgICBpZiAodGhpcy5yZXNldFB3ZEZvcm0udXNlcm5hbWUgPT0gIiIpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLor7fovpPlhaXmiYvmnLrlj7ciKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgcmVzZXRDYXB0Y2hhKHRoaXMucmVzZXRQd2RGb3JtKS50aGVuKGZ1bmN0aW9uIChyZXNwKSB7CiAgICAgICAgdGhhdC5yZXNldENvZGVUeHQgPSAiNjBz5ZCO6YeN5paw6I635Y+WIjsKICAgICAgICBzZXRUaW1lb3V0KGZ1bmN0aW9uICgpIHsKICAgICAgICAgIHRoYXQucmVzZXRDb2RlVHh0ID0gIuiOt+WPlumqjOivgeeggSI7CiAgICAgICAgfSwgNjAgKiAxMDAwKTsKICAgICAgfSk7CiAgICB9LAogICAgaGFuZFJlc2V0UHdkOiBmdW5jdGlvbiBoYW5kUmVzZXRQd2QoKSB7CiAgICAgIHZhciBfdGhpczUgPSB0aGlzOwogICAgICB2YXIgdGhhdCA9IHRoaXM7CiAgICAgIHRoaXMuJHJlZnNbInJlc2V0UHdkRm9ybSJdLnZhbGlkYXRlKGZ1bmN0aW9uICh2YWxpZCkgewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgaWYgKF90aGlzNS5yZXNldFB3ZEZvcm0ucGFzc3dvcmQgPT0gIiIgfHwgX3RoaXM1LnJlc2V0UHdkRm9ybS5wYXNzd29yZCAhPSBfdGhpczUucmVzZXRQd2RGb3JtLnBhc3N3b3JkMikgewogICAgICAgICAgICBfdGhpczUuJG1lc3NhZ2UuZXJyb3IoIuS4pOasoei+k+WFpeWvhueggeS4jeS4gOiHtCIpOwogICAgICAgICAgICByZXR1cm47CiAgICAgICAgICB9CiAgICAgICAgICBpZiAoX3RoaXM1LnJlc2V0UHdkRm9ybS5wYXNzd29yZC5sZW5ndGggPCA2KSB7CiAgICAgICAgICAgIF90aGlzNS4kbWVzc2FnZS5lcnJvcigi5a+G56CB6ZW/5bqm5LiN6IO95bCP5LqOIik7CiAgICAgICAgICAgIHJldHVybjsKICAgICAgICAgIH0KICAgICAgICAgIHJlc2V0UHdkQnlQaG9uZShfdGhpczUucmVzZXRQd2RGb3JtKS50aGVuKGZ1bmN0aW9uIChyZXNwKSB7CiAgICAgICAgICAgIF90aGlzNS5tc2dTdWNjZXNzKCLph43nva7miJDlip8iKTsKICAgICAgICAgICAgdGhhdC5yZXN0T3BlbiA9IGZhbHNlOwogICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["register", "getCodeImg", "resetCaptcha", "resetPwdByPhone", "Cookies", "encrypt", "decrypt", "ImageUpload", "regionData", "CodeToText", "name", "components", "data", "_this", "bargainPic", "rule", "value", "callback", "imgName", "Error", "codeUrl", "cookiePassword", "loginForm", "username", "password", "rememberMe", "code", "uuid", "loginRules", "required", "trigger", "message", "loading", "redirect", "undefined", "title", "open", "rules", "userName", "company", "businessNo", "province", "validator", "password2", "email", "type", "phonenumber", "pattern", "nick<PERSON><PERSON>", "address", "sexOptions", "smsSendOptions", "auditStatusOptions", "roleOptions", "form", "provinceAndCityData", "cityOptions", "img", "imgFile", "restOpen", "resetCodeTxt", "resetPwdForm", "resetRules", "watch", "$route", "handler", "route", "query", "immediate", "created", "getCode", "<PERSON><PERSON><PERSON><PERSON>", "methods", "_this2", "then", "res", "get", "Boolean", "handleLogin", "_this3", "$refs", "validate", "valid", "set", "expires", "remove", "$store", "dispatch", "$router", "push", "path", "catch", "reset", "userId", "userType", "sex", "avatar", "businessNoPic", "dealer", "resetForm", "cancel", "handleAdd", "handleRest", "submitForm", "_this4", "$message", "error", "param", "FormData", "append", "response", "msgSuccess", "handleCityChange", "txt", "for<PERSON>ach", "item", "length", "substring", "addImg", "file1", "file", "raw", "isJPG", "isPNG", "isWEBP", "isLt1M", "size", "self", "reader", "FileReader", "onload", "e", "target", "result", "readAsDataURL", "validateField", "removeImage", "getPhoneCode", "that", "resp", "setTimeout", "handResetPwd", "_this5"], "sources": ["src/views/login.vue"], "sourcesContent": ["<template>\n  <div class=\"login login-padding\">\n    <div class=\"logo-wrapper\">\n      <div class=\"logo\">\n        <img src=\"../assets/images/logo.png\" alt=\"logo\" />\n      </div>\n    </div>\n    <el-form\n      ref=\"loginForm\"\n      :model=\"loginForm\"\n      :rules=\"loginRules\"\n      class=\"login-form\"\n    >\n      <h3 class=\"title\">项目管理系统</h3>\n      <el-form-item prop=\"username\">\n        <el-input\n          v-model=\"loginForm.username\"\n          type=\"text\"\n          auto-complete=\"off\"\n          placeholder=\"账号\"\n        >\n          <svg-icon\n            slot=\"prefix\"\n            icon-class=\"user\"\n            class=\"el-input__icon input-icon\"\n          />\n        </el-input>\n      </el-form-item>\n      <el-form-item prop=\"password\">\n        <el-input\n          v-model=\"loginForm.password\"\n          type=\"password\"\n          auto-complete=\"off\"\n          placeholder=\"密码\"\n          @keyup.enter.native=\"handleLogin\"\n        >\n          <svg-icon\n            slot=\"prefix\"\n            icon-class=\"password\"\n            class=\"el-input__icon input-icon\"\n          />\n        </el-input>\n      </el-form-item>\n      <el-form-item prop=\"code\">\n        <el-input\n          v-model=\"loginForm.code\"\n          auto-complete=\"off\"\n          placeholder=\"验证码\"\n          style=\"width: 63%\"\n          @keyup.enter.native=\"handleLogin\"\n        >\n          <svg-icon\n            slot=\"prefix\"\n            icon-class=\"validCode\"\n            class=\"el-input__icon input-icon\"\n          />\n        </el-input>\n        <div class=\"login-code\">\n          <img :src=\"codeUrl\" @click=\"getCode\" class=\"login-code-img\" />\n        </div>\n      </el-form-item>\n\n      <el-row :gutter=\"20\">\n        <el-col :span=\"7\">\n          <el-checkbox\n            v-model=\"loginForm.rememberMe\"\n            style=\"margin: 0px 0px 25px 0px\"\n            >记住密码</el-checkbox\n          >\n        </el-col>\n        <el-col :span=\"7\" :offset=\"4\"\n          ><el-link @click=\"handleRest\" type=\"primary\"\n            >忘记密码</el-link\n          ></el-col\n        >\n        <el-col :span=\"6\" style=\"padding-left: 10%\"\n          ><el-link @click=\"handleAdd\" type=\"primary\">注册</el-link></el-col\n        >\n      </el-row>\n      <el-form-item style=\"width: 100%\">\n        <el-button\n          :loading=\"loading\"\n          size=\"medium\"\n          type=\"primary\"\n          style=\"width: 100%\"\n          @click.native.prevent=\"handleLogin\"\n        >\n          <span v-if=\"!loading\">登 录</span>\n          <span v-else>登 录 中...</span>\n        </el-button>\n      </el-form-item>\n    </el-form>\n    <!--  底部  -->\n    <div class=\"el-login-footer\">\n      <span\n        >Copyright © 2018 福建省海佳集团股份有限公司.All Rights Reserved.\n        闽ICP备18002975号</span\n      >\n    </div>\n\n    <!-- 添加或修改参数配置对话框 -->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"open\"\n      :close-on-click-modal=\"false\"\n      width=\"1000px\"\n      append-to-body\n    >\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"用户账号\" prop=\"userName\">\n              <el-input v-model=\"form.userName\" placeholder=\"请输入用户账号\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"报备人姓名\" prop=\"nickName\">\n              <el-input\n                v-model=\"form.nickName\"\n                placeholder=\"请输入报备人姓名\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"报备人电话\" prop=\"phonenumber\">\n              <el-input\n                v-model=\"form.phonenumber\"\n                placeholder=\"请输入报备人电话\"\n                maxlength=\"11\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"所在区域\" prop=\"province\" :required=\"true\">\n              <el-cascader\n                :options=\"provinceAndCityData\"\n                clearable\n                :props=\"{ expandTrigger: 'hover' }\"\n                v-model=\"cityOptions\"\n                @change=\"handleCityChange\"\n              >\n              </el-cascader>\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"密码\" prop=\"password\">\n              <el-input\n                v-model=\"form.password\"\n                placeholder=\"请输入密码\"\n                type=\"password\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"确认密码\" prop=\"password2\">\n              <el-input\n                v-model=\"form.password2\"\n                placeholder=\"请输入密码\"\n                type=\"password\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"营业执照号码\" prop=\"businessNo\">\n              <el-input\n                v-model=\"form.businessNo\"\n                placeholder=\"请输入营业执照号码\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"公司全称\" prop=\"company\">\n              <el-input v-model=\"form.company\" placeholder=\"请输入公司全称\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <!-- <el-col :span=\"12\">\n            <el-form-item label=\"营业执照图片\" prop=\"businessNoPic\">\n              <imageUpload v-model=\"form.businessNoPic\" />\n            </el-form-item>\n          </el-col> -->\n          <el-col :span=\"12\">\n            <el-form-item label=\"营业执照图片\" prop=\"imgName\">\n              <el-upload\n                action=\"/undefind\"\n                :on-change=\"addImg\"\n                :auto-upload=\"false\"\n                :show-file-list=\"false\"\n              >\n                <el-image v-if=\"!img\" :src=\"img\" :style=\"`width:150px;`\">\n                  <div slot=\"error\" class=\"image-slot\">\n                    <i class=\"el-icon-plus\" :style=\"`height:60px;`\" />\n                  </div>\n                </el-image>\n                <div v-else class=\"image\">\n                  <el-image :src=\"img\" :style=\"`width:150px;`\" fit=\"fill\" />\n                  <div class=\"mask\">\n                    <div class=\"actions\">\n                      <span title=\"移除\" @click.stop=\"removeImage\">\n                        <i class=\"el-icon-delete\" />\n                      </span>\n                    </div>\n                  </div>\n                </div>\n              </el-upload>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"隶属经销商\" prop=\"dealer\">\n              <el-input v-model=\"form.dealer\" placeholder=\"请输入隶属经销商\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"资料邮寄地址\" prop=\"address\">\n              <el-input\n                v-model=\"form.address\"\n                placeholder=\"请输入资料邮寄地址\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"资料接收邮箱\" prop=\"email\">\n              <el-input\n                v-model=\"form.email\"\n                placeholder=\"请输入资料接收邮箱\"\n                maxlength=\"50\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n    <el-dialog\n      title=\"重置密码\"\n      :visible.sync=\"restOpen\"\n      :close-on-click-modal=\"false\"\n      width=\"400px\"\n      append-to-body\n    >\n      <el-form\n        ref=\"resetPwdForm\"\n        :model=\"resetPwdForm\"\n        :rules=\"resetRules\"\n        label-width=\"90px\"\n      >\n        <el-form-item prop=\"username\" label=\"手机号码\">\n          <el-input\n            v-model=\"resetPwdForm.username\"\n            type=\"text\"\n            auto-complete=\"off\"\n            placeholder=\"手机号码\"\n          >\n            <svg-icon\n              slot=\"prefix\"\n              icon-class=\"user\"\n              class=\"el-input__icon input-icon\"\n            />\n          </el-input>\n        </el-form-item>\n        <el-form-item prop=\"password\" label=\"密码\">\n          <el-input\n            v-model=\"resetPwdForm.password\"\n            type=\"password\"\n            auto-complete=\"off\"\n            placeholder=\"密码\"\n          >\n            <svg-icon\n              slot=\"prefix\"\n              icon-class=\"password\"\n              class=\"el-input__icon input-icon\"\n            />\n          </el-input>\n        </el-form-item>\n        <el-form-item prop=\"password2\" label=\"确认密码\" required>\n          <el-input\n            v-model=\"resetPwdForm.password2\"\n            type=\"password\"\n            auto-complete=\"off\"\n            placeholder=\"确认密码\"\n          >\n            <svg-icon\n              slot=\"prefix\"\n              icon-class=\"password\"\n              class=\"el-input__icon input-icon\"\n            />\n          </el-input>\n        </el-form-item>\n        <el-form-item prop=\"code\" label=\"验证码\">\n          <el-input\n            v-model=\"resetPwdForm.code\"\n            auto-complete=\"off\"\n            placeholder=\"验证码\"\n            style=\"width: 50%\"\n          >\n          </el-input>\n          <div class=\"login-code\" style=\"width: 45%\">\n            <el-link\n              @click=\"getPhoneCode\"\n              type=\"primary\"\n              :disabled=\"resetCodeTxt != '获取验证码'\"\n              >{{ resetCodeTxt }}</el-link\n            >\n          </div>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"handResetPwd\">确 定</el-button>\n        <el-button @click=\"restOpen = false\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { register } from \"@/api/system/user\";\nimport { getCodeImg, resetCaptcha, resetPwdByPhone } from \"@/api/login\";\nimport Cookies from \"js-cookie\";\nimport { encrypt, decrypt } from \"@/utils/jsencrypt\";\nimport ImageUpload from \"@/components/ImageUpload\";\nimport { regionData, CodeToText } from \"element-china-area-data\";\nexport default {\n  name: \"Login\",\n  components: { ImageUpload },\n  data() {\n    // 自定义校验规则\n    var bargainPic = (rule, value, callback) => {\n      if (!this.imgName) {\n        callback(new Error(\"营业执照照片必传\"));\n      } else {\n        callback();\n      }\n    };\n    return {\n      codeUrl: \"\",\n      cookiePassword: \"\",\n      loginForm: {\n        username: \"\",\n        password: \"\",\n        rememberMe: false,\n        code: \"\",\n        uuid: \"\",\n      },\n      loginRules: {\n        username: [\n          { required: true, trigger: \"blur\", message: \"用户名不能为空\" },\n        ],\n        password: [\n          { required: true, trigger: \"blur\", message: \"密码不能为空\" },\n        ],\n        code: [\n          { required: true, trigger: \"change\", message: \"验证码不能为空\" },\n        ],\n      },\n      loading: false,\n      redirect: undefined,\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      open: false,\n      // 表单校验\n      rules: {\n        userName: [\n          { required: true, message: \"用户名不能为空\", trigger: \"blur\" },\n        ],\n        company: [\n          { required: true, message: \"公司全称不能为空\", trigger: \"blur\" },\n        ],\n        businessNo: [\n          { required: true, message: \"营业执照号码不能为空\", trigger: \"blur\" },\n        ],\n        province: [\n          { required: true, message: \"所在区域必选\", trigger: \"blur\" },\n        ],\n        imgName: [\n          {\n            validator: bargainPic,\n            required: true,\n            message: \"营业执照照片必传\",\n            trigger: \"change\",\n          },\n        ],\n        password: [\n          { required: true, trigger: \"blur\", message: \"密码不能为空\" },\n        ],\n        password2: [\n          { required: true, trigger: \"blur\", message: \"确认密码不能为空\" },\n        ],\n        email: [\n          {\n            required: true,\n            type: \"email\",\n            message: \"'请输入正确的邮箱地址\",\n            trigger: [\"blur\", \"change\"],\n          },\n        ],\n        phonenumber: [\n          {\n            required: true,\n            pattern: /^1[3|4|5|6|7|8|9][0-9]\\d{8}$/,\n            message: \"请输入正确的手机号码\",\n            trigger: \"blur\",\n          },\n        ],\n        nickName: [\n          { required: true, message: \"报备人姓名不能为空\", trigger: \"blur\" },\n        ],\n        address: [\n          { required: true, message: \"资料邮寄地址不能为空\", trigger: \"blur\" },\n        ],\n        code: [\n          { required: true, trigger: \"change\", message: \"验证码不能为空\" },\n        ],\n      },\n      // 性别状态字典\n      sexOptions: [],\n      // 短信通知字典\n      smsSendOptions: [],\n      // 审核状态字典\n      auditStatusOptions: [],\n      // 角色选项\n      roleOptions: [],\n      // 表单参数\n      form: {},\n      provinceAndCityData: regionData,\n      cityOptions: [],\n      img: undefined,\n      imgName: undefined,\n      imgFile: undefined,\n      restOpen: false,\n      resetCodeTxt: \"获取验证码\",\n      resetPwdForm: {\n        username: \"\",\n        password: \"\",\n        code: \"\",\n      },\n      resetRules: {\n        username: [\n          {\n            required: true,\n            pattern: /^1[3|4|5|6|7|8|9][0-9]\\d{8}$/,\n            message: \"请输入正确的手机号码\",\n            trigger: \"blur\",\n          },\n        ],\n        password: [\n          { required: true, trigger: \"blur\", message: \"密码不能为空\" },\n        ],\n        code: [\n          { required: true, trigger: \"change\", message: \"验证码不能为空\" },\n        ],\n      },\n    };\n  },\n  watch: {\n    $route: {\n      handler: function (route) {\n        this.redirect = route.query && route.query.redirect;\n      },\n      immediate: true,\n    },\n  },\n  created() {\n    this.getCode();\n    this.getCookie();\n  },\n  methods: {\n    getCode() {\n      getCodeImg().then((res) => {\n        this.codeUrl = \"data:image/gif;base64,\" + res.img;\n        this.loginForm.uuid = res.uuid;\n      });\n    },\n    getCookie() {\n      const username = Cookies.get(\"username\");\n      const password = Cookies.get(\"password\");\n      const rememberMe = Cookies.get(\"rememberMe\");\n      this.loginForm = {\n        username: username === undefined ? this.loginForm.username : username,\n        password:\n          password === undefined ? this.loginForm.password : decrypt(password),\n        rememberMe: rememberMe === undefined ? false : Boolean(rememberMe),\n      };\n    },\n    handleLogin() {\n      this.$refs.loginForm.validate((valid) => {\n        if (valid) {\n          this.loading = true;\n          if (this.loginForm.rememberMe) {\n            Cookies.set(\"username\", this.loginForm.username, { expires: 30 });\n            Cookies.set(\"password\", encrypt(this.loginForm.password), {\n              expires: 30,\n            });\n            Cookies.set(\"rememberMe\", this.loginForm.rememberMe, {\n              expires: 30,\n            });\n          } else {\n            Cookies.remove(\"username\");\n            Cookies.remove(\"password\");\n            Cookies.remove(\"rememberMe\");\n          }\n          this.$store\n            .dispatch(\"Login\", this.loginForm)\n            .then(() => {\n              this.$router.push({ path: this.redirect || \"/\" }).catch(() => {});\n            })\n            .catch(() => {\n              this.loading = false;\n              this.getCode();\n            });\n        }\n      });\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        userId: undefined,\n        userName: undefined,\n        nickName: undefined,\n        userType: undefined,\n        email: undefined,\n        phonenumber: undefined,\n        sex: \"2\",\n        avatar: undefined,\n        password: undefined,\n        company: undefined,\n        businessNo: undefined,\n        businessNoPic: undefined,\n        province: undefined,\n        address: undefined,\n        dealer: undefined,\n      };\n      this.img = undefined;\n      this.imgName = undefined;\n      this.imgFile = undefined;\n      this.resetForm(\"form\");\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.reset();\n      //this.getTreeselect();\n      this.open = true;\n      this.title = \"用户注册\";\n    },\n    handleRest() {\n      //this.getTreeselect();\n      this.restOpen = true;\n    },\n    /** 提交按钮 */\n    submitForm: function () {\n      if (this.form.password != this.form.password2) {\n        this.$message.error(\"两次输入密码不一致\");\n        return;\n      }\n      // this.form.file = this.imgFile;\n      // console.log(this.form)\n\n      this.$refs[\"form\"].validate((valid) => {\n        if (valid) {\n          let param = new FormData();\n          param.append(\"file\", this.imgFile);\n          param.append(\"userName\", this.form.userName);\n          param.append(\"nickName\", this.form.nickName);\n          param.append(\"email\", this.form.email);\n          param.append(\"phonenumber\", this.form.phonenumber);\n          param.append(\"password\", this.form.password);\n          param.append(\"company\", this.form.company);\n          param.append(\"businessNo\", this.form.businessNo);\n          param.append(\"province\", this.form.province);\n          param.append(\"address\", this.form.address);\n          param.append(\"dealer\", this.form.dealer);\n          ``;\n          register(param).then((response) => {\n            this.msgSuccess(\"提交成功\");\n            this.open = false;\n            this.reset();\n          });\n        }\n      });\n    },\n    handleCityChange(value) {\n      this.cityOptions = value;\n      var txt = \"\";\n      value.forEach(function (item) {\n        txt += CodeToText[item] + \"/\";\n      });\n      if (txt.length > 1) {\n        txt = txt.substring(0, txt.length - 1);\n        this.form.province = txt;\n      } else {\n        this.form.province = undefined;\n      }\n    },\n    addImg(file1) {\n      const file = file1.raw;\n      const isJPG = file.type === \"image/jpeg\";\n      const isPNG = file.type === \"image/png\";\n      const isWEBP = file.type === \"image/webp\";\n      const isLt1M = file.size / 1024 / 1024 < 1;\n\n      if (!isJPG && !isPNG && !isWEBP) {\n        this.$message.error(\"上传图片只能是 JPG、PNG、WEBP 格式!\");\n      } else if (!isLt1M) {\n        this.$message.error(\"上传图片大小不能超过 1MB!\");\n      } else {\n        this.imgFile = file;\n        this.imgName = file.name;\n        var self = this;\n        //定义一个文件阅读器\n        var reader = new FileReader();\n        //文件装载后将其显示在图片预览里\n        reader.onload = function (e) {\n          //将bade64位图片保存至数组里供上面图片显示\n          self.img = e.target.result;\n        };\n        reader.readAsDataURL(file);\n      }\n      this.$refs.form.validateField(\"imgName\");\n    },\n    removeImage() {\n      this.imgFile = undefined;\n      this.img = undefined;\n      this.imgName = undefined;\n    },\n    getPhoneCode() {\n      let that = this;\n      if (this.resetPwdForm.username == \"\") {\n        this.$message.error(\"请输入手机号\");\n        return;\n      }\n\n      resetCaptcha(this.resetPwdForm).then((resp) => {\n        that.resetCodeTxt = \"60s后重新获取\";\n        setTimeout(() => {\n          that.resetCodeTxt = \"获取验证码\";\n        }, 60 * 1000);\n      });\n    },\n    handResetPwd() {\n      let that = this;\n      this.$refs[\"resetPwdForm\"].validate((valid) => {\n        if (valid) {\n          if (\n            this.resetPwdForm.password == \"\" ||\n            this.resetPwdForm.password != this.resetPwdForm.password2\n          ) {\n            this.$message.error(\"两次输入密码不一致\");\n            return;\n          }\n          if (this.resetPwdForm.password.length < 6) {\n            this.$message.error(\"密码长度不能小于\");\n            return;\n          }\n          resetPwdByPhone(this.resetPwdForm).then((resp) => {\n            this.msgSuccess(\"重置成功\");\n            that.restOpen = false;\n          });\n        }\n      });\n    },\n  },\n};\n</script>\n\n<style rel=\"stylesheet/scss\" lang=\"scss\">\n.login {\n  display: flex;\n  justify-content: flex-end;\n  align-items: center;\n  height: 100%;\n  background-image: url(\"../assets/images/login-background.webp\");\n  background-size: cover;\n}\n@media screen and (min-width: 600px) {\n  .login-padding {\n    padding-right: 10%;\n  }\n}\n@media screen and (max-width: 599px) {\n  .login-padding {\n    padding-left: 2px;\n    padding-right: 7px;\n  }\n}\n.title {\n  margin: 0px auto 30px auto;\n  text-align: center;\n  color: #707070;\n}\n\n.login-form {\n  border-radius: 6px;\n  background: #ffffff;\n  width: 400px;\n  padding: 25px 25px 5px 25px;\n  .el-input {\n    height: 38px;\n    input {\n      height: 38px;\n    }\n  }\n  .input-icon {\n    height: 39px;\n    width: 14px;\n    margin-left: 2px;\n  }\n}\n.login-tip {\n  font-size: 13px;\n  text-align: center;\n  color: #bfbfbf;\n}\n.login-code {\n  width: 33%;\n  height: 38px;\n  float: right;\n  img {\n    cursor: pointer;\n    vertical-align: middle;\n  }\n}\n.el-login-footer {\n  height: 40px;\n  line-height: 40px;\n  position: fixed;\n  bottom: 0;\n  width: 100%;\n  text-align: right;\n  color: #999999;\n  font-family: Arial;\n  font-size: 12px;\n  letter-spacing: 1px;\n}\n.login-code-img {\n  height: 38px;\n}\n.logo-wrapper {\n  position: fixed;\n  top: 30px;\n  left: 70px;\n}\n.logo img {\n  max-width: 153px;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyUA,SAAAA,QAAA;AACA,SAAAC,UAAA,EAAAC,YAAA,EAAAC,eAAA;AACA,OAAAC,OAAA;AACA,SAAAC,OAAA,EAAAC,OAAA;AACA,OAAAC,WAAA;AACA,SAAAC,UAAA,EAAAC,UAAA;AACA;EACAC,IAAA;EACAC,UAAA;IAAAJ,WAAA,EAAAA;EAAA;EACAK,IAAA,WAAAA,KAAA;IAAA,IAAAC,KAAA;IACA;IACA,IAAAC,UAAA,YAAAA,WAAAC,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,KAAAJ,KAAA,CAAAK,OAAA;QACAD,QAAA,KAAAE,KAAA;MACA;QACAF,QAAA;MACA;IACA;IACA;MACAG,OAAA;MACAC,cAAA;MACAC,SAAA;QACAC,QAAA;QACAC,QAAA;QACAC,UAAA;QACAC,IAAA;QACAC,IAAA;MACA;MACAC,UAAA;QACAL,QAAA,GACA;UAAAM,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAP,QAAA,GACA;UAAAK,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAL,IAAA,GACA;UAAAG,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAC,OAAA;MACAC,QAAA,EAAAC,SAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,KAAA;QACAC,QAAA,GACA;UAAAT,QAAA;UAAAE,OAAA;UAAAD,OAAA;QAAA,EACA;QACAS,OAAA,GACA;UAAAV,QAAA;UAAAE,OAAA;UAAAD,OAAA;QAAA,EACA;QACAU,UAAA,GACA;UAAAX,QAAA;UAAAE,OAAA;UAAAD,OAAA;QAAA,EACA;QACAW,QAAA,GACA;UAAAZ,QAAA;UAAAE,OAAA;UAAAD,OAAA;QAAA,EACA;QACAZ,OAAA,GACA;UACAwB,SAAA,EAAA5B,UAAA;UACAe,QAAA;UACAE,OAAA;UACAD,OAAA;QACA,EACA;QACAN,QAAA,GACA;UAAAK,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAY,SAAA,GACA;UAAAd,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAa,KAAA,GACA;UACAf,QAAA;UACAgB,IAAA;UACAd,OAAA;UACAD,OAAA;QACA,EACA;QACAgB,WAAA,GACA;UACAjB,QAAA;UACAkB,OAAA;UACAhB,OAAA;UACAD,OAAA;QACA,EACA;QACAkB,QAAA,GACA;UAAAnB,QAAA;UAAAE,OAAA;UAAAD,OAAA;QAAA,EACA;QACAmB,OAAA,GACA;UAAApB,QAAA;UAAAE,OAAA;UAAAD,OAAA;QAAA,EACA;QACAJ,IAAA,GACA;UAAAG,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACA;MACAmB,UAAA;MACA;MACAC,cAAA;MACA;MACAC,kBAAA;MACA;MACAC,WAAA;MACA;MACAC,IAAA;MACAC,mBAAA,EAAA/C,UAAA;MACAgD,WAAA;MACAC,GAAA,EAAAvB,SAAA;MACAhB,OAAA,EAAAgB,SAAA;MACAwB,OAAA,EAAAxB,SAAA;MACAyB,QAAA;MACAC,YAAA;MACAC,YAAA;QACAtC,QAAA;QACAC,QAAA;QACAE,IAAA;MACA;MACAoC,UAAA;QACAvC,QAAA,GACA;UACAM,QAAA;UACAkB,OAAA;UACAhB,OAAA;UACAD,OAAA;QACA,EACA;QACAN,QAAA,GACA;UAAAK,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAL,IAAA,GACA;UAAAG,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAgC,KAAA;IACAC,MAAA;MACAC,OAAA,WAAAA,QAAAC,KAAA;QACA,KAAAjC,QAAA,GAAAiC,KAAA,CAAAC,KAAA,IAAAD,KAAA,CAAAC,KAAA,CAAAlC,QAAA;MACA;MACAmC,SAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;IACA,KAAAC,SAAA;EACA;EACAC,OAAA;IACAF,OAAA,WAAAA,QAAA;MAAA,IAAAG,MAAA;MACAxE,UAAA,GAAAyE,IAAA,WAAAC,GAAA;QACAF,MAAA,CAAArD,OAAA,8BAAAuD,GAAA,CAAAlB,GAAA;QACAgB,MAAA,CAAAnD,SAAA,CAAAK,IAAA,GAAAgD,GAAA,CAAAhD,IAAA;MACA;IACA;IACA4C,SAAA,WAAAA,UAAA;MACA,IAAAhD,QAAA,GAAAnB,OAAA,CAAAwE,GAAA;MACA,IAAApD,QAAA,GAAApB,OAAA,CAAAwE,GAAA;MACA,IAAAnD,UAAA,GAAArB,OAAA,CAAAwE,GAAA;MACA,KAAAtD,SAAA;QACAC,QAAA,EAAAA,QAAA,KAAAW,SAAA,QAAAZ,SAAA,CAAAC,QAAA,GAAAA,QAAA;QACAC,QAAA,EACAA,QAAA,KAAAU,SAAA,QAAAZ,SAAA,CAAAE,QAAA,GAAAlB,OAAA,CAAAkB,QAAA;QACAC,UAAA,EAAAA,UAAA,KAAAS,SAAA,WAAA2C,OAAA,CAAApD,UAAA;MACA;IACA;IACAqD,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,CAAA1D,SAAA,CAAA2D,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAH,MAAA,CAAA/C,OAAA;UACA,IAAA+C,MAAA,CAAAzD,SAAA,CAAAG,UAAA;YACArB,OAAA,CAAA+E,GAAA,aAAAJ,MAAA,CAAAzD,SAAA,CAAAC,QAAA;cAAA6D,OAAA;YAAA;YACAhF,OAAA,CAAA+E,GAAA,aAAA9E,OAAA,CAAA0E,MAAA,CAAAzD,SAAA,CAAAE,QAAA;cACA4D,OAAA;YACA;YACAhF,OAAA,CAAA+E,GAAA,eAAAJ,MAAA,CAAAzD,SAAA,CAAAG,UAAA;cACA2D,OAAA;YACA;UACA;YACAhF,OAAA,CAAAiF,MAAA;YACAjF,OAAA,CAAAiF,MAAA;YACAjF,OAAA,CAAAiF,MAAA;UACA;UACAN,MAAA,CAAAO,MAAA,CACAC,QAAA,UAAAR,MAAA,CAAAzD,SAAA,EACAoD,IAAA;YACAK,MAAA,CAAAS,OAAA,CAAAC,IAAA;cAAAC,IAAA,EAAAX,MAAA,CAAA9C,QAAA;YAAA,GAAA0D,KAAA;UACA,GACAA,KAAA;YACAZ,MAAA,CAAA/C,OAAA;YACA+C,MAAA,CAAAT,OAAA;UACA;QACA;MACA;IACA;IACA;IACAsB,KAAA,WAAAA,MAAA;MACA,KAAAtC,IAAA;QACAuC,MAAA,EAAA3D,SAAA;QACAI,QAAA,EAAAJ,SAAA;QACAc,QAAA,EAAAd,SAAA;QACA4D,QAAA,EAAA5D,SAAA;QACAU,KAAA,EAAAV,SAAA;QACAY,WAAA,EAAAZ,SAAA;QACA6D,GAAA;QACAC,MAAA,EAAA9D,SAAA;QACAV,QAAA,EAAAU,SAAA;QACAK,OAAA,EAAAL,SAAA;QACAM,UAAA,EAAAN,SAAA;QACA+D,aAAA,EAAA/D,SAAA;QACAO,QAAA,EAAAP,SAAA;QACAe,OAAA,EAAAf,SAAA;QACAgE,MAAA,EAAAhE;MACA;MACA,KAAAuB,GAAA,GAAAvB,SAAA;MACA,KAAAhB,OAAA,GAAAgB,SAAA;MACA,KAAAwB,OAAA,GAAAxB,SAAA;MACA,KAAAiE,SAAA;IACA;IACA;IACAC,MAAA,WAAAA,OAAA;MACA,KAAAhE,IAAA;MACA,KAAAwD,KAAA;IACA;IACA,aACAS,SAAA,WAAAA,UAAA;MACA,KAAAT,KAAA;MACA;MACA,KAAAxD,IAAA;MACA,KAAAD,KAAA;IACA;IACAmE,UAAA,WAAAA,WAAA;MACA;MACA,KAAA3C,QAAA;IACA;IACA;IACA4C,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,SAAAlD,IAAA,CAAA9B,QAAA,SAAA8B,IAAA,CAAAX,SAAA;QACA,KAAA8D,QAAA,CAAAC,KAAA;QACA;MACA;MACA;MACA;;MAEA,KAAA1B,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAyB,KAAA,OAAAC,QAAA;UACAD,KAAA,CAAAE,MAAA,SAAAL,MAAA,CAAA9C,OAAA;UACAiD,KAAA,CAAAE,MAAA,aAAAL,MAAA,CAAAlD,IAAA,CAAAhB,QAAA;UACAqE,KAAA,CAAAE,MAAA,aAAAL,MAAA,CAAAlD,IAAA,CAAAN,QAAA;UACA2D,KAAA,CAAAE,MAAA,UAAAL,MAAA,CAAAlD,IAAA,CAAAV,KAAA;UACA+D,KAAA,CAAAE,MAAA,gBAAAL,MAAA,CAAAlD,IAAA,CAAAR,WAAA;UACA6D,KAAA,CAAAE,MAAA,aAAAL,MAAA,CAAAlD,IAAA,CAAA9B,QAAA;UACAmF,KAAA,CAAAE,MAAA,YAAAL,MAAA,CAAAlD,IAAA,CAAAf,OAAA;UACAoE,KAAA,CAAAE,MAAA,eAAAL,MAAA,CAAAlD,IAAA,CAAAd,UAAA;UACAmE,KAAA,CAAAE,MAAA,aAAAL,MAAA,CAAAlD,IAAA,CAAAb,QAAA;UACAkE,KAAA,CAAAE,MAAA,YAAAL,MAAA,CAAAlD,IAAA,CAAAL,OAAA;UACA0D,KAAA,CAAAE,MAAA,WAAAL,MAAA,CAAAlD,IAAA,CAAA4C,MAAA;UACA;UACAlG,QAAA,CAAA2G,KAAA,EAAAjC,IAAA,WAAAoC,QAAA;YACAN,MAAA,CAAAO,UAAA;YACAP,MAAA,CAAApE,IAAA;YACAoE,MAAA,CAAAZ,KAAA;UACA;QACA;MACA;IACA;IACAoB,gBAAA,WAAAA,iBAAAhG,KAAA;MACA,KAAAwC,WAAA,GAAAxC,KAAA;MACA,IAAAiG,GAAA;MACAjG,KAAA,CAAAkG,OAAA,WAAAC,IAAA;QACAF,GAAA,IAAAxG,UAAA,CAAA0G,IAAA;MACA;MACA,IAAAF,GAAA,CAAAG,MAAA;QACAH,GAAA,GAAAA,GAAA,CAAAI,SAAA,IAAAJ,GAAA,CAAAG,MAAA;QACA,KAAA9D,IAAA,CAAAb,QAAA,GAAAwE,GAAA;MACA;QACA,KAAA3D,IAAA,CAAAb,QAAA,GAAAP,SAAA;MACA;IACA;IACAoF,MAAA,WAAAA,OAAAC,KAAA;MACA,IAAAC,IAAA,GAAAD,KAAA,CAAAE,GAAA;MACA,IAAAC,KAAA,GAAAF,IAAA,CAAA3E,IAAA;MACA,IAAA8E,KAAA,GAAAH,IAAA,CAAA3E,IAAA;MACA,IAAA+E,MAAA,GAAAJ,IAAA,CAAA3E,IAAA;MACA,IAAAgF,MAAA,GAAAL,IAAA,CAAAM,IAAA;MAEA,KAAAJ,KAAA,KAAAC,KAAA,KAAAC,MAAA;QACA,KAAAnB,QAAA,CAAAC,KAAA;MACA,YAAAmB,MAAA;QACA,KAAApB,QAAA,CAAAC,KAAA;MACA;QACA,KAAAhD,OAAA,GAAA8D,IAAA;QACA,KAAAtG,OAAA,GAAAsG,IAAA,CAAA9G,IAAA;QACA,IAAAqH,IAAA;QACA;QACA,IAAAC,MAAA,OAAAC,UAAA;QACA;QACAD,MAAA,CAAAE,MAAA,aAAAC,CAAA;UACA;UACAJ,IAAA,CAAAtE,GAAA,GAAA0E,CAAA,CAAAC,MAAA,CAAAC,MAAA;QACA;QACAL,MAAA,CAAAM,aAAA,CAAAd,IAAA;MACA;MACA,KAAAxC,KAAA,CAAA1B,IAAA,CAAAiF,aAAA;IACA;IACAC,WAAA,WAAAA,YAAA;MACA,KAAA9E,OAAA,GAAAxB,SAAA;MACA,KAAAuB,GAAA,GAAAvB,SAAA;MACA,KAAAhB,OAAA,GAAAgB,SAAA;IACA;IACAuG,YAAA,WAAAA,aAAA;MACA,IAAAC,IAAA;MACA,SAAA7E,YAAA,CAAAtC,QAAA;QACA,KAAAkF,QAAA,CAAAC,KAAA;QACA;MACA;MAEAxG,YAAA,MAAA2D,YAAA,EAAAa,IAAA,WAAAiE,IAAA;QACAD,IAAA,CAAA9E,YAAA;QACAgF,UAAA;UACAF,IAAA,CAAA9E,YAAA;QACA;MACA;IACA;IACAiF,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,IAAAJ,IAAA;MACA,KAAA1D,KAAA,iBAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IACA4D,MAAA,CAAAjF,YAAA,CAAArC,QAAA,UACAsH,MAAA,CAAAjF,YAAA,CAAArC,QAAA,IAAAsH,MAAA,CAAAjF,YAAA,CAAAlB,SAAA,EACA;YACAmG,MAAA,CAAArC,QAAA,CAAAC,KAAA;YACA;UACA;UACA,IAAAoC,MAAA,CAAAjF,YAAA,CAAArC,QAAA,CAAA4F,MAAA;YACA0B,MAAA,CAAArC,QAAA,CAAAC,KAAA;YACA;UACA;UACAvG,eAAA,CAAA2I,MAAA,CAAAjF,YAAA,EAAAa,IAAA,WAAAiE,IAAA;YACAG,MAAA,CAAA/B,UAAA;YACA2B,IAAA,CAAA/E,QAAA;UACA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}