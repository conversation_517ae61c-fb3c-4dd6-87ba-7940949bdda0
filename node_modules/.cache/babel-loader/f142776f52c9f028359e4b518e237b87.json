{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/system/user/form.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/system/user/form.vue", "mtime": 1668865468000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldFVzZXIsIGFkZFVzZXIsIHVwZGF0ZVVzZXIgfSBmcm9tICJAL2FwaS9zeXN0ZW0vdXNlciI7CmltcG9ydCBUcmVlc2VsZWN0IGZyb20gIkByaW9waGFlL3Z1ZS10cmVlc2VsZWN0IjsKaW1wb3J0ICJAcmlvcGhhZS92dWUtdHJlZXNlbGVjdC9kaXN0L3Z1ZS10cmVlc2VsZWN0LmNzcyI7CmltcG9ydCBJbWFnZVVwbG9hZCBmcm9tICJAL2NvbXBvbmVudHMvSW1hZ2VVcGxvYWQiOwppbXBvcnQgZmxvd2FibGUgZnJvbSAnQC92aWV3cy9mbG93YWJsZS90YXNrL3JlY29yZC9pbmRleCc7CmltcG9ydCB7IHJlZ2lvbkRhdGEsIENvZGVUb1RleHQsIFRleHRUb0NvZGUgfSBmcm9tICJlbGVtZW50LWNoaW5hLWFyZWEtZGF0YSI7CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiVXNlciIsCiAgY29tcG9uZW50czogewogICAgVHJlZXNlbGVjdDogVHJlZXNlbGVjdCwKICAgIEltYWdlVXBsb2FkOiBJbWFnZVVwbG9hZCwKICAgIGZsb3dhYmxlOiBmbG93YWJsZQogIH0sCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIC8vIOmBrue9qeWxggogICAgICBsb2FkaW5nOiB0cnVlLAogICAgICAvLyDpgInkuK3mlbDnu4QKICAgICAgaWRzOiBbXSwKICAgICAgLy8g6Z2e5Y2V5Liq56aB55SoCiAgICAgIHNpbmdsZTogdHJ1ZSwKICAgICAgLy8g6Z2e5aSa5Liq56aB55SoCiAgICAgIG11bHRpcGxlOiB0cnVlLAogICAgICAvLyDmmL7npLrmkJzntKLmnaHku7YKICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwKICAgICAgLy8g5oC75p2h5pWwCiAgICAgIHRvdGFsOiAwLAogICAgICAvLyDnlKjmiLfooajmoLzmlbDmja4KICAgICAgdXNlckxpc3Q6IG51bGwsCiAgICAgIC8vIOW8ueWHuuWxguagh+mimAogICAgICB0aXRsZTogIiIsCiAgICAgIC8vIOmDqOmXqOagkemAiemhuQogICAgICBkZXB0T3B0aW9uczogdW5kZWZpbmVkLAogICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYIKICAgICAgb3BlbjogZmFsc2UsCiAgICAgIC8vIOmDqOmXqOWQjeensAogICAgICBkZXB0TmFtZTogdW5kZWZpbmVkLAogICAgICAvLyDpu5jorqTlr4bnoIEKICAgICAgaW5pdFBhc3N3b3JkOiB1bmRlZmluZWQsCiAgICAgIC8vIOaXpeacn+iMg+WbtAogICAgICBkYXRlUmFuZ2U6IFtdLAogICAgICAvLyDnirbmgIHmlbDmja7lrZflhbgKICAgICAgc3RhdHVzT3B0aW9uczogW10sCiAgICAgIC8vIOaAp+WIq+eKtuaAgeWtl+WFuAogICAgICBzZXhPcHRpb25zOiBbXSwKICAgICAgLy8g55+t5L+h6YCa55+l5a2X5YW4CiAgICAgIHNtc1NlbmRPcHRpb25zOiBbXSwKICAgICAgLy8g5a6h5qC454q25oCB5a2X5YW4CiAgICAgIGF1ZGl0U3RhdHVzT3B0aW9uczogW10sCiAgICAgIC8vIOinkuiJsumAiemhuQogICAgICByb2xlT3B0aW9uczogW10sCiAgICAgIC8vIOihqOWNleWPguaVsAogICAgICBmb3JtOiB7fSwKICAgICAgZGVmYXVsdFByb3BzOiB7CiAgICAgICAgY2hpbGRyZW46ICJjaGlsZHJlbiIsCiAgICAgICAgbGFiZWw6ICJsYWJlbCIKICAgICAgfSwKICAgICAgLy8g6KGo5Y2V5qCh6aqMCiAgICAgIHJ1bGVzOiB7CiAgICAgICAgdXNlck5hbWU6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICLnlKjmiLflkI3kuI3og73kuLrnqboiLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgfV0sCiAgICAgICAgY29tcGFueTogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogIuWFrOWPuOWFqOensOS4jeiDveS4uuepuiIsCiAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIKICAgICAgICB9XSwKICAgICAgICBidXNpbmVzc05vOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAi6JCl5Lia5omn54Wn5Y+356CB5LiN6IO95Li656m6IiwKICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgIH1dLAogICAgICAgIGJ1c2luZXNzTm9QaWM6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICLokKXkuJrmiafnhaflm77niYflv4XkvKAiCiAgICAgICAgfV0sCiAgICAgICAgZW1haWw6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIHR5cGU6ICJlbWFpbCIsCiAgICAgICAgICBtZXNzYWdlOiAiJ+ivt+i+k+WF<PERSON>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"}, {"version": 3, "names": ["getUser", "addUser", "updateUser", "Treeselect", "ImageUpload", "flowable", "regionData", "CodeToText", "TextToCode", "name", "components", "data", "loading", "ids", "single", "multiple", "showSearch", "total", "userList", "title", "deptOptions", "undefined", "open", "deptName", "initPassword", "date<PERSON><PERSON><PERSON>", "statusOptions", "sexOptions", "smsSendOptions", "auditStatusOptions", "roleOptions", "form", "defaultProps", "children", "label", "rules", "userName", "required", "message", "trigger", "company", "businessNo", "businessNoPic", "email", "type", "phonenumber", "pattern", "nick<PERSON><PERSON>", "province", "address", "auditStatusTree", "provinceAndCityData", "cityOptions", "queryArea", "finished", "taskId", "procInsId", "businessKey", "audit", "formEdit", "mounted", "reset", "created", "_this", "$route", "query", "edit", "getUserInfo", "then", "response", "roles", "password", "getDicts", "getConfigKey", "msg", "opt", "push", "id", "for<PERSON>ach", "elem", "index", "obj", "dict<PERSON><PERSON>l", "dict<PERSON><PERSON>ue", "auditStatusTrees", "methods", "cancel", "userId", "deptId", "userType", "sex", "avatar", "status", "delFlag", "loginIp", "loginDate", "createBy", "createTime", "updateBy", "updateTime", "remark", "dealer", "smsSend", "auditStatus", "roleIds", "resetForm", "handleSelectionChange", "selection", "map", "item", "length", "_this2", "postOptions", "posts", "postIds", "provinces", "split", "citys", "code", "submitForm", "_this3", "that", "$refs", "validate", "valid", "$loading", "lock", "text", "spinner", "background", "target", "document", "querySelector", "documentElement", "style", "overflowY", "taskComplete", "startFlow", "setTimeout", "close", "catch", "res", "console", "log", "handleCityChange", "value", "district", "txt", "substring", "handleComplete", "handleReturn", "goBack", "$store", "dispatch", "$router", "go", "variables", "PROCESS_AREA", "state", "user", "includes", "isManage", "BUSINESSKEY"], "sources": ["src/views/system/user/form.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-card class=\"box-card\" >\n        <div slot=\"header\" class=\"clearfix\">\n          <span class=\"el-icon-document\">用户审核流程</span>\n          <span style=\"float: right;\">\n            <el-button icon=\"el-icon-edit-outline\" type=\"success\" v-if=\"audit\" @click=\"handleComplete\">审批</el-button>\n            <el-button icon=\"el-icon-refresh-left\" type=\"warning\" v-if=\"audit\" @click=\"handleReturn\">退回</el-button>\n            <el-button type=\"primary\" @click=\"goBack\">返回</el-button>\n          </span>\n        </div>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" :disabled=\"!formEdit\" label-width=\"120px\">\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"用户账号\" prop=\"userName\">\n              <el-input v-model=\"form.userName\" placeholder=\"请输入用户账号\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"报备人姓名\" prop=\"nickName\">\n              <el-input\n                v-model=\"form.nickName\"\n                placeholder=\"请输入报备人姓名\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"角色\">\n              <el-select v-model=\"form.roleIds\" multiple placeholder=\"请选择\">\n                <el-option\n                  v-for=\"item in roleOptions\"\n                  :key=\"item.roleId\"\n                  :label=\"item.roleName\"\n                  :value=\"item.roleId\"\n                  :disabled=\"item.status == 1\"\n                ></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"资料接收邮箱\" prop=\"email\">\n              <el-input\n                v-model=\"form.email\"\n                placeholder=\"请输入资料接收邮箱\"\n                maxlength=\"50\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"报备人电话\" prop=\"phonenumber\">\n              <el-input\n                v-model=\"form.phonenumber\"\n                placeholder=\"请输入报备人电话\"\n                maxlength=\"11\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"用户性别\">\n              <el-select v-model=\"form.sex\" placeholder=\"请选择\">\n                <el-option\n                  v-for=\"dict in sexOptions\"\n                  :key=\"dict.dictValue\"\n                  :label=\"dict.dictLabel\"\n                  :value=\"dict.dictValue\"\n                ></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <!-- <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"头像地址\">\n              <imageUpload v-model=\"form.avatar\" />\n            </el-form-item>\n          </el-col>\n           <el-col :span=\"12\">\n            <el-form-item label=\"密码\" prop=\"password\">\n              <el-input\n                v-model=\"form.password\"\n                placeholder=\"请输入密码\"\n                type=\"password\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row> -->\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"帐号状态\">\n              <el-radio-group v-model=\"form.status\">\n                <el-radio\n                  v-for=\"dict in statusOptions\"\n                  :key=\"dict.dictValue\"\n                  :label=\"dict.dictValue\"\n                  >{{ dict.dictLabel }}</el-radio\n                >\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"备注\" prop=\"remark\">\n              <el-input\n                v-model=\"form.remark\"\n                type=\"textarea\"\n                placeholder=\"请输入内容\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"公司全称\" prop=\"company\">\n              <el-input v-model=\"form.company\" placeholder=\"请输入公司全称\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"营业执照号码\" prop=\"businessNo\">\n              <el-input\n                v-model=\"form.businessNo\"\n                placeholder=\"请输入营业执照号码\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"营业执照图片\" prop=\"businessNoPic\" :required=\"true\" error=\"营业执照图片必传\">\n              <imageUpload v-model=\"form.businessNoPic\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            \n            <el-form-item label=\"所在区域\" prop=\"province\">\n              <el-cascader\n                :options=\"provinceAndCityData\"\n                clearable\n                :props=\"{ expandTrigger: 'hover' }\"\n                v-model=\"cityOptions\"\n                @change=\"handleCityChange\"\n              >\n              </el-cascader>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"资料邮寄地址\" prop=\"address\">\n              <el-input\n                v-model=\"form.address\"\n                placeholder=\"请输入资料邮寄地址\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"隶属经销商\" prop=\"dealer\">\n              <el-input v-model=\"form.dealer\" placeholder=\"请输入隶属经销商\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"短信通知\">\n              <el-radio-group v-model=\"form.smsSend\">\n                <el-radio\n                  v-for=\"dict in smsSendOptions\"\n                  :key=\"dict.dictValue\"\n                  :label=\"dict.dictValue\"\n                  >{{ dict.dictLabel }}</el-radio\n                >\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n          <!-- <el-col :span=\"12\">\n            <el-form-item label=\"审核状态\">\n              <el-radio-group v-model=\"form.auditStatus\">\n                <el-radio\n                  v-for=\"dict in auditStatusOptions\"\n                  :key=\"dict.dictValue\"\n                  :label=\"dict.dictValue\"\n                  >{{ dict.dictLabel }}</el-radio\n                >\n              </el-radio-group>\n            </el-form-item>\n          </el-col> -->\n        </el-row>\n      </el-form>\n      <el-col :span=\"16\" :offset=\"8\" v-if=\"formEdit\" >\n        <div style=\"margin-left:10%;margin-bottom: 20px;font-size: 14px;\">\n          <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n          <el-button @click=\"goBack\">取 消</el-button>\n        </div>\n      </el-col>\n    </el-card>\n    <flowable ref=\"flow\" :key=\"businessKey\" procDefKey=\"process_user_reg\" :procInsId=\"procInsId\" :taskId=\"taskId\" :finished=\"finished\"></flowable>\n\n  </div>\n</template>\n\n<script>\nimport {\n  getUser,\n  addUser,\n  updateUser\n} from \"@/api/system/user\";\nimport Treeselect from \"@riophae/vue-treeselect\";\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\";\nimport ImageUpload from \"@/components/ImageUpload\";\nimport flowable from '@/views/flowable/task/record/index'\nimport { regionData,CodeToText, TextToCode } from \"element-china-area-data\";\nexport default {\n  name: \"User\",\n  components: { Treeselect, ImageUpload, flowable },\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 用户表格数据\n      userList: null,\n      // 弹出层标题\n      title: \"\",\n      // 部门树选项\n      deptOptions: undefined,\n      // 是否显示弹出层\n      open: false,\n      // 部门名称\n      deptName: undefined,\n      // 默认密码\n      initPassword: undefined,\n      // 日期范围\n      dateRange: [],\n      // 状态数据字典\n      statusOptions: [],\n      // 性别状态字典\n      sexOptions: [],\n      // 短信通知字典\n      smsSendOptions: [],\n      // 审核状态字典\n      auditStatusOptions: [],\n      // 角色选项\n      roleOptions: [],\n      // 表单参数\n      form: {},\n      defaultProps: {\n        children: \"children\",\n        label: \"label\",\n      },\n      // 表单校验\n      rules: {\n        userName: [\n          { required: true, message: \"用户名不能为空\", trigger: \"blur\" },\n        ],\n        company: [\n          { required: true, message: \"公司全称不能为空\", trigger: \"blur\" },\n        ],\n        businessNo: [\n          { required: true, message: \"营业执照号码不能为空\", trigger: \"blur\" },\n        ],\n        businessNoPic: [\n          { required: true, message: \"营业执照图片必传\" },\n        ],\n        email: [\n          {\n            required: true,\n            type: \"email\",\n            message: \"'请输入正确的邮箱地址\",\n            trigger: [\"blur\", \"change\"],\n          },\n        ],\n        phonenumber: [\n          {\n            required: true,\n            pattern: /^1[3|4|5|6|7|8|9][0-9]\\d{8}$/,\n            message: \"请输入正确的手机号码\",\n            trigger: \"blur\",\n          },\n        ],\n        nickName: [\n          { required: true, message: \"报备人姓名不能为空\", trigger: \"blur\" },\n        ],\n        province: [\n          { required: true, message: \"所在区域不能为空\", trigger: \"blur\" },\n        ],\n        address: [\n          { required: true, message: \"资料邮寄地址不能为空\", trigger: \"blur\" },\n        ],\n      },\n      auditStatusTree: [],\n      provinceAndCityData: regionData,\n      cityOptions: [],\n      queryArea: [],\n      //工作流参数\n      finished: 'false',\n      taskId: undefined,\n      procInsId: undefined,\n      businessKey: undefined,\n      audit: false,\n      formEdit: false\n      //工作流参数end\n    };\n  },\n  mounted(){\n    this.reset();\n  },\n  created() {\n    this.taskId  = this.$route.query && this.$route.query.taskId;\n    this.procInsId = this.$route.query && this.$route.query.procInsId;\n    this.finished =  this.$route.query && this.$route.query.finished;\n    this.businessKey = this.$route.query && this.$route.query.businessKey;\n    let edit =  this.$route.query && this.$route.query.formEdit;\n    if(edit == \"true\"){\n      this.formEdit = true;\n    }else{\n      this.formEdit = false;\n    }\n    if(this.businessKey){\n      this.getUserInfo(this.businessKey);\n      if(this.finished == \"false\" && !this.formEdit){\n        this.audit = true;\n      }\n    }else{\n      getUser().then((response) => {\n        this.roleOptions = response.roles;\n        this.form.password = this.initPassword;\n      });\n    }\n\n    this.getDicts(\"sys_normal_disable\").then((response) => {\n      this.statusOptions = response.data;\n    });\n    this.getDicts(\"sys_user_sex\").then((response) => {\n      this.sexOptions = response.data;\n    });\n    this.getConfigKey(\"sys.user.initPassword\").then((response) => {\n      this.initPassword = response.msg;\n    });\n    this.getDicts(\"pr_sms_notify\").then((response) => {\n      this.smsSendOptions = response.data;\n    });\n    this.getDicts(\"pr_audit_status\").then((response) => {\n      this.auditStatusOptions = response.data;\n      var opt = [];\n      opt.push({id: 0, label:'全部'})\n      response.data.forEach((elem, index) => {\n        var obj = {};\n        obj.label = elem.dictLabel;\n        obj.id = elem.dictValue;\n        opt.push(obj);\n      });\n      var auditStatusTree = {};\n      auditStatusTree.label = \"审核状态\";\n      auditStatusTree.children = opt;\n      var auditStatusTrees = [];\n      auditStatusTrees.push(auditStatusTree);\n      this.auditStatusTree = auditStatusTrees;\n    });\n  },\n  methods: {\n    // 取消按钮\n    cancel() {\n      this.reset();\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        userId: undefined,\n        deptId: undefined,\n        userName: undefined,\n        nickName: undefined,\n        userType: undefined,\n        email: undefined,\n        phonenumber: undefined,\n        sex: \"0\",\n        avatar: undefined,\n        password: undefined,\n        status: \"0\",\n        delFlag: undefined,\n        loginIp: undefined,\n        loginDate: undefined,\n        createBy: undefined,\n        createTime: undefined,\n        updateBy: undefined,\n        updateTime: undefined,\n        remark: undefined,\n        company: undefined,\n        businessNo: undefined,\n        businessNoPic: undefined,\n        province: undefined,\n        address: undefined,\n        dealer: undefined,\n        smsSend: \"0\",\n        auditStatus: \"1\",\n        roleIds: [],\n      };\n      this.resetForm(\"form\");\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map((item) => item.userId);\n      this.single = selection.length != 1;\n      this.multiple = !selection.length;\n    },\n    /** 修改按钮操作 */\n    getUserInfo(userId) {\n      getUser(userId).then((response) => {\n        this.form = response.data;\n        this.postOptions = response.posts;\n        this.roleOptions = response.roles;\n        this.form.postIds = response.postIds;\n        this.form.roleIds = response.roleIds;\n        this.form.password = \"\";\n        var provinces = response.data.province;\n        if(provinces.length > 0){\n          var address = provinces.split(\"/\");\n          var citys = [];\n          // 省份\n          if(address.length > 0)\n          citys.push(TextToCode[address[0]].code);\n          // 城市\n          if(address.length > 1)\n          citys.push(TextToCode[address[0]][address[1]].code);\n          // 地区\n          if(address.length > 2)\n          citys.push(TextToCode[address[0]][address[1]][address[2]].code);\n          \n          this.cityOptions = citys;\n        }\n      });\n    },\n\n    /** 提交按钮 */\n    submitForm: function () {\n      let that = this;\n      // if(this.cityOptions.length < 1){\n        \n      // }\n      this.$refs[\"form\"].validate((valid) => {\n        if (valid) {\n          const loading = this.$loading({\n            lock: true,//lock的修改符--默认是false\n            text: 'Loading',//显示在加载图标下方的加载文案\n            spinner: 'el-icon-loading',//自定义加载图标类名\n            background: 'rgba(0, 0, 0, 0.7)',//遮罩层颜色\n            target: document.querySelector('.main-container')//loadin覆盖的dom元素节点\n          }); \n          document.documentElement.style.overflowY = 'hidden' //禁止底层div滚动\n          if (that.form.userId != undefined) {\n            updateUser(that.form).then((response) => {\n              if(that.businessKey){\n                that.$refs['flow'].taskComplete(\"重新提交\");\n              }else{\n                that.startFlow(that.form);\n              }\n              setTimeout(() => {\n                loading.close();\n                document.documentElement.style.overflowY = 'auto' //允许底层div滚动\n              }, 2000);\n            }).catch(res=>{\n              console.log(res)\n              loading.close();\n              document.documentElement.style.overflowY = 'auto' //允许底层div滚动\n            });\n          } else {\n            that.form.auditStatus = 1;\n            addUser(that.form).then((response) => {\n              that.form.userId = response.data;\n              that.startFlow(that.form)\n              setTimeout(() => {\n                loading.close();\n                document.documentElement.style.overflowY = 'auto' //允许底层div滚动\n              }, 2000);\n            }).catch(res=>{\n              console.log(res)\n              loading.close();\n              document.documentElement.style.overflowY = 'auto' //允许底层div滚动\n            });\n          }\n        }\n      });\n    },\n    handleCityChange(value) {\n      if(!value || value.length == 0 ){\n        this.cityOptions = null;\n        this.form.province = undefined;\n        this.form.district = undefined;\n        return\n      }\n      this.cityOptions = value\n      var txt = \"\";\n      value.forEach(function (item) {\n          txt += CodeToText[item]+\"/\"\n      });\n      if(txt.length > 1){\n        txt = txt.substring(0, txt.length-1);\n        this.form.province=txt;\n        this.form.district = CodeToText[value[0]];\n      }else{\n        this.form.province=undefined;\n        this.form.district = undefined;\n      }\n      \n    },\n    /** 审批 */\n    handleComplete(){\n      this.$refs['flow'].handleComplete();\n    },\n    /** 退回 */\n    handleReturn(){\n      this.$refs['flow'].handleReturn();\n    },\n    /** 返回页面 */\n    goBack() {\n      // 关闭当前标签页并返回上个页面\n      this.$store.dispatch(\"tagsView/delView\", this.$route);\n      this.$router.go(-1)\n    },\n    startFlow(form){\n      var variables = {};\n      variables.PROCESS_AREA = form.district;\n      //是否省负责人角色\n      if(this.$store.state.user.roles && this.$store.state.user.roles.includes(\"province_admin\")){\n        variables.isManage = 1;\n      }else{\n        variables.isManage = 0;\n      }\n      variables.BUSINESSKEY = form.userId;            \n      this.$refs['flow'].startFlow(form.userId, \"用户审批\", variables);\n    }\n  },\n};\n</script>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2MA,SACAA,OAAA,EACAC,OAAA,EACAC,UAAA,QACA;AACA,OAAAC,UAAA;AACA;AACA,OAAAC,WAAA;AACA,OAAAC,QAAA;AACA,SAAAC,UAAA,EAAAC,UAAA,EAAAC,UAAA;AACA;EACAC,IAAA;EACAC,UAAA;IAAAP,UAAA,EAAAA,UAAA;IAAAC,WAAA,EAAAA,WAAA;IAAAC,QAAA,EAAAA;EAAA;EACAM,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,QAAA;MACA;MACAC,KAAA;MACA;MACAC,WAAA,EAAAC,SAAA;MACA;MACAC,IAAA;MACA;MACAC,QAAA,EAAAF,SAAA;MACA;MACAG,YAAA,EAAAH,SAAA;MACA;MACAI,SAAA;MACA;MACAC,aAAA;MACA;MACAC,UAAA;MACA;MACAC,cAAA;MACA;MACAC,kBAAA;MACA;MACAC,WAAA;MACA;MACAC,IAAA;MACAC,YAAA;QACAC,QAAA;QACAC,KAAA;MACA;MACA;MACAC,KAAA;QACAC,QAAA,GACA;UAAAC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAC,OAAA,GACA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAE,UAAA,GACA;UAAAJ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAG,aAAA,GACA;UAAAL,QAAA;UAAAC,OAAA;QAAA,EACA;QACAK,KAAA,GACA;UACAN,QAAA;UACAO,IAAA;UACAN,OAAA;UACAC,OAAA;QACA,EACA;QACAM,WAAA,GACA;UACAR,QAAA;UACAS,OAAA;UACAR,OAAA;UACAC,OAAA;QACA,EACA;QACAQ,QAAA,GACA;UAAAV,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAS,QAAA,GACA;UAAAX,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAU,OAAA,GACA;UAAAZ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAW,eAAA;MACAC,mBAAA,EAAA7C,UAAA;MACA8C,WAAA;MACAC,SAAA;MACA;MACAC,QAAA;MACAC,MAAA,EAAAlC,SAAA;MACAmC,SAAA,EAAAnC,SAAA;MACAoC,WAAA,EAAApC,SAAA;MACAqC,KAAA;MACAC,QAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,KAAA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA,KAAAR,MAAA,QAAAS,MAAA,CAAAC,KAAA,SAAAD,MAAA,CAAAC,KAAA,CAAAV,MAAA;IACA,KAAAC,SAAA,QAAAQ,MAAA,CAAAC,KAAA,SAAAD,MAAA,CAAAC,KAAA,CAAAT,SAAA;IACA,KAAAF,QAAA,QAAAU,MAAA,CAAAC,KAAA,SAAAD,MAAA,CAAAC,KAAA,CAAAX,QAAA;IACA,KAAAG,WAAA,QAAAO,MAAA,CAAAC,KAAA,SAAAD,MAAA,CAAAC,KAAA,CAAAR,WAAA;IACA,IAAAS,IAAA,QAAAF,MAAA,CAAAC,KAAA,SAAAD,MAAA,CAAAC,KAAA,CAAAN,QAAA;IACA,IAAAO,IAAA;MACA,KAAAP,QAAA;IACA;MACA,KAAAA,QAAA;IACA;IACA,SAAAF,WAAA;MACA,KAAAU,WAAA,MAAAV,WAAA;MACA,SAAAH,QAAA,qBAAAK,QAAA;QACA,KAAAD,KAAA;MACA;IACA;MACA1D,OAAA,GAAAoE,IAAA,WAAAC,QAAA;QACAN,KAAA,CAAAjC,WAAA,GAAAuC,QAAA,CAAAC,KAAA;QACAP,KAAA,CAAAhC,IAAA,CAAAwC,QAAA,GAAAR,KAAA,CAAAvC,YAAA;MACA;IACA;IAEA,KAAAgD,QAAA,uBAAAJ,IAAA,WAAAC,QAAA;MACAN,KAAA,CAAArC,aAAA,GAAA2C,QAAA,CAAA1D,IAAA;IACA;IACA,KAAA6D,QAAA,iBAAAJ,IAAA,WAAAC,QAAA;MACAN,KAAA,CAAApC,UAAA,GAAA0C,QAAA,CAAA1D,IAAA;IACA;IACA,KAAA8D,YAAA,0BAAAL,IAAA,WAAAC,QAAA;MACAN,KAAA,CAAAvC,YAAA,GAAA6C,QAAA,CAAAK,GAAA;IACA;IACA,KAAAF,QAAA,kBAAAJ,IAAA,WAAAC,QAAA;MACAN,KAAA,CAAAnC,cAAA,GAAAyC,QAAA,CAAA1D,IAAA;IACA;IACA,KAAA6D,QAAA,oBAAAJ,IAAA,WAAAC,QAAA;MACAN,KAAA,CAAAlC,kBAAA,GAAAwC,QAAA,CAAA1D,IAAA;MACA,IAAAgE,GAAA;MACAA,GAAA,CAAAC,IAAA;QAAAC,EAAA;QAAA3C,KAAA;MAAA;MACAmC,QAAA,CAAA1D,IAAA,CAAAmE,OAAA,WAAAC,IAAA,EAAAC,KAAA;QACA,IAAAC,GAAA;QACAA,GAAA,CAAA/C,KAAA,GAAA6C,IAAA,CAAAG,SAAA;QACAD,GAAA,CAAAJ,EAAA,GAAAE,IAAA,CAAAI,SAAA;QACAR,GAAA,CAAAC,IAAA,CAAAK,GAAA;MACA;MACA,IAAA/B,eAAA;MACAA,eAAA,CAAAhB,KAAA;MACAgB,eAAA,CAAAjB,QAAA,GAAA0C,GAAA;MACA,IAAAS,gBAAA;MACAA,gBAAA,CAAAR,IAAA,CAAA1B,eAAA;MACAa,KAAA,CAAAb,eAAA,GAAAkC,gBAAA;IACA;EACA;EACAC,OAAA;IACA;IACAC,MAAA,WAAAA,OAAA;MACA,KAAAzB,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAA9B,IAAA;QACAwD,MAAA,EAAAlE,SAAA;QACAmE,MAAA,EAAAnE,SAAA;QACAe,QAAA,EAAAf,SAAA;QACA0B,QAAA,EAAA1B,SAAA;QACAoE,QAAA,EAAApE,SAAA;QACAsB,KAAA,EAAAtB,SAAA;QACAwB,WAAA,EAAAxB,SAAA;QACAqE,GAAA;QACAC,MAAA,EAAAtE,SAAA;QACAkD,QAAA,EAAAlD,SAAA;QACAuE,MAAA;QACAC,OAAA,EAAAxE,SAAA;QACAyE,OAAA,EAAAzE,SAAA;QACA0E,SAAA,EAAA1E,SAAA;QACA2E,QAAA,EAAA3E,SAAA;QACA4E,UAAA,EAAA5E,SAAA;QACA6E,QAAA,EAAA7E,SAAA;QACA8E,UAAA,EAAA9E,SAAA;QACA+E,MAAA,EAAA/E,SAAA;QACAmB,OAAA,EAAAnB,SAAA;QACAoB,UAAA,EAAApB,SAAA;QACAqB,aAAA,EAAArB,SAAA;QACA2B,QAAA,EAAA3B,SAAA;QACA4B,OAAA,EAAA5B,SAAA;QACAgF,MAAA,EAAAhF,SAAA;QACAiF,OAAA;QACAC,WAAA;QACAC,OAAA;MACA;MACA,KAAAC,SAAA;IACA;IACA;IACAC,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAA9F,GAAA,GAAA8F,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAtB,MAAA;MAAA;MACA,KAAAzE,MAAA,GAAA6F,SAAA,CAAAG,MAAA;MACA,KAAA/F,QAAA,IAAA4F,SAAA,CAAAG,MAAA;IACA;IACA,aACA3C,WAAA,WAAAA,YAAAoB,MAAA;MAAA,IAAAwB,MAAA;MACA/G,OAAA,CAAAuF,MAAA,EAAAnB,IAAA,WAAAC,QAAA;QACA0C,MAAA,CAAAhF,IAAA,GAAAsC,QAAA,CAAA1D,IAAA;QACAoG,MAAA,CAAAC,WAAA,GAAA3C,QAAA,CAAA4C,KAAA;QACAF,MAAA,CAAAjF,WAAA,GAAAuC,QAAA,CAAAC,KAAA;QACAyC,MAAA,CAAAhF,IAAA,CAAAmF,OAAA,GAAA7C,QAAA,CAAA6C,OAAA;QACAH,MAAA,CAAAhF,IAAA,CAAAyE,OAAA,GAAAnC,QAAA,CAAAmC,OAAA;QACAO,MAAA,CAAAhF,IAAA,CAAAwC,QAAA;QACA,IAAA4C,SAAA,GAAA9C,QAAA,CAAA1D,IAAA,CAAAqC,QAAA;QACA,IAAAmE,SAAA,CAAAL,MAAA;UACA,IAAA7D,OAAA,GAAAkE,SAAA,CAAAC,KAAA;UACA,IAAAC,KAAA;UACA;UACA,IAAApE,OAAA,CAAA6D,MAAA,MACAO,KAAA,CAAAzC,IAAA,CAAApE,UAAA,CAAAyC,OAAA,KAAAqE,IAAA;UACA;UACA,IAAArE,OAAA,CAAA6D,MAAA,MACAO,KAAA,CAAAzC,IAAA,CAAApE,UAAA,CAAAyC,OAAA,KAAAA,OAAA,KAAAqE,IAAA;UACA;UACA,IAAArE,OAAA,CAAA6D,MAAA,MACAO,KAAA,CAAAzC,IAAA,CAAApE,UAAA,CAAAyC,OAAA,KAAAA,OAAA,KAAAA,OAAA,KAAAqE,IAAA;UAEAP,MAAA,CAAA3D,WAAA,GAAAiE,KAAA;QACA;MACA;IACA;IAEA;IACAE,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,IAAA;MACA;;MAEA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAhH,OAAA,GAAA4G,MAAA,CAAAK,QAAA;YACAC,IAAA;YAAA;YACAC,IAAA;YAAA;YACAC,OAAA;YAAA;YACAC,UAAA;YAAA;YACAC,MAAA,EAAAC,QAAA,CAAAC,aAAA;UACA;UACAD,QAAA,CAAAE,eAAA,CAAAC,KAAA,CAAAC,SAAA;UACA,IAAAd,IAAA,CAAA1F,IAAA,CAAAwD,MAAA,IAAAlE,SAAA;YACAnB,UAAA,CAAAuH,IAAA,CAAA1F,IAAA,EAAAqC,IAAA,WAAAC,QAAA;cACA,IAAAoD,IAAA,CAAAhE,WAAA;gBACAgE,IAAA,CAAAC,KAAA,SAAAc,YAAA;cACA;gBACAf,IAAA,CAAAgB,SAAA,CAAAhB,IAAA,CAAA1F,IAAA;cACA;cACA2G,UAAA;gBACA9H,OAAA,CAAA+H,KAAA;gBACAR,QAAA,CAAAE,eAAA,CAAAC,KAAA,CAAAC,SAAA;cACA;YACA,GAAAK,KAAA,WAAAC,GAAA;cACAC,OAAA,CAAAC,GAAA,CAAAF,GAAA;cACAjI,OAAA,CAAA+H,KAAA;cACAR,QAAA,CAAAE,eAAA,CAAAC,KAAA,CAAAC,SAAA;YACA;UACA;YACAd,IAAA,CAAA1F,IAAA,CAAAwE,WAAA;YACAtG,OAAA,CAAAwH,IAAA,CAAA1F,IAAA,EAAAqC,IAAA,WAAAC,QAAA;cACAoD,IAAA,CAAA1F,IAAA,CAAAwD,MAAA,GAAAlB,QAAA,CAAA1D,IAAA;cACA8G,IAAA,CAAAgB,SAAA,CAAAhB,IAAA,CAAA1F,IAAA;cACA2G,UAAA;gBACA9H,OAAA,CAAA+H,KAAA;gBACAR,QAAA,CAAAE,eAAA,CAAAC,KAAA,CAAAC,SAAA;cACA;YACA,GAAAK,KAAA,WAAAC,GAAA;cACAC,OAAA,CAAAC,GAAA,CAAAF,GAAA;cACAjI,OAAA,CAAA+H,KAAA;cACAR,QAAA,CAAAE,eAAA,CAAAC,KAAA,CAAAC,SAAA;YACA;UACA;QACA;MACA;IACA;IACAS,gBAAA,WAAAA,iBAAAC,KAAA;MACA,KAAAA,KAAA,IAAAA,KAAA,CAAAnC,MAAA;QACA,KAAA1D,WAAA;QACA,KAAArB,IAAA,CAAAiB,QAAA,GAAA3B,SAAA;QACA,KAAAU,IAAA,CAAAmH,QAAA,GAAA7H,SAAA;QACA;MACA;MACA,KAAA+B,WAAA,GAAA6F,KAAA;MACA,IAAAE,GAAA;MACAF,KAAA,CAAAnE,OAAA,WAAA+B,IAAA;QACAsC,GAAA,IAAA5I,UAAA,CAAAsG,IAAA;MACA;MACA,IAAAsC,GAAA,CAAArC,MAAA;QACAqC,GAAA,GAAAA,GAAA,CAAAC,SAAA,IAAAD,GAAA,CAAArC,MAAA;QACA,KAAA/E,IAAA,CAAAiB,QAAA,GAAAmG,GAAA;QACA,KAAApH,IAAA,CAAAmH,QAAA,GAAA3I,UAAA,CAAA0I,KAAA;MACA;QACA,KAAAlH,IAAA,CAAAiB,QAAA,GAAA3B,SAAA;QACA,KAAAU,IAAA,CAAAmH,QAAA,GAAA7H,SAAA;MACA;IAEA;IACA,SACAgI,cAAA,WAAAA,eAAA;MACA,KAAA3B,KAAA,SAAA2B,cAAA;IACA;IACA,SACAC,YAAA,WAAAA,aAAA;MACA,KAAA5B,KAAA,SAAA4B,YAAA;IACA;IACA,WACAC,MAAA,WAAAA,OAAA;MACA;MACA,KAAAC,MAAA,CAAAC,QAAA,0BAAAzF,MAAA;MACA,KAAA0F,OAAA,CAAAC,EAAA;IACA;IACAlB,SAAA,WAAAA,UAAA1G,IAAA;MACA,IAAA6H,SAAA;MACAA,SAAA,CAAAC,YAAA,GAAA9H,IAAA,CAAAmH,QAAA;MACA;MACA,SAAAM,MAAA,CAAAM,KAAA,CAAAC,IAAA,CAAAzF,KAAA,SAAAkF,MAAA,CAAAM,KAAA,CAAAC,IAAA,CAAAzF,KAAA,CAAA0F,QAAA;QACAJ,SAAA,CAAAK,QAAA;MACA;QACAL,SAAA,CAAAK,QAAA;MACA;MACAL,SAAA,CAAAM,WAAA,GAAAnI,IAAA,CAAAwD,MAAA;MACA,KAAAmC,KAAA,SAAAe,SAAA,CAAA1G,IAAA,CAAAwD,MAAA,UAAAqE,SAAA;IACA;EACA;AACA", "ignoreList": []}]}