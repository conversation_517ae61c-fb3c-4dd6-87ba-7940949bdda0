{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/components/ImageUpload/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/components/ImageUpload/index.vue", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldFRva2VuIH0gZnJvbSAiQC91dGlscy9hdXRoIjsKZXhwb3J0IGRlZmF1bHQgewogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBkaWFsb2dWaXNpYmxlOiBmYWxzZSwKICAgICAgdXBsb2FkSW1nVXJsOiBwcm9jZXNzLmVudi5WVUVfQVBQX0JBU0VfQVBJICsgIi9jb21tb24vdXBsb2FkIiwKICAgICAgLy8g5LiK5Lyg55qE5Zu+54mH5pyN5Yqh5Zmo5Zyw5Z2ACiAgICAgIGhlYWRlcnM6IHsKICAgICAgICBBdXRob3JpemF0aW9uOiAiQmVhcmVyICIgKyBnZXRUb2tlbigpCiAgICAgIH0KICAgIH07CiAgfSwKICBwcm9wczogewogICAgdmFsdWU6IHsKICAgICAgdHlwZTogU3RyaW5nLAogICAgICBkZWZhdWx0OiAiIgogICAgfQogIH0sCiAgbWV0aG9kczogewogICAgcmVtb3ZlSW1hZ2U6IGZ1bmN0aW9uIHJlbW92ZUltYWdlKCkgewogICAgICB0aGlzLiRlbWl0KCJpbnB1dCIsICIiKTsKICAgIH0sCiAgICBoYW5kbGVVcGxvYWRTdWNjZXNzOiBmdW5jdGlvbiBoYW5kbGVVcGxvYWRTdWNjZXNzKHJlcykgewogICAgICB0aGlzLiRlbWl0KCJpbnB1dCIsIHJlcy51cmwpOwogICAgICB0aGlzLmxvYWRpbmcuY2xvc2UoKTsKICAgIH0sCiAgICBoYW5kbGVCZWZvcmVVcGxvYWQ6IGZ1bmN0aW9uIGhhbmRsZUJlZm9yZVVwbG9hZCgpIHsKICAgICAgdGhpcy5sb2FkaW5nID0gdGhpcy4kbG9hZGluZyh7CiAgICAgICAgbG9jazogdHJ1ZSwKICAgICAgICB0ZXh0OiAi5LiK5Lyg5LitIiwKICAgICAgICBiYWNrZ3JvdW5kOiAicmdiYSgwLCAwLCAwLCAwLjcpIgogICAgICB9KTsKICAgIH0sCiAgICBoYW5kbGVVcGxvYWRFcnJvcjogZnVuY3Rpb24gaGFuZGxlVXBsb2FkRXJyb3IoKSB7CiAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgIHR5cGU6ICJlcnJvciIsCiAgICAgICAgbWVzc2FnZTogIuS4iuS8oOWksei0pSIKICAgICAgfSk7CiAgICAgIHRoaXMubG9hZGluZy5jbG9zZSgpOwogICAgfQogIH0sCiAgd2F0Y2g6IHt9Cn07"}, {"version": 3, "names": ["getToken", "data", "dialogVisible", "uploadImgUrl", "process", "env", "VUE_APP_BASE_API", "headers", "Authorization", "props", "value", "type", "String", "default", "methods", "removeImage", "$emit", "handleUploadSuccess", "res", "url", "loading", "close", "handleBeforeUpload", "$loading", "lock", "text", "background", "handleUploadError", "$message", "message", "watch"], "sources": ["src/components/ImageUpload/index.vue"], "sourcesContent": ["<template>\n  <div class=\"component-upload-image\">\n    <el-upload\n      :action=\"uploadImgUrl\"\n      list-type=\"picture-card\"\n      :on-success=\"handleUploadSuccess\"\n      :before-upload=\"handleBeforeUpload\"\n      :on-error=\"handleUploadError\"\n      name=\"file\"\n      :show-file-list=\"false\"\n      :headers=\"headers\"\n      style=\"display: inline-block; vertical-align: top\"\n    >\n      <el-image v-if=\"!value\" :src=\"value\">\n        <div slot=\"error\" class=\"image-slot\">\n          <i class=\"el-icon-plus\" />\n        </div>\n      </el-image>\n      <div v-else class=\"image\">\n        <el-image :src=\"value\" :style=\"`width:150px;height:150px;`\" fit=\"fill\"/>\n        <div class=\"mask\">\n          <div class=\"actions\">\n            <span title=\"预览\" @click.stop=\"dialogVisible = true\">\n              <i class=\"el-icon-zoom-in\" />\n            </span>\n            <span title=\"移除\" @click.stop=\"removeImage\">\n              <i class=\"el-icon-delete\" />\n            </span>\n          </div>\n        </div>\n      </div>\n    </el-upload>\n    <el-dialog :visible.sync=\"dialogVisible\" title=\"预览\" width=\"800\" append-to-body>\n      <img :src=\"value\" style=\"display: block; max-width: 100%; margin: 0 auto;\">\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getToken } from \"@/utils/auth\";\n\nexport default {\n  data() {\n    return {\n      dialogVisible: false,\n      uploadImgUrl: process.env.VUE_APP_BASE_API + \"/common/upload\", // 上传的图片服务器地址\n      headers: {\n        Authorization: \"Bearer \" + getToken(),\n      },\n    };\n  },\n  props: {\n    value: {\n      type: String,\n      default: \"\",\n    },\n  },\n  methods: {\n    removeImage() {\n      this.$emit(\"input\", \"\");\n    },\n    handleUploadSuccess(res) {\n      this.$emit(\"input\", res.url);\n      this.loading.close();\n    },\n    handleBeforeUpload() {\n      this.loading = this.$loading({\n        lock: true,\n        text: \"上传中\",\n        background: \"rgba(0, 0, 0, 0.7)\",\n      });\n    },\n    handleUploadError() {\n      this.$message({\n        type: \"error\",\n        message: \"上传失败\",\n      });\n      this.loading.close();\n    },\n  },\n  watch: {},\n};\n</script>\n\n<style scoped lang=\"scss\">\n.image {\n  position: relative;\n  .mask {\n    opacity: 0;\n    position: absolute;\n    top: 0;\n    width: 100%;\n    background-color: rgba(0, 0, 0, 0.5);\n    transition: all 0.3s;\n  }\n  &:hover .mask {\n    opacity: 1;\n  }\n}\n</style>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuCA,SAAAA,QAAA;AAEA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,aAAA;MACAC,YAAA,EAAAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA;MAAA;MACAC,OAAA;QACAC,aAAA,cAAAR,QAAA;MACA;IACA;EACA;EACAS,KAAA;IACAC,KAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;EACA;EACAC,OAAA;IACAC,WAAA,WAAAA,YAAA;MACA,KAAAC,KAAA;IACA;IACAC,mBAAA,WAAAA,oBAAAC,GAAA;MACA,KAAAF,KAAA,UAAAE,GAAA,CAAAC,GAAA;MACA,KAAAC,OAAA,CAAAC,KAAA;IACA;IACAC,kBAAA,WAAAA,mBAAA;MACA,KAAAF,OAAA,QAAAG,QAAA;QACAC,IAAA;QACAC,IAAA;QACAC,UAAA;MACA;IACA;IACAC,iBAAA,WAAAA,kBAAA;MACA,KAAAC,QAAA;QACAjB,IAAA;QACAkB,OAAA;MACA;MACA,KAAAT,OAAA,CAAAC,KAAA;IACA;EACA;EACAS,KAAA;AACA", "ignoreList": []}]}