{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/tool/build/main.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/tool/build/main.js", "mtime": 1641277326000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IFZ1ZSBmcm9tICd2dWUnOwppbXBvcnQgQXBwIGZyb20gJy4vQXBwLnZ1ZSc7CmltcG9ydCByb3V0ZXIgZnJvbSAnQC9yb3V0ZXInOwppbXBvcnQgJ0Avc3R5bGVzL2luZGV4LnNjc3MnOwppbXBvcnQgJ0AvaWNvbnMnOwppbXBvcnQgYXhpb3MgZnJvbSAnYXhpb3MnOwppbXBvcnQgVGlueW1jZSBmcm9tICdAL2NvbXBvbmVudHMvdGlueW1jZS9pbmRleC52dWUnOwpWdWUuY29tcG9uZW50KCd0aW55bWNlJywgVGlueW1jZSk7ClZ1ZS5jb25maWcucHJvZHVjdGlvblRpcCA9IGZhbHNlOwpWdWUucHJvdG90eXBlLiRheGlvcyA9IGF4aW9zOwpuZXcgVnVlKHsKICByb3V0ZXI6IHJvdXRlciwKICByZW5kZXI6IGZ1bmN0aW9uIHJlbmRlcihoKSB7CiAgICByZXR1cm4gaChBcHApOwogIH0KfSkuJG1vdW50KCcjYXBwJyk7"}, {"version": 3, "names": ["<PERSON><PERSON>", "App", "router", "axios", "<PERSON><PERSON><PERSON>", "component", "config", "productionTip", "prototype", "$axios", "render", "h", "$mount"], "sources": ["/Users/<USER>/develop/code/project/haijia/adminweb/src/views/tool/build/main.js"], "sourcesContent": ["import Vue from 'vue'\nimport App from './App.vue'\nimport router from '@/router'\nimport '@/styles/index.scss'\nimport '@/icons'\nimport axios from 'axios'\nimport Tinymce from '@/components/tinymce/index.vue'\n\nVue.component('tinymce', Tinymce)\n\nVue.config.productionTip = false\nVue.prototype.$axios = axios\n\nnew Vue({\n  router,\n  render: h => h(App)\n}).$mount('#app')\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,GAAG,MAAM,WAAW;AAC3B,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAO,qBAAqB;AAC5B,OAAO,SAAS;AAChB,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,OAAO,MAAM,gCAAgC;AAEpDJ,GAAG,CAACK,SAAS,CAAC,SAAS,EAAED,OAAO,CAAC;AAEjCJ,GAAG,CAACM,MAAM,CAACC,aAAa,GAAG,KAAK;AAChCP,GAAG,CAACQ,SAAS,CAACC,MAAM,GAAGN,KAAK;AAE5B,IAAIH,GAAG,CAAC;EACNE,MAAM,EAANA,MAAM;EACNQ,MAAM,EAAE,SAAAA,OAAAC,CAAC;IAAA,OAAIA,CAAC,CAACV,GAAG,CAAC;EAAA;AACrB,CAAC,CAAC,CAACW,MAAM,CAAC,MAAM,CAAC", "ignoreList": []}]}