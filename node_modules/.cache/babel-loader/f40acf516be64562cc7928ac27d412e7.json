{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/tool/preview/main.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/tool/preview/main.js", "mtime": 1641277326000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["<PERSON><PERSON>", "loadScriptQueue", "axios", "<PERSON><PERSON><PERSON>", "component", "prototype", "$axios", "$previewApp", "document", "getElementById", "childAttrs", "file", "dialog", "window", "addEventListener", "init", "buildLinks", "links", "strs", "for<PERSON>ach", "url", "concat", "event", "data", "type", "code", "attrs", "generateConf", "Array", "isArray", "length", "innerHTML", "css", "scripts", "newVue", "js", "html", "main", "eval", "template", "components", "child", "visible", "$mount"], "sources": ["/Users/<USER>/develop/code/project/haijia/adminweb/src/views/tool/preview/main.js"], "sourcesContent": ["import Vue from 'vue'\nimport { loadScriptQueue } from '@/utils/loadScript'\nimport axios from 'axios'\nimport Tiny<PERSON><PERSON> from '@/components/tinymce/index.vue'\n\nVue.component('tinymce', Tinymce)\nVue.prototype.$axios = axios\n\nconst $previewApp = document.getElementById('previewApp')\nconst childAttrs = {\n  file: '',\n  dialog: ' width=\"600px\" class=\"dialog-width\" v-if=\"visible\" :visible.sync=\"visible\" :modal-append-to-body=\"false\" '\n}\n\nwindow.addEventListener('message', init, false)\n\nfunction buildLinks(links) {\n  let strs = ''\n  links.forEach(url => {\n    strs += `<link href=\"${url}\" rel=\"stylesheet\">`\n  })\n  return strs\n}\n\nfunction init(event) {\n  if (event.data.type === 'refreshFrame') {\n    const code = event.data.data\n    const attrs = childAttrs[code.generateConf.type]\n    let links = ''\n\n    if (Array.isArray(code.links) && code.links.length > 0) {\n      links = buildLinks(code.links)\n    }\n\n    $previewApp.innerHTML = `${links}<style>${code.css}</style><div id=\"app\"></div>`\n\n    if (Array.isArray(code.scripts) && code.scripts.length > 0) {\n      loadScriptQueue(code.scripts, () => {\n        newVue(attrs, code.js, code.html)\n      })\n    } else {\n      newVue(attrs, code.js, code.html)\n    }\n  }\n}\n\nfunction newVue(attrs, main, html) {\n  main = eval(`(${main})`)\n  main.template = `<div>${html}</div>`\n  new Vue({\n    components: {\n      child: main\n    },\n    data() {\n      return {\n        visible: true\n      }\n    },\n    template: `<div><child ${attrs}/></div>`\n  }).$mount('#app')\n}\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,SAASC,eAAe,QAAQ,oBAAoB;AACpD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,OAAO,MAAM,gCAAgC;AAEpDH,GAAG,CAACI,SAAS,CAAC,SAAS,EAAED,OAAO,CAAC;AACjCH,GAAG,CAACK,SAAS,CAACC,MAAM,GAAGJ,KAAK;AAE5B,IAAMK,WAAW,GAAGC,QAAQ,CAACC,cAAc,CAAC,YAAY,CAAC;AACzD,IAAMC,UAAU,GAAG;EACjBC,IAAI,EAAE,EAAE;EACRC,MAAM,EAAE;AACV,CAAC;AAEDC,MAAM,CAACC,gBAAgB,CAAC,SAAS,EAAEC,IAAI,EAAE,KAAK,CAAC;AAE/C,SAASC,UAAUA,CAACC,KAAK,EAAE;EACzB,IAAIC,IAAI,GAAG,EAAE;EACbD,KAAK,CAACE,OAAO,CAAC,UAAAC,GAAG,EAAI;IACnBF,IAAI,oBAAAG,MAAA,CAAmBD,GAAG,2BAAqB;EACjD,CAAC,CAAC;EACF,OAAOF,IAAI;AACb;AAEA,SAASH,IAAIA,CAACO,KAAK,EAAE;EACnB,IAAIA,KAAK,CAACC,IAAI,CAACC,IAAI,KAAK,cAAc,EAAE;IACtC,IAAMC,IAAI,GAAGH,KAAK,CAACC,IAAI,CAACA,IAAI;IAC5B,IAAMG,KAAK,GAAGhB,UAAU,CAACe,IAAI,CAACE,YAAY,CAACH,IAAI,CAAC;IAChD,IAAIP,KAAK,GAAG,EAAE;IAEd,IAAIW,KAAK,CAACC,OAAO,CAACJ,IAAI,CAACR,KAAK,CAAC,IAAIQ,IAAI,CAACR,KAAK,CAACa,MAAM,GAAG,CAAC,EAAE;MACtDb,KAAK,GAAGD,UAAU,CAACS,IAAI,CAACR,KAAK,CAAC;IAChC;IAEAV,WAAW,CAACwB,SAAS,MAAAV,MAAA,CAAMJ,KAAK,aAAAI,MAAA,CAAUI,IAAI,CAACO,GAAG,mCAA8B;IAEhF,IAAIJ,KAAK,CAACC,OAAO,CAACJ,IAAI,CAACQ,OAAO,CAAC,IAAIR,IAAI,CAACQ,OAAO,CAACH,MAAM,GAAG,CAAC,EAAE;MAC1D7B,eAAe,CAACwB,IAAI,CAACQ,OAAO,EAAE,YAAM;QAClCC,MAAM,CAACR,KAAK,EAAED,IAAI,CAACU,EAAE,EAAEV,IAAI,CAACW,IAAI,CAAC;MACnC,CAAC,CAAC;IACJ,CAAC,MAAM;MACLF,MAAM,CAACR,KAAK,EAAED,IAAI,CAACU,EAAE,EAAEV,IAAI,CAACW,IAAI,CAAC;IACnC;EACF;AACF;AAEA,SAASF,MAAMA,CAACR,KAAK,EAAEW,IAAI,EAAED,IAAI,EAAE;EACjCC,IAAI,GAAGC,IAAI,KAAAjB,MAAA,CAAKgB,IAAI,MAAG,CAAC;EACxBA,IAAI,CAACE,QAAQ,WAAAlB,MAAA,CAAWe,IAAI,WAAQ;EACpC,IAAIpC,GAAG,CAAC;IACNwC,UAAU,EAAE;MACVC,KAAK,EAAEJ;IACT,CAAC;IACDd,IAAI,WAAAA,KAAA,EAAG;MACL,OAAO;QACLmB,OAAO,EAAE;MACX,CAAC;IACH,CAAC;IACDH,QAAQ,iBAAAlB,MAAA,CAAiBK,KAAK;EAChC,CAAC,CAAC,CAACiB,MAAM,CAAC,MAAM,CAAC;AACnB", "ignoreList": []}]}