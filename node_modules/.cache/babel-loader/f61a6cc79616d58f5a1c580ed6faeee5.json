{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Process/components/custom/index.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Process/components/custom/index.js", "mtime": 1650121562000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IEN1c3RvbUNvbnRleHRQYWQgZnJvbSAnLi9jdXN0b21Db250ZXh0UGFkJzsKZXhwb3J0IGRlZmF1bHQgewogIF9faW5pdF9fOiBbJ2N1c3RvbUNvbnRleHRQYWQnXSwKICBjdXN0b21Db250ZXh0UGFkOiBbJ3R5cGUnLCBDdXN0b21Db250ZXh0UGFkXQp9Ow=="}, {"version": 3, "names": ["CustomContextPad", "__init__", "customContextPad"], "sources": ["/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Process/components/custom/index.js"], "sourcesContent": ["import CustomContextPad from './customContextPad'\r\n\r\nexport default {\r\n  __init__: ['customContextPad'],\r\n  customContextPad: ['type', CustomContextPad]\r\n}"], "mappings": "AAAA,OAAOA,gBAAgB,MAAM,oBAAoB;AAEjD,eAAe;EACbC,QAAQ,EAAE,CAAC,kBAAkB,CAAC;EAC9BC,gBAAgB,EAAE,CAAC,MAAM,EAAEF,gBAAgB;AAC7C,CAAC", "ignoreList": []}]}