{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/flowable/task/record/view.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/flowable/task/record/view.vue", "mtime": 1717760123606}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGZsb3dSZWNvcmQsIGdldEhpc0lucyB9IGZyb20gIkAvYXBpL2Zsb3dhYmxlL2ZpbmlzaGVkIjsKaW1wb3J0IFBhcnNlciBmcm9tICJAL2NvbXBvbmVudHMvcGFyc2VyL1BhcnNlciI7CmltcG9ydCBVcGxvYWQgZnJvbSAiQC9jb21wb25lbnRzL0ZpbGVVcGxvYWQiOwppbXBvcnQgeyBkZWZpbml0aW9uU3RhcnRCeUtleSwgZ2V0UHJvY2Vzc1ZhcmlhYmxlcywgcmVhZFhtbEJ5S2V5LCBnZXRGbG93Vmlld2VyIGFzIF9nZXRGbG93Vmlld2VyIH0gZnJvbSAiQC9hcGkvZmxvd2FibGUvZGVmaW5pdGlvbiI7CmltcG9ydCB7IGNvbXBsZXRlLCByZWplY3RUYXNrLCByZXR1cm5MaXN0LCByZXR1cm5UYXNrLCBnZXROZXh0Rmxvd05vZGUgYXMgX2dldE5leHRGbG93Tm9kZSwgZGVsZWdhdGUsIGVuZFRhc2sgfSBmcm9tICJAL2FwaS9mbG93YWJsZS90b2RvIjsKaW1wb3J0IGZsb3cgZnJvbSAiQC92aWV3cy9mbG93YWJsZS90YXNrL3JlY29yZC9mbG93IjsKaW1wb3J0IHsgdHJlZXNlbGVjdCB9IGZyb20gIkAvYXBpL3N5c3RlbS9kZXB0IjsKaW1wb3J0ICJAcmlvcGhhZS92dWUtdHJlZXNlbGVjdC9kaXN0L3Z1ZS10cmVlc2VsZWN0LmNzcyI7CmltcG9ydCBUcmVlc2VsZWN0IGZyb20gIkByaW9waGFlL3Z1ZS10cmVlc2VsZWN0IjsKaW1wb3J0IHsgbGlzdFVzZXIgfSBmcm9tICJAL2FwaS9zeXN0ZW0vdXNlciI7CmltcG9ydCBtb21lbnQgZnJvbSAibW9tZW50IjsKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICJSZWNvcmQiLAogIGNvbXBvbmVudHM6IHsKICAgIFBhcnNlcjogUGFyc2VyLAogICAgZmxvdzogZmxvdywKICAgIFRyZWVzZWxlY3Q6IFRyZWVzZWxlY3QsCiAgICBVcGxvYWQ6IFVwbG9hZAogIH0sCiAgcHJvcHM6IHsKICAgIHByb2NEZWZLZXk6IHsKICAgICAgdHlwZTogU3RyaW5nLAogICAgICBkZWZhdWx0OiB1bmRlZmluZWQKICAgIH0sCiAgICB0YXNrSWQ6IHsKICAgICAgdHlwZTogU3RyaW5nLAogICAgICBkZWZhdWx0OiB1bmRlZmluZWQKICAgIH0sCiAgICBwcm9jSW5zSWQ6IHsKICAgICAgdHlwZTogU3RyaW5nLAogICAgICBkZWZhdWx0OiB1bmRlZmluZWQKICAgIH0sCiAgICBiaXpLZXk6IHsKICAgICAgdHlwZTogU3RyaW5nLAogICAgICBkZWZhdWx0OiB1bmRlZmluZWQKICAgIH0sCiAgICBmaW5pc2hlZDogewogICAgICB0eXBlOiBCb29sZWFuLAogICAgICBkZWZhdWx0OiB0cnVlCiAgICB9LAogICAgaXNBdXRoSW1hZ2VzOiB7CiAgICAgIHR5cGU6IEJvb2xlYW4sCiAgICAgIGRlZmF1bHQ6IGZhbHNlCiAgICB9LAogICAgdmlld09wZW46IHsKICAgICAgdHlwZTogQm9vbGVhbiwKICAgICAgZGVmYXVsdDogZmFsc2UKICAgIH0KICB9LAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICAvLyDmqKHlnot4bWzmlbDmja4KICAgICAgeG1sRGF0YTogIiIsCiAgICAgIHRhc2tMaXN0OiBbXSwKICAgICAgLy8g6YOo6Zeo5ZCN56ewCiAgICAgIGRlcHROYW1lOiB1bmRlZmluZWQsCiAgICAgIC8vIOmDqOmXqOagkemAiemhuQogICAgICBkZXB0T3B0aW9uczogdW5kZWZpbmVkLAogICAgICAvLyDnlKjmiLfooajmoLzmlbDmja4KICAgICAgdXNlckxpc3Q6IG51bGwsCiAgICAgIGRlZmF1bHRQcm9wczogewogICAgICAgIGNoaWxkcmVuOiAiY2hpbGRyZW4iLAogICAgICAgIGxhYmVsOiAibGFiZWwiCiAgICAgIH0sCiAgICAgIC8vIOafpeivouWPguaVsAogICAgICBxdWVyeVBhcmFtczogewogICAgICAgIGRlcHRJZDogdW5kZWZpbmVkCiAgICAgIH0sCiAgICAgIHR5cGVBcnI6IFsiemlwIiwgInJhciJdLAogICAgICAvLyDpga7nvanlsYIKICAgICAgbG9hZGluZzogdHJ1ZSwKICAgICAgZmxvd1JlY29yZExpc3Q6IFtdLAogICAgICAvLyDmtYHnqIvmtYHovazmlbDmja4KICAgICAgZmxvd1JlY29yZExpc3RzOiB7fSwKICAgICAgZm9ybUNvbmZDb3B5OiB7fSwKICAgICAgc3JjOiBudWxsLAogICAgICBydWxlczoge30sCiAgICAgIC8vIOihqOWNleagoemqjAogICAgICB2YXJpYWJsZXNGb3JtOiB7fSwKICAgICAgLy8g5rWB56iL5Y+Y6YeP5pWw5o2uCiAgICAgIHRhc2tGb3JtOiB7CiAgICAgICAgcmV0dXJuVGFza1Nob3c6IGZhbHNlLAogICAgICAgIC8vIOaYr+WQpuWxleekuuWbnumAgOihqOWNlQogICAgICAgIGRlbGVnYXRlVGFza1Nob3c6IGZhbHNlLAogICAgICAgIC8vIOaYr+WQpuWxleekuuWbnumAgOihqOWNlQogICAgICAgIGRlZmF1bHRUYXNrU2hvdzogdHJ1ZSwKICAgICAgICAvLyDpu5jorqTlpITnkIYKICAgICAgICBzZW5kVXNlclNob3c6IGZhbHNlLAogICAgICAgIC8vIOWuoeaJueeUqOaItwogICAgICAgIG11bHRpcGxlOiBmYWxzZSwKICAgICAgICBjb21tZW50OiAiIiwKICAgICAgICAvLyDmhI/op4HlhoXlrrkKICAgICAgICBwcm9jSW5zSWQ6ICIiLAogICAgICAgIC8vIOa1geeoi+WunuS+i+e8luWPtwogICAgICAgIGluc3RhbmNlSWQ6ICIiLAogICAgICAgIC8vIOa1geeoi+WunuS+i+e8luWPtwogICAgICAgIHRhc2tJZDogIiIsCiAgICAgICAgLy8g5rWB56iL5Lu75Yqh57yW5Y+3CiAgICAgICAgcHJvY0RlZktleTogIiIsCiAgICAgICAgLy8g5rWB56iL57yW5Y+3CiAgICAgICAgdmFyczogIiIsCiAgICAgICAgdGFyZ2V0S2V5OiAiIiwKICAgICAgICBhdXRoSW1hZ2VzOiAiIgogICAgICB9LAogICAgICB1c2VyRGF0YUxpc3Q6IFtdLAogICAgICAvLyDmtYHnqIvlgJnpgInkuroKICAgICAgYXNzaWduZWU6IG51bGwsCiAgICAgIGZvcm1Db25mOiB7fSwKICAgICAgLy8g6buY6K6k6KGo5Y2V5pWw5o2uCiAgICAgIGZvcm1Db25mT3BlbjogZmFsc2UsCiAgICAgIC8vIOaYr+WQpuWKoOi9vem7mOiupOihqOWNleaVsOaNrgogICAgICB2YXJpYWJsZXM6IFtdLAogICAgICAvLyDmtYHnqIvlj5jph4/mlbDmja4KICAgICAgdmFyaWFibGVzRGF0YToge30sCiAgICAgIC8vIOa1geeoi+WPmOmHj+aVsOaNrgogICAgICB2YXJpYWJsZU9wZW46IGZhbHNlLAogICAgICAvLyDmmK/lkKbliqDovb3mtYHnqIvlj5jph4/mlbDmja4KICAgICAgcmV0dXJuVGFza0xpc3Q6IFtdLAogICAgICAvLyDlm57pgIDliJfooajmlbDmja4KICAgICAgY29tcGxldGVUaXRsZTogbnVsbCwKICAgICAgY29tcGxldGVPcGVuOiBmYWxzZSwKICAgICAgcmV0dXJuVGl0bGU6IG51bGwsCiAgICAgIHJldHVybk9wZW46IGZhbHNlLAogICAgICByZWplY3RPcGVuOiBmYWxzZSwKICAgICAgcmVqZWN0VGl0bGU6IG51bGwsCiAgICAgIHJlamVjdE9wZW4xOiBmYWxzZSwKICAgICAgcmVqZWN0VGl0bGUxOiBudWxsLAogICAgICB1c2VyRGF0YTogW10sCiAgICAgIGF1ZGl0OiB0cnVlLAogICAgICBjYW5GaW5pc2g6IGZhbHNlLAogICAgICBmbG93SGlzOiBbXSwKICAgICAgZmxvd0FjdGl2ZTogbnVsbCwKICAgICAgYml6S2V5OiBudWxsLAogICAgICBpc0NvbW1vbjogZmFsc2UsCiAgICAgIGF1dGhJbWFnZXM6IFtdCiAgICB9OwogIH0sCiAgY3JlYXRlZDogZnVuY3Rpb24gY3JlYXRlZCgpIHsKICAgIGlmICh0aGlzLiRzdG9yZS5zdGF0ZS51c2VyLnJvbGVzICYmIHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIucm9sZXMuaW5jbHVkZXMoImNvbW1vbiIpKSB7CiAgICAgIHRoaXMuaXNDb21tb24gPSB0cnVlOwogICAgfQogICAgY29uc29sZS5sb2coIj09PT09PT09cmVjb3JkPT09PT09PT1jcmVhdGVkPT4+PiIpOwogICAgY29uc29sZS5sb2codGhpcy5fcHJvcHMpOwogICAgdmFyIHRoYXQgPSB0aGlzOwogICAgdmFyIF90aGlzJF9wcm9wcyA9IHRoaXMuX3Byb3BzLAogICAgICB0YXNrSWQgPSBfdGhpcyRfcHJvcHMudGFza0lkLAogICAgICBwcm9jRGVmS2V5ID0gX3RoaXMkX3Byb3BzLnByb2NEZWZLZXksCiAgICAgIHByb2NJbnNJZCA9IF90aGlzJF9wcm9wcy5wcm9jSW5zSWQsCiAgICAgIGZpbmlzaGVkID0gX3RoaXMkX3Byb3BzLmZpbmlzaGVkLAogICAgICBiaXpLZXkgPSBfdGhpcyRfcHJvcHMuYml6S2V5OwogICAgLy8gaWYoIXZpZXdPcGVuKXsKICAgIC8vICAgY29uc29sZS5sb2coIj09PT4+PuWFs+mXrSzkuI3muLLmn5MiKQogICAgLy8gICByZXR1cm47CiAgICAvLyB9CiAgICB0aGlzLnRhc2tGb3JtLnRhc2tJZCA9IHRhc2tJZDsKICAgIHRoaXMudGFza0Zvcm0ucHJvY0luc0lkID0gcHJvY0luc0lkOwogICAgdGhpcy50YXNrRm9ybS5pbnN0YW5jZUlkID0gcHJvY0luc0lkOwogICAgLy8g5Yid5aeL5YyW6KGo5Y2VCiAgICB0aGlzLnRhc2tGb3JtLnByb2NEZWZLZXkgPSBwcm9jRGVmS2V5OwogICAgdGhpcy5iaXpLZXkgPSBiaXpLZXk7CiAgICAvL+mHjee9rgogICAgdGhhdC5mbG93SGlzID0gW107CiAgICB0aGF0LmZsb3dSZWNvcmRMaXN0cyA9IG51bGw7CiAgICAvLyDlm57mmL7mtYHnqIvorrDlvZUKICAgIGlmIChwcm9jSW5zSWQpIHsKICAgICAgdGhpcy5nZXRGbG93Vmlld2VyKHRoaXMudGFza0Zvcm0ucHJvY0luc0lkLCB0aGlzLnRhc2tGb3JtLnByb2NEZWZLZXkpOwogICAgICAvLyDmtYHnqIvku7vliqHph43ojrflj5blj5jph4/ooajljZUKICAgICAgaWYgKHRoaXMudGFza0Zvcm0udGFza0lkKSB7CiAgICAgICAgdGhpcy5wcm9jZXNzVmFyaWFibGVzKHRoaXMudGFza0Zvcm0udGFza0lkKTsKICAgICAgICB0aGlzLmdldE5leHRGbG93Tm9kZSh0aGlzLnRhc2tGb3JtLnRhc2tJZCk7CiAgICAgIH0KICAgICAgdGhpcy5nZXRGbG93UmVjb3JkTGlzdCh0aGlzLnRhc2tGb3JtLnByb2NJbnNJZCk7CiAgICAgIHRoYXQuZmxvd0FjdGl2ZSA9IHByb2NJbnNJZDsKICAgICAgZ2V0SGlzSW5zKHsKICAgICAgICBiaXpLZXk6IHRoYXQuYml6S2V5LAogICAgICAgIGRlZktleTogcHJvY0RlZktleQogICAgICB9KS50aGVuKGZ1bmN0aW9uIChyZXNwKSB7CiAgICAgICAgaWYgKHJlc3AuZGF0YSAmJiByZXNwLmRhdGEubGVuZ3RoID4gMCkgewogICAgICAgICAgdGhhdC50YXNrRm9ybS50YXNrTmFtZSA9IHJlc3AuZGF0YVswXS5uYW1lOwogICAgICAgIH0KICAgICAgICBpZiAocmVzcC5kYXRhLmxlbmd0aCA+IDEpIHsKICAgICAgICAgIHRoYXQuZmxvd0hpcyA9IHJlc3AuZGF0YTsKICAgICAgICAgIHRoYXQuZmxvd1JlY29yZExpc3RzID0gbmV3IE1hcCgpOwogICAgICAgICAgdGhhdC5mbG93UmVjb3JkTGlzdHMuc2V0KHByb2NJbnNJZCwgdGhhdC5mbG93UmVjb3JkTGlzdCk7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0gZWxzZSB7CiAgICAgIHRoaXMuZ2V0TW9kZWxEZXRhaWwocHJvY0RlZktleSk7CiAgICB9CiAgICB0aGlzLmZpbmlzaGVkID0gZmluaXNoZWQ7CiAgfSwKICBtb3VudGVkOiBmdW5jdGlvbiBtb3VudGVkKCkgewogICAgLy8gLy8g6KGo5Y2V5pWw5o2u5Zue5aGr77yM5qih5ouf5byC5q2l6K+35rGC5Zy65pmvCiAgICAvLyBzZXRUaW1lb3V0KCgpID0+IHsKICAgIC8vICAgLy8g6K+35rGC5Zue5p2l55qE6KGo5Y2V5pWw5o2uCiAgICAvLyAgIGNvbnN0IGRhdGEgPSB7CiAgICAvLyAgICAgZmllbGQxMDI6ICcxODgzNjY2MjU1NScKICAgIC8vICAgfQogICAgLy8gICAvLyDlm57loavmlbDmja4KICAgIC8vICAgdGhpcy5maWxsRm9ybURhdGEodGhpcy5mb3JtQ29uZiwgZGF0YSkKICAgIC8vICAgLy8g5pu05paw6KGo5Y2VCiAgICAvLyAgIHRoaXMua2V5ID0gK25ldyBEYXRlKCkuZ2V0VGltZSgpCiAgICAvLyB9LCAxMDAwKQogIH0sCiAgbWV0aG9kczogewogICAgLyoqIOafpeivoumDqOmXqOS4i+aLieagkee7k+aehCAqL2dldFRyZWVzZWxlY3Q6IGZ1bmN0aW9uIGdldFRyZWVzZWxlY3QoKSB7CiAgICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICAgIHRyZWVzZWxlY3QoKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgIF90aGlzLmRlcHRPcHRpb25zID0gcmVzcG9uc2UuZGF0YTsKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOafpeivoueUqOaIt+WIl+ihqCAqL2dldExpc3Q6IGZ1bmN0aW9uIGdldExpc3QoKSB7CiAgICAgIHZhciBfdGhpczIgPSB0aGlzOwogICAgICBsaXN0VXNlcih0aGlzLmFkZERhdGVSYW5nZSh0aGlzLnF1ZXJ5UGFyYW1zLCB0aGlzLmRhdGVSYW5nZSkpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgX3RoaXMyLnVzZXJMaXN0ID0gcmVzcG9uc2Uucm93czsKICAgICAgICBfdGhpczIudG90YWwgPSByZXNwb25zZS50b3RhbDsKICAgICAgfSk7CiAgICB9LAogICAgLy8g562b6YCJ6IqC54K5CiAgICBmaWx0ZXJOb2RlOiBmdW5jdGlvbiBmaWx0ZXJOb2RlKHZhbHVlLCBkYXRhKSB7CiAgICAgIGlmICghdmFsdWUpIHJldHVybiB0cnVlOwogICAgICByZXR1cm4gZGF0YS5sYWJlbC5pbmRleE9mKHZhbHVlKSAhPT0gLTE7CiAgICB9LAogICAgLy8g6IqC54K55Y2V5Ye75LqL5Lu2CiAgICBoYW5kbGVOb2RlQ2xpY2s6IGZ1bmN0aW9uIGhhbmRsZU5vZGVDbGljayhkYXRhKSB7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMuZGVwdElkID0gZGF0YS5pZDsKICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICB9LAogICAgLyoqIHhtbCDmlofku7YgKi9nZXRNb2RlbERldGFpbDogZnVuY3Rpb24gZ2V0TW9kZWxEZXRhaWwoZGVwbG95S2V5KSB7CiAgICAgIHZhciBfdGhpczMgPSB0aGlzOwogICAgICAvLyDlj5HpgIHor7fmsYLvvIzojrflj5Z4bWwKICAgICAgcmVhZFhtbEJ5S2V5KGRlcGxveUtleSkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgX3RoaXMzLnhtbERhdGEgPSByZXMuZGF0YTsKICAgICAgfSk7CiAgICB9LAogICAgZ2V0Rmxvd1ZpZXdlcjogZnVuY3Rpb24gZ2V0Rmxvd1ZpZXdlcihwcm9jSW5zSWQsIGRlcGxveUtleSkgewogICAgICB2YXIgX3RoaXM0ID0gdGhpczsKICAgICAgX2dldEZsb3dWaWV3ZXIocHJvY0luc0lkKS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICBfdGhpczQudGFza0xpc3QgPSByZXMuZGF0YTsKICAgICAgICBfdGhpczQuZ2V0TW9kZWxEZXRhaWwoZGVwbG95S2V5KTsKICAgICAgfSk7CiAgICB9LAogICAgc2V0SWNvbjogZnVuY3Rpb24gc2V0SWNvbih2YWwpIHsKICAgICAgaWYgKHZhbCkgewogICAgICAgIHJldHVybiAiZWwtaWNvbi1jaGVjayI7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgcmV0dXJuICJlbC1pY29uLXRpbWUiOwogICAgICB9CiAgICB9LAogICAgc2V0Q29sb3I6IGZ1bmN0aW9uIHNldENvbG9yKHZhbCkgewogICAgICBpZiAodmFsKSB7CiAgICAgICAgcmV0dXJuICIjMmJjNDE4IjsKICAgICAgfSBlbHNlIHsKICAgICAgICByZXR1cm4gIiNiM2JkYmIiOwogICAgICB9CiAgICB9LAogICAgLy8g5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2U6IGZ1bmN0aW9uIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsKICAgICAgdGhpcy51c2VyRGF0YSA9IHNlbGVjdGlvbjsKICAgICAgdmFyIHZhbCA9IHNlbGVjdGlvbi5tYXAoZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICByZXR1cm4gaXRlbS51c2VySWQ7CiAgICAgIH0pWzBdOwogICAgICBpZiAodmFsIGluc3RhbmNlb2YgQXJyYXkpIHsKICAgICAgICB0aGlzLnRhc2tGb3JtLnZhbHVlcyA9IHsKICAgICAgICAgIGFwcHJvdmFsOiB2YWwuam9pbigiLCIpCiAgICAgICAgfTsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLnRhc2tGb3JtLnZhbHVlcyA9IHsKICAgICAgICAgIGFwcHJvdmFsOiB2YWwKICAgICAgICB9OwogICAgICB9CiAgICB9LAogICAgLy8g5YWz6Zet5qCH562+CiAgICBoYW5kbGVDbG9zZTogZnVuY3Rpb24gaGFuZGxlQ2xvc2UodGFnKSB7CiAgICAgIHRoaXMudXNlckRhdGEuc3BsaWNlKHRoaXMudXNlckRhdGEuaW5kZXhPZih0YWcpLCAxKTsKICAgIH0sCiAgICAvKiog5rWB56iL5Y+Y6YeP6LWL5YC8ICovaGFuZGxlQ2hlY2tDaGFuZ2U6IGZ1bmN0aW9uIGhhbmRsZUNoZWNrQ2hhbmdlKHZhbCkgewogICAgICBpZiAodmFsIGluc3RhbmNlb2YgQXJyYXkpIHsKICAgICAgICB0aGlzLnRhc2tGb3JtLnZhbHVlcyA9IHsKICAgICAgICAgIGFwcHJvdmFsOiB2YWwuam9pbigiLCIpCiAgICAgICAgfTsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLnRhc2tGb3JtLnZhbHVlcyA9IHsKICAgICAgICAgIGFwcHJvdmFsOiB2YWwKICAgICAgICB9OwogICAgICB9CiAgICB9LAogICAgLyoqIOa1geeoi+a1gei9rOiusOW9lSAqL2dldEZsb3dSZWNvcmRMaXN0OiBmdW5jdGlvbiBnZXRGbG93UmVjb3JkTGlzdChwcm9jSW5zSWQpIHsKICAgICAgdmFyIHRoYXQgPSB0aGlzOwogICAgICB2YXIgcGFyYW1zID0gewogICAgICAgIHByb2NJbnNJZDogcHJvY0luc0lkCiAgICAgIH07CiAgICAgIGZsb3dSZWNvcmQocGFyYW1zKS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICB0aGF0LmZsb3dSZWNvcmRMaXN0ID0gcmVzLmRhdGEuZmxvd0xpc3Q7CiAgICAgICAgLy8g5rWB56iL6L+H56iL5Lit5LiN5a2Y5Zyo5Yid5aeL5YyW6KGo5Y2VIOebtOaOpeivu+WPlueahOa1geeoi+WPmOmHj+S4reWtmOWCqOeahOihqOWNleWAvAogICAgICAgIGlmIChyZXMuZGF0YS5mb3JtRGF0YSkgewogICAgICAgICAgdGhhdC5mb3JtQ29uZiA9IHJlcy5kYXRhLmZvcm1EYXRhOwogICAgICAgICAgdGhhdC5mb3JtQ29uZk9wZW4gPSB0cnVlOwogICAgICAgIH0KICAgICAgICBpZiAodGhhdC5mbG93UmVjb3JkTGlzdHMpIHsKICAgICAgICAgIHRoYXQuZmxvd1JlY29yZExpc3RzLnNldChwcm9jSW5zSWQsIHRoYXQuZmxvd1JlY29yZExpc3QpOwogICAgICAgIH0KICAgICAgfSkuY2F0Y2goZnVuY3Rpb24gKHJlcykgewogICAgICAgIHRoYXQuJHJvdXRlci5nbygwKTsKICAgICAgfSk7CiAgICB9LAogICAgZmlsbEZvcm1EYXRhOiBmdW5jdGlvbiBmaWxsRm9ybURhdGEoZm9ybSwgZGF0YSkgewogICAgICBmb3JtLmZpZWxkcy5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgdmFyIHZhbCA9IGRhdGFbaXRlbS5fX3ZNb2RlbF9fXTsKICAgICAgICBpZiAodmFsKSB7CiAgICAgICAgICBpdGVtLl9fY29uZmlnX18uZGVmYXVsdFZhbHVlID0gdmFsOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLyoqIOiOt+WPlua1geeoi+WPmOmHj+WGheWuuSAqL3Byb2Nlc3NWYXJpYWJsZXM6IGZ1bmN0aW9uIHByb2Nlc3NWYXJpYWJsZXModGFza0lkKSB7CiAgICAgIHZhciBfdGhpczUgPSB0aGlzOwogICAgICBpZiAodGFza0lkKSB7CiAgICAgICAgLy8g5o+Q5Lqk5rWB56iL55Sz6K+35pe25aGr5YaZ55qE6KGo5Y2V5a2Y5YWl5LqG5rWB56iL5Y+Y6YeP5Lit5ZCO57ut5Lu75Yqh5aSE55CG5pe26ZyA6KaB5bGV56S6CiAgICAgICAgZ2V0UHJvY2Vzc1ZhcmlhYmxlcyh0YXNrSWQpLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgICAgLy8gdGhpcy52YXJpYWJsZXMgPSByZXMuZGF0YS52YXJpYWJsZXM7CiAgICAgICAgICBfdGhpczUudmFyaWFibGVzRGF0YSA9IHJlcy5kYXRhLnZhcmlhYmxlczsKICAgICAgICAgIF90aGlzNS52YXJpYWJsZU9wZW4gPSB0cnVlOwogICAgICAgIH0pOwogICAgICB9CiAgICB9LAogICAgLyoqIOagueaNruW9k+WJjeS7u+WKoeaIluiAhea1geeoi+iuvuiuoemFjee9rueahOS4i+S4gOatpeiKgueCuSAqL2dldE5leHRGbG93Tm9kZTogZnVuY3Rpb24gZ2V0TmV4dEZsb3dOb2RlKHRhc2tJZCkgewogICAgICB2YXIgX3RoaXM2ID0gdGhpczsKICAgICAgLy8g5qC55o2u5b2T5YmN5Lu75Yqh5oiW6ICF5rWB56iL6K6+6K6h6YWN572u55qE5LiL5LiA5q2l6IqC54K5IHRvZG8g5pqC5pe25pyq5raJ5Y+K5Yiw6ICD6JmR572R5YWz44CB6KGo6L6+5byP5ZKM5aSa6IqC54K55oOF5Ya1CiAgICAgIHZhciBwYXJhbXMgPSB7CiAgICAgICAgdGFza0lkOiB0YXNrSWQKICAgICAgfTsKICAgICAgX2dldE5leHRGbG93Tm9kZShwYXJhbXMpLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgIHZhciBkYXRhID0gcmVzLmRhdGE7CiAgICAgICAgaWYgKGRhdGEpIHsKICAgICAgICAgIGlmIChkYXRhLnR5cGUgPT09ICJhc3NpZ25lZSIpIHsKICAgICAgICAgICAgX3RoaXM2LnVzZXJEYXRhTGlzdCA9IHJlcy5kYXRhLnVzZXJMaXN0OwogICAgICAgICAgfSBlbHNlIGlmIChkYXRhLnR5cGUgPT09ICJjYW5kaWRhdGVVc2VycyIpIHsKICAgICAgICAgICAgX3RoaXM2LnVzZXJEYXRhTGlzdCA9IHJlcy5kYXRhLnVzZXJMaXN0OwogICAgICAgICAgICBfdGhpczYudGFza0Zvcm0ubXVsdGlwbGUgPSB0cnVlOwogICAgICAgICAgfSBlbHNlIGlmIChkYXRhLnR5cGUgPT09ICJjYW5kaWRhdGVHcm91cHMiKSB7CiAgICAgICAgICAgIHJlcy5kYXRhLnJvbGVMaXN0LmZvckVhY2goZnVuY3Rpb24gKHJvbGUpIHsKICAgICAgICAgICAgICByb2xlLnVzZXJJZCA9IHJvbGUucm9sZUlkOwogICAgICAgICAgICAgIHJvbGUubmlja05hbWUgPSByb2xlLnJvbGVOYW1lOwogICAgICAgICAgICB9KTsKICAgICAgICAgICAgX3RoaXM2LnVzZXJEYXRhTGlzdCA9IHJlcy5kYXRhLnJvbGVMaXN0OwogICAgICAgICAgICBfdGhpczYudGFza0Zvcm0ubXVsdGlwbGUgPSBmYWxzZTsKICAgICAgICAgIH0gZWxzZSBpZiAoZGF0YS50eXBlID09PSAibXVsdGlJbnN0YW5jZSIpIHsKICAgICAgICAgICAgX3RoaXM2LnVzZXJEYXRhTGlzdCA9IHJlcy5kYXRhLnVzZXJMaXN0OwogICAgICAgICAgICBfdGhpczYudGFza0Zvcm0ubXVsdGlwbGUgPSB0cnVlOwogICAgICAgICAgfQogICAgICAgICAgX3RoaXM2LnRhc2tGb3JtLnNlbmRVc2VyU2hvdyA9IHRydWU7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIF90aGlzNi5jYW5GaW5pc2ggPSB0cnVlOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLyoqIOWuoeaJueS7u+WKoemAieaLqSAqL2hhbmRsZUNvbXBsZXRlOiBmdW5jdGlvbiBoYW5kbGVDb21wbGV0ZSgpIHsKICAgICAgdGhpcy5jb21wbGV0ZU9wZW4gPSB0cnVlOwogICAgICB0aGlzLmNvbXBsZXRlVGl0bGUgPSAi5a6h5om55rWB56iLIjsKICAgICAgLy90aGlzLmdldFRyZWVzZWxlY3QoKTsKICAgIH0sCiAgICAvKiog5a6h5om55Lu75YqhICovdGFza0NvbXBsZXRlOiBmdW5jdGlvbiB0YXNrQ29tcGxldGUoY29tbWVudCkgewogICAgICB2YXIgX3RoaXM3ID0gdGhpczsKICAgICAgLy8gaWYgKCF0aGlzLnRhc2tGb3JtLnZhbHVlcyl7CiAgICAgIC8vICAgdGhpcy5tc2dFcnJvcigi6K+36YCJ5oup5rWB56iL5o6l5pS25Lq65ZGYIik7CiAgICAgIC8vICAgcmV0dXJuOwogICAgICAvLyB9CiAgICAgIGlmIChjb21tZW50ICYmIHR5cGVvZiBjb21tZW50ID09ICJzdHJpbmciICYmIGNvbW1lbnQuY29uc3RydWN0b3IgPT0gU3RyaW5nKSB7CiAgICAgICAgdGhpcy50YXNrRm9ybS5jb21tZW50ID0gY29tbWVudDsKICAgICAgfQogICAgICBpZiAoIXRoaXMudGFza0Zvcm0uY29tbWVudCkgewogICAgICAgIHRoaXMubXNnRXJyb3IoIuivt+i+k+WFpeWuoeaJueaEj+ingSIpOwogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICAvL2lmKHRoaXMuY2FuRmluaXNoKXsKICAgICAgdGhpcy50YXNrRm9ybS52YWx1ZXMgPSB7fTsKICAgICAgdGhpcy50YXNrRm9ybS5iaXpLZXkgPSB0aGlzLmJpektleTsKICAgICAgdGhpcy50YXNrRm9ybS52YWx1ZXMuaXNSZWplY3QgPSBmYWxzZTsKICAgICAgLy99CiAgICAgIGNvbXBsZXRlKHRoaXMudGFza0Zvcm0pLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgX3RoaXM3Lm1zZ1N1Y2Nlc3MocmVzcG9uc2UubXNnKTsKICAgICAgICAvL3RoaXMuZ29CYWNrKCk7CiAgICAgICAgX3RoaXM3LiRyb3V0ZXIuZ28oMCk7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDlp5TmtL7ku7vliqEgKi9oYW5kbGVEZWxlZ2F0ZTogZnVuY3Rpb24gaGFuZGxlRGVsZWdhdGUoKSB7CiAgICAgIHRoaXMudGFza0Zvcm0uZGVsZWdhdGVUYXNrU2hvdyA9IHRydWU7CiAgICAgIHRoaXMudGFza0Zvcm0uZGVmYXVsdFRhc2tTaG93ID0gZmFsc2U7CiAgICB9LAogICAgaGFuZGxlQXNzaWduOiBmdW5jdGlvbiBoYW5kbGVBc3NpZ24oKSB7fSwKICAgIC8qKiDov5Tlm57pobXpnaIgKi9nb0JhY2s6IGZ1bmN0aW9uIGdvQmFjaygpIHsKICAgICAgLy8g5YWz6Zet5b2T5YmN5qCH562+6aG15bm26L+U5Zue5LiK5Liq6aG16Z2iCiAgICAgIHRoaXMuJHN0b3JlLmRpc3BhdGNoKCJ0YWdzVmlldy9kZWxWaWV3IiwgdGhpcy4kcm91dGUpOwogICAgICB0aGlzLiRyb3V0ZXIuZ28oLTEpOwogICAgfSwKICAgIC8qKiDmjqXmlLblrZDnu4Tku7bkvKDnmoTlgLwgKi9nZXREYXRhOiBmdW5jdGlvbiBnZXREYXRhKGRhdGEpIHsKICAgICAgaWYgKGRhdGEpIHsKICAgICAgICB2YXIgdmFyaWFibGVzID0gW107CiAgICAgICAgZGF0YS5maWVsZHMuZm9yRWFjaChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgdmFyIHZhcmlhYmxlRGF0YSA9IHt9OwogICAgICAgICAgdmFyaWFibGVEYXRhLmxhYmVsID0gaXRlbS5fX2NvbmZpZ19fLmxhYmVsOwogICAgICAgICAgLy8g6KGo5Y2V5YC85Li65aSa5Liq6YCJ6aG55pe2CiAgICAgICAgICBpZiAoaXRlbS5fX2NvbmZpZ19fLmRlZmF1bHRWYWx1ZSBpbnN0YW5jZW9mIEFycmF5KSB7CiAgICAgICAgICAgIHZhciBhcnJheSA9IFtdOwogICAgICAgICAgICBpdGVtLl9fY29uZmlnX18uZGVmYXVsdFZhbHVlLmZvckVhY2goZnVuY3Rpb24gKHZhbCkgewogICAgICAgICAgICAgIGFycmF5LnB1c2godmFsKTsKICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIHZhcmlhYmxlRGF0YS52YWwgPSBhcnJheTsKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIHZhcmlhYmxlRGF0YS52YWwgPSBpdGVtLl9fY29uZmlnX18uZGVmYXVsdFZhbHVlOwogICAgICAgICAgfQogICAgICAgICAgdmFyaWFibGVzLnB1c2godmFyaWFibGVEYXRhKTsKICAgICAgICB9KTsKICAgICAgICB0aGlzLnZhcmlhYmxlcyA9IHZhcmlhYmxlczsKICAgICAgfQogICAgfSwKICAgIC8vIOaOpeaUtuWtkOe7hOS7tuWbvueJh+S4iuS8oOeahOWAvAogICAgaW1hZ2VVcmxzOiBmdW5jdGlvbiBpbWFnZVVybHModmFsdWUpIHsKICAgICAgdGhpcy50YXNrRm9ybS5hdXRoSW1hZ2VzID0gdmFsdWU7CiAgICB9LAogICAgLyoqIOeUs+ivt+a1geeoi+ihqOWNleaVsOaNruaPkOS6pCAqLwogICAgLy8gc3VibWl0Rm9ybShkYXRhKSB7CiAgICAvLyAgIGlmIChkYXRhKSB7CiAgICAvLyAgICAgY29uc3QgdmFyaWFibGVzID0gZGF0YS52YWxEYXRhOwogICAgLy8gICAgIGNvbnN0IGZvcm1EYXRhID0gZGF0YS5mb3JtRGF0YTsKICAgIC8vICAgICBmb3JtRGF0YS5kaXNhYmxlZCA9IHRydWU7CiAgICAvLyAgICAgZm9ybURhdGEuZm9ybUJ0bnMgPSBmYWxzZTsKICAgIC8vICAgICBpZiAodGhpcy50YXNrRm9ybS5wcm9jRGVmS2V5KSB7CiAgICAvLyAgICAgICB2YXJpYWJsZXMudmFyaWFibGVzID0gZm9ybURhdGE7CiAgICAvLyAgICAgICB2YXJpYWJsZXMuYnVzaW5lc3NLZXkgPSBkYXRhLmJ1c2luZXNzS2V5OwogICAgLy8gICAgICAgIC8vIOWQr+WKqOa1geeoi+W5tuWwhuihqOWNleaVsOaNruWKoOWFpea1geeoi+WPmOmHjwogICAgLy8gICAgICAgZGVmaW5pdGlvblN0YXJ0QnlLZXkodGhpcy50YXNrRm9ybS5wcm9jRGVmS2V5LCBKU09OLnN0cmluZ2lmeSh2YXJpYWJsZXMpKS50aGVuKHJlcyA9PiB7CiAgICAvLyAgICAgICAgIHRoaXMubXNnU3VjY2VzcyhyZXMubXNnKTsKICAgIC8vICAgICAgICAgdGhpcy5nb0JhY2soKTsKICAgIC8vICAgICAgIH0pCiAgICAvLyAgICAgfQogICAgLy8gICB9CiAgICAvLyB9LAogICAgc3RhcnRGbG93OiBmdW5jdGlvbiBzdGFydEZsb3coYnVzaW5lc3NLZXksIG5hbWUsIHZhcmlhYmxlcykgewogICAgICB2YXIgX3RoaXM4ID0gdGhpczsKICAgICAgdmFyIHN0YXJ0RGF0ZSA9IG1vbWVudChuZXcgRGF0ZSgpKS5mb3JtYXQoIllZWVlNTURESEhtbXNzIik7CiAgICAgIHZhciBkYXRhID0ge307CiAgICAgIGlmICh0aGlzLnRhc2tGb3JtLnByb2NEZWZLZXkpIHsKICAgICAgICBpZiAoIXZhcmlhYmxlcykgewogICAgICAgICAgZGF0YS52YXJpYWJsZXMgPSB7fTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgZGF0YS52YXJpYWJsZXMgPSB2YXJpYWJsZXM7CiAgICAgICAgfQogICAgICAgIGRhdGEuYnVzaW5lc3NLZXkgPSBidXNpbmVzc0tleTsKICAgICAgICBkYXRhLnByb2NEZWZLZXkgPSB0aGlzLnRhc2tGb3JtLnByb2NEZWZLZXk7CiAgICAgICAgZGF0YS50YXNrTmFtZSA9IG5hbWU7CiAgICAgICAgLy8g5ZCv5Yqo5rWB56iL5bm25bCG6KGo5Y2V5pWw5o2u5Yqg5YWl5rWB56iL5Y+Y6YePCiAgICAgICAgZGVmaW5pdGlvblN0YXJ0QnlLZXkoSlNPTi5zdHJpbmdpZnkoZGF0YSkpLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgICAgX3RoaXM4Lm1zZ1N1Y2Nlc3MocmVzLm1zZyk7CiAgICAgICAgICBfdGhpczguZ29CYWNrKCk7CiAgICAgICAgfSk7CiAgICAgIH0KICAgIH0sCiAgICAvKiog6amz5Zue5Lu75YqhICovaGFuZGxlUmVqZWN0OiBmdW5jdGlvbiBoYW5kbGVSZWplY3QoKSB7CiAgICAgIHRoaXMucmVqZWN0T3BlbiA9IHRydWU7CiAgICAgIHRoaXMucmVqZWN0VGl0bGUgPSAi6YCA5Zue5rWB56iLIjsKICAgIH0sCiAgICAvKiog6amz5Zue5Lu75YqhICovdGFza1JlamVjdDogZnVuY3Rpb24gdGFza1JlamVjdCgpIHsKICAgICAgdmFyIF90aGlzOSA9IHRoaXM7CiAgICAgIHRoaXMuJHJlZnNbInRhc2tGb3JtIl0udmFsaWRhdGUoZnVuY3Rpb24gKHZhbGlkKSB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICByZWplY3RUYXNrKF90aGlzOS50YXNrRm9ybSkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgICAgIF90aGlzOS5tc2dTdWNjZXNzKHJlcy5tc2cpOwogICAgICAgICAgICBfdGhpczkuJHJvdXRlci5nbygwKTsKICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLyoqIOWPr+mAgOWbnuS7u+WKoeWIl+ihqCAqL2hhbmRsZVJldHVybjogZnVuY3Rpb24gaGFuZGxlUmV0dXJuKCkgewogICAgICB2YXIgX3RoaXMxMCA9IHRoaXM7CiAgICAgIHRoaXMucmV0dXJuT3BlbiA9IHRydWU7CiAgICAgIHRoaXMucmV0dXJuVGl0bGUgPSAi6YCA5Zue5rWB56iLIjsKICAgICAgcmV0dXJuTGlzdCh0aGlzLnRhc2tGb3JtKS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICBfdGhpczEwLnJldHVyblRhc2tMaXN0ID0gcmVzLmRhdGE7CiAgICAgICAgX3RoaXMxMC50YXNrRm9ybS52YWx1ZXMgPSBudWxsOwogICAgICAgIGlmIChyZXMuZGF0YSAmJiByZXMuZGF0YS5sZW5ndGggPiAwKSB7CiAgICAgICAgICBfdGhpczEwLnRhc2tGb3JtLnRhcmdldEtleSA9IHJlcy5kYXRhW3Jlcy5kYXRhLmxlbmd0aCAtIDFdLmlkOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLyoqIOaPkOS6pOmAgOWbnuS7u+WKoSAqL3Rhc2tSZXR1cm46IGZ1bmN0aW9uIHRhc2tSZXR1cm4oKSB7CiAgICAgIHZhciBfdGhpczExID0gdGhpczsKICAgICAgdGhpcy4kcmVmc1sidGFza0Zvcm0iXS52YWxpZGF0ZShmdW5jdGlvbiAodmFsaWQpIHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIC8v6YCA5Zue5Lia5YqhSUTvvIznlKjkuo7kv67mlLnnirbmgIEKICAgICAgICAgIF90aGlzMTEudGFza0Zvcm0uYml6S2V5ID0gX3RoaXMxMS5iaXpLZXk7CiAgICAgICAgICByZXR1cm5UYXNrKF90aGlzMTEudGFza0Zvcm0pLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgICAgICBfdGhpczExLm1zZ1N1Y2Nlc3MocmVzLm1zZyk7CiAgICAgICAgICAgIF90aGlzMTEuJHJvdXRlci5nbygwKTsKICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLyoqIOWPlua2iOWbnumAgOS7u+WKoeaMiemSriAqL2NhbmNlbFRhc2s6IGZ1bmN0aW9uIGNhbmNlbFRhc2soKSB7CiAgICAgIHRoaXMudGFza0Zvcm0ucmV0dXJuVGFza1Nob3cgPSBmYWxzZTsKICAgICAgdGhpcy50YXNrRm9ybS5kZWZhdWx0VGFza1Nob3cgPSB0cnVlOwogICAgICB0aGlzLnRhc2tGb3JtLnNlbmRVc2VyU2hvdyA9IHRydWU7CiAgICAgIHRoaXMucmV0dXJuVGFza0xpc3QgPSBbXTsKICAgIH0sCiAgICAvKiog5aeU5rS+5Lu75YqhICovc3VibWl0RGVsZXRlVGFzazogZnVuY3Rpb24gc3VibWl0RGVsZXRlVGFzaygpIHsKICAgICAgdmFyIF90aGlzMTIgPSB0aGlzOwogICAgICB0aGlzLiRyZWZzWyJ0YXNrRm9ybSJdLnZhbGlkYXRlKGZ1bmN0aW9uICh2YWxpZCkgewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgZGVsZWdhdGUoX3RoaXMxMi50YXNrRm9ybSkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICAgICAgX3RoaXMxMi5tc2dTdWNjZXNzKHJlc3BvbnNlLm1zZyk7CiAgICAgICAgICAgIF90aGlzMTIuJHJvdXRlci5nbygwKTsKICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLyoqIOWPlua2iOWbnumAgOS7u+WKoeaMiemSriAqL2NhbmNlbERlbGVnYXRlVGFzazogZnVuY3Rpb24gY2FuY2VsRGVsZWdhdGVUYXNrKCkgewogICAgICB0aGlzLnRhc2tGb3JtLmRlbGVnYXRlVGFza1Nob3cgPSBmYWxzZTsKICAgICAgdGhpcy50YXNrRm9ybS5kZWZhdWx0VGFza1Nob3cgPSB0cnVlOwogICAgICB0aGlzLnRhc2tGb3JtLnNlbmRVc2VyU2hvdyA9IHRydWU7CiAgICAgIHRoaXMucmV0dXJuVGFza0xpc3QgPSBbXTsKICAgIH0sCiAgICBoYW5kbGVFbmQ6IGZ1bmN0aW9uIGhhbmRsZUVuZCgpIHsKICAgICAgdGhpcy5yZWplY3RPcGVuMSA9IHRydWU7CiAgICAgIHRoaXMucmVqZWN0VGl0bGUxID0gIumps+Wbnua1geeoiyI7CiAgICB9LAogICAgLyoqIOmps+Wbnue7k+adn+S7u+WKoSAqL3Rhc2tFbmQ6IGZ1bmN0aW9uIHRhc2tFbmQoKSB7CiAgICAgIHZhciBfdGhpczEzID0gdGhpczsKICAgICAgdGhpcy4kcmVmc1sidGFza0Zvcm0iXS52YWxpZGF0ZShmdW5jdGlvbiAodmFsaWQpIHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIF90aGlzMTMudGFza0Zvcm0udmFsdWVzID0ge307CiAgICAgICAgICBfdGhpczEzLnRhc2tGb3JtLnZhbHVlcy5pc1JlamVjdCA9IHRydWU7CiAgICAgICAgICBlbmRUYXNrKF90aGlzMTMudGFza0Zvcm0pLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgICAgICBfdGhpczEzLm1zZ1N1Y2Nlc3MocmVzLm1zZyk7CiAgICAgICAgICAgIF90aGlzMTMuJHJvdXRlci5nbygwKTsKICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgaGFuZGxlQ2xpY2s6IGZ1bmN0aW9uIGhhbmRsZUNsaWNrKHRhYiwgZXZlbnQpIHsKICAgICAgaWYgKHRoaXMuZmxvd1JlY29yZExpc3RzICYmIHRoaXMuZmxvd1JlY29yZExpc3RzLmdldCh0YWIubmFtZSkpIHsKICAgICAgICB0aGlzLmZsb3dSZWNvcmRMaXN0ID0gdGhpcy5mbG93UmVjb3JkTGlzdHMuZ2V0KHRhYi5uYW1lKTsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLmdldEZsb3dSZWNvcmRMaXN0KHRhYi5uYW1lKTsKICAgICAgfQogICAgfQogIH0KfTs="}, {"version": 3, "names": ["flowRecord", "getHisIns", "<PERSON><PERSON><PERSON>", "Upload", "definitionStartByKey", "getProcessVariables", "readXmlByKey", "getFlowViewer", "complete", "rejectTask", "returnList", "returnTask", "getNextFlowNode", "delegate", "endTask", "flow", "treeselect", "Treeselect", "listUser", "moment", "name", "components", "props", "procDef<PERSON>ey", "type", "String", "default", "undefined", "taskId", "procInsId", "bizKey", "finished", "Boolean", "isAuthImages", "viewOpen", "data", "xmlData", "taskList", "deptName", "deptOptions", "userList", "defaultProps", "children", "label", "queryParams", "deptId", "typeArr", "loading", "flowRecordList", "flowRecordLists", "formConfCopy", "src", "rules", "variablesForm", "taskForm", "returnTaskShow", "delegateTaskShow", "defaultTaskShow", "sendUserShow", "multiple", "comment", "instanceId", "vars", "<PERSON><PERSON><PERSON>", "authImages", "userDataList", "assignee", "formConf", "formConfOpen", "variables", "variablesData", "variableOpen", "returnTaskList", "completeTitle", "completeOpen", "returnTitle", "returnOpen", "rejectOpen", "rejectTitle", "rejectOpen1", "rejectTitle1", "userData", "audit", "canFinish", "flowHis", "flowActive", "isCommon", "created", "$store", "state", "user", "roles", "includes", "console", "log", "_props", "that", "_this$_props", "processVariables", "getFlowRecordList", "def<PERSON><PERSON>", "then", "resp", "length", "taskName", "Map", "set", "getModelDetail", "mounted", "methods", "getTreeselect", "_this", "response", "getList", "_this2", "addDateRange", "date<PERSON><PERSON><PERSON>", "rows", "total", "filterNode", "value", "indexOf", "handleNodeClick", "id", "deployKey", "_this3", "res", "_this4", "setIcon", "val", "setColor", "handleSelectionChange", "selection", "map", "item", "userId", "Array", "values", "approval", "join", "handleClose", "tag", "splice", "handleCheckChange", "params", "flowList", "formData", "catch", "$router", "go", "fillFormData", "form", "fields", "for<PERSON>ach", "__vModel__", "__config__", "defaultValue", "_this5", "_this6", "roleList", "role", "roleId", "nick<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "handleComplete", "taskComplete", "_this7", "constructor", "msgError", "isReject", "msgSuccess", "msg", "handleDelegate", "handleAssign", "goBack", "dispatch", "$route", "getData", "variableData", "array", "push", "imageUrls", "startFlow", "businessKey", "_this8", "startDate", "Date", "format", "JSON", "stringify", "handleReject", "taskReject", "_this9", "$refs", "validate", "valid", "handleReturn", "_this10", "taskReturn", "_this11", "cancelTask", "submitDeleteTask", "_this12", "cancelDelegateTask", "handleEnd", "taskEnd", "_this13", "handleClick", "tab", "event", "get"], "sources": ["src/views/flowable/task/record/view.vue"], "sourcesContent": ["<template>\n  <div class=\"\">\n    <div class=\"clearfix\" v-if=\"!finished\">\n      <span style=\"float: right; margin-bottom: 20px\">\n        <el-button\n          icon=\"el-icon-edit-outline\"\n          type=\"success\"\n          @click=\"handleComplete\"\n          >{{ isCommon ? \"重新提交\" : \"审批\" }}</el-button\n        >\n        <el-button\n          v-if=\"!isCommon\"\n          icon=\"el-icon-refresh-left\"\n          type=\"warning\"\n          @click=\"handleReturn\"\n          >退回</el-button\n        >\n        <!-- <el-button v-if=\"!isCommon\" icon=\"el-icon-refresh-left\" type=\"warning\" @click=\"handleReject\">退回</el-button> -->\n        <el-button\n          v-if=\"!isCommon\"\n          icon=\"el-icon-refresh-left\"\n          type=\"danger\"\n          @click=\"handleEnd\"\n          >驳回</el-button\n        >\n      </span>\n    </div>\n    <!--流程流转记录-->\n    <el-card class=\"box-card\">\n      <div slot=\"header\" class=\"clearfix\">\n        <span class=\"el-icon-notebook-1\">审批记录</span>\n      </div>\n      <div class=\"block\">\n        <span v-if=\"!flowRecordList || flowRecordList.length == 0\"\n          >无审批记录</span\n        >\n        <el-tabs\n          v-if=\"!flowHis || flowHis.length > 1\"\n          v-model=\"flowActive\"\n          type=\"card\"\n          @tab-click=\"handleClick\"\n        >\n          <el-tab-pane\n            :key=\"index\"\n            :label=\"item.taskName\"\n            :name=\"item.procInsId\"\n            v-for=\"(item, index) in flowHis\"\n          ></el-tab-pane>\n        </el-tabs>\n        <el-timeline>\n          <el-timeline-item\n            v-for=\"(item, index) in flowRecordList\"\n            :key=\"index\"\n            :icon=\"setIcon(item.finishTime)\"\n            :color=\"setColor(item.finishTime)\"\n          >\n            <p style=\"font-weight: 700\">{{ item.taskName }}</p>\n            <el-card class=\"audit-record\" :body-style=\"{ padding: '10px' }\">\n              <label\n                v-if=\"item.assigneeName\"\n                style=\"font-weight: normal; margin-right: 30px\"\n                >实际办理：\n                <span\n                  style=\"\n                    margin-right: 30px;\n                    color: #8a909c;\n                    font-weight: normal;\n                  \"\n                  >{{ item.assigneeName }}</span\n                ></label\n              >\n              <label\n                v-if=\"item.candidate\"\n                style=\"font-weight: normal; margin-right: 30px\"\n                >候选办理：\n                <span\n                  style=\"\n                    margin-right: 30px;\n                    color: #8a909c;\n                    font-weight: normal;\n                  \"\n                  >{{ item.candidate }}</span\n                ></label\n              >\n              <label style=\"font-weight: normal\"\n                >接收时间：\n                <span\n                  style=\"\n                    margin-right: 30px;\n                    color: #8a909c;\n                    font-weight: normal;\n                  \"\n                  >{{ item.createTime }}</span\n                ></label\n              >\n              <label v-if=\"item.finishTime\" style=\"font-weight: normal\"\n                >办结时间：\n                <span\n                  style=\"\n                    margin-right: 30px;\n                    color: #8a909c;\n                    font-weight: normal;\n                  \"\n                  >{{ item.finishTime }}</span\n                ></label\n              >\n              <label v-if=\"item.duration\" style=\"font-weight: normal\"\n                >耗时：\n                <span\n                  style=\"\n                    margin-right: 30px;\n                    color: #8a909c;\n                    font-weight: normal;\n                  \"\n                  >{{ item.duration }}</span\n                ></label\n              >\n\n              <p v-if=\"item.comment\">\n                <el-tag type=\"success\" v-if=\"item.comment.type === '1'\">\n                  {{ item.comment.comment }}</el-tag\n                >\n                <el-tag type=\"warning\" v-if=\"item.comment.type === '2'\">\n                  {{ item.comment.comment }}</el-tag\n                >\n                <el-tag type=\"danger\" v-if=\"item.comment.type === '3'\">\n                  {{ item.comment.comment }}</el-tag\n                >\n              </p>\n            </el-card>\n          </el-timeline-item>\n        </el-timeline>\n      </div>\n    </el-card>\n    <el-card class=\"box-card\">\n      <div slot=\"header\" class=\"clearfix\">\n        <span class=\"el-icon-notebook-2\"><slot name=\"title\"></slot></span>\n      </div>\n      <slot name=\"content\"></slot>\n    </el-card>\n    <el-card class=\"box-card\">\n      <div slot=\"header\" class=\"clearfix\">\n        <span class=\"el-icon-picture-outline\">流程图</span>\n      </div>\n      <flow :xmlData=\"xmlData\" :taskData=\"taskList\"></flow>\n    </el-card>\n\n    <!--审批正常流程-->\n    <el-dialog\n      :title=\"completeTitle\"\n      visible.sync=\"false\"\n      width=\"60%\"\n      append-to-body\n    >\n      <el-row :gutter=\"20\">\n        <!--部门数据-->\n        <el-col :span=\"4\" :xs=\"24\">\n          <h6>部门列表</h6>\n          <div class=\"head-container\">\n            <el-input\n              v-model=\"deptName\"\n              placeholder=\"请输入部门名称\"\n              clearable\n              size=\"small\"\n              prefix-icon=\"el-icon-search\"\n              style=\"margin-bottom: 20px\"\n            />\n          </div>\n          <div class=\"head-container\">\n            <el-tree\n              :data=\"deptOptions\"\n              :props=\"defaultProps\"\n              :expand-on-click-node=\"false\"\n              :filter-node-method=\"filterNode\"\n              ref=\"tree\"\n              default-expand-all\n              @node-click=\"handleNodeClick\"\n            />\n          </div>\n        </el-col>\n        <el-col :span=\"12\" :xs=\"24\">\n          <h6>待选人员</h6>\n          <el-table\n            ref=\"singleTable\"\n            :data=\"userList\"\n            border\n            style=\"width: 100%\"\n            @selection-change=\"handleSelectionChange\"\n          >\n            <el-table-column type=\"selection\" width=\"50\" align=\"center\" />\n            <el-table-column label=\"用户名\" align=\"center\" prop=\"nickName\" />\n            <el-table-column label=\"部门\" align=\"center\" prop=\"dept.deptName\" />\n          </el-table>\n        </el-col>\n        <el-col :span=\"8\" :xs=\"24\">\n          <h6>已选人员</h6>\n          <el-tag\n            v-for=\"tag in userData\"\n            :key=\"tag.nickName\"\n            closable\n            @close=\"handleClose(tag)\"\n          >\n            {{ tag.nickName }} {{ tag.dept.deptName }}\n          </el-tag>\n        </el-col>\n      </el-row>\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-input\n          style=\"width: 50%; margin-right: 34%\"\n          type=\"textarea\"\n          v-model=\"taskForm.comment\"\n          placeholder=\"请输入处理意见\"\n        />\n        <el-button @click=\"completeOpen = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"taskComplete\">确 定</el-button>\n      </span>\n    </el-dialog>\n\n    <el-dialog\n      :title=\"completeTitle\"\n      :visible.sync=\"completeOpen\"\n      width=\"40%\"\n      append-to-body\n    >\n      <el-form ref=\"taskForm\" :model=\"taskForm\" label-width=\"120px\">\n        <el-form-item\n          label=\"审批意见\"\n          prop=\"comment\"\n          :rules=\"[\n            { required: true, message: '请输入处理意见', trigger: 'blur' },\n          ]\"\n        >\n          <el-input\n            style=\"width: 50%\"\n            type=\"textarea\"\n            v-model=\"taskForm.comment\"\n            placeholder=\"请输入处理意见\"\n          />\n        </el-form-item>\n        <el-form-item label=\"授权书图片\" v-if=\"isAuthImages\">\n          <Upload\n            :fileSize=\"30\"\n            :value=\"taskForm.authImages\"\n            @input=\"imageUrls\"\n            :fileType=\"typeArr\"\n          ></Upload>\n        </el-form-item>\n      </el-form>\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"completeOpen = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"taskComplete\">确 定</el-button>\n      </span>\n    </el-dialog>\n\n    <!--退回流程-->\n    <el-dialog\n      :title=\"returnTitle\"\n      :visible.sync=\"returnOpen\"\n      width=\"40%\"\n      append-to-body\n    >\n      <el-form ref=\"taskForm\" :model=\"taskForm\" label-width=\"80px\">\n        <el-form-item\n          :v-if=\"false\"\n          label=\"退回节点\"\n          prop=\"targetKey\"\n          :rules=\"[\n            { required: true, message: '请选择退回节点', trigger: 'change' },\n          ]\"\n        >\n          <el-radio-group v-model=\"taskForm.targetKey\">\n            <el-radio-button\n              v-for=\"item in returnTaskList\"\n              :key=\"item.id\"\n              :label=\"item.id\"\n              >{{ item.name }}</el-radio-button\n            >\n          </el-radio-group>\n        </el-form-item>\n        <el-form-item\n          label=\"退回意见\"\n          prop=\"comment\"\n          :rules=\"[{ required: true, message: '请输入意见', trigger: 'blur' }]\"\n        >\n          <el-input\n            style=\"width: 50%\"\n            type=\"textarea\"\n            v-model=\"taskForm.comment\"\n            placeholder=\"请输入意见\"\n          />\n        </el-form-item>\n      </el-form>\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"returnOpen = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"taskReturn\">确 定</el-button>\n      </span>\n    </el-dialog>\n\n    <!--驳回流程-->\n    <el-dialog\n      :title=\"rejectTitle\"\n      :visible.sync=\"rejectOpen\"\n      width=\"40%\"\n      append-to-body\n    >\n      <el-form ref=\"taskForm\" :model=\"taskForm\" label-width=\"80px\">\n        <el-form-item\n          label=\"退回意见\"\n          prop=\"comment\"\n          :rules=\"[{ required: true, message: '请输入意见', trigger: 'blur' }]\"\n        >\n          <el-input\n            style=\"width: 50%\"\n            type=\"textarea\"\n            v-model=\"taskForm.comment\"\n            placeholder=\"请输入意见\"\n          />\n        </el-form-item>\n      </el-form>\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"rejectOpen = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"taskReject\">确 定</el-button>\n      </span>\n    </el-dialog>\n    <el-dialog\n      :title=\"rejectTitle1\"\n      :visible.sync=\"rejectOpen1\"\n      width=\"40%\"\n      append-to-body\n    >\n      <el-form ref=\"taskForm\" :model=\"taskForm\" label-width=\"80px\">\n        <el-form-item\n          label=\"驳回意见\"\n          prop=\"comment\"\n          :rules=\"[{ required: true, message: '请输入意见', trigger: 'blur' }]\"\n        >\n          <el-input\n            style=\"width: 50%\"\n            type=\"textarea\"\n            v-model=\"taskForm.comment\"\n            placeholder=\"请输入意见\"\n          />\n        </el-form-item>\n      </el-form>\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"rejectOpen1 = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"taskEnd\">确 定</el-button>\n      </span>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { flowRecord, getHisIns } from \"@/api/flowable/finished\";\nimport Parser from \"@/components/parser/Parser\";\nimport Upload from \"@/components/FileUpload\";\nimport {\n  definitionStartByKey,\n  getProcessVariables,\n  readXmlByKey,\n  getFlowViewer,\n} from \"@/api/flowable/definition\";\nimport {\n  complete,\n  rejectTask,\n  returnList,\n  returnTask,\n  getNextFlowNode,\n  delegate,\n  endTask,\n} from \"@/api/flowable/todo\";\nimport flow from \"@/views/flowable/task/record/flow\";\nimport { treeselect } from \"@/api/system/dept\";\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\";\nimport Treeselect from \"@riophae/vue-treeselect\";\nimport { listUser } from \"@/api/system/user\";\nimport moment from \"moment\";\nexport default {\n  name: \"Record\",\n  components: {\n    Parser,\n    flow,\n    Treeselect,\n    Upload,\n  },\n  props: {\n    procDefKey: {\n      type: String,\n      default: undefined,\n    },\n    taskId: {\n      type: String,\n      default: undefined,\n    },\n    procInsId: {\n      type: String,\n      default: undefined,\n    },\n    bizKey: {\n      type: String,\n      default: undefined,\n    },\n    finished: {\n      type: Boolean,\n      default: true,\n    },\n    isAuthImages: {\n      type: Boolean,\n      default: false,\n    },\n    viewOpen: {\n      type: Boolean,\n      default: false,\n    },\n  },\n  data() {\n    return {\n      // 模型xml数据\n      xmlData: \"\",\n      taskList: [],\n      // 部门名称\n      deptName: undefined,\n      // 部门树选项\n      deptOptions: undefined,\n      // 用户表格数据\n      userList: null,\n      defaultProps: {\n        children: \"children\",\n        label: \"label\",\n      },\n      // 查询参数\n      queryParams: {\n        deptId: undefined,\n      },\n      typeArr: [\"zip\", \"rar\"],\n      // 遮罩层\n      loading: true,\n      flowRecordList: [], // 流程流转数据\n      flowRecordLists: {},\n      formConfCopy: {},\n      src: null,\n      rules: {}, // 表单校验\n      variablesForm: {}, // 流程变量数据\n      taskForm: {\n        returnTaskShow: false, // 是否展示回退表单\n        delegateTaskShow: false, // 是否展示回退表单\n        defaultTaskShow: true, // 默认处理\n        sendUserShow: false, // 审批用户\n        multiple: false,\n        comment: \"\", // 意见内容\n        procInsId: \"\", // 流程实例编号\n        instanceId: \"\", // 流程实例编号\n        taskId: \"\", // 流程任务编号\n        procDefKey: \"\", // 流程编号\n        vars: \"\",\n        targetKey: \"\",\n        authImages: \"\",\n      },\n      userDataList: [], // 流程候选人\n      assignee: null,\n      formConf: {}, // 默认表单数据\n      formConfOpen: false, // 是否加载默认表单数据\n      variables: [], // 流程变量数据\n      variablesData: {}, // 流程变量数据\n      variableOpen: false, // 是否加载流程变量数据\n      returnTaskList: [], // 回退列表数据\n      completeTitle: null,\n      completeOpen: false,\n      returnTitle: null,\n      returnOpen: false,\n      rejectOpen: false,\n      rejectTitle: null,\n      rejectOpen1: false,\n      rejectTitle1: null,\n      userData: [],\n      audit: true,\n      canFinish: false,\n      flowHis: [],\n      flowActive: null,\n      bizKey: null,\n      isCommon: false,\n      authImages: [],\n    };\n  },\n  created() {\n    if (\n      this.$store.state.user.roles &&\n      this.$store.state.user.roles.includes(\"common\")\n    ) {\n      this.isCommon = true;\n    }\n    console.log(\"========record========created=>>>\");\n    console.log(this._props);\n    let that = this;\n    let { taskId, procDefKey, procInsId, finished, bizKey } = this._props;\n    // if(!viewOpen){\n    //   console.log(\"===>>>关闭,不渲染\")\n    //   return;\n    // }\n    this.taskForm.taskId = taskId;\n    this.taskForm.procInsId = procInsId;\n    this.taskForm.instanceId = procInsId;\n    // 初始化表单\n    this.taskForm.procDefKey = procDefKey;\n    this.bizKey = bizKey;\n    //重置\n    that.flowHis = [];\n    that.flowRecordLists = null;\n    // 回显流程记录\n    if (procInsId) {\n      this.getFlowViewer(this.taskForm.procInsId, this.taskForm.procDefKey);\n      // 流程任务重获取变量表单\n      if (this.taskForm.taskId) {\n        this.processVariables(this.taskForm.taskId);\n        this.getNextFlowNode(this.taskForm.taskId);\n      }\n      this.getFlowRecordList(this.taskForm.procInsId);\n      that.flowActive = procInsId;\n      getHisIns({\n        bizKey: that.bizKey,\n        defKey: procDefKey,\n      }).then((resp) => {\n        if (resp.data && resp.data.length > 0) {\n          that.taskForm.taskName = resp.data[0].name;\n        }\n        if (resp.data.length > 1) {\n          that.flowHis = resp.data;\n          that.flowRecordLists = new Map();\n          that.flowRecordLists.set(procInsId, that.flowRecordList);\n        }\n      });\n    } else {\n      this.getModelDetail(procDefKey);\n    }\n    this.finished = finished;\n  },\n  mounted() {\n    // // 表单数据回填，模拟异步请求场景\n    // setTimeout(() => {\n    //   // 请求回来的表单数据\n    //   const data = {\n    //     field102: '18836662555'\n    //   }\n    //   // 回填数据\n    //   this.fillFormData(this.formConf, data)\n    //   // 更新表单\n    //   this.key = +new Date().getTime()\n    // }, 1000)\n  },\n  methods: {\n    /** 查询部门下拉树结构 */\n    getTreeselect() {\n      treeselect().then((response) => {\n        this.deptOptions = response.data;\n      });\n    },\n    /** 查询用户列表 */\n    getList() {\n      listUser(this.addDateRange(this.queryParams, this.dateRange)).then(\n        (response) => {\n          this.userList = response.rows;\n          this.total = response.total;\n        }\n      );\n    },\n    // 筛选节点\n    filterNode(value, data) {\n      if (!value) return true;\n      return data.label.indexOf(value) !== -1;\n    },\n    // 节点单击事件\n    handleNodeClick(data) {\n      this.queryParams.deptId = data.id;\n      this.getList();\n    },\n    /** xml 文件 */\n    getModelDetail(deployKey) {\n      // 发送请求，获取xml\n      readXmlByKey(deployKey).then((res) => {\n        this.xmlData = res.data;\n      });\n    },\n    getFlowViewer(procInsId, deployKey) {\n      getFlowViewer(procInsId).then((res) => {\n        this.taskList = res.data;\n        this.getModelDetail(deployKey);\n      });\n    },\n    setIcon(val) {\n      if (val) {\n        return \"el-icon-check\";\n      } else {\n        return \"el-icon-time\";\n      }\n    },\n    setColor(val) {\n      if (val) {\n        return \"#2bc418\";\n      } else {\n        return \"#b3bdbb\";\n      }\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.userData = selection;\n      const val = selection.map((item) => item.userId)[0];\n      if (val instanceof Array) {\n        this.taskForm.values = {\n          approval: val.join(\",\"),\n        };\n      } else {\n        this.taskForm.values = {\n          approval: val,\n        };\n      }\n    },\n    // 关闭标签\n    handleClose(tag) {\n      this.userData.splice(this.userData.indexOf(tag), 1);\n    },\n    /** 流程变量赋值 */\n    handleCheckChange(val) {\n      if (val instanceof Array) {\n        this.taskForm.values = {\n          approval: val.join(\",\"),\n        };\n      } else {\n        this.taskForm.values = {\n          approval: val,\n        };\n      }\n    },\n    /** 流程流转记录 */\n    getFlowRecordList(procInsId) {\n      let that = this;\n      const params = { procInsId: procInsId };\n      flowRecord(params)\n        .then((res) => {\n          that.flowRecordList = res.data.flowList;\n          // 流程过程中不存在初始化表单 直接读取的流程变量中存储的表单值\n          if (res.data.formData) {\n            that.formConf = res.data.formData;\n            that.formConfOpen = true;\n          }\n          if (that.flowRecordLists) {\n            that.flowRecordLists.set(procInsId, that.flowRecordList);\n          }\n        })\n        .catch((res) => {\n          that.$router.go(0);\n        });\n    },\n    fillFormData(form, data) {\n      form.fields.forEach((item) => {\n        const val = data[item.__vModel__];\n        if (val) {\n          item.__config__.defaultValue = val;\n        }\n      });\n    },\n    /** 获取流程变量内容 */\n    processVariables(taskId) {\n      if (taskId) {\n        // 提交流程申请时填写的表单存入了流程变量中后续任务处理时需要展示\n        getProcessVariables(taskId).then((res) => {\n          // this.variables = res.data.variables;\n          this.variablesData = res.data.variables;\n          this.variableOpen = true;\n        });\n      }\n    },\n    /** 根据当前任务或者流程设计配置的下一步节点 */\n    getNextFlowNode(taskId) {\n      // 根据当前任务或者流程设计配置的下一步节点 todo 暂时未涉及到考虑网关、表达式和多节点情况\n      const params = { taskId: taskId };\n      getNextFlowNode(params).then((res) => {\n        const data = res.data;\n        if (data) {\n          if (data.type === \"assignee\") {\n            this.userDataList = res.data.userList;\n          } else if (data.type === \"candidateUsers\") {\n            this.userDataList = res.data.userList;\n            this.taskForm.multiple = true;\n          } else if (data.type === \"candidateGroups\") {\n            res.data.roleList.forEach((role) => {\n              role.userId = role.roleId;\n              role.nickName = role.roleName;\n            });\n            this.userDataList = res.data.roleList;\n            this.taskForm.multiple = false;\n          } else if (data.type === \"multiInstance\") {\n            this.userDataList = res.data.userList;\n            this.taskForm.multiple = true;\n          }\n          this.taskForm.sendUserShow = true;\n        } else {\n          this.canFinish = true;\n        }\n      });\n    },\n    /** 审批任务选择 */\n    handleComplete() {\n      this.completeOpen = true;\n      this.completeTitle = \"审批流程\";\n      //this.getTreeselect();\n    },\n    /** 审批任务 */\n    taskComplete(comment) {\n      // if (!this.taskForm.values){\n      //   this.msgError(\"请选择流程接收人员\");\n      //   return;\n      // }\n      if (\n        comment &&\n        typeof comment == \"string\" &&\n        comment.constructor == String\n      ) {\n        this.taskForm.comment = comment;\n      }\n      if (!this.taskForm.comment) {\n        this.msgError(\"请输入审批意见\");\n        return;\n      }\n      //if(this.canFinish){\n      this.taskForm.values = {};\n      this.taskForm.bizKey = this.bizKey;\n      this.taskForm.values.isReject = false;\n      //}\n      complete(this.taskForm).then((response) => {\n        this.msgSuccess(response.msg);\n        //this.goBack();\n        this.$router.go(0);\n      });\n    },\n    /** 委派任务 */\n    handleDelegate() {\n      this.taskForm.delegateTaskShow = true;\n      this.taskForm.defaultTaskShow = false;\n    },\n    handleAssign() {},\n    /** 返回页面 */\n    goBack() {\n      // 关闭当前标签页并返回上个页面\n      this.$store.dispatch(\"tagsView/delView\", this.$route);\n      this.$router.go(-1);\n    },\n    /** 接收子组件传的值 */\n    getData(data) {\n      if (data) {\n        const variables = [];\n        data.fields.forEach((item) => {\n          let variableData = {};\n          variableData.label = item.__config__.label;\n          // 表单值为多个选项时\n          if (item.__config__.defaultValue instanceof Array) {\n            const array = [];\n            item.__config__.defaultValue.forEach((val) => {\n              array.push(val);\n            });\n            variableData.val = array;\n          } else {\n            variableData.val = item.__config__.defaultValue;\n          }\n          variables.push(variableData);\n        });\n        this.variables = variables;\n      }\n    },\n    // 接收子组件图片上传的值\n    imageUrls(value) {\n      this.taskForm.authImages = value;\n    },\n    /** 申请流程表单数据提交 */\n    // submitForm(data) {\n    //   if (data) {\n    //     const variables = data.valData;\n    //     const formData = data.formData;\n    //     formData.disabled = true;\n    //     formData.formBtns = false;\n    //     if (this.taskForm.procDefKey) {\n    //       variables.variables = formData;\n    //       variables.businessKey = data.businessKey;\n    //        // 启动流程并将表单数据加入流程变量\n    //       definitionStartByKey(this.taskForm.procDefKey, JSON.stringify(variables)).then(res => {\n    //         this.msgSuccess(res.msg);\n    //         this.goBack();\n    //       })\n    //     }\n    //   }\n    // },\n    startFlow(businessKey, name, variables) {\n      let startDate = moment(new Date()).format(\"YYYYMMDDHHmmss\");\n      const data = {};\n      if (this.taskForm.procDefKey) {\n        if (!variables) {\n          data.variables = {};\n        } else {\n          data.variables = variables;\n        }\n        data.businessKey = businessKey;\n        data.procDefKey = this.taskForm.procDefKey;\n        data.taskName = name;\n        // 启动流程并将表单数据加入流程变量\n        definitionStartByKey(JSON.stringify(data)).then((res) => {\n          this.msgSuccess(res.msg);\n          this.goBack();\n        });\n      }\n    },\n    /** 驳回任务 */\n    handleReject() {\n      this.rejectOpen = true;\n      this.rejectTitle = \"退回流程\";\n    },\n    /** 驳回任务 */\n    taskReject() {\n      this.$refs[\"taskForm\"].validate((valid) => {\n        if (valid) {\n          rejectTask(this.taskForm).then((res) => {\n            this.msgSuccess(res.msg);\n            this.$router.go(0);\n          });\n        }\n      });\n    },\n    /** 可退回任务列表 */\n    handleReturn() {\n      this.returnOpen = true;\n      this.returnTitle = \"退回流程\";\n      returnList(this.taskForm).then((res) => {\n        this.returnTaskList = res.data;\n        this.taskForm.values = null;\n        if (res.data && res.data.length > 0) {\n          this.taskForm.targetKey = res.data[res.data.length - 1].id;\n        }\n      });\n    },\n    /** 提交退回任务 */\n    taskReturn() {\n      this.$refs[\"taskForm\"].validate((valid) => {\n        if (valid) {\n          //退回业务ID，用于修改状态\n          this.taskForm.bizKey = this.bizKey;\n          returnTask(this.taskForm).then((res) => {\n            this.msgSuccess(res.msg);\n            this.$router.go(0);\n          });\n        }\n      });\n    },\n    /** 取消回退任务按钮 */\n    cancelTask() {\n      this.taskForm.returnTaskShow = false;\n      this.taskForm.defaultTaskShow = true;\n      this.taskForm.sendUserShow = true;\n      this.returnTaskList = [];\n    },\n    /** 委派任务 */\n    submitDeleteTask() {\n      this.$refs[\"taskForm\"].validate((valid) => {\n        if (valid) {\n          delegate(this.taskForm).then((response) => {\n            this.msgSuccess(response.msg);\n            this.$router.go(0);\n          });\n        }\n      });\n    },\n    /** 取消回退任务按钮 */\n    cancelDelegateTask() {\n      this.taskForm.delegateTaskShow = false;\n      this.taskForm.defaultTaskShow = true;\n      this.taskForm.sendUserShow = true;\n      this.returnTaskList = [];\n    },\n    handleEnd() {\n      this.rejectOpen1 = true;\n      this.rejectTitle1 = \"驳回流程\";\n    },\n    /** 驳回结束任务 */\n    taskEnd() {\n      this.$refs[\"taskForm\"].validate((valid) => {\n        if (valid) {\n          this.taskForm.values = {};\n          this.taskForm.values.isReject = true;\n          endTask(this.taskForm).then((res) => {\n            this.msgSuccess(res.msg);\n            this.$router.go(0);\n          });\n        }\n      });\n    },\n    handleClick(tab, event) {\n      if (this.flowRecordLists && this.flowRecordLists.get(tab.name)) {\n        this.flowRecordList = this.flowRecordLists.get(tab.name);\n      } else {\n        this.getFlowRecordList(tab.name);\n      }\n    },\n  },\n};\n</script>\n<style lang=\"scss\" scoped>\n@media screen and (max-width: 600px) {\n  .el-timeline {\n    margin: 0;\n    font-size: 14px;\n    list-style: none;\n    padding-left: 0;\n  }\n\n  ::v-deep .audit-record .el-card__body {\n    display: flex;\n    flex-direction: column;\n  }\n}\n\n.test-form {\n  margin: 15px auto;\n  width: 800px;\n  padding: 15px;\n}\n\n.clearfix:before,\n.clearfix:after {\n  display: table;\n  content: \"\";\n}\n.clearfix:after {\n  clear: both;\n}\n\n.box-card {\n  width: 100%;\n  margin-bottom: 20px;\n}\n\n.el-tag + .el-tag {\n  margin-left: 10px;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiWA,SAAAA,UAAA,EAAAC,SAAA;AACA,OAAAC,MAAA;AACA,OAAAC,MAAA;AACA,SACAC,oBAAA,EACAC,mBAAA,EACAC,YAAA,EACAC,aAAA,IAAAA,cAAA,QACA;AACA,SACAC,QAAA,EACAC,UAAA,EACAC,UAAA,EACAC,UAAA,EACAC,eAAA,IAAAA,gBAAA,EACAC,QAAA,EACAC,OAAA,QACA;AACA,OAAAC,IAAA;AACA,SAAAC,UAAA;AACA;AACA,OAAAC,UAAA;AACA,SAAAC,QAAA;AACA,OAAAC,MAAA;AACA;EACAC,IAAA;EACAC,UAAA;IACAnB,MAAA,EAAAA,MAAA;IACAa,IAAA,EAAAA,IAAA;IACAE,UAAA,EAAAA,UAAA;IACAd,MAAA,EAAAA;EACA;EACAmB,KAAA;IACAC,UAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA,EAAAC;IACA;IACAC,MAAA;MACAJ,IAAA,EAAAC,MAAA;MACAC,OAAA,EAAAC;IACA;IACAE,SAAA;MACAL,IAAA,EAAAC,MAAA;MACAC,OAAA,EAAAC;IACA;IACAG,MAAA;MACAN,IAAA,EAAAC,MAAA;MACAC,OAAA,EAAAC;IACA;IACAI,QAAA;MACAP,IAAA,EAAAQ,OAAA;MACAN,OAAA;IACA;IACAO,YAAA;MACAT,IAAA,EAAAQ,OAAA;MACAN,OAAA;IACA;IACAQ,QAAA;MACAV,IAAA,EAAAQ,OAAA;MACAN,OAAA;IACA;EACA;EACAS,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACAC,QAAA;MACA;MACAC,QAAA,EAAAX,SAAA;MACA;MACAY,WAAA,EAAAZ,SAAA;MACA;MACAa,QAAA;MACAC,YAAA;QACAC,QAAA;QACAC,KAAA;MACA;MACA;MACAC,WAAA;QACAC,MAAA,EAAAlB;MACA;MACAmB,OAAA;MACA;MACAC,OAAA;MACAC,cAAA;MAAA;MACAC,eAAA;MACAC,YAAA;MACAC,GAAA;MACAC,KAAA;MAAA;MACAC,aAAA;MAAA;MACAC,QAAA;QACAC,cAAA;QAAA;QACAC,gBAAA;QAAA;QACAC,eAAA;QAAA;QACAC,YAAA;QAAA;QACAC,QAAA;QACAC,OAAA;QAAA;QACA/B,SAAA;QAAA;QACAgC,UAAA;QAAA;QACAjC,MAAA;QAAA;QACAL,UAAA;QAAA;QACAuC,IAAA;QACAC,SAAA;QACAC,UAAA;MACA;MACAC,YAAA;MAAA;MACAC,QAAA;MACAC,QAAA;MAAA;MACAC,YAAA;MAAA;MACAC,SAAA;MAAA;MACAC,aAAA;MAAA;MACAC,YAAA;MAAA;MACAC,cAAA;MAAA;MACAC,aAAA;MACAC,YAAA;MACAC,WAAA;MACAC,UAAA;MACAC,UAAA;MACAC,WAAA;MACAC,WAAA;MACAC,YAAA;MACAC,QAAA;MACAC,KAAA;MACAC,SAAA;MACAC,OAAA;MACAC,UAAA;MACAvD,MAAA;MACAwD,QAAA;MACAtB,UAAA;IACA;EACA;EACAuB,OAAA,WAAAA,QAAA;IACA,IACA,KAAAC,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,KAAA,IACA,KAAAH,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,KAAA,CAAAC,QAAA,YACA;MACA,KAAAN,QAAA;IACA;IACAO,OAAA,CAAAC,GAAA;IACAD,OAAA,CAAAC,GAAA,MAAAC,MAAA;IACA,IAAAC,IAAA;IACA,IAAAC,YAAA,QAAAF,MAAA;MAAAnE,MAAA,GAAAqE,YAAA,CAAArE,MAAA;MAAAL,UAAA,GAAA0E,YAAA,CAAA1E,UAAA;MAAAM,SAAA,GAAAoE,YAAA,CAAApE,SAAA;MAAAE,QAAA,GAAAkE,YAAA,CAAAlE,QAAA;MAAAD,MAAA,GAAAmE,YAAA,CAAAnE,MAAA;IACA;IACA;IACA;IACA;IACA,KAAAwB,QAAA,CAAA1B,MAAA,GAAAA,MAAA;IACA,KAAA0B,QAAA,CAAAzB,SAAA,GAAAA,SAAA;IACA,KAAAyB,QAAA,CAAAO,UAAA,GAAAhC,SAAA;IACA;IACA,KAAAyB,QAAA,CAAA/B,UAAA,GAAAA,UAAA;IACA,KAAAO,MAAA,GAAAA,MAAA;IACA;IACAkE,IAAA,CAAAZ,OAAA;IACAY,IAAA,CAAA/C,eAAA;IACA;IACA,IAAApB,SAAA;MACA,KAAAtB,aAAA,MAAA+C,QAAA,CAAAzB,SAAA,OAAAyB,QAAA,CAAA/B,UAAA;MACA;MACA,SAAA+B,QAAA,CAAA1B,MAAA;QACA,KAAAsE,gBAAA,MAAA5C,QAAA,CAAA1B,MAAA;QACA,KAAAhB,eAAA,MAAA0C,QAAA,CAAA1B,MAAA;MACA;MACA,KAAAuE,iBAAA,MAAA7C,QAAA,CAAAzB,SAAA;MACAmE,IAAA,CAAAX,UAAA,GAAAxD,SAAA;MACA5B,SAAA;QACA6B,MAAA,EAAAkE,IAAA,CAAAlE,MAAA;QACAsE,MAAA,EAAA7E;MACA,GAAA8E,IAAA,WAAAC,IAAA;QACA,IAAAA,IAAA,CAAAnE,IAAA,IAAAmE,IAAA,CAAAnE,IAAA,CAAAoE,MAAA;UACAP,IAAA,CAAA1C,QAAA,CAAAkD,QAAA,GAAAF,IAAA,CAAAnE,IAAA,IAAAf,IAAA;QACA;QACA,IAAAkF,IAAA,CAAAnE,IAAA,CAAAoE,MAAA;UACAP,IAAA,CAAAZ,OAAA,GAAAkB,IAAA,CAAAnE,IAAA;UACA6D,IAAA,CAAA/C,eAAA,OAAAwD,GAAA;UACAT,IAAA,CAAA/C,eAAA,CAAAyD,GAAA,CAAA7E,SAAA,EAAAmE,IAAA,CAAAhD,cAAA;QACA;MACA;IACA;MACA,KAAA2D,cAAA,CAAApF,UAAA;IACA;IACA,KAAAQ,QAAA,GAAAA,QAAA;EACA;EACA6E,OAAA,WAAAA,QAAA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EAAA,CACA;EACAC,OAAA;IACA,gBACAC,aAAA,WAAAA,cAAA;MAAA,IAAAC,KAAA;MACA/F,UAAA,GAAAqF,IAAA,WAAAW,QAAA;QACAD,KAAA,CAAAxE,WAAA,GAAAyE,QAAA,CAAA7E,IAAA;MACA;IACA;IACA,aACA8E,OAAA,WAAAA,QAAA;MAAA,IAAAC,MAAA;MACAhG,QAAA,MAAAiG,YAAA,MAAAvE,WAAA,OAAAwE,SAAA,GAAAf,IAAA,CACA,UAAAW,QAAA;QACAE,MAAA,CAAA1E,QAAA,GAAAwE,QAAA,CAAAK,IAAA;QACAH,MAAA,CAAAI,KAAA,GAAAN,QAAA,CAAAM,KAAA;MACA,CACA;IACA;IACA;IACAC,UAAA,WAAAA,WAAAC,KAAA,EAAArF,IAAA;MACA,KAAAqF,KAAA;MACA,OAAArF,IAAA,CAAAQ,KAAA,CAAA8E,OAAA,CAAAD,KAAA;IACA;IACA;IACAE,eAAA,WAAAA,gBAAAvF,IAAA;MACA,KAAAS,WAAA,CAAAC,MAAA,GAAAV,IAAA,CAAAwF,EAAA;MACA,KAAAV,OAAA;IACA;IACA,aACAN,cAAA,WAAAA,eAAAiB,SAAA;MAAA,IAAAC,MAAA;MACA;MACAvH,YAAA,CAAAsH,SAAA,EAAAvB,IAAA,WAAAyB,GAAA;QACAD,MAAA,CAAAzF,OAAA,GAAA0F,GAAA,CAAA3F,IAAA;MACA;IACA;IACA5B,aAAA,WAAAA,cAAAsB,SAAA,EAAA+F,SAAA;MAAA,IAAAG,MAAA;MACAxH,cAAA,CAAAsB,SAAA,EAAAwE,IAAA,WAAAyB,GAAA;QACAC,MAAA,CAAA1F,QAAA,GAAAyF,GAAA,CAAA3F,IAAA;QACA4F,MAAA,CAAApB,cAAA,CAAAiB,SAAA;MACA;IACA;IACAI,OAAA,WAAAA,QAAAC,GAAA;MACA,IAAAA,GAAA;QACA;MACA;QACA;MACA;IACA;IACAC,QAAA,WAAAA,SAAAD,GAAA;MACA,IAAAA,GAAA;QACA;MACA;QACA;MACA;IACA;IACA;IACAE,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAnD,QAAA,GAAAmD,SAAA;MACA,IAAAH,GAAA,GAAAG,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,MAAA;MAAA;MACA,IAAAN,GAAA,YAAAO,KAAA;QACA,KAAAlF,QAAA,CAAAmF,MAAA;UACAC,QAAA,EAAAT,GAAA,CAAAU,IAAA;QACA;MACA;QACA,KAAArF,QAAA,CAAAmF,MAAA;UACAC,QAAA,EAAAT;QACA;MACA;IACA;IACA;IACAW,WAAA,WAAAA,YAAAC,GAAA;MACA,KAAA5D,QAAA,CAAA6D,MAAA,MAAA7D,QAAA,CAAAwC,OAAA,CAAAoB,GAAA;IACA;IACA,aACAE,iBAAA,WAAAA,kBAAAd,GAAA;MACA,IAAAA,GAAA,YAAAO,KAAA;QACA,KAAAlF,QAAA,CAAAmF,MAAA;UACAC,QAAA,EAAAT,GAAA,CAAAU,IAAA;QACA;MACA;QACA,KAAArF,QAAA,CAAAmF,MAAA;UACAC,QAAA,EAAAT;QACA;MACA;IACA;IACA,aACA9B,iBAAA,WAAAA,kBAAAtE,SAAA;MACA,IAAAmE,IAAA;MACA,IAAAgD,MAAA;QAAAnH,SAAA,EAAAA;MAAA;MACA7B,UAAA,CAAAgJ,MAAA,EACA3C,IAAA,WAAAyB,GAAA;QACA9B,IAAA,CAAAhD,cAAA,GAAA8E,GAAA,CAAA3F,IAAA,CAAA8G,QAAA;QACA;QACA,IAAAnB,GAAA,CAAA3F,IAAA,CAAA+G,QAAA;UACAlD,IAAA,CAAA7B,QAAA,GAAA2D,GAAA,CAAA3F,IAAA,CAAA+G,QAAA;UACAlD,IAAA,CAAA5B,YAAA;QACA;QACA,IAAA4B,IAAA,CAAA/C,eAAA;UACA+C,IAAA,CAAA/C,eAAA,CAAAyD,GAAA,CAAA7E,SAAA,EAAAmE,IAAA,CAAAhD,cAAA;QACA;MACA,GACAmG,KAAA,WAAArB,GAAA;QACA9B,IAAA,CAAAoD,OAAA,CAAAC,EAAA;MACA;IACA;IACAC,YAAA,WAAAA,aAAAC,IAAA,EAAApH,IAAA;MACAoH,IAAA,CAAAC,MAAA,CAAAC,OAAA,WAAAnB,IAAA;QACA,IAAAL,GAAA,GAAA9F,IAAA,CAAAmG,IAAA,CAAAoB,UAAA;QACA,IAAAzB,GAAA;UACAK,IAAA,CAAAqB,UAAA,CAAAC,YAAA,GAAA3B,GAAA;QACA;MACA;IACA;IACA,eACA/B,gBAAA,WAAAA,iBAAAtE,MAAA;MAAA,IAAAiI,MAAA;MACA,IAAAjI,MAAA;QACA;QACAvB,mBAAA,CAAAuB,MAAA,EAAAyE,IAAA,WAAAyB,GAAA;UACA;UACA+B,MAAA,CAAAvF,aAAA,GAAAwD,GAAA,CAAA3F,IAAA,CAAAkC,SAAA;UACAwF,MAAA,CAAAtF,YAAA;QACA;MACA;IACA;IACA,2BACA3D,eAAA,WAAAA,gBAAAgB,MAAA;MAAA,IAAAkI,MAAA;MACA;MACA,IAAAd,MAAA;QAAApH,MAAA,EAAAA;MAAA;MACAhB,gBAAA,CAAAoI,MAAA,EAAA3C,IAAA,WAAAyB,GAAA;QACA,IAAA3F,IAAA,GAAA2F,GAAA,CAAA3F,IAAA;QACA,IAAAA,IAAA;UACA,IAAAA,IAAA,CAAAX,IAAA;YACAsI,MAAA,CAAA7F,YAAA,GAAA6D,GAAA,CAAA3F,IAAA,CAAAK,QAAA;UACA,WAAAL,IAAA,CAAAX,IAAA;YACAsI,MAAA,CAAA7F,YAAA,GAAA6D,GAAA,CAAA3F,IAAA,CAAAK,QAAA;YACAsH,MAAA,CAAAxG,QAAA,CAAAK,QAAA;UACA,WAAAxB,IAAA,CAAAX,IAAA;YACAsG,GAAA,CAAA3F,IAAA,CAAA4H,QAAA,CAAAN,OAAA,WAAAO,IAAA;cACAA,IAAA,CAAAzB,MAAA,GAAAyB,IAAA,CAAAC,MAAA;cACAD,IAAA,CAAAE,QAAA,GAAAF,IAAA,CAAAG,QAAA;YACA;YACAL,MAAA,CAAA7F,YAAA,GAAA6D,GAAA,CAAA3F,IAAA,CAAA4H,QAAA;YACAD,MAAA,CAAAxG,QAAA,CAAAK,QAAA;UACA,WAAAxB,IAAA,CAAAX,IAAA;YACAsI,MAAA,CAAA7F,YAAA,GAAA6D,GAAA,CAAA3F,IAAA,CAAAK,QAAA;YACAsH,MAAA,CAAAxG,QAAA,CAAAK,QAAA;UACA;UACAmG,MAAA,CAAAxG,QAAA,CAAAI,YAAA;QACA;UACAoG,MAAA,CAAA3E,SAAA;QACA;MACA;IACA;IACA,aACAiF,cAAA,WAAAA,eAAA;MACA,KAAA1F,YAAA;MACA,KAAAD,aAAA;MACA;IACA;IACA,WACA4F,YAAA,WAAAA,aAAAzG,OAAA;MAAA,IAAA0G,MAAA;MACA;MACA;MACA;MACA;MACA,IACA1G,OAAA,IACA,OAAAA,OAAA,gBACAA,OAAA,CAAA2G,WAAA,IAAA9I,MAAA,EACA;QACA,KAAA6B,QAAA,CAAAM,OAAA,GAAAA,OAAA;MACA;MACA,UAAAN,QAAA,CAAAM,OAAA;QACA,KAAA4G,QAAA;QACA;MACA;MACA;MACA,KAAAlH,QAAA,CAAAmF,MAAA;MACA,KAAAnF,QAAA,CAAAxB,MAAA,QAAAA,MAAA;MACA,KAAAwB,QAAA,CAAAmF,MAAA,CAAAgC,QAAA;MACA;MACAjK,QAAA,MAAA8C,QAAA,EAAA+C,IAAA,WAAAW,QAAA;QACAsD,MAAA,CAAAI,UAAA,CAAA1D,QAAA,CAAA2D,GAAA;QACA;QACAL,MAAA,CAAAlB,OAAA,CAAAC,EAAA;MACA;IACA;IACA,WACAuB,cAAA,WAAAA,eAAA;MACA,KAAAtH,QAAA,CAAAE,gBAAA;MACA,KAAAF,QAAA,CAAAG,eAAA;IACA;IACAoH,YAAA,WAAAA,aAAA;IACA,WACAC,MAAA,WAAAA,OAAA;MACA;MACA,KAAAtF,MAAA,CAAAuF,QAAA,0BAAAC,MAAA;MACA,KAAA5B,OAAA,CAAAC,EAAA;IACA;IACA,eACA4B,OAAA,WAAAA,QAAA9I,IAAA;MACA,IAAAA,IAAA;QACA,IAAAkC,SAAA;QACAlC,IAAA,CAAAqH,MAAA,CAAAC,OAAA,WAAAnB,IAAA;UACA,IAAA4C,YAAA;UACAA,YAAA,CAAAvI,KAAA,GAAA2F,IAAA,CAAAqB,UAAA,CAAAhH,KAAA;UACA;UACA,IAAA2F,IAAA,CAAAqB,UAAA,CAAAC,YAAA,YAAApB,KAAA;YACA,IAAA2C,KAAA;YACA7C,IAAA,CAAAqB,UAAA,CAAAC,YAAA,CAAAH,OAAA,WAAAxB,GAAA;cACAkD,KAAA,CAAAC,IAAA,CAAAnD,GAAA;YACA;YACAiD,YAAA,CAAAjD,GAAA,GAAAkD,KAAA;UACA;YACAD,YAAA,CAAAjD,GAAA,GAAAK,IAAA,CAAAqB,UAAA,CAAAC,YAAA;UACA;UACAvF,SAAA,CAAA+G,IAAA,CAAAF,YAAA;QACA;QACA,KAAA7G,SAAA,GAAAA,SAAA;MACA;IACA;IACA;IACAgH,SAAA,WAAAA,UAAA7D,KAAA;MACA,KAAAlE,QAAA,CAAAU,UAAA,GAAAwD,KAAA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA8D,SAAA,WAAAA,UAAAC,WAAA,EAAAnK,IAAA,EAAAiD,SAAA;MAAA,IAAAmH,MAAA;MACA,IAAAC,SAAA,GAAAtK,MAAA,KAAAuK,IAAA,IAAAC,MAAA;MACA,IAAAxJ,IAAA;MACA,SAAAmB,QAAA,CAAA/B,UAAA;QACA,KAAA8C,SAAA;UACAlC,IAAA,CAAAkC,SAAA;QACA;UACAlC,IAAA,CAAAkC,SAAA,GAAAA,SAAA;QACA;QACAlC,IAAA,CAAAoJ,WAAA,GAAAA,WAAA;QACApJ,IAAA,CAAAZ,UAAA,QAAA+B,QAAA,CAAA/B,UAAA;QACAY,IAAA,CAAAqE,QAAA,GAAApF,IAAA;QACA;QACAhB,oBAAA,CAAAwL,IAAA,CAAAC,SAAA,CAAA1J,IAAA,GAAAkE,IAAA,WAAAyB,GAAA;UACA0D,MAAA,CAAAd,UAAA,CAAA5C,GAAA,CAAA6C,GAAA;UACAa,MAAA,CAAAV,MAAA;QACA;MACA;IACA;IACA,WACAgB,YAAA,WAAAA,aAAA;MACA,KAAAjH,UAAA;MACA,KAAAC,WAAA;IACA;IACA,WACAiH,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,aAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA1L,UAAA,CAAAuL,MAAA,CAAA1I,QAAA,EAAA+C,IAAA,WAAAyB,GAAA;YACAkE,MAAA,CAAAtB,UAAA,CAAA5C,GAAA,CAAA6C,GAAA;YACAqB,MAAA,CAAA5C,OAAA,CAAAC,EAAA;UACA;QACA;MACA;IACA;IACA,cACA+C,YAAA,WAAAA,aAAA;MAAA,IAAAC,OAAA;MACA,KAAAzH,UAAA;MACA,KAAAD,WAAA;MACAjE,UAAA,MAAA4C,QAAA,EAAA+C,IAAA,WAAAyB,GAAA;QACAuE,OAAA,CAAA7H,cAAA,GAAAsD,GAAA,CAAA3F,IAAA;QACAkK,OAAA,CAAA/I,QAAA,CAAAmF,MAAA;QACA,IAAAX,GAAA,CAAA3F,IAAA,IAAA2F,GAAA,CAAA3F,IAAA,CAAAoE,MAAA;UACA8F,OAAA,CAAA/I,QAAA,CAAAS,SAAA,GAAA+D,GAAA,CAAA3F,IAAA,CAAA2F,GAAA,CAAA3F,IAAA,CAAAoE,MAAA,MAAAoB,EAAA;QACA;MACA;IACA;IACA,aACA2E,UAAA,WAAAA,WAAA;MAAA,IAAAC,OAAA;MACA,KAAAN,KAAA,aAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA;UACAI,OAAA,CAAAjJ,QAAA,CAAAxB,MAAA,GAAAyK,OAAA,CAAAzK,MAAA;UACAnB,UAAA,CAAA4L,OAAA,CAAAjJ,QAAA,EAAA+C,IAAA,WAAAyB,GAAA;YACAyE,OAAA,CAAA7B,UAAA,CAAA5C,GAAA,CAAA6C,GAAA;YACA4B,OAAA,CAAAnD,OAAA,CAAAC,EAAA;UACA;QACA;MACA;IACA;IACA,eACAmD,UAAA,WAAAA,WAAA;MACA,KAAAlJ,QAAA,CAAAC,cAAA;MACA,KAAAD,QAAA,CAAAG,eAAA;MACA,KAAAH,QAAA,CAAAI,YAAA;MACA,KAAAc,cAAA;IACA;IACA,WACAiI,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,OAAA;MACA,KAAAT,KAAA,aAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAtL,QAAA,CAAA6L,OAAA,CAAApJ,QAAA,EAAA+C,IAAA,WAAAW,QAAA;YACA0F,OAAA,CAAAhC,UAAA,CAAA1D,QAAA,CAAA2D,GAAA;YACA+B,OAAA,CAAAtD,OAAA,CAAAC,EAAA;UACA;QACA;MACA;IACA;IACA,eACAsD,kBAAA,WAAAA,mBAAA;MACA,KAAArJ,QAAA,CAAAE,gBAAA;MACA,KAAAF,QAAA,CAAAG,eAAA;MACA,KAAAH,QAAA,CAAAI,YAAA;MACA,KAAAc,cAAA;IACA;IACAoI,SAAA,WAAAA,UAAA;MACA,KAAA7H,WAAA;MACA,KAAAC,YAAA;IACA;IACA,aACA6H,OAAA,WAAAA,QAAA;MAAA,IAAAC,OAAA;MACA,KAAAb,KAAA,aAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAW,OAAA,CAAAxJ,QAAA,CAAAmF,MAAA;UACAqE,OAAA,CAAAxJ,QAAA,CAAAmF,MAAA,CAAAgC,QAAA;UACA3J,OAAA,CAAAgM,OAAA,CAAAxJ,QAAA,EAAA+C,IAAA,WAAAyB,GAAA;YACAgF,OAAA,CAAApC,UAAA,CAAA5C,GAAA,CAAA6C,GAAA;YACAmC,OAAA,CAAA1D,OAAA,CAAAC,EAAA;UACA;QACA;MACA;IACA;IACA0D,WAAA,WAAAA,YAAAC,GAAA,EAAAC,KAAA;MACA,SAAAhK,eAAA,SAAAA,eAAA,CAAAiK,GAAA,CAAAF,GAAA,CAAA5L,IAAA;QACA,KAAA4B,cAAA,QAAAC,eAAA,CAAAiK,GAAA,CAAAF,GAAA,CAAA5L,IAAA;MACA;QACA,KAAA+E,iBAAA,CAAA6G,GAAA,CAAA5L,IAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}