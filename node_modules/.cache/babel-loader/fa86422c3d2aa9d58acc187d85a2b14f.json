{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/api/flowable/form.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/api/flowable/form.js", "mtime": 1641277326000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QgZnJvbSAnQC91dGlscy9yZXF1ZXN0JzsKCi8vIOafpeivoua1geeoi+ihqOWNleWIl+ihqApleHBvcnQgZnVuY3Rpb24gbGlzdEZvcm0ocXVlcnkpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvZmxvd2FibGUvZm9ybS9saXN0JywKICAgIG1ldGhvZDogJ2dldCcsCiAgICBwYXJhbXM6IHF1ZXJ5CiAgfSk7Cn0KCi8vIOafpeivoua1geeoi+ihqOWNleivpue7hgpleHBvcnQgZnVuY3Rpb24gZ2V0Rm9ybShmb3JtSWQpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvZmxvd2FibGUvZm9ybS8nICsgZm9ybUlkLAogICAgbWV0aG9kOiAnZ2V0JwogIH0pOwp9CgovLyDmlrDlop7mtYHnqIvooajljZUKZXhwb3J0IGZ1bmN0aW9uIGFkZEZvcm0oZGF0YSkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9mbG93YWJsZS9mb3JtJywKICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgZGF0YTogZGF0YQogIH0pOwp9CgovLyDkv67mlLnmtYHnqIvooajljZUKZXhwb3J0IGZ1bmN0aW9uIHVwZGF0ZUZvcm0oZGF0YSkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9mbG93YWJsZS9mb3JtJywKICAgIG1ldGhvZDogJ3B1dCcsCiAgICBkYXRhOiBkYXRhCiAgfSk7Cn0KLy8g5oyC6L296KGo5Y2VCmV4cG9ydCBmdW5jdGlvbiBhZGREZXBsb3lGb3JtKGRhdGEpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvZmxvd2FibGUvZm9ybS9hZGREZXBsb3lGb3JtJywKICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgZGF0YTogZGF0YQogIH0pOwp9CgovLyDliKDpmaTmtYHnqIvooajljZUKZXhwb3J0IGZ1bmN0aW9uIGRlbEZvcm0oZm9ybUlkKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL2Zsb3dhYmxlL2Zvcm0vJyArIGZvcm1JZCwKICAgIG1ldGhvZDogJ2RlbGV0ZScKICB9KTsKfQoKLy8g5a+85Ye65rWB56iL6KGo5Y2VCmV4cG9ydCBmdW5jdGlvbiBleHBvcnRGb3JtKHF1ZXJ5KSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL2Zsb3dhYmxlL2Zvcm0vZXhwb3J0JywKICAgIG1ldGhvZDogJ2dldCcsCiAgICBwYXJhbXM6IHF1ZXJ5CiAgfSk7Cn0="}, {"version": 3, "names": ["request", "listForm", "query", "url", "method", "params", "getForm", "formId", "addForm", "data", "updateForm", "addDeployForm", "delForm", "exportForm"], "sources": ["/Users/<USER>/develop/code/project/haijia/adminweb/src/api/flowable/form.js"], "sourcesContent": ["import request from '@/utils/request'\n\n// 查询流程表单列表\nexport function listForm(query) {\n  return request({\n    url: '/flowable/form/list',\n    method: 'get',\n    params: query\n  })\n}\n\n// 查询流程表单详细\nexport function getForm(formId) {\n  return request({\n    url: '/flowable/form/' + formId,\n    method: 'get'\n  })\n}\n\n// 新增流程表单\nexport function addForm(data) {\n  return request({\n    url: '/flowable/form',\n    method: 'post',\n    data: data\n  })\n}\n\n// 修改流程表单\nexport function updateForm(data) {\n  return request({\n    url: '/flowable/form',\n    method: 'put',\n    data: data\n  })\n}\n// 挂载表单\nexport function addDeployForm(data) {\n  return request({\n    url: '/flowable/form/addDeployForm',\n    method: 'post',\n    data: data\n  })\n}\n\n// 删除流程表单\nexport function delForm(formId) {\n  return request({\n    url: '/flowable/form/' + formId,\n    method: 'delete'\n  })\n}\n\n// 导出流程表单\nexport function exportForm(query) {\n  return request({\n    url: '/flowable/form/export',\n    method: 'get',\n    params: query\n  })\n}\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;;AAErC;AACA,OAAO,SAASC,QAAQA,CAACC,KAAK,EAAE;EAC9B,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,qBAAqB;IAC1BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEH;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASI,OAAOA,CAACC,MAAM,EAAE;EAC9B,OAAOP,OAAO,CAAC;IACbG,GAAG,EAAE,iBAAiB,GAAGI,MAAM;IAC/BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASI,OAAOA,CAACC,IAAI,EAAE;EAC5B,OAAOT,OAAO,CAAC;IACbG,GAAG,EAAE,gBAAgB;IACrBC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASC,UAAUA,CAACD,IAAI,EAAE;EAC/B,OAAOT,OAAO,CAAC;IACbG,GAAG,EAAE,gBAAgB;IACrBC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;AACA;AACA,OAAO,SAASE,aAAaA,CAACF,IAAI,EAAE;EAClC,OAAOT,OAAO,CAAC;IACbG,GAAG,EAAE,8BAA8B;IACnCC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASG,OAAOA,CAACL,MAAM,EAAE;EAC9B,OAAOP,OAAO,CAAC;IACbG,GAAG,EAAE,iBAAiB,GAAGI,MAAM;IAC/BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASS,UAAUA,CAACX,KAAK,EAAE;EAChC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEH;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}