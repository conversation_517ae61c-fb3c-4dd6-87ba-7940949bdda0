{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/api/system/role.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/api/system/role.js", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QgZnJvbSAnQC91dGlscy9yZXF1ZXN0JzsKCi8vIOafpeivouinkuiJsuWIl+ihqApleHBvcnQgZnVuY3Rpb24gbGlzdFJvbGUocXVlcnkpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvc3lzdGVtL3JvbGUvbGlzdCcsCiAgICBtZXRob2Q6ICdnZXQnLAogICAgcGFyYW1zOiBxdWVyeQogIH0pOwp9CgovLyDmn6Xor6Lop5LoibLor6bnu4YKZXhwb3J0IGZ1bmN0aW9uIGdldFJvbGUocm9sZUlkKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL3N5c3RlbS9yb2xlLycgKyByb2xlSWQsCiAgICBtZXRob2Q6ICdnZXQnCiAgfSk7Cn0KCi8vIOaWsOWinuinkuiJsgpleHBvcnQgZnVuY3Rpb24gYWRkUm9sZShkYXRhKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL3N5c3RlbS9yb2xlJywKICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgZGF0YTogZGF0YQogIH0pOwp9CgovLyDkv67mlLnop5LoibIKZXhwb3J0IGZ1bmN0aW9uIHVwZGF0ZVJvbGUoZGF0YSkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9zeXN0ZW0vcm9sZScsCiAgICBtZXRob2Q6ICdwdXQnLAogICAgZGF0YTogZGF0YQogIH0pOwp9CgovLyDop5LoibLmlbDmja7mnYPpmZAKZXhwb3J0IGZ1bmN0aW9uIGRhdGFTY29wZShkYXRhKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL3N5c3RlbS9yb2xlL2RhdGFTY29wZScsCiAgICBtZXRob2Q6ICdwdXQnLAogICAgZGF0YTogZGF0YQogIH0pOwp9CgovLyDop5LoibLnirbmgIHkv67mlLkKZXhwb3J0IGZ1bmN0aW9uIGNoYW5nZVJvbGVTdGF0dXMocm9sZUlkLCBzdGF0dXMpIHsKICB2YXIgZGF0YSA9IHsKICAgIHJvbGVJZDogcm9sZUlkLAogICAgc3RhdHVzOiBzdGF0dXMKICB9OwogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9zeXN0ZW0vcm9sZS9jaGFuZ2VTdGF0dXMnLAogICAgbWV0aG9kOiAncHV0JywKICAgIGRhdGE6IGRhdGEKICB9KTsKfQoKLy8g5Yig6Zmk6KeS6ImyCmV4cG9ydCBmdW5jdGlvbiBkZWxSb2xlKHJvbGVJZCkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9zeXN0ZW0vcm9sZS8nICsgcm9sZUlkLAogICAgbWV0aG9kOiAnZGVsZXRlJwogIH0pOwp9CgovLyDlr7zlh7rop5LoibIKZXhwb3J0IGZ1bmN0aW9uIGV4cG9ydFJvbGUocXVlcnkpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvc3lzdGVtL3JvbGUvZXhwb3J0JywKICAgIG1ldGhvZDogJ2dldCcsCiAgICBwYXJhbXM6IHF1ZXJ5CiAgfSk7Cn0="}, {"version": 3, "names": ["request", "listRole", "query", "url", "method", "params", "getRole", "roleId", "addRole", "data", "updateRole", "dataScope", "changeRoleStatus", "status", "delRole", "exportRole"], "sources": ["/Users/<USER>/develop/code/project/haijia/adminweb/src/api/system/role.js"], "sourcesContent": ["import request from '@/utils/request'\n\n// 查询角色列表\nexport function listRole(query) {\n  return request({\n    url: '/system/role/list',\n    method: 'get',\n    params: query\n  })\n}\n\n// 查询角色详细\nexport function getRole(roleId) {\n  return request({\n    url: '/system/role/' + roleId,\n    method: 'get'\n  })\n}\n\n// 新增角色\nexport function addRole(data) {\n  return request({\n    url: '/system/role',\n    method: 'post',\n    data: data\n  })\n}\n\n// 修改角色\nexport function updateRole(data) {\n  return request({\n    url: '/system/role',\n    method: 'put',\n    data: data\n  })\n}\n\n// 角色数据权限\nexport function dataScope(data) {\n  return request({\n    url: '/system/role/dataScope',\n    method: 'put',\n    data: data\n  })\n}\n\n// 角色状态修改\nexport function changeRoleStatus(roleId, status) {\n  const data = {\n    roleId,\n    status\n  }\n  return request({\n    url: '/system/role/changeStatus',\n    method: 'put',\n    data: data\n  })\n}\n\n// 删除角色\nexport function delRole(roleId) {\n  return request({\n    url: '/system/role/' + roleId,\n    method: 'delete'\n  })\n}\n\n// 导出角色\nexport function exportRole(query) {\n  return request({\n    url: '/system/role/export',\n    method: 'get',\n    params: query\n  })\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;;AAErC;AACA,OAAO,SAASC,QAAQA,CAACC,KAAK,EAAE;EAC9B,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEH;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASI,OAAOA,CAACC,MAAM,EAAE;EAC9B,OAAOP,OAAO,CAAC;IACbG,GAAG,EAAE,eAAe,GAAGI,MAAM;IAC7BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASI,OAAOA,CAACC,IAAI,EAAE;EAC5B,OAAOT,OAAO,CAAC;IACbG,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASC,UAAUA,CAACD,IAAI,EAAE;EAC/B,OAAOT,OAAO,CAAC;IACbG,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASE,SAASA,CAACF,IAAI,EAAE;EAC9B,OAAOT,OAAO,CAAC;IACbG,GAAG,EAAE,wBAAwB;IAC7BC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASG,gBAAgBA,CAACL,MAAM,EAAEM,MAAM,EAAE;EAC/C,IAAMJ,IAAI,GAAG;IACXF,MAAM,EAANA,MAAM;IACNM,MAAM,EAANA;EACF,CAAC;EACD,OAAOb,OAAO,CAAC;IACbG,GAAG,EAAE,2BAA2B;IAChCC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASK,OAAOA,CAACP,MAAM,EAAE;EAC9B,OAAOP,OAAO,CAAC;IACbG,GAAG,EAAE,eAAe,GAAGI,MAAM;IAC7BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASW,UAAUA,CAACb,KAAK,EAAE;EAChC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,qBAAqB;IAC1BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEH;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}