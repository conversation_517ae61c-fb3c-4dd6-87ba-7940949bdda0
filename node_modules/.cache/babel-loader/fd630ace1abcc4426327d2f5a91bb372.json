{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/utils/request.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/utils/request.js", "mtime": 1716992753042}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/thread-loader/dist/cjs.js", "mtime": 1751171659043}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}