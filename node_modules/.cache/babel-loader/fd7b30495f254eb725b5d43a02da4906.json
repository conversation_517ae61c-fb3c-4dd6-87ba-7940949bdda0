{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/api/monitor/job.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/api/monitor/job.js", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QgZnJvbSAnQC91dGlscy9yZXF1ZXN0JzsKCi8vIOafpeivouWumuaXtuS7u+WKoeiwg+W6puWIl+ihqApleHBvcnQgZnVuY3Rpb24gbGlzdEpvYihxdWVyeSkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9tb25pdG9yL2pvYi9saXN0JywKICAgIG1ldGhvZDogJ2dldCcsCiAgICBwYXJhbXM6IHF1ZXJ5CiAgfSk7Cn0KCi8vIOafpeivouWumuaXtuS7u+WKoeiwg+W6puivpue7hgpleHBvcnQgZnVuY3Rpb24gZ2V0Sm9iKGpvYklkKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL21vbml0b3Ivam9iLycgKyBqb2JJZCwKICAgIG1ldGhvZDogJ2dldCcKICB9KTsKfQoKLy8g5paw5aKe5a6a5pe25Lu75Yqh6LCD5bqmCmV4cG9ydCBmdW5jdGlvbiBhZGRKb2IoZGF0YSkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9tb25pdG9yL2pvYicsCiAgICBtZXRob2Q6ICdwb3N0JywKICAgIGRhdGE6IGRhdGEKICB9KTsKfQoKLy8g5L+u5pS55a6a5pe25Lu75Yqh6LCD5bqmCmV4cG9ydCBmdW5jdGlvbiB1cGRhdGVKb2IoZGF0YSkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9tb25pdG9yL2pvYicsCiAgICBtZXRob2Q6ICdwdXQnLAogICAgZGF0YTogZGF0YQogIH0pOwp9CgovLyDliKDpmaTlrprml7bku7vliqHosIPluqYKZXhwb3J0IGZ1bmN0aW9uIGRlbEpvYihqb2JJZCkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9tb25pdG9yL2pvYi8nICsgam9iSWQsCiAgICBtZXRob2Q6ICdkZWxldGUnCiAgfSk7Cn0KCi8vIOWvvOWHuuWumuaXtuS7u+WKoeiwg+W6pgpleHBvcnQgZnVuY3Rpb24gZXhwb3J0Sm9iKHF1ZXJ5KSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL21vbml0b3Ivam9iL2V4cG9ydCcsCiAgICBtZXRob2Q6ICdnZXQnLAogICAgcGFyYW1zOiBxdWVyeQogIH0pOwp9CgovLyDku7vliqHnirbmgIHkv67mlLkKZXhwb3J0IGZ1bmN0aW9uIGNoYW5nZUpvYlN0YXR1cyhqb2JJZCwgc3RhdHVzKSB7CiAgdmFyIGRhdGEgPSB7CiAgICBqb2JJZDogam9iSWQsCiAgICBzdGF0dXM6IHN0YXR1cwogIH07CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL21vbml0b3Ivam9iL2NoYW5nZVN0YXR1cycsCiAgICBtZXRob2Q6ICdwdXQnLAogICAgZGF0YTogZGF0YQogIH0pOwp9CgovLyDlrprml7bku7vliqHnq4vljbPmiafooYzkuIDmrKEKZXhwb3J0IGZ1bmN0aW9uIHJ1bkpvYihqb2JJZCwgam9iR3JvdXApIHsKICB2YXIgZGF0YSA9IHsKICAgIGpvYklkOiBqb2JJZCwKICAgIGpvYkdyb3VwOiBqb2JHcm91cAogIH07CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL21vbml0b3Ivam9iL3J1bicsCiAgICBtZXRob2Q6ICdwdXQnLAogICAgZGF0YTogZGF0YQogIH0pOwp9"}, {"version": 3, "names": ["request", "listJob", "query", "url", "method", "params", "get<PERSON>ob", "jobId", "addJob", "data", "updateJob", "<PERSON><PERSON><PERSON>", "exportJob", "changeJobStatus", "status", "runJob", "jobGroup"], "sources": ["/Users/<USER>/develop/code/project/haijia/adminweb/src/api/monitor/job.js"], "sourcesContent": ["import request from '@/utils/request'\n\n// 查询定时任务调度列表\nexport function listJob(query) {\n  return request({\n    url: '/monitor/job/list',\n    method: 'get',\n    params: query\n  })\n}\n\n// 查询定时任务调度详细\nexport function getJob(jobId) {\n  return request({\n    url: '/monitor/job/' + jobId,\n    method: 'get'\n  })\n}\n\n// 新增定时任务调度\nexport function addJob(data) {\n  return request({\n    url: '/monitor/job',\n    method: 'post',\n    data: data\n  })\n}\n\n// 修改定时任务调度\nexport function updateJob(data) {\n  return request({\n    url: '/monitor/job',\n    method: 'put',\n    data: data\n  })\n}\n\n// 删除定时任务调度\nexport function delJob(jobId) {\n  return request({\n    url: '/monitor/job/' + jobId,\n    method: 'delete'\n  })\n}\n\n// 导出定时任务调度\nexport function exportJob(query) {\n  return request({\n    url: '/monitor/job/export',\n    method: 'get',\n    params: query\n  })\n}\n\n// 任务状态修改\nexport function changeJobStatus(jobId, status) {\n  const data = {\n    jobId,\n    status\n  }\n  return request({\n    url: '/monitor/job/changeStatus',\n    method: 'put',\n    data: data\n  })\n}\n\n\n// 定时任务立即执行一次\nexport function runJob(jobId, jobGroup) {\n  const data = {\n    jobId,\n    jobGroup\n  }\n  return request({\n    url: '/monitor/job/run',\n    method: 'put',\n    data: data\n  })\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;;AAErC;AACA,OAAO,SAASC,OAAOA,CAACC,KAAK,EAAE;EAC7B,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEH;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASI,MAAMA,CAACC,KAAK,EAAE;EAC5B,OAAOP,OAAO,CAAC;IACbG,GAAG,EAAE,eAAe,GAAGI,KAAK;IAC5BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASI,MAAMA,CAACC,IAAI,EAAE;EAC3B,OAAOT,OAAO,CAAC;IACbG,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASC,SAASA,CAACD,IAAI,EAAE;EAC9B,OAAOT,OAAO,CAAC;IACbG,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASE,MAAMA,CAACJ,KAAK,EAAE;EAC5B,OAAOP,OAAO,CAAC;IACbG,GAAG,EAAE,eAAe,GAAGI,KAAK;IAC5BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASQ,SAASA,CAACV,KAAK,EAAE;EAC/B,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,qBAAqB;IAC1BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEH;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASW,eAAeA,CAACN,KAAK,EAAEO,MAAM,EAAE;EAC7C,IAAML,IAAI,GAAG;IACXF,KAAK,EAALA,KAAK;IACLO,MAAM,EAANA;EACF,CAAC;EACD,OAAOd,OAAO,CAAC;IACbG,GAAG,EAAE,2BAA2B;IAChCC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAGA;AACA,OAAO,SAASM,MAAMA,CAACR,KAAK,EAAES,QAAQ,EAAE;EACtC,IAAMP,IAAI,GAAG;IACXF,KAAK,EAALA,KAAK;IACLS,QAAQ,EAARA;EACF,CAAC;EACD,OAAOhB,OAAO,CAAC;IACbG,GAAG,EAAE,kBAAkB;IACvBC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ", "ignoreList": []}]}