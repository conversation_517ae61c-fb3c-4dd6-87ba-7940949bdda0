{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/utils/loadTinymce.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/utils/loadTinymce.js", "mtime": 1641277326000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IGxvYWRTY3JpcHQgZnJvbSAnLi9sb2FkU2NyaXB0JzsKaW1wb3J0IEVMRU1FTlQgZnJvbSAnZWxlbWVudC11aSc7CmltcG9ydCBwbHVnaW5zQ29uZmlnIGZyb20gJy4vcGx1Z2luc0NvbmZpZyc7CnZhciB0aW55bWNlT2JqOwpleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBsb2FkVGlueW1jZShjYikgewogIHZhciB0aW55bWNlVXJsID0gcGx1Z2luc0NvbmZpZy50aW55bWNlVXJsOwogIGlmICh0aW55bWNlT2JqKSB7CiAgICBjYih0aW55bWNlT2JqKTsKICAgIHJldHVybjsKICB9CiAgdmFyIGxvYWRpbmcgPSBFTEVNRU5ULkxvYWRpbmcuc2VydmljZSh7CiAgICBmdWxsc2NyZWVuOiB0cnVlLAogICAgbG9jazogdHJ1ZSwKICAgIHRleHQ6ICflr4zmlofmnKzotYTmupDliqDovb3kuK0uLi4nLAogICAgc3Bpbm5lcjogJ2VsLWljb24tbG9hZGluZycsCiAgICBiYWNrZ3JvdW5kOiAncmdiYSgyNTUsIDI1NSwgMjU1LCAwLjUpJwogIH0pOwogIGxvYWRTY3JpcHQodGlueW1jZVVybCwgZnVuY3Rpb24gKCkgewogICAgbG9hZGluZy5jbG9zZSgpOwogICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIG5vLXVuZGVmCiAgICB0aW55bWNlT2JqID0gdGlueW1jZTsKICAgIGNiKHRpbnltY2VPYmopOwogIH0pOwp9"}, {"version": 3, "names": ["loadScript", "ELEMENT", "pluginsConfig", "tinymceObj", "loadTinymce", "cb", "tinymceUrl", "loading", "Loading", "service", "fullscreen", "lock", "text", "spinner", "background", "close", "<PERSON><PERSON><PERSON>"], "sources": ["/Users/<USER>/develop/code/project/haijia/adminweb/src/utils/loadTinymce.js"], "sourcesContent": ["import loadScript from './loadScript'\nimport ELEMENT from 'element-ui'\nimport pluginsConfig from './pluginsConfig'\n\nlet tinymceObj\n\nexport default function loadTinymce(cb) {\n  const { tinymceUrl } = pluginsConfig\n\n  if (tinymceObj) {\n    cb(tinymceObj)\n    return\n  }\n\n  const loading = ELEMENT.Loading.service({\n    fullscreen: true,\n    lock: true,\n    text: '富文本资源加载中...',\n    spinner: 'el-icon-loading',\n    background: 'rgba(255, 255, 255, 0.5)'\n  })\n\n  loadScript(tinymceUrl, () => {\n    loading.close()\n    // eslint-disable-next-line no-undef\n    tinymceObj = tinymce\n    cb(tinymceObj)\n  })\n}\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,cAAc;AACrC,OAAOC,OAAO,MAAM,YAAY;AAChC,OAAOC,aAAa,MAAM,iBAAiB;AAE3C,IAAIC,UAAU;AAEd,eAAe,SAASC,WAAWA,CAACC,EAAE,EAAE;EACtC,IAAQC,UAAU,GAAKJ,aAAa,CAA5BI,UAAU;EAElB,IAAIH,UAAU,EAAE;IACdE,EAAE,CAACF,UAAU,CAAC;IACd;EACF;EAEA,IAAMI,OAAO,GAAGN,OAAO,CAACO,OAAO,CAACC,OAAO,CAAC;IACtCC,UAAU,EAAE,IAAI;IAChBC,IAAI,EAAE,IAAI;IACVC,IAAI,EAAE,aAAa;IACnBC,OAAO,EAAE,iBAAiB;IAC1BC,UAAU,EAAE;EACd,CAAC,CAAC;EAEFd,UAAU,CAACM,UAAU,EAAE,YAAM;IAC3BC,OAAO,CAACQ,KAAK,CAAC,CAAC;IACf;IACAZ,UAAU,GAAGa,OAAO;IACpBX,EAAE,CAACF,UAAU,CAAC;EAChB,CAAC,CAAC;AACJ", "ignoreList": []}]}