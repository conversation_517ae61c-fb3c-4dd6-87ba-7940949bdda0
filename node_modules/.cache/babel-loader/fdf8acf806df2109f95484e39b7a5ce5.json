{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js!/Users/<USER>/develop/code/project/haijia/adminweb/src/config/env.js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/config/env.js", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/babel.config.js", "mtime": 1650035952000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly/pmL/ph4xPU1PphY3nva4KdmFyIG9zc0FsaSA9IHsKICByZWdpb246ICdvc3MtY24temhhbmdqaWFrb3UnLAogIGVuZHBvaW50OiAnb3NzLWNuLXpoYW5namlha291LmFsaXl1bmNzLmNvbScsCiAgc3RzVG9rZW46ICcnLAogIGFjY2Vzc0tleUlkOiAneHh4eHh4eHh4JywKICBhY2Nlc3NLZXlTZWNyZXQ6ICd4eHh4eHh4eHh4eHh4eHh4eHh4eHh4JywKICBidWNrZXQ6ICdqb29sdW4tb3BlbicKfTsKZXhwb3J0IHsgb3NzQWxpIH07"}, {"version": 3, "names": ["ossAli", "region", "endpoint", "stsToken", "accessKeyId", "accessKeySecret", "bucket"], "sources": ["/Users/<USER>/develop/code/project/haijia/adminweb/src/config/env.js"], "sourcesContent": ["//阿里OSS配置\nlet ossAli = {\n  region: 'oss-cn-zhangjiakou',\n  endpoint: 'oss-cn-zhangjiakou.aliyuncs.com',\n  stsToken: '',\n  accessKeyId: 'xxxxxxxxx',\n  accessKeySecret: 'xxxxxxxxxxxxxxxxxxxxxx',\n  bucket: 'joolun-open',\n}\n\nexport {\n  ossAli\n}\n"], "mappings": "AAAA;AACA,IAAIA,MAAM,GAAG;EACXC,MAAM,EAAE,oBAAoB;EAC5BC,QAAQ,EAAE,iCAAiC;EAC3CC,QAAQ,EAAE,EAAE;EACZC,WAAW,EAAE,WAAW;EACxBC,eAAe,EAAE,wBAAwB;EACzCC,MAAM,EAAE;AACV,CAAC;AAED,SACEN,MAAM", "ignoreList": []}]}