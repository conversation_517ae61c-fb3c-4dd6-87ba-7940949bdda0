{"code": "(window[\"webpackJsonp\"]=window[\"webpackJsonp\"]||[]).push([[\"chunk-3e6c2b3d\"],{\"0595\":function(e,t,a){\"use strict\";a.d(t,\"d\",(function(){return c})),a.d(t,\"c\",(function(){return r})),a.d(t,\"a\",(function(){return s})),a.d(t,\"b\",(function(){return w}));var o,n,i=a(\"2e2a\");function l(e){return'<el-dialog v-bind=\"$attrs\" v-on=\"$listeners\" @open=\"onOpen\" @close=\"onClose\" title=\"Dialog Titile\">\\n    '.concat(e,'\\n    <div slot=\"footer\">\\n      <el-button @click=\"close\">取消</el-button>\\n      <el-button type=\"primary\" @click=\"handelConfirm\">确定</el-button>\\n    </div>\\n  </el-dialog>')}function c(e){return\"<template>\\n    <div>\\n      \".concat(e,\"\\n    </div>\\n  </template>\")}function r(e){return\"<script>\\n    \".concat(e,\"\\n  <\\/script>\")}function s(e){return\"<style>\\n    \".concat(e,\"\\n  </style>\")}function u(e,t,a){var o=\"\";\"right\"!==e.labelPosition&&(o='label-position=\"'.concat(e.labelPosition,'\"'));var i=e.disabled?':disabled=\"'.concat(e.disabled,'\"'):\"\",l='<el-form ref=\"'.concat(e.formRef,'\" :model=\"').concat(e.formModel,'\" :rules=\"').concat(e.formRules,'\" size=\"').concat(e.size,'\" ').concat(i,' label-width=\"').concat(e.labelWidth,'px\" ').concat(o,\">\\n      \").concat(t,\"\\n      \").concat(d(e,a),\"\\n    </el-form>\");return n&&(l='<el-row :gutter=\"'.concat(e.gutter,'\">\\n        ').concat(l,\"\\n      </el-row>\")),l}function d(e,t){var a=\"\";return e.formBtns&&\"file\"===t&&(a='<el-form-item size=\"large\">\\n          <el-button type=\"primary\" @click=\"submitForm\">提交</el-button>\\n          <el-button @click=\"resetForm\">重置</el-button>\\n        </el-form-item>',n&&(a='<el-col :span=\"24\">\\n          '.concat(a,\"\\n        </el-col>\"))),a}function p(e,t){return n||24!==e.span?'<el-col :span=\"'.concat(e.span,'\">\\n      ').concat(t,\"\\n    </el-col>\"):t}var f={colFormItem:function(e){var t=\"\";e.labelWidth&&e.labelWidth!==o.labelWidth&&(t='label-width=\"'.concat(e.labelWidth,'px\"'));var a=!i[\"e\"][e.tag]&&e.required?\"required\":\"\",n=m[e.tag]?m[e.tag](e):null,l=\"<el-form-item \".concat(t,' label=\"').concat(e.label,'\" prop=\"').concat(e.vModel,'\" ').concat(a,\">\\n        \").concat(n,\"\\n      </el-form-item>\");return l=p(e,l),l},rowFormItem:function(e){var t=\"default\"===e.type?\"\":'type=\"'.concat(e.type,'\"'),a=\"default\"===e.type?\"\":'justify=\"'.concat(e.justify,'\"'),o=\"default\"===e.type?\"\":'align=\"'.concat(e.align,'\"'),n=e.gutter?'gutter=\"'.concat(e.gutter,'\"'):\"\",i=e.children.map((function(e){return f[e.layout](e)})),l=\"<el-row \".concat(t,\" \").concat(a,\" \").concat(o,\" \").concat(n,\">\\n      \").concat(i.join(\"\\n\"),\"\\n    </el-row>\");return l=p(e,l),l}},m={\"el-button\":function(e){var t=v(e),a=(t.tag,t.disabled),o=e.type?'type=\"'.concat(e.type,'\"'):\"\",n=e.icon?'icon=\"'.concat(e.icon,'\"'):\"\",i=e.size?'size=\"'.concat(e.size,'\"'):\"\",l=b(e);return l&&(l=\"\\n\".concat(l,\"\\n\")),\"<\".concat(e.tag,\" \").concat(o,\" \").concat(n,\" \").concat(i,\" \").concat(a,\">\").concat(l,\"</\").concat(e.tag,\">\")},\"el-input\":function(e){var t=v(e),a=t.disabled,o=t.vModel,n=t.clearable,i=t.placeholder,l=t.width,c=e.maxlength?':maxlength=\"'.concat(e.maxlength,'\"'):\"\",r=e[\"show-word-limit\"]?\"show-word-limit\":\"\",s=e.readonly?\"readonly\":\"\",u=e[\"prefix-icon\"]?\"prefix-icon='\".concat(e[\"prefix-icon\"],\"'\"):\"\",d=e[\"suffix-icon\"]?\"suffix-icon='\".concat(e[\"suffix-icon\"],\"'\"):\"\",p=e[\"show-password\"]?\"show-password\":\"\",f=e.type?'type=\"'.concat(e.type,'\"'):\"\",m=e.autosize&&e.autosize.minRows?':autosize=\"{minRows: '.concat(e.autosize.minRows,\", maxRows: \").concat(e.autosize.maxRows,'}\"'):\"\",b=h(e);return b&&(b=\"\\n\".concat(b,\"\\n\")),\"<\".concat(e.tag,\" \").concat(o,\" \").concat(f,\" \").concat(i,\" \").concat(c,\" \").concat(r,\" \").concat(s,\" \").concat(a,\" \").concat(n,\" \").concat(u,\" \").concat(d,\" \").concat(p,\" \").concat(m,\" \").concat(l,\">\").concat(b,\"</\").concat(e.tag,\">\")},\"el-input-number\":function(e){var t=v(e),a=t.disabled,o=t.vModel,n=t.placeholder,i=e[\"controls-position\"]?\"controls-position=\".concat(e[\"controls-position\"]):\"\",l=e.min?\":min='\".concat(e.min,\"'\"):\"\",c=e.max?\":max='\".concat(e.max,\"'\"):\"\",r=e.step?\":step='\".concat(e.step,\"'\"):\"\",s=e[\"step-strictly\"]?\"step-strictly\":\"\",u=e.precision?\":precision='\".concat(e.precision,\"'\"):\"\";return\"<\".concat(e.tag,\" \").concat(o,\" \").concat(n,\" \").concat(r,\" \").concat(s,\" \").concat(u,\" \").concat(i,\" \").concat(l,\" \").concat(c,\" \").concat(a,\"></\").concat(e.tag,\">\")},\"el-select\":function(e){var t=v(e),a=t.disabled,o=t.vModel,n=t.clearable,i=t.placeholder,l=t.width,c=e.filterable?\"filterable\":\"\",r=e.multiple?\"multiple\":\"\",s=_(e);return s&&(s=\"\\n\".concat(s,\"\\n\")),\"<\".concat(e.tag,\" \").concat(o,\" \").concat(i,\" \").concat(a,\" \").concat(r,\" \").concat(c,\" \").concat(n,\" \").concat(l,\">\").concat(s,\"</\").concat(e.tag,\">\")},\"el-radio-group\":function(e){var t=v(e),a=t.disabled,o=t.vModel,n='size=\"'.concat(e.size,'\"'),i=g(e);return i&&(i=\"\\n\".concat(i,\"\\n\")),\"<\".concat(e.tag,\" \").concat(o,\" \").concat(n,\" \").concat(a,\">\").concat(i,\"</\").concat(e.tag,\">\")},\"el-checkbox-group\":function(e){var t=v(e),a=t.disabled,o=t.vModel,n='size=\"'.concat(e.size,'\"'),i=e.min?':min=\"'.concat(e.min,'\"'):\"\",l=e.max?':max=\"'.concat(e.max,'\"'):\"\",c=y(e);return c&&(c=\"\\n\".concat(c,\"\\n\")),\"<\".concat(e.tag,\" \").concat(o,\" \").concat(i,\" \").concat(l,\" \").concat(n,\" \").concat(a,\">\").concat(c,\"</\").concat(e.tag,\">\")},\"el-switch\":function(e){var t=v(e),a=t.disabled,o=t.vModel,n=e[\"active-text\"]?'active-text=\"'.concat(e[\"active-text\"],'\"'):\"\",i=e[\"inactive-text\"]?'inactive-text=\"'.concat(e[\"inactive-text\"],'\"'):\"\",l=e[\"active-color\"]?'active-color=\"'.concat(e[\"active-color\"],'\"'):\"\",c=e[\"inactive-color\"]?'inactive-color=\"'.concat(e[\"inactive-color\"],'\"'):\"\",r=!0!==e[\"active-value\"]?\":active-value='\".concat(JSON.stringify(e[\"active-value\"]),\"'\"):\"\",s=!1!==e[\"inactive-value\"]?\":inactive-value='\".concat(JSON.stringify(e[\"inactive-value\"]),\"'\"):\"\";return\"<\".concat(e.tag,\" \").concat(o,\" \").concat(n,\" \").concat(i,\" \").concat(l,\" \").concat(c,\" \").concat(r,\" \").concat(s,\" \").concat(a,\"></\").concat(e.tag,\">\")},\"el-cascader\":function(e){var t=v(e),a=t.disabled,o=t.vModel,n=t.clearable,i=t.placeholder,l=t.width,c=e.options?':options=\"'.concat(e.vModel,'Options\"'):\"\",r=e.props?':props=\"'.concat(e.vModel,'Props\"'):\"\",s=e[\"show-all-levels\"]?\"\":':show-all-levels=\"false\"',u=e.filterable?\"filterable\":\"\",d=\"/\"===e.separator?\"\":'separator=\"'.concat(e.separator,'\"');return\"<\".concat(e.tag,\" \").concat(o,\" \").concat(c,\" \").concat(r,\" \").concat(l,\" \").concat(s,\" \").concat(i,\" \").concat(d,\" \").concat(u,\" \").concat(n,\" \").concat(a,\"></\").concat(e.tag,\">\")},\"el-slider\":function(e){var t=v(e),a=t.disabled,o=t.vModel,n=e.min?\":min='\".concat(e.min,\"'\"):\"\",i=e.max?\":max='\".concat(e.max,\"'\"):\"\",l=e.step?\":step='\".concat(e.step,\"'\"):\"\",c=e.range?\"range\":\"\",r=e[\"show-stops\"]?':show-stops=\"'.concat(e[\"show-stops\"],'\"'):\"\";return\"<\".concat(e.tag,\" \").concat(n,\" \").concat(i,\" \").concat(l,\" \").concat(o,\" \").concat(c,\" \").concat(r,\" \").concat(a,\"></\").concat(e.tag,\">\")},\"el-time-picker\":function(e){var t=v(e),a=t.disabled,o=t.vModel,n=t.clearable,i=t.placeholder,l=t.width,c=e[\"start-placeholder\"]?'start-placeholder=\"'.concat(e[\"start-placeholder\"],'\"'):\"\",r=e[\"end-placeholder\"]?'end-placeholder=\"'.concat(e[\"end-placeholder\"],'\"'):\"\",s=e[\"range-separator\"]?'range-separator=\"'.concat(e[\"range-separator\"],'\"'):\"\",u=e[\"is-range\"]?\"is-range\":\"\",d=e.format?'format=\"'.concat(e.format,'\"'):\"\",p=e[\"value-format\"]?'value-format=\"'.concat(e[\"value-format\"],'\"'):\"\",f=e[\"picker-options\"]?\":picker-options='\".concat(JSON.stringify(e[\"picker-options\"]),\"'\"):\"\";return\"<\".concat(e.tag,\" \").concat(o,\" \").concat(u,\" \").concat(d,\" \").concat(p,\" \").concat(f,\" \").concat(l,\" \").concat(i,\" \").concat(c,\" \").concat(r,\" \").concat(s,\" \").concat(n,\" \").concat(a,\"></\").concat(e.tag,\">\")},\"el-date-picker\":function(e){var t=v(e),a=t.disabled,o=t.vModel,n=t.clearable,i=t.placeholder,l=t.width,c=e[\"start-placeholder\"]?'start-placeholder=\"'.concat(e[\"start-placeholder\"],'\"'):\"\",r=e[\"end-placeholder\"]?'end-placeholder=\"'.concat(e[\"end-placeholder\"],'\"'):\"\",s=e[\"range-separator\"]?'range-separator=\"'.concat(e[\"range-separator\"],'\"'):\"\",u=e.format?'format=\"'.concat(e.format,'\"'):\"\",d=e[\"value-format\"]?'value-format=\"'.concat(e[\"value-format\"],'\"'):\"\",p=\"date\"===e.type?\"\":'type=\"'.concat(e.type,'\"'),f=e.readonly?\"readonly\":\"\";return\"<\".concat(e.tag,\" \").concat(p,\" \").concat(o,\" \").concat(u,\" \").concat(d,\" \").concat(l,\" \").concat(i,\" \").concat(c,\" \").concat(r,\" \").concat(s,\" \").concat(n,\" \").concat(f,\" \").concat(a,\"></\").concat(e.tag,\">\")},\"el-rate\":function(e){var t=v(e),a=t.disabled,o=t.vModel,n=(e.max&&\":max='\".concat(e.max,\"'\"),e[\"allow-half\"]?\"allow-half\":\"\"),i=e[\"show-text\"]?\"show-text\":\"\",l=e[\"show-score\"]?\"show-score\":\"\";return\"<\".concat(e.tag,\" \").concat(o,\" \").concat(n,\" \").concat(i,\" \").concat(l,\" \").concat(a,\"></\").concat(e.tag,\">\")},\"el-color-picker\":function(e){var t=v(e),a=t.disabled,o=t.vModel,n='size=\"'.concat(e.size,'\"'),i=e[\"show-alpha\"]?\"show-alpha\":\"\",l=e[\"color-format\"]?'color-format=\"'.concat(e[\"color-format\"],'\"'):\"\";return\"<\".concat(e.tag,\" \").concat(o,\" \").concat(n,\" \").concat(i,\" \").concat(l,\" \").concat(a,\"></\").concat(e.tag,\">\")},\"el-upload\":function(e){var t=e.disabled?\":disabled='true'\":\"\",a=e.action?':action=\"'.concat(e.vModel,'Action\"'):\"\",o=e.multiple?\"multiple\":\"\",n=\"text\"!==e[\"list-type\"]?'list-type=\"'.concat(e[\"list-type\"],'\"'):\"\",i=e.accept?'accept=\"'.concat(e.accept,'\"'):\"\",l=\"file\"!==e.name?'name=\"'.concat(e.name,'\"'):\"\",c=!1===e[\"auto-upload\"]?':auto-upload=\"false\"':\"\",r=':before-upload=\"'.concat(e.vModel,'BeforeUpload\"'),s=':file-list=\"'.concat(e.vModel,'fileList\"'),u='ref=\"'.concat(e.vModel,'\"'),d=D(e);return d&&(d=\"\\n\".concat(d,\"\\n\")),\"<\".concat(e.tag,\" \").concat(u,\" \").concat(s,\" \").concat(a,\" \").concat(c,\" \").concat(o,\" \").concat(r,\" \").concat(n,\" \").concat(i,\" \").concat(l,\" \").concat(t,\">\").concat(d,\"</\").concat(e.tag,\">\")}};function v(e){return{vModel:'v-model=\"'.concat(o.formModel,\".\").concat(e.vModel,'\"'),clearable:e.clearable?\"clearable\":\"\",placeholder:e.placeholder?'placeholder=\"'.concat(e.placeholder,'\"'):\"\",width:e.style&&e.style.width?\":style=\\\"{width: '100%'}\\\"\":\"\",disabled:e.disabled?\":disabled='true'\":\"\"}}function b(e){var t=[];return e.default&&t.push(e.default),t.join(\"\\n\")}function h(e){var t=[];return e.prepend&&t.push('<template slot=\"prepend\">'.concat(e.prepend,\"</template>\")),e.append&&t.push('<template slot=\"append\">'.concat(e.append,\"</template>\")),t.join(\"\\n\")}function _(e){var t=[];return e.options&&e.options.length&&t.push('<el-option v-for=\"(item, index) in '.concat(e.vModel,'Options\" :key=\"index\" :label=\"item.label\" :value=\"item.value\" :disabled=\"item.disabled\"></el-option>')),t.join(\"\\n\")}function g(e){var t=[];if(e.options&&e.options.length){var a=\"button\"===e.optionType?\"el-radio-button\":\"el-radio\",o=e.border?\"border\":\"\";t.push(\"<\".concat(a,' v-for=\"(item, index) in ').concat(e.vModel,'Options\" :key=\"index\" :label=\"item.value\" :disabled=\"item.disabled\" ').concat(o,\">{{item.label}}</\").concat(a,\">\"))}return t.join(\"\\n\")}function y(e){var t=[];if(e.options&&e.options.length){var a=\"button\"===e.optionType?\"el-checkbox-button\":\"el-checkbox\",o=e.border?\"border\":\"\";t.push(\"<\".concat(a,' v-for=\"(item, index) in ').concat(e.vModel,'Options\" :key=\"index\" :label=\"item.value\" :disabled=\"item.disabled\" ').concat(o,\">{{item.label}}</\").concat(a,\">\"))}return t.join(\"\\n\")}function D(e){var t=[];return\"picture-card\"===e[\"list-type\"]?t.push('<i class=\"el-icon-plus\"></i>'):t.push('<el-button size=\"small\" type=\"primary\" icon=\"el-icon-upload\">'.concat(e.buttonText,\"</el-button>\")),e.showTip&&t.push('<div slot=\"tip\" class=\"el-upload__tip\">只能上传不超过 '.concat(e.fileSize).concat(e.sizeUnit,\" 的\").concat(e.accept,\"文件</div>\")),t.join(\"\\n\")}function w(e,t){var a=[];o=e,n=e.fields.some((function(e){return 24!==e.span})),e.fields.forEach((function(e){a.push(f[e.layout](e))}));var i=a.join(\"\\n\"),c=u(e,i,t);return\"dialog\"===t&&(c=l(c)),o=null,c}},\"0f5c\":function(e,t,a){},2855:function(e,t,a){\"use strict\";a.r(t);var o,n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a(\"div\",{staticClass:\"container\"},[a(\"div\",{staticClass:\"left-board\"},[a(\"div\",{staticClass:\"logo-wrapper\"},[a(\"div\",{staticClass:\"logo\"},[a(\"img\",{attrs:{src:e.logo,alt:\"logo\"}}),e._v(\" Form Generator \")])]),a(\"el-scrollbar\",{staticClass:\"left-scrollbar\"},[a(\"div\",{staticClass:\"components-list\"},e._l(e.leftComponents,(function(t,o){return a(\"div\",{key:o},[a(\"div\",{staticClass:\"components-title\"},[a(\"svg-icon\",{attrs:{\"icon-class\":\"component\"}}),e._v(\" \"+e._s(t.title)+\" \")],1),a(\"draggable\",{staticClass:\"components-draggable\",attrs:{list:t.list,group:{name:\"componentsGroup\",pull:\"clone\",put:!1},clone:e.cloneComponent,draggable:\".components-item\",sort:!1},on:{end:e.onEnd}},e._l(t.list,(function(t,o){return a(\"div\",{key:o,staticClass:\"components-item\",on:{click:function(a){return e.addComponent(t)}}},[a(\"div\",{staticClass:\"components-body\"},[a(\"svg-icon\",{attrs:{\"icon-class\":t.__config__.tagIcon}}),e._v(\" \"+e._s(t.__config__.label)+\" \")],1)])})),0)],1)})),0)])],1),a(\"div\",{staticClass:\"center-board\"},[a(\"div\",{staticClass:\"action-bar\"},[a(\"el-button\",{attrs:{icon:\"el-icon-plus\",type:\"text\"},on:{click:e.handleForm}},[e._v(\" 保存 \")]),a(\"el-button\",{attrs:{icon:\"el-icon-video-play\",type:\"text\"},on:{click:e.run}},[e._v(\" 运行 \")]),a(\"el-button\",{attrs:{icon:\"el-icon-view\",type:\"text\"},on:{click:e.showJson}},[e._v(\" 查看json \")]),a(\"el-button\",{attrs:{icon:\"el-icon-download\",type:\"text\"},on:{click:e.download}},[e._v(\" 导出vue文件 \")]),a(\"el-button\",{staticClass:\"copy-btn-main\",attrs:{icon:\"el-icon-document-copy\",type:\"text\"},on:{click:e.copy}},[e._v(\" 复制代码 \")]),a(\"el-button\",{staticClass:\"delete-btn\",attrs:{icon:\"el-icon-delete\",type:\"text\"},on:{click:e.empty}},[e._v(\" 清空 \")])],1),a(\"el-scrollbar\",{staticClass:\"center-scrollbar\"},[a(\"el-row\",{staticClass:\"center-board-row\",attrs:{gutter:e.formConf.gutter}},[a(\"el-form\",{attrs:{size:e.formConf.size,\"label-position\":e.formConf.labelPosition,disabled:e.formConf.disabled,\"label-width\":e.formConf.labelWidth+\"px\"}},[a(\"draggable\",{staticClass:\"drawing-board\",attrs:{list:e.drawingList,animation:340,group:\"componentsGroup\"}},e._l(e.drawingList,(function(t,o){return a(\"draggable-item\",{key:t.renderKey,attrs:{\"drawing-list\":e.drawingList,\"current-item\":t,index:o,\"active-id\":e.activeId,\"form-conf\":e.formConf},on:{activeItem:e.activeFormItem,copyItem:e.drawingItemCopy,deleteItem:e.drawingItemDelete}})})),1),a(\"div\",{directives:[{name:\"show\",rawName:\"v-show\",value:!e.drawingList.length,expression:\"!drawingList.length\"}],staticClass:\"empty-info\"},[e._v(\" 从左侧拖入或点选组件进行表单设计 \")])],1)],1)],1)],1),a(\"right-panel\",{attrs:{\"active-data\":e.activeData,\"form-conf\":e.formConf,\"show-field\":!!e.drawingList.length},on:{\"tag-change\":e.tagChange,\"fetch-data\":e.fetchData}}),a(\"form-drawer\",{attrs:{visible:e.drawerVisible,\"form-data\":e.formData,size:\"100%\",\"generate-conf\":e.generateConf},on:{\"update:visible\":function(t){e.drawerVisible=t}}}),a(\"json-drawer\",{attrs:{size:\"60%\",visible:e.jsonDrawerVisible,\"json-str\":JSON.stringify(e.formData)},on:{\"update:visible\":function(t){e.jsonDrawerVisible=t},refresh:e.refreshJson}}),a(\"code-type-dialog\",{attrs:{visible:e.dialogVisible,title:\"选择生成类型\",\"show-file-name\":e.showFileName},on:{\"update:visible\":function(t){e.dialogVisible=t},confirm:e.generate}}),a(\"input\",{attrs:{id:\"copyNode\",type:\"hidden\"}}),a(\"el-dialog\",{attrs:{title:e.formTitle,visible:e.formOpen,width:\"500px\",\"append-to-body\":\"\"},on:{\"update:visible\":function(t){e.formOpen=t}}},[a(\"el-form\",{ref:\"form\",attrs:{model:e.form,rules:e.rules,\"label-width\":\"80px\"}},[a(\"el-form-item\",{attrs:{label:\"表单名称\",prop:\"formName\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入表单名称\"},model:{value:e.form.formName,callback:function(t){e.$set(e.form,\"formName\",t)},expression:\"form.formName\"}})],1),a(\"el-form-item\",{attrs:{label:\"备注\",prop:\"remark\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入备注\"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,\"remark\",t)},expression:\"form.remark\"}})],1)],1),a(\"div\",{staticClass:\"dialog-footer\",attrs:{slot:\"footer\"},slot:\"footer\"},[a(\"el-button\",{attrs:{type:\"primary\"},on:{click:e.submitForm}},[e._v(\"确 定\")]),a(\"el-button\",{on:{click:e.cancel}},[e._v(\"取 消\")])],1)],1)],1)},i=[],l=a(\"b76a\"),c=a.n(l),r=a(\"9619\"),s=a(\"21a6\"),u=a(\"b311\"),d=a.n(u),p=a(\"4758\"),f=a(\"f7ac\"),m=a(\"ad7f\"),v=a(\"766b\"),b=a(\"2e2a\"),h=a(\"ed08\"),_=a(\"0595\"),g=a(\"80de\"),y=a(\"cc7a\"),D=[{layout:\"colFormItem\",tagIcon:\"input\",label:\"手机号\",vModel:\"mobile\",formId:6,tag:\"el-input\",placeholder:\"请输入手机号\",defaultValue:\"\",span:24,style:{width:\"100%\"},clearable:!0,prepend:\"\",append:\"\",\"prefix-icon\":\"el-icon-mobile\",\"suffix-icon\":\"\",maxlength:11,\"show-word-limit\":!0,readonly:!1,disabled:!1,required:!0,changeTag:!0,regList:[{pattern:\"/^1(3|4|5|7|8|9)\\\\d{9}$/\",message:\"手机号格式错误\"}]}],w=a(\"81a5\"),x=a.n(w),k=a(\"a92a\"),C=a(\"4923\"),O=a(\"e31c\"),j=a(\"b3ae\"),$=a(\"3ab9\");function I(e){return I=\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&\"function\"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?\"symbol\":typeof e},I(e)}function T(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,o)}return a}function M(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?T(Object(a),!0).forEach((function(t){S(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):T(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}function S(e,t,a){return(t=L(t))in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}function L(e){var t=V(e,\"string\");return\"symbol\"==I(t)?t:t+\"\"}function V(e,t){if(\"object\"!=I(e)||!e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var o=a.call(e,t||\"default\");if(\"object\"!=I(o))return o;throw new TypeError(\"@@toPrimitive must return a primitive value.\")}return(\"string\"===t?String:Number)(e)}var E,N,z=Object(O[\"a\"])(),R=Object(O[\"b\"])(),P=Object(O[\"c\"])(),F={components:{draggable:c.a,render:p[\"a\"],FormDrawer:f[\"default\"],JsonDrawer:m[\"default\"],RightPanel:v[\"default\"],CodeTypeDialog:k[\"default\"],DraggableItem:C[\"default\"]},data:function(){return{logo:x.a,idGlobal:P,formConf:b[\"a\"],inputComponents:b[\"b\"],selectComponents:b[\"d\"],layoutComponents:b[\"c\"],labelWidth:100,drawingList:D,drawingData:{},activeId:D[0].formId,drawerVisible:!1,formData:{},dialogVisible:!1,jsonDrawerVisible:!1,generateConf:null,showFileName:!1,activeData:D[0],saveDrawingListDebounce:Object(r[\"debounce\"])(340,O[\"e\"]),saveIdGlobalDebounce:Object(r[\"debounce\"])(340,O[\"g\"]),leftComponents:[{title:\"输入型组件\",list:b[\"b\"]},{title:\"选择型组件\",list:b[\"d\"]},{title:\"布局型组件\",list:b[\"c\"]}],formOpen:!1,formTitle:\"\",form:{formId:null,formName:null,formContent:null,remark:null},rules:{}}},computed:{},watch:{\"activeData.__config__.label\":function(e,t){void 0!==this.activeData.placeholder&&this.activeData.__config__.tag&&E===this.activeId&&(this.activeData.placeholder=this.activeData.placeholder.replace(t,\"\")+e)},activeId:{handler:function(e){E=e},immediate:!0},drawingList:{handler:function(e){this.saveDrawingListDebounce(e),0===e.length&&(this.idGlobal=100)},deep:!0},idGlobal:{handler:function(e){this.saveIdGlobalDebounce(e)},immediate:!0}},mounted:function(){var e=this,t=this;Array.isArray(z)&&z.length>0?t.drawingList=z:t.drawingList=D,this.activeFormItem(t.drawingList[0]),t.drawingList=[];var a=t.$route.query&&t.$route.query.formId;a?Object($[\"e\"])(a).then((function(e){t.formConf=JSON.parse(e.data.formContent),t.drawingList=t.formConf.fields,t.form=e.data})):R&&(t.formConf=R),Object(j[\"a\"])((function(e){o=e}));var n=new d.a(\"#copyNode\",{text:function(t){var a=e.generateCode();return e.$notify({title:\"成功\",message:\"代码已复制到剪切板，可粘贴。\",type:\"success\"}),a}});n.on(\"error\",(function(t){e.$message.error(\"代码复制失败\")}))},methods:{setObjectValueReduce:function(e,t,a){var o=t.split(\".\");o.reduce((function(e,t,n){return o.length===n+1?e[t]=a:Object(h[\"isObjectObject\"])(e[t])||(e[t]={}),e[t]}),e)},setRespData:function(e,t){var a=e.__config__,o=a.dataPath,n=a.renderKey,i=a.dataConsumer;if(o&&i){var l=o.split(\".\").reduce((function(e,t){return e[t]}),t);this.setObjectValueReduce(e,i,l);var c=this.drawingList.findIndex((function(e){return e.__config__.renderKey===n}));c>-1&&this.$set(this.drawingList,c,e)}},fetchData:function(e){var t=this,a=e.__config__,o=a.dataType,n=a.method,i=a.url;\"dynamic\"===o&&n&&i&&(this.setLoading(e,!0),this.$axios({method:n,url:i}).then((function(a){t.setLoading(e,!1),t.setRespData(e,a.data)})))},setLoading:function(e,t){var a=e.directives;if(Array.isArray(a)){var o=a.find((function(e){return\"loading\"===e.name}));o&&(o.value=t)}},activeFormItem:function(e){this.activeData=e,this.activeId=e.__config__.formId},onEnd:function(e){e.from!==e.to&&(this.fetchData(N),this.activeData=N,this.activeId=this.idGlobal)},addComponent:function(e){var t=this.cloneComponent(e);this.fetchData(t),this.drawingList.push(t),this.activeFormItem(t)},cloneComponent:function(e){var t=Object(h[\"d\"])(e),a=t.__config__;return a.span=this.formConf.span,this.createIdAndKey(t),void 0!==t.placeholder&&(t.placeholder+=a.label),N=t,N},createIdAndKey:function(e){var t=this,a=e.__config__;return a.formId=++this.idGlobal,a.renderKey=\"\".concat(a.formId).concat(+new Date),\"colFormItem\"===a.layout?e.__vModel__=\"field\".concat(this.idGlobal):\"rowFormItem\"===a.layout&&(a.componentName=\"row\".concat(this.idGlobal),!Array.isArray(a.children)&&(a.children=[]),delete a.label),Array.isArray(a.children)&&(a.children=a.children.map((function(e){return t.createIdAndKey(e)}))),e},AssembleFormData:function(){this.formData=M({fields:Object(h[\"d\"])(this.drawingList)},this.formConf)},generate:function(e){var t=this[\"exec\".concat(Object(h[\"i\"])(this.operationType))];this.generateConf=e,t&&t(e)},execRun:function(e){this.AssembleFormData(),this.drawerVisible=!0},execDownload:function(e){var t=this.generateCode(),a=new Blob([t],{type:\"text/plain;charset=utf-8\"});Object(s[\"saveAs\"])(a,e.fileName)},execCopy:function(e){document.getElementById(\"copyNode\").click()},empty:function(){var e=this;this.$confirm(\"确定要清空所有组件吗？\",\"提示\",{type:\"warning\"}).then((function(){e.drawingList=[],e.idGlobal=100}))},drawingItemCopy:function(e,t){var a=Object(h[\"d\"])(e);a=this.createIdAndKey(a),t.push(a),this.activeFormItem(a)},drawingItemDelete:function(e,t){var a=this;t.splice(e,1),this.$nextTick((function(){var e=a.drawingList.length;e&&a.activeFormItem(a.drawingList[e-1])}))},generateCode:function(){var e=this.generateConf.type;this.AssembleFormData();var t=Object(_[\"c\"])(Object(g[\"a\"])(this.formData,e)),a=Object(_[\"d\"])(Object(_[\"b\"])(this.formData,e)),n=Object(_[\"a\"])(Object(y[\"a\"])(this.formData));return o.html(a+t+n,h[\"b\"].html)},showJson:function(){this.AssembleFormData(),this.jsonDrawerVisible=!0},download:function(){this.dialogVisible=!0,this.showFileName=!0,this.operationType=\"download\"},run:function(){this.dialogVisible=!0,this.showFileName=!1,this.operationType=\"run\"},copy:function(){this.dialogVisible=!0,this.showFileName=!1,this.operationType=\"copy\"},tagChange:function(e){var t=this;e=this.cloneComponent(e);var a=e.__config__;e.__vModel__=this.activeData.__vModel__,a.formId=this.activeId,a.span=this.activeData.__config__.span,this.activeData.__config__.tag=a.tag,this.activeData.__config__.tagIcon=a.tagIcon,this.activeData.__config__.document=a.document,I(this.activeData.__config__.defaultValue)===I(a.defaultValue)&&(a.defaultValue=this.activeData.__config__.defaultValue),Object.keys(e).forEach((function(a){void 0!==t.activeData[a]&&(e[a]=t.activeData[a])})),this.activeData=e,this.updateDrawingList(e,this.drawingList)},updateDrawingList:function(e,t){var a=this,o=t.findIndex((function(e){return e.__config__.formId===a.activeId}));o>-1?t.splice(o,1,e):t.forEach((function(t){Array.isArray(t.__config__.children)&&a.updateDrawingList(e,t.__config__.children)}))},refreshJson:function(e){this.drawingList=Object(h[\"d\"])(e.fields),delete e.fields,this.formConf=e},handleForm:function(){this.formData=M({fields:Object(h[\"d\"])(this.drawingList)},this.formConf),this.form.formContent=JSON.stringify(this.formData),this.formOpen=!0,this.formTitle=\"添加表单\"},reset:function(){this.form={formId:null,formName:null,formContent:null,remark:null},this.resetForm(\"form\")},cancel:function(){this.formOpen=!1,this.reset()},submitForm:function(){var e=this;this.$refs[\"form\"].validate((function(t){t&&(null!=e.form.formId?Object($[\"g\"])(e.form).then((function(t){e.msgSuccess(\"修改成功\")})):Object($[\"b\"])(e.form).then((function(t){e.msgSuccess(\"新增成功\")})),e.drawingList=[],e.idGlobal=100,e.open=!1,e.$store.dispatch(\"tagsView/delView\",e.$route),e.$router.go(-1))}))}}},A=F,q=(a(\"beb6\"),a(\"2877\")),W=Object(q[\"a\"])(A,n,i,!1,null,null,null);t[\"default\"]=W.exports},\"2e2a\":function(e,t,a){\"use strict\";a.d(t,\"a\",(function(){return o})),a.d(t,\"b\",(function(){return n})),a.d(t,\"d\",(function(){return i})),a.d(t,\"c\",(function(){return l})),a.d(t,\"e\",(function(){return c}));var o={formRef:\"elForm\",formModel:\"formData\",size:\"medium\",labelPosition:\"right\",labelWidth:100,formRules:\"rules\",gutter:15,disabled:!1,span:24,formBtns:!0},n=[{label:\"单行文本\",tag:\"el-input\",tagIcon:\"input\",placeholder:\"请输入\",defaultValue:void 0,span:24,labelWidth:null,style:{width:\"100%\"},clearable:!0,prepend:\"\",append:\"\",\"prefix-icon\":\"\",\"suffix-icon\":\"\",maxlength:null,\"show-word-limit\":!1,readonly:!1,disabled:!1,required:!0,regList:[],changeTag:!0,document:\"https://element.eleme.cn/#/zh-CN/component/input\"},{label:\"多行文本\",tag:\"el-input\",tagIcon:\"textarea\",type:\"textarea\",placeholder:\"请输入\",defaultValue:void 0,span:24,labelWidth:null,autosize:{minRows:4,maxRows:4},style:{width:\"100%\"},maxlength:null,\"show-word-limit\":!1,readonly:!1,disabled:!1,required:!0,regList:[],changeTag:!0,document:\"https://element.eleme.cn/#/zh-CN/component/input\"},{label:\"密码\",tag:\"el-input\",tagIcon:\"password\",placeholder:\"请输入\",defaultValue:void 0,span:24,\"show-password\":!0,labelWidth:null,style:{width:\"100%\"},clearable:!0,prepend:\"\",append:\"\",\"prefix-icon\":\"\",\"suffix-icon\":\"\",maxlength:null,\"show-word-limit\":!1,readonly:!1,disabled:!1,required:!0,regList:[],changeTag:!0,document:\"https://element.eleme.cn/#/zh-CN/component/input\"},{label:\"计数器\",tag:\"el-input-number\",tagIcon:\"number\",placeholder:\"\",defaultValue:void 0,span:24,labelWidth:null,min:void 0,max:void 0,step:void 0,\"step-strictly\":!1,precision:void 0,\"controls-position\":\"\",disabled:!1,required:!0,regList:[],changeTag:!0,document:\"https://element.eleme.cn/#/zh-CN/component/input-number\"}],i=[{label:\"下拉选择\",tag:\"el-select\",tagIcon:\"select\",placeholder:\"请选择\",defaultValue:void 0,span:24,labelWidth:null,style:{width:\"100%\"},clearable:!0,disabled:!1,required:!0,filterable:!1,multiple:!1,options:[{label:\"选项一\",value:1},{label:\"选项二\",value:2}],regList:[],changeTag:!0,document:\"https://element.eleme.cn/#/zh-CN/component/select\"},{label:\"级联选择\",tag:\"el-cascader\",tagIcon:\"cascader\",placeholder:\"请选择\",defaultValue:[],span:24,labelWidth:null,style:{width:\"100%\"},props:{props:{multiple:!1}},\"show-all-levels\":!0,disabled:!1,clearable:!0,filterable:!1,required:!0,options:[{id:1,value:1,label:\"选项1\",children:[{id:2,value:2,label:\"选项1-1\"}]}],dataType:\"dynamic\",labelKey:\"label\",valueKey:\"value\",childrenKey:\"children\",separator:\"/\",regList:[],changeTag:!0,document:\"https://element.eleme.cn/#/zh-CN/component/cascader\"},{label:\"单选框组\",tag:\"el-radio-group\",tagIcon:\"radio\",defaultValue:void 0,span:24,labelWidth:null,style:{},optionType:\"default\",border:!1,size:\"medium\",disabled:!1,required:!0,options:[{label:\"选项一\",value:1},{label:\"选项二\",value:2}],regList:[],changeTag:!0,document:\"https://element.eleme.cn/#/zh-CN/component/radio\"},{label:\"多选框组\",tag:\"el-checkbox-group\",tagIcon:\"checkbox\",defaultValue:[],span:24,labelWidth:null,style:{},optionType:\"default\",border:!1,size:\"medium\",disabled:!1,required:!0,options:[{label:\"选项一\",value:1},{label:\"选项二\",value:2}],regList:[],changeTag:!0,document:\"https://element.eleme.cn/#/zh-CN/component/checkbox\"},{label:\"开关\",tag:\"el-switch\",tagIcon:\"switch\",defaultValue:!1,span:24,labelWidth:null,style:{},disabled:!1,required:!0,\"active-text\":\"\",\"inactive-text\":\"\",\"active-color\":null,\"inactive-color\":null,\"active-value\":!0,\"inactive-value\":!1,regList:[],changeTag:!0,document:\"https://element.eleme.cn/#/zh-CN/component/switch\"},{label:\"滑块\",tag:\"el-slider\",tagIcon:\"slider\",defaultValue:null,span:24,labelWidth:null,disabled:!1,required:!0,min:0,max:100,step:1,\"show-stops\":!1,range:!1,regList:[],changeTag:!0,document:\"https://element.eleme.cn/#/zh-CN/component/slider\"},{label:\"时间选择\",tag:\"el-time-picker\",tagIcon:\"time\",placeholder:\"请选择\",defaultValue:null,span:24,labelWidth:null,style:{width:\"100%\"},disabled:!1,clearable:!0,required:!0,\"picker-options\":{selectableRange:\"00:00:00-23:59:59\"},format:\"HH:mm:ss\",\"value-format\":\"HH:mm:ss\",regList:[],changeTag:!0,document:\"https://element.eleme.cn/#/zh-CN/component/time-picker\"},{label:\"时间范围\",tag:\"el-time-picker\",tagIcon:\"time-range\",defaultValue:null,span:24,labelWidth:null,style:{width:\"100%\"},disabled:!1,clearable:!0,required:!0,\"is-range\":!0,\"range-separator\":\"至\",\"start-placeholder\":\"开始时间\",\"end-placeholder\":\"结束时间\",format:\"HH:mm:ss\",\"value-format\":\"HH:mm:ss\",regList:[],changeTag:!0,document:\"https://element.eleme.cn/#/zh-CN/component/time-picker\"},{label:\"日期选择\",tag:\"el-date-picker\",tagIcon:\"date\",placeholder:\"请选择\",defaultValue:null,type:\"date\",span:24,labelWidth:null,style:{width:\"100%\"},disabled:!1,clearable:!0,required:!0,format:\"yyyy-MM-dd\",\"value-format\":\"yyyy-MM-dd\",readonly:!1,regList:[],changeTag:!0,document:\"https://element.eleme.cn/#/zh-CN/component/date-picker\"},{label:\"日期范围\",tag:\"el-date-picker\",tagIcon:\"date-range\",defaultValue:null,span:24,labelWidth:null,style:{width:\"100%\"},type:\"daterange\",\"range-separator\":\"至\",\"start-placeholder\":\"开始日期\",\"end-placeholder\":\"结束日期\",disabled:!1,clearable:!0,required:!0,format:\"yyyy-MM-dd\",\"value-format\":\"yyyy-MM-dd\",readonly:!1,regList:[],changeTag:!0,document:\"https://element.eleme.cn/#/zh-CN/component/date-picker\"},{label:\"评分\",tag:\"el-rate\",tagIcon:\"rate\",defaultValue:0,span:24,labelWidth:null,style:{},max:5,\"allow-half\":!1,\"show-text\":!1,\"show-score\":!1,disabled:!1,required:!0,regList:[],changeTag:!0,document:\"https://element.eleme.cn/#/zh-CN/component/rate\"},{label:\"颜色选择\",tag:\"el-color-picker\",tagIcon:\"color\",defaultValue:null,labelWidth:null,\"show-alpha\":!1,\"color-format\":\"\",disabled:!1,required:!0,size:\"medium\",regList:[],changeTag:!0,document:\"https://element.eleme.cn/#/zh-CN/component/color-picker\"},{label:\"上传\",tag:\"el-upload\",tagIcon:\"upload\",action:\"https://jsonplaceholder.typicode.com/posts/\",defaultValue:null,labelWidth:null,disabled:!1,required:!0,accept:\"\",name:\"file\",\"auto-upload\":!0,showTip:!1,buttonText:\"点击上传\",fileSize:2,sizeUnit:\"MB\",\"list-type\":\"text\",multiple:!1,regList:[],changeTag:!0,document:\"https://element.eleme.cn/#/zh-CN/component/upload\"}],l=[{layout:\"rowFormItem\",tagIcon:\"row\",type:\"default\",justify:\"start\",align:\"top\",label:\"行容器\",layoutTree:!0,children:[],document:\"https://element.eleme.cn/#/zh-CN/component/layout\"},{layout:\"colFormItem\",label:\"按钮\",changeTag:!0,labelWidth:null,tag:\"el-button\",tagIcon:\"button\",span:24,default:\"主要按钮\",type:\"primary\",icon:\"el-icon-search\",size:\"medium\",disabled:!1,document:\"https://element.eleme.cn/#/zh-CN/component/button\"}],c={\"el-input\":\"blur\",\"el-input-number\":\"blur\",\"el-select\":\"change\",\"el-radio-group\":\"change\",\"el-checkbox-group\":\"change\",\"el-cascader\":\"change\",\"el-time-picker\":\"change\",\"el-date-picker\":\"change\",\"el-rate\":\"change\"}},\"3ab9\":function(e,t,a){\"use strict\";a.d(t,\"f\",(function(){return n})),a.d(t,\"e\",(function(){return i})),a.d(t,\"b\",(function(){return l})),a.d(t,\"g\",(function(){return c})),a.d(t,\"a\",(function(){return r})),a.d(t,\"c\",(function(){return s})),a.d(t,\"d\",(function(){return u}));var o=a(\"b775\");function n(e){return Object(o[\"a\"])({url:\"/flowable/form/list\",method:\"get\",params:e})}function i(e){return Object(o[\"a\"])({url:\"/flowable/form/\"+e,method:\"get\"})}function l(e){return Object(o[\"a\"])({url:\"/flowable/form\",method:\"post\",data:e})}function c(e){return Object(o[\"a\"])({url:\"/flowable/form\",method:\"put\",data:e})}function r(e){return Object(o[\"a\"])({url:\"/flowable/form/addDeployForm\",method:\"post\",data:e})}function s(e){return Object(o[\"a\"])({url:\"/flowable/form/\"+e,method:\"delete\"})}function u(e){return Object(o[\"a\"])({url:\"/flowable/form/export\",method:\"get\",params:e})}},\"3f32\":function(e,t,a){\"use strict\";a(\"630b\")},4771:function(e,t,a){\"use strict\";var o=\"https://lib.baomitu.com/\",n=\"/\";function i(e,t,a){return\"\".concat(o).concat(e,\"/\").concat(t,\"/\").concat(a)}t[\"a\"]={beautifierUrl:i(\"js-beautify\",\"1.13.5\",\"beautifier.min.js\"),monacoEditorUrl:\"\".concat(n,\"libs/monaco-editor/vs\"),tinymceUrl:i(\"tinymce\",\"5.7.0\",\"tinymce.min.js\")}},4923:function(e,t,a){\"use strict\";a.r(t);var o=a(\"b76a\"),n=a.n(o),i=a(\"4758\"),l={itemBtns:function(e,t,a,o){var n=this.$listeners,i=n.copyItem,l=n.deleteItem;return[e(\"span\",{class:\"drawing-item-copy\",attrs:{title:\"复制\"},on:{click:function(e){i(t,o),e.stopPropagation()}}},[e(\"i\",{class:\"el-icon-copy-document\"})]),e(\"span\",{class:\"drawing-item-delete\",attrs:{title:\"删除\"},on:{click:function(e){l(a,o),e.stopPropagation()}}},[e(\"i\",{class:\"el-icon-delete\"})])]}},c={colFormItem:function(e,t,a,o){var n=this,c=this.$listeners.activeItem,s=t.__config__,u=r.apply(this,arguments),d=this.activeId===s.formId?\"drawing-item active-from-item\":\"drawing-item\";this.formConf.unFocusedComponentBorder&&(d+=\" unfocus-bordered\");var p=s.labelWidth?\"\".concat(s.labelWidth,\"px\"):null;return!1===s.showLabel&&(p=\"0\"),e(\"el-col\",{attrs:{span:s.span},class:d,nativeOn:{click:function(e){c(t),e.stopPropagation()}}},[e(\"el-form-item\",{attrs:{\"label-width\":p,label:s.showLabel?s.label:\"\",required:s.required}},[e(i[\"a\"],{key:s.renderKey,attrs:{conf:t},on:{input:function(e){n.$set(s,\"defaultValue\",e)}}},[u])]),l.itemBtns.apply(this,arguments)])},rowFormItem:function(e,t,a,o){var i=this.$listeners.activeItem,c=t.__config__,s=this.activeId===c.formId?\"drawing-row-item active-from-item\":\"drawing-row-item\",u=r.apply(this,arguments);return\"flex\"===t.type&&(u=e(\"el-row\",{attrs:{type:t.type,justify:t.justify,align:t.align}},[u])),e(\"el-col\",{attrs:{span:c.span}},[e(\"el-row\",{attrs:{gutter:c.gutter},class:s,nativeOn:{click:function(e){i(t),e.stopPropagation()}}},[e(\"span\",{class:\"component-name\"},[c.componentName]),e(n.a,{attrs:{list:c.children||[],animation:340,group:\"componentsGroup\"},class:\"drag-wrapper\"},[u]),l.itemBtns.apply(this,arguments)])])},raw:function(e,t,a,o){var n=this,l=t.__config__,c=r.apply(this,arguments);return e(i[\"a\"],{key:l.renderKey,attrs:{conf:t},on:{input:function(e){n.$set(l,\"defaultValue\",e)}}},[c])}};function r(e,t,a,o){var n=this,i=t.__config__;return Array.isArray(i.children)?i.children.map((function(t,a){var o=c[t.__config__.layout];return o?o.call(n,e,t,a,i.children):s.call(n)})):null}function s(){throw new Error(\"没有与\".concat(this.currentItem.__config__.layout,\"匹配的layout\"))}var u,d,p={components:{render:i[\"a\"],draggable:n.a},props:[\"currentItem\",\"index\",\"drawingList\",\"activeId\",\"formConf\"],render:function(e){var t=c[this.currentItem.__config__.layout];return t?t.call(this,e,this.currentItem,this.index,this.drawingList):s.call(this)}},f=p,m=a(\"2877\"),v=Object(m[\"a\"])(f,u,d,!1,null,null,null);t[\"default\"]=v.exports},\"4c3b\":function(e,t,a){\"use strict\";a.d(t,\"a\",(function(){return r}));var o,n=a(\"c88b\"),i=a(\"5c96\"),l=a.n(i),c=a(\"4771\");function r(e){if(o)e(o);else{var t=c[\"a\"].monacoEditorUrl,a=l.a.Loading.service({fullscreen:!0,lock:!0,text:\"编辑器资源初始化中...\",spinner:\"el-icon-loading\",background:\"rgba(255, 255, 255, 0.5)\"});!window.require&&(window.require={}),!window.require.paths&&(window.require.paths={}),window.require.paths.vs=t,Object(n[\"a\"])(\"\".concat(t,\"/loader.js\"),(function(){window.require([\"vs/editor/editor.main\"],(function(){a.close(),o=window.monaco,e(o)}))}))}}},\"630b\":function(e,t,a){},\"6acb\":function(e,t,a){},\"766b\":function(e,t,a){\"use strict\";a.r(t);var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a(\"div\",{staticClass:\"right-board\"},[a(\"el-tabs\",{staticClass:\"center-tabs\",model:{value:e.currentTab,callback:function(t){e.currentTab=t},expression:\"currentTab\"}},[a(\"el-tab-pane\",{attrs:{label:\"组件属性\",name:\"field\"}}),a(\"el-tab-pane\",{attrs:{label:\"表单属性\",name:\"form\"}})],1),a(\"div\",{staticClass:\"field-box\"},[a(\"a\",{staticClass:\"document-link\",attrs:{target:\"_blank\",href:e.documentLink,title:\"查看组件文档\"}},[a(\"i\",{staticClass:\"el-icon-link\"})]),a(\"el-scrollbar\",{staticClass:\"right-scrollbar\"},[a(\"el-form\",{directives:[{name:\"show\",rawName:\"v-show\",value:\"field\"===e.currentTab&&e.showField,expression:\"currentTab==='field' && showField\"}],attrs:{size:\"small\",\"label-width\":\"90px\"}},[e.activeData.__config__.changeTag?a(\"el-form-item\",{attrs:{label:\"组件类型\"}},[a(\"el-select\",{style:{width:\"100%\"},attrs:{placeholder:\"请选择组件类型\"},on:{change:e.tagChange},model:{value:e.activeData.__config__.tagIcon,callback:function(t){e.$set(e.activeData.__config__,\"tagIcon\",t)},expression:\"activeData.__config__.tagIcon\"}},e._l(e.tagList,(function(t){return a(\"el-option-group\",{key:t.label,attrs:{label:t.label}},e._l(t.options,(function(t){return a(\"el-option\",{key:t.__config__.label,attrs:{label:t.__config__.label,value:t.__config__.tagIcon}},[a(\"svg-icon\",{staticClass:\"node-icon\",attrs:{\"icon-class\":t.__config__.tagIcon}}),a(\"span\",[e._v(\" \"+e._s(t.__config__.label))])],1)})),1)})),1)],1):e._e(),void 0!==e.activeData.__vModel__?a(\"el-form-item\",{attrs:{label:\"字段名\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入字段名（v-model）\"},model:{value:e.activeData.__vModel__,callback:function(t){e.$set(e.activeData,\"__vModel__\",t)},expression:\"activeData.__vModel__\"}})],1):e._e(),void 0!==e.activeData.__config__.componentName?a(\"el-form-item\",{attrs:{label:\"组件名\"}},[e._v(\" \"+e._s(e.activeData.__config__.componentName)+\" \")]):e._e(),void 0!==e.activeData.__config__.label?a(\"el-form-item\",{attrs:{label:\"标题\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入标题\"},on:{input:e.changeRenderKey},model:{value:e.activeData.__config__.label,callback:function(t){e.$set(e.activeData.__config__,\"label\",t)},expression:\"activeData.__config__.label\"}})],1):e._e(),void 0!==e.activeData.placeholder?a(\"el-form-item\",{attrs:{label:\"占位提示\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入占位提示\"},on:{input:e.changeRenderKey},model:{value:e.activeData.placeholder,callback:function(t){e.$set(e.activeData,\"placeholder\",t)},expression:\"activeData.placeholder\"}})],1):e._e(),void 0!==e.activeData[\"start-placeholder\"]?a(\"el-form-item\",{attrs:{label:\"开始占位\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入占位提示\"},model:{value:e.activeData[\"start-placeholder\"],callback:function(t){e.$set(e.activeData,\"start-placeholder\",t)},expression:\"activeData['start-placeholder']\"}})],1):e._e(),void 0!==e.activeData[\"end-placeholder\"]?a(\"el-form-item\",{attrs:{label:\"结束占位\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入占位提示\"},model:{value:e.activeData[\"end-placeholder\"],callback:function(t){e.$set(e.activeData,\"end-placeholder\",t)},expression:\"activeData['end-placeholder']\"}})],1):e._e(),void 0!==e.activeData.__config__.span?a(\"el-form-item\",{attrs:{label:\"表单栅格\"}},[a(\"el-slider\",{attrs:{max:24,min:1,marks:{12:\"\"}},on:{change:e.spanChange},model:{value:e.activeData.__config__.span,callback:function(t){e.$set(e.activeData.__config__,\"span\",t)},expression:\"activeData.__config__.span\"}})],1):e._e(),\"rowFormItem\"===e.activeData.__config__.layout&&void 0!==e.activeData.gutter?a(\"el-form-item\",{attrs:{label:\"栅格间隔\"}},[a(\"el-input-number\",{attrs:{min:0,placeholder:\"栅格间隔\"},model:{value:e.activeData.gutter,callback:function(t){e.$set(e.activeData,\"gutter\",t)},expression:\"activeData.gutter\"}})],1):e._e(),\"rowFormItem\"===e.activeData.__config__.layout&&void 0!==e.activeData.type?a(\"el-form-item\",{attrs:{label:\"布局模式\"}},[a(\"el-radio-group\",{model:{value:e.activeData.type,callback:function(t){e.$set(e.activeData,\"type\",t)},expression:\"activeData.type\"}},[a(\"el-radio-button\",{attrs:{label:\"default\"}}),a(\"el-radio-button\",{attrs:{label:\"flex\"}})],1)],1):e._e(),void 0!==e.activeData.justify&&\"flex\"===e.activeData.type?a(\"el-form-item\",{attrs:{label:\"水平排列\"}},[a(\"el-select\",{style:{width:\"100%\"},attrs:{placeholder:\"请选择水平排列\"},model:{value:e.activeData.justify,callback:function(t){e.$set(e.activeData,\"justify\",t)},expression:\"activeData.justify\"}},e._l(e.justifyOptions,(function(e,t){return a(\"el-option\",{key:t,attrs:{label:e.label,value:e.value}})})),1)],1):e._e(),void 0!==e.activeData.align&&\"flex\"===e.activeData.type?a(\"el-form-item\",{attrs:{label:\"垂直排列\"}},[a(\"el-radio-group\",{model:{value:e.activeData.align,callback:function(t){e.$set(e.activeData,\"align\",t)},expression:\"activeData.align\"}},[a(\"el-radio-button\",{attrs:{label:\"top\"}}),a(\"el-radio-button\",{attrs:{label:\"middle\"}}),a(\"el-radio-button\",{attrs:{label:\"bottom\"}})],1)],1):e._e(),void 0!==e.activeData.__config__.labelWidth?a(\"el-form-item\",{attrs:{label:\"标签宽度\"}},[a(\"el-input\",{attrs:{type:\"number\",placeholder:\"请输入标签宽度\"},model:{value:e.activeData.__config__.labelWidth,callback:function(t){e.$set(e.activeData.__config__,\"labelWidth\",e._n(t))},expression:\"activeData.__config__.labelWidth\"}})],1):e._e(),e.activeData.style&&void 0!==e.activeData.style.width?a(\"el-form-item\",{attrs:{label:\"组件宽度\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入组件宽度\",clearable:\"\"},model:{value:e.activeData.style.width,callback:function(t){e.$set(e.activeData.style,\"width\",t)},expression:\"activeData.style.width\"}})],1):e._e(),void 0!==e.activeData.__vModel__?a(\"el-form-item\",{attrs:{label:\"默认值\"}},[a(\"el-input\",{attrs:{value:e.setDefaultValue(e.activeData.__config__.defaultValue),placeholder:\"请输入默认值\"},on:{input:e.onDefaultValueInput}})],1):e._e(),\"el-checkbox-group\"===e.activeData.__config__.tag?a(\"el-form-item\",{attrs:{label:\"至少应选\"}},[a(\"el-input-number\",{attrs:{value:e.activeData.min,min:0,placeholder:\"至少应选\"},on:{input:function(t){return e.$set(e.activeData,\"min\",t||void 0)}}})],1):e._e(),\"el-checkbox-group\"===e.activeData.__config__.tag?a(\"el-form-item\",{attrs:{label:\"最多可选\"}},[a(\"el-input-number\",{attrs:{value:e.activeData.max,min:0,placeholder:\"最多可选\"},on:{input:function(t){return e.$set(e.activeData,\"max\",t||void 0)}}})],1):e._e(),e.activeData.__slot__&&void 0!==e.activeData.__slot__.prepend?a(\"el-form-item\",{attrs:{label:\"前缀\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入前缀\"},model:{value:e.activeData.__slot__.prepend,callback:function(t){e.$set(e.activeData.__slot__,\"prepend\",t)},expression:\"activeData.__slot__.prepend\"}})],1):e._e(),e.activeData.__slot__&&void 0!==e.activeData.__slot__.append?a(\"el-form-item\",{attrs:{label:\"后缀\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入后缀\"},model:{value:e.activeData.__slot__.append,callback:function(t){e.$set(e.activeData.__slot__,\"append\",t)},expression:\"activeData.__slot__.append\"}})],1):e._e(),void 0!==e.activeData[\"prefix-icon\"]?a(\"el-form-item\",{attrs:{label:\"前图标\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入前图标名称\"},model:{value:e.activeData[\"prefix-icon\"],callback:function(t){e.$set(e.activeData,\"prefix-icon\",t)},expression:\"activeData['prefix-icon']\"}},[a(\"el-button\",{attrs:{slot:\"append\",icon:\"el-icon-thumb\"},on:{click:function(t){return e.openIconsDialog(\"prefix-icon\")}},slot:\"append\"},[e._v(\" 选择 \")])],1)],1):e._e(),void 0!==e.activeData[\"suffix-icon\"]?a(\"el-form-item\",{attrs:{label:\"后图标\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入后图标名称\"},model:{value:e.activeData[\"suffix-icon\"],callback:function(t){e.$set(e.activeData,\"suffix-icon\",t)},expression:\"activeData['suffix-icon']\"}},[a(\"el-button\",{attrs:{slot:\"append\",icon:\"el-icon-thumb\"},on:{click:function(t){return e.openIconsDialog(\"suffix-icon\")}},slot:\"append\"},[e._v(\" 选择 \")])],1)],1):e._e(),void 0!==e.activeData[\"icon\"]&&\"el-button\"===e.activeData.__config__.tag?a(\"el-form-item\",{attrs:{label:\"按钮图标\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入按钮图标名称\"},model:{value:e.activeData[\"icon\"],callback:function(t){e.$set(e.activeData,\"icon\",t)},expression:\"activeData['icon']\"}},[a(\"el-button\",{attrs:{slot:\"append\",icon:\"el-icon-thumb\"},on:{click:function(t){return e.openIconsDialog(\"icon\")}},slot:\"append\"},[e._v(\" 选择 \")])],1)],1):e._e(),\"el-cascader\"===e.activeData.__config__.tag?a(\"el-form-item\",{attrs:{label:\"选项分隔符\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入选项分隔符\"},model:{value:e.activeData.separator,callback:function(t){e.$set(e.activeData,\"separator\",t)},expression:\"activeData.separator\"}})],1):e._e(),void 0!==e.activeData.autosize?a(\"el-form-item\",{attrs:{label:\"最小行数\"}},[a(\"el-input-number\",{attrs:{min:1,placeholder:\"最小行数\"},model:{value:e.activeData.autosize.minRows,callback:function(t){e.$set(e.activeData.autosize,\"minRows\",t)},expression:\"activeData.autosize.minRows\"}})],1):e._e(),void 0!==e.activeData.autosize?a(\"el-form-item\",{attrs:{label:\"最大行数\"}},[a(\"el-input-number\",{attrs:{min:1,placeholder:\"最大行数\"},model:{value:e.activeData.autosize.maxRows,callback:function(t){e.$set(e.activeData.autosize,\"maxRows\",t)},expression:\"activeData.autosize.maxRows\"}})],1):e._e(),e.isShowMin?a(\"el-form-item\",{attrs:{label:\"最小值\"}},[a(\"el-input-number\",{attrs:{placeholder:\"最小值\"},model:{value:e.activeData.min,callback:function(t){e.$set(e.activeData,\"min\",t)},expression:\"activeData.min\"}})],1):e._e(),e.isShowMax?a(\"el-form-item\",{attrs:{label:\"最大值\"}},[a(\"el-input-number\",{attrs:{placeholder:\"最大值\"},model:{value:e.activeData.max,callback:function(t){e.$set(e.activeData,\"max\",t)},expression:\"activeData.max\"}})],1):e._e(),void 0!==e.activeData.height?a(\"el-form-item\",{attrs:{label:\"组件高度\"}},[a(\"el-input-number\",{attrs:{placeholder:\"高度\"},on:{input:e.changeRenderKey},model:{value:e.activeData.height,callback:function(t){e.$set(e.activeData,\"height\",t)},expression:\"activeData.height\"}})],1):e._e(),e.isShowStep?a(\"el-form-item\",{attrs:{label:\"步长\"}},[a(\"el-input-number\",{attrs:{placeholder:\"步数\"},model:{value:e.activeData.step,callback:function(t){e.$set(e.activeData,\"step\",t)},expression:\"activeData.step\"}})],1):e._e(),\"el-input-number\"===e.activeData.__config__.tag?a(\"el-form-item\",{attrs:{label:\"精度\"}},[a(\"el-input-number\",{attrs:{min:0,placeholder:\"精度\"},model:{value:e.activeData.precision,callback:function(t){e.$set(e.activeData,\"precision\",t)},expression:\"activeData.precision\"}})],1):e._e(),\"el-input-number\"===e.activeData.__config__.tag?a(\"el-form-item\",{attrs:{label:\"按钮位置\"}},[a(\"el-radio-group\",{model:{value:e.activeData[\"controls-position\"],callback:function(t){e.$set(e.activeData,\"controls-position\",t)},expression:\"activeData['controls-position']\"}},[a(\"el-radio-button\",{attrs:{label:\"\"}},[e._v(\" 默认 \")]),a(\"el-radio-button\",{attrs:{label:\"right\"}},[e._v(\" 右侧 \")])],1)],1):e._e(),void 0!==e.activeData.maxlength?a(\"el-form-item\",{attrs:{label:\"最多输入\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入字符长度\"},model:{value:e.activeData.maxlength,callback:function(t){e.$set(e.activeData,\"maxlength\",t)},expression:\"activeData.maxlength\"}},[a(\"template\",{slot:\"append\"},[e._v(\" 个字符 \")])],2)],1):e._e(),void 0!==e.activeData[\"active-text\"]?a(\"el-form-item\",{attrs:{label:\"开启提示\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入开启提示\"},model:{value:e.activeData[\"active-text\"],callback:function(t){e.$set(e.activeData,\"active-text\",t)},expression:\"activeData['active-text']\"}})],1):e._e(),void 0!==e.activeData[\"inactive-text\"]?a(\"el-form-item\",{attrs:{label:\"关闭提示\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入关闭提示\"},model:{value:e.activeData[\"inactive-text\"],callback:function(t){e.$set(e.activeData,\"inactive-text\",t)},expression:\"activeData['inactive-text']\"}})],1):e._e(),void 0!==e.activeData[\"active-value\"]?a(\"el-form-item\",{attrs:{label:\"开启值\"}},[a(\"el-input\",{attrs:{value:e.setDefaultValue(e.activeData[\"active-value\"]),placeholder:\"请输入开启值\"},on:{input:function(t){return e.onSwitchValueInput(t,\"active-value\")}}})],1):e._e(),void 0!==e.activeData[\"inactive-value\"]?a(\"el-form-item\",{attrs:{label:\"关闭值\"}},[a(\"el-input\",{attrs:{value:e.setDefaultValue(e.activeData[\"inactive-value\"]),placeholder:\"请输入关闭值\"},on:{input:function(t){return e.onSwitchValueInput(t,\"inactive-value\")}}})],1):e._e(),void 0!==e.activeData.type&&\"el-date-picker\"===e.activeData.__config__.tag?a(\"el-form-item\",{attrs:{label:\"时间类型\"}},[a(\"el-select\",{style:{width:\"100%\"},attrs:{placeholder:\"请选择时间类型\"},on:{change:e.dateTypeChange},model:{value:e.activeData.type,callback:function(t){e.$set(e.activeData,\"type\",t)},expression:\"activeData.type\"}},e._l(e.dateOptions,(function(e,t){return a(\"el-option\",{key:t,attrs:{label:e.label,value:e.value}})})),1)],1):e._e(),void 0!==e.activeData.name?a(\"el-form-item\",{attrs:{label:\"文件字段名\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入上传文件字段名\"},model:{value:e.activeData.name,callback:function(t){e.$set(e.activeData,\"name\",t)},expression:\"activeData.name\"}})],1):e._e(),void 0!==e.activeData.accept?a(\"el-form-item\",{attrs:{label:\"文件类型\"}},[a(\"el-select\",{style:{width:\"100%\"},attrs:{placeholder:\"请选择文件类型\",clearable:\"\"},model:{value:e.activeData.accept,callback:function(t){e.$set(e.activeData,\"accept\",t)},expression:\"activeData.accept\"}},[a(\"el-option\",{attrs:{label:\"图片\",value:\"image/*\"}}),a(\"el-option\",{attrs:{label:\"视频\",value:\"video/*\"}}),a(\"el-option\",{attrs:{label:\"音频\",value:\"audio/*\"}}),a(\"el-option\",{attrs:{label:\"excel\",value:\".xls,.xlsx\"}}),a(\"el-option\",{attrs:{label:\"word\",value:\".doc,.docx\"}}),a(\"el-option\",{attrs:{label:\"pdf\",value:\".pdf\"}}),a(\"el-option\",{attrs:{label:\"txt\",value:\".txt\"}})],1)],1):e._e(),void 0!==e.activeData.__config__.fileSize?a(\"el-form-item\",{attrs:{label:\"文件大小\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入文件大小\"},model:{value:e.activeData.__config__.fileSize,callback:function(t){e.$set(e.activeData.__config__,\"fileSize\",e._n(t))},expression:\"activeData.__config__.fileSize\"}},[a(\"el-select\",{style:{width:\"66px\"},attrs:{slot:\"append\"},slot:\"append\",model:{value:e.activeData.__config__.sizeUnit,callback:function(t){e.$set(e.activeData.__config__,\"sizeUnit\",t)},expression:\"activeData.__config__.sizeUnit\"}},[a(\"el-option\",{attrs:{label:\"KB\",value:\"KB\"}}),a(\"el-option\",{attrs:{label:\"MB\",value:\"MB\"}}),a(\"el-option\",{attrs:{label:\"GB\",value:\"GB\"}})],1)],1)],1):e._e(),void 0!==e.activeData.action?a(\"el-form-item\",{attrs:{label:\"上传地址\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入上传地址\",clearable:\"\"},model:{value:e.activeData.action,callback:function(t){e.$set(e.activeData,\"action\",t)},expression:\"activeData.action\"}})],1):e._e(),void 0!==e.activeData[\"list-type\"]?a(\"el-form-item\",{attrs:{label:\"列表类型\"}},[a(\"el-radio-group\",{attrs:{size:\"small\"},model:{value:e.activeData[\"list-type\"],callback:function(t){e.$set(e.activeData,\"list-type\",t)},expression:\"activeData['list-type']\"}},[a(\"el-radio-button\",{attrs:{label:\"text\"}},[e._v(\" text \")]),a(\"el-radio-button\",{attrs:{label:\"picture\"}},[e._v(\" picture \")]),a(\"el-radio-button\",{attrs:{label:\"picture-card\"}},[e._v(\" picture-card \")])],1)],1):e._e(),void 0!==e.activeData.type&&\"el-button\"===e.activeData.__config__.tag?a(\"el-form-item\",{attrs:{label:\"按钮类型\"}},[a(\"el-select\",{style:{width:\"100%\"},model:{value:e.activeData.type,callback:function(t){e.$set(e.activeData,\"type\",t)},expression:\"activeData.type\"}},[a(\"el-option\",{attrs:{label:\"primary\",value:\"primary\"}}),a(\"el-option\",{attrs:{label:\"success\",value:\"success\"}}),a(\"el-option\",{attrs:{label:\"warning\",value:\"warning\"}}),a(\"el-option\",{attrs:{label:\"danger\",value:\"danger\"}}),a(\"el-option\",{attrs:{label:\"info\",value:\"info\"}}),a(\"el-option\",{attrs:{label:\"text\",value:\"text\"}})],1)],1):e._e(),void 0!==e.activeData.__config__.buttonText?a(\"el-form-item\",{directives:[{name:\"show\",rawName:\"v-show\",value:\"picture-card\"!==e.activeData[\"list-type\"],expression:\"'picture-card' !== activeData['list-type']\"}],attrs:{label:\"按钮文字\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入按钮文字\"},model:{value:e.activeData.__config__.buttonText,callback:function(t){e.$set(e.activeData.__config__,\"buttonText\",t)},expression:\"activeData.__config__.buttonText\"}})],1):e._e(),\"el-button\"===e.activeData.__config__.tag?a(\"el-form-item\",{attrs:{label:\"按钮文字\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入按钮文字\"},model:{value:e.activeData.__slot__.default,callback:function(t){e.$set(e.activeData.__slot__,\"default\",t)},expression:\"activeData.__slot__.default\"}})],1):e._e(),void 0!==e.activeData[\"range-separator\"]?a(\"el-form-item\",{attrs:{label:\"分隔符\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入分隔符\"},model:{value:e.activeData[\"range-separator\"],callback:function(t){e.$set(e.activeData,\"range-separator\",t)},expression:\"activeData['range-separator']\"}})],1):e._e(),void 0!==e.activeData[\"picker-options\"]?a(\"el-form-item\",{attrs:{label:\"时间段\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入时间段\"},model:{value:e.activeData[\"picker-options\"].selectableRange,callback:function(t){e.$set(e.activeData[\"picker-options\"],\"selectableRange\",t)},expression:\"activeData['picker-options'].selectableRange\"}})],1):e._e(),void 0!==e.activeData.format?a(\"el-form-item\",{attrs:{label:\"时间格式\"}},[a(\"el-input\",{attrs:{value:e.activeData.format,placeholder:\"请输入时间格式\"},on:{input:function(t){return e.setTimeValue(t)}}})],1):e._e(),[\"el-checkbox-group\",\"el-radio-group\",\"el-select\"].indexOf(e.activeData.__config__.tag)>-1?[a(\"el-divider\",[e._v(\"选项\")]),a(\"draggable\",{attrs:{list:e.activeData.__slot__.options,animation:340,group:\"selectItem\",handle:\".option-drag\"}},e._l(e.activeData.__slot__.options,(function(t,o){return a(\"div\",{key:o,staticClass:\"select-item\"},[a(\"div\",{staticClass:\"select-line-icon option-drag\"},[a(\"i\",{staticClass:\"el-icon-s-operation\"})]),a(\"el-input\",{attrs:{placeholder:\"选项名\",size:\"small\"},model:{value:t.label,callback:function(a){e.$set(t,\"label\",a)},expression:\"item.label\"}}),a(\"el-input\",{attrs:{placeholder:\"选项值\",size:\"small\",value:t.value},on:{input:function(a){return e.setOptionValue(t,a)}}}),a(\"div\",{staticClass:\"close-btn select-line-icon\",on:{click:function(t){return e.activeData.__slot__.options.splice(o,1)}}},[a(\"i\",{staticClass:\"el-icon-remove-outline\"})])],1)})),0),a(\"div\",{staticStyle:{\"margin-left\":\"20px\"}},[a(\"el-button\",{staticStyle:{\"padding-bottom\":\"0\"},attrs:{icon:\"el-icon-circle-plus-outline\",type:\"text\"},on:{click:e.addSelectItem}},[e._v(\" 添加选项 \")])],1),a(\"el-divider\")]:e._e(),[\"el-cascader\",\"el-table\"].includes(e.activeData.__config__.tag)?[a(\"el-divider\",[e._v(\"选项\")]),e.activeData.__config__.dataType?a(\"el-form-item\",{attrs:{label:\"数据类型\"}},[a(\"el-radio-group\",{attrs:{size:\"small\"},model:{value:e.activeData.__config__.dataType,callback:function(t){e.$set(e.activeData.__config__,\"dataType\",t)},expression:\"activeData.__config__.dataType\"}},[a(\"el-radio-button\",{attrs:{label:\"dynamic\"}},[e._v(\" 动态数据 \")]),a(\"el-radio-button\",{attrs:{label:\"static\"}},[e._v(\" 静态数据 \")])],1)],1):e._e(),\"dynamic\"===e.activeData.__config__.dataType?[a(\"el-form-item\",{attrs:{label:\"接口地址\"}},[a(\"el-input\",{attrs:{title:e.activeData.__config__.url,placeholder:\"请输入接口地址\",clearable:\"\"},on:{blur:function(t){return e.$emit(\"fetch-data\",e.activeData)}},model:{value:e.activeData.__config__.url,callback:function(t){e.$set(e.activeData.__config__,\"url\",t)},expression:\"activeData.__config__.url\"}},[a(\"el-select\",{style:{width:\"85px\"},attrs:{slot:\"prepend\"},on:{change:function(t){return e.$emit(\"fetch-data\",e.activeData)}},slot:\"prepend\",model:{value:e.activeData.__config__.method,callback:function(t){e.$set(e.activeData.__config__,\"method\",t)},expression:\"activeData.__config__.method\"}},[a(\"el-option\",{attrs:{label:\"get\",value:\"get\"}}),a(\"el-option\",{attrs:{label:\"post\",value:\"post\"}}),a(\"el-option\",{attrs:{label:\"put\",value:\"put\"}}),a(\"el-option\",{attrs:{label:\"delete\",value:\"delete\"}})],1)],1)],1),a(\"el-form-item\",{attrs:{label:\"数据位置\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入数据位置\"},on:{blur:function(t){return e.$emit(\"fetch-data\",e.activeData)}},model:{value:e.activeData.__config__.dataPath,callback:function(t){e.$set(e.activeData.__config__,\"dataPath\",t)},expression:\"activeData.__config__.dataPath\"}})],1),e.activeData.props&&e.activeData.props.props?[a(\"el-form-item\",{attrs:{label:\"标签键名\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入标签键名\"},model:{value:e.activeData.props.props.label,callback:function(t){e.$set(e.activeData.props.props,\"label\",t)},expression:\"activeData.props.props.label\"}})],1),a(\"el-form-item\",{attrs:{label:\"值键名\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入值键名\"},model:{value:e.activeData.props.props.value,callback:function(t){e.$set(e.activeData.props.props,\"value\",t)},expression:\"activeData.props.props.value\"}})],1),a(\"el-form-item\",{attrs:{label:\"子级键名\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入子级键名\"},model:{value:e.activeData.props.props.children,callback:function(t){e.$set(e.activeData.props.props,\"children\",t)},expression:\"activeData.props.props.children\"}})],1)]:e._e()]:e._e(),\"static\"===e.activeData.__config__.dataType?a(\"el-tree\",{attrs:{draggable:\"\",data:e.activeData.options,\"node-key\":\"id\",\"expand-on-click-node\":!1,\"render-content\":e.renderContent}}):e._e(),\"static\"===e.activeData.__config__.dataType?a(\"div\",{staticStyle:{\"margin-left\":\"20px\"}},[a(\"el-button\",{staticStyle:{\"padding-bottom\":\"0\"},attrs:{icon:\"el-icon-circle-plus-outline\",type:\"text\"},on:{click:e.addTreeItem}},[e._v(\" 添加父级 \")])],1):e._e(),a(\"el-divider\")]:e._e(),void 0!==e.activeData.__config__.optionType?a(\"el-form-item\",{attrs:{label:\"选项样式\"}},[a(\"el-radio-group\",{model:{value:e.activeData.__config__.optionType,callback:function(t){e.$set(e.activeData.__config__,\"optionType\",t)},expression:\"activeData.__config__.optionType\"}},[a(\"el-radio-button\",{attrs:{label:\"default\"}},[e._v(\" 默认 \")]),a(\"el-radio-button\",{attrs:{label:\"button\"}},[e._v(\" 按钮 \")])],1)],1):e._e(),void 0!==e.activeData[\"active-color\"]?a(\"el-form-item\",{attrs:{label:\"开启颜色\"}},[a(\"el-color-picker\",{model:{value:e.activeData[\"active-color\"],callback:function(t){e.$set(e.activeData,\"active-color\",t)},expression:\"activeData['active-color']\"}})],1):e._e(),void 0!==e.activeData[\"inactive-color\"]?a(\"el-form-item\",{attrs:{label:\"关闭颜色\"}},[a(\"el-color-picker\",{model:{value:e.activeData[\"inactive-color\"],callback:function(t){e.$set(e.activeData,\"inactive-color\",t)},expression:\"activeData['inactive-color']\"}})],1):e._e(),void 0!==e.activeData.__config__.showLabel&&void 0!==e.activeData.__config__.labelWidth?a(\"el-form-item\",{attrs:{label:\"显示标签\"}},[a(\"el-switch\",{model:{value:e.activeData.__config__.showLabel,callback:function(t){e.$set(e.activeData.__config__,\"showLabel\",t)},expression:\"activeData.__config__.showLabel\"}})],1):e._e(),void 0!==e.activeData.branding?a(\"el-form-item\",{attrs:{label:\"品牌烙印\"}},[a(\"el-switch\",{on:{input:e.changeRenderKey},model:{value:e.activeData.branding,callback:function(t){e.$set(e.activeData,\"branding\",t)},expression:\"activeData.branding\"}})],1):e._e(),void 0!==e.activeData[\"allow-half\"]?a(\"el-form-item\",{attrs:{label:\"允许半选\"}},[a(\"el-switch\",{model:{value:e.activeData[\"allow-half\"],callback:function(t){e.$set(e.activeData,\"allow-half\",t)},expression:\"activeData['allow-half']\"}})],1):e._e(),void 0!==e.activeData[\"show-text\"]?a(\"el-form-item\",{attrs:{label:\"辅助文字\"}},[a(\"el-switch\",{on:{change:e.rateTextChange},model:{value:e.activeData[\"show-text\"],callback:function(t){e.$set(e.activeData,\"show-text\",t)},expression:\"activeData['show-text']\"}})],1):e._e(),void 0!==e.activeData[\"show-score\"]?a(\"el-form-item\",{attrs:{label:\"显示分数\"}},[a(\"el-switch\",{on:{change:e.rateScoreChange},model:{value:e.activeData[\"show-score\"],callback:function(t){e.$set(e.activeData,\"show-score\",t)},expression:\"activeData['show-score']\"}})],1):e._e(),void 0!==e.activeData[\"show-stops\"]?a(\"el-form-item\",{attrs:{label:\"显示间断点\"}},[a(\"el-switch\",{model:{value:e.activeData[\"show-stops\"],callback:function(t){e.$set(e.activeData,\"show-stops\",t)},expression:\"activeData['show-stops']\"}})],1):e._e(),void 0!==e.activeData.range?a(\"el-form-item\",{attrs:{label:\"范围选择\"}},[a(\"el-switch\",{on:{change:e.rangeChange},model:{value:e.activeData.range,callback:function(t){e.$set(e.activeData,\"range\",t)},expression:\"activeData.range\"}})],1):e._e(),void 0!==e.activeData.__config__.border&&\"default\"===e.activeData.__config__.optionType?a(\"el-form-item\",{attrs:{label:\"是否带边框\"}},[a(\"el-switch\",{model:{value:e.activeData.__config__.border,callback:function(t){e.$set(e.activeData.__config__,\"border\",t)},expression:\"activeData.__config__.border\"}})],1):e._e(),\"el-color-picker\"===e.activeData.__config__.tag?a(\"el-form-item\",{attrs:{label:\"颜色格式\"}},[a(\"el-select\",{style:{width:\"100%\"},attrs:{placeholder:\"请选择颜色格式\",clearable:\"\"},on:{change:e.colorFormatChange},model:{value:e.activeData[\"color-format\"],callback:function(t){e.$set(e.activeData,\"color-format\",t)},expression:\"activeData['color-format']\"}},e._l(e.colorFormatOptions,(function(e,t){return a(\"el-option\",{key:t,attrs:{label:e.label,value:e.value}})})),1)],1):e._e(),void 0===e.activeData.size||\"button\"!==e.activeData.__config__.optionType&&!e.activeData.__config__.border&&\"el-color-picker\"!==e.activeData.__config__.tag&&\"el-button\"!==e.activeData.__config__.tag?e._e():a(\"el-form-item\",{attrs:{label:\"组件尺寸\"}},[a(\"el-radio-group\",{model:{value:e.activeData.size,callback:function(t){e.$set(e.activeData,\"size\",t)},expression:\"activeData.size\"}},[a(\"el-radio-button\",{attrs:{label:\"medium\"}},[e._v(\" 中等 \")]),a(\"el-radio-button\",{attrs:{label:\"small\"}},[e._v(\" 较小 \")]),a(\"el-radio-button\",{attrs:{label:\"mini\"}},[e._v(\" 迷你 \")])],1)],1),void 0!==e.activeData[\"show-word-limit\"]?a(\"el-form-item\",{attrs:{label:\"输入统计\"}},[a(\"el-switch\",{model:{value:e.activeData[\"show-word-limit\"],callback:function(t){e.$set(e.activeData,\"show-word-limit\",t)},expression:\"activeData['show-word-limit']\"}})],1):e._e(),\"el-input-number\"===e.activeData.__config__.tag?a(\"el-form-item\",{attrs:{label:\"严格步数\"}},[a(\"el-switch\",{model:{value:e.activeData[\"step-strictly\"],callback:function(t){e.$set(e.activeData,\"step-strictly\",t)},expression:\"activeData['step-strictly']\"}})],1):e._e(),\"el-cascader\"===e.activeData.__config__.tag?a(\"el-form-item\",{attrs:{label:\"任选层级\"}},[a(\"el-switch\",{model:{value:e.activeData.props.props.checkStrictly,callback:function(t){e.$set(e.activeData.props.props,\"checkStrictly\",t)},expression:\"activeData.props.props.checkStrictly\"}})],1):e._e(),\"el-cascader\"===e.activeData.__config__.tag?a(\"el-form-item\",{attrs:{label:\"是否多选\"}},[a(\"el-switch\",{model:{value:e.activeData.props.props.multiple,callback:function(t){e.$set(e.activeData.props.props,\"multiple\",t)},expression:\"activeData.props.props.multiple\"}})],1):e._e(),\"el-cascader\"===e.activeData.__config__.tag?a(\"el-form-item\",{attrs:{label:\"展示全路径\"}},[a(\"el-switch\",{model:{value:e.activeData[\"show-all-levels\"],callback:function(t){e.$set(e.activeData,\"show-all-levels\",t)},expression:\"activeData['show-all-levels']\"}})],1):e._e(),\"el-cascader\"===e.activeData.__config__.tag?a(\"el-form-item\",{attrs:{label:\"可否筛选\"}},[a(\"el-switch\",{model:{value:e.activeData.filterable,callback:function(t){e.$set(e.activeData,\"filterable\",t)},expression:\"activeData.filterable\"}})],1):e._e(),void 0!==e.activeData.clearable?a(\"el-form-item\",{attrs:{label:\"能否清空\"}},[a(\"el-switch\",{model:{value:e.activeData.clearable,callback:function(t){e.$set(e.activeData,\"clearable\",t)},expression:\"activeData.clearable\"}})],1):e._e(),void 0!==e.activeData.__config__.showTip?a(\"el-form-item\",{attrs:{label:\"显示提示\"}},[a(\"el-switch\",{model:{value:e.activeData.__config__.showTip,callback:function(t){e.$set(e.activeData.__config__,\"showTip\",t)},expression:\"activeData.__config__.showTip\"}})],1):e._e(),\"el-upload\"===e.activeData.__config__.tag?a(\"el-form-item\",{attrs:{label:\"多选文件\"}},[a(\"el-switch\",{model:{value:e.activeData.multiple,callback:function(t){e.$set(e.activeData,\"multiple\",t)},expression:\"activeData.multiple\"}})],1):e._e(),void 0!==e.activeData[\"auto-upload\"]?a(\"el-form-item\",{attrs:{label:\"自动上传\"}},[a(\"el-switch\",{model:{value:e.activeData[\"auto-upload\"],callback:function(t){e.$set(e.activeData,\"auto-upload\",t)},expression:\"activeData['auto-upload']\"}})],1):e._e(),void 0!==e.activeData.readonly?a(\"el-form-item\",{attrs:{label:\"是否只读\"}},[a(\"el-switch\",{model:{value:e.activeData.readonly,callback:function(t){e.$set(e.activeData,\"readonly\",t)},expression:\"activeData.readonly\"}})],1):e._e(),void 0!==e.activeData.disabled?a(\"el-form-item\",{attrs:{label:\"是否禁用\"}},[a(\"el-switch\",{model:{value:e.activeData.disabled,callback:function(t){e.$set(e.activeData,\"disabled\",t)},expression:\"activeData.disabled\"}})],1):e._e(),\"el-select\"===e.activeData.__config__.tag?a(\"el-form-item\",{attrs:{label:\"能否搜索\"}},[a(\"el-switch\",{model:{value:e.activeData.filterable,callback:function(t){e.$set(e.activeData,\"filterable\",t)},expression:\"activeData.filterable\"}})],1):e._e(),\"el-select\"===e.activeData.__config__.tag?a(\"el-form-item\",{attrs:{label:\"是否多选\"}},[a(\"el-switch\",{on:{change:e.multipleChange},model:{value:e.activeData.multiple,callback:function(t){e.$set(e.activeData,\"multiple\",t)},expression:\"activeData.multiple\"}})],1):e._e(),void 0!==e.activeData.__config__.required?a(\"el-form-item\",{attrs:{label:\"是否必填\"}},[a(\"el-switch\",{model:{value:e.activeData.__config__.required,callback:function(t){e.$set(e.activeData.__config__,\"required\",t)},expression:\"activeData.__config__.required\"}})],1):e._e(),e.activeData.__config__.layoutTree?[a(\"el-divider\",[e._v(\"布局结构树\")]),a(\"el-tree\",{attrs:{data:[e.activeData.__config__],props:e.layoutTreeProps,\"node-key\":\"renderKey\",\"default-expand-all\":\"\",draggable:\"\"},scopedSlots:e._u([{key:\"default\",fn:function(t){var o=t.node,n=t.data;return a(\"span\",{},[a(\"span\",{staticClass:\"node-label\"},[a(\"svg-icon\",{staticClass:\"node-icon\",attrs:{\"icon-class\":n.__config__?n.__config__.tagIcon:n.tagIcon}}),e._v(\" \"+e._s(o.label)+\" \")],1)])}}],null,!1,3924665115)})]:e._e(),Array.isArray(e.activeData.__config__.regList)?[a(\"el-divider\",[e._v(\"正则校验\")]),e._l(e.activeData.__config__.regList,(function(t,o){return a(\"div\",{key:o,staticClass:\"reg-item\"},[a(\"span\",{staticClass:\"close-btn\",on:{click:function(t){return e.activeData.__config__.regList.splice(o,1)}}},[a(\"i\",{staticClass:\"el-icon-close\"})]),a(\"el-form-item\",{attrs:{label:\"表达式\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入正则\"},model:{value:t.pattern,callback:function(a){e.$set(t,\"pattern\",a)},expression:\"item.pattern\"}})],1),a(\"el-form-item\",{staticStyle:{\"margin-bottom\":\"0\"},attrs:{label:\"错误提示\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入错误提示\"},model:{value:t.message,callback:function(a){e.$set(t,\"message\",a)},expression:\"item.message\"}})],1)],1)})),a(\"div\",{staticStyle:{\"margin-left\":\"20px\"}},[a(\"el-button\",{attrs:{icon:\"el-icon-circle-plus-outline\",type:\"text\"},on:{click:e.addReg}},[e._v(\" 添加规则 \")])],1)]:e._e()],2),a(\"el-form\",{directives:[{name:\"show\",rawName:\"v-show\",value:\"form\"===e.currentTab,expression:\"currentTab === 'form'\"}],attrs:{size:\"small\",\"label-width\":\"90px\"}},[a(\"el-form-item\",{attrs:{label:\"表单名\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入表单名（ref）\"},model:{value:e.formConf.formRef,callback:function(t){e.$set(e.formConf,\"formRef\",t)},expression:\"formConf.formRef\"}})],1),a(\"el-form-item\",{attrs:{label:\"表单模型\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入数据模型\"},model:{value:e.formConf.formModel,callback:function(t){e.$set(e.formConf,\"formModel\",t)},expression:\"formConf.formModel\"}})],1),a(\"el-form-item\",{attrs:{label:\"校验模型\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入校验模型\"},model:{value:e.formConf.formRules,callback:function(t){e.$set(e.formConf,\"formRules\",t)},expression:\"formConf.formRules\"}})],1),a(\"el-form-item\",{attrs:{label:\"表单尺寸\"}},[a(\"el-radio-group\",{model:{value:e.formConf.size,callback:function(t){e.$set(e.formConf,\"size\",t)},expression:\"formConf.size\"}},[a(\"el-radio-button\",{attrs:{label:\"medium\"}},[e._v(\" 中等 \")]),a(\"el-radio-button\",{attrs:{label:\"small\"}},[e._v(\" 较小 \")]),a(\"el-radio-button\",{attrs:{label:\"mini\"}},[e._v(\" 迷你 \")])],1)],1),a(\"el-form-item\",{attrs:{label:\"标签对齐\"}},[a(\"el-radio-group\",{model:{value:e.formConf.labelPosition,callback:function(t){e.$set(e.formConf,\"labelPosition\",t)},expression:\"formConf.labelPosition\"}},[a(\"el-radio-button\",{attrs:{label:\"left\"}},[e._v(\" 左对齐 \")]),a(\"el-radio-button\",{attrs:{label:\"right\"}},[e._v(\" 右对齐 \")]),a(\"el-radio-button\",{attrs:{label:\"top\"}},[e._v(\" 顶部对齐 \")])],1)],1),a(\"el-form-item\",{attrs:{label:\"标签宽度\"}},[a(\"el-input\",{attrs:{type:\"number\",placeholder:\"请输入标签宽度\"},model:{value:e.formConf.labelWidth,callback:function(t){e.$set(e.formConf,\"labelWidth\",e._n(t))},expression:\"formConf.labelWidth\"}})],1),a(\"el-form-item\",{attrs:{label:\"栅格间隔\"}},[a(\"el-input-number\",{attrs:{min:0,placeholder:\"栅格间隔\"},model:{value:e.formConf.gutter,callback:function(t){e.$set(e.formConf,\"gutter\",t)},expression:\"formConf.gutter\"}})],1),a(\"el-form-item\",{attrs:{label:\"禁用表单\"}},[a(\"el-switch\",{model:{value:e.formConf.disabled,callback:function(t){e.$set(e.formConf,\"disabled\",t)},expression:\"formConf.disabled\"}})],1),a(\"el-form-item\",{attrs:{label:\"表单按钮\"}},[a(\"el-switch\",{model:{value:e.formConf.formBtns,callback:function(t){e.$set(e.formConf,\"formBtns\",t)},expression:\"formConf.formBtns\"}})],1),a(\"el-form-item\",{attrs:{label:\"显示未选中组件边框\"}},[a(\"el-switch\",{model:{value:e.formConf.unFocusedComponentBorder,callback:function(t){e.$set(e.formConf,\"unFocusedComponentBorder\",t)},expression:\"formConf.unFocusedComponentBorder\"}})],1)],1)],1)],1),a(\"treeNode-dialog\",{attrs:{visible:e.dialogVisible,title:\"添加选项\"},on:{\"update:visible\":function(t){e.dialogVisible=t},commit:e.addNode}}),a(\"icons-dialog\",{attrs:{visible:e.iconsVisible,current:e.activeData[e.currentIconModel]},on:{\"update:visible\":function(t){e.iconsVisible=t},select:e.setIcon}})],1)},n=[],i=a(\"3022\"),l=a(\"c81a\"),c=a(\"ed08\"),r=a(\"d0b2\"),s=a(\"2e2a\"),u=a(\"e31c\"),d={date:\"yyyy-MM-dd\",week:\"yyyy 第 WW 周\",month:\"yyyy-MM\",year:\"yyyy\",datetime:\"yyyy-MM-dd HH:mm:ss\",daterange:\"yyyy-MM-dd\",monthrange:\"yyyy-MM\",datetimerange:\"yyyy-MM-dd HH:mm:ss\"},p=[\"tinymce\"],f={components:{TreeNodeDialog:l[\"default\"],IconsDialog:r[\"default\"]},props:[\"showField\",\"activeData\",\"formConf\"],data:function(){return{currentTab:\"field\",currentNode:null,dialogVisible:!1,iconsVisible:!1,currentIconModel:null,dateTypeOptions:[{label:\"日(date)\",value:\"date\"},{label:\"周(week)\",value:\"week\"},{label:\"月(month)\",value:\"month\"},{label:\"年(year)\",value:\"year\"},{label:\"日期时间(datetime)\",value:\"datetime\"}],dateRangeTypeOptions:[{label:\"日期范围(daterange)\",value:\"daterange\"},{label:\"月范围(monthrange)\",value:\"monthrange\"},{label:\"日期时间范围(datetimerange)\",value:\"datetimerange\"}],colorFormatOptions:[{label:\"hex\",value:\"hex\"},{label:\"rgb\",value:\"rgb\"},{label:\"rgba\",value:\"rgba\"},{label:\"hsv\",value:\"hsv\"},{label:\"hsl\",value:\"hsl\"}],justifyOptions:[{label:\"start\",value:\"start\"},{label:\"end\",value:\"end\"},{label:\"center\",value:\"center\"},{label:\"space-around\",value:\"space-around\"},{label:\"space-between\",value:\"space-between\"}],layoutTreeProps:{label:function(e,t){var a=e.__config__;return e.componentName||\"\".concat(a.label,\": \").concat(e.__vModel__)}}}},computed:{documentLink:function(){return this.activeData.__config__.document||\"https://element.eleme.cn/#/zh-CN/component/installation\"},dateOptions:function(){return void 0!==this.activeData.type&&\"el-date-picker\"===this.activeData.__config__.tag?void 0===this.activeData[\"start-placeholder\"]?this.dateTypeOptions:this.dateRangeTypeOptions:[]},tagList:function(){return[{label:\"输入型组件\",options:s[\"b\"]},{label:\"选择型组件\",options:s[\"d\"]}]},activeTag:function(){return this.activeData.__config__.tag},isShowMin:function(){return[\"el-input-number\",\"el-slider\"].indexOf(this.activeTag)>-1},isShowMax:function(){return[\"el-input-number\",\"el-slider\",\"el-rate\"].indexOf(this.activeTag)>-1},isShowStep:function(){return[\"el-input-number\",\"el-slider\"].indexOf(this.activeTag)>-1}},watch:{formConf:{handler:function(e){Object(u[\"f\"])(e)},deep:!0}},methods:{addReg:function(){this.activeData.__config__.regList.push({pattern:\"\",message:\"\"})},addSelectItem:function(){this.activeData.__slot__.options.push({label:\"\",value:\"\"})},addTreeItem:function(){++this.idGlobal,this.dialogVisible=!0,this.currentNode=this.activeData.options},renderContent:function(e,t){var a=this,o=t.node,n=t.data;t.store;return e(\"div\",{class:\"custom-tree-node\"},[e(\"span\",[o.label]),e(\"span\",{class:\"node-operation\"},[e(\"i\",{on:{click:function(){return a.append(n)}},class:\"el-icon-plus\",attrs:{title:\"添加\"}}),e(\"i\",{on:{click:function(){return a.remove(o,n)}},class:\"el-icon-delete\",attrs:{title:\"删除\"}})])])},append:function(e){e.children||this.$set(e,\"children\",[]),this.dialogVisible=!0,this.currentNode=e.children},remove:function(e,t){this.activeData.__config__.defaultValue=[];var a=e.parent,o=a.data.children||a.data,n=o.findIndex((function(e){return e.id===t.id}));o.splice(n,1)},addNode:function(e){this.currentNode.push(e)},setOptionValue:function(e,t){e.value=Object(c[\"f\"])(t)?+t:t},setDefaultValue:function(e){return Array.isArray(e)?e.join(\",\"):\"boolean\"===typeof e?\"\".concat(e):e},onDefaultValueInput:function(e){Object(i[\"isArray\"])(this.activeData.__config__.defaultValue)?this.$set(this.activeData.__config__,\"defaultValue\",e.split(\",\").map((function(e){return Object(c[\"f\"])(e)?+e:e}))):[\"true\",\"false\"].indexOf(e)>-1?this.$set(this.activeData.__config__,\"defaultValue\",JSON.parse(e)):this.$set(this.activeData.__config__,\"defaultValue\",Object(c[\"f\"])(e)?+e:e)},onSwitchValueInput:function(e,t){[\"true\",\"false\"].indexOf(e)>-1?this.$set(this.activeData,t,JSON.parse(e)):this.$set(this.activeData,t,Object(c[\"f\"])(e)?+e:e)},setTimeValue:function(e,t){var a=\"week\"===t?d.date:e;this.$set(this.activeData.__config__,\"defaultValue\",null),this.$set(this.activeData,\"value-format\",a),this.$set(this.activeData,\"format\",e)},spanChange:function(e){this.formConf.span=e},multipleChange:function(e){this.$set(this.activeData.__config__,\"defaultValue\",e?[]:\"\")},dateTypeChange:function(e){this.setTimeValue(d[e],e)},rangeChange:function(e){this.$set(this.activeData.__config__,\"defaultValue\",e?[this.activeData.min,this.activeData.max]:this.activeData.min)},rateTextChange:function(e){e&&(this.activeData[\"show-score\"]=!1)},rateScoreChange:function(e){e&&(this.activeData[\"show-text\"]=!1)},colorFormatChange:function(e){this.activeData.__config__.defaultValue=null,this.activeData[\"show-alpha\"]=e.indexOf(\"a\")>-1,this.activeData.__config__.renderKey=+new Date},openIconsDialog:function(e){this.iconsVisible=!0,this.currentIconModel=e},setIcon:function(e){this.activeData[this.currentIconModel]=e},tagChange:function(e){var t=s[\"b\"].find((function(t){return t.__config__.tagIcon===e}));t||(t=s[\"d\"].find((function(t){return t.__config__.tagIcon===e}))),this.$emit(\"tag-change\",t)},changeRenderKey:function(){p.includes(this.activeData.__config__.tag)&&(this.activeData.__config__.renderKey=+new Date)}}},m=f,v=(a(\"3f32\"),a(\"2877\")),b=Object(v[\"a\"])(m,o,n,!1,null,\"23f17c7d\",null);t[\"default\"]=b.exports},\"80de\":function(module,__webpack_exports__,__webpack_require__){\"use strict\";__webpack_require__.d(__webpack_exports__,\"a\",(function(){return makeUpJs}));var util__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__(\"3022\"),util__WEBPACK_IMPORTED_MODULE_0___default=__webpack_require__.n(util__WEBPACK_IMPORTED_MODULE_0__),_utils_index__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__(\"ed08\"),_config__WEBPACK_IMPORTED_MODULE_2__=__webpack_require__(\"2e2a\"),units={KB:\"1024\",MB:\"1024 / 1024\",GB:\"1024 / 1024 / 1024\"},confGlobal,inheritAttrs={file:\"\",dialog:\"inheritAttrs: false,\"};function makeUpJs(e,t){confGlobal=e=JSON.parse(JSON.stringify(e));var a=[],o=[],n=[],i=[],l=mixinMethod(t),c=[];e.fields.forEach((function(e){buildAttributes(e,a,o,n,l,i,c)}));var r=buildexport(e,t,a.join(\"\\n\"),o.join(\"\\n\"),n.join(\"\\n\"),c.join(\"\\n\"),i.join(\"\\n\"),l.join(\"\\n\"));return confGlobal=null,r}function buildAttributes(e,t,a,o,n,i,l){if(buildData(e,t),buildRules(e,a),e.options&&e.options.length&&(buildOptions(e,o),\"dynamic\"===e.dataType)){var c=\"\".concat(e.vModel,\"Options\"),r=Object(_utils_index__WEBPACK_IMPORTED_MODULE_1__[\"i\"])(c);buildOptionMethod(\"get\".concat(r),c,n)}e.props&&e.props.props&&buildProps(e,i),e.action&&\"el-upload\"===e.tag&&(l.push(\"\".concat(e.vModel,\"Action: '\").concat(e.action,\"',\\n      \").concat(e.vModel,\"fileList: [],\")),n.push(buildBeforeUpload(e)),e[\"auto-upload\"]||n.push(buildSubmitUpload(e))),e.children&&e.children.forEach((function(e){buildAttributes(e,t,a,o,n,i,l)}))}function mixinMethod(e){var t=[],a={file:confGlobal.formBtns?{submitForm:\"submitForm() {\\n        this.$refs['\".concat(confGlobal.formRef,\"'].validate(valid => {\\n          if(!valid) return\\n          // TODO 提交表单\\n        })\\n      },\"),resetForm:\"resetForm() {\\n        this.$refs['\".concat(confGlobal.formRef,\"'].resetFields()\\n      },\")}:null,dialog:{onOpen:\"onOpen() {},\",onClose:\"onClose() {\\n        this.$refs['\".concat(confGlobal.formRef,\"'].resetFields()\\n      },\"),close:\"close() {\\n        this.$emit('update:visible', false)\\n      },\",handelConfirm:\"handelConfirm() {\\n        this.$refs['\".concat(confGlobal.formRef,\"'].validate(valid => {\\n          if(!valid) return\\n          this.close()\\n        })\\n      },\")}},o=a[e];return o&&Object.keys(o).forEach((function(e){t.push(o[e])})),t}function buildData(e,t){var a;void 0!==e.vModel&&(a=\"string\"!==typeof e.defaultValue||e.multiple?\"\".concat(JSON.stringify(e.defaultValue)):\"'\".concat(e.defaultValue,\"'\"),t.push(\"\".concat(e.vModel,\": \").concat(a,\",\")))}function buildRules(conf,ruleList){if(void 0!==conf.vModel){var rules=[];if(_config__WEBPACK_IMPORTED_MODULE_2__[\"e\"][conf.tag]){if(conf.required){var type=Object(util__WEBPACK_IMPORTED_MODULE_0__[\"isArray\"])(conf.defaultValue)?\"type: 'array',\":\"\",message=Object(util__WEBPACK_IMPORTED_MODULE_0__[\"isArray\"])(conf.defaultValue)?\"请至少选择一个\".concat(conf.vModel):conf.placeholder;void 0===message&&(message=\"\".concat(conf.label,\"不能为空\")),rules.push(\"{ required: true, \".concat(type,\" message: '\").concat(message,\"', trigger: '\").concat(_config__WEBPACK_IMPORTED_MODULE_2__[\"e\"][conf.tag],\"' }\"))}conf.regList&&Object(util__WEBPACK_IMPORTED_MODULE_0__[\"isArray\"])(conf.regList)&&conf.regList.forEach((function(item){item.pattern&&rules.push(\"{ pattern: \".concat(eval(item.pattern),\", message: '\").concat(item.message,\"', trigger: '\").concat(_config__WEBPACK_IMPORTED_MODULE_2__[\"e\"][conf.tag],\"' }\"))})),ruleList.push(\"\".concat(conf.vModel,\": [\").concat(rules.join(\",\"),\"],\"))}}}function buildOptions(e,t){if(void 0!==e.vModel){\"dynamic\"===e.dataType&&(e.options=[]);var a=\"\".concat(e.vModel,\"Options: \").concat(JSON.stringify(e.options),\",\");t.push(a)}}function buildProps(e,t){\"dynamic\"===e.dataType&&(\"value\"!==e.valueKey&&(e.props.props.value=e.valueKey),\"label\"!==e.labelKey&&(e.props.props.label=e.labelKey),\"children\"!==e.childrenKey&&(e.props.props.children=e.childrenKey));var a=\"\".concat(e.vModel,\"Props: \").concat(JSON.stringify(e.props.props),\",\");t.push(a)}function buildBeforeUpload(e){var t=units[e.sizeUnit],a=\"\",o=\"\",n=[];e.fileSize&&(a=\"let isRightSize = file.size / \".concat(t,\" < \").concat(e.fileSize,\"\\n    if(!isRightSize){\\n      this.$message.error('文件大小超过 \").concat(e.fileSize).concat(e.sizeUnit,\"')\\n    }\"),n.push(\"isRightSize\")),e.accept&&(o=\"let isAccept = new RegExp('\".concat(e.accept,\"').test(file.type)\\n    if(!isAccept){\\n      this.$message.error('应该选择\").concat(e.accept,\"类型的文件')\\n    }\"),n.push(\"isAccept\"));var i=\"\".concat(e.vModel,\"BeforeUpload(file) {\\n    \").concat(a,\"\\n    \").concat(o,\"\\n    return \").concat(n.join(\"&&\"),\"\\n  },\");return n.length?i:\"\"}function buildSubmitUpload(e){var t=\"submitUpload() {\\n    this.$refs['\".concat(e.vModel,\"'].submit()\\n  },\");return t}function buildOptionMethod(e,t,a){var o=\"\".concat(e,\"() {\\n    // TODO 发起请求获取数据\\n    this.\").concat(t,\"\\n  },\");a.push(o)}function buildexport(e,t,a,o,n,i,l,c){var r=\"\".concat(_utils_index__WEBPACK_IMPORTED_MODULE_1__[\"e\"],\"{\\n  \").concat(inheritAttrs[t],\"\\n  components: {},\\n  props: [],\\n  data () {\\n    return {\\n      \").concat(e.formModel,\": {\\n        \").concat(a,\"\\n      },\\n      \").concat(e.formRules,\": {\\n        \").concat(o,\"\\n      },\\n      \").concat(i,\"\\n      \").concat(n,\"\\n      \").concat(l,\"\\n    }\\n  },\\n  computed: {},\\n  watch: {},\\n  created () {},\\n  mounted () {},\\n  methods: {\\n    \").concat(c,\"\\n  }\\n}\");return r}},8401:function(e,t,a){\"use strict\";a(\"6acb\")},8981:function(e,t,a){\"use strict\";a(\"a427\")},9977:function(e,t,a){var o={\"./el-button.js\":\"aace\",\"./el-checkbox-group.js\":\"9413\",\"./el-input.js\":\"167d\",\"./el-radio-group.js\":\"2cfa\",\"./el-select.js\":\"7f29\",\"./el-upload.js\":\"0f88\"};function n(e){var t=i(e);return a(t)}function i(e){if(!a.o(o,e)){var t=new Error(\"Cannot find module '\"+e+\"'\");throw t.code=\"MODULE_NOT_FOUND\",t}return o[e]}n.keys=function(){return Object.keys(o)},n.resolve=i,e.exports=n,n.id=\"9977\"},a2e1:function(e,t,a){},a427:function(e,t,a){},a92a:function(e,t,a){\"use strict\";a.r(t);var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a(\"div\",[a(\"el-dialog\",e._g(e._b({attrs:{width:\"500px\",\"close-on-click-modal\":!1,\"modal-append-to-body\":!1},on:{open:e.onOpen,close:e.onClose}},\"el-dialog\",e.$attrs,!1),e.$listeners),[a(\"el-row\",{attrs:{gutter:15}},[a(\"el-form\",{ref:\"elForm\",attrs:{model:e.formData,rules:e.rules,size:\"medium\",\"label-width\":\"100px\"}},[a(\"el-col\",{attrs:{span:24}},[a(\"el-form-item\",{attrs:{label:\"生成类型\",prop:\"type\"}},[a(\"el-radio-group\",{model:{value:e.formData.type,callback:function(t){e.$set(e.formData,\"type\",t)},expression:\"formData.type\"}},e._l(e.typeOptions,(function(t,o){return a(\"el-radio-button\",{key:o,attrs:{label:t.value,disabled:t.disabled}},[e._v(\" \"+e._s(t.label)+\" \")])})),1)],1),e.showFileName?a(\"el-form-item\",{attrs:{label:\"文件名\",prop:\"fileName\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入文件名\",clearable:\"\"},model:{value:e.formData.fileName,callback:function(t){e.$set(e.formData,\"fileName\",t)},expression:\"formData.fileName\"}})],1):e._e()],1)],1)],1),a(\"div\",{attrs:{slot:\"footer\"},slot:\"footer\"},[a(\"el-button\",{on:{click:e.close}},[e._v(\" 取消 \")]),a(\"el-button\",{attrs:{type:\"primary\"},on:{click:e.handelConfirm}},[e._v(\" 确定 \")])],1)],1)],1)},n=[];function i(e){return i=\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&\"function\"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?\"symbol\":typeof e},i(e)}function l(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,o)}return a}function c(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?l(Object(a),!0).forEach((function(t){r(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):l(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}function r(e,t,a){return(t=s(t))in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}function s(e){var t=u(e,\"string\");return\"symbol\"==i(t)?t:t+\"\"}function u(e,t){if(\"object\"!=i(e)||!e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var o=a.call(e,t||\"default\");if(\"object\"!=i(o))return o;throw new TypeError(\"@@toPrimitive must return a primitive value.\")}return(\"string\"===t?String:Number)(e)}var d={inheritAttrs:!1,props:[\"showFileName\"],data:function(){return{formData:{fileName:void 0,type:\"file\"},rules:{fileName:[{required:!0,message:\"请输入文件名\",trigger:\"blur\"}],type:[{required:!0,message:\"生成类型不能为空\",trigger:\"change\"}]},typeOptions:[{label:\"页面\",value:\"file\"},{label:\"弹窗\",value:\"dialog\"}]}},computed:{},watch:{},mounted:function(){},methods:{onOpen:function(){this.showFileName&&(this.formData.fileName=\"\".concat(+new Date,\".vue\"))},onClose:function(){},close:function(e){this.$emit(\"update:visible\",!1)},handelConfirm:function(){var e=this;this.$refs.elForm.validate((function(t){t&&(e.$emit(\"confirm\",c({},e.formData)),e.close())}))}}},p=d,f=a(\"2877\"),m=Object(f[\"a\"])(p,o,n,!1,null,\"e8322442\",null);t[\"default\"]=m.exports},a9fc:function(e,t,a){\"use strict\";a.r(t);var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a(\"div\",[a(\"el-dialog\",e._g(e._b({attrs:{title:\"外部资源引用\",width:\"600px\",\"close-on-click-modal\":!1},on:{open:e.onOpen,close:e.onClose}},\"el-dialog\",e.$attrs,!1),e.$listeners),[e._l(e.resources,(function(t,o){return a(\"el-input\",{key:o,staticClass:\"url-item\",attrs:{placeholder:\"请输入 css 或 js 资源路径\",\"prefix-icon\":\"el-icon-link\",clearable:\"\"},model:{value:e.resources[o],callback:function(t){e.$set(e.resources,o,t)},expression:\"resources[index]\"}},[a(\"el-button\",{attrs:{slot:\"append\",icon:\"el-icon-delete\"},on:{click:function(t){return e.deleteOne(o)}},slot:\"append\"})],1)})),a(\"el-button-group\",{staticClass:\"add-item\"},[a(\"el-button\",{attrs:{plain:\"\"},on:{click:function(t){return e.addOne(\"https://lib.baomitu.com/jquery/1.8.3/jquery.min.js\")}}},[e._v(\" jQuery1.8.3 \")]),a(\"el-button\",{attrs:{plain:\"\"},on:{click:function(t){return e.addOne(\"https://unpkg.com/http-vue-loader\")}}},[e._v(\" http-vue-loader \")]),a(\"el-button\",{attrs:{icon:\"el-icon-circle-plus-outline\",plain:\"\"},on:{click:function(t){return e.addOne(\"\")}}},[e._v(\" 添加其他 \")])],1),a(\"div\",{attrs:{slot:\"footer\"},slot:\"footer\"},[a(\"el-button\",{on:{click:e.close}},[e._v(\" 取消 \")]),a(\"el-button\",{attrs:{type:\"primary\"},on:{click:e.handelConfirm}},[e._v(\" 确定 \")])],1)],2)],1)},n=[],i=a(\"ed08\"),l={components:{},inheritAttrs:!1,props:[\"originResource\"],data:function(){return{resources:null}},computed:{},watch:{},created:function(){},mounted:function(){},methods:{onOpen:function(){this.resources=this.originResource.length?Object(i[\"d\"])(this.originResource):[\"\"]},onClose:function(){},close:function(){this.$emit(\"update:visible\",!1)},handelConfirm:function(){var e=this.resources.filter((function(e){return!!e}))||[];this.$emit(\"save\",e),this.close(),e.length&&(this.resources=e)},deleteOne:function(e){this.resources.splice(e,1)},addOne:function(e){this.resources.indexOf(e)>-1?this.$message(\"资源已存在\"):this.resources.push(e)}}},c=l,r=(a(\"e7bb\"),a(\"2877\")),s=Object(r[\"a\"])(c,o,n,!1,null,\"5ed905cd\",null);t[\"default\"]=s.exports},ad7f:function(e,t,a){\"use strict\";a.r(t);var o,n,i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a(\"div\",[a(\"el-drawer\",e._g(e._b({on:{opened:e.onOpen,close:e.onClose}},\"el-drawer\",e.$attrs,!1),e.$listeners),[a(\"div\",{staticClass:\"action-bar\",style:{\"text-align\":\"left\"}},[a(\"span\",{staticClass:\"bar-btn\",on:{click:e.refresh}},[a(\"i\",{staticClass:\"el-icon-refresh\"}),e._v(\" 刷新 \")]),a(\"span\",{ref:\"copyBtn\",staticClass:\"bar-btn copy-json-btn\"},[a(\"i\",{staticClass:\"el-icon-document-copy\"}),e._v(\" 复制JSON \")]),a(\"span\",{staticClass:\"bar-btn\",on:{click:e.exportJsonFile}},[a(\"i\",{staticClass:\"el-icon-download\"}),e._v(\" 导出JSON文件 \")]),a(\"span\",{staticClass:\"bar-btn delete-btn\",on:{click:function(t){return e.$emit(\"update:visible\",!1)}}},[a(\"i\",{staticClass:\"el-icon-circle-close\"}),e._v(\" 关闭 \")])]),a(\"div\",{staticClass:\"json-editor\",attrs:{id:\"editorJson\"}})])],1)},l=[],c=a(\"ed08\"),r=a(\"b311\"),s=a.n(r),u=a(\"21a6\"),d=a(\"4c3b\"),p=a(\"b3ae\"),f={components:{},props:{jsonStr:{type:String,required:!0}},data:function(){return{}},computed:{},watch:{},created:function(){},mounted:function(){var e=this;window.addEventListener(\"keydown\",this.preventDefaultSave);var t=new s.a(\".copy-json-btn\",{text:function(t){return e.$notify({title:\"成功\",message:\"代码已复制到剪切板，可粘贴。\",type:\"success\"}),e.beautifierJson}});t.on(\"error\",(function(t){e.$message.error(\"代码复制失败\")}))},beforeDestroy:function(){window.removeEventListener(\"keydown\",this.preventDefaultSave)},methods:{preventDefaultSave:function(e){\"s\"===e.key&&(e.metaKey||e.ctrlKey)&&e.preventDefault()},onOpen:function(){var e=this;Object(p[\"a\"])((function(t){o=t,e.beautifierJson=o.js(e.jsonStr,c[\"b\"].js),Object(d[\"a\"])((function(t){n=t,e.setEditorValue(\"editorJson\",e.beautifierJson)}))}))},onClose:function(){},setEditorValue:function(e,t){var a=this;this.jsonEditor?this.jsonEditor.setValue(t):(this.jsonEditor=n.editor.create(document.getElementById(e),{value:t,theme:\"vs-dark\",language:\"json\",automaticLayout:!0}),this.jsonEditor.onKeyDown((function(e){49===e.keyCode&&(e.metaKey||e.ctrlKey)&&a.refresh()})))},exportJsonFile:function(){var e=this;this.$prompt(\"文件名:\",\"导出文件\",{inputValue:\"\".concat(+new Date,\".json\"),closeOnClickModal:!1,inputPlaceholder:\"请输入文件名\"}).then((function(t){var a=t.value;a||(a=\"\".concat(+new Date,\".json\"));var o=e.jsonEditor.getValue(),n=new Blob([o],{type:\"text/plain;charset=utf-8\"});Object(u[\"saveAs\"])(n,a)}))},refresh:function(){try{this.$emit(\"refresh\",JSON.parse(this.jsonEditor.getValue()))}catch(e){this.$notify({title:\"错误\",message:\"JSON格式错误，请检查\",type:\"error\"})}}}},m=f,v=(a(\"8401\"),a(\"2877\")),b=Object(v[\"a\"])(m,i,l,!1,null,\"b6fde942\",null);t[\"default\"]=b.exports},b3ae:function(e,t,a){\"use strict\";a.d(t,\"a\",(function(){return r}));var o,n=a(\"c88b\"),i=a(\"5c96\"),l=a.n(i),c=a(\"4771\");function r(e){var t=c[\"a\"].beautifierUrl;if(o)e(o);else{var a=l.a.Loading.service({fullscreen:!0,lock:!0,text:\"格式化资源加载中...\",spinner:\"el-icon-loading\",background:\"rgba(255, 255, 255, 0.5)\"});Object(n[\"a\"])(t,(function(){a.close(),o=beautifier,e(o)}))}}},beb6:function(e,t,a){\"use strict\";a(\"c517\")},c517:function(e,t,a){},c81a:function(e,t,a){\"use strict\";a.r(t);var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a(\"div\",[a(\"el-dialog\",e._g(e._b({attrs:{\"close-on-click-modal\":!1,\"modal-append-to-body\":!1},on:{open:e.onOpen,close:e.onClose}},\"el-dialog\",e.$attrs,!1),e.$listeners),[a(\"el-row\",{attrs:{gutter:0}},[a(\"el-form\",{ref:\"elForm\",attrs:{model:e.formData,rules:e.rules,size:\"small\",\"label-width\":\"100px\"}},[a(\"el-col\",{attrs:{span:24}},[a(\"el-form-item\",{attrs:{label:\"选项名\",prop:\"label\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入选项名\",clearable:\"\"},model:{value:e.formData.label,callback:function(t){e.$set(e.formData,\"label\",t)},expression:\"formData.label\"}})],1)],1),a(\"el-col\",{attrs:{span:24}},[a(\"el-form-item\",{attrs:{label:\"选项值\",prop:\"value\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入选项值\",clearable:\"\"},model:{value:e.formData.value,callback:function(t){e.$set(e.formData,\"value\",t)},expression:\"formData.value\"}},[a(\"el-select\",{style:{width:\"100px\"},attrs:{slot:\"append\"},slot:\"append\",model:{value:e.dataType,callback:function(t){e.dataType=t},expression:\"dataType\"}},e._l(e.dataTypeOptions,(function(e,t){return a(\"el-option\",{key:t,attrs:{label:e.label,value:e.value,disabled:e.disabled}})})),1)],1)],1)],1)],1)],1),a(\"div\",{attrs:{slot:\"footer\"},slot:\"footer\"},[a(\"el-button\",{attrs:{type:\"primary\"},on:{click:e.handelConfirm}},[e._v(\" 确定 \")]),a(\"el-button\",{on:{click:e.close}},[e._v(\" 取消 \")])],1)],1)],1)},n=[],i=a(\"ed08\"),l=a(\"e31c\"),c=Object(l[\"d\"])(),r={components:{},inheritAttrs:!1,props:[],data:function(){return{id:c,formData:{label:void 0,value:void 0},rules:{label:[{required:!0,message:\"请输入选项名\",trigger:\"blur\"}],value:[{required:!0,message:\"请输入选项值\",trigger:\"blur\"}]},dataType:\"string\",dataTypeOptions:[{label:\"字符串\",value:\"string\"},{label:\"数字\",value:\"number\"}]}},computed:{},watch:{\"formData.value\":function(e){this.dataType=Object(i[\"f\"])(e)?\"number\":\"string\"},id:function(e){Object(l[\"h\"])(e)}},created:function(){},mounted:function(){},methods:{onOpen:function(){this.formData={label:void 0,value:void 0}},onClose:function(){},close:function(){this.$emit(\"update:visible\",!1)},handelConfirm:function(){var e=this;this.$refs.elForm.validate((function(t){t&&(\"number\"===e.dataType&&(e.formData.value=parseFloat(e.formData.value)),e.formData.id=e.id++,e.$emit(\"commit\",e.formData),e.close())}))}}},s=r,u=a(\"2877\"),d=Object(u[\"a\"])(s,o,n,!1,null,\"ee7a470e\",null);t[\"default\"]=d.exports},c88b:function(e,t,a){\"use strict\";a.d(t,\"b\",(function(){return i}));var o={};function n(e,t){var a=document.getElementById(e),n=t||function(){};if(!a){o[e]=[];var i=document.createElement(\"script\");i.src=e,i.id=e,i.async=1,document.body.appendChild(i);var l=\"onload\"in i?c.bind(i):r.bind(i);l(i)}function c(t){var a=this;t.onload=function(){a.onerror=a.onload=null,o[e].forEach((function(e){e(null,t)})),delete o[e]},t.onerror=function(){a.onerror=a.onload=null,n(new Error(\"Failed to load \".concat(e)),t)}}function r(t){var a=this;t.onreadystatechange=function(){\"complete\"!==a.readyState&&\"loaded\"!==a.readyState||(a.onreadystatechange=null,o[e].forEach((function(e){e(null,t)})),delete o[e])}}o[e].push(n)}function i(e,t){var a=e.shift();e.length?n(a,(function(){return i(e,t)})):n(a,t)}t[\"a\"]=n},cc7a:function(e,t,a){\"use strict\";a.d(t,\"a\",(function(){return i}));var o={\"el-rate\":\".el-rate{display: inline-block; vertical-align: text-top;}\",\"el-upload\":\".el-upload__tip{line-height: 1.2;}\"};function n(e,t){var a=o[t.tag];a&&-1===e.indexOf(a)&&e.push(a),t.children&&t.children.forEach((function(t){return n(e,t)}))}function i(e){var t=[];return e.fields.forEach((function(e){return n(t,e)})),t.join(\"\\n\")}},d0b2:function(e,t,a){\"use strict\";a.r(t);var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a(\"div\",{staticClass:\"icon-dialog\"},[a(\"el-dialog\",e._g(e._b({attrs:{width:\"980px\",\"modal-append-to-body\":!1},on:{open:e.onOpen,close:e.onClose}},\"el-dialog\",e.$attrs,!1),e.$listeners),[a(\"div\",{attrs:{slot:\"title\"},slot:\"title\"},[e._v(\" 选择图标 \"),a(\"el-input\",{style:{width:\"260px\"},attrs:{size:\"mini\",placeholder:\"请输入图标名称\",\"prefix-icon\":\"el-icon-search\",clearable:\"\"},model:{value:e.key,callback:function(t){e.key=t},expression:\"key\"}})],1),a(\"ul\",{staticClass:\"icon-ul\"},e._l(e.iconList,(function(t){return a(\"li\",{key:t,class:e.active===t?\"active-item\":\"\",on:{click:function(a){return e.onSelect(t)}}},[a(\"i\",{class:t}),a(\"div\",[e._v(e._s(t))])])})),0)])],1)},n=[],i=a(\"de0a\"),l=i.map((function(e){return\"el-icon-\".concat(e)})),c={inheritAttrs:!1,props:[\"current\"],data:function(){return{iconList:l,active:null,key:\"\"}},watch:{key:function(e){this.iconList=e?l.filter((function(t){return t.indexOf(e)>-1})):l}},methods:{onOpen:function(){this.active=this.current,this.key=\"\"},onClose:function(){},onSelect:function(e){this.active=e,this.$emit(\"select\",e),this.$emit(\"update:visible\",!1)}}},r=c,s=(a(\"de89\"),a(\"2877\")),u=Object(s[\"a\"])(r,o,n,!1,null,\"2fa68d6e\",null);t[\"default\"]=u.exports},de0a:function(e){e.exports=JSON.parse('[\"platform-eleme\",\"eleme\",\"delete-solid\",\"delete\",\"s-tools\",\"setting\",\"user-solid\",\"user\",\"phone\",\"phone-outline\",\"more\",\"more-outline\",\"star-on\",\"star-off\",\"s-goods\",\"goods\",\"warning\",\"warning-outline\",\"question\",\"info\",\"remove\",\"circle-plus\",\"success\",\"error\",\"zoom-in\",\"zoom-out\",\"remove-outline\",\"circle-plus-outline\",\"circle-check\",\"circle-close\",\"s-help\",\"help\",\"minus\",\"plus\",\"check\",\"close\",\"picture\",\"picture-outline\",\"picture-outline-round\",\"upload\",\"upload2\",\"download\",\"camera-solid\",\"camera\",\"video-camera-solid\",\"video-camera\",\"message-solid\",\"bell\",\"s-cooperation\",\"s-order\",\"s-platform\",\"s-fold\",\"s-unfold\",\"s-operation\",\"s-promotion\",\"s-home\",\"s-release\",\"s-ticket\",\"s-management\",\"s-open\",\"s-shop\",\"s-marketing\",\"s-flag\",\"s-comment\",\"s-finance\",\"s-claim\",\"s-custom\",\"s-opportunity\",\"s-data\",\"s-check\",\"s-grid\",\"menu\",\"share\",\"d-caret\",\"caret-left\",\"caret-right\",\"caret-bottom\",\"caret-top\",\"bottom-left\",\"bottom-right\",\"back\",\"right\",\"bottom\",\"top\",\"top-left\",\"top-right\",\"arrow-left\",\"arrow-right\",\"arrow-down\",\"arrow-up\",\"d-arrow-left\",\"d-arrow-right\",\"video-pause\",\"video-play\",\"refresh\",\"refresh-right\",\"refresh-left\",\"finished\",\"sort\",\"sort-up\",\"sort-down\",\"rank\",\"loading\",\"view\",\"c-scale-to-original\",\"date\",\"edit\",\"edit-outline\",\"folder\",\"folder-opened\",\"folder-add\",\"folder-remove\",\"folder-delete\",\"folder-checked\",\"tickets\",\"document-remove\",\"document-delete\",\"document-copy\",\"document-checked\",\"document\",\"document-add\",\"printer\",\"paperclip\",\"takeaway-box\",\"search\",\"monitor\",\"attract\",\"mobile\",\"scissors\",\"umbrella\",\"headset\",\"brush\",\"mouse\",\"coordinate\",\"magic-stick\",\"reading\",\"data-line\",\"data-board\",\"pie-chart\",\"data-analysis\",\"collection-tag\",\"film\",\"suitcase\",\"suitcase-1\",\"receiving\",\"collection\",\"files\",\"notebook-1\",\"notebook-2\",\"toilet-paper\",\"office-building\",\"school\",\"table-lamp\",\"house\",\"no-smoking\",\"smoking\",\"shopping-cart-full\",\"shopping-cart-1\",\"shopping-cart-2\",\"shopping-bag-1\",\"shopping-bag-2\",\"sold-out\",\"sell\",\"present\",\"box\",\"bank-card\",\"money\",\"coin\",\"wallet\",\"discount\",\"price-tag\",\"news\",\"guide\",\"male\",\"female\",\"thumb\",\"cpu\",\"link\",\"connection\",\"open\",\"turn-off\",\"set-up\",\"chat-round\",\"chat-line-round\",\"chat-square\",\"chat-dot-round\",\"chat-dot-square\",\"chat-line-square\",\"message\",\"postcard\",\"position\",\"turn-off-microphone\",\"microphone\",\"close-notification\",\"bangzhu\",\"time\",\"odometer\",\"crop\",\"aim\",\"switch-button\",\"full-screen\",\"copy-document\",\"mic\",\"stopwatch\",\"medal-1\",\"medal\",\"trophy\",\"trophy-1\",\"first-aid-kit\",\"discover\",\"place\",\"location\",\"location-outline\",\"location-information\",\"add-location\",\"delete-location\",\"map-location\",\"alarm-clock\",\"timer\",\"watch-1\",\"watch\",\"lock\",\"unlock\",\"key\",\"service\",\"mobile-phone\",\"bicycle\",\"truck\",\"ship\",\"basketball\",\"football\",\"soccer\",\"baseball\",\"wind-power\",\"light-rain\",\"lightning\",\"heavy-rain\",\"sunrise\",\"sunrise-1\",\"sunset\",\"sunny\",\"cloudy\",\"partly-cloudy\",\"cloudy-and-sunny\",\"moon\",\"moon-night\",\"dish\",\"dish-1\",\"food\",\"chicken\",\"fork-spoon\",\"knife-fork\",\"burger\",\"tableware\",\"sugar\",\"dessert\",\"ice-cream\",\"hot-water\",\"water-cup\",\"coffee-cup\",\"cold-drink\",\"goblet\",\"goblet-full\",\"goblet-square\",\"goblet-square-full\",\"refrigerator\",\"grape\",\"watermelon\",\"cherry\",\"apple\",\"pear\",\"orange\",\"coffee\",\"ice-tea\",\"ice-drink\",\"milk-tea\",\"potato-strips\",\"lollipop\",\"ice-cream-square\",\"ice-cream-round\"]')},de89:function(e,t,a){\"use strict\";a(\"a2e1\")},e31c:function(e,t,a){\"use strict\";a.d(t,\"a\",(function(){return s})),a.d(t,\"e\",(function(){return u})),a.d(t,\"c\",(function(){return d})),a.d(t,\"g\",(function(){return p})),a.d(t,\"d\",(function(){return f})),a.d(t,\"h\",(function(){return m})),a.d(t,\"b\",(function(){return v})),a.d(t,\"f\",(function(){return b}));a(\"dcec\");var o=\"drawingItems\",n=\"1.2\",i=\"DRAWING_ITEMS_VERSION\",l=\"idGlobal\",c=\"treeNodeId\",r=\"formConf\";function s(){var e=localStorage.getItem(i);if(e!==n)return localStorage.setItem(i,n),u([]),null;var t=localStorage.getItem(o);return t?JSON.parse(t):null}function u(e){localStorage.setItem(o,JSON.stringify(e))}function d(){var e=localStorage.getItem(l);return e?parseInt(e,10):100}function p(e){localStorage.setItem(l,\"\".concat(e))}function f(){var e=localStorage.getItem(c);return e?parseInt(e,10):100}function m(e){localStorage.setItem(c,\"\".concat(e))}function v(){var e=localStorage.getItem(r);return e?JSON.parse(e):null}function b(e){localStorage.setItem(r,JSON.stringify(e))}},e7bb:function(e,t,a){\"use strict\";a(\"0f5c\")},f7ac:function(e,t,a){\"use strict\";a.r(t);var o,n,i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a(\"div\",[a(\"el-drawer\",e._g(e._b({on:{opened:e.onOpen,close:e.onClose}},\"el-drawer\",e.$attrs,!1),e.$listeners),[a(\"div\",{staticStyle:{height:\"100%\"}},[a(\"el-row\",{staticStyle:{height:\"100%\",overflow:\"auto\"}},[a(\"el-col\",{staticClass:\"left-editor\",attrs:{md:24,lg:12}},[a(\"div\",{staticClass:\"setting\",attrs:{title:\"资源引用\"},on:{click:e.showResource}},[a(\"el-badge\",{staticClass:\"item\",attrs:{\"is-dot\":!!e.resources.length}},[a(\"i\",{staticClass:\"el-icon-setting\"})])],1),a(\"el-tabs\",{staticClass:\"editor-tabs\",attrs:{type:\"card\"},model:{value:e.activeTab,callback:function(t){e.activeTab=t},expression:\"activeTab\"}},[a(\"el-tab-pane\",{attrs:{name:\"html\"}},[a(\"span\",{attrs:{slot:\"label\"},slot:\"label\"},[\"html\"===e.activeTab?a(\"i\",{staticClass:\"el-icon-edit\"}):a(\"i\",{staticClass:\"el-icon-document\"}),e._v(\" template \")])]),a(\"el-tab-pane\",{attrs:{name:\"js\"}},[a(\"span\",{attrs:{slot:\"label\"},slot:\"label\"},[\"js\"===e.activeTab?a(\"i\",{staticClass:\"el-icon-edit\"}):a(\"i\",{staticClass:\"el-icon-document\"}),e._v(\" script \")])]),a(\"el-tab-pane\",{attrs:{name:\"css\"}},[a(\"span\",{attrs:{slot:\"label\"},slot:\"label\"},[\"css\"===e.activeTab?a(\"i\",{staticClass:\"el-icon-edit\"}):a(\"i\",{staticClass:\"el-icon-document\"}),e._v(\" css \")])])],1),a(\"div\",{directives:[{name:\"show\",rawName:\"v-show\",value:\"html\"===e.activeTab,expression:\"activeTab==='html'\"}],staticClass:\"tab-editor\",attrs:{id:\"editorHtml\"}}),a(\"div\",{directives:[{name:\"show\",rawName:\"v-show\",value:\"js\"===e.activeTab,expression:\"activeTab==='js'\"}],staticClass:\"tab-editor\",attrs:{id:\"editorJs\"}}),a(\"div\",{directives:[{name:\"show\",rawName:\"v-show\",value:\"css\"===e.activeTab,expression:\"activeTab==='css'\"}],staticClass:\"tab-editor\",attrs:{id:\"editorCss\"}})],1),a(\"el-col\",{staticClass:\"right-preview\",attrs:{md:24,lg:12}},[a(\"div\",{staticClass:\"action-bar\",style:{\"text-align\":\"left\"}},[a(\"span\",{staticClass:\"bar-btn\",on:{click:e.runCode}},[a(\"i\",{staticClass:\"el-icon-refresh\"}),e._v(\" 刷新 \")]),a(\"span\",{staticClass:\"bar-btn\",on:{click:e.exportFile}},[a(\"i\",{staticClass:\"el-icon-download\"}),e._v(\" 导出vue文件 \")]),a(\"span\",{ref:\"copyBtn\",staticClass:\"bar-btn copy-btn\"},[a(\"i\",{staticClass:\"el-icon-document-copy\"}),e._v(\" 复制代码 \")]),a(\"span\",{staticClass:\"bar-btn delete-btn\",on:{click:function(t){return e.$emit(\"update:visible\",!1)}}},[a(\"i\",{staticClass:\"el-icon-circle-close\"}),e._v(\" 关闭 \")])]),a(\"iframe\",{directives:[{name:\"show\",rawName:\"v-show\",value:e.isIframeLoaded,expression:\"isIframeLoaded\"}],ref:\"previewPage\",staticClass:\"result-wrapper\",attrs:{frameborder:\"0\",src:\"preview.html\"},on:{load:e.iframeLoad}}),a(\"div\",{directives:[{name:\"show\",rawName:\"v-show\",value:!e.isIframeLoaded,expression:\"!isIframeLoaded\"},{name:\"loading\",rawName:\"v-loading\",value:!0,expression:\"true\"}],staticClass:\"result-wrapper\"})])],1)],1)]),a(\"resource-dialog\",{attrs:{visible:e.resourceVisible,\"origin-resource\":e.resources},on:{\"update:visible\":function(t){e.resourceVisible=t},save:e.setResource}})],1)},l=[],c=a(\"1861\"),r=a(\"b311\"),s=a.n(r),u=a(\"21a6\"),d=a(\"0595\"),p=a(\"80de\"),f=a(\"cc7a\"),m=a(\"ed08\"),v=a(\"a9fc\"),b=a(\"4c3b\"),h=a(\"b3ae\"),_={html:null,js:null,css:null},g={html:\"html\",js:\"javascript\",css:\"css\"},y={components:{ResourceDialog:v[\"default\"]},props:[\"formData\",\"generateConf\"],data:function(){return{activeTab:\"html\",htmlCode:\"\",jsCode:\"\",cssCode:\"\",codeFrame:\"\",isIframeLoaded:!1,isInitcode:!1,isRefreshCode:!1,resourceVisible:!1,scripts:[],links:[],monaco:null}},computed:{resources:function(){return this.scripts.concat(this.links)}},watch:{},created:function(){},mounted:function(){var e=this;window.addEventListener(\"keydown\",this.preventDefaultSave);var t=new s.a(\".copy-btn\",{text:function(t){var a=e.generateCode();return e.$notify({title:\"成功\",message:\"代码已复制到剪切板，可粘贴。\",type:\"success\"}),a}});t.on(\"error\",(function(t){e.$message.error(\"代码复制失败\")}))},beforeDestroy:function(){window.removeEventListener(\"keydown\",this.preventDefaultSave)},methods:{preventDefaultSave:function(e){\"s\"===e.key&&(e.metaKey||e.ctrlKey)&&e.preventDefault()},onOpen:function(){var e=this,t=this.generateConf.type;this.htmlCode=Object(d[\"b\"])(this.formData,t),this.jsCode=Object(p[\"a\"])(this.formData,t),this.cssCode=Object(f[\"a\"])(this.formData),Object(h[\"a\"])((function(t){o=t,e.htmlCode=o.html(e.htmlCode,m[\"b\"].html),e.jsCode=o.js(e.jsCode,m[\"b\"].js),e.cssCode=o.css(e.cssCode,m[\"b\"].html),Object(b[\"a\"])((function(t){n=t,e.setEditorValue(\"editorHtml\",\"html\",e.htmlCode),e.setEditorValue(\"editorJs\",\"js\",e.jsCode),e.setEditorValue(\"editorCss\",\"css\",e.cssCode),e.isInitcode||(e.isRefreshCode=!0,e.isIframeLoaded&&(e.isInitcode=!0)&&e.runCode())}))}))},onClose:function(){this.isInitcode=!1,this.isRefreshCode=!1},iframeLoad:function(){this.isInitcode||(this.isIframeLoaded=!0,this.isRefreshCode&&(this.isInitcode=!0)&&this.runCode())},setEditorValue:function(e,t,a){var o=this;_[t]?_[t].setValue(a):_[t]=n.editor.create(document.getElementById(e),{value:a,theme:\"vs-dark\",language:g[t],automaticLayout:!0}),_[t].onKeyDown((function(e){49===e.keyCode&&(e.metaKey||e.ctrlKey)&&o.runCode()}))},runCode:function(){var e=_.js.getValue();try{var t=Object(c[\"parse\"])(e,{sourceType:\"module\"}),a=t.program.body;if(a.length>1)return void this.$confirm(\"js格式不能识别，仅支持修改export default的对象内容\",\"提示\",{type:\"warning\"});if(\"ExportDefaultDeclaration\"===a[0].type){var o={type:\"refreshFrame\",data:{generateConf:this.generateConf,html:_.html.getValue(),js:e.replace(m[\"e\"],\"\"),css:_.css.getValue(),scripts:this.scripts,links:this.links}};this.$refs.previewPage.contentWindow.postMessage(o,location.origin)}else this.$message.error(\"请使用export default\")}catch(n){this.$message.error(\"js错误：\".concat(n)),console.error(n)}},generateCode:function(){var e=Object(d[\"d\"])(_.html.getValue()),t=Object(d[\"c\"])(_.js.getValue()),a=Object(d[\"a\"])(_.css.getValue());return o.html(e+t+a,m[\"b\"].html)},exportFile:function(){var e=this;this.$prompt(\"文件名:\",\"导出文件\",{inputValue:\"\".concat(+new Date,\".vue\"),closeOnClickModal:!1,inputPlaceholder:\"请输入文件名\"}).then((function(t){var a=t.value;a||(a=\"\".concat(+new Date,\".vue\"));var o=e.generateCode(),n=new Blob([o],{type:\"text/plain;charset=utf-8\"});Object(u[\"saveAs\"])(n,a)}))},showResource:function(){this.resourceVisible=!0},setResource:function(e){var t=[],a=[];Array.isArray(e)?(e.forEach((function(e){e.endsWith(\".css\")?a.push(e):t.push(e)})),this.scripts=t,this.links=a):(this.scripts=[],this.links=[])}}},D=y,w=(a(\"8981\"),a(\"2877\")),x=Object(w[\"a\"])(D,i,l,!1,null,\"b5faf870\",null);t[\"default\"]=x.exports}}]);", "extractedComments": []}