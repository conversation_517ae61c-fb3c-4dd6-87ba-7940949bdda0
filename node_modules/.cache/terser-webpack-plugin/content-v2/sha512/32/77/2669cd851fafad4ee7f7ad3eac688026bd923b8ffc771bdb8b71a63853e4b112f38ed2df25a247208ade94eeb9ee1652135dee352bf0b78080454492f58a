{"code": "(window[\"webpackJsonp\"]=window[\"webpackJsonp\"]||[]).push([[\"chunk-2252adb6\"],{1538:function(t,e,n){\"use strict\";n.d(e,\"h\",(function(){return a})),n.d(e,\"b\",(function(){return o})),n.d(e,\"c\",(function(){return s})),n.d(e,\"g\",(function(){return i})),n.d(e,\"n\",(function(){return l})),n.d(e,\"o\",(function(){return c})),n.d(e,\"k\",(function(){return u})),n.d(e,\"i\",(function(){return d})),n.d(e,\"j\",(function(){return f})),n.d(e,\"f\",(function(){return m})),n.d(e,\"l\",(function(){return p})),n.d(e,\"a\",(function(){return h})),n.d(e,\"m\",(function(){return b})),n.d(e,\"d\",(function(){return k})),n.d(e,\"e\",(function(){return g}));var r=n(\"b775\");function a(t){return Object(r[\"a\"])({url:\"/flowable/definition/list\",method:\"get\",params:t})}function o(t,e){return Object(r[\"a\"])({url:\"/flowable/definition/start/\"+t,method:\"post\",data:e})}function s(t){return Object(r[\"a\"])({url:\"/flowable/definition/startByKey\",method:\"post\",data:t})}function i(t){return Object(r[\"a\"])({url:\"/flowable/task/processVariables/\"+t,method:\"get\"})}function l(t){return Object(r[\"a\"])({url:\"/flowable/definition/updateState\",method:\"put\",params:t})}function c(t){return Object(r[\"a\"])({url:\"/flowable/definition/userList\",method:\"get\",params:t})}function u(t){return Object(r[\"a\"])({url:\"/flowable/definition/roleList\",method:\"get\",params:t})}function d(t){return Object(r[\"a\"])({url:\"/flowable/definition/readXml/\"+t,method:\"get\"})}function f(t){return Object(r[\"a\"])({url:\"/flowable/definition/readXmlByKey/\"+t,method:\"get\"})}function m(t){return Object(r[\"a\"])({url:\"/flowable/task/flowViewer/\"+t,method:\"get\"})}function p(t){return Object(r[\"a\"])({url:\"/flowable/definition/save\",method:\"post\",data:t})}function h(t){return Object(r[\"a\"])({url:\"/system/deployment\",method:\"post\",data:t})}function b(t){return Object(r[\"a\"])({url:\"/system/deployment\",method:\"put\",data:t})}function k(t){return Object(r[\"a\"])({url:\"/flowable/definition/delete/\",method:\"delete\",params:t})}function g(t){return Object(r[\"a\"])({url:\"/system/deployment/export\",method:\"get\",params:t})}},2638:function(t,e,n){\"use strict\";function r(){return r=Object.assign?Object.assign.bind():function(t){for(var e,n=1;n<arguments.length;n++)for(var r in e=arguments[n],e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t},r.apply(this,arguments)}var a=[\"attrs\",\"props\",\"domProps\"],o=[\"class\",\"style\",\"directives\"],s=[\"on\",\"nativeOn\"],i=function(t){return t.reduce((function(t,e){for(var n in e)if(t[n])if(-1!==a.indexOf(n))t[n]=r({},t[n],e[n]);else if(-1!==o.indexOf(n)){var i=t[n]instanceof Array?t[n]:[t[n]],c=e[n]instanceof Array?e[n]:[e[n]];t[n]=[].concat(i,c)}else if(-1!==s.indexOf(n))for(var u in e[n])if(t[n][u]){var d=t[n][u]instanceof Array?t[n][u]:[t[n][u]],f=e[n][u]instanceof Array?e[n][u]:[e[n][u]];t[n][u]=[].concat(d,f)}else t[n][u]=e[n][u];else if(\"hook\"===n)for(var m in e[n])t[n][m]=t[n][m]?l(t[n][m],e[n][m]):e[n][m];else t[n]=e[n];else t[n]=e[n];return t}),{})},l=function(t,e){return function(){t&&t.apply(this,arguments),e&&e.apply(this,arguments)}};t.exports=i},\"589b\":function(t,e,n){\"use strict\";n.r(e);var r=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n(\"div\",[n(\"bpmn-modeler\",{ref:\"refNode\",attrs:{xml:t.xmlData,\"is-view\":!0,taskList:t.taskData}})],1)},a=[],o=n(\"ae3a\"),s={name:\"Flow\",components:{bpmnModeler:o[\"a\"]},props:{xmlData:{type:String,default:\"\"},taskData:{type:Array,default:function(){return[]}}},data:function(){return{}}},i=s,l=n(\"2877\"),c=Object(l[\"a\"])(i,r,a,!1,null,null,null);e[\"default\"]=c.exports},6905:function(t,e,n){\"use strict\";n(\"9150\")},\"6e55\":function(t,e,n){\"use strict\";n.d(e,\"d\",(function(){return a})),n.d(e,\"e\",(function(){return o})),n.d(e,\"h\",(function(){return s})),n.d(e,\"f\",(function(){return i})),n.d(e,\"a\",(function(){return l})),n.d(e,\"i\",(function(){return c})),n.d(e,\"b\",(function(){return u})),n.d(e,\"c\",(function(){return d})),n.d(e,\"g\",(function(){return f}));var r=n(\"b775\");n(\"3fd3\");function a(t){return Object(r[\"a\"])({url:\"/flowable/task/finishedList\",method:\"get\",params:t})}function o(t){return Object(r[\"a\"])({url:\"/flowable/task/flowRecord\",method:\"get\",params:t})}function s(t){return Object(r[\"a\"])({url:\"/flowable/task/revokeProcess\",method:\"post\",data:t})}function i(t){return Object(r[\"a\"])({url:\"/system/deployment/\"+t,method:\"get\"})}function l(t){return Object(r[\"a\"])({url:\"/system/deployment\",method:\"post\",data:t})}function c(t){return Object(r[\"a\"])({url:\"/system/deployment\",method:\"put\",data:t})}function u(t){return Object(r[\"a\"])({url:\"/system/deployment/\"+t,method:\"delete\"})}function d(t){return Object(r[\"a\"])({url:\"/system/deployment/export\",method:\"get\",params:t})}function f(t){return Object(r[\"a\"])({url:\"/flowable/instance/getHisIns\",method:\"get\",params:t})}},9150:function(t,e,n){},9718:function(t,e,n){\"use strict\";n.r(e);var r=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n(\"div\",{},[t.flowRecordList?n(\"el-card\",{staticClass:\"box-card\"},[n(\"div\",{staticClass:\"clearfix\",attrs:{slot:\"header\"},slot:\"header\"},[n(\"span\",{staticClass:\"el-icon-notebook-1\"},[t._v(\"审批记录\")])]),n(\"el-col\",{attrs:{span:16,offset:4}},[n(\"div\",{staticClass:\"block\"},[n(\"el-timeline\",t._l(t.flowRecordList,(function(e,r){return n(\"el-timeline-item\",{key:r,attrs:{icon:t.setIcon(e.finishTime),color:t.setColor(e.finishTime)}},[n(\"p\",{staticStyle:{\"font-weight\":\"700\"}},[t._v(t._s(e.taskName))]),n(\"el-card\",{attrs:{\"body-style\":{padding:\"10px\"}}},[e.assigneeName?n(\"label\",{staticStyle:{\"font-weight\":\"normal\",\"margin-right\":\"30px\"}},[t._v(\"实际办理： \"+t._s(e.assigneeName)+\" \"),n(\"el-tag\",{attrs:{type:\"info\",size:\"mini\"}},[t._v(t._s(e.deptName))])],1):t._e(),e.candidate?n(\"label\",{staticStyle:{\"font-weight\":\"normal\",\"margin-right\":\"30px\"}},[t._v(\"候选办理： \"+t._s(e.candidate))]):t._e(),n(\"label\",{staticStyle:{\"font-weight\":\"normal\"}},[t._v(\"接收时间： \")]),n(\"label\",{staticStyle:{color:\"#8a909c\",\"font-weight\":\"normal\"}},[t._v(t._s(e.createTime))]),e.finishTime?n(\"label\",{staticStyle:{\"margin-left\":\"30px\",\"font-weight\":\"normal\"}},[t._v(\"办结时间： \")]):t._e(),n(\"label\",{staticStyle:{color:\"#8a909c\",\"font-weight\":\"normal\"}},[t._v(t._s(e.finishTime))]),e.duration?n(\"label\",{staticStyle:{\"margin-left\":\"30px\",\"font-weight\":\"normal\"}},[t._v(\"耗时： \")]):t._e(),n(\"label\",{staticStyle:{color:\"#8a909c\",\"font-weight\":\"normal\"}},[t._v(t._s(e.duration))]),e.comment?n(\"p\",[\"1\"===e.comment.type?n(\"el-tag\",{attrs:{type:\"success\"}},[t._v(\" \"+t._s(e.comment.comment))]):t._e(),\"2\"===e.comment.type?n(\"el-tag\",{attrs:{type:\"warning\"}},[t._v(\" \"+t._s(e.comment.comment))]):t._e(),\"3\"===e.comment.type?n(\"el-tag\",{attrs:{type:\"danger\"}},[t._v(\" \"+t._s(e.comment.comment))]):t._e()],1):t._e()])],1)})),1)],1)])],1):t._e(),n(\"el-card\",{staticClass:\"box-card\"},[n(\"div\",{staticClass:\"clearfix\",attrs:{slot:\"header\"},slot:\"header\"},[n(\"span\",{staticClass:\"el-icon-picture-outline\"},[t._v(\"流程图\")])]),n(\"flow\",{attrs:{xmlData:t.xmlData,taskData:t.taskList}})],1),n(\"el-dialog\",{attrs:{title:t.completeTitle,\"visible.sync\":\"false\",width:\"60%\",\"append-to-body\":\"\"}},[n(\"el-row\",{attrs:{gutter:20}},[n(\"el-col\",{attrs:{span:4,xs:24}},[n(\"h6\",[t._v(\"部门列表\")]),n(\"div\",{staticClass:\"head-container\"},[n(\"el-input\",{staticStyle:{\"margin-bottom\":\"20px\"},attrs:{placeholder:\"请输入部门名称\",clearable:\"\",size:\"small\",\"prefix-icon\":\"el-icon-search\"},model:{value:t.deptName,callback:function(e){t.deptName=e},expression:\"deptName\"}})],1),n(\"div\",{staticClass:\"head-container\"},[n(\"el-tree\",{ref:\"tree\",attrs:{data:t.deptOptions,props:t.defaultProps,\"expand-on-click-node\":!1,\"filter-node-method\":t.filterNode,\"default-expand-all\":\"\"},on:{\"node-click\":t.handleNodeClick}})],1)]),n(\"el-col\",{attrs:{span:12,xs:24}},[n(\"h6\",[t._v(\"待选人员\")]),n(\"el-table\",{ref:\"singleTable\",staticStyle:{width:\"100%\"},attrs:{data:t.userList,border:\"\"},on:{\"selection-change\":t.handleSelectionChange}},[n(\"el-table-column\",{attrs:{type:\"selection\",width:\"50\",align:\"center\"}}),n(\"el-table-column\",{attrs:{label:\"用户名\",align:\"center\",prop:\"nickName\"}}),n(\"el-table-column\",{attrs:{label:\"部门\",align:\"center\",prop:\"dept.deptName\"}})],1)],1),n(\"el-col\",{attrs:{span:8,xs:24}},[n(\"h6\",[t._v(\"已选人员\")]),t._l(t.userData,(function(e){return n(\"el-tag\",{key:e.nickName,attrs:{closable:\"\"},on:{close:function(n){return t.handleClose(e)}}},[t._v(\" \"+t._s(e.nickName)+\" \"+t._s(e.dept.deptName)+\" \")])}))],2)],1),n(\"span\",{staticClass:\"dialog-footer\",attrs:{slot:\"footer\"},slot:\"footer\"},[n(\"el-input\",{staticStyle:{width:\"50%\",\"margin-right\":\"34%\"},attrs:{type:\"textarea\",placeholder:\"请输入处理意见\"},model:{value:t.taskForm.comment,callback:function(e){t.$set(t.taskForm,\"comment\",e)},expression:\"taskForm.comment\"}}),n(\"el-button\",{on:{click:function(e){t.completeOpen=!1}}},[t._v(\"取 消\")]),n(\"el-button\",{attrs:{type:\"primary\"},on:{click:t.taskComplete}},[t._v(\"确 定\")])],1)],1),n(\"el-dialog\",{attrs:{title:t.completeTitle,visible:t.completeOpen,width:\"40%\",\"append-to-body\":\"\"},on:{\"update:visible\":function(e){t.completeOpen=e}}},[n(\"el-form\",{ref:\"taskForm\",attrs:{model:t.taskForm,\"label-width\":\"80px\"}},[n(\"el-form-item\",{attrs:{label:\"审批意见\",prop:\"comment\",rules:[{required:!0,message:\"请输入处理意见\",trigger:\"blur\"}]}},[n(\"el-input\",{staticStyle:{width:\"50%\"},attrs:{type:\"textarea\",placeholder:\"请输入处理意见\"},model:{value:t.taskForm.comment,callback:function(e){t.$set(t.taskForm,\"comment\",e)},expression:\"taskForm.comment\"}})],1)],1),n(\"span\",{staticClass:\"dialog-footer\",attrs:{slot:\"footer\"},slot:\"footer\"},[n(\"el-button\",{on:{click:function(e){t.completeOpen=!1}}},[t._v(\"取 消\")]),n(\"el-button\",{attrs:{type:\"primary\"},on:{click:t.taskComplete}},[t._v(\"确 定\")])],1)],1),n(\"el-dialog\",{attrs:{title:t.returnTitle,visible:t.returnOpen,width:\"40%\",\"append-to-body\":\"\"},on:{\"update:visible\":function(e){t.returnOpen=e}}},[n(\"el-form\",{ref:\"taskForm\",attrs:{model:t.taskForm,\"label-width\":\"80px\"}},[n(\"el-form-item\",{attrs:{label:\"退回节点\",prop:\"targetKey\",rules:[{required:!0,message:\"请选择退回节点\",trigger:\"change\"}]}},[n(\"el-radio-group\",{model:{value:t.taskForm.targetKey,callback:function(e){t.$set(t.taskForm,\"targetKey\",e)},expression:\"taskForm.targetKey\"}},t._l(t.returnTaskList,(function(e){return n(\"el-radio-button\",{key:e.id,attrs:{label:e.id}},[t._v(t._s(e.name))])})),1)],1),n(\"el-form-item\",{attrs:{label:\"退回意见\",prop:\"comment\",rules:[{required:!0,message:\"请输入意见\",trigger:\"blur\"}]}},[n(\"el-input\",{staticStyle:{width:\"50%\"},attrs:{type:\"textarea\",placeholder:\"请输入意见\"},model:{value:t.taskForm.comment,callback:function(e){t.$set(t.taskForm,\"comment\",e)},expression:\"taskForm.comment\"}})],1)],1),n(\"span\",{staticClass:\"dialog-footer\",attrs:{slot:\"footer\"},slot:\"footer\"},[n(\"el-button\",{on:{click:function(e){t.returnOpen=!1}}},[t._v(\"取 消\")]),n(\"el-button\",{attrs:{type:\"primary\"},on:{click:t.taskReturn}},[t._v(\"确 定\")])],1)],1),n(\"el-dialog\",{attrs:{title:t.rejectTitle,visible:t.rejectOpen,width:\"40%\",\"append-to-body\":\"\"},on:{\"update:visible\":function(e){t.rejectOpen=e}}},[n(\"el-form\",{ref:\"taskForm\",attrs:{model:t.taskForm,\"label-width\":\"80px\"}},[n(\"el-form-item\",{attrs:{label:\"驳回意见\",prop:\"comment\",rules:[{required:!0,message:\"请输入意见\",trigger:\"blur\"}]}},[n(\"el-input\",{staticStyle:{width:\"50%\"},attrs:{type:\"textarea\",placeholder:\"请输入意见\"},model:{value:t.taskForm.comment,callback:function(e){t.$set(t.taskForm,\"comment\",e)},expression:\"taskForm.comment\"}})],1)],1),n(\"span\",{staticClass:\"dialog-footer\",attrs:{slot:\"footer\"},slot:\"footer\"},[n(\"el-button\",{on:{click:function(e){t.rejectOpen=!1}}},[t._v(\"取 消\")]),n(\"el-button\",{attrs:{type:\"primary\"},on:{click:t.taskReject}},[t._v(\"确 定\")])],1)],1)],1)},a=[],o=n(\"6e55\"),s=n(\"9f75\"),i=n(\"1538\"),l=n(\"e099\"),c=n(\"589b\"),u=n(\"fcb7\"),d=(n(\"542c\"),n(\"ca17\")),f=n.n(d),m=n(\"c0c7\"),p=n(\"c1df\"),h=n.n(p),b={name:\"Record\",components:{Parser:s[\"a\"],flow:c[\"default\"],Treeselect:f.a},props:{procDefKey:{type:String,default:void 0},taskId:{type:String,default:void 0},procInsId:{type:String,default:void 0}},data:function(){return{xmlData:\"\",taskList:[],deptName:void 0,deptOptions:void 0,userList:null,defaultProps:{children:\"children\",label:\"label\"},queryParams:{deptId:void 0},loading:!0,flowRecordList:[],formConfCopy:{},src:null,rules:{},variablesForm:{},taskForm:{returnTaskShow:!1,delegateTaskShow:!1,defaultTaskShow:!0,sendUserShow:!1,multiple:!1,comment:\"\",procInsId:\"\",instanceId:\"\",taskId:\"\",procDefKey:\"\",vars:\"\",targetKey:\"\"},userDataList:[],assignee:null,formConf:{},formConfOpen:!1,variables:[],variablesData:{},variableOpen:!1,returnTaskList:[],finished:\"false\",completeTitle:null,completeOpen:!1,returnTitle:null,returnOpen:!1,rejectOpen:!1,rejectTitle:null,userData:[]}},created:function(){console.log(\"========record========created=>>>\"),console.log(this._props);var t=this._props,e=t.taskId,n=t.procDefKey,r=t.procInsId,a=t.finished;this.taskForm.taskId=e,this.taskForm.procInsId=r,this.taskForm.instanceId=r,this.taskForm.procDefKey=n,this.finished=a,this.getFlowViewer(this.taskForm.procInsId,this.taskForm.procDefKey),this.taskForm.taskId&&(this.processVariables(this.taskForm.taskId),this.getNextFlowNode(this.taskForm.taskId)),this.getFlowRecordList(this.taskForm.procInsId),this.finished=this.$route.query&&this.$route.query.finished},activated:function(){console.log(\"========record========activated=>>>\");var t=this._props,e=t.taskId,n=t.procDefKey,r=t.procInsId,a=t.finished;console.log(this._props),this.taskForm.taskId=e,this.taskForm.procInsId=r,this.taskForm.instanceId=r,this.taskForm.procDefKey=n,this.finished=a,this.getFlowViewer(this.taskForm.procInsId,this.taskForm.procDefKey),this.taskForm.taskId&&(this.processVariables(this.taskForm.taskId),this.getNextFlowNode(this.taskForm.taskId)),this.getFlowRecordList(this.taskForm.procInsId),this.finished=this.$route.query&&this.$route.query.finished},mounted:function(){},methods:{getTreeselect:function(){var t=this;Object(u[\"g\"])().then((function(e){t.deptOptions=e.data}))},getList:function(){var t=this;Object(m[\"h\"])(this.addDateRange(this.queryParams,this.dateRange)).then((function(e){t.userList=e.rows,t.total=e.total}))},filterNode:function(t,e){return!t||-1!==e.label.indexOf(t)},handleNodeClick:function(t){this.queryParams.deptId=t.id,this.getList()},getModelDetail:function(t){var e=this;Object(i[\"j\"])(t).then((function(t){e.xmlData=t.data}))},getFlowViewer:function(t,e){var n=this;Object(i[\"f\"])(t).then((function(t){n.taskList=t.data,n.getModelDetail(e)}))},setIcon:function(t){return t?\"el-icon-check\":\"el-icon-time\"},setColor:function(t){return t?\"#2bc418\":\"#b3bdbb\"},handleSelectionChange:function(t){this.userData=t;var e=t.map((function(t){return t.userId}))[0];this.taskForm.values=e instanceof Array?{approval:e.join(\",\")}:{approval:e}},handleClose:function(t){this.userData.splice(this.userData.indexOf(t),1)},handleCheckChange:function(t){this.taskForm.values=t instanceof Array?{approval:t.join(\",\")}:{approval:t}},getFlowRecordList:function(t){var e=this,n={procInsId:t};Object(o[\"e\"])(n).then((function(t){e.flowRecordList=t.data.flowList,t.data.formData&&(e.formConf=t.data.formData,e.formConfOpen=!0)})).catch((function(t){e.goBack()}))},fillFormData:function(t,e){t.fields.forEach((function(t){var n=e[t.__vModel__];n&&(t.__config__.defaultValue=n)}))},processVariables:function(t){var e=this;t&&Object(i[\"g\"])(t).then((function(t){e.variablesData=t.data.variables,e.variableOpen=!0}))},getNextFlowNode:function(t){var e=this,n={taskId:t};Object(l[\"h\"])(n).then((function(t){var n=t.data;n&&(\"assignee\"===n.type?e.userDataList=t.data.userList:\"candidateUsers\"===n.type?(e.userDataList=t.data.userList,e.taskForm.multiple=!0):\"candidateGroups\"===n.type?(t.data.roleList.forEach((function(t){t.userId=t.roleId,t.nickName=t.roleName})),e.userDataList=t.data.roleList,e.taskForm.multiple=!1):\"multiInstance\"===n.type&&(e.userDataList=t.data.userList,e.taskForm.multiple=!0),e.taskForm.sendUserShow=!0)}))},handleComplete:function(){this.completeOpen=!0,this.completeTitle=\"审批流程\"},taskComplete:function(t){var e=this;t&&\"string\"==typeof t&&t.constructor==String&&(this.taskForm.comment=t),this.taskForm.comment?Object(l[\"a\"])(this.taskForm).then((function(t){e.msgSuccess(t.msg),e.goBack()})):this.msgError(\"请输入审批意见\")},handleDelegate:function(){this.taskForm.delegateTaskShow=!0,this.taskForm.defaultTaskShow=!1},handleAssign:function(){},goBack:function(){this.$store.dispatch(\"tagsView/delView\",this.$route),this.$router.go(-1)},getData:function(t){if(t){var e=[];t.fields.forEach((function(t){var n={};if(n.label=t.__config__.label,t.__config__.defaultValue instanceof Array){var r=[];t.__config__.defaultValue.forEach((function(t){r.push(t)})),n.val=r}else n.val=t.__config__.defaultValue;e.push(n)})),this.variables=e}},startFlow:function(t,e,n){var r=this,a=(h()(new Date).format(\"YYYYMMDDHHmmss\"),{});this.taskForm.procDefKey&&(a.variables=n||{},a.businessKey=t,a.procDefKey=this.taskForm.procDefKey,a.taskName=e,Object(i[\"c\"])(JSON.stringify(a)).then((function(t){r.msgSuccess(t.msg),r.goBack()})))},handleReject:function(){this.rejectOpen=!0,this.rejectTitle=\"驳回流程\"},taskReject:function(){var t=this;this.$refs[\"taskForm\"].validate((function(e){e&&Object(l[\"i\"])(t.taskForm).then((function(e){t.msgSuccess(e.msg),t.goBack()}))}))},handleReturn:function(){var t=this;this.returnOpen=!0,this.returnTitle=\"退回流程\",Object(l[\"j\"])(this.taskForm).then((function(e){t.returnTaskList=e.data,t.taskForm.values=null}))},taskReturn:function(){var t=this;this.$refs[\"taskForm\"].validate((function(e){e&&Object(l[\"k\"])(t.taskForm).then((function(e){t.msgSuccess(e.msg),t.goBack()}))}))},cancelTask:function(){this.taskForm.returnTaskShow=!1,this.taskForm.defaultTaskShow=!0,this.taskForm.sendUserShow=!0,this.returnTaskList=[]},submitDeleteTask:function(){var t=this;this.$refs[\"taskForm\"].validate((function(e){e&&Object(l[\"c\"])(t.taskForm).then((function(e){t.msgSuccess(e.msg),t.goBack()}))}))},cancelDelegateTask:function(){this.taskForm.delegateTaskShow=!1,this.taskForm.defaultTaskShow=!0,this.taskForm.sendUserShow=!0,this.returnTaskList=[]}}},k=b,g=(n(\"6905\"),n(\"2877\")),y=Object(g[\"a\"])(k,r,a,!1,null,\"f0f66594\",null);e[\"default\"]=y.exports},9977:function(t,e,n){var r={\"./el-button.js\":\"aace\",\"./el-checkbox-group.js\":\"9413\",\"./el-input.js\":\"167d\",\"./el-radio-group.js\":\"2cfa\",\"./el-select.js\":\"7f29\",\"./el-upload.js\":\"0f88\"};function a(t){var e=o(t);return n(e)}function o(t){if(!n.o(r,t)){var e=new Error(\"Cannot find module '\"+t+\"'\");throw e.code=\"MODULE_NOT_FOUND\",e}return r[t]}a.keys=function(){return Object.keys(r)},a.resolve=o,t.exports=a,a.id=\"9977\"},c0c7:function(t,e,n){\"use strict\";n.d(e,\"h\",(function(){return o})),n.d(e,\"e\",(function(){return s})),n.d(e,\"a\",(function(){return i})),n.d(e,\"l\",(function(){return l})),n.d(e,\"c\",(function(){return c})),n.d(e,\"d\",(function(){return u})),n.d(e,\"i\",(function(){return d})),n.d(e,\"k\",(function(){return f})),n.d(e,\"b\",(function(){return m})),n.d(e,\"f\",(function(){return p})),n.d(e,\"m\",(function(){return h})),n.d(e,\"n\",(function(){return b})),n.d(e,\"o\",(function(){return k})),n.d(e,\"g\",(function(){return g})),n.d(e,\"j\",(function(){return y}));var r=n(\"b775\"),a=n(\"c38a\");function o(t){return Object(r[\"a\"])({url:\"/system/user/list\",method:\"get\",params:t})}function s(t){return Object(r[\"a\"])({url:\"/system/user/\"+Object(a[\"f\"])(t),method:\"get\"})}function i(t){return Object(r[\"a\"])({url:\"/system/user\",method:\"post\",data:t})}function l(t){return Object(r[\"a\"])({url:\"/system/user\",method:\"put\",data:t})}function c(t){return Object(r[\"a\"])({url:\"/system/user/\"+t,method:\"delete\"})}function u(t){return Object(r[\"a\"])({url:\"/system/user/export\",method:\"get\",params:t,timeout:6e5})}function d(t){return Object(r[\"a\"])({url:\"/system/user/print\",method:\"get\",params:t})}function f(t,e){var n={userId:t,password:e};return Object(r[\"a\"])({url:\"/system/user/resetPwd\",method:\"put\",data:n})}function m(t,e){var n={userId:t,status:e};return Object(r[\"a\"])({url:\"/system/user/changeStatus\",method:\"put\",data:n})}function p(){return Object(r[\"a\"])({url:\"/system/user/profile\",method:\"get\"})}function h(t){return Object(r[\"a\"])({url:\"/system/user/profile\",method:\"put\",data:t})}function b(t,e){var n={oldPassword:t,newPassword:e};return Object(r[\"a\"])({url:\"/system/user/profile/updatePwd\",method:\"put\",params:n})}function k(t){return Object(r[\"a\"])({url:\"/system/user/profile/avatar\",method:\"post\",data:t})}function g(){return Object(r[\"a\"])({url:\"/system/user/importTemplate\",method:\"get\"})}function y(t){return Object(r[\"a\"])({url:\"/system/user/register\",method:\"post\",data:t})}},e099:function(t,e,n){\"use strict\";n.d(e,\"l\",(function(){return a})),n.d(e,\"a\",(function(){return o})),n.d(e,\"c\",(function(){return s})),n.d(e,\"k\",(function(){return i})),n.d(e,\"i\",(function(){return l})),n.d(e,\"j\",(function(){return c})),n.d(e,\"h\",(function(){return u})),n.d(e,\"f\",(function(){return d})),n.d(e,\"b\",(function(){return f})),n.d(e,\"e\",(function(){return m})),n.d(e,\"g\",(function(){return p})),n.d(e,\"d\",(function(){return h}));var r=n(\"b775\");n(\"3fd3\");function a(t){return Object(r[\"a\"])({url:\"/flowable/task/todoList\",method:\"get\",params:t})}function o(t){return Object(r[\"a\"])({url:\"/flowable/task/complete\",method:\"post\",data:t})}function s(t){return Object(r[\"a\"])({url:\"/flowable/task/delegate\",method:\"post\",data:t})}function i(t){return Object(r[\"a\"])({url:\"/flowable/task/return\",method:\"post\",data:t})}function l(t){return Object(r[\"a\"])({url:\"/flowable/task/reject\",method:\"post\",data:t})}function c(t){return Object(r[\"a\"])({url:\"/flowable/task/returnList\",method:\"post\",data:t})}function u(t){return Object(r[\"a\"])({url:\"/flowable/task/nextFlowNode\",method:\"post\",data:t})}function d(t){return Object(r[\"a\"])({url:\"/system/deployment/\"+t,method:\"get\"})}function f(t){return Object(r[\"a\"])({url:\"/system/deployment/\"+t,method:\"delete\"})}function m(t){return Object(r[\"a\"])({url:\"/system/deployment/export\",method:\"get\",params:t})}function p(t){return Object(r[\"a\"])({url:\"/flowable/task/getInsIdByBizKey?bizKey=\"+t.bizKey+\"&defKey=\"+t.defKey,method:\"get\",data:t})}function h(t){return Object(r[\"a\"])({url:\"/flowable/task/end\",method:\"post\",data:t})}},fcb7:function(t,e,n){\"use strict\";n.d(e,\"d\",(function(){return a})),n.d(e,\"e\",(function(){return o})),n.d(e,\"c\",(function(){return s})),n.d(e,\"g\",(function(){return i})),n.d(e,\"f\",(function(){return l})),n.d(e,\"a\",(function(){return c})),n.d(e,\"h\",(function(){return u})),n.d(e,\"b\",(function(){return d}));var r=n(\"b775\");function a(t){return Object(r[\"a\"])({url:\"/system/dept/list\",method:\"get\",params:t})}function o(t){return Object(r[\"a\"])({url:\"/system/dept/list/exclude/\"+t,method:\"get\"})}function s(t){return Object(r[\"a\"])({url:\"/system/dept/\"+t,method:\"get\"})}function i(){return Object(r[\"a\"])({url:\"/system/dept/treeselect\",method:\"get\"})}function l(t){return Object(r[\"a\"])({url:\"/system/dept/roleDeptTreeselect/\"+t,method:\"get\"})}function c(t){return Object(r[\"a\"])({url:\"/system/dept\",method:\"post\",data:t})}function u(t){return Object(r[\"a\"])({url:\"/system/dept\",method:\"put\",data:t})}function d(t){return Object(r[\"a\"])({url:\"/system/dept/\"+t,method:\"delete\"})}}}]);", "extractedComments": []}