{"code": "(window[\"webpackJsonp\"]=window[\"webpackJsonp\"]||[]).push([[\"chunk-57a2bd4d\"],{\"24e5\":function(t,e,r){!function(t,r){r(e)}(0,(function(t){\"use strict\";var e=\"0123456789abcdefghijklmnopqrstuvwxyz\";function r(t){return e.charAt(t)}function n(t,e){return t&e}function i(t,e){return t|e}function s(t,e){return t^e}function o(t,e){return t&~e}function a(t){if(0==t)return-1;var e=0;return 0==(65535&t)&&(t>>=16,e+=16),0==(255&t)&&(t>>=8,e+=8),0==(15&t)&&(t>>=4,e+=4),0==(3&t)&&(t>>=2,e+=2),0==(1&t)&&++e,e}function h(t){for(var e=0;0!=t;)t&=t-1,++e;return e}var u=\"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\";function c(t){var e,r,n=\"\";for(e=0;e+3<=t.length;e+=3)r=parseInt(t.substring(e,e+3),16),n+=u.charAt(r>>6)+u.charAt(63&r);for(e+1==t.length?(r=parseInt(t.substring(e,e+1),16),n+=u.charAt(r<<2)):e+2==t.length&&(r=parseInt(t.substring(e,e+2),16),n+=u.charAt(r>>2)+u.charAt((3&r)<<4));0<(3&n.length);)n+=\"=\";return n}function f(t){var e,n=\"\",i=0,s=0;for(e=0;e<t.length&&\"=\"!=t.charAt(e);++e){var o=u.indexOf(t.charAt(e));o<0||(0==i?(n+=r(o>>2),s=3&o,i=1):1==i?(n+=r(s<<2|o>>4),s=15&o,i=2):2==i?(n+=r(s),n+=r(o>>2),s=3&o,i=3):(n+=r(s<<2|o>>4),n+=r(15&o),i=0))}return 1==i&&(n+=r(s<<2)),n}var l,p,g=function(t,e){return(g=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},d=function(t){var e;if(void 0===l){var r=\"0123456789ABCDEF\",n=\" \\f\\n\\r\\t \\u2028\\u2029\";for(l={},e=0;e<16;++e)l[r.charAt(e)]=e;for(r=r.toLowerCase(),e=10;e<16;++e)l[r.charAt(e)]=e;for(e=0;e<n.length;++e)l[n.charAt(e)]=-1}var i=[],s=0,o=0;for(e=0;e<t.length;++e){var a=t.charAt(e);if(\"=\"==a)break;if(-1!=(a=l[a])){if(void 0===a)throw new Error(\"Illegal character at offset \"+e);s|=a,2<=++o?(i[i.length]=s,o=s=0):s<<=4}}if(o)throw new Error(\"Hex encoding incomplete: 4 bits missing\");return i},v={decode:function(t){var e;if(void 0===p){var r=\"= \\f\\n\\r\\t \\u2028\\u2029\";for(p=Object.create(null),e=0;e<64;++e)p[\"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\".charAt(e)]=e;for(e=0;e<r.length;++e)p[r.charAt(e)]=-1}var n=[],i=0,s=0;for(e=0;e<t.length;++e){var o=t.charAt(e);if(\"=\"==o)break;if(-1!=(o=p[o])){if(void 0===o)throw new Error(\"Illegal character at offset \"+e);i|=o,4<=++s?(n[n.length]=i>>16,n[n.length]=i>>8&255,n[n.length]=255&i,s=i=0):i<<=6}}switch(s){case 1:throw new Error(\"Base64 encoding incomplete: at least 2 bits missing\");case 2:n[n.length]=i>>10;break;case 3:n[n.length]=i>>16,n[n.length]=i>>8&255}return n},re:/-----BEGIN [^-]+-----([A-Za-z0-9+\\/=\\s]+)-----END [^-]+-----|begin-base64[^\\n]+\\n([A-Za-z0-9+\\/=\\s]+)====/,unarmor:function(t){var e=v.re.exec(t);if(e)if(e[1])t=e[1];else{if(!e[2])throw new Error(\"RegExp out of sync\");t=e[2]}return v.decode(t)}},y=1e13,b=function(){function t(t){this.buf=[+t||0]}return t.prototype.mulAdd=function(t,e){var r,n,i=this.buf,s=i.length;for(r=0;r<s;++r)(n=i[r]*t+e)<y?e=0:n-=(e=0|n/y)*y,i[r]=n;0<e&&(i[r]=e)},t.prototype.sub=function(t){var e,r,n=this.buf,i=n.length;for(e=0;e<i;++e)(r=n[e]-t)<0?(r+=y,t=1):t=0,n[e]=r;for(;0===n[n.length-1];)n.pop()},t.prototype.toString=function(t){if(10!=(t||10))throw new Error(\"only base 10 is supported\");for(var e=this.buf,r=e[e.length-1].toString(),n=e.length-2;0<=n;--n)r+=(y+e[n]).toString().substring(1);return r},t.prototype.valueOf=function(){for(var t=this.buf,e=0,r=t.length-1;0<=r;--r)e=e*y+t[r];return e},t.prototype.simplify=function(){var t=this.buf;return 1==t.length?t[0]:this},t}(),m=\"…\",S=/^(\\d\\d)(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])([01]\\d|2[0-3])(?:([0-5]\\d)(?:([0-5]\\d)(?:[.,](\\d{1,3}))?)?)?(Z|[-+](?:[0]\\d|1[0-2])([0-5]\\d)?)?$/,T=/^(\\d\\d\\d\\d)(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])([01]\\d|2[0-3])(?:([0-5]\\d)(?:([0-5]\\d)(?:[.,](\\d{1,3}))?)?)?(Z|[-+](?:[0]\\d|1[0-2])([0-5]\\d)?)?$/;function w(t,e){return t.length>e&&(t=t.substring(0,e)+m),t}var E,D=function(){function t(e,r){this.hexDigits=\"0123456789ABCDEF\",e instanceof t?(this.enc=e.enc,this.pos=e.pos):(this.enc=e,this.pos=r)}return t.prototype.get=function(t){if(void 0===t&&(t=this.pos++),t>=this.enc.length)throw new Error(\"Requesting byte offset \"+t+\" on a stream of length \"+this.enc.length);return\"string\"==typeof this.enc?this.enc.charCodeAt(t):this.enc[t]},t.prototype.hexByte=function(t){return this.hexDigits.charAt(t>>4&15)+this.hexDigits.charAt(15&t)},t.prototype.hexDump=function(t,e,r){for(var n=\"\",i=t;i<e;++i)if(n+=this.hexByte(this.get(i)),!0!==r)switch(15&i){case 7:n+=\"  \";break;case 15:n+=\"\\n\";break;default:n+=\" \"}return n},t.prototype.isASCII=function(t,e){for(var r=t;r<e;++r){var n=this.get(r);if(n<32||176<n)return!1}return!0},t.prototype.parseStringISO=function(t,e){for(var r=\"\",n=t;n<e;++n)r+=String.fromCharCode(this.get(n));return r},t.prototype.parseStringUTF=function(t,e){for(var r=\"\",n=t;n<e;){var i=this.get(n++);r+=i<128?String.fromCharCode(i):191<i&&i<224?String.fromCharCode((31&i)<<6|63&this.get(n++)):String.fromCharCode((15&i)<<12|(63&this.get(n++))<<6|63&this.get(n++))}return r},t.prototype.parseStringBMP=function(t,e){for(var r,n,i=\"\",s=t;s<e;)r=this.get(s++),n=this.get(s++),i+=String.fromCharCode(r<<8|n);return i},t.prototype.parseTime=function(t,e,r){var n=this.parseStringISO(t,e),i=(r?S:T).exec(n);return i?(r&&(i[1]=+i[1],i[1]+=+i[1]<70?2e3:1900),n=i[1]+\"-\"+i[2]+\"-\"+i[3]+\" \"+i[4],i[5]&&(n+=\":\"+i[5],i[6]&&(n+=\":\"+i[6],i[7]&&(n+=\".\"+i[7]))),i[8]&&(n+=\" UTC\",\"Z\"!=i[8]&&(n+=i[8],i[9]&&(n+=\":\"+i[9]))),n):\"Unrecognized time: \"+n},t.prototype.parseInteger=function(t,e){for(var r,n=this.get(t),i=127<n,s=i?255:0,o=\"\";n==s&&++t<e;)n=this.get(t);if(0===(r=e-t))return i?-1:0;if(4<r){for(o=n,r<<=3;0==(128&(+o^s));)o=+o<<1,--r;o=\"(\"+r+\" bit)\\n\"}i&&(n-=256);for(var a=new b(n),h=t+1;h<e;++h)a.mulAdd(256,this.get(h));return o+a.toString()},t.prototype.parseBitString=function(t,e,r){for(var n=this.get(t),i=\"(\"+((e-t-1<<3)-n)+\" bit)\\n\",s=\"\",o=t+1;o<e;++o){for(var a=this.get(o),h=o==e-1?n:0,u=7;h<=u;--u)s+=a>>u&1?\"1\":\"0\";if(s.length>r)return i+w(s,r)}return i+s},t.prototype.parseOctetString=function(t,e,r){if(this.isASCII(t,e))return w(this.parseStringISO(t,e),r);var n=e-t,i=\"(\"+n+\" byte)\\n\";(r/=2)<n&&(e=t+r);for(var s=t;s<e;++s)i+=this.hexByte(this.get(s));return r<n&&(i+=m),i},t.prototype.parseOID=function(t,e,r){for(var n=\"\",i=new b,s=0,o=t;o<e;++o){var a=this.get(o);if(i.mulAdd(128,127&a),s+=7,!(128&a)){if(\"\"===n)if((i=i.simplify())instanceof b)i.sub(80),n=\"2.\"+i.toString();else{var h=i<80?i<40?0:1:2;n=h+\".\"+(i-40*h)}else n+=\".\"+i.toString();if(n.length>r)return w(n,r);i=new b,s=0}}return 0<s&&(n+=\".incomplete\"),n},t}(),j=function(){function t(t,e,r,n,i){if(!(n instanceof O))throw new Error(\"Invalid tag value.\");this.stream=t,this.header=e,this.length=r,this.tag=n,this.sub=i}return t.prototype.typeName=function(){switch(this.tag.tagClass){case 0:switch(this.tag.tagNumber){case 0:return\"EOC\";case 1:return\"BOOLEAN\";case 2:return\"INTEGER\";case 3:return\"BIT_STRING\";case 4:return\"OCTET_STRING\";case 5:return\"NULL\";case 6:return\"OBJECT_IDENTIFIER\";case 7:return\"ObjectDescriptor\";case 8:return\"EXTERNAL\";case 9:return\"REAL\";case 10:return\"ENUMERATED\";case 11:return\"EMBEDDED_PDV\";case 12:return\"UTF8String\";case 16:return\"SEQUENCE\";case 17:return\"SET\";case 18:return\"NumericString\";case 19:return\"PrintableString\";case 20:return\"TeletexString\";case 21:return\"VideotexString\";case 22:return\"IA5String\";case 23:return\"UTCTime\";case 24:return\"GeneralizedTime\";case 25:return\"GraphicString\";case 26:return\"VisibleString\";case 27:return\"GeneralString\";case 28:return\"UniversalString\";case 30:return\"BMPString\"}return\"Universal_\"+this.tag.tagNumber.toString();case 1:return\"Application_\"+this.tag.tagNumber.toString();case 2:return\"[\"+this.tag.tagNumber.toString()+\"]\";case 3:return\"Private_\"+this.tag.tagNumber.toString()}},t.prototype.content=function(t){if(void 0===this.tag)return null;void 0===t&&(t=1/0);var e=this.posContent(),r=Math.abs(this.length);if(!this.tag.isUniversal())return null!==this.sub?\"(\"+this.sub.length+\" elem)\":this.stream.parseOctetString(e,e+r,t);switch(this.tag.tagNumber){case 1:return 0===this.stream.get(e)?\"false\":\"true\";case 2:return this.stream.parseInteger(e,e+r);case 3:return this.sub?\"(\"+this.sub.length+\" elem)\":this.stream.parseBitString(e,e+r,t);case 4:return this.sub?\"(\"+this.sub.length+\" elem)\":this.stream.parseOctetString(e,e+r,t);case 6:return this.stream.parseOID(e,e+r,t);case 16:case 17:return null!==this.sub?\"(\"+this.sub.length+\" elem)\":\"(no elem)\";case 12:return w(this.stream.parseStringUTF(e,e+r),t);case 18:case 19:case 20:case 21:case 22:case 26:return w(this.stream.parseStringISO(e,e+r),t);case 30:return w(this.stream.parseStringBMP(e,e+r),t);case 23:case 24:return this.stream.parseTime(e,e+r,23==this.tag.tagNumber)}return null},t.prototype.toString=function(){return this.typeName()+\"@\"+this.stream.pos+\"[header:\"+this.header+\",length:\"+this.length+\",sub:\"+(null===this.sub?\"null\":this.sub.length)+\"]\"},t.prototype.toPrettyString=function(t){void 0===t&&(t=\"\");var e=t+this.typeName()+\" @\"+this.stream.pos;if(0<=this.length&&(e+=\"+\"),e+=this.length,this.tag.tagConstructed?e+=\" (constructed)\":!this.tag.isUniversal()||3!=this.tag.tagNumber&&4!=this.tag.tagNumber||null===this.sub||(e+=\" (encapsulates)\"),e+=\"\\n\",null!==this.sub){t+=\"  \";for(var r=0,n=this.sub.length;r<n;++r)e+=this.sub[r].toPrettyString(t)}return e},t.prototype.posStart=function(){return this.stream.pos},t.prototype.posContent=function(){return this.stream.pos+this.header},t.prototype.posEnd=function(){return this.stream.pos+this.header+Math.abs(this.length)},t.prototype.toHexString=function(){return this.stream.hexDump(this.posStart(),this.posEnd(),!0)},t.decodeLength=function(t){var e=t.get(),r=127&e;if(r==e)return r;if(6<r)throw new Error(\"Length over 48 bits not supported at position \"+(t.pos-1));if(0===r)return null;for(var n=e=0;n<r;++n)e=256*e+t.get();return e},t.prototype.getHexStringValue=function(){var t=this.toHexString(),e=2*this.header,r=2*this.length;return t.substr(e,r)},t.decode=function(e){var r;r=e instanceof D?e:new D(e,0);var n=new D(r),i=new O(r),s=t.decodeLength(r),o=r.pos,a=o-n.pos,h=null,u=function(){var e=[];if(null!==s){for(var n=o+s;r.pos<n;)e[e.length]=t.decode(r);if(r.pos!=n)throw new Error(\"Content size is not correct for container starting at offset \"+o)}else try{for(;;){var i=t.decode(r);if(i.tag.isEOC())break;e[e.length]=i}s=o-r.pos}catch(e){throw new Error(\"Exception while decoding undefined length content: \"+e)}return e};if(i.tagConstructed)h=u();else if(i.isUniversal()&&(3==i.tagNumber||4==i.tagNumber))try{if(3==i.tagNumber&&0!=r.get())throw new Error(\"BIT STRINGs with unused bits cannot encapsulate.\");h=u();for(var c=0;c<h.length;++c)if(h[c].tag.isEOC())throw new Error(\"EOC is not supposed to be actual content.\")}catch(e){h=null}if(null===h){if(null===s)throw new Error(\"We can't skip over an invalid tag with undefined length at offset \"+o);r.pos=o+Math.abs(s)}return new t(n,a,s,i,h)},t}(),O=function(){function t(t){var e=t.get();if(this.tagClass=e>>6,this.tagConstructed=0!=(32&e),this.tagNumber=31&e,31==this.tagNumber){for(var r=new b;e=t.get(),r.mulAdd(128,127&e),128&e;);this.tagNumber=r.simplify()}}return t.prototype.isUniversal=function(){return 0===this.tagClass},t.prototype.isEOC=function(){return 0===this.tagClass&&0===this.tagNumber},t}(),x=[2,3,5,7,11,13,17,19,23,29,31,37,41,43,47,53,59,61,67,71,73,79,83,89,97,101,103,107,109,113,127,131,137,139,149,151,157,163,167,173,179,181,191,193,197,199,211,223,227,229,233,239,241,251,257,263,269,271,277,281,283,293,307,311,313,317,331,337,347,349,353,359,367,373,379,383,389,397,401,409,419,421,431,433,439,443,449,457,461,463,467,479,487,491,499,503,509,521,523,541,547,557,563,569,571,577,587,593,599,601,607,613,617,619,631,641,643,647,653,659,661,673,677,683,691,701,709,719,727,733,739,743,751,757,761,769,773,787,797,809,811,821,823,827,829,839,853,857,859,863,877,881,883,887,907,911,919,929,937,941,947,953,967,971,977,983,991,997],A=(1<<26)/x[x.length-1],B=function(){function t(t,e,r){null!=t&&(\"number\"==typeof t?this.fromNumber(t,e,r):null==e&&\"string\"!=typeof t?this.fromString(t,256):this.fromString(t,e))}return t.prototype.toString=function(t){if(this.s<0)return\"-\"+this.negate().toString(t);var e;if(16==t)e=4;else if(8==t)e=3;else if(2==t)e=1;else if(32==t)e=5;else{if(4!=t)return this.toRadix(t);e=2}var n,i=(1<<e)-1,s=!1,o=\"\",a=this.t,h=this.DB-a*this.DB%e;if(0<a--)for(h<this.DB&&0<(n=this[a]>>h)&&(s=!0,o=r(n));0<=a;)h<e?(n=(this[a]&(1<<h)-1)<<e-h,n|=this[--a]>>(h+=this.DB-e)):(n=this[a]>>(h-=e)&i,h<=0&&(h+=this.DB,--a)),0<n&&(s=!0),s&&(o+=r(n));return s?o:\"0\"},t.prototype.negate=function(){var e=P();return t.ZERO.subTo(this,e),e},t.prototype.abs=function(){return this.s<0?this.negate():this},t.prototype.compareTo=function(t){var e=this.s-t.s;if(0!=e)return e;var r=this.t;if(0!=(e=r-t.t))return this.s<0?-e:e;for(;0<=--r;)if(0!=(e=this[r]-t[r]))return e;return 0},t.prototype.bitLength=function(){return this.t<=0?0:this.DB*(this.t-1)+F(this[this.t-1]^this.s&this.DM)},t.prototype.mod=function(e){var r=P();return this.abs().divRemTo(e,null,r),this.s<0&&0<r.compareTo(t.ZERO)&&e.subTo(r,r),r},t.prototype.modPowInt=function(t,e){var r;return r=t<256||e.isEven()?new V(e):new _(e),this.exp(t,r)},t.prototype.clone=function(){var t=P();return this.copyTo(t),t},t.prototype.intValue=function(){if(this.s<0){if(1==this.t)return this[0]-this.DV;if(0==this.t)return-1}else{if(1==this.t)return this[0];if(0==this.t)return 0}return(this[1]&(1<<32-this.DB)-1)<<this.DB|this[0]},t.prototype.byteValue=function(){return 0==this.t?this.s:this[0]<<24>>24},t.prototype.shortValue=function(){return 0==this.t?this.s:this[0]<<16>>16},t.prototype.signum=function(){return this.s<0?-1:this.t<=0||1==this.t&&this[0]<=0?0:1},t.prototype.toByteArray=function(){var t=this.t,e=[];e[0]=this.s;var r,n=this.DB-t*this.DB%8,i=0;if(0<t--)for(n<this.DB&&(r=this[t]>>n)!=(this.s&this.DM)>>n&&(e[i++]=r|this.s<<this.DB-n);0<=t;)n<8?(r=(this[t]&(1<<n)-1)<<8-n,r|=this[--t]>>(n+=this.DB-8)):(r=this[t]>>(n-=8)&255,n<=0&&(n+=this.DB,--t)),0!=(128&r)&&(r|=-256),0==i&&(128&this.s)!=(128&r)&&++i,(0<i||r!=this.s)&&(e[i++]=r);return e},t.prototype.equals=function(t){return 0==this.compareTo(t)},t.prototype.min=function(t){return this.compareTo(t)<0?this:t},t.prototype.max=function(t){return 0<this.compareTo(t)?this:t},t.prototype.and=function(t){var e=P();return this.bitwiseTo(t,n,e),e},t.prototype.or=function(t){var e=P();return this.bitwiseTo(t,i,e),e},t.prototype.xor=function(t){var e=P();return this.bitwiseTo(t,s,e),e},t.prototype.andNot=function(t){var e=P();return this.bitwiseTo(t,o,e),e},t.prototype.not=function(){for(var t=P(),e=0;e<this.t;++e)t[e]=this.DM&~this[e];return t.t=this.t,t.s=~this.s,t},t.prototype.shiftLeft=function(t){var e=P();return t<0?this.rShiftTo(-t,e):this.lShiftTo(t,e),e},t.prototype.shiftRight=function(t){var e=P();return t<0?this.lShiftTo(-t,e):this.rShiftTo(t,e),e},t.prototype.getLowestSetBit=function(){for(var t=0;t<this.t;++t)if(0!=this[t])return t*this.DB+a(this[t]);return this.s<0?this.t*this.DB:-1},t.prototype.bitCount=function(){for(var t=0,e=this.s&this.DM,r=0;r<this.t;++r)t+=h(this[r]^e);return t},t.prototype.testBit=function(t){var e=Math.floor(t/this.DB);return e>=this.t?0!=this.s:0!=(this[e]&1<<t%this.DB)},t.prototype.setBit=function(t){return this.changeBit(t,i)},t.prototype.clearBit=function(t){return this.changeBit(t,o)},t.prototype.flipBit=function(t){return this.changeBit(t,s)},t.prototype.add=function(t){var e=P();return this.addTo(t,e),e},t.prototype.subtract=function(t){var e=P();return this.subTo(t,e),e},t.prototype.multiply=function(t){var e=P();return this.multiplyTo(t,e),e},t.prototype.divide=function(t){var e=P();return this.divRemTo(t,e,null),e},t.prototype.remainder=function(t){var e=P();return this.divRemTo(t,null,e),e},t.prototype.divideAndRemainder=function(t){var e=P(),r=P();return this.divRemTo(t,e,r),[e,r]},t.prototype.modPow=function(t,e){var r,n,i=t.bitLength(),s=H(1);if(i<=0)return s;r=i<18?1:i<48?3:i<144?4:i<768?5:6,n=i<8?new V(e):e.isEven()?new I(e):new _(e);var o=[],a=3,h=r-1,u=(1<<r)-1;if(o[1]=n.convert(this),1<r){var c=P();for(n.sqrTo(o[1],c);a<=u;)o[a]=P(),n.mulTo(c,o[a-2],o[a]),a+=2}var f,l,p=t.t-1,g=!0,d=P();for(i=F(t[p])-1;0<=p;){for(h<=i?f=t[p]>>i-h&u:(f=(t[p]&(1<<i+1)-1)<<h-i,0<p&&(f|=t[p-1]>>this.DB+i-h)),a=r;0==(1&f);)f>>=1,--a;if((i-=a)<0&&(i+=this.DB,--p),g)o[f].copyTo(s),g=!1;else{for(;1<a;)n.sqrTo(s,d),n.sqrTo(d,s),a-=2;0<a?n.sqrTo(s,d):(l=s,s=d,d=l),n.mulTo(d,o[f],s)}for(;0<=p&&0==(t[p]&1<<i);)n.sqrTo(s,d),l=s,s=d,d=l,--i<0&&(i=this.DB-1,--p)}return n.revert(s)},t.prototype.modInverse=function(e){var r=e.isEven();if(this.isEven()&&r||0==e.signum())return t.ZERO;for(var n=e.clone(),i=this.clone(),s=H(1),o=H(0),a=H(0),h=H(1);0!=n.signum();){for(;n.isEven();)n.rShiftTo(1,n),r?(s.isEven()&&o.isEven()||(s.addTo(this,s),o.subTo(e,o)),s.rShiftTo(1,s)):o.isEven()||o.subTo(e,o),o.rShiftTo(1,o);for(;i.isEven();)i.rShiftTo(1,i),r?(a.isEven()&&h.isEven()||(a.addTo(this,a),h.subTo(e,h)),a.rShiftTo(1,a)):h.isEven()||h.subTo(e,h),h.rShiftTo(1,h);0<=n.compareTo(i)?(n.subTo(i,n),r&&s.subTo(a,s),o.subTo(h,o)):(i.subTo(n,i),r&&a.subTo(s,a),h.subTo(o,h))}return 0!=i.compareTo(t.ONE)?t.ZERO:0<=h.compareTo(e)?h.subtract(e):h.signum()<0?(h.addTo(e,h),h.signum()<0?h.add(e):h):h},t.prototype.pow=function(t){return this.exp(t,new R)},t.prototype.gcd=function(t){var e=this.s<0?this.negate():this.clone(),r=t.s<0?t.negate():t.clone();if(e.compareTo(r)<0){var n=e;e=r,r=n}var i=e.getLowestSetBit(),s=r.getLowestSetBit();if(s<0)return e;for(i<s&&(s=i),0<s&&(e.rShiftTo(s,e),r.rShiftTo(s,r));0<e.signum();)0<(i=e.getLowestSetBit())&&e.rShiftTo(i,e),0<(i=r.getLowestSetBit())&&r.rShiftTo(i,r),0<=e.compareTo(r)?(e.subTo(r,e),e.rShiftTo(1,e)):(r.subTo(e,r),r.rShiftTo(1,r));return 0<s&&r.lShiftTo(s,r),r},t.prototype.isProbablePrime=function(t){var e,r=this.abs();if(1==r.t&&r[0]<=x[x.length-1]){for(e=0;e<x.length;++e)if(r[0]==x[e])return!0;return!1}if(r.isEven())return!1;for(e=1;e<x.length;){for(var n=x[e],i=e+1;i<x.length&&n<A;)n*=x[i++];for(n=r.modInt(n);e<i;)if(n%x[e++]==0)return!1}return r.millerRabin(t)},t.prototype.copyTo=function(t){for(var e=this.t-1;0<=e;--e)t[e]=this[e];t.t=this.t,t.s=this.s},t.prototype.fromInt=function(t){this.t=1,this.s=t<0?-1:0,0<t?this[0]=t:t<-1?this[0]=t+this.DV:this.t=0},t.prototype.fromString=function(e,r){var n;if(16==r)n=4;else if(8==r)n=3;else if(256==r)n=8;else if(2==r)n=1;else if(32==r)n=5;else{if(4!=r)return void this.fromRadix(e,r);n=2}this.t=0,this.s=0;for(var i=e.length,s=!1,o=0;0<=--i;){var a=8==n?255&+e[i]:C(e,i);a<0?\"-\"==e.charAt(i)&&(s=!0):(s=!1,0==o?this[this.t++]=a:o+n>this.DB?(this[this.t-1]|=(a&(1<<this.DB-o)-1)<<o,this[this.t++]=a>>this.DB-o):this[this.t-1]|=a<<o,(o+=n)>=this.DB&&(o-=this.DB))}8==n&&0!=(128&+e[0])&&(this.s=-1,0<o&&(this[this.t-1]|=(1<<this.DB-o)-1<<o)),this.clamp(),s&&t.ZERO.subTo(this,this)},t.prototype.clamp=function(){for(var t=this.s&this.DM;0<this.t&&this[this.t-1]==t;)--this.t},t.prototype.dlShiftTo=function(t,e){var r;for(r=this.t-1;0<=r;--r)e[r+t]=this[r];for(r=t-1;0<=r;--r)e[r]=0;e.t=this.t+t,e.s=this.s},t.prototype.drShiftTo=function(t,e){for(var r=t;r<this.t;++r)e[r-t]=this[r];e.t=Math.max(this.t-t,0),e.s=this.s},t.prototype.lShiftTo=function(t,e){for(var r=t%this.DB,n=this.DB-r,i=(1<<n)-1,s=Math.floor(t/this.DB),o=this.s<<r&this.DM,a=this.t-1;0<=a;--a)e[a+s+1]=this[a]>>n|o,o=(this[a]&i)<<r;for(a=s-1;0<=a;--a)e[a]=0;e[s]=o,e.t=this.t+s+1,e.s=this.s,e.clamp()},t.prototype.rShiftTo=function(t,e){e.s=this.s;var r=Math.floor(t/this.DB);if(r>=this.t)e.t=0;else{var n=t%this.DB,i=this.DB-n,s=(1<<n)-1;e[0]=this[r]>>n;for(var o=r+1;o<this.t;++o)e[o-r-1]|=(this[o]&s)<<i,e[o-r]=this[o]>>n;0<n&&(e[this.t-r-1]|=(this.s&s)<<i),e.t=this.t-r,e.clamp()}},t.prototype.subTo=function(t,e){for(var r=0,n=0,i=Math.min(t.t,this.t);r<i;)n+=this[r]-t[r],e[r++]=n&this.DM,n>>=this.DB;if(t.t<this.t){for(n-=t.s;r<this.t;)n+=this[r],e[r++]=n&this.DM,n>>=this.DB;n+=this.s}else{for(n+=this.s;r<t.t;)n-=t[r],e[r++]=n&this.DM,n>>=this.DB;n-=t.s}e.s=n<0?-1:0,n<-1?e[r++]=this.DV+n:0<n&&(e[r++]=n),e.t=r,e.clamp()},t.prototype.multiplyTo=function(e,r){var n=this.abs(),i=e.abs(),s=n.t;for(r.t=s+i.t;0<=--s;)r[s]=0;for(s=0;s<i.t;++s)r[s+n.t]=n.am(0,i[s],r,s,0,n.t);r.s=0,r.clamp(),this.s!=e.s&&t.ZERO.subTo(r,r)},t.prototype.squareTo=function(t){for(var e=this.abs(),r=t.t=2*e.t;0<=--r;)t[r]=0;for(r=0;r<e.t-1;++r){var n=e.am(r,e[r],t,2*r,0,1);(t[r+e.t]+=e.am(r+1,2*e[r],t,2*r+1,n,e.t-r-1))>=e.DV&&(t[r+e.t]-=e.DV,t[r+e.t+1]=1)}0<t.t&&(t[t.t-1]+=e.am(r,e[r],t,2*r,0,1)),t.s=0,t.clamp()},t.prototype.divRemTo=function(e,r,n){var i=e.abs();if(!(i.t<=0)){var s=this.abs();if(s.t<i.t)return null!=r&&r.fromInt(0),void(null!=n&&this.copyTo(n));null==n&&(n=P());var o=P(),a=this.s,h=e.s,u=this.DB-F(i[i.t-1]);0<u?(i.lShiftTo(u,o),s.lShiftTo(u,n)):(i.copyTo(o),s.copyTo(n));var c=o.t,f=o[c-1];if(0!=f){var l=f*(1<<this.F1)+(1<c?o[c-2]>>this.F2:0),p=this.FV/l,g=(1<<this.F1)/l,d=1<<this.F2,v=n.t,y=v-c,b=null==r?P():r;for(o.dlShiftTo(y,b),0<=n.compareTo(b)&&(n[n.t++]=1,n.subTo(b,n)),t.ONE.dlShiftTo(c,b),b.subTo(o,o);o.t<c;)o[o.t++]=0;for(;0<=--y;){var m=n[--v]==f?this.DM:Math.floor(n[v]*p+(n[v-1]+d)*g);if((n[v]+=o.am(0,m,n,y,0,c))<m)for(o.dlShiftTo(y,b),n.subTo(b,n);n[v]<--m;)n.subTo(b,n)}null!=r&&(n.drShiftTo(c,r),a!=h&&t.ZERO.subTo(r,r)),n.t=c,n.clamp(),0<u&&n.rShiftTo(u,n),a<0&&t.ZERO.subTo(n,n)}}},t.prototype.invDigit=function(){if(this.t<1)return 0;var t=this[0];if(0==(1&t))return 0;var e=3&t;return 0<(e=(e=(e=(e=e*(2-(15&t)*e)&15)*(2-(255&t)*e)&255)*(2-((65535&t)*e&65535))&65535)*(2-t*e%this.DV)%this.DV)?this.DV-e:-e},t.prototype.isEven=function(){return 0==(0<this.t?1&this[0]:this.s)},t.prototype.exp=function(e,r){if(4294967295<e||e<1)return t.ONE;var n=P(),i=P(),s=r.convert(this),o=F(e)-1;for(s.copyTo(n);0<=--o;)if(r.sqrTo(n,i),0<(e&1<<o))r.mulTo(i,s,n);else{var a=n;n=i,i=a}return r.revert(n)},t.prototype.chunkSize=function(t){return Math.floor(Math.LN2*this.DB/Math.log(t))},t.prototype.toRadix=function(t){if(null==t&&(t=10),0==this.signum()||t<2||36<t)return\"0\";var e=this.chunkSize(t),r=Math.pow(t,e),n=H(r),i=P(),s=P(),o=\"\";for(this.divRemTo(n,i,s);0<i.signum();)o=(r+s.intValue()).toString(t).substr(1)+o,i.divRemTo(n,i,s);return s.intValue().toString(t)+o},t.prototype.fromRadix=function(e,r){this.fromInt(0),null==r&&(r=10);for(var n=this.chunkSize(r),i=Math.pow(r,n),s=!1,o=0,a=0,h=0;h<e.length;++h){var u=C(e,h);u<0?\"-\"==e.charAt(h)&&0==this.signum()&&(s=!0):(a=r*a+u,++o>=n&&(this.dMultiply(i),this.dAddOffset(a,0),a=o=0))}0<o&&(this.dMultiply(Math.pow(r,o)),this.dAddOffset(a,0)),s&&t.ZERO.subTo(this,this)},t.prototype.fromNumber=function(e,r,n){if(\"number\"==typeof r)if(e<2)this.fromInt(1);else for(this.fromNumber(e,n),this.testBit(e-1)||this.bitwiseTo(t.ONE.shiftLeft(e-1),i,this),this.isEven()&&this.dAddOffset(1,0);!this.isProbablePrime(r);)this.dAddOffset(2,0),this.bitLength()>e&&this.subTo(t.ONE.shiftLeft(e-1),this);else{var s=[],o=7&e;s.length=1+(e>>3),r.nextBytes(s),0<o?s[0]&=(1<<o)-1:s[0]=0,this.fromString(s,256)}},t.prototype.bitwiseTo=function(t,e,r){var n,i,s=Math.min(t.t,this.t);for(n=0;n<s;++n)r[n]=e(this[n],t[n]);if(t.t<this.t){for(i=t.s&this.DM,n=s;n<this.t;++n)r[n]=e(this[n],i);r.t=this.t}else{for(i=this.s&this.DM,n=s;n<t.t;++n)r[n]=e(i,t[n]);r.t=t.t}r.s=e(this.s,t.s),r.clamp()},t.prototype.changeBit=function(e,r){var n=t.ONE.shiftLeft(e);return this.bitwiseTo(n,r,n),n},t.prototype.addTo=function(t,e){for(var r=0,n=0,i=Math.min(t.t,this.t);r<i;)n+=this[r]+t[r],e[r++]=n&this.DM,n>>=this.DB;if(t.t<this.t){for(n+=t.s;r<this.t;)n+=this[r],e[r++]=n&this.DM,n>>=this.DB;n+=this.s}else{for(n+=this.s;r<t.t;)n+=t[r],e[r++]=n&this.DM,n>>=this.DB;n+=t.s}e.s=n<0?-1:0,0<n?e[r++]=n:n<-1&&(e[r++]=this.DV+n),e.t=r,e.clamp()},t.prototype.dMultiply=function(t){this[this.t]=this.am(0,t-1,this,0,0,this.t),++this.t,this.clamp()},t.prototype.dAddOffset=function(t,e){if(0!=t){for(;this.t<=e;)this[this.t++]=0;for(this[e]+=t;this[e]>=this.DV;)this[e]-=this.DV,++e>=this.t&&(this[this.t++]=0),++this[e]}},t.prototype.multiplyLowerTo=function(t,e,r){var n=Math.min(this.t+t.t,e);for(r.s=0,r.t=n;0<n;)r[--n]=0;for(var i=r.t-this.t;n<i;++n)r[n+this.t]=this.am(0,t[n],r,n,0,this.t);for(i=Math.min(t.t,e);n<i;++n)this.am(0,t[n],r,n,0,e-n);r.clamp()},t.prototype.multiplyUpperTo=function(t,e,r){--e;var n=r.t=this.t+t.t-e;for(r.s=0;0<=--n;)r[n]=0;for(n=Math.max(e-this.t,0);n<t.t;++n)r[this.t+n-e]=this.am(e-n,t[n],r,0,0,this.t+n-e);r.clamp(),r.drShiftTo(1,r)},t.prototype.modInt=function(t){if(t<=0)return 0;var e=this.DV%t,r=this.s<0?t-1:0;if(0<this.t)if(0==e)r=this[0]%t;else for(var n=this.t-1;0<=n;--n)r=(e*r+this[n])%t;return r},t.prototype.millerRabin=function(e){var r=this.subtract(t.ONE),n=r.getLowestSetBit();if(n<=0)return!1;var i=r.shiftRight(n);x.length<(e=e+1>>1)&&(e=x.length);for(var s=P(),o=0;o<e;++o){s.fromInt(x[Math.floor(Math.random()*x.length)]);var a=s.modPow(i,this);if(0!=a.compareTo(t.ONE)&&0!=a.compareTo(r)){for(var h=1;h++<n&&0!=a.compareTo(r);)if(0==(a=a.modPowInt(2,this)).compareTo(t.ONE))return!1;if(0!=a.compareTo(r))return!1}}return!0},t.prototype.square=function(){var t=P();return this.squareTo(t),t},t.prototype.gcda=function(t,e){var r=this.s<0?this.negate():this.clone(),n=t.s<0?t.negate():t.clone();if(r.compareTo(n)<0){var i=r;r=n,n=i}var s=r.getLowestSetBit(),o=n.getLowestSetBit();if(o<0)e(r);else{s<o&&(o=s),0<o&&(r.rShiftTo(o,r),n.rShiftTo(o,n));var a=function(){0<(s=r.getLowestSetBit())&&r.rShiftTo(s,r),0<(s=n.getLowestSetBit())&&n.rShiftTo(s,n),0<=r.compareTo(n)?(r.subTo(n,r),r.rShiftTo(1,r)):(n.subTo(r,n),n.rShiftTo(1,n)),0<r.signum()?setTimeout(a,0):(0<o&&n.lShiftTo(o,n),setTimeout((function(){e(n)}),0))};setTimeout(a,10)}},t.prototype.fromNumberAsync=function(e,r,n,s){if(\"number\"==typeof r)if(e<2)this.fromInt(1);else{this.fromNumber(e,n),this.testBit(e-1)||this.bitwiseTo(t.ONE.shiftLeft(e-1),i,this),this.isEven()&&this.dAddOffset(1,0);var o=this,a=function(){o.dAddOffset(2,0),o.bitLength()>e&&o.subTo(t.ONE.shiftLeft(e-1),o),o.isProbablePrime(r)?setTimeout((function(){s()}),0):setTimeout(a,0)};setTimeout(a,0)}else{var h=[],u=7&e;h.length=1+(e>>3),r.nextBytes(h),0<u?h[0]&=(1<<u)-1:h[0]=0,this.fromString(h,256)}},t}(),R=function(){function t(){}return t.prototype.convert=function(t){return t},t.prototype.revert=function(t){return t},t.prototype.mulTo=function(t,e,r){t.multiplyTo(e,r)},t.prototype.sqrTo=function(t,e){t.squareTo(e)},t}(),V=function(){function t(t){this.m=t}return t.prototype.convert=function(t){return t.s<0||0<=t.compareTo(this.m)?t.mod(this.m):t},t.prototype.revert=function(t){return t},t.prototype.reduce=function(t){t.divRemTo(this.m,null,t)},t.prototype.mulTo=function(t,e,r){t.multiplyTo(e,r),this.reduce(r)},t.prototype.sqrTo=function(t,e){t.squareTo(e),this.reduce(e)},t}(),_=function(){function t(t){this.m=t,this.mp=t.invDigit(),this.mpl=32767&this.mp,this.mph=this.mp>>15,this.um=(1<<t.DB-15)-1,this.mt2=2*t.t}return t.prototype.convert=function(t){var e=P();return t.abs().dlShiftTo(this.m.t,e),e.divRemTo(this.m,null,e),t.s<0&&0<e.compareTo(B.ZERO)&&this.m.subTo(e,e),e},t.prototype.revert=function(t){var e=P();return t.copyTo(e),this.reduce(e),e},t.prototype.reduce=function(t){for(;t.t<=this.mt2;)t[t.t++]=0;for(var e=0;e<this.m.t;++e){var r=32767&t[e],n=r*this.mpl+((r*this.mph+(t[e]>>15)*this.mpl&this.um)<<15)&t.DM;for(t[r=e+this.m.t]+=this.m.am(0,n,t,e,0,this.m.t);t[r]>=t.DV;)t[r]-=t.DV,t[++r]++}t.clamp(),t.drShiftTo(this.m.t,t),0<=t.compareTo(this.m)&&t.subTo(this.m,t)},t.prototype.mulTo=function(t,e,r){t.multiplyTo(e,r),this.reduce(r)},t.prototype.sqrTo=function(t,e){t.squareTo(e),this.reduce(e)},t}(),I=function(){function t(t){this.m=t,this.r2=P(),this.q3=P(),B.ONE.dlShiftTo(2*t.t,this.r2),this.mu=this.r2.divide(t)}return t.prototype.convert=function(t){if(t.s<0||t.t>2*this.m.t)return t.mod(this.m);if(t.compareTo(this.m)<0)return t;var e=P();return t.copyTo(e),this.reduce(e),e},t.prototype.revert=function(t){return t},t.prototype.reduce=function(t){for(t.drShiftTo(this.m.t-1,this.r2),t.t>this.m.t+1&&(t.t=this.m.t+1,t.clamp()),this.mu.multiplyUpperTo(this.r2,this.m.t+1,this.q3),this.m.multiplyLowerTo(this.q3,this.m.t+1,this.r2);t.compareTo(this.r2)<0;)t.dAddOffset(1,this.m.t+1);for(t.subTo(this.r2,t);0<=t.compareTo(this.m);)t.subTo(this.m,t)},t.prototype.mulTo=function(t,e,r){t.multiplyTo(e,r),this.reduce(r)},t.prototype.sqrTo=function(t,e){t.squareTo(e),this.reduce(e)},t}();function P(){return new B(null)}function N(t,e){return new B(t,e)}\"Microsoft Internet Explorer\"==navigator.appName?(B.prototype.am=function(t,e,r,n,i,s){for(var o=32767&e,a=e>>15;0<=--s;){var h=32767&this[t],u=this[t++]>>15,c=a*h+u*o;i=((h=o*h+((32767&c)<<15)+r[n]+(1073741823&i))>>>30)+(c>>>15)+a*u+(i>>>30),r[n++]=1073741823&h}return i},E=30):\"Netscape\"!=navigator.appName?(B.prototype.am=function(t,e,r,n,i,s){for(;0<=--s;){var o=e*this[t++]+r[n]+i;i=Math.floor(o/67108864),r[n++]=67108863&o}return i},E=26):(B.prototype.am=function(t,e,r,n,i,s){for(var o=16383&e,a=e>>14;0<=--s;){var h=16383&this[t],u=this[t++]>>14,c=a*h+u*o;i=((h=o*h+((16383&c)<<14)+r[n]+i)>>28)+(c>>14)+a*u,r[n++]=268435455&h}return i},E=28),B.prototype.DB=E,B.prototype.DM=(1<<E)-1,B.prototype.DV=1<<E,B.prototype.FV=Math.pow(2,52),B.prototype.F1=52-E,B.prototype.F2=2*E-52;var M,q,L=[];for(M=\"0\".charCodeAt(0),q=0;q<=9;++q)L[M++]=q;for(M=\"a\".charCodeAt(0),q=10;q<36;++q)L[M++]=q;for(M=\"A\".charCodeAt(0),q=10;q<36;++q)L[M++]=q;function C(t,e){var r=L[t.charCodeAt(e)];return null==r?-1:r}function H(t){var e=P();return e.fromInt(t),e}function F(t){var e,r=1;return 0!=(e=t>>>16)&&(t=e,r+=16),0!=(e=t>>8)&&(t=e,r+=8),0!=(e=t>>4)&&(t=e,r+=4),0!=(e=t>>2)&&(t=e,r+=2),0!=(e=t>>1)&&(t=e,r+=1),r}B.ZERO=H(0),B.ONE=H(1);var U,k,K=function(){function t(){this.i=0,this.j=0,this.S=[]}return t.prototype.init=function(t){var e,r,n;for(e=0;e<256;++e)this.S[e]=e;for(e=r=0;e<256;++e)r=r+this.S[e]+t[e%t.length]&255,n=this.S[e],this.S[e]=this.S[r],this.S[r]=n;this.i=0,this.j=0},t.prototype.next=function(){var t;return this.i=this.i+1&255,this.j=this.j+this.S[this.i]&255,t=this.S[this.i],this.S[this.i]=this.S[this.j],this.S[this.j]=t,this.S[t+this.S[this.i]&255]},t}(),z=256,Z=null;if(null==Z){Z=[];var $=void(k=0);if(window.crypto&&window.crypto.getRandomValues){var G=new Uint32Array(256);for(window.crypto.getRandomValues(G),$=0;$<G.length;++$)Z[k++]=255&G[$]}var J=function(t){if(this.count=this.count||0,256<=this.count||z<=k)window.removeEventListener?window.removeEventListener(\"mousemove\",J,!1):window.detachEvent&&window.detachEvent(\"onmousemove\",J);else try{var e=t.x+t.y;Z[k++]=255&e,this.count+=1}catch(t){}};window.addEventListener?window.addEventListener(\"mousemove\",J,!1):window.attachEvent&&window.attachEvent(\"onmousemove\",J)}function W(){if(null==U){for(U=new K;k<z;){var t=Math.floor(65536*Math.random());Z[k++]=255&t}for(U.init(Z),k=0;k<Z.length;++k)Z[k]=0;k=0}return U.next()}var Y=function(){function t(){}return t.prototype.nextBytes=function(t){for(var e=0;e<t.length;++e)t[e]=W()},t}(),X=function(){function t(){this.n=null,this.e=0,this.d=null,this.p=null,this.q=null,this.dmp1=null,this.dmq1=null,this.coeff=null}return t.prototype.doPublic=function(t){return t.modPowInt(this.e,this.n)},t.prototype.doPrivate=function(t){if(null==this.p||null==this.q)return t.modPow(this.d,this.n);for(var e=t.mod(this.p).modPow(this.dmp1,this.p),r=t.mod(this.q).modPow(this.dmq1,this.q);e.compareTo(r)<0;)e=e.add(this.p);return e.subtract(r).multiply(this.coeff).mod(this.p).multiply(this.q).add(r)},t.prototype.setPublic=function(t,e){null!=t&&null!=e&&0<t.length&&0<e.length?(this.n=N(t,16),this.e=parseInt(e,16)):console.error(\"Invalid RSA public key\")},t.prototype.encrypt=function(t){var e=function(t,e){if(e<t.length+11)return console.error(\"Message too long for RSA\"),null;for(var r=[],n=t.length-1;0<=n&&0<e;){var i=t.charCodeAt(n--);i<128?r[--e]=i:127<i&&i<2048?(r[--e]=63&i|128,r[--e]=i>>6|192):(r[--e]=63&i|128,r[--e]=i>>6&63|128,r[--e]=i>>12|224)}r[--e]=0;for(var s=new Y,o=[];2<e;){for(o[0]=0;0==o[0];)s.nextBytes(o);r[--e]=o[0]}return r[--e]=2,r[--e]=0,new B(r)}(t,this.n.bitLength()+7>>3);if(null==e)return null;var r=this.doPublic(e);if(null==r)return null;var n=r.toString(16);return 0==(1&n.length)?n:\"0\"+n},t.prototype.setPrivate=function(t,e,r){null!=t&&null!=e&&0<t.length&&0<e.length?(this.n=N(t,16),this.e=parseInt(e,16),this.d=N(r,16)):console.error(\"Invalid RSA private key\")},t.prototype.setPrivateEx=function(t,e,r,n,i,s,o,a){null!=t&&null!=e&&0<t.length&&0<e.length?(this.n=N(t,16),this.e=parseInt(e,16),this.d=N(r,16),this.p=N(n,16),this.q=N(i,16),this.dmp1=N(s,16),this.dmq1=N(o,16),this.coeff=N(a,16)):console.error(\"Invalid RSA private key\")},t.prototype.generate=function(t,e){var r=new Y,n=t>>1;this.e=parseInt(e,16);for(var i=new B(e,16);;){for(;this.p=new B(t-n,1,r),0!=this.p.subtract(B.ONE).gcd(i).compareTo(B.ONE)||!this.p.isProbablePrime(10););for(;this.q=new B(n,1,r),0!=this.q.subtract(B.ONE).gcd(i).compareTo(B.ONE)||!this.q.isProbablePrime(10););if(this.p.compareTo(this.q)<=0){var s=this.p;this.p=this.q,this.q=s}var o=this.p.subtract(B.ONE),a=this.q.subtract(B.ONE),h=o.multiply(a);if(0==h.gcd(i).compareTo(B.ONE)){this.n=this.p.multiply(this.q),this.d=i.modInverse(h),this.dmp1=this.d.mod(o),this.dmq1=this.d.mod(a),this.coeff=this.q.modInverse(this.p);break}}},t.prototype.decrypt=function(t){var e=N(t,16),r=this.doPrivate(e);return null==r?null:function(t,e){for(var r=t.toByteArray(),n=0;n<r.length&&0==r[n];)++n;if(r.length-n!=e-1||2!=r[n])return null;for(++n;0!=r[n];)if(++n>=r.length)return null;for(var i=\"\";++n<r.length;){var s=255&r[n];s<128?i+=String.fromCharCode(s):191<s&&s<224?(i+=String.fromCharCode((31&s)<<6|63&r[n+1]),++n):(i+=String.fromCharCode((15&s)<<12|(63&r[n+1])<<6|63&r[n+2]),n+=2)}return i}(r,this.n.bitLength()+7>>3)},t.prototype.generateAsync=function(t,e,r){var n=new Y,i=t>>1;this.e=parseInt(e,16);var s=new B(e,16),o=this,a=function(){var e=function(){if(o.p.compareTo(o.q)<=0){var t=o.p;o.p=o.q,o.q=t}var e=o.p.subtract(B.ONE),n=o.q.subtract(B.ONE),i=e.multiply(n);0==i.gcd(s).compareTo(B.ONE)?(o.n=o.p.multiply(o.q),o.d=s.modInverse(i),o.dmp1=o.d.mod(e),o.dmq1=o.d.mod(n),o.coeff=o.q.modInverse(o.p),setTimeout((function(){r()}),0)):setTimeout(a,0)},h=function(){o.q=P(),o.q.fromNumberAsync(i,1,n,(function(){o.q.subtract(B.ONE).gcda(s,(function(t){0==t.compareTo(B.ONE)&&o.q.isProbablePrime(10)?setTimeout(e,0):setTimeout(h,0)}))}))},u=function(){o.p=P(),o.p.fromNumberAsync(t-i,1,n,(function(){o.p.subtract(B.ONE).gcda(s,(function(t){0==t.compareTo(B.ONE)&&o.p.isProbablePrime(10)?setTimeout(h,0):setTimeout(u,0)}))}))};setTimeout(u,0)};setTimeout(a,0)},t.prototype.sign=function(t,e,r){var n=function(t,e){if(e<t.length+22)return console.error(\"Message too long for RSA\"),null;for(var r=e-t.length-6,n=\"\",i=0;i<r;i+=2)n+=\"ff\";return N(\"0001\"+n+\"00\"+t,16)}((Q[r]||\"\")+e(t).toString(),this.n.bitLength()/4);if(null==n)return null;var i=this.doPrivate(n);if(null==i)return null;var s=i.toString(16);return 0==(1&s.length)?s:\"0\"+s},t.prototype.verify=function(t,e,r){var n=N(e,16),i=this.doPublic(n);return null==i?null:function(t){for(var e in Q)if(Q.hasOwnProperty(e)){var r=Q[e],n=r.length;if(t.substr(0,n)==r)return t.substr(n)}return t}(i.toString(16).replace(/^1f+00/,\"\"))==r(t).toString()},t}(),Q={md2:\"3020300c06082a864886f70d020205000410\",md5:\"3020300c06082a864886f70d020505000410\",sha1:\"3021300906052b0e03021a05000414\",sha224:\"302d300d06096086480165030402040500041c\",sha256:\"3031300d060960864801650304020105000420\",sha384:\"3041300d060960864801650304020205000430\",sha512:\"3051300d060960864801650304020305000440\",ripemd160:\"3021300906052b2403020105000414\"},tt={};tt.lang={extend:function(t,e,r){if(!e||!t)throw new Error(\"YAHOO.lang.extend failed, please check that all dependencies are included.\");var n=function(){};if(n.prototype=e.prototype,t.prototype=new n,(t.prototype.constructor=t).superclass=e.prototype,e.prototype.constructor==Object.prototype.constructor&&(e.prototype.constructor=e),r){var i;for(i in r)t.prototype[i]=r[i];var s=function(){},o=[\"toString\",\"valueOf\"];try{/MSIE/.test(navigator.userAgent)&&(s=function(t,e){for(i=0;i<o.length;i+=1){var r=o[i],n=e[r];\"function\"==typeof n&&n!=Object.prototype[r]&&(t[r]=n)}})}catch(t){}s(t.prototype,r)}}};var et={};void 0!==et.asn1&&et.asn1||(et.asn1={}),et.asn1.ASN1Util=new function(){this.integerToByteHex=function(t){var e=t.toString(16);return e.length%2==1&&(e=\"0\"+e),e},this.bigIntToMinTwosComplementsHex=function(t){var e=t.toString(16);if(\"-\"!=e.substr(0,1))e.length%2==1?e=\"0\"+e:e.match(/^[0-7]/)||(e=\"00\"+e);else{var r=e.substr(1).length;r%2==1?r+=1:e.match(/^[0-7]/)||(r+=2);for(var n=\"\",i=0;i<r;i++)n+=\"f\";e=new B(n,16).xor(t).add(B.ONE).toString(16).replace(/^-/,\"\")}return e},this.getPEMStringFromHex=function(t,e){return hextopem(t,e)},this.newObject=function(t){var e=et.asn1,r=e.DERBoolean,n=e.DERInteger,i=e.DERBitString,s=e.DEROctetString,o=e.DERNull,a=e.DERObjectIdentifier,h=e.DEREnumerated,u=e.DERUTF8String,c=e.DERNumericString,f=e.DERPrintableString,l=e.DERTeletexString,p=e.DERIA5String,g=e.DERUTCTime,d=e.DERGeneralizedTime,v=e.DERSequence,y=e.DERSet,b=e.DERTaggedObject,m=e.ASN1Util.newObject,S=Object.keys(t);if(1!=S.length)throw\"key of param shall be only one.\";var T=S[0];if(-1==\":bool:int:bitstr:octstr:null:oid:enum:utf8str:numstr:prnstr:telstr:ia5str:utctime:gentime:seq:set:tag:\".indexOf(\":\"+T+\":\"))throw\"undefined key: \"+T;if(\"bool\"==T)return new r(t[T]);if(\"int\"==T)return new n(t[T]);if(\"bitstr\"==T)return new i(t[T]);if(\"octstr\"==T)return new s(t[T]);if(\"null\"==T)return new o(t[T]);if(\"oid\"==T)return new a(t[T]);if(\"enum\"==T)return new h(t[T]);if(\"utf8str\"==T)return new u(t[T]);if(\"numstr\"==T)return new c(t[T]);if(\"prnstr\"==T)return new f(t[T]);if(\"telstr\"==T)return new l(t[T]);if(\"ia5str\"==T)return new p(t[T]);if(\"utctime\"==T)return new g(t[T]);if(\"gentime\"==T)return new d(t[T]);if(\"seq\"==T){for(var w=t[T],E=[],D=0;D<w.length;D++){var j=m(w[D]);E.push(j)}return new v({array:E})}if(\"set\"==T){for(w=t[T],E=[],D=0;D<w.length;D++)j=m(w[D]),E.push(j);return new y({array:E})}if(\"tag\"==T){var O=t[T];if(\"[object Array]\"===Object.prototype.toString.call(O)&&3==O.length){var x=m(O[2]);return new b({tag:O[0],explicit:O[1],obj:x})}var A={};if(void 0!==O.explicit&&(A.explicit=O.explicit),void 0!==O.tag&&(A.tag=O.tag),void 0===O.obj)throw\"obj shall be specified for 'tag'.\";return A.obj=m(O.obj),new b(A)}},this.jsonToASN1HEX=function(t){return this.newObject(t).getEncodedHex()}},et.asn1.ASN1Util.oidHexToInt=function(t){for(var e=\"\",r=parseInt(t.substr(0,2),16),n=(e=Math.floor(r/40)+\".\"+r%40,\"\"),i=2;i<t.length;i+=2){var s=(\"00000000\"+parseInt(t.substr(i,2),16).toString(2)).slice(-8);n+=s.substr(1,7),\"0\"==s.substr(0,1)&&(e=e+\".\"+new B(n,2).toString(10),n=\"\")}return e},et.asn1.ASN1Util.oidIntToHex=function(t){var e=function(t){var e=t.toString(16);return 1==e.length&&(e=\"0\"+e),e},r=function(t){var r=\"\",n=new B(t,10).toString(2),i=7-n.length%7;7==i&&(i=0);for(var s=\"\",o=0;o<i;o++)s+=\"0\";for(n=s+n,o=0;o<n.length-1;o+=7){var a=n.substr(o,7);o!=n.length-7&&(a=\"1\"+a),r+=e(parseInt(a,2))}return r};if(!t.match(/^[0-9.]+$/))throw\"malformed oid string: \"+t;var n=\"\",i=t.split(\".\"),s=40*parseInt(i[0])+parseInt(i[1]);n+=e(s),i.splice(0,2);for(var o=0;o<i.length;o++)n+=r(i[o]);return n},et.asn1.ASN1Object=function(){this.getLengthHexFromValue=function(){if(void 0===this.hV||null==this.hV)throw\"this.hV is null or undefined.\";if(this.hV.length%2==1)throw\"value hex must be even length: n=\"+\"\".length+\",v=\"+this.hV;var t=this.hV.length/2,e=t.toString(16);if(e.length%2==1&&(e=\"0\"+e),t<128)return e;var r=e.length/2;if(15<r)throw\"ASN.1 length too long to represent by 8x: n = \"+t.toString(16);return(128+r).toString(16)+e},this.getEncodedHex=function(){return(null==this.hTLV||this.isModified)&&(this.hV=this.getFreshValueHex(),this.hL=this.getLengthHexFromValue(),this.hTLV=this.hT+this.hL+this.hV,this.isModified=!1),this.hTLV},this.getValueHex=function(){return this.getEncodedHex(),this.hV},this.getFreshValueHex=function(){return\"\"}},et.asn1.DERAbstractString=function(t){et.asn1.DERAbstractString.superclass.constructor.call(this),this.getString=function(){return this.s},this.setString=function(t){this.hTLV=null,this.isModified=!0,this.s=t,this.hV=stohex(this.s)},this.setStringHex=function(t){this.hTLV=null,this.isModified=!0,this.s=null,this.hV=t},this.getFreshValueHex=function(){return this.hV},void 0!==t&&(\"string\"==typeof t?this.setString(t):void 0!==t.str?this.setString(t.str):void 0!==t.hex&&this.setStringHex(t.hex))},tt.lang.extend(et.asn1.DERAbstractString,et.asn1.ASN1Object),et.asn1.DERAbstractTime=function(t){et.asn1.DERAbstractTime.superclass.constructor.call(this),this.localDateToUTC=function(t){return utc=t.getTime()+6e4*t.getTimezoneOffset(),new Date(utc)},this.formatDate=function(t,e,r){var n=this.zeroPadding,i=this.localDateToUTC(t),s=String(i.getFullYear());\"utc\"==e&&(s=s.substr(2,2));var o=s+n(String(i.getMonth()+1),2)+n(String(i.getDate()),2)+n(String(i.getHours()),2)+n(String(i.getMinutes()),2)+n(String(i.getSeconds()),2);if(!0===r){var a=i.getMilliseconds();if(0!=a){var h=n(String(a),3);o=o+\".\"+(h=h.replace(/[0]+$/,\"\"))}}return o+\"Z\"},this.zeroPadding=function(t,e){return t.length>=e?t:new Array(e-t.length+1).join(\"0\")+t},this.getString=function(){return this.s},this.setString=function(t){this.hTLV=null,this.isModified=!0,this.s=t,this.hV=stohex(t)},this.setByDateValue=function(t,e,r,n,i,s){var o=new Date(Date.UTC(t,e-1,r,n,i,s,0));this.setByDate(o)},this.getFreshValueHex=function(){return this.hV}},tt.lang.extend(et.asn1.DERAbstractTime,et.asn1.ASN1Object),et.asn1.DERAbstractStructured=function(t){et.asn1.DERAbstractString.superclass.constructor.call(this),this.setByASN1ObjectArray=function(t){this.hTLV=null,this.isModified=!0,this.asn1Array=t},this.appendASN1Object=function(t){this.hTLV=null,this.isModified=!0,this.asn1Array.push(t)},this.asn1Array=new Array,void 0!==t&&void 0!==t.array&&(this.asn1Array=t.array)},tt.lang.extend(et.asn1.DERAbstractStructured,et.asn1.ASN1Object),et.asn1.DERBoolean=function(){et.asn1.DERBoolean.superclass.constructor.call(this),this.hT=\"01\",this.hTLV=\"0101ff\"},tt.lang.extend(et.asn1.DERBoolean,et.asn1.ASN1Object),et.asn1.DERInteger=function(t){et.asn1.DERInteger.superclass.constructor.call(this),this.hT=\"02\",this.setByBigInteger=function(t){this.hTLV=null,this.isModified=!0,this.hV=et.asn1.ASN1Util.bigIntToMinTwosComplementsHex(t)},this.setByInteger=function(t){var e=new B(String(t),10);this.setByBigInteger(e)},this.setValueHex=function(t){this.hV=t},this.getFreshValueHex=function(){return this.hV},void 0!==t&&(void 0!==t.bigint?this.setByBigInteger(t.bigint):void 0!==t.int?this.setByInteger(t.int):\"number\"==typeof t?this.setByInteger(t):void 0!==t.hex&&this.setValueHex(t.hex))},tt.lang.extend(et.asn1.DERInteger,et.asn1.ASN1Object),et.asn1.DERBitString=function(t){if(void 0!==t&&void 0!==t.obj){var e=et.asn1.ASN1Util.newObject(t.obj);t.hex=\"00\"+e.getEncodedHex()}et.asn1.DERBitString.superclass.constructor.call(this),this.hT=\"03\",this.setHexValueIncludingUnusedBits=function(t){this.hTLV=null,this.isModified=!0,this.hV=t},this.setUnusedBitsAndHexValue=function(t,e){if(t<0||7<t)throw\"unused bits shall be from 0 to 7: u = \"+t;var r=\"0\"+t;this.hTLV=null,this.isModified=!0,this.hV=r+e},this.setByBinaryString=function(t){var e=8-(t=t.replace(/0+$/,\"\")).length%8;8==e&&(e=0);for(var r=0;r<=e;r++)t+=\"0\";var n=\"\";for(r=0;r<t.length-1;r+=8){var i=t.substr(r,8),s=parseInt(i,2).toString(16);1==s.length&&(s=\"0\"+s),n+=s}this.hTLV=null,this.isModified=!0,this.hV=\"0\"+e+n},this.setByBooleanArray=function(t){for(var e=\"\",r=0;r<t.length;r++)1==t[r]?e+=\"1\":e+=\"0\";this.setByBinaryString(e)},this.newFalseArray=function(t){for(var e=new Array(t),r=0;r<t;r++)e[r]=!1;return e},this.getFreshValueHex=function(){return this.hV},void 0!==t&&(\"string\"==typeof t&&t.toLowerCase().match(/^[0-9a-f]+$/)?this.setHexValueIncludingUnusedBits(t):void 0!==t.hex?this.setHexValueIncludingUnusedBits(t.hex):void 0!==t.bin?this.setByBinaryString(t.bin):void 0!==t.array&&this.setByBooleanArray(t.array))},tt.lang.extend(et.asn1.DERBitString,et.asn1.ASN1Object),et.asn1.DEROctetString=function(t){if(void 0!==t&&void 0!==t.obj){var e=et.asn1.ASN1Util.newObject(t.obj);t.hex=e.getEncodedHex()}et.asn1.DEROctetString.superclass.constructor.call(this,t),this.hT=\"04\"},tt.lang.extend(et.asn1.DEROctetString,et.asn1.DERAbstractString),et.asn1.DERNull=function(){et.asn1.DERNull.superclass.constructor.call(this),this.hT=\"05\",this.hTLV=\"0500\"},tt.lang.extend(et.asn1.DERNull,et.asn1.ASN1Object),et.asn1.DERObjectIdentifier=function(t){var e=function(t){var e=t.toString(16);return 1==e.length&&(e=\"0\"+e),e},r=function(t){var r=\"\",n=new B(t,10).toString(2),i=7-n.length%7;7==i&&(i=0);for(var s=\"\",o=0;o<i;o++)s+=\"0\";for(n=s+n,o=0;o<n.length-1;o+=7){var a=n.substr(o,7);o!=n.length-7&&(a=\"1\"+a),r+=e(parseInt(a,2))}return r};et.asn1.DERObjectIdentifier.superclass.constructor.call(this),this.hT=\"06\",this.setValueHex=function(t){this.hTLV=null,this.isModified=!0,this.s=null,this.hV=t},this.setValueOidString=function(t){if(!t.match(/^[0-9.]+$/))throw\"malformed oid string: \"+t;var n=\"\",i=t.split(\".\"),s=40*parseInt(i[0])+parseInt(i[1]);n+=e(s),i.splice(0,2);for(var o=0;o<i.length;o++)n+=r(i[o]);this.hTLV=null,this.isModified=!0,this.s=null,this.hV=n},this.setValueName=function(t){var e=et.asn1.x509.OID.name2oid(t);if(\"\"===e)throw\"DERObjectIdentifier oidName undefined: \"+t;this.setValueOidString(e)},this.getFreshValueHex=function(){return this.hV},void 0!==t&&(\"string\"==typeof t?t.match(/^[0-2].[0-9.]+$/)?this.setValueOidString(t):this.setValueName(t):void 0!==t.oid?this.setValueOidString(t.oid):void 0!==t.hex?this.setValueHex(t.hex):void 0!==t.name&&this.setValueName(t.name))},tt.lang.extend(et.asn1.DERObjectIdentifier,et.asn1.ASN1Object),et.asn1.DEREnumerated=function(t){et.asn1.DEREnumerated.superclass.constructor.call(this),this.hT=\"0a\",this.setByBigInteger=function(t){this.hTLV=null,this.isModified=!0,this.hV=et.asn1.ASN1Util.bigIntToMinTwosComplementsHex(t)},this.setByInteger=function(t){var e=new B(String(t),10);this.setByBigInteger(e)},this.setValueHex=function(t){this.hV=t},this.getFreshValueHex=function(){return this.hV},void 0!==t&&(void 0!==t.int?this.setByInteger(t.int):\"number\"==typeof t?this.setByInteger(t):void 0!==t.hex&&this.setValueHex(t.hex))},tt.lang.extend(et.asn1.DEREnumerated,et.asn1.ASN1Object),et.asn1.DERUTF8String=function(t){et.asn1.DERUTF8String.superclass.constructor.call(this,t),this.hT=\"0c\"},tt.lang.extend(et.asn1.DERUTF8String,et.asn1.DERAbstractString),et.asn1.DERNumericString=function(t){et.asn1.DERNumericString.superclass.constructor.call(this,t),this.hT=\"12\"},tt.lang.extend(et.asn1.DERNumericString,et.asn1.DERAbstractString),et.asn1.DERPrintableString=function(t){et.asn1.DERPrintableString.superclass.constructor.call(this,t),this.hT=\"13\"},tt.lang.extend(et.asn1.DERPrintableString,et.asn1.DERAbstractString),et.asn1.DERTeletexString=function(t){et.asn1.DERTeletexString.superclass.constructor.call(this,t),this.hT=\"14\"},tt.lang.extend(et.asn1.DERTeletexString,et.asn1.DERAbstractString),et.asn1.DERIA5String=function(t){et.asn1.DERIA5String.superclass.constructor.call(this,t),this.hT=\"16\"},tt.lang.extend(et.asn1.DERIA5String,et.asn1.DERAbstractString),et.asn1.DERUTCTime=function(t){et.asn1.DERUTCTime.superclass.constructor.call(this,t),this.hT=\"17\",this.setByDate=function(t){this.hTLV=null,this.isModified=!0,this.date=t,this.s=this.formatDate(this.date,\"utc\"),this.hV=stohex(this.s)},this.getFreshValueHex=function(){return void 0===this.date&&void 0===this.s&&(this.date=new Date,this.s=this.formatDate(this.date,\"utc\"),this.hV=stohex(this.s)),this.hV},void 0!==t&&(void 0!==t.str?this.setString(t.str):\"string\"==typeof t&&t.match(/^[0-9]{12}Z$/)?this.setString(t):void 0!==t.hex?this.setStringHex(t.hex):void 0!==t.date&&this.setByDate(t.date))},tt.lang.extend(et.asn1.DERUTCTime,et.asn1.DERAbstractTime),et.asn1.DERGeneralizedTime=function(t){et.asn1.DERGeneralizedTime.superclass.constructor.call(this,t),this.hT=\"18\",this.withMillis=!1,this.setByDate=function(t){this.hTLV=null,this.isModified=!0,this.date=t,this.s=this.formatDate(this.date,\"gen\",this.withMillis),this.hV=stohex(this.s)},this.getFreshValueHex=function(){return void 0===this.date&&void 0===this.s&&(this.date=new Date,this.s=this.formatDate(this.date,\"gen\",this.withMillis),this.hV=stohex(this.s)),this.hV},void 0!==t&&(void 0!==t.str?this.setString(t.str):\"string\"==typeof t&&t.match(/^[0-9]{14}Z$/)?this.setString(t):void 0!==t.hex?this.setStringHex(t.hex):void 0!==t.date&&this.setByDate(t.date),!0===t.millis&&(this.withMillis=!0))},tt.lang.extend(et.asn1.DERGeneralizedTime,et.asn1.DERAbstractTime),et.asn1.DERSequence=function(t){et.asn1.DERSequence.superclass.constructor.call(this,t),this.hT=\"30\",this.getFreshValueHex=function(){for(var t=\"\",e=0;e<this.asn1Array.length;e++)t+=this.asn1Array[e].getEncodedHex();return this.hV=t,this.hV}},tt.lang.extend(et.asn1.DERSequence,et.asn1.DERAbstractStructured),et.asn1.DERSet=function(t){et.asn1.DERSet.superclass.constructor.call(this,t),this.hT=\"31\",this.sortFlag=!0,this.getFreshValueHex=function(){for(var t=new Array,e=0;e<this.asn1Array.length;e++){var r=this.asn1Array[e];t.push(r.getEncodedHex())}return 1==this.sortFlag&&t.sort(),this.hV=t.join(\"\"),this.hV},void 0!==t&&void 0!==t.sortflag&&0==t.sortflag&&(this.sortFlag=!1)},tt.lang.extend(et.asn1.DERSet,et.asn1.DERAbstractStructured),et.asn1.DERTaggedObject=function(t){et.asn1.DERTaggedObject.superclass.constructor.call(this),this.hT=\"a0\",this.hV=\"\",this.isExplicit=!0,this.asn1Object=null,this.setASN1Object=function(t,e,r){this.hT=e,this.isExplicit=t,this.asn1Object=r,this.isExplicit?(this.hV=this.asn1Object.getEncodedHex(),this.hTLV=null,this.isModified=!0):(this.hV=null,this.hTLV=r.getEncodedHex(),this.hTLV=this.hTLV.replace(/^../,e),this.isModified=!1)},this.getFreshValueHex=function(){return this.hV},void 0!==t&&(void 0!==t.tag&&(this.hT=t.tag),void 0!==t.explicit&&(this.isExplicit=t.explicit),void 0!==t.obj&&(this.asn1Object=t.obj,this.setASN1Object(this.isExplicit,this.hT,this.asn1Object)))},tt.lang.extend(et.asn1.DERTaggedObject,et.asn1.ASN1Object);var rt=function(t){function e(r){var n=t.call(this)||this;return r&&(\"string\"==typeof r?n.parseKey(r):(e.hasPrivateKeyProperty(r)||e.hasPublicKeyProperty(r))&&n.parsePropertiesFrom(r)),n}return function(t,e){function r(){this.constructor=t}g(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}(e,t),e.prototype.parseKey=function(t){try{var e=0,r=0,n=/^\\s*(?:[0-9A-Fa-f][0-9A-Fa-f]\\s*)+$/.test(t)?d(t):v.unarmor(t),i=j.decode(n);if(3===i.sub.length&&(i=i.sub[2].sub[0]),9===i.sub.length){e=i.sub[1].getHexStringValue(),this.n=N(e,16),r=i.sub[2].getHexStringValue(),this.e=parseInt(r,16);var s=i.sub[3].getHexStringValue();this.d=N(s,16);var o=i.sub[4].getHexStringValue();this.p=N(o,16);var a=i.sub[5].getHexStringValue();this.q=N(a,16);var h=i.sub[6].getHexStringValue();this.dmp1=N(h,16);var u=i.sub[7].getHexStringValue();this.dmq1=N(u,16);var c=i.sub[8].getHexStringValue();this.coeff=N(c,16)}else{if(2!==i.sub.length)return!1;var f=i.sub[1].sub[0];e=f.sub[0].getHexStringValue(),this.n=N(e,16),r=f.sub[1].getHexStringValue(),this.e=parseInt(r,16)}return!0}catch(t){return!1}},e.prototype.getPrivateBaseKey=function(){var t={array:[new et.asn1.DERInteger({int:0}),new et.asn1.DERInteger({bigint:this.n}),new et.asn1.DERInteger({int:this.e}),new et.asn1.DERInteger({bigint:this.d}),new et.asn1.DERInteger({bigint:this.p}),new et.asn1.DERInteger({bigint:this.q}),new et.asn1.DERInteger({bigint:this.dmp1}),new et.asn1.DERInteger({bigint:this.dmq1}),new et.asn1.DERInteger({bigint:this.coeff})]};return new et.asn1.DERSequence(t).getEncodedHex()},e.prototype.getPrivateBaseKeyB64=function(){return c(this.getPrivateBaseKey())},e.prototype.getPublicBaseKey=function(){var t=new et.asn1.DERSequence({array:[new et.asn1.DERObjectIdentifier({oid:\"1.2.840.113549.1.1.1\"}),new et.asn1.DERNull]}),e=new et.asn1.DERSequence({array:[new et.asn1.DERInteger({bigint:this.n}),new et.asn1.DERInteger({int:this.e})]}),r=new et.asn1.DERBitString({hex:\"00\"+e.getEncodedHex()});return new et.asn1.DERSequence({array:[t,r]}).getEncodedHex()},e.prototype.getPublicBaseKeyB64=function(){return c(this.getPublicBaseKey())},e.wordwrap=function(t,e){if(!t)return t;var r=\"(.{1,\"+(e=e||64)+\"})( +|$\\n?)|(.{1,\"+e+\"})\";return t.match(RegExp(r,\"g\")).join(\"\\n\")},e.prototype.getPrivateKey=function(){var t=\"-----BEGIN RSA PRIVATE KEY-----\\n\";return t+=e.wordwrap(this.getPrivateBaseKeyB64())+\"\\n\",t+\"-----END RSA PRIVATE KEY-----\"},e.prototype.getPublicKey=function(){var t=\"-----BEGIN PUBLIC KEY-----\\n\";return t+=e.wordwrap(this.getPublicBaseKeyB64())+\"\\n\",t+\"-----END PUBLIC KEY-----\"},e.hasPublicKeyProperty=function(t){return(t=t||{}).hasOwnProperty(\"n\")&&t.hasOwnProperty(\"e\")},e.hasPrivateKeyProperty=function(t){return(t=t||{}).hasOwnProperty(\"n\")&&t.hasOwnProperty(\"e\")&&t.hasOwnProperty(\"d\")&&t.hasOwnProperty(\"p\")&&t.hasOwnProperty(\"q\")&&t.hasOwnProperty(\"dmp1\")&&t.hasOwnProperty(\"dmq1\")&&t.hasOwnProperty(\"coeff\")},e.prototype.parsePropertiesFrom=function(t){this.n=t.n,this.e=t.e,t.hasOwnProperty(\"d\")&&(this.d=t.d,this.p=t.p,this.q=t.q,this.dmp1=t.dmp1,this.dmq1=t.dmq1,this.coeff=t.coeff)},e}(X),nt=function(){function t(t){t=t||{},this.default_key_size=parseInt(t.default_key_size,10)||1024,this.default_public_exponent=t.default_public_exponent||\"010001\",this.log=t.log||!1,this.key=null}return t.prototype.setKey=function(t){this.log&&this.key&&console.warn(\"A key was already set, overriding existing.\"),this.key=new rt(t)},t.prototype.setPrivateKey=function(t){this.setKey(t)},t.prototype.setPublicKey=function(t){this.setKey(t)},t.prototype.decrypt=function(t){try{return this.getKey().decrypt(f(t))}catch(t){return!1}},t.prototype.encrypt=function(t){try{return c(this.getKey().encrypt(t))}catch(t){return!1}},t.prototype.sign=function(t,e,r){try{return c(this.getKey().sign(t,e,r))}catch(t){return!1}},t.prototype.verify=function(t,e,r){try{return this.getKey().verify(t,f(e),r)}catch(t){return!1}},t.prototype.getKey=function(t){if(!this.key){if(this.key=new rt,t&&\"[object Function]\"==={}.toString.call(t))return void this.key.generateAsync(this.default_key_size,this.default_public_exponent,t);this.key.generate(this.default_key_size,this.default_public_exponent)}return this.key},t.prototype.getPrivateKey=function(){return this.getKey().getPrivateKey()},t.prototype.getPrivateKeyB64=function(){return this.getKey().getPrivateBaseKeyB64()},t.prototype.getPublicKey=function(){return this.getKey().getPublicKey()},t.prototype.getPublicKeyB64=function(){return this.getKey().getPublicBaseKeyB64()},t.version=\"3.0.0-rc.1\",t}();window.JSEncrypt=nt,t.JSEncrypt=nt,t.default=nt,Object.defineProperty(t,\"__esModule\",{value:!0})}))},ef6c:function(t,e,r){!function(e,r){t.exports=r()}(window,(function(){return function(t){var e={};function r(n){if(e[n])return e[n].exports;var i=e[n]={i:n,l:!1,exports:{}};return t[n].call(i.exports,i,i.exports,r),i.l=!0,i.exports}return r.m=t,r.c=e,r.d=function(t,e,n){r.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:n})},r.r=function(t){\"undefined\"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:\"Module\"}),Object.defineProperty(t,\"__esModule\",{value:!0})},r.t=function(t,e){if(1&e&&(t=r(t)),8&e)return t;if(4&e&&\"object\"==typeof t&&t&&t.__esModule)return t;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,\"default\",{enumerable:!0,value:t}),2&e&&\"string\"!=typeof t)for(var i in t)r.d(n,i,function(e){return t[e]}.bind(null,i));return n},r.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return r.d(e,\"a\",e),e},r.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},r.p=\"\",r(r.s=10)}([function(t,e,r){\"use strict\";var n=r(3),i=\"object\"==typeof self&&self&&self.Object===Object&&self,s=n.a||i||Function(\"return this\")();e.a=s},function(t,e,r){t.exports=r(8)},function(t,r,n){\"use strict\";(function(t){var i=n(3),s=e&&!e.nodeType&&e,o=s&&\"object\"==typeof t&&t&&!t.nodeType&&t,a=o&&o.exports===s&&i.a.process,h=function(){try{var t=o&&o.require&&o.require(\"util\").types;return t||a&&a.binding&&a.binding(\"util\")}catch(t){}}();r.a=h}).call(this,n(5)(t))},function(t,e,r){\"use strict\";(function(t){var r=\"object\"==typeof t&&t&&t.Object===Object&&t;e.a=r}).call(this,r(9))},function(t,r,n){\"use strict\";(function(t){var i=n(0),s=n(6),o=e&&!e.nodeType&&e,a=o&&\"object\"==typeof t&&t&&!t.nodeType&&t,h=a&&a.exports===o?i.a.Buffer:void 0,u=(h?h.isBuffer:void 0)||s.a;r.a=u}).call(this,n(5)(t))},function(t,e){t.exports=function(t){if(!t.webpackPolyfill){var e=Object.create(t);e.children||(e.children=[]),Object.defineProperty(e,\"loaded\",{enumerable:!0,get:function(){return e.l}}),Object.defineProperty(e,\"id\",{enumerable:!0,get:function(){return e.i}}),Object.defineProperty(e,\"exports\",{enumerable:!0}),e.webpackPolyfill=1}return e}},function(t,e,r){\"use strict\";e.a=function(){return!1}},function(t,r,n){\"use strict\";(function(t){var i=n(0),s=e&&!e.nodeType&&e,o=s&&\"object\"==typeof t&&t&&!t.nodeType&&t,a=o&&o.exports===s?i.a.Buffer:void 0,h=a?a.allocUnsafe:void 0;r.a=function(t,e){if(e)return t.slice();var r=t.length,n=h?h(r):new t.constructor(r);return t.copy(n),n}}).call(this,n(5)(t))},function(t){t.exports=JSON.parse('{\"86\":{\"110000\":\"北京市\",\"120000\":\"天津市\",\"130000\":\"河北省\",\"140000\":\"山西省\",\"150000\":\"内蒙古自治区\",\"210000\":\"辽宁省\",\"220000\":\"吉林省\",\"230000\":\"黑龙江省\",\"310000\":\"上海市\",\"320000\":\"江苏省\",\"330000\":\"浙江省\",\"340000\":\"安徽省\",\"350000\":\"福建省\",\"360000\":\"江西省\",\"370000\":\"山东省\",\"410000\":\"河南省\",\"420000\":\"湖北省\",\"430000\":\"湖南省\",\"440000\":\"广东省\",\"450000\":\"广西壮族自治区\",\"460000\":\"海南省\",\"500000\":\"重庆市\",\"510000\":\"四川省\",\"520000\":\"贵州省\",\"530000\":\"云南省\",\"540000\":\"西藏自治区\",\"610000\":\"陕西省\",\"620000\":\"甘肃省\",\"630000\":\"青海省\",\"640000\":\"宁夏回族自治区\",\"650000\":\"新疆维吾尔自治区\",\"710000\":\"台湾省\",\"810000\":\"香港特别行政区\",\"820000\":\"澳门特别行政区\"},\"110000\":{\"110100\":\"市辖区\"},\"110100\":{\"110101\":\"东城区\",\"110102\":\"西城区\",\"110105\":\"朝阳区\",\"110106\":\"丰台区\",\"110107\":\"石景山区\",\"110108\":\"海淀区\",\"110109\":\"门头沟区\",\"110111\":\"房山区\",\"110112\":\"通州区\",\"110113\":\"顺义区\",\"110114\":\"昌平区\",\"110115\":\"大兴区\",\"110116\":\"怀柔区\",\"110117\":\"平谷区\",\"110118\":\"密云区\",\"110119\":\"延庆区\"},\"120000\":{\"120100\":\"市辖区\"},\"120100\":{\"120101\":\"和平区\",\"120102\":\"河东区\",\"120103\":\"河西区\",\"120104\":\"南开区\",\"120105\":\"河北区\",\"120106\":\"红桥区\",\"120110\":\"东丽区\",\"120111\":\"西青区\",\"120112\":\"津南区\",\"120113\":\"北辰区\",\"120114\":\"武清区\",\"120115\":\"宝坻区\",\"120116\":\"滨海新区\",\"120117\":\"宁河区\",\"120118\":\"静海区\",\"120119\":\"蓟州区\"},\"130000\":{\"130100\":\"石家庄市\",\"130200\":\"唐山市\",\"130300\":\"秦皇岛市\",\"130400\":\"邯郸市\",\"130500\":\"邢台市\",\"130600\":\"保定市\",\"130700\":\"张家口市\",\"130800\":\"承德市\",\"130900\":\"沧州市\",\"131000\":\"廊坊市\",\"131100\":\"衡水市\"},\"130100\":{\"130101\":\"市辖区\",\"130102\":\"长安区\",\"130104\":\"桥西区\",\"130105\":\"新华区\",\"130107\":\"井陉矿区\",\"130108\":\"裕华区\",\"130109\":\"藁城区\",\"130110\":\"鹿泉区\",\"130111\":\"栾城区\",\"130121\":\"井陉县\",\"130123\":\"正定县\",\"130125\":\"行唐县\",\"130126\":\"灵寿县\",\"130127\":\"高邑县\",\"130128\":\"深泽县\",\"130129\":\"赞皇县\",\"130130\":\"无极县\",\"130131\":\"平山县\",\"130132\":\"元氏县\",\"130133\":\"赵县\",\"130171\":\"石家庄高新技术产业开发区\",\"130172\":\"石家庄循环化工园区\",\"130181\":\"辛集市\",\"130183\":\"晋州市\",\"130184\":\"新乐市\"},\"130200\":{\"130201\":\"市辖区\",\"130202\":\"路南区\",\"130203\":\"路北区\",\"130204\":\"古冶区\",\"130205\":\"开平区\",\"130207\":\"丰南区\",\"130208\":\"丰润区\",\"130209\":\"曹妃甸区\",\"130224\":\"滦南县\",\"130225\":\"乐亭县\",\"130227\":\"迁西县\",\"130229\":\"玉田县\",\"130271\":\"河北唐山芦台经济开发区\",\"130272\":\"唐山市汉沽管理区\",\"130273\":\"唐山高新技术产业开发区\",\"130274\":\"河北唐山海港经济开发区\",\"130281\":\"遵化市\",\"130283\":\"迁安市\",\"130284\":\"滦州市\"},\"130300\":{\"130301\":\"市辖区\",\"130302\":\"海港区\",\"130303\":\"山海关区\",\"130304\":\"北戴河区\",\"130306\":\"抚宁区\",\"130321\":\"青龙满族自治县\",\"130322\":\"昌黎县\",\"130324\":\"卢龙县\",\"130371\":\"秦皇岛市经济技术开发区\",\"130372\":\"北戴河新区\"},\"130400\":{\"130401\":\"市辖区\",\"130402\":\"邯山区\",\"130403\":\"丛台区\",\"130404\":\"复兴区\",\"130406\":\"峰峰矿区\",\"130407\":\"肥乡区\",\"130408\":\"永年区\",\"130423\":\"临漳县\",\"130424\":\"成安县\",\"130425\":\"大名县\",\"130426\":\"涉县\",\"130427\":\"磁县\",\"130430\":\"邱县\",\"130431\":\"鸡泽县\",\"130432\":\"广平县\",\"130433\":\"馆陶县\",\"130434\":\"魏县\",\"130435\":\"曲周县\",\"130471\":\"邯郸经济技术开发区\",\"130473\":\"邯郸冀南新区\",\"130481\":\"武安市\"},\"130500\":{\"130501\":\"市辖区\",\"130502\":\"桥东区\",\"130503\":\"桥西区\",\"130521\":\"邢台县\",\"130522\":\"临城县\",\"130523\":\"内丘县\",\"130524\":\"柏乡县\",\"130525\":\"隆尧县\",\"130526\":\"任县\",\"130527\":\"南和县\",\"130528\":\"宁晋县\",\"130529\":\"巨鹿县\",\"130530\":\"新河县\",\"130531\":\"广宗县\",\"130532\":\"平乡县\",\"130533\":\"威县\",\"130534\":\"清河县\",\"130535\":\"临西县\",\"130571\":\"河北邢台经济开发区\",\"130581\":\"南宫市\",\"130582\":\"沙河市\"},\"130600\":{\"130601\":\"市辖区\",\"130602\":\"竞秀区\",\"130606\":\"莲池区\",\"130607\":\"满城区\",\"130608\":\"清苑区\",\"130609\":\"徐水区\",\"130623\":\"涞水县\",\"130624\":\"阜平县\",\"130626\":\"定兴县\",\"130627\":\"唐县\",\"130628\":\"高阳县\",\"130629\":\"容城县\",\"130630\":\"涞源县\",\"130631\":\"望都县\",\"130632\":\"安新县\",\"130633\":\"易县\",\"130634\":\"曲阳县\",\"130635\":\"蠡县\",\"130636\":\"顺平县\",\"130637\":\"博野县\",\"130638\":\"雄县\",\"130671\":\"保定高新技术产业开发区\",\"130672\":\"保定白沟新城\",\"130681\":\"涿州市\",\"130682\":\"定州市\",\"130683\":\"安国市\",\"130684\":\"高碑店市\"},\"130700\":{\"130701\":\"市辖区\",\"130702\":\"桥东区\",\"130703\":\"桥西区\",\"130705\":\"宣化区\",\"130706\":\"下花园区\",\"130708\":\"万全区\",\"130709\":\"崇礼区\",\"130722\":\"张北县\",\"130723\":\"康保县\",\"130724\":\"沽源县\",\"130725\":\"尚义县\",\"130726\":\"蔚县\",\"130727\":\"阳原县\",\"130728\":\"怀安县\",\"130730\":\"怀来县\",\"130731\":\"涿鹿县\",\"130732\":\"赤城县\",\"130771\":\"张家口经济开发区\",\"130772\":\"张家口市察北管理区\",\"130773\":\"张家口市塞北管理区\"},\"130800\":{\"130801\":\"市辖区\",\"130802\":\"双桥区\",\"130803\":\"双滦区\",\"130804\":\"鹰手营子矿区\",\"130821\":\"承德县\",\"130822\":\"兴隆县\",\"130824\":\"滦平县\",\"130825\":\"隆化县\",\"130826\":\"丰宁满族自治县\",\"130827\":\"宽城满族自治县\",\"130828\":\"围场满族蒙古族自治县\",\"130871\":\"承德高新技术产业开发区\",\"130881\":\"平泉市\"},\"130900\":{\"130901\":\"市辖区\",\"130902\":\"新华区\",\"130903\":\"运河区\",\"130921\":\"沧县\",\"130922\":\"青县\",\"130923\":\"东光县\",\"130924\":\"海兴县\",\"130925\":\"盐山县\",\"130926\":\"肃宁县\",\"130927\":\"南皮县\",\"130928\":\"吴桥县\",\"130929\":\"献县\",\"130930\":\"孟村回族自治县\",\"130971\":\"河北沧州经济开发区\",\"130972\":\"沧州高新技术产业开发区\",\"130973\":\"沧州渤海新区\",\"130981\":\"泊头市\",\"130982\":\"任丘市\",\"130983\":\"黄骅市\",\"130984\":\"河间市\"},\"131000\":{\"131001\":\"市辖区\",\"131002\":\"安次区\",\"131003\":\"广阳区\",\"131022\":\"固安县\",\"131023\":\"永清县\",\"131024\":\"香河县\",\"131025\":\"大城县\",\"131026\":\"文安县\",\"131028\":\"大厂回族自治县\",\"131071\":\"廊坊经济技术开发区\",\"131081\":\"霸州市\",\"131082\":\"三河市\"},\"131100\":{\"131101\":\"市辖区\",\"131102\":\"桃城区\",\"131103\":\"冀州区\",\"131121\":\"枣强县\",\"131122\":\"武邑县\",\"131123\":\"武强县\",\"131124\":\"饶阳县\",\"131125\":\"安平县\",\"131126\":\"故城县\",\"131127\":\"景县\",\"131128\":\"阜城县\",\"131171\":\"河北衡水高新技术产业开发区\",\"131172\":\"衡水滨湖新区\",\"131182\":\"深州市\"},\"140000\":{\"140100\":\"太原市\",\"140200\":\"大同市\",\"140300\":\"阳泉市\",\"140400\":\"长治市\",\"140500\":\"晋城市\",\"140600\":\"朔州市\",\"140700\":\"晋中市\",\"140800\":\"运城市\",\"140900\":\"忻州市\",\"141000\":\"临汾市\",\"141100\":\"吕梁市\"},\"140100\":{\"140101\":\"市辖区\",\"140105\":\"小店区\",\"140106\":\"迎泽区\",\"140107\":\"杏花岭区\",\"140108\":\"尖草坪区\",\"140109\":\"万柏林区\",\"140110\":\"晋源区\",\"140121\":\"清徐县\",\"140122\":\"阳曲县\",\"140123\":\"娄烦县\",\"140171\":\"山西转型综合改革示范区\",\"140181\":\"古交市\"},\"140200\":{\"140201\":\"市辖区\",\"140212\":\"新荣区\",\"140213\":\"平城区\",\"140214\":\"云冈区\",\"140215\":\"云州区\",\"140221\":\"阳高县\",\"140222\":\"天镇县\",\"140223\":\"广灵县\",\"140224\":\"灵丘县\",\"140225\":\"浑源县\",\"140226\":\"左云县\",\"140271\":\"山西大同经济开发区\"},\"140300\":{\"140301\":\"市辖区\",\"140302\":\"城区\",\"140303\":\"矿区\",\"140311\":\"郊区\",\"140321\":\"平定县\",\"140322\":\"盂县\"},\"140400\":{\"140401\":\"市辖区\",\"140403\":\"潞州区\",\"140404\":\"上党区\",\"140405\":\"屯留区\",\"140406\":\"潞城区\",\"140423\":\"襄垣县\",\"140425\":\"平顺县\",\"140426\":\"黎城县\",\"140427\":\"壶关县\",\"140428\":\"长子县\",\"140429\":\"武乡县\",\"140430\":\"沁县\",\"140431\":\"沁源县\",\"140471\":\"山西长治高新技术产业园区\"},\"140500\":{\"140501\":\"市辖区\",\"140502\":\"城区\",\"140521\":\"沁水县\",\"140522\":\"阳城县\",\"140524\":\"陵川县\",\"140525\":\"泽州县\",\"140581\":\"高平市\"},\"140600\":{\"140601\":\"市辖区\",\"140602\":\"朔城区\",\"140603\":\"平鲁区\",\"140621\":\"山阴县\",\"140622\":\"应县\",\"140623\":\"右玉县\",\"140671\":\"山西朔州经济开发区\",\"140681\":\"怀仁市\"},\"140700\":{\"140701\":\"市辖区\",\"140702\":\"榆次区\",\"140721\":\"榆社县\",\"140722\":\"左权县\",\"140723\":\"和顺县\",\"140724\":\"昔阳县\",\"140725\":\"寿阳县\",\"140726\":\"太谷县\",\"140727\":\"祁县\",\"140728\":\"平遥县\",\"140729\":\"灵石县\",\"140781\":\"介休市\"},\"140800\":{\"140801\":\"市辖区\",\"140802\":\"盐湖区\",\"140821\":\"临猗县\",\"140822\":\"万荣县\",\"140823\":\"闻喜县\",\"140824\":\"稷山县\",\"140825\":\"新绛县\",\"140826\":\"绛县\",\"140827\":\"垣曲县\",\"140828\":\"夏县\",\"140829\":\"平陆县\",\"140830\":\"芮城县\",\"140881\":\"永济市\",\"140882\":\"河津市\"},\"140900\":{\"140901\":\"市辖区\",\"140902\":\"忻府区\",\"140921\":\"定襄县\",\"140922\":\"五台县\",\"140923\":\"代县\",\"140924\":\"繁峙县\",\"140925\":\"宁武县\",\"140926\":\"静乐县\",\"140927\":\"神池县\",\"140928\":\"五寨县\",\"140929\":\"岢岚县\",\"140930\":\"河曲县\",\"140931\":\"保德县\",\"140932\":\"偏关县\",\"140971\":\"五台山风景名胜区\",\"140981\":\"原平市\"},\"141000\":{\"141001\":\"市辖区\",\"141002\":\"尧都区\",\"141021\":\"曲沃县\",\"141022\":\"翼城县\",\"141023\":\"襄汾县\",\"141024\":\"洪洞县\",\"141025\":\"古县\",\"141026\":\"安泽县\",\"141027\":\"浮山县\",\"141028\":\"吉县\",\"141029\":\"乡宁县\",\"141030\":\"大宁县\",\"141031\":\"隰县\",\"141032\":\"永和县\",\"141033\":\"蒲县\",\"141034\":\"汾西县\",\"141081\":\"侯马市\",\"141082\":\"霍州市\"},\"141100\":{\"141101\":\"市辖区\",\"141102\":\"离石区\",\"141121\":\"文水县\",\"141122\":\"交城县\",\"141123\":\"兴县\",\"141124\":\"临县\",\"141125\":\"柳林县\",\"141126\":\"石楼县\",\"141127\":\"岚县\",\"141128\":\"方山县\",\"141129\":\"中阳县\",\"141130\":\"交口县\",\"141181\":\"孝义市\",\"141182\":\"汾阳市\"},\"150000\":{\"150100\":\"呼和浩特市\",\"150200\":\"包头市\",\"150300\":\"乌海市\",\"150400\":\"赤峰市\",\"150500\":\"通辽市\",\"150600\":\"鄂尔多斯市\",\"150700\":\"呼伦贝尔市\",\"150800\":\"巴彦淖尔市\",\"150900\":\"乌兰察布市\",\"152200\":\"兴安盟\",\"152500\":\"锡林郭勒盟\",\"152900\":\"阿拉善盟\"},\"150100\":{\"150101\":\"市辖区\",\"150102\":\"新城区\",\"150103\":\"回民区\",\"150104\":\"玉泉区\",\"150105\":\"赛罕区\",\"150121\":\"土默特左旗\",\"150122\":\"托克托县\",\"150123\":\"和林格尔县\",\"150124\":\"清水河县\",\"150125\":\"武川县\",\"150171\":\"呼和浩特金海工业园区\",\"150172\":\"呼和浩特经济技术开发区\"},\"150200\":{\"150201\":\"市辖区\",\"150202\":\"东河区\",\"150203\":\"昆都仑区\",\"150204\":\"青山区\",\"150205\":\"石拐区\",\"150206\":\"白云鄂博矿区\",\"150207\":\"九原区\",\"150221\":\"土默特右旗\",\"150222\":\"固阳县\",\"150223\":\"达尔罕茂明安联合旗\",\"150271\":\"包头稀土高新技术产业开发区\"},\"150300\":{\"150301\":\"市辖区\",\"150302\":\"海勃湾区\",\"150303\":\"海南区\",\"150304\":\"乌达区\"},\"150400\":{\"150401\":\"市辖区\",\"150402\":\"红山区\",\"150403\":\"元宝山区\",\"150404\":\"松山区\",\"150421\":\"阿鲁科尔沁旗\",\"150422\":\"巴林左旗\",\"150423\":\"巴林右旗\",\"150424\":\"林西县\",\"150425\":\"克什克腾旗\",\"150426\":\"翁牛特旗\",\"150428\":\"喀喇沁旗\",\"150429\":\"宁城县\",\"150430\":\"敖汉旗\"},\"150500\":{\"150501\":\"市辖区\",\"150502\":\"科尔沁区\",\"150521\":\"科尔沁左翼中旗\",\"150522\":\"科尔沁左翼后旗\",\"150523\":\"开鲁县\",\"150524\":\"库伦旗\",\"150525\":\"奈曼旗\",\"150526\":\"扎鲁特旗\",\"150571\":\"通辽经济技术开发区\",\"150581\":\"霍林郭勒市\"},\"150600\":{\"150601\":\"市辖区\",\"150602\":\"东胜区\",\"150603\":\"康巴什区\",\"150621\":\"达拉特旗\",\"150622\":\"准格尔旗\",\"150623\":\"鄂托克前旗\",\"150624\":\"鄂托克旗\",\"150625\":\"杭锦旗\",\"150626\":\"乌审旗\",\"150627\":\"伊金霍洛旗\"},\"150700\":{\"150701\":\"市辖区\",\"150702\":\"海拉尔区\",\"150703\":\"扎赉诺尔区\",\"150721\":\"阿荣旗\",\"150722\":\"莫力达瓦达斡尔族自治旗\",\"150723\":\"鄂伦春自治旗\",\"150724\":\"鄂温克族自治旗\",\"150725\":\"陈巴尔虎旗\",\"150726\":\"新巴尔虎左旗\",\"150727\":\"新巴尔虎右旗\",\"150781\":\"满洲里市\",\"150782\":\"牙克石市\",\"150783\":\"扎兰屯市\",\"150784\":\"额尔古纳市\",\"150785\":\"根河市\"},\"150800\":{\"150801\":\"市辖区\",\"150802\":\"临河区\",\"150821\":\"五原县\",\"150822\":\"磴口县\",\"150823\":\"乌拉特前旗\",\"150824\":\"乌拉特中旗\",\"150825\":\"乌拉特后旗\",\"150826\":\"杭锦后旗\"},\"150900\":{\"150901\":\"市辖区\",\"150902\":\"集宁区\",\"150921\":\"卓资县\",\"150922\":\"化德县\",\"150923\":\"商都县\",\"150924\":\"兴和县\",\"150925\":\"凉城县\",\"150926\":\"察哈尔右翼前旗\",\"150927\":\"察哈尔右翼中旗\",\"150928\":\"察哈尔右翼后旗\",\"150929\":\"四子王旗\",\"150981\":\"丰镇市\"},\"152200\":{\"152201\":\"乌兰浩特市\",\"152202\":\"阿尔山市\",\"152221\":\"科尔沁右翼前旗\",\"152222\":\"科尔沁右翼中旗\",\"152223\":\"扎赉特旗\",\"152224\":\"突泉县\"},\"152500\":{\"152501\":\"二连浩特市\",\"152502\":\"锡林浩特市\",\"152522\":\"阿巴嘎旗\",\"152523\":\"苏尼特左旗\",\"152524\":\"苏尼特右旗\",\"152525\":\"东乌珠穆沁旗\",\"152526\":\"西乌珠穆沁旗\",\"152527\":\"太仆寺旗\",\"152528\":\"镶黄旗\",\"152529\":\"正镶白旗\",\"152530\":\"正蓝旗\",\"152531\":\"多伦县\",\"152571\":\"乌拉盖管委会\"},\"152900\":{\"152921\":\"阿拉善左旗\",\"152922\":\"阿拉善右旗\",\"152923\":\"额济纳旗\",\"152971\":\"内蒙古阿拉善经济开发区\"},\"210000\":{\"210100\":\"沈阳市\",\"210200\":\"大连市\",\"210300\":\"鞍山市\",\"210400\":\"抚顺市\",\"210500\":\"本溪市\",\"210600\":\"丹东市\",\"210700\":\"锦州市\",\"210800\":\"营口市\",\"210900\":\"阜新市\",\"211000\":\"辽阳市\",\"211100\":\"盘锦市\",\"211200\":\"铁岭市\",\"211300\":\"朝阳市\",\"211400\":\"葫芦岛市\"},\"210100\":{\"210101\":\"市辖区\",\"210102\":\"和平区\",\"210103\":\"沈河区\",\"210104\":\"大东区\",\"210105\":\"皇姑区\",\"210106\":\"铁西区\",\"210111\":\"苏家屯区\",\"210112\":\"浑南区\",\"210113\":\"沈北新区\",\"210114\":\"于洪区\",\"210115\":\"辽中区\",\"210123\":\"康平县\",\"210124\":\"法库县\",\"210181\":\"新民市\"},\"210200\":{\"210201\":\"市辖区\",\"210202\":\"中山区\",\"210203\":\"西岗区\",\"210204\":\"沙河口区\",\"210211\":\"甘井子区\",\"210212\":\"旅顺口区\",\"210213\":\"金州区\",\"210214\":\"普兰店区\",\"210224\":\"长海县\",\"210281\":\"瓦房店市\",\"210283\":\"庄河市\"},\"210300\":{\"210301\":\"市辖区\",\"210302\":\"铁东区\",\"210303\":\"铁西区\",\"210304\":\"立山区\",\"210311\":\"千山区\",\"210321\":\"台安县\",\"210323\":\"岫岩满族自治县\",\"210381\":\"海城市\"},\"210400\":{\"210401\":\"市辖区\",\"210402\":\"新抚区\",\"210403\":\"东洲区\",\"210404\":\"望花区\",\"210411\":\"顺城区\",\"210421\":\"抚顺县\",\"210422\":\"新宾满族自治县\",\"210423\":\"清原满族自治县\"},\"210500\":{\"210501\":\"市辖区\",\"210502\":\"平山区\",\"210503\":\"溪湖区\",\"210504\":\"明山区\",\"210505\":\"南芬区\",\"210521\":\"本溪满族自治县\",\"210522\":\"桓仁满族自治县\"},\"210600\":{\"210601\":\"市辖区\",\"210602\":\"元宝区\",\"210603\":\"振兴区\",\"210604\":\"振安区\",\"210624\":\"宽甸满族自治县\",\"210681\":\"东港市\",\"210682\":\"凤城市\"},\"210700\":{\"210701\":\"市辖区\",\"210702\":\"古塔区\",\"210703\":\"凌河区\",\"210711\":\"太和区\",\"210726\":\"黑山县\",\"210727\":\"义县\",\"210781\":\"凌海市\",\"210782\":\"北镇市\"},\"210800\":{\"210801\":\"市辖区\",\"210802\":\"站前区\",\"210803\":\"西市区\",\"210804\":\"鲅鱼圈区\",\"210811\":\"老边区\",\"210881\":\"盖州市\",\"210882\":\"大石桥市\"},\"210900\":{\"210901\":\"市辖区\",\"210902\":\"海州区\",\"210903\":\"新邱区\",\"210904\":\"太平区\",\"210905\":\"清河门区\",\"210911\":\"细河区\",\"210921\":\"阜新蒙古族自治县\",\"210922\":\"彰武县\"},\"211000\":{\"211001\":\"市辖区\",\"211002\":\"白塔区\",\"211003\":\"文圣区\",\"211004\":\"宏伟区\",\"211005\":\"弓长岭区\",\"211011\":\"太子河区\",\"211021\":\"辽阳县\",\"211081\":\"灯塔市\"},\"211100\":{\"211101\":\"市辖区\",\"211102\":\"双台子区\",\"211103\":\"兴隆台区\",\"211104\":\"大洼区\",\"211122\":\"盘山县\"},\"211200\":{\"211201\":\"市辖区\",\"211202\":\"银州区\",\"211204\":\"清河区\",\"211221\":\"铁岭县\",\"211223\":\"西丰县\",\"211224\":\"昌图县\",\"211281\":\"调兵山市\",\"211282\":\"开原市\"},\"211300\":{\"211301\":\"市辖区\",\"211302\":\"双塔区\",\"211303\":\"龙城区\",\"211321\":\"朝阳县\",\"211322\":\"建平县\",\"211324\":\"喀喇沁左翼蒙古族自治县\",\"211381\":\"北票市\",\"211382\":\"凌源市\"},\"211400\":{\"211401\":\"市辖区\",\"211402\":\"连山区\",\"211403\":\"龙港区\",\"211404\":\"南票区\",\"211421\":\"绥中县\",\"211422\":\"建昌县\",\"211481\":\"兴城市\"},\"220000\":{\"220100\":\"长春市\",\"220200\":\"吉林市\",\"220300\":\"四平市\",\"220400\":\"辽源市\",\"220500\":\"通化市\",\"220600\":\"白山市\",\"220700\":\"松原市\",\"220800\":\"白城市\",\"222400\":\"延边朝鲜族自治州\"},\"220100\":{\"220101\":\"市辖区\",\"220102\":\"南关区\",\"220103\":\"宽城区\",\"220104\":\"朝阳区\",\"220105\":\"二道区\",\"220106\":\"绿园区\",\"220112\":\"双阳区\",\"220113\":\"九台区\",\"220122\":\"农安县\",\"220171\":\"长春经济技术开发区\",\"220172\":\"长春净月高新技术产业开发区\",\"220173\":\"长春高新技术产业开发区\",\"220174\":\"长春汽车经济技术开发区\",\"220182\":\"榆树市\",\"220183\":\"德惠市\"},\"220200\":{\"220201\":\"市辖区\",\"220202\":\"昌邑区\",\"220203\":\"龙潭区\",\"220204\":\"船营区\",\"220211\":\"丰满区\",\"220221\":\"永吉县\",\"220271\":\"吉林经济开发区\",\"220272\":\"吉林高新技术产业开发区\",\"220273\":\"吉林中国新加坡食品区\",\"220281\":\"蛟河市\",\"220282\":\"桦甸市\",\"220283\":\"舒兰市\",\"220284\":\"磐石市\"},\"220300\":{\"220301\":\"市辖区\",\"220302\":\"铁西区\",\"220303\":\"铁东区\",\"220322\":\"梨树县\",\"220323\":\"伊通满族自治县\",\"220381\":\"公主岭市\",\"220382\":\"双辽市\"},\"220400\":{\"220401\":\"市辖区\",\"220402\":\"龙山区\",\"220403\":\"西安区\",\"220421\":\"东丰县\",\"220422\":\"东辽县\"},\"220500\":{\"220501\":\"市辖区\",\"220502\":\"东昌区\",\"220503\":\"二道江区\",\"220521\":\"通化县\",\"220523\":\"辉南县\",\"220524\":\"柳河县\",\"220581\":\"梅河口市\",\"220582\":\"集安市\"},\"220600\":{\"220601\":\"市辖区\",\"220602\":\"浑江区\",\"220605\":\"江源区\",\"220621\":\"抚松县\",\"220622\":\"靖宇县\",\"220623\":\"长白朝鲜族自治县\",\"220681\":\"临江市\"},\"220700\":{\"220701\":\"市辖区\",\"220702\":\"宁江区\",\"220721\":\"前郭尔罗斯蒙古族自治县\",\"220722\":\"长岭县\",\"220723\":\"乾安县\",\"220771\":\"吉林松原经济开发区\",\"220781\":\"扶余市\"},\"220800\":{\"220801\":\"市辖区\",\"220802\":\"洮北区\",\"220821\":\"镇赉县\",\"220822\":\"通榆县\",\"220871\":\"吉林白城经济开发区\",\"220881\":\"洮南市\",\"220882\":\"大安市\"},\"222400\":{\"222401\":\"延吉市\",\"222402\":\"图们市\",\"222403\":\"敦化市\",\"222404\":\"珲春市\",\"222405\":\"龙井市\",\"222406\":\"和龙市\",\"222424\":\"汪清县\",\"222426\":\"安图县\"},\"230000\":{\"230100\":\"哈尔滨市\",\"230200\":\"齐齐哈尔市\",\"230300\":\"鸡西市\",\"230400\":\"鹤岗市\",\"230500\":\"双鸭山市\",\"230600\":\"大庆市\",\"230700\":\"伊春市\",\"230800\":\"佳木斯市\",\"230900\":\"七台河市\",\"231000\":\"牡丹江市\",\"231100\":\"黑河市\",\"231200\":\"绥化市\",\"232700\":\"大兴安岭地区\"},\"230100\":{\"230101\":\"市辖区\",\"230102\":\"道里区\",\"230103\":\"南岗区\",\"230104\":\"道外区\",\"230108\":\"平房区\",\"230109\":\"松北区\",\"230110\":\"香坊区\",\"230111\":\"呼兰区\",\"230112\":\"阿城区\",\"230113\":\"双城区\",\"230123\":\"依兰县\",\"230124\":\"方正县\",\"230125\":\"宾县\",\"230126\":\"巴彦县\",\"230127\":\"木兰县\",\"230128\":\"通河县\",\"230129\":\"延寿县\",\"230183\":\"尚志市\",\"230184\":\"五常市\"},\"230200\":{\"230201\":\"市辖区\",\"230202\":\"龙沙区\",\"230203\":\"建华区\",\"230204\":\"铁锋区\",\"230205\":\"昂昂溪区\",\"230206\":\"富拉尔基区\",\"230207\":\"碾子山区\",\"230208\":\"梅里斯达斡尔族区\",\"230221\":\"龙江县\",\"230223\":\"依安县\",\"230224\":\"泰来县\",\"230225\":\"甘南县\",\"230227\":\"富裕县\",\"230229\":\"克山县\",\"230230\":\"克东县\",\"230231\":\"拜泉县\",\"230281\":\"讷河市\"},\"230300\":{\"230301\":\"市辖区\",\"230302\":\"鸡冠区\",\"230303\":\"恒山区\",\"230304\":\"滴道区\",\"230305\":\"梨树区\",\"230306\":\"城子河区\",\"230307\":\"麻山区\",\"230321\":\"鸡东县\",\"230381\":\"虎林市\",\"230382\":\"密山市\"},\"230400\":{\"230401\":\"市辖区\",\"230402\":\"向阳区\",\"230403\":\"工农区\",\"230404\":\"南山区\",\"230405\":\"兴安区\",\"230406\":\"东山区\",\"230407\":\"兴山区\",\"230421\":\"萝北县\",\"230422\":\"绥滨县\"},\"230500\":{\"230501\":\"市辖区\",\"230502\":\"尖山区\",\"230503\":\"岭东区\",\"230505\":\"四方台区\",\"230506\":\"宝山区\",\"230521\":\"集贤县\",\"230522\":\"友谊县\",\"230523\":\"宝清县\",\"230524\":\"饶河县\"},\"230600\":{\"230601\":\"市辖区\",\"230602\":\"萨尔图区\",\"230603\":\"龙凤区\",\"230604\":\"让胡路区\",\"230605\":\"红岗区\",\"230606\":\"大同区\",\"230621\":\"肇州县\",\"230622\":\"肇源县\",\"230623\":\"林甸县\",\"230624\":\"杜尔伯特蒙古族自治县\",\"230671\":\"大庆高新技术产业开发区\"},\"230700\":{\"230701\":\"市辖区\",\"230717\":\"伊美区\",\"230718\":\"乌翠区\",\"230719\":\"友好区\",\"230722\":\"嘉荫县\",\"230723\":\"汤旺县\",\"230724\":\"丰林县\",\"230725\":\"大箐山县\",\"230726\":\"南岔县\",\"230751\":\"金林区\",\"230781\":\"铁力市\"},\"230800\":{\"230801\":\"市辖区\",\"230803\":\"向阳区\",\"230804\":\"前进区\",\"230805\":\"东风区\",\"230811\":\"郊区\",\"230822\":\"桦南县\",\"230826\":\"桦川县\",\"230828\":\"汤原县\",\"230881\":\"同江市\",\"230882\":\"富锦市\",\"230883\":\"抚远市\"},\"230900\":{\"230901\":\"市辖区\",\"230902\":\"新兴区\",\"230903\":\"桃山区\",\"230904\":\"茄子河区\",\"230921\":\"勃利县\"},\"231000\":{\"231001\":\"市辖区\",\"231002\":\"东安区\",\"231003\":\"阳明区\",\"231004\":\"爱民区\",\"231005\":\"西安区\",\"231025\":\"林口县\",\"231071\":\"牡丹江经济技术开发区\",\"231081\":\"绥芬河市\",\"231083\":\"海林市\",\"231084\":\"宁安市\",\"231085\":\"穆棱市\",\"231086\":\"东宁市\"},\"231100\":{\"231101\":\"市辖区\",\"231102\":\"爱辉区\",\"231123\":\"逊克县\",\"231124\":\"孙吴县\",\"231181\":\"北安市\",\"231182\":\"五大连池市\",\"231183\":\"嫩江市\"},\"231200\":{\"231201\":\"市辖区\",\"231202\":\"北林区\",\"231221\":\"望奎县\",\"231222\":\"兰西县\",\"231223\":\"青冈县\",\"231224\":\"庆安县\",\"231225\":\"明水县\",\"231226\":\"绥棱县\",\"231281\":\"安达市\",\"231282\":\"肇东市\",\"231283\":\"海伦市\"},\"232700\":{\"232701\":\"漠河市\",\"232721\":\"呼玛县\",\"232722\":\"塔河县\",\"232761\":\"加格达奇区\",\"232762\":\"松岭区\",\"232763\":\"新林区\",\"232764\":\"呼中区\"},\"310000\":{\"310100\":\"市辖区\"},\"310100\":{\"310101\":\"黄浦区\",\"310104\":\"徐汇区\",\"310105\":\"长宁区\",\"310106\":\"静安区\",\"310107\":\"普陀区\",\"310109\":\"虹口区\",\"310110\":\"杨浦区\",\"310112\":\"闵行区\",\"310113\":\"宝山区\",\"310114\":\"嘉定区\",\"310115\":\"浦东新区\",\"310116\":\"金山区\",\"310117\":\"松江区\",\"310118\":\"青浦区\",\"310120\":\"奉贤区\",\"310151\":\"崇明区\"},\"320000\":{\"320100\":\"南京市\",\"320200\":\"无锡市\",\"320300\":\"徐州市\",\"320400\":\"常州市\",\"320500\":\"苏州市\",\"320600\":\"南通市\",\"320700\":\"连云港市\",\"320800\":\"淮安市\",\"320900\":\"盐城市\",\"321000\":\"扬州市\",\"321100\":\"镇江市\",\"321200\":\"泰州市\",\"321300\":\"宿迁市\"},\"320100\":{\"320101\":\"市辖区\",\"320102\":\"玄武区\",\"320104\":\"秦淮区\",\"320105\":\"建邺区\",\"320106\":\"鼓楼区\",\"320111\":\"浦口区\",\"320113\":\"栖霞区\",\"320114\":\"雨花台区\",\"320115\":\"江宁区\",\"320116\":\"六合区\",\"320117\":\"溧水区\",\"320118\":\"高淳区\"},\"320200\":{\"320201\":\"市辖区\",\"320205\":\"锡山区\",\"320206\":\"惠山区\",\"320211\":\"滨湖区\",\"320213\":\"梁溪区\",\"320214\":\"新吴区\",\"320281\":\"江阴市\",\"320282\":\"宜兴市\"},\"320300\":{\"320301\":\"市辖区\",\"320302\":\"鼓楼区\",\"320303\":\"云龙区\",\"320305\":\"贾汪区\",\"320311\":\"泉山区\",\"320312\":\"铜山区\",\"320321\":\"丰县\",\"320322\":\"沛县\",\"320324\":\"睢宁县\",\"320371\":\"徐州经济技术开发区\",\"320381\":\"新沂市\",\"320382\":\"邳州市\"},\"320400\":{\"320401\":\"市辖区\",\"320402\":\"天宁区\",\"320404\":\"钟楼区\",\"320411\":\"新北区\",\"320412\":\"武进区\",\"320413\":\"金坛区\",\"320481\":\"溧阳市\"},\"320500\":{\"320501\":\"市辖区\",\"320505\":\"虎丘区\",\"320506\":\"吴中区\",\"320507\":\"相城区\",\"320508\":\"姑苏区\",\"320509\":\"吴江区\",\"320571\":\"苏州工业园区\",\"320581\":\"常熟市\",\"320582\":\"张家港市\",\"320583\":\"昆山市\",\"320585\":\"太仓市\"},\"320600\":{\"320601\":\"市辖区\",\"320602\":\"崇川区\",\"320611\":\"港闸区\",\"320612\":\"通州区\",\"320623\":\"如东县\",\"320671\":\"南通经济技术开发区\",\"320681\":\"启东市\",\"320682\":\"如皋市\",\"320684\":\"海门市\",\"320685\":\"海安市\"},\"320700\":{\"320701\":\"市辖区\",\"320703\":\"连云区\",\"320706\":\"海州区\",\"320707\":\"赣榆区\",\"320722\":\"东海县\",\"320723\":\"灌云县\",\"320724\":\"灌南县\",\"320771\":\"连云港经济技术开发区\",\"320772\":\"连云港高新技术产业开发区\"},\"320800\":{\"320801\":\"市辖区\",\"320803\":\"淮安区\",\"320804\":\"淮阴区\",\"320812\":\"清江浦区\",\"320813\":\"洪泽区\",\"320826\":\"涟水县\",\"320830\":\"盱眙县\",\"320831\":\"金湖县\",\"320871\":\"淮安经济技术开发区\"},\"320900\":{\"320901\":\"市辖区\",\"320902\":\"亭湖区\",\"320903\":\"盐都区\",\"320904\":\"大丰区\",\"320921\":\"响水县\",\"320922\":\"滨海县\",\"320923\":\"阜宁县\",\"320924\":\"射阳县\",\"320925\":\"建湖县\",\"320971\":\"盐城经济技术开发区\",\"320981\":\"东台市\"},\"321000\":{\"321001\":\"市辖区\",\"321002\":\"广陵区\",\"321003\":\"邗江区\",\"321012\":\"江都区\",\"321023\":\"宝应县\",\"321071\":\"扬州经济技术开发区\",\"321081\":\"仪征市\",\"321084\":\"高邮市\"},\"321100\":{\"321101\":\"市辖区\",\"321102\":\"京口区\",\"321111\":\"润州区\",\"321112\":\"丹徒区\",\"321171\":\"镇江新区\",\"321181\":\"丹阳市\",\"321182\":\"扬中市\",\"321183\":\"句容市\"},\"321200\":{\"321201\":\"市辖区\",\"321202\":\"海陵区\",\"321203\":\"高港区\",\"321204\":\"姜堰区\",\"321271\":\"泰州医药高新技术产业开发区\",\"321281\":\"兴化市\",\"321282\":\"靖江市\",\"321283\":\"泰兴市\"},\"321300\":{\"321301\":\"市辖区\",\"321302\":\"宿城区\",\"321311\":\"宿豫区\",\"321322\":\"沭阳县\",\"321323\":\"泗阳县\",\"321324\":\"泗洪县\",\"321371\":\"宿迁经济技术开发区\"},\"330000\":{\"330100\":\"杭州市\",\"330200\":\"宁波市\",\"330300\":\"温州市\",\"330400\":\"嘉兴市\",\"330500\":\"湖州市\",\"330600\":\"绍兴市\",\"330700\":\"金华市\",\"330800\":\"衢州市\",\"330900\":\"舟山市\",\"331000\":\"台州市\",\"331100\":\"丽水市\"},\"330100\":{\"330101\":\"市辖区\",\"330102\":\"上城区\",\"330103\":\"下城区\",\"330104\":\"江干区\",\"330105\":\"拱墅区\",\"330106\":\"西湖区\",\"330108\":\"滨江区\",\"330109\":\"萧山区\",\"330110\":\"余杭区\",\"330111\":\"富阳区\",\"330112\":\"临安区\",\"330122\":\"桐庐县\",\"330127\":\"淳安县\",\"330182\":\"建德市\"},\"330200\":{\"330201\":\"市辖区\",\"330203\":\"海曙区\",\"330205\":\"江北区\",\"330206\":\"北仑区\",\"330211\":\"镇海区\",\"330212\":\"鄞州区\",\"330213\":\"奉化区\",\"330225\":\"象山县\",\"330226\":\"宁海县\",\"330281\":\"余姚市\",\"330282\":\"慈溪市\"},\"330300\":{\"330301\":\"市辖区\",\"330302\":\"鹿城区\",\"330303\":\"龙湾区\",\"330304\":\"瓯海区\",\"330305\":\"洞头区\",\"330324\":\"永嘉县\",\"330326\":\"平阳县\",\"330327\":\"苍南县\",\"330328\":\"文成县\",\"330329\":\"泰顺县\",\"330371\":\"温州经济技术开发区\",\"330381\":\"瑞安市\",\"330382\":\"乐清市\",\"330383\":\"龙港市\"},\"330400\":{\"330401\":\"市辖区\",\"330402\":\"南湖区\",\"330411\":\"秀洲区\",\"330421\":\"嘉善县\",\"330424\":\"海盐县\",\"330481\":\"海宁市\",\"330482\":\"平湖市\",\"330483\":\"桐乡市\"},\"330500\":{\"330501\":\"市辖区\",\"330502\":\"吴兴区\",\"330503\":\"南浔区\",\"330521\":\"德清县\",\"330522\":\"长兴县\",\"330523\":\"安吉县\"},\"330600\":{\"330601\":\"市辖区\",\"330602\":\"越城区\",\"330603\":\"柯桥区\",\"330604\":\"上虞区\",\"330624\":\"新昌县\",\"330681\":\"诸暨市\",\"330683\":\"嵊州市\"},\"330700\":{\"330701\":\"市辖区\",\"330702\":\"婺城区\",\"330703\":\"金东区\",\"330723\":\"武义县\",\"330726\":\"浦江县\",\"330727\":\"磐安县\",\"330781\":\"兰溪市\",\"330782\":\"义乌市\",\"330783\":\"东阳市\",\"330784\":\"永康市\"},\"330800\":{\"330801\":\"市辖区\",\"330802\":\"柯城区\",\"330803\":\"衢江区\",\"330822\":\"常山县\",\"330824\":\"开化县\",\"330825\":\"龙游县\",\"330881\":\"江山市\"},\"330900\":{\"330901\":\"市辖区\",\"330902\":\"定海区\",\"330903\":\"普陀区\",\"330921\":\"岱山县\",\"330922\":\"嵊泗县\"},\"331000\":{\"331001\":\"市辖区\",\"331002\":\"椒江区\",\"331003\":\"黄岩区\",\"331004\":\"路桥区\",\"331022\":\"三门县\",\"331023\":\"天台县\",\"331024\":\"仙居县\",\"331081\":\"温岭市\",\"331082\":\"临海市\",\"331083\":\"玉环市\"},\"331100\":{\"331101\":\"市辖区\",\"331102\":\"莲都区\",\"331121\":\"青田县\",\"331122\":\"缙云县\",\"331123\":\"遂昌县\",\"331124\":\"松阳县\",\"331125\":\"云和县\",\"331126\":\"庆元县\",\"331127\":\"景宁畲族自治县\",\"331181\":\"龙泉市\"},\"340000\":{\"340100\":\"合肥市\",\"340200\":\"芜湖市\",\"340300\":\"蚌埠市\",\"340400\":\"淮南市\",\"340500\":\"马鞍山市\",\"340600\":\"淮北市\",\"340700\":\"铜陵市\",\"340800\":\"安庆市\",\"341000\":\"黄山市\",\"341100\":\"滁州市\",\"341200\":\"阜阳市\",\"341300\":\"宿州市\",\"341500\":\"六安市\",\"341600\":\"亳州市\",\"341700\":\"池州市\",\"341800\":\"宣城市\"},\"340100\":{\"340101\":\"市辖区\",\"340102\":\"瑶海区\",\"340103\":\"庐阳区\",\"340104\":\"蜀山区\",\"340111\":\"包河区\",\"340121\":\"长丰县\",\"340122\":\"肥东县\",\"340123\":\"肥西县\",\"340124\":\"庐江县\",\"340171\":\"合肥高新技术产业开发区\",\"340172\":\"合肥经济技术开发区\",\"340173\":\"合肥新站高新技术产业开发区\",\"340181\":\"巢湖市\"},\"340200\":{\"340201\":\"市辖区\",\"340202\":\"镜湖区\",\"340203\":\"弋江区\",\"340207\":\"鸠江区\",\"340208\":\"三山区\",\"340221\":\"芜湖县\",\"340222\":\"繁昌县\",\"340223\":\"南陵县\",\"340225\":\"无为县\",\"340271\":\"芜湖经济技术开发区\",\"340272\":\"安徽芜湖长江大桥经济开发区\"},\"340300\":{\"340301\":\"市辖区\",\"340302\":\"龙子湖区\",\"340303\":\"蚌山区\",\"340304\":\"禹会区\",\"340311\":\"淮上区\",\"340321\":\"怀远县\",\"340322\":\"五河县\",\"340323\":\"固镇县\",\"340371\":\"蚌埠市高新技术开发区\",\"340372\":\"蚌埠市经济开发区\"},\"340400\":{\"340401\":\"市辖区\",\"340402\":\"大通区\",\"340403\":\"田家庵区\",\"340404\":\"谢家集区\",\"340405\":\"八公山区\",\"340406\":\"潘集区\",\"340421\":\"凤台县\",\"340422\":\"寿县\"},\"340500\":{\"340501\":\"市辖区\",\"340503\":\"花山区\",\"340504\":\"雨山区\",\"340506\":\"博望区\",\"340521\":\"当涂县\",\"340522\":\"含山县\",\"340523\":\"和县\"},\"340600\":{\"340601\":\"市辖区\",\"340602\":\"杜集区\",\"340603\":\"相山区\",\"340604\":\"烈山区\",\"340621\":\"濉溪县\"},\"340700\":{\"340701\":\"市辖区\",\"340705\":\"铜官区\",\"340706\":\"义安区\",\"340711\":\"郊区\",\"340722\":\"枞阳县\"},\"340800\":{\"340801\":\"市辖区\",\"340802\":\"迎江区\",\"340803\":\"大观区\",\"340811\":\"宜秀区\",\"340822\":\"怀宁县\",\"340825\":\"太湖县\",\"340826\":\"宿松县\",\"340827\":\"望江县\",\"340828\":\"岳西县\",\"340871\":\"安徽安庆经济开发区\",\"340881\":\"桐城市\",\"340882\":\"潜山市\"},\"341000\":{\"341001\":\"市辖区\",\"341002\":\"屯溪区\",\"341003\":\"黄山区\",\"341004\":\"徽州区\",\"341021\":\"歙县\",\"341022\":\"休宁县\",\"341023\":\"黟县\",\"341024\":\"祁门县\"},\"341100\":{\"341101\":\"市辖区\",\"341102\":\"琅琊区\",\"341103\":\"南谯区\",\"341122\":\"来安县\",\"341124\":\"全椒县\",\"341125\":\"定远县\",\"341126\":\"凤阳县\",\"341171\":\"苏滁现代产业园\",\"341172\":\"滁州经济技术开发区\",\"341181\":\"天长市\",\"341182\":\"明光市\"},\"341200\":{\"341201\":\"市辖区\",\"341202\":\"颍州区\",\"341203\":\"颍东区\",\"341204\":\"颍泉区\",\"341221\":\"临泉县\",\"341222\":\"太和县\",\"341225\":\"阜南县\",\"341226\":\"颍上县\",\"341271\":\"阜阳合肥现代产业园区\",\"341272\":\"阜阳经济技术开发区\",\"341282\":\"界首市\"},\"341300\":{\"341301\":\"市辖区\",\"341302\":\"埇桥区\",\"341321\":\"砀山县\",\"341322\":\"萧县\",\"341323\":\"灵璧县\",\"341324\":\"泗县\",\"341371\":\"宿州马鞍山现代产业园区\",\"341372\":\"宿州经济技术开发区\"},\"341500\":{\"341501\":\"市辖区\",\"341502\":\"金安区\",\"341503\":\"裕安区\",\"341504\":\"叶集区\",\"341522\":\"霍邱县\",\"341523\":\"舒城县\",\"341524\":\"金寨县\",\"341525\":\"霍山县\"},\"341600\":{\"341601\":\"市辖区\",\"341602\":\"谯城区\",\"341621\":\"涡阳县\",\"341622\":\"蒙城县\",\"341623\":\"利辛县\"},\"341700\":{\"341701\":\"市辖区\",\"341702\":\"贵池区\",\"341721\":\"东至县\",\"341722\":\"石台县\",\"341723\":\"青阳县\"},\"341800\":{\"341801\":\"市辖区\",\"341802\":\"宣州区\",\"341821\":\"郎溪县\",\"341823\":\"泾县\",\"341824\":\"绩溪县\",\"341825\":\"旌德县\",\"341871\":\"宣城市经济开发区\",\"341881\":\"宁国市\",\"341882\":\"广德市\"},\"350000\":{\"350100\":\"福州市\",\"350200\":\"厦门市\",\"350300\":\"莆田市\",\"350400\":\"三明市\",\"350500\":\"泉州市\",\"350600\":\"漳州市\",\"350700\":\"南平市\",\"350800\":\"龙岩市\",\"350900\":\"宁德市\"},\"350100\":{\"350101\":\"市辖区\",\"350102\":\"鼓楼区\",\"350103\":\"台江区\",\"350104\":\"仓山区\",\"350105\":\"马尾区\",\"350111\":\"晋安区\",\"350112\":\"长乐区\",\"350121\":\"闽侯县\",\"350122\":\"连江县\",\"350123\":\"罗源县\",\"350124\":\"闽清县\",\"350125\":\"永泰县\",\"350128\":\"平潭县\",\"350181\":\"福清市\"},\"350200\":{\"350201\":\"市辖区\",\"350203\":\"思明区\",\"350205\":\"海沧区\",\"350206\":\"湖里区\",\"350211\":\"集美区\",\"350212\":\"同安区\",\"350213\":\"翔安区\"},\"350300\":{\"350301\":\"市辖区\",\"350302\":\"城厢区\",\"350303\":\"涵江区\",\"350304\":\"荔城区\",\"350305\":\"秀屿区\",\"350322\":\"仙游县\"},\"350400\":{\"350401\":\"市辖区\",\"350402\":\"梅列区\",\"350403\":\"三元区\",\"350421\":\"明溪县\",\"350423\":\"清流县\",\"350424\":\"宁化县\",\"350425\":\"大田县\",\"350426\":\"尤溪县\",\"350427\":\"沙县\",\"350428\":\"将乐县\",\"350429\":\"泰宁县\",\"350430\":\"建宁县\",\"350481\":\"永安市\"},\"350500\":{\"350501\":\"市辖区\",\"350502\":\"鲤城区\",\"350503\":\"丰泽区\",\"350504\":\"洛江区\",\"350505\":\"泉港区\",\"350521\":\"惠安县\",\"350524\":\"安溪县\",\"350525\":\"永春县\",\"350526\":\"德化县\",\"350527\":\"金门县\",\"350581\":\"石狮市\",\"350582\":\"晋江市\",\"350583\":\"南安市\"},\"350600\":{\"350601\":\"市辖区\",\"350602\":\"芗城区\",\"350603\":\"龙文区\",\"350622\":\"云霄县\",\"350623\":\"漳浦县\",\"350624\":\"诏安县\",\"350625\":\"长泰县\",\"350626\":\"东山县\",\"350627\":\"南靖县\",\"350628\":\"平和县\",\"350629\":\"华安县\",\"350681\":\"龙海市\"},\"350700\":{\"350701\":\"市辖区\",\"350702\":\"延平区\",\"350703\":\"建阳区\",\"350721\":\"顺昌县\",\"350722\":\"浦城县\",\"350723\":\"光泽县\",\"350724\":\"松溪县\",\"350725\":\"政和县\",\"350781\":\"邵武市\",\"350782\":\"武夷山市\",\"350783\":\"建瓯市\"},\"350800\":{\"350801\":\"市辖区\",\"350802\":\"新罗区\",\"350803\":\"永定区\",\"350821\":\"长汀县\",\"350823\":\"上杭县\",\"350824\":\"武平县\",\"350825\":\"连城县\",\"350881\":\"漳平市\"},\"350900\":{\"350901\":\"市辖区\",\"350902\":\"蕉城区\",\"350921\":\"霞浦县\",\"350922\":\"古田县\",\"350923\":\"屏南县\",\"350924\":\"寿宁县\",\"350925\":\"周宁县\",\"350926\":\"柘荣县\",\"350981\":\"福安市\",\"350982\":\"福鼎市\"},\"360000\":{\"360100\":\"南昌市\",\"360200\":\"景德镇市\",\"360300\":\"萍乡市\",\"360400\":\"九江市\",\"360500\":\"新余市\",\"360600\":\"鹰潭市\",\"360700\":\"赣州市\",\"360800\":\"吉安市\",\"360900\":\"宜春市\",\"361000\":\"抚州市\",\"361100\":\"上饶市\"},\"360100\":{\"360101\":\"市辖区\",\"360102\":\"东湖区\",\"360103\":\"西湖区\",\"360104\":\"青云谱区\",\"360105\":\"湾里区\",\"360111\":\"青山湖区\",\"360112\":\"新建区\",\"360121\":\"南昌县\",\"360123\":\"安义县\",\"360124\":\"进贤县\"},\"360200\":{\"360201\":\"市辖区\",\"360202\":\"昌江区\",\"360203\":\"珠山区\",\"360222\":\"浮梁县\",\"360281\":\"乐平市\"},\"360300\":{\"360301\":\"市辖区\",\"360302\":\"安源区\",\"360313\":\"湘东区\",\"360321\":\"莲花县\",\"360322\":\"上栗县\",\"360323\":\"芦溪县\"},\"360400\":{\"360401\":\"市辖区\",\"360402\":\"濂溪区\",\"360403\":\"浔阳区\",\"360404\":\"柴桑区\",\"360423\":\"武宁县\",\"360424\":\"修水县\",\"360425\":\"永修县\",\"360426\":\"德安县\",\"360428\":\"都昌县\",\"360429\":\"湖口县\",\"360430\":\"彭泽县\",\"360481\":\"瑞昌市\",\"360482\":\"共青城市\",\"360483\":\"庐山市\"},\"360500\":{\"360501\":\"市辖区\",\"360502\":\"渝水区\",\"360521\":\"分宜县\"},\"360600\":{\"360601\":\"市辖区\",\"360602\":\"月湖区\",\"360603\":\"余江区\",\"360681\":\"贵溪市\"},\"360700\":{\"360701\":\"市辖区\",\"360702\":\"章贡区\",\"360703\":\"南康区\",\"360704\":\"赣县区\",\"360722\":\"信丰县\",\"360723\":\"大余县\",\"360724\":\"上犹县\",\"360725\":\"崇义县\",\"360726\":\"安远县\",\"360727\":\"龙南县\",\"360728\":\"定南县\",\"360729\":\"全南县\",\"360730\":\"宁都县\",\"360731\":\"于都县\",\"360732\":\"兴国县\",\"360733\":\"会昌县\",\"360734\":\"寻乌县\",\"360735\":\"石城县\",\"360781\":\"瑞金市\"},\"360800\":{\"360801\":\"市辖区\",\"360802\":\"吉州区\",\"360803\":\"青原区\",\"360821\":\"吉安县\",\"360822\":\"吉水县\",\"360823\":\"峡江县\",\"360824\":\"新干县\",\"360825\":\"永丰县\",\"360826\":\"泰和县\",\"360827\":\"遂川县\",\"360828\":\"万安县\",\"360829\":\"安福县\",\"360830\":\"永新县\",\"360881\":\"井冈山市\"},\"360900\":{\"360901\":\"市辖区\",\"360902\":\"袁州区\",\"360921\":\"奉新县\",\"360922\":\"万载县\",\"360923\":\"上高县\",\"360924\":\"宜丰县\",\"360925\":\"靖安县\",\"360926\":\"铜鼓县\",\"360981\":\"丰城市\",\"360982\":\"樟树市\",\"360983\":\"高安市\"},\"361000\":{\"361001\":\"市辖区\",\"361002\":\"临川区\",\"361003\":\"东乡区\",\"361021\":\"南城县\",\"361022\":\"黎川县\",\"361023\":\"南丰县\",\"361024\":\"崇仁县\",\"361025\":\"乐安县\",\"361026\":\"宜黄县\",\"361027\":\"金溪县\",\"361028\":\"资溪县\",\"361030\":\"广昌县\"},\"361100\":{\"361101\":\"市辖区\",\"361102\":\"信州区\",\"361103\":\"广丰区\",\"361104\":\"广信区\",\"361123\":\"玉山县\",\"361124\":\"铅山县\",\"361125\":\"横峰县\",\"361126\":\"弋阳县\",\"361127\":\"余干县\",\"361128\":\"鄱阳县\",\"361129\":\"万年县\",\"361130\":\"婺源县\",\"361181\":\"德兴市\"},\"370000\":{\"370100\":\"济南市\",\"370200\":\"青岛市\",\"370300\":\"淄博市\",\"370400\":\"枣庄市\",\"370500\":\"东营市\",\"370600\":\"烟台市\",\"370700\":\"潍坊市\",\"370800\":\"济宁市\",\"370900\":\"泰安市\",\"371000\":\"威海市\",\"371100\":\"日照市\",\"371300\":\"临沂市\",\"371400\":\"德州市\",\"371500\":\"聊城市\",\"371600\":\"滨州市\",\"371700\":\"菏泽市\"},\"370100\":{\"370101\":\"市辖区\",\"370102\":\"历下区\",\"370103\":\"市中区\",\"370104\":\"槐荫区\",\"370105\":\"天桥区\",\"370112\":\"历城区\",\"370113\":\"长清区\",\"370114\":\"章丘区\",\"370115\":\"济阳区\",\"370116\":\"莱芜区\",\"370117\":\"钢城区\",\"370124\":\"平阴县\",\"370126\":\"商河县\",\"370171\":\"济南高新技术产业开发区\"},\"370200\":{\"370201\":\"市辖区\",\"370202\":\"市南区\",\"370203\":\"市北区\",\"370211\":\"黄岛区\",\"370212\":\"崂山区\",\"370213\":\"李沧区\",\"370214\":\"城阳区\",\"370215\":\"即墨区\",\"370271\":\"青岛高新技术产业开发区\",\"370281\":\"胶州市\",\"370283\":\"平度市\",\"370285\":\"莱西市\"},\"370300\":{\"370301\":\"市辖区\",\"370302\":\"淄川区\",\"370303\":\"张店区\",\"370304\":\"博山区\",\"370305\":\"临淄区\",\"370306\":\"周村区\",\"370321\":\"桓台县\",\"370322\":\"高青县\",\"370323\":\"沂源县\"},\"370400\":{\"370401\":\"市辖区\",\"370402\":\"市中区\",\"370403\":\"薛城区\",\"370404\":\"峄城区\",\"370405\":\"台儿庄区\",\"370406\":\"山亭区\",\"370481\":\"滕州市\"},\"370500\":{\"370501\":\"市辖区\",\"370502\":\"东营区\",\"370503\":\"河口区\",\"370505\":\"垦利区\",\"370522\":\"利津县\",\"370523\":\"广饶县\",\"370571\":\"东营经济技术开发区\",\"370572\":\"东营港经济开发区\"},\"370600\":{\"370601\":\"市辖区\",\"370602\":\"芝罘区\",\"370611\":\"福山区\",\"370612\":\"牟平区\",\"370613\":\"莱山区\",\"370634\":\"长岛县\",\"370671\":\"烟台高新技术产业开发区\",\"370672\":\"烟台经济技术开发区\",\"370681\":\"龙口市\",\"370682\":\"莱阳市\",\"370683\":\"莱州市\",\"370684\":\"蓬莱市\",\"370685\":\"招远市\",\"370686\":\"栖霞市\",\"370687\":\"海阳市\"},\"370700\":{\"370701\":\"市辖区\",\"370702\":\"潍城区\",\"370703\":\"寒亭区\",\"370704\":\"坊子区\",\"370705\":\"奎文区\",\"370724\":\"临朐县\",\"370725\":\"昌乐县\",\"370772\":\"潍坊滨海经济技术开发区\",\"370781\":\"青州市\",\"370782\":\"诸城市\",\"370783\":\"寿光市\",\"370784\":\"安丘市\",\"370785\":\"高密市\",\"370786\":\"昌邑市\"},\"370800\":{\"370801\":\"市辖区\",\"370811\":\"任城区\",\"370812\":\"兖州区\",\"370826\":\"微山县\",\"370827\":\"鱼台县\",\"370828\":\"金乡县\",\"370829\":\"嘉祥县\",\"370830\":\"汶上县\",\"370831\":\"泗水县\",\"370832\":\"梁山县\",\"370871\":\"济宁高新技术产业开发区\",\"370881\":\"曲阜市\",\"370883\":\"邹城市\"},\"370900\":{\"370901\":\"市辖区\",\"370902\":\"泰山区\",\"370911\":\"岱岳区\",\"370921\":\"宁阳县\",\"370923\":\"东平县\",\"370982\":\"新泰市\",\"370983\":\"肥城市\"},\"371000\":{\"371001\":\"市辖区\",\"371002\":\"环翠区\",\"371003\":\"文登区\",\"371071\":\"威海火炬高技术产业开发区\",\"371072\":\"威海经济技术开发区\",\"371073\":\"威海临港经济技术开发区\",\"371082\":\"荣成市\",\"371083\":\"乳山市\"},\"371100\":{\"371101\":\"市辖区\",\"371102\":\"东港区\",\"371103\":\"岚山区\",\"371121\":\"五莲县\",\"371122\":\"莒县\",\"371171\":\"日照经济技术开发区\"},\"371300\":{\"371301\":\"市辖区\",\"371302\":\"兰山区\",\"371311\":\"罗庄区\",\"371312\":\"河东区\",\"371321\":\"沂南县\",\"371322\":\"郯城县\",\"371323\":\"沂水县\",\"371324\":\"兰陵县\",\"371325\":\"费县\",\"371326\":\"平邑县\",\"371327\":\"莒南县\",\"371328\":\"蒙阴县\",\"371329\":\"临沭县\",\"371371\":\"临沂高新技术产业开发区\",\"371372\":\"临沂经济技术开发区\",\"371373\":\"临沂临港经济开发区\"},\"371400\":{\"371401\":\"市辖区\",\"371402\":\"德城区\",\"371403\":\"陵城区\",\"371422\":\"宁津县\",\"371423\":\"庆云县\",\"371424\":\"临邑县\",\"371425\":\"齐河县\",\"371426\":\"平原县\",\"371427\":\"夏津县\",\"371428\":\"武城县\",\"371471\":\"德州经济技术开发区\",\"371472\":\"德州运河经济开发区\",\"371481\":\"乐陵市\",\"371482\":\"禹城市\"},\"371500\":{\"371501\":\"市辖区\",\"371502\":\"东昌府区\",\"371503\":\"茌平区\",\"371521\":\"阳谷县\",\"371522\":\"莘县\",\"371524\":\"东阿县\",\"371525\":\"冠县\",\"371526\":\"高唐县\",\"371581\":\"临清市\"},\"371600\":{\"371601\":\"市辖区\",\"371602\":\"滨城区\",\"371603\":\"沾化区\",\"371621\":\"惠民县\",\"371622\":\"阳信县\",\"371623\":\"无棣县\",\"371625\":\"博兴县\",\"371681\":\"邹平市\"},\"371700\":{\"371701\":\"市辖区\",\"371702\":\"牡丹区\",\"371703\":\"定陶区\",\"371721\":\"曹县\",\"371722\":\"单县\",\"371723\":\"成武县\",\"371724\":\"巨野县\",\"371725\":\"郓城县\",\"371726\":\"鄄城县\",\"371728\":\"东明县\",\"371771\":\"菏泽经济技术开发区\",\"371772\":\"菏泽高新技术开发区\"},\"410000\":{\"410100\":\"郑州市\",\"410200\":\"开封市\",\"410300\":\"洛阳市\",\"410400\":\"平顶山市\",\"410500\":\"安阳市\",\"410600\":\"鹤壁市\",\"410700\":\"新乡市\",\"410800\":\"焦作市\",\"410900\":\"濮阳市\",\"411000\":\"许昌市\",\"411100\":\"漯河市\",\"411200\":\"三门峡市\",\"411300\":\"南阳市\",\"411400\":\"商丘市\",\"411500\":\"信阳市\",\"411600\":\"周口市\",\"411700\":\"驻马店市\",\"419000\":\"省直辖县级行政区划\"},\"410100\":{\"410101\":\"市辖区\",\"410102\":\"中原区\",\"410103\":\"二七区\",\"410104\":\"管城回族区\",\"410105\":\"金水区\",\"410106\":\"上街区\",\"410108\":\"惠济区\",\"410122\":\"中牟县\",\"410171\":\"郑州经济技术开发区\",\"410172\":\"郑州高新技术产业开发区\",\"410173\":\"郑州航空港经济综合实验区\",\"410181\":\"巩义市\",\"410182\":\"荥阳市\",\"410183\":\"新密市\",\"410184\":\"新郑市\",\"410185\":\"登封市\"},\"410200\":{\"410201\":\"市辖区\",\"410202\":\"龙亭区\",\"410203\":\"顺河回族区\",\"410204\":\"鼓楼区\",\"410205\":\"禹王台区\",\"410212\":\"祥符区\",\"410221\":\"杞县\",\"410222\":\"通许县\",\"410223\":\"尉氏县\",\"410225\":\"兰考县\"},\"410300\":{\"410301\":\"市辖区\",\"410302\":\"老城区\",\"410303\":\"西工区\",\"410304\":\"瀍河回族区\",\"410305\":\"涧西区\",\"410306\":\"吉利区\",\"410311\":\"洛龙区\",\"410322\":\"孟津县\",\"410323\":\"新安县\",\"410324\":\"栾川县\",\"410325\":\"嵩县\",\"410326\":\"汝阳县\",\"410327\":\"宜阳县\",\"410328\":\"洛宁县\",\"410329\":\"伊川县\",\"410371\":\"洛阳高新技术产业开发区\",\"410381\":\"偃师市\"},\"410400\":{\"410401\":\"市辖区\",\"410402\":\"新华区\",\"410403\":\"卫东区\",\"410404\":\"石龙区\",\"410411\":\"湛河区\",\"410421\":\"宝丰县\",\"410422\":\"叶县\",\"410423\":\"鲁山县\",\"410425\":\"郏县\",\"410471\":\"平顶山高新技术产业开发区\",\"410472\":\"平顶山市城乡一体化示范区\",\"410481\":\"舞钢市\",\"410482\":\"汝州市\"},\"410500\":{\"410501\":\"市辖区\",\"410502\":\"文峰区\",\"410503\":\"北关区\",\"410505\":\"殷都区\",\"410506\":\"龙安区\",\"410522\":\"安阳县\",\"410523\":\"汤阴县\",\"410526\":\"滑县\",\"410527\":\"内黄县\",\"410571\":\"安阳高新技术产业开发区\",\"410581\":\"林州市\"},\"410600\":{\"410601\":\"市辖区\",\"410602\":\"鹤山区\",\"410603\":\"山城区\",\"410611\":\"淇滨区\",\"410621\":\"浚县\",\"410622\":\"淇县\",\"410671\":\"鹤壁经济技术开发区\"},\"410700\":{\"410701\":\"市辖区\",\"410702\":\"红旗区\",\"410703\":\"卫滨区\",\"410704\":\"凤泉区\",\"410711\":\"牧野区\",\"410721\":\"新乡县\",\"410724\":\"获嘉县\",\"410725\":\"原阳县\",\"410726\":\"延津县\",\"410727\":\"封丘县\",\"410771\":\"新乡高新技术产业开发区\",\"410772\":\"新乡经济技术开发区\",\"410773\":\"新乡市平原城乡一体化示范区\",\"410781\":\"卫辉市\",\"410782\":\"辉县市\",\"410783\":\"长垣市\"},\"410800\":{\"410801\":\"市辖区\",\"410802\":\"解放区\",\"410803\":\"中站区\",\"410804\":\"马村区\",\"410811\":\"山阳区\",\"410821\":\"修武县\",\"410822\":\"博爱县\",\"410823\":\"武陟县\",\"410825\":\"温县\",\"410871\":\"焦作城乡一体化示范区\",\"410882\":\"沁阳市\",\"410883\":\"孟州市\"},\"410900\":{\"410901\":\"市辖区\",\"410902\":\"华龙区\",\"410922\":\"清丰县\",\"410923\":\"南乐县\",\"410926\":\"范县\",\"410927\":\"台前县\",\"410928\":\"濮阳县\",\"410971\":\"河南濮阳工业园区\",\"410972\":\"濮阳经济技术开发区\"},\"411000\":{\"411001\":\"市辖区\",\"411002\":\"魏都区\",\"411003\":\"建安区\",\"411024\":\"鄢陵县\",\"411025\":\"襄城县\",\"411071\":\"许昌经济技术开发区\",\"411081\":\"禹州市\",\"411082\":\"长葛市\"},\"411100\":{\"411101\":\"市辖区\",\"411102\":\"源汇区\",\"411103\":\"郾城区\",\"411104\":\"召陵区\",\"411121\":\"舞阳县\",\"411122\":\"临颍县\",\"411171\":\"漯河经济技术开发区\"},\"411200\":{\"411201\":\"市辖区\",\"411202\":\"湖滨区\",\"411203\":\"陕州区\",\"411221\":\"渑池县\",\"411224\":\"卢氏县\",\"411271\":\"河南三门峡经济开发区\",\"411281\":\"义马市\",\"411282\":\"灵宝市\"},\"411300\":{\"411301\":\"市辖区\",\"411302\":\"宛城区\",\"411303\":\"卧龙区\",\"411321\":\"南召县\",\"411322\":\"方城县\",\"411323\":\"西峡县\",\"411324\":\"镇平县\",\"411325\":\"内乡县\",\"411326\":\"淅川县\",\"411327\":\"社旗县\",\"411328\":\"唐河县\",\"411329\":\"新野县\",\"411330\":\"桐柏县\",\"411371\":\"南阳高新技术产业开发区\",\"411372\":\"南阳市城乡一体化示范区\",\"411381\":\"邓州市\"},\"411400\":{\"411401\":\"市辖区\",\"411402\":\"梁园区\",\"411403\":\"睢阳区\",\"411421\":\"民权县\",\"411422\":\"睢县\",\"411423\":\"宁陵县\",\"411424\":\"柘城县\",\"411425\":\"虞城县\",\"411426\":\"夏邑县\",\"411471\":\"豫东综合物流产业聚集区\",\"411472\":\"河南商丘经济开发区\",\"411481\":\"永城市\"},\"411500\":{\"411501\":\"市辖区\",\"411502\":\"浉河区\",\"411503\":\"平桥区\",\"411521\":\"罗山县\",\"411522\":\"光山县\",\"411523\":\"新县\",\"411524\":\"商城县\",\"411525\":\"固始县\",\"411526\":\"潢川县\",\"411527\":\"淮滨县\",\"411528\":\"息县\",\"411571\":\"信阳高新技术产业开发区\"},\"411600\":{\"411601\":\"市辖区\",\"411602\":\"川汇区\",\"411603\":\"淮阳区\",\"411621\":\"扶沟县\",\"411622\":\"西华县\",\"411623\":\"商水县\",\"411624\":\"沈丘县\",\"411625\":\"郸城县\",\"411627\":\"太康县\",\"411628\":\"鹿邑县\",\"411671\":\"河南周口经济开发区\",\"411681\":\"项城市\"},\"411700\":{\"411701\":\"市辖区\",\"411702\":\"驿城区\",\"411721\":\"西平县\",\"411722\":\"上蔡县\",\"411723\":\"平舆县\",\"411724\":\"正阳县\",\"411725\":\"确山县\",\"411726\":\"泌阳县\",\"411727\":\"汝南县\",\"411728\":\"遂平县\",\"411729\":\"新蔡县\",\"411771\":\"河南驻马店经济开发区\"},\"419000\":{\"419001\":\"济源市\"},\"420000\":{\"420100\":\"武汉市\",\"420200\":\"黄石市\",\"420300\":\"十堰市\",\"420500\":\"宜昌市\",\"420600\":\"襄阳市\",\"420700\":\"鄂州市\",\"420800\":\"荆门市\",\"420900\":\"孝感市\",\"421000\":\"荆州市\",\"421100\":\"黄冈市\",\"421200\":\"咸宁市\",\"421300\":\"随州市\",\"422800\":\"恩施土家族苗族自治州\",\"429000\":\"省直辖县级行政区划\"},\"420100\":{\"420101\":\"市辖区\",\"420102\":\"江岸区\",\"420103\":\"江汉区\",\"420104\":\"硚口区\",\"420105\":\"汉阳区\",\"420106\":\"武昌区\",\"420107\":\"青山区\",\"420111\":\"洪山区\",\"420112\":\"东西湖区\",\"420113\":\"汉南区\",\"420114\":\"蔡甸区\",\"420115\":\"江夏区\",\"420116\":\"黄陂区\",\"420117\":\"新洲区\"},\"420200\":{\"420201\":\"市辖区\",\"420202\":\"黄石港区\",\"420203\":\"西塞山区\",\"420204\":\"下陆区\",\"420205\":\"铁山区\",\"420222\":\"阳新县\",\"420281\":\"大冶市\"},\"420300\":{\"420301\":\"市辖区\",\"420302\":\"茅箭区\",\"420303\":\"张湾区\",\"420304\":\"郧阳区\",\"420322\":\"郧西县\",\"420323\":\"竹山县\",\"420324\":\"竹溪县\",\"420325\":\"房县\",\"420381\":\"丹江口市\"},\"420500\":{\"420501\":\"市辖区\",\"420502\":\"西陵区\",\"420503\":\"伍家岗区\",\"420504\":\"点军区\",\"420505\":\"猇亭区\",\"420506\":\"夷陵区\",\"420525\":\"远安县\",\"420526\":\"兴山县\",\"420527\":\"秭归县\",\"420528\":\"长阳土家族自治县\",\"420529\":\"五峰土家族自治县\",\"420581\":\"宜都市\",\"420582\":\"当阳市\",\"420583\":\"枝江市\"},\"420600\":{\"420601\":\"市辖区\",\"420602\":\"襄城区\",\"420606\":\"樊城区\",\"420607\":\"襄州区\",\"420624\":\"南漳县\",\"420625\":\"谷城县\",\"420626\":\"保康县\",\"420682\":\"老河口市\",\"420683\":\"枣阳市\",\"420684\":\"宜城市\"},\"420700\":{\"420701\":\"市辖区\",\"420702\":\"梁子湖区\",\"420703\":\"华容区\",\"420704\":\"鄂城区\"},\"420800\":{\"420801\":\"市辖区\",\"420802\":\"东宝区\",\"420804\":\"掇刀区\",\"420822\":\"沙洋县\",\"420881\":\"钟祥市\",\"420882\":\"京山市\"},\"420900\":{\"420901\":\"市辖区\",\"420902\":\"孝南区\",\"420921\":\"孝昌县\",\"420922\":\"大悟县\",\"420923\":\"云梦县\",\"420981\":\"应城市\",\"420982\":\"安陆市\",\"420984\":\"汉川市\"},\"421000\":{\"421001\":\"市辖区\",\"421002\":\"沙市区\",\"421003\":\"荆州区\",\"421022\":\"公安县\",\"421023\":\"监利县\",\"421024\":\"江陵县\",\"421071\":\"荆州经济技术开发区\",\"421081\":\"石首市\",\"421083\":\"洪湖市\",\"421087\":\"松滋市\"},\"421100\":{\"421101\":\"市辖区\",\"421102\":\"黄州区\",\"421121\":\"团风县\",\"421122\":\"红安县\",\"421123\":\"罗田县\",\"421124\":\"英山县\",\"421125\":\"浠水县\",\"421126\":\"蕲春县\",\"421127\":\"黄梅县\",\"421171\":\"龙感湖管理区\",\"421181\":\"麻城市\",\"421182\":\"武穴市\"},\"421200\":{\"421201\":\"市辖区\",\"421202\":\"咸安区\",\"421221\":\"嘉鱼县\",\"421222\":\"通城县\",\"421223\":\"崇阳县\",\"421224\":\"通山县\",\"421281\":\"赤壁市\"},\"421300\":{\"421301\":\"市辖区\",\"421303\":\"曾都区\",\"421321\":\"随县\",\"421381\":\"广水市\"},\"422800\":{\"422801\":\"恩施市\",\"422802\":\"利川市\",\"422822\":\"建始县\",\"422823\":\"巴东县\",\"422825\":\"宣恩县\",\"422826\":\"咸丰县\",\"422827\":\"来凤县\",\"422828\":\"鹤峰县\"},\"429000\":{\"429004\":\"仙桃市\",\"429005\":\"潜江市\",\"429006\":\"天门市\",\"429021\":\"神农架林区\"},\"430000\":{\"430100\":\"长沙市\",\"430200\":\"株洲市\",\"430300\":\"湘潭市\",\"430400\":\"衡阳市\",\"430500\":\"邵阳市\",\"430600\":\"岳阳市\",\"430700\":\"常德市\",\"430800\":\"张家界市\",\"430900\":\"益阳市\",\"431000\":\"郴州市\",\"431100\":\"永州市\",\"431200\":\"怀化市\",\"431300\":\"娄底市\",\"433100\":\"湘西土家族苗族自治州\"},\"430100\":{\"430101\":\"市辖区\",\"430102\":\"芙蓉区\",\"430103\":\"天心区\",\"430104\":\"岳麓区\",\"430105\":\"开福区\",\"430111\":\"雨花区\",\"430112\":\"望城区\",\"430121\":\"长沙县\",\"430181\":\"浏阳市\",\"430182\":\"宁乡市\"},\"430200\":{\"430201\":\"市辖区\",\"430202\":\"荷塘区\",\"430203\":\"芦淞区\",\"430204\":\"石峰区\",\"430211\":\"天元区\",\"430212\":\"渌口区\",\"430223\":\"攸县\",\"430224\":\"茶陵县\",\"430225\":\"炎陵县\",\"430271\":\"云龙示范区\",\"430281\":\"醴陵市\"},\"430300\":{\"430301\":\"市辖区\",\"430302\":\"雨湖区\",\"430304\":\"岳塘区\",\"430321\":\"湘潭县\",\"430371\":\"湖南湘潭高新技术产业园区\",\"430372\":\"湘潭昭山示范区\",\"430373\":\"湘潭九华示范区\",\"430381\":\"湘乡市\",\"430382\":\"韶山市\"},\"430400\":{\"430401\":\"市辖区\",\"430405\":\"珠晖区\",\"430406\":\"雁峰区\",\"430407\":\"石鼓区\",\"430408\":\"蒸湘区\",\"430412\":\"南岳区\",\"430421\":\"衡阳县\",\"430422\":\"衡南县\",\"430423\":\"衡山县\",\"430424\":\"衡东县\",\"430426\":\"祁东县\",\"430471\":\"衡阳综合保税区\",\"430472\":\"湖南衡阳高新技术产业园区\",\"430473\":\"湖南衡阳松木经济开发区\",\"430481\":\"耒阳市\",\"430482\":\"常宁市\"},\"430500\":{\"430501\":\"市辖区\",\"430502\":\"双清区\",\"430503\":\"大祥区\",\"430511\":\"北塔区\",\"430522\":\"新邵县\",\"430523\":\"邵阳县\",\"430524\":\"隆回县\",\"430525\":\"洞口县\",\"430527\":\"绥宁县\",\"430528\":\"新宁县\",\"430529\":\"城步苗族自治县\",\"430581\":\"武冈市\",\"430582\":\"邵东市\"},\"430600\":{\"430601\":\"市辖区\",\"430602\":\"岳阳楼区\",\"430603\":\"云溪区\",\"430611\":\"君山区\",\"430621\":\"岳阳县\",\"430623\":\"华容县\",\"430624\":\"湘阴县\",\"430626\":\"平江县\",\"430671\":\"岳阳市屈原管理区\",\"430681\":\"汨罗市\",\"430682\":\"临湘市\"},\"430700\":{\"430701\":\"市辖区\",\"430702\":\"武陵区\",\"430703\":\"鼎城区\",\"430721\":\"安乡县\",\"430722\":\"汉寿县\",\"430723\":\"澧县\",\"430724\":\"临澧县\",\"430725\":\"桃源县\",\"430726\":\"石门县\",\"430771\":\"常德市西洞庭管理区\",\"430781\":\"津市市\"},\"430800\":{\"430801\":\"市辖区\",\"430802\":\"永定区\",\"430811\":\"武陵源区\",\"430821\":\"慈利县\",\"430822\":\"桑植县\"},\"430900\":{\"430901\":\"市辖区\",\"430902\":\"资阳区\",\"430903\":\"赫山区\",\"430921\":\"南县\",\"430922\":\"桃江县\",\"430923\":\"安化县\",\"430971\":\"益阳市大通湖管理区\",\"430972\":\"湖南益阳高新技术产业园区\",\"430981\":\"沅江市\"},\"431000\":{\"431001\":\"市辖区\",\"431002\":\"北湖区\",\"431003\":\"苏仙区\",\"431021\":\"桂阳县\",\"431022\":\"宜章县\",\"431023\":\"永兴县\",\"431024\":\"嘉禾县\",\"431025\":\"临武县\",\"431026\":\"汝城县\",\"431027\":\"桂东县\",\"431028\":\"安仁县\",\"431081\":\"资兴市\"},\"431100\":{\"431101\":\"市辖区\",\"431102\":\"零陵区\",\"431103\":\"冷水滩区\",\"431121\":\"祁阳县\",\"431122\":\"东安县\",\"431123\":\"双牌县\",\"431124\":\"道县\",\"431125\":\"江永县\",\"431126\":\"宁远县\",\"431127\":\"蓝山县\",\"431128\":\"新田县\",\"431129\":\"江华瑶族自治县\",\"431171\":\"永州经济技术开发区\",\"431172\":\"永州市金洞管理区\",\"431173\":\"永州市回龙圩管理区\"},\"431200\":{\"431201\":\"市辖区\",\"431202\":\"鹤城区\",\"431221\":\"中方县\",\"431222\":\"沅陵县\",\"431223\":\"辰溪县\",\"431224\":\"溆浦县\",\"431225\":\"会同县\",\"431226\":\"麻阳苗族自治县\",\"431227\":\"新晃侗族自治县\",\"431228\":\"芷江侗族自治县\",\"431229\":\"靖州苗族侗族自治县\",\"431230\":\"通道侗族自治县\",\"431271\":\"怀化市洪江管理区\",\"431281\":\"洪江市\"},\"431300\":{\"431301\":\"市辖区\",\"431302\":\"娄星区\",\"431321\":\"双峰县\",\"431322\":\"新化县\",\"431381\":\"冷水江市\",\"431382\":\"涟源市\"},\"433100\":{\"433101\":\"吉首市\",\"433122\":\"泸溪县\",\"433123\":\"凤凰县\",\"433124\":\"花垣县\",\"433125\":\"保靖县\",\"433126\":\"古丈县\",\"433127\":\"永顺县\",\"433130\":\"龙山县\",\"433173\":\"湖南永顺经济开发区\"},\"440000\":{\"440100\":\"广州市\",\"440200\":\"韶关市\",\"440300\":\"深圳市\",\"440400\":\"珠海市\",\"440500\":\"汕头市\",\"440600\":\"佛山市\",\"440700\":\"江门市\",\"440800\":\"湛江市\",\"440900\":\"茂名市\",\"441200\":\"肇庆市\",\"441300\":\"惠州市\",\"441400\":\"梅州市\",\"441500\":\"汕尾市\",\"441600\":\"河源市\",\"441700\":\"阳江市\",\"441800\":\"清远市\",\"441900\":\"东莞市\",\"442000\":\"中山市\",\"445100\":\"潮州市\",\"445200\":\"揭阳市\",\"445300\":\"云浮市\"},\"440100\":{\"440101\":\"市辖区\",\"440103\":\"荔湾区\",\"440104\":\"越秀区\",\"440105\":\"海珠区\",\"440106\":\"天河区\",\"440111\":\"白云区\",\"440112\":\"黄埔区\",\"440113\":\"番禺区\",\"440114\":\"花都区\",\"440115\":\"南沙区\",\"440117\":\"从化区\",\"440118\":\"增城区\"},\"440200\":{\"440201\":\"市辖区\",\"440203\":\"武江区\",\"440204\":\"浈江区\",\"440205\":\"曲江区\",\"440222\":\"始兴县\",\"440224\":\"仁化县\",\"440229\":\"翁源县\",\"440232\":\"乳源瑶族自治县\",\"440233\":\"新丰县\",\"440281\":\"乐昌市\",\"440282\":\"南雄市\"},\"440300\":{\"440301\":\"市辖区\",\"440303\":\"罗湖区\",\"440304\":\"福田区\",\"440305\":\"南山区\",\"440306\":\"宝安区\",\"440307\":\"龙岗区\",\"440308\":\"盐田区\",\"440309\":\"龙华区\",\"440310\":\"坪山区\",\"440311\":\"光明区\"},\"440400\":{\"440401\":\"市辖区\",\"440402\":\"香洲区\",\"440403\":\"斗门区\",\"440404\":\"金湾区\"},\"440500\":{\"440501\":\"市辖区\",\"440507\":\"龙湖区\",\"440511\":\"金平区\",\"440512\":\"濠江区\",\"440513\":\"潮阳区\",\"440514\":\"潮南区\",\"440515\":\"澄海区\",\"440523\":\"南澳县\"},\"440600\":{\"440601\":\"市辖区\",\"440604\":\"禅城区\",\"440605\":\"南海区\",\"440606\":\"顺德区\",\"440607\":\"三水区\",\"440608\":\"高明区\"},\"440700\":{\"440701\":\"市辖区\",\"440703\":\"蓬江区\",\"440704\":\"江海区\",\"440705\":\"新会区\",\"440781\":\"台山市\",\"440783\":\"开平市\",\"440784\":\"鹤山市\",\"440785\":\"恩平市\"},\"440800\":{\"440801\":\"市辖区\",\"440802\":\"赤坎区\",\"440803\":\"霞山区\",\"440804\":\"坡头区\",\"440811\":\"麻章区\",\"440823\":\"遂溪县\",\"440825\":\"徐闻县\",\"440881\":\"廉江市\",\"440882\":\"雷州市\",\"440883\":\"吴川市\"},\"440900\":{\"440901\":\"市辖区\",\"440902\":\"茂南区\",\"440904\":\"电白区\",\"440981\":\"高州市\",\"440982\":\"化州市\",\"440983\":\"信宜市\"},\"441200\":{\"441201\":\"市辖区\",\"441202\":\"端州区\",\"441203\":\"鼎湖区\",\"441204\":\"高要区\",\"441223\":\"广宁县\",\"441224\":\"怀集县\",\"441225\":\"封开县\",\"441226\":\"德庆县\",\"441284\":\"四会市\"},\"441300\":{\"441301\":\"市辖区\",\"441302\":\"惠城区\",\"441303\":\"惠阳区\",\"441322\":\"博罗县\",\"441323\":\"惠东县\",\"441324\":\"龙门县\"},\"441400\":{\"441401\":\"市辖区\",\"441402\":\"梅江区\",\"441403\":\"梅县区\",\"441422\":\"大埔县\",\"441423\":\"丰顺县\",\"441424\":\"五华县\",\"441426\":\"平远县\",\"441427\":\"蕉岭县\",\"441481\":\"兴宁市\"},\"441500\":{\"441501\":\"市辖区\",\"441502\":\"城区\",\"441521\":\"海丰县\",\"441523\":\"陆河县\",\"441581\":\"陆丰市\"},\"441600\":{\"441601\":\"市辖区\",\"441602\":\"源城区\",\"441621\":\"紫金县\",\"441622\":\"龙川县\",\"441623\":\"连平县\",\"441624\":\"和平县\",\"441625\":\"东源县\"},\"441700\":{\"441701\":\"市辖区\",\"441702\":\"江城区\",\"441704\":\"阳东区\",\"441721\":\"阳西县\",\"441781\":\"阳春市\"},\"441800\":{\"441801\":\"市辖区\",\"441802\":\"清城区\",\"441803\":\"清新区\",\"441821\":\"佛冈县\",\"441823\":\"阳山县\",\"441825\":\"连山壮族瑶族自治县\",\"441826\":\"连南瑶族自治县\",\"441881\":\"英德市\",\"441882\":\"连州市\"},\"441900\":{\"441900003\":\"东城街道\",\"441900004\":\"南城街道\",\"441900005\":\"万江街道\",\"441900006\":\"莞城街道\",\"441900101\":\"石碣镇\",\"441900102\":\"石龙镇\",\"441900103\":\"茶山镇\",\"441900104\":\"石排镇\",\"441900105\":\"企石镇\",\"441900106\":\"横沥镇\",\"441900107\":\"桥头镇\",\"441900108\":\"谢岗镇\",\"441900109\":\"东坑镇\",\"441900110\":\"常平镇\",\"441900111\":\"寮步镇\",\"441900112\":\"樟木头镇\",\"441900113\":\"大朗镇\",\"441900114\":\"黄江镇\",\"441900115\":\"清溪镇\",\"441900116\":\"塘厦镇\",\"441900117\":\"凤岗镇\",\"441900118\":\"大岭山镇\",\"441900119\":\"长安镇\",\"441900121\":\"虎门镇\",\"441900122\":\"厚街镇\",\"441900123\":\"沙田镇\",\"441900124\":\"道滘镇\",\"441900125\":\"洪梅镇\",\"441900126\":\"麻涌镇\",\"441900127\":\"望牛墩镇\",\"441900128\":\"中堂镇\",\"441900129\":\"高埗镇\",\"441900401\":\"松山湖\",\"441900402\":\"东莞港\",\"441900403\":\"东莞生态园\"},\"442000\":{\"442000001\":\"石岐街道\",\"442000002\":\"东区街道\",\"442000003\":\"中山港街道\",\"442000004\":\"西区街道\",\"442000005\":\"南区街道\",\"442000006\":\"五桂山街道\",\"442000100\":\"小榄镇\",\"442000101\":\"黄圃镇\",\"442000102\":\"民众镇\",\"442000103\":\"东凤镇\",\"442000104\":\"东升镇\",\"442000105\":\"古镇镇\",\"442000106\":\"沙溪镇\",\"442000107\":\"坦洲镇\",\"442000108\":\"港口镇\",\"442000109\":\"三角镇\",\"442000110\":\"横栏镇\",\"442000111\":\"南头镇\",\"442000112\":\"阜沙镇\",\"442000113\":\"南朗镇\",\"442000114\":\"三乡镇\",\"442000115\":\"板芙镇\",\"442000116\":\"大涌镇\",\"442000117\":\"神湾镇\"},\"445100\":{\"445101\":\"市辖区\",\"445102\":\"湘桥区\",\"445103\":\"潮安区\",\"445122\":\"饶平县\"},\"445200\":{\"445201\":\"市辖区\",\"445202\":\"榕城区\",\"445203\":\"揭东区\",\"445222\":\"揭西县\",\"445224\":\"惠来县\",\"445281\":\"普宁市\"},\"445300\":{\"445301\":\"市辖区\",\"445302\":\"云城区\",\"445303\":\"云安区\",\"445321\":\"新兴县\",\"445322\":\"郁南县\",\"445381\":\"罗定市\"},\"450000\":{\"450100\":\"南宁市\",\"450200\":\"柳州市\",\"450300\":\"桂林市\",\"450400\":\"梧州市\",\"450500\":\"北海市\",\"450600\":\"防城港市\",\"450700\":\"钦州市\",\"450800\":\"贵港市\",\"450900\":\"玉林市\",\"451000\":\"百色市\",\"451100\":\"贺州市\",\"451200\":\"河池市\",\"451300\":\"来宾市\",\"451400\":\"崇左市\"},\"450100\":{\"450101\":\"市辖区\",\"450102\":\"兴宁区\",\"450103\":\"青秀区\",\"450105\":\"江南区\",\"450107\":\"西乡塘区\",\"450108\":\"良庆区\",\"450109\":\"邕宁区\",\"450110\":\"武鸣区\",\"450123\":\"隆安县\",\"450124\":\"马山县\",\"450125\":\"上林县\",\"450126\":\"宾阳县\",\"450127\":\"横县\"},\"450200\":{\"450201\":\"市辖区\",\"450202\":\"城中区\",\"450203\":\"鱼峰区\",\"450204\":\"柳南区\",\"450205\":\"柳北区\",\"450206\":\"柳江区\",\"450222\":\"柳城县\",\"450223\":\"鹿寨县\",\"450224\":\"融安县\",\"450225\":\"融水苗族自治县\",\"450226\":\"三江侗族自治县\"},\"450300\":{\"450301\":\"市辖区\",\"450302\":\"秀峰区\",\"450303\":\"叠彩区\",\"450304\":\"象山区\",\"450305\":\"七星区\",\"450311\":\"雁山区\",\"450312\":\"临桂区\",\"450321\":\"阳朔县\",\"450323\":\"灵川县\",\"450324\":\"全州县\",\"450325\":\"兴安县\",\"450326\":\"永福县\",\"450327\":\"灌阳县\",\"450328\":\"龙胜各族自治县\",\"450329\":\"资源县\",\"450330\":\"平乐县\",\"450332\":\"恭城瑶族自治县\",\"450381\":\"荔浦市\"},\"450400\":{\"450401\":\"市辖区\",\"450403\":\"万秀区\",\"450405\":\"长洲区\",\"450406\":\"龙圩区\",\"450421\":\"苍梧县\",\"450422\":\"藤县\",\"450423\":\"蒙山县\",\"450481\":\"岑溪市\"},\"450500\":{\"450501\":\"市辖区\",\"450502\":\"海城区\",\"450503\":\"银海区\",\"450512\":\"铁山港区\",\"450521\":\"合浦县\"},\"450600\":{\"450601\":\"市辖区\",\"450602\":\"港口区\",\"450603\":\"防城区\",\"450621\":\"上思县\",\"450681\":\"东兴市\"},\"450700\":{\"450701\":\"市辖区\",\"450702\":\"钦南区\",\"450703\":\"钦北区\",\"450721\":\"灵山县\",\"450722\":\"浦北县\"},\"450800\":{\"450801\":\"市辖区\",\"450802\":\"港北区\",\"450803\":\"港南区\",\"450804\":\"覃塘区\",\"450821\":\"平南县\",\"450881\":\"桂平市\"},\"450900\":{\"450901\":\"市辖区\",\"450902\":\"玉州区\",\"450903\":\"福绵区\",\"450921\":\"容县\",\"450922\":\"陆川县\",\"450923\":\"博白县\",\"450924\":\"兴业县\",\"450981\":\"北流市\"},\"451000\":{\"451001\":\"市辖区\",\"451002\":\"右江区\",\"451003\":\"田阳区\",\"451022\":\"田东县\",\"451023\":\"平果县\",\"451024\":\"德保县\",\"451026\":\"那坡县\",\"451027\":\"凌云县\",\"451028\":\"乐业县\",\"451029\":\"田林县\",\"451030\":\"西林县\",\"451031\":\"隆林各族自治县\",\"451081\":\"靖西市\"},\"451100\":{\"451101\":\"市辖区\",\"451102\":\"八步区\",\"451103\":\"平桂区\",\"451121\":\"昭平县\",\"451122\":\"钟山县\",\"451123\":\"富川瑶族自治县\"},\"451200\":{\"451201\":\"市辖区\",\"451202\":\"金城江区\",\"451203\":\"宜州区\",\"451221\":\"南丹县\",\"451222\":\"天峨县\",\"451223\":\"凤山县\",\"451224\":\"东兰县\",\"451225\":\"罗城仫佬族自治县\",\"451226\":\"环江毛南族自治县\",\"451227\":\"巴马瑶族自治县\",\"451228\":\"都安瑶族自治县\",\"451229\":\"大化瑶族自治县\"},\"451300\":{\"451301\":\"市辖区\",\"451302\":\"兴宾区\",\"451321\":\"忻城县\",\"451322\":\"象州县\",\"451323\":\"武宣县\",\"451324\":\"金秀瑶族自治县\",\"451381\":\"合山市\"},\"451400\":{\"451401\":\"市辖区\",\"451402\":\"江州区\",\"451421\":\"扶绥县\",\"451422\":\"宁明县\",\"451423\":\"龙州县\",\"451424\":\"大新县\",\"451425\":\"天等县\",\"451481\":\"凭祥市\"},\"460000\":{\"460100\":\"海口市\",\"460200\":\"三亚市\",\"460300\":\"三沙市\",\"460400\":\"儋州市\",\"469000\":\"省直辖县级行政区划\"},\"460100\":{\"460101\":\"市辖区\",\"460105\":\"秀英区\",\"460106\":\"龙华区\",\"460107\":\"琼山区\",\"460108\":\"美兰区\"},\"460200\":{\"460201\":\"市辖区\",\"460202\":\"海棠区\",\"460203\":\"吉阳区\",\"460204\":\"天涯区\",\"460205\":\"崖州区\"},\"460300\":{\"460321\":\"西沙群岛\",\"460322\":\"南沙群岛\",\"460323\":\"中沙群岛的岛礁及其海域\"},\"460400\":{\"460400100\":\"那大镇\",\"460400101\":\"和庆镇\",\"460400102\":\"南丰镇\",\"460400103\":\"大成镇\",\"460400104\":\"雅星镇\",\"460400105\":\"兰洋镇\",\"460400106\":\"光村镇\",\"460400107\":\"木棠镇\",\"460400108\":\"海头镇\",\"460400109\":\"峨蔓镇\",\"460400111\":\"王五镇\",\"460400112\":\"白马井镇\",\"460400113\":\"中和镇\",\"460400114\":\"排浦镇\",\"460400115\":\"东成镇\",\"460400116\":\"新州镇\",\"460400499\":\"洋浦经济开发区\",\"460400500\":\"华南热作学院\"},\"469000\":{\"469001\":\"五指山市\",\"469002\":\"琼海市\",\"469005\":\"文昌市\",\"469006\":\"万宁市\",\"469007\":\"东方市\",\"469021\":\"定安县\",\"469022\":\"屯昌县\",\"469023\":\"澄迈县\",\"469024\":\"临高县\",\"469025\":\"白沙黎族自治县\",\"469026\":\"昌江黎族自治县\",\"469027\":\"乐东黎族自治县\",\"469028\":\"陵水黎族自治县\",\"469029\":\"保亭黎族苗族自治县\",\"469030\":\"琼中黎族苗族自治县\"},\"500000\":{\"500100\":\"市辖区\",\"500200\":\"县\"},\"500100\":{\"500101\":\"万州区\",\"500102\":\"涪陵区\",\"500103\":\"渝中区\",\"500104\":\"大渡口区\",\"500105\":\"江北区\",\"500106\":\"沙坪坝区\",\"500107\":\"九龙坡区\",\"500108\":\"南岸区\",\"500109\":\"北碚区\",\"500110\":\"綦江区\",\"500111\":\"大足区\",\"500112\":\"渝北区\",\"500113\":\"巴南区\",\"500114\":\"黔江区\",\"500115\":\"长寿区\",\"500116\":\"江津区\",\"500117\":\"合川区\",\"500118\":\"永川区\",\"500119\":\"南川区\",\"500120\":\"璧山区\",\"500151\":\"铜梁区\",\"500152\":\"潼南区\",\"500153\":\"荣昌区\",\"500154\":\"开州区\",\"500155\":\"梁平区\",\"500156\":\"武隆区\"},\"500200\":{\"500229\":\"城口县\",\"500230\":\"丰都县\",\"500231\":\"垫江县\",\"500233\":\"忠县\",\"500235\":\"云阳县\",\"500236\":\"奉节县\",\"500237\":\"巫山县\",\"500238\":\"巫溪县\",\"500240\":\"石柱土家族自治县\",\"500241\":\"秀山土家族苗族自治县\",\"500242\":\"酉阳土家族苗族自治县\",\"500243\":\"彭水苗族土家族自治县\"},\"510000\":{\"510100\":\"成都市\",\"510300\":\"自贡市\",\"510400\":\"攀枝花市\",\"510500\":\"泸州市\",\"510600\":\"德阳市\",\"510700\":\"绵阳市\",\"510800\":\"广元市\",\"510900\":\"遂宁市\",\"511000\":\"内江市\",\"511100\":\"乐山市\",\"511300\":\"南充市\",\"511400\":\"眉山市\",\"511500\":\"宜宾市\",\"511600\":\"广安市\",\"511700\":\"达州市\",\"511800\":\"雅安市\",\"511900\":\"巴中市\",\"512000\":\"资阳市\",\"513200\":\"阿坝藏族羌族自治州\",\"513300\":\"甘孜藏族自治州\",\"513400\":\"凉山彝族自治州\"},\"510100\":{\"510101\":\"市辖区\",\"510104\":\"锦江区\",\"510105\":\"青羊区\",\"510106\":\"金牛区\",\"510107\":\"武侯区\",\"510108\":\"成华区\",\"510112\":\"龙泉驿区\",\"510113\":\"青白江区\",\"510114\":\"新都区\",\"510115\":\"温江区\",\"510116\":\"双流区\",\"510117\":\"郫都区\",\"510121\":\"金堂县\",\"510129\":\"大邑县\",\"510131\":\"蒲江县\",\"510132\":\"新津县\",\"510181\":\"都江堰市\",\"510182\":\"彭州市\",\"510183\":\"邛崃市\",\"510184\":\"崇州市\",\"510185\":\"简阳市\"},\"510300\":{\"510301\":\"市辖区\",\"510302\":\"自流井区\",\"510303\":\"贡井区\",\"510304\":\"大安区\",\"510311\":\"沿滩区\",\"510321\":\"荣县\",\"510322\":\"富顺县\"},\"510400\":{\"510401\":\"市辖区\",\"510402\":\"东区\",\"510403\":\"西区\",\"510411\":\"仁和区\",\"510421\":\"米易县\",\"510422\":\"盐边县\"},\"510500\":{\"510501\":\"市辖区\",\"510502\":\"江阳区\",\"510503\":\"纳溪区\",\"510504\":\"龙马潭区\",\"510521\":\"泸县\",\"510522\":\"合江县\",\"510524\":\"叙永县\",\"510525\":\"古蔺县\"},\"510600\":{\"510601\":\"市辖区\",\"510603\":\"旌阳区\",\"510604\":\"罗江区\",\"510623\":\"中江县\",\"510681\":\"广汉市\",\"510682\":\"什邡市\",\"510683\":\"绵竹市\"},\"510700\":{\"510701\":\"市辖区\",\"510703\":\"涪城区\",\"510704\":\"游仙区\",\"510705\":\"安州区\",\"510722\":\"三台县\",\"510723\":\"盐亭县\",\"510725\":\"梓潼县\",\"510726\":\"北川羌族自治县\",\"510727\":\"平武县\",\"510781\":\"江油市\"},\"510800\":{\"510801\":\"市辖区\",\"510802\":\"利州区\",\"510811\":\"昭化区\",\"510812\":\"朝天区\",\"510821\":\"旺苍县\",\"510822\":\"青川县\",\"510823\":\"剑阁县\",\"510824\":\"苍溪县\"},\"510900\":{\"510901\":\"市辖区\",\"510903\":\"船山区\",\"510904\":\"安居区\",\"510921\":\"蓬溪县\",\"510923\":\"大英县\",\"510981\":\"射洪市\"},\"511000\":{\"511001\":\"市辖区\",\"511002\":\"市中区\",\"511011\":\"东兴区\",\"511024\":\"威远县\",\"511025\":\"资中县\",\"511071\":\"内江经济开发区\",\"511083\":\"隆昌市\"},\"511100\":{\"511101\":\"市辖区\",\"511102\":\"市中区\",\"511111\":\"沙湾区\",\"511112\":\"五通桥区\",\"511113\":\"金口河区\",\"511123\":\"犍为县\",\"511124\":\"井研县\",\"511126\":\"夹江县\",\"511129\":\"沐川县\",\"511132\":\"峨边彝族自治县\",\"511133\":\"马边彝族自治县\",\"511181\":\"峨眉山市\"},\"511300\":{\"511301\":\"市辖区\",\"511302\":\"顺庆区\",\"511303\":\"高坪区\",\"511304\":\"嘉陵区\",\"511321\":\"南部县\",\"511322\":\"营山县\",\"511323\":\"蓬安县\",\"511324\":\"仪陇县\",\"511325\":\"西充县\",\"511381\":\"阆中市\"},\"511400\":{\"511401\":\"市辖区\",\"511402\":\"东坡区\",\"511403\":\"彭山区\",\"511421\":\"仁寿县\",\"511423\":\"洪雅县\",\"511424\":\"丹棱县\",\"511425\":\"青神县\"},\"511500\":{\"511501\":\"市辖区\",\"511502\":\"翠屏区\",\"511503\":\"南溪区\",\"511504\":\"叙州区\",\"511523\":\"江安县\",\"511524\":\"长宁县\",\"511525\":\"高县\",\"511526\":\"珙县\",\"511527\":\"筠连县\",\"511528\":\"兴文县\",\"511529\":\"屏山县\"},\"511600\":{\"511601\":\"市辖区\",\"511602\":\"广安区\",\"511603\":\"前锋区\",\"511621\":\"岳池县\",\"511622\":\"武胜县\",\"511623\":\"邻水县\",\"511681\":\"华蓥市\"},\"511700\":{\"511701\":\"市辖区\",\"511702\":\"通川区\",\"511703\":\"达川区\",\"511722\":\"宣汉县\",\"511723\":\"开江县\",\"511724\":\"大竹县\",\"511725\":\"渠县\",\"511771\":\"达州经济开发区\",\"511781\":\"万源市\"},\"511800\":{\"511801\":\"市辖区\",\"511802\":\"雨城区\",\"511803\":\"名山区\",\"511822\":\"荥经县\",\"511823\":\"汉源县\",\"511824\":\"石棉县\",\"511825\":\"天全县\",\"511826\":\"芦山县\",\"511827\":\"宝兴县\"},\"511900\":{\"511901\":\"市辖区\",\"511902\":\"巴州区\",\"511903\":\"恩阳区\",\"511921\":\"通江县\",\"511922\":\"南江县\",\"511923\":\"平昌县\",\"511971\":\"巴中经济开发区\"},\"512000\":{\"512001\":\"市辖区\",\"512002\":\"雁江区\",\"512021\":\"安岳县\",\"512022\":\"乐至县\"},\"513200\":{\"513201\":\"马尔康市\",\"513221\":\"汶川县\",\"513222\":\"理县\",\"513223\":\"茂县\",\"513224\":\"松潘县\",\"513225\":\"九寨沟县\",\"513226\":\"金川县\",\"513227\":\"小金县\",\"513228\":\"黑水县\",\"513230\":\"壤塘县\",\"513231\":\"阿坝县\",\"513232\":\"若尔盖县\",\"513233\":\"红原县\"},\"513300\":{\"513301\":\"康定市\",\"513322\":\"泸定县\",\"513323\":\"丹巴县\",\"513324\":\"九龙县\",\"513325\":\"雅江县\",\"513326\":\"道孚县\",\"513327\":\"炉霍县\",\"513328\":\"甘孜县\",\"513329\":\"新龙县\",\"513330\":\"德格县\",\"513331\":\"白玉县\",\"513332\":\"石渠县\",\"513333\":\"色达县\",\"513334\":\"理塘县\",\"513335\":\"巴塘县\",\"513336\":\"乡城县\",\"513337\":\"稻城县\",\"513338\":\"得荣县\"},\"513400\":{\"513401\":\"西昌市\",\"513422\":\"木里藏族自治县\",\"513423\":\"盐源县\",\"513424\":\"德昌县\",\"513425\":\"会理县\",\"513426\":\"会东县\",\"513427\":\"宁南县\",\"513428\":\"普格县\",\"513429\":\"布拖县\",\"513430\":\"金阳县\",\"513431\":\"昭觉县\",\"513432\":\"喜德县\",\"513433\":\"冕宁县\",\"513434\":\"越西县\",\"513435\":\"甘洛县\",\"513436\":\"美姑县\",\"513437\":\"雷波县\"},\"520000\":{\"520100\":\"贵阳市\",\"520200\":\"六盘水市\",\"520300\":\"遵义市\",\"520400\":\"安顺市\",\"520500\":\"毕节市\",\"520600\":\"铜仁市\",\"522300\":\"黔西南布依族苗族自治州\",\"522600\":\"黔东南苗族侗族自治州\",\"522700\":\"黔南布依族苗族自治州\"},\"520100\":{\"520101\":\"市辖区\",\"520102\":\"南明区\",\"520103\":\"云岩区\",\"520111\":\"花溪区\",\"520112\":\"乌当区\",\"520113\":\"白云区\",\"520115\":\"观山湖区\",\"520121\":\"开阳县\",\"520122\":\"息烽县\",\"520123\":\"修文县\",\"520181\":\"清镇市\"},\"520200\":{\"520201\":\"钟山区\",\"520203\":\"六枝特区\",\"520221\":\"水城县\",\"520281\":\"盘州市\"},\"520300\":{\"520301\":\"市辖区\",\"520302\":\"红花岗区\",\"520303\":\"汇川区\",\"520304\":\"播州区\",\"520322\":\"桐梓县\",\"520323\":\"绥阳县\",\"520324\":\"正安县\",\"520325\":\"道真仡佬族苗族自治县\",\"520326\":\"务川仡佬族苗族自治县\",\"520327\":\"凤冈县\",\"520328\":\"湄潭县\",\"520329\":\"余庆县\",\"520330\":\"习水县\",\"520381\":\"赤水市\",\"520382\":\"仁怀市\"},\"520400\":{\"520401\":\"市辖区\",\"520402\":\"西秀区\",\"520403\":\"平坝区\",\"520422\":\"普定县\",\"520423\":\"镇宁布依族苗族自治县\",\"520424\":\"关岭布依族苗族自治县\",\"520425\":\"紫云苗族布依族自治县\"},\"520500\":{\"520501\":\"市辖区\",\"520502\":\"七星关区\",\"520521\":\"大方县\",\"520522\":\"黔西县\",\"520523\":\"金沙县\",\"520524\":\"织金县\",\"520525\":\"纳雍县\",\"520526\":\"威宁彝族回族苗族自治县\",\"520527\":\"赫章县\"},\"520600\":{\"520601\":\"市辖区\",\"520602\":\"碧江区\",\"520603\":\"万山区\",\"520621\":\"江口县\",\"520622\":\"玉屏侗族自治县\",\"520623\":\"石阡县\",\"520624\":\"思南县\",\"520625\":\"印江土家族苗族自治县\",\"520626\":\"德江县\",\"520627\":\"沿河土家族自治县\",\"520628\":\"松桃苗族自治县\"},\"522300\":{\"522301\":\"兴义市\",\"522302\":\"兴仁市\",\"522323\":\"普安县\",\"522324\":\"晴隆县\",\"522325\":\"贞丰县\",\"522326\":\"望谟县\",\"522327\":\"册亨县\",\"522328\":\"安龙县\"},\"522600\":{\"522601\":\"凯里市\",\"522622\":\"黄平县\",\"522623\":\"施秉县\",\"522624\":\"三穗县\",\"522625\":\"镇远县\",\"522626\":\"岑巩县\",\"522627\":\"天柱县\",\"522628\":\"锦屏县\",\"522629\":\"剑河县\",\"522630\":\"台江县\",\"522631\":\"黎平县\",\"522632\":\"榕江县\",\"522633\":\"从江县\",\"522634\":\"雷山县\",\"522635\":\"麻江县\",\"522636\":\"丹寨县\"},\"522700\":{\"522701\":\"都匀市\",\"522702\":\"福泉市\",\"522722\":\"荔波县\",\"522723\":\"贵定县\",\"522725\":\"瓮安县\",\"522726\":\"独山县\",\"522727\":\"平塘县\",\"522728\":\"罗甸县\",\"522729\":\"长顺县\",\"522730\":\"龙里县\",\"522731\":\"惠水县\",\"522732\":\"三都水族自治县\"},\"530000\":{\"530100\":\"昆明市\",\"530300\":\"曲靖市\",\"530400\":\"玉溪市\",\"530500\":\"保山市\",\"530600\":\"昭通市\",\"530700\":\"丽江市\",\"530800\":\"普洱市\",\"530900\":\"临沧市\",\"532300\":\"楚雄彝族自治州\",\"532500\":\"红河哈尼族彝族自治州\",\"532600\":\"文山壮族苗族自治州\",\"532800\":\"西双版纳傣族自治州\",\"532900\":\"大理白族自治州\",\"533100\":\"德宏傣族景颇族自治州\",\"533300\":\"怒江傈僳族自治州\",\"533400\":\"迪庆藏族自治州\"},\"530100\":{\"530101\":\"市辖区\",\"530102\":\"五华区\",\"530103\":\"盘龙区\",\"530111\":\"官渡区\",\"530112\":\"西山区\",\"530113\":\"东川区\",\"530114\":\"呈贡区\",\"530115\":\"晋宁区\",\"530124\":\"富民县\",\"530125\":\"宜良县\",\"530126\":\"石林彝族自治县\",\"530127\":\"嵩明县\",\"530128\":\"禄劝彝族苗族自治县\",\"530129\":\"寻甸回族彝族自治县\",\"530181\":\"安宁市\"},\"530300\":{\"530301\":\"市辖区\",\"530302\":\"麒麟区\",\"530303\":\"沾益区\",\"530304\":\"马龙区\",\"530322\":\"陆良县\",\"530323\":\"师宗县\",\"530324\":\"罗平县\",\"530325\":\"富源县\",\"530326\":\"会泽县\",\"530381\":\"宣威市\"},\"530400\":{\"530401\":\"市辖区\",\"530402\":\"红塔区\",\"530403\":\"江川区\",\"530422\":\"澄江县\",\"530423\":\"通海县\",\"530424\":\"华宁县\",\"530425\":\"易门县\",\"530426\":\"峨山彝族自治县\",\"530427\":\"新平彝族傣族自治县\",\"530428\":\"元江哈尼族彝族傣族自治县\"},\"530500\":{\"530501\":\"市辖区\",\"530502\":\"隆阳区\",\"530521\":\"施甸县\",\"530523\":\"龙陵县\",\"530524\":\"昌宁县\",\"530581\":\"腾冲市\"},\"530600\":{\"530601\":\"市辖区\",\"530602\":\"昭阳区\",\"530621\":\"鲁甸县\",\"530622\":\"巧家县\",\"530623\":\"盐津县\",\"530624\":\"大关县\",\"530625\":\"永善县\",\"530626\":\"绥江县\",\"530627\":\"镇雄县\",\"530628\":\"彝良县\",\"530629\":\"威信县\",\"530681\":\"水富市\"},\"530700\":{\"530701\":\"市辖区\",\"530702\":\"古城区\",\"530721\":\"玉龙纳西族自治县\",\"530722\":\"永胜县\",\"530723\":\"华坪县\",\"530724\":\"宁蒗彝族自治县\"},\"530800\":{\"530801\":\"市辖区\",\"530802\":\"思茅区\",\"530821\":\"宁洱哈尼族彝族自治县\",\"530822\":\"墨江哈尼族自治县\",\"530823\":\"景东彝族自治县\",\"530824\":\"景谷傣族彝族自治县\",\"530825\":\"镇沅彝族哈尼族拉祜族自治县\",\"530826\":\"江城哈尼族彝族自治县\",\"530827\":\"孟连傣族拉祜族佤族自治县\",\"530828\":\"澜沧拉祜族自治县\",\"530829\":\"西盟佤族自治县\"},\"530900\":{\"530901\":\"市辖区\",\"530902\":\"临翔区\",\"530921\":\"凤庆县\",\"530922\":\"云县\",\"530923\":\"永德县\",\"530924\":\"镇康县\",\"530925\":\"双江拉祜族佤族布朗族傣族自治县\",\"530926\":\"耿马傣族佤族自治县\",\"530927\":\"沧源佤族自治县\"},\"532300\":{\"532301\":\"楚雄市\",\"532322\":\"双柏县\",\"532323\":\"牟定县\",\"532324\":\"南华县\",\"532325\":\"姚安县\",\"532326\":\"大姚县\",\"532327\":\"永仁县\",\"532328\":\"元谋县\",\"532329\":\"武定县\",\"532331\":\"禄丰县\"},\"532500\":{\"532501\":\"个旧市\",\"532502\":\"开远市\",\"532503\":\"蒙自市\",\"532504\":\"弥勒市\",\"532523\":\"屏边苗族自治县\",\"532524\":\"建水县\",\"532525\":\"石屏县\",\"532527\":\"泸西县\",\"532528\":\"元阳县\",\"532529\":\"红河县\",\"532530\":\"金平苗族瑶族傣族自治县\",\"532531\":\"绿春县\",\"532532\":\"河口瑶族自治县\"},\"532600\":{\"532601\":\"文山市\",\"532622\":\"砚山县\",\"532623\":\"西畴县\",\"532624\":\"麻栗坡县\",\"532625\":\"马关县\",\"532626\":\"丘北县\",\"532627\":\"广南县\",\"532628\":\"富宁县\"},\"532800\":{\"532801\":\"景洪市\",\"532822\":\"勐海县\",\"532823\":\"勐腊县\"},\"532900\":{\"532901\":\"大理市\",\"532922\":\"漾濞彝族自治县\",\"532923\":\"祥云县\",\"532924\":\"宾川县\",\"532925\":\"弥渡县\",\"532926\":\"南涧彝族自治县\",\"532927\":\"巍山彝族回族自治县\",\"532928\":\"永平县\",\"532929\":\"云龙县\",\"532930\":\"洱源县\",\"532931\":\"剑川县\",\"532932\":\"鹤庆县\"},\"533100\":{\"533102\":\"瑞丽市\",\"533103\":\"芒市\",\"533122\":\"梁河县\",\"533123\":\"盈江县\",\"533124\":\"陇川县\"},\"533300\":{\"533301\":\"泸水市\",\"533323\":\"福贡县\",\"533324\":\"贡山独龙族怒族自治县\",\"533325\":\"兰坪白族普米族自治县\"},\"533400\":{\"533401\":\"香格里拉市\",\"533422\":\"德钦县\",\"533423\":\"维西傈僳族自治县\"},\"540000\":{\"540100\":\"拉萨市\",\"540200\":\"日喀则市\",\"540300\":\"昌都市\",\"540400\":\"林芝市\",\"540500\":\"山南市\",\"540600\":\"那曲市\",\"542500\":\"阿里地区\"},\"540100\":{\"540101\":\"市辖区\",\"540102\":\"城关区\",\"540103\":\"堆龙德庆区\",\"540104\":\"达孜区\",\"540121\":\"林周县\",\"540122\":\"当雄县\",\"540123\":\"尼木县\",\"540124\":\"曲水县\",\"540127\":\"墨竹工卡县\",\"540171\":\"格尔木藏青工业园区\",\"540172\":\"拉萨经济技术开发区\",\"540173\":\"西藏文化旅游创意园区\",\"540174\":\"达孜工业园区\"},\"540200\":{\"540202\":\"桑珠孜区\",\"540221\":\"南木林县\",\"540222\":\"江孜县\",\"540223\":\"定日县\",\"540224\":\"萨迦县\",\"540225\":\"拉孜县\",\"540226\":\"昂仁县\",\"540227\":\"谢通门县\",\"540228\":\"白朗县\",\"540229\":\"仁布县\",\"540230\":\"康马县\",\"540231\":\"定结县\",\"540232\":\"仲巴县\",\"540233\":\"亚东县\",\"540234\":\"吉隆县\",\"540235\":\"聂拉木县\",\"540236\":\"萨嘎县\",\"540237\":\"岗巴县\"},\"540300\":{\"540302\":\"卡若区\",\"540321\":\"江达县\",\"540322\":\"贡觉县\",\"540323\":\"类乌齐县\",\"540324\":\"丁青县\",\"540325\":\"察雅县\",\"540326\":\"八宿县\",\"540327\":\"左贡县\",\"540328\":\"芒康县\",\"540329\":\"洛隆县\",\"540330\":\"边坝县\"},\"540400\":{\"540402\":\"巴宜区\",\"540421\":\"工布江达县\",\"540422\":\"米林县\",\"540423\":\"墨脱县\",\"540424\":\"波密县\",\"540425\":\"察隅县\",\"540426\":\"朗县\"},\"540500\":{\"540501\":\"市辖区\",\"540502\":\"乃东区\",\"540521\":\"扎囊县\",\"540522\":\"贡嘎县\",\"540523\":\"桑日县\",\"540524\":\"琼结县\",\"540525\":\"曲松县\",\"540526\":\"措美县\",\"540527\":\"洛扎县\",\"540528\":\"加查县\",\"540529\":\"隆子县\",\"540530\":\"错那县\",\"540531\":\"浪卡子县\"},\"540600\":{\"540602\":\"色尼区\",\"540621\":\"嘉黎县\",\"540622\":\"比如县\",\"540623\":\"聂荣县\",\"540624\":\"安多县\",\"540625\":\"申扎县\",\"540626\":\"索县\",\"540627\":\"班戈县\",\"540628\":\"巴青县\",\"540629\":\"尼玛县\",\"540630\":\"双湖县\"},\"542500\":{\"542521\":\"普兰县\",\"542522\":\"札达县\",\"542523\":\"噶尔县\",\"542524\":\"日土县\",\"542525\":\"革吉县\",\"542526\":\"改则县\",\"542527\":\"措勤县\"},\"610000\":{\"610100\":\"西安市\",\"610200\":\"铜川市\",\"610300\":\"宝鸡市\",\"610400\":\"咸阳市\",\"610500\":\"渭南市\",\"610600\":\"延安市\",\"610700\":\"汉中市\",\"610800\":\"榆林市\",\"610900\":\"安康市\",\"611000\":\"商洛市\"},\"610100\":{\"610101\":\"市辖区\",\"610102\":\"新城区\",\"610103\":\"碑林区\",\"610104\":\"莲湖区\",\"610111\":\"灞桥区\",\"610112\":\"未央区\",\"610113\":\"雁塔区\",\"610114\":\"阎良区\",\"610115\":\"临潼区\",\"610116\":\"长安区\",\"610117\":\"高陵区\",\"610118\":\"鄠邑区\",\"610122\":\"蓝田县\",\"610124\":\"周至县\"},\"610200\":{\"610201\":\"市辖区\",\"610202\":\"王益区\",\"610203\":\"印台区\",\"610204\":\"耀州区\",\"610222\":\"宜君县\"},\"610300\":{\"610301\":\"市辖区\",\"610302\":\"渭滨区\",\"610303\":\"金台区\",\"610304\":\"陈仓区\",\"610322\":\"凤翔县\",\"610323\":\"岐山县\",\"610324\":\"扶风县\",\"610326\":\"眉县\",\"610327\":\"陇县\",\"610328\":\"千阳县\",\"610329\":\"麟游县\",\"610330\":\"凤县\",\"610331\":\"太白县\"},\"610400\":{\"610401\":\"市辖区\",\"610402\":\"秦都区\",\"610403\":\"杨陵区\",\"610404\":\"渭城区\",\"610422\":\"三原县\",\"610423\":\"泾阳县\",\"610424\":\"乾县\",\"610425\":\"礼泉县\",\"610426\":\"永寿县\",\"610428\":\"长武县\",\"610429\":\"旬邑县\",\"610430\":\"淳化县\",\"610431\":\"武功县\",\"610481\":\"兴平市\",\"610482\":\"彬州市\"},\"610500\":{\"610501\":\"市辖区\",\"610502\":\"临渭区\",\"610503\":\"华州区\",\"610522\":\"潼关县\",\"610523\":\"大荔县\",\"610524\":\"合阳县\",\"610525\":\"澄城县\",\"610526\":\"蒲城县\",\"610527\":\"白水县\",\"610528\":\"富平县\",\"610581\":\"韩城市\",\"610582\":\"华阴市\"},\"610600\":{\"610601\":\"市辖区\",\"610602\":\"宝塔区\",\"610603\":\"安塞区\",\"610621\":\"延长县\",\"610622\":\"延川县\",\"610625\":\"志丹县\",\"610626\":\"吴起县\",\"610627\":\"甘泉县\",\"610628\":\"富县\",\"610629\":\"洛川县\",\"610630\":\"宜川县\",\"610631\":\"黄龙县\",\"610632\":\"黄陵县\",\"610681\":\"子长市\"},\"610700\":{\"610701\":\"市辖区\",\"610702\":\"汉台区\",\"610703\":\"南郑区\",\"610722\":\"城固县\",\"610723\":\"洋县\",\"610724\":\"西乡县\",\"610725\":\"勉县\",\"610726\":\"宁强县\",\"610727\":\"略阳县\",\"610728\":\"镇巴县\",\"610729\":\"留坝县\",\"610730\":\"佛坪县\"},\"610800\":{\"610801\":\"市辖区\",\"610802\":\"榆阳区\",\"610803\":\"横山区\",\"610822\":\"府谷县\",\"610824\":\"靖边县\",\"610825\":\"定边县\",\"610826\":\"绥德县\",\"610827\":\"米脂县\",\"610828\":\"佳县\",\"610829\":\"吴堡县\",\"610830\":\"清涧县\",\"610831\":\"子洲县\",\"610881\":\"神木市\"},\"610900\":{\"610901\":\"市辖区\",\"610902\":\"汉滨区\",\"610921\":\"汉阴县\",\"610922\":\"石泉县\",\"610923\":\"宁陕县\",\"610924\":\"紫阳县\",\"610925\":\"岚皋县\",\"610926\":\"平利县\",\"610927\":\"镇坪县\",\"610928\":\"旬阳县\",\"610929\":\"白河县\"},\"611000\":{\"611001\":\"市辖区\",\"611002\":\"商州区\",\"611021\":\"洛南县\",\"611022\":\"丹凤县\",\"611023\":\"商南县\",\"611024\":\"山阳县\",\"611025\":\"镇安县\",\"611026\":\"柞水县\"},\"620000\":{\"620100\":\"兰州市\",\"620200\":\"嘉峪关市\",\"620300\":\"金昌市\",\"620400\":\"白银市\",\"620500\":\"天水市\",\"620600\":\"武威市\",\"620700\":\"张掖市\",\"620800\":\"平凉市\",\"620900\":\"酒泉市\",\"621000\":\"庆阳市\",\"621100\":\"定西市\",\"621200\":\"陇南市\",\"622900\":\"临夏回族自治州\",\"623000\":\"甘南藏族自治州\"},\"620100\":{\"620101\":\"市辖区\",\"620102\":\"城关区\",\"620103\":\"七里河区\",\"620104\":\"西固区\",\"620105\":\"安宁区\",\"620111\":\"红古区\",\"620121\":\"永登县\",\"620122\":\"皋兰县\",\"620123\":\"榆中县\",\"620171\":\"兰州新区\"},\"620200\":{\"620201\":\"市辖区\"},\"620300\":{\"620301\":\"市辖区\",\"620302\":\"金川区\",\"620321\":\"永昌县\"},\"620400\":{\"620401\":\"市辖区\",\"620402\":\"白银区\",\"620403\":\"平川区\",\"620421\":\"靖远县\",\"620422\":\"会宁县\",\"620423\":\"景泰县\"},\"620500\":{\"620501\":\"市辖区\",\"620502\":\"秦州区\",\"620503\":\"麦积区\",\"620521\":\"清水县\",\"620522\":\"秦安县\",\"620523\":\"甘谷县\",\"620524\":\"武山县\",\"620525\":\"张家川回族自治县\"},\"620600\":{\"620601\":\"市辖区\",\"620602\":\"凉州区\",\"620621\":\"民勤县\",\"620622\":\"古浪县\",\"620623\":\"天祝藏族自治县\"},\"620700\":{\"620701\":\"市辖区\",\"620702\":\"甘州区\",\"620721\":\"肃南裕固族自治县\",\"620722\":\"民乐县\",\"620723\":\"临泽县\",\"620724\":\"高台县\",\"620725\":\"山丹县\"},\"620800\":{\"620801\":\"市辖区\",\"620802\":\"崆峒区\",\"620821\":\"泾川县\",\"620822\":\"灵台县\",\"620823\":\"崇信县\",\"620825\":\"庄浪县\",\"620826\":\"静宁县\",\"620881\":\"华亭市\"},\"620900\":{\"620901\":\"市辖区\",\"620902\":\"肃州区\",\"620921\":\"金塔县\",\"620922\":\"瓜州县\",\"620923\":\"肃北蒙古族自治县\",\"620924\":\"阿克塞哈萨克族自治县\",\"620981\":\"玉门市\",\"620982\":\"敦煌市\"},\"621000\":{\"621001\":\"市辖区\",\"621002\":\"西峰区\",\"621021\":\"庆城县\",\"621022\":\"环县\",\"621023\":\"华池县\",\"621024\":\"合水县\",\"621025\":\"正宁县\",\"621026\":\"宁县\",\"621027\":\"镇原县\"},\"621100\":{\"621101\":\"市辖区\",\"621102\":\"安定区\",\"621121\":\"通渭县\",\"621122\":\"陇西县\",\"621123\":\"渭源县\",\"621124\":\"临洮县\",\"621125\":\"漳县\",\"621126\":\"岷县\"},\"621200\":{\"621201\":\"市辖区\",\"621202\":\"武都区\",\"621221\":\"成县\",\"621222\":\"文县\",\"621223\":\"宕昌县\",\"621224\":\"康县\",\"621225\":\"西和县\",\"621226\":\"礼县\",\"621227\":\"徽县\",\"621228\":\"两当县\"},\"622900\":{\"622901\":\"临夏市\",\"622921\":\"临夏县\",\"622922\":\"康乐县\",\"622923\":\"永靖县\",\"622924\":\"广河县\",\"622925\":\"和政县\",\"622926\":\"东乡族自治县\",\"622927\":\"积石山保安族东乡族撒拉族自治县\"},\"623000\":{\"623001\":\"合作市\",\"623021\":\"临潭县\",\"623022\":\"卓尼县\",\"623023\":\"舟曲县\",\"623024\":\"迭部县\",\"623025\":\"玛曲县\",\"623026\":\"碌曲县\",\"623027\":\"夏河县\"},\"630000\":{\"630100\":\"西宁市\",\"630200\":\"海东市\",\"632200\":\"海北藏族自治州\",\"632300\":\"黄南藏族自治州\",\"632500\":\"海南藏族自治州\",\"632600\":\"果洛藏族自治州\",\"632700\":\"玉树藏族自治州\",\"632800\":\"海西蒙古族藏族自治州\"},\"630100\":{\"630101\":\"市辖区\",\"630102\":\"城东区\",\"630103\":\"城中区\",\"630104\":\"城西区\",\"630105\":\"城北区\",\"630121\":\"大通回族土族自治县\",\"630122\":\"湟中县\",\"630123\":\"湟源县\"},\"630200\":{\"630202\":\"乐都区\",\"630203\":\"平安区\",\"630222\":\"民和回族土族自治县\",\"630223\":\"互助土族自治县\",\"630224\":\"化隆回族自治县\",\"630225\":\"循化撒拉族自治县\"},\"632200\":{\"632221\":\"门源回族自治县\",\"632222\":\"祁连县\",\"632223\":\"海晏县\",\"632224\":\"刚察县\"},\"632300\":{\"632321\":\"同仁县\",\"632322\":\"尖扎县\",\"632323\":\"泽库县\",\"632324\":\"河南蒙古族自治县\"},\"632500\":{\"632521\":\"共和县\",\"632522\":\"同德县\",\"632523\":\"贵德县\",\"632524\":\"兴海县\",\"632525\":\"贵南县\"},\"632600\":{\"632621\":\"玛沁县\",\"632622\":\"班玛县\",\"632623\":\"甘德县\",\"632624\":\"达日县\",\"632625\":\"久治县\",\"632626\":\"玛多县\"},\"632700\":{\"632701\":\"玉树市\",\"632722\":\"杂多县\",\"632723\":\"称多县\",\"632724\":\"治多县\",\"632725\":\"囊谦县\",\"632726\":\"曲麻莱县\"},\"632800\":{\"632801\":\"格尔木市\",\"632802\":\"德令哈市\",\"632803\":\"茫崖市\",\"632821\":\"乌兰县\",\"632822\":\"都兰县\",\"632823\":\"天峻县\",\"632857\":\"大柴旦行政委员会\"},\"640000\":{\"640100\":\"银川市\",\"640200\":\"石嘴山市\",\"640300\":\"吴忠市\",\"640400\":\"固原市\",\"640500\":\"中卫市\"},\"640100\":{\"640101\":\"市辖区\",\"640104\":\"兴庆区\",\"640105\":\"西夏区\",\"640106\":\"金凤区\",\"640121\":\"永宁县\",\"640122\":\"贺兰县\",\"640181\":\"灵武市\"},\"640200\":{\"640201\":\"市辖区\",\"640202\":\"大武口区\",\"640205\":\"惠农区\",\"640221\":\"平罗县\"},\"640300\":{\"640301\":\"市辖区\",\"640302\":\"利通区\",\"640303\":\"红寺堡区\",\"640323\":\"盐池县\",\"640324\":\"同心县\",\"640381\":\"青铜峡市\"},\"640400\":{\"640401\":\"市辖区\",\"640402\":\"原州区\",\"640422\":\"西吉县\",\"640423\":\"隆德县\",\"640424\":\"泾源县\",\"640425\":\"彭阳县\"},\"640500\":{\"640501\":\"市辖区\",\"640502\":\"沙坡头区\",\"640521\":\"中宁县\",\"640522\":\"海原县\"},\"650000\":{\"650100\":\"乌鲁木齐市\",\"650200\":\"克拉玛依市\",\"650400\":\"吐鲁番市\",\"650500\":\"哈密市\",\"652300\":\"昌吉回族自治州\",\"652700\":\"博尔塔拉蒙古自治州\",\"652800\":\"巴音郭楞蒙古自治州\",\"652900\":\"阿克苏地区\",\"653000\":\"克孜勒苏柯尔克孜自治州\",\"653100\":\"喀什地区\",\"653200\":\"和田地区\",\"654000\":\"伊犁哈萨克自治州\",\"654200\":\"塔城地区\",\"654300\":\"阿勒泰地区\",\"659000\":\"自治区直辖县级行政区划\"},\"650100\":{\"650101\":\"市辖区\",\"650102\":\"天山区\",\"650103\":\"沙依巴克区\",\"650104\":\"新市区\",\"650105\":\"水磨沟区\",\"650106\":\"头屯河区\",\"650107\":\"达坂城区\",\"650109\":\"米东区\",\"650121\":\"乌鲁木齐县\"},\"650200\":{\"650201\":\"市辖区\",\"650202\":\"独山子区\",\"650203\":\"克拉玛依区\",\"650204\":\"白碱滩区\",\"650205\":\"乌尔禾区\"},\"650400\":{\"650402\":\"高昌区\",\"650421\":\"鄯善县\",\"650422\":\"托克逊县\"},\"650500\":{\"650502\":\"伊州区\",\"650521\":\"巴里坤哈萨克自治县\",\"650522\":\"伊吾县\"},\"652300\":{\"652301\":\"昌吉市\",\"652302\":\"阜康市\",\"652323\":\"呼图壁县\",\"652324\":\"玛纳斯县\",\"652325\":\"奇台县\",\"652327\":\"吉木萨尔县\",\"652328\":\"木垒哈萨克自治县\"},\"652700\":{\"652701\":\"博乐市\",\"652702\":\"阿拉山口市\",\"652722\":\"精河县\",\"652723\":\"温泉县\"},\"652800\":{\"652801\":\"库尔勒市\",\"652822\":\"轮台县\",\"652823\":\"尉犁县\",\"652824\":\"若羌县\",\"652825\":\"且末县\",\"652826\":\"焉耆回族自治县\",\"652827\":\"和静县\",\"652828\":\"和硕县\",\"652829\":\"博湖县\",\"652871\":\"库尔勒经济技术开发区\"},\"652900\":{\"652901\":\"阿克苏市\",\"652922\":\"温宿县\",\"652923\":\"库车县\",\"652924\":\"沙雅县\",\"652925\":\"新和县\",\"652926\":\"拜城县\",\"652927\":\"乌什县\",\"652928\":\"阿瓦提县\",\"652929\":\"柯坪县\"},\"653000\":{\"653001\":\"阿图什市\",\"653022\":\"阿克陶县\",\"653023\":\"阿合奇县\",\"653024\":\"乌恰县\"},\"653100\":{\"653101\":\"喀什市\",\"653121\":\"疏附县\",\"653122\":\"疏勒县\",\"653123\":\"英吉沙县\",\"653124\":\"泽普县\",\"653125\":\"莎车县\",\"653126\":\"叶城县\",\"653127\":\"麦盖提县\",\"653128\":\"岳普湖县\",\"653129\":\"伽师县\",\"653130\":\"巴楚县\",\"653131\":\"塔什库尔干塔吉克自治县\"},\"653200\":{\"653201\":\"和田市\",\"653221\":\"和田县\",\"653222\":\"墨玉县\",\"653223\":\"皮山县\",\"653224\":\"洛浦县\",\"653225\":\"策勒县\",\"653226\":\"于田县\",\"653227\":\"民丰县\"},\"654000\":{\"654002\":\"伊宁市\",\"654003\":\"奎屯市\",\"654004\":\"霍尔果斯市\",\"654021\":\"伊宁县\",\"654022\":\"察布查尔锡伯自治县\",\"654023\":\"霍城县\",\"654024\":\"巩留县\",\"654025\":\"新源县\",\"654026\":\"昭苏县\",\"654027\":\"特克斯县\",\"654028\":\"尼勒克县\"},\"654200\":{\"654201\":\"塔城市\",\"654202\":\"乌苏市\",\"654221\":\"额敏县\",\"654223\":\"沙湾县\",\"654224\":\"托里县\",\"654225\":\"裕民县\",\"654226\":\"和布克赛尔蒙古自治县\"},\"654300\":{\"654301\":\"阿勒泰市\",\"654321\":\"布尔津县\",\"654322\":\"富蕴县\",\"654323\":\"福海县\",\"654324\":\"哈巴河县\",\"654325\":\"青河县\",\"654326\":\"吉木乃县\"},\"659000\":{\"659001\":\"石河子市\",\"659002\":\"阿拉尔市\",\"659003\":\"图木舒克市\",\"659004\":\"五家渠市\",\"659006\":\"铁门关市\"},\"710000\":{\"710100\":\"台北市\",\"710200\":\"高雄市\",\"710300\":\"基隆市\",\"710400\":\"台中市\",\"710500\":\"台南市\",\"710600\":\"新竹市\",\"710700\":\"嘉义市\"},\"710100\":{\"710101\":\"内湖区\",\"710102\":\"南港区\",\"710103\":\"中正区\",\"710104\":\"松山区\",\"710105\":\"信义区\",\"710106\":\"大安区\",\"710107\":\"中山区\",\"710108\":\"文山区\",\"710109\":\"大同区\",\"710110\":\"万华区\",\"710111\":\"士林区\",\"710112\":\"北投区\"},\"710200\":{\"710201\":\"新兴区\",\"710202\":\"前金区\",\"710203\":\"芩雅区\",\"710204\":\"盐埕区\",\"710205\":\"鼓山区\",\"710206\":\"旗津区\",\"710207\":\"前镇区\",\"710208\":\"三民区\",\"710209\":\"左营区\",\"710210\":\"楠梓区\",\"710211\":\"小港区\"},\"710300\":{\"710301\":\"仁爱区\",\"710302\":\"信义区\",\"710303\":\"中正区\",\"710304\":\"暖暖区\",\"710305\":\"安乐区\",\"710307\":\"七堵区\"},\"710400\":{\"710301\":\"中区\",\"710302\":\"东区\",\"710303\":\"南区\",\"710304\":\"西区\",\"710305\":\"北区\",\"710306\":\"北屯区\",\"710307\":\"西屯区\",\"710308\":\"南屯区\"},\"710500\":{\"710501\":\"中西区\",\"710502\":\"东区\",\"710503\":\"南区\",\"710504\":\"北区\",\"710505\":\"安平区\",\"710506\":\"安南区\"},\"710600\":{\"710601\":\"东区\",\"710602\":\"北区\",\"710603\":\"香山区\"},\"710700\":{\"710701\":\"东区\",\"710702\":\"西区\"},\"810000\":{\"810001\":\"中西區\",\"810002\":\"灣仔區\",\"810003\":\"東區\",\"810004\":\"南區\",\"810005\":\"油尖旺區\",\"810006\":\"深水埗區\",\"810007\":\"九龍城區\",\"810008\":\"黃大仙區\",\"810009\":\"觀塘區\",\"810010\":\"荃灣區\",\"810011\":\"屯門區\",\"810012\":\"元朗區\",\"810013\":\"北區\",\"810014\":\"大埔區\",\"810015\":\"西貢區\",\"810016\":\"沙田區\",\"810017\":\"葵青區\",\"810018\":\"離島區\"},\"820000\":{\"820001\":\"花地瑪堂區\",\"820002\":\"花王堂區\",\"820003\":\"望德堂區\",\"820004\":\"大堂區\",\"820005\":\"風順堂區\",\"820006\":\"嘉模堂區\",\"820007\":\"路氹填海區\",\"820008\":\"聖方濟各堂區\"}}')},function(t,e){var r;r=function(){return this}();try{r=r||new Function(\"return this\")()}catch(t){\"object\"==typeof window&&(r=window)}t.exports=r},function(t,e,r){\"use strict\";r.r(e),r.d(e,\"provinceAndCityData\",(function(){return rr})),r.d(e,\"regionData\",(function(){return er})),r.d(e,\"provinceAndCityDataPlus\",(function(){return Sr})),r.d(e,\"regionDataPlus\",(function(){return xr})),r.d(e,\"CodeToText\",(function(){return Xe})),r.d(e,\"TextToCode\",(function(){return Qe}));var n=r(1),i=r.n(n),s=function(){this.__data__=[],this.size=0},o=function(t,e){return t===e||t!=t&&e!=e},a=function(t,e){for(var r=t.length;r--;)if(o(t[r][0],e))return r;return-1},h=Array.prototype.splice,u=function(t){var e=this.__data__,r=a(e,t);return!(r<0)&&(r==e.length-1?e.pop():h.call(e,r,1),--this.size,!0)},c=function(t){var e=this.__data__,r=a(e,t);return r<0?void 0:e[r][1]},f=function(t){return a(this.__data__,t)>-1},l=function(t,e){var r=this.__data__,n=a(r,t);return n<0?(++this.size,r.push([t,e])):r[n][1]=e,this};function p(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}p.prototype.clear=s,p.prototype.delete=u,p.prototype.get=c,p.prototype.has=f,p.prototype.set=l;var g,d=p,v=function(){this.__data__=new d,this.size=0},y=function(t){var e=this.__data__,r=e.delete(t);return this.size=e.size,r},b=function(t){return this.__data__.get(t)},m=function(t){return this.__data__.has(t)},S=r(0),T=S.a.Symbol,w=Object.prototype,E=w.hasOwnProperty,D=w.toString,j=T?T.toStringTag:void 0,O=function(t){var e=E.call(t,j),r=t[j];try{t[j]=void 0;var n=!0}catch(t){}var i=D.call(t);return n&&(e?t[j]=r:delete t[j]),i},x=Object.prototype.toString,A=function(t){return x.call(t)},B=T?T.toStringTag:void 0,R=function(t){return null==t?void 0===t?\"[object Undefined]\":\"[object Null]\":B&&B in Object(t)?O(t):A(t)},V=function(t){var e=typeof t;return null!=t&&(\"object\"==e||\"function\"==e)},_=function(t){if(!V(t))return!1;var e=R(t);return\"[object Function]\"==e||\"[object GeneratorFunction]\"==e||\"[object AsyncFunction]\"==e||\"[object Proxy]\"==e},I=S.a[\"__core-js_shared__\"],P=(g=/[^.]+$/.exec(I&&I.keys&&I.keys.IE_PROTO||\"\"))?\"Symbol(src)_1.\"+g:\"\",N=function(t){return!!P&&P in t},M=Function.prototype.toString,q=function(t){if(null!=t){try{return M.call(t)}catch(t){}try{return t+\"\"}catch(t){}}return\"\"},L=/^\\[object .+?Constructor\\]$/,C=Function.prototype,H=Object.prototype,F=C.toString,U=H.hasOwnProperty,k=RegExp(\"^\"+F.call(U).replace(/[\\\\^$.*+?()[\\]{}|]/g,\"\\\\$&\").replace(/hasOwnProperty|(function).*?(?=\\\\\\()| for .+?(?=\\\\\\])/g,\"$1.*?\")+\"$\"),K=function(t){return!(!V(t)||N(t))&&(_(t)?k:L).test(q(t))},z=function(t,e){return null==t?void 0:t[e]},Z=function(t,e){var r=z(t,e);return K(r)?r:void 0},$=Z(S.a,\"Map\"),G=Z(Object,\"create\"),J=function(){this.__data__=G?G(null):{},this.size=0},W=function(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=e?1:0,e},Y=Object.prototype.hasOwnProperty,X=function(t){var e=this.__data__;if(G){var r=e[t];return\"__lodash_hash_undefined__\"===r?void 0:r}return Y.call(e,t)?e[t]:void 0},Q=Object.prototype.hasOwnProperty,tt=function(t){var e=this.__data__;return G?void 0!==e[t]:Q.call(e,t)},et=function(t,e){var r=this.__data__;return this.size+=this.has(t)?0:1,r[t]=G&&void 0===e?\"__lodash_hash_undefined__\":e,this};function rt(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}rt.prototype.clear=J,rt.prototype.delete=W,rt.prototype.get=X,rt.prototype.has=tt,rt.prototype.set=et;var nt=rt,it=function(){this.size=0,this.__data__={hash:new nt,map:new($||d),string:new nt}},st=function(t){var e=typeof t;return\"string\"==e||\"number\"==e||\"symbol\"==e||\"boolean\"==e?\"__proto__\"!==t:null===t},ot=function(t,e){var r=t.__data__;return st(e)?r[\"string\"==typeof e?\"string\":\"hash\"]:r.map},at=function(t){var e=ot(this,t).delete(t);return this.size-=e?1:0,e},ht=function(t){return ot(this,t).get(t)},ut=function(t){return ot(this,t).has(t)},ct=function(t,e){var r=ot(this,t),n=r.size;return r.set(t,e),this.size+=r.size==n?0:1,this};function ft(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}ft.prototype.clear=it,ft.prototype.delete=at,ft.prototype.get=ht,ft.prototype.has=ut,ft.prototype.set=ct;var lt=ft,pt=function(t,e){var r=this.__data__;if(r instanceof d){var n=r.__data__;if(!$||n.length<199)return n.push([t,e]),this.size=++r.size,this;r=this.__data__=new lt(n)}return r.set(t,e),this.size=r.size,this};function gt(t){var e=this.__data__=new d(t);this.size=e.size}gt.prototype.clear=v,gt.prototype.delete=y,gt.prototype.get=b,gt.prototype.has=m,gt.prototype.set=pt;var dt=gt,vt=function(t,e){for(var r=-1,n=null==t?0:t.length;++r<n&&!1!==e(t[r],r,t););return t},yt=function(){try{var t=Z(Object,\"defineProperty\");return t({},\"\",{}),t}catch(t){}}(),bt=function(t,e,r){\"__proto__\"==e&&yt?yt(t,e,{configurable:!0,enumerable:!0,value:r,writable:!0}):t[e]=r},mt=Object.prototype.hasOwnProperty,St=function(t,e,r){var n=t[e];mt.call(t,e)&&o(n,r)&&(void 0!==r||e in t)||bt(t,e,r)},Tt=function(t,e,r,n){var i=!r;r||(r={});for(var s=-1,o=e.length;++s<o;){var a=e[s],h=n?n(r[a],t[a],a,r,t):void 0;void 0===h&&(h=t[a]),i?bt(r,a,h):St(r,a,h)}return r},wt=function(t,e){for(var r=-1,n=Array(t);++r<t;)n[r]=e(r);return n},Et=function(t){return null!=t&&\"object\"==typeof t},Dt=function(t){return Et(t)&&\"[object Arguments]\"==R(t)},jt=Object.prototype,Ot=jt.hasOwnProperty,xt=jt.propertyIsEnumerable,At=Dt(function(){return arguments}())?Dt:function(t){return Et(t)&&Ot.call(t,\"callee\")&&!xt.call(t,\"callee\")},Bt=Array.isArray,Rt=r(4),Vt=/^(?:0|[1-9]\\d*)$/,_t=function(t,e){var r=typeof t;return!!(e=null==e?9007199254740991:e)&&(\"number\"==r||\"symbol\"!=r&&Vt.test(t))&&t>-1&&t%1==0&&t<e},It=function(t){return\"number\"==typeof t&&t>-1&&t%1==0&&t<=9007199254740991},Pt={};Pt[\"[object Float32Array]\"]=Pt[\"[object Float64Array]\"]=Pt[\"[object Int8Array]\"]=Pt[\"[object Int16Array]\"]=Pt[\"[object Int32Array]\"]=Pt[\"[object Uint8Array]\"]=Pt[\"[object Uint8ClampedArray]\"]=Pt[\"[object Uint16Array]\"]=Pt[\"[object Uint32Array]\"]=!0,Pt[\"[object Arguments]\"]=Pt[\"[object Array]\"]=Pt[\"[object ArrayBuffer]\"]=Pt[\"[object Boolean]\"]=Pt[\"[object DataView]\"]=Pt[\"[object Date]\"]=Pt[\"[object Error]\"]=Pt[\"[object Function]\"]=Pt[\"[object Map]\"]=Pt[\"[object Number]\"]=Pt[\"[object Object]\"]=Pt[\"[object RegExp]\"]=Pt[\"[object Set]\"]=Pt[\"[object String]\"]=Pt[\"[object WeakMap]\"]=!1;var Nt=function(t){return Et(t)&&It(t.length)&&!!Pt[R(t)]},Mt=function(t){return function(e){return t(e)}},qt=r(2),Lt=qt.a&&qt.a.isTypedArray,Ct=Lt?Mt(Lt):Nt,Ht=Object.prototype.hasOwnProperty,Ft=function(t,e){var r=Bt(t),n=!r&&At(t),i=!r&&!n&&Object(Rt.a)(t),s=!r&&!n&&!i&&Ct(t),o=r||n||i||s,a=o?wt(t.length,String):[],h=a.length;for(var u in t)!e&&!Ht.call(t,u)||o&&(\"length\"==u||i&&(\"offset\"==u||\"parent\"==u)||s&&(\"buffer\"==u||\"byteLength\"==u||\"byteOffset\"==u)||_t(u,h))||a.push(u);return a},Ut=Object.prototype,kt=function(t){var e=t&&t.constructor;return t===(\"function\"==typeof e&&e.prototype||Ut)},Kt=function(t,e){return function(r){return t(e(r))}},zt=Kt(Object.keys,Object),Zt=Object.prototype.hasOwnProperty,$t=function(t){if(!kt(t))return zt(t);var e=[];for(var r in Object(t))Zt.call(t,r)&&\"constructor\"!=r&&e.push(r);return e},Gt=function(t){return null!=t&&It(t.length)&&!_(t)},Jt=function(t){return Gt(t)?Ft(t):$t(t)},Wt=function(t,e){return t&&Tt(e,Jt(e),t)},Yt=function(t){var e=[];if(null!=t)for(var r in Object(t))e.push(r);return e},Xt=Object.prototype.hasOwnProperty,Qt=function(t){if(!V(t))return Yt(t);var e=kt(t),r=[];for(var n in t)(\"constructor\"!=n||!e&&Xt.call(t,n))&&r.push(n);return r},te=function(t){return Gt(t)?Ft(t,!0):Qt(t)},ee=function(t,e){return t&&Tt(e,te(e),t)},re=r(7),ne=function(t,e){var r=-1,n=t.length;for(e||(e=Array(n));++r<n;)e[r]=t[r];return e},ie=function(t,e){for(var r=-1,n=null==t?0:t.length,i=0,s=[];++r<n;){var o=t[r];e(o,r,t)&&(s[i++]=o)}return s},se=function(){return[]},oe=Object.prototype.propertyIsEnumerable,ae=Object.getOwnPropertySymbols,he=ae?function(t){return null==t?[]:(t=Object(t),ie(ae(t),(function(e){return oe.call(t,e)})))}:se,ue=function(t,e){return Tt(t,he(t),e)},ce=function(t,e){for(var r=-1,n=e.length,i=t.length;++r<n;)t[i+r]=e[r];return t},fe=Kt(Object.getPrototypeOf,Object),le=Object.getOwnPropertySymbols?function(t){for(var e=[];t;)ce(e,he(t)),t=fe(t);return e}:se,pe=function(t,e){return Tt(t,le(t),e)},ge=function(t,e,r){var n=e(t);return Bt(t)?n:ce(n,r(t))},de=function(t){return ge(t,Jt,he)},ve=function(t){return ge(t,te,le)},ye=Z(S.a,\"DataView\"),be=Z(S.a,\"Promise\"),me=Z(S.a,\"Set\"),Se=Z(S.a,\"WeakMap\"),Te=q(ye),we=q($),Ee=q(be),De=q(me),je=q(Se),Oe=R;(ye&&\"[object DataView]\"!=Oe(new ye(new ArrayBuffer(1)))||$&&\"[object Map]\"!=Oe(new $)||be&&\"[object Promise]\"!=Oe(be.resolve())||me&&\"[object Set]\"!=Oe(new me)||Se&&\"[object WeakMap]\"!=Oe(new Se))&&(Oe=function(t){var e=R(t),r=\"[object Object]\"==e?t.constructor:void 0,n=r?q(r):\"\";if(n)switch(n){case Te:return\"[object DataView]\";case we:return\"[object Map]\";case Ee:return\"[object Promise]\";case De:return\"[object Set]\";case je:return\"[object WeakMap]\"}return e});var xe=Oe,Ae=Object.prototype.hasOwnProperty,Be=function(t){var e=t.length,r=new t.constructor(e);return e&&\"string\"==typeof t[0]&&Ae.call(t,\"index\")&&(r.index=t.index,r.input=t.input),r},Re=S.a.Uint8Array,Ve=function(t){var e=new t.constructor(t.byteLength);return new Re(e).set(new Re(t)),e},_e=function(t,e){var r=e?Ve(t.buffer):t.buffer;return new t.constructor(r,t.byteOffset,t.byteLength)},Ie=/\\w*$/,Pe=function(t){var e=new t.constructor(t.source,Ie.exec(t));return e.lastIndex=t.lastIndex,e},Ne=T?T.prototype:void 0,Me=Ne?Ne.valueOf:void 0,qe=function(t){return Me?Object(Me.call(t)):{}},Le=function(t,e){var r=e?Ve(t.buffer):t.buffer;return new t.constructor(r,t.byteOffset,t.length)},Ce=function(t,e,r){var n=t.constructor;switch(e){case\"[object ArrayBuffer]\":return Ve(t);case\"[object Boolean]\":case\"[object Date]\":return new n(+t);case\"[object DataView]\":return _e(t,r);case\"[object Float32Array]\":case\"[object Float64Array]\":case\"[object Int8Array]\":case\"[object Int16Array]\":case\"[object Int32Array]\":case\"[object Uint8Array]\":case\"[object Uint8ClampedArray]\":case\"[object Uint16Array]\":case\"[object Uint32Array]\":return Le(t,r);case\"[object Map]\":return new n;case\"[object Number]\":case\"[object String]\":return new n(t);case\"[object RegExp]\":return Pe(t);case\"[object Set]\":return new n;case\"[object Symbol]\":return qe(t)}},He=Object.create,Fe=function(){function t(){}return function(e){if(!V(e))return{};if(He)return He(e);t.prototype=e;var r=new t;return t.prototype=void 0,r}}(),Ue=function(t){return\"function\"!=typeof t.constructor||kt(t)?{}:Fe(fe(t))},ke=function(t){return Et(t)&&\"[object Map]\"==xe(t)},Ke=qt.a&&qt.a.isMap,ze=Ke?Mt(Ke):ke,Ze=function(t){return Et(t)&&\"[object Set]\"==xe(t)},$e=qt.a&&qt.a.isSet,Ge=$e?Mt($e):Ze,Je={};Je[\"[object Arguments]\"]=Je[\"[object Array]\"]=Je[\"[object ArrayBuffer]\"]=Je[\"[object DataView]\"]=Je[\"[object Boolean]\"]=Je[\"[object Date]\"]=Je[\"[object Float32Array]\"]=Je[\"[object Float64Array]\"]=Je[\"[object Int8Array]\"]=Je[\"[object Int16Array]\"]=Je[\"[object Int32Array]\"]=Je[\"[object Map]\"]=Je[\"[object Number]\"]=Je[\"[object Object]\"]=Je[\"[object RegExp]\"]=Je[\"[object Set]\"]=Je[\"[object String]\"]=Je[\"[object Symbol]\"]=Je[\"[object Uint8Array]\"]=Je[\"[object Uint8ClampedArray]\"]=Je[\"[object Uint16Array]\"]=Je[\"[object Uint32Array]\"]=!0,Je[\"[object Error]\"]=Je[\"[object Function]\"]=Je[\"[object WeakMap]\"]=!1;var We=function t(e,r,n,i,s,o){var a,h=1&r,u=2&r,c=4&r;if(n&&(a=s?n(e,i,s,o):n(e)),void 0!==a)return a;if(!V(e))return e;var f=Bt(e);if(f){if(a=Be(e),!h)return ne(e,a)}else{var l=xe(e),p=\"[object Function]\"==l||\"[object GeneratorFunction]\"==l;if(Object(Rt.a)(e))return Object(re.a)(e,h);if(\"[object Object]\"==l||\"[object Arguments]\"==l||p&&!s){if(a=u||p?{}:Ue(e),!h)return u?pe(e,ee(a,e)):ue(e,Wt(a,e))}else{if(!Je[l])return s?e:{};a=Ce(e,l,h)}}o||(o=new dt);var g=o.get(e);if(g)return g;o.set(e,a),Ge(e)?e.forEach((function(i){a.add(t(i,r,n,i,e,o))})):ze(e)&&e.forEach((function(i,s){a.set(s,t(i,r,n,s,e,o))}));var d=c?u?ve:de:u?keysIn:Jt,v=f?void 0:d(e);return vt(v||e,(function(i,s){v&&(i=e[s=i]),St(a,s,t(i,r,n,s,e,o))})),a},Ye=function(t){return We(t,5)},Xe={},Qe={},tr=i.a[86],er=[],rr=[];for(var nr in Xe[\"\"]=\"全部\",tr)er.push({value:nr,label:tr[nr]}),Xe[nr]=tr[nr],Qe[tr[nr]]={code:nr},Qe[tr[nr]][\"全部\"]={code:\"\"};for(var ir=0,sr=er.length;ir<sr;ir++){var or=er[ir].value,ar=er[ir].label,hr=[];for(var ur in i.a[or])hr.push({value:ur,label:i.a[or][ur]}),Xe[ur]=i.a[or][ur],Qe[ar][i.a[or][ur]]={code:ur},Qe[ar][i.a[or][ur]][\"全部\"]={code:\"\"};hr.length&&(er[ir].children=hr)}rr=Ye(er);for(var cr=0,fr=er.length;cr<fr;cr++){var lr=er[cr].children,pr=er[cr].label;if(lr)for(var gr=0,dr=lr.length;gr<dr;gr++){var vr=lr[gr].value,yr=lr[gr].label,br=[];for(var mr in i.a[vr])br.push({value:mr,label:i.a[vr][mr]}),Xe[mr]=i.a[vr][mr],Qe[pr][yr][i.a[vr][mr]]={code:mr};br.length&&(lr[gr].children=br)}}var Sr=Ye(rr);Sr.unshift({value:\"\",label:\"全部\"});for(var Tr=0,wr=Sr.length;Tr<wr;Tr++){var Er=Sr[Tr].children;if(Er&&Er.length){Er.unshift({value:\"\",label:\"全部\"});for(var Dr=0,jr=Er.length;Dr<jr;Dr++){var Or=Er[Dr].children;Or&&Or.length&&Or.unshift({value:\"\",label:\"全部\"})}}}var xr=Ye(er);xr.unshift({value:\"\",label:\"全部\"});for(var Ar=0,Br=xr.length;Ar<Br;Ar++){var Rr=xr[Ar].children;if(Rr&&Rr.length){Rr.unshift({value:\"\",label:\"全部\"});for(var Vr=0,_r=Rr.length;Vr<_r;Vr++){var Ir=Rr[Vr].children;Ir&&Ir.length&&Ir.unshift({value:\"\",label:\"全部\"})}}}}])}))}}]);", "extractedComments": []}