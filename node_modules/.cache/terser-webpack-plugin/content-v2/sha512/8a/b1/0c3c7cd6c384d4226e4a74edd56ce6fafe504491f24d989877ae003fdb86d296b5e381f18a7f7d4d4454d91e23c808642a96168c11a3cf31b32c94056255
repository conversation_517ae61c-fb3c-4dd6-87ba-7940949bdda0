{"code": "(window[\"webpackJsonp\"]=window[\"webpackJsonp\"]||[]).push([[\"chunk-3c9adc44\"],{1538:function(t,e,n){\"use strict\";n.d(e,\"h\",(function(){return o})),n.d(e,\"b\",(function(){return a})),n.d(e,\"c\",(function(){return i})),n.d(e,\"g\",(function(){return u})),n.d(e,\"n\",(function(){return s})),n.d(e,\"o\",(function(){return l})),n.d(e,\"k\",(function(){return c})),n.d(e,\"i\",(function(){return d})),n.d(e,\"j\",(function(){return f})),n.d(e,\"f\",(function(){return m})),n.d(e,\"l\",(function(){return p})),n.d(e,\"a\",(function(){return h})),n.d(e,\"m\",(function(){return b})),n.d(e,\"d\",(function(){return g})),n.d(e,\"e\",(function(){return v}));var r=n(\"b775\");function o(t){return Object(r[\"a\"])({url:\"/flowable/definition/list\",method:\"get\",params:t})}function a(t,e){return Object(r[\"a\"])({url:\"/flowable/definition/start/\"+t,method:\"post\",data:e})}function i(t){return Object(r[\"a\"])({url:\"/flowable/definition/startByKey\",method:\"post\",data:t})}function u(t){return Object(r[\"a\"])({url:\"/flowable/task/processVariables/\"+t,method:\"get\"})}function s(t){return Object(r[\"a\"])({url:\"/flowable/definition/updateState\",method:\"put\",params:t})}function l(t){return Object(r[\"a\"])({url:\"/flowable/definition/userList\",method:\"get\",params:t})}function c(t){return Object(r[\"a\"])({url:\"/flowable/definition/roleList\",method:\"get\",params:t})}function d(t){return Object(r[\"a\"])({url:\"/flowable/definition/readXml/\"+t,method:\"get\"})}function f(t){return Object(r[\"a\"])({url:\"/flowable/definition/readXmlByKey/\"+t,method:\"get\"})}function m(t){return Object(r[\"a\"])({url:\"/flowable/task/flowViewer/\"+t,method:\"get\"})}function p(t){return Object(r[\"a\"])({url:\"/flowable/definition/save\",method:\"post\",data:t})}function h(t){return Object(r[\"a\"])({url:\"/system/deployment\",method:\"post\",data:t})}function b(t){return Object(r[\"a\"])({url:\"/system/deployment\",method:\"put\",data:t})}function g(t){return Object(r[\"a\"])({url:\"/flowable/definition/delete/\",method:\"delete\",params:t})}function v(t){return Object(r[\"a\"])({url:\"/system/deployment/export\",method:\"get\",params:t})}},\"251b\":function(t,e,n){\"use strict\";n.r(e);var r=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n(\"div\",[n(\"bpmn-modeler\",{ref:\"refNode\",attrs:{xml:t.xml,users:t.users,groups:t.groups,categorys:t.categorys,\"is-view\":!1},on:{save:t.save,showXML:t.showXML,dataType:t.dataType}}),n(\"el-dialog\",{attrs:{title:t.xmlTitle,visible:t.xmlOpen,width:\"60%\",\"append-to-body\":\"\"},on:{\"update:visible\":function(e){t.xmlOpen=e}}},[n(\"div\",[n(\"pre\",{directives:[{name:\"highlight\",rawName:\"v-highlight\"}]},[t._v(\"         \"),n(\"code\",{staticClass:\"xml\"},[t._v(\"\\n              \"+t._s(t.xmlContent)+\"\\n         \")]),t._v(\"\\n      \")])])])],1)},o=[],a=n(\"1538\"),i=n(\"ae3a\"),u=n(\"347e9\"),s=n.n(u),l=n(\"1487\"),c=n.n(l),d=(n(\"9f21\"),{name:\"Model\",components:{bpmnModeler:i[\"a\"],vkbeautify:s.a},directives:{highlight:function(t){var e=t.querySelectorAll(\"pre code\");e.forEach((function(t){c.a.highlightBlock(t)}))}},data:function(){return{xml:\"\",modeler:\"\",xmlOpen:!1,xmlTitle:\"\",xmlContent:\"\",users:[],groups:[],categorys:[]}},activated:function(){var t=this,e=this.$route.query&&this.$route.query.deployId;e&&this.getModelDetail(e),this.getDicts(\"sys_process_category\").then((function(e){t.categorys=e.data})),this.getDataList()},methods:{getModelDetail:function(t){var e=this;Object(a[\"i\"])(t).then((function(t){e.xml=t.data,e.modeler=t.data}))},save:function(t){var e=this,n={name:t.process.name,category:t.process.category,xml:t.xml};Object(a[\"l\"])(n).then((function(t){e.$message(t.msg),e.$store.dispatch(\"tagsView/delView\",e.$route),e.$router.go(-1)}))},getDataList:function(){var t=this;Object(a[\"o\"])().then((function(e){e.data.forEach((function(t){t.userId=t.userId.toString()})),t.users=e.data;var n={nickName:\"流程发起人\",userId:\"${INITIATOR}\"};t.users.push(n)})),Object(a[\"k\"])().then((function(e){e.data.forEach((function(t){t.roleId=t.roleId.toString()})),t.groups=e.data}))},showXML:function(t){this.xmlTitle=\"xml查看\",this.xmlOpen=!0,this.xmlContent=s.a.xml(t)},dataType:function(t){this.users=[],this.groups=[],t&&(\"dynamic\"===t.dataType?\"assignee\"===t.userType?this.users=[{nickName:\"${INITIATOR}\",userId:\"${INITIATOR}\"},{nickName:\"#{approval}\",userId:\"#{approval}\"}]:\"candidateUsers\"===t.userType?this.users=[{nickName:\"#{approval}\",userId:\"#{approval}\"}]:this.groups=[{roleName:\"#{approval}\",roleId:\"#{approval}\"}]:this.getDataList())}}}),f=d,m=n(\"2877\"),p=Object(m[\"a\"])(f,r,o,!1,null,null,null);e[\"default\"]=p.exports}}]);", "extractedComments": []}