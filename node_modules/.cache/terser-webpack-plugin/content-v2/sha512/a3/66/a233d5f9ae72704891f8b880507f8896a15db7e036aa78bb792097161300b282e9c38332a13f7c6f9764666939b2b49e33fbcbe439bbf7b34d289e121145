{"code": "(window[\"webpackJsonp\"]=window[\"webpackJsonp\"]||[]).push([[\"chunk-d19c1a98\"],{bfc4:function(t,e,a){\"use strict\";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a(\"div\",{staticClass:\"app-container\"},[a(\"el-form\",{directives:[{name:\"show\",rawName:\"v-show\",value:t.showSearch,expression:\"showSearch\"}],ref:\"queryForm\",attrs:{model:t.queryParams,inline:!0,\"label-width\":\"68px\"}},[a(\"el-form-item\",{attrs:{label:\"字典名称\",prop:\"dictType\"}},[a(\"el-select\",{attrs:{size:\"small\"},model:{value:t.queryParams.dictType,callback:function(e){t.$set(t.queryParams,\"dictType\",e)},expression:\"queryParams.dictType\"}},t._l(t.typeOptions,(function(t){return a(\"el-option\",{key:t.dictId,attrs:{label:t.dictName,value:t.dictType}})})),1)],1),a(\"el-form-item\",{attrs:{label:\"字典标签\",prop:\"dictLabel\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入字典标签\",clearable:\"\",size:\"small\"},nativeOn:{keyup:function(e){return!e.type.indexOf(\"key\")&&t._k(e.keyCode,\"enter\",13,e.key,\"Enter\")?null:t.handleQuery(e)}},model:{value:t.queryParams.dictLabel,callback:function(e){t.$set(t.queryParams,\"dictLabel\",e)},expression:\"queryParams.dictLabel\"}})],1),a(\"el-form-item\",{attrs:{label:\"状态\",prop:\"status\"}},[a(\"el-select\",{attrs:{placeholder:\"数据状态\",clearable:\"\",size:\"small\"},model:{value:t.queryParams.status,callback:function(e){t.$set(t.queryParams,\"status\",e)},expression:\"queryParams.status\"}},t._l(t.statusOptions,(function(t){return a(\"el-option\",{key:t.dictValue,attrs:{label:t.dictLabel,value:t.dictValue}})})),1)],1),a(\"el-form-item\",[a(\"el-button\",{attrs:{type:\"primary\",icon:\"el-icon-search\",size:\"mini\"},on:{click:t.handleQuery}},[t._v(\"搜索\")]),a(\"el-button\",{attrs:{icon:\"el-icon-refresh\",size:\"mini\"},on:{click:t.resetQuery}},[t._v(\"重置\")])],1)],1),a(\"el-row\",{staticClass:\"mb8\",attrs:{gutter:10}},[a(\"el-col\",{attrs:{span:1.5}},[a(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"system:dict:add\"],expression:\"['system:dict:add']\"}],attrs:{type:\"primary\",plain:\"\",icon:\"el-icon-plus\",size:\"mini\"},on:{click:t.handleAdd}},[t._v(\"新增\")])],1),a(\"el-col\",{attrs:{span:1.5}},[a(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"system:dict:edit\"],expression:\"['system:dict:edit']\"}],attrs:{type:\"success\",plain:\"\",icon:\"el-icon-edit\",size:\"mini\",disabled:t.single},on:{click:t.handleUpdate}},[t._v(\"修改\")])],1),a(\"el-col\",{attrs:{span:1.5}},[a(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"system:dict:remove\"],expression:\"['system:dict:remove']\"}],attrs:{type:\"danger\",plain:\"\",icon:\"el-icon-delete\",size:\"mini\",disabled:t.multiple},on:{click:t.handleDelete}},[t._v(\"删除\")])],1),a(\"el-col\",{attrs:{span:1.5}},[a(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"system:user:import\"],expression:\"['system:user:import']\"}],attrs:{type:\"info\",plain:\"\",icon:\"el-icon-upload2\",size:\"mini\"},on:{click:t.handleImport}},[t._v(\"导入\")])],1),a(\"el-col\",{attrs:{span:1.5}},[a(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"system:dict:export\"],expression:\"['system:dict:export']\"}],attrs:{type:\"warning\",plain:\"\",icon:\"el-icon-download\",size:\"mini\"},on:{click:t.handleExport}},[t._v(\"导出\")])],1),a(\"right-toolbar\",{attrs:{showSearch:t.showSearch},on:{\"update:showSearch\":function(e){t.showSearch=e},\"update:show-search\":function(e){t.showSearch=e},queryTable:t.getList}})],1),a(\"el-table\",{directives:[{name:\"loading\",rawName:\"v-loading\",value:t.loading,expression:\"loading\"}],attrs:{data:t.dataList},on:{\"selection-change\":t.handleSelectionChange}},[a(\"el-table-column\",{attrs:{type:\"selection\",width:\"55\",align:\"center\"}}),a(\"el-table-column\",{attrs:{label:\"字典编码\",align:\"center\",prop:\"dictCode\"}}),a(\"el-table-column\",{attrs:{label:\"字典标签\",align:\"center\",prop:\"dictLabel\"}}),a(\"el-table-column\",{attrs:{label:\"字典键值\",align:\"center\",prop:\"dictValue\"}}),a(\"el-table-column\",{attrs:{label:\"字典排序\",align:\"center\",prop:\"dictSort\"}}),a(\"el-table-column\",{attrs:{label:\"状态\",align:\"center\",prop:\"status\",formatter:t.statusFormat}}),a(\"el-table-column\",{attrs:{label:\"备注\",align:\"center\",prop:\"remark\",\"show-overflow-tooltip\":!0}}),a(\"el-table-column\",{attrs:{label:\"创建时间\",align:\"center\",prop:\"createTime\",width:\"180\"},scopedSlots:t._u([{key:\"default\",fn:function(e){return[a(\"span\",[t._v(t._s(t.parseTime(e.row.createTime)))])]}}])}),a(\"el-table-column\",{attrs:{label:\"操作\",align:\"center\",\"class-name\":\"small-padding fixed-width\"},scopedSlots:t._u([{key:\"default\",fn:function(e){return[a(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"system:dict:edit\"],expression:\"['system:dict:edit']\"}],attrs:{size:\"mini\",type:\"text\",icon:\"el-icon-edit\"},on:{click:function(a){return t.handleUpdate(e.row)}}},[t._v(\"修改\")]),a(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"system:dict:remove\"],expression:\"['system:dict:remove']\"}],attrs:{size:\"mini\",type:\"text\",icon:\"el-icon-delete\"},on:{click:function(a){return t.handleDelete(e.row)}}},[t._v(\"删除\")])]}}])})],1),a(\"pagination\",{directives:[{name:\"show\",rawName:\"v-show\",value:t.total>0,expression:\"total>0\"}],attrs:{total:t.total,page:t.queryParams.pageNum,limit:t.queryParams.pageSize},on:{\"update:page\":function(e){return t.$set(t.queryParams,\"pageNum\",e)},\"update:limit\":function(e){return t.$set(t.queryParams,\"pageSize\",e)},pagination:t.getList}}),a(\"el-dialog\",{attrs:{title:t.title,visible:t.open,width:\"500px\",\"append-to-body\":\"\"},on:{\"update:visible\":function(e){t.open=e}}},[a(\"el-form\",{ref:\"form\",attrs:{model:t.form,rules:t.rules,\"label-width\":\"80px\"}},[a(\"el-form-item\",{attrs:{label:\"字典类型\"}},[a(\"el-input\",{attrs:{disabled:!0},model:{value:t.form.dictType,callback:function(e){t.$set(t.form,\"dictType\",e)},expression:\"form.dictType\"}})],1),a(\"el-form-item\",{attrs:{label:\"数据标签\",prop:\"dictLabel\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入数据标签\"},model:{value:t.form.dictLabel,callback:function(e){t.$set(t.form,\"dictLabel\",e)},expression:\"form.dictLabel\"}})],1),a(\"el-form-item\",{attrs:{label:\"数据键值\",prop:\"dictValue\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入数据键值\"},model:{value:t.form.dictValue,callback:function(e){t.$set(t.form,\"dictValue\",e)},expression:\"form.dictValue\"}})],1),a(\"el-form-item\",{attrs:{label:\"显示排序\",prop:\"dictSort\"}},[a(\"el-input-number\",{attrs:{\"controls-position\":\"right\",min:0},model:{value:t.form.dictSort,callback:function(e){t.$set(t.form,\"dictSort\",e)},expression:\"form.dictSort\"}})],1),a(\"el-form-item\",{attrs:{label:\"状态\",prop:\"status\"}},[a(\"el-radio-group\",{model:{value:t.form.status,callback:function(e){t.$set(t.form,\"status\",e)},expression:\"form.status\"}},t._l(t.statusOptions,(function(e){return a(\"el-radio\",{key:e.dictValue,attrs:{label:e.dictValue}},[t._v(t._s(e.dictLabel))])})),1)],1),a(\"el-form-item\",{attrs:{label:\"备注\",prop:\"remark\"}},[a(\"el-input\",{attrs:{type:\"textarea\",placeholder:\"请输入内容\"},model:{value:t.form.remark,callback:function(e){t.$set(t.form,\"remark\",e)},expression:\"form.remark\"}})],1)],1),a(\"div\",{staticClass:\"dialog-footer\",attrs:{slot:\"footer\"},slot:\"footer\"},[a(\"el-button\",{attrs:{type:\"primary\"},on:{click:t.submitForm}},[t._v(\"确 定\")]),a(\"el-button\",{on:{click:t.cancel}},[t._v(\"取 消\")])],1)],1),a(\"el-dialog\",{attrs:{title:t.upload.title,visible:t.upload.open,width:\"400px\",\"append-to-body\":\"\"},on:{\"update:visible\":function(e){return t.$set(t.upload,\"open\",e)}}},[a(\"el-upload\",{ref:\"upload\",attrs:{limit:1,accept:\".xlsx, .xls\",headers:t.upload.headers,action:t.upload.url+\"?dictType=\"+t.defaultDictType,disabled:t.upload.isUploading,\"on-progress\":t.handleFileUploadProgress,\"on-success\":t.handleFileSuccess,\"auto-upload\":!1,drag:\"\"}},[a(\"i\",{staticClass:\"el-icon-upload\"}),a(\"div\",{staticClass:\"el-upload__text\"},[t._v(\" 将文件拖到此处，或 \"),a(\"em\",[t._v(\"点击上传\")])]),a(\"div\",{staticClass:\"el-upload__tip\",staticStyle:{color:\"red\"},attrs:{slot:\"tip\"},slot:\"tip\"},[t._v(\" 提示：仅允许导入“xls”或“xlsx”格式文件！ \")])]),a(\"div\",{staticClass:\"dialog-footer\",attrs:{slot:\"footer\"},slot:\"footer\"},[a(\"el-button\",{attrs:{type:\"primary\"},on:{click:t.submitFileForm}},[t._v(\"确 定\")]),a(\"el-button\",{on:{click:function(e){t.upload.open=!1}}},[t._v(\"取 消\")])],1)],1)],1)},s=[],r=a(\"aa3a\"),o=a(\"ed45\"),n=a(\"5f87\"),l={name:\"Data\",data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,dataList:[],defaultDictType:\"\",title:\"\",open:!1,statusOptions:[],typeOptions:[],queryParams:{pageNum:1,pageSize:10,dictName:void 0,dictType:void 0,status:void 0},form:{},upload:{open:!1,title:\"\",isUploading:!1,headers:{Authorization:\"Bearer \"+Object(n[\"a\"])()},url:\"/prod-api/system/dict/data/importData\"},rules:{dictLabel:[{required:!0,message:\"数据标签不能为空\",trigger:\"blur\"}],dictValue:[{required:!0,message:\"数据键值不能为空\",trigger:\"blur\"}],dictSort:[{required:!0,message:\"数据顺序不能为空\",trigger:\"blur\"}]}}},created:function(){var t=this,e=this.$route.params&&this.$route.params.dictId;this.getType(e),this.getTypeList(),this.getDicts(\"sys_normal_disable\").then((function(e){t.statusOptions=e.data}))},methods:{getType:function(t){var e=this;Object(o[\"e\"])(t).then((function(t){e.queryParams.dictType=t.data.dictType,e.defaultDictType=t.data.dictType,e.getList()}))},getTypeList:function(){var t=this;Object(o[\"f\"])().then((function(e){t.typeOptions=e.rows}))},getList:function(){var t=this;this.loading=!0,Object(r[\"f\"])(this.queryParams).then((function(e){t.dataList=e.rows,t.total=e.total,t.loading=!1}))},statusFormat:function(t,e){return this.selectDictLabel(this.statusOptions,t.status)},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={dictCode:void 0,dictLabel:void 0,dictValue:void 0,dictSort:0,status:\"0\",remark:void 0},this.resetForm(\"form\")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm(\"queryForm\"),this.queryParams.dictType=this.defaultDictType,this.handleQuery()},handleAdd:function(){this.reset(),this.open=!0,this.title=\"添加字典数据\",this.form.dictType=this.queryParams.dictType},handleSelectionChange:function(t){this.ids=t.map((function(t){return t.dictCode})),this.single=1!=t.length,this.multiple=!t.length},handleUpdate:function(t){var e=this;this.reset();var a=t.dictCode||this.ids;Object(r[\"d\"])(a).then((function(t){e.form=t.data,e.open=!0,e.title=\"修改字典数据\"}))},submitForm:function(){var t=this;this.$refs[\"form\"].validate((function(e){e&&(void 0!=t.form.dictCode?Object(r[\"g\"])(t.form).then((function(e){t.msgSuccess(\"修改成功\"),t.open=!1,t.getList()})):Object(r[\"a\"])(t.form).then((function(e){t.msgSuccess(\"新增成功\"),t.open=!1,t.getList()})))}))},handleDelete:function(t){var e=this,a=t.dictCode||this.ids;this.$confirm('是否确认删除字典编码为\"'+a+'\"的数据项?',\"警告\",{confirmButtonText:\"确定\",cancelButtonText:\"取消\",type:\"warning\"}).then((function(){return Object(r[\"b\"])(a)})).then((function(){e.getList(),e.msgSuccess(\"删除成功\")}))},handleExport:function(){var t=this,e=this.queryParams;this.$confirm(\"是否确认导出所有数据项?\",\"警告\",{confirmButtonText:\"确定\",cancelButtonText:\"取消\",type:\"warning\"}).then((function(){return Object(r[\"c\"])(e)})).then((function(e){t.download(e.msg)}))},handleImport:function(){this.upload.title=\"字典值导入\",this.upload.open=!0},handleFileUploadProgress:function(t,e,a){this.upload.isUploading=!0},handleFileSuccess:function(t,e,a){this.upload.open=!1,this.upload.isUploading=!1,this.$refs.upload.clearFiles(),this.$alert(t.msg,\"导入结果\",{dangerouslyUseHTMLString:!0}),this.getList()},submitFileForm:function(){this.$refs.upload.submit()}}},c=l,u=a(\"2877\"),d=Object(u[\"a\"])(c,i,s,!1,null,null,null);e[\"default\"]=d.exports},ed45:function(t,e,a){\"use strict\";a.d(e,\"f\",(function(){return s})),a.d(e,\"e\",(function(){return r})),a.d(e,\"a\",(function(){return o})),a.d(e,\"h\",(function(){return n})),a.d(e,\"c\",(function(){return l})),a.d(e,\"b\",(function(){return c})),a.d(e,\"d\",(function(){return u})),a.d(e,\"g\",(function(){return d}));var i=a(\"b775\");function s(t){return Object(i[\"a\"])({url:\"/system/dict/type/list\",method:\"get\",params:t})}function r(t){return Object(i[\"a\"])({url:\"/system/dict/type/\"+t,method:\"get\"})}function o(t){return Object(i[\"a\"])({url:\"/system/dict/type\",method:\"post\",data:t})}function n(t){return Object(i[\"a\"])({url:\"/system/dict/type\",method:\"put\",data:t})}function l(t){return Object(i[\"a\"])({url:\"/system/dict/type/\"+t,method:\"delete\"})}function c(){return Object(i[\"a\"])({url:\"/system/dict/type/clearCache\",method:\"delete\"})}function u(t){return Object(i[\"a\"])({url:\"/system/dict/type/export\",method:\"get\",params:t})}function d(){return Object(i[\"a\"])({url:\"/system/dict/type/optionselect\",method:\"get\"})}}}]);", "extractedComments": []}