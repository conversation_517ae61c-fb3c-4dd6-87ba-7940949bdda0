{"code": "(window[\"webpackJsonp\"]=window[\"webpackJsonp\"]||[]).push([[\"chunk-commons\"],{\"0835\":function(e,t,n){\"use strict\";var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n(\"div\",{staticClass:\"component-upload-image\"},[n(\"el-upload\",{staticStyle:{display:\"inline-block\",\"vertical-align\":\"top\"},attrs:{action:e.uploadImgUrl,\"list-type\":\"picture-card\",\"on-success\":e.handleUploadSuccess,\"before-upload\":e.handleBeforeUpload,\"on-error\":e.handleUploadError,name:\"file\",\"show-file-list\":!1,headers:e.headers}},[e.value?n(\"div\",{staticClass:\"image\"},[n(\"el-image\",{style:\"width:150px;height:150px;\",attrs:{src:e.value,fit:\"fill\"}}),n(\"div\",{staticClass:\"mask\"},[n(\"div\",{staticClass:\"actions\"},[n(\"span\",{attrs:{title:\"预览\"},on:{click:function(t){t.stopPropagation(),e.dialogVisible=!0}}},[n(\"i\",{staticClass:\"el-icon-zoom-in\"})]),n(\"span\",{attrs:{title:\"移除\"},on:{click:function(t){return t.stopPropagation(),e.removeImage(t)}}},[n(\"i\",{staticClass:\"el-icon-delete\"})])])])],1):n(\"el-image\",{attrs:{src:e.value}},[n(\"div\",{staticClass:\"image-slot\",attrs:{slot:\"error\"},slot:\"error\"},[n(\"i\",{staticClass:\"el-icon-plus\"})])])],1),n(\"el-dialog\",{attrs:{visible:e.dialogVisible,title:\"预览\",width:\"800\",\"append-to-body\":\"\"},on:{\"update:visible\":function(t){e.dialogVisible=t}}},[n(\"img\",{staticStyle:{display:\"block\",\"max-width\":\"100%\",margin:\"0 auto\"},attrs:{src:e.value}})])],1)},i=[],a=n(\"5f87\"),o={data:function(){return{dialogVisible:!1,uploadImgUrl:\"/prod-api/common/upload\",headers:{Authorization:\"Bearer \"+Object(a[\"a\"])()}}},props:{value:{type:String,default:\"\"}},methods:{removeImage:function(){this.$emit(\"input\",\"\")},handleUploadSuccess:function(e){this.$emit(\"input\",e.url),this.loading.close()},handleBeforeUpload:function(){this.loading=this.$loading({lock:!0,text:\"上传中\",background:\"rgba(0, 0, 0, 0.7)\"})},handleUploadError:function(){this.$message({type:\"error\",message:\"上传失败\"}),this.loading.close()}},watch:{}},s=o,l=(n(\"090d\"),n(\"2877\")),u=Object(l[\"a\"])(s,r,i,!1,null,\"0c75380c\",null);t[\"a\"]=u.exports},\"090d\":function(e,t,n){\"use strict\";n(\"3149\")},\"0b11\":function(e,t,n){\"use strict\";n(\"dac7\")},\"0f88\":function(e,t,n){\"use strict\";n.r(t),t[\"default\"]={\"list-type\":function(e,t,n){var r=[],i=t.__config__;return\"picture-card\"===t[\"list-type\"]?r.push(e(\"i\",{class:\"el-icon-plus\"})):r.push(e(\"el-button\",{attrs:{size:\"small\",type:\"primary\",icon:\"el-icon-upload\"}},[i.buttonText])),i.showTip&&r.push(e(\"div\",{slot:\"tip\",class:\"el-upload__tip\"},[\"只能上传不超过 \",i.fileSize,i.sizeUnit,\" 的\",t.accept,\"文件\"])),r}}},\"167d\":function(e,t,n){\"use strict\";n.r(t),t[\"default\"]={prepend:function(e,t,n){return e(\"template\",{slot:\"prepend\"},[t.__slot__[n]])},append:function(e,t,n){return e(\"template\",{slot:\"append\"},[t.__slot__[n]])}}},\"1f81\":function(e,t,n){},2730:function(e){e.exports=JSON.parse('{\"name\":\"Flowable\",\"uri\":\"http://flowable.org/bpmn\",\"prefix\":\"flowable\",\"xml\":{\"tagAlias\":\"lowerCase\"},\"associations\":[],\"types\":[{\"name\":\"InOutBinding\",\"superClass\":[\"Element\"],\"isAbstract\":true,\"properties\":[{\"name\":\"source\",\"isAttr\":true,\"type\":\"String\"},{\"name\":\"sourceExpression\",\"isAttr\":true,\"type\":\"String\"},{\"name\":\"target\",\"isAttr\":true,\"type\":\"String\"},{\"name\":\"businessKey\",\"isAttr\":true,\"type\":\"String\"},{\"name\":\"local\",\"isAttr\":true,\"type\":\"Boolean\",\"default\":false},{\"name\":\"variables\",\"isAttr\":true,\"type\":\"String\"}]},{\"name\":\"In\",\"superClass\":[\"InOutBinding\"],\"meta\":{\"allowedIn\":[\"bpmn:CallActivity\"]}},{\"name\":\"Out\",\"superClass\":[\"InOutBinding\"],\"meta\":{\"allowedIn\":[\"bpmn:CallActivity\"]}},{\"name\":\"AsyncCapable\",\"isAbstract\":true,\"extends\":[\"bpmn:Activity\",\"bpmn:Gateway\",\"bpmn:Event\"],\"properties\":[{\"name\":\"async\",\"isAttr\":true,\"type\":\"Boolean\",\"default\":false},{\"name\":\"asyncBefore\",\"isAttr\":true,\"type\":\"Boolean\",\"default\":false},{\"name\":\"asyncAfter\",\"isAttr\":true,\"type\":\"Boolean\",\"default\":false},{\"name\":\"exclusive\",\"isAttr\":true,\"type\":\"Boolean\",\"default\":true}]},{\"name\":\"flowable:in\",\"superClass\":[\"Element\"],\"properties\":[{\"name\":\"source\",\"type\":\"string\",\"isAttr\":true},{\"name\":\"target\",\"type\":\"string\",\"isAttr\":true}]},{\"name\":\"flowable:out\",\"superClass\":[\"Element\"],\"properties\":[{\"name\":\"source\",\"type\":\"string\",\"isAttr\":true},{\"name\":\"target\",\"type\":\"string\",\"isAttr\":true}]},{\"name\":\"BoundaryEvent\",\"superClass\":[\"CatchEvent\"],\"properties\":[{\"name\":\"cancelActivity\",\"default\":true,\"isAttr\":true,\"type\":\"Boolean\"},{\"name\":\"attachedToRef\",\"type\":\"Activity\",\"isAttr\":true,\"isReference\":true}]},{\"name\":\"JobPriorized\",\"isAbstract\":true,\"extends\":[\"bpmn:Process\",\"flowable:AsyncCapable\"],\"properties\":[{\"name\":\"jobPriority\",\"isAttr\":true,\"type\":\"String\"}]},{\"name\":\"SignalEventDefinition\",\"isAbstract\":true,\"extends\":[\"bpmn:SignalEventDefinition\"],\"properties\":[{\"name\":\"async\",\"isAttr\":true,\"type\":\"Boolean\",\"default\":false}]},{\"name\":\"ErrorEventDefinition\",\"isAbstract\":true,\"extends\":[\"bpmn:ErrorEventDefinition\"],\"properties\":[{\"name\":\"errorCodeVariable\",\"isAttr\":true,\"type\":\"String\"},{\"name\":\"errorMessageVariable\",\"isAttr\":true,\"type\":\"String\"}]},{\"name\":\"Error\",\"isAbstract\":true,\"extends\":[\"bpmn:Error\"],\"properties\":[{\"name\":\"flowable:errorMessage\",\"isAttr\":true,\"type\":\"String\"}]},{\"name\":\"PotentialStarter\",\"superClass\":[\"Element\"],\"properties\":[{\"name\":\"resourceAssignmentExpression\",\"type\":\"bpmn:ResourceAssignmentExpression\"}]},{\"name\":\"UserTask\",\"isAbstract\":true,\"extends\":[\"bpmn:UserTask\"],\"properties\":[{\"name\":\"timerEventDefinition\",\"type\":\"Expression\"},{\"name\":\"multiInstanceLoopCharacteristics\",\"type\":\"MultiInstanceLoopCharacteristics\"}]},{\"name\":\"StartEvent\",\"isAbstract\":true,\"extends\":[\"bpmn:StartEvent\"],\"properties\":[{\"name\":\"timerEventDefinition\",\"type\":\"Expression\"}]},{\"name\":\"FormSupported\",\"isAbstract\":true,\"extends\":[\"bpmn:StartEvent\",\"bpmn:UserTask\"],\"properties\":[{\"name\":\"formHandlerClass\",\"isAttr\":true,\"type\":\"String\"},{\"name\":\"formKey\",\"isAttr\":true,\"type\":\"String\"}]},{\"name\":\"TemplateSupported\",\"isAbstract\":true,\"extends\":[\"bpmn:Process\",\"bpmn:FlowElement\"],\"properties\":[{\"name\":\"modelerTemplate\",\"isAttr\":true,\"type\":\"String\"}]},{\"name\":\"Initiator\",\"isAbstract\":true,\"extends\":[\"bpmn:StartEvent\"],\"properties\":[{\"name\":\"initiator\",\"isAttr\":true,\"type\":\"String\"}]},{\"name\":\"ScriptTask\",\"isAbstract\":true,\"extends\":[\"bpmn:ScriptTask\"],\"properties\":[{\"name\":\"resultVariable\",\"isAttr\":true,\"type\":\"String\"},{\"name\":\"resource\",\"isAttr\":true,\"type\":\"String\"}]},{\"name\":\"Process\",\"isAbstract\":true,\"extends\":[\"bpmn:Process\"],\"properties\":[{\"name\":\"candidateStarterGroups\",\"isAttr\":true,\"type\":\"String\"},{\"name\":\"candidateStarterUsers\",\"isAttr\":true,\"type\":\"String\"},{\"name\":\"versionTag\",\"isAttr\":true,\"type\":\"String\"},{\"name\":\"historyTimeToLive\",\"isAttr\":true,\"type\":\"String\"},{\"name\":\"isStartableInTasklist\",\"isAttr\":true,\"type\":\"Boolean\",\"default\":true}]},{\"name\":\"EscalationEventDefinition\",\"isAbstract\":true,\"extends\":[\"bpmn:EscalationEventDefinition\"],\"properties\":[{\"name\":\"escalationCodeVariable\",\"isAttr\":true,\"type\":\"String\"}]},{\"name\":\"FormalExpression\",\"isAbstract\":true,\"extends\":[\"bpmn:FormalExpression\"],\"properties\":[{\"name\":\"resource\",\"isAttr\":true,\"type\":\"String\"}]},{\"name\":\"Assignable\",\"extends\":[\"bpmn:UserTask\"],\"properties\":[{\"name\":\"candidateGroups\",\"isAttr\":true,\"type\":\"String\"},{\"name\":\"dueDate\",\"isAttr\":true,\"type\":\"String\"},{\"name\":\"followUpDate\",\"isAttr\":true,\"type\":\"String\"},{\"name\":\"priority\",\"isAttr\":true,\"type\":\"String\"}]},{\"name\":\"CallActivity\",\"extends\":[\"bpmn:CallActivity\"],\"properties\":[{\"name\":\"calledElementBinding\",\"isAttr\":true,\"type\":\"String\",\"default\":\"latest\"},{\"name\":\"calledElementVersion\",\"isAttr\":true,\"type\":\"String\"},{\"name\":\"calledElementVersionTag\",\"isAttr\":true,\"type\":\"String\"},{\"name\":\"calledElementTenantId\",\"isAttr\":true,\"type\":\"String\"},{\"name\":\"caseRef\",\"isAttr\":true,\"type\":\"String\"},{\"name\":\"caseBinding\",\"isAttr\":true,\"type\":\"String\",\"default\":\"latest\"},{\"name\":\"caseVersion\",\"isAttr\":true,\"type\":\"String\"},{\"name\":\"caseTenantId\",\"isAttr\":true,\"type\":\"String\"},{\"name\":\"variableMappingClass\",\"isAttr\":true,\"type\":\"String\"},{\"name\":\"variableMappingDelegateExpression\",\"isAttr\":true,\"type\":\"String\"}]},{\"name\":\"ServiceTaskLike\",\"extends\":[\"bpmn:ServiceTask\",\"bpmn:BusinessRuleTask\",\"bpmn:SendTask\",\"bpmn:MessageEventDefinition\"],\"properties\":[{\"name\":\"expression\",\"isAttr\":true,\"type\":\"String\"},{\"name\":\"class\",\"isAttr\":true,\"type\":\"String\"},{\"name\":\"delegateExpression\",\"isAttr\":true,\"type\":\"String\"},{\"name\":\"resultVariable\",\"isAttr\":true,\"type\":\"String\"}]},{\"name\":\"ExclusiveGateway\",\"isAbstract\":true,\"extends\":[\"bpmn:ExclusiveGateway\"],\"properties\":[{\"name\":\"serviceClass\",\"isAttr\":true,\"type\":\"String\"}]},{\"name\":\"DmnCapable\",\"extends\":[\"bpmn:BusinessRuleTask\"],\"properties\":[{\"name\":\"decisionRef\",\"isAttr\":true,\"type\":\"String\"},{\"name\":\"decisionRefBinding\",\"isAttr\":true,\"type\":\"String\",\"default\":\"latest\"},{\"name\":\"decisionRefVersion\",\"isAttr\":true,\"type\":\"String\"},{\"name\":\"mapDecisionResult\",\"isAttr\":true,\"type\":\"String\",\"default\":\"resultList\"},{\"name\":\"decisionRefTenantId\",\"isAttr\":true,\"type\":\"String\"}]},{\"name\":\"ExternalCapable\",\"extends\":[\"flowable:ServiceTaskLike\"],\"properties\":[{\"name\":\"type\",\"isAttr\":true,\"type\":\"String\"},{\"name\":\"topic\",\"isAttr\":true,\"type\":\"String\"}]},{\"name\":\"TaskPriorized\",\"extends\":[\"bpmn:Process\",\"flowable:ExternalCapable\"],\"properties\":[{\"name\":\"taskPriority\",\"isAttr\":true,\"type\":\"String\"}]},{\"name\":\"Properties\",\"superClass\":[\"Element\"],\"meta\":{\"allowedIn\":[\"*\"]},\"properties\":[{\"name\":\"values\",\"type\":\"Property\",\"isMany\":true}]},{\"name\":\"Property\",\"superClass\":[\"Element\"],\"properties\":[{\"name\":\"id\",\"type\":\"String\",\"isAttr\":true},{\"name\":\"name\",\"type\":\"String\",\"isAttr\":true},{\"name\":\"value\",\"type\":\"String\",\"isAttr\":true}]},{\"name\":\"Connector\",\"superClass\":[\"Element\"],\"meta\":{\"allowedIn\":[\"flowable:ServiceTaskLike\"]},\"properties\":[{\"name\":\"inputOutput\",\"type\":\"InputOutput\"},{\"name\":\"connectorId\",\"type\":\"String\"}]},{\"name\":\"InputOutput\",\"superClass\":[\"Element\"],\"meta\":{\"allowedIn\":[\"bpmn:FlowNode\",\"flowable:Connector\"]},\"properties\":[{\"name\":\"inputOutput\",\"type\":\"InputOutput\"},{\"name\":\"connectorId\",\"type\":\"String\"},{\"name\":\"inputParameters\",\"isMany\":true,\"type\":\"InputParameter\"},{\"name\":\"outputParameters\",\"isMany\":true,\"type\":\"OutputParameter\"}]},{\"name\":\"InputOutputParameter\",\"properties\":[{\"name\":\"name\",\"isAttr\":true,\"type\":\"String\"},{\"name\":\"value\",\"isBody\":true,\"type\":\"String\"},{\"name\":\"definition\",\"type\":\"InputOutputParameterDefinition\"}]},{\"name\":\"InputOutputParameterDefinition\",\"isAbstract\":true},{\"name\":\"List\",\"superClass\":[\"InputOutputParameterDefinition\"],\"properties\":[{\"name\":\"items\",\"isMany\":true,\"type\":\"InputOutputParameterDefinition\"}]},{\"name\":\"Map\",\"superClass\":[\"InputOutputParameterDefinition\"],\"properties\":[{\"name\":\"entries\",\"isMany\":true,\"type\":\"Entry\"}]},{\"name\":\"Entry\",\"properties\":[{\"name\":\"key\",\"isAttr\":true,\"type\":\"String\"},{\"name\":\"value\",\"isBody\":true,\"type\":\"String\"},{\"name\":\"definition\",\"type\":\"InputOutputParameterDefinition\"}]},{\"name\":\"Value\",\"superClass\":[\"InputOutputParameterDefinition\"],\"properties\":[{\"name\":\"id\",\"isAttr\":true,\"type\":\"String\"},{\"name\":\"name\",\"isAttr\":true,\"type\":\"String\"},{\"name\":\"value\",\"isBody\":true,\"type\":\"String\"}]},{\"name\":\"Script\",\"superClass\":[\"InputOutputParameterDefinition\"],\"properties\":[{\"name\":\"scriptFormat\",\"isAttr\":true,\"type\":\"String\"},{\"name\":\"resource\",\"isAttr\":true,\"type\":\"String\"},{\"name\":\"value\",\"isBody\":true,\"type\":\"String\"}]},{\"name\":\"Field\",\"superClass\":[\"Element\"],\"meta\":{\"allowedIn\":[\"flowable:ServiceTaskLike\",\"flowable:ExecutionListener\",\"flowable:TaskListener\"]},\"properties\":[{\"name\":\"name\",\"isAttr\":true,\"type\":\"String\"},{\"name\":\"expression\",\"isAttr\":true,\"type\":\"expression\"},{\"name\":\"string\",\"type\":\"string\"},{\"name\":\"stringValue\",\"isAttr\":true,\"type\":\"String\"}]},{\"name\":\"string\",\"superClass\":[\"Element\"],\"meta\":{\"allowedIn\":[\"flowable:Field\"]},\"properties\":[{\"name\":\"body\",\"isBody\":true,\"type\":\"String\"}]},{\"name\":\"expression\",\"superClass\":[\"Element\"],\"meta\":{\"allowedIn\":[\"flowable:Field\"]},\"properties\":[{\"name\":\"body\",\"isBody\":true,\"type\":\"String\"}]},{\"name\":\"InputParameter\",\"superClass\":[\"InputOutputParameter\"]},{\"name\":\"OutputParameter\",\"superClass\":[\"InputOutputParameter\"]},{\"name\":\"Collectable\",\"isAbstract\":true,\"extends\":[\"bpmn:MultiInstanceLoopCharacteristics\"],\"superClass\":[\"flowable:AsyncCapable\"],\"properties\":[{\"name\":\"collection\",\"isAttr\":true,\"type\":\"String\"},{\"name\":\"elementVariable\",\"isAttr\":true,\"type\":\"String\"}]},{\"name\":\"SequenceFlow\",\"superClass\":[\"FlowElement\"],\"properties\":[{\"name\":\"isImmediate\",\"isAttr\":true,\"type\":\"Boolean\"},{\"name\":\"conditionExpression\",\"type\":\"Expression\"},{\"name\":\"sourceRef\",\"type\":\"FlowNode\",\"isAttr\":true,\"isReference\":true},{\"name\":\"targetRef\",\"type\":\"FlowNode\",\"isAttr\":true,\"isReference\":true}]},{\"name\":\"MultiInstanceLoopCharacteristics\",\"superClass\":[\"LoopCharacteristics\"],\"properties\":[{\"name\":\"isSequential\",\"default\":false,\"isAttr\":true,\"type\":\"Boolean\"},{\"name\":\"behavior\",\"type\":\"MultiInstanceBehavior\",\"default\":\"All\",\"isAttr\":true},{\"name\":\"loopCardinality\",\"type\":\"Expression\",\"xml\":{\"serialize\":\"xsi:type\"}},{\"name\":\"loopDataInputRef\",\"type\":\"ItemAwareElement\",\"isReference\":true},{\"name\":\"loopDataOutputRef\",\"type\":\"ItemAwareElement\",\"isReference\":true},{\"name\":\"inputDataItem\",\"type\":\"DataInput\",\"xml\":{\"serialize\":\"property\"}},{\"name\":\"outputDataItem\",\"type\":\"DataOutput\",\"xml\":{\"serialize\":\"property\"}},{\"name\":\"complexBehaviorDefinition\",\"type\":\"ComplexBehaviorDefinition\",\"isMany\":true},{\"name\":\"completionCondition\",\"type\":\"Expression\",\"xml\":{\"serialize\":\"xsi:type\"}},{\"name\":\"oneBehaviorEventRef\",\"type\":\"EventDefinition\",\"isAttr\":true,\"isReference\":true},{\"name\":\"noneBehaviorEventRef\",\"type\":\"EventDefinition\",\"isAttr\":true,\"isReference\":true}]},{\"name\":\"FailedJobRetryTimeCycle\",\"superClass\":[\"Element\"],\"meta\":{\"allowedIn\":[\"flowable:AsyncCapable\",\"bpmn:MultiInstanceLoopCharacteristics\"]},\"properties\":[{\"name\":\"body\",\"isBody\":true,\"type\":\"String\"}]},{\"name\":\"ExecutionListener\",\"superClass\":[\"Element\"],\"meta\":{\"allowedIn\":[\"bpmn:Task\",\"bpmn:ServiceTask\",\"bpmn:UserTask\",\"bpmn:BusinessRuleTask\",\"bpmn:ScriptTask\",\"bpmn:ReceiveTask\",\"bpmn:ManualTask\",\"bpmn:ExclusiveGateway\",\"bpmn:SequenceFlow\",\"bpmn:ParallelGateway\",\"bpmn:InclusiveGateway\",\"bpmn:EventBasedGateway\",\"bpmn:StartEvent\",\"bpmn:IntermediateCatchEvent\",\"bpmn:IntermediateThrowEvent\",\"bpmn:EndEvent\",\"bpmn:BoundaryEvent\",\"bpmn:CallActivity\",\"bpmn:SubProcess\",\"bpmn:Process\"]},\"properties\":[{\"name\":\"expression\",\"isAttr\":true,\"type\":\"String\"},{\"name\":\"class\",\"isAttr\":true,\"type\":\"String\"},{\"name\":\"delegateExpression\",\"isAttr\":true,\"type\":\"String\"},{\"name\":\"event\",\"isAttr\":true,\"type\":\"String\"},{\"name\":\"script\",\"type\":\"Script\"},{\"name\":\"fields\",\"type\":\"Field\",\"isMany\":true}]},{\"name\":\"TaskListener\",\"superClass\":[\"Element\"],\"meta\":{\"allowedIn\":[\"bpmn:UserTask\"]},\"properties\":[{\"name\":\"expression\",\"isAttr\":true,\"type\":\"String\"},{\"name\":\"class\",\"isAttr\":true,\"type\":\"String\"},{\"name\":\"delegateExpression\",\"isAttr\":true,\"type\":\"String\"},{\"name\":\"event\",\"isAttr\":true,\"type\":\"String\"},{\"name\":\"script\",\"type\":\"Script\"},{\"name\":\"fields\",\"type\":\"Field\",\"isMany\":true}]},{\"name\":\"FormProperty\",\"superClass\":[\"Element\"],\"meta\":{\"allowedIn\":[\"bpmn:StartEvent\",\"bpmn:UserTask\"]},\"properties\":[{\"name\":\"id\",\"type\":\"String\",\"isAttr\":true},{\"name\":\"name\",\"type\":\"String\",\"isAttr\":true},{\"name\":\"type\",\"type\":\"String\",\"isAttr\":true},{\"name\":\"required\",\"type\":\"String\",\"isAttr\":true},{\"name\":\"readable\",\"type\":\"String\",\"isAttr\":true},{\"name\":\"writable\",\"type\":\"String\",\"isAttr\":true},{\"name\":\"variable\",\"type\":\"String\",\"isAttr\":true},{\"name\":\"expression\",\"type\":\"String\",\"isAttr\":true},{\"name\":\"datePattern\",\"type\":\"String\",\"isAttr\":true},{\"name\":\"default\",\"type\":\"String\",\"isAttr\":true},{\"name\":\"values\",\"type\":\"Value\",\"isMany\":true}]},{\"name\":\"FormData\",\"superClass\":[\"Element\"],\"meta\":{\"allowedIn\":[\"bpmn:StartEvent\",\"bpmn:UserTask\"]},\"properties\":[{\"name\":\"fields\",\"type\":\"FormField\",\"isMany\":true},{\"name\":\"businessKey\",\"type\":\"String\",\"isAttr\":true}]},{\"name\":\"FormField\",\"superClass\":[\"Element\"],\"properties\":[{\"name\":\"id\",\"type\":\"String\",\"isAttr\":true},{\"name\":\"label\",\"type\":\"String\",\"isAttr\":true},{\"name\":\"type\",\"type\":\"String\",\"isAttr\":true},{\"name\":\"datePattern\",\"type\":\"String\",\"isAttr\":true},{\"name\":\"defaultValue\",\"type\":\"String\",\"isAttr\":true},{\"name\":\"properties\",\"type\":\"Properties\"},{\"name\":\"validation\",\"type\":\"Validation\"},{\"name\":\"values\",\"type\":\"Value\",\"isMany\":true}]},{\"name\":\"Validation\",\"superClass\":[\"Element\"],\"properties\":[{\"name\":\"constraints\",\"type\":\"Constraint\",\"isMany\":true}]},{\"name\":\"Constraint\",\"superClass\":[\"Element\"],\"properties\":[{\"name\":\"name\",\"type\":\"String\",\"isAttr\":true},{\"name\":\"config\",\"type\":\"String\",\"isAttr\":true}]},{\"name\":\"ConditionalEventDefinition\",\"isAbstract\":true,\"extends\":[\"bpmn:ConditionalEventDefinition\"],\"properties\":[{\"name\":\"variableName\",\"isAttr\":true,\"type\":\"String\"},{\"name\":\"variableEvent\",\"isAttr\":true,\"type\":\"String\"}]}],\"emumerations\":[]}')},\"2cfa\":function(e,t,n){\"use strict\";n.r(t),t[\"default\"]={options:function(e,t,n){var r=[];return t.__slot__.options.forEach((function(n){\"button\"===t.__config__.optionType?r.push(e(\"el-radio-button\",{attrs:{label:n.value}},[n.label])):r.push(e(\"el-radio\",{attrs:{label:n.value,border:t.border}},[n.label]))})),r}}},3149:function(e,t,n){},\"449f\":function(e,t,n){},4758:function(e,t,n){\"use strict\";var r=n(\"ed08\");function i(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function a(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?i(Object(n),!0).forEach((function(t){o(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):i(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function o(e,t,n){return(t=s(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function s(e){var t=l(e,\"string\");return\"symbol\"==g(t)?t:t+\"\"}function l(e,t){if(\"object\"!=g(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||\"default\");if(\"object\"!=g(r))return r;throw new TypeError(\"@@toPrimitive must return a primitive value.\")}return(\"string\"===t?String:Number)(e)}function u(e){return d(e)||m(e)||p(e)||c()}function c(){throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\")}function p(e,t){if(e){if(\"string\"==typeof e)return f(e,t);var n={}.toString.call(e).slice(8,-1);return\"Object\"===n&&e.constructor&&(n=e.constructor.name),\"Map\"===n||\"Set\"===n?Array.from(e):\"Arguments\"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?f(e,t):void 0}}function m(e){if(\"undefined\"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e[\"@@iterator\"])return Array.from(e)}function d(e){if(Array.isArray(e))return f(e)}function f(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function g(e){return g=\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&\"function\"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?\"symbol\":typeof e},g(e)}var h={},b=n(\"9977\"),y=b.keys()||[];function v(e,t){var n=this;e.props.value=t,e.on.input=function(e){n.$emit(\"input\",e)}}function x(e,t,n){var r=h[t.__config__.tag];r&&Object.keys(r).forEach((function(i){var a=r[i];t.__slot__&&t.__slot__[i]&&n.push(a(e,t,i))}))}function w(e){var t=this;[\"on\",\"nativeOn\"].forEach((function(n){var r=Object.keys(e[n]||{});r.forEach((function(r){var i=e[n][r];\"string\"===typeof i&&(e[n][r]=function(e){return t.$emit(i,e)})}))}))}function _(e,t){var n=this;Object.keys(e).forEach((function(r){var i=e[r];\"__vModel__\"===r?v.call(n,t,e.__config__.defaultValue):void 0!==t[r]?null===t[r]||t[r]instanceof RegExp||[\"boolean\",\"string\",\"number\",\"function\"].includes(g(t[r]))?t[r]=i:Array.isArray(t[r])?t[r]=[].concat(u(t[r]),u(i)):t[r]=a(a({},t[r]),i):t.attrs[r]=i})),E(t)}function E(e){delete e.attrs.__config__,delete e.attrs.__slot__,delete e.attrs.__methods__}function k(){return{class:{},attrs:{},props:{},domProps:{},nativeOn:{},on:{},style:{},directives:[],scopedSlots:{},slot:null,key:null,ref:null,refInFor:!0}}y.forEach((function(e){var t=e.replace(/^\\.\\/(.*)\\.\\w+$/,\"$1\"),n=b(e).default;h[t]=n})),t[\"a\"]={props:{conf:{type:Object,required:!0}},render:function(e){var t=k(),n=Object(r[\"d\"])(this.conf),i=this.$slots.default||[];return x.call(this,e,n,i),w.call(this,n),_.call(this,n,t),e(this.conf.__config__.tag,t,i)}}},\"7ca5\":function(module,__webpack_exports__,__webpack_require__){\"use strict\";var _vue_babel_helper_vue_jsx_merge_props__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__(\"2638\"),_vue_babel_helper_vue_jsx_merge_props__WEBPACK_IMPORTED_MODULE_0___default=__webpack_require__.n(_vue_babel_helper_vue_jsx_merge_props__WEBPACK_IMPORTED_MODULE_0__),_utils_index__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__(\"ed08\"),_components_render_render_js__WEBPACK_IMPORTED_MODULE_2__=__webpack_require__(\"4758\");function _typeof(e){return _typeof=\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&\"function\"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?\"symbol\":typeof e},_typeof(e)}function _defineProperty(e,t,n){return(t=_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function _toPropertyKey(e){var t=_toPrimitive(e,\"string\");return\"symbol\"==_typeof(t)?t:t+\"\"}function _toPrimitive(e,t){if(\"object\"!=_typeof(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||\"default\");if(\"object\"!=_typeof(r))return r;throw new TypeError(\"@@toPrimitive must return a primitive value.\")}return(\"string\"===t?String:Number)(e)}var ruleTrigger={\"el-input\":\"blur\",\"el-input-number\":\"blur\",\"el-select\":\"change\",\"el-radio-group\":\"change\",\"el-checkbox-group\":\"change\",\"el-cascader\":\"change\",\"el-time-picker\":\"change\",\"el-date-picker\":\"change\",\"el-rate\":\"change\"},layouts={colFormItem:function(e,t){var n=t.__config__,r=buildListeners.call(this,t),i=n.labelWidth?\"\".concat(n.labelWidth,\"px\"):null;return!1===n.showLabel&&(i=\"0\"),e(\"el-col\",{attrs:{span:n.span}},[e(\"el-form-item\",{attrs:{\"label-width\":i,prop:t.__vModel__,label:n.showLabel?n.label:\"\"}},[e(_components_render_render_js__WEBPACK_IMPORTED_MODULE_2__[\"a\"],_vue_babel_helper_vue_jsx_merge_props__WEBPACK_IMPORTED_MODULE_0___default()([{attrs:{conf:t}},{on:r}]))])])},rowFormItem:function(e,t){var n=renderChildren.apply(this,arguments);return\"flex\"===t.type&&(n=e(\"el-row\",{attrs:{type:t.type,justify:t.justify,align:t.align}},[n])),e(\"el-col\",{attrs:{span:t.span}},[e(\"el-row\",{attrs:{gutter:t.gutter}},[n])])}};function renderFrom(e){var t=this.formConfCopy;return e(\"el-row\",{attrs:{gutter:t.gutter}},[e(\"el-form\",_vue_babel_helper_vue_jsx_merge_props__WEBPACK_IMPORTED_MODULE_0___default()([{attrs:{size:t.size,\"label-position\":t.labelPosition,disabled:t.disabled,\"label-width\":\"\".concat(t.labelWidth,\"px\")},ref:t.formRef},{props:{model:this[t.formModel]}},{attrs:{rules:this[t.formRules]}}]),[renderFormItem.call(this,e,t.fields),t.formBtns&&formBtns.call(this,e)])])}function formBtns(e){return e(\"el-col\",[e(\"el-form-item\",{attrs:{size:\"large\"}},[e(\"el-button\",{attrs:{type:\"primary\"},on:{click:this.submitForm}},[\"提交\"]),e(\"el-button\",{on:{click:this.resetForm}},[\"重置\"])])])}function renderFormItem(e,t){var n=this;return t.map((function(t){var r=t.__config__,i=layouts[r.layout];if(i)return i.call(n,e,t);throw new Error(\"没有与\".concat(r.layout,\"匹配的layout\"))}))}function renderChildren(e,t){var n=t.__config__;return Array.isArray(n.children)?renderFormItem.call(this,e,n.children):null}function setValue(e,t,n){this.$set(t,\"defaultValue\",e),this.$set(this[this.formConf.formModel],n.__vModel__,e)}function buildListeners(e){var t=this,n=e.__config__,r=this.formConf.__methods__||{},i={};return Object.keys(r).forEach((function(e){i[e]=function(n){return r[e].call(t,n)}})),i.input=function(r){return setValue.call(t,r,n,e)},i}__webpack_exports__[\"a\"]={components:{render:_components_render_render_js__WEBPACK_IMPORTED_MODULE_2__[\"a\"]},props:{formConf:{type:Object,required:!0}},data:function(){var e=_defineProperty(_defineProperty({formConfCopy:Object(_utils_index__WEBPACK_IMPORTED_MODULE_1__[\"d\"])(this.formConf)},this.formConf.formModel,{}),this.formConf.formRules,{});return this.initFormData(e.formConfCopy.fields,e[this.formConf.formModel]),this.buildRules(e.formConfCopy.fields,e[this.formConf.formRules]),e},methods:{initFormData:function(e,t){var n=this;e.forEach((function(e){var r=e.__config__;e.__vModel__&&(t[e.__vModel__]=r.defaultValue),r.children&&n.initFormData(r.children,t)}))},buildRules:function buildRules(componentList,rules){var _this4=this;componentList.forEach((function(cur){var config=cur.__config__;if(Array.isArray(config.regList)){if(config.required){var required={required:config.required,message:cur.placeholder};Array.isArray(config.defaultValue)&&(required.type=\"array\",required.message=\"请至少选择一个\".concat(config.label)),void 0===required.message&&(required.message=\"\".concat(config.label,\"不能为空\")),config.regList.push(required)}rules[cur.__vModel__]=config.regList.map((function(item){return item.pattern&&(item.pattern=eval(item.pattern)),item.trigger=ruleTrigger&&ruleTrigger[config.tag],item}))}config.children&&_this4.buildRules(config.children,rules)}))},resetForm:function(){this.formConfCopy=Object(_utils_index__WEBPACK_IMPORTED_MODULE_1__[\"d\"])(this.formConf),this.$refs[this.formConf.formRef].resetFields()},submitForm:function(){var e=this;this.$refs[this.formConf.formRef].validate((function(t){if(!t)return!1;var n={formData:e.formConfCopy,valData:e[e.formConf.formModel]};return e.$emit(\"submit\",n),!0}))},getData:function(){this.$emit(\"getData\",this[this.formConf.formModel])}},render:function(e){return renderFrom.call(this,e)}}},\"7f29\":function(e,t,n){\"use strict\";n.r(t),t[\"default\"]={options:function(e,t,n){var r=[];return t.__slot__.options.forEach((function(t){r.push(e(\"el-option\",{attrs:{label:t.label,value:t.value,disabled:t.disabled}}))})),r}}},\"81bc\":function(e,t,n){\"use strict\";n(\"9f9f\")},\"934c\":function(e,t,n){\"use strict\";n(\"1f81\")},9413:function(e,t,n){\"use strict\";n.r(t),t[\"default\"]={options:function(e,t,n){var r=[];return t.__slot__.options.forEach((function(n){\"button\"===t.__config__.optionType?r.push(e(\"el-checkbox-button\",{attrs:{label:n.value}},[n.label])):r.push(e(\"el-checkbox\",{attrs:{label:n.value,border:t.border}},[n.label]))})),r}}},\"9f75\":function(e,t,n){\"use strict\";var r,i,a=n(\"7ca5\"),o=a[\"a\"],s=n(\"2877\"),l=Object(s[\"a\"])(o,r,i,!1,null,null,null);t[\"a\"]=l.exports},\"9f9f\":function(e,t,n){},aace:function(e,t,n){\"use strict\";n.r(t),t[\"default\"]={default:function(e,t,n){return t.__slot__[n]}}},ae3a:function(e,t,n){\"use strict\";var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n(\"div\",{directives:[{name:\"loading\",rawName:\"v-loading\",value:e.isView,expression:\"isView\"}],staticClass:\"flow-containers\",class:{\"view-mode\":e.isView}},[n(\"el-container\",{staticStyle:{height:\"100%\"}},[n(\"el-header\",{staticStyle:{\"border-bottom\":\"1px solid rgb(218 218 218)\",height:\"auto\"}},[n(\"div\",{staticStyle:{display:\"flex\",padding:\"10px 0px\",\"justify-content\":\"space-between\"}},[n(\"div\",[n(\"el-upload\",{staticStyle:{\"margin-right\":\"10px\",display:\"inline-block\"},attrs:{action:\"\",\"before-upload\":e.openBpmn}},[n(\"el-tooltip\",{attrs:{effect:\"dark\",content:\"加载xml\",placement:\"bottom\"}},[n(\"el-button\",{attrs:{size:\"mini\",icon:\"el-icon-folder-opened\"}})],1)],1),n(\"el-tooltip\",{attrs:{effect:\"dark\",content:\"新建\",placement:\"bottom\"}},[n(\"el-button\",{attrs:{size:\"mini\",icon:\"el-icon-circle-plus\"},on:{click:e.newDiagram}})],1),n(\"el-tooltip\",{attrs:{effect:\"dark\",content:\"自适应屏幕\",placement:\"bottom\"}},[n(\"el-button\",{attrs:{size:\"mini\",icon:\"el-icon-rank\"},on:{click:e.fitViewport}})],1),n(\"el-tooltip\",{attrs:{effect:\"dark\",content:\"放大\",placement:\"bottom\"}},[n(\"el-button\",{attrs:{size:\"mini\",icon:\"el-icon-zoom-in\"},on:{click:function(t){return e.zoomViewport(!0)}}})],1),n(\"el-tooltip\",{attrs:{effect:\"dark\",content:\"缩小\",placement:\"bottom\"}},[n(\"el-button\",{attrs:{size:\"mini\",icon:\"el-icon-zoom-out\"},on:{click:function(t){return e.zoomViewport(!1)}}})],1),n(\"el-tooltip\",{attrs:{effect:\"dark\",content:\"后退\",placement:\"bottom\"}},[n(\"el-button\",{attrs:{size:\"mini\",icon:\"el-icon-back\"},on:{click:function(t){e.modeler.get(\"commandStack\").undo()}}})],1),n(\"el-tooltip\",{attrs:{effect:\"dark\",content:\"前进\",placement:\"bottom\"}},[n(\"el-button\",{attrs:{size:\"mini\",icon:\"el-icon-right\"},on:{click:function(t){e.modeler.get(\"commandStack\").redo()}}})],1)],1),n(\"div\",[n(\"el-button\",{attrs:{size:\"mini\",icon:\"el-icon-view\"},on:{click:e.showXML}},[e._v(\"查看xml\")]),n(\"el-button\",{attrs:{size:\"mini\",icon:\"el-icon-download\"},on:{click:function(t){return e.saveXML(!0)}}},[e._v(\"下载xml\")]),n(\"el-button\",{attrs:{size:\"mini\",icon:\"el-icon-picture\"},on:{click:function(t){return e.saveImg(\"svg\",!0)}}},[e._v(\"下载svg\")]),n(\"el-button\",{attrs:{size:\"mini\",type:\"primary\"},on:{click:e.save}},[e._v(\"保存模型\")])],1)])]),n(\"el-container\",{staticStyle:{\"align-items\":\"stretch\"}},[n(\"el-main\",{staticStyle:{padding:\"0\"}},[n(\"div\",{ref:\"canvas\",staticClass:\"canvas\"})]),n(\"el-aside\",{staticStyle:{width:\"400px\",\"min-height\":\"650px\",\"background-color\":\"#f0f2f5\"}},[e.modeler?n(\"panel\",{attrs:{modeler:e.modeler,users:e.users,groups:e.groups,categorys:e.categorys},on:{dataType:e.dataType}}):e._e()],1)],1)],1)],1)},i=[],a={\"Activate the global connect tool\":\"激活全局连接工具\",\"Append {type}\":\"添加 {type}\",\"Append EndEvent\":\"添加结束事件\",\"Append Gateway\":\"添加互斥网关\",\"Append Task\":\"添加任务\",\"Add Lane above\":\"在上面添加道\",\"Divide into two Lanes\":\"分割成两个道\",\"Divide into three Lanes\":\"分割成三个道\",\"Add Lane below\":\"在下面添加道\",\"Append compensation activity\":\"追加补偿活动\",\"Change type\":\"修改类型\",\"Connect using Association\":\"使用关联连接\",\"Connect using Sequence/MessageFlow or Association\":\"使用顺序/消息流或者关联连接\",\"Connect using DataInputAssociation\":\"使用数据输入关联连接\",Remove:\"移除\",\"Activate the hand tool\":\"激活抓手工具\",\"Activate the lasso tool\":\"激活套索工具\",\"Activate the create/remove space tool\":\"激活创建/删除空间工具\",\"Create expanded SubProcess\":\"创建扩展子流程\",\"Create IntermediateThrowEvent/BoundaryEvent\":\"创建中间抛出事件/边界事件\",\"Create Pool/Participant\":\"创建池/参与者\",\"Parallel Multi Instance\":\"并行多重事件\",\"Sequential Multi Instance\":\"时序多重事件\",DataObjectReference:\"数据对象参考\",DataStoreReference:\"数据存储参考\",Loop:\"循环\",\"Ad-hoc\":\"即席\",\"Create {type}\":\"创建 {type}\",Task:\"任务\",\"Send Task\":\"发送任务\",\"Receive Task\":\"接收任务\",\"User Task\":\"用户任务\",\"Manual Task\":\"手工任务\",\"Business Rule Task\":\"业务规则任务\",\"Service Task\":\"服务任务\",\"Script Task\":\"脚本任务\",\"Call Activity\":\"调用活动\",\"Sub Process (collapsed)\":\"子流程（折叠的）\",\"Sub Process (expanded)\":\"子流程（展开的）\",\"Start Event\":\"开始事件\",StartEvent:\"开始事件\",\"Intermediate Throw Event\":\"中间事件\",\"End Event\":\"结束事件\",EndEvent:\"结束事件\",\"Create Gateway\":\"创建网关\",\"Create Group\":\"创建分组\",\"Create Intermediate/Boundary Event\":\"创建中间/边界事件\",\"Message Start Event\":\"消息开始事件\",\"Timer Start Event\":\"定时开始事件\",\"Conditional Start Event\":\"条件开始事件\",\"Signal Start Event\":\"信号开始事件\",\"Error Start Event\":\"错误开始事件\",\"Escalation Start Event\":\"升级开始事件\",\"Compensation Start Event\":\"补偿开始事件\",\"Message Start Event (non-interrupting)\":\"消息开始事件（非中断）\",\"Timer Start Event (non-interrupting)\":\"定时开始事件（非中断）\",\"Conditional Start Event (non-interrupting)\":\"条件开始事件（非中断）\",\"Signal Start Event (non-interrupting)\":\"信号开始事件（非中断）\",\"Escalation Start Event (non-interrupting)\":\"升级开始事件（非中断）\",\"Message Intermediate Catch Event\":\"消息中间捕获事件\",\"Message Intermediate Throw Event\":\"消息中间抛出事件\",\"Timer Intermediate Catch Event\":\"定时中间捕获事件\",\"Escalation Intermediate Throw Event\":\"升级中间抛出事件\",\"Conditional Intermediate Catch Event\":\"条件中间捕获事件\",\"Link Intermediate Catch Event\":\"链接中间捕获事件\",\"Link Intermediate Throw Event\":\"链接中间抛出事件\",\"Compensation Intermediate Throw Event\":\"补偿中间抛出事件\",\"Signal Intermediate Catch Event\":\"信号中间捕获事件\",\"Signal Intermediate Throw Event\":\"信号中间抛出事件\",\"Message End Event\":\"消息结束事件\",\"Escalation End Event\":\"定时结束事件\",\"Error End Event\":\"错误结束事件\",\"Cancel End Event\":\"取消结束事件\",\"Compensation End Event\":\"补偿结束事件\",\"Signal End Event\":\"信号结束事件\",\"Terminate End Event\":\"终止结束事件\",\"Message Boundary Event\":\"消息边界事件\",\"Message Boundary Event (non-interrupting)\":\"消息边界事件（非中断）\",\"Timer Boundary Event\":\"定时边界事件\",\"Timer Boundary Event (non-interrupting)\":\"定时边界事件（非中断）\",\"Escalation Boundary Event\":\"升级边界事件\",\"Escalation Boundary Event (non-interrupting)\":\"升级边界事件（非中断）\",\"Conditional Boundary Event\":\"条件边界事件\",\"Conditional Boundary Event (non-interrupting)\":\"条件边界事件（非中断）\",\"Error Boundary Event\":\"错误边界事件\",\"Cancel Boundary Event\":\"取消边界事件\",\"Signal Boundary Event\":\"信号边界事件\",\"Signal Boundary Event (non-interrupting)\":\"信号边界事件（非中断）\",\"Compensation Boundary Event\":\"补偿边界事件\",\"Exclusive Gateway\":\"互斥网关\",\"Parallel Gateway\":\"并行网关\",\"Inclusive Gateway\":\"相容网关\",\"Complex Gateway\":\"复杂网关\",\"Event based Gateway\":\"事件网关\",Transaction:\"转运\",\"Sub Process\":\"子流程\",\"Event Sub Process\":\"事件子流程\",\"Collapsed Pool\":\"折叠池\",\"Expanded Pool\":\"展开池\",\"no parent for {element} in {parent}\":\"在{parent}里，{element}没有父类\",\"no shape type specified\":\"没有指定的形状类型\",\"flow elements must be children of pools/participants\":\"流元素必须是池/参与者的子类\",\"out of bounds release\":\"out of bounds release\",\"more than {count} child lanes\":\"子道大于{count} \",\"element required\":\"元素不能为空\",\"diagram not part of bpmn:Definitions\":\"流程图不符合bpmn规范\",\"no diagram to display\":\"没有可展示的流程图\",\"no process or collaboration to display\":\"没有可展示的流程/协作\",\"element {element} referenced by {referenced}#{property} not yet drawn\":\"由{referenced}#{property}引用的{element}元素仍未绘制\",\"already rendered {element}\":\"{element} 已被渲染\",\"failed to import {element}\":\"导入{element}失败\",Id:\"标识\",Name:\"名称\",General:\"常规\",Details:\"详情\",\"Message Name\":\"消息名称\",Message:\"消息\",Initiator:\"创建者\",\"Asynchronous Continuations\":\"持续异步\",\"Asynchronous Before\":\"异步前\",\"Asynchronous After\":\"异步后\",\"Job Configuration\":\"工作配置\",Exclusive:\"排除\",\"Job Priority\":\"工作优先级\",\"Retry Time Cycle\":\"重试时间周期\",Documentation:\"文档\",\"Element Documentation\":\"元素文档\",\"History Configuration\":\"历史配置\",\"History Time To Live\":\"历史的生存时间\",Forms:\"表单\",\"Form Key\":\"表单key\",\"Form Fields\":\"表单字段\",\"Business Key\":\"业务key\",\"Form Field\":\"表单字段\",ID:\"编号\",Type:\"类型\",Label:\"名称\",\"Default Value\":\"默认值\",Validation:\"校验\",\"Add Constraint\":\"添加约束\",Config:\"配置\",Properties:\"属性\",\"Add Property\":\"添加属性\",Value:\"值\",Listeners:\"监听器\",\"Execution Listener\":\"执行监听\",\"Event Type\":\"事件类型\",\"Listener Type\":\"监听器类型\",\"Java Class\":\"Java类\",Expression:\"表达式\",\"Must provide a value\":\"必须提供一个值\",\"Delegate Expression\":\"代理表达式\",Script:\"脚本\",\"Script Format\":\"脚本格式\",\"Script Type\":\"脚本类型\",\"Inline Script\":\"内联脚本\",\"External Script\":\"外部脚本\",Resource:\"资源\",\"Field Injection\":\"字段注入\",Extensions:\"扩展\",\"Input/Output\":\"输入/输出\",\"Input Parameters\":\"输入参数\",\"Output Parameters\":\"输出参数\",Parameters:\"参数\",\"Output Parameter\":\"输出参数\",\"Timer Definition Type\":\"定时器定义类型\",\"Timer Definition\":\"定时器定义\",Date:\"日期\",Duration:\"持续\",Cycle:\"循环\",Signal:\"信号\",\"Signal Name\":\"信号名称\",Escalation:\"升级\",Error:\"错误\",\"Link Name\":\"链接名称\",Condition:\"条件名称\",\"Variable Name\":\"变量名称\",\"Variable Event\":\"变量事件\",\"Specify more than one variable change event as a comma separated list.\":\"多个变量事件以逗号隔开\",\"Wait for Completion\":\"等待完成\",\"Activity Ref\":\"活动参考\",\"Version Tag\":\"版本标签\",Executable:\"可执行文件\",\"External Task Configuration\":\"扩展任务配置\",\"Task Priority\":\"任务优先级\",External:\"外部\",Connector:\"连接器\",\"Must configure Connector\":\"必须配置连接器\",\"Connector Id\":\"连接器编号\",Implementation:\"实现方式\",\"Field Injections\":\"字段注入\",Fields:\"字段\",\"Result Variable\":\"结果变量\",Topic:\"主题\",\"Configure Connector\":\"配置连接器\",\"Input Parameter\":\"输入参数\",Assignee:\"代理人\",\"Candidate Users\":\"候选用户\",\"Candidate Groups\":\"候选组\",\"Due Date\":\"到期时间\",\"Follow Up Date\":\"跟踪日期\",Priority:\"优先级\",\"The follow up date as an EL expression (e.g. ${someDate} or an ISO date (e.g. 2015-06-26T09:54:00)\":\"跟踪日期必须符合EL表达式，如： ${someDate} ,或者一个ISO标准日期，如：2015-06-26T09:54:00\",\"The due date as an EL expression (e.g. ${someDate} or an ISO date (e.g. 2015-06-26T09:54:00)\":\"跟踪日期必须符合EL表达式，如： ${someDate} ,或者一个ISO标准日期，如：2015-06-26T09:54:00\",Variables:\"变量\"},o={\"bpmn:Process\":\"流程\",\"bpmn:StartEvent\":\"开始事件\",\"bpmn:IntermediateThrowEvent\":\"中间事件\",\"bpmn:Task\":\"任务\",\"bpmn:SendTask\":\"发送任务\",\"bpmn:ReceiveTask\":\"接收任务\",\"bpmn:UserTask\":\"用户任务\",\"bpmn:ManualTask\":\"手工任务\",\"bpmn:BusinessRuleTask\":\"业务规则任务\",\"bpmn:ServiceTask\":\"服务任务\",\"bpmn:ScriptTask\":\"脚本任务\",\"bpmn:EndEvent\":\"结束事件\",\"bpmn:SequenceFlow\":\"流程线\",\"bpmn:ExclusiveGateway\":\"互斥网关\",\"bpmn:ParallelGateway\":\"并行网关\",\"bpmn:InclusiveGateway\":\"相容网关\",\"bpmn:ComplexGateway\":\"复杂网关\",\"bpmn:EventBasedGateway\":\"事件网关\"};function s(e,t){return t=t||{},e=a[e]||e,e.replace(/{([^}]+)}/g,(function(e,n){var r=t[n];return null!==a[t[n]]&&\"undefined\"!==a[t[n]]&&(r=a[t[n]]),r||\"{\"+n+\"}\"}))}var l=n(\"7024\"),u=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n(\"div\",{ref:\"propertyPanel\",staticClass:\"property-panel\"},[e.nodeName?n(\"div\",{staticClass:\"node-name\"},[e._v(e._s(e.nodeName))]):e._e(),e.element?n(e.getComponent,{tag:\"component\",attrs:{element:e.element,modeler:e.modeler,users:e.users,groups:e.groups,categorys:e.categorys},on:{dataType:e.dataType}}):e._e()],1)},c=[],p=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n(\"div\",[n(\"x-form\",{ref:\"xForm\",attrs:{config:e.formConfig},scopedSlots:e._u([{key:\"executionListener\",fn:function(){return[n(\"el-badge\",{attrs:{value:e.executionListenerLength}},[n(\"el-button\",{attrs:{size:\"small\"},on:{click:function(t){e.dialogName=\"executionListenerDialog\"}}},[e._v(\"编辑\")])],1)]},proxy:!0},{key:\"taskListener\",fn:function(){return[n(\"el-badge\",{attrs:{value:e.taskListenerLength}},[n(\"el-button\",{attrs:{size:\"small\"},on:{click:function(t){e.dialogName=\"taskListenerDialog\"}}},[e._v(\"编辑\")])],1)]},proxy:!0},{key:\"multiInstance\",fn:function(){return[n(\"el-badge\",{attrs:{\"is-dot\":e.hasMultiInstance}},[n(\"el-button\",{attrs:{size:\"small\"},on:{click:function(t){e.dialogName=\"multiInstanceDialog\"}}},[e._v(\"编辑\")])],1)]},proxy:!0}]),model:{value:e.formData,callback:function(t){e.formData=t},expression:\"formData\"}}),\"executionListenerDialog\"===e.dialogName?n(\"executionListenerDialog\",{attrs:{element:e.element,modeler:e.modeler},on:{close:e.finishExecutionListener}}):e._e(),\"taskListenerDialog\"===e.dialogName?n(\"taskListenerDialog\",{attrs:{element:e.element,modeler:e.modeler},on:{close:e.finishTaskListener}}):e._e(),\"multiInstanceDialog\"===e.dialogName?n(\"multiInstanceDialog\",{attrs:{element:e.element,modeler:e.modeler},on:{close:e.finishMultiInstance}}):e._e()],1)},m=[],d=n(\"9b3c\"),f=n.n(d),g=n(\"a8b6\"),h={\"bpmn:EndEvent\":{},\"bpmn:StartEvent\":{initiator:!0,formKey:!0},\"bpmn:UserTask\":{userType:!0,dataType:!0,assignee:!0,candidateUsers:!0,candidateGroups:!0,async:!1,priority:!1,formKey:!1,skipExpression:!1,taskListener:!0},\"bpmn:ServiceTask\":{async:!0,skipExpression:!0,isForCompensation:!0,triggerable:!0,class:!0},\"bpmn:ScriptTask\":{async:!0,isForCompensation:!0,autoStoreVariables:!0},\"bpmn:ManualTask\":{async:!0,isForCompensation:!0},\"bpmn:ReceiveTask\":{async:!0,isForCompensation:!0},\"bpmn:SendTask\":{async:!0,isForCompensation:!0},\"bpmn:BusinessRuleTask\":{async:!0,isForCompensation:!0,ruleVariablesInput:!0,rules:!0,resultVariable:!0,exclude:!0}};g[\"a\"].set({input:{},select:{},colorPicker:{showAlpha:!0},xform:{form:{labelWidth:\"auto\"}}});var b={components:{xForm:f.a.xForm},props:{modeler:{type:Object,required:!0},element:{type:Object,required:!0},categorys:{type:Array,default:function(){return[]}}},watch:{\"formData.id\":function(e){this.updateProperties({id:e})},\"formData.name\":function(e){this.updateProperties({name:e})},\"formData.documentation\":function(e){if(e){var t=this.modeler.get(\"moddle\").create(\"bpmn:Documentation\",{text:e});this.updateProperties({documentation:[t]})}else this.updateProperties({documentation:[]})}},methods:{updateProperties:function(e){var t=this.modeler.get(\"modeling\");t.updateProperties(this.element,e)}},computed:{elementType:function(){var e=this.element.businessObject;return e.eventDefinitions?e.eventDefinitions[0].$type:e.$type},showConfig:function(){return h[this.elementType]||{}}}},y=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n(\"div\",[n(\"el-dialog\",{attrs:{title:\"执行监听器\",visible:e.dialogVisible,width:\"900px\",\"close-on-click-modal\":!1,\"close-on-press-escape\":!1,\"show-close\":!1},on:{\"update:visible\":function(t){e.dialogVisible=t},closed:function(t){return e.$emit(\"close\")}}},[n(\"x-form\",{ref:\"xForm\",attrs:{config:e.formConfig},scopedSlots:e._u([{key:\"params\",fn:function(t){return[n(\"el-badge\",{attrs:{value:t.row.params?t.row.params.length:0,type:\"primary\"}},[n(\"el-button\",{attrs:{size:\"small\"},on:{click:function(n){return e.configParam(t.$index)}}},[e._v(\"配置\")])],1)]}}]),model:{value:e.formData,callback:function(t){e.formData=t},expression:\"formData\"}}),n(\"span\",{staticClass:\"dialog-footer\",attrs:{slot:\"footer\"},slot:\"footer\"},[n(\"el-button\",{attrs:{type:\"primary\",size:\"medium\"},on:{click:e.closeDialog}},[e._v(\"确 定\")])],1)],1),e.showParamDialog?n(\"listenerParam\",{attrs:{value:e.formData.executionListener[e.nowIndex].params},on:{close:e.finishConfigParam}}):e._e()],1)},v=[],x=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n(\"div\",[n(\"el-dialog\",{attrs:{title:\"监听器参数\",visible:e.dialogVisible,width:\"700px\",\"close-on-click-modal\":!1,\"close-on-press-escape\":!1,\"show-close\":!1},on:{\"update:visible\":function(t){e.dialogVisible=t},closed:function(t){return e.$emit(\"close\",e.formData.paramList)}}},[n(\"x-form\",{ref:\"xForm\",attrs:{config:e.formConfig},model:{value:e.formData,callback:function(t){e.formData=t},expression:\"formData\"}}),n(\"span\",{staticClass:\"dialog-footer\",attrs:{slot:\"footer\"},slot:\"footer\"},[n(\"el-button\",{attrs:{type:\"primary\",size:\"medium\"},on:{click:e.closeDialog}},[e._v(\"确 定\")])],1)],1)],1)},w=[];g[\"a\"].set({input:{},select:{},colorPicker:{showAlpha:!0},xform:{form:{labelWidth:\"auto\"}}});var _={components:{xForm:f.a.xForm}},E={mixins:[_],props:{value:{type:Array,default:function(){return[]}}},data:function(){return{dialogVisible:!0,formData:{paramList:this.value}}},computed:{formConfig:function(){return{inline:!1,item:[{xType:\"tabs\",tabs:[{label:\"监听器参数\",name:\"paramList\",column:[{label:\"类型\",name:\"type\",width:180,rules:[{required:!0,message:\"请选择\",trigger:[\"blur\",\"change\"]}],xType:\"select\",dic:[{label:\"字符串\",value:\"stringValue\"},{label:\"表达式\",value:\"expression\"}]},{label:\"名称\",name:\"name\",width:180,rules:[{required:!0,message:\"请选择\",trigger:[\"blur\",\"change\"]}],xType:\"input\"},{label:\"值\",name:\"value\",xType:\"input\",rules:[{required:!0,message:\"请输入\",trigger:[\"blur\",\"change\"]}]}]}]}]}}},methods:{closeDialog:function(){var e=this;this.$refs.xForm.validate().then((function(){e.dialogVisible=!1})).catch((function(e){return console.error(e)}))}}},k=E,S=(n(\"934c\"),n(\"2877\")),T=Object(S[\"a\"])(k,x,w,!1,null,null,null),D=T.exports,C={components:{listenerParam:D},mixins:[b],data:function(){return{dialogVisible:!0,showParamDialog:!1,nowIndex:null,formData:{executionListener:[]}}},computed:{formConfig:function(){return{inline:!1,item:[{xType:\"tabs\",tabs:[{label:\"执行监听器\",name:\"executionListener\",column:[{label:\"事件\",name:\"event\",width:180,rules:[{required:!0,message:\"请选择\",trigger:[\"blur\",\"change\"]}],xType:\"select\",dic:[{label:\"start\",value:\"start\"},{label:\"end\",value:\"end\"},{label:\"take\",value:\"take\"}]},{label:\"类型\",name:\"type\",width:180,rules:[{required:!0,message:\"请选择\",trigger:[\"blur\",\"change\"]}],xType:\"select\",dic:[{label:\"类\",value:\"class\"},{label:\"表达式\",value:\"expression\"},{label:\"委托表达式\",value:\"delegateExpression\"}],tooltip:\"类：示例 com.company.MyCustomListener，自定义类必须实现 org.flowable.engine.delegate.TaskListener 接口 <br />\\n                              表达式：示例 ${myObject.callMethod(task, task.eventName)} <br />\\n                              委托表达式：示例 ${myListenerSpringBean} ，该 springBean 需要实现 org.flowable.engine.delegate.TaskListener 接口\\n                    \"},{label:\"java 类名\",name:\"className\",xType:\"input\",rules:[{required:!0,message:\"请输入\",trigger:[\"blur\",\"change\"]}]},{xType:\"slot\",label:\"参数\",width:120,slot:!0,name:\"params\"}]}]}]}}},mounted:function(){var e,t;this.formData.executionListener=null!==(e=null===(t=this.element.businessObject.extensionElements)||void 0===t?void 0:t.values.filter((function(e){return\"flowable:ExecutionListener\"===e.$type})).map((function(e){var t,n,r;return\"class\"in e&&(r=\"class\"),\"expression\"in e&&(r=\"expression\"),\"delegateExpression\"in e&&(r=\"delegateExpression\"),{event:e.event,type:r,className:e[r],params:null!==(t=null===(n=e.fields)||void 0===n?void 0:n.map((function(e){var t;return\"stringValue\"in e&&(t=\"stringValue\"),\"expression\"in e&&(t=\"expression\"),{name:e.name,type:t,value:e[t]}})))&&void 0!==t?t:[]}})))&&void 0!==e?e:[]},methods:{configParam:function(e){this.nowIndex=e;var t=this.formData.executionListener[e];t.params||(t.params=[]),this.showParamDialog=!0},finishConfigParam:function(e){this.showParamDialog=!1;var t=this.formData.executionListener[this.nowIndex];t.params=e,this.$set(this.formData.executionListener[this.nowIndex],this.nowIndex,t),this.nowIndex=null},updateElement:function(){var e,t=this;if(null!==(e=this.formData.executionListener)&&void 0!==e&&e.length){var n,r,i=this.element.businessObject.get(\"extensionElements\");i||(i=this.modeler.get(\"moddle\").create(\"bpmn:ExtensionElements\")),i.values=null!==(n=null===(r=i.values)||void 0===r?void 0:r.filter((function(e){return\"flowable:ExecutionListener\"!==e.$type})))&&void 0!==n?n:[],this.formData.executionListener.forEach((function(e){var n=t.modeler.get(\"moddle\").create(\"flowable:ExecutionListener\");n[\"event\"]=e.event,n[e.type]=e.className,e.params&&e.params.length&&e.params.forEach((function(e){var r=t.modeler.get(\"moddle\").create(\"flowable:Field\");r[\"name\"]=e.name,r[e.type]=e.value,n.get(\"fields\").push(r)})),i.get(\"values\").push(n)})),this.updateProperties({extensionElements:i})}else{var a,o,s=this.element.businessObject[\"extensionElements\"];if(s)s.values=null!==(a=null===(o=s.values)||void 0===o?void 0:o.filter((function(e){return\"flowable:ExecutionListener\"!==e.$type})))&&void 0!==a?a:[]}},closeDialog:function(){var e=this;this.$refs.xForm.validate().then((function(){e.updateElement(),e.dialogVisible=!1})).catch((function(e){return console.error(e)}))}}},P=C,L=(n(\"0b11\"),Object(S[\"a\"])(P,y,v,!1,null,null,null)),A=L.exports,O=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n(\"div\",[n(\"el-dialog\",{attrs:{title:\"任务监听器\",visible:e.dialogVisible,width:\"900px\",\"close-on-click-modal\":!1,\"close-on-press-escape\":!1,\"show-close\":!1},on:{\"update:visible\":function(t){e.dialogVisible=t},closed:function(t){return e.$emit(\"close\")}}},[n(\"x-form\",{ref:\"xForm\",attrs:{config:e.formConfig},scopedSlots:e._u([{key:\"params\",fn:function(t){return[n(\"el-badge\",{attrs:{value:t.row.params?t.row.params.length:0,type:\"primary\"}},[n(\"el-button\",{attrs:{size:\"small\"},on:{click:function(n){return e.configParam(t.$index)}}},[e._v(\"配置\")])],1)]}}]),model:{value:e.formData,callback:function(t){e.formData=t},expression:\"formData\"}}),n(\"span\",{staticClass:\"dialog-footer\",attrs:{slot:\"footer\"},slot:\"footer\"},[n(\"el-button\",{attrs:{type:\"primary\",size:\"medium\"},on:{click:e.closeDialog}},[e._v(\"确 定\")])],1)],1),e.showParamDialog?n(\"listenerParam\",{attrs:{value:e.formData.taskListener[e.nowIndex].params},on:{close:e.finishConfigParam}}):e._e()],1)},I=[],j={components:{listenerParam:D},mixins:[b],data:function(){return{dialogVisible:!0,showParamDialog:!1,nowIndex:null,formData:{taskListener:[]}}},computed:{formConfig:function(){return{inline:!1,item:[{xType:\"tabs\",tabs:[{label:\"任务监听器\",name:\"taskListener\",column:[{label:\"事件\",name:\"event\",width:180,rules:[{required:!0,message:\"请选择\",trigger:[\"blur\",\"change\"]}],xType:\"select\",dic:[{label:\"create\",value:\"create\"},{label:\"assignment\",value:\"assignment\"},{label:\"complete\",value:\"complete\"},{label:\"delete\",value:\"delete\"}],tooltip:\"create（创建）：当任务已经创建，并且所有任务参数都已经设置时触发。<br />\\n                              assignment（指派）：当任务已经指派给某人时触发。请注意：当流程执行到达用户任务时，在触发create事件之前，会首先触发assignment事件。<br />\\n                              complete（完成）：当任务已经完成，从运行时数据中删除前触发。<br />\\n                              delete（删除）：在任务即将被删除前触发。请注意任务由completeTask正常完成时也会触发。\\n                    \"},{label:\"类型\",name:\"type\",width:180,rules:[{required:!0,message:\"请选择\",trigger:[\"blur\",\"change\"]}],xType:\"select\",dic:[{label:\"类\",value:\"class\"},{label:\"表达式\",value:\"expression\"},{label:\"委托表达式\",value:\"delegateExpression\"}]},{label:\"java 类名\",name:\"className\",xType:\"input\",rules:[{required:!0,message:\"请输入\",trigger:[\"blur\",\"change\"]}]},{xType:\"slot\",label:\"参数\",width:120,slot:!0,name:\"params\"}]}]}]}}},mounted:function(){var e,t;this.formData.taskListener=null!==(e=null===(t=this.element.businessObject.extensionElements)||void 0===t?void 0:t.values.filter((function(e){return\"flowable:TaskListener\"===e.$type})).map((function(e){var t,n,r;return\"class\"in e&&(r=\"class\"),\"expression\"in e&&(r=\"expression\"),\"delegateExpression\"in e&&(r=\"delegateExpression\"),{event:e.event,type:r,className:e[r],params:null!==(t=null===(n=e.fields)||void 0===n?void 0:n.map((function(e){var t;return\"stringValue\"in e&&(t=\"stringValue\"),\"expression\"in e&&(t=\"expression\"),{name:e.name,type:t,value:e[t]}})))&&void 0!==t?t:[]}})))&&void 0!==e?e:[]},methods:{configParam:function(e){this.nowIndex=e;var t=this.formData.taskListener[e];t.params||(t.params=[]),this.showParamDialog=!0},finishConfigParam:function(e){this.showParamDialog=!1;var t=this.formData.taskListener[this.nowIndex];t.params=e,this.$set(this.formData.taskListener[this.nowIndex],this.nowIndex,t),this.nowIndex=null},updateElement:function(){var e,t=this;if(null!==(e=this.formData.taskListener)&&void 0!==e&&e.length){var n,r,i=this.element.businessObject.get(\"extensionElements\");i||(i=this.modeler.get(\"moddle\").create(\"bpmn:ExtensionElements\")),i.values=null!==(n=null===(r=i.values)||void 0===r?void 0:r.filter((function(e){return\"flowable:TaskListener\"!==e.$type})))&&void 0!==n?n:[],this.formData.taskListener.forEach((function(e){var n=t.modeler.get(\"moddle\").create(\"flowable:TaskListener\");n[\"event\"]=e.event,n[e.type]=e.className,e.params&&e.params.length&&e.params.forEach((function(e){var r=t.modeler.get(\"moddle\").create(\"flowable:Field\");r[\"name\"]=e.name,r[e.type]=e.value,n.get(\"fields\").push(r)})),i.get(\"values\").push(n)})),this.updateProperties({extensionElements:i})}else{var a,o,s=this.element.businessObject[\"extensionElements\"];if(s)s.values=null!==(a=null===(o=s.values)||void 0===o?void 0:o.filter((function(e){return\"flowable:TaskListener\"!==e.$type})))&&void 0!==a?a:[]}},closeDialog:function(){var e=this;this.$refs.xForm.validate().then((function(){e.updateElement(),e.dialogVisible=!1})).catch((function(e){return console.error(e)}))}}},M=j,F=(n(\"c0fc\"),Object(S[\"a\"])(M,O,I,!1,null,null,null)),B=F.exports,N=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n(\"div\",[n(\"el-dialog\",{staticClass:\"muti-instance\",attrs:{title:\"多实例配置\",visible:e.dialogVisible,width:\"500px\",\"close-on-click-modal\":!1,\"close-on-press-escape\":!1,\"show-close\":!1},on:{\"update:visible\":function(t){e.dialogVisible=t},closed:function(t){return e.$emit(\"close\")}}},[n(\"el-alert\",{staticStyle:{\"margin-bottom\":\"20px\"},attrs:{type:\"info\",closable:!1,\"show-icon\":\"\"},scopedSlots:e._u([{key:\"title\",fn:function(){return[e._v(\" 按照BPMN2.0规范的要求，用于为每个实例创建执行的父执行，会提供下列变量:\"),n(\"br\"),e._v(\" nrOfInstances：实例总数。\"),n(\"br\"),e._v(\" nrOfActiveInstances：当前活动的（即未完成的），实例数量。对于顺序多实例，这个值总为1。\"),n(\"br\"),e._v(\" nrOfCompletedInstances：已完成的实例数量。\"),n(\"br\"),e._v(\" loopCounter：给定实例在for-each循环中的index。\"),n(\"br\")]},proxy:!0}])}),n(\"x-form\",{ref:\"xForm\",attrs:{config:e.formConfig},model:{value:e.formData,callback:function(t){e.formData=t},expression:\"formData\"}})],1)],1)},V=[];function $(e){return $=\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&\"function\"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?\"symbol\":typeof e},$(e)}function R(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function U(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?R(Object(n),!0).forEach((function(t){q(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):R(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function q(e,t,n){return(t=z(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function z(e){var t=G(e,\"string\");return\"symbol\"==$(t)?t:t+\"\"}function G(e,t){if(\"object\"!=$(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||\"default\");if(\"object\"!=$(r))return r;throw new TypeError(\"@@toPrimitive must return a primitive value.\")}return(\"string\"===t?String:Number)(e)}function K(e){var t=U(U({},e.businessObject),e.businessObject.$attrs);return W(t)}function W(e){for(var t in e)if(0===t.indexOf(\"flowable:\")){var n=t.replace(\"flowable:\",\"\");e[n]=e[t],delete e[t]}return e=J(e),e}function J(e){if(\"documentation\"in e){var t=\"\";e.documentation.forEach((function(e){t+=e.text})),e.documentation=t}return e}function X(e){return\"conditionExpression\"in e&&(e.conditionExpression=e.conditionExpression.body),e}function H(e){for(var t in e){var n;if(\"candidateUsers\"===t)e.userType=\"candidateUsers\",e[t]=(null===(n=e[t])||void 0===n?void 0:n.split(\",\"))||[];else if(\"candidateGroups\"===t){var r;e.userType=\"candidateGroups\",e[t]=(null===(r=e[t])||void 0===r?void 0:r.split(\",\"))||[]}else\"assignee\"===t&&(e.userType=\"assignee\")}return e}var Y={mixins:[b],data:function(){return{dialogVisible:!0,formData:{}}},computed:{formConfig:function(){var e=this;return{inline:!1,item:[{xType:\"input\",name:\"collection\",label:\"集合\",tooltip:\"属性会作为表达式进行解析。如果表达式解析为字符串而不是一个集合，<br />不论是因为本身配置的就是静态字符串值，还是表达式计算结果为字符串，<br />这个字符串都会被当做变量名，并从流程变量中用于获取实际的集合。\"},{xType:\"input\",name:\"elementVariable\",label:\"元素变量\",tooltip:\"每创建一个用户任务前，先以该元素变量为label，集合中的一项为value，<br />创建（局部）流程变量，该局部流程变量被用于指派用户任务。<br />一般来说，该字符串应与指定人员变量相同。\"},{xType:\"radio\",name:\"isSequential\",label:\"执行方式\",dic:[{label:\"串行\",value:!0},{label:\"并行\",value:!1}]},{xType:\"input\",name:\"completionCondition\",label:\"完成条件\",tooltip:\"多实例活动在所有实例都完成时结束，然而也可以指定一个表达式，在每个实例<br />结束时进行计算。当表达式计算为true时，将销毁所有剩余的实例，并结束多实例<br />活动，继续执行流程。例如 ${nrOfCompletedInstances/nrOfInstances >= 0.6 }，<br />表示当任务完成60%时，该节点就算完成\"}],operate:[{text:\"确定\",show:!0,click:e.save},{text:\"清空\",show:!0,click:function(){e.formData={}}}]}}},mounted:function(){var e,t,n=JSON.parse(JSON.stringify(null!==(e=this.element.businessObject.loopCharacteristics)&&void 0!==e?e:{}));n.completionCondition=null===(t=n.completionCondition)||void 0===t?void 0:t.body,this.formData=W(n)},methods:{updateElement:function(){if(null!==this.formData.isSequential&&void 0!==this.formData.isSequential){var e=this.element.businessObject.get(\"loopCharacteristics\");if(e||(e=this.modeler.get(\"moddle\").create(\"bpmn:MultiInstanceLoopCharacteristics\")),e[\"isSequential\"]=this.formData.isSequential,e[\"collection\"]=this.formData.collection,e[\"elementVariable\"]=this.formData.elementVariable,this.formData.completionCondition){var t=this.modeler.get(\"moddle\").create(\"bpmn:Expression\",{body:this.formData.completionCondition});e[\"completionCondition\"]=t}this.updateProperties({loopCharacteristics:e})}else delete this.element.businessObject.loopCharacteristics},save:function(){this.updateElement(),this.dialogVisible=!1}}},Q=Y,Z=(n(\"d314\"),Object(S[\"a\"])(Q,N,V,!1,null,null,null)),ee=Z.exports,te={components:{executionListenerDialog:A,taskListenerDialog:B,multiInstanceDialog:ee},mixins:[b],props:{users:{type:Array,required:!0},groups:{type:Array,required:!0}},data:function(){return{userTypeOption:[{label:\"指定人员\",value:\"assignee\"},{label:\"候选人员\",value:\"candidateUsers\"},{label:\"候选组\",value:\"candidateGroups\"}],dataTypeOption:[{label:\"固定\",value:\"fixed\"},{label:\"动态\",value:\"dynamic\"}],dialogName:\"\",executionListenerLength:0,taskListenerLength:0,hasMultiInstance:!1,formData:{}}},computed:{formConfig:function(){var e=this;return{inline:!1,item:[{xType:\"input\",name:\"id\",label:\"节点 id\",rules:[{required:!0,message:\"Id 不能为空\"}]},{xType:\"input\",name:\"name\",label:\"节点名称\",rules:[{required:!0,message:\"节点名称不能为空\"}]},{xType:\"input\",name:\"documentation\",label:\"节点描述\"},{xType:\"slot\",name:\"executionListener\",label:\"执行监听器\"},{xType:\"slot\",name:\"taskListener\",label:\"任务监听器\",show:!!e.showConfig.taskListener},{xType:\"select\",name:\"userType\",label:\"人员类型\",dic:e.userTypeOption,show:!!e.showConfig.userType},{xType:\"radio\",name:\"dataType\",label:\"指定方式\",dic:e.dataTypeOption,show:!!e.showConfig.dataType,rules:[{required:!0,message:\"请指定方式\"}]},{xType:\"select\",name:\"assignee\",label:\"指定人员\",allowCreate:!0,filterable:!0,dic:{data:e.users,label:\"nickName\",value:\"userId\"},show:!!e.showConfig.assignee&&\"assignee\"===e.formData.userType},{xType:\"select\",name:\"candidateUsers\",label:\"候选人员\",multiple:!0,allowCreate:!0,filterable:!0,dic:{data:e.users,label:\"nickName\",value:\"userId\"},show:!!e.showConfig.candidateUsers&&\"candidateUsers\"===e.formData.userType},{xType:\"select\",name:\"candidateGroups\",label:\"候选组\",multiple:!0,allowCreate:!0,filterable:!0,dic:{data:e.groups,label:\"roleName\",value:\"roleId\"},show:!!e.showConfig.candidateGroups&&\"candidateGroups\"===e.formData.userType},{xType:\"slot\",name:\"multiInstance\",label:\"多实例\"},{xType:\"switch\",name:\"async\",label:\"异步\",activeText:\"是\",inactiveText:\"否\",show:!!e.showConfig.async},{xType:\"input\",name:\"priority\",label:\"优先级\",show:!!e.showConfig.priority},{xType:\"input\",name:\"formKey\",label:\"表单标识key\",show:!!e.showConfig.formKey},{xType:\"input\",name:\"skipExpression\",label:\"跳过表达式\",show:!!e.showConfig.skipExpression},{xType:\"switch\",name:\"isForCompensation\",label:\"是否为补偿\",activeText:\"是\",inactiveText:\"否\",show:!!e.showConfig.isForCompensation},{xType:\"switch\",name:\"triggerable\",label:\"服务任务可触发\",activeText:\"是\",inactiveText:\"否\",show:!!e.showConfig.triggerable},{xType:\"switch\",name:\"autoStoreVariables\",label:\"自动存储变量\",activeText:\"是\",inactiveText:\"否\",show:!!e.showConfig.autoStoreVariables},{xType:\"input\",name:\"ruleVariablesInput\",label:\"输入变量\",show:!!e.showConfig.ruleVariablesInput},{xType:\"input\",name:\"rules\",label:\"规则\",show:!!e.showConfig.rules},{xType:\"input\",name:\"resultVariable\",label:\"结果变量\",show:!!e.showConfig.resultVariable},{xType:\"switch\",name:\"exclude\",label:\"排除\",activeText:\"是\",inactiveText:\"否\",show:!!e.showConfig.exclude},{xType:\"input\",name:\"class\",label:\"类\",show:!!e.showConfig.class},{xType:\"datePicker\",type:\"datetime\",name:\"dueDate\",label:\"到期时间\",show:!!e.showConfig.dueDate}]}}},watch:{\"formData.userType\":function(e,t){var n=this;if(t){var r=[\"assignee\",\"candidateUsers\",\"candidateGroups\"];r.forEach((function(e){delete n.element.businessObject.$attrs[\"flowable:\".concat(e)],delete n.formData[e]}))}},\"formData.dataType\":function(e){var t=this,n=this;this.updateProperties({\"flowable:dataType\":e}),\"dynamic\"===e&&this.updateProperties({\"flowable:userType\":n.formData.userType});var r=[\"assignee\",\"candidateUsers\",\"candidateGroups\"];r.forEach((function(e){delete t.element.businessObject.$attrs[\"flowable:\".concat(e)],delete t.formData[e]}));var i={dataType:e,userType:this.formData.userType};this.$emit(\"dataType\",i)},\"formData.assignee\":function(e){\"assignee\"===this.formData.userType?this.updateProperties({\"flowable:assignee\":e}):delete this.element.businessObject.$attrs[\"flowable:assignee\"]},\"formData.candidateUsers\":function(e){\"candidateUsers\"===this.formData.userType?this.updateProperties({\"flowable:candidateUsers\":null===e||void 0===e?void 0:e.join(\",\")}):delete this.element.businessObject.$attrs[\"flowable:candidateUsers\"]},\"formData.candidateGroups\":function(e){\"candidateGroups\"===this.formData.userType?this.updateProperties({\"flowable:candidateGroups\":null===e||void 0===e?void 0:e.join(\",\")}):delete this.element.businessObject.$attrs[\"flowable:candidateGroups\"]},\"formData.async\":function(e){\"\"===e&&(e=null),this.updateProperties({\"flowable:async\":!0})},\"formData.dueDate\":function(e){\"\"===e&&(e=null),this.updateProperties({\"flowable:dueDate\":e})},\"formData.formKey\":function(e){\"\"===e&&(e=null),this.updateProperties({\"flowable:formKey\":e})},\"formData.priority\":function(e){\"\"===e&&(e=null),this.updateProperties({\"flowable:priority\":e})},\"formData.skipExpression\":function(e){\"\"===e&&(e=null),this.updateProperties({\"flowable:skipExpression\":e})},\"formData.isForCompensation\":function(e){\"\"===e&&(e=null),this.updateProperties({isForCompensation:e})},\"formData.triggerable\":function(e){\"\"===e&&(e=null),this.updateProperties({\"flowable:triggerable\":e})},\"formData.class\":function(e){\"\"===e&&(e=null),this.updateProperties({\"flowable:class\":e})},\"formData.autoStoreVariables\":function(e){\"\"===e&&(e=null),this.updateProperties({\"flowable:autoStoreVariables\":e})},\"formData.exclude\":function(e){\"\"===e&&(e=null),this.updateProperties({\"flowable:exclude\":e})},\"formData.ruleVariablesInput\":function(e){\"\"===e&&(e=null),this.updateProperties({\"flowable:ruleVariablesInput\":e})},\"formData.rules\":function(e){\"\"===e&&(e=null),this.updateProperties({\"flowable:rules\":e})},\"formData.resultVariable\":function(e){\"\"===e&&(e=null),this.updateProperties({\"flowable:resultVariable\":e})}},created:function(){var e=K(this.element);e=H(e),this.formData=e,this.computedExecutionListenerLength(),this.computedTaskListenerLength(),this.computedHasMultiInstance()},methods:{computedExecutionListenerLength:function(){var e,t;this.executionListenerLength=null!==(e=null===(t=this.element.businessObject.extensionElements)||void 0===t||null===(t=t.values)||void 0===t?void 0:t.filter((function(e){return\"flowable:ExecutionListener\"===e.$type})).length)&&void 0!==e?e:0},computedTaskListenerLength:function(){var e,t;this.taskListenerLength=null!==(e=null===(t=this.element.businessObject.extensionElements)||void 0===t||null===(t=t.values)||void 0===t?void 0:t.filter((function(e){return\"flowable:TaskListener\"===e.$type})).length)&&void 0!==e?e:0},computedHasMultiInstance:function(){this.element.businessObject.loopCharacteristics?this.hasMultiInstance=!0:this.hasMultiInstance=!1},finishExecutionListener:function(){\"executionListenerDialog\"===this.dialogName&&this.computedExecutionListenerLength(),this.dialogName=\"\"},finishTaskListener:function(){\"taskListenerDialog\"===this.dialogName&&this.computedTaskListenerLength(),this.dialogName=\"\"},finishMultiInstance:function(){\"multiInstanceDialog\"===this.dialogName&&this.computedHasMultiInstance(),this.dialogName=\"\"}}},ne=te,re=Object(S[\"a\"])(ne,p,m,!1,null,null,null),ie=re.exports,ae=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n(\"div\",[n(\"x-form\",{ref:\"xForm\",attrs:{config:e.formConfig},scopedSlots:e._u([{key:\"executionListener\",fn:function(){return[n(\"el-badge\",{attrs:{value:e.executionListenerLength}},[n(\"el-button\",{attrs:{size:\"small\"},on:{click:function(t){e.dialogName=\"executionListenerDialog\"}}},[e._v(\"编辑\")])],1)]},proxy:!0}]),model:{value:e.formData,callback:function(t){e.formData=t},expression:\"formData\"}}),\"executionListenerDialog\"===e.dialogName?n(\"executionListenerDialog\",{attrs:{element:e.element,modeler:e.modeler},on:{close:e.finishExecutionListener}}):e._e()],1)},oe=[],se={components:{executionListenerDialog:A},data:function(){return{executionListenerLength:0,dialogName:null}},methods:{computedExecutionListenerLength:function(){var e,t;this.executionListenerLength=null!==(e=null===(t=this.element.businessObject.extensionElements)||void 0===t||null===(t=t.values)||void 0===t?void 0:t.length)&&void 0!==e?e:0},finishExecutionListener:function(){\"executionListenerDialog\"===this.dialogName&&this.computedExecutionListenerLength(),this.dialogName=\"\"}}},le={mixins:[b,se],data:function(){return{formData:{}}},computed:{formConfig:function(){var e=this;return{inline:!1,item:[{xType:\"input\",name:\"id\",label:\"节点 id\",rules:[{required:!0,message:\"Id 不能为空\"}]},{xType:\"input\",name:\"name\",label:\"节点名称\"},{xType:\"input\",name:\"documentation\",label:\"节点描述\"},{xType:\"slot\",name:\"executionListener\",label:\"执行监听器\"},{xType:\"input\",name:\"initiator\",label:\"发起人\",show:!!e.showConfig.initiator},{xType:\"input\",name:\"formKey\",label:\"表单标识key\",show:!!e.showConfig.formKey}]}}},watch:{\"formData.initiator\":function(e){\"\"===e&&(e=null),this.updateProperties({\"flowable:initiator\":e})},\"formData.formKey\":function(e){\"\"===e&&(e=null),this.updateProperties({\"flowable:formKey\":e})}},created:function(){this.formData=K(this.element)}},ue=le,ce=Object(S[\"a\"])(ue,ae,oe,!1,null,null,null),pe=ce.exports,me=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n(\"div\",[n(\"x-form\",{ref:\"xForm\",attrs:{config:e.formConfig},scopedSlots:e._u([{key:\"executionListener\",fn:function(){return[n(\"el-badge\",{attrs:{value:e.executionListenerLength}},[n(\"el-button\",{attrs:{size:\"small\"},on:{click:function(t){e.dialogName=\"executionListenerDialog\"}}},[e._v(\"编辑\")])],1)]},proxy:!0},{key:\"signal\",fn:function(){return[n(\"el-badge\",{attrs:{value:e.signalLength}},[n(\"el-button\",{attrs:{size:\"small\"},on:{click:function(t){e.dialogName=\"signalDialog\"}}},[e._v(\"编辑\")])],1)]},proxy:!0}]),model:{value:e.formData,callback:function(t){e.formData=t},expression:\"formData\"}}),\"executionListenerDialog\"===e.dialogName?n(\"executionListenerDialog\",{attrs:{element:e.element,modeler:e.modeler},on:{close:e.finishExecutionListener}}):e._e(),\"signalDialog\"===e.dialogName?n(\"signalDialog\",{attrs:{element:e.element,modeler:e.modeler},on:{close:e.finishExecutionListener}}):e._e()],1)},de=[],fe=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n(\"div\",[n(\"el-dialog\",{attrs:{title:\"信号定义\",visible:e.dialogVisible,width:\"700px\",\"close-on-click-modal\":!1,\"close-on-press-escape\":!1,\"show-close\":!1},on:{\"update:visible\":function(t){e.dialogVisible=t},closed:function(t){return e.$emit(\"close\")}}},[n(\"x-form\",{ref:\"xForm\",attrs:{config:e.formConfig},model:{value:e.formData,callback:function(t){e.formData=t},expression:\"formData\"}}),n(\"span\",{staticClass:\"dialog-footer\",attrs:{slot:\"footer\"},slot:\"footer\"},[n(\"el-button\",{attrs:{type:\"primary\",size:\"medium\"},on:{click:e.closeDialog}},[e._v(\"确 定\")])],1)],1)],1)},ge=[],he={mixins:[b],data:function(){return{dialogVisible:!0,formData:{signal:[]}}},computed:{formConfig:function(){return{inline:!1,item:[{xType:\"tabs\",tabs:[{label:\"信号定义\",name:\"signal\",column:[{label:\"scope\",name:\"scope\",width:180,rules:[{required:!0,message:\"请选择\",trigger:[\"blur\",\"change\"]}],xType:\"select\",dic:[{label:\"全局\",value:\"start\"},{label:\"流程实例\",value:\"end\"}]},{label:\"id\",name:\"id\",width:200,rules:[{required:!0,message:\"请输入\",trigger:[\"blur\",\"change\"]}],xType:\"input\"},{label:\"名称\",name:\"name\",xType:\"input\",rules:[{required:!0,message:\"请输入\",trigger:[\"blur\",\"change\"]}]}]}]}]}}},mounted:function(){},methods:{updateElement:function(){var e;if(null!==(e=this.formData.signal)&&void 0!==e&&e.length){var t=this.element.businessObject.get(\"extensionElements\");t||(t=this.modeler.get(\"moddle\").create(\"bpmn:signal\"));for(var n=t.get(\"values\").length,r=0;r<n;r++)t.get(\"values\").pop();this.updateProperties({extensionElements:t})}else{var i,a=this.element.businessObject[\"extensionElements\"];if(a)a.values=null===(i=a.values)||void 0===i?void 0:i.filter((function(e){return\"flowable:ExecutionListener\"!==e.$type}))}},closeDialog:function(){var e=this;this.$refs.xForm.validate().then((function(){e.updateElement(),e.dialogVisible=!1})).catch((function(e){return console.error(e)}))}}},be=he,ye=(n(\"c970\"),Object(S[\"a\"])(be,fe,ge,!1,null,null,null)),ve=ye.exports,xe={components:{signalDialog:ve},mixins:[b,se],data:function(){return{signalLength:0,formData:{}}},computed:{formConfig:function(){var e=this;return{inline:!1,item:[{xType:\"select\",name:\"processCategory\",label:\"流程分类\",dic:{data:e.categorys,label:\"dictLabel\",value:\"dictValue\"}},{xType:\"input\",name:\"id\",label:\"流程标识key\",rules:[{required:!0,message:\"Id 不能为空\"}]},{xType:\"input\",name:\"name\",label:\"流程名称\"},{xType:\"input\",name:\"documentation\",label:\"节点描述\"},{xType:\"slot\",name:\"executionListener\",label:\"执行监听器\"},{xType:\"slot\",name:\"signal\",label:\"信号定义\"}]}}},watch:{\"formData.processCategory\":function(e){\"\"===e&&(e=null),this.updateProperties({\"flowable:processCategory\":e})}},created:function(){this.formData=K(this.element)},methods:{computedSignalLength:function(){var e,t;this.signalLength=null!==(e=null===(t=this.element.businessObject.extensionElements)||void 0===t||null===(t=t.values)||void 0===t?void 0:t.length)&&void 0!==e?e:0},finishSignal:function(){\"signalDialog\"===this.dialogName&&this.computedSignalLength(),this.dialogName=\"\"}}},we=xe,_e=Object(S[\"a\"])(we,me,de,!1,null,null,null),Ee=_e.exports,ke=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n(\"div\",[n(\"x-form\",{ref:\"xForm\",attrs:{config:e.formConfig},scopedSlots:e._u([{key:\"executionListener\",fn:function(){return[n(\"el-badge\",{attrs:{value:e.executionListenerLength}},[n(\"el-button\",{attrs:{size:\"small\"},on:{click:function(t){e.dialogName=\"executionListenerDialog\"}}},[e._v(\"编辑\")])],1)]},proxy:!0}]),model:{value:e.formData,callback:function(t){e.formData=t},expression:\"formData\"}}),\"executionListenerDialog\"===e.dialogName?n(\"executionListenerDialog\",{attrs:{element:e.element,modeler:e.modeler},on:{close:e.finishExecutionListener}}):e._e()],1)},Se=[],Te={mixins:[b,se],data:function(){return{formData:{}}},computed:{formConfig:function(){return{inline:!1,item:[{xType:\"input\",name:\"id\",label:\"节点 id\",rules:[{required:!0,message:\"Id 不能为空\"}]},{xType:\"input\",name:\"name\",label:\"节点名称\"},{xType:\"input\",name:\"documentation\",label:\"节点描述\"},{xType:\"slot\",name:\"executionListener\",label:\"执行监听器\"},{xType:\"input\",name:\"conditionExpression\",label:\"跳转条件\"},{xType:\"input\",name:\"skipExpression\",label:\"跳过表达式\"}]}}},watch:{\"formData.conditionExpression\":function(e){if(e){var t=this.modeler.get(\"moddle\").create(\"bpmn:FormalExpression\",{body:e});this.updateProperties({conditionExpression:t})}else this.updateProperties({conditionExpression:null})},\"formData.skipExpression\":function(e){\"\"===e&&(e=null),this.updateProperties({\"flowable:skipExpression\":e})}},created:function(){var e=K(this.element);e=X(e),this.formData=e}},De=Te,Ce=Object(S[\"a\"])(De,ke,Se,!1,null,null,null),Pe=Ce.exports,Le=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n(\"div\",[n(\"x-form\",{ref:\"xForm\",attrs:{config:e.formConfig},scopedSlots:e._u([{key:\"executionListener\",fn:function(){return[n(\"el-badge\",{attrs:{value:e.executionListenerLength}},[n(\"el-button\",{attrs:{size:\"small\"},on:{click:function(t){e.dialogName=\"executionListenerDialog\"}}},[e._v(\"编辑\")])],1)]},proxy:!0}]),model:{value:e.formData,callback:function(t){e.formData=t},expression:\"formData\"}}),\"executionListenerDialog\"===e.dialogName?n(\"executionListenerDialog\",{attrs:{element:e.element,modeler:e.modeler},on:{close:e.finishExecutionListener}}):e._e()],1)},Ae=[],Oe={mixins:[b,se],data:function(){return{formData:{}}},computed:{formConfig:function(){return{inline:!1,item:[{xType:\"input\",name:\"id\",label:\"节点 id\",rules:[{required:!0,message:\"Id 不能为空\"}]},{xType:\"input\",name:\"name\",label:\"节点名称\"},{xType:\"input\",name:\"documentation\",label:\"节点描述\"},{xType:\"slot\",name:\"executionListener\",label:\"执行监听器\"},{xType:\"switch\",name:\"async\",label:\"异步\",activeText:\"是\",inactiveText:\"否\"}]}}},watch:{\"formData.async\":function(e){\"\"===e&&(e=null),this.updateProperties({\"flowable:async\":e})}},created:function(){this.formData=K(this.element)}},Ie=Oe,je=Object(S[\"a\"])(Ie,Le,Ae,!1,null,null,null),Me=je.exports,Fe={name:\"PropertyPanel\",components:{processPanel:Ee,taskPanel:ie,startEndPanel:pe,sequenceFlowPanel:Pe,gatewayPanel:Me},props:{users:{type:Array,required:!0},groups:{type:Array,required:!0},categorys:{type:Array,required:!0},modeler:{type:Object,required:!0}},data:function(){return{element:null,form:{id:\"\",name:\"\",color:null},roles:[{value:\"manager\",label:\"经理\"},{value:\"personnel\",label:\"人事\"},{value:\"charge\",label:\"主管\"}]}},computed:{getComponent:function(){var e,t=null===(e=this.element)||void 0===e?void 0:e.type;return[\"bpmn:IntermediateThrowEvent\",\"bpmn:StartEvent\",\"bpmn:EndEvent\"].includes(t)?\"startEndPanel\":[\"bpmn:UserTask\",\"bpmn:Task\",\"bpmn:SendTask\",\"bpmn:ReceiveTask\",\"bpmn:ManualTask\",\"bpmn:BusinessRuleTask\",\"bpmn:ServiceTask\",\"bpmn:ScriptTask\"].includes(t)?\"taskPanel\":\"bpmn:SequenceFlow\"===t?\"sequenceFlowPanel\":[\"bpmn:InclusiveGateway\",\"bpmn:ExclusiveGateway\",\"bpmn:ParallelGateway\",\"bpmn:EventBasedGateway\"].includes(t)?\"gatewayPanel\":\"bpmn:Process\"===t?\"processPanel\":null},nodeName:function(){if(this.element){var e=this.element.businessObject,t=null!==e&&void 0!==e&&e.eventDefinitions?e.eventDefinitions[0].$type:e.$type;return o[t]||t}return\"\"}},mounted:function(){this.handleModeler()},methods:{handleModeler:function(){var e=this;this.modeler.on(\"root.added\",(function(t){\"bpmn:Process\"===t.element.type&&(e.element=null,e.$nextTick().then((function(){e.element=t.element})))})),this.modeler.on(\"element.click\",(function(t){var n=t.element;console.log(n),\"bpmn:Process\"===n.type&&(e.element=n)})),this.modeler.on(\"selection.changed\",(function(t){e.element=null;var n=t.newSelection[0];n&&e.$nextTick().then((function(){e.element=n}))}))},dataType:function(e){this.$emit(\"dataType\",e)}}},Be=Fe,Ne=(n(\"81bc\"),Object(S[\"a\"])(Be,u,c,!1,null,null,null)),Ve=Ne.exports;function $e(e){return $e=\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&\"function\"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?\"symbol\":typeof e},$e(e)}function Re(e,t){if(!(e instanceof t))throw new TypeError(\"Cannot call a class as a function\")}function Ue(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,\"value\"in r&&(r.writable=!0),Object.defineProperty(e,ze(r.key),r)}}function qe(e,t,n){return t&&Ue(e.prototype,t),n&&Ue(e,n),Object.defineProperty(e,\"prototype\",{writable:!1}),e}function ze(e){var t=Ge(e,\"string\");return\"symbol\"==$e(t)?t:t+\"\"}function Ge(e,t){if(\"object\"!=$e(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||\"default\");if(\"object\"!=$e(r))return r;throw new TypeError(\"@@toPrimitive must return a primitive value.\")}return(\"string\"===t?String:Number)(e)}var Ke=function(){function e(){Re(this,e),this.controls=[],this.init()}return qe(e,[{key:\"init\",value:function(){this.controls=[{action:\"hand-tool\",title:\"抓手\"},{action:\"lasso-tool\",title:\"套手\"},{action:\"create.start-event\",title:\"开始\"},{action:\"create.intermediate-event\",title:\"中间\"},{action:\"create.end-event\",title:\"结束\"},{action:\"create.exclusive-gateway\",title:\"互斥网关\"},{action:\"create.parallel-gateway\",title:\"合并网关\"},{action:\"create.inclusive-gateway\",title:\"融合网关\"},{action:\"create.task\",title:\"任务\"},{action:\"create.user-task\",title:\"用户任务\"},{action:\"create.user-sign-task\",title:\"会签任务\"},{action:\"create.subprocess-expanded\",title:\"子流程\"},{action:\"create.data-object\",title:\"数据对象\"},{action:\"create.data-store\",title:\"数据存储\"},{action:\"create.participant-expanded\",title:\"扩展流程\"},{action:\"create.group\",title:\"分组\"}]}},{key:\"getControl\",value:function(e){var t=this.controls.filter((function(t){return t.action===e}));return t[0]||{}}}])}();function We(){return Math.random().toString(36).slice(-8)}var Je=function(){return'<?xml version=\"1.0\" encoding=\"UTF-8\"?>\\n    <definitions xmlns=\"http://www.omg.org/spec/BPMN/20100524/MODEL\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:bpmndi=\"http://www.omg.org/spec/BPMN/20100524/DI\" xmlns:omgdc=\"http://www.omg.org/spec/DD/20100524/DC\" xmlns:bioc=\"http://bpmn.io/schema/bpmn/biocolor/1.0\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:flowable=\"http://flowable.org/bpmn\" targetNamespace=\"http://www.flowable.org/processdef\">\\n      <process id=\"process_'.concat(We(),'\" name=\"name_').concat(We(),'\">\\n        <startEvent id=\"startNode1\" name=\"开始\" />\\n      </process>\\n      <bpmndi:BPMNDiagram id=\"BPMNDiagram_flow\">\\n        <bpmndi:BPMNPlane id=\"BPMNPlane_flow\" bpmnElement=\"T-2d89e7a3-ba79-4abd-9f64-ea59621c258c\">\\n          <bpmndi:BPMNShape id=\"BPMNShape_startNode1\" bpmnElement=\"startNode1\" bioc:stroke=\"\">\\n            <omgdc:Bounds x=\"240\" y=\"200\" width=\"30\" height=\"30\" />\\n            <bpmndi:BPMNLabel>\\n              <omgdc:Bounds x=\"242\" y=\"237\" width=\"23\" height=\"14\" />\\n            </bpmndi:BPMNLabel>\\n          </bpmndi:BPMNShape>\\n        </bpmndi:BPMNPlane>\\n      </bpmndi:BPMNDiagram>\\n    </definitions>\\n    ')};function Xe(e){return Xe=\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&\"function\"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?\"symbol\":typeof e},Xe(e)}function He(e,t){if(!(e instanceof t))throw new TypeError(\"Cannot call a class as a function\")}function Ye(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,\"value\"in r&&(r.writable=!0),Object.defineProperty(e,Ze(r.key),r)}}function Qe(e,t,n){return t&&Ye(e.prototype,t),n&&Ye(e,n),Object.defineProperty(e,\"prototype\",{writable:!1}),e}function Ze(e){var t=et(e,\"string\");return\"symbol\"==Xe(t)?t:t+\"\"}function et(e,t){if(\"object\"!=Xe(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||\"default\");if(\"object\"!=Xe(r))return r;throw new TypeError(\"@@toPrimitive must return a primitive value.\")}return(\"string\"===t?String:Number)(e)}var tt=25,nt=function(){function e(t,n,r,i,a,o){He(this,e),this.create=r,this.elementFactory=i,this.bpmnFactory=i._bpmnFactory,this.translate=o,!1!==t.autoPlace&&(this.autoPlace=a.get(\"autoPlace\",!1)),n.registerProvider(this)}return Qe(e,[{key:\"getContextPadEntries\",value:function(e){if(\"bpmn:EndEvent\"!=e.type){var t=this.autoPlace,n=this.bpmnFactory,r=this.create,i=this.elementFactory,a=this.translate;return{\"append.user-task\":{group:\"model\",className:\"bpmn-icon-user-task\",title:a(\"User Task\"),action:{click:o(tt),dragstart:s(tt)}}}}function o(e){return function(r,a){if(t){var o=n.create(\"bpmn:UserTask\");o.suitable=e;var l=i.createShape({type:\"bpmn:UserTask\",businessObject:o});t.append(a,l)}else s(r,a)}}function s(t){return function(a){var o=n.create(\"bpmn:UserTask\");o.suitable=t;var s=i.createShape({type:\"bpmn:UserTask\",businessObject:o});is(o,\"bpmn:EndEvent\")||r.start(a,s,e)}}}}])}();nt.$inject=[\"config\",\"contextPad\",\"create\",\"elementFactory\",\"injector\",\"translate\"];var rt={__init__:[\"customContextPad\"],customContextPad:[\"type\",nt]},it=n(\"2730\");function at(e){return at=\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&\"function\"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?\"symbol\":typeof e},at(e)}function ot(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ot=function(){return t};var e,t={},n=Object.prototype,r=n.hasOwnProperty,i=Object.defineProperty||function(e,t,n){e[t]=n.value},a=\"function\"==typeof Symbol?Symbol:{},o=a.iterator||\"@@iterator\",s=a.asyncIterator||\"@@asyncIterator\",l=a.toStringTag||\"@@toStringTag\";function u(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{u({},\"\")}catch(e){u=function(e,t,n){return e[t]=n}}function c(e,t,n,r){var a=t&&t.prototype instanceof b?t:b,o=Object.create(a.prototype),s=new L(r||[]);return i(o,\"_invoke\",{value:T(e,n,s)}),o}function p(e,t,n){try{return{type:\"normal\",arg:e.call(t,n)}}catch(e){return{type:\"throw\",arg:e}}}t.wrap=c;var m=\"suspendedStart\",d=\"suspendedYield\",f=\"executing\",g=\"completed\",h={};function b(){}function y(){}function v(){}var x={};u(x,o,(function(){return this}));var w=Object.getPrototypeOf,_=w&&w(w(A([])));_&&_!==n&&r.call(_,o)&&(x=_);var E=v.prototype=b.prototype=Object.create(x);function k(e){[\"next\",\"throw\",\"return\"].forEach((function(t){u(e,t,(function(e){return this._invoke(t,e)}))}))}function S(e,t){function n(i,a,o,s){var l=p(e[i],e,a);if(\"throw\"!==l.type){var u=l.arg,c=u.value;return c&&\"object\"==at(c)&&r.call(c,\"__await\")?t.resolve(c.__await).then((function(e){n(\"next\",e,o,s)}),(function(e){n(\"throw\",e,o,s)})):t.resolve(c).then((function(e){u.value=e,o(u)}),(function(e){return n(\"throw\",e,o,s)}))}s(l.arg)}var a;i(this,\"_invoke\",{value:function(e,r){function i(){return new t((function(t,i){n(e,r,t,i)}))}return a=a?a.then(i,i):i()}})}function T(t,n,r){var i=m;return function(a,o){if(i===f)throw Error(\"Generator is already running\");if(i===g){if(\"throw\"===a)throw o;return{value:e,done:!0}}for(r.method=a,r.arg=o;;){var s=r.delegate;if(s){var l=D(s,r);if(l){if(l===h)continue;return l}}if(\"next\"===r.method)r.sent=r._sent=r.arg;else if(\"throw\"===r.method){if(i===m)throw i=g,r.arg;r.dispatchException(r.arg)}else\"return\"===r.method&&r.abrupt(\"return\",r.arg);i=f;var u=p(t,n,r);if(\"normal\"===u.type){if(i=r.done?g:d,u.arg===h)continue;return{value:u.arg,done:r.done}}\"throw\"===u.type&&(i=g,r.method=\"throw\",r.arg=u.arg)}}}function D(t,n){var r=n.method,i=t.iterator[r];if(i===e)return n.delegate=null,\"throw\"===r&&t.iterator.return&&(n.method=\"return\",n.arg=e,D(t,n),\"throw\"===n.method)||\"return\"!==r&&(n.method=\"throw\",n.arg=new TypeError(\"The iterator does not provide a '\"+r+\"' method\")),h;var a=p(i,t.iterator,n.arg);if(\"throw\"===a.type)return n.method=\"throw\",n.arg=a.arg,n.delegate=null,h;var o=a.arg;return o?o.done?(n[t.resultName]=o.value,n.next=t.nextLoc,\"return\"!==n.method&&(n.method=\"next\",n.arg=e),n.delegate=null,h):o:(n.method=\"throw\",n.arg=new TypeError(\"iterator result is not an object\"),n.delegate=null,h)}function C(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function P(e){var t=e.completion||{};t.type=\"normal\",delete t.arg,e.completion=t}function L(e){this.tryEntries=[{tryLoc:\"root\"}],e.forEach(C,this),this.reset(!0)}function A(t){if(t||\"\"===t){var n=t[o];if(n)return n.call(t);if(\"function\"==typeof t.next)return t;if(!isNaN(t.length)){var i=-1,a=function n(){for(;++i<t.length;)if(r.call(t,i))return n.value=t[i],n.done=!1,n;return n.value=e,n.done=!0,n};return a.next=a}}throw new TypeError(at(t)+\" is not iterable\")}return y.prototype=v,i(E,\"constructor\",{value:v,configurable:!0}),i(v,\"constructor\",{value:y,configurable:!0}),y.displayName=u(v,l,\"GeneratorFunction\"),t.isGeneratorFunction=function(e){var t=\"function\"==typeof e&&e.constructor;return!!t&&(t===y||\"GeneratorFunction\"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,v):(e.__proto__=v,u(e,l,\"GeneratorFunction\")),e.prototype=Object.create(E),e},t.awrap=function(e){return{__await:e}},k(S.prototype),u(S.prototype,s,(function(){return this})),t.AsyncIterator=S,t.async=function(e,n,r,i,a){void 0===a&&(a=Promise);var o=new S(c(e,n,r,i),a);return t.isGeneratorFunction(n)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},k(E),u(E,l,\"Generator\"),u(E,o,(function(){return this})),u(E,\"toString\",(function(){return\"[object Generator]\"})),t.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},t.values=A,L.prototype={constructor:L,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method=\"next\",this.arg=e,this.tryEntries.forEach(P),!t)for(var n in this)\"t\"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if(\"throw\"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var n=this;function i(r,i){return s.type=\"throw\",s.arg=t,n.next=r,i&&(n.method=\"next\",n.arg=e),!!i}for(var a=this.tryEntries.length-1;a>=0;--a){var o=this.tryEntries[a],s=o.completion;if(\"root\"===o.tryLoc)return i(\"end\");if(o.tryLoc<=this.prev){var l=r.call(o,\"catchLoc\"),u=r.call(o,\"finallyLoc\");if(l&&u){if(this.prev<o.catchLoc)return i(o.catchLoc,!0);if(this.prev<o.finallyLoc)return i(o.finallyLoc)}else if(l){if(this.prev<o.catchLoc)return i(o.catchLoc,!0)}else{if(!u)throw Error(\"try statement without catch or finally\");if(this.prev<o.finallyLoc)return i(o.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var i=this.tryEntries[n];if(i.tryLoc<=this.prev&&r.call(i,\"finallyLoc\")&&this.prev<i.finallyLoc){var a=i;break}}a&&(\"break\"===e||\"continue\"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var o=a?a.completion:{};return o.type=e,o.arg=t,a?(this.method=\"next\",this.next=a.finallyLoc,h):this.complete(o)},complete:function(e,t){if(\"throw\"===e.type)throw e.arg;return\"break\"===e.type||\"continue\"===e.type?this.next=e.arg:\"return\"===e.type?(this.rval=this.arg=e.arg,this.method=\"return\",this.next=\"end\"):\"normal\"===e.type&&t&&(this.next=t),h},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),P(n),h}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if(\"throw\"===r.type){var i=r.arg;P(n)}return i}}throw Error(\"illegal catch attempt\")},delegateYield:function(t,n,r){return this.delegate={iterator:A(t),resultName:n,nextLoc:r},\"next\"===this.method&&(this.arg=e),h}},t}function st(e,t,n,r,i,a,o){try{var s=e[a](o),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,i)}function lt(e){return function(){var t=this,n=arguments;return new Promise((function(r,i){var a=e.apply(t,n);function o(e){st(a,r,i,o,s,\"next\",e)}function s(e){st(a,r,i,o,s,\"throw\",e)}o(void 0)}))}}var ut={name:\"WorkflowBpmnModeler\",components:{panel:Ve},props:{xml:{type:String,default:\"\"},users:{type:Array,default:function(){return[]}},groups:{type:Array,default:function(){return[]}},categorys:{type:Array,default:function(){return[]}},isView:{type:Boolean,default:!1},taskList:{type:Array,default:function(){return[]}}},data:function(){return{modeler:null,zoom:1}},watch:{xml:function(e){e&&this.createNewDiagram(e)}},mounted:function(){this.modeler=new l[\"a\"]({container:this.$refs.canvas,additionalModules:[{translate:[\"value\",s]},rt],moddleExtensions:{flowable:it}}),this.xml?this.createNewDiagram(this.xml):this.newDiagram()},methods:{newDiagram:function(){this.createNewDiagram(Je())},fitViewport:function(){this.zoom=this.modeler.get(\"canvas\").zoom(\"fit-viewport\");var e=document.querySelector(\".flow-containers .viewport\").getBBox(),t=this.modeler.get(\"canvas\").viewbox(),n={x:e.x+e.width/2-65,y:e.y+e.height/2};this.modeler.get(\"canvas\").viewbox({x:n.x-t.width/2,y:n.y-t.height/2,width:t.width,height:t.height}),this.zoom=e.width/t.width*1.8},zoomViewport:function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];this.zoom=this.modeler.get(\"canvas\").zoom(),this.zoom+=e?.1:-.1,this.modeler.get(\"canvas\").zoom(this.zoom)},createNewDiagram:function(e){var t=this;return lt(ot().mark((function n(){return ot().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return e=e.replace(/<!\\[CDATA\\[(.+?)]]>/g,(function(e,t){return t.replace(/</g,\"&lt;\")})),n.prev=1,n.next=4,t.modeler.importXML(e);case 4:t.adjustPalette(),t.fitViewport(),void 0!==t.taskList&&t.taskList.length>0&&t.fillColor(),n.next=12;break;case 9:n.prev=9,n.t0=n[\"catch\"](1),console.error(n.t0.message,n.t0.warnings);case 12:case\"end\":return n.stop()}}),n,null,[[1,9]])})))()},adjustPalette:function(){try{var e=this.$refs.canvas,t=e.children[0].children[1].children[4],n={width:\"130px\",padding:\"5px\",background:\"white\",left:\"20px\",borderRadius:0};for(var r in n)t.style[r]=n[r];var i=t.children[0],a=i.children;for(var o in a){var s=a[o];for(var l in s.children){var u=s.children[l],c={display:\"flex\",justifyContent:\"flex-start\",alignItems:\"center\",width:\"100%\",padding:\"5px\"};if(u.className&&u.dataset&&-1!==u.className.indexOf(\"entry\")){var p=(new Ke).getControl(u.dataset.action);for(var m in u.innerHTML=\"<div style='font-size: 14px;font-weight:500;margin-left:15px;'>\".concat(p[\"title\"],\"</div>\"),c)u.style[m]=c[m]}}}}catch(d){console.log(d)}},fillColor:function(){var e=this,t=this.modeler.get(\"canvas\");this.modeler._definitions.rootElements[0].flowElements.forEach((function(n){var r,i=e.taskList.find((function(e){return e.key===n.id})),a=e.taskList.find((function(e){return!e.completed})),o=e.taskList[e.taskList.length-1];if(\"bpmn:UserTask\"===n.$type)i&&(t.addMarker(n.id,i.completed?\"highlight\":\"highlight-todo\"),null===(r=n.outgoing)||void 0===r||r.forEach((function(n){var r=e.taskList.find((function(e){return e.key===n.targetRef.id}));r&&(a&&i.key===a.key&&!a.completed?(t.addMarker(n.id,a.completed?\"highlight\":\"highlight-todo\"),t.addMarker(n.targetRef.id,a.completed?\"highlight\":\"highlight-todo\")):(t.addMarker(n.id,r.completed?\"highlight\":\"highlight-todo\"),t.addMarker(n.targetRef.id,r.completed?\"highlight\":\"highlight-todo\")))})));else if(\"bpmn:ExclusiveGateway\"===n.$type){var s;if(i)t.addMarker(n.id,i.completed?\"highlight\":\"highlight-todo\"),null===(s=n.outgoing)||void 0===s||s.forEach((function(n){var r=e.taskList.find((function(e){return e.key===n.targetRef.id}));r&&(t.addMarker(n.id,r.completed?\"highlight\":\"highlight-todo\"),t.addMarker(n.targetRef.id,r.completed?\"highlight\":\"highlight-todo\"))}))}else if(\"bpmn:ParallelGateway\"===n.$type){var l;if(i)t.addMarker(n.id,i.completed?\"highlight\":\"highlight-todo\"),null===(l=n.outgoing)||void 0===l||l.forEach((function(n){var r=e.taskList.find((function(e){return e.key===n.targetRef.id}));r&&(t.addMarker(n.id,r.completed?\"highlight\":\"highlight-todo\"),t.addMarker(n.targetRef.id,r.completed?\"highlight\":\"highlight-todo\"))}))}else if(\"bpmn:StartEvent\"===n.$type)n.outgoing.forEach((function(r){var i=e.taskList.find((function(e){return e.key===r.targetRef.id}));if(i)return t.addMarker(r.id,\"highlight\"),void t.addMarker(n.id,\"highlight\")}));else if(\"bpmn:EndEvent\"===n.$type&&o.key===n.id&&o.completed)return void t.addMarker(n.id,\"highlight\")}))},getProcess:function(){var e=this.getProcessElement();return{id:e.id,name:e.name,category:e.$attrs[\"flowable:processCategory\"]}},getProcessElement:function(){for(var e=this.modeler.getDefinitions().rootElements,t=0;t<e.length;t++)if(\"bpmn:Process\"===e[t].$type)return e[t]},saveXML:function(){var e=arguments,t=this;return lt(ot().mark((function n(){var r,i,a;return ot().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return r=e.length>0&&void 0!==e[0]&&e[0],n.prev=1,n.next=4,t.modeler.saveXML({format:!0});case 4:return i=n.sent,a=i.xml,r&&t.downloadFile(\"\".concat(t.getProcessElement().name,\".bpmn20.xml\"),a,\"application/xml\"),n.abrupt(\"return\",a);case 10:n.prev=10,n.t0=n[\"catch\"](1),console.log(n.t0);case 13:case\"end\":return n.stop()}}),n,null,[[1,10]])})))()},showXML:function(){var e=this;return lt(ot().mark((function t(){var n,r;return ot().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,e.modeler.saveXML({format:!0});case 3:n=t.sent,r=n.xml,e.$emit(\"showXML\",r),t.next=11;break;case 8:t.prev=8,t.t0=t[\"catch\"](0),console.log(t.t0);case 11:case\"end\":return t.stop()}}),t,null,[[0,8]])})))()},saveImg:function(){var e=arguments,t=this;return lt(ot().mark((function n(){var r,i,a;return ot().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return e.length>0&&void 0!==e[0]?e[0]:\"svg\",r=e.length>1&&void 0!==e[1]&&e[1],n.prev=2,n.next=5,t.modeler.saveSVG({format:!0});case 5:return i=n.sent,a=i.svg,r&&t.downloadFile(t.getProcessElement().name,a,\"image/svg+xml\"),n.abrupt(\"return\",a);case 11:n.prev=11,n.t0=n[\"catch\"](2),console.log(n.t0);case 14:case\"end\":return n.stop()}}),n,null,[[2,11]])})))()},save:function(){var e=this;return lt(ot().mark((function t(){var n,r,i,a;return ot().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return n=e.getProcess(),t.next=3,e.saveXML();case 3:return r=t.sent,t.next=6,e.saveImg();case 6:i=t.sent,a={process:n,xml:r,svg:i},e.$emit(\"save\",a),window.parent.postMessage(a,\"*\");case 10:case\"end\":return t.stop()}}),t)})))()},openBpmn:function(e){var t=this,n=new FileReader;return n.readAsText(e,\"utf-8\"),n.onload=function(){t.createNewDiagram(n.result)},!1},downloadFile:function(e,t,n){var r=document.createElement(\"a\"),i=window.URL.createObjectURL(new Blob([t],{type:n}));r.href=i,r.download=e,r.click(),window.URL.revokeObjectURL(i)},dataType:function(e){this.$emit(\"dataType\",e)}}},ct=ut,pt=(n(\"d062\"),Object(S[\"a\"])(ct,r,i,!1,null,null,null)),mt=pt.exports;mt.install=function(e){return e.component(mt.name,mt)};t[\"a\"]=mt},b8b8:function(e,t,n){},c0fc:function(e,t,n){\"use strict\";n(\"b8b8\")},c970:function(e,t,n){\"use strict\";n(\"449f\")},d062:function(e,t,n){\"use strict\";n(\"deb3\")},d314:function(e,t,n){\"use strict\";n(\"f815\")},dac7:function(e,t,n){},deb3:function(e,t,n){},f815:function(e,t,n){}}]);", "extractedComments": []}