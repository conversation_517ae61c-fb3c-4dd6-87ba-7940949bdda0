{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/tool/build/TreeNodeDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/tool/build/TreeNodeDialog.vue", "mtime": 1641277326000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["TreeNodeDialog.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "TreeNodeDialog.vue", "sourceRoot": "src/views/tool/build", "sourcesContent": ["<template>\n  <div>\n    <el-dialog\n      v-bind=\"$attrs\"\n      :close-on-click-modal=\"false\"\n      :modal-append-to-body=\"false\"\n      v-on=\"$listeners\"\n      @open=\"onOpen\"\n      @close=\"onClose\"\n    >\n      <el-row :gutter=\"0\">\n        <el-form\n          ref=\"elForm\"\n          :model=\"formData\"\n          :rules=\"rules\"\n          size=\"small\"\n          label-width=\"100px\"\n        >\n          <el-col :span=\"24\">\n            <el-form-item\n              label=\"选项名\"\n              prop=\"label\"\n            >\n              <el-input\n                v-model=\"formData.label\"\n                placeholder=\"请输入选项名\"\n                clearable\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item\n              label=\"选项值\"\n              prop=\"value\"\n            >\n              <el-input\n                v-model=\"formData.value\"\n                placeholder=\"请输入选项值\"\n                clearable\n              >\n                <el-select\n                  slot=\"append\"\n                  v-model=\"dataType\"\n                  :style=\"{width: '100px'}\"\n                >\n                  <el-option\n                    v-for=\"(item, index) in dataTypeOptions\"\n                    :key=\"index\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                    :disabled=\"item.disabled\"\n                  />\n                </el-select>\n              </el-input>\n            </el-form-item>\n          </el-col>\n        </el-form>\n      </el-row>\n      <div slot=\"footer\">\n        <el-button\n          type=\"primary\"\n          @click=\"handelConfirm\"\n        >\n          确定\n        </el-button>\n        <el-button @click=\"close\">\n          取消\n        </el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n<script>\nimport { isNumberStr } from '@/utils/index'\nimport { getTreeNodeId, saveTreeNodeId } from '@/utils/db'\n\nconst id = getTreeNodeId()\n\nexport default {\n  components: {},\n  inheritAttrs: false,\n  props: [],\n  data() {\n    return {\n      id,\n      formData: {\n        label: undefined,\n        value: undefined\n      },\n      rules: {\n        label: [\n          {\n            required: true,\n            message: '请输入选项名',\n            trigger: 'blur'\n          }\n        ],\n        value: [\n          {\n            required: true,\n            message: '请输入选项值',\n            trigger: 'blur'\n          }\n        ]\n      },\n      dataType: 'string',\n      dataTypeOptions: [\n        {\n          label: '字符串',\n          value: 'string'\n        },\n        {\n          label: '数字',\n          value: 'number'\n        }\n      ]\n    }\n  },\n  computed: {},\n  watch: {\n    // eslint-disable-next-line func-names\n    'formData.value': function (val) {\n      this.dataType = isNumberStr(val) ? 'number' : 'string'\n    },\n    id(val) {\n      saveTreeNodeId(val)\n    }\n  },\n  created() {},\n  mounted() {},\n  methods: {\n    onOpen() {\n      this.formData = {\n        label: undefined,\n        value: undefined\n      }\n    },\n    onClose() {},\n    close() {\n      this.$emit('update:visible', false)\n    },\n    handelConfirm() {\n      this.$refs.elForm.validate(valid => {\n        if (!valid) return\n        if (this.dataType === 'number') {\n          this.formData.value = parseFloat(this.formData.value)\n        }\n        this.formData.id = this.id++\n        this.$emit('commit', this.formData)\n        this.close()\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n</style>\n"]}]}