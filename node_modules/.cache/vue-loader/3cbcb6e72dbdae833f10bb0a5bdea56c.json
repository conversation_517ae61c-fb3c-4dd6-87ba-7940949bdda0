{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/components/IconSelect/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/components/IconSelect/index.vue", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBpY29ucyBmcm9tICcuL3JlcXVpcmVJY29ucycKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdJY29uU2VsZWN0JywKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgbmFtZTogJycsCiAgICAgIGljb25MaXN0OiBpY29ucwogICAgfQogIH0sCiAgbWV0aG9kczogewogICAgZmlsdGVySWNvbnMoKSB7CiAgICAgIHRoaXMuaWNvbkxpc3QgPSBpY29ucwogICAgICBpZiAodGhpcy5uYW1lKSB7CiAgICAgICAgdGhpcy5pY29uTGlzdCA9IHRoaXMuaWNvbkxpc3QuZmlsdGVyKGl0ZW0gPT4gaXRlbS5pbmNsdWRlcyh0aGlzLm5hbWUpKQogICAgICB9CiAgICB9LAogICAgc2VsZWN0ZWRJY29uKG5hbWUpIHsKICAgICAgdGhpcy4kZW1pdCgnc2VsZWN0ZWQnLCBuYW1lKQogICAgICBkb2N1bWVudC5ib2R5LmNsaWNrKCkKICAgIH0sCiAgICByZXNldCgpIHsKICAgICAgdGhpcy5uYW1lID0gJycKICAgICAgdGhpcy5pY29uTGlzdCA9IGljb25zCiAgICB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAgBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/IconSelect", "sourcesContent": ["<!-- <AUTHOR> -->\n<template>\n  <div class=\"icon-body\">\n    <el-input v-model=\"name\" style=\"position: relative;\" clearable placeholder=\"请输入图标名称\" @clear=\"filterIcons\" @input.native=\"filterIcons\">\n      <i slot=\"suffix\" class=\"el-icon-search el-input__icon\" />\n    </el-input>\n    <div class=\"icon-list\">\n      <div v-for=\"(item, index) in iconList\" :key=\"index\" @click=\"selectedIcon(item)\">\n        <svg-icon :icon-class=\"item\" style=\"height: 30px;width: 16px;\" />\n        <span>{{ item }}</span>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport icons from './requireIcons'\nexport default {\n  name: 'IconSelect',\n  data() {\n    return {\n      name: '',\n      iconList: icons\n    }\n  },\n  methods: {\n    filterIcons() {\n      this.iconList = icons\n      if (this.name) {\n        this.iconList = this.iconList.filter(item => item.includes(this.name))\n      }\n    },\n    selectedIcon(name) {\n      this.$emit('selected', name)\n      document.body.click()\n    },\n    reset() {\n      this.name = ''\n      this.iconList = icons\n    }\n  }\n}\n</script>\n\n<style rel=\"stylesheet/scss\" lang=\"scss\" scoped>\n  .icon-body {\n    width: 100%;\n    padding: 10px;\n    .icon-list {\n      height: 200px;\n      overflow-y: scroll;\n      div {\n        height: 30px;\n        line-height: 30px;\n        margin-bottom: -5px;\n        cursor: pointer;\n        width: 33%;\n        float: left;\n      }\n      span {\n        display: inline-block;\n        vertical-align: -0.15em;\n        fill: currentColor;\n        overflow: hidden;\n      }\n    }\n  }\n</style>\n"]}]}