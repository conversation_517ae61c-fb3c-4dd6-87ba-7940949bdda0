{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Process/components/nodePanel/property/listenerParam.vue?vue&type=style&index=0&id=57fef0c5&lang=css", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Process/components/nodePanel/property/listenerParam.vue", "mtime": 1641277326000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/css-loader/dist/cjs.js", "mtime": 1751171659443}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/loaders/stylePostLoader.js", "mtime": 1751171659924}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/postcss-loader/src/index.js", "mtime": 1751171659554}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgouZmxvdy1jb250YWluZXJzICAuZWwtYmFkZ2VfX2NvbnRlbnQuaXMtZml4ZWQgewogICAgdG9wOiAxOHB4Owp9Cg=="}, {"version": 3, "sources": ["listenerParam.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4FA;AACA;AACA", "file": "listenerParam.vue", "sourceRoot": "src/components/Process/components/nodePanel/property", "sourcesContent": ["<template>\n  <div>\n    <el-dialog\n      title=\"监听器参数\"\n      :visible.sync=\"dialogVisible\"\n      width=\"700px\"\n      :close-on-click-modal=\"false\"\n      :close-on-press-escape=\"false\"\n      :show-close=\"false\"\n      @closed=\"$emit('close', formData.paramList)\"\n    >\n      <x-form ref=\"xForm\" v-model=\"formData\" :config=\"formConfig\" />\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" size=\"medium\" @click=\"closeDialog\">确 定</el-button>\n      </span>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport mixinXcrud from '../../../common/mixinXcrud'\nexport default {\n  mixins: [mixinXcrud],\n  props: {\n    value: {\n      type: Array,\n      default: () => []\n    }\n  },\n  data() {\n    return {\n      dialogVisible: true,\n      formData: {\n        paramList: this.value\n      }\n    }\n  },\n  computed: {\n    formConfig() {\n      return {\n        inline: false,\n        item: [\n          {\n            xType: 'tabs',\n            tabs: [\n              {\n                label: '监听器参数',\n                name: 'paramList',\n                column: [\n                  {\n                    label: '类型',\n                    name: 'type',\n                    width: 180,\n                    rules: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],\n                    xType: 'select',\n                    dic: [\n                      { label: '字符串', value: 'stringValue' },\n                      { label: '表达式', value: 'expression' }\n                    ]\n                  },\n                  {\n                    label: '名称',\n                    name: 'name',\n                    width: 180,\n                    rules: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],\n                    xType: 'input'\n                  },\n                  {\n                    label: '值',\n                    name: 'value',\n                    xType: 'input',\n                    rules: [{ required: true, message: '请输入', trigger: ['blur', 'change'] }]\n                  }\n                ]\n              }\n            ]\n          }\n        ]\n      }\n    }\n  },\n  methods: {\n    closeDialog() {\n      this.$refs.xForm.validate().then(() => {\n        this.dialogVisible = false\n      }).catch(e => console.error(e))\n    }\n  }\n}\n</script>\n\n<style>\n.flow-containers  .el-badge__content.is-fixed {\n    top: 18px;\n}\n</style>\n"]}]}