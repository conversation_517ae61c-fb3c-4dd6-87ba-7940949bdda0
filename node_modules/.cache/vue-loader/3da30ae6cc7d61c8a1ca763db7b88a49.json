{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Breadcrumb/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Breadcrumb/index.vue", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmV4cG9ydCBkZWZhdWx0IHsKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgbGV2ZWxMaXN0OiBudWxsCiAgICB9CiAgfSwKICB3YXRjaDogewogICAgJHJvdXRlKHJvdXRlKSB7CiAgICAgIC8vIGlmIHlvdSBnbyB0byB0aGUgcmVkaXJlY3QgcGFnZSwgZG8gbm90IHVwZGF0ZSB0aGUgYnJlYWRjcnVtYnMKICAgICAgaWYgKHJvdXRlLnBhdGguc3RhcnRzV2l0aCgnL3JlZGlyZWN0LycpKSB7CiAgICAgICAgcmV0dXJuCiAgICAgIH0KICAgICAgdGhpcy5nZXRCcmVhZGNydW1iKCkKICAgIH0KICB9LAogIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmdldEJyZWFkY3J1bWIoKQogIH0sCiAgbWV0aG9kczogewogICAgZ2V0QnJlYWRjcnVtYigpIHsKICAgICAgLy8gb25seSBzaG93IHJvdXRlcyB3aXRoIG1ldGEudGl0bGUKICAgICAgbGV0IG1hdGNoZWQgPSB0aGlzLiRyb3V0ZS5tYXRjaGVkLmZpbHRlcihpdGVtID0+IGl0ZW0ubWV0YSAmJiBpdGVtLm1ldGEudGl0bGUpCiAgICAgIGNvbnN0IGZpcnN0ID0gbWF0Y2hlZFswXQoKICAgICAgaWYgKCF0aGlzLmlzRGFzaGJvYXJkKGZpcnN0KSkgewogICAgICAgIG1hdGNoZWQgPSBbeyBwYXRoOiAnL2luZGV4JywgbWV0YTogeyB0aXRsZTogJ+mmlumhtScgfX1dLmNvbmNhdChtYXRjaGVkKQogICAgICB9CgogICAgICB0aGlzLmxldmVsTGlzdCA9IG1hdGNoZWQuZmlsdGVyKGl0ZW0gPT4gaXRlbS5tZXRhICYmIGl0ZW0ubWV0YS50aXRsZSAmJiBpdGVtLm1ldGEuYnJlYWRjcnVtYiAhPT0gZmFsc2UpCiAgICB9LAogICAgaXNEYXNoYm9hcmQocm91dGUpIHsKICAgICAgY29uc3QgbmFtZSA9IHJvdXRlICYmIHJvdXRlLm5hbWUKICAgICAgaWYgKCFuYW1lKSB7CiAgICAgICAgcmV0dXJuIGZhbHNlCiAgICAgIH0KICAgICAgcmV0dXJuIG5hbWUudHJpbSgpID09PSAn6aaW6aG1JwogICAgfSwKICAgIGhhbmRsZUxpbmsoaXRlbSkgewogICAgICBjb25zdCB7IHJlZGlyZWN0LCBwYXRoIH0gPSBpdGVtCiAgICAgIGlmIChyZWRpcmVjdCkgewogICAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHJlZGlyZWN0KQogICAgICAgIHJldHVybgogICAgICB9CiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHBhdGgpCiAgICB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;AAYA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/Breadcrumb", "sourcesContent": ["<template>\n  <el-breadcrumb class=\"app-breadcrumb\" separator=\"/\">\n    <transition-group name=\"breadcrumb\">\n      <el-breadcrumb-item v-for=\"(item,index) in levelList\" :key=\"item.path\">\n        <span v-if=\"item.redirect==='noRedirect'||index==levelList.length-1\" class=\"no-redirect\">{{ item.meta.title }}</span>\n        <a v-else @click.prevent=\"handleLink(item)\">{{ item.meta.title }}</a>\n      </el-breadcrumb-item>\n    </transition-group>\n  </el-breadcrumb>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      levelList: null\n    }\n  },\n  watch: {\n    $route(route) {\n      // if you go to the redirect page, do not update the breadcrumbs\n      if (route.path.startsWith('/redirect/')) {\n        return\n      }\n      this.getBreadcrumb()\n    }\n  },\n  created() {\n    this.getBreadcrumb()\n  },\n  methods: {\n    getBreadcrumb() {\n      // only show routes with meta.title\n      let matched = this.$route.matched.filter(item => item.meta && item.meta.title)\n      const first = matched[0]\n\n      if (!this.isDashboard(first)) {\n        matched = [{ path: '/index', meta: { title: '首页' }}].concat(matched)\n      }\n\n      this.levelList = matched.filter(item => item.meta && item.meta.title && item.meta.breadcrumb !== false)\n    },\n    isDashboard(route) {\n      const name = route && route.name\n      if (!name) {\n        return false\n      }\n      return name.trim() === '首页'\n    },\n    handleLink(item) {\n      const { redirect, path } = item\n      if (redirect) {\n        this.$router.push(redirect)\n        return\n      }\n      this.$router.push(path)\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.app-breadcrumb.el-breadcrumb {\n  display: inline-block;\n  font-size: 14px;\n  line-height: 50px;\n  margin-left: 8px;\n\n  .no-redirect {\n    color: #97a8be;\n    cursor: text;\n  }\n}\n</style>\n"]}]}