{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/tool/build/DraggableItem.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/tool/build/DraggableItem.vue", "mtime": 1641277326000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}