{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/components/ImageUpload/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/components/ImageUpload/index.vue", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldFRva2VuIH0gZnJvbSAiQC91dGlscy9hdXRoIjsKCmV4cG9ydCBkZWZhdWx0IHsKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgZGlhbG9nVmlzaWJsZTogZmFsc2UsCiAgICAgIHVwbG9hZEltZ1VybDogcHJvY2Vzcy5lbnYuVlVFX0FQUF9CQVNFX0FQSSArICIvY29tbW9uL3VwbG9hZCIsIC8vIOS4iuS8oOeahOWbvueJh+acjeWKoeWZqOWcsOWdgAogICAgICBoZWFkZXJzOiB7CiAgICAgICAgQXV0aG9yaXphdGlvbjogIkJlYXJlciAiICsgZ2V0VG9rZW4oKSwKICAgICAgfSwKICAgIH07CiAgfSwKICBwcm9wczogewogICAgdmFsdWU6IHsKICAgICAgdHlwZTogU3RyaW5nLAogICAgICBkZWZhdWx0OiAiIiwKICAgIH0sCiAgfSwKICBtZXRob2RzOiB7CiAgICByZW1vdmVJbWFnZSgpIHsKICAgICAgdGhpcy4kZW1pdCgiaW5wdXQiLCAiIik7CiAgICB9LAogICAgaGFuZGxlVXBsb2FkU3VjY2VzcyhyZXMpIHsKICAgICAgdGhpcy4kZW1pdCgiaW5wdXQiLCByZXMudXJsKTsKICAgICAgdGhpcy5sb2FkaW5nLmNsb3NlKCk7CiAgICB9LAogICAgaGFuZGxlQmVmb3JlVXBsb2FkKCkgewogICAgICB0aGlzLmxvYWRpbmcgPSB0aGlzLiRsb2FkaW5nKHsKICAgICAgICBsb2NrOiB0cnVlLAogICAgICAgIHRleHQ6ICLkuIrkvKDkuK0iLAogICAgICAgIGJhY2tncm91bmQ6ICJyZ2JhKDAsIDAsIDAsIDAuNykiLAogICAgICB9KTsKICAgIH0sCiAgICBoYW5kbGVVcGxvYWRFcnJvcigpIHsKICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgdHlwZTogImVycm9yIiwKICAgICAgICBtZXNzYWdlOiAi5LiK5Lyg5aSx6LSlIiwKICAgICAgfSk7CiAgICAgIHRoaXMubG9hZGluZy5jbG9zZSgpOwogICAgfSwKICB9LAogIHdhdGNoOiB7fSwKfTsK"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuCA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/ImageUpload", "sourcesContent": ["<template>\n  <div class=\"component-upload-image\">\n    <el-upload\n      :action=\"uploadImgUrl\"\n      list-type=\"picture-card\"\n      :on-success=\"handleUploadSuccess\"\n      :before-upload=\"handleBeforeUpload\"\n      :on-error=\"handleUploadError\"\n      name=\"file\"\n      :show-file-list=\"false\"\n      :headers=\"headers\"\n      style=\"display: inline-block; vertical-align: top\"\n    >\n      <el-image v-if=\"!value\" :src=\"value\">\n        <div slot=\"error\" class=\"image-slot\">\n          <i class=\"el-icon-plus\" />\n        </div>\n      </el-image>\n      <div v-else class=\"image\">\n        <el-image :src=\"value\" :style=\"`width:150px;height:150px;`\" fit=\"fill\"/>\n        <div class=\"mask\">\n          <div class=\"actions\">\n            <span title=\"预览\" @click.stop=\"dialogVisible = true\">\n              <i class=\"el-icon-zoom-in\" />\n            </span>\n            <span title=\"移除\" @click.stop=\"removeImage\">\n              <i class=\"el-icon-delete\" />\n            </span>\n          </div>\n        </div>\n      </div>\n    </el-upload>\n    <el-dialog :visible.sync=\"dialogVisible\" title=\"预览\" width=\"800\" append-to-body>\n      <img :src=\"value\" style=\"display: block; max-width: 100%; margin: 0 auto;\">\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getToken } from \"@/utils/auth\";\n\nexport default {\n  data() {\n    return {\n      dialogVisible: false,\n      uploadImgUrl: process.env.VUE_APP_BASE_API + \"/common/upload\", // 上传的图片服务器地址\n      headers: {\n        Authorization: \"Bearer \" + getToken(),\n      },\n    };\n  },\n  props: {\n    value: {\n      type: String,\n      default: \"\",\n    },\n  },\n  methods: {\n    removeImage() {\n      this.$emit(\"input\", \"\");\n    },\n    handleUploadSuccess(res) {\n      this.$emit(\"input\", res.url);\n      this.loading.close();\n    },\n    handleBeforeUpload() {\n      this.loading = this.$loading({\n        lock: true,\n        text: \"上传中\",\n        background: \"rgba(0, 0, 0, 0.7)\",\n      });\n    },\n    handleUploadError() {\n      this.$message({\n        type: \"error\",\n        message: \"上传失败\",\n      });\n      this.loading.close();\n    },\n  },\n  watch: {},\n};\n</script>\n\n<style scoped lang=\"scss\">\n.image {\n  position: relative;\n  .mask {\n    opacity: 0;\n    position: absolute;\n    top: 0;\n    width: 100%;\n    background-color: rgba(0, 0, 0, 0.5);\n    transition: all 0.3s;\n  }\n  &:hover .mask {\n    opacity: 1;\n  }\n}\n</style>"]}]}