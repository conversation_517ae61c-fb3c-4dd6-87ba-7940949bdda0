{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/system/user/index1.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/system/user/index1.vue", "mtime": 1649074414000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICBsaXN0VXNlciwNCiAgZ2V0VXNlciwNCiAgZGVsVXNlciwNCiAgYWRkVXNlciwNCiAgdXBkYXRlVXNlciwNCiAgZXhwb3J0VXNlciwNCn0gZnJvbSAiQC9hcGkvc3lzdGVtL3VzZXIiOw0KaW1wb3J0IEltYWdlVXBsb2FkIGZyb20gIkAvY29tcG9uZW50cy9JbWFnZVVwbG9hZCI7DQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogIlVzZXIiLA0KICBjb21wb25lbnRzOiB7DQogICAgSW1hZ2VVcGxvYWQsDQogIH0sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIC8vIOmBrue9qeWxgg0KICAgICAgbG9hZGluZzogdHJ1ZSwNCiAgICAgIC8vIOmAieS4reaVsOe7hA0KICAgICAgaWRzOiBbXSwNCiAgICAgIC8vIOmdnuWNleS4quemgeeUqA0KICAgICAgc2luZ2xlOiB0cnVlLA0KICAgICAgLy8g6Z2e5aSa5Liq56aB55SoDQogICAgICBtdWx0aXBsZTogdHJ1ZSwNCiAgICAgIC8vIOaYvuekuuaQnOe0ouadoeS7tg0KICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwNCiAgICAgIC8vIOaAu+adoeaVsA0KICAgICAgdG90YWw6IDAsDQogICAgICAvLyDnlKjmiLfkv6Hmga/ooajmoLzmlbDmja4NCiAgICAgIHVzZXJMaXN0OiBbXSwNCiAgICAgIC8vIOW8ueWHuuWxguagh+mimA0KICAgICAgdGl0bGU6ICIiLA0KICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGCDQogICAgICBvcGVuOiBmYWxzZSwNCiAgICAgIC8vIOeUqOaIt+aAp+WIq+Wtl+WFuA0KICAgICAgc2V4T3B0aW9uczogW10sDQogICAgICAvLyDluJDlj7fnirbmgIHlrZflhbgNCiAgICAgIHN0YXR1c09wdGlvbnM6IFtdLA0KICAgICAgLy8g55+t5L+h6YCa55+l5a2X5YW4DQogICAgICBzbXNTZW5kT3B0aW9uczogW10sDQogICAgICAvLyDlrqHmoLjnirbmgIHlrZflhbgNCiAgICAgIGF1ZGl0U3RhdHVzT3B0aW9uczogW10sDQogICAgICAvLyDmn6Xor6Llj4LmlbANCiAgICAgIHF1ZXJ5UGFyYW1zOiB7DQogICAgICAgIHBhZ2VOdW06IDEsDQogICAgICAgIHBhZ2VTaXplOiAxMCwNCiAgICAgICAgdXNlck5hbWU6IG51bGwsDQogICAgICAgIG5pY2tOYW1lOiBudWxsLA0KICAgICAgICB1c2VyVHlwZTogbnVsbCwNCiAgICAgICAgZW1haWw6IG51bGwsDQogICAgICAgIHBob25lbnVtYmVyOiBudWxsLA0KICAgICAgICBzdGF0dXM6IG51bGwsDQogICAgICAgIGNvbXBhbnk6IG51bGwsDQogICAgICAgIGF1ZGl0U3RhdHVzOiBudWxsLA0KICAgICAgfSwNCiAgICAgIC8vIOihqOWNleWPguaVsA0KICAgICAgZm9ybToge30sDQogICAgICAvLyDooajljZXmoKHpqowNCiAgICAgIHJ1bGVzOiB7DQogICAgICAgIHVzZXJOYW1lOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIueUqOaIt+i0puWPt+S4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9LA0KICAgICAgICBdLA0KICAgICAgICBuaWNrTmFtZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLmiqXlpIfkurrlp5PlkI3kuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgXSwNCiAgICAgIH0sDQogICAgfTsNCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICB0aGlzLmdldExpc3QoKTsNCiAgICB0aGlzLmdldERpY3RzKCJzeXNfdXNlcl9zZXgiKS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgdGhpcy5zZXhPcHRpb25zID0gcmVzcG9uc2UuZGF0YTsNCiAgICB9KTsNCiAgICB0aGlzLmdldERpY3RzKCJzeXNfbm9ybWFsX2Rpc2FibGUiKS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgdGhpcy5zdGF0dXNPcHRpb25zID0gcmVzcG9uc2UuZGF0YTsNCiAgICB9KTsNCiAgICB0aGlzLmdldERpY3RzKCJzeXNfeWVzX25vIikudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgIHRoaXMuc21zU2VuZE9wdGlvbnMgPSByZXNwb25zZS5kYXRhOw0KICAgIH0pOw0KICAgIHRoaXMuZ2V0RGljdHMoInByX2F1ZGl0X3N0YXR1cyIpLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICB0aGlzLmF1ZGl0U3RhdHVzT3B0aW9ucyA9IHJlc3BvbnNlLmRhdGE7DQogICAgfSk7DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICAvKiog5p+l6K+i55So5oi35L+h5oGv5YiX6KGoICovDQogICAgZ2V0TGlzdCgpIHsNCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7DQogICAgICBsaXN0VXNlcih0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICB0aGlzLnVzZXJMaXN0ID0gcmVzcG9uc2Uucm93czsNCiAgICAgICAgdGhpcy50b3RhbCA9IHJlc3BvbnNlLnRvdGFsOw0KICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLy8g55So5oi35oCn5Yir5a2X5YW457+76K+RDQogICAgc2V4Rm9ybWF0KHJvdywgY29sdW1uKSB7DQogICAgICByZXR1cm4gdGhpcy5zZWxlY3REaWN0TGFiZWwodGhpcy5zZXhPcHRpb25zLCByb3cuc2V4KTsNCiAgICB9LA0KICAgIC8vIOW4kOWPt+eKtuaAgeWtl+WFuOe/u+ivkQ0KICAgIHN0YXR1c0Zvcm1hdChyb3csIGNvbHVtbikgew0KICAgICAgcmV0dXJuIHRoaXMuc2VsZWN0RGljdExhYmVsKHRoaXMuc3RhdHVzT3B0aW9ucywgcm93LnN0YXR1cyk7DQogICAgfSwNCiAgICAvLyDnn63kv6HpgJrnn6XlrZflhbjnv7vor5ENCiAgICBzbXNTZW5kRm9ybWF0KHJvdywgY29sdW1uKSB7DQogICAgICByZXR1cm4gdGhpcy5zZWxlY3REaWN0TGFiZWwodGhpcy5zbXNTZW5kT3B0aW9ucywgcm93LnNtc1NlbmQpOw0KICAgIH0sDQogICAgLy8g5a6h5qC454q25oCB5a2X5YW457+76K+RDQogICAgYXVkaXRTdGF0dXNGb3JtYXQocm93LCBjb2x1bW4pIHsNCiAgICAgIHJldHVybiB0aGlzLnNlbGVjdERpY3RMYWJlbCh0aGlzLmF1ZGl0U3RhdHVzT3B0aW9ucywgcm93LmF1ZGl0U3RhdHVzKTsNCiAgICB9LA0KICAgIC8vIOWPlua2iOaMiemSrg0KICAgIGNhbmNlbCgpIHsNCiAgICAgIHRoaXMub3BlbiA9IGZhbHNlOw0KICAgICAgdGhpcy5yZXNldCgpOw0KICAgIH0sDQogICAgLy8g6KGo5Y2V6YeN572uDQogICAgcmVzZXQoKSB7DQogICAgICB0aGlzLmZvcm0gPSB7DQogICAgICAgIHVzZXJJZDogbnVsbCwNCiAgICAgICAgZGVwdElkOiBudWxsLA0KICAgICAgICB1c2VyTmFtZTogbnVsbCwNCiAgICAgICAgbmlja05hbWU6IG51bGwsDQogICAgICAgIHVzZXJUeXBlOiBudWxsLA0KICAgICAgICBlbWFpbDogbnVsbCwNCiAgICAgICAgcGhvbmVudW1iZXI6IG51bGwsDQogICAgICAgIHNleDogIjAiLA0KICAgICAgICBhdmF0YXI6IG51bGwsDQogICAgICAgIHBhc3N3b3JkOiBudWxsLA0KICAgICAgICBzdGF0dXM6ICIwIiwNCiAgICAgICAgZGVsRmxhZzogbnVsbCwNCiAgICAgICAgbG9naW5JcDogbnVsbCwNCiAgICAgICAgbG9naW5EYXRlOiBudWxsLA0KICAgICAgICBjcmVhdGVCeTogbnVsbCwNCiAgICAgICAgY3JlYXRlVGltZTogbnVsbCwNCiAgICAgICAgdXBkYXRlQnk6IG51bGwsDQogICAgICAgIHVwZGF0ZVRpbWU6IG51bGwsDQogICAgICAgIHJlbWFyazogbnVsbCwNCiAgICAgICAgY29tcGFueTogbnVsbCwNCiAgICAgICAgYnVzaW5lc3NObzogbnVsbCwNCiAgICAgICAgYnVzaW5lc3NOb1BpYzogbnVsbCwNCiAgICAgICAgcHJvdmluY2U6IG51bGwsDQogICAgICAgIGFkZHJlc3M6IG51bGwsDQogICAgICAgIGRlYWxlcjogbnVsbCwNCiAgICAgICAgc21zU2VuZDogIjAiLA0KICAgICAgICBhdWRpdFN0YXR1czogIjAiLA0KICAgICAgfTsNCiAgICAgIHRoaXMucmVzZXRGb3JtKCJmb3JtIik7DQogICAgfSwNCiAgICAvKiog5pCc57Si5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlUXVlcnkoKSB7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOw0KICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgfSwNCiAgICAvKiog6YeN572u5oyJ6ZKu5pON5L2cICovDQogICAgcmVzZXRRdWVyeSgpIHsNCiAgICAgIHRoaXMucmVzZXRGb3JtKCJxdWVyeUZvcm0iKTsNCiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsNCiAgICB9LA0KICAgIC8vIOWkmumAieahhumAieS4reaVsOaNrg0KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsNCiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcCgoaXRlbSkgPT4gaXRlbS51c2VySWQpOw0KICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoICE9PSAxOw0KICAgICAgdGhpcy5tdWx0aXBsZSA9ICFzZWxlY3Rpb24ubGVuZ3RoOw0KICAgIH0sDQogICAgLyoqIOaWsOWinuaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZUFkZCgpIHsNCiAgICAgIHRoaXMucmVzZXQoKTsNCiAgICAgIHRoaXMub3BlbiA9IHRydWU7DQogICAgICB0aGlzLnRpdGxlID0gIua3u+WKoOeUqOaIt+S/oeaBryI7DQogICAgfSwNCiAgICAvKiog5L+u5pS55oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlVXBkYXRlKHJvdykgew0KICAgICAgdGhpcy5yZXNldCgpOw0KICAgICAgY29uc3QgdXNlcklkID0gcm93LnVzZXJJZCB8fCB0aGlzLmlkczsNCiAgICAgIGdldFVzZXIodXNlcklkKS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICB0aGlzLmZvcm0gPSByZXNwb25zZS5kYXRhOw0KICAgICAgICB0aGlzLm9wZW4gPSB0cnVlOw0KICAgICAgICB0aGlzLnRpdGxlID0gIuS/ruaUueeUqOaIt+S/oeaBryI7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDmj5DkuqTmjInpkq4gKi8NCiAgICBzdWJtaXRGb3JtKCkgew0KICAgICAgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKCh2YWxpZCkgPT4gew0KICAgICAgICBpZiAodmFsaWQpIHsNCiAgICAgICAgICBpZiAodGhpcy5mb3JtLnVzZXJJZCAhPSBudWxsKSB7DQogICAgICAgICAgICB1cGRhdGVVc2VyKHRoaXMuZm9ybSkudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgICAgICAgdGhpcy5tc2dTdWNjZXNzKCLkv67mlLnmiJDlip8iKTsNCiAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7DQogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIGFkZFVzZXIodGhpcy5mb3JtKS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICAgICAgICB0aGlzLm1zZ1N1Y2Nlc3MoIuaWsOWinuaIkOWKnyIpOw0KICAgICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsNCiAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOWIoOmZpOaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZURlbGV0ZShyb3cpIHsNCiAgICAgIGNvbnN0IHVzZXJJZHMgPSByb3cudXNlcklkIHx8IHRoaXMuaWRzOw0KICAgICAgdGhpcy4kY29uZmlybSgNCiAgICAgICAgJ+aYr+WQpuehruiupOWIoOmZpOeUqOaIt+S/oeaBr+e8luWPt+S4uiInICsgdXNlcklkcyArICci55qE5pWw5o2u6aG5PycsDQogICAgICAgICLorablkYoiLA0KICAgICAgICB7DQogICAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLA0KICAgICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLA0KICAgICAgICAgIHR5cGU6ICJ3YXJuaW5nIiwNCiAgICAgICAgfQ0KICAgICAgKQ0KICAgICAgICAudGhlbihmdW5jdGlvbiAoKSB7DQogICAgICAgICAgcmV0dXJuIGRlbFVzZXIodXNlcklkcyk7DQogICAgICAgIH0pDQogICAgICAgIC50aGVuKCgpID0+IHsNCiAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICB0aGlzLm1zZ1N1Y2Nlc3MoIuWIoOmZpOaIkOWKnyIpOw0KICAgICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDlr7zlh7rmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVFeHBvcnQoKSB7DQogICAgICBjb25zdCBxdWVyeVBhcmFtcyA9IHRoaXMucXVlcnlQYXJhbXM7DQogICAgICB0aGlzLiRjb25maXJtKCLmmK/lkKbnoa7orqTlr7zlh7rmiYDmnInnlKjmiLfkv6Hmga/mlbDmja7pobk/IiwgIuitpuWRiiIsIHsNCiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLA0KICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAi5Y+W5raIIiwNCiAgICAgICAgdHlwZTogIndhcm5pbmciLA0KICAgICAgfSkNCiAgICAgICAgLnRoZW4oZnVuY3Rpb24gKCkgew0KICAgICAgICAgIHJldHVybiBleHBvcnRVc2VyKHF1ZXJ5UGFyYW1zKTsNCiAgICAgICAgfSkNCiAgICAgICAgLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgICAgdGhpcy5kb3dubG9hZChyZXNwb25zZS5tc2cpOw0KICAgICAgICB9KTsNCiAgICB9LA0KICB9LA0KfTsNCg=="}, {"version": 3, "sources": ["index1.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkbA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index1.vue", "sourceRoot": "src/views/system/user", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form\r\n      :model=\"queryParams\"\r\n      ref=\"queryForm\"\r\n      :inline=\"true\"\r\n      v-show=\"showSearch\"\r\n      label-width=\"68px\"\r\n    >\r\n      <el-form-item label=\"用户账号\" prop=\"userName\">\r\n        <el-input\r\n          v-model=\"queryParams.userName\"\r\n          placeholder=\"请输入用户账号\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"报备人姓名\" prop=\"nickName\">\r\n        <el-input\r\n          v-model=\"queryParams.nickName\"\r\n          placeholder=\"请输入报备人姓名\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"用户类型\" prop=\"userType\">\r\n        <el-select\r\n          v-model=\"queryParams.userType\"\r\n          placeholder=\"请选择用户类型\"\r\n          clearable\r\n          size=\"small\"\r\n        >\r\n          <el-option label=\"请选择字典生成\" value=\"\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"资料接收邮箱\" prop=\"email\">\r\n        <el-input\r\n          v-model=\"queryParams.email\"\r\n          placeholder=\"请输入资料接收邮箱\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"报备人电话\" prop=\"phonenumber\">\r\n        <el-input\r\n          v-model=\"queryParams.phonenumber\"\r\n          placeholder=\"请输入报备人电话\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"帐号状态\" prop=\"status\">\r\n        <el-select\r\n          v-model=\"queryParams.status\"\r\n          placeholder=\"请选择帐号状态\"\r\n          clearable\r\n          size=\"small\"\r\n        >\r\n          <el-option\r\n            v-for=\"dict in statusOptions\"\r\n            :key=\"dict.dictValue\"\r\n            :label=\"dict.dictLabel\"\r\n            :value=\"dict.dictValue\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"公司全称\" prop=\"company\">\r\n        <el-input\r\n          v-model=\"queryParams.company\"\r\n          placeholder=\"请输入公司全称\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"审核状态\" prop=\"auditStatus\">\r\n        <el-select\r\n          v-model=\"queryParams.auditStatus\"\r\n          placeholder=\"请选择审核状态\"\r\n          clearable\r\n          size=\"small\"\r\n        >\r\n          <el-option\r\n            v-for=\"dict in auditStatusOptions\"\r\n            :key=\"dict.dictValue\"\r\n            :label=\"dict.dictLabel\"\r\n            :value=\"dict.dictValue\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button\r\n          type=\"primary\"\r\n          icon=\"el-icon-search\"\r\n          size=\"mini\"\r\n          @click=\"handleQuery\"\r\n          >搜索</el-button\r\n        >\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\"\r\n          >重置</el-button\r\n        >\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n          v-hasPermi=\"['system:user:add']\"\r\n          >新增</el-button\r\n        >\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleUpdate\"\r\n          v-hasPermi=\"['system:user:edit']\"\r\n          >修改</el-button\r\n        >\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['system:user:remove']\"\r\n          >删除</el-button\r\n        >\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['system:user:export']\"\r\n          >导出</el-button\r\n        >\r\n      </el-col>\r\n      <right-toolbar\r\n        :showSearch.sync=\"showSearch\"\r\n        @queryTable=\"getList\"\r\n      ></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table\r\n      v-loading=\"loading\"\r\n      :data=\"userList\"\r\n      @selection-change=\"handleSelectionChange\"\r\n    >\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"用户ID\" align=\"center\" prop=\"userId\" />\r\n      <el-table-column label=\"用户账号\" align=\"center\" prop=\"userName\" />\r\n      <el-table-column label=\"报备人姓名\" align=\"center\" prop=\"nickName\" />\r\n      <el-table-column label=\"用户类型\" align=\"center\" prop=\"userType\" />\r\n      <el-table-column label=\"资料接收邮箱\" align=\"center\" prop=\"email\" />\r\n      <el-table-column label=\"报备人电话\" align=\"center\" prop=\"phonenumber\" />\r\n      <el-table-column\r\n        label=\"用户性别\"\r\n        align=\"center\"\r\n        prop=\"sex\"\r\n        :formatter=\"sexFormat\"\r\n      />\r\n      <el-table-column label=\"头像地址\" align=\"center\" prop=\"avatar\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <img :src=\"scope.row.avatar\" width=\"100\" height=\"100\" />\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"公司全称\" align=\"center\" prop=\"company\" />\r\n      <el-table-column label=\"营业执照号码\" align=\"center\" prop=\"businessNo\" />\r\n      <el-table-column label=\"营业执照图片\" align=\"center\" prop=\"businessNoPic\" width=\"150\">\r\n        <template slot-scope=\"scope\">\r\n          <img :src=\"scope.row.businessNoPic\" width=\"150\" height=\"100\" />\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"所在省份\" align=\"center\" prop=\"province\" />\r\n      <el-table-column label=\"资料邮寄地址\" align=\"center\" prop=\"address\" />\r\n      <el-table-column label=\"隶属经销商\" align=\"center\" prop=\"dealer\" />\r\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" v-if=\"columns[6].visible\" width=\"160\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.createTime) }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column\r\n        label=\"短信通知\"\r\n        align=\"center\"\r\n        prop=\"smsSend\"\r\n        :formatter=\"smsSendFormat\"\r\n      />\r\n      <el-table-column\r\n        label=\"审核状态\"\r\n        align=\"center\"\r\n        prop=\"auditStatus\"\r\n        :formatter=\"auditStatusFormat\"\r\n      />\r\n      <el-table-column label=\"帐号状态\" align=\"center\" prop=\"status\">\r\n        <template slot-scope=\"scope\">\r\n          <el-switch\r\n            v-model=\"scope.row.status\"\r\n            active-value=\"0\"\r\n            inactive-value=\"1\"\r\n            @change=\"handleStatusChange(scope.row)\"\r\n          ></el-switch>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column\r\n        label=\"操作\"\r\n        align=\"center\"\r\n        class-name=\"small-padding fixed-width\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n            v-hasPermi=\"['system:user:edit']\"\r\n            >修改</el-button\r\n          >\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n            v-hasPermi=\"['system:user:remove']\"\r\n            >删除</el-button\r\n          >\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination\r\n      v-show=\"total > 0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 添加或修改用户信息对话框 -->\r\n    <el-dialog\r\n      :title=\"title\"\r\n      :visible.sync=\"open\"\r\n      width=\"1000px\"\r\n      append-to-body\r\n    >\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\r\n        <el-row>\r\n          <el-col span=\"12\">\r\n            <el-form-item label=\"用户账号\" prop=\"userName\">\r\n              <el-input v-model=\"form.userName\" placeholder=\"请输入用户账号\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col span=\"12\">\r\n            <el-form-item label=\"报备人姓名\" prop=\"nickName\">\r\n              <el-input\r\n                v-model=\"form.nickName\"\r\n                placeholder=\"请输入报备人姓名\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col span=\"12\">\r\n            <el-form-item label=\"角色\">\r\n              <el-select v-model=\"form.roleIds\" multiple placeholder=\"请选择\">\r\n                <el-option\r\n                  v-for=\"item in roleOptions\"\r\n                  :key=\"item.roleId\"\r\n                  :label=\"item.roleName\"\r\n                  :value=\"item.roleId\"\r\n                  :disabled=\"item.status == 1\"\r\n                ></el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col span=\"12\">\r\n            <el-form-item label=\"资料接收邮箱\" prop=\"email\">\r\n              <el-input v-model=\"form.email\" placeholder=\"请输入资料接收邮箱\" maxlength=\"50\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col span=\"12\">\r\n            <el-form-item label=\"报备人电话\" prop=\"phonenumber\">\r\n              <el-input\r\n                v-model=\"form.phonenumber\"\r\n                placeholder=\"请输入报备人电话\"\r\n                 maxlength=\"11\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col span=\"12\">\r\n            <el-form-item label=\"用户性别\">\r\n              <el-select v-model=\"form.sex\" placeholder=\"请选择\">\r\n                <el-option\r\n                  v-for=\"dict in sexOptions\"\r\n                  :key=\"dict.dictValue\"\r\n                  :label=\"dict.dictLabel\"\r\n                  :value=\"dict.dictValue\"\r\n                ></el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col span=\"12\">\r\n            <el-form-item label=\"头像地址\">\r\n              <imageUpload v-model=\"form.avatar\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col span=\"12\">\r\n            <el-form-item label=\"密码\" prop=\"password\">\r\n              <el-input v-model=\"form.password\" placeholder=\"请输入密码\" type=\"password\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col span=\"12\">\r\n            <el-form-item label=\"帐号状态\">\r\n              <el-radio-group v-model=\"form.status\">\r\n                <el-radio\r\n                  v-for=\"dict in statusOptions\"\r\n                  :key=\"dict.dictValue\"\r\n                  :label=\"dict.dictValue\"\r\n                  >{{ dict.dictLabel }}</el-radio\r\n                >\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col span=\"12\">\r\n            <el-form-item label=\"备注\" prop=\"remark\">\r\n              <el-input\r\n                v-model=\"form.remark\"\r\n                type=\"textarea\"\r\n                placeholder=\"请输入内容\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col span=\"12\">\r\n            <el-form-item label=\"公司全称\" prop=\"company\">\r\n              <el-input v-model=\"form.company\" placeholder=\"请输入公司全称\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col span=\"12\">\r\n            <el-form-item label=\"营业执照号码\" prop=\"businessNo\">\r\n              <el-input\r\n                v-model=\"form.businessNo\"\r\n                placeholder=\"请输入营业执照号码\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col span=\"12\">\r\n            <el-form-item label=\"营业执照图片\">\r\n              <imageUpload v-model=\"form.businessNoPic\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col span=\"12\">\r\n            <el-form-item label=\"所在省份\" prop=\"province\">\r\n              <el-input v-model=\"form.province\" placeholder=\"请输入所在省份\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col span=\"12\">\r\n            <el-form-item label=\"资料邮寄地址\" prop=\"address\">\r\n              <el-input\r\n                v-model=\"form.address\"\r\n                placeholder=\"请输入资料邮寄地址\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col span=\"12\">\r\n            <el-form-item label=\"隶属经销商\" prop=\"dealer\">\r\n              <el-input v-model=\"form.dealer\" placeholder=\"请输入隶属经销商\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col span=\"12\">\r\n            <el-form-item label=\"短信通知\">\r\n              <el-radio-group v-model=\"form.smsSend\">\r\n                <el-radio\r\n                  v-for=\"dict in smsSendOptions\"\r\n                  :key=\"dict.dictValue\"\r\n                  :label=\"dict.dictValue\"\r\n                  >{{ dict.dictLabel }}</el-radio\r\n                >\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col span=\"12\">\r\n            <el-form-item label=\"审核状态\">\r\n              <el-radio-group v-model=\"form.auditStatus\">\r\n                <el-radio\r\n                  v-for=\"dict in auditStatusOptions\"\r\n                  :key=\"dict.dictValue\"\r\n                  :label=\"dict.dictValue\"\r\n                  >{{ dict.dictLabel }}</el-radio\r\n                >\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  listUser,\r\n  getUser,\r\n  delUser,\r\n  addUser,\r\n  updateUser,\r\n  exportUser,\r\n} from \"@/api/system/user\";\r\nimport ImageUpload from \"@/components/ImageUpload\";\r\n\r\nexport default {\r\n  name: \"User\",\r\n  components: {\r\n    ImageUpload,\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 用户信息表格数据\r\n      userList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 用户性别字典\r\n      sexOptions: [],\r\n      // 帐号状态字典\r\n      statusOptions: [],\r\n      // 短信通知字典\r\n      smsSendOptions: [],\r\n      // 审核状态字典\r\n      auditStatusOptions: [],\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        userName: null,\r\n        nickName: null,\r\n        userType: null,\r\n        email: null,\r\n        phonenumber: null,\r\n        status: null,\r\n        company: null,\r\n        auditStatus: null,\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        userName: [\r\n          { required: true, message: \"用户账号不能为空\", trigger: \"blur\" },\r\n        ],\r\n        nickName: [\r\n          { required: true, message: \"报备人姓名不能为空\", trigger: \"blur\" },\r\n        ],\r\n      },\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n    this.getDicts(\"sys_user_sex\").then((response) => {\r\n      this.sexOptions = response.data;\r\n    });\r\n    this.getDicts(\"sys_normal_disable\").then((response) => {\r\n      this.statusOptions = response.data;\r\n    });\r\n    this.getDicts(\"sys_yes_no\").then((response) => {\r\n      this.smsSendOptions = response.data;\r\n    });\r\n    this.getDicts(\"pr_audit_status\").then((response) => {\r\n      this.auditStatusOptions = response.data;\r\n    });\r\n  },\r\n  methods: {\r\n    /** 查询用户信息列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listUser(this.queryParams).then((response) => {\r\n        this.userList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 用户性别字典翻译\r\n    sexFormat(row, column) {\r\n      return this.selectDictLabel(this.sexOptions, row.sex);\r\n    },\r\n    // 帐号状态字典翻译\r\n    statusFormat(row, column) {\r\n      return this.selectDictLabel(this.statusOptions, row.status);\r\n    },\r\n    // 短信通知字典翻译\r\n    smsSendFormat(row, column) {\r\n      return this.selectDictLabel(this.smsSendOptions, row.smsSend);\r\n    },\r\n    // 审核状态字典翻译\r\n    auditStatusFormat(row, column) {\r\n      return this.selectDictLabel(this.auditStatusOptions, row.auditStatus);\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        userId: null,\r\n        deptId: null,\r\n        userName: null,\r\n        nickName: null,\r\n        userType: null,\r\n        email: null,\r\n        phonenumber: null,\r\n        sex: \"0\",\r\n        avatar: null,\r\n        password: null,\r\n        status: \"0\",\r\n        delFlag: null,\r\n        loginIp: null,\r\n        loginDate: null,\r\n        createBy: null,\r\n        createTime: null,\r\n        updateBy: null,\r\n        updateTime: null,\r\n        remark: null,\r\n        company: null,\r\n        businessNo: null,\r\n        businessNoPic: null,\r\n        province: null,\r\n        address: null,\r\n        dealer: null,\r\n        smsSend: \"0\",\r\n        auditStatus: \"0\",\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map((item) => item.userId);\r\n      this.single = selection.length !== 1;\r\n      this.multiple = !selection.length;\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加用户信息\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const userId = row.userId || this.ids;\r\n      getUser(userId).then((response) => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改用户信息\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n          if (this.form.userId != null) {\r\n            updateUser(this.form).then((response) => {\r\n              this.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addUser(this.form).then((response) => {\r\n              this.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const userIds = row.userId || this.ids;\r\n      this.$confirm(\r\n        '是否确认删除用户信息编号为\"' + userIds + '\"的数据项?',\r\n        \"警告\",\r\n        {\r\n          confirmButtonText: \"确定\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\",\r\n        }\r\n      )\r\n        .then(function () {\r\n          return delUser(userIds);\r\n        })\r\n        .then(() => {\r\n          this.getList();\r\n          this.msgSuccess(\"删除成功\");\r\n        });\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      const queryParams = this.queryParams;\r\n      this.$confirm(\"是否确认导出所有用户信息数据项?\", \"警告\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(function () {\r\n          return exportUser(queryParams);\r\n        })\r\n        .then((response) => {\r\n          this.download(response.msg);\r\n        });\r\n    },\r\n  },\r\n};\r\n</script>\r\n"]}]}