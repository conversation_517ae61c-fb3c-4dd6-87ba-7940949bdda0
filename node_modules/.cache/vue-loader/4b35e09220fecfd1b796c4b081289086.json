{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/components/HeaderSearch/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/components/HeaderSearch/index.vue", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAoBA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/HeaderSearch", "sourcesContent": ["<template>\n  <div :class=\"{'show':show}\" class=\"header-search\">\n    <svg-icon class-name=\"search-icon\" icon-class=\"search\" @click.stop=\"click\" />\n    <el-select\n      ref=\"headerSearchSelect\"\n      v-model=\"search\"\n      :remote-method=\"querySearch\"\n      filterable\n      default-first-option\n      remote\n      placeholder=\"Search\"\n      class=\"header-search-select\"\n      @change=\"change\"\n    >\n      <el-option v-for=\"option in options\" :key=\"option.item.path\" :value=\"option.item\" :label=\"option.item.title.join(' > ')\" />\n    </el-select>\n  </div>\n</template>\n\n<script>\n// fuse is a lightweight fuzzy-search module\n// make search results more in line with expectations\nimport Fuse from 'fuse.js/dist/fuse.min.js'\nimport path from 'path'\n\nexport default {\n  name: 'HeaderSearch',\n  data() {\n    return {\n      search: '',\n      options: [],\n      searchPool: [],\n      show: false,\n      fuse: undefined\n    }\n  },\n  computed: {\n    routes() {\n      return this.$store.getters.permission_routes\n    }\n  },\n  watch: {\n    routes() {\n      this.searchPool = this.generateRoutes(this.routes)\n    },\n    searchPool(list) {\n      this.initFuse(list)\n    },\n    show(value) {\n      if (value) {\n        document.body.addEventListener('click', this.close)\n      } else {\n        document.body.removeEventListener('click', this.close)\n      }\n    }\n  },\n  mounted() {\n    this.searchPool = this.generateRoutes(this.routes)\n  },\n  methods: {\n    click() {\n      this.show = !this.show\n      if (this.show) {\n        this.$refs.headerSearchSelect && this.$refs.headerSearchSelect.focus()\n      }\n    },\n    close() {\n      this.$refs.headerSearchSelect && this.$refs.headerSearchSelect.blur()\n      this.options = []\n      this.show = false\n    },\n    change(val) {\n      if(this.ishttp(val.path)) {\n        // http(s):// 路径新窗口打开\n        window.open(val.path, \"_blank\");\n      } else {\n        this.$router.push(val.path)\n      }\n      this.search = ''\n      this.options = []\n      this.$nextTick(() => {\n        this.show = false\n      })\n    },\n    initFuse(list) {\n      this.fuse = new Fuse(list, {\n        shouldSort: true,\n        threshold: 0.4,\n        location: 0,\n        distance: 100,\n        maxPatternLength: 32,\n        minMatchCharLength: 1,\n        keys: [{\n          name: 'title',\n          weight: 0.7\n        }, {\n          name: 'path',\n          weight: 0.3\n        }]\n      })\n    },\n    // Filter out the routes that can be displayed in the sidebar\n    // And generate the internationalized title\n    generateRoutes(routes, basePath = '/', prefixTitle = []) {\n      let res = []\n\n      for (const router of routes) {\n        // skip hidden router\n        if (router.hidden) { continue }\n\n        const data = {\n          path: !this.ishttp(router.path) ? path.resolve(basePath, router.path) : router.path,\n          title: [...prefixTitle]\n        }\n\n        if (router.meta && router.meta.title) {\n          data.title = [...data.title, router.meta.title]\n\n          if (router.redirect !== 'noRedirect') {\n            // only push the routes with title\n            // special case: need to exclude parent router without redirect\n            res.push(data)\n          }\n        }\n\n        // recursive child routes\n        if (router.children) {\n          const tempRoutes = this.generateRoutes(router.children, data.path, data.title)\n          if (tempRoutes.length >= 1) {\n            res = [...res, ...tempRoutes]\n          }\n        }\n      }\n      return res\n    },\n    querySearch(query) {\n      if (query !== '') {\n        this.options = this.fuse.search(query)\n      } else {\n        this.options = []\n      }\n    },\n    ishttp(url) {\n      return url.indexOf('http://') !== -1 || url.indexOf('https://') !== -1\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.header-search {\n  font-size: 0 !important;\n\n  .search-icon {\n    cursor: pointer;\n    font-size: 18px;\n    vertical-align: middle;\n  }\n\n  .header-search-select {\n    font-size: 18px;\n    transition: width 0.2s;\n    width: 0;\n    overflow: hidden;\n    background: transparent;\n    border-radius: 0;\n    display: inline-block;\n    vertical-align: middle;\n\n    ::v-deep .el-input__inner {\n      border-radius: 0;\n      border: 0;\n      padding-left: 0;\n      padding-right: 0;\n      box-shadow: none !important;\n      border-bottom: 1px solid #d9d9d9;\n      vertical-align: middle;\n    }\n  }\n\n  &.show {\n    .header-search-select {\n      width: 210px;\n      margin-left: 10px;\n    }\n  }\n}\n</style>\n"]}]}