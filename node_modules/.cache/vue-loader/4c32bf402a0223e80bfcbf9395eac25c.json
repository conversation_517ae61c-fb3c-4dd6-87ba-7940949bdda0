{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/flowable/task/finished/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/flowable/task/finished/index.vue", "mtime": 1650978246000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqFA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/flowable/task/finished", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"名称\" prop=\"name\">\n        <el-input\n          v-model=\"queryParams.name\"\n          placeholder=\"请输入名称\"\n          clearable\n          size=\"small\"\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"开始时间\" prop=\"deployTime\">\n        <el-date-picker clearable size=\"small\"\n                        v-model=\"queryParams.deployTime\"\n                        type=\"date\"\n                        value-format=\"yyyy-MM-dd\"\n                        placeholder=\"选择时间\">\n        </el-date-picker>\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"danger\"\n          plain\n          icon=\"el-icon-delete\"\n          size=\"mini\"\n          :disabled=\"multiple\"\n          @click=\"handleDelete\"\n          v-hasPermi=\"['system:deployment:remove']\"\n        >删除</el-button>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <el-table v-loading=\"loading\" :data=\"finishedList\" border @selection-change=\"handleSelectionChange\">\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column label=\"任务编号\" align=\"center\" prop=\"taskId\" :show-overflow-tooltip=\"true\"/>\n      <el-table-column label=\"流程名称\" align=\"center\" prop=\"procDefName\" :show-overflow-tooltip=\"true\"/>\n      <el-table-column label=\"任务名称\" align=\"center\" prop=\"name\" :show-overflow-tooltip=\"true\"/>\n      <el-table-column label=\"任务节点\" align=\"center\" prop=\"taskName\" />\n      <el-table-column label=\"流程发起人\" align=\"center\">\n        <template slot-scope=\"scope\">\n          <label>{{scope.row.startUserName}} <el-tag type=\"info\" size=\"mini\">{{scope.row.startDeptName}}</el-tag></label>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"接收时间\" align=\"center\" prop=\"createTime\" width=\"180\"/>\n      <el-table-column label=\"审批时间\" align=\"center\" prop=\"finishTime\" width=\"180\"/>\n      <el-table-column label=\"耗时\" align=\"center\" prop=\"duration\" width=\"180\"/>\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-tickets\"\n            @click=\"handleFlowRecord(scope.row)\"\n          >流转记录</el-button>\n           <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-tickets\"\n            @click=\"handleRevoke(scope.row)\"\n          >撤回\n          </el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n  </div>\n</template>\n\n<script>\nimport { finishedList, getDeployment, delDeployment, addDeployment, updateDeployment, exportDeployment, revokeProcess } from \"@/api/flowable/finished\";\n\nexport default {\n  name: \"Deploy\",\n  components: {\n  },\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 已办任务列表数据\n      finishedList: [],\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      open: false,\n      src: \"\",\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        name: null,\n        category: null,\n        key: null,\n        tenantId: null,\n        deployTime: null,\n        derivedFrom: null,\n        derivedFromRoot: null,\n        parentDeploymentId: null,\n        engineVersion: null\n      },\n      // 表单参数\n      form: {},\n      // 表单校验\n      rules: {\n      }\n    };\n  },\n  created() {\n    this.getList();\n  },\n  methods: {\n    /** 查询流程定义列表 */\n    getList() {\n      this.loading = true;\n      finishedList(this.queryParams).then(response => {\n        this.finishedList = response.data.records;\n        this.total = response.data.total;\n        this.loading = false;\n      });\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        id: null,\n        name: null,\n        category: null,\n        key: null,\n        tenantId: null,\n        deployTime: null,\n        derivedFrom: null,\n        derivedFromRoot: null,\n        parentDeploymentId: null,\n        engineVersion: null\n      };\n      this.resetForm(\"form\");\n    },\n    setIcon(val){\n      if (val){\n        return \"el-icon-check\";\n      }else {\n        return \"el-icon-time\";\n      }\n\n    },\n    setColor(val){\n      if (val){\n        return \"#2bc418\";\n      }else {\n        return \"#b3bdbb\";\n      }\n\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.id)\n      this.single = selection.length!==1\n      this.multiple = !selection.length\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.reset();\n      this.open = true;\n      this.title = \"添加流程定义\";\n    },\n    /** 流程流转记录 */\n    handleFlowRecord(row){\n      // this.$router.push({ path: '/flowable/task/record/index',\n      //   query: {\n      //     procInsId: row.procInsId,\n      //     deployId: row.deployId,\n      //     taskId: row.taskId,\n      //     finished: false\n      // }})\n      var path;\n      if(row.procDefKey == 'process_project_report'){\n        path = '/project/report/form';\n      }else if(row.procDefKey == 'process_user_reg'){\n        path = '/system/user/form';\n      }\n      this.$router.push({ path: path,\n        query: {\n          businessKey: row.businessKey,\n          procInsId: row.procInsId,\n          taskId: row.taskId,\n          finished: true\n      }})\n    },\n    /** 撤回任务 */\n    handleRevoke(row){\n      const params = {\n        instanceId: row.procInsId\n      }\n      revokeProcess(params).then( res => {\n        this.msgSuccess(res.msg);\n        this.getList();\n      });\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset();\n      const id = row.id || this.ids\n      getDeployment(id).then(response => {\n        this.form = response.data;\n        this.open = true;\n        this.title = \"修改流程定义\";\n      });\n    },\n    /** 提交按钮 */\n    submitForm() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          if (this.form.id != null) {\n            updateDeployment(this.form).then(response => {\n              this.msgSuccess(\"修改成功\");\n              this.open = false;\n              this.getList();\n            });\n          } else {\n            addDeployment(this.form).then(response => {\n              this.msgSuccess(\"新增成功\");\n              this.open = false;\n              this.getList();\n            });\n          }\n        }\n      });\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const ids = row.id || this.ids;\n      this.$confirm('是否确认删除流程定义编号为\"' + ids + '\"的数据项?', \"警告\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      }).then(function() {\n        return delDeployment(ids);\n      }).then(() => {\n        this.getList();\n        this.msgSuccess(\"删除成功\");\n      })\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      const queryParams = this.queryParams;\n      this.$confirm('是否确认导出所有流程定义数据项?', \"警告\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      }).then(function() {\n        return exportDeployment(queryParams);\n      }).then(response => {\n        this.download(response.msg);\n      })\n    }\n  }\n};\n</script>\n\n"]}]}