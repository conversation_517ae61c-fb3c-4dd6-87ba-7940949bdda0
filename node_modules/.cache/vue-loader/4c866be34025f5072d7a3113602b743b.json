{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/components/icons/index.vue?vue&type=style&index=0&id=279234be&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/components/icons/index.vue", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/css-loader/dist/cjs.js", "mtime": 1751171659443}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/loaders/stylePostLoader.js", "mtime": 1751171659924}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/postcss-loader/src/index.js", "mtime": 1751171659554}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/sass-loader/dist/cjs.js", "mtime": 1751171659051}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5pY29ucy1jb250YWluZXIgewogIG1hcmdpbjogMTBweCAyMHB4IDA7CiAgb3ZlcmZsb3c6IGhpZGRlbjsKCiAgLmljb24taXRlbSB7CiAgICBtYXJnaW46IDIwcHg7CiAgICBoZWlnaHQ6IDg1cHg7CiAgICB0ZXh0LWFsaWduOiBjZW50ZXI7CiAgICB3aWR0aDogMTAwcHg7CiAgICBmbG9hdDogbGVmdDsKICAgIGZvbnQtc2l6ZTogMzBweDsKICAgIGNvbG9yOiAjMjQyOTJlOwogICAgY3Vyc29yOiBwb2ludGVyOwogIH0KCiAgc3BhbiB7CiAgICBkaXNwbGF5OiBibG9jazsKICAgIGZvbnQtc2l6ZTogMTZweDsKICAgIG1hcmdpbi10b3A6IDEwcHg7CiAgfQoKICAuZGlzYWJsZWQgewogICAgcG9pbnRlci1ldmVudHM6IG5vbmU7CiAgfQp9Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6DA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/components/icons", "sourcesContent": ["<template>\n  <div class=\"icons-container\">\n    <aside>\n      <a href=\"#\" target=\"_blank\">Add and use\n      </a>\n    </aside>\n    <el-tabs type=\"border-card\">\n      <el-tab-pane label=\"Icons\">\n        <div v-for=\"item of svgIcons\" :key=\"item\">\n          <el-tooltip placement=\"top\">\n            <div slot=\"content\">\n              {{ generateIconCode(item) }}\n            </div>\n            <div class=\"icon-item\">\n              <svg-icon :icon-class=\"item\" class-name=\"disabled\" />\n              <span>{{ item }}</span>\n            </div>\n          </el-tooltip>\n        </div>\n      </el-tab-pane>\n      <el-tab-pane label=\"Element-UI Icons\">\n        <div v-for=\"item of elementIcons\" :key=\"item\">\n          <el-tooltip placement=\"top\">\n            <div slot=\"content\">\n              {{ generateElementIconCode(item) }}\n            </div>\n            <div class=\"icon-item\">\n              <i :class=\"'el-icon-' + item\" />\n              <span>{{ item }}</span>\n            </div>\n          </el-tooltip>\n        </div>\n      </el-tab-pane>\n    </el-tabs>\n  </div>\n</template>\n\n<script>\nimport svgIcons from './svg-icons'\nimport elementIcons from './element-icons'\n\nexport default {\n  name: 'Icons',\n  data() {\n    return {\n      svgIcons,\n      elementIcons\n    }\n  },\n  methods: {\n    generateIconCode(symbol) {\n      return `<svg-icon icon-class=\"${symbol}\" />`\n    },\n    generateElementIconCode(symbol) {\n      return `<i class=\"el-icon-${symbol}\" />`\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.icons-container {\n  margin: 10px 20px 0;\n  overflow: hidden;\n\n  .icon-item {\n    margin: 20px;\n    height: 85px;\n    text-align: center;\n    width: 100px;\n    float: left;\n    font-size: 30px;\n    color: #24292e;\n    cursor: pointer;\n  }\n\n  span {\n    display: block;\n    font-size: 16px;\n    margin-top: 10px;\n  }\n\n  .disabled {\n    pointer-events: none;\n  }\n}\n</style>\n"]}]}