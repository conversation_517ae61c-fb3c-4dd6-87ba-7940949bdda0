{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/tool/build/FormDrawer.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/tool/build/FormDrawer.vue", "mtime": 1641277326000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["FormDrawer.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8EA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "FormDrawer.vue", "sourceRoot": "src/views/tool/build", "sourcesContent": ["<template>\n  <div>\n    <el-drawer v-bind=\"$attrs\" v-on=\"$listeners\" @opened=\"onOpen\" @close=\"onClose\">\n      <div style=\"height:100%\">\n        <el-row style=\"height:100%;overflow:auto\">\n          <el-col :md=\"24\" :lg=\"12\" class=\"left-editor\">\n            <div class=\"setting\" title=\"资源引用\" @click=\"showResource\">\n              <el-badge :is-dot=\"!!resources.length\" class=\"item\">\n                <i class=\"el-icon-setting\" />\n              </el-badge>\n            </div>\n            <el-tabs v-model=\"activeTab\" type=\"card\" class=\"editor-tabs\">\n              <el-tab-pane name=\"html\">\n                <span slot=\"label\">\n                  <i v-if=\"activeTab==='html'\" class=\"el-icon-edit\" />\n                  <i v-else class=\"el-icon-document\" />\n                  template\n                </span>\n              </el-tab-pane>\n              <el-tab-pane name=\"js\">\n                <span slot=\"label\">\n                  <i v-if=\"activeTab==='js'\" class=\"el-icon-edit\" />\n                  <i v-else class=\"el-icon-document\" />\n                  script\n                </span>\n              </el-tab-pane>\n              <el-tab-pane name=\"css\">\n                <span slot=\"label\">\n                  <i v-if=\"activeTab==='css'\" class=\"el-icon-edit\" />\n                  <i v-else class=\"el-icon-document\" />\n                  css\n                </span>\n              </el-tab-pane>\n            </el-tabs>\n            <div v-show=\"activeTab==='html'\" id=\"editorHtml\" class=\"tab-editor\" />\n            <div v-show=\"activeTab==='js'\" id=\"editorJs\" class=\"tab-editor\" />\n            <div v-show=\"activeTab==='css'\" id=\"editorCss\" class=\"tab-editor\" />\n          </el-col>\n          <el-col :md=\"24\" :lg=\"12\" class=\"right-preview\">\n            <div class=\"action-bar\" :style=\"{'text-align': 'left'}\">\n              <span class=\"bar-btn\" @click=\"runCode\">\n                <i class=\"el-icon-refresh\" />\n                刷新\n              </span>\n              <span class=\"bar-btn\" @click=\"exportFile\">\n                <i class=\"el-icon-download\" />\n                导出vue文件\n              </span>\n              <span ref=\"copyBtn\" class=\"bar-btn copy-btn\">\n                <i class=\"el-icon-document-copy\" />\n                复制代码\n              </span>\n              <span class=\"bar-btn delete-btn\" @click=\"$emit('update:visible', false)\">\n                <i class=\"el-icon-circle-close\" />\n                关闭\n              </span>\n            </div>\n            <iframe\n              v-show=\"isIframeLoaded\"\n              ref=\"previewPage\"\n              class=\"result-wrapper\"\n              frameborder=\"0\"\n              src=\"preview.html\"\n              @load=\"iframeLoad\"\n            />\n            <div v-show=\"!isIframeLoaded\" v-loading=\"true\" class=\"result-wrapper\" />\n          </el-col>\n        </el-row>\n      </div>\n    </el-drawer>\n    <resource-dialog\n      :visible.sync=\"resourceVisible\"\n      :origin-resource=\"resources\"\n      @save=\"setResource\"\n    />\n  </div>\n</template>\n<script>\nimport { parse } from '@babel/parser'\nimport ClipboardJS from 'clipboard'\nimport { saveAs } from 'file-saver'\nimport {\n  makeUpHtml, vueTemplate, vueScript, cssStyle\n} from '@/utils/generator/html'\nimport { makeUpJs } from '@/utils/generator/js'\nimport { makeUpCss } from '@/utils/generator/css'\nimport { exportDefault, beautifierConf, titleCase } from '@/utils/index'\nimport ResourceDialog from './ResourceDialog'\nimport loadMonaco from '@/utils/loadMonaco'\nimport loadBeautifier from '@/utils/loadBeautifier'\n\nconst editorObj = {\n  html: null,\n  js: null,\n  css: null\n}\nconst mode = {\n  html: 'html',\n  js: 'javascript',\n  css: 'css'\n}\nlet beautifier\nlet monaco\n\nexport default {\n  components: { ResourceDialog },\n  props: ['formData', 'generateConf'],\n  data() {\n    return {\n      activeTab: 'html',\n      htmlCode: '',\n      jsCode: '',\n      cssCode: '',\n      codeFrame: '',\n      isIframeLoaded: false,\n      isInitcode: false, // 保证open后两个异步只执行一次runcode\n      isRefreshCode: false, // 每次打开都需要重新刷新代码\n      resourceVisible: false,\n      scripts: [],\n      links: [],\n      monaco: null\n    }\n  },\n  computed: {\n    resources() {\n      return this.scripts.concat(this.links)\n    }\n  },\n  watch: {},\n  created() {\n  },\n  mounted() {\n    window.addEventListener('keydown', this.preventDefaultSave)\n    const clipboard = new ClipboardJS('.copy-btn', {\n      text: trigger => {\n        const codeStr = this.generateCode()\n        this.$notify({\n          title: '成功',\n          message: '代码已复制到剪切板，可粘贴。',\n          type: 'success'\n        })\n        return codeStr\n      }\n    })\n    clipboard.on('error', e => {\n      this.$message.error('代码复制失败')\n    })\n  },\n  beforeDestroy() {\n    window.removeEventListener('keydown', this.preventDefaultSave)\n  },\n  methods: {\n    preventDefaultSave(e) {\n      if (e.key === 's' && (e.metaKey || e.ctrlKey)) {\n        e.preventDefault()\n      }\n    },\n    onOpen() {\n      const { type } = this.generateConf\n      this.htmlCode = makeUpHtml(this.formData, type)\n      this.jsCode = makeUpJs(this.formData, type)\n      this.cssCode = makeUpCss(this.formData)\n\n      loadBeautifier(btf => {\n        beautifier = btf\n        this.htmlCode = beautifier.html(this.htmlCode, beautifierConf.html)\n        this.jsCode = beautifier.js(this.jsCode, beautifierConf.js)\n        this.cssCode = beautifier.css(this.cssCode, beautifierConf.html)\n\n        loadMonaco(val => {\n          monaco = val\n          this.setEditorValue('editorHtml', 'html', this.htmlCode)\n          this.setEditorValue('editorJs', 'js', this.jsCode)\n          this.setEditorValue('editorCss', 'css', this.cssCode)\n          if (!this.isInitcode) {\n            this.isRefreshCode = true\n            this.isIframeLoaded && (this.isInitcode = true) && this.runCode()\n          }\n        })\n      })\n    },\n    onClose() {\n      this.isInitcode = false\n      this.isRefreshCode = false\n    },\n    iframeLoad() {\n      if (!this.isInitcode) {\n        this.isIframeLoaded = true\n        this.isRefreshCode && (this.isInitcode = true) && this.runCode()\n      }\n    },\n    setEditorValue(id, type, codeStr) {\n      if (editorObj[type]) {\n        editorObj[type].setValue(codeStr)\n      } else {\n        editorObj[type] = monaco.editor.create(document.getElementById(id), {\n          value: codeStr,\n          theme: 'vs-dark',\n          language: mode[type],\n          automaticLayout: true\n        })\n      }\n      // ctrl + s 刷新\n      editorObj[type].onKeyDown(e => {\n        if (e.keyCode === 49 && (e.metaKey || e.ctrlKey)) {\n          this.runCode()\n        }\n      })\n    },\n    runCode() {\n      const jsCodeStr = editorObj.js.getValue()\n      try {\n        const ast = parse(jsCodeStr, { sourceType: 'module' })\n        const astBody = ast.program.body\n        if (astBody.length > 1) {\n          this.$confirm(\n            'js格式不能识别，仅支持修改export default的对象内容',\n            '提示',\n            {\n              type: 'warning'\n            }\n          )\n          return\n        }\n        if (astBody[0].type === 'ExportDefaultDeclaration') {\n          const postData = {\n            type: 'refreshFrame',\n            data: {\n              generateConf: this.generateConf,\n              html: editorObj.html.getValue(),\n              js: jsCodeStr.replace(exportDefault, ''),\n              css: editorObj.css.getValue(),\n              scripts: this.scripts,\n              links: this.links\n            }\n          }\n\n          this.$refs.previewPage.contentWindow.postMessage(\n            postData,\n            location.origin\n          )\n        } else {\n          this.$message.error('请使用export default')\n        }\n      } catch (err) {\n        this.$message.error(`js错误：${err}`)\n        console.error(err)\n      }\n    },\n    generateCode() {\n      const html = vueTemplate(editorObj.html.getValue())\n      const script = vueScript(editorObj.js.getValue())\n      const css = cssStyle(editorObj.css.getValue())\n      return beautifier.html(html + script + css, beautifierConf.html)\n    },\n    exportFile() {\n      this.$prompt('文件名:', '导出文件', {\n        inputValue: `${+new Date()}.vue`,\n        closeOnClickModal: false,\n        inputPlaceholder: '请输入文件名'\n      }).then(({ value }) => {\n        if (!value) value = `${+new Date()}.vue`\n        const codeStr = this.generateCode()\n        const blob = new Blob([codeStr], { type: 'text/plain;charset=utf-8' })\n        saveAs(blob, value)\n      })\n    },\n    showResource() {\n      this.resourceVisible = true\n    },\n    setResource(arr) {\n      const scripts = []; const\n        links = []\n      if (Array.isArray(arr)) {\n        arr.forEach(item => {\n          if (item.endsWith('.css')) {\n            links.push(item)\n          } else {\n            scripts.push(item)\n          }\n        })\n        this.scripts = scripts\n        this.links = links\n      } else {\n        this.scripts = []\n        this.links = []\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import '@/styles/mixin.scss';\n.tab-editor {\n  position: absolute;\n  top: 33px;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  font-size: 14px;\n}\n.left-editor {\n  position: relative;\n  height: 100%;\n  background: #1e1e1e;\n  overflow: hidden;\n}\n.setting{\n  position: absolute;\n  right: 15px;\n  top: 3px;\n  color: #a9f122;\n  font-size: 18px;\n  cursor: pointer;\n  z-index: 1;\n}\n.right-preview {\n  height: 100%;\n  .result-wrapper {\n    height: calc(100vh - 33px);\n    width: 100%;\n    overflow: auto;\n    padding: 12px;\n    box-sizing: border-box;\n  }\n}\n@include action-bar;\n::v-deep .el-drawer__header {\n  display: none;\n}\n</style>\n"]}]}