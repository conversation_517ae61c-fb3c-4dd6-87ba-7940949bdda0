{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/system/menu/index.vue?vue&type=template&id=ae67b526", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/system/menu/index.vue", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/loaders/templateLoader.js", "mtime": 1751171659981}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}