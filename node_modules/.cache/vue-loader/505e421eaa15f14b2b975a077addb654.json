{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/tool/build/RightPanel.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/tool/build/RightPanel.vue", "mtime": 1641277326000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGlzQXJyYXkgfSBmcm9tICd1dGlsJwppbXBvcnQgVHJlZU5vZGVEaWFsb2cgZnJvbSAnLi9UcmVlTm9kZURpYWxvZycKaW1wb3J0IHsgaXNOdW1iZXJTdHIgfSBmcm9tICdAL3V0aWxzL2luZGV4JwppbXBvcnQgSWNvbnNEaWFsb2cgZnJvbSAnLi9JY29uc0RpYWxvZycKaW1wb3J0IHsKICBpbnB1dENvbXBvbmVudHMsIHNlbGVjdENvbXBvbmVudHMsIGxheW91dENvbXBvbmVudHMKfSBmcm9tICdAL3V0aWxzL2dlbmVyYXRvci9jb25maWcnCmltcG9ydCB7IHNhdmVGb3JtQ29uZiB9IGZyb20gJ0AvdXRpbHMvZGInCgpjb25zdCBkYXRlVGltZUZvcm1hdCA9IHsKICBkYXRlOiAneXl5eS1NTS1kZCcsCiAgd2VlazogJ3l5eXkg56ysIFdXIOWRqCcsCiAgbW9udGg6ICd5eXl5LU1NJywKICB5ZWFyOiAneXl5eScsCiAgZGF0ZXRpbWU6ICd5eXl5LU1NLWRkIEhIOm1tOnNzJywKICBkYXRlcmFuZ2U6ICd5eXl5LU1NLWRkJywKICBtb250aHJhbmdlOiAneXl5eS1NTScsCiAgZGF0ZXRpbWVyYW5nZTogJ3l5eXktTU0tZGQgSEg6bW06c3MnCn0KCi8vIOS9v2NoYW5nZVJlbmRlcktleeWcqOebruagh+e7hOS7tuaUueWPmOaXtuWPr+eUqApjb25zdCBuZWVkUmVyZW5kZXJMaXN0ID0gWyd0aW55bWNlJ10KCmV4cG9ydCBkZWZhdWx0IHsKICBjb21wb25lbnRzOiB7CiAgICBUcmVlTm9kZURpYWxvZywKICAgIEljb25zRGlhbG9nCiAgfSwKICBwcm9wczogWydzaG93RmllbGQnLCAnYWN0aXZlRGF0YScsICdmb3JtQ29uZiddLAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBjdXJyZW50VGFiOiAnZmllbGQnLAogICAgICBjdXJyZW50Tm9kZTogbnVsbCwKICAgICAgZGlhbG9nVmlzaWJsZTogZmFsc2UsCiAgICAgIGljb25zVmlzaWJsZTogZmFsc2UsCiAgICAgIGN1cnJlbnRJY29uTW9kZWw6IG51bGwsCiAgICAgIGRhdGVUeXBlT3B0aW9uczogWwogICAgICAgIHsKICAgICAgICAgIGxhYmVsOiAn5pelKGRhdGUpJywKICAgICAgICAgIHZhbHVlOiAnZGF0ZScKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIGxhYmVsOiAn5ZGoKHdlZWspJywKICAgICAgICAgIHZhbHVlOiAnd2VlaycKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIGxhYmVsOiAn5pyIKG1vbnRoKScsCiAgICAgICAgICB2YWx1ZTogJ21vbnRoJwogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgbGFiZWw6ICflubQoeWVhciknLAogICAgICAgICAgdmFsdWU6ICd5ZWFyJwogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgbGFiZWw6ICfml6XmnJ/ml7bpl7QoZGF0ZXRpbWUpJywKICAgICAgICAgIHZhbHVlOiAnZGF0ZXRpbWUnCiAgICAgICAgfQogICAgICBdLAogICAgICBkYXRlUmFuZ2VUeXBlT3B0aW9uczogWwogICAgICAgIHsKICAgICAgICAgIGxhYmVsOiAn5pel5pyf6IyD5Zu0KGRhdGVyYW5nZSknLAogICAgICAgICAgdmFsdWU6ICdkYXRlcmFuZ2UnCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBsYWJlbDogJ+aciOiMg+WbtChtb250aHJhbmdlKScsCiAgICAgICAgICB2YWx1ZTogJ21vbnRocmFuZ2UnCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBsYWJlbDogJ+aXpeacn+aXtumXtOiMg+WbtChkYXRldGltZXJhbmdlKScsCiAgICAgICAgICB2YWx1ZTogJ2RhdGV0aW1lcmFuZ2UnCiAgICAgICAgfQogICAgICBdLAogICAgICBjb2xvckZvcm1hdE9wdGlvbnM6IFsKICAgICAgICB7CiAgICAgICAgICBsYWJlbDogJ2hleCcsCiAgICAgICAgICB2YWx1ZTogJ2hleCcKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIGxhYmVsOiAncmdiJywKICAgICAgICAgIHZhbHVlOiAncmdiJwogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgbGFiZWw6ICdyZ2JhJywKICAgICAgICAgIHZhbHVlOiAncmdiYScKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIGxhYmVsOiAnaHN2JywKICAgICAgICAgIHZhbHVlOiAnaHN2JwogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgbGFiZWw6ICdoc2wnLAogICAgICAgICAgdmFsdWU6ICdoc2wnCiAgICAgICAgfQogICAgICBdLAogICAgICBqdXN0aWZ5T3B0aW9uczogWwogICAgICAgIHsKICAgICAgICAgIGxhYmVsOiAnc3RhcnQnLAogICAgICAgICAgdmFsdWU6ICdzdGFydCcKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIGxhYmVsOiAnZW5kJywKICAgICAgICAgIHZhbHVlOiAnZW5kJwogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgbGFiZWw6ICdjZW50ZXInLAogICAgICAgICAgdmFsdWU6ICdjZW50ZXInCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBsYWJlbDogJ3NwYWNlLWFyb3VuZCcsCiAgICAgICAgICB2YWx1ZTogJ3NwYWNlLWFyb3VuZCcKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIGxhYmVsOiAnc3BhY2UtYmV0d2VlbicsCiAgICAgICAgICB2YWx1ZTogJ3NwYWNlLWJldHdlZW4nCiAgICAgICAgfQogICAgICBdLAogICAgICBsYXlvdXRUcmVlUHJvcHM6IHsKICAgICAgICBsYWJlbChkYXRhLCBub2RlKSB7CiAgICAgICAgICBjb25zdCBjb25maWcgPSBkYXRhLl9fY29uZmlnX18KICAgICAgICAgIHJldHVybiBkYXRhLmNvbXBvbmVudE5hbWUgfHwgYCR7Y29uZmlnLmxhYmVsfTogJHtkYXRhLl9fdk1vZGVsX199YAogICAgICAgIH0KICAgICAgfQogICAgfQogIH0sCiAgY29tcHV0ZWQ6IHsKICAgIGRvY3VtZW50TGluaygpIHsKICAgICAgcmV0dXJuICgKICAgICAgICB0aGlzLmFjdGl2ZURhdGEuX19jb25maWdfXy5kb2N1bWVudAogICAgICAgIHx8ICdodHRwczovL2VsZW1lbnQuZWxlbWUuY24vIy96aC1DTi9jb21wb25lbnQvaW5zdGFsbGF0aW9uJwogICAgICApCiAgICB9LAogICAgZGF0ZU9wdGlvbnMoKSB7CiAgICAgIGlmICgKICAgICAgICB0aGlzLmFjdGl2ZURhdGEudHlwZSAhPT0gdW5kZWZpbmVkCiAgICAgICAgJiYgdGhpcy5hY3RpdmVEYXRhLl9fY29uZmlnX18udGFnID09PSAnZWwtZGF0ZS1waWNrZXInCiAgICAgICkgewogICAgICAgIGlmICh0aGlzLmFjdGl2ZURhdGFbJ3N0YXJ0LXBsYWNlaG9sZGVyJ10gPT09IHVuZGVmaW5lZCkgewogICAgICAgICAgcmV0dXJuIHRoaXMuZGF0ZVR5cGVPcHRpb25zCiAgICAgICAgfQogICAgICAgIHJldHVybiB0aGlzLmRhdGVSYW5nZVR5cGVPcHRpb25zCiAgICAgIH0KICAgICAgcmV0dXJuIFtdCiAgICB9LAogICAgdGFnTGlzdCgpIHsKICAgICAgcmV0dXJuIFsKICAgICAgICB7CiAgICAgICAgICBsYWJlbDogJ+i+k+WFpeWei+e7hOS7ticsCiAgICAgICAgICBvcHRpb25zOiBpbnB1dENvbXBvbmVudHMKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIGxhYmVsOiAn6YCJ5oup5Z6L57uE5Lu2JywKICAgICAgICAgIG9wdGlvbnM6IHNlbGVjdENvbXBvbmVudHMKICAgICAgICB9CiAgICAgIF0KICAgIH0sCiAgICBhY3RpdmVUYWcoKSB7CiAgICAgIHJldHVybiB0aGlzLmFjdGl2ZURhdGEuX19jb25maWdfXy50YWcKICAgIH0sCiAgICBpc1Nob3dNaW4oKSB7CiAgICAgIHJldHVybiBbJ2VsLWlucHV0LW51bWJlcicsICdlbC1zbGlkZXInXS5pbmRleE9mKHRoaXMuYWN0aXZlVGFnKSA+IC0xCiAgICB9LAogICAgaXNTaG93TWF4KCkgewogICAgICByZXR1cm4gWydlbC1pbnB1dC1udW1iZXInLCAnZWwtc2xpZGVyJywgJ2VsLXJhdGUnXS5pbmRleE9mKHRoaXMuYWN0aXZlVGFnKSA+IC0xCiAgICB9LAogICAgaXNTaG93U3RlcCgpIHsKICAgICAgcmV0dXJuIFsnZWwtaW5wdXQtbnVtYmVyJywgJ2VsLXNsaWRlciddLmluZGV4T2YodGhpcy5hY3RpdmVUYWcpID4gLTEKICAgIH0KICB9LAogIHdhdGNoOiB7CiAgICBmb3JtQ29uZjogewogICAgICBoYW5kbGVyKHZhbCkgewogICAgICAgIHNhdmVGb3JtQ29uZih2YWwpCiAgICAgIH0sCiAgICAgIGRlZXA6IHRydWUKICAgIH0KICB9LAogIG1ldGhvZHM6IHsKICAgIGFkZFJlZygpIHsKICAgICAgdGhpcy5hY3RpdmVEYXRhLl9fY29uZmlnX18ucmVnTGlzdC5wdXNoKHsKICAgICAgICBwYXR0ZXJuOiAnJywKICAgICAgICBtZXNzYWdlOiAnJwogICAgICB9KQogICAgfSwKICAgIGFkZFNlbGVjdEl0ZW0oKSB7CiAgICAgIHRoaXMuYWN0aXZlRGF0YS5fX3Nsb3RfXy5vcHRpb25zLnB1c2goewogICAgICAgIGxhYmVsOiAnJywKICAgICAgICB2YWx1ZTogJycKICAgICAgfSkKICAgIH0sCiAgICBhZGRUcmVlSXRlbSgpIHsKICAgICAgKyt0aGlzLmlkR2xvYmFsCiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHRydWUKICAgICAgdGhpcy5jdXJyZW50Tm9kZSA9IHRoaXMuYWN0aXZlRGF0YS5vcHRpb25zCiAgICB9LAogICAgcmVuZGVyQ29udGVudChoLCB7IG5vZGUsIGRhdGEsIHN0b3JlIH0pIHsKICAgICAgcmV0dXJuICgKICAgICAgICA8ZGl2IGNsYXNzPSJjdXN0b20tdHJlZS1ub2RlIj4KICAgICAgICAgIDxzcGFuPntub2RlLmxhYmVsfTwvc3Bhbj4KICAgICAgICAgIDxzcGFuIGNsYXNzPSJub2RlLW9wZXJhdGlvbiI+CiAgICAgICAgICAgIDxpIG9uLWNsaWNrPXsoKSA9PiB0aGlzLmFwcGVuZChkYXRhKX0KICAgICAgICAgICAgICBjbGFzcz0iZWwtaWNvbi1wbHVzIgogICAgICAgICAgICAgIHRpdGxlPSLmt7vliqAiCiAgICAgICAgICAgID48L2k+CiAgICAgICAgICAgIDxpIG9uLWNsaWNrPXsoKSA9PiB0aGlzLnJlbW92ZShub2RlLCBkYXRhKX0KICAgICAgICAgICAgICBjbGFzcz0iZWwtaWNvbi1kZWxldGUiCiAgICAgICAgICAgICAgdGl0bGU9IuWIoOmZpCIKICAgICAgICAgICAgPjwvaT4KICAgICAgICAgIDwvc3Bhbj4KICAgICAgICA8L2Rpdj4KICAgICAgKQogICAgfSwKICAgIGFwcGVuZChkYXRhKSB7CiAgICAgIGlmICghZGF0YS5jaGlsZHJlbikgewogICAgICAgIHRoaXMuJHNldChkYXRhLCAnY2hpbGRyZW4nLCBbXSkKICAgICAgfQogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSB0cnVlCiAgICAgIHRoaXMuY3VycmVudE5vZGUgPSBkYXRhLmNoaWxkcmVuCiAgICB9LAogICAgcmVtb3ZlKG5vZGUsIGRhdGEpIHsKICAgICAgdGhpcy5hY3RpdmVEYXRhLl9fY29uZmlnX18uZGVmYXVsdFZhbHVlID0gW10gLy8g6YG/5YWN5Yig6Zmk5pe25oql6ZSZCiAgICAgIGNvbnN0IHsgcGFyZW50IH0gPSBub2RlCiAgICAgIGNvbnN0IGNoaWxkcmVuID0gcGFyZW50LmRhdGEuY2hpbGRyZW4gfHwgcGFyZW50LmRhdGEKICAgICAgY29uc3QgaW5kZXggPSBjaGlsZHJlbi5maW5kSW5kZXgoZCA9PiBkLmlkID09PSBkYXRhLmlkKQogICAgICBjaGlsZHJlbi5zcGxpY2UoaW5kZXgsIDEpCiAgICB9LAogICAgYWRkTm9kZShkYXRhKSB7CiAgICAgIHRoaXMuY3VycmVudE5vZGUucHVzaChkYXRhKQogICAgfSwKICAgIHNldE9wdGlvblZhbHVlKGl0ZW0sIHZhbCkgewogICAgICBpdGVtLnZhbHVlID0gaXNOdW1iZXJTdHIodmFsKSA/ICt2YWwgOiB2YWwKICAgIH0sCiAgICBzZXREZWZhdWx0VmFsdWUodmFsKSB7CiAgICAgIGlmIChBcnJheS5pc0FycmF5KHZhbCkpIHsKICAgICAgICByZXR1cm4gdmFsLmpvaW4oJywnKQogICAgICB9CiAgICAgIC8vIGlmIChbJ3N0cmluZycsICdudW1iZXInXS5pbmRleE9mKHR5cGVvZiB2YWwpID4gLTEpIHsKICAgICAgLy8gICByZXR1cm4gdmFsCiAgICAgIC8vIH0KICAgICAgaWYgKHR5cGVvZiB2YWwgPT09ICdib29sZWFuJykgewogICAgICAgIHJldHVybiBgJHt2YWx9YAogICAgICB9CiAgICAgIHJldHVybiB2YWwKICAgIH0sCiAgICBvbkRlZmF1bHRWYWx1ZUlucHV0KHN0cikgewogICAgICBpZiAoaXNBcnJheSh0aGlzLmFjdGl2ZURhdGEuX19jb25maWdfXy5kZWZhdWx0VmFsdWUpKSB7CiAgICAgICAgLy8g5pWw57uECiAgICAgICAgdGhpcy4kc2V0KAogICAgICAgICAgdGhpcy5hY3RpdmVEYXRhLl9fY29uZmlnX18sCiAgICAgICAgICAnZGVmYXVsdFZhbHVlJywKICAgICAgICAgIHN0ci5zcGxpdCgnLCcpLm1hcCh2YWwgPT4gKGlzTnVtYmVyU3RyKHZhbCkgPyArdmFsIDogdmFsKSkKICAgICAgICApCiAgICAgIH0gZWxzZSBpZiAoWyd0cnVlJywgJ2ZhbHNlJ10uaW5kZXhPZihzdHIpID4gLTEpIHsKICAgICAgICAvLyDluIPlsJQKICAgICAgICB0aGlzLiRzZXQodGhpcy5hY3RpdmVEYXRhLl9fY29uZmlnX18sICdkZWZhdWx0VmFsdWUnLCBKU09OLnBhcnNlKHN0cikpCiAgICAgIH0gZWxzZSB7CiAgICAgICAgLy8g5a2X56ym5Liy5ZKM5pWw5a2XCiAgICAgICAgdGhpcy4kc2V0KAogICAgICAgICAgdGhpcy5hY3RpdmVEYXRhLl9fY29uZmlnX18sCiAgICAgICAgICAnZGVmYXVsdFZhbHVlJywKICAgICAgICAgIGlzTnVtYmVyU3RyKHN0cikgPyArc3RyIDogc3RyCiAgICAgICAgKQogICAgICB9CiAgICB9LAogICAgb25Td2l0Y2hWYWx1ZUlucHV0KHZhbCwgbmFtZSkgewogICAgICBpZiAoWyd0cnVlJywgJ2ZhbHNlJ10uaW5kZXhPZih2YWwpID4gLTEpIHsKICAgICAgICB0aGlzLiRzZXQodGhpcy5hY3RpdmVEYXRhLCBuYW1lLCBKU09OLnBhcnNlKHZhbCkpCiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy4kc2V0KHRoaXMuYWN0aXZlRGF0YSwgbmFtZSwgaXNOdW1iZXJTdHIodmFsKSA/ICt2YWwgOiB2YWwpCiAgICAgIH0KICAgIH0sCiAgICBzZXRUaW1lVmFsdWUodmFsLCB0eXBlKSB7CiAgICAgIGNvbnN0IHZhbHVlRm9ybWF0ID0gdHlwZSA9PT0gJ3dlZWsnID8gZGF0ZVRpbWVGb3JtYXQuZGF0ZSA6IHZhbAogICAgICB0aGlzLiRzZXQodGhpcy5hY3RpdmVEYXRhLl9fY29uZmlnX18sICdkZWZhdWx0VmFsdWUnLCBudWxsKQogICAgICB0aGlzLiRzZXQodGhpcy5hY3RpdmVEYXRhLCAndmFsdWUtZm9ybWF0JywgdmFsdWVGb3JtYXQpCiAgICAgIHRoaXMuJHNldCh0aGlzLmFjdGl2ZURhdGEsICdmb3JtYXQnLCB2YWwpCiAgICB9LAogICAgc3BhbkNoYW5nZSh2YWwpIHsKICAgICAgdGhpcy5mb3JtQ29uZi5zcGFuID0gdmFsCiAgICB9LAogICAgbXVsdGlwbGVDaGFuZ2UodmFsKSB7CiAgICAgIHRoaXMuJHNldCh0aGlzLmFjdGl2ZURhdGEuX19jb25maWdfXywgJ2RlZmF1bHRWYWx1ZScsIHZhbCA/IFtdIDogJycpCiAgICB9LAogICAgZGF0ZVR5cGVDaGFuZ2UodmFsKSB7CiAgICAgIHRoaXMuc2V0VGltZVZhbHVlKGRhdGVUaW1lRm9ybWF0W3ZhbF0sIHZhbCkKICAgIH0sCiAgICByYW5nZUNoYW5nZSh2YWwpIHsKICAgICAgdGhpcy4kc2V0KAogICAgICAgIHRoaXMuYWN0aXZlRGF0YS5fX2NvbmZpZ19fLAogICAgICAgICdkZWZhdWx0VmFsdWUnLAogICAgICAgIHZhbCA/IFt0aGlzLmFjdGl2ZURhdGEubWluLCB0aGlzLmFjdGl2ZURhdGEubWF4XSA6IHRoaXMuYWN0aXZlRGF0YS5taW4KICAgICAgKQogICAgfSwKICAgIHJhdGVUZXh0Q2hhbmdlKHZhbCkgewogICAgICBpZiAodmFsKSB0aGlzLmFjdGl2ZURhdGFbJ3Nob3ctc2NvcmUnXSA9IGZhbHNlCiAgICB9LAogICAgcmF0ZVNjb3JlQ2hhbmdlKHZhbCkgewogICAgICBpZiAodmFsKSB0aGlzLmFjdGl2ZURhdGFbJ3Nob3ctdGV4dCddID0gZmFsc2UKICAgIH0sCiAgICBjb2xvckZvcm1hdENoYW5nZSh2YWwpIHsKICAgICAgdGhpcy5hY3RpdmVEYXRhLl9fY29uZmlnX18uZGVmYXVsdFZhbHVlID0gbnVsbAogICAgICB0aGlzLmFjdGl2ZURhdGFbJ3Nob3ctYWxwaGEnXSA9IHZhbC5pbmRleE9mKCdhJykgPiAtMQogICAgICB0aGlzLmFjdGl2ZURhdGEuX19jb25maWdfXy5yZW5kZXJLZXkgPSArbmV3IERhdGUoKSAvLyDmm7TmlrByZW5kZXJLZXks6YeN5paw5riy5p+T6K+l57uE5Lu2CiAgICB9LAogICAgb3Blbkljb25zRGlhbG9nKG1vZGVsKSB7CiAgICAgIHRoaXMuaWNvbnNWaXNpYmxlID0gdHJ1ZQogICAgICB0aGlzLmN1cnJlbnRJY29uTW9kZWwgPSBtb2RlbAogICAgfSwKICAgIHNldEljb24odmFsKSB7CiAgICAgIHRoaXMuYWN0aXZlRGF0YVt0aGlzLmN1cnJlbnRJY29uTW9kZWxdID0gdmFsCiAgICB9LAogICAgdGFnQ2hhbmdlKHRhZ0ljb24pIHsKICAgICAgbGV0IHRhcmdldCA9IGlucHV0Q29tcG9uZW50cy5maW5kKGl0ZW0gPT4gaXRlbS5fX2NvbmZpZ19fLnRhZ0ljb24gPT09IHRhZ0ljb24pCiAgICAgIGlmICghdGFyZ2V0KSB0YXJnZXQgPSBzZWxlY3RDb21wb25lbnRzLmZpbmQoaXRlbSA9PiBpdGVtLl9fY29uZmlnX18udGFnSWNvbiA9PT0gdGFnSWNvbikKICAgICAgdGhpcy4kZW1pdCgndGFnLWNoYW5nZScsIHRhcmdldCkKICAgIH0sCiAgICBjaGFuZ2VSZW5kZXJLZXkoKSB7CiAgICAgIGlmIChuZWVkUmVyZW5kZXJMaXN0LmluY2x1ZGVzKHRoaXMuYWN0aXZlRGF0YS5fX2NvbmZpZ19fLnRhZykpIHsKICAgICAgICB0aGlzLmFjdGl2ZURhdGEuX19jb25maWdfXy5yZW5kZXJLZXkgPSArbmV3IERhdGUoKQogICAgICB9CiAgICB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["RightPanel.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2oBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "RightPanel.vue", "sourceRoot": "src/views/tool/build", "sourcesContent": ["<template>\n  <div class=\"right-board\">\n    <el-tabs v-model=\"currentTab\" class=\"center-tabs\">\n      <el-tab-pane label=\"组件属性\" name=\"field\" />\n      <el-tab-pane label=\"表单属性\" name=\"form\" />\n    </el-tabs>\n    <div class=\"field-box\">\n      <a class=\"document-link\" target=\"_blank\" :href=\"documentLink\" title=\"查看组件文档\">\n        <i class=\"el-icon-link\" />\n      </a>\n      <el-scrollbar class=\"right-scrollbar\">\n        <!-- 组件属性 -->\n        <el-form v-show=\"currentTab==='field' && showField\" size=\"small\" label-width=\"90px\">\n          <el-form-item v-if=\"activeData.__config__.changeTag\" label=\"组件类型\">\n            <el-select\n              v-model=\"activeData.__config__.tagIcon\"\n              placeholder=\"请选择组件类型\"\n              :style=\"{width: '100%'}\"\n              @change=\"tagChange\"\n            >\n              <el-option-group v-for=\"group in tagList\" :key=\"group.label\" :label=\"group.label\">\n                <el-option\n                  v-for=\"item in group.options\"\n                  :key=\"item.__config__.label\"\n                  :label=\"item.__config__.label\"\n                  :value=\"item.__config__.tagIcon\"\n                >\n                  <svg-icon class=\"node-icon\" :icon-class=\"item.__config__.tagIcon\" />\n                  <span> {{ item.__config__.label }}</span>\n                </el-option>\n              </el-option-group>\n            </el-select>\n          </el-form-item>\n          <el-form-item v-if=\"activeData.__vModel__!==undefined\" label=\"字段名\">\n            <el-input v-model=\"activeData.__vModel__\" placeholder=\"请输入字段名（v-model）\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.__config__.componentName!==undefined\" label=\"组件名\">\n            {{ activeData.__config__.componentName }}\n          </el-form-item>\n          <el-form-item v-if=\"activeData.__config__.label!==undefined\" label=\"标题\">\n            <el-input v-model=\"activeData.__config__.label\" placeholder=\"请输入标题\" @input=\"changeRenderKey\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.placeholder!==undefined\" label=\"占位提示\">\n            <el-input v-model=\"activeData.placeholder\" placeholder=\"请输入占位提示\" @input=\"changeRenderKey\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData['start-placeholder']!==undefined\" label=\"开始占位\">\n            <el-input v-model=\"activeData['start-placeholder']\" placeholder=\"请输入占位提示\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData['end-placeholder']!==undefined\" label=\"结束占位\">\n            <el-input v-model=\"activeData['end-placeholder']\" placeholder=\"请输入占位提示\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.__config__.span!==undefined\" label=\"表单栅格\">\n            <el-slider v-model=\"activeData.__config__.span\" :max=\"24\" :min=\"1\" :marks=\"{12:''}\" @change=\"spanChange\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.__config__.layout==='rowFormItem'&&activeData.gutter!==undefined\" label=\"栅格间隔\">\n            <el-input-number v-model=\"activeData.gutter\" :min=\"0\" placeholder=\"栅格间隔\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.__config__.layout==='rowFormItem'&&activeData.type!==undefined\" label=\"布局模式\">\n            <el-radio-group v-model=\"activeData.type\">\n              <el-radio-button label=\"default\" />\n              <el-radio-button label=\"flex\" />\n            </el-radio-group>\n          </el-form-item>\n          <el-form-item v-if=\"activeData.justify!==undefined&&activeData.type==='flex'\" label=\"水平排列\">\n            <el-select v-model=\"activeData.justify\" placeholder=\"请选择水平排列\" :style=\"{width: '100%'}\">\n              <el-option\n                v-for=\"(item, index) in justifyOptions\"\n                :key=\"index\"\n                :label=\"item.label\"\n                :value=\"item.value\"\n              />\n            </el-select>\n          </el-form-item>\n          <el-form-item v-if=\"activeData.align!==undefined&&activeData.type==='flex'\" label=\"垂直排列\">\n            <el-radio-group v-model=\"activeData.align\">\n              <el-radio-button label=\"top\" />\n              <el-radio-button label=\"middle\" />\n              <el-radio-button label=\"bottom\" />\n            </el-radio-group>\n          </el-form-item>\n          <el-form-item v-if=\"activeData.__config__.labelWidth!==undefined\" label=\"标签宽度\">\n            <el-input v-model.number=\"activeData.__config__.labelWidth\" type=\"number\" placeholder=\"请输入标签宽度\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.style&&activeData.style.width!==undefined\" label=\"组件宽度\">\n            <el-input v-model=\"activeData.style.width\" placeholder=\"请输入组件宽度\" clearable />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.__vModel__!==undefined\" label=\"默认值\">\n            <el-input\n              :value=\"setDefaultValue(activeData.__config__.defaultValue)\"\n              placeholder=\"请输入默认值\"\n              @input=\"onDefaultValueInput\"\n            />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.__config__.tag==='el-checkbox-group'\" label=\"至少应选\">\n            <el-input-number\n              :value=\"activeData.min\"\n              :min=\"0\"\n              placeholder=\"至少应选\"\n              @input=\"$set(activeData, 'min', $event?$event:undefined)\"\n            />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.__config__.tag==='el-checkbox-group'\" label=\"最多可选\">\n            <el-input-number\n              :value=\"activeData.max\"\n              :min=\"0\"\n              placeholder=\"最多可选\"\n              @input=\"$set(activeData, 'max', $event?$event:undefined)\"\n            />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.__slot__&&activeData.__slot__.prepend!==undefined\" label=\"前缀\">\n            <el-input v-model=\"activeData.__slot__.prepend\" placeholder=\"请输入前缀\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.__slot__&&activeData.__slot__.append!==undefined\" label=\"后缀\">\n            <el-input v-model=\"activeData.__slot__.append\" placeholder=\"请输入后缀\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData['prefix-icon']!==undefined\" label=\"前图标\">\n            <el-input v-model=\"activeData['prefix-icon']\" placeholder=\"请输入前图标名称\">\n              <el-button slot=\"append\" icon=\"el-icon-thumb\" @click=\"openIconsDialog('prefix-icon')\">\n                选择\n              </el-button>\n            </el-input>\n          </el-form-item>\n          <el-form-item v-if=\"activeData['suffix-icon'] !== undefined\" label=\"后图标\">\n            <el-input v-model=\"activeData['suffix-icon']\" placeholder=\"请输入后图标名称\">\n              <el-button slot=\"append\" icon=\"el-icon-thumb\" @click=\"openIconsDialog('suffix-icon')\">\n                选择\n              </el-button>\n            </el-input>\n          </el-form-item>\n          <el-form-item\n            v-if=\"activeData['icon']!==undefined && activeData.__config__.tag === 'el-button'\"\n            label=\"按钮图标\"\n          >\n            <el-input v-model=\"activeData['icon']\" placeholder=\"请输入按钮图标名称\">\n              <el-button slot=\"append\" icon=\"el-icon-thumb\" @click=\"openIconsDialog('icon')\">\n                选择\n              </el-button>\n            </el-input>\n          </el-form-item>\n          <el-form-item v-if=\"activeData.__config__.tag === 'el-cascader'\" label=\"选项分隔符\">\n            <el-input v-model=\"activeData.separator\" placeholder=\"请输入选项分隔符\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.autosize !== undefined\" label=\"最小行数\">\n            <el-input-number v-model=\"activeData.autosize.minRows\" :min=\"1\" placeholder=\"最小行数\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.autosize !== undefined\" label=\"最大行数\">\n            <el-input-number v-model=\"activeData.autosize.maxRows\" :min=\"1\" placeholder=\"最大行数\" />\n          </el-form-item>\n          <el-form-item v-if=\"isShowMin\" label=\"最小值\">\n            <el-input-number v-model=\"activeData.min\" placeholder=\"最小值\" />\n          </el-form-item>\n          <el-form-item v-if=\"isShowMax\" label=\"最大值\">\n            <el-input-number v-model=\"activeData.max\" placeholder=\"最大值\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.height!==undefined\" label=\"组件高度\">\n            <el-input-number v-model=\"activeData.height\" placeholder=\"高度\" @input=\"changeRenderKey\" />\n          </el-form-item>\n          <el-form-item v-if=\"isShowStep\" label=\"步长\">\n            <el-input-number v-model=\"activeData.step\" placeholder=\"步数\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.__config__.tag === 'el-input-number'\" label=\"精度\">\n            <el-input-number v-model=\"activeData.precision\" :min=\"0\" placeholder=\"精度\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.__config__.tag === 'el-input-number'\" label=\"按钮位置\">\n            <el-radio-group v-model=\"activeData['controls-position']\">\n              <el-radio-button label=\"\">\n                默认\n              </el-radio-button>\n              <el-radio-button label=\"right\">\n                右侧\n              </el-radio-button>\n            </el-radio-group>\n          </el-form-item>\n          <el-form-item v-if=\"activeData.maxlength !== undefined\" label=\"最多输入\">\n            <el-input v-model=\"activeData.maxlength\" placeholder=\"请输入字符长度\">\n              <template slot=\"append\">\n                个字符\n              </template>\n            </el-input>\n          </el-form-item>\n          <el-form-item v-if=\"activeData['active-text'] !== undefined\" label=\"开启提示\">\n            <el-input v-model=\"activeData['active-text']\" placeholder=\"请输入开启提示\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData['inactive-text'] !== undefined\" label=\"关闭提示\">\n            <el-input v-model=\"activeData['inactive-text']\" placeholder=\"请输入关闭提示\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData['active-value'] !== undefined\" label=\"开启值\">\n            <el-input\n              :value=\"setDefaultValue(activeData['active-value'])\"\n              placeholder=\"请输入开启值\"\n              @input=\"onSwitchValueInput($event, 'active-value')\"\n            />\n          </el-form-item>\n          <el-form-item v-if=\"activeData['inactive-value'] !== undefined\" label=\"关闭值\">\n            <el-input\n              :value=\"setDefaultValue(activeData['inactive-value'])\"\n              placeholder=\"请输入关闭值\"\n              @input=\"onSwitchValueInput($event, 'inactive-value')\"\n            />\n          </el-form-item>\n          <el-form-item\n            v-if=\"activeData.type !== undefined && 'el-date-picker' === activeData.__config__.tag\"\n            label=\"时间类型\"\n          >\n            <el-select\n              v-model=\"activeData.type\"\n              placeholder=\"请选择时间类型\"\n              :style=\"{ width: '100%' }\"\n              @change=\"dateTypeChange\"\n            >\n              <el-option\n                v-for=\"(item, index) in dateOptions\"\n                :key=\"index\"\n                :label=\"item.label\"\n                :value=\"item.value\"\n              />\n            </el-select>\n          </el-form-item>\n          <el-form-item v-if=\"activeData.name !== undefined\" label=\"文件字段名\">\n            <el-input v-model=\"activeData.name\" placeholder=\"请输入上传文件字段名\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.accept !== undefined\" label=\"文件类型\">\n            <el-select\n              v-model=\"activeData.accept\"\n              placeholder=\"请选择文件类型\"\n              :style=\"{ width: '100%' }\"\n              clearable\n            >\n              <el-option label=\"图片\" value=\"image/*\" />\n              <el-option label=\"视频\" value=\"video/*\" />\n              <el-option label=\"音频\" value=\"audio/*\" />\n              <el-option label=\"excel\" value=\".xls,.xlsx\" />\n              <el-option label=\"word\" value=\".doc,.docx\" />\n              <el-option label=\"pdf\" value=\".pdf\" />\n              <el-option label=\"txt\" value=\".txt\" />\n            </el-select>\n          </el-form-item>\n          <el-form-item v-if=\"activeData.__config__.fileSize !== undefined\" label=\"文件大小\">\n            <el-input v-model.number=\"activeData.__config__.fileSize\" placeholder=\"请输入文件大小\">\n              <el-select slot=\"append\" v-model=\"activeData.__config__.sizeUnit\" :style=\"{ width: '66px' }\">\n                <el-option label=\"KB\" value=\"KB\" />\n                <el-option label=\"MB\" value=\"MB\" />\n                <el-option label=\"GB\" value=\"GB\" />\n              </el-select>\n            </el-input>\n          </el-form-item>\n          <el-form-item v-if=\"activeData.action !== undefined\" label=\"上传地址\">\n            <el-input v-model=\"activeData.action\" placeholder=\"请输入上传地址\" clearable />\n          </el-form-item>\n          <el-form-item v-if=\"activeData['list-type'] !== undefined\" label=\"列表类型\">\n            <el-radio-group v-model=\"activeData['list-type']\" size=\"small\">\n              <el-radio-button label=\"text\">\n                text\n              </el-radio-button>\n              <el-radio-button label=\"picture\">\n                picture\n              </el-radio-button>\n              <el-radio-button label=\"picture-card\">\n                picture-card\n              </el-radio-button>\n            </el-radio-group>\n          </el-form-item>\n          <el-form-item\n            v-if=\"activeData.type !== undefined && activeData.__config__.tag === 'el-button'\"\n            label=\"按钮类型\"\n          >\n            <el-select v-model=\"activeData.type\" :style=\"{ width: '100%' }\">\n              <el-option label=\"primary\" value=\"primary\" />\n              <el-option label=\"success\" value=\"success\" />\n              <el-option label=\"warning\" value=\"warning\" />\n              <el-option label=\"danger\" value=\"danger\" />\n              <el-option label=\"info\" value=\"info\" />\n              <el-option label=\"text\" value=\"text\" />\n            </el-select>\n          </el-form-item>\n          <el-form-item\n            v-if=\"activeData.__config__.buttonText !== undefined\"\n            v-show=\"'picture-card' !== activeData['list-type']\"\n            label=\"按钮文字\"\n          >\n            <el-input v-model=\"activeData.__config__.buttonText\" placeholder=\"请输入按钮文字\" />\n          </el-form-item>\n          <el-form-item\n            v-if=\"activeData.__config__.tag === 'el-button'\"\n            label=\"按钮文字\"\n          >\n            <el-input v-model=\"activeData.__slot__.default\" placeholder=\"请输入按钮文字\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData['range-separator'] !== undefined\" label=\"分隔符\">\n            <el-input v-model=\"activeData['range-separator']\" placeholder=\"请输入分隔符\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData['picker-options'] !== undefined\" label=\"时间段\">\n            <el-input\n              v-model=\"activeData['picker-options'].selectableRange\"\n              placeholder=\"请输入时间段\"\n            />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.format !== undefined\" label=\"时间格式\">\n            <el-input\n              :value=\"activeData.format\"\n              placeholder=\"请输入时间格式\"\n              @input=\"setTimeValue($event)\"\n            />\n          </el-form-item>\n          <template v-if=\"['el-checkbox-group', 'el-radio-group', 'el-select'].indexOf(activeData.__config__.tag) > -1\">\n            <el-divider>选项</el-divider>\n            <draggable\n              :list=\"activeData.__slot__.options\"\n              :animation=\"340\"\n              group=\"selectItem\"\n              handle=\".option-drag\"\n            >\n              <div v-for=\"(item, index) in activeData.__slot__.options\" :key=\"index\" class=\"select-item\">\n                <div class=\"select-line-icon option-drag\">\n                  <i class=\"el-icon-s-operation\" />\n                </div>\n                <el-input v-model=\"item.label\" placeholder=\"选项名\" size=\"small\" />\n                <el-input\n                  placeholder=\"选项值\"\n                  size=\"small\"\n                  :value=\"item.value\"\n                  @input=\"setOptionValue(item, $event)\"\n                />\n                <div class=\"close-btn select-line-icon\" @click=\"activeData.__slot__.options.splice(index, 1)\">\n                  <i class=\"el-icon-remove-outline\" />\n                </div>\n              </div>\n            </draggable>\n            <div style=\"margin-left: 20px;\">\n              <el-button\n                style=\"padding-bottom: 0\"\n                icon=\"el-icon-circle-plus-outline\"\n                type=\"text\"\n                @click=\"addSelectItem\"\n              >\n                添加选项\n              </el-button>\n            </div>\n            <el-divider />\n          </template>\n\n          <template v-if=\"['el-cascader', 'el-table'].includes(activeData.__config__.tag)\">\n            <el-divider>选项</el-divider>\n            <el-form-item v-if=\"activeData.__config__.dataType\" label=\"数据类型\">\n              <el-radio-group v-model=\"activeData.__config__.dataType\" size=\"small\">\n                <el-radio-button label=\"dynamic\">\n                  动态数据\n                </el-radio-button>\n                <el-radio-button label=\"static\">\n                  静态数据\n                </el-radio-button>\n              </el-radio-group>\n            </el-form-item>\n\n            <template v-if=\"activeData.__config__.dataType === 'dynamic'\">\n              <el-form-item label=\"接口地址\">\n                <el-input\n                  v-model=\"activeData.__config__.url\"\n                  :title=\"activeData.__config__.url\"\n                  placeholder=\"请输入接口地址\"\n                  clearable\n                  @blur=\"$emit('fetch-data', activeData)\"\n                >\n                  <el-select\n                    slot=\"prepend\"\n                    v-model=\"activeData.__config__.method\"\n                    :style=\"{width: '85px'}\"\n                    @change=\"$emit('fetch-data', activeData)\"\n                  >\n                    <el-option label=\"get\" value=\"get\" />\n                    <el-option label=\"post\" value=\"post\" />\n                    <el-option label=\"put\" value=\"put\" />\n                    <el-option label=\"delete\" value=\"delete\" />\n                  </el-select>\n                </el-input>\n              </el-form-item>\n              <el-form-item label=\"数据位置\">\n                <el-input\n                  v-model=\"activeData.__config__.dataPath\"\n                  placeholder=\"请输入数据位置\"\n                  @blur=\"$emit('fetch-data', activeData)\"\n                />\n              </el-form-item>\n\n              <template v-if=\"activeData.props && activeData.props.props\">\n                <el-form-item label=\"标签键名\">\n                  <el-input v-model=\"activeData.props.props.label\" placeholder=\"请输入标签键名\" />\n                </el-form-item>\n                <el-form-item label=\"值键名\">\n                  <el-input v-model=\"activeData.props.props.value\" placeholder=\"请输入值键名\" />\n                </el-form-item>\n                <el-form-item label=\"子级键名\">\n                  <el-input v-model=\"activeData.props.props.children\" placeholder=\"请输入子级键名\" />\n                </el-form-item>\n              </template>\n            </template>\n\n            <!-- 级联选择静态树 -->\n            <el-tree\n              v-if=\"activeData.__config__.dataType === 'static'\"\n              draggable\n              :data=\"activeData.options\"\n              node-key=\"id\"\n              :expand-on-click-node=\"false\"\n              :render-content=\"renderContent\"\n            />\n            <div v-if=\"activeData.__config__.dataType === 'static'\" style=\"margin-left: 20px\">\n              <el-button\n                style=\"padding-bottom: 0\"\n                icon=\"el-icon-circle-plus-outline\"\n                type=\"text\"\n                @click=\"addTreeItem\"\n              >\n                添加父级\n              </el-button>\n            </div>\n            <el-divider />\n          </template>\n\n          <el-form-item v-if=\"activeData.__config__.optionType !== undefined\" label=\"选项样式\">\n            <el-radio-group v-model=\"activeData.__config__.optionType\">\n              <el-radio-button label=\"default\">\n                默认\n              </el-radio-button>\n              <el-radio-button label=\"button\">\n                按钮\n              </el-radio-button>\n            </el-radio-group>\n          </el-form-item>\n          <el-form-item v-if=\"activeData['active-color'] !== undefined\" label=\"开启颜色\">\n            <el-color-picker v-model=\"activeData['active-color']\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData['inactive-color'] !== undefined\" label=\"关闭颜色\">\n            <el-color-picker v-model=\"activeData['inactive-color']\" />\n          </el-form-item>\n\n          <el-form-item v-if=\"activeData.__config__.showLabel !== undefined\n            && activeData.__config__.labelWidth !== undefined\" label=\"显示标签\"\n          >\n            <el-switch v-model=\"activeData.__config__.showLabel\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.branding !== undefined\" label=\"品牌烙印\">\n            <el-switch v-model=\"activeData.branding\" @input=\"changeRenderKey\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData['allow-half'] !== undefined\" label=\"允许半选\">\n            <el-switch v-model=\"activeData['allow-half']\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData['show-text'] !== undefined\" label=\"辅助文字\">\n            <el-switch v-model=\"activeData['show-text']\" @change=\"rateTextChange\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData['show-score'] !== undefined\" label=\"显示分数\">\n            <el-switch v-model=\"activeData['show-score']\" @change=\"rateScoreChange\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData['show-stops'] !== undefined\" label=\"显示间断点\">\n            <el-switch v-model=\"activeData['show-stops']\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.range !== undefined\" label=\"范围选择\">\n            <el-switch v-model=\"activeData.range\" @change=\"rangeChange\" />\n          </el-form-item>\n          <el-form-item\n            v-if=\"activeData.__config__.border !== undefined && activeData.__config__.optionType === 'default'\"\n            label=\"是否带边框\"\n          >\n            <el-switch v-model=\"activeData.__config__.border\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.__config__.tag === 'el-color-picker'\" label=\"颜色格式\">\n            <el-select\n              v-model=\"activeData['color-format']\"\n              placeholder=\"请选择颜色格式\"\n              :style=\"{ width: '100%' }\"\n              clearable\n              @change=\"colorFormatChange\"\n            >\n              <el-option\n                v-for=\"(item, index) in colorFormatOptions\"\n                :key=\"index\"\n                :label=\"item.label\"\n                :value=\"item.value\"\n              />\n            </el-select>\n          </el-form-item>\n          <el-form-item\n            v-if=\"activeData.size !== undefined &&\n              (activeData.__config__.optionType === 'button' ||\n                activeData.__config__.border ||\n                activeData.__config__.tag === 'el-color-picker' ||\n                activeData.__config__.tag === 'el-button')\"\n            label=\"组件尺寸\"\n          >\n            <el-radio-group v-model=\"activeData.size\">\n              <el-radio-button label=\"medium\">\n                中等\n              </el-radio-button>\n              <el-radio-button label=\"small\">\n                较小\n              </el-radio-button>\n              <el-radio-button label=\"mini\">\n                迷你\n              </el-radio-button>\n            </el-radio-group>\n          </el-form-item>\n          <el-form-item v-if=\"activeData['show-word-limit'] !== undefined\" label=\"输入统计\">\n            <el-switch v-model=\"activeData['show-word-limit']\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.__config__.tag === 'el-input-number'\" label=\"严格步数\">\n            <el-switch v-model=\"activeData['step-strictly']\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.__config__.tag === 'el-cascader'\" label=\"任选层级\">\n            <el-switch v-model=\"activeData.props.props.checkStrictly\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.__config__.tag === 'el-cascader'\" label=\"是否多选\">\n            <el-switch v-model=\"activeData.props.props.multiple\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.__config__.tag === 'el-cascader'\" label=\"展示全路径\">\n            <el-switch v-model=\"activeData['show-all-levels']\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.__config__.tag === 'el-cascader'\" label=\"可否筛选\">\n            <el-switch v-model=\"activeData.filterable\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.clearable !== undefined\" label=\"能否清空\">\n            <el-switch v-model=\"activeData.clearable\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.__config__.showTip !== undefined\" label=\"显示提示\">\n            <el-switch v-model=\"activeData.__config__.showTip\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.__config__.tag === 'el-upload'\" label=\"多选文件\">\n            <el-switch v-model=\"activeData.multiple\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData['auto-upload'] !== undefined\" label=\"自动上传\">\n            <el-switch v-model=\"activeData['auto-upload']\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.readonly !== undefined\" label=\"是否只读\">\n            <el-switch v-model=\"activeData.readonly\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.disabled !== undefined\" label=\"是否禁用\">\n            <el-switch v-model=\"activeData.disabled\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.__config__.tag === 'el-select'\" label=\"能否搜索\">\n            <el-switch v-model=\"activeData.filterable\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.__config__.tag === 'el-select'\" label=\"是否多选\">\n            <el-switch v-model=\"activeData.multiple\" @change=\"multipleChange\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.__config__.required !== undefined\" label=\"是否必填\">\n            <el-switch v-model=\"activeData.__config__.required\" />\n          </el-form-item>\n\n          <template v-if=\"activeData.__config__.layoutTree\">\n            <el-divider>布局结构树</el-divider>\n            <el-tree\n              :data=\"[activeData.__config__]\"\n              :props=\"layoutTreeProps\"\n              node-key=\"renderKey\"\n              default-expand-all\n              draggable\n            >\n              <span slot-scope=\"{ node, data }\">\n                <span class=\"node-label\">\n                  <svg-icon class=\"node-icon\" :icon-class=\"data.__config__?data.__config__.tagIcon:data.tagIcon\" />\n                  {{ node.label }}\n                </span>\n              </span>\n            </el-tree>\n          </template>\n\n          <template v-if=\"Array.isArray(activeData.__config__.regList)\">\n            <el-divider>正则校验</el-divider>\n            <div\n              v-for=\"(item, index) in activeData.__config__.regList\"\n              :key=\"index\"\n              class=\"reg-item\"\n            >\n              <span class=\"close-btn\" @click=\"activeData.__config__.regList.splice(index, 1)\">\n                <i class=\"el-icon-close\" />\n              </span>\n              <el-form-item label=\"表达式\">\n                <el-input v-model=\"item.pattern\" placeholder=\"请输入正则\" />\n              </el-form-item>\n              <el-form-item label=\"错误提示\" style=\"margin-bottom:0\">\n                <el-input v-model=\"item.message\" placeholder=\"请输入错误提示\" />\n              </el-form-item>\n            </div>\n            <div style=\"margin-left: 20px\">\n              <el-button icon=\"el-icon-circle-plus-outline\" type=\"text\" @click=\"addReg\">\n                添加规则\n              </el-button>\n            </div>\n          </template>\n        </el-form>\n        <!-- 表单属性 -->\n        <el-form v-show=\"currentTab === 'form'\" size=\"small\" label-width=\"90px\">\n          <el-form-item label=\"表单名\">\n            <el-input v-model=\"formConf.formRef\" placeholder=\"请输入表单名（ref）\" />\n          </el-form-item>\n          <el-form-item label=\"表单模型\">\n            <el-input v-model=\"formConf.formModel\" placeholder=\"请输入数据模型\" />\n          </el-form-item>\n          <el-form-item label=\"校验模型\">\n            <el-input v-model=\"formConf.formRules\" placeholder=\"请输入校验模型\" />\n          </el-form-item>\n          <el-form-item label=\"表单尺寸\">\n            <el-radio-group v-model=\"formConf.size\">\n              <el-radio-button label=\"medium\">\n                中等\n              </el-radio-button>\n              <el-radio-button label=\"small\">\n                较小\n              </el-radio-button>\n              <el-radio-button label=\"mini\">\n                迷你\n              </el-radio-button>\n            </el-radio-group>\n          </el-form-item>\n          <el-form-item label=\"标签对齐\">\n            <el-radio-group v-model=\"formConf.labelPosition\">\n              <el-radio-button label=\"left\">\n                左对齐\n              </el-radio-button>\n              <el-radio-button label=\"right\">\n                右对齐\n              </el-radio-button>\n              <el-radio-button label=\"top\">\n                顶部对齐\n              </el-radio-button>\n            </el-radio-group>\n          </el-form-item>\n          <el-form-item label=\"标签宽度\">\n            <el-input v-model.number=\"formConf.labelWidth\" type=\"number\" placeholder=\"请输入标签宽度\" />\n          </el-form-item>\n          <el-form-item label=\"栅格间隔\">\n            <el-input-number v-model=\"formConf.gutter\" :min=\"0\" placeholder=\"栅格间隔\" />\n          </el-form-item>\n          <el-form-item label=\"禁用表单\">\n            <el-switch v-model=\"formConf.disabled\" />\n          </el-form-item>\n          <el-form-item label=\"表单按钮\">\n            <el-switch v-model=\"formConf.formBtns\" />\n          </el-form-item>\n          <el-form-item label=\"显示未选中组件边框\">\n            <el-switch v-model=\"formConf.unFocusedComponentBorder\" />\n          </el-form-item>\n        </el-form>\n      </el-scrollbar>\n    </div>\n\n    <treeNode-dialog :visible.sync=\"dialogVisible\" title=\"添加选项\" @commit=\"addNode\" />\n    <icons-dialog :visible.sync=\"iconsVisible\" :current=\"activeData[currentIconModel]\" @select=\"setIcon\" />\n  </div>\n</template>\n\n<script>\nimport { isArray } from 'util'\nimport TreeNodeDialog from './TreeNodeDialog'\nimport { isNumberStr } from '@/utils/index'\nimport IconsDialog from './IconsDialog'\nimport {\n  inputComponents, selectComponents, layoutComponents\n} from '@/utils/generator/config'\nimport { saveFormConf } from '@/utils/db'\n\nconst dateTimeFormat = {\n  date: 'yyyy-MM-dd',\n  week: 'yyyy 第 WW 周',\n  month: 'yyyy-MM',\n  year: 'yyyy',\n  datetime: 'yyyy-MM-dd HH:mm:ss',\n  daterange: 'yyyy-MM-dd',\n  monthrange: 'yyyy-MM',\n  datetimerange: 'yyyy-MM-dd HH:mm:ss'\n}\n\n// 使changeRenderKey在目标组件改变时可用\nconst needRerenderList = ['tinymce']\n\nexport default {\n  components: {\n    TreeNodeDialog,\n    IconsDialog\n  },\n  props: ['showField', 'activeData', 'formConf'],\n  data() {\n    return {\n      currentTab: 'field',\n      currentNode: null,\n      dialogVisible: false,\n      iconsVisible: false,\n      currentIconModel: null,\n      dateTypeOptions: [\n        {\n          label: '日(date)',\n          value: 'date'\n        },\n        {\n          label: '周(week)',\n          value: 'week'\n        },\n        {\n          label: '月(month)',\n          value: 'month'\n        },\n        {\n          label: '年(year)',\n          value: 'year'\n        },\n        {\n          label: '日期时间(datetime)',\n          value: 'datetime'\n        }\n      ],\n      dateRangeTypeOptions: [\n        {\n          label: '日期范围(daterange)',\n          value: 'daterange'\n        },\n        {\n          label: '月范围(monthrange)',\n          value: 'monthrange'\n        },\n        {\n          label: '日期时间范围(datetimerange)',\n          value: 'datetimerange'\n        }\n      ],\n      colorFormatOptions: [\n        {\n          label: 'hex',\n          value: 'hex'\n        },\n        {\n          label: 'rgb',\n          value: 'rgb'\n        },\n        {\n          label: 'rgba',\n          value: 'rgba'\n        },\n        {\n          label: 'hsv',\n          value: 'hsv'\n        },\n        {\n          label: 'hsl',\n          value: 'hsl'\n        }\n      ],\n      justifyOptions: [\n        {\n          label: 'start',\n          value: 'start'\n        },\n        {\n          label: 'end',\n          value: 'end'\n        },\n        {\n          label: 'center',\n          value: 'center'\n        },\n        {\n          label: 'space-around',\n          value: 'space-around'\n        },\n        {\n          label: 'space-between',\n          value: 'space-between'\n        }\n      ],\n      layoutTreeProps: {\n        label(data, node) {\n          const config = data.__config__\n          return data.componentName || `${config.label}: ${data.__vModel__}`\n        }\n      }\n    }\n  },\n  computed: {\n    documentLink() {\n      return (\n        this.activeData.__config__.document\n        || 'https://element.eleme.cn/#/zh-CN/component/installation'\n      )\n    },\n    dateOptions() {\n      if (\n        this.activeData.type !== undefined\n        && this.activeData.__config__.tag === 'el-date-picker'\n      ) {\n        if (this.activeData['start-placeholder'] === undefined) {\n          return this.dateTypeOptions\n        }\n        return this.dateRangeTypeOptions\n      }\n      return []\n    },\n    tagList() {\n      return [\n        {\n          label: '输入型组件',\n          options: inputComponents\n        },\n        {\n          label: '选择型组件',\n          options: selectComponents\n        }\n      ]\n    },\n    activeTag() {\n      return this.activeData.__config__.tag\n    },\n    isShowMin() {\n      return ['el-input-number', 'el-slider'].indexOf(this.activeTag) > -1\n    },\n    isShowMax() {\n      return ['el-input-number', 'el-slider', 'el-rate'].indexOf(this.activeTag) > -1\n    },\n    isShowStep() {\n      return ['el-input-number', 'el-slider'].indexOf(this.activeTag) > -1\n    }\n  },\n  watch: {\n    formConf: {\n      handler(val) {\n        saveFormConf(val)\n      },\n      deep: true\n    }\n  },\n  methods: {\n    addReg() {\n      this.activeData.__config__.regList.push({\n        pattern: '',\n        message: ''\n      })\n    },\n    addSelectItem() {\n      this.activeData.__slot__.options.push({\n        label: '',\n        value: ''\n      })\n    },\n    addTreeItem() {\n      ++this.idGlobal\n      this.dialogVisible = true\n      this.currentNode = this.activeData.options\n    },\n    renderContent(h, { node, data, store }) {\n      return (\n        <div class=\"custom-tree-node\">\n          <span>{node.label}</span>\n          <span class=\"node-operation\">\n            <i on-click={() => this.append(data)}\n              class=\"el-icon-plus\"\n              title=\"添加\"\n            ></i>\n            <i on-click={() => this.remove(node, data)}\n              class=\"el-icon-delete\"\n              title=\"删除\"\n            ></i>\n          </span>\n        </div>\n      )\n    },\n    append(data) {\n      if (!data.children) {\n        this.$set(data, 'children', [])\n      }\n      this.dialogVisible = true\n      this.currentNode = data.children\n    },\n    remove(node, data) {\n      this.activeData.__config__.defaultValue = [] // 避免删除时报错\n      const { parent } = node\n      const children = parent.data.children || parent.data\n      const index = children.findIndex(d => d.id === data.id)\n      children.splice(index, 1)\n    },\n    addNode(data) {\n      this.currentNode.push(data)\n    },\n    setOptionValue(item, val) {\n      item.value = isNumberStr(val) ? +val : val\n    },\n    setDefaultValue(val) {\n      if (Array.isArray(val)) {\n        return val.join(',')\n      }\n      // if (['string', 'number'].indexOf(typeof val) > -1) {\n      //   return val\n      // }\n      if (typeof val === 'boolean') {\n        return `${val}`\n      }\n      return val\n    },\n    onDefaultValueInput(str) {\n      if (isArray(this.activeData.__config__.defaultValue)) {\n        // 数组\n        this.$set(\n          this.activeData.__config__,\n          'defaultValue',\n          str.split(',').map(val => (isNumberStr(val) ? +val : val))\n        )\n      } else if (['true', 'false'].indexOf(str) > -1) {\n        // 布尔\n        this.$set(this.activeData.__config__, 'defaultValue', JSON.parse(str))\n      } else {\n        // 字符串和数字\n        this.$set(\n          this.activeData.__config__,\n          'defaultValue',\n          isNumberStr(str) ? +str : str\n        )\n      }\n    },\n    onSwitchValueInput(val, name) {\n      if (['true', 'false'].indexOf(val) > -1) {\n        this.$set(this.activeData, name, JSON.parse(val))\n      } else {\n        this.$set(this.activeData, name, isNumberStr(val) ? +val : val)\n      }\n    },\n    setTimeValue(val, type) {\n      const valueFormat = type === 'week' ? dateTimeFormat.date : val\n      this.$set(this.activeData.__config__, 'defaultValue', null)\n      this.$set(this.activeData, 'value-format', valueFormat)\n      this.$set(this.activeData, 'format', val)\n    },\n    spanChange(val) {\n      this.formConf.span = val\n    },\n    multipleChange(val) {\n      this.$set(this.activeData.__config__, 'defaultValue', val ? [] : '')\n    },\n    dateTypeChange(val) {\n      this.setTimeValue(dateTimeFormat[val], val)\n    },\n    rangeChange(val) {\n      this.$set(\n        this.activeData.__config__,\n        'defaultValue',\n        val ? [this.activeData.min, this.activeData.max] : this.activeData.min\n      )\n    },\n    rateTextChange(val) {\n      if (val) this.activeData['show-score'] = false\n    },\n    rateScoreChange(val) {\n      if (val) this.activeData['show-text'] = false\n    },\n    colorFormatChange(val) {\n      this.activeData.__config__.defaultValue = null\n      this.activeData['show-alpha'] = val.indexOf('a') > -1\n      this.activeData.__config__.renderKey = +new Date() // 更新renderKey,重新渲染该组件\n    },\n    openIconsDialog(model) {\n      this.iconsVisible = true\n      this.currentIconModel = model\n    },\n    setIcon(val) {\n      this.activeData[this.currentIconModel] = val\n    },\n    tagChange(tagIcon) {\n      let target = inputComponents.find(item => item.__config__.tagIcon === tagIcon)\n      if (!target) target = selectComponents.find(item => item.__config__.tagIcon === tagIcon)\n      this.$emit('tag-change', target)\n    },\n    changeRenderKey() {\n      if (needRerenderList.includes(this.activeData.__config__.tag)) {\n        this.activeData.__config__.renderKey = +new Date()\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.right-board {\n  width: 350px;\n  position: absolute;\n  right: 0;\n  top: 0;\n  padding-top: 3px;\n  .field-box {\n    position: relative;\n    height: calc(100vh - 42px);\n    box-sizing: border-box;\n    overflow: hidden;\n  }\n  .el-scrollbar {\n    height: 100%;\n  }\n}\n.select-item {\n  display: flex;\n  border: 1px dashed #fff;\n  box-sizing: border-box;\n  & .close-btn {\n    cursor: pointer;\n    color: #f56c6c;\n  }\n  & .el-input + .el-input {\n    margin-left: 4px;\n  }\n}\n.select-item + .select-item {\n  margin-top: 4px;\n}\n.select-item.sortable-chosen {\n  border: 1px dashed #409eff;\n}\n.select-line-icon {\n  line-height: 32px;\n  font-size: 22px;\n  padding: 0 4px;\n  color: #777;\n}\n.option-drag {\n  cursor: move;\n}\n.time-range {\n  .el-date-editor {\n    width: 227px;\n  }\n  ::v-deep .el-icon-time {\n    display: none;\n  }\n}\n.document-link {\n  position: absolute;\n  display: block;\n  width: 26px;\n  height: 26px;\n  top: 0;\n  left: 0;\n  cursor: pointer;\n  background: #409eff;\n  z-index: 1;\n  border-radius: 0 0 6px 0;\n  text-align: center;\n  line-height: 26px;\n  color: #fff;\n  font-size: 18px;\n}\n.node-label{\n  font-size: 14px;\n}\n.node-icon{\n  color: #bebfc3;\n}\n</style>\n"]}]}