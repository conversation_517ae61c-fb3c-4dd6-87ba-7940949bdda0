{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/system/user/index.vue?vue&type=template&id=78084362", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/system/user/index.vue", "mtime": 1665234686000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/loaders/templateLoader.js", "mtime": 1751171659981}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}