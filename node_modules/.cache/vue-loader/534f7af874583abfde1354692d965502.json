{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/project/dict/data.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/project/dict/data.vue", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGxpc3REYXRhLCBnZXREYXRhLCBkZWxEYXRhLCBhZGREYXRhLCB1cGRhdGVEYXRhLCBleHBvcnREYXRhIH0gZnJvbSAiQC9hcGkvc3lzdGVtL2RpY3QvZGF0YSI7CmltcG9ydCB7IGxpc3RUeXBlLCBnZXRUeXBlIH0gZnJvbSAiQC9hcGkvc3lzdGVtL2RpY3QvdHlwZSI7CgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogIkRhdGEiLAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICAvLyDpga7nvanlsYIKICAgICAgbG9hZGluZzogdHJ1ZSwKICAgICAgLy8g6YCJ5Lit5pWw57uECiAgICAgIGlkczogW10sCiAgICAgIC8vIOmdnuWNleS4quemgeeUqAogICAgICBzaW5nbGU6IHRydWUsCiAgICAgIC8vIOmdnuWkmuS4quemgeeUqAogICAgICBtdWx0aXBsZTogdHJ1ZSwKICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2CiAgICAgIHNob3dTZWFyY2g6IHRydWUsCiAgICAgIC8vIOaAu+adoeaVsAogICAgICB0b3RhbDogMCwKICAgICAgLy8g5a2X5YW46KGo5qC85pWw5o2uCiAgICAgIGRhdGFMaXN0OiBbXSwKICAgICAgLy8g6buY6K6k5a2X5YW457G75Z6LCiAgICAgIGRlZmF1bHREaWN0VHlwZTogIiIsCiAgICAgIC8vIOW8ueWHuuWxguagh+mimAogICAgICB0aXRsZTogIiIsCiAgICAgIC8vIOaYr+WQpuaYvuekuuW8ueWHuuWxggogICAgICBvcGVuOiBmYWxzZSwKICAgICAgLy8g54q25oCB5pWw5o2u5a2X5YW4CiAgICAgIHN0YXR1c09wdGlvbnM6IFtdLAogICAgICAvLyDnsbvlnovmlbDmja7lrZflhbgKICAgICAgdHlwZU9wdGlvbnM6IFtdLAogICAgICAvLyDmn6Xor6Llj4LmlbAKICAgICAgcXVlcnlQYXJhbXM6IHsKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICBkaWN0TmFtZTogdW5kZWZpbmVkLAogICAgICAgIGRpY3RUeXBlOiB1bmRlZmluZWQsCiAgICAgICAgc3RhdHVzOiB1bmRlZmluZWQKICAgICAgfSwKICAgICAgLy8g6KGo5Y2V5Y+C5pWwCiAgICAgIGZvcm06IHt9LAogICAgICAvLyDooajljZXmoKHpqowKICAgICAgcnVsZXM6IHsKICAgICAgICBkaWN0TGFiZWw6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLmlbDmja7moIfnrb7kuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfQogICAgICAgIF0sCiAgICAgICAgZGljdFZhbHVlOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5pWw5o2u6ZSu5YC85LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0KICAgICAgICBdLAogICAgICAgIGRpY3RTb3J0OiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5pWw5o2u6aG65bqP5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0KICAgICAgICBdCiAgICAgIH0KICAgIH07CiAgfSwKICBjcmVhdGVkKCkgewogICAgY29uc3QgZGljdElkID0gdGhpcy4kcm91dGUucGFyYW1zICYmIHRoaXMuJHJvdXRlLnBhcmFtcy5kaWN0SWQ7CiAgICB0aGlzLmdldFR5cGUoZGljdElkKTsKICAgIHRoaXMuZ2V0VHlwZUxpc3QoKTsKICAgIHRoaXMuZ2V0RGljdHMoInN5c19ub3JtYWxfZGlzYWJsZSIpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICB0aGlzLnN0YXR1c09wdGlvbnMgPSByZXNwb25zZS5kYXRhOwogICAgfSk7CiAgfSwKICBtZXRob2RzOiB7CiAgICAvKiog5p+l6K+i5a2X5YW457G75Z6L6K+m57uGICovCiAgICBnZXRUeXBlKGRpY3RJZCkgewogICAgICBnZXRUeXBlKGRpY3RJZCkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5kaWN0VHlwZSA9IHJlc3BvbnNlLmRhdGEuZGljdFR5cGU7CiAgICAgICAgdGhpcy5kZWZhdWx0RGljdFR5cGUgPSByZXNwb25zZS5kYXRhLmRpY3RUeXBlOwogICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICB9KTsKICAgIH0sCiAgICAvKiog5p+l6K+i5a2X5YW457G75Z6L5YiX6KGoICovCiAgICBnZXRUeXBlTGlzdCgpIHsKICAgICAgbGlzdFR5cGUoKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLnR5cGVPcHRpb25zID0gcmVzcG9uc2Uucm93czsKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOafpeivouWtl+WFuOaVsOaNruWIl+ihqCAqLwogICAgZ2V0TGlzdCgpIHsKICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsKICAgICAgbGlzdERhdGEodGhpcy5xdWVyeVBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5kYXRhTGlzdCA9IHJlc3BvbnNlLnJvd3M7CiAgICAgICAgdGhpcy50b3RhbCA9IHJlc3BvbnNlLnRvdGFsOwogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOwogICAgICB9KTsKICAgIH0sCiAgICAvLyDmlbDmja7nirbmgIHlrZflhbjnv7vor5EKICAgIHN0YXR1c0Zvcm1hdChyb3csIGNvbHVtbikgewogICAgICByZXR1cm4gdGhpcy5zZWxlY3REaWN0TGFiZWwodGhpcy5zdGF0dXNPcHRpb25zLCByb3cuc3RhdHVzKTsKICAgIH0sCiAgICAvLyDlj5bmtojmjInpkq4KICAgIGNhbmNlbCgpIHsKICAgICAgdGhpcy5vcGVuID0gZmFsc2U7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgIH0sCiAgICAvLyDooajljZXph43nva4KICAgIHJlc2V0KCkgewogICAgICB0aGlzLmZvcm0gPSB7CiAgICAgICAgZGljdENvZGU6IHVuZGVmaW5lZCwKICAgICAgICBkaWN0TGFiZWw6IHVuZGVmaW5lZCwKICAgICAgICBkaWN0VmFsdWU6IHVuZGVmaW5lZCwKICAgICAgICBkaWN0U29ydDogMCwKICAgICAgICBzdGF0dXM6ICIwIiwKICAgICAgICByZW1hcms6IHVuZGVmaW5lZAogICAgICB9OwogICAgICB0aGlzLnJlc2V0Rm9ybSgiZm9ybSIpOwogICAgfSwKICAgIC8qKiDmkJzntKLmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZVF1ZXJ5KCkgewogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOwogICAgICB0aGlzLmdldExpc3QoKTsKICAgIH0sCiAgICAvKiog6YeN572u5oyJ6ZKu5pON5L2cICovCiAgICByZXNldFF1ZXJ5KCkgewogICAgICB0aGlzLnJlc2V0Rm9ybSgicXVlcnlGb3JtIik7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMuZGljdFR5cGUgPSB0aGlzLmRlZmF1bHREaWN0VHlwZTsKICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOwogICAgfSwKICAgIC8qKiDmlrDlop7mjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZUFkZCgpIHsKICAgICAgdGhpcy5yZXNldCgpOwogICAgICB0aGlzLm9wZW4gPSB0cnVlOwogICAgICB0aGlzLnRpdGxlID0gIua3u+WKoOWtl+WFuOaVsOaNriI7CiAgICAgIHRoaXMuZm9ybS5kaWN0VHlwZSA9IHRoaXMucXVlcnlQYXJhbXMuZGljdFR5cGU7CiAgICB9LAogICAgLy8g5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7CiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcChpdGVtID0+IGl0ZW0uZGljdENvZGUpCiAgICAgIHRoaXMuc2luZ2xlID0gc2VsZWN0aW9uLmxlbmd0aCE9MQogICAgICB0aGlzLm11bHRpcGxlID0gIXNlbGVjdGlvbi5sZW5ndGgKICAgIH0sCiAgICAvKiog5L+u5pS55oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVVcGRhdGUocm93KSB7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgICAgY29uc3QgZGljdENvZGUgPSByb3cuZGljdENvZGUgfHwgdGhpcy5pZHMKICAgICAgZ2V0RGF0YShkaWN0Q29kZSkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5mb3JtID0gcmVzcG9uc2UuZGF0YTsKICAgICAgICB0aGlzLm9wZW4gPSB0cnVlOwogICAgICAgIHRoaXMudGl0bGUgPSAi5L+u5pS55a2X5YW45pWw5o2uIjsKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOaPkOS6pOaMiemSriAqLwogICAgc3VibWl0Rm9ybTogZnVuY3Rpb24oKSB7CiAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICBpZiAodGhpcy5mb3JtLmRpY3RDb2RlICE9IHVuZGVmaW5lZCkgewogICAgICAgICAgICB1cGRhdGVEYXRhKHRoaXMuZm9ybSkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgICAgICAgdGhpcy5tc2dTdWNjZXNzKCLkv67mlLnmiJDlip8iKTsKICAgICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsKICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICBhZGREYXRhKHRoaXMuZm9ybSkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgICAgICAgdGhpcy5tc2dTdWNjZXNzKCLmlrDlop7miJDlip8iKTsKICAgICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsKICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvKiog5Yig6Zmk5oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVEZWxldGUocm93KSB7CiAgICAgIGNvbnN0IGRpY3RDb2RlcyA9IHJvdy5kaWN0Q29kZSB8fCB0aGlzLmlkczsKICAgICAgdGhpcy4kY29uZmlybSgn5piv5ZCm56Gu6K6k5Yig6Zmk5a2X5YW457yW56CB5Li6IicgKyBkaWN0Q29kZXMgKyAnIueahOaVsOaNrumhuT8nLCAi6K2m5ZGKIiwgewogICAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLAogICAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogIuWPlua2iCIsCiAgICAgICAgICB0eXBlOiAid2FybmluZyIKICAgICAgICB9KS50aGVuKGZ1bmN0aW9uKCkgewogICAgICAgICAgcmV0dXJuIGRlbERhdGEoZGljdENvZGVzKTsKICAgICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICAgICAgdGhpcy5tc2dTdWNjZXNzKCLliKDpmaTmiJDlip8iKTsKICAgICAgICB9KQogICAgfSwKICAgIC8qKiDlr7zlh7rmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZUV4cG9ydCgpIHsKICAgICAgY29uc3QgcXVlcnlQYXJhbXMgPSB0aGlzLnF1ZXJ5UGFyYW1zOwogICAgICB0aGlzLiRjb25maXJtKCfmmK/lkKbnoa7orqTlr7zlh7rmiYDmnInmlbDmja7pobk/JywgIuitpuWRiiIsIHsKICAgICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAi56Gu5a6aIiwKICAgICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLAogICAgICAgICAgdHlwZTogIndhcm5pbmciCiAgICAgICAgfSkudGhlbihmdW5jdGlvbigpIHsKICAgICAgICAgIHJldHVybiBleHBvcnREYXRhKHF1ZXJ5UGFyYW1zKTsKICAgICAgICB9KS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICAgIHRoaXMuZG93bmxvYWQocmVzcG9uc2UubXNnKTsKICAgICAgICB9KQogICAgfQogIH0KfTsK"}, {"version": 3, "sources": ["data.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkKA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "data.vue", "sourceRoot": "src/views/project/dict", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"字典名称\" prop=\"dictType\">\n        <el-select v-model=\"queryParams.dictType\" size=\"small\">\n          <el-option\n            v-for=\"item in typeOptions\"\n            :key=\"item.dictId\"\n            :label=\"item.dictName\"\n            :value=\"item.dictType\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"字典标签\" prop=\"dictLabel\">\n        <el-input\n          v-model=\"queryParams.dictLabel\"\n          placeholder=\"请输入字典标签\"\n          clearable\n          size=\"small\"\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"状态\" prop=\"status\">\n        <el-select v-model=\"queryParams.status\" placeholder=\"数据状态\" clearable size=\"small\">\n          <el-option\n            v-for=\"dict in statusOptions\"\n            :key=\"dict.dictValue\"\n            :label=\"dict.dictLabel\"\n            :value=\"dict.dictValue\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"primary\"\n          plain\n          icon=\"el-icon-plus\"\n          size=\"mini\"\n          @click=\"handleAdd\"\n          v-hasPermi=\"['system:dict:add']\"\n        >新增</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"success\"\n          plain\n          icon=\"el-icon-edit\"\n          size=\"mini\"\n          :disabled=\"single\"\n          @click=\"handleUpdate\"\n          v-hasPermi=\"['system:dict:edit']\"\n        >修改</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"danger\"\n          plain\n          icon=\"el-icon-delete\"\n          size=\"mini\"\n          :disabled=\"multiple\"\n          @click=\"handleDelete\"\n          v-hasPermi=\"['system:dict:remove']\"\n        >删除</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"warning\"\n          plain\n          icon=\"el-icon-download\"\n          size=\"mini\"\n          @click=\"handleExport\"\n          v-hasPermi=\"['system:dict:export']\"\n        >导出</el-button>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <el-table v-loading=\"loading\" :data=\"dataList\" @selection-change=\"handleSelectionChange\">\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column label=\"字典编码\" align=\"center\" prop=\"dictCode\" />\n      <el-table-column label=\"字典标签\" align=\"center\" prop=\"dictLabel\" />\n      <el-table-column label=\"字典键值\" align=\"center\" prop=\"dictValue\" />\n      <el-table-column label=\"字典排序\" align=\"center\" prop=\"dictSort\" />\n      <el-table-column label=\"状态\" align=\"center\" prop=\"status\" :formatter=\"statusFormat\" />\n      <el-table-column label=\"备注\" align=\"center\" prop=\"remark\" :show-overflow-tooltip=\"true\" />\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"180\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.createTime) }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-edit\"\n            @click=\"handleUpdate(scope.row)\"\n            v-hasPermi=\"['system:dict:edit']\"\n          >修改</el-button>\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-delete\"\n            @click=\"handleDelete(scope.row)\"\n            v-hasPermi=\"['system:dict:remove']\"\n          >删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 添加或修改参数配置对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"500px\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\n        <el-form-item label=\"字典类型\">\n          <el-input v-model=\"form.dictType\" :disabled=\"true\" />\n        </el-form-item>\n        <el-form-item label=\"数据标签\" prop=\"dictLabel\">\n          <el-input v-model=\"form.dictLabel\" placeholder=\"请输入数据标签\" />\n        </el-form-item>\n        <el-form-item label=\"数据键值\" prop=\"dictValue\">\n          <el-input v-model=\"form.dictValue\" placeholder=\"请输入数据键值\" />\n        </el-form-item>\n        <el-form-item label=\"显示排序\" prop=\"dictSort\">\n          <el-input-number v-model=\"form.dictSort\" controls-position=\"right\" :min=\"0\" />\n        </el-form-item>\n        <el-form-item label=\"状态\" prop=\"status\">\n          <el-radio-group v-model=\"form.status\">\n            <el-radio\n              v-for=\"dict in statusOptions\"\n              :key=\"dict.dictValue\"\n              :label=\"dict.dictValue\"\n            >{{dict.dictLabel}}</el-radio>\n          </el-radio-group>\n        </el-form-item>\n        <el-form-item label=\"备注\" prop=\"remark\">\n          <el-input v-model=\"form.remark\" type=\"textarea\" placeholder=\"请输入内容\"></el-input>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { listData, getData, delData, addData, updateData, exportData } from \"@/api/system/dict/data\";\nimport { listType, getType } from \"@/api/system/dict/type\";\n\nexport default {\n  name: \"Data\",\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 字典表格数据\n      dataList: [],\n      // 默认字典类型\n      defaultDictType: \"\",\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      open: false,\n      // 状态数据字典\n      statusOptions: [],\n      // 类型数据字典\n      typeOptions: [],\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        dictName: undefined,\n        dictType: undefined,\n        status: undefined\n      },\n      // 表单参数\n      form: {},\n      // 表单校验\n      rules: {\n        dictLabel: [\n          { required: true, message: \"数据标签不能为空\", trigger: \"blur\" }\n        ],\n        dictValue: [\n          { required: true, message: \"数据键值不能为空\", trigger: \"blur\" }\n        ],\n        dictSort: [\n          { required: true, message: \"数据顺序不能为空\", trigger: \"blur\" }\n        ]\n      }\n    };\n  },\n  created() {\n    const dictId = this.$route.params && this.$route.params.dictId;\n    this.getType(dictId);\n    this.getTypeList();\n    this.getDicts(\"sys_normal_disable\").then(response => {\n      this.statusOptions = response.data;\n    });\n  },\n  methods: {\n    /** 查询字典类型详细 */\n    getType(dictId) {\n      getType(dictId).then(response => {\n        this.queryParams.dictType = response.data.dictType;\n        this.defaultDictType = response.data.dictType;\n        this.getList();\n      });\n    },\n    /** 查询字典类型列表 */\n    getTypeList() {\n      listType().then(response => {\n        this.typeOptions = response.rows;\n      });\n    },\n    /** 查询字典数据列表 */\n    getList() {\n      this.loading = true;\n      listData(this.queryParams).then(response => {\n        this.dataList = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    // 数据状态字典翻译\n    statusFormat(row, column) {\n      return this.selectDictLabel(this.statusOptions, row.status);\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        dictCode: undefined,\n        dictLabel: undefined,\n        dictValue: undefined,\n        dictSort: 0,\n        status: \"0\",\n        remark: undefined\n      };\n      this.resetForm(\"form\");\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.queryParams.dictType = this.defaultDictType;\n      this.handleQuery();\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.reset();\n      this.open = true;\n      this.title = \"添加字典数据\";\n      this.form.dictType = this.queryParams.dictType;\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.dictCode)\n      this.single = selection.length!=1\n      this.multiple = !selection.length\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset();\n      const dictCode = row.dictCode || this.ids\n      getData(dictCode).then(response => {\n        this.form = response.data;\n        this.open = true;\n        this.title = \"修改字典数据\";\n      });\n    },\n    /** 提交按钮 */\n    submitForm: function() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          if (this.form.dictCode != undefined) {\n            updateData(this.form).then(response => {\n              this.msgSuccess(\"修改成功\");\n              this.open = false;\n              this.getList();\n            });\n          } else {\n            addData(this.form).then(response => {\n              this.msgSuccess(\"新增成功\");\n              this.open = false;\n              this.getList();\n            });\n          }\n        }\n      });\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const dictCodes = row.dictCode || this.ids;\n      this.$confirm('是否确认删除字典编码为\"' + dictCodes + '\"的数据项?', \"警告\", {\n          confirmButtonText: \"确定\",\n          cancelButtonText: \"取消\",\n          type: \"warning\"\n        }).then(function() {\n          return delData(dictCodes);\n        }).then(() => {\n          this.getList();\n          this.msgSuccess(\"删除成功\");\n        })\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      const queryParams = this.queryParams;\n      this.$confirm('是否确认导出所有数据项?', \"警告\", {\n          confirmButtonText: \"确定\",\n          cancelButtonText: \"取消\",\n          type: \"warning\"\n        }).then(function() {\n          return exportData(queryParams);\n        }).then(response => {\n          this.download(response.msg);\n        })\n    }\n  }\n};\n</script>"]}]}