{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/components/RightPanel/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/components/RightPanel/index.vue", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGFkZENsYXNzLCByZW1vdmVDbGFzcyB9IGZyb20gJ0AvdXRpbHMnCgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ1JpZ2h0UGFuZWwnLAogIHByb3BzOiB7CiAgICBjbGlja05vdENsb3NlOiB7CiAgICAgIGRlZmF1bHQ6IGZhbHNlLAogICAgICB0eXBlOiBCb29sZWFuCiAgICB9LAogICAgYnV0dG9uVG9wOiB7CiAgICAgIGRlZmF1bHQ6IDI1MCwKICAgICAgdHlwZTogTnVtYmVyCiAgICB9CiAgfSwKICBjb21wdXRlZDogewogICAgc2hvdzogewogICAgICBnZXQoKSB7CiAgICAgICAgcmV0dXJuIHRoaXMuJHN0b3JlLnN0YXRlLnNldHRpbmdzLnNob3dTZXR0aW5ncwogICAgICB9LAogICAgICBzZXQodmFsKSB7CiAgICAgICAgdGhpcy4kc3RvcmUuZGlzcGF0Y2goJ3NldHRpbmdzL2NoYW5nZVNldHRpbmcnLCB7CiAgICAgICAgICBrZXk6ICdzaG93U2V0dGluZ3MnLAogICAgICAgICAgdmFsdWU6IHZhbAogICAgICAgIH0pCiAgICAgIH0KICAgIH0sCiAgICB0aGVtZSgpIHsKICAgICAgcmV0dXJuIHRoaXMuJHN0b3JlLnN0YXRlLnNldHRpbmdzLnRoZW1lCiAgICB9LAogIH0sCiAgd2F0Y2g6IHsKICAgIHNob3codmFsdWUpIHsKICAgICAgaWYgKHZhbHVlICYmICF0aGlzLmNsaWNrTm90Q2xvc2UpIHsKICAgICAgICB0aGlzLmFkZEV2ZW50Q2xpY2soKQogICAgICB9CiAgICAgIGlmICh2YWx1ZSkgewogICAgICAgIGFkZENsYXNzKGRvY3VtZW50LmJvZHksICdzaG93UmlnaHRQYW5lbCcpCiAgICAgIH0gZWxzZSB7CiAgICAgICAgcmVtb3ZlQ2xhc3MoZG9jdW1lbnQuYm9keSwgJ3Nob3dSaWdodFBhbmVsJykKICAgICAgfQogICAgfQogIH0sCiAgbW91bnRlZCgpIHsKICAgIHRoaXMuaW5zZXJ0VG9Cb2R5KCkKICAgIHRoaXMuYWRkRXZlbnRDbGljaygpCiAgfSwKICBiZWZvcmVEZXN0cm95KCkgewogICAgY29uc3QgZWx4ID0gdGhpcy4kcmVmcy5yaWdodFBhbmVsCiAgICBlbHgucmVtb3ZlKCkKICB9LAogIG1ldGhvZHM6IHsKICAgIGFkZEV2ZW50Q2xpY2soKSB7CiAgICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKCdjbGljaycsIHRoaXMuY2xvc2VTaWRlYmFyKQogICAgfSwKICAgIGNsb3NlU2lkZWJhcihldnQpIHsKICAgICAgY29uc3QgcGFyZW50ID0gZXZ0LnRhcmdldC5jbG9zZXN0KCcucmlnaHRQYW5lbCcpCiAgICAgIGlmICghcGFyZW50KSB7CiAgICAgICAgdGhpcy5zaG93ID0gZmFsc2UKICAgICAgICB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcignY2xpY2snLCB0aGlzLmNsb3NlU2lkZWJhcikKICAgICAgfQogICAgfSwKICAgIGluc2VydFRvQm9keSgpIHsKICAgICAgY29uc3QgZWx4ID0gdGhpcy4kcmVmcy5yaWdodFBhbmVsCiAgICAgIGNvbnN0IGJvZHkgPSBkb2N1bWVudC5xdWVyeVNlbGVjdG9yKCdib2R5JykKICAgICAgYm9keS5pbnNlcnRCZWZvcmUoZWx4LCBib2R5LmZpcnN0Q2hpbGQpCiAgICB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;AAYA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/RightPanel", "sourcesContent": ["<template>\n  <div ref=\"rightPanel\" :class=\"{show:show}\" class=\"rightPanel-container\">\n    <div class=\"rightPanel-background\" />\n    <div class=\"rightPanel\">\n      <div class=\"rightPanel-items\">\n        <slot />\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { addClass, removeClass } from '@/utils'\n\nexport default {\n  name: 'RightPanel',\n  props: {\n    clickNotClose: {\n      default: false,\n      type: Boolean\n    },\n    buttonTop: {\n      default: 250,\n      type: Number\n    }\n  },\n  computed: {\n    show: {\n      get() {\n        return this.$store.state.settings.showSettings\n      },\n      set(val) {\n        this.$store.dispatch('settings/changeSetting', {\n          key: 'showSettings',\n          value: val\n        })\n      }\n    },\n    theme() {\n      return this.$store.state.settings.theme\n    },\n  },\n  watch: {\n    show(value) {\n      if (value && !this.clickNotClose) {\n        this.addEventClick()\n      }\n      if (value) {\n        addClass(document.body, 'showRightPanel')\n      } else {\n        removeClass(document.body, 'showRightPanel')\n      }\n    }\n  },\n  mounted() {\n    this.insertToBody()\n    this.addEventClick()\n  },\n  beforeDestroy() {\n    const elx = this.$refs.rightPanel\n    elx.remove()\n  },\n  methods: {\n    addEventClick() {\n      window.addEventListener('click', this.closeSidebar)\n    },\n    closeSidebar(evt) {\n      const parent = evt.target.closest('.rightPanel')\n      if (!parent) {\n        this.show = false\n        window.removeEventListener('click', this.closeSidebar)\n      }\n    },\n    insertToBody() {\n      const elx = this.$refs.rightPanel\n      const body = document.querySelector('body')\n      body.insertBefore(elx, body.firstChild)\n    }\n  }\n}\n</script>\n\n<style>\n.showRightPanel {\n  overflow: hidden;\n  position: relative;\n  width: calc(100% - 15px);\n}\n</style>\n\n<style lang=\"scss\" scoped>\n.rightPanel-background {\n  position: fixed;\n  top: 0;\n  left: 0;\n  opacity: 0;\n  transition: opacity .3s cubic-bezier(.7, .3, .1, 1);\n  background: rgba(0, 0, 0, .2);\n  z-index: -1;\n}\n\n.rightPanel {\n  width: 100%;\n  max-width: 260px;\n  height: 100vh;\n  position: fixed;\n  top: 0;\n  right: 0;\n  box-shadow: 0px 0px 15px 0px rgba(0, 0, 0, .05);\n  transition: all .25s cubic-bezier(.7, .3, .1, 1);\n  transform: translate(100%);\n  background: #fff;\n  z-index: 40000;\n}\n\n.show {\n  transition: all .3s cubic-bezier(.7, .3, .1, 1);\n\n  .rightPanel-background {\n    z-index: 20000;\n    opacity: 1;\n    width: 100%;\n    height: 100%;\n  }\n\n  .rightPanel {\n    transform: translate(0);\n  }\n}\n\n.handle-button {\n  width: 48px;\n  height: 48px;\n  position: absolute;\n  left: -48px;\n  text-align: center;\n  font-size: 24px;\n  border-radius: 6px 0 0 6px !important;\n  z-index: 0;\n  pointer-events: auto;\n  cursor: pointer;\n  color: #fff;\n  line-height: 48px;\n  i {\n    font-size: 24px;\n    line-height: 48px;\n  }\n}\n</style>\n"]}]}