{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/tool/gen/basicInfoForm.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/tool/gen/basicInfoForm.vue", "mtime": 1641277326000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiQmFzaWNJbmZvRm9ybSIsCiAgcHJvcHM6IHsKICAgIGluZm86IHsKICAgICAgdHlwZTogT2JqZWN0LAogICAgICBkZWZhdWx0OiBudWxsCiAgICB9CiAgfSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgcnVsZXM6IHsKICAgICAgICB0YWJsZU5hbWU6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor7fovpPlhaXooajlkI3np7AiLCB0cmlnZ2VyOiAiYmx1ciIgfQogICAgICAgIF0sCiAgICAgICAgdGFibGVDb21tZW50OiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+36L6T5YWl6KGo5o+P6L+wIiwgdHJpZ2dlcjogImJsdXIiIH0KICAgICAgICBdLAogICAgICAgIGNsYXNzTmFtZTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+i+k+WFpeWunuS9k+exu+WQjeensCIsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXSwKICAgICAgICBmdW5jdGlvbkF1dGhvcjogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+i+k+WFpeS9nOiAhSIsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXQogICAgICB9CiAgICB9OwogIH0KfTsK"}, {"version": 3, "sources": ["basicInfoForm.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "basicInfoForm.vue", "sourceRoot": "src/views/tool/gen", "sourcesContent": ["<template>\n  <el-form ref=\"basicInfoForm\" :model=\"info\" :rules=\"rules\" label-width=\"150px\">\n    <el-row>\n      <el-col :span=\"12\">\n        <el-form-item label=\"表名称\" prop=\"tableName\">\n          <el-input placeholder=\"请输入仓库名称\" v-model=\"info.tableName\" />\n        </el-form-item>\n      </el-col>\n      <el-col :span=\"12\">\n        <el-form-item label=\"表描述\" prop=\"tableComment\">\n          <el-input placeholder=\"请输入\" v-model=\"info.tableComment\" />\n        </el-form-item>\n      </el-col>\n\n      <el-col :span=\"12\">\n        <el-form-item label=\"实体类名称\" prop=\"className\">\n          <el-input placeholder=\"请输入\" v-model=\"info.className\" />\n        </el-form-item>\n      </el-col>\n      <el-col :span=\"12\">\n        <el-form-item label=\"作者\" prop=\"functionAuthor\">\n          <el-input placeholder=\"请输入\" v-model=\"info.functionAuthor\" />\n        </el-form-item>\n      </el-col>\n      <el-col :span=\"24\">\n        <el-form-item label=\"备注\" prop=\"remark\">\n          <el-input type=\"textarea\" :rows=\"3\" v-model=\"info.remark\"></el-input>\n        </el-form-item>\n      </el-col>\n    </el-row>\n  </el-form>\n</template>\n<script>\nexport default {\n  name: \"BasicInfoForm\",\n  props: {\n    info: {\n      type: Object,\n      default: null\n    }\n  },\n  data() {\n    return {\n      rules: {\n        tableName: [\n          { required: true, message: \"请输入表名称\", trigger: \"blur\" }\n        ],\n        tableComment: [\n          { required: true, message: \"请输入表描述\", trigger: \"blur\" }\n        ],\n        className: [\n          { required: true, message: \"请输入实体类名称\", trigger: \"blur\" }\n        ],\n        functionAuthor: [\n          { required: true, message: \"请输入作者\", trigger: \"blur\" }\n        ]\n      }\n    };\n  }\n};\n</script>\n"]}]}