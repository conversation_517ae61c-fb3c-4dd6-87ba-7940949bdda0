{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Process/PropertyPanel.vue?vue&type=style&index=0&id=89df6c22&lang=scss", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Process/PropertyPanel.vue", "mtime": 1641277326000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/css-loader/dist/cjs.js", "mtime": 1751171659443}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/loaders/stylePostLoader.js", "mtime": 1751171659924}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/postcss-loader/src/index.js", "mtime": 1751171659554}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/sass-loader/dist/cjs.js", "mtime": 1751171659051}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLnByb3BlcnR5LXBhbmVsIHsKICBwYWRkaW5nOiAyMHB4IDIwcHg7CiAgLy8gcmVzZXQgZWxlbWVudCBjc3MKICAuZWwtZm9ybS0tbGFiZWwtdG9wIC5lbC1mb3JtLWl0ZW1fX2xhYmVsIHsKICAgIHBhZGRpbmc6IDA7CiAgfQogIC5lbC1mb3JtLWl0ZW0gewogICAgbWFyZ2luLWJvdHRvbTogNnB4OwogIH0KICAudGFiLXRhYmxlIC5lbC1mb3JtLWl0ZW0gewogICAgbWFyZ2luLWJvdHRvbTogMTZweDsKICB9CiAgLm5vZGUtbmFtZXsKICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjY2NjOwogICAgcGFkZGluZzogMCAwIDEwcHggMjBweDsKICAgIG1hcmdpbi1ib3R0b206IDEwcHg7CiAgICBmb250LXNpemU6IDE2cHg7CiAgICBmb250LXdlaWdodDogYm9sZDsKICAgIGNvbG9yOiAjNDQ0OwogIH0KfQo="}, {"version": 3, "sources": ["PropertyPanel.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmJA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "PropertyPanel.vue", "sourceRoot": "src/components/Process", "sourcesContent": ["<template>\n  <div ref=\"propertyPanel\" class=\"property-panel\">\n    <div v-if=\"nodeName\" class=\"node-name\">{{ nodeName }}</div>\n    <component\n      :is=\"getComponent\"\n      v-if=\"element\"\n      :element=\"element\"\n      :modeler=\"modeler\"\n      :users=\"users\"\n      :groups=\"groups\"\n      :categorys=\"categorys\"\n      @dataType=\"dataType\"\n    />\n  </div>\n</template>\n\n<script>\nimport taskPanel from './components/nodePanel/task'\nimport startEndPanel from './components/nodePanel/startEnd'\nimport processPanel from './components/nodePanel/process'\nimport sequenceFlowPanel from './components/nodePanel/sequenceFlow'\nimport gatewayPanel from './components/nodePanel/gateway'\nimport { NodeName } from './lang/zh'\n\nexport default {\n  name: 'PropertyPanel',\n  components: { processPanel, taskPanel, startEndPanel, sequenceFlowPanel, gatewayPanel },\n  props: {\n    users: {\n      type: Array,\n      required: true\n    },\n    groups: {\n      type: Array,\n      required: true\n    },\n    categorys: {\n      type: Array,\n      required: true\n    },\n    modeler: {\n      type: Object,\n      required: true\n    }\n  },\n  data() {\n    return {\n      element: null,\n      form: {\n        id: '',\n        name: '',\n        color: null\n      },\n      roles: [\n        { value: 'manager', label: '经理' },\n        { value: 'personnel', label: '人事' },\n        { value: 'charge', label: '主管' }\n      ]\n    }\n  },\n  computed: {\n    getComponent() {\n      const type = this.element?.type\n      if (['bpmn:IntermediateThrowEvent', 'bpmn:StartEvent', 'bpmn:EndEvent'].includes(type)) {\n        return 'startEndPanel'\n      }\n      if ([\n        'bpmn:UserTask',\n        'bpmn:Task',\n        'bpmn:SendTask',\n        'bpmn:ReceiveTask',\n        'bpmn:ManualTask',\n        'bpmn:BusinessRuleTask',\n        'bpmn:ServiceTask',\n        'bpmn:ScriptTask'\n        // 'bpmn:CallActivity',\n        // 'bpmn:SubProcess'\n      ].includes(type)) {\n        return 'taskPanel'\n      }\n      if (type === 'bpmn:SequenceFlow') {\n        return 'sequenceFlowPanel'\n      }\n      if ([\n        'bpmn:InclusiveGateway',\n        'bpmn:ExclusiveGateway',\n        'bpmn:ParallelGateway',\n        'bpmn:EventBasedGateway'\n      ].includes(type)) {\n        return 'gatewayPanel'\n      }\n      if (type === 'bpmn:Process') {\n        return 'processPanel'\n      }\n      return null\n    },\n    nodeName() {\n      if (this.element) {\n        const bizObj = this.element.businessObject\n        const type = bizObj?.eventDefinitions\n          ? bizObj.eventDefinitions[0].$type\n          : bizObj.$type\n        return NodeName[type] || type\n      }\n      return ''\n    }\n  },\n  mounted() {\n    this.handleModeler()\n  },\n  methods: {\n    handleModeler() {\n      this.modeler.on('root.added', e => {\n        if (e.element.type === 'bpmn:Process') {\n          this.element = null\n          this.$nextTick().then(() => {\n            this.element = e.element\n          })\n        }\n      })\n      this.modeler.on('element.click', e => {\n        const { element } = e\n        console.log(element)\n        if (element.type === 'bpmn:Process') {\n          this.element = element\n        }\n      })\n      this.modeler.on('selection.changed', e => {\n        // hack 同类型面板不刷新\n        this.element = null\n        const element = e.newSelection[0]\n        if (element) {\n          this.$nextTick().then(() => {\n            this.element = element\n          })\n        }\n      })\n    },\n    /** 获取数据类型 */\n    dataType(data){\n      this.$emit('dataType', data)\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\">\n.property-panel {\n  padding: 20px 20px;\n  // reset element css\n  .el-form--label-top .el-form-item__label {\n    padding: 0;\n  }\n  .el-form-item {\n    margin-bottom: 6px;\n  }\n  .tab-table .el-form-item {\n    margin-bottom: 16px;\n  }\n  .node-name{\n    border-bottom: 1px solid #ccc;\n    padding: 0 0 10px 20px;\n    margin-bottom: 10px;\n    font-size: 16px;\n    font-weight: bold;\n    color: #444;\n  }\n}\n</style>\n"]}]}