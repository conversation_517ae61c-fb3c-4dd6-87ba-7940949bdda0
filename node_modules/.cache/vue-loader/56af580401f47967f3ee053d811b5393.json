{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/layout/components/Sidebar/Logo.vue?vue&type=template&id=6494804b&scoped=true", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/layout/components/Sidebar/Logo.vue", "mtime": 1660751098000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/loaders/templateLoader.js", "mtime": 1751171659981}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}