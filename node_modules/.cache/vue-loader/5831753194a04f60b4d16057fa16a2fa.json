{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Process/components/nodePanel/property/executionListener.vue?vue&type=style&index=0&id=4d7decf8&lang=css", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Process/components/nodePanel/property/executionListener.vue", "mtime": 1641277326000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/css-loader/dist/cjs.js", "mtime": 1751171659443}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/loaders/stylePostLoader.js", "mtime": 1751171659924}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/postcss-loader/src/index.js", "mtime": 1751171659554}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5mbG93LWNvbnRhaW5lcnMgIC5lbC1iYWRnZV9fY29udGVudC5pcy1maXhlZCB7CiAgICB0b3A6IDE4cHg7Cn0K"}, {"version": 3, "sources": ["executionListener.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8LA;AACA;AACA", "file": "executionListener.vue", "sourceRoot": "src/components/Process/components/nodePanel/property", "sourcesContent": ["<template>\n  <div>\n    <el-dialog\n      title=\"执行监听器\"\n      :visible.sync=\"dialogVisible\"\n      width=\"900px\"\n      :close-on-click-modal=\"false\"\n      :close-on-press-escape=\"false\"\n      :show-close=\"false\"\n      @closed=\"$emit('close')\"\n    >\n      <x-form ref=\"xForm\" v-model=\"formData\" :config=\"formConfig\">\n        <template #params=\"scope\">\n          <el-badge :value=\"scope.row.params ? scope.row.params.length : 0\" type=\"primary\">\n            <el-button size=\"small\" @click=\"configParam(scope.$index)\">配置</el-button>\n          </el-badge>\n        </template>\n      </x-form>\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" size=\"medium\" @click=\"closeDialog\">确 定</el-button>\n      </span>\n    </el-dialog>\n    <listenerParam v-if=\"showParamDialog\" :value=\"formData.executionListener[nowIndex].params\" @close=\"finishConfigParam\" />\n  </div>\n</template>\n\n<script>\nimport mixinPanel from '../../../common/mixinPanel'\nimport listenerParam from './listenerParam'\nexport default {\n  components: { listenerParam },\n  mixins: [mixinPanel],\n  data() {\n    return {\n      dialogVisible: true,\n      showParamDialog: false,\n      nowIndex: null,\n      formData: {\n        executionListener: []\n      }\n    }\n  },\n  computed: {\n    formConfig() {\n    //   const _this = this\n      return {\n        inline: false,\n        item: [\n          {\n            xType: 'tabs',\n            tabs: [\n              {\n                label: '执行监听器',\n                name: 'executionListener',\n                column: [\n                  {\n                    label: '事件',\n                    name: 'event',\n                    width: 180,\n                    rules: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],\n                    xType: 'select',\n                    dic: [\n                      { label: 'start', value: 'start' },\n                      { label: 'end', value: 'end' },\n                      { label: 'take', value: 'take' }\n                    ]\n                  },\n                  {\n                    label: '类型',\n                    name: 'type',\n                    width: 180,\n                    rules: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],\n                    xType: 'select',\n                    dic: [\n                      { label: '类', value: 'class' },\n                      { label: '表达式', value: 'expression' },\n                      { label: '委托表达式', value: 'delegateExpression' }\n                    ],\n                    tooltip: `类：示例 com.company.MyCustomListener，自定义类必须实现 org.flowable.engine.delegate.TaskListener 接口 <br />\n                              表达式：示例 \\${myObject.callMethod(task, task.eventName)} <br />\n                              委托表达式：示例 \\${myListenerSpringBean} ，该 springBean 需要实现 org.flowable.engine.delegate.TaskListener 接口\n                    `\n                  },\n                  {\n                    label: 'java 类名',\n                    name: 'className',\n                    xType: 'input',\n                    rules: [{ required: true, message: '请输入', trigger: ['blur', 'change'] }]\n                  },\n                  {\n                    xType: 'slot',\n                    label: '参数',\n                    width: 120,\n                    slot: true,\n                    name: 'params'\n                  }\n                ]\n              }\n            ]\n          }\n        ]\n      }\n    }\n  },\n  mounted() {\n    this.formData.executionListener = this.element.businessObject.extensionElements?.values\n      .filter(item => item.$type === 'flowable:ExecutionListener')\n      .map(item => {\n        let type\n        if ('class' in item) type = 'class'\n        if ('expression' in item) type = 'expression'\n        if ('delegateExpression' in item) type = 'delegateExpression'\n        return {\n          event: item.event,\n          type: type,\n          className: item[type],\n          params: item.fields?.map(field => {\n            let fieldType\n            if ('stringValue' in field) fieldType = 'stringValue'\n            if ('expression' in field) fieldType = 'expression'\n            return {\n              name: field.name,\n              type: fieldType,\n              value: field[fieldType]\n            }\n          }) ?? []\n        }\n      }) ?? []\n  },\n  methods: {\n    configParam(index) {\n      this.nowIndex = index\n      const nowObj = this.formData.executionListener[index]\n      if (!nowObj.params) {\n        nowObj.params = []\n      }\n      this.showParamDialog = true\n    },\n    finishConfigParam(param) {\n      this.showParamDialog = false\n      // hack 数量不更新问题\n      const cache = this.formData.executionListener[this.nowIndex]\n      cache.params = param\n      this.$set(this.formData.executionListener[this.nowIndex], this.nowIndex, cache)\n      this.nowIndex = null\n    },\n    updateElement() {\n      if (this.formData.executionListener?.length) {\n        let extensionElements = this.element.businessObject.get('extensionElements')\n        if (!extensionElements) {\n          extensionElements = this.modeler.get('moddle').create('bpmn:ExtensionElements')\n        }\n        // 清除旧值\n        extensionElements.values = extensionElements.values?.filter(item => item.$type !== 'flowable:ExecutionListener') ?? []\n        this.formData.executionListener.forEach(item => {\n          const executionListener = this.modeler.get('moddle').create('flowable:ExecutionListener')\n          executionListener['event'] = item.event\n          executionListener[item.type] = item.className\n          if (item.params && item.params.length) {\n            item.params.forEach(field => {\n              const fieldElement = this.modeler.get('moddle').create('flowable:Field')\n              fieldElement['name'] = field.name\n              fieldElement[field.type] = field.value\n              // 注意：flowable.json 中定义的string和expression类为小写，不然会和原生的String类冲突，此处为hack\n              // const valueElement = this.modeler.get('moddle').create(`flowable:${field.type}`, { body: field.value })\n              // fieldElement[field.type] = valueElement\n              executionListener.get('fields').push(fieldElement)\n            })\n          }\n          extensionElements.get('values').push(executionListener)\n        })\n        this.updateProperties({ extensionElements: extensionElements })\n      } else {\n        const extensionElements = this.element.businessObject[`extensionElements`]\n        if (extensionElements) {\n          extensionElements.values = extensionElements.values?.filter(item => item.$type !== 'flowable:ExecutionListener') ?? []\n        }\n      }\n    },\n    closeDialog() {\n      this.$refs.xForm.validate().then(() => {\n        this.updateElement()\n        this.dialogVisible = false\n      }).catch(e => console.error(e))\n    }\n  }\n}\n</script>\n\n<style>\n.flow-containers  .el-badge__content.is-fixed {\n    top: 18px;\n}\n</style>\n"]}]}