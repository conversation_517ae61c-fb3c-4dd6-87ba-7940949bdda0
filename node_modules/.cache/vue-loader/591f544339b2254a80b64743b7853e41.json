{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/flowable/definition/model.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/flowable/definition/model.vue", "mtime": 1662389806000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["model.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AA0BA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "model.vue", "sourceRoot": "src/views/flowable/definition", "sourcesContent": ["<template>\n  <div>\n    <bpmn-modeler\n      ref=\"refNode\"\n      :xml=\"xml\"\n      :users=\"users\"\n      :groups=\"groups\"\n      :categorys=\"categorys\"\n      :is-view=\"false\"\n      @save=\"save\"\n      @showXML=\"showXML\"\n      @dataType=\"dataType\"\n    />\n    <!--在线查看xml-->\n    <el-dialog :title=\"xmlTitle\" :visible.sync=\"xmlOpen\" width=\"60%\" append-to-body>\n      <div>\n        <pre v-highlight>\n           <code class=\"xml\">\n                {{xmlContent}}\n           </code>\n        </pre>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n<script>\nimport {readXml, roleList, saveXml, userList} from \"@/api/flowable/definition\";\nimport bpmnModeler from '@/components/Process/index'\nimport vkbeautify from 'vkbeautify'\nimport Hljs from 'highlight.js'\nimport 'highlight.js/styles/atom-one-dark.css'\n\nexport default {\n  name: \"Model\",\n  components: {\n    bpmnModeler,\n    vkbeautify\n  },\n  // 自定义指令\n  directives: {\n    highlight:(el) => {\n      let blocks = el.querySelectorAll('pre code');\n      blocks.forEach((block) => {\n        Hljs.highlightBlock(block)\n      })\n    }\n  },\n  data() {\n    return {\n      xml: \"\", // 后端查询到的xml\n      modeler:\"\",\n      xmlOpen: false,\n      xmlTitle: '',\n      xmlContent: '',\n      users: [],\n      groups: [],\n      categorys: [],\n\n    };\n  },\n  activated () {\n    const deployId = this.$route.query && this.$route.query.deployId;\n    //console.log(\"deployId :\"+deployId)\n    //  查询流程xml\n    if (deployId) {\n      //console.log(\"deployId :\"+deployId)\n      this.getModelDetail(deployId);\n    }\n    this.getDicts(\"sys_process_category\").then(res => {\n      this.categorys = res.data;\n    });\n    this.getDataList()\n  },\n  methods: {\n    /** xml 文件 */\n    getModelDetail(deployId) {\n      // 发送请求，获取xml\n      readXml(deployId).then(res =>{\n        this.xml = res.data;\n        this.modeler = res.data\n      })\n    },\n    /** 保存xml */\n    save(data) {\n      const params = {\n        name: data.process.name,\n        category: data.process.category,\n        xml: data.xml\n      }\n      saveXml(params).then(res => {\n        this.$message(res.msg)\n        // 关闭当前标签页并返回上个页面\n        this.$store.dispatch(\"tagsView/delView\", this.$route);\n        this.$router.go(-1)\n      })\n    },\n    /** 指定流程办理人员列表 */\n    getDataList() {\n      // todo 待根据部门选择人员\n      // const params = {\n      //\n      // }\n      userList().then(res =>{\n        res.data.forEach(val =>{\n          val.userId = val.userId.toString();\n        })\n        this.users = res.data;\n        let arr = {nickName: \"流程发起人\", userId: \"${INITIATOR}\"}\n        this.users.push(arr)\n      });\n      roleList().then(res =>{\n        res.data.forEach(val =>{\n          val.roleId = val.roleId.toString();\n        })\n        this.groups = res.data;\n      });\n    },\n    /** 展示xml */\n    showXML(data){\n      this.xmlTitle = 'xml查看';\n      this.xmlOpen = true;\n      this.xmlContent = vkbeautify.xml(data);\n    },\n    /** 获取数据类型 */\n    dataType(data){\n      this.users = [];\n      this.groups = [];\n      if (data) {\n        if (data.dataType === 'dynamic') {\n          if (data.userType === 'assignee') {\n            this.users = [{nickName: \"${INITIATOR}\", userId: \"${INITIATOR}\"},\n                          {nickName: \"#{approval}\", userId: \"#{approval}\"}\n              ]\n          } else if (data.userType === 'candidateUsers') {\n            this.users = [ {nickName: \"#{approval}\", userId: \"#{approval}\"}]\n          } else {\n            this.groups = [{roleName: \"#{approval}\", roleId: \"#{approval}\"}]\n          }\n        } else {\n          this.getDataList()\n        }\n      }\n    }\n  },\n};\n</script>\n"]}]}