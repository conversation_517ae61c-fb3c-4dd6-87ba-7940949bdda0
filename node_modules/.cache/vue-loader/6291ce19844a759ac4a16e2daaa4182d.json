{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/project/report/form.vue?vue&type=style&index=0&id=1f2aa81f&lang=css", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/project/report/form.vue", "mtime": 1753528817894}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/css-loader/dist/cjs.js", "mtime": 1751171659443}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/loaders/stylePostLoader.js", "mtime": 1751171659924}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/postcss-loader/src/index.js", "mtime": 1751171659554}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLmJveC1jYXJkIHsNCiAgd2lkdGg6IDEwMCU7DQogIG1hcmdpbi1ib3R0b206IDIwcHg7DQp9DQoNCkBtZWRpYSBzY3JlZW4gYW5kIChtYXgtd2lkdGg6IDU5OXB4KSB7DQogIC5lbC1mb3JtIC5lbC1jb2wgew0KICAgIHdpZHRoOiAxMDAlICFpbXBvcnRhbnQ7DQogIH0NCg0KICAuaW5mby10eXBlIC5lbC1yb3cgew0KICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogIH0NCg0KICAuaW5mby10eXBlIC5lbC1pbnB1dCB7DQogICAgd2lkdGg6IDEwMCUgIWltcG9ydGFudDsNCiAgfQ0KDQogIC5tb2JpbGUtd2lkdGggew0KICAgIHdpZHRoOiAxMDAlICFpbXBvcnRhbnQ7DQogIH0NCg0KICAuYXBwLWNvbnRhaW5lciB7DQogICAgcGFkZGluZzogMCAhaW1wb3J0YW50Ow0KICB9DQp9DQo="}, {"version": 3, "sources": ["form.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAm4BA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "form.vue", "sourceRoot": "src/views/project/report", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-card class=\"box-card\">\r\n      <div slot=\"header\" class=\"clearfix\">\r\n        <span class=\"el-icon-document\">项目报备流程</span>\r\n        <span style=\"float: right;\">\r\n          <el-button icon=\"el-icon-edit-outline\" type=\"success\" v-if=\"audit\" @click=\"handleComplete\">审批</el-button>\r\n          <el-button icon=\"el-icon-refresh-left\" type=\"warning\" v-if=\"audit\" @click=\"handleReturn\">退回</el-button>\r\n          <el-button type=\"primary\" @click=\"goBack\">返回</el-button>\r\n        </span>\r\n\r\n      </div>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" :disabled=\"!formEdit\" label-width=\"120px\">\r\n\r\n        <!-- <el-row> -->\r\n        <!-- <el-col :span=\"12\">\r\n            <el-form-item label=\"编辑状态\">\r\n              <el-radio-group v-model=\"form.editStatus\">\r\n                <el-radio\r\n                  v-for=\"dict in editStatusOptions\"\r\n                  :key=\"dict.dictValue\"\r\n                  :label=\"dict.dictValue\"\r\n                  >{{ dict.dictLabel }}</el-radio\r\n                >\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col> -->\r\n        <!-- <el-col :span=\"12\">\r\n            <el-form-item label=\"所属用户\" prop=\"belongUser\">\r\n              <el-select v-model=\"form.belongUser\" placeholder=\"请选择所属用户\">\r\n                <el-option label=\"请选择字典生成\" value=\"\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col> -->\r\n        <!-- </el-row> -->\r\n        <!-- <el-row>\r\n          <el-col :span=\"20\">\r\n            <el-form-item label=\"驳回原因\" prop=\"rejectReason\">\r\n              <el-input\r\n                v-model=\"form.rejectReason\"\r\n                type=\"textarea\"\r\n                placeholder=\"请输入内容\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row> -->\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"项目编号\" prop=\"projectNo\">\r\n              <el-input v-model=\"form.projectNo\" placeholder=\"若无编号则为当前时间(年月日时间)\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"项目名称\" prop=\"projectName\">\r\n              <el-input v-model=\"form.projectName\" placeholder=\"请输入项目名称\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"项目所在地\" prop=\"province\">\r\n              <el-cascader :options=\"options\" clearable :props=\"{ expandTrigger: 'hover' }\" v-model=\"selectedOptions\"\r\n                @change=\"handleChange\">\r\n              </el-cascader>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"详细地址\" prop=\"address\">\r\n              <el-input v-model=\"form.address\" placeholder=\"请输入详细地址\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"项目所属省份\" prop=\"belongProvince\">\r\n              <el-select v-model=\"form.belongProvince\" clearable placeholder=\"请选择所属省份\">\r\n                <el-option v-for=\"item in belongProvinceOptions\" :key=\"item.value\" :label=\"item.label\"\r\n                  :value=\"item.value\">\r\n                </el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <!-- <el-form-item label=\"招标单位联系人/联系电话\" prop=\"biddingContact\">\r\n              <el-input v-model=\"form.biddingContact\" placeholder=\"请输入联系人/联系电话\" />\r\n            </el-form-item> -->\r\n            <el-form-item label=\"招标单位\" prop=\"biddingCompany\">\r\n              <el-input v-model=\"form.biddingCompany\" placeholder=\"请输入招标单位\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"挂网日期\" prop=\"hangDate\">\r\n              <el-input v-model=\"form.hangDate\" placeholder=\"请输入挂网日期，没有请写无，日期格式XXXX（年）-XX（月）-XX（日\" />\r\n              <!-- <el-date-picker clearable size=\"small\" v-model=\"form.hangDate\" type=\"date\" value-format=\"yyyy-MM-dd\"\r\n                placeholder=\"选择挂网日期\">\r\n              </el-date-picker> -->\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"开标日期\" prop=\"openDate\">\r\n              <el-input v-model=\"form.openDate\" placeholder=\"请输入开标日期，没有请写无，日期格式XXXX（年）-XX（月）-XX（日\" />\r\n              <!-- <el-date-picker clearable size=\"small\" v-model=\"form.openDate\" type=\"date\" value-format=\"yyyy-MM-dd\"\r\n                placeholder=\"选择开标日期\">\r\n              </el-date-picker> -->\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"所属经销商\" prop=\"distributor\">\r\n              <el-input v-model=\"form.distributor\" placeholder=\"请输入经销商\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <!-- <el-form-item label=\"招标单位联系人/联系电话\" prop=\"biddingContact\">\r\n              <el-input v-model=\"form.biddingContact\" placeholder=\"请输入联系人/联系电话\" />\r\n            </el-form-item> -->\r\n            <el-form-item label=\"售后年限\" prop=\"afterSaleYear\">\r\n              <el-select v-model=\"form.afterSaleYear\" clearable placeholder=\"请选择售后年限\">\r\n                <el-option v-for=\"item in afterSaleYearOptions\" :key=\"item.value\" :label=\"item.label\"\r\n                  :value=\"item.value\">\r\n                </el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <!-- <el-col :span=\"12\">\r\n            <el-form-item label=\"预算金额\" prop=\"budgetMoney\">\r\n              <el-input\r\n                type=\"number\"\r\n                v-model=\"form.budgetMoney\"\r\n                placeholder=\"请输入预算金额\"\r\n              />\r\n            </el-form-item>\r\n          </el-col> -->\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"被授权公司\" prop=\"authCompany\">\r\n              <el-input v-model=\"form.authCompany\" placeholder=\"请输入授权公司\" />\r\n              <el-link @click=\"addDomain\" type=\"primary\" :disabled=\"!formEdit\">添加</el-link>\r\n            </el-form-item>\r\n            <el-form-item v-for=\"(company, index) in authCompanys\" :label=\"'被授权公司' + (index + 1)\" :key=\"company.key\">\r\n              <el-input v-model=\"company.value\" :placeholder=\"'被授权公司' + (index + 1)\" style=\"max-width:300px\" />\r\n              <el-link @click=\"removeDomain(index)\" type=\"primary\" :disabled=\"!formEdit\">删除</el-link>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"授权公司联系人/联系电话\" prop=\"authContact\">\r\n              <el-input v-model=\"form.authContact\" placeholder=\"请输入授权公司联系人/联系电话\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <!-- <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"招标信息公布网站\" prop=\"biddingNet\">\r\n              <el-input\r\n                v-model=\"form.biddingNet\"\r\n                placeholder=\"请输入招标信息公布网站\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"招标单位\" prop=\"biddingCompany\">\r\n              <el-input\r\n                v-model=\"form.biddingCompany\"\r\n                placeholder=\"请输入招标单位\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row> -->\r\n        <!-- <el-row v-if=\"form.operationType == 2\">\r\n          <el-col :span=\"20\">\r\n            <el-form-item label=\"模板下载\">\r\n              <el-col :span=\"8\">\r\n                <el-link @click=\"downloadSQS\" type=\"primary\" :disabled=\"!formEdit\">海佳集团-授权书.docx</el-link>\r\n              </el-col>\r\n              <el-col :span=\"8\">\r\n                <el-link @click=\"downloadCRH\" type=\"primary\" :disabled=\"!formEdit\">海佳集团-售后服务承诺函.docx</el-link>\r\n              </el-col>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row> -->\r\n        <!-- <el-form-item label=\"授权书\" v-if=\"form.operationType == 2\" :required=\"form.operationType == 2\">\r\n          <fileUpload v-model=\"form.authFile\" :fileType=\"['doc', 'docx']\" />\r\n        </el-form-item> -->\r\n        <el-form-item label=\"其余附件\">\r\n          <span style=\"color: red;\">请勿上传项目授权书、售后声明函</span>\r\n          <fileUpload v-model=\"form.afterSaleFile\" :fileType=\"['doc', 'docx']\" />\r\n        </el-form-item>\r\n        <el-row>\r\n          <el-col :span=\"20\">\r\n            <el-form-item label=\"投标产品型号\" prop=\"model\" :required=\"true\">\r\n              <el-cascader class=\"mobile-width\" style=\"width: 700px\" v-model=\"form.model\" placeholder=\"可输入产品型号搜索\"\r\n                :options=\"modelOptions\" :props=\"{ multiple: true }\" clearable filterable></el-cascader>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"20\">\r\n            <el-form-item label=\"投标产品规格\" prop=\"spec\" :required=\"true\">\r\n              <el-cascader class=\"mobile-width\" style=\"width: 700px\" v-model=\"form.spec\" placeholder=\"可输入产品规格搜索\"\r\n                :options=\"specOptions\" :props=\"{ multiple: true }\" clearable filterable></el-cascader>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"安装面积\" prop=\"area\">\r\n              <el-input v-model=\"form.area\" type=\"number\" placeholder=\"请输入安装面积\">\r\n                <template slot=\"append\">m²</template>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"20\">\r\n            <el-form-item label=\"所需资料\">\r\n              <el-cascader class=\"mobile-width\" style=\"width: 700px\" v-model=\"form.requireInfo\" placeholder=\"可输入资料类型搜索\"\r\n                :options=\"requireInfoOptions\" :props=\"{ multiple: true }\" clearable filterable></el-cascader>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <!-- <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"资料类型\">\r\n              <el-checkbox-group v-model=\"form.infoType\">\r\n                <el-checkbox\r\n                  v-for=\"dict in infoTypeOptions\"\r\n                  :key=\"dict.dictValue\"\r\n                  :label=\"dict.dictValue\"\r\n                >\r\n                  {{ dict.dictLabel }}\r\n                </el-checkbox>\r\n              </el-checkbox-group>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row> -->\r\n        <el-row>\r\n          <el-form-item label=\"资料接收方式\" prop=\"infoType\" :required=\"true\">\r\n            <el-checkbox-group v-model=\"form.infoType\" class=\"info-type\">\r\n              <!-- 选项A -->\r\n              <el-row style=\"display:flex;margin-bottom: 22px;\">\r\n                <el-col :span=\"12\" style=\"display:flex;\">\r\n                  <el-checkbox label=\"1\" style=\"margin-left:20px;margin-right:10px !important;\">邮件</el-checkbox>\r\n                  <el-form-item prop=\"scanFile\">\r\n                    <el-input v-model=\"form.scanFile\" placeholder=\"请输入邮箱地址\" style=\"width:300px;\"\r\n                      type=\"email\"></el-input>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"12\">\r\n                  <el-form-item label=\"邮件发送信息\" prop=\"mailInfo\">\r\n                    <el-input v-model=\"form.mailInfo\" placeholder=\"请输入邮件发送信息\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <!-- 选项B  -->\r\n              <el-row style=\"display:flex;margin-bottom: 22px;\">\r\n                <el-col :span=\"12\" style=\"display:flex;\">\r\n                  <el-checkbox label=\"2\" style=\"margin-left:20px;margin-right:10px !important;\">邮寄</el-checkbox>\r\n                  <el-form-item prop=\"sendAddress\">\r\n                    <el-input v-model=\"form.sendAddress\" placeholder=\"请输入收件地址\" style=\"width:300px;\"></el-input>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"12\">\r\n                  <el-form-item label=\"快递单号\" prop=\"expressInfo\">\r\n                    <el-input v-model=\"form.expressInfo\" placeholder=\"请输入快递单号\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </el-checkbox-group>\r\n          </el-form-item>\r\n        </el-row>\r\n        <!-- <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"邮件发送信息\" prop=\"mailInfo\">\r\n              <el-input\r\n                v-model=\"form.mailInfo\"\r\n                placeholder=\"请输入邮件发送信息\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"快递单号\" prop=\"expressInfo\">\r\n              <el-input\r\n                v-model=\"form.expressInfo\"\r\n                placeholder=\"请输入快递单号\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row> -->\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"操作类型\" prop=\"operationType\">\r\n              <el-radio-group v-model=\"form.operationType\">\r\n                <el-radio v-for=\"dict in operationTypeOptions\" :key=\"dict.dictValue\"\r\n                  :label=\"dict.dictValue\">{{ dict.dictLabel }}</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col>\r\n          <!-- <el-col :span=\"12\">\r\n            <el-form-item label=\"审核状态\">\r\n              <el-radio-group v-model=\"form.auditStatus\">\r\n                <el-radio\r\n                  v-for=\"dict in auditStatusOptions\"\r\n                  :key=\"dict.dictValue\"\r\n                  :label=\"dict.dictValue\"\r\n                  >{{ dict.dictLabel }}</el-radio\r\n                >\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col> -->\r\n        </el-row>\r\n        <el-form-item label=\"备注\" prop=\"remark\">\r\n          <el-input v-model=\"form.remark\" type=\"textarea\" placeholder=\"请输入备注\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <el-col :span=\"16\" :offset=\"8\" v-if=\"formEdit\">\r\n        <div style=\"margin-left:10%;margin-bottom: 20px;font-size: 14px;\">\r\n          <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n          <el-button @click=\"goBack\">取 消</el-button>\r\n        </div>\r\n      </el-col>\r\n    </el-card>\r\n    <flowable :key=\"businessKey\" ref=\"flow\" procDefKey=\"process_project_report\" :procInsId=\"procInsId\" :taskId=\"taskId\"\r\n      :finished=\"finished\"></flowable>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  getReport,\r\n  addReport,\r\n  updateReport,\r\n  checkNameUnique\r\n} from \"@/api/project/report\";\r\nimport FileUpload from \"@/components/FileUpload\";\r\nimport flowable from '@/views/flowable/task/record/index'\r\nimport { regionData, CodeToText, TextToCode } from \"element-china-area-data\";\r\nimport print from \"print-js\";\r\nexport default {\r\n  name: \"Report\",\r\n  components: {\r\n    flowable,\r\n    FileUpload,\r\n    print\r\n  },\r\n  data() {\r\n    var that = this;\r\n    var infoTypeValueVali = (rule, value, callback) => {\r\n      if (that.form.infoType.indexOf('1') >= 0 && !that.form.scanFile) {\r\n        callback(new Error(\"邮箱地址必填\"));\r\n        return;\r\n      }\r\n      callback();\r\n    };\r\n    var infoTypeValueVali2 = (rule, value, callback) => {\r\n      if (that.form.infoType.indexOf('2') >= 0 && !that.form.sendAddress) {\r\n        callback(new Error(\"收件地址必填\"));\r\n        return;\r\n      }\r\n      callback();\r\n    };\r\n    var nameVali = (rule, value, callback) => {\r\n      if (!that.form.projectName) {\r\n        callback(new Error(\"项目名称必填\"));\r\n      } else {\r\n        if (/\\s+/g.test(that.form.projectName)) {\r\n          callback(new Error(\"项目名称不规范\"));\r\n          return;\r\n        }\r\n        checkNameUnique({ projectName: that.form.projectName, projectId: that.form.projectId }).then((response) => {\r\n          if (response.data == 0) {\r\n            callback();\r\n          } else {\r\n            callback(new Error(\"项目名称已存在\"));\r\n          }\r\n        })\r\n      }\r\n    };\r\n    var codeVali = (rule, value, callback) => {\r\n      if (!that.form.projectNo) {\r\n        callback(new Error(\"项目编号必填\"));\r\n      } else if (/\\s+/g.test(that.form.projectNo)) {\r\n        callback(new Error(\"项目编号不规范\"));\r\n        return;\r\n      }\r\n      callback();\r\n    };\r\n    var openDateVali = (rule, value, callback) => {\r\n      if (!that.form.openDate) {\r\n        callback(new Error(\"开标日期必填\"));\r\n        return;\r\n      } else if (value === \"无\") {\r\n        callback();\r\n        return;\r\n      } else if (!/^\\d{4}-\\d{2}-\\d{2}$/.test(value)) {\r\n        callback(new Error(\"开标日期格式不合法，示例2025-01-01\"));\r\n        return;\r\n      }\r\n      callback();\r\n    };\r\n    var hangDateVali = (rule, value, callback) => {\r\n      if (!that.form.hangDate) {\r\n        callback(new Error(\"挂网日期必填\"));\r\n        return;\r\n      } else if (value === \"无\") {\r\n        callback();\r\n        return;\r\n      } else if (!/^\\d{4}-\\d{2}-\\d{2}$/.test(value)) {\r\n        callback(new Error(\"挂网日期格式不合法，示例2025-01-01\"));\r\n        return;\r\n      }\r\n      callback();\r\n    };\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 操作类型字典\r\n      operationTypeOptions: [],\r\n      // 审核状态字典\r\n      auditStatusOptions: [],\r\n      // 编辑状态字典\r\n      editStatusOptions: [],\r\n      // 招标方式字典\r\n      biddingTypeOptions: [],\r\n      // 投标产品型号字典\r\n      modelOptions: [],\r\n      modelOption1: [],\r\n      // 所需资料字典\r\n      requireInfoOptions: [],\r\n      requireInfoOption1: [],\r\n      // 资料类型字典\r\n      infoTypeOptions: [],\r\n      // 所属省份字典\r\n      belongProvinceOptions: [],\r\n      belongProvinceOptions1: [],\r\n      // 售后年限\r\n      afterSaleYearOptions: [],\r\n      afterSaleYearOptions1: [],\r\n      specOptions: [],\r\n      specOption1: [],\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      // 表单校验\r\n      rules: {\r\n        operationType: [{ required: true, message: \"操作类型必选\" }],\r\n        projectNo: [\r\n          { validator: codeVali, required: true, trigger: \"blur\" },\r\n        ],\r\n        projectName: [\r\n          { validator: nameVali, required: true, trigger: \"blur\" },\r\n        ],\r\n        address: [\r\n          { required: true, message: \"详细地址不能为空\", trigger: \"blur\" },\r\n        ],\r\n        biddingCompany: [\r\n          { required: true, message: \"招标单位不能为空\", trigger: \"blur\" },\r\n        ],\r\n        openDate: [\r\n          { required: true, validator: openDateVali, trigger: \"blur\" },\r\n        ],\r\n        afterSaleYear: [\r\n          { required: true, message: \"售后年限不能为空\", trigger: \"blur\" },\r\n        ],\r\n        hangDate: [\r\n          { required: true, validator: hangDateVali, trigger: \"blur\" },\r\n        ],\r\n        belongProvince: [\r\n          { required: true, message: \"售后年限不能为空\", trigger: \"blur\" },\r\n        ],\r\n        distributor: [\r\n          { required: true, message: \"所属经销商不能为空\", trigger: \"blur\" },\r\n        ],\r\n        scanFile: [\r\n          { validator: infoTypeValueVali, trigger: \"blur\" },\r\n        ],\r\n        sendAddress: [\r\n          { validator: infoTypeValueVali2, trigger: \"blur\" },\r\n        ],\r\n        model: [\r\n          { required: true, message: \"投标产品型号必选\" },\r\n        ],\r\n        spec: [\r\n          { required: true, message: \"投标产品规格必选\" },\r\n        ],\r\n        province: [\r\n          { required: true, message: \"项目所在地必选\" },\r\n        ],\r\n        infoType: [\r\n          { required: true, message: \"资料接收方式必选\", trigger: \"change\" },\r\n        ],\r\n        biddingContact: [\r\n          { required: true, message: \"招标单位联系人/联系电话必填\" },\r\n        ],\r\n        authContact: [\r\n          { required: true, message: \"授权公司联系人/联系电话必填\" },\r\n        ]\r\n      },\r\n      options: regionData,\r\n      selectedOptions: [],\r\n      queryArea: [],\r\n      //工作流参数\r\n      finished: 'false',\r\n      taskId: undefined,\r\n      procInsId: undefined,\r\n      businessKey: undefined,\r\n      audit: false,\r\n      formEdit: false,\r\n      //工作流参数end\r\n      authCompanys: [],\r\n    };\r\n  },\r\n  activated() {\r\n\r\n    this.reset();\r\n    //this.form.projectNo = moment(new Date()).format('YYYYMMDDHHmm');\r\n    this.taskId = this.$route.query && this.$route.query.taskId;\r\n    this.procInsId = this.$route.query && this.$route.query.procInsId;\r\n    this.finished = this.$route.query && this.$route.query.finished;\r\n    this.businessKey = this.$route.query && this.$route.query.businessKey;\r\n    let edit = this.$route.query && this.$route.query.formEdit;\r\n    if (edit == \"true\") {\r\n      this.formEdit = true;\r\n    } else {\r\n      this.formEdit = false;\r\n    }\r\n    if (this.businessKey) {\r\n      if (this.finished == \"false\" && !this.formEdit) {\r\n        this.audit = true;\r\n      }\r\n      this.getReportInfo(this.businessKey);\r\n    }\r\n    console.log(\"========project=========>activated>formEdit>>\" + this.formEdit);\r\n  },\r\n  created() {\r\n\r\n    this.reset();\r\n    //this.form.projectNo = moment(new Date()).format('YYYYMMDDHHmm');\r\n    this.taskId = this.$route.query && this.$route.query.taskId;\r\n    this.procInsId = this.$route.query && this.$route.query.procInsId;\r\n    this.finished = this.$route.query && this.$route.query.finished;\r\n    this.businessKey = this.$route.query && this.$route.query.businessKey;\r\n    let edit = this.$route.query && this.$route.query.formEdit;\r\n    if (edit == \"true\") {\r\n      this.formEdit = true;\r\n    } else {\r\n      this.formEdit = false;\r\n    }\r\n    if (this.businessKey) {\r\n      if (this.finished == \"false\" && !this.formEdit) {\r\n        this.audit = true;\r\n      }\r\n      this.getReportInfo(this.businessKey);\r\n    }\r\n    // this.audit = true;\r\n    console.log(\"=========project========>created>>formEdit>\" + this.formEdit);\r\n\r\n    this.getDicts(\"pr_operation_type\").then((response) => {\r\n      this.operationTypeOptions = response.data;\r\n    });\r\n    this.getDicts(\"pr_audit_status\").then((response) => {\r\n      this.auditStatusOptions = response.data;\r\n    });\r\n    this.getDicts(\"pr_edit_status\").then((response) => {\r\n      this.editStatusOptions = response.data;\r\n    });\r\n    // this.getDicts(\"pr_bidding_type\").then((response) => {\r\n    //   this.biddingTypeOptions = response.data;\r\n    // });\r\n    this.getDicts(\"pr_model\").then((response) => {\r\n      this.modelOption1 = response.data;\r\n      var opt = [];\r\n      response.data.forEach((elem, index) => {\r\n        var obj = {};\r\n        obj.label = elem.dictLabel;\r\n        obj.value = elem.dictValue;\r\n        opt.push(obj);\r\n      });\r\n      this.modelOptions = opt;\r\n    });\r\n    this.getDicts(\"pr_spec\").then((response) => {\r\n      this.specOption1 = response.data;\r\n      var opt = [];\r\n      response.data.forEach((elem, index) => {\r\n        var obj = {};\r\n        obj.label = elem.dictLabel;\r\n        obj.value = elem.dictValue;\r\n        opt.push(obj);\r\n      });\r\n      this.specOptions = opt;\r\n    });\r\n    this.getDicts(\"pr_info\").then((response) => {\r\n      this.requireInfoOption1 = response.data;\r\n      var opt = [];\r\n      response.data.forEach((elem, index) => {\r\n        var obj = {};\r\n        obj.label = elem.dictLabel;\r\n        obj.value = elem.dictValue;\r\n        opt.push(obj);\r\n      });\r\n      this.requireInfoOptions = opt;\r\n    });\r\n    this.getDicts(\"pr_province\").then((response) => {\r\n      this.belongProvinceOptions1 = response.data;\r\n      var opt = [];\r\n      response.data.forEach((elem, index) => {\r\n        var obj = {};\r\n        obj.label = elem.dictLabel;\r\n        obj.value = elem.dictValue;\r\n        opt.push(obj);\r\n      });\r\n      this.belongProvinceOptions = opt;\r\n    });\r\n    this.getDicts(\"pr_after_sale_year\").then((response) => {\r\n      this.afterSaleYearOptions1 = response.data;\r\n      var opt = [];\r\n      response.data.forEach((elem, index) => {\r\n        var obj = {};\r\n        obj.label = elem.dictLabel;\r\n        obj.value = elem.dictValue;\r\n        opt.push(obj);\r\n      });\r\n      this.afterSaleYearOptions = opt;\r\n    });\r\n    this.getDicts(\"pr_data_type\").then((response) => {\r\n      this.infoTypeOptions = response.data;\r\n    });\r\n    //默认报备\r\n    this.form.operationType = '1';\r\n  },\r\n  methods: {\r\n\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        projectId: null,\r\n        projectNo: null,\r\n        projectName: null,\r\n        operationType: 1,\r\n        auditStatus: \"1\",\r\n        rejectReason: null,\r\n        province: null,\r\n        city: null,\r\n        district: null,\r\n        address: null,\r\n        editStatus: \"0\",\r\n        belongUser: null,\r\n        biddingCompany: null,\r\n        openDate: null,\r\n        belongProvince: null,\r\n        afterSaleYear: null,\r\n        hangDate: null,\r\n        biddingType: null,\r\n        budgetMoney: null,\r\n        authCompany: null,\r\n        biddingNet: null,\r\n        distributor: null,\r\n        model: [],\r\n        spec: [],\r\n        area: null,\r\n        authFile: null,\r\n        afterSaleFile: null,\r\n        requireInfo: [],\r\n        infoType: [],\r\n        scanFile: null,\r\n        sendAddress: null,\r\n        mailInfo: null,\r\n        expressInfo: null,\r\n        remark: null,\r\n        spare1: null,\r\n        spare2: null,\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map((item) => item.projectId);\r\n      this.single = selection.length !== 1;\r\n      this.multiple = !selection.length;\r\n      console.info(selection);\r\n    },\r\n\r\n    /** 修改按钮操作 */\r\n    getReportInfo(projectId) {\r\n      getReport(projectId).then((response) => {\r\n        this.form = response.data;\r\n        if (this.form.model) this.form.model = this.form.model.split(\",\");\r\n        else this.form.model = [];\r\n        if (this.form.requireInfo)\r\n          this.form.requireInfo = this.form.requireInfo.split(\",\");\r\n        else this.form.requireInfo = [];\r\n        if (this.form.infoType)\r\n          this.form.infoType = this.form.infoType.split(\",\");\r\n        else this.form.infoType = [];\r\n        if (this.form.spec) this.form.spec = this.form.spec.split(\",\");\r\n        else this.form.spec = [];\r\n        var provinces = response.data.province;\r\n        if (provinces.length > 0) {\r\n          var address = provinces.split(\"/\");\r\n          var citys = [];\r\n          // 省份\r\n          if (address.length > 0) citys.push(TextToCode[address[0]].code);\r\n          // 城市\r\n          if (address.length > 1)\r\n            citys.push(TextToCode[address[0]][address[1]].code);\r\n          // 地区\r\n          if (address.length > 2)\r\n            citys.push(TextToCode[address[0]][address[1]][address[2]].code);\r\n\r\n          this.selectedOptions = citys;\r\n        }\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      let that = this;\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n          if (this.form.infoType.indexOf('1') >= 0 && this.form.scanFile) {\r\n            let emailReg = /^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\\.[a-zA-Z0-9_-]+)+$/;\r\n            if (!emailReg.test(this.form.scanFile)) {\r\n              this.$message.error(\"资料接收方式邮箱格式错误\");\r\n              return;\r\n            }\r\n          }\r\n          // if (this.form.operationType == 2 && !this.form.authFile) {\r\n          //   this.$message.error(\"授权类型必需上传授权书\");\r\n          //   return;\r\n          // }\r\n          var formStr = JSON.stringify(this.form);\r\n          var formData = JSON.parse(formStr);\r\n          if (formData.model && formData.model.length > 0)\r\n            formData.model = formData.model.join(\",\");\r\n          else formData.model = undefined;\r\n          if (formData.requireInfo && formData.requireInfo.length > 0)\r\n            formData.requireInfo = formData.requireInfo.join(\",\");\r\n          else formData.requireInfo = undefined;\r\n          if (formData.infoType && formData.infoType.length > 0)\r\n            formData.infoType = formData.infoType.join(\",\");\r\n          else formData.infoType = undefined;\r\n          if (formData.spec && formData.spec.length > 0)\r\n            formData.spec = formData.spec.join(\",\");\r\n          else formData.spec = undefined;\r\n\r\n          //授权公司\r\n          if (this.authCompanys.length > 0) {\r\n            var array = new Array();\r\n            this.authCompanys.forEach(function (e) {\r\n              array.push(e.value);\r\n            })\r\n            formData.authCompany += \",\" + array.join(\",\")\r\n          }\r\n\r\n          const loading = this.$loading({\r\n            lock: true,//lock的修改符--默认是false\r\n            text: 'Loading',//显示在加载图标下方的加载文案\r\n            spinner: 'el-icon-loading',//自定义加载图标类名\r\n            background: 'rgba(0, 0, 0, 0.7)',//遮罩层颜色\r\n            target: document.querySelector('.main-container')//loadin覆盖的dom元素节点\r\n          });\r\n          document.documentElement.style.overflowY = 'hidden' //禁止底层div滚动\r\n\r\n          if (formData.projectId != null) {\r\n            updateReport(formData).then((response) => {\r\n              //this.msgSuccess(\"修改成功\");\r\n              if (that.businessKey) {\r\n                that.$refs['flow'].taskComplete(\"重新提交\");\r\n              } else {\r\n                that.startFlow(formData);\r\n              }\r\n              setTimeout(() => {\r\n                loading.close();\r\n                document.documentElement.style.overflowY = 'auto' //允许底层div滚动\r\n              }, 1000);\r\n            }).catch(res => {\r\n              console.log(res)\r\n              loading.close();\r\n              document.documentElement.style.overflowY = 'auto' //允许底层div滚动\r\n            });\r\n          } else {\r\n            addReport(formData).then((response) => {\r\n              console.log(\"===addReport=>>>\")\r\n              //this.msgSuccess(\"新增成功\");\r\n              formData.projectId = response.data;\r\n              that.form.projectId = response.data;\r\n              that.startFlow(formData);\r\n              setTimeout(() => {\r\n                loading.close();\r\n                document.documentElement.style.overflowY = 'auto' //允许底层div滚动\r\n              }, 1000);\r\n            }).catch(res => {\r\n              console.log(res)\r\n              loading.close();\r\n              document.documentElement.style.overflowY = 'auto' //允许底层div滚动\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    startFlow(formData) {\r\n      //项目区域\r\n      //var area = formData.district;\r\n      //用户区域 6-11修改，根据用户所在区域判断\r\n      var variables = {};\r\n      variables.PROCESS_AREA = this.$store.state.user.province;\r\n      //是否省负责人角色\r\n      if (this.$store.state.user.roles && this.$store.state.user.roles.includes(\"province_admin\")) {\r\n        variables.isManage = 1;\r\n      } else {\r\n        variables.isManage = 0;\r\n      }\r\n      //新增全走报备审批流程\r\n      // if(formData.operationType == '2'){\r\n      //   variables.isAuth = true;\r\n      // }else{\r\n      //   variables.isAuth = false;\r\n      // }\r\n      //variables.isAuth = false;\r\n      variables.BUSINESSKEY = formData.projectId;\r\n      var taskName = \"项目报备\";\r\n      if (formData.operationType == '2') {\r\n        taskName = \"项目授权\";\r\n      }\r\n      this.$refs['flow'].startFlow(formData.projectId, taskName, variables);\r\n    },\r\n    downloadSQS() {\r\n      this.download(\"海佳集团-授权书.docx\", false);\r\n    },\r\n    downloadCRH() {\r\n      this.download(\"海佳集团-售后服务承诺函.doc\", false);\r\n    },\r\n    handleChange(value) {\r\n      if (!value || value.length == 0) {\r\n        this.selectedOptions = null;\r\n        this.form.province = undefined;\r\n        this.form.district = undefined;\r\n        return\r\n      }\r\n      this.selectedOptions = value;\r\n      var txt = \"\";\r\n      value.forEach(function (item) {\r\n        txt += CodeToText[item] + \"/\";\r\n      });\r\n      if (txt.length > 1) {\r\n        txt = txt.substring(0, txt.length - 1);\r\n        this.form.province = txt;\r\n        this.form.district = this.$store.state.user.province;\r\n      } else {\r\n        this.form.province = undefined;\r\n        this.form.district = undefined;\r\n      }\r\n    },\r\n    handleQueryCityChange(value) {\r\n      this.queryArea = value;\r\n      var txt = \"\";\r\n      value.forEach(function (item) {\r\n        txt += CodeToText[item] + \"/\";\r\n      });\r\n      if (txt.length > 1) {\r\n        txt = txt.substring(0, txt.length - 1);\r\n        this.queryParams.province = txt;\r\n      } else {\r\n        this.queryParams.province = undefined;\r\n      }\r\n    },\r\n    /** 审批 */\r\n    handleComplete() {\r\n      this.$refs['flow'].handleComplete();\r\n    },\r\n    /** 退回 */\r\n    handleReturn() {\r\n      this.$refs['flow'].handleReturn();\r\n    },\r\n    /** 返回页面 */\r\n    goBack() {\r\n      // 关闭当前标签页并返回上个页面\r\n      this.$store.dispatch(\"tagsView/delView\", this.$route);\r\n      this.$router.go(-1)\r\n    },\r\n    removeDomain(index) {\r\n      if (index !== -1) {\r\n        this.authCompanys.splice(index, 1)\r\n      }\r\n    },\r\n    addDomain() {\r\n      this.authCompanys.push({\r\n        value: '',\r\n        key: Date.now()\r\n      });\r\n    }\r\n  },\r\n};\r\n</script>\r\n<style>\r\n.box-card {\r\n  width: 100%;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n@media screen and (max-width: 599px) {\r\n  .el-form .el-col {\r\n    width: 100% !important;\r\n  }\r\n\r\n  .info-type .el-row {\r\n    flex-direction: column;\r\n  }\r\n\r\n  .info-type .el-input {\r\n    width: 100% !important;\r\n  }\r\n\r\n  .mobile-width {\r\n    width: 100% !important;\r\n  }\r\n\r\n  .app-container {\r\n    padding: 0 !important;\r\n  }\r\n}\r\n</style>"]}]}