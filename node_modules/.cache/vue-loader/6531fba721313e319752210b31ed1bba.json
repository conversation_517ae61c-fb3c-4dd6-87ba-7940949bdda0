{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Process/index.vue?vue&type=style&index=0&id=71e54f9c&lang=scss", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Process/index.vue", "mtime": 1661782128000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/css-loader/dist/cjs.js", "mtime": 1751171659443}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/loaders/stylePostLoader.js", "mtime": 1751171659924}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/postcss-loader/src/index.js", "mtime": 1751171659554}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/sass-loader/dist/cjs.js", "mtime": 1751171659051}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi8q5bem6L655bel5YW35qCP5Lul5Y+K57yW6L6R6IqC54K555qE5qC35byPKi8KQGltcG9ydCAifmJwbW4tanMvZGlzdC9hc3NldHMvZGlhZ3JhbS1qcy5jc3MiOwpAaW1wb3J0ICJ+YnBtbi1qcy9kaXN0L2Fzc2V0cy9icG1uLWZvbnQvY3NzL2JwbW4uY3NzIjsKQGltcG9ydCAifmJwbW4tanMvZGlzdC9hc3NldHMvYnBtbi1mb250L2Nzcy9icG1uLWNvZGVzLmNzcyI7CkBpbXBvcnQgIn5icG1uLWpzL2Rpc3QvYXNzZXRzL2JwbW4tZm9udC9jc3MvYnBtbi1lbWJlZGRlZC5jc3MiOwoudmlldy1tb2RlIHsKICAuZWwtaGVhZGVyLCAuZWwtYXNpZGUsIC5kanMtcGFsZXR0ZSwgLmJqcy1wb3dlcmVkLWJ5IHsKICAgIGRpc3BsYXk6IG5vbmU7CiAgfQogIC5lbC1sb2FkaW5nLW1hc2sgewogICAgYmFja2dyb3VuZC1jb2xvcjogaW5pdGlhbDsKICB9CiAgLmVsLWxvYWRpbmctc3Bpbm5lciB7CiAgICBkaXNwbGF5OiBub25lOwogIH0KfQouZmxvdy1jb250YWluZXJzIHsKICAvLyBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmZmZmOwogIHdpZHRoOiAxMDAlOwogIGhlaWdodDogMTAwJTsKICAuY2FudmFzIHsKICAgIHdpZHRoOiAxMDAlOwogICAgaGVpZ2h0OiAxMDAlOwogIH0KICAucGFuZWwgewogICAgcG9zaXRpb246IGFic29sdXRlOwogICAgcmlnaHQ6IDA7CiAgICB0b3A6IDUwcHg7CiAgICB3aWR0aDogMzAwcHg7CiAgfQogIC5sb2FkIHsKICAgIG1hcmdpbi1yaWdodDogMTBweDsKICB9CiAgLmVsLWZvcm0taXRlbV9fbGFiZWx7CiAgICBmb250LXNpemU6IDEzcHg7CiAgfQoKICAuZGpzLXBhbGV0dGV7CiAgICBsZWZ0OiAwcHghaW1wb3J0YW50OwogICAgdG9wOiAwcHg7CiAgICBib3JkZXItdG9wOiBub25lOwogIH0KCiAgLmRqcy1jb250YWluZXIgc3ZnIHsKICAgIG1pbi1oZWlnaHQ6IDY1MHB4OwogIH0KCiAgIC5oaWdobGlnaHQuZGpzLXNoYXBlIC5kanMtdmlzdWFsID4gOm50aC1jaGlsZCgxKSB7CiAgICAgZmlsbDogZ3JlZW4gIWltcG9ydGFudDsKICAgICBzdHJva2U6IGdyZWVuICFpbXBvcnRhbnQ7CiAgICAgZmlsbC1vcGFjaXR5OiAwLjIgIWltcG9ydGFudDsKICAgfQogICAuaGlnaGxpZ2h0LmRqcy1zaGFwZSAuZGpzLXZpc3VhbCA+IDpudGgtY2hpbGQoMikgewogICAgIGZpbGw6IGdyZWVuICFpbXBvcnRhbnQ7CiAgIH0KICAgLmhpZ2hsaWdodC5kanMtc2hhcGUgLmRqcy12aXN1YWwgPiBwYXRoIHsKICAgICBmaWxsOiBncmVlbiAhaW1wb3J0YW50OwogICAgIGZpbGwtb3BhY2l0eTogMC4yICFpbXBvcnRhbnQ7CiAgICAgc3Ryb2tlOiBncmVlbiAhaW1wb3J0YW50OwogICB9CiAgIC5oaWdobGlnaHQuZGpzLWNvbm5lY3Rpb24gPiAuZGpzLXZpc3VhbCA+IHBhdGggewogICAgIHN0cm9rZTogZ3JlZW4gIWltcG9ydGFudDsKICAgfQogICAvLyAuZGpzLWNvbm5lY3Rpb24gPiAuZGpzLXZpc3VhbCA+IHBhdGggewogICAvLyAgIHN0cm9rZTogb3JhbmdlICFpbXBvcnRhbnQ7CiAgIC8vICAgc3Ryb2tlLWRhc2hhcnJheTogNHB4ICFpbXBvcnRhbnQ7CiAgIC8vICAgZmlsbC1vcGFjaXR5OiAwLjIgIWltcG9ydGFudDsKICAgLy8gfQogICAvLyAuZGpzLXNoYXBlIC5kanMtdmlzdWFsID4gOm50aC1jaGlsZCgxKSB7CiAgIC8vICAgZmlsbDogb3JhbmdlICFpbXBvcnRhbnQ7CiAgIC8vICAgc3Ryb2tlOiBvcmFuZ2UgIWltcG9ydGFudDsKICAgLy8gICBzdHJva2UtZGFzaGFycmF5OiA0cHggIWltcG9ydGFudDsKICAgLy8gICBmaWxsLW9wYWNpdHk6IDAuMiAhaW1wb3J0YW50OwogICAvLyB9CiAgIC5oaWdobGlnaHQtdG9kby5kanMtY29ubmVjdGlvbiA+IC5kanMtdmlzdWFsID4gcGF0aCB7CiAgICAgc3Ryb2tlOiBvcmFuZ2UgIWltcG9ydGFudDsKICAgICBzdHJva2UtZGFzaGFycmF5OiA0cHggIWltcG9ydGFudDsKICAgICBmaWxsLW9wYWNpdHk6IDAuMiAhaW1wb3J0YW50OwogICB9CiAgIC5oaWdobGlnaHQtdG9kby5kanMtc2hhcGUgLmRqcy12aXN1YWwgPiA6bnRoLWNoaWxkKDEpIHsKICAgICBmaWxsOiBvcmFuZ2UgIWltcG9ydGFudDsKICAgICBzdHJva2U6IG9yYW5nZSAhaW1wb3J0YW50OwogICAgIHN0cm9rZS1kYXNoYXJyYXk6IDRweCAhaW1wb3J0YW50OwogICAgIGZpbGwtb3BhY2l0eTogMC4yICFpbXBvcnRhbnQ7CiAgIH0KICAgLm92ZXJsYXlzLWRpdiB7CiAgICAgZm9udC1zaXplOiAxMHB4OwogICAgIGNvbG9yOiByZWQ7CiAgICAgd2lkdGg6IDEwMHB4OwogICAgIHRvcDogLTIwcHggIWltcG9ydGFudDsKICAgfQp9CkBtZWRpYSBzY3JlZW4gYW5kIChtYXgtd2lkdGg6IDYwMHB4KSB7CiAgLmZsb3ctY29udGFpbmVycyAuZGpzLWNvbnRhaW5lciBzdmcgewogICAgICBtaW4taGVpZ2h0OiAzNTBweDsKICB9Cn0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqXA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/Process", "sourcesContent": ["<template>\n  <div v-loading=\"isView\" class=\"flow-containers\" :class=\"{ 'view-mode': isView }\">\n    <el-container style=\"height: 100%\">\n      <el-header style=\"border-bottom: 1px solid rgb(218 218 218);height: auto;\">\n        <div style=\"display: flex; padding: 10px 0px; justify-content: space-between;\">\n          <div>\n            <el-upload action=\"\" :before-upload=\"openBpmn\" style=\"margin-right: 10px; display:inline-block;\">\n              <el-tooltip effect=\"dark\" content=\"加载xml\" placement=\"bottom\">\n                <el-button size=\"mini\" icon=\"el-icon-folder-opened\" />\n              </el-tooltip>\n            </el-upload>\n            <el-tooltip effect=\"dark\" content=\"新建\" placement=\"bottom\">\n              <el-button size=\"mini\" icon=\"el-icon-circle-plus\" @click=\"newDiagram\" />\n            </el-tooltip>\n            <el-tooltip effect=\"dark\" content=\"自适应屏幕\" placement=\"bottom\">\n              <el-button size=\"mini\" icon=\"el-icon-rank\" @click=\"fitViewport\" />\n            </el-tooltip>\n            <el-tooltip effect=\"dark\" content=\"放大\" placement=\"bottom\">\n              <el-button size=\"mini\" icon=\"el-icon-zoom-in\" @click=\"zoomViewport(true)\" />\n            </el-tooltip>\n            <el-tooltip effect=\"dark\" content=\"缩小\" placement=\"bottom\">\n              <el-button size=\"mini\" icon=\"el-icon-zoom-out\" @click=\"zoomViewport(false)\" />\n            </el-tooltip>\n            <el-tooltip effect=\"dark\" content=\"后退\" placement=\"bottom\">\n              <el-button size=\"mini\" icon=\"el-icon-back\" @click=\"modeler.get('commandStack').undo()\" />\n            </el-tooltip>\n            <el-tooltip effect=\"dark\" content=\"前进\" placement=\"bottom\">\n              <el-button size=\"mini\" icon=\"el-icon-right\" @click=\"modeler.get('commandStack').redo()\" />\n            </el-tooltip>\n          </div>\n          <div>\n            <el-button size=\"mini\" icon=\"el-icon-view\" @click=\"showXML\">查看xml</el-button>\n            <el-button size=\"mini\" icon=\"el-icon-download\" @click=\"saveXML(true)\">下载xml</el-button>\n            <el-button size=\"mini\" icon=\"el-icon-picture\" @click=\"saveImg('svg', true)\">下载svg</el-button>\n            <el-button size=\"mini\" type=\"primary\" @click=\"save\">保存模型</el-button>\n          </div>\n        </div>\n      </el-header>\n      <el-container style=\"align-items: stretch\">\n        <el-main style=\"padding: 0;\">\n          <div ref=\"canvas\" class=\"canvas\" />\n        </el-main>\n        <el-aside style=\"width: 400px; min-height: 650px; background-color: #f0f2f5\">\n          <panel v-if=\"modeler\" :modeler=\"modeler\" :users=\"users\" :groups=\"groups\" :categorys=\"categorys\" @dataType=\"dataType\" />\n        </el-aside>\n      </el-container>\n    </el-container>\n  </div>\n</template>\n\n<script>\n// 汉化\nimport customTranslate from './common/customTranslate'\nimport Modeler from 'bpmn-js/lib/Modeler'\nimport panel from './PropertyPanel'\nimport BpmData from './BpmData'\nimport getInitStr from './flowable/init'\nimport customContextPad from './components/custom'\n// 引入flowable的节点文件\nimport flowableModdle from './flowable/flowable.json'\nexport default {\n  name: 'WorkflowBpmnModeler',\n  components: {\n    panel\n  },\n  props: {\n    xml: {\n      type: String,\n      default: ''\n    },\n    users: {\n      type: Array,\n      default: () => []\n    },\n    groups: {\n      type: Array,\n      default: () => []\n    },\n    categorys: {\n      type: Array,\n      default: () => []\n    },\n    isView: {\n      type: Boolean,\n      default: false\n    },\n    taskList: {\n      type: Array,\n      default: () => []\n    }\n  },\n  data() {\n    return {\n      modeler: null,\n      // taskList: [],\n      zoom: 1\n    }\n  },\n  watch: {\n    xml: function(val) {\n      if (val) {\n        this.createNewDiagram(val)\n      }\n    }\n  },\n  mounted() {\n    // 生成实例\n    this.modeler = new Modeler({\n      container: this.$refs.canvas,\n      additionalModules: [\n        {\n          translate: ['value', customTranslate]\n        },\n        customContextPad\n      ],\n      moddleExtensions: {\n        flowable: flowableModdle\n      }\n    })\n    // 新增流程定义\n    if (!this.xml) {\n      this.newDiagram()\n    } else {\n      this.createNewDiagram(this.xml)\n    }\n  },\n  methods: {\n    newDiagram() {\n      this.createNewDiagram(getInitStr())\n    },\n    // 让图能自适应屏幕\n    fitViewport() {\n      this.zoom = this.modeler.get('canvas').zoom('fit-viewport')\n      const bbox = document.querySelector('.flow-containers .viewport').getBBox()\n      const currentViewbox = this.modeler.get('canvas').viewbox()\n      const elementMid = {\n        x: bbox.x + bbox.width / 2 - 65,\n        y: bbox.y + bbox.height / 2\n      }\n      this.modeler.get('canvas').viewbox({\n        x: elementMid.x - currentViewbox.width / 2,\n        y: elementMid.y - currentViewbox.height / 2,\n        width: currentViewbox.width,\n        height: currentViewbox.height\n      })\n      this.zoom = bbox.width / currentViewbox.width * 1.8\n    },\n    // 放大缩小\n    zoomViewport(zoomIn = true) {\n      this.zoom = this.modeler.get('canvas').zoom()\n      this.zoom += (zoomIn ? 0.1 : -0.1)\n      this.modeler.get('canvas').zoom(this.zoom)\n    },\n    async createNewDiagram(data) {\n      // 将字符串转换成图显示出来\n      // data = data.replace(/<!\\[CDATA\\[(.+?)]]>/g, '&lt;![CDATA[$1]]&gt;')\n      data = data.replace(/<!\\[CDATA\\[(.+?)]]>/g, function(match, str) {\n        return str.replace(/</g, '&lt;')\n      })\n      try {\n        await this.modeler.importXML(data)\n        this.adjustPalette()\n        this.fitViewport()\n        if (this.taskList !==undefined && this.taskList.length > 0 ) {\n          this.fillColor()\n        }\n      } catch (err) {\n        console.error(err.message, err.warnings)\n      }\n    },\n    // 调整左侧工具栏排版\n    adjustPalette() {\n      try {\n        // 获取 bpmn 设计器实例\n        const canvas = this.$refs.canvas\n        const djsPalette = canvas.children[0].children[1].children[4]\n        const djsPalStyle = {\n          width: '130px',\n          padding: '5px',\n          background: 'white',\n          left: '20px',\n          borderRadius: 0\n        }\n        for (var key in djsPalStyle) {\n          djsPalette.style[key] = djsPalStyle[key]\n        }\n        const palette = djsPalette.children[0]\n        const allGroups = palette.children\n        // console.log(\"===============>>>\")\n        // console.log(allGroups)\n        // allGroups[0].children[2].style['display'] = 'none'\n        // allGroups[0].children[3].style['display'] = 'none'\n        // 修改控件样式\n        for (var gKey in allGroups) {\n          const group = allGroups[gKey]\n          for (var cKey in group.children) {\n            const control = group.children[cKey]\n            const controlStyle = {\n              display: 'flex',\n              justifyContent: 'flex-start',\n              alignItems: 'center',\n              width: '100%',\n              padding: '5px'\n            }\n            if (\n              control.className &&\n              control.dataset &&\n              control.className.indexOf('entry') !== -1\n            ) {\n              const controlProps = new BpmData().getControl(\n                control.dataset.action\n              )\n              control.innerHTML = `<div style='font-size: 14px;font-weight:500;margin-left:15px;'>${\n                controlProps['title']\n              }</div>`\n              for (var csKey in controlStyle) {\n                control.style[csKey] = controlStyle[csKey]\n              }\n            }\n          }\n        }\n      } catch (e) {\n        console.log(e)\n      }\n    },\n    fillColor() {\n      const canvas = this.modeler.get('canvas')\n      this.modeler._definitions.rootElements[0].flowElements.forEach(n => {\n        const completeTask = this.taskList.find(m => m.key === n.id)\n        const todoTask = this.taskList.find(m => !m.completed)\n        const endTask = this.taskList[this.taskList.length - 1]\n        if (n.$type === 'bpmn:UserTask') {\n          if (completeTask) {\n            canvas.addMarker(n.id, completeTask.completed ? 'highlight' : 'highlight-todo')\n            n.outgoing?.forEach(nn => {\n              const targetTask = this.taskList.find(m => m.key === nn.targetRef.id)\n              if (targetTask) {\n                if (todoTask && completeTask.key === todoTask.key && !todoTask.completed){\n                  canvas.addMarker(nn.id, todoTask.completed ? 'highlight' : 'highlight-todo')\n                  canvas.addMarker(nn.targetRef.id, todoTask.completed ? 'highlight' : 'highlight-todo')\n                }else {\n                  canvas.addMarker(nn.id, targetTask.completed ? 'highlight' : 'highlight-todo')\n                  canvas.addMarker(nn.targetRef.id, targetTask.completed ? 'highlight' : 'highlight-todo')\n                }\n              }\n            })\n          }\n        }\n        // 排他网关\n        else if (n.$type === 'bpmn:ExclusiveGateway') {\n          if (completeTask) {\n            canvas.addMarker(n.id, completeTask.completed ? 'highlight' : 'highlight-todo')\n            n.outgoing?.forEach(nn => {\n              const targetTask = this.taskList.find(m => m.key === nn.targetRef.id)\n              if (targetTask) {\n\n                canvas.addMarker(nn.id, targetTask.completed ? 'highlight' : 'highlight-todo')\n                canvas.addMarker(nn.targetRef.id, targetTask.completed ? 'highlight' : 'highlight-todo')\n              }\n\n            })\n          }\n\n        }\n        // 并行网关\n        else if (n.$type === 'bpmn:ParallelGateway') {\n          if (completeTask) {\n            canvas.addMarker(n.id, completeTask.completed ? 'highlight' : 'highlight-todo')\n            n.outgoing?.forEach(nn => {\n              const targetTask = this.taskList.find(m => m.key === nn.targetRef.id)\n              if (targetTask) {\n                canvas.addMarker(nn.id, targetTask.completed ? 'highlight' : 'highlight-todo')\n                canvas.addMarker(nn.targetRef.id, targetTask.completed ? 'highlight' : 'highlight-todo')\n              }\n            })\n          }\n        }\n        else if (n.$type === 'bpmn:StartEvent') {\n          n.outgoing.forEach(nn => {\n            const completeTask = this.taskList.find(m => m.key === nn.targetRef.id)\n            if (completeTask) {\n              canvas.addMarker(nn.id, 'highlight')\n              canvas.addMarker(n.id, 'highlight')\n              return\n            }\n          })\n        }\n        else if (n.$type === 'bpmn:EndEvent') {\n          if (endTask.key === n.id && endTask.completed) {\n            canvas.addMarker(n.id, 'highlight')\n            return\n          }\n        }\n      })\n    },\n    // 对外 api\n    getProcess() {\n      const element = this.getProcessElement()\n      return {\n        id: element.id,\n        name: element.name,\n        category: element.$attrs['flowable:processCategory']\n      }\n    },\n    getProcessElement() {\n      const rootElements = this.modeler.getDefinitions().rootElements\n      for (let i = 0; i < rootElements.length; i++) {\n        if (rootElements[i].$type === 'bpmn:Process') return rootElements[i]\n      }\n    },\n    async saveXML(download = false) {\n      try {\n        const { xml } = await this.modeler.saveXML({ format: true })\n        if (download) {\n          this.downloadFile(`${this.getProcessElement().name}.bpmn20.xml`, xml, 'application/xml')\n        }\n        return xml\n      } catch (err) {\n        console.log(err)\n      }\n    },\n    async showXML() {\n      try {\n        const { xml } = await this.modeler.saveXML({ format: true })\n        this.$emit('showXML',xml)\n      } catch (err) {\n        console.log(err)\n      }\n    },\n    async saveImg(type = 'svg', download = false) {\n      try {\n        const { svg } = await this.modeler.saveSVG({ format: true })\n        if (download) {\n          this.downloadFile(this.getProcessElement().name, svg, 'image/svg+xml')\n        }\n        return svg\n      } catch (err) {\n        console.log(err)\n      }\n    },\n    async save() {\n      const process = this.getProcess()\n      const xml = await this.saveXML()\n      const svg = await this.saveImg()\n      const result = { process, xml, svg }\n      this.$emit('save', result)\n      window.parent.postMessage(result, '*')\n    },\n    openBpmn(file) {\n      const reader = new FileReader()\n      reader.readAsText(file, 'utf-8')\n      reader.onload = () => {\n        this.createNewDiagram(reader.result)\n      }\n      return false\n    },\n    downloadFile(filename, data, type) {\n      var a = document.createElement('a')\n      var url = window.URL.createObjectURL(new Blob([data], { type: type }))\n      a.href = url\n      a.download = filename\n      a.click()\n      window.URL.revokeObjectURL(url)\n    },\n    /** 获取数据类型 */\n    dataType(data){\n      this.$emit('dataType', data)\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\">\n/*左边工具栏以及编辑节点的样式*/\n@import \"~bpmn-js/dist/assets/diagram-js.css\";\n@import \"~bpmn-js/dist/assets/bpmn-font/css/bpmn.css\";\n@import \"~bpmn-js/dist/assets/bpmn-font/css/bpmn-codes.css\";\n@import \"~bpmn-js/dist/assets/bpmn-font/css/bpmn-embedded.css\";\n.view-mode {\n  .el-header, .el-aside, .djs-palette, .bjs-powered-by {\n    display: none;\n  }\n  .el-loading-mask {\n    background-color: initial;\n  }\n  .el-loading-spinner {\n    display: none;\n  }\n}\n.flow-containers {\n  // background-color: #ffffff;\n  width: 100%;\n  height: 100%;\n  .canvas {\n    width: 100%;\n    height: 100%;\n  }\n  .panel {\n    position: absolute;\n    right: 0;\n    top: 50px;\n    width: 300px;\n  }\n  .load {\n    margin-right: 10px;\n  }\n  .el-form-item__label{\n    font-size: 13px;\n  }\n\n  .djs-palette{\n    left: 0px!important;\n    top: 0px;\n    border-top: none;\n  }\n\n  .djs-container svg {\n    min-height: 650px;\n  }\n\n   .highlight.djs-shape .djs-visual > :nth-child(1) {\n     fill: green !important;\n     stroke: green !important;\n     fill-opacity: 0.2 !important;\n   }\n   .highlight.djs-shape .djs-visual > :nth-child(2) {\n     fill: green !important;\n   }\n   .highlight.djs-shape .djs-visual > path {\n     fill: green !important;\n     fill-opacity: 0.2 !important;\n     stroke: green !important;\n   }\n   .highlight.djs-connection > .djs-visual > path {\n     stroke: green !important;\n   }\n   // .djs-connection > .djs-visual > path {\n   //   stroke: orange !important;\n   //   stroke-dasharray: 4px !important;\n   //   fill-opacity: 0.2 !important;\n   // }\n   // .djs-shape .djs-visual > :nth-child(1) {\n   //   fill: orange !important;\n   //   stroke: orange !important;\n   //   stroke-dasharray: 4px !important;\n   //   fill-opacity: 0.2 !important;\n   // }\n   .highlight-todo.djs-connection > .djs-visual > path {\n     stroke: orange !important;\n     stroke-dasharray: 4px !important;\n     fill-opacity: 0.2 !important;\n   }\n   .highlight-todo.djs-shape .djs-visual > :nth-child(1) {\n     fill: orange !important;\n     stroke: orange !important;\n     stroke-dasharray: 4px !important;\n     fill-opacity: 0.2 !important;\n   }\n   .overlays-div {\n     font-size: 10px;\n     color: red;\n     width: 100px;\n     top: -20px !important;\n   }\n}\n@media screen and (max-width: 600px) {\n  .flow-containers .djs-container svg {\n      min-height: 350px;\n  }\n}\n</style>\n"]}]}