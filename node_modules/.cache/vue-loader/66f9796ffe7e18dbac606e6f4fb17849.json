{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/tool/build/index.vue?vue&type=style&index=0&id=39cfdb14&lang=scss", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/tool/build/index.vue", "mtime": 1641277326000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/css-loader/dist/cjs.js", "mtime": 1751171659443}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/loaders/stylePostLoader.js", "mtime": 1751171659924}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/postcss-loader/src/index.js", "mtime": 1751171659554}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/sass-loader/dist/cjs.js", "mtime": 1751171659051}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgpAaW1wb3J0ICdAL3N0eWxlcy9ob21lJzsK"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6iBA", "file": "index.vue", "sourceRoot": "src/views/tool/build", "sourcesContent": ["<template>\n  <div class=\"container\">\n    <div class=\"left-board\">\n      <div class=\"logo-wrapper\">\n        <div class=\"logo\">\n          <img :src=\"logo\" alt=\"logo\"> Form Generator\n        </div>\n      </div>\n      <el-scrollbar class=\"left-scrollbar\">\n        <div class=\"components-list\">\n          <div v-for=\"(item, listIndex) in leftComponents\" :key=\"listIndex\">\n            <div class=\"components-title\">\n              <svg-icon icon-class=\"component\" />\n              {{ item.title }}\n            </div>\n            <draggable\n              class=\"components-draggable\"\n              :list=\"item.list\"\n              :group=\"{ name: 'componentsGroup', pull: 'clone', put: false }\"\n              :clone=\"cloneComponent\"\n              draggable=\".components-item\"\n              :sort=\"false\"\n              @end=\"onEnd\"\n            >\n              <div\n                v-for=\"(element, index) in item.list\"\n                :key=\"index\"\n                class=\"components-item\"\n                @click=\"addComponent(element)\"\n              >\n                <div class=\"components-body\">\n                  <svg-icon :icon-class=\"element.__config__.tagIcon\" />\n                  {{ element.__config__.label }}\n                </div>\n              </div>\n            </draggable>\n          </div>\n        </div>\n      </el-scrollbar>\n    </div>\n\n    <div class=\"center-board\">\n      <div class=\"action-bar\">\n        <el-button icon=\"el-icon-plus\" type=\"text\" @click=\"handleForm\">\n          保存\n        </el-button>\n        <el-button icon=\"el-icon-video-play\" type=\"text\" @click=\"run\">\n          运行\n        </el-button>\n        <el-button icon=\"el-icon-view\" type=\"text\" @click=\"showJson\">\n          查看json\n        </el-button>\n        <el-button icon=\"el-icon-download\" type=\"text\" @click=\"download\">\n          导出vue文件\n        </el-button>\n        <el-button class=\"copy-btn-main\" icon=\"el-icon-document-copy\" type=\"text\" @click=\"copy\">\n          复制代码\n        </el-button>\n        <el-button class=\"delete-btn\" icon=\"el-icon-delete\" type=\"text\" @click=\"empty\">\n          清空\n        </el-button>\n      </div>\n      <el-scrollbar class=\"center-scrollbar\">\n        <el-row class=\"center-board-row\" :gutter=\"formConf.gutter\">\n          <el-form\n            :size=\"formConf.size\"\n            :label-position=\"formConf.labelPosition\"\n            :disabled=\"formConf.disabled\"\n            :label-width=\"formConf.labelWidth + 'px'\"\n          >\n            <draggable class=\"drawing-board\" :list=\"drawingList\" :animation=\"340\" group=\"componentsGroup\">\n              <draggable-item\n                v-for=\"(item, index) in drawingList\"\n                :key=\"item.renderKey\"\n                :drawing-list=\"drawingList\"\n                :current-item=\"item\"\n                :index=\"index\"\n                :active-id=\"activeId\"\n                :form-conf=\"formConf\"\n                @activeItem=\"activeFormItem\"\n                @copyItem=\"drawingItemCopy\"\n                @deleteItem=\"drawingItemDelete\"\n              />\n            </draggable>\n            <div v-show=\"!drawingList.length\" class=\"empty-info\">\n              从左侧拖入或点选组件进行表单设计\n            </div>\n          </el-form>\n        </el-row>\n      </el-scrollbar>\n    </div>\n\n    <right-panel\n      :active-data=\"activeData\"\n      :form-conf=\"formConf\"\n      :show-field=\"!!drawingList.length\"\n      @tag-change=\"tagChange\"\n      @fetch-data=\"fetchData\"\n    />\n\n    <form-drawer\n      :visible.sync=\"drawerVisible\"\n      :form-data=\"formData\"\n      size=\"100%\"\n      :generate-conf=\"generateConf\"\n    />\n    <json-drawer\n      size=\"60%\"\n      :visible.sync=\"jsonDrawerVisible\"\n      :json-str=\"JSON.stringify(formData)\"\n      @refresh=\"refreshJson\"\n    />\n    <code-type-dialog\n      :visible.sync=\"dialogVisible\"\n      title=\"选择生成类型\"\n      :show-file-name=\"showFileName\"\n      @confirm=\"generate\"\n    />\n    <input id=\"copyNode\" type=\"hidden\">\n    <!--表单配置详情-->\n    <el-dialog :title=\"formTitle\" :visible.sync=\"formOpen\" width=\"500px\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\n        <el-form-item label=\"表单名称\" prop=\"formName\">\n          <el-input v-model=\"form.formName\" placeholder=\"请输入表单名称\" />\n        </el-form-item>\n        <el-form-item label=\"备注\" prop=\"remark\">\n          <el-input v-model=\"form.remark\" placeholder=\"请输入备注\" />\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport draggable from 'vuedraggable'\nimport { debounce } from 'throttle-debounce'\nimport { saveAs } from 'file-saver'\nimport ClipboardJS from 'clipboard'\nimport render from '@/components/render/render'\nimport FormDrawer from './FormDrawer'\nimport JsonDrawer from './JsonDrawer'\nimport RightPanel from './RightPanel'\nimport {\n  inputComponents, selectComponents, layoutComponents, formConf\n} from '@/utils/generator/config'\nimport {\n  exportDefault, beautifierConf, isNumberStr, titleCase, deepClone, isObjectObject\n} from '@/utils/index'\nimport {\n  makeUpHtml, vueTemplate, vueScript, cssStyle\n} from '@/utils/generator/html'\nimport { makeUpJs } from '@/utils/generator/js'\nimport { makeUpCss } from '@/utils/generator/css'\nimport drawingDefalut from '@/utils/generator/drawingDefalut'\nimport logo from '@/assets/logo/logo.png'\nimport CodeTypeDialog from './CodeTypeDialog'\nimport DraggableItem from './DraggableItem'\nimport {\n  getDrawingList, saveDrawingList, getIdGlobal, saveIdGlobal, getFormConf\n} from '@/utils/db'\nimport loadBeautifier from '@/utils/loadBeautifier'\nimport {getForm, addForm, updateForm} from \"@/api/flowable/form\";\n\nlet beautifier\nconst emptyActiveData = { style: {}, autosize: {} }\nlet oldActiveId\nlet tempActiveData\nconst drawingListInDB = getDrawingList()\nconst formConfInDB = getFormConf()\nconst idGlobal = getIdGlobal()\n\nexport default {\n  components: {\n    draggable,\n    render,\n    FormDrawer,\n    JsonDrawer,\n    RightPanel,\n    CodeTypeDialog,\n    DraggableItem\n  },\n  data() {\n    return {\n      logo,\n      idGlobal,\n      formConf,\n      inputComponents,\n      selectComponents,\n      layoutComponents,\n      labelWidth: 100,\n      drawingList: drawingDefalut,\n      drawingData: {},\n      activeId: drawingDefalut[0].formId,\n      drawerVisible: false,\n      formData: {},\n      dialogVisible: false,\n      jsonDrawerVisible: false,\n      generateConf: null,\n      showFileName: false,\n      activeData: drawingDefalut[0],\n      saveDrawingListDebounce: debounce(340, saveDrawingList),\n      saveIdGlobalDebounce: debounce(340, saveIdGlobal),\n      leftComponents: [\n        {\n          title: '输入型组件',\n          list: inputComponents\n        },\n        {\n          title: '选择型组件',\n          list: selectComponents\n        },\n        {\n          title: '布局型组件',\n          list: layoutComponents\n        }\n      ],\n      formOpen: false,\n      formTitle: \"\",\n      // 表单参数\n      form: {\n        formId: null,\n        formName: null,\n        formContent: null,\n        remark: null\n      },\n      // 表单校验\n      rules: {}\n    }\n  },\n  computed: {\n  },\n  watch: {\n    // eslint-disable-next-line func-names\n    'activeData.__config__.label': function (val, oldVal) {\n      if (\n        this.activeData.placeholder === undefined\n        || !this.activeData.__config__.tag\n        || oldActiveId !== this.activeId\n      ) {\n        return\n      }\n      this.activeData.placeholder = this.activeData.placeholder.replace(oldVal, '') + val\n    },\n    activeId: {\n      handler(val) {\n        oldActiveId = val\n      },\n      immediate: true\n    },\n    drawingList: {\n      handler(val) {\n        this.saveDrawingListDebounce(val)\n        if (val.length === 0) this.idGlobal = 100\n      },\n      deep: true\n    },\n    idGlobal: {\n      handler(val) {\n        this.saveIdGlobalDebounce(val)\n      },\n      immediate: true\n    }\n  },\n\n  mounted() {\n    const that = this;\n    if (Array.isArray(drawingListInDB) && drawingListInDB.length > 0) {\n      that.drawingList = drawingListInDB\n    } else {\n      that.drawingList = drawingDefalut\n    }\n    this.activeFormItem(that.drawingList[0])\n    // // if (formConfInDB) {\n    // //   this.formConf = formConfInDB\n    // // }\n    that.drawingList = [];\n    const formId =  that.$route.query && that.$route.query.formId;\n    if (formId) {\n      getForm(formId).then(res =>{\n        that.formConf = JSON.parse(res.data.formContent);\n        that.drawingList = that.formConf.fields;\n        that.form = res.data;\n      })\n    }else {\n      if (formConfInDB) {\n        that.formConf = formConfInDB\n      }\n    }\n    loadBeautifier(btf => {\n      beautifier = btf\n    })\n    const clipboard = new ClipboardJS('#copyNode', {\n      text: trigger => {\n        const codeStr = this.generateCode()\n        this.$notify({\n          title: '成功',\n          message: '代码已复制到剪切板，可粘贴。',\n          type: 'success'\n        })\n        return codeStr\n      }\n    })\n    clipboard.on('error', e => {\n      this.$message.error('代码复制失败')\n    })\n  },\n  methods: {\n    setObjectValueReduce(obj, strKeys, data) {\n      const arr = strKeys.split('.')\n      arr.reduce((pre, item, i) => {\n        if (arr.length === i + 1) {\n          pre[item] = data\n        } else if (!isObjectObject(pre[item])) {\n          pre[item] = {}\n        }\n        return pre[item]\n      }, obj)\n    },\n    setRespData(component, resp) {\n      const { dataPath, renderKey, dataConsumer } = component.__config__\n      if (!dataPath || !dataConsumer) return\n      const respData = dataPath.split('.').reduce((pre, item) => pre[item], resp)\n\n      // 将请求回来的数据，赋值到指定属性。\n      // 以el-tabel为例，根据Element文档，应该将数据赋值给el-tabel的data属性，所以dataConsumer的值应为'data';\n      // 此时赋值代码可写成 component[dataConsumer] = respData；\n      // 但为支持更深层级的赋值（如：dataConsumer的值为'options.data'）,使用setObjectValueReduce\n      this.setObjectValueReduce(component, dataConsumer, respData)\n      const i = this.drawingList.findIndex(item => item.__config__.renderKey === renderKey)\n      if (i > -1) this.$set(this.drawingList, i, component)\n    },\n    fetchData(component) {\n      const { dataType, method, url } = component.__config__\n      if (dataType === 'dynamic' && method && url) {\n        this.setLoading(component, true)\n        this.$axios({\n          method,\n          url\n        }).then(resp => {\n          this.setLoading(component, false)\n          this.setRespData(component, resp.data)\n        })\n      }\n    },\n    setLoading(component, val) {\n      const { directives } = component\n      if (Array.isArray(directives)) {\n        const t = directives.find(d => d.name === 'loading')\n        if (t) t.value = val\n      }\n    },\n    activeFormItem(currentItem) {\n      this.activeData = currentItem\n      this.activeId = currentItem.__config__.formId\n    },\n    onEnd(obj) {\n      if (obj.from !== obj.to) {\n        this.fetchData(tempActiveData)\n        this.activeData = tempActiveData\n        this.activeId = this.idGlobal\n      }\n    },\n    addComponent(item) {\n      const clone = this.cloneComponent(item)\n      this.fetchData(clone)\n      this.drawingList.push(clone)\n      this.activeFormItem(clone)\n    },\n    cloneComponent(origin) {\n      const clone = deepClone(origin)\n      const config = clone.__config__\n      config.span = this.formConf.span // 生成代码时，会根据span做精简判断\n      this.createIdAndKey(clone)\n      clone.placeholder !== undefined && (clone.placeholder += config.label)\n      tempActiveData = clone\n      return tempActiveData\n    },\n    createIdAndKey(item) {\n      const config = item.__config__\n      config.formId = ++this.idGlobal\n      config.renderKey = `${config.formId}${+new Date()}` // 改变renderKey后可以实现强制更新组件\n      if (config.layout === 'colFormItem') {\n        item.__vModel__ = `field${this.idGlobal}`\n      } else if (config.layout === 'rowFormItem') {\n        config.componentName = `row${this.idGlobal}`\n        !Array.isArray(config.children) && (config.children = [])\n        delete config.label // rowFormItem无需配置label属性\n      }\n      if (Array.isArray(config.children)) {\n        config.children = config.children.map(childItem => this.createIdAndKey(childItem))\n      }\n      return item\n    },\n    AssembleFormData() {\n      this.formData = {\n        fields: deepClone(this.drawingList),\n        ...this.formConf\n      }\n    },\n    generate(data) {\n      const func = this[`exec${titleCase(this.operationType)}`]\n      this.generateConf = data\n      func && func(data)\n    },\n    execRun(data) {\n      this.AssembleFormData()\n      this.drawerVisible = true\n    },\n    execDownload(data) {\n      const codeStr = this.generateCode()\n      const blob = new Blob([codeStr], { type: 'text/plain;charset=utf-8' })\n      saveAs(blob, data.fileName)\n    },\n    execCopy(data) {\n      document.getElementById('copyNode').click()\n    },\n    empty() {\n      this.$confirm('确定要清空所有组件吗？', '提示', { type: 'warning' }).then(\n        () => {\n          this.drawingList = []\n          this.idGlobal = 100\n        }\n      )\n    },\n    drawingItemCopy(item, list) {\n      let clone = deepClone(item)\n      clone = this.createIdAndKey(clone)\n      list.push(clone)\n      this.activeFormItem(clone)\n    },\n    drawingItemDelete(index, list) {\n      list.splice(index, 1)\n      this.$nextTick(() => {\n        const len = this.drawingList.length\n        if (len) {\n          this.activeFormItem(this.drawingList[len - 1])\n        }\n      })\n    },\n    generateCode() {\n      const { type } = this.generateConf\n      this.AssembleFormData()\n      const script = vueScript(makeUpJs(this.formData, type))\n      const html = vueTemplate(makeUpHtml(this.formData, type))\n      const css = cssStyle(makeUpCss(this.formData))\n      return beautifier.html(html + script + css, beautifierConf.html)\n    },\n    showJson() {\n      this.AssembleFormData()\n      this.jsonDrawerVisible = true\n    },\n    download() {\n      this.dialogVisible = true\n      this.showFileName = true\n      this.operationType = 'download'\n    },\n    run() {\n      this.dialogVisible = true\n      this.showFileName = false\n      this.operationType = 'run'\n    },\n    copy() {\n      this.dialogVisible = true\n      this.showFileName = false\n      this.operationType = 'copy'\n    },\n    tagChange(newTag) {\n      newTag = this.cloneComponent(newTag)\n      const config = newTag.__config__\n      newTag.__vModel__ = this.activeData.__vModel__\n      config.formId = this.activeId\n      config.span = this.activeData.__config__.span\n      this.activeData.__config__.tag = config.tag\n      this.activeData.__config__.tagIcon = config.tagIcon\n      this.activeData.__config__.document = config.document\n      if (typeof this.activeData.__config__.defaultValue === typeof config.defaultValue) {\n        config.defaultValue = this.activeData.__config__.defaultValue\n      }\n      Object.keys(newTag).forEach(key => {\n        if (this.activeData[key] !== undefined) {\n          newTag[key] = this.activeData[key]\n        }\n      })\n      this.activeData = newTag\n      this.updateDrawingList(newTag, this.drawingList)\n    },\n    updateDrawingList(newTag, list) {\n      const index = list.findIndex(item => item.__config__.formId === this.activeId)\n      if (index > -1) {\n        list.splice(index, 1, newTag)\n      } else {\n        list.forEach(item => {\n          if (Array.isArray(item.__config__.children)) this.updateDrawingList(newTag, item.__config__.children)\n        })\n      }\n    },\n    refreshJson(data) {\n      this.drawingList = deepClone(data.fields)\n      delete data.fields\n      this.formConf = data\n    },\n    /** 表单基本信息 */\n    handleForm(){\n      this.formData = {\n        fields: deepClone(this.drawingList),\n        ...this.formConf\n      }\n     this.form.formContent = JSON.stringify(this.formData);\n     this.formOpen = true;\n     this.formTitle = \"添加表单\";\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        formId: null,\n        formName: null,\n        formContent: null,\n        remark: null\n      };\n      this.resetForm(\"form\");\n    },\n    // 取消按钮\n    cancel() {\n      this.formOpen = false;\n      this.reset();\n    },\n    /** 保存表单信息 */\n    submitForm(){\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          if (this.form.formId != null) {\n            updateForm(this.form).then(response => {\n              this.msgSuccess(\"修改成功\");\n            });\n          } else {\n            addForm(this.form).then(response => {\n              this.msgSuccess(\"新增成功\");\n            });\n          }\n          this.drawingList = []\n          this.idGlobal = 100\n          this.open = false;\n          // 关闭当前标签页并返回上个页面\n          this.$store.dispatch(\"tagsView/delView\", this.$route);\n          this.$router.go(-1)\n        }\n      });\n    }\n  }\n}\n</script>\n\n<style lang='scss'>\n@import '@/styles/home';\n</style>\n"]}]}