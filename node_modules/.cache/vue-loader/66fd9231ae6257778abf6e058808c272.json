{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/components/HeaderSearch/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/components/HeaderSearch/index.vue", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}