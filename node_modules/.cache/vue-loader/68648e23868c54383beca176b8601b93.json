{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/flowable/task/record/view.vue?vue&type=style&index=0&id=542ee9d4&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/flowable/task/record/view.vue", "mtime": 1717760123606}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/css-loader/dist/cjs.js", "mtime": 1751171659443}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/loaders/stylePostLoader.js", "mtime": 1751171659924}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/postcss-loader/src/index.js", "mtime": 1751171659554}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/sass-loader/dist/cjs.js", "mtime": 1751171659051}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKQG1lZGlhIHNjcmVlbiBhbmQgKG1heC13aWR0aDogNjAwcHgpIHsKICAuZWwtdGltZWxpbmUgewogICAgbWFyZ2luOiAwOwogICAgZm9udC1zaXplOiAxNHB4OwogICAgbGlzdC1zdHlsZTogbm9uZTsKICAgIHBhZGRpbmctbGVmdDogMDsKICB9CgogIDo6di1kZWVwIC5hdWRpdC1yZWNvcmQgLmVsLWNhcmRfX2JvZHkgewogICAgZGlzcGxheTogZmxleDsKICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47CiAgfQp9CgoudGVzdC1mb3JtIHsKICBtYXJnaW46IDE1cHggYXV0bzsKICB3aWR0aDogODAwcHg7CiAgcGFkZGluZzogMTVweDsKfQoKLmNsZWFyZml4OmJlZm9yZSwKLmNsZWFyZml4OmFmdGVyIHsKICBkaXNwbGF5OiB0YWJsZTsKICBjb250ZW50OiAiIjsKfQouY2xlYXJmaXg6YWZ0ZXIgewogIGNsZWFyOiBib3RoOwp9CgouYm94LWNhcmQgewogIHdpZHRoOiAxMDAlOwogIG1hcmdpbi1ib3R0b206IDIwcHg7Cn0KCi5lbC10YWcgKyAuZWwtdGFnIHsKICBtYXJnaW4tbGVmdDogMTBweDsKfQo="}, {"version": 3, "sources": ["view.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAu4BA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "view.vue", "sourceRoot": "src/views/flowable/task/record", "sourcesContent": ["<template>\n  <div class=\"\">\n    <div class=\"clearfix\" v-if=\"!finished\">\n      <span style=\"float: right; margin-bottom: 20px\">\n        <el-button\n          icon=\"el-icon-edit-outline\"\n          type=\"success\"\n          @click=\"handleComplete\"\n          >{{ isCommon ? \"重新提交\" : \"审批\" }}</el-button\n        >\n        <el-button\n          v-if=\"!isCommon\"\n          icon=\"el-icon-refresh-left\"\n          type=\"warning\"\n          @click=\"handleReturn\"\n          >退回</el-button\n        >\n        <!-- <el-button v-if=\"!isCommon\" icon=\"el-icon-refresh-left\" type=\"warning\" @click=\"handleReject\">退回</el-button> -->\n        <el-button\n          v-if=\"!isCommon\"\n          icon=\"el-icon-refresh-left\"\n          type=\"danger\"\n          @click=\"handleEnd\"\n          >驳回</el-button\n        >\n      </span>\n    </div>\n    <!--流程流转记录-->\n    <el-card class=\"box-card\">\n      <div slot=\"header\" class=\"clearfix\">\n        <span class=\"el-icon-notebook-1\">审批记录</span>\n      </div>\n      <div class=\"block\">\n        <span v-if=\"!flowRecordList || flowRecordList.length == 0\"\n          >无审批记录</span\n        >\n        <el-tabs\n          v-if=\"!flowHis || flowHis.length > 1\"\n          v-model=\"flowActive\"\n          type=\"card\"\n          @tab-click=\"handleClick\"\n        >\n          <el-tab-pane\n            :key=\"index\"\n            :label=\"item.taskName\"\n            :name=\"item.procInsId\"\n            v-for=\"(item, index) in flowHis\"\n          ></el-tab-pane>\n        </el-tabs>\n        <el-timeline>\n          <el-timeline-item\n            v-for=\"(item, index) in flowRecordList\"\n            :key=\"index\"\n            :icon=\"setIcon(item.finishTime)\"\n            :color=\"setColor(item.finishTime)\"\n          >\n            <p style=\"font-weight: 700\">{{ item.taskName }}</p>\n            <el-card class=\"audit-record\" :body-style=\"{ padding: '10px' }\">\n              <label\n                v-if=\"item.assigneeName\"\n                style=\"font-weight: normal; margin-right: 30px\"\n                >实际办理：\n                <span\n                  style=\"\n                    margin-right: 30px;\n                    color: #8a909c;\n                    font-weight: normal;\n                  \"\n                  >{{ item.assigneeName }}</span\n                ></label\n              >\n              <label\n                v-if=\"item.candidate\"\n                style=\"font-weight: normal; margin-right: 30px\"\n                >候选办理：\n                <span\n                  style=\"\n                    margin-right: 30px;\n                    color: #8a909c;\n                    font-weight: normal;\n                  \"\n                  >{{ item.candidate }}</span\n                ></label\n              >\n              <label style=\"font-weight: normal\"\n                >接收时间：\n                <span\n                  style=\"\n                    margin-right: 30px;\n                    color: #8a909c;\n                    font-weight: normal;\n                  \"\n                  >{{ item.createTime }}</span\n                ></label\n              >\n              <label v-if=\"item.finishTime\" style=\"font-weight: normal\"\n                >办结时间：\n                <span\n                  style=\"\n                    margin-right: 30px;\n                    color: #8a909c;\n                    font-weight: normal;\n                  \"\n                  >{{ item.finishTime }}</span\n                ></label\n              >\n              <label v-if=\"item.duration\" style=\"font-weight: normal\"\n                >耗时：\n                <span\n                  style=\"\n                    margin-right: 30px;\n                    color: #8a909c;\n                    font-weight: normal;\n                  \"\n                  >{{ item.duration }}</span\n                ></label\n              >\n\n              <p v-if=\"item.comment\">\n                <el-tag type=\"success\" v-if=\"item.comment.type === '1'\">\n                  {{ item.comment.comment }}</el-tag\n                >\n                <el-tag type=\"warning\" v-if=\"item.comment.type === '2'\">\n                  {{ item.comment.comment }}</el-tag\n                >\n                <el-tag type=\"danger\" v-if=\"item.comment.type === '3'\">\n                  {{ item.comment.comment }}</el-tag\n                >\n              </p>\n            </el-card>\n          </el-timeline-item>\n        </el-timeline>\n      </div>\n    </el-card>\n    <el-card class=\"box-card\">\n      <div slot=\"header\" class=\"clearfix\">\n        <span class=\"el-icon-notebook-2\"><slot name=\"title\"></slot></span>\n      </div>\n      <slot name=\"content\"></slot>\n    </el-card>\n    <el-card class=\"box-card\">\n      <div slot=\"header\" class=\"clearfix\">\n        <span class=\"el-icon-picture-outline\">流程图</span>\n      </div>\n      <flow :xmlData=\"xmlData\" :taskData=\"taskList\"></flow>\n    </el-card>\n\n    <!--审批正常流程-->\n    <el-dialog\n      :title=\"completeTitle\"\n      visible.sync=\"false\"\n      width=\"60%\"\n      append-to-body\n    >\n      <el-row :gutter=\"20\">\n        <!--部门数据-->\n        <el-col :span=\"4\" :xs=\"24\">\n          <h6>部门列表</h6>\n          <div class=\"head-container\">\n            <el-input\n              v-model=\"deptName\"\n              placeholder=\"请输入部门名称\"\n              clearable\n              size=\"small\"\n              prefix-icon=\"el-icon-search\"\n              style=\"margin-bottom: 20px\"\n            />\n          </div>\n          <div class=\"head-container\">\n            <el-tree\n              :data=\"deptOptions\"\n              :props=\"defaultProps\"\n              :expand-on-click-node=\"false\"\n              :filter-node-method=\"filterNode\"\n              ref=\"tree\"\n              default-expand-all\n              @node-click=\"handleNodeClick\"\n            />\n          </div>\n        </el-col>\n        <el-col :span=\"12\" :xs=\"24\">\n          <h6>待选人员</h6>\n          <el-table\n            ref=\"singleTable\"\n            :data=\"userList\"\n            border\n            style=\"width: 100%\"\n            @selection-change=\"handleSelectionChange\"\n          >\n            <el-table-column type=\"selection\" width=\"50\" align=\"center\" />\n            <el-table-column label=\"用户名\" align=\"center\" prop=\"nickName\" />\n            <el-table-column label=\"部门\" align=\"center\" prop=\"dept.deptName\" />\n          </el-table>\n        </el-col>\n        <el-col :span=\"8\" :xs=\"24\">\n          <h6>已选人员</h6>\n          <el-tag\n            v-for=\"tag in userData\"\n            :key=\"tag.nickName\"\n            closable\n            @close=\"handleClose(tag)\"\n          >\n            {{ tag.nickName }} {{ tag.dept.deptName }}\n          </el-tag>\n        </el-col>\n      </el-row>\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-input\n          style=\"width: 50%; margin-right: 34%\"\n          type=\"textarea\"\n          v-model=\"taskForm.comment\"\n          placeholder=\"请输入处理意见\"\n        />\n        <el-button @click=\"completeOpen = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"taskComplete\">确 定</el-button>\n      </span>\n    </el-dialog>\n\n    <el-dialog\n      :title=\"completeTitle\"\n      :visible.sync=\"completeOpen\"\n      width=\"40%\"\n      append-to-body\n    >\n      <el-form ref=\"taskForm\" :model=\"taskForm\" label-width=\"120px\">\n        <el-form-item\n          label=\"审批意见\"\n          prop=\"comment\"\n          :rules=\"[\n            { required: true, message: '请输入处理意见', trigger: 'blur' },\n          ]\"\n        >\n          <el-input\n            style=\"width: 50%\"\n            type=\"textarea\"\n            v-model=\"taskForm.comment\"\n            placeholder=\"请输入处理意见\"\n          />\n        </el-form-item>\n        <el-form-item label=\"授权书图片\" v-if=\"isAuthImages\">\n          <Upload\n            :fileSize=\"30\"\n            :value=\"taskForm.authImages\"\n            @input=\"imageUrls\"\n            :fileType=\"typeArr\"\n          ></Upload>\n        </el-form-item>\n      </el-form>\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"completeOpen = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"taskComplete\">确 定</el-button>\n      </span>\n    </el-dialog>\n\n    <!--退回流程-->\n    <el-dialog\n      :title=\"returnTitle\"\n      :visible.sync=\"returnOpen\"\n      width=\"40%\"\n      append-to-body\n    >\n      <el-form ref=\"taskForm\" :model=\"taskForm\" label-width=\"80px\">\n        <el-form-item\n          :v-if=\"false\"\n          label=\"退回节点\"\n          prop=\"targetKey\"\n          :rules=\"[\n            { required: true, message: '请选择退回节点', trigger: 'change' },\n          ]\"\n        >\n          <el-radio-group v-model=\"taskForm.targetKey\">\n            <el-radio-button\n              v-for=\"item in returnTaskList\"\n              :key=\"item.id\"\n              :label=\"item.id\"\n              >{{ item.name }}</el-radio-button\n            >\n          </el-radio-group>\n        </el-form-item>\n        <el-form-item\n          label=\"退回意见\"\n          prop=\"comment\"\n          :rules=\"[{ required: true, message: '请输入意见', trigger: 'blur' }]\"\n        >\n          <el-input\n            style=\"width: 50%\"\n            type=\"textarea\"\n            v-model=\"taskForm.comment\"\n            placeholder=\"请输入意见\"\n          />\n        </el-form-item>\n      </el-form>\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"returnOpen = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"taskReturn\">确 定</el-button>\n      </span>\n    </el-dialog>\n\n    <!--驳回流程-->\n    <el-dialog\n      :title=\"rejectTitle\"\n      :visible.sync=\"rejectOpen\"\n      width=\"40%\"\n      append-to-body\n    >\n      <el-form ref=\"taskForm\" :model=\"taskForm\" label-width=\"80px\">\n        <el-form-item\n          label=\"退回意见\"\n          prop=\"comment\"\n          :rules=\"[{ required: true, message: '请输入意见', trigger: 'blur' }]\"\n        >\n          <el-input\n            style=\"width: 50%\"\n            type=\"textarea\"\n            v-model=\"taskForm.comment\"\n            placeholder=\"请输入意见\"\n          />\n        </el-form-item>\n      </el-form>\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"rejectOpen = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"taskReject\">确 定</el-button>\n      </span>\n    </el-dialog>\n    <el-dialog\n      :title=\"rejectTitle1\"\n      :visible.sync=\"rejectOpen1\"\n      width=\"40%\"\n      append-to-body\n    >\n      <el-form ref=\"taskForm\" :model=\"taskForm\" label-width=\"80px\">\n        <el-form-item\n          label=\"驳回意见\"\n          prop=\"comment\"\n          :rules=\"[{ required: true, message: '请输入意见', trigger: 'blur' }]\"\n        >\n          <el-input\n            style=\"width: 50%\"\n            type=\"textarea\"\n            v-model=\"taskForm.comment\"\n            placeholder=\"请输入意见\"\n          />\n        </el-form-item>\n      </el-form>\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"rejectOpen1 = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"taskEnd\">确 定</el-button>\n      </span>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { flowRecord, getHisIns } from \"@/api/flowable/finished\";\nimport Parser from \"@/components/parser/Parser\";\nimport Upload from \"@/components/FileUpload\";\nimport {\n  definitionStartByKey,\n  getProcessVariables,\n  readXmlByKey,\n  getFlowViewer,\n} from \"@/api/flowable/definition\";\nimport {\n  complete,\n  rejectTask,\n  returnList,\n  returnTask,\n  getNextFlowNode,\n  delegate,\n  endTask,\n} from \"@/api/flowable/todo\";\nimport flow from \"@/views/flowable/task/record/flow\";\nimport { treeselect } from \"@/api/system/dept\";\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\";\nimport Treeselect from \"@riophae/vue-treeselect\";\nimport { listUser } from \"@/api/system/user\";\nimport moment from \"moment\";\nexport default {\n  name: \"Record\",\n  components: {\n    Parser,\n    flow,\n    Treeselect,\n    Upload,\n  },\n  props: {\n    procDefKey: {\n      type: String,\n      default: undefined,\n    },\n    taskId: {\n      type: String,\n      default: undefined,\n    },\n    procInsId: {\n      type: String,\n      default: undefined,\n    },\n    bizKey: {\n      type: String,\n      default: undefined,\n    },\n    finished: {\n      type: Boolean,\n      default: true,\n    },\n    isAuthImages: {\n      type: Boolean,\n      default: false,\n    },\n    viewOpen: {\n      type: Boolean,\n      default: false,\n    },\n  },\n  data() {\n    return {\n      // 模型xml数据\n      xmlData: \"\",\n      taskList: [],\n      // 部门名称\n      deptName: undefined,\n      // 部门树选项\n      deptOptions: undefined,\n      // 用户表格数据\n      userList: null,\n      defaultProps: {\n        children: \"children\",\n        label: \"label\",\n      },\n      // 查询参数\n      queryParams: {\n        deptId: undefined,\n      },\n      typeArr: [\"zip\", \"rar\"],\n      // 遮罩层\n      loading: true,\n      flowRecordList: [], // 流程流转数据\n      flowRecordLists: {},\n      formConfCopy: {},\n      src: null,\n      rules: {}, // 表单校验\n      variablesForm: {}, // 流程变量数据\n      taskForm: {\n        returnTaskShow: false, // 是否展示回退表单\n        delegateTaskShow: false, // 是否展示回退表单\n        defaultTaskShow: true, // 默认处理\n        sendUserShow: false, // 审批用户\n        multiple: false,\n        comment: \"\", // 意见内容\n        procInsId: \"\", // 流程实例编号\n        instanceId: \"\", // 流程实例编号\n        taskId: \"\", // 流程任务编号\n        procDefKey: \"\", // 流程编号\n        vars: \"\",\n        targetKey: \"\",\n        authImages: \"\",\n      },\n      userDataList: [], // 流程候选人\n      assignee: null,\n      formConf: {}, // 默认表单数据\n      formConfOpen: false, // 是否加载默认表单数据\n      variables: [], // 流程变量数据\n      variablesData: {}, // 流程变量数据\n      variableOpen: false, // 是否加载流程变量数据\n      returnTaskList: [], // 回退列表数据\n      completeTitle: null,\n      completeOpen: false,\n      returnTitle: null,\n      returnOpen: false,\n      rejectOpen: false,\n      rejectTitle: null,\n      rejectOpen1: false,\n      rejectTitle1: null,\n      userData: [],\n      audit: true,\n      canFinish: false,\n      flowHis: [],\n      flowActive: null,\n      bizKey: null,\n      isCommon: false,\n      authImages: [],\n    };\n  },\n  created() {\n    if (\n      this.$store.state.user.roles &&\n      this.$store.state.user.roles.includes(\"common\")\n    ) {\n      this.isCommon = true;\n    }\n    console.log(\"========record========created=>>>\");\n    console.log(this._props);\n    let that = this;\n    let { taskId, procDefKey, procInsId, finished, bizKey } = this._props;\n    // if(!viewOpen){\n    //   console.log(\"===>>>关闭,不渲染\")\n    //   return;\n    // }\n    this.taskForm.taskId = taskId;\n    this.taskForm.procInsId = procInsId;\n    this.taskForm.instanceId = procInsId;\n    // 初始化表单\n    this.taskForm.procDefKey = procDefKey;\n    this.bizKey = bizKey;\n    //重置\n    that.flowHis = [];\n    that.flowRecordLists = null;\n    // 回显流程记录\n    if (procInsId) {\n      this.getFlowViewer(this.taskForm.procInsId, this.taskForm.procDefKey);\n      // 流程任务重获取变量表单\n      if (this.taskForm.taskId) {\n        this.processVariables(this.taskForm.taskId);\n        this.getNextFlowNode(this.taskForm.taskId);\n      }\n      this.getFlowRecordList(this.taskForm.procInsId);\n      that.flowActive = procInsId;\n      getHisIns({\n        bizKey: that.bizKey,\n        defKey: procDefKey,\n      }).then((resp) => {\n        if (resp.data && resp.data.length > 0) {\n          that.taskForm.taskName = resp.data[0].name;\n        }\n        if (resp.data.length > 1) {\n          that.flowHis = resp.data;\n          that.flowRecordLists = new Map();\n          that.flowRecordLists.set(procInsId, that.flowRecordList);\n        }\n      });\n    } else {\n      this.getModelDetail(procDefKey);\n    }\n    this.finished = finished;\n  },\n  mounted() {\n    // // 表单数据回填，模拟异步请求场景\n    // setTimeout(() => {\n    //   // 请求回来的表单数据\n    //   const data = {\n    //     field102: '18836662555'\n    //   }\n    //   // 回填数据\n    //   this.fillFormData(this.formConf, data)\n    //   // 更新表单\n    //   this.key = +new Date().getTime()\n    // }, 1000)\n  },\n  methods: {\n    /** 查询部门下拉树结构 */\n    getTreeselect() {\n      treeselect().then((response) => {\n        this.deptOptions = response.data;\n      });\n    },\n    /** 查询用户列表 */\n    getList() {\n      listUser(this.addDateRange(this.queryParams, this.dateRange)).then(\n        (response) => {\n          this.userList = response.rows;\n          this.total = response.total;\n        }\n      );\n    },\n    // 筛选节点\n    filterNode(value, data) {\n      if (!value) return true;\n      return data.label.indexOf(value) !== -1;\n    },\n    // 节点单击事件\n    handleNodeClick(data) {\n      this.queryParams.deptId = data.id;\n      this.getList();\n    },\n    /** xml 文件 */\n    getModelDetail(deployKey) {\n      // 发送请求，获取xml\n      readXmlByKey(deployKey).then((res) => {\n        this.xmlData = res.data;\n      });\n    },\n    getFlowViewer(procInsId, deployKey) {\n      getFlowViewer(procInsId).then((res) => {\n        this.taskList = res.data;\n        this.getModelDetail(deployKey);\n      });\n    },\n    setIcon(val) {\n      if (val) {\n        return \"el-icon-check\";\n      } else {\n        return \"el-icon-time\";\n      }\n    },\n    setColor(val) {\n      if (val) {\n        return \"#2bc418\";\n      } else {\n        return \"#b3bdbb\";\n      }\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.userData = selection;\n      const val = selection.map((item) => item.userId)[0];\n      if (val instanceof Array) {\n        this.taskForm.values = {\n          approval: val.join(\",\"),\n        };\n      } else {\n        this.taskForm.values = {\n          approval: val,\n        };\n      }\n    },\n    // 关闭标签\n    handleClose(tag) {\n      this.userData.splice(this.userData.indexOf(tag), 1);\n    },\n    /** 流程变量赋值 */\n    handleCheckChange(val) {\n      if (val instanceof Array) {\n        this.taskForm.values = {\n          approval: val.join(\",\"),\n        };\n      } else {\n        this.taskForm.values = {\n          approval: val,\n        };\n      }\n    },\n    /** 流程流转记录 */\n    getFlowRecordList(procInsId) {\n      let that = this;\n      const params = { procInsId: procInsId };\n      flowRecord(params)\n        .then((res) => {\n          that.flowRecordList = res.data.flowList;\n          // 流程过程中不存在初始化表单 直接读取的流程变量中存储的表单值\n          if (res.data.formData) {\n            that.formConf = res.data.formData;\n            that.formConfOpen = true;\n          }\n          if (that.flowRecordLists) {\n            that.flowRecordLists.set(procInsId, that.flowRecordList);\n          }\n        })\n        .catch((res) => {\n          that.$router.go(0);\n        });\n    },\n    fillFormData(form, data) {\n      form.fields.forEach((item) => {\n        const val = data[item.__vModel__];\n        if (val) {\n          item.__config__.defaultValue = val;\n        }\n      });\n    },\n    /** 获取流程变量内容 */\n    processVariables(taskId) {\n      if (taskId) {\n        // 提交流程申请时填写的表单存入了流程变量中后续任务处理时需要展示\n        getProcessVariables(taskId).then((res) => {\n          // this.variables = res.data.variables;\n          this.variablesData = res.data.variables;\n          this.variableOpen = true;\n        });\n      }\n    },\n    /** 根据当前任务或者流程设计配置的下一步节点 */\n    getNextFlowNode(taskId) {\n      // 根据当前任务或者流程设计配置的下一步节点 todo 暂时未涉及到考虑网关、表达式和多节点情况\n      const params = { taskId: taskId };\n      getNextFlowNode(params).then((res) => {\n        const data = res.data;\n        if (data) {\n          if (data.type === \"assignee\") {\n            this.userDataList = res.data.userList;\n          } else if (data.type === \"candidateUsers\") {\n            this.userDataList = res.data.userList;\n            this.taskForm.multiple = true;\n          } else if (data.type === \"candidateGroups\") {\n            res.data.roleList.forEach((role) => {\n              role.userId = role.roleId;\n              role.nickName = role.roleName;\n            });\n            this.userDataList = res.data.roleList;\n            this.taskForm.multiple = false;\n          } else if (data.type === \"multiInstance\") {\n            this.userDataList = res.data.userList;\n            this.taskForm.multiple = true;\n          }\n          this.taskForm.sendUserShow = true;\n        } else {\n          this.canFinish = true;\n        }\n      });\n    },\n    /** 审批任务选择 */\n    handleComplete() {\n      this.completeOpen = true;\n      this.completeTitle = \"审批流程\";\n      //this.getTreeselect();\n    },\n    /** 审批任务 */\n    taskComplete(comment) {\n      // if (!this.taskForm.values){\n      //   this.msgError(\"请选择流程接收人员\");\n      //   return;\n      // }\n      if (\n        comment &&\n        typeof comment == \"string\" &&\n        comment.constructor == String\n      ) {\n        this.taskForm.comment = comment;\n      }\n      if (!this.taskForm.comment) {\n        this.msgError(\"请输入审批意见\");\n        return;\n      }\n      //if(this.canFinish){\n      this.taskForm.values = {};\n      this.taskForm.bizKey = this.bizKey;\n      this.taskForm.values.isReject = false;\n      //}\n      complete(this.taskForm).then((response) => {\n        this.msgSuccess(response.msg);\n        //this.goBack();\n        this.$router.go(0);\n      });\n    },\n    /** 委派任务 */\n    handleDelegate() {\n      this.taskForm.delegateTaskShow = true;\n      this.taskForm.defaultTaskShow = false;\n    },\n    handleAssign() {},\n    /** 返回页面 */\n    goBack() {\n      // 关闭当前标签页并返回上个页面\n      this.$store.dispatch(\"tagsView/delView\", this.$route);\n      this.$router.go(-1);\n    },\n    /** 接收子组件传的值 */\n    getData(data) {\n      if (data) {\n        const variables = [];\n        data.fields.forEach((item) => {\n          let variableData = {};\n          variableData.label = item.__config__.label;\n          // 表单值为多个选项时\n          if (item.__config__.defaultValue instanceof Array) {\n            const array = [];\n            item.__config__.defaultValue.forEach((val) => {\n              array.push(val);\n            });\n            variableData.val = array;\n          } else {\n            variableData.val = item.__config__.defaultValue;\n          }\n          variables.push(variableData);\n        });\n        this.variables = variables;\n      }\n    },\n    // 接收子组件图片上传的值\n    imageUrls(value) {\n      this.taskForm.authImages = value;\n    },\n    /** 申请流程表单数据提交 */\n    // submitForm(data) {\n    //   if (data) {\n    //     const variables = data.valData;\n    //     const formData = data.formData;\n    //     formData.disabled = true;\n    //     formData.formBtns = false;\n    //     if (this.taskForm.procDefKey) {\n    //       variables.variables = formData;\n    //       variables.businessKey = data.businessKey;\n    //        // 启动流程并将表单数据加入流程变量\n    //       definitionStartByKey(this.taskForm.procDefKey, JSON.stringify(variables)).then(res => {\n    //         this.msgSuccess(res.msg);\n    //         this.goBack();\n    //       })\n    //     }\n    //   }\n    // },\n    startFlow(businessKey, name, variables) {\n      let startDate = moment(new Date()).format(\"YYYYMMDDHHmmss\");\n      const data = {};\n      if (this.taskForm.procDefKey) {\n        if (!variables) {\n          data.variables = {};\n        } else {\n          data.variables = variables;\n        }\n        data.businessKey = businessKey;\n        data.procDefKey = this.taskForm.procDefKey;\n        data.taskName = name;\n        // 启动流程并将表单数据加入流程变量\n        definitionStartByKey(JSON.stringify(data)).then((res) => {\n          this.msgSuccess(res.msg);\n          this.goBack();\n        });\n      }\n    },\n    /** 驳回任务 */\n    handleReject() {\n      this.rejectOpen = true;\n      this.rejectTitle = \"退回流程\";\n    },\n    /** 驳回任务 */\n    taskReject() {\n      this.$refs[\"taskForm\"].validate((valid) => {\n        if (valid) {\n          rejectTask(this.taskForm).then((res) => {\n            this.msgSuccess(res.msg);\n            this.$router.go(0);\n          });\n        }\n      });\n    },\n    /** 可退回任务列表 */\n    handleReturn() {\n      this.returnOpen = true;\n      this.returnTitle = \"退回流程\";\n      returnList(this.taskForm).then((res) => {\n        this.returnTaskList = res.data;\n        this.taskForm.values = null;\n        if (res.data && res.data.length > 0) {\n          this.taskForm.targetKey = res.data[res.data.length - 1].id;\n        }\n      });\n    },\n    /** 提交退回任务 */\n    taskReturn() {\n      this.$refs[\"taskForm\"].validate((valid) => {\n        if (valid) {\n          //退回业务ID，用于修改状态\n          this.taskForm.bizKey = this.bizKey;\n          returnTask(this.taskForm).then((res) => {\n            this.msgSuccess(res.msg);\n            this.$router.go(0);\n          });\n        }\n      });\n    },\n    /** 取消回退任务按钮 */\n    cancelTask() {\n      this.taskForm.returnTaskShow = false;\n      this.taskForm.defaultTaskShow = true;\n      this.taskForm.sendUserShow = true;\n      this.returnTaskList = [];\n    },\n    /** 委派任务 */\n    submitDeleteTask() {\n      this.$refs[\"taskForm\"].validate((valid) => {\n        if (valid) {\n          delegate(this.taskForm).then((response) => {\n            this.msgSuccess(response.msg);\n            this.$router.go(0);\n          });\n        }\n      });\n    },\n    /** 取消回退任务按钮 */\n    cancelDelegateTask() {\n      this.taskForm.delegateTaskShow = false;\n      this.taskForm.defaultTaskShow = true;\n      this.taskForm.sendUserShow = true;\n      this.returnTaskList = [];\n    },\n    handleEnd() {\n      this.rejectOpen1 = true;\n      this.rejectTitle1 = \"驳回流程\";\n    },\n    /** 驳回结束任务 */\n    taskEnd() {\n      this.$refs[\"taskForm\"].validate((valid) => {\n        if (valid) {\n          this.taskForm.values = {};\n          this.taskForm.values.isReject = true;\n          endTask(this.taskForm).then((res) => {\n            this.msgSuccess(res.msg);\n            this.$router.go(0);\n          });\n        }\n      });\n    },\n    handleClick(tab, event) {\n      if (this.flowRecordLists && this.flowRecordLists.get(tab.name)) {\n        this.flowRecordList = this.flowRecordLists.get(tab.name);\n      } else {\n        this.getFlowRecordList(tab.name);\n      }\n    },\n  },\n};\n</script>\n<style lang=\"scss\" scoped>\n@media screen and (max-width: 600px) {\n  .el-timeline {\n    margin: 0;\n    font-size: 14px;\n    list-style: none;\n    padding-left: 0;\n  }\n\n  ::v-deep .audit-record .el-card__body {\n    display: flex;\n    flex-direction: column;\n  }\n}\n\n.test-form {\n  margin: 15px auto;\n  width: 800px;\n  padding: 15px;\n}\n\n.clearfix:before,\n.clearfix:after {\n  display: table;\n  content: \"\";\n}\n.clearfix:after {\n  clear: both;\n}\n\n.box-card {\n  width: 100%;\n  margin-bottom: 20px;\n}\n\n.el-tag + .el-tag {\n  margin-left: 10px;\n}\n</style>\n"]}]}