{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/tool/build/RightPanel.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/tool/build/RightPanel.vue", "mtime": 1641277326000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGlzQXJyYXkgfSBmcm9tICd1dGlsJwppbXBvcnQgVHJlZU5vZGVEaWFsb2cgZnJvbSAnLi9UcmVlTm9kZURpYWxvZycKaW1wb3J0IHsgaXNOdW1iZXJTdHIgfSBmcm9tICdAL3V0aWxzL2luZGV4JwppbXBvcnQgSWNvbnNEaWFsb2cgZnJvbSAnLi9JY29uc0RpYWxvZycKaW1wb3J0IHsKICBpbnB1dENvbXBvbmVudHMsIHNlbGVjdENvbXBvbmVudHMsIGxheW91dENvbXBvbmVudHMKfSBmcm9tICdAL3V0aWxzL2dlbmVyYXRvci9jb25maWcnCmltcG9ydCB7IHNhdmVGb3JtQ29uZiB9IGZyb20gJ0AvdXRpbHMvZGInCgpjb25zdCBkYXRlVGltZUZvcm1hdCA9IHsKICBkYXRlOiAneXl5eS1NTS1kZCcsCiAgd2VlazogJ3l5eXkg56ysIFdXIOWRqCcsCiAgbW9udGg6ICd5eXl5LU1NJywKICB5ZWFyOiAneXl5eScsCiAgZGF0ZXRpbWU6ICd5eXl5LU1NLWRkIEhIOm1tOnNzJywKICBkYXRlcmFuZ2U6ICd5eXl5LU1NLWRkJywKICBtb250aHJhbmdlOiAneXl5eS1NTScsCiAgZGF0ZXRpbWVyYW5nZTogJ3l5eXktTU0tZGQgSEg6bW06c3MnCn0KCi8vIOS9v2NoYW5nZVJlbmRlcktleeWcqOebruagh+e7hOS7tuaUueWPmOaXtuWPr+eUqApjb25zdCBuZWVkUmVyZW5kZXJMaXN0ID0gWyd0aW55bWNlJ10KCmV4cG9ydCBkZWZhdWx0IHsKICBjb21wb25lbnRzOiB7CiAgICBUcmVlTm9kZURpYWxvZywKICAgIEljb25zRGlhbG9nCiAgfSwKICBwcm9wczogWydzaG93RmllbGQnLCAnYWN0aXZlRGF0YScsICdmb3JtQ29uZiddLAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBjdXJyZW50VGFiOiAnZmllbGQnLAogICAgICBjdXJyZW50Tm9kZTogbnVsbCwKICAgICAgZGlhbG9nVmlzaWJsZTogZmFsc2UsCiAgICAgIGljb25zVmlzaWJsZTogZmFsc2UsCiAgICAgIGN1cnJlbnRJY29uTW9kZWw6IG51bGwsCiAgICAgIGRhdGVUeXBlT3B0aW9uczogWwogICAgICAgIHsKICAgICAgICAgIGxhYmVsOiAn5pelKGRhdGUpJywKICAgICAgICAgIHZhbHVlOiAnZGF0ZScKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIGxhYmVsOiAn5ZGoKHdlZWspJywKICAgICAgICAgIHZhbHVlOiAnd2VlaycKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIGxhYmVsOiAn5pyIKG1vbnRoKScsCiAgICAgICAgICB2YWx1ZTogJ21vbnRoJwogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgbGFiZWw6ICflubQoeWVhciknLAogICAgICAgICAgdmFsdWU6ICd5ZWFyJwogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgbGFiZWw6ICfml6XmnJ/ml7bpl7QoZGF0ZXRpbWUpJywKICAgICAgICAgIHZhbHVlOiAnZGF0ZXRpbWUnCiAgICAgICAgfQogICAgICBdLAogICAgICBkYXRlUmFuZ2VUeXBlT3B0aW9uczogWwogICAgICAgIHsKICAgICAgICAgIGxhYmVsOiAn5pel5pyf6IyD5Zu0KGRhdGVyYW5nZSknLAogICAgICAgICAgdmFsdWU6ICdkYXRlcmFuZ2UnCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBsYWJlbDogJ+aciOiMg+WbtChtb250aHJhbmdlKScsCiAgICAgICAgICB2YWx1ZTogJ21vbnRocmFuZ2UnCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBsYWJlbDogJ+aXpeacn+aXtumXtOiMg+WbtChkYXRldGltZXJhbmdlKScsCiAgICAgICAgICB2YWx1ZTogJ2RhdGV0aW1lcmFuZ2UnCiAgICAgICAgfQogICAgICBdLAogICAgICBjb2xvckZvcm1hdE9wdGlvbnM6IFsKICAgICAgICB7CiAgICAgICAgICBsYWJlbDogJ2hleCcsCiAgICAgICAgICB2YWx1ZTogJ2hleCcKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIGxhYmVsOiAncmdiJywKICAgICAgICAgIHZhbHVlOiAncmdiJwogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgbGFiZWw6ICdyZ2JhJywKICAgICAgICAgIHZhbHVlOiAncmdiYScKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIGxhYmVsOiAnaHN2JywKICAgICAgICAgIHZhbHVlOiAnaHN2JwogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgbGFiZWw6ICdoc2wnLAogICAgICAgICAgdmFsdWU6ICdoc2wnCiAgICAgICAgfQogICAgICBdLAogICAgICBqdXN0aWZ5T3B0aW9uczogWwogICAgICAgIHsKICAgICAgICAgIGxhYmVsOiAnc3RhcnQnLAogICAgICAgICAgdmFsdWU6ICdzdGFydCcKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIGxhYmVsOiAnZW5kJywKICAgICAgICAgIHZhbHVlOiAnZW5kJwogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgbGFiZWw6ICdjZW50ZXInLAogICAgICAgICAgdmFsdWU6ICdjZW50ZXInCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBsYWJlbDogJ3NwYWNlLWFyb3VuZCcsCiAgICAgICAgICB2YWx1ZTogJ3NwYWNlLWFyb3VuZCcKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIGxhYmVsOiAnc3BhY2UtYmV0d2VlbicsCiAgICAgICAgICB2YWx1ZTogJ3NwYWNlLWJldHdlZW4nCiAgICAgICAgfQogICAgICBdLAogICAgICBsYXlvdXRUcmVlUHJvcHM6IHsKICAgICAgICBsYWJlbChkYXRhLCBub2RlKSB7CiAgICAgICAgICBjb25zdCBjb25maWcgPSBkYXRhLl9fY29uZmlnX18KICAgICAgICAgIHJldHVybiBkYXRhLmNvbXBvbmVudE5hbWUgfHwgYCR7Y29uZmlnLmxhYmVsfTogJHtkYXRhLl9fdk1vZGVsX199YAogICAgICAgIH0KICAgICAgfQogICAgfQogIH0sCiAgY29tcHV0ZWQ6IHsKICAgIGRvY3VtZW50TGluaygpIHsKICAgICAgcmV0dXJuICgKICAgICAgICB0aGlzLmFjdGl2ZURhdGEuX19jb25maWdfXy5kb2N1bWVudAogICAgICAgIHx8ICdodHRwczovL2VsZW1lbnQuZWxlbWUuY24vIy96aC1DTi9jb21wb25lbnQvaW5zdGFsbGF0aW9uJwogICAgICApCiAgICB9LAogICAgZGF0ZU9wdGlvbnMoKSB7CiAgICAgIGlmICgKICAgICAgICB0aGlzLmFjdGl2ZURhdGEudHlwZSAhPT0gdW5kZWZpbmVkCiAgICAgICAgJiYgdGhpcy5hY3RpdmVEYXRhLl9fY29uZmlnX18udGFnID09PSAnZWwtZGF0ZS1waWNrZXInCiAgICAgICkgewogICAgICAgIGlmICh0aGlzLmFjdGl2ZURhdGFbJ3N0YXJ0LXBsYWNlaG9sZGVyJ10gPT09IHVuZGVmaW5lZCkgewogICAgICAgICAgcmV0dXJuIHRoaXMuZGF0ZVR5cGVPcHRpb25zCiAgICAgICAgfQogICAgICAgIHJldHVybiB0aGlzLmRhdGVSYW5nZVR5cGVPcHRpb25zCiAgICAgIH0KICAgICAgcmV0dXJuIFtdCiAgICB9LAogICAgdGFnTGlzdCgpIHsKICAgICAgcmV0dXJuIFsKICAgICAgICB7CiAgICAgICAgICBsYWJlbDogJ+i+k+WFpeWei+e7hOS7ticsCiAgICAgICAgICBvcHRpb25zOiBpbnB1dENvbXBvbmVudHMKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIGxhYmVsOiAn6YCJ5oup5Z6L57uE5Lu2JywKICAgICAgICAgIG9wdGlvbnM6IHNlbGVjdENvbXBvbmVudHMKICAgICAgICB9CiAgICAgIF0KICAgIH0sCiAgICBhY3RpdmVUYWcoKSB7CiAgICAgIHJldHVybiB0aGlzLmFjdGl2ZURhdGEuX19jb25maWdfXy50YWcKICAgIH0sCiAgICBpc1Nob3dNaW4oKSB7CiAgICAgIHJldHVybiBbJ2VsLWlucHV0LW51bWJlcicsICdlbC1zbGlkZXInXS5pbmRleE9mKHRoaXMuYWN0aXZlVGFnKSA+IC0xCiAgICB9LAogICAgaXNTaG93TWF4KCkgewogICAgICByZXR1cm4gWydlbC1pbnB1dC1udW1iZXInLCAnZWwtc2xpZGVyJywgJ2VsLXJhdGUnXS5pbmRleE9mKHRoaXMuYWN0aXZlVGFnKSA+IC0xCiAgICB9LAogICAgaXNTaG93U3RlcCgpIHsKICAgICAgcmV0dXJuIFsnZWwtaW5wdXQtbnVtYmVyJywgJ2VsLXNsaWRlciddLmluZGV4T2YodGhpcy5hY3RpdmVUYWcpID4gLTEKICAgIH0KICB9LAogIHdhdGNoOiB7CiAgICBmb3JtQ29uZjogewogICAgICBoYW5kbGVyKHZhbCkgewogICAgICAgIHNhdmVGb3JtQ29uZih2YWwpCiAgICAgIH0sCiAgICAgIGRlZXA6IHRydWUKICAgIH0KICB9LAogIG1ldGhvZHM6IHsKICAgIGFkZFJlZygpIHsKICAgICAgdGhpcy5hY3RpdmVEYXRhLl9fY29uZmlnX18ucmVnTGlzdC5wdXNoKHsKICAgICAgICBwYXR0ZXJuOiAnJywKICAgICAgICBtZXNzYWdlOiAnJwogICAgICB9KQogICAgfSwKICAgIGFkZFNlbGVjdEl0ZW0oKSB7CiAgICAgIHRoaXMuYWN0aXZlRGF0YS5fX3Nsb3RfXy5vcHRpb25zLnB1c2goewogICAgICAgIGxhYmVsOiAnJywKICAgICAgICB2YWx1ZTogJycKICAgICAgfSkKICAgIH0sCiAgICBhZGRUcmVlSXRlbSgpIHsKICAgICAgKyt0aGlzLmlkR2xvYmFsCiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHRydWUKICAgICAgdGhpcy5jdXJyZW50Tm9kZSA9IHRoaXMuYWN0aXZlRGF0YS5vcHRpb25zCiAgICB9LAogICAgcmVuZGVyQ29udGVudChoLCB7IG5vZGUsIGRhdGEsIHN0b3JlIH0pIHsKICAgICAgcmV0dXJuICgKICAgICAgICA8ZGl2IGNsYXNzPSJjdXN0b20tdHJlZS1ub2RlIj4KICAgICAgICAgIDxzcGFuPntub2RlLmxhYmVsfTwvc3Bhbj4KICAgICAgICAgIDxzcGFuIGNsYXNzPSJub2RlLW9wZXJhdGlvbiI+CiAgICAgICAgICAgIDxpIG9uLWNsaWNrPXsoKSA9PiB0aGlzLmFwcGVuZChkYXRhKX0KICAgICAgICAgICAgICBjbGFzcz0iZWwtaWNvbi1wbHVzIgogICAgICAgICAgICAgIHRpdGxlPSLmt7vliqAiCiAgICAgICAgICAgID48L2k+CiAgICAgICAgICAgIDxpIG9uLWNsaWNrPXsoKSA9PiB0aGlzLnJlbW92ZShub2RlLCBkYXRhKX0KICAgICAgICAgICAgICBjbGFzcz0iZWwtaWNvbi1kZWxldGUiCiAgICAgICAgICAgICAgdGl0bGU9IuWIoOmZpCIKICAgICAgICAgICAgPjwvaT4KICAgICAgICAgIDwvc3Bhbj4KICAgICAgICA8L2Rpdj4KICAgICAgKQogICAgfSwKICAgIGFwcGVuZChkYXRhKSB7CiAgICAgIGlmICghZGF0YS5jaGlsZHJlbikgewogICAgICAgIHRoaXMuJHNldChkYXRhLCAnY2hpbGRyZW4nLCBbXSkKICAgICAgfQogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSB0cnVlCiAgICAgIHRoaXMuY3VycmVudE5vZGUgPSBkYXRhLmNoaWxkcmVuCiAgICB9LAogICAgcmVtb3ZlKG5vZGUsIGRhdGEpIHsKICAgICAgdGhpcy5hY3RpdmVEYXRhLl9fY29uZmlnX18uZGVmYXVsdFZhbHVlID0gW10gLy8g6YG/5YWN5Yig6Zmk5pe25oql6ZSZCiAgICAgIGNvbnN0IHsgcGFyZW50IH0gPSBub2RlCiAgICAgIGNvbnN0IGNoaWxkcmVuID0gcGFyZW50LmRhdGEuY2hpbGRyZW4gfHwgcGFyZW50LmRhdGEKICAgICAgY29uc3QgaW5kZXggPSBjaGlsZHJlbi5maW5kSW5kZXgoZCA9PiBkLmlkID09PSBkYXRhLmlkKQogICAgICBjaGlsZHJlbi5zcGxpY2UoaW5kZXgsIDEpCiAgICB9LAogICAgYWRkTm9kZShkYXRhKSB7CiAgICAgIHRoaXMuY3VycmVudE5vZGUucHVzaChkYXRhKQogICAgfSwKICAgIHNldE9wdGlvblZhbHVlKGl0ZW0sIHZhbCkgewogICAgICBpdGVtLnZhbHVlID0gaXNOdW1iZXJTdHIodmFsKSA/ICt2YWwgOiB2YWwKICAgIH0sCiAgICBzZXREZWZhdWx0VmFsdWUodmFsKSB7CiAgICAgIGlmIChBcnJheS5pc0FycmF5KHZhbCkpIHsKICAgICAgICByZXR1cm4gdmFsLmpvaW4oJywnKQogICAgICB9CiAgICAgIC8vIGlmIChbJ3N0cmluZycsICdudW1iZXInXS5pbmRleE9mKHR5cGVvZiB2YWwpID4gLTEpIHsKICAgICAgLy8gICByZXR1cm4gdmFsCiAgICAgIC8vIH0KICAgICAgaWYgKHR5cGVvZiB2YWwgPT09ICdib29sZWFuJykgewogICAgICAgIHJldHVybiBgJHt2YWx9YAogICAgICB9CiAgICAgIHJldHVybiB2YWwKICAgIH0sCiAgICBvbkRlZmF1bHRWYWx1ZUlucHV0KHN0cikgewogICAgICBpZiAoaXNBcnJheSh0aGlzLmFjdGl2ZURhdGEuX19jb25maWdfXy5kZWZhdWx0VmFsdWUpKSB7CiAgICAgICAgLy8g5pWw57uECiAgICAgICAgdGhpcy4kc2V0KAogICAgICAgICAgdGhpcy5hY3RpdmVEYXRhLl9fY29uZmlnX18sCiAgICAgICAgICAnZGVmYXVsdFZhbHVlJywKICAgICAgICAgIHN0ci5zcGxpdCgnLCcpLm1hcCh2YWwgPT4gKGlzTnVtYmVyU3RyKHZhbCkgPyArdmFsIDogdmFsKSkKICAgICAgICApCiAgICAgIH0gZWxzZSBpZiAoWyd0cnVlJywgJ2ZhbHNlJ10uaW5kZXhPZihzdHIpID4gLTEpIHsKICAgICAgICAvLyDluIPlsJQKICAgICAgICB0aGlzLiRzZXQodGhpcy5hY3RpdmVEYXRhLl9fY29uZmlnX18sICdkZWZhdWx0VmFsdWUnLCBKU09OLnBhcnNlKHN0cikpCiAgICAgIH0gZWxzZSB7CiAgICAgICAgLy8g5a2X56ym5Liy5ZKM5pWw5a2XCiAgICAgICAgdGhpcy4kc2V0KAogICAgICAgICAgdGhpcy5hY3RpdmVEYXRhLl9fY29uZmlnX18sCiAgICAgICAgICAnZGVmYXVsdFZhbHVlJywKICAgICAgICAgIGlzTnVtYmVyU3RyKHN0cikgPyArc3RyIDogc3RyCiAgICAgICAgKQogICAgICB9CiAgICB9LAogICAgb25Td2l0Y2hWYWx1ZUlucHV0KHZhbCwgbmFtZSkgewogICAgICBpZiAoWyd0cnVlJywgJ2ZhbHNlJ10uaW5kZXhPZih2YWwpID4gLTEpIHsKICAgICAgICB0aGlzLiRzZXQodGhpcy5hY3RpdmVEYXRhLCBuYW1lLCBKU09OLnBhcnNlKHZhbCkpCiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy4kc2V0KHRoaXMuYWN0aXZlRGF0YSwgbmFtZSwgaXNOdW1iZXJTdHIodmFsKSA/ICt2YWwgOiB2YWwpCiAgICAgIH0KICAgIH0sCiAgICBzZXRUaW1lVmFsdWUodmFsLCB0eXBlKSB7CiAgICAgIGNvbnN0IHZhbHVlRm9ybWF0ID0gdHlwZSA9PT0gJ3dlZWsnID8gZGF0ZVRpbWVGb3JtYXQuZGF0ZSA6IHZhbAogICAgICB0aGlzLiRzZXQodGhpcy5hY3RpdmVEYXRhLl9fY29uZmlnX18sICdkZWZhdWx0VmFsdWUnLCBudWxsKQogICAgICB0aGlzLiRzZXQodGhpcy5hY3RpdmVEYXRhLCAndmFsdWUtZm9ybWF0JywgdmFsdWVGb3JtYXQpCiAgICAgIHRoaXMuJHNldCh0aGlzLmFjdGl2ZURhdGEsICdmb3JtYXQnLCB2YWwpCiAgICB9LAogICAgc3BhbkNoYW5nZSh2YWwpIHsKICAgICAgdGhpcy5mb3JtQ29uZi5zcGFuID0gdmFsCiAgICB9LAogICAgbXVsdGlwbGVDaGFuZ2UodmFsKSB7CiAgICAgIHRoaXMuJHNldCh0aGlzLmFjdGl2ZURhdGEuX19jb25maWdfXywgJ2RlZmF1bHRWYWx1ZScsIHZhbCA/IFtdIDogJycpCiAgICB9LAogICAgZGF0ZVR5cGVDaGFuZ2UodmFsKSB7CiAgICAgIHRoaXMuc2V0VGltZVZhbHVlKGRhdGVUaW1lRm9ybWF0W3ZhbF0sIHZhbCkKICAgIH0sCiAgICByYW5nZUNoYW5nZSh2YWwpIHsKICAgICAgdGhpcy4kc2V0KAogICAgICAgIHRoaXMuYWN0aXZlRGF0YS5fX2NvbmZpZ19fLAogICAgICAgICdkZWZhdWx0VmFsdWUnLAogICAgICAgIHZhbCA/IFt0aGlzLmFjdGl2ZURhdGEubWluLCB0aGlzLmFjdGl2ZURhdGEubWF4XSA6IHRoaXMuYWN0aXZlRGF0YS5taW4KICAgICAgKQogICAgfSwKICAgIHJhdGVUZXh0Q2hhbmdlKHZhbCkgewogICAgICBpZiAodmFsKSB0aGlzLmFjdGl2ZURhdGFbJ3Nob3ctc2NvcmUnXSA9IGZhbHNlCiAgICB9LAogICAgcmF0ZVNjb3JlQ2hhbmdlKHZhbCkgewogICAgICBpZiAodmFsKSB0aGlzLmFjdGl2ZURhdGFbJ3Nob3ctdGV4dCddID0gZmFsc2UKICAgIH0sCiAgICBjb2xvckZvcm1hdENoYW5nZSh2YWwpIHsKICAgICAgdGhpcy5hY3RpdmVEYXRhLl9fY29uZmlnX18uZGVmYXVsdFZhbHVlID0gbnVsbAogICAgICB0aGlzLmFjdGl2ZURhdGFbJ3Nob3ctYWxwaGEnXSA9IHZhbC5pbmRleE9mKCdhJykgPiAtMQogICAgICB0aGlzLmFjdGl2ZURhdGEuX19jb25maWdfXy5yZW5kZXJLZXkgPSArbmV3IERhdGUoKSAvLyDmm7TmlrByZW5kZXJLZXks6YeN5paw5riy5p+T6K+l57uE5Lu2CiAgICB9LAogICAgb3Blbkljb25zRGlhbG9nKG1vZGVsKSB7CiAgICAgIHRoaXMuaWNvbnNWaXNpYmxlID0gdHJ1ZQogICAgICB0aGlzLmN1cnJlbnRJY29uTW9kZWwgPSBtb2RlbAogICAgfSwKICAgIHNldEljb24odmFsKSB7CiAgICAgIHRoaXMuYWN0aXZlRGF0YVt0aGlzLmN1cnJlbnRJY29uTW9kZWxdID0gdmFsCiAgICB9LAogICAgdGFnQ2hhbmdlKHRhZ0ljb24pIHsKICAgICAgbGV0IHRhcmdldCA9IGlucHV0Q29tcG9uZW50cy5maW5kKGl0ZW0gPT4gaXRlbS5fX2NvbmZpZ19fLnRhZ0ljb24gPT09IHRhZ0ljb24pCiAgICAgIGlmICghdGFyZ2V0KSB0YXJnZXQgPSBzZWxlY3RDb21wb25lbnRzLmZpbmQoaXRlbSA9PiBpdGVtLl9fY29uZmlnX18udGFnSWNvbiA9PT0gdGFnSWNvbikKICAgICAgdGhpcy4kZW1pdCgndGFnLWNoYW5nZScsIHRhcmdldCkKICAgIH0sCiAgICBjaGFuZ2VSZW5kZXJLZXkoKSB7CiAgICAgIGlmIChuZWVkUmVyZW5kZXJMaXN0LmluY2x1ZGVzKHRoaXMuYWN0aXZlRGF0YS5fX2NvbmZpZ19fLnRhZykpIHsKICAgICAgICB0aGlzLmFjdGl2ZURhdGEuX19jb25maWdfXy5yZW5kZXJLZXkgPSArbmV3IERhdGUoKQogICAgICB9CiAgICB9CiAgfQp9Cg=="}, null]}