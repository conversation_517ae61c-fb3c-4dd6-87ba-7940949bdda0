{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Process/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Process/index.vue", "mtime": 1661782128000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/Process", "sourcesContent": ["<template>\n  <div v-loading=\"isView\" class=\"flow-containers\" :class=\"{ 'view-mode': isView }\">\n    <el-container style=\"height: 100%\">\n      <el-header style=\"border-bottom: 1px solid rgb(218 218 218);height: auto;\">\n        <div style=\"display: flex; padding: 10px 0px; justify-content: space-between;\">\n          <div>\n            <el-upload action=\"\" :before-upload=\"openBpmn\" style=\"margin-right: 10px; display:inline-block;\">\n              <el-tooltip effect=\"dark\" content=\"加载xml\" placement=\"bottom\">\n                <el-button size=\"mini\" icon=\"el-icon-folder-opened\" />\n              </el-tooltip>\n            </el-upload>\n            <el-tooltip effect=\"dark\" content=\"新建\" placement=\"bottom\">\n              <el-button size=\"mini\" icon=\"el-icon-circle-plus\" @click=\"newDiagram\" />\n            </el-tooltip>\n            <el-tooltip effect=\"dark\" content=\"自适应屏幕\" placement=\"bottom\">\n              <el-button size=\"mini\" icon=\"el-icon-rank\" @click=\"fitViewport\" />\n            </el-tooltip>\n            <el-tooltip effect=\"dark\" content=\"放大\" placement=\"bottom\">\n              <el-button size=\"mini\" icon=\"el-icon-zoom-in\" @click=\"zoomViewport(true)\" />\n            </el-tooltip>\n            <el-tooltip effect=\"dark\" content=\"缩小\" placement=\"bottom\">\n              <el-button size=\"mini\" icon=\"el-icon-zoom-out\" @click=\"zoomViewport(false)\" />\n            </el-tooltip>\n            <el-tooltip effect=\"dark\" content=\"后退\" placement=\"bottom\">\n              <el-button size=\"mini\" icon=\"el-icon-back\" @click=\"modeler.get('commandStack').undo()\" />\n            </el-tooltip>\n            <el-tooltip effect=\"dark\" content=\"前进\" placement=\"bottom\">\n              <el-button size=\"mini\" icon=\"el-icon-right\" @click=\"modeler.get('commandStack').redo()\" />\n            </el-tooltip>\n          </div>\n          <div>\n            <el-button size=\"mini\" icon=\"el-icon-view\" @click=\"showXML\">查看xml</el-button>\n            <el-button size=\"mini\" icon=\"el-icon-download\" @click=\"saveXML(true)\">下载xml</el-button>\n            <el-button size=\"mini\" icon=\"el-icon-picture\" @click=\"saveImg('svg', true)\">下载svg</el-button>\n            <el-button size=\"mini\" type=\"primary\" @click=\"save\">保存模型</el-button>\n          </div>\n        </div>\n      </el-header>\n      <el-container style=\"align-items: stretch\">\n        <el-main style=\"padding: 0;\">\n          <div ref=\"canvas\" class=\"canvas\" />\n        </el-main>\n        <el-aside style=\"width: 400px; min-height: 650px; background-color: #f0f2f5\">\n          <panel v-if=\"modeler\" :modeler=\"modeler\" :users=\"users\" :groups=\"groups\" :categorys=\"categorys\" @dataType=\"dataType\" />\n        </el-aside>\n      </el-container>\n    </el-container>\n  </div>\n</template>\n\n<script>\n// 汉化\nimport customTranslate from './common/customTranslate'\nimport Modeler from 'bpmn-js/lib/Modeler'\nimport panel from './PropertyPanel'\nimport BpmData from './BpmData'\nimport getInitStr from './flowable/init'\nimport customContextPad from './components/custom'\n// 引入flowable的节点文件\nimport flowableModdle from './flowable/flowable.json'\nexport default {\n  name: 'WorkflowBpmnModeler',\n  components: {\n    panel\n  },\n  props: {\n    xml: {\n      type: String,\n      default: ''\n    },\n    users: {\n      type: Array,\n      default: () => []\n    },\n    groups: {\n      type: Array,\n      default: () => []\n    },\n    categorys: {\n      type: Array,\n      default: () => []\n    },\n    isView: {\n      type: Boolean,\n      default: false\n    },\n    taskList: {\n      type: Array,\n      default: () => []\n    }\n  },\n  data() {\n    return {\n      modeler: null,\n      // taskList: [],\n      zoom: 1\n    }\n  },\n  watch: {\n    xml: function(val) {\n      if (val) {\n        this.createNewDiagram(val)\n      }\n    }\n  },\n  mounted() {\n    // 生成实例\n    this.modeler = new Modeler({\n      container: this.$refs.canvas,\n      additionalModules: [\n        {\n          translate: ['value', customTranslate]\n        },\n        customContextPad\n      ],\n      moddleExtensions: {\n        flowable: flowableModdle\n      }\n    })\n    // 新增流程定义\n    if (!this.xml) {\n      this.newDiagram()\n    } else {\n      this.createNewDiagram(this.xml)\n    }\n  },\n  methods: {\n    newDiagram() {\n      this.createNewDiagram(getInitStr())\n    },\n    // 让图能自适应屏幕\n    fitViewport() {\n      this.zoom = this.modeler.get('canvas').zoom('fit-viewport')\n      const bbox = document.querySelector('.flow-containers .viewport').getBBox()\n      const currentViewbox = this.modeler.get('canvas').viewbox()\n      const elementMid = {\n        x: bbox.x + bbox.width / 2 - 65,\n        y: bbox.y + bbox.height / 2\n      }\n      this.modeler.get('canvas').viewbox({\n        x: elementMid.x - currentViewbox.width / 2,\n        y: elementMid.y - currentViewbox.height / 2,\n        width: currentViewbox.width,\n        height: currentViewbox.height\n      })\n      this.zoom = bbox.width / currentViewbox.width * 1.8\n    },\n    // 放大缩小\n    zoomViewport(zoomIn = true) {\n      this.zoom = this.modeler.get('canvas').zoom()\n      this.zoom += (zoomIn ? 0.1 : -0.1)\n      this.modeler.get('canvas').zoom(this.zoom)\n    },\n    async createNewDiagram(data) {\n      // 将字符串转换成图显示出来\n      // data = data.replace(/<!\\[CDATA\\[(.+?)]]>/g, '&lt;![CDATA[$1]]&gt;')\n      data = data.replace(/<!\\[CDATA\\[(.+?)]]>/g, function(match, str) {\n        return str.replace(/</g, '&lt;')\n      })\n      try {\n        await this.modeler.importXML(data)\n        this.adjustPalette()\n        this.fitViewport()\n        if (this.taskList !==undefined && this.taskList.length > 0 ) {\n          this.fillColor()\n        }\n      } catch (err) {\n        console.error(err.message, err.warnings)\n      }\n    },\n    // 调整左侧工具栏排版\n    adjustPalette() {\n      try {\n        // 获取 bpmn 设计器实例\n        const canvas = this.$refs.canvas\n        const djsPalette = canvas.children[0].children[1].children[4]\n        const djsPalStyle = {\n          width: '130px',\n          padding: '5px',\n          background: 'white',\n          left: '20px',\n          borderRadius: 0\n        }\n        for (var key in djsPalStyle) {\n          djsPalette.style[key] = djsPalStyle[key]\n        }\n        const palette = djsPalette.children[0]\n        const allGroups = palette.children\n        // console.log(\"===============>>>\")\n        // console.log(allGroups)\n        // allGroups[0].children[2].style['display'] = 'none'\n        // allGroups[0].children[3].style['display'] = 'none'\n        // 修改控件样式\n        for (var gKey in allGroups) {\n          const group = allGroups[gKey]\n          for (var cKey in group.children) {\n            const control = group.children[cKey]\n            const controlStyle = {\n              display: 'flex',\n              justifyContent: 'flex-start',\n              alignItems: 'center',\n              width: '100%',\n              padding: '5px'\n            }\n            if (\n              control.className &&\n              control.dataset &&\n              control.className.indexOf('entry') !== -1\n            ) {\n              const controlProps = new BpmData().getControl(\n                control.dataset.action\n              )\n              control.innerHTML = `<div style='font-size: 14px;font-weight:500;margin-left:15px;'>${\n                controlProps['title']\n              }</div>`\n              for (var csKey in controlStyle) {\n                control.style[csKey] = controlStyle[csKey]\n              }\n            }\n          }\n        }\n      } catch (e) {\n        console.log(e)\n      }\n    },\n    fillColor() {\n      const canvas = this.modeler.get('canvas')\n      this.modeler._definitions.rootElements[0].flowElements.forEach(n => {\n        const completeTask = this.taskList.find(m => m.key === n.id)\n        const todoTask = this.taskList.find(m => !m.completed)\n        const endTask = this.taskList[this.taskList.length - 1]\n        if (n.$type === 'bpmn:UserTask') {\n          if (completeTask) {\n            canvas.addMarker(n.id, completeTask.completed ? 'highlight' : 'highlight-todo')\n            n.outgoing?.forEach(nn => {\n              const targetTask = this.taskList.find(m => m.key === nn.targetRef.id)\n              if (targetTask) {\n                if (todoTask && completeTask.key === todoTask.key && !todoTask.completed){\n                  canvas.addMarker(nn.id, todoTask.completed ? 'highlight' : 'highlight-todo')\n                  canvas.addMarker(nn.targetRef.id, todoTask.completed ? 'highlight' : 'highlight-todo')\n                }else {\n                  canvas.addMarker(nn.id, targetTask.completed ? 'highlight' : 'highlight-todo')\n                  canvas.addMarker(nn.targetRef.id, targetTask.completed ? 'highlight' : 'highlight-todo')\n                }\n              }\n            })\n          }\n        }\n        // 排他网关\n        else if (n.$type === 'bpmn:ExclusiveGateway') {\n          if (completeTask) {\n            canvas.addMarker(n.id, completeTask.completed ? 'highlight' : 'highlight-todo')\n            n.outgoing?.forEach(nn => {\n              const targetTask = this.taskList.find(m => m.key === nn.targetRef.id)\n              if (targetTask) {\n\n                canvas.addMarker(nn.id, targetTask.completed ? 'highlight' : 'highlight-todo')\n                canvas.addMarker(nn.targetRef.id, targetTask.completed ? 'highlight' : 'highlight-todo')\n              }\n\n            })\n          }\n\n        }\n        // 并行网关\n        else if (n.$type === 'bpmn:ParallelGateway') {\n          if (completeTask) {\n            canvas.addMarker(n.id, completeTask.completed ? 'highlight' : 'highlight-todo')\n            n.outgoing?.forEach(nn => {\n              const targetTask = this.taskList.find(m => m.key === nn.targetRef.id)\n              if (targetTask) {\n                canvas.addMarker(nn.id, targetTask.completed ? 'highlight' : 'highlight-todo')\n                canvas.addMarker(nn.targetRef.id, targetTask.completed ? 'highlight' : 'highlight-todo')\n              }\n            })\n          }\n        }\n        else if (n.$type === 'bpmn:StartEvent') {\n          n.outgoing.forEach(nn => {\n            const completeTask = this.taskList.find(m => m.key === nn.targetRef.id)\n            if (completeTask) {\n              canvas.addMarker(nn.id, 'highlight')\n              canvas.addMarker(n.id, 'highlight')\n              return\n            }\n          })\n        }\n        else if (n.$type === 'bpmn:EndEvent') {\n          if (endTask.key === n.id && endTask.completed) {\n            canvas.addMarker(n.id, 'highlight')\n            return\n          }\n        }\n      })\n    },\n    // 对外 api\n    getProcess() {\n      const element = this.getProcessElement()\n      return {\n        id: element.id,\n        name: element.name,\n        category: element.$attrs['flowable:processCategory']\n      }\n    },\n    getProcessElement() {\n      const rootElements = this.modeler.getDefinitions().rootElements\n      for (let i = 0; i < rootElements.length; i++) {\n        if (rootElements[i].$type === 'bpmn:Process') return rootElements[i]\n      }\n    },\n    async saveXML(download = false) {\n      try {\n        const { xml } = await this.modeler.saveXML({ format: true })\n        if (download) {\n          this.downloadFile(`${this.getProcessElement().name}.bpmn20.xml`, xml, 'application/xml')\n        }\n        return xml\n      } catch (err) {\n        console.log(err)\n      }\n    },\n    async showXML() {\n      try {\n        const { xml } = await this.modeler.saveXML({ format: true })\n        this.$emit('showXML',xml)\n      } catch (err) {\n        console.log(err)\n      }\n    },\n    async saveImg(type = 'svg', download = false) {\n      try {\n        const { svg } = await this.modeler.saveSVG({ format: true })\n        if (download) {\n          this.downloadFile(this.getProcessElement().name, svg, 'image/svg+xml')\n        }\n        return svg\n      } catch (err) {\n        console.log(err)\n      }\n    },\n    async save() {\n      const process = this.getProcess()\n      const xml = await this.saveXML()\n      const svg = await this.saveImg()\n      const result = { process, xml, svg }\n      this.$emit('save', result)\n      window.parent.postMessage(result, '*')\n    },\n    openBpmn(file) {\n      const reader = new FileReader()\n      reader.readAsText(file, 'utf-8')\n      reader.onload = () => {\n        this.createNewDiagram(reader.result)\n      }\n      return false\n    },\n    downloadFile(filename, data, type) {\n      var a = document.createElement('a')\n      var url = window.URL.createObjectURL(new Blob([data], { type: type }))\n      a.href = url\n      a.download = filename\n      a.click()\n      window.URL.revokeObjectURL(url)\n    },\n    /** 获取数据类型 */\n    dataType(data){\n      this.$emit('dataType', data)\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\">\n/*左边工具栏以及编辑节点的样式*/\n@import \"~bpmn-js/dist/assets/diagram-js.css\";\n@import \"~bpmn-js/dist/assets/bpmn-font/css/bpmn.css\";\n@import \"~bpmn-js/dist/assets/bpmn-font/css/bpmn-codes.css\";\n@import \"~bpmn-js/dist/assets/bpmn-font/css/bpmn-embedded.css\";\n.view-mode {\n  .el-header, .el-aside, .djs-palette, .bjs-powered-by {\n    display: none;\n  }\n  .el-loading-mask {\n    background-color: initial;\n  }\n  .el-loading-spinner {\n    display: none;\n  }\n}\n.flow-containers {\n  // background-color: #ffffff;\n  width: 100%;\n  height: 100%;\n  .canvas {\n    width: 100%;\n    height: 100%;\n  }\n  .panel {\n    position: absolute;\n    right: 0;\n    top: 50px;\n    width: 300px;\n  }\n  .load {\n    margin-right: 10px;\n  }\n  .el-form-item__label{\n    font-size: 13px;\n  }\n\n  .djs-palette{\n    left: 0px!important;\n    top: 0px;\n    border-top: none;\n  }\n\n  .djs-container svg {\n    min-height: 650px;\n  }\n\n   .highlight.djs-shape .djs-visual > :nth-child(1) {\n     fill: green !important;\n     stroke: green !important;\n     fill-opacity: 0.2 !important;\n   }\n   .highlight.djs-shape .djs-visual > :nth-child(2) {\n     fill: green !important;\n   }\n   .highlight.djs-shape .djs-visual > path {\n     fill: green !important;\n     fill-opacity: 0.2 !important;\n     stroke: green !important;\n   }\n   .highlight.djs-connection > .djs-visual > path {\n     stroke: green !important;\n   }\n   // .djs-connection > .djs-visual > path {\n   //   stroke: orange !important;\n   //   stroke-dasharray: 4px !important;\n   //   fill-opacity: 0.2 !important;\n   // }\n   // .djs-shape .djs-visual > :nth-child(1) {\n   //   fill: orange !important;\n   //   stroke: orange !important;\n   //   stroke-dasharray: 4px !important;\n   //   fill-opacity: 0.2 !important;\n   // }\n   .highlight-todo.djs-connection > .djs-visual > path {\n     stroke: orange !important;\n     stroke-dasharray: 4px !important;\n     fill-opacity: 0.2 !important;\n   }\n   .highlight-todo.djs-shape .djs-visual > :nth-child(1) {\n     fill: orange !important;\n     stroke: orange !important;\n     stroke-dasharray: 4px !important;\n     fill-opacity: 0.2 !important;\n   }\n   .overlays-div {\n     font-size: 10px;\n     color: red;\n     width: 100px;\n     top: -20px !important;\n   }\n}\n@media screen and (max-width: 600px) {\n  .flow-containers .djs-container svg {\n      min-height: 350px;\n  }\n}\n</style>\n"]}]}