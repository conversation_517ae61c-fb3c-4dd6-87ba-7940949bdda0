{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/project/report/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/project/report/index.vue", "mtime": 1752653921015}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7CiAgbGlzdFJlcG9ydCwKICBnZXRSZXBvcnQsCiAgZGVsUmVwb3J0LAogIGFkZFJlcG9ydCwKICB1cGRhdGVSZXBvcnQsCiAgZXhwb3J0UmVwb3J0LAogIGltcG9ydFRlbXBsYXRlLAogIHByaW50UmVwb3J0LAogIGNoZWNrTmFtZVVuaXF1ZSwKICBnZXRMaWtlTGlzdCwKICBhdXRoUmVwb3J0LAp9IGZyb20gIkAvYXBpL3Byb2plY3QvcmVwb3J0IjsKaW1wb3J0IHsgZ2V0VG9rZW4gfSBmcm9tICJAL3V0aWxzL2F1dGgiOwppbXBvcnQgRmlsZVVwbG9hZCBmcm9tICJAL2NvbXBvbmVudHMvRmlsZVVwbG9hZCI7CmltcG9ydCBmbG93YWJsZSBmcm9tICJAL3ZpZXdzL2Zsb3dhYmxlL3Rhc2svcmVjb3JkL3ZpZXciOwppbXBvcnQgeyBnZXRJbnNJZEJ5Qml6S2V5IH0gZnJvbSAiQC9hcGkvZmxvd2FibGUvdG9kbyI7CmltcG9ydCB7IHJlZ2lvbkRhdGEsIENvZGVUb1RleHQsIFRleHRUb0NvZGUgfSBmcm9tICJlbGVtZW50LWNoaW5hLWFyZWEtZGF0YSI7CmltcG9ydCBwcmludCBmcm9tICJwcmludC1qcyI7CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiUmVwb3J0IiwKICBjb21wb25lbnRzOiB7CiAgICBGaWxlVXBsb2FkLAogICAgcHJpbnQsCiAgICBmbG93YWJsZSwKICB9LAogIGRhdGEoKSB7CiAgICB2YXIgaW5mb1R5cGVWYWx1ZVZhbGkgPSAocnVsZSwgdmFsdWUsIGNhbGxiYWNrKSA9PiB7CiAgICAgIGlmICh0aGlzLmZvcm0uaW5mb1R5cGUuaW5kZXhPZigiMSIpID49IDAgJiYgIXRoaXMuZm9ybS5zY2FuRmlsZSkgewogICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcigi6YKu566x5Zyw5Z2A5b+F5aGrIikpOwogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICBjYWxsYmFjaygpOwogICAgfTsKICAgIHZhciBpbmZvVHlwZVZhbHVlVmFsaTIgPSAocnVsZSwgdmFsdWUsIGNhbGxiYWNrKSA9PiB7CiAgICAgIGlmICh0aGlzLmZvcm0uaW5mb1R5cGUuaW5kZXhPZigiMiIpID49IDAgJiYgIXRoaXMuZm9ybS5zZW5kQWRkcmVzcykgewogICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcigi5pS25Lu25Zyw5Z2A5b+F5aGrIikpOwogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICBjYWxsYmFjaygpOwogICAgfTsKICAgIHZhciBuYW1lVmFsaSA9IChydWxlLCB2YWx1ZSwgY2FsbGJhY2spID0+IHsKICAgICAgaWYgKCF0aGlzLmZvcm0ucHJvamVjdE5hbWUpIHsKICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoIumhueebruWQjeensOW/heWhqyIpKTsKICAgICAgfSBlbHNlIHsKICAgICAgICBpZiAoL1xzKy9nLnRlc3QodGhpcy5mb3JtLnByb2plY3ROYW1lKSkgewogICAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCLpobnnm67lkI3np7DkuI3op4TojIMiKSk7CiAgICAgICAgICByZXR1cm47CiAgICAgICAgfQogICAgICAgIGNoZWNrTmFtZVVuaXF1ZSh7CiAgICAgICAgICBwcm9qZWN0TmFtZTogdGhpcy5mb3JtLnByb2plY3ROYW1lLAogICAgICAgICAgcHJvamVjdElkOiB0aGlzLmZvcm0ucHJvamVjdElkLAogICAgICAgIH0pLnRoZW4oKHJlc3BvbnNlKSA9PiB7CiAgICAgICAgICBpZiAocmVzcG9uc2UuZGF0YSA9PSAwKSB7CiAgICAgICAgICAgIGNhbGxiYWNrKCk7CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoIumhueebruWQjeensOW3suWtmOWcqCIpKTsKICAgICAgICAgIH0KICAgICAgICB9KTsKICAgICAgfQogICAgfTsKICAgIHZhciBjb2RlVmFsaSA9IChydWxlLCB2YWx1ZSwgY2FsbGJhY2spID0+IHsKICAgICAgaWYgKCF0aGF0LmZvcm0ucHJvamVjdE5vKSB7CiAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCLpobnnm67nvJblj7flv4XloasiKSk7CiAgICAgIH0gZWxzZSBpZiAoL1xzKy9nLnRlc3QodGhhdC5mb3JtLnByb2plY3RObykpIHsKICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoIumhueebrue8luWPt+S4jeinhOiMgyIpKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgY2FsbGJhY2soKTsKICAgIH07CiAgICB2YXIgYXV0aEZpbGVWYWx1ZVZhbGkgPSAocnVsZSwgdmFsdWUsIGNhbGxiYWNrKSA9PiB7CiAgICAgIGlmICh0aGlzLmZvcm0ub3BlcmF0aW9uVHlwZSA9PSAyICYmICF0aGlzLmZvcm0uYXV0aEZpbGUpIHsKICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoIuaOiOadg+exu+Wei+W/heS8oOaOiOadg+S5piIpKTsKICAgICAgfQogICAgICBjYWxsYmFjaygpOwogICAgfTsKICAgIHJldHVybiB7CiAgICAgIGlzTW9iaWxlOiBmYWxzZSwKICAgICAgcGFnZUxheW91dDogInRvdGFsLCBzaXplcywgcHJldiwgcGFnZXIsIG5leHQsIGp1bXBlciIsCiAgICAgIC8vIOmBrue9qeWxggogICAgICBsb2FkaW5nOiB0cnVlLAogICAgICAvLyDpgInkuK3mlbDnu4QKICAgICAgaWRzOiBbXSwKICAgICAgLy8g6Z2e5Y2V5Liq56aB55SoCiAgICAgIHNpbmdsZTogdHJ1ZSwKICAgICAgLy8g6Z2e5aSa5Liq56aB55SoCiAgICAgIG11bHRpcGxlOiB0cnVlLAogICAgICAvLyDmmL7npLrmkJzntKLmnaHku7YKICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwKICAgICAgc2hvd0V4cG9ydDogZmFsc2UsCiAgICAgIHNob3dQcmludDogZmFsc2UsCiAgICAgIC8vIOaAu+adoeaVsAogICAgICB0b3RhbDogMCwKICAgICAgLy8g6aG555uu5oql5aSH6KGo5qC85pWw5o2uCiAgICAgIHJlcG9ydExpc3Q6IFtdLAogICAgICAvLyDlvLnlh7rlsYLmoIfpopgKICAgICAgdGl0bGU6ICIiLAogICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYIKICAgICAgb3BlbjogZmFsc2UsCiAgICAgIC8vIOaTjeS9nOexu+Wei+Wtl+WFuAogICAgICBvcGVyYXRpb25UeXBlT3B0aW9uczogW10sCiAgICAgIC8vIOWuoeaguOeKtuaAgeWtl+WFuAogICAgICBhdWRpdFN0YXR1c09wdGlvbnM6IFtdLAogICAgICAvLyDnvJbovpHnirbmgIHlrZflhbgKICAgICAgZWRpdFN0YXR1c09wdGlvbnM6IFtdLAogICAgICAvLyDmi5vmoIfmlrnlvI/lrZflhbgKICAgICAgYmlkZGluZ1R5cGVPcHRpb25zOiBbXSwKICAgICAgLy8g5oqV5qCH5Lqn5ZOB5Z6L5Y+35a2X5YW4CiAgICAgIG1vZGVsT3B0aW9uczogW10sCiAgICAgIG1vZGVsT3B0aW9uMTogW10sCiAgICAgIC8vIOaJgOmcgOi1hOaWmeWtl+WFuAogICAgICByZXF1aXJlSW5mb09wdGlvbnM6IFtdLAogICAgICByZXF1aXJlSW5mb09wdGlvbjE6IFtdLAogICAgICAvLyDotYTmlpnnsbvlnovlrZflhbgKICAgICAgaW5mb1R5cGVPcHRpb25zOiBbXSwKICAgICAgc3BlY09wdGlvbnM6IFtdLAogICAgICBzcGVjT3B0aW9uMTogW10sCiAgICAgIC8vIOaJgOWxnuecgeS7veWtl+WFuAogICAgICBiZWxvbmdQcm92aW5jZU9wdGlvbnM6IFtdLAogICAgICBiZWxvbmdQcm92aW5jZU9wdGlvbnMxOiBbXSwKICAgICAgLy8g5ZSu5ZCO5bm06ZmQCiAgICAgIGFmdGVyU2FsZVllYXJPcHRpb25zOiBbXSwKICAgICAgYWZ0ZXJTYWxlWWVhck9wdGlvbnMxOiBbXSwKICAgICAgLy8g6aG555uu5a+85YWl5Y+C5pWwCiAgICAgIHVwbG9hZDogewogICAgICAgIC8vIOaYr+WQpuaYvuekuuW8ueWHuuWxgu+8iOeUqOaIt+WvvOWFpe+8iQogICAgICAgIG9wZW46IGZhbHNlLAogICAgICAgIC8vIOW8ueWHuuWxguagh+mimO+8iOeUqOaIt+WvvOWFpe+8iQogICAgICAgIHRpdGxlOiAiIiwKICAgICAgICAvLyDmmK/lkKbnpoHnlKjkuIrkvKAKICAgICAgICBpc1VwbG9hZGluZzogZmFsc2UsCiAgICAgICAgLy8g5piv5ZCm5pu05paw5bey57uP5a2Y5Zyo55qE55So5oi35pWw5o2uCiAgICAgICAgdXBkYXRlU3VwcG9ydDogMCwKICAgICAgICAvLyDorr7nva7kuIrkvKDnmoTor7fmsYLlpLTpg6gKICAgICAgICBoZWFkZXJzOiB7IEF1dGhvcml6YXRpb246ICJCZWFyZXIgIiArIGdldFRva2VuKCkgfSwKICAgICAgICAvLyDkuIrkvKDnmoTlnLDlnYAKICAgICAgICB1cmw6IHByb2Nlc3MuZW52LlZVRV9BUFBfQkFTRV9BUEkgKyAiL3Byb2plY3QvcmVwb3J0L2ltcG9ydERhdGEiLAogICAgICB9LAogICAgICAvLyDmn6Xor6Llj4LmlbAKICAgICAgcXVlcnlQYXJhbXM6IHsKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICBwcm9qZWN0Tm86IG51bGwsCiAgICAgICAgcHJvamVjdE5hbWU6IG51bGwsCiAgICAgICAgb3BlcmF0aW9uVHlwZTogbnVsbCwKICAgICAgICBhdWRpdFN0YXR1czogbnVsbCwKICAgICAgICBwcm92aW5jZTogbnVsbCwKICAgICAgICB1c2VyVHlwZTogbnVsbCwKICAgICAgICBiZWxvbmdVc2VyOiBudWxsLAogICAgICAgIHVwZGF0ZVRpbWVBcnI6IFtdLAogICAgICAgIHNwYXJlMTogbnVsbCwKICAgICAgICBhZGRyZXNzOiBudWxsLAogICAgICAgIGJpZGRpbmdDb21wYW55OiBudWxsLAogICAgICAgIGF1dGhDb21wYW55OiBudWxsLAogICAgICAgIGZ1bGxGaWVsZDogbnVsbAogICAgICB9LAogICAgICAvLyDooajljZXlj4LmlbAKICAgICAgZm9ybToge30sCiAgICAgIC8vIOihqOWNleagoemqjAogICAgICAvLyDooajljZXmoKHpqowKICAgICAgcnVsZXM6IHsKICAgICAgICBvcGVyYXRpb25UeXBlOiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuaTjeS9nOexu+Wei+W/hemAiSIgfV0sCiAgICAgICAgcHJvamVjdE5vOiBbeyByZXF1aXJlZDogdHJ1ZSwgdmFsaWRhdGU6IGNvZGVWYWxpLCB0cmlnZ2VyOiAiYmx1ciIgfV0sCiAgICAgICAgcHJvamVjdE5hbWU6IFt7IHJlcXVpcmVkOiB0cnVlLCB2YWxpZGF0ZTogbmFtZVZhbGksIHRyaWdnZXI6ICJibHVyIiB9XSwKICAgICAgICBhZGRyZXNzOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+m57uG5Zyw5Z2A5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0sCiAgICAgICAgXSwKICAgICAgICBiaWRkaW5nQ29tcGFueTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuaLm+agh+WNleS9jeS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9LAogICAgICAgIF0sCiAgICAgICAgb3BlbkRhdGU6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLlvIDmoIfml6XmnJ/kuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwKICAgICAgICBdLAogICAgICAgIGFmdGVyU2FsZVllYXI6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLllK7lkI7lubTpmZDkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwKICAgICAgICBdLAogICAgICAgIGhhbmdEYXRlOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5oyC572R5pel5pyf5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0sCiAgICAgICAgXSwKICAgICAgICBiZWxvbmdQcm92aW5jZTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuWUruWQjuW5tOmZkOS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9LAogICAgICAgIF0sCiAgICAgICAgZGlzdHJpYnV0b3I6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLmiYDlsZ7nu4/plIDllYbkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwKICAgICAgICBdLAogICAgICAgIHNjYW5GaWxlOiBbeyB2YWxpZGF0b3I6IGluZm9UeXBlVmFsdWVWYWxpLCB0cmlnZ2VyOiAiYmx1ciIgfV0sCiAgICAgICAgc2VuZEFkZHJlc3M6IFt7IHZhbGlkYXRvcjogaW5mb1R5cGVWYWx1ZVZhbGkyLCB0cmlnZ2VyOiAiYmx1ciIgfV0sCiAgICAgICAgbW9kZWw6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5oqV5qCH5Lqn5ZOB5Z6L5Y+35b+F6YCJIiB9XSwKICAgICAgICBzcGVjOiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuaKleagh+S6p+WTgeinhOagvOW/hemAiSIgfV0sCiAgICAgICAgcHJvdmluY2U6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6aG555uu5omA5Zyo5Zyw5b+F6YCJIiB9XSwKICAgICAgICBpbmZvVHlwZTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIui1hOaWmeaOpeaUtuaWueW8j+W/hemAiSIsIHRyaWdnZXI6ICJjaGFuZ2UiIH0sCiAgICAgICAgXSwKICAgICAgICBhdXRoRmlsZTogW3sgdmFsaWRhdG9yOiBhdXRoRmlsZVZhbHVlVmFsaSwgdHJpZ2dlcjogImNoYW5nZSIgfV0sCiAgICAgICAgYmlkZGluZ0NvbnRhY3Q6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLmi5vmoIfljZXkvY3ogZTns7vkurov6IGU57O755S16K+d5b+F5aGrIiB9LAogICAgICAgIF0sCiAgICAgICAgYXV0aENvbnRhY3Q6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLmjojmnYPlhazlj7jogZTns7vkurov6IGU57O755S16K+d5b+F5aGrIiB9LAogICAgICAgIF0sCiAgICAgIH0sCiAgICAgIC8vIOWIl+S/oeaBrwogICAgICBjb2x1bW5zOiBbCiAgICAgICAgeyBrZXk6ICJiZWxvbmdVc2VyIiwgaW5kZXg6IDEsIGxhYmVsOiBg5omA5bGe55So5oi3YCwgdmlzaWJsZTogdHJ1ZSB9LAogICAgICAgIHsga2V5OiAicHJvamVjdE5vIiwgaW5kZXg6IDIsIGxhYmVsOiBg6aG555uu57yW5Y+3YCwgdmlzaWJsZTogdHJ1ZSB9LAogICAgICAgIHsga2V5OiAicHJvamVjdE5hbWUiLCBpbmRleDogMywgbGFiZWw6IGDpobnnm67lkI3np7BgLCB2aXNpYmxlOiB0cnVlIH0sCiAgICAgICAgeyBrZXk6ICJvcGVyYXRpb25UeXBlIiwgaW5kZXg6IDQsIGxhYmVsOiBg5pON5L2c57G75Z6LYCwgdmlzaWJsZTogdHJ1ZSB9LAogICAgICAgIHsga2V5OiAicHJvdmluY2UiLCBpbmRleDogNSwgbGFiZWw6IGDpobnnm67miYDlnKjlnLBgLCB2aXNpYmxlOiB0cnVlIH0sCiAgICAgICAgeyBrZXk6ICJhZGRyZXNzIiwgaW5kZXg6IDYsIGxhYmVsOiBg6K+m57uG5Zyw5Z2AYCwgdmlzaWJsZTogdHJ1ZSB9LAogICAgICAgIHsga2V5OiAiYXV0aENvbXBhbnkiLCBpbmRleDogNywgbGFiZWw6IGDooqvmjojmnYPlhazlj7hgLCB2aXNpYmxlOiB0cnVlIH0sCiAgICAgICAgeyBrZXk6ICJkaXN0cmlidXRvciIsIGluZGV4OiA4LCBsYWJlbDogYOaJgOWxnue7j+mUgOWVhmAsIHZpc2libGU6IHRydWUgfSwKICAgICAgICB7IGtleTogImJpZGRpbmdDb21wYW55IiwgaW5kZXg6IDksIGxhYmVsOiBg5oub5qCH5Y2V5L2NYCwgdmlzaWJsZTogdHJ1ZSB9LAogICAgICAgIHsga2V5OiAibW9kZWwiLCBpbmRleDogMTAsIGxhYmVsOiBg5oqV5qCH5Lqn5ZOB5Z6L5Y+3YCwgdmlzaWJsZTogdHJ1ZSB9LAogICAgICAgIHsga2V5OiAic3BlYyIsIGluZGV4OiAxMSwgbGFiZWw6IGDmipXmoIfkuqflk4Hop4TmoLxgLCB2aXNpYmxlOiB0cnVlIH0sCiAgICAgICAgeyBrZXk6ICJhcmVhIiwgaW5kZXg6IDEyLCBsYWJlbDogYOWuieijhemdouenr2AsIHZpc2libGU6IHRydWUgfSwKICAgICAgICB7IGtleTogInJlcXVpcmVJbmZvIiwgaW5kZXg6IDEzLCBsYWJlbDogYOaJgOmcgOi1hOaWmWAsIHZpc2libGU6IHRydWUgfSwKICAgICAgICB7IGtleTogImluZm9UeXBlIiwgaW5kZXg6IDE0LCBsYWJlbDogYOi1hOaWmeexu+Wei2AsIHZpc2libGU6IHRydWUgfSwKICAgICAgICB7IGtleTogInNjYW5GaWxlIiwgaW5kZXg6IDE1LCBsYWJlbDogYOi1hOaWmeaOpeaUtumCruS7tmAsIHZpc2libGU6IHRydWUgfSwKICAgICAgICB7IGtleTogInNjYW5GaWxlIiwgaW5kZXg6IDE2LCBsYWJlbDogYOi1hOaWmeaOpeaUtuWcsOWdgGAsIHZpc2libGU6IHRydWUgfSwKICAgICAgICB7IGtleTogImJlbG9uZ1Byb3ZpbmNlIiwgaW5kZXg6IDE3LCBsYWJlbDogYOmhueebruaJgOWxnuecgeS7vWAsIHZpc2libGU6IHRydWUgfSwKICAgICAgICB7IGtleTogImFmdGVyU2FsZVllYXIiLCBpbmRleDogMTgsIGxhYmVsOiBg5ZSu5ZCO5bm06ZmQYCwgdmlzaWJsZTogdHJ1ZSB9LAogICAgICAgIHsga2V5OiAib3BlbkRhdGUiLCBpbmRleDogMTksIGxhYmVsOiBg5byA5qCH5pel5pyfYCwgdmlzaWJsZTogdHJ1ZSB9LAogICAgICAgIHsga2V5OiAiaGFuZ0RhdGUiLCBpbmRleDogMjAsIGxhYmVsOiBg5oyC572R5pel5pyfYCwgdmlzaWJsZTogdHJ1ZSB9LAogICAgICAgIHsga2V5OiAiY3JlYXRlVGltZSIsIGluZGV4OiAyMSwgbGFiZWw6IGDmj5DkuqTml7bpl7RgLCB2aXNpYmxlOiB0cnVlIH0sCiAgICAgICAgeyBrZXk6ICJ1cGRhdGVUaW1lIiwgaW5kZXg6IDIyLCBsYWJlbDogYOS/ruaUueaXtumXtGAsIHZpc2libGU6IHRydWUgfSwKICAgICAgICAvLyB7IGtleTogImF1ZGl0U3RhdHVzIiwgaW5kZXg6IDE5LCBsYWJlbDogYOWuoeaguOeKtuaAgWAsIHZpc2libGU6IGZhbHNlIH0sCiAgICAgICAgLy8geyBrZXk6ICJlZGl0U3RhdHVzIiwgaW5kZXg6IDIwLCBsYWJlbDogYOe8lui+keeKtuaAgWAsIHZpc2libGU6IGZhbHNlIH0sCiAgICAgICAgLy8geyBrZXk6ICIxMSIsIGluZGV4OiAyMSwgbGFiZWw6IGDmjojmnYPkuaZgLCB2aXNpYmxlOiBmYWxzZSB9LAogICAgICAgIC8veyBrZXk6ICIxMiIsIGluZGV4OiAyMywgbGFiZWw6IGDllK7lkI7mnI3liqHmib/or7rlh71gLCB2aXNpYmxlOiBmYWxzZSB9LAogICAgICBdLAogICAgICBvcHRpb25zOiByZWdpb25EYXRhLAogICAgICBzZWxlY3RlZE9wdGlvbnM6IFtdLAogICAgICBxdWVyeUFyZWE6IFtdLAogICAgICB2aWV3T3BlbjogZmFsc2UsCiAgICAgIHZpZXc6IHt9LAogICAgICBpbmZvTGlzdDE6IFtdLAogICAgICBpbmZvTGlzdDI6IFtdLAogICAgICBkZWZLZXk6ICJwcm9jZXNzX3Byb2plY3RfcmVwb3J0IiwKICAgICAgcHJvY0luc0lkOiB1bmRlZmluZWQsCiAgICAgIHRhc2tJZDogdW5kZWZpbmVkLAogICAgICBmaW5pc2hlZDogdHJ1ZSwKICAgICAgYml6S2V5OiB1bmRlZmluZWQsCiAgICAgIGF1ZGl0U3RhdHVzVHJlZTogW10sCiAgICAgIG9wZXJhdGlvblR5cGVUcmVlOiBbXSwKICAgICAgdXNlclR5cGVUcmVlOiBbXSwKICAgICAgZGVmYXVsdFByb3BzOiB7CiAgICAgICAgY2hpbGRyZW46ICJjaGlsZHJlbiIsCiAgICAgICAgbGFiZWw6ICJsYWJlbCIsCiAgICAgIH0sCiAgICAgIG9sZE9wZXJhdGlvblR5cGU6IHVuZGVmaW5lZCwKICAgICAgc2hvd1VUeXBlOiB0cnVlLAogICAgICBjaG9vc2VPcHRUeXBlOiB1bmRlZmluZWQsCiAgICAgIGNob29zZUF1ZGl0U3RhdHVzOiB1bmRlZmluZWQsCiAgICAgIGNob29zZVVzZXJJZDogdW5kZWZpbmVkLAogICAgICBjaG9vc2VFZGl0U3RhdHVzOiB1bmRlZmluZWQsCiAgICAgIGNob29zZVNwYXJlMjogdW5kZWZpbmVkLAogICAgICBsaWtlTGlzdDogdW5kZWZpbmVkLAogICAgICBsaWtlQ291bnQ6IHVuZGVmaW5lZCwKICAgICAgYXV0aENvbXBhbnlzOiBbXSwKICAgICAgaXNBZG1pbjogdHJ1ZSwKICAgICAgYXVkaXRTdGF0dXNFZGl0OiB0cnVlLAogICAgICBpc0F1dGhJbWFnZXM6IGZhbHNlLAogICAgICBzZWFyY2hQaWNrZXJPcHRpb25zOiB7CiAgICAgICAgc2hvcnRjdXRzOiBbewogICAgICAgICAgdGV4dDogJ+acgOi/keS4gOWRqCcsCiAgICAgICAgICBvbkNsaWNrKHBpY2tlcikgewogICAgICAgICAgICBjb25zdCBlbmQgPSBuZXcgRGF0ZSgpOwogICAgICAgICAgICBjb25zdCBzdGFydCA9IG5ldyBEYXRlKCk7CiAgICAgICAgICAgIHN0YXJ0LnNldFRpbWUoc3RhcnQuZ2V0VGltZSgpIC0gMzYwMCAqIDEwMDAgKiAyNCAqIDcpOwogICAgICAgICAgICBwaWNrZXIuJGVtaXQoJ3BpY2snLCBbc3RhcnQsIGVuZF0pOwogICAgICAgICAgfQogICAgICAgIH0sIHsKICAgICAgICAgIHRleHQ6ICfmnIDov5HkuIDkuKrmnIgnLAogICAgICAgICAgb25DbGljayhwaWNrZXIpIHsKICAgICAgICAgICAgY29uc3QgZW5kID0gbmV3IERhdGUoKTsKICAgICAgICAgICAgY29uc3Qgc3RhcnQgPSBuZXcgRGF0ZSgpOwogICAgICAgICAgICBzdGFydC5zZXRUaW1lKHN0YXJ0LmdldFRpbWUoKSAtIDM2MDAgKiAxMDAwICogMjQgKiAzMCk7CiAgICAgICAgICAgIHBpY2tlci4kZW1pdCgncGljaycsIFtzdGFydCwgZW5kXSk7CiAgICAgICAgICB9CiAgICAgICAgfSwgewogICAgICAgICAgdGV4dDogJ+acgOi/keS4ieS4quaciCcsCiAgICAgICAgICBvbkNsaWNrKHBpY2tlcikgewogICAgICAgICAgICBjb25zdCBlbmQgPSBuZXcgRGF0ZSgpOwogICAgICAgICAgICBjb25zdCBzdGFydCA9IG5ldyBEYXRlKCk7CiAgICAgICAgICAgIHN0YXJ0LnNldFRpbWUoc3RhcnQuZ2V0VGltZSgpIC0gMzYwMCAqIDEwMDAgKiAyNCAqIDkwKTsKICAgICAgICAgICAgcGlja2VyLiRlbWl0KCdwaWNrJywgW3N0YXJ0LCBlbmRdKTsKICAgICAgICAgIH0KICAgICAgICB9XQogICAgICB9LAogICAgICBwaWNrZXJPcHRpb25zOiB7CiAgICAgICAgZGlzYWJsZWREYXRlKHRpbWUpIHsKICAgICAgICAgIHJldHVybiB0aW1lLmdldFRpbWUoKSA+IERhdGUubm93KCk7CiAgICAgICAgfSwKICAgICAgICBzaG9ydGN1dHM6IFsKICAgICAgICAgIHsKICAgICAgICAgICAgdGV4dDogIuS7iuWkqSIsCiAgICAgICAgICAgIG9uQ2xpY2socGlja2VyKSB7CiAgICAgICAgICAgICAgcGlja2VyLiRlbWl0KCJwaWNrIiwgbmV3IERhdGUoKSk7CiAgICAgICAgICAgIH0sCiAgICAgICAgICB9LAogICAgICAgICAgewogICAgICAgICAgICB0ZXh0OiAi5pio5aSpIiwKICAgICAgICAgICAgb25DbGljayhwaWNrZXIpIHsKICAgICAgICAgICAgICBjb25zdCBkYXRlID0gbmV3IERhdGUoKTsKICAgICAgICAgICAgICBkYXRlLnNldFRpbWUoZGF0ZS5nZXRUaW1lKCkgLSAzNjAwICogMTAwMCAqIDI0KTsKICAgICAgICAgICAgICBwaWNrZXIuJGVtaXQoInBpY2siLCBkYXRlKTsKICAgICAgICAgICAgfSwKICAgICAgICAgIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIHRleHQ6ICLkuIDlkajliY0iLAogICAgICAgICAgICBvbkNsaWNrKHBpY2tlcikgewogICAgICAgICAgICAgIGNvbnN0IGRhdGUgPSBuZXcgRGF0ZSgpOwogICAgICAgICAgICAgIGRhdGUuc2V0VGltZShkYXRlLmdldFRpbWUoKSAtIDM2MDAgKiAxMDAwICogMjQgKiA3KTsKICAgICAgICAgICAgICBwaWNrZXIuJGVtaXQoInBpY2siLCBkYXRlKTsKICAgICAgICAgICAgfSwKICAgICAgICAgIH0sCiAgICAgICAgXSwKICAgICAgfSwKICAgICAgc2VhcmNoRmllbGQ6ICdhbGwnLCAvLyDmlrDlop7vvJrlvZPliY3pgInkuK3nmoTmkJzntKLlrZfmrrXvvIzpu5jorqTlhajlrZfmrrUKICAgICAgc2VhcmNoRmllbGRPcHRpb25zOiBbCiAgICAgICAgeyBsYWJlbDogJ+WFqOWtl+autScsIHZhbHVlOiAnYWxsJyB9LAogICAgICAgIHsgbGFiZWw6ICfpobnnm67nvJblj7cnLCB2YWx1ZTogJ3Byb2plY3RObycgfSwKICAgICAgICB7IGxhYmVsOiAn6aG555uu5ZCN56ewJywgdmFsdWU6ICdwcm9qZWN0TmFtZScgfSwKICAgICAgICB7IGxhYmVsOiAn6K+m57uG5Zyw5Z2AJywgdmFsdWU6ICdhZGRyZXNzJyB9LAogICAgICAgIHsgbGFiZWw6ICfmi5vmoIfljZXkvY0nLCB2YWx1ZTogJ2JpZGRpbmdDb21wYW55JyB9LAogICAgICAgIHsgbGFiZWw6ICfmjojmnYPlhazlj7gnLCB2YWx1ZTogJ2F1dGhDb21wYW55JyB9CiAgICAgIF0sCiAgICAgIHNlYXJjaFZhbHVlOiAnJywgLy8g5paw5aKe77ya5pCc57Si5YaF5a65CiAgICAgIGhpZ2hsaWdodEZpZWxkczogWydwcm9qZWN0Tm8nLCAncHJvamVjdE5hbWUnLCAnYWRkcmVzcycsICdiaWRkaW5nQ29tcGFueScsICdhdXRoQ29tcGFueSddLCAvLyDmlrDlop7vvJrpq5jkuq7lrZfmrrUKICAgIH07CiAgfSwKICBhY3RpdmF0ZWQoKSB7CiAgICBjb25zb2xlLmxvZygiPXJlcG9ydCBpbmRleD09Pj5hY3RpdmF0ZWQiKTsKICAgIHRoaXMuZ2V0TGlzdCgpOwogIH0sCiAgY3JlYXRlZCgpIHsKICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgdGhpcy5nZXREaWN0cygicHJfb3BlcmF0aW9uX3R5cGUiKS50aGVuKChyZXNwb25zZSkgPT4gewogICAgICB0aGlzLm9wZXJhdGlvblR5cGVPcHRpb25zID0gcmVzcG9uc2UuZGF0YTsKICAgICAgdmFyIG9wdCA9IFtdOwogICAgICBvcHQucHVzaCh7IGlkOiAwLCBsYWJlbDogIuWFqOmDqCIgfSk7CiAgICAgIHJlc3BvbnNlLmRhdGEuZm9yRWFjaCgoZWxlbSwgaW5kZXgpID0+IHsKICAgICAgICB2YXIgb2JqID0ge307CiAgICAgICAgb2JqLmxhYmVsID0gZWxlbS5kaWN0TGFiZWw7CiAgICAgICAgb2JqLmlkID0gZWxlbS5kaWN0VmFsdWU7CiAgICAgICAgb3B0LnB1c2gob2JqKTsKICAgICAgfSk7CiAgICAgIHZhciBvcGVyYXRpb25UeXBlID0ge307CiAgICAgIG9wZXJhdGlvblR5cGUubGFiZWwgPSAi5pON5L2c57G75Z6LIjsKICAgICAgb3BlcmF0aW9uVHlwZS5jaGlsZHJlbiA9IG9wdDsKICAgICAgdmFyIG9wZXJhdGlvblR5cGVzID0gW107CiAgICAgIG9wZXJhdGlvblR5cGVzLnB1c2gob3BlcmF0aW9uVHlwZSk7CiAgICAgIHRoaXMub3BlcmF0aW9uVHlwZVRyZWUgPSBvcGVyYXRpb25UeXBlczsKICAgIH0pOwogICAgdGhpcy5nZXREaWN0cygicHJfYXVkaXRfc3RhdHVzIikudGhlbigocmVzcG9uc2UpID0+IHsKICAgICAgdmFyIHR5cGUgPSAwOwogICAgICBpZiAodGhpcy4kc3RvcmUuc3RhdGUudXNlci5yb2xlcykgewogICAgICAgIGlmICh0aGlzLiRzdG9yZS5zdGF0ZS51c2VyLnJvbGVzLmluY2x1ZGVzKCJjb21tb24iKSkgewogICAgICAgICAgdHlwZSA9IDE7CiAgICAgICAgfQogICAgICAgIGlmICh0aGlzLiRzdG9yZS5zdGF0ZS51c2VyLnJvbGVzLmluY2x1ZGVzKCJwcm92aW5jZV9hZG1pbiIpKSB7CiAgICAgICAgICB0eXBlID0gMjsKICAgICAgICB9CiAgICAgICAgaWYgKHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIucm9sZXMuaW5jbHVkZXMoInJlcG9ydF9hZG1pbiIpKSB7CiAgICAgICAgICB0eXBlID0gMzsKICAgICAgICB9CiAgICAgIH0KICAgICAgdGhpcy5hdWRpdFN0YXR1c09wdGlvbnMgPSByZXNwb25zZS5kYXRhOwogICAgICB2YXIgb3B0ID0gW107CiAgICAgIG9wdC5wdXNoKHsgaWQ6IDksIGxhYmVsOiAi5YWo6YOoIiB9KTsKICAgICAgaWYgKHR5cGUgPT0gMiB8fCB0eXBlID09IDMpIHsKICAgICAgICBvcHQucHVzaCh7IGlkOiAxMCwgbGFiZWw6ICLmnKrlrqHmibkiIH0pOwogICAgICB9CiAgICAgIHJlc3BvbnNlLmRhdGEuZm9yRWFjaCgoZWxlbSwgaW5kZXgpID0+IHsKICAgICAgICB2YXIgb2JqID0ge307CiAgICAgICAgb2JqLmxhYmVsID0gZWxlbS5kaWN0TGFiZWw7CiAgICAgICAgb2JqLmlkID0gZWxlbS5kaWN0VmFsdWU7CiAgICAgICAgb3B0LnB1c2gob2JqKTsKICAgICAgfSk7CiAgICAgIHZhciBhdWRpdFN0YXR1c1RyZWUgPSB7fTsKICAgICAgYXVkaXRTdGF0dXNUcmVlLmxhYmVsID0gIuWuoeaguOeKtuaAgSI7CiAgICAgIGF1ZGl0U3RhdHVzVHJlZS5jaGlsZHJlbiA9IG9wdDsKICAgICAgdmFyIGF1ZGl0U3RhdHVzVHJlZXMgPSBbXTsKICAgICAgYXVkaXRTdGF0dXNUcmVlcy5wdXNoKGF1ZGl0U3RhdHVzVHJlZSk7CiAgICAgIHRoaXMuYXVkaXRTdGF0dXNUcmVlID0gYXVkaXRTdGF0dXNUcmVlczsKICAgIH0pOwogICAgdGhpcy5nZXREaWN0cygicHJfZWRpdF9zdGF0dXMiKS50aGVuKChyZXNwb25zZSkgPT4gewogICAgICB0aGlzLmVkaXRTdGF0dXNPcHRpb25zID0gcmVzcG9uc2UuZGF0YTsKICAgIH0pOwogICAgLy8gdGhpcy5nZXREaWN0cygicHJfYmlkZGluZ190eXBlIikudGhlbigocmVzcG9uc2UpID0+IHsKICAgIC8vICAgdGhpcy5iaWRkaW5nVHlwZU9wdGlvbnMgPSByZXNwb25zZS5kYXRhOwogICAgLy8gfSk7CiAgICB0aGlzLmdldERpY3RzKCJwcl9tb2RlbCIpLnRoZW4oKHJlc3BvbnNlKSA9PiB7CiAgICAgIHRoaXMubW9kZWxPcHRpb24xID0gcmVzcG9uc2UuZGF0YTsKICAgICAgdmFyIG9wdCA9IFtdOwogICAgICByZXNwb25zZS5kYXRhLmZvckVhY2goKGVsZW0sIGluZGV4KSA9PiB7CiAgICAgICAgdmFyIG9iaiA9IHt9OwogICAgICAgIG9iai5sYWJlbCA9IGVsZW0uZGljdExhYmVsOwogICAgICAgIG9iai52YWx1ZSA9IGVsZW0uZGljdFZhbHVlOwogICAgICAgIG9wdC5wdXNoKG9iaik7CiAgICAgIH0pOwogICAgICB0aGlzLm1vZGVsT3B0aW9ucyA9IG9wdDsKICAgIH0pOwogICAgdGhpcy5nZXREaWN0cygicHJfc3BlYyIpLnRoZW4oKHJlc3BvbnNlKSA9PiB7CiAgICAgIHRoaXMuc3BlY09wdGlvbjEgPSByZXNwb25zZS5kYXRhOwogICAgICB2YXIgb3B0ID0gW107CiAgICAgIHJlc3BvbnNlLmRhdGEuZm9yRWFjaCgoZWxlbSwgaW5kZXgpID0+IHsKICAgICAgICB2YXIgb2JqID0ge307CiAgICAgICAgb2JqLmxhYmVsID0gZWxlbS5kaWN0TGFiZWw7CiAgICAgICAgb2JqLnZhbHVlID0gZWxlbS5kaWN0VmFsdWU7CiAgICAgICAgb3B0LnB1c2gob2JqKTsKICAgICAgfSk7CiAgICAgIHRoaXMuc3BlY09wdGlvbnMgPSBvcHQ7CiAgICB9KTsKICAgIHRoaXMuZ2V0RGljdHMoInByX2luZm8iKS50aGVuKChyZXNwb25zZSkgPT4gewogICAgICB0aGlzLnJlcXVpcmVJbmZvT3B0aW9uMSA9IHJlc3BvbnNlLmRhdGE7CiAgICAgIHZhciBvcHQgPSBbXTsKICAgICAgcmVzcG9uc2UuZGF0YS5mb3JFYWNoKChlbGVtLCBpbmRleCkgPT4gewogICAgICAgIHZhciBvYmogPSB7fTsKICAgICAgICBvYmoubGFiZWwgPSBlbGVtLmRpY3RMYWJlbDsKICAgICAgICBvYmoudmFsdWUgPSBlbGVtLmRpY3RWYWx1ZTsKICAgICAgICBvcHQucHVzaChvYmopOwogICAgICB9KTsKICAgICAgdGhpcy5yZXF1aXJlSW5mb09wdGlvbnMgPSBvcHQ7CiAgICB9KTsKICAgIHRoaXMuZ2V0RGljdHMoInByX3Byb3ZpbmNlIikudGhlbigocmVzcG9uc2UpID0+IHsKICAgICAgdGhpcy5iZWxvbmdQcm92aW5jZU9wdGlvbnMxID0gcmVzcG9uc2UuZGF0YTsKICAgICAgdmFyIG9wdCA9IFtdOwogICAgICByZXNwb25zZS5kYXRhLmZvckVhY2goKGVsZW0sIGluZGV4KSA9PiB7CiAgICAgICAgdmFyIG9iaiA9IHt9OwogICAgICAgIG9iai5sYWJlbCA9IGVsZW0uZGljdExhYmVsOwogICAgICAgIG9iai52YWx1ZSA9IGVsZW0uZGljdFZhbHVlOwogICAgICAgIG9wdC5wdXNoKG9iaik7CiAgICAgIH0pOwogICAgICB0aGlzLmJlbG9uZ1Byb3ZpbmNlT3B0aW9ucyA9IG9wdDsKICAgIH0pOwogICAgdGhpcy5nZXREaWN0cygicHJfYWZ0ZXJfc2FsZV95ZWFyIikudGhlbigocmVzcG9uc2UpID0+IHsKICAgICAgdGhpcy5hZnRlclNhbGVZZWFyT3B0aW9uczEgPSByZXNwb25zZS5kYXRhOwogICAgICB2YXIgb3B0ID0gW107CiAgICAgIHJlc3BvbnNlLmRhdGEuZm9yRWFjaCgoZWxlbSwgaW5kZXgpID0+IHsKICAgICAgICB2YXIgb2JqID0ge307CiAgICAgICAgb2JqLmxhYmVsID0gZWxlbS5kaWN0TGFiZWw7CiAgICAgICAgb2JqLnZhbHVlID0gZWxlbS5kaWN0VmFsdWU7CiAgICAgICAgb3B0LnB1c2gob2JqKTsKICAgICAgfSk7CiAgICAgIHRoaXMuYWZ0ZXJTYWxlWWVhck9wdGlvbnMgPSBvcHQ7CiAgICB9KTsKICAgIHRoaXMuZ2V0RGljdHMoInByX2RhdGFfdHlwZSIpLnRoZW4oKHJlc3BvbnNlKSA9PiB7CiAgICAgIHRoaXMuaW5mb1R5cGVPcHRpb25zID0gcmVzcG9uc2UuZGF0YTsKICAgIH0pOwoKICAgIHZhciBvcHQgPSBbXTsKICAgIG9wdC5wdXNoKHsgaWQ6IDAsIGxhYmVsOiAi5YWo6YOoIiB9KTsKICAgIG9wdC5wdXNoKHsgaWQ6IDIsIGxhYmVsOiAi5pmu6YCa55So5oi3IiB9KTsKICAgIG9wdC5wdXNoKHsgaWQ6IDEwLCBsYWJlbDogIuecgei0n+i0o+S6uiIgfSk7CgogICAgdmFyIHVzZXJUeXBlID0ge307CiAgICB1c2VyVHlwZS5sYWJlbCA9ICLmiYDlsZ7nlKjmiLciOwogICAgdXNlclR5cGUuY2hpbGRyZW4gPSBvcHQ7CiAgICB2YXIgdXNlclR5cGVzID0gW107CiAgICB1c2VyVHlwZXMucHVzaCh1c2VyVHlwZSk7CiAgICB0aGlzLnVzZXJUeXBlVHJlZSA9IHVzZXJUeXBlczsKICAgIGlmICgKICAgICAgdGhpcy4kc3RvcmUuc3RhdGUudXNlci5yb2xlcyAmJgogICAgICB0aGlzLiRzdG9yZS5zdGF0ZS51c2VyLnJvbGVzLmluY2x1ZGVzKCJjb21tb24iKQogICAgKSB7CiAgICAgIHRoaXMuc2hvd1VUeXBlID0gZmFsc2U7CiAgICB9CiAgICBpZiAodGhpcy5faXNNb2JpbGUoKSkgewogICAgICB0aGlzLmlzTW9iaWxlID0gdHJ1ZTsKICAgICAgdGhpcy5wYWdlTGF5b3V0ID0gInRvdGFsLCBwcmV2LCBuZXh0LCBqdW1wZXIiOwogICAgfQogICAgaWYgKAogICAgICB0aGlzLiRzdG9yZS5zdGF0ZS51c2VyLnJvbGVzICYmCiAgICAgIHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIucm9sZXMuaW5jbHVkZXMoInJlcG9ydF9hZG1pbiIpCiAgICApIHsKICAgICAgdGhpcy5pc0FkbWluID0gZmFsc2U7CiAgICB9CiAgfSwKICBtZXRob2RzOiB7CiAgICBfaXNNb2JpbGUoKSB7CiAgICAgIGxldCBmbGFnID0gbmF2aWdhdG9yLnVzZXJBZ2VudC5tYXRjaCgKICAgICAgICAvKHBob25lfHBhZHxwb2R8aVBob25lfGlQb2R8aW9zfGlQYWR8QW5kcm9pZHxNb2JpbGV8QmxhY2tCZXJyeXxJRU1vYmlsZXxNUVFCcm93c2VyfEpVQ3xGZW5uZWN8d09TQnJvd3NlcnxCcm93c2VyTkd8V2ViT1N8U3ltYmlhbnxXaW5kb3dzIFBob25lKS9pCiAgICAgICk7CiAgICAgIHJldHVybiBmbGFnOwogICAgfSwKICAgIC8qKiDmn6Xor6Lpobnnm67miqXlpIfliJfooaggKi8KICAgIGdldExpc3QoKSB7CiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7CiAgICAgIGxpc3RSZXBvcnQodGhpcy5xdWVyeVBhcmFtcykudGhlbigocmVzcG9uc2UpID0+IHsKICAgICAgICB0aGlzLnJlcG9ydExpc3QgPSByZXNwb25zZS5yb3dzOwogICAgICAgIHRoaXMudG90YWwgPSByZXNwb25zZS50b3RhbDsKICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgfSk7CiAgICB9LAogICAgaGFuZGxlRG93bmxvYWQodXJsKSB7CiAgICAgIGNvbnN0IGEgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCJhIik7IC8vIOWIm+W7uuS4gOS4qkhUTUwg5YWD57SgCiAgICAgIGEuc2V0QXR0cmlidXRlKCJ0YXJnZXQiLCAiX2JsYW5rIik7CiAgICAgIGEuc2V0QXR0cmlidXRlKCJkb3dubG9hZCIsICIiKTsgLy9kb3dubG9hZOWxnuaApwogICAgICBjb25zdCBocmVmID0KICAgICAgICAiaHR0cHM6Ly9yZXBvcnQuY2xsZWQuY29tL3Byb2QtYXBpL2NvbW1vbi9kb3dubG9hZC9yZXNvdXJjZT9yZXNvdXJjZT0iICsKICAgICAgICB1cmw7CiAgICAgIGNvbnNvbGUubG9nKGhyZWYpOwogICAgICBhLnNldEF0dHJpYnV0ZSgiaHJlZiIsIGhyZWYpOyAvLyBocmVm6ZO+5o6lCiAgICAgIGEuY2xpY2soKTsgLy8g6Ieq5omn6KGM54K55Ye75LqL5Lu2CiAgICB9LAogICAgLy8g5a6h5qC454q25oCB6IqC54K55Y2V5Ye75LqL5Lu2CiAgICBoYW5kbGVBdWRpdE5vZGVDbGljayhkYXRhKSB7CiAgICAgIGlmIChkYXRhLmlkID09IDkpIHsKICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmF1ZGl0U3RhdHVzID0gdW5kZWZpbmVkOwogICAgICAgIHRoaXMucXVlcnlQYXJhbXMubm9kZSA9IHVuZGVmaW5lZDsKICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnNwYXJlMSA9IHVuZGVmaW5lZDsKICAgICAgfSBlbHNlIHsKICAgICAgICBpZiAoZGF0YS5pZCA9PSAxIHx8IGRhdGEuaWQgPT0gMTApIHsKICAgICAgICAgIHRoaXMucXVlcnlQYXJhbXMuYXVkaXRTdGF0dXMgPSAxOwogICAgICAgICAgaWYgKHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIucm9sZXMpIHsKICAgICAgICAgICAgaWYgKHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIucm9sZXMuaW5jbHVkZXMoInByb3ZpbmNlX2FkbWluIikpIHsKICAgICAgICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLm5vZGUgPSAi55yB6LSf6LSj5Lq6IjsKICAgICAgICAgICAgICBpZiAoZGF0YS5pZCA9PSAxMCkgewogICAgICAgICAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5zcGFyZTEgPSAiPSI7CiAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgIHRoaXMucXVlcnlQYXJhbXMuc3BhcmUxID0gIiE9IjsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0KICAgICAgICAgICAgaWYgKHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIucm9sZXMuaW5jbHVkZXMoInJlcG9ydF9hZG1pbiIpKSB7CiAgICAgICAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5ub2RlID0gIuWuoeaguOWRmCI7CiAgICAgICAgICAgICAgaWYgKGRhdGEuaWQgPT0gMTApIHsKICAgICAgICAgICAgICAgIHRoaXMucXVlcnlQYXJhbXMuc3BhcmUxID0gIj0iOwogICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnNwYXJlMSA9ICIhPSI7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMucXVlcnlQYXJhbXMuYXVkaXRTdGF0dXMgPSBkYXRhLmlkOwogICAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5ub2RlID0gdW5kZWZpbmVkOwogICAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5zcGFyZTEgPSB1bmRlZmluZWQ7CiAgICAgICAgfQogICAgICB9CiAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgfSwKICAgIC8vIOaTjeS9nOexu+Wei+iKgueCueWNleWHu+S6i+S7tgogICAgaGFuZGxlT3B0Tm9kZUNsaWNrKGRhdGEpIHsKICAgICAgaWYgKGRhdGEuaWQgPT0gMCkgewogICAgICAgIHRoaXMucXVlcnlQYXJhbXMub3BlcmF0aW9uVHlwZSA9IHVuZGVmaW5lZDsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLm9wZXJhdGlvblR5cGUgPSBkYXRhLmlkOwogICAgICB9CiAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgfSwKICAgIC8vIOeUqOaIt+exu+Wei+iKgueCueWNleWHu+S6i+S7tgogICAgaGFuZGxlVXNlck5vZGVDbGljayhkYXRhKSB7CiAgICAgIGlmIChkYXRhLmlkID09IDApIHsKICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnVzZXJUeXBlID0gdW5kZWZpbmVkOwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMucXVlcnlQYXJhbXMudXNlclR5cGUgPSBkYXRhLmlkOwogICAgICB9CiAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgfSwKICAgIC8vIOetm+mAieiKgueCuQogICAgZmlsdGVyTm9kZSh2YWx1ZSwgZGF0YSkgewogICAgICBpZiAoIXZhbHVlKSByZXR1cm4gdHJ1ZTsKICAgICAgcmV0dXJuIGRhdGEubGFiZWwuaW5kZXhPZih2YWx1ZSkgIT09IC0xOwogICAgfSwKICAgIHNlYXJjaEZvcm1hdChyb3csIGNvbHVtbikgewogICAgICBpZiAoCiAgICAgICAgcm93LmluZGV4T2YodGhpcy5xdWVyeVBhcmFtcy5zcGFyZTEpICE9PSAtMSAmJgogICAgICAgIHRoaXMucXVlcnlQYXJhbXMuc3BhcmUxICE9PSAiIgogICAgICApIHsKICAgICAgICByZXR1cm4gcm93LnJlcGxhY2UoCiAgICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnNwYXJlMSwKICAgICAgICAgICc8Zm9udCBjb2xvcj0iI2YwMCI+JyArIHRoaXMucXVlcnlQYXJhbXMuc3BhcmUxICsgIjwvZm9udD4iCiAgICAgICAgKTsKICAgICAgfSBlbHNlIHsKICAgICAgICByZXR1cm4gcm93OwogICAgICB9CiAgICB9LAogICAgLy8g5pON5L2c57G75Z6L5a2X5YW457+76K+RCiAgICBvcGVyYXRpb25UeXBlRm9ybWF0KHJvdywgY29sdW1uKSB7CiAgICAgIHJldHVybiB0aGlzLnNlbGVjdERpY3RMYWJlbCh0aGlzLm9wZXJhdGlvblR5cGVPcHRpb25zLCByb3cub3BlcmF0aW9uVHlwZSk7CiAgICB9LAogICAgLy8g5a6h5qC454q25oCB5a2X5YW457+76K+RCiAgICBhdWRpdFN0YXR1c0Zvcm1hdChyb3csIGNvbHVtbikgewogICAgICByZXR1cm4gdGhpcy5zZWxlY3REaWN0TGFiZWwodGhpcy5hdWRpdFN0YXR1c09wdGlvbnMsIHJvdy5hdWRpdFN0YXR1cyk7CiAgICB9LAogICAgLy8g57yW6L6R54q25oCB5a2X5YW457+76K+RCiAgICBlZGl0U3RhdHVzRm9ybWF0KHJvdywgY29sdW1uKSB7CiAgICAgIHJldHVybiB0aGlzLnNlbGVjdERpY3RMYWJlbCh0aGlzLmVkaXRTdGF0dXNPcHRpb25zLCByb3cuZWRpdFN0YXR1cyk7CiAgICB9LAogICAgLy8g5oub5qCH5pa55byP5a2X5YW457+76K+RCiAgICBiaWRkaW5nVHlwZUZvcm1hdChyb3csIGNvbHVtbikgewogICAgICByZXR1cm4gdGhpcy5zZWxlY3REaWN0TGFiZWwodGhpcy5iaWRkaW5nVHlwZU9wdGlvbnMsIHJvdy5iaWRkaW5nVHlwZSk7CiAgICB9LAogICAgLy8g5oqV5qCH5Lqn5ZOB5Z6L5Y+35a2X5YW457+76K+RCiAgICBtb2RlbEZvcm1hdChyb3csIGNvbHVtbikgewogICAgICByZXR1cm4gdGhpcy5zZWxlY3REaWN0TGFiZWxzKHRoaXMubW9kZWxPcHRpb24xLCByb3cubW9kZWwpOwogICAgfSwKICAgIC8vIOaKleagh+S6p+WTgeinhOagvOWtl+WFuOe/u+ivkQogICAgc3BlY0Zvcm1hdChyb3csIGNvbHVtbikgewogICAgICByZXR1cm4gdGhpcy5zZWxlY3REaWN0TGFiZWxzKHRoaXMuc3BlY09wdGlvbjEsIHJvdy5zcGVjKTsKICAgIH0sCiAgICAvLyDmiYDpnIDotYTmlpnlrZflhbjnv7vor5EKICAgIHJlcXVpcmVJbmZvRm9ybWF0KHJvdywgY29sdW1uKSB7CiAgICAgIGlmIChyb3cucmVxdWlyZUluZm8pIHsKICAgICAgICByZXR1cm4gdGhpcy5zZWxlY3REaWN0TGFiZWxzKHRoaXMucmVxdWlyZUluZm9PcHRpb24xLCByb3cucmVxdWlyZUluZm8pOwogICAgICB9CiAgICB9LAogICAgLy8g6LWE5paZ57G75Z6L5a2X5YW457+76K+RCiAgICBpbmZvVHlwZUZvcm1hdChyb3csIGNvbHVtbikgewogICAgICByZXR1cm4gdGhpcy5zZWxlY3REaWN0TGFiZWxzKHRoaXMuaW5mb1R5cGVPcHRpb25zLCByb3cuaW5mb1R5cGUpOwogICAgfSwKICAgIC8vIOaJgOWxnuecgeS7veWtl+WFuOe/u+ivkQogICAgYmVsb25nUHJvdmluY2VGb3JtYXQocm93LCBjb2x1bW4pIHsKICAgICAgcmV0dXJuIHRoaXMuc2VsZWN0RGljdExhYmVscyh0aGlzLmJlbG9uZ1Byb3ZpbmNlT3B0aW9uczEsIHJvdy5iZWxvbmdQcm92aW5jZSk7CiAgICB9LAogICAgLy8g5ZSu5ZCO5bm06ZmQ5a2X5YW457+76K+RCiAgICBhZnRlclNhbGVZZWFyRm9ybWF0KHJvdywgY29sdW1uKSB7CiAgICAgIHJldHVybiB0aGlzLnNlbGVjdERpY3RMYWJlbHModGhpcy5hZnRlclNhbGVZZWFyT3B0aW9uczEsIHJvdy5hZnRlclNhbGVZZWFyKTsKICAgIH0sCiAgICAvLyDlj5bmtojmjInpkq4KICAgIGNhbmNlbCgpIHsKICAgICAgdGhpcy5vcGVuID0gZmFsc2U7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgIH0sCiAgICB2aWV3T2soKSB7CiAgICAgIHRoaXMudmlld09wZW4gPSBmYWxzZTsKICAgIH0sCiAgICAvLyDooajljZXph43nva4KICAgIHJlc2V0KCkgewogICAgICB0aGlzLmZvcm0gPSB7CiAgICAgICAgcHJvamVjdElkOiBudWxsLAogICAgICAgIHByb2plY3RObzogbnVsbCwKICAgICAgICBwcm9qZWN0TmFtZTogbnVsbCwKICAgICAgICBvcGVyYXRpb25UeXBlOiBudWxsLAogICAgICAgIGF1ZGl0U3RhdHVzOiAiMCIsCiAgICAgICAgcmVqZWN0UmVhc29uOiBudWxsLAogICAgICAgIHByb3ZpbmNlOiBudWxsLAogICAgICAgIGNpdHk6IG51bGwsCiAgICAgICAgZGlzdHJpY3Q6IG51bGwsCiAgICAgICAgYWRkcmVzczogbnVsbCwKICAgICAgICBlZGl0U3RhdHVzOiAiMCIsCiAgICAgICAgYmVsb25nVXNlcjogbnVsbCwKICAgICAgICBiaWRkaW5nQ29tcGFueTogbnVsbCwKICAgICAgICBvcGVuRGF0ZTogbnVsbCwKICAgICAgICBiaWRkaW5nVHlwZTogbnVsbCwKICAgICAgICBidWRnZXRNb25leTogbnVsbCwKICAgICAgICBhdXRoQ29tcGFueTogbnVsbCwKICAgICAgICBiaWRkaW5nTmV0OiBudWxsLAogICAgICAgIGRpc3RyaWJ1dG9yOiBudWxsLAogICAgICAgIG1vZGVsOiBbXSwKICAgICAgICBzcGVjOiBbXSwKICAgICAgICBhcmVhOiBudWxsLAogICAgICAgIGF1dGhGaWxlOiBudWxsLAogICAgICAgIGFmdGVyU2FsZUZpbGU6IG51bGwsCiAgICAgICAgcmVxdWlyZUluZm86IFtdLAogICAgICAgIGluZm9UeXBlOiBbXSwKICAgICAgICBzY2FuRmlsZTogbnVsbCwKICAgICAgICBzZW5kQWRkcmVzczogbnVsbCwKICAgICAgICBtYWlsSW5mbzogbnVsbCwKICAgICAgICBleHByZXNzSW5mbzogbnVsbCwKICAgICAgICByZW1hcms6IG51bGwsCiAgICAgICAgc3BhcmUxOiBudWxsLAogICAgICAgIHNwYXJlMjogbnVsbCwKICAgICAgfTsKICAgICAgdGhpcy5yZXNldEZvcm0oImZvcm0iKTsKICAgIH0sCiAgICAvKiog5pCc57Si5oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVRdWVyeSgpIHsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsKICAgICAgLy8g5riF56m65omA5pyJ55u45YWz5a2X5q61CiAgICAgIHRoaXMucXVlcnlQYXJhbXMucHJvamVjdE5vID0gbnVsbDsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wcm9qZWN0TmFtZSA9IG51bGw7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMuYWRkcmVzcyA9IG51bGw7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMuYmlkZGluZ0NvbXBhbnkgPSBudWxsOwogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmF1dGhDb21wYW55ID0gbnVsbDsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5mdWxsRmllbGQgPSBudWxsOwoKICAgICAgaWYgKHRoaXMuc2VhcmNoRmllbGQgPT09ICdhbGwnKSB7CiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5mdWxsRmllbGQgPSB0aGlzLnNlYXJjaFZhbHVlOyAvLyDlgYforr7lkI7nq68gZnVsbEZpZWxkIOWBmuWFqOWtl+auteaooeezigogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMucXVlcnlQYXJhbXNbdGhpcy5zZWFyY2hGaWVsZF0gPSB0aGlzLnNlYXJjaFZhbHVlOwogICAgICB9CiAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgfSwKICAgIC8qKiDph43nva7mjInpkq7mk43kvZwgKi8KICAgIHJlc2V0UXVlcnkoKSB7CiAgICAgIHRoaXMucXVlcnlBcmVhID0gW107CiAgICAgIHRoaXMucXVlcnlQYXJhbXMgPSB7CiAgICAgICAgcGFnZU51bTogMSwKICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgcHJvamVjdE5vOiBudWxsLAogICAgICAgIHByb2plY3ROYW1lOiBudWxsLAogICAgICAgIG9wZXJhdGlvblR5cGU6IG51bGwsCiAgICAgICAgYXVkaXRTdGF0dXM6IG51bGwsCiAgICAgICAgcHJvdmluY2U6IG51bGwsCiAgICAgICAgdXNlclR5cGU6IG51bGwsCiAgICAgICAgYmVsb25nVXNlcjogbnVsbCwKICAgICAgICBzcGFyZTE6IG51bGwsCiAgICAgICAgYWRkcmVzczogbnVsbCwKICAgICAgICBiaWRkaW5nQ29tcGFueTogbnVsbCwKICAgICAgICBhdXRoQ29tcGFueTogbnVsbCwKICAgICAgICBmdWxsRmllbGQ6IG51bGwKICAgICAgfTsKICAgICAgdGhpcy5zZWFyY2hGaWVsZCA9ICdhbGwnOwogICAgICB0aGlzLnNlYXJjaFZhbHVlID0gJyc7CiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsKICAgIH0sCiAgICAvLyDlpJrpgInmoYbpgInkuK3mlbDmja4KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsKICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKChpdGVtKSA9PiBpdGVtLnByb2plY3RJZCk7CiAgICAgIHRoaXMuc2luZ2xlID0gc2VsZWN0aW9uLmxlbmd0aCAhPT0gMTsKICAgICAgdGhpcy5tdWx0aXBsZSA9ICFzZWxlY3Rpb24ubGVuZ3RoOwogICAgICB0aGlzLmNob29zZU9wdFR5cGUgPSBzZWxlY3Rpb25bMF0ub3BlcmF0aW9uVHlwZTsKICAgICAgdGhpcy5jaG9vc2VBdWRpdFN0YXR1cyA9IHNlbGVjdGlvblswXS5hdWRpdFN0YXR1czsKICAgICAgdGhpcy5jaG9vc2VVc2VySWQgPSBzZWxlY3Rpb25bMF0udXNlcklkOwogICAgICB0aGlzLmNob29zZUVkaXRTdGF0dXMgPSBzZWxlY3Rpb25bMF0uZWRpdFN0YXR1czsKICAgICAgdGhpcy5jaG9vc2VTcGFyZTIgPSBzZWxlY3Rpb25bMF0uc3BhcmUyOwogICAgfSwKICAgIC8qKiDmlrDlop7mjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZUFkZCgpIHsKICAgICAgLy8gdGhpcy5yZXNldCgpOwogICAgICAvLyB0aGlzLm9wZW4gPSB0cnVlOwogICAgICAvLyB0aGlzLnRpdGxlID0gIua3u+WKoOmhueebruaKpeWkhyI7CiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHsKICAgICAgICBwYXRoOiAiL3Byb2plY3QvcmVwb3J0L2Zvcm0iLAogICAgICAgIHF1ZXJ5OiB7CiAgICAgICAgICBidXNpbmVzc0tleTogdW5kZWZpbmVkLAogICAgICAgICAgZm9ybUVkaXQ6IHRydWUsCiAgICAgICAgfSwKICAgICAgfSk7CiAgICB9LAogICAgaGFuZGxlVmlldyhyb3cpIHsKICAgICAgbGV0IHRoYXQgPSB0aGlzOwogICAgICB0aGlzLnZpZXcgPSBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KHJvdykpOwogICAgICB0aGlzLnZpZXcub3BlcmF0aW9uVHlwZSA9IHRoaXMub3BlcmF0aW9uVHlwZUZvcm1hdChyb3cpOwogICAgICB0aGlzLnZpZXcuYXVkaXRTdGF0dXMgPSB0aGlzLmF1ZGl0U3RhdHVzRm9ybWF0KHJvdyk7CiAgICAgIHRoaXMudmlldy5lZGl0U3RhdHVzID0gdGhpcy5lZGl0U3RhdHVzRm9ybWF0KHJvdyk7CiAgICAgIHRoaXMudmlldy5iaWRkaW5nVHlwZSA9IHRoaXMuYmlkZGluZ1R5cGVGb3JtYXQocm93KTsKICAgICAgdGhpcy52aWV3Lm1vZGVsID0gdGhpcy5tb2RlbEZvcm1hdChyb3cpOwogICAgICB0aGlzLnZpZXcuc3BlYyA9IHRoaXMuc3BlY0Zvcm1hdChyb3cpOwogICAgICB0aGlzLnZpZXcucmVxdWlyZUluZm8gPSB0aGlzLnJlcXVpcmVJbmZvRm9ybWF0KHJvdyk7CiAgICAgIHRoaXMudmlldy5pbmZvVHlwZSA9IHRoaXMuaW5mb1R5cGVGb3JtYXQocm93KTsKICAgICAgdGhpcy52aWV3LmJlbG9uZ1Byb3ZpbmNlID0gdGhpcy5iZWxvbmdQcm92aW5jZUZvcm1hdChyb3cpOwogICAgICB0aGlzLnZpZXcuYWZ0ZXJTYWxlWWVhciA9IHRoaXMuYWZ0ZXJTYWxlWWVhckZvcm1hdChyb3cpOwogICAgICBpZiAocm93LnJlcXVpcmVJbmZvcykgewogICAgICAgIHRoaXMuc2VsZWN0RGljdExhYmVscyh0aGlzLnJlcXVpcmVJbmZvT3B0aW9uMSwgcm93LnJlcXVpcmVJbmZvKTsKICAgICAgICAvL3RoaXMudmlldy5yZXF1aXJlSW5mbyA9CiAgICAgICAgLy8gY29uc3QgaW5mb0xpc3QgPSB0aGlzLnZpZXcucmVxdWlyZUluZm8uc3BsaXQoIiwiKTsKICAgICAgICBjb25zdCBoYWxmID0gTWF0aC5jZWlsKHJvdy5yZXF1aXJlSW5mb3MubGVuZ3RoIC8gMik7CgogICAgICAgIHRoaXMuaW5mb0xpc3QxID0gcm93LnJlcXVpcmVJbmZvcy5zcGxpY2UoMCwgaGFsZik7CiAgICAgICAgdGhpcy5pbmZvTGlzdDIgPSByb3cucmVxdWlyZUluZm9zLnNwbGljZSgtaGFsZik7CgogICAgICAgIC8vIGNvbnN0IHRtcExpc3QxID0gaW5mb0xpc3Quc3BsaWNlKDAsIGhhbGYpOwogICAgICAgIC8vIGNvbnN0IHRtcExpc3QyID0gaW5mb0xpc3Quc3BsaWNlKC1oYWxmKTsKICAgICAgICAvLyB0bXBMaXN0MS5mb3JFYWNoKChlbGVtZW50KSA9PiB7CiAgICAgICAgLy8gICBjb25zb2xlLmxvZyhlbGVtZW50KTsKICAgICAgICAvLyB9KTsKICAgICAgICAvLyDlvqrnjq/lr7nosaHotYvlgLwKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLmluZm9MaXN0MSA9IFtdOwogICAgICAgIHRoaXMuaW5mb0xpc3QyID0gW107CiAgICAgIH0KCiAgICAgIGlmIChyb3cub3BlcmF0aW9uVHlwZSA9PSAiMiIgJiYgcm93LnNwYXJlMSA9PSAiMSIpIHsKICAgICAgICB0aGlzLmRlZktleSA9ICJwcm9jZXNzX3Byb2plY3RfYXV0aCI7CiAgICAgICAgdGhpcy50aXRsZSA9ICLmn6XnnIvpobnnm67miqXlpIfovazmjojmnYMiOwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuZGVmS2V5ID0gInByb2Nlc3NfcHJvamVjdF9yZXBvcnQiOwogICAgICAgIHRoaXMudGl0bGUgPSAi5p+l55yL6aG555uu5oql5aSHL+aOiOadgyI7CiAgICAgIH0KICAgICAgY29uc3QgcGFyYW1zID0geyBiaXpLZXk6IHJvdy5wcm9qZWN0SWQsIGRlZktleTogdGhpcy5kZWZLZXkgfTsKICAgICAgZ2V0SW5zSWRCeUJpektleShwYXJhbXMpLnRoZW4oKHJlc3ApID0+IHsKICAgICAgICB0aGlzLmJpektleSA9IHJvdy5wcm9qZWN0SWQ7CiAgICAgICAgaWYgKHJlc3AuZGF0YSAmJiByZXNwLmRhdGEuaW5zdGFuY2VJZCkgewogICAgICAgICAgdGhpcy5wcm9jSW5zSWQgPSByZXNwLmRhdGEuaW5zdGFuY2VJZDsKICAgICAgICAgIHRoaXMudGFza0lkID0gcmVzcC5kYXRhLnRhc2tJZDsKICAgICAgICAgIC8vY29uc29sZS5sb2coIj09aGFuZGxlVmlldz0+PiIpCiAgICAgICAgICAvL2NvbnNvbGUubG9nKHJlc3AuZGF0YSkKICAgICAgICAgIGlmICgKICAgICAgICAgICAgcmVzcC5kYXRhLmluc3RhbmNlSWQgJiYKICAgICAgICAgICAgIXJlc3AuZGF0YS5lbmRUaW1lICYmCiAgICAgICAgICAgIHJlc3AuZGF0YS5hc3NpZ25lZSA9PSB0aGlzLiRzdG9yZS5zdGF0ZS51c2VyLnVzZXJJZAogICAgICAgICAgKSB7CiAgICAgICAgICAgIGlmICgKICAgICAgICAgICAgICB0aGlzLiRzdG9yZS5zdGF0ZS51c2VyLnJvbGVzICYmCiAgICAgICAgICAgICAgdGhpcy4kc3RvcmUuc3RhdGUudXNlci5yb2xlcy5pbmNsdWRlcygicmVwb3J0X2FkbWluIikgJiYKICAgICAgICAgICAgICByb3cub3BlcmF0aW9uVHlwZSA9PSAiMiIKICAgICAgICAgICAgKSB7CiAgICAgICAgICAgICAgdGhpcy5pc0F1dGhJbWFnZXMgPSB0cnVlOwogICAgICAgICAgICB9CiAgICAgICAgICAgIHRoaXMuZmluaXNoZWQgPSBmYWxzZTsKICAgICAgICAgIH0gZWxzZSBpZiAoCiAgICAgICAgICAgIHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIucm9sZXMgJiYKICAgICAgICAgICAgdGhpcy4kc3RvcmUuc3RhdGUudXNlci5yb2xlcy5pbmNsdWRlcygicmVwb3J0X2FkbWluIikgJiYKICAgICAgICAgICAgcm93Lm5vZGUgPT0gIuWuoeaguOWRmCIKICAgICAgICAgICkgewogICAgICAgICAgICBpZiAocm93Lm9wZXJhdGlvblR5cGUgPT0gIjIiKSB7CiAgICAgICAgICAgICAgdGhpcy5pc0F1dGhJbWFnZXMgPSB0cnVlOwogICAgICAgICAgICB9CiAgICAgICAgICAgIC8v5a6h5qC45ZGY6KeS6Imy5LiN5o6n5Yi26LCB5pON5L2cCiAgICAgICAgICAgIHRoaXMuZmluaXNoZWQgPSBmYWxzZTsKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIHRoaXMuZmluaXNoZWQgPSB0cnVlOwogICAgICAgICAgfQogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLmZpbmlzaGVkID0gdHJ1ZTsKICAgICAgICAgIHRoaXMucHJvY0luc0lkID0gdW5kZWZpbmVkOwogICAgICAgICAgdGhpcy50YXNrSWQgPSB1bmRlZmluZWQ7CiAgICAgICAgfQoKICAgICAgICAvLyBjb25zb2xlLmxvZygiPT09PT4+Pumps+WbniIpCiAgICAgICAgLy8gLy/pqbPlm57nlKjmiLcKICAgICAgICAvLyBpZihyb3cuYXVkaXRTdGF0dXMgPT0gJzMnICYmIHJvdy51c2VySWQgPT0gdGhpcy4kc3RvcmUuc3RhdGUudXNlci51c2VySWQpewogICAgICAgIC8vICAgdGhpcy5maW5pc2hlZCA9IGZhbHNlOwogICAgICAgIC8vIH0KICAgICAgICAvLyBjb25zb2xlLmxvZygiPT09PT4+Pumps+Wbnu+8miIgKyB0aGlzLmZpbmlzaGVkKQoKICAgICAgICB0aGlzLnZpZXdPcGVuID0gdHJ1ZTsKICAgICAgfSk7CiAgICAgIGdldExpa2VMaXN0KHsKICAgICAgICBwcm9qZWN0TmFtZTogcm93LnByb2plY3ROYW1lLAogICAgICAgIHByb2plY3RJZDogcm93LnByb2plY3RJZCwKICAgICAgfSkudGhlbigocmVzcCkgPT4gewogICAgICAgIC8vY29uc29sZS5sb2cocmVzcCkKICAgICAgICBpZiAocmVzcC5kYXRhICYmIHJlc3AuZGF0YS5sZW5ndGggPiAwKSB7CiAgICAgICAgICB0aGlzLmxpa2VMaXN0ID0gcmVzcC5kYXRhOwogICAgICAgICAgdGhhdC5saWtlY291bnQgPSByZXNwLmRhdGEubGVuZ3RoOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLmxpa2VMaXN0ID0gdW5kZWZpbmVkOwogICAgICAgICAgdGhhdC5saWtlY291bnQgPSB1bmRlZmluZWQ7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvKiog5o6I5p2D5oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVBdXRoKHJvdykgewogICAgICBjb25zdCBsb2FkaW5nID0gdGhpcy4kbG9hZGluZyh7CiAgICAgICAgbG9jazogdHJ1ZSwKICAgICAgICB0ZXh0OiAi5o6I5p2D5LitLi4uIiwKICAgICAgICBzcGlubmVyOiAiZWwtaWNvbi1sb2FkaW5nIiwKICAgICAgICBiYWNrZ3JvdW5kOiAicmdiYSgwLCAwLCAwLCAwLjcpIiwKICAgICAgfSk7CiAgICAgIGF1dGhSZXBvcnQocm93KQogICAgICAgIC50aGVuKChyZXNwKSA9PiB7CiAgICAgICAgICBsb2FkaW5nLmNsb3NlKCk7CiAgICAgICAgICB0aGlzLm1zZ1N1Y2Nlc3MocmVzcC5tc2cpOwogICAgICAgICAgdGhpcy4kcm91dGVyLmdvKDApOwogICAgICAgIH0pCiAgICAgICAgLmNhdGNoKChlKSA9PiB7CiAgICAgICAgICBsb2FkaW5nLmNsb3NlKCk7CiAgICAgICAgfSk7CiAgICB9LAogICAgLyoqIOS/ruaUueaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlVXBkYXRlKCkgewogICAgICBsZXQgdGhhdCA9IHRoaXM7CiAgICAgIC8vIHRoYXQuYXVkaXRTdGF0dXNFZGl0ID0gdHJ1ZTsKICAgICAgLy8gaWYoIXRoYXQuaXNBZG1pbiAmJiB0aGF0LmNob29zZUF1ZGl0U3RhdHVzID09IDMpewogICAgICAvLyAgIHRoYXQuYXVkaXRTdGF0dXNFZGl0ID0gZmFsc2U7CiAgICAgIC8vIH1lbHNle30KICAgICAgLy/nlLPor7fogIUKICAgICAgdmFyIGlzQXBwbHkgPQogICAgICAgIHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIucm9sZXMgJiYKICAgICAgICAodGhpcy4kc3RvcmUuc3RhdGUudXNlci5yb2xlcy5pbmNsdWRlcygiY29tbW9uIikgfHwKICAgICAgICAgIHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIucm9sZXMuaW5jbHVkZXMoInByb3ZpbmNlX2FkbWluIikpOwoKICAgICAgaWYgKGlzQXBwbHkgJiYgdGhpcy5jaG9vc2VVc2VySWQgIT0gdGhpcy4kc3RvcmUuc3RhdGUudXNlci51c2VySWQpIHsKICAgICAgICB0aGlzLm1zZ0Vycm9yKCLlj6rog73kv67mlLnmnKzkurrmj5DkuqTnmoTpobnnm64iKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KCiAgICAgIGlmICh0aGlzLmNob29zZU9wdFR5cGUgPT0gMikgewogICAgICAgIGlmIChpc0FwcGx5ICYmIHRoaXMuY2hvb3NlU3BhcmUyICE9IDEpIHsKICAgICAgICAgIHRoaXMubXNnRXJyb3IoIuaOiOadg+iiq+mAgOWbnuaJjeiDveS/ruaUuSIpOwogICAgICAgICAgcmV0dXJuOwogICAgICAgIH0KICAgICAgfQogICAgICBpZiAodGhpcy5jaG9vc2VBdWRpdFN0YXR1cyA9PSAzICYmIGlzQXBwbHkpIHsKICAgICAgICBpZiAodGhpcy5jaG9vc2VFZGl0U3RhdHVzID09ICIwIikgewogICAgICAgICAgdGhpcy5tc2dFcnJvcigi5a6h5om56KKr6amz5Zue5peg5rOV5L+u5pS5Iik7CiAgICAgICAgICByZXR1cm47CiAgICAgICAgfQogICAgICB9CiAgICAgIC8vIGlmKHRoaXMuY2hvb3NlT3B0VHlwZSA9PSAxKXsKICAgICAgLy8gICBpZihpc0FwcGx5ICYmIHRoaXMuY2hvb3NlVXNlcklkICE9IHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIudXNlcklkICl7CiAgICAgIC8vICAgICB0aGlzLm1zZ0Vycm9yKCLlj6rog73kv67mlLnmnKzkurrmj5DkuqTnmoTmiqXlpIfpobnnm64iKTsKICAgICAgLy8gICAgIHJldHVybjsKICAgICAgLy8gICB9CiAgICAgIC8vIH0KICAgICAgdGhpcy5yZXNldCgpOwogICAgICBjb25zdCBwcm9qZWN0SWQgPSB0aGlzLmlkczsKICAgICAgZ2V0UmVwb3J0KHByb2plY3RJZCkudGhlbigocmVzcG9uc2UpID0+IHsKICAgICAgICB0aGlzLmZvcm0gPSByZXNwb25zZS5kYXRhOwogICAgICAgIGlmICh0aGlzLmZvcm0ubW9kZWwpIHRoaXMuZm9ybS5tb2RlbCA9IHRoaXMuZm9ybS5tb2RlbC5zcGxpdCgiLCIpOwogICAgICAgIGVsc2UgdGhpcy5mb3JtLm1vZGVsID0gW107CiAgICAgICAgaWYgKHRoaXMuZm9ybS5yZXF1aXJlSW5mbykKICAgICAgICAgIHRoaXMuZm9ybS5yZXF1aXJlSW5mbyA9IHRoaXMuZm9ybS5yZXF1aXJlSW5mby5zcGxpdCgiLCIpOwogICAgICAgIGVsc2UgdGhpcy5mb3JtLnJlcXVpcmVJbmZvID0gW107CiAgICAgICAgaWYgKHRoaXMuZm9ybS5pbmZvVHlwZSkKICAgICAgICAgIHRoaXMuZm9ybS5pbmZvVHlwZSA9IHRoaXMuZm9ybS5pbmZvVHlwZS5zcGxpdCgiLCIpOwogICAgICAgIGVsc2UgdGhpcy5mb3JtLmluZm9UeXBlID0gW107CiAgICAgICAgaWYgKHRoaXMuZm9ybS5zcGVjKSB0aGlzLmZvcm0uc3BlYyA9IHRoaXMuZm9ybS5zcGVjLnNwbGl0KCIsIik7CiAgICAgICAgZWxzZSB0aGlzLmZvcm0uc3BlYyA9IFtdOwoKICAgICAgICBpZiAodGhpcy5mb3JtLmF1dGhDb21wYW55KSB7CiAgICAgICAgICB0aGF0LmF1dGhDb21wYW55cyA9IFtdOwogICAgICAgICAgdmFyIGFycmF5ID0gdGhpcy5mb3JtLmF1dGhDb21wYW55LnNwbGl0KCIsIik7CiAgICAgICAgICBhcnJheS5mb3JFYWNoKGZ1bmN0aW9uIChlKSB7CiAgICAgICAgICAgIHRoYXQuYXV0aENvbXBhbnlzLnB1c2goewogICAgICAgICAgICAgIHZhbHVlOiBlLAogICAgICAgICAgICAgIGtleTogRGF0ZS5ub3coKSwKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9KTsKICAgICAgICAgIHRoYXQuZm9ybS5hdXRoQ29tcGFueSA9IHRoYXQuYXV0aENvbXBhbnlzWzBdLnZhbHVlOwogICAgICAgICAgdGhhdC5hdXRoQ29tcGFueXMuc3BsaWNlKDAsIDEpOwogICAgICAgICAgLy9jb25zb2xlLmxvZyh0aGF0LmF1dGhDb21wYW55cykKICAgICAgICB9IGVsc2UgdGhpcy5hdXRoQ29tcGFueXMgPSBbXTsKCiAgICAgICAgdGhpcy5vbGRPcGVyYXRpb25UeXBlID0gcmVzcG9uc2UuZGF0YS5vcGVyYXRpb25UeXBlOwoKICAgICAgICB0aGlzLm9wZW4gPSB0cnVlOwogICAgICAgIHRoaXMudGl0bGUgPSAi5L+u5pS56aG555uu5oql5aSHIjsKICAgICAgICB2YXIgcHJvdmluY2VzID0gcmVzcG9uc2UuZGF0YS5wcm92aW5jZTsKICAgICAgICBpZiAocHJvdmluY2VzLmxlbmd0aCA+IDApIHsKICAgICAgICAgIHZhciBhZGRyZXNzID0gcHJvdmluY2VzLnNwbGl0KCIvIik7CiAgICAgICAgICB2YXIgY2l0eXMgPSBbXTsKICAgICAgICAgIC8vIOecgeS7vQogICAgICAgICAgaWYgKGFkZHJlc3MubGVuZ3RoID4gMCkgY2l0eXMucHVzaChUZXh0VG9Db2RlW2FkZHJlc3NbMF1dLmNvZGUpOwogICAgICAgICAgLy8g5Z+O5biCCiAgICAgICAgICBpZiAoYWRkcmVzcy5sZW5ndGggPiAxKQogICAgICAgICAgICBjaXR5cy5wdXNoKFRleHRUb0NvZGVbYWRkcmVzc1swXV1bYWRkcmVzc1sxXV0uY29kZSk7CiAgICAgICAgICAvLyDlnLDljLoKICAgICAgICAgIGlmIChhZGRyZXNzLmxlbmd0aCA+IDIpCiAgICAgICAgICAgIGNpdHlzLnB1c2goVGV4dFRvQ29kZVthZGRyZXNzWzBdXVthZGRyZXNzWzFdXVthZGRyZXNzWzJdXS5jb2RlKTsKCiAgICAgICAgICB0aGlzLnNlbGVjdGVkT3B0aW9ucyA9IGNpdHlzOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLyoqIOaPkOS6pOaMiemSriAqLwogICAgc3VibWl0Rm9ybSgpIHsKICAgICAgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKCh2YWxpZCkgPT4gewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgaWYgKHRoaXMuZm9ybS5pbmZvVHlwZS5pbmRleE9mKCIxIikgPj0gMCAmJiB0aGlzLmZvcm0uc2NhbkZpbGUpIHsKICAgICAgICAgICAgbGV0IGVtYWlsUmVnID0gL15bYS16QS1aMC05Xy1dK0BbYS16QS1aMC05Xy1dKyhcLlthLXpBLVowLTlfLV0rKSskLzsKICAgICAgICAgICAgaWYgKCFlbWFpbFJlZy50ZXN0KHRoaXMuZm9ybS5zY2FuRmlsZSkpIHsKICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLotYTmlpnmjqXmlLbmlrnlvI/pgq7nrrHmoLzlvI/plJnor68iKTsKICAgICAgICAgICAgICByZXR1cm47CiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICAgIGlmICh0aGlzLmZvcm0ub3BlcmF0aW9uVHlwZSA9PSAyICYmICF0aGlzLmZvcm0uYXV0aEZpbGUpIHsKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi5o6I5p2D57G75Z6L5b+F6ZyA5LiK5Lyg5o6I5p2D5LmmIik7CiAgICAgICAgICAgIHJldHVybjsKICAgICAgICAgIH0KICAgICAgICAgIGlmICh0aGlzLmZvcm0ub3BlcmF0aW9uVHlwZSA9PSAxKSB7CiAgICAgICAgICAgIHRoaXMuZm9ybS5hdXRoRmlsZSA9ICIiOwogICAgICAgICAgICB0aGlzLmZvcm0uYWZ0ZXJTYWxlRmlsZSA9ICIiOwogICAgICAgICAgfQogICAgICAgICAgdmFyIGZvcm1TdHIgPSBKU09OLnN0cmluZ2lmeSh0aGlzLmZvcm0pOwogICAgICAgICAgdmFyIGZvcm1EYXRhID0gSlNPTi5wYXJzZShmb3JtU3RyKTsKICAgICAgICAgIGlmIChmb3JtRGF0YS5tb2RlbCAmJiBmb3JtRGF0YS5tb2RlbC5sZW5ndGggPiAwKQogICAgICAgICAgICBmb3JtRGF0YS5tb2RlbCA9IGZvcm1EYXRhLm1vZGVsLmpvaW4oIiwiKTsKICAgICAgICAgIGVsc2UgZm9ybURhdGEubW9kZWwgPSB1bmRlZmluZWQ7CiAgICAgICAgICBpZiAoZm9ybURhdGEucmVxdWlyZUluZm8gJiYgZm9ybURhdGEucmVxdWlyZUluZm8ubGVuZ3RoID4gMCkKICAgICAgICAgICAgZm9ybURhdGEucmVxdWlyZUluZm8gPSBmb3JtRGF0YS5yZXF1aXJlSW5mby5qb2luKCIsIik7CiAgICAgICAgICBlbHNlIGZvcm1EYXRhLnJlcXVpcmVJbmZvID0gdW5kZWZpbmVkOwogICAgICAgICAgaWYgKGZvcm1EYXRhLmluZm9UeXBlICYmIGZvcm1EYXRhLmluZm9UeXBlLmxlbmd0aCA+IDApCiAgICAgICAgICAgIGZvcm1EYXRhLmluZm9UeXBlID0gZm9ybURhdGEuaW5mb1R5cGUuam9pbigiLCIpOwogICAgICAgICAgZWxzZSBmb3JtRGF0YS5pbmZvVHlwZSA9IHVuZGVmaW5lZDsKICAgICAgICAgIGlmIChmb3JtRGF0YS5zcGVjICYmIGZvcm1EYXRhLnNwZWMubGVuZ3RoID4gMCkKICAgICAgICAgICAgZm9ybURhdGEuc3BlYyA9IGZvcm1EYXRhLnNwZWMuam9pbigiLCIpOwogICAgICAgICAgZWxzZSBmb3JtRGF0YS5zcGVjID0gdW5kZWZpbmVkOwoKICAgICAgICAgIC8v5o6I5p2D5YWs5Y+4CiAgICAgICAgICBpZiAodGhpcy5hdXRoQ29tcGFueXMubGVuZ3RoID4gMCkgewogICAgICAgICAgICB2YXIgYXJyYXkgPSBuZXcgQXJyYXkoKTsKICAgICAgICAgICAgdGhpcy5hdXRoQ29tcGFueXMuZm9yRWFjaChmdW5jdGlvbiAoZSkgewogICAgICAgICAgICAgIGFycmF5LnB1c2goZS52YWx1ZSk7CiAgICAgICAgICAgIH0pOwogICAgICAgICAgICBmb3JtRGF0YS5hdXRoQ29tcGFueSArPSAiLCIgKyBhcnJheS5qb2luKCIsIik7CiAgICAgICAgICB9CgogICAgICAgICAgaWYgKGZvcm1EYXRhLnByb2plY3RJZCAhPSBudWxsKSB7CiAgICAgICAgICAgIHVwZGF0ZVJlcG9ydChmb3JtRGF0YSkudGhlbigocmVzcG9uc2UpID0+IHsKICAgICAgICAgICAgICB0aGlzLm1zZ1N1Y2Nlc3MoIuS/ruaUueaIkOWKnyIpOwoKICAgICAgICAgICAgICBpZiAodGhpcy5vbGRPcGVyYXRpb25UeXBlID09IDEgJiYgZm9ybURhdGEub3BlcmF0aW9uVHlwZSA9PSAyKSB7CiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygiPT09PT0+Pj7miqXlpIfmlLnmjojmnYMiKTsKICAgICAgICAgICAgICB9CgogICAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlOwogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICAgICAgICB9KTsKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIGFkZFJlcG9ydChmb3JtRGF0YSkudGhlbigocmVzcG9uc2UpID0+IHsKICAgICAgICAgICAgICB0aGlzLm1zZ1N1Y2Nlc3MoIuaWsOWinuaIkOWKnyIpOwogICAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlOwogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICAgICAgICB9KTsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDliKDpmaTmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZURlbGV0ZShyb3cpIHsKICAgICAgY29uc3QgcHJvamVjdElkcyA9IHJvdy5wcm9qZWN0SWQgfHwgdGhpcy5pZHM7CiAgICAgIHRoaXMuJGNvbmZpcm0oCiAgICAgICAgJ+aYr+WQpuehruiupOWIoOmZpOmhueebruaKpeWkh+e8luWPt+S4uiInICsgcHJvamVjdElkcyArICci55qE5pWw5o2u6aG5PycsCiAgICAgICAgIuitpuWRiiIsCiAgICAgICAgewogICAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLAogICAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogIuWPlua2iCIsCiAgICAgICAgICB0eXBlOiAid2FybmluZyIsCiAgICAgICAgfQogICAgICApCiAgICAgICAgLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgICAgcmV0dXJuIGRlbFJlcG9ydChwcm9qZWN0SWRzKTsKICAgICAgICB9KQogICAgICAgIC50aGVuKCgpID0+IHsKICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICAgICAgdGhpcy5tc2dTdWNjZXNzKCLliKDpmaTmiJDlip8iKTsKICAgICAgICB9KTsKICAgIH0sCiAgICBjbGlja0V4cG9ydCgpIHsKICAgICAgdGhpcy5zaG93RXhwb3J0ID0gdHJ1ZTsKICAgIH0sCiAgICBjbGlja1ByaW50KCkgewogICAgICB0aGlzLnNob3dQcmludCA9IHRydWU7CiAgICB9LAogICAgLyoqIOWvvOWHuuaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlRXhwb3J0KHR5cGUpIHsKICAgICAgbGV0IGxvYWRpbmd3aW47CiAgICAgIGxldCB0aGF0ID0gdGhpczsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5zcGFyZTEgPSB0eXBlOwogICAgICB2YXIgY29sID0gW107CiAgICAgIHRoaXMuY29sdW1ucy5mb3JFYWNoKChpdGVtKSA9PiB7CiAgICAgICAgaWYgKGl0ZW0udmlzaWJsZSkgewogICAgICAgICAgY29sLnB1c2goaXRlbS5sYWJlbCk7CiAgICAgICAgfQogICAgICB9KTsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5zcGFyZTIgPSBjb2wuam9pbigiLCIpOwogICAgICBjb25zdCBxdWVyeVBhcmFtcyA9IHRoaXMucXVlcnlQYXJhbXM7CiAgICAgIHRoaXMuJGNvbmZpcm0oCiAgICAgICAgIuaYr+WQpuehruiupOWvvOWHuumhueebruaKpeWkh+aQnOe0oue7k+aenCIgKwogICAgICAgICh0eXBlID09IDAgPyAi5pys6aG1IiA6ICLlhajpg6giKSArCiAgICAgICAgIuaVsOaNrumhuT8iLAogICAgICAgICLorablkYoiLAogICAgICAgIHsKICAgICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAi56Gu5a6aIiwKICAgICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLAogICAgICAgICAgdHlwZTogIndhcm5pbmciLAogICAgICAgIH0KICAgICAgKQogICAgICAgIC50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICAgIGxvYWRpbmd3aW4gPSB0aGF0LiRsb2FkaW5nKHsKICAgICAgICAgICAgbG9jazogdHJ1ZSwgLy9sb2Nr55qE5L+u5pS556ymLS3pu5jorqTmmK9mYWxzZQogICAgICAgICAgICB0ZXh0OiAi5a+85Ye65LitLi4uIiwgLy/mmL7npLrlnKjliqDovb3lm77moIfkuIvmlrnnmoTliqDovb3mlofmoYgKICAgICAgICAgICAgc3Bpbm5lcjogImVsLWljb24tbG9hZGluZyIsIC8v6Ieq5a6a5LmJ5Yqg6L295Zu+5qCH57G75ZCNCiAgICAgICAgICAgIGJhY2tncm91bmQ6ICJyZ2JhKDAsIDAsIDAsIDAuNykiLCAvL+mBrue9qeWxguminOiJsgogICAgICAgICAgICB0YXJnZXQ6IGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3IoIi5hcHAtd3JhcHBlciIpLCAvL2xvYWRpbuimhueblueahGRvbeWFg+e0oOiKgueCuQogICAgICAgICAgfSk7CiAgICAgICAgICBkb2N1bWVudC5kb2N1bWVudEVsZW1lbnQuc3R5bGUub3ZlcmZsb3dZID0gImhpZGRlbiI7IC8v56aB5q2i5bqV5bGCZGl25rua5YqoCiAgICAgICAgICByZXR1cm4gZXhwb3J0UmVwb3J0KHF1ZXJ5UGFyYW1zKTsKICAgICAgICB9KQogICAgICAgIC50aGVuKChyZXNwb25zZSkgPT4gewogICAgICAgICAgdGhpcy5kb3dubG9hZChyZXNwb25zZS5tc2cpOwogICAgICAgICAgdGhpcy5zaG93RXhwb3J0ID0gZmFsc2U7CiAgICAgICAgICBsb2FkaW5nd2luLmNsb3NlKCk7CiAgICAgICAgICBkb2N1bWVudC5kb2N1bWVudEVsZW1lbnQuc3R5bGUub3ZlcmZsb3dZID0gImF1dG8iOyAvL+WFgeiuuOW6leWxgmRpdua7muWKqAogICAgICAgIH0pCiAgICAgICAgLmNhdGNoKCgpID0+IHsKICAgICAgICAgIHRoaXMuc2hvd0V4cG9ydCA9IGZhbHNlOwogICAgICAgICAgbG9hZGluZ3dpbi5jbG9zZSgpOwogICAgICAgICAgZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50LnN0eWxlLm92ZXJmbG93WSA9ICJhdXRvIjsgLy/lhYHorrjlupXlsYJkaXbmu5rliqgKICAgICAgICB9KTsKICAgIH0sCiAgICAvKiog5a+85YWl5oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVJbXBvcnQoKSB7CiAgICAgIHRoaXMudXBsb2FkLnRpdGxlID0gIumhueebruWvvOWFpSI7CiAgICAgIHRoaXMudXBsb2FkLm9wZW4gPSB0cnVlOwogICAgfSwKICAgIC8qKiDkuIvovb3mqKHmnb/mk43kvZwgKi8KICAgIGltcG9ydFRlbXBsYXRlKCkgewogICAgICBpbXBvcnRUZW1wbGF0ZSgpLnRoZW4oKHJlc3BvbnNlKSA9PiB7CiAgICAgICAgdGhpcy5kb3dubG9hZChyZXNwb25zZS5tc2cpOwogICAgICB9KTsKICAgIH0sCiAgICBkb3dubG9hZFNRUygpIHsKICAgICAgdGhpcy5kb3dubG9hZCgi5rW35L2z6ZuG5ZuiLeaOiOadg+S5pi5kb2N4IiwgZmFsc2UpOwogICAgfSwKICAgIGRvd25sb2FkQ1JIKCkgewogICAgICB0aGlzLmRvd25sb2FkKCLmtbfkvbPpm4blm6It5ZSu5ZCO5pyN5Yqh5om/6K+65Ye9LmRvYyIsIGZhbHNlKTsKICAgIH0sCiAgICAvLyDmlofku7bkuIrkvKDkuK3lpITnkIYKICAgIGhhbmRsZUZpbGVVcGxvYWRQcm9ncmVzcyhldmVudCwgZmlsZSwgZmlsZUxpc3QpIHsKICAgICAgdGhpcy51cGxvYWQuaXNVcGxvYWRpbmcgPSB0cnVlOwogICAgfSwKICAgIC8vIOaWh+S7tuS4iuS8oOaIkOWKn+WkhOeQhgogICAgaGFuZGxlRmlsZVN1Y2Nlc3MocmVzcG9uc2UsIGZpbGUsIGZpbGVMaXN0KSB7CiAgICAgIHRoaXMudXBsb2FkLm9wZW4gPSBmYWxzZTsKICAgICAgdGhpcy51cGxvYWQuaXNVcGxvYWRpbmcgPSBmYWxzZTsKICAgICAgdGhpcy4kcmVmcy51cGxvYWQuY2xlYXJGaWxlcygpOwogICAgICB0aGlzLiRhbGVydChyZXNwb25zZS5tc2csICLlr7zlhaXnu5PmnpwiLCB7IGRhbmdlcm91c2x5VXNlSFRNTFN0cmluZzogdHJ1ZSB9KTsKICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICB9LAogICAgLy8g5o+Q5Lqk5LiK5Lyg5paH5Lu2CiAgICBzdWJtaXRGaWxlRm9ybSgpIHsKICAgICAgdGhpcy4kcmVmcy51cGxvYWQuc3VibWl0KCk7CiAgICB9LAogICAgaGFuZGxlQ2hhbmdlKHZhbHVlKSB7CiAgICAgIGlmICghdmFsdWUgfHwgdmFsdWUubGVuZ3RoID09IDApIHsKICAgICAgICB0aGlzLnNlbGVjdGVkT3B0aW9ucyA9IG51bGw7CiAgICAgICAgdGhpcy5mb3JtLnByb3ZpbmNlID0gdW5kZWZpbmVkOwogICAgICAgIHRoaXMuZm9ybS5kaXN0cmljdCA9IHVuZGVmaW5lZDsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgdGhpcy5zZWxlY3RlZE9wdGlvbnMgPSB2YWx1ZTsKICAgICAgdmFyIHR4dCA9ICIiOwogICAgICB2YWx1ZS5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgdHh0ICs9IENvZGVUb1RleHRbaXRlbV0gKyAiLyI7CiAgICAgIH0pOwogICAgICBpZiAodHh0Lmxlbmd0aCA+IDEpIHsKICAgICAgICB0eHQgPSB0eHQuc3Vic3RyaW5nKDAsIHR4dC5sZW5ndGggLSAxKTsKICAgICAgICB0aGlzLmZvcm0uZGlzdHJpY3QgPSB0aGlzLiRzdG9yZS5zdGF0ZS51c2VyLnByb3ZpbmNlOwogICAgICAgIHRoaXMuZm9ybS5wcm92aW5jZSA9IHR4dDsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLmZvcm0ucHJvdmluY2UgPSB1bmRlZmluZWQ7CiAgICAgICAgdGhpcy5mb3JtLmRpc3RyaWN0ID0gdW5kZWZpbmVkOwogICAgICB9CiAgICB9LAogICAgaGFuZGxlUXVlcnlDaXR5Q2hhbmdlKHZhbHVlKSB7CiAgICAgIHRoaXMucXVlcnlBcmVhID0gdmFsdWU7CiAgICAgIHZhciB0eHQgPSAiIjsKICAgICAgdmFsdWUuZm9yRWFjaChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgIHR4dCArPSBDb2RlVG9UZXh0W2l0ZW1dICsgIi8iOwogICAgICB9KTsKICAgICAgaWYgKHR4dC5sZW5ndGggPiAxKSB7CiAgICAgICAgdHh0ID0gdHh0LnN1YnN0cmluZygwLCB0eHQubGVuZ3RoIC0gMSk7CiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wcm92aW5jZSA9IHR4dDsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnByb3ZpbmNlID0gdW5kZWZpbmVkOwogICAgICB9CiAgICB9LAogICAgaGFuZGxlUHJpbnQodHlwZSkgewogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnNwYXJlMSA9IHR5cGU7CiAgICAgIHZhciBwcm9wZXJ0aWVzID0gW107CiAgICAgIC8vcHJvcGVydGllcy5wdXNoKHtmaWVsZDogJ2luZGV4JywgZGlzcGxheU5hbWU6ICfluo/lj7cnfSk7CiAgICAgIHByb3BlcnRpZXMucHVzaCh7IGZpZWxkOiAicHJvamVjdElkIiwgZGlzcGxheU5hbWU6ICLpobnnm65JRCIgfSk7CiAgICAgIHRoaXMuY29sdW1ucy5mb3JFYWNoKChpdGVtKSA9PiB7CiAgICAgICAgaWYgKGl0ZW0udmlzaWJsZSkgewogICAgICAgICAgcHJvcGVydGllcy5wdXNoKHsgZmllbGQ6IGl0ZW0ua2V5LCBkaXNwbGF5TmFtZTogaXRlbS5sYWJlbCB9KTsKICAgICAgICB9CiAgICAgIH0pOwogICAgICBwcmludFJlcG9ydCh0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKChyZXNwb25zZSkgPT4gewogICAgICAgIHByaW50SlMoewogICAgICAgICAgcHJpbnRhYmxlOiByZXNwb25zZS5kYXRhLAogICAgICAgICAgdHlwZTogImpzb24iLAogICAgICAgICAgcHJvcGVydGllczogcHJvcGVydGllcywKICAgICAgICAgIGhlYWRlcjogJzxkaXYgc3R5bGU9InRleHQtYWxpZ246IGNlbnRlciI+PGgzPumhueebruaKpeWkh+WIl+ihqDwvaDM+PC9kaXY+JywKICAgICAgICAgIHRhcmdldFN0eWxlczogWyIqIl0sCiAgICAgICAgICBncmlkSGVhZGVyU3R5bGU6CiAgICAgICAgICAgICJtYXJnaW4tdG9wOjIwcHg7Ym9yZGVyOiAxcHggc29saWQgIzAwMDt0ZXh0LWFsaWduOmNlbnRlciIsCiAgICAgICAgICBncmlkU3R5bGU6ICJib3JkZXI6IDFweCBzb2xpZCAjMDAwO3RleHQtYWxpZ246Y2VudGVyO21pbi13aWR0aDo1MHB4OyIsCiAgICAgICAgICBzdHlsZTogIkBwYWdlIHttYXJnaW46MCAxMG1tO21hcmdpbi10b3A6MTBtbTt9IiwKICAgICAgICB9KTsKICAgICAgfSk7CiAgICAgIC8vIHByaW50SlMoewogICAgICAvLyAgIHByaW50YWJsZTogInByaW50QXJlYSIsCiAgICAgIC8vICAgdHlwZTonaHRtbCcsCiAgICAgIC8vICAgaGVhZGVyOm51bGwsCiAgICAgIC8vICAgdGFyZ2V0U3R5bGVzOlsnKiddLAogICAgICAvLyAgIHN0eWxlOiJAcGFnZSB7bWFyZ2luOjAgMTBtbX0iCiAgICAgIC8vIH0pCiAgICB9LAogICAgLy8g5Yig6ZmkIHNob3dOYW1lQ29ybG9yIOWSjCBzaG93Tm9Db3Jsb3Ig5pa55rOV77yM5paw5aKe6auY5Lqu5pa55rOVCiAgICBoaWdobGlnaHRUZXh0KHRleHQsIGtleXdvcmQpIHsKICAgICAgaWYgKCFrZXl3b3JkKSByZXR1cm4gdGV4dDsKICAgICAgLy8g5YWo6YOo6auY5LquCiAgICAgIHJldHVybiB0ZXh0ID8gdGV4dC5yZXBsYWNlKG5ldyBSZWdFeHAoa2V5d29yZCwgJ2cnKSwgYDxmb250IGNvbG9yPSIjZjAwIj4ke2tleXdvcmR9PC9mb250PmApIDogdGV4dDsKICAgIH0sCiAgICBoaWdobGlnaHRDZWxsKGZpZWxkLCB0ZXh0KSB7CiAgICAgIGlmICh0aGlzLnNlYXJjaEZpZWxkID09PSAnYWxsJyAmJiB0aGlzLnNlYXJjaFZhbHVlICYmIHRoaXMuaGlnaGxpZ2h0RmllbGRzLmluY2x1ZGVzKGZpZWxkKSkgewogICAgICAgIHJldHVybiB0aGlzLmhpZ2hsaWdodFRleHQodGV4dCwgdGhpcy5zZWFyY2hWYWx1ZSk7CiAgICAgIH0KICAgICAgaWYgKHRoaXMuc2VhcmNoRmllbGQgPT09IGZpZWxkICYmIHRoaXMuc2VhcmNoVmFsdWUpIHsKICAgICAgICByZXR1cm4gdGhpcy5oaWdobGlnaHRUZXh0KHRleHQsIHRoaXMuc2VhcmNoVmFsdWUpOwogICAgICB9CiAgICAgIHJldHVybiB0ZXh0OwogICAgfSwKICAgIHJlbW92ZURvbWFpbihpbmRleCkgewogICAgICBpZiAoaW5kZXggIT09IC0xKSB7CiAgICAgICAgdGhpcy5hdXRoQ29tcGFueXMuc3BsaWNlKGluZGV4LCAxKTsKICAgICAgfQogICAgfSwKICAgIGFkZERvbWFpbigpIHsKICAgICAgdGhpcy5hdXRoQ29tcGFueXMucHVzaCh7CiAgICAgICAgdmFsdWU6ICIiLAogICAgICAgIGtleTogRGF0ZS5ub3coKSwKICAgICAgfSk7CiAgICB9LAogICAgdXNlclNlYXJjaChjcmVhdGVCeSkgewogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmJlbG9uZ1VzZXIgPSBjcmVhdGVCeTsKICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOwogICAgfSwKICAgIG9wdFR5cGVTZWFyY2godHlwZSkgewogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLm9wZXJhdGlvblR5cGUgPSB0eXBlOwogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7CiAgICB9LAogICAgb3B0VHlwZUNoYW5nZShlKSB7CiAgICAgIGNvbnNvbGUubG9nKGUpOwogICAgfSwKICB9LAp9Owo="}, null]}