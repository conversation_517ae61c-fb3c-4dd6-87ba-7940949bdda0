{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/tool/gen/editTable.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/tool/gen/editTable.vue", "mtime": 1641277326000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldEdlblRhYmxlLCB1cGRhdGVHZW5UYWJsZSB9IGZyb20gIkAvYXBpL3Rvb2wvZ2VuIjsKaW1wb3J0IHsgb3B0aW9uc2VsZWN0IGFzIGdldERpY3RPcHRpb25zZWxlY3QgfSBmcm9tICJAL2FwaS9zeXN0ZW0vZGljdC90eXBlIjsKaW1wb3J0IHsgbGlzdE1lbnUgYXMgZ2V0TWVudVRyZWVzZWxlY3QgfSBmcm9tICJAL2FwaS9zeXN0ZW0vbWVudSI7CmltcG9ydCBiYXNpY0luZm9Gb3JtIGZyb20gIi4vYmFzaWNJbmZvRm9ybSI7CmltcG9ydCBnZW5JbmZvRm9ybSBmcm9tICIuL2dlbkluZm9Gb3JtIjsKaW1wb3J0IFNvcnRhYmxlIGZyb20gJ3NvcnRhYmxlanMnCgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogIkdlbkVkaXQiLAogIGNvbXBvbmVudHM6IHsKICAgIGJhc2ljSW5mb0Zvcm0sCiAgICBnZW5JbmZvRm9ybQogIH0sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIC8vIOmAieS4remAiemhueWNoeeahCBuYW1lCiAgICAgIGFjdGl2ZU5hbWU6ICJjbG91bSIsCiAgICAgIC8vIOihqOagvOeahOmrmOW6pgogICAgICB0YWJsZUhlaWdodDogZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50LnNjcm9sbEhlaWdodCAtIDI0NSArICJweCIsCiAgICAgIC8vIOihqOS/oeaBrwogICAgICB0YWJsZXM6IFtdLAogICAgICAvLyDooajliJfkv6Hmga8KICAgICAgY2xvdW1uczogW10sCiAgICAgIC8vIOWtl+WFuOS/oeaBrwogICAgICBkaWN0T3B0aW9uczogW10sCiAgICAgIC8vIOiPnOWNleS/oeaBrwogICAgICBtZW51czogW10sCiAgICAgIC8vIOihqOivpue7huS/oeaBrwogICAgICBpbmZvOiB7fQogICAgfTsKICB9LAogIGNyZWF0ZWQoKSB7CiAgICBjb25zdCB0YWJsZUlkID0gdGhpcy4kcm91dGUucGFyYW1zICYmIHRoaXMuJHJvdXRlLnBhcmFtcy50YWJsZUlkOwogICAgaWYgKHRhYmxlSWQpIHsKICAgICAgLy8g6I635Y+W6KGo6K+m57uG5L+h5oGvCiAgICAgIGdldEdlblRhYmxlKHRhYmxlSWQpLnRoZW4ocmVzID0+IHsKICAgICAgICB0aGlzLmNsb3VtbnMgPSByZXMuZGF0YS5yb3dzOwogICAgICAgIHRoaXMuaW5mbyA9IHJlcy5kYXRhLmluZm87CiAgICAgICAgdGhpcy50YWJsZXMgPSByZXMuZGF0YS50YWJsZXM7CiAgICAgIH0pOwogICAgICAvKiog5p+l6K+i5a2X5YW45LiL5ouJ5YiX6KGoICovCiAgICAgIGdldERpY3RPcHRpb25zZWxlY3QoKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLmRpY3RPcHRpb25zID0gcmVzcG9uc2UuZGF0YTsKICAgICAgfSk7CiAgICAgIC8qKiDmn6Xor6Loj5zljZXkuIvmi4nliJfooaggKi8KICAgICAgZ2V0TWVudVRyZWVzZWxlY3QoKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLm1lbnVzID0gdGhpcy5oYW5kbGVUcmVlKHJlc3BvbnNlLmRhdGEsICJtZW51SWQiKTsKICAgICAgfSk7CiAgICB9CiAgfSwKICBtZXRob2RzOiB7CiAgICAvKiog5o+Q5Lqk5oyJ6ZKuICovCiAgICBzdWJtaXRGb3JtKCkgewogICAgICBjb25zdCBiYXNpY0Zvcm0gPSB0aGlzLiRyZWZzLmJhc2ljSW5mby4kcmVmcy5iYXNpY0luZm9Gb3JtOwogICAgICBjb25zdCBnZW5Gb3JtID0gdGhpcy4kcmVmcy5nZW5JbmZvLiRyZWZzLmdlbkluZm9Gb3JtOwogICAgICBQcm9taXNlLmFsbChbYmFzaWNGb3JtLCBnZW5Gb3JtXS5tYXAodGhpcy5nZXRGb3JtUHJvbWlzZSkpLnRoZW4ocmVzID0+IHsKICAgICAgICBjb25zdCB2YWxpZGF0ZVJlc3VsdCA9IHJlcy5ldmVyeShpdGVtID0+ICEhaXRlbSk7CiAgICAgICAgaWYgKHZhbGlkYXRlUmVzdWx0KSB7CiAgICAgICAgICBjb25zdCBnZW5UYWJsZSA9IE9iamVjdC5hc3NpZ24oe30sIGJhc2ljRm9ybS5tb2RlbCwgZ2VuRm9ybS5tb2RlbCk7CiAgICAgICAgICBnZW5UYWJsZS5jb2x1bW5zID0gdGhpcy5jbG91bW5zOwogICAgICAgICAgZ2VuVGFibGUucGFyYW1zID0gewogICAgICAgICAgICB0cmVlQ29kZTogZ2VuVGFibGUudHJlZUNvZGUsCiAgICAgICAgICAgIHRyZWVOYW1lOiBnZW5UYWJsZS50cmVlTmFtZSwKICAgICAgICAgICAgdHJlZVBhcmVudENvZGU6IGdlblRhYmxlLnRyZWVQYXJlbnRDb2RlLAogICAgICAgICAgICBwYXJlbnRNZW51SWQ6IGdlblRhYmxlLnBhcmVudE1lbnVJZAogICAgICAgICAgfTsKICAgICAgICAgIHVwZGF0ZUdlblRhYmxlKGdlblRhYmxlKS50aGVuKHJlcyA9PiB7CiAgICAgICAgICAgIHRoaXMubXNnU3VjY2VzcyhyZXMubXNnKTsKICAgICAgICAgICAgaWYgKHJlcy5jb2RlID09PSAyMDApIHsKICAgICAgICAgICAgICB0aGlzLmNsb3NlKCk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0pOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLm1zZ0Vycm9yKCLooajljZXmoKHpqozmnKrpgJrov4fvvIzor7fph43mlrDmo4Dmn6Xmj5DkuqTlhoXlrrkiKTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIGdldEZvcm1Qcm9taXNlKGZvcm0pIHsKICAgICAgcmV0dXJuIG5ldyBQcm9taXNlKHJlc29sdmUgPT4gewogICAgICAgIGZvcm0udmFsaWRhdGUocmVzID0+IHsKICAgICAgICAgIHJlc29sdmUocmVzKTsKICAgICAgICB9KTsKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOWFs+mXreaMiemSriAqLwogICAgY2xvc2UoKSB7CiAgICAgIHRoaXMuJHN0b3JlLmRpc3BhdGNoKCJ0YWdzVmlldy9kZWxWaWV3IiwgdGhpcy4kcm91dGUpOwogICAgICB0aGlzLiRyb3V0ZXIucHVzaCh7IHBhdGg6ICIvdG9vbC9nZW4iLCBxdWVyeTogeyB0OiBEYXRlLm5vdygpfX0pCiAgICB9CiAgfSwKICBtb3VudGVkKCkgewogICAgY29uc3QgZWwgPSB0aGlzLiRyZWZzLmRyYWdUYWJsZS4kZWwucXVlcnlTZWxlY3RvckFsbCgiLmVsLXRhYmxlX19ib2R5LXdyYXBwZXIgPiB0YWJsZSA+IHRib2R5IilbMF07CiAgICBjb25zdCBzb3J0YWJsZSA9IFNvcnRhYmxlLmNyZWF0ZShlbCwgewogICAgICBoYW5kbGU6ICIuYWxsb3dEcmFnIiwKICAgICAgb25FbmQ6IGV2dCA9PiB7CiAgICAgICAgY29uc3QgdGFyZ2V0Um93ID0gdGhpcy5jbG91bW5zLnNwbGljZShldnQub2xkSW5kZXgsIDEpWzBdOwogICAgICAgIHRoaXMuY2xvdW1ucy5zcGxpY2UoZXZ0Lm5ld0luZGV4LCAwLCB0YXJnZXRSb3cpOwogICAgICAgIGZvciAobGV0IGluZGV4IGluIHRoaXMuY2xvdW1ucykgewogICAgICAgICAgdGhpcy5jbG91bW5zW2luZGV4XS5zb3J0ID0gcGFyc2VJbnQoaW5kZXgpICsgMTsKICAgICAgICB9CiAgICAgIH0KICAgIH0pOwogIH0KfTsK"}, {"version": 3, "sources": ["editTable.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+HA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "editTable.vue", "sourceRoot": "src/views/tool/gen", "sourcesContent": ["<template>\n  <el-card>\n    <el-tabs v-model=\"activeName\">\n      <el-tab-pane label=\"基本信息\" name=\"basic\">\n        <basic-info-form ref=\"basicInfo\" :info=\"info\" />\n      </el-tab-pane>\n      <el-tab-pane label=\"字段信息\" name=\"cloum\">\n        <el-table ref=\"dragTable\" :data=\"cloumns\" row-key=\"columnId\" :max-height=\"tableHeight\">\n          <el-table-column label=\"序号\" type=\"index\" min-width=\"5%\" class-name=\"allowDrag\" />\n          <el-table-column\n            label=\"字段列名\"\n            prop=\"columnName\"\n            min-width=\"10%\"\n            :show-overflow-tooltip=\"true\"\n          />\n          <el-table-column label=\"字段描述\" min-width=\"10%\">\n            <template slot-scope=\"scope\">\n              <el-input v-model=\"scope.row.columnComment\"></el-input>\n            </template>\n          </el-table-column>\n          <el-table-column\n            label=\"物理类型\"\n            prop=\"columnType\"\n            min-width=\"10%\"\n            :show-overflow-tooltip=\"true\"\n          />\n          <el-table-column label=\"Java类型\" min-width=\"11%\">\n            <template slot-scope=\"scope\">\n              <el-select v-model=\"scope.row.javaType\">\n                <el-option label=\"Long\" value=\"Long\" />\n                <el-option label=\"String\" value=\"String\" />\n                <el-option label=\"Integer\" value=\"Integer\" />\n                <el-option label=\"Double\" value=\"Double\" />\n                <el-option label=\"BigDecimal\" value=\"BigDecimal\" />\n                <el-option label=\"Date\" value=\"Date\" />\n              </el-select>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"java属性\" min-width=\"10%\">\n            <template slot-scope=\"scope\">\n              <el-input v-model=\"scope.row.javaField\"></el-input>\n            </template>\n          </el-table-column>\n\n          <el-table-column label=\"插入\" min-width=\"5%\">\n            <template slot-scope=\"scope\">\n              <el-checkbox true-label=\"1\" v-model=\"scope.row.isInsert\"></el-checkbox>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"编辑\" min-width=\"5%\">\n            <template slot-scope=\"scope\">\n              <el-checkbox true-label=\"1\" v-model=\"scope.row.isEdit\"></el-checkbox>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"列表\" min-width=\"5%\">\n            <template slot-scope=\"scope\">\n              <el-checkbox true-label=\"1\" v-model=\"scope.row.isList\"></el-checkbox>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"查询\" min-width=\"5%\">\n            <template slot-scope=\"scope\">\n              <el-checkbox true-label=\"1\" v-model=\"scope.row.isQuery\"></el-checkbox>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"查询方式\" min-width=\"10%\">\n            <template slot-scope=\"scope\">\n              <el-select v-model=\"scope.row.queryType\">\n                <el-option label=\"=\" value=\"EQ\" />\n                <el-option label=\"!=\" value=\"NE\" />\n                <el-option label=\">\" value=\"GT\" />\n                <el-option label=\">=\" value=\"GTE\" />\n                <el-option label=\"<\" value=\"LT\" />\n                <el-option label=\"<=\" value=\"LTE\" />\n                <el-option label=\"LIKE\" value=\"LIKE\" />\n                <el-option label=\"BETWEEN\" value=\"BETWEEN\" />\n              </el-select>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"必填\" min-width=\"5%\">\n            <template slot-scope=\"scope\">\n              <el-checkbox true-label=\"1\" v-model=\"scope.row.isRequired\"></el-checkbox>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"显示类型\" min-width=\"12%\">\n            <template slot-scope=\"scope\">\n              <el-select v-model=\"scope.row.htmlType\">\n                <el-option label=\"文本框\" value=\"input\" />\n                <el-option label=\"文本域\" value=\"textarea\" />\n                <el-option label=\"下拉框\" value=\"select\" />\n                <el-option label=\"单选框\" value=\"radio\" />\n                <el-option label=\"复选框\" value=\"checkbox\" />\n                <el-option label=\"日期控件\" value=\"datetime\" />\n                <el-option label=\"图片上传\" value=\"imageUpload\" />\n                <el-option label=\"文件上传\" value=\"fileUpload\" />\n                <el-option label=\"富文本控件\" value=\"editor\" />\n              </el-select>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"字典类型\" min-width=\"12%\">\n            <template slot-scope=\"scope\">\n              <el-select v-model=\"scope.row.dictType\" clearable filterable placeholder=\"请选择\">\n                <el-option\n                  v-for=\"dict in dictOptions\"\n                  :key=\"dict.dictType\"\n                  :label=\"dict.dictName\"\n                  :value=\"dict.dictType\">\n                  <span style=\"float: left\">{{ dict.dictName }}</span>\n                  <span style=\"float: right; color: #8492a6; font-size: 13px\">{{ dict.dictType }}</span>\n              </el-option>\n              </el-select>\n            </template>\n          </el-table-column>\n        </el-table>\n      </el-tab-pane>\n      <el-tab-pane label=\"生成信息\" name=\"genInfo\">\n        <gen-info-form ref=\"genInfo\" :info=\"info\" :tables=\"tables\" :menus=\"menus\"/>\n      </el-tab-pane>\n    </el-tabs>\n    <el-form label-width=\"100px\">\n      <el-form-item style=\"text-align: center;margin-left:-100px;margin-top:10px;\">\n        <el-button type=\"primary\" @click=\"submitForm()\">提交</el-button>\n        <el-button @click=\"close()\">返回</el-button>\n      </el-form-item>\n    </el-form>\n  </el-card>\n</template>\n<script>\nimport { getGenTable, updateGenTable } from \"@/api/tool/gen\";\nimport { optionselect as getDictOptionselect } from \"@/api/system/dict/type\";\nimport { listMenu as getMenuTreeselect } from \"@/api/system/menu\";\nimport basicInfoForm from \"./basicInfoForm\";\nimport genInfoForm from \"./genInfoForm\";\nimport Sortable from 'sortablejs'\n\nexport default {\n  name: \"GenEdit\",\n  components: {\n    basicInfoForm,\n    genInfoForm\n  },\n  data() {\n    return {\n      // 选中选项卡的 name\n      activeName: \"cloum\",\n      // 表格的高度\n      tableHeight: document.documentElement.scrollHeight - 245 + \"px\",\n      // 表信息\n      tables: [],\n      // 表列信息\n      cloumns: [],\n      // 字典信息\n      dictOptions: [],\n      // 菜单信息\n      menus: [],\n      // 表详细信息\n      info: {}\n    };\n  },\n  created() {\n    const tableId = this.$route.params && this.$route.params.tableId;\n    if (tableId) {\n      // 获取表详细信息\n      getGenTable(tableId).then(res => {\n        this.cloumns = res.data.rows;\n        this.info = res.data.info;\n        this.tables = res.data.tables;\n      });\n      /** 查询字典下拉列表 */\n      getDictOptionselect().then(response => {\n        this.dictOptions = response.data;\n      });\n      /** 查询菜单下拉列表 */\n      getMenuTreeselect().then(response => {\n        this.menus = this.handleTree(response.data, \"menuId\");\n      });\n    }\n  },\n  methods: {\n    /** 提交按钮 */\n    submitForm() {\n      const basicForm = this.$refs.basicInfo.$refs.basicInfoForm;\n      const genForm = this.$refs.genInfo.$refs.genInfoForm;\n      Promise.all([basicForm, genForm].map(this.getFormPromise)).then(res => {\n        const validateResult = res.every(item => !!item);\n        if (validateResult) {\n          const genTable = Object.assign({}, basicForm.model, genForm.model);\n          genTable.columns = this.cloumns;\n          genTable.params = {\n            treeCode: genTable.treeCode,\n            treeName: genTable.treeName,\n            treeParentCode: genTable.treeParentCode,\n            parentMenuId: genTable.parentMenuId\n          };\n          updateGenTable(genTable).then(res => {\n            this.msgSuccess(res.msg);\n            if (res.code === 200) {\n              this.close();\n            }\n          });\n        } else {\n          this.msgError(\"表单校验未通过，请重新检查提交内容\");\n        }\n      });\n    },\n    getFormPromise(form) {\n      return new Promise(resolve => {\n        form.validate(res => {\n          resolve(res);\n        });\n      });\n    },\n    /** 关闭按钮 */\n    close() {\n      this.$store.dispatch(\"tagsView/delView\", this.$route);\n      this.$router.push({ path: \"/tool/gen\", query: { t: Date.now()}})\n    }\n  },\n  mounted() {\n    const el = this.$refs.dragTable.$el.querySelectorAll(\".el-table__body-wrapper > table > tbody\")[0];\n    const sortable = Sortable.create(el, {\n      handle: \".allowDrag\",\n      onEnd: evt => {\n        const targetRow = this.cloumns.splice(evt.oldIndex, 1)[0];\n        this.cloumns.splice(evt.newIndex, 0, targetRow);\n        for (let index in this.cloumns) {\n          this.cloumns[index].sort = parseInt(index) + 1;\n        }\n      }\n    });\n  }\n};\n</script>\n"]}]}