{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Editor/index.vue?vue&type=style&index=0&id=7480c5e0&lang=css", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Editor/index.vue", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/css-loader/dist/cjs.js", "mtime": 1751171659443}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/loaders/stylePostLoader.js", "mtime": 1751171659924}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/postcss-loader/src/index.js", "mtime": 1751171659554}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/Editor", "sourcesContent": ["<template>\n    <div class=\"editor\" ref=\"editor\" :style=\"styles\"></div>\n</template>\n\n<script>\nimport Quill from \"quill\";\nimport \"quill/dist/quill.core.css\";\nimport \"quill/dist/quill.snow.css\";\nimport \"quill/dist/quill.bubble.css\";\n\nexport default {\n  name: \"Editor\",\n  props: {\n    /* 编辑器的内容 */\n    value: {\n      type: String,\n      default: \"\",\n    },\n    /* 高度 */\n    height: {\n      type: Number,\n      default: null,\n    },\n    /* 最小高度 */\n    minHeight: {\n      type: Number,\n      default: null,\n    },\n  },\n  data() {\n    return {\n      Quill: null,\n      currentValue: \"\",\n      options: {\n        theme: \"snow\",\n        bounds: document.body,\n        debug: \"warn\",\n        modules: {\n          // 工具栏配置\n          toolbar: [\n            [\"bold\", \"italic\", \"underline\", \"strike\"],       // 加粗 斜体 下划线 删除线\n            [\"blockquote\", \"code-block\"],                    // 引用  代码块\n            [{ list: \"ordered\" }, { list: \"bullet\" }],       // 有序、无序列表\n            [{ indent: \"-1\" }, { indent: \"+1\" }],            // 缩进\n            [{ size: [\"small\", false, \"large\", \"huge\"] }],   // 字体大小\n            [{ header: [1, 2, 3, 4, 5, 6, false] }],         // 标题\n            [{ color: [] }, { background: [] }],             // 字体颜色、字体背景颜色\n            [{ align: [] }],                                 // 对齐方式\n            [\"clean\"],                                       // 清除文本格式\n            [\"link\", \"image\", \"video\"]                       // 链接、图片、视频\n          ],\n        },\n        placeholder: \"请输入内容\",\n        readOnly: false,\n      },\n    };\n  },\n  computed: {\n    styles() {\n      let style = {};\n      if (this.minHeight) {\n        style.minHeight = `${this.minHeight}px`;\n      }\n      if (this.height) {\n        style.height = `${this.height}px`;\n      }\n      return style;\n    },\n  },\n  watch: {\n    value: {\n      handler(val) {\n        if (val !== this.currentValue) {\n          this.currentValue = val === null ? \"\" : val;\n          if (this.Quill) {\n            this.Quill.pasteHTML(this.currentValue);\n          }\n        }\n      },\n      immediate: true,\n    },\n  },\n  mounted() {\n    this.init();\n  },\n  beforeDestroy() {\n    this.Quill = null;\n  },\n  methods: {\n    init() {\n      const editor = this.$refs.editor;\n      this.Quill = new Quill(editor, this.options);\n      this.Quill.pasteHTML(this.currentValue);\n      this.Quill.on(\"text-change\", (delta, oldDelta, source) => {\n        const html = this.$refs.editor.children[0].innerHTML;\n        const text = this.Quill.getText();\n        const quill = this.Quill;\n        this.currentValue = html;\n        this.$emit(\"input\", html);\n        this.$emit(\"on-change\", { html, text, quill });\n      });\n      this.Quill.on(\"text-change\", (delta, oldDelta, source) => {\n        this.$emit(\"on-text-change\", delta, oldDelta, source);\n      });\n      this.Quill.on(\"selection-change\", (range, oldRange, source) => {\n        this.$emit(\"on-selection-change\", range, oldRange, source);\n      });\n      this.Quill.on(\"editor-change\", (eventName, ...args) => {\n        this.$emit(\"on-editor-change\", eventName, ...args);\n      });\n    },\n  },\n};\n</script>\n\n<style>\n.editor, .ql-toolbar {\n  white-space: pre-wrap!important;\n  line-height: normal !important;\n}\n.quill-img {\n  display: none;\n}\n.ql-snow .ql-tooltip[data-mode=\"link\"]::before {\n  content: \"请输入链接地址:\";\n}\n.ql-snow .ql-tooltip.ql-editing a.ql-action::after {\n  border-right: 0px;\n  content: \"保存\";\n  padding-right: 0px;\n}\n\n.ql-snow .ql-tooltip[data-mode=\"video\"]::before {\n  content: \"请输入视频地址:\";\n}\n\n.ql-snow .ql-picker.ql-size .ql-picker-label::before,\n.ql-snow .ql-picker.ql-size .ql-picker-item::before {\n  content: \"14px\";\n}\n.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=\"small\"]::before,\n.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=\"small\"]::before {\n  content: \"10px\";\n}\n.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=\"large\"]::before,\n.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=\"large\"]::before {\n  content: \"18px\";\n}\n.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=\"huge\"]::before,\n.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=\"huge\"]::before {\n  content: \"32px\";\n}\n\n.ql-snow .ql-picker.ql-header .ql-picker-label::before,\n.ql-snow .ql-picker.ql-header .ql-picker-item::before {\n  content: \"文本\";\n}\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"1\"]::before,\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"1\"]::before {\n  content: \"标题1\";\n}\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"2\"]::before,\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"2\"]::before {\n  content: \"标题2\";\n}\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"3\"]::before,\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"3\"]::before {\n  content: \"标题3\";\n}\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"4\"]::before,\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"4\"]::before {\n  content: \"标题4\";\n}\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"5\"]::before,\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"5\"]::before {\n  content: \"标题5\";\n}\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"6\"]::before,\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"6\"]::before {\n  content: \"标题6\";\n}\n\n.ql-snow .ql-picker.ql-font .ql-picker-label::before,\n.ql-snow .ql-picker.ql-font .ql-picker-item::before {\n  content: \"标准字体\";\n}\n.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=\"serif\"]::before,\n.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=\"serif\"]::before {\n  content: \"衬线字体\";\n}\n.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=\"monospace\"]::before,\n.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=\"monospace\"]::before {\n  content: \"等宽字体\";\n}\n</style>"]}]}