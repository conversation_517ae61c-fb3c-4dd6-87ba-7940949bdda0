{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/project/report/form.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/project/report/form.vue", "mtime": 1753528817894}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICBnZXRSZXBvcnQsDQogIGFkZFJlcG9ydCwNCiAgdXBkYXRlUmVwb3J0LA0KICBjaGVja05hbWVVbmlxdWUNCn0gZnJvbSAiQC9hcGkvcHJvamVjdC9yZXBvcnQiOw0KaW1wb3J0IEZpbGVVcGxvYWQgZnJvbSAiQC9jb21wb25lbnRzL0ZpbGVVcGxvYWQiOw0KaW1wb3J0IGZsb3dhYmxlIGZyb20gJ0Avdmlld3MvZmxvd2FibGUvdGFzay9yZWNvcmQvaW5kZXgnDQppbXBvcnQgeyByZWdpb25EYXRhLCBDb2RlVG9UZXh0LCBUZXh0VG9Db2RlIH0gZnJvbSAiZWxlbWVudC1jaGluYS1hcmVhLWRhdGEiOw0KaW1wb3J0IHByaW50IGZyb20gInByaW50LWpzIjsNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogIlJlcG9ydCIsDQogIGNvbXBvbmVudHM6IHsNCiAgICBmbG93YWJsZSwNCiAgICBGaWxlVXBsb2FkLA0KICAgIHByaW50DQogIH0sDQogIGRhdGEoKSB7DQogICAgdmFyIHRoYXQgPSB0aGlzOw0KICAgIHZhciBpbmZvVHlwZVZhbHVlVmFsaSA9IChydWxlLCB2YWx1ZSwgY2FsbGJhY2spID0+IHsNCiAgICAgIGlmICh0aGF0LmZvcm0uaW5mb1R5cGUuaW5kZXhPZignMScpID49IDAgJiYgIXRoYXQuZm9ybS5zY2FuRmlsZSkgew0KICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoIumCrueuseWcsOWdgOW/heWhqyIpKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KICAgICAgY2FsbGJhY2soKTsNCiAgICB9Ow0KICAgIHZhciBpbmZvVHlwZVZhbHVlVmFsaTIgPSAocnVsZSwgdmFsdWUsIGNhbGxiYWNrKSA9PiB7DQogICAgICBpZiAodGhhdC5mb3JtLmluZm9UeXBlLmluZGV4T2YoJzInKSA+PSAwICYmICF0aGF0LmZvcm0uc2VuZEFkZHJlc3MpIHsNCiAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCLmlLbku7blnLDlnYDlv4XloasiKSk7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCiAgICAgIGNhbGxiYWNrKCk7DQogICAgfTsNCiAgICB2YXIgbmFtZVZhbGkgPSAocnVsZSwgdmFsdWUsIGNhbGxiYWNrKSA9PiB7DQogICAgICBpZiAoIXRoYXQuZm9ybS5wcm9qZWN0TmFtZSkgew0KICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoIumhueebruWQjeensOW/heWhqyIpKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGlmICgvXHMrL2cudGVzdCh0aGF0LmZvcm0ucHJvamVjdE5hbWUpKSB7DQogICAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCLpobnnm67lkI3np7DkuI3op4TojIMiKSk7DQogICAgICAgICAgcmV0dXJuOw0KICAgICAgICB9DQogICAgICAgIGNoZWNrTmFtZVVuaXF1ZSh7IHByb2plY3ROYW1lOiB0aGF0LmZvcm0ucHJvamVjdE5hbWUsIHByb2plY3RJZDogdGhhdC5mb3JtLnByb2plY3RJZCB9KS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICAgIGlmIChyZXNwb25zZS5kYXRhID09IDApIHsNCiAgICAgICAgICAgIGNhbGxiYWNrKCk7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcigi6aG555uu5ZCN56ew5bey5a2Y5ZyoIikpOw0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCiAgICAgIH0NCiAgICB9Ow0KICAgIHZhciBjb2RlVmFsaSA9IChydWxlLCB2YWx1ZSwgY2FsbGJhY2spID0+IHsNCiAgICAgIGlmICghdGhhdC5mb3JtLnByb2plY3RObykgew0KICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoIumhueebrue8luWPt+W/heWhqyIpKTsNCiAgICAgIH0gZWxzZSBpZiAoL1xzKy9nLnRlc3QodGhhdC5mb3JtLnByb2plY3RObykpIHsNCiAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCLpobnnm67nvJblj7fkuI3op4TojIMiKSk7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCiAgICAgIGNhbGxiYWNrKCk7DQogICAgfTsNCiAgICB2YXIgb3BlbkRhdGVWYWxpID0gKHJ1bGUsIHZhbHVlLCBjYWxsYmFjaykgPT4gew0KICAgICAgaWYgKCF0aGF0LmZvcm0ub3BlbkRhdGUpIHsNCiAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCLlvIDmoIfml6XmnJ/lv4XloasiKSk7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0gZWxzZSBpZiAodmFsdWUgPT09ICLml6AiKSB7DQogICAgICAgIGNhbGxiYWNrKCk7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0gZWxzZSBpZiAoIS9eXGR7NH0tXGR7Mn0tXGR7Mn0kLy50ZXN0KHZhbHVlKSkgew0KICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoIuW8gOagh+aXpeacn+agvOW8j+S4jeWQiOazle+8jOekuuS+izIwMjUtMDEtMDEiKSk7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCiAgICAgIGNhbGxiYWNrKCk7DQogICAgfTsNCiAgICB2YXIgaGFuZ0RhdGVWYWxpID0gKHJ1bGUsIHZhbHVlLCBjYWxsYmFjaykgPT4gew0KICAgICAgaWYgKCF0aGF0LmZvcm0uaGFuZ0RhdGUpIHsNCiAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCLmjILnvZHml6XmnJ/lv4XloasiKSk7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0gZWxzZSBpZiAodmFsdWUgPT09ICLml6AiKSB7DQogICAgICAgIGNhbGxiYWNrKCk7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0gZWxzZSBpZiAoIS9eXGR7NH0tXGR7Mn0tXGR7Mn0kLy50ZXN0KHZhbHVlKSkgew0KICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoIuaMgue9keaXpeacn+agvOW8j+S4jeWQiOazle+8jOekuuS+izIwMjUtMDEtMDEiKSk7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCiAgICAgIGNhbGxiYWNrKCk7DQogICAgfTsNCiAgICByZXR1cm4gew0KICAgICAgLy8g6YGu572p5bGCDQogICAgICBsb2FkaW5nOiB0cnVlLA0KICAgICAgLy8g5pON5L2c57G75Z6L5a2X5YW4DQogICAgICBvcGVyYXRpb25UeXBlT3B0aW9uczogW10sDQogICAgICAvLyDlrqHmoLjnirbmgIHlrZflhbgNCiAgICAgIGF1ZGl0U3RhdHVzT3B0aW9uczogW10sDQogICAgICAvLyDnvJbovpHnirbmgIHlrZflhbgNCiAgICAgIGVkaXRTdGF0dXNPcHRpb25zOiBbXSwNCiAgICAgIC8vIOaLm+agh+aWueW8j+Wtl+WFuA0KICAgICAgYmlkZGluZ1R5cGVPcHRpb25zOiBbXSwNCiAgICAgIC8vIOaKleagh+S6p+WTgeWei+WPt+Wtl+WFuA0KICAgICAgbW9kZWxPcHRpb25zOiBbXSwNCiAgICAgIG1vZGVsT3B0aW9uMTogW10sDQogICAgICAvLyDmiYDpnIDotYTmlpnlrZflhbgNCiAgICAgIHJlcXVpcmVJbmZvT3B0aW9uczogW10sDQogICAgICByZXF1aXJlSW5mb09wdGlvbjE6IFtdLA0KICAgICAgLy8g6LWE5paZ57G75Z6L5a2X5YW4DQogICAgICBpbmZvVHlwZU9wdGlvbnM6IFtdLA0KICAgICAgLy8g5omA5bGe55yB5Lu95a2X5YW4DQogICAgICBiZWxvbmdQcm92aW5jZU9wdGlvbnM6IFtdLA0KICAgICAgYmVsb25nUHJvdmluY2VPcHRpb25zMTogW10sDQogICAgICAvLyDllK7lkI7lubTpmZANCiAgICAgIGFmdGVyU2FsZVllYXJPcHRpb25zOiBbXSwNCiAgICAgIGFmdGVyU2FsZVllYXJPcHRpb25zMTogW10sDQogICAgICBzcGVjT3B0aW9uczogW10sDQogICAgICBzcGVjT3B0aW9uMTogW10sDQogICAgICAvLyDooajljZXlj4LmlbANCiAgICAgIGZvcm06IHt9LA0KICAgICAgLy8g6KGo5Y2V5qCh6aqMDQogICAgICAvLyDooajljZXmoKHpqowNCiAgICAgIHJ1bGVzOiB7DQogICAgICAgIG9wZXJhdGlvblR5cGU6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5pON5L2c57G75Z6L5b+F6YCJIiB9XSwNCiAgICAgICAgcHJvamVjdE5vOiBbDQogICAgICAgICAgeyB2YWxpZGF0b3I6IGNvZGVWYWxpLCByZXF1aXJlZDogdHJ1ZSwgdHJpZ2dlcjogImJsdXIiIH0sDQogICAgICAgIF0sDQogICAgICAgIHByb2plY3ROYW1lOiBbDQogICAgICAgICAgeyB2YWxpZGF0b3I6IG5hbWVWYWxpLCByZXF1aXJlZDogdHJ1ZSwgdHJpZ2dlcjogImJsdXIiIH0sDQogICAgICAgIF0sDQogICAgICAgIGFkZHJlc3M6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+m57uG5Zyw5Z2A5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0sDQogICAgICAgIF0sDQogICAgICAgIGJpZGRpbmdDb21wYW55OiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuaLm+agh+WNleS9jeS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9LA0KICAgICAgICBdLA0KICAgICAgICBvcGVuRGF0ZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIHZhbGlkYXRvcjogb3BlbkRhdGVWYWxpLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgXSwNCiAgICAgICAgYWZ0ZXJTYWxlWWVhcjogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLllK7lkI7lubTpmZDkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgXSwNCiAgICAgICAgaGFuZ0RhdGU6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCB2YWxpZGF0b3I6IGhhbmdEYXRlVmFsaSwgdHJpZ2dlcjogImJsdXIiIH0sDQogICAgICAgIF0sDQogICAgICAgIGJlbG9uZ1Byb3ZpbmNlOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuWUruWQjuW5tOmZkOS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9LA0KICAgICAgICBdLA0KICAgICAgICBkaXN0cmlidXRvcjogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLmiYDlsZ7nu4/plIDllYbkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgXSwNCiAgICAgICAgc2NhbkZpbGU6IFsNCiAgICAgICAgICB7IHZhbGlkYXRvcjogaW5mb1R5cGVWYWx1ZVZhbGksIHRyaWdnZXI6ICJibHVyIiB9LA0KICAgICAgICBdLA0KICAgICAgICBzZW5kQWRkcmVzczogWw0KICAgICAgICAgIHsgdmFsaWRhdG9yOiBpbmZvVHlwZVZhbHVlVmFsaTIsIHRyaWdnZXI6ICJibHVyIiB9LA0KICAgICAgICBdLA0KICAgICAgICBtb2RlbDogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLmipXmoIfkuqflk4Hlnovlj7flv4XpgIkiIH0sDQogICAgICAgIF0sDQogICAgICAgIHNwZWM6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5oqV5qCH5Lqn5ZOB6KeE5qC85b+F6YCJIiB9LA0KICAgICAgICBdLA0KICAgICAgICBwcm92aW5jZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLpobnnm67miYDlnKjlnLDlv4XpgIkiIH0sDQogICAgICAgIF0sDQogICAgICAgIGluZm9UeXBlOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIui1hOaWmeaOpeaUtuaWueW8j+W/hemAiSIsIHRyaWdnZXI6ICJjaGFuZ2UiIH0sDQogICAgICAgIF0sDQogICAgICAgIGJpZGRpbmdDb250YWN0OiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuaLm+agh+WNleS9jeiBlOezu+S6ui/ogZTns7vnlLXor53lv4XloasiIH0sDQogICAgICAgIF0sDQogICAgICAgIGF1dGhDb250YWN0OiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuaOiOadg+WFrOWPuOiBlOezu+S6ui/ogZTns7vnlLXor53lv4XloasiIH0sDQogICAgICAgIF0NCiAgICAgIH0sDQogICAgICBvcHRpb25zOiByZWdpb25EYXRhLA0KICAgICAgc2VsZWN0ZWRPcHRpb25zOiBbXSwNCiAgICAgIHF1ZXJ5QXJlYTogW10sDQogICAgICAvL+W3peS9nOa1geWPguaVsA0KICAgICAgZmluaXNoZWQ6ICdmYWxzZScsDQogICAgICB0YXNrSWQ6IHVuZGVmaW5lZCwNCiAgICAgIHByb2NJbnNJZDogdW5kZWZpbmVkLA0KICAgICAgYnVzaW5lc3NLZXk6IHVuZGVmaW5lZCwNCiAgICAgIGF1ZGl0OiBmYWxzZSwNCiAgICAgIGZvcm1FZGl0OiBmYWxzZSwNCiAgICAgIC8v5bel5L2c5rWB5Y+C5pWwZW5kDQogICAgICBhdXRoQ29tcGFueXM6IFtdLA0KICAgIH07DQogIH0sDQogIGFjdGl2YXRlZCgpIHsNCg0KICAgIHRoaXMucmVzZXQoKTsNCiAgICAvL3RoaXMuZm9ybS5wcm9qZWN0Tm8gPSBtb21lbnQobmV3IERhdGUoKSkuZm9ybWF0KCdZWVlZTU1EREhIbW0nKTsNCiAgICB0aGlzLnRhc2tJZCA9IHRoaXMuJHJvdXRlLnF1ZXJ5ICYmIHRoaXMuJHJvdXRlLnF1ZXJ5LnRhc2tJZDsNCiAgICB0aGlzLnByb2NJbnNJZCA9IHRoaXMuJHJvdXRlLnF1ZXJ5ICYmIHRoaXMuJHJvdXRlLnF1ZXJ5LnByb2NJbnNJZDsNCiAgICB0aGlzLmZpbmlzaGVkID0gdGhpcy4kcm91dGUucXVlcnkgJiYgdGhpcy4kcm91dGUucXVlcnkuZmluaXNoZWQ7DQogICAgdGhpcy5idXNpbmVzc0tleSA9IHRoaXMuJHJvdXRlLnF1ZXJ5ICYmIHRoaXMuJHJvdXRlLnF1ZXJ5LmJ1c2luZXNzS2V5Ow0KICAgIGxldCBlZGl0ID0gdGhpcy4kcm91dGUucXVlcnkgJiYgdGhpcy4kcm91dGUucXVlcnkuZm9ybUVkaXQ7DQogICAgaWYgKGVkaXQgPT0gInRydWUiKSB7DQogICAgICB0aGlzLmZvcm1FZGl0ID0gdHJ1ZTsNCiAgICB9IGVsc2Ugew0KICAgICAgdGhpcy5mb3JtRWRpdCA9IGZhbHNlOw0KICAgIH0NCiAgICBpZiAodGhpcy5idXNpbmVzc0tleSkgew0KICAgICAgaWYgKHRoaXMuZmluaXNoZWQgPT0gImZhbHNlIiAmJiAhdGhpcy5mb3JtRWRpdCkgew0KICAgICAgICB0aGlzLmF1ZGl0ID0gdHJ1ZTsNCiAgICAgIH0NCiAgICAgIHRoaXMuZ2V0UmVwb3J0SW5mbyh0aGlzLmJ1c2luZXNzS2V5KTsNCiAgICB9DQogICAgY29uc29sZS5sb2coIj09PT09PT09cHJvamVjdD09PT09PT09PT5hY3RpdmF0ZWQ+Zm9ybUVkaXQ+PiIgKyB0aGlzLmZvcm1FZGl0KTsNCiAgfSwNCiAgY3JlYXRlZCgpIHsNCg0KICAgIHRoaXMucmVzZXQoKTsNCiAgICAvL3RoaXMuZm9ybS5wcm9qZWN0Tm8gPSBtb21lbnQobmV3IERhdGUoKSkuZm9ybWF0KCdZWVlZTU1EREhIbW0nKTsNCiAgICB0aGlzLnRhc2tJZCA9IHRoaXMuJHJvdXRlLnF1ZXJ5ICYmIHRoaXMuJHJvdXRlLnF1ZXJ5LnRhc2tJZDsNCiAgICB0aGlzLnByb2NJbnNJZCA9IHRoaXMuJHJvdXRlLnF1ZXJ5ICYmIHRoaXMuJHJvdXRlLnF1ZXJ5LnByb2NJbnNJZDsNCiAgICB0aGlzLmZpbmlzaGVkID0gdGhpcy4kcm91dGUucXVlcnkgJiYgdGhpcy4kcm91dGUucXVlcnkuZmluaXNoZWQ7DQogICAgdGhpcy5idXNpbmVzc0tleSA9IHRoaXMuJHJvdXRlLnF1ZXJ5ICYmIHRoaXMuJHJvdXRlLnF1ZXJ5LmJ1c2luZXNzS2V5Ow0KICAgIGxldCBlZGl0ID0gdGhpcy4kcm91dGUucXVlcnkgJiYgdGhpcy4kcm91dGUucXVlcnkuZm9ybUVkaXQ7DQogICAgaWYgKGVkaXQgPT0gInRydWUiKSB7DQogICAgICB0aGlzLmZvcm1FZGl0ID0gdHJ1ZTsNCiAgICB9IGVsc2Ugew0KICAgICAgdGhpcy5mb3JtRWRpdCA9IGZhbHNlOw0KICAgIH0NCiAgICBpZiAodGhpcy5idXNpbmVzc0tleSkgew0KICAgICAgaWYgKHRoaXMuZmluaXNoZWQgPT0gImZhbHNlIiAmJiAhdGhpcy5mb3JtRWRpdCkgew0KICAgICAgICB0aGlzLmF1ZGl0ID0gdHJ1ZTsNCiAgICAgIH0NCiAgICAgIHRoaXMuZ2V0UmVwb3J0SW5mbyh0aGlzLmJ1c2luZXNzS2V5KTsNCiAgICB9DQogICAgLy8gdGhpcy5hdWRpdCA9IHRydWU7DQogICAgY29uc29sZS5sb2coIj09PT09PT09PXByb2plY3Q9PT09PT09PT5jcmVhdGVkPj5mb3JtRWRpdD4iICsgdGhpcy5mb3JtRWRpdCk7DQoNCiAgICB0aGlzLmdldERpY3RzKCJwcl9vcGVyYXRpb25fdHlwZSIpLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICB0aGlzLm9wZXJhdGlvblR5cGVPcHRpb25zID0gcmVzcG9uc2UuZGF0YTsNCiAgICB9KTsNCiAgICB0aGlzLmdldERpY3RzKCJwcl9hdWRpdF9zdGF0dXMiKS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgdGhpcy5hdWRpdFN0YXR1c09wdGlvbnMgPSByZXNwb25zZS5kYXRhOw0KICAgIH0pOw0KICAgIHRoaXMuZ2V0RGljdHMoInByX2VkaXRfc3RhdHVzIikudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgIHRoaXMuZWRpdFN0YXR1c09wdGlvbnMgPSByZXNwb25zZS5kYXRhOw0KICAgIH0pOw0KICAgIC8vIHRoaXMuZ2V0RGljdHMoInByX2JpZGRpbmdfdHlwZSIpLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgLy8gICB0aGlzLmJpZGRpbmdUeXBlT3B0aW9ucyA9IHJlc3BvbnNlLmRhdGE7DQogICAgLy8gfSk7DQogICAgdGhpcy5nZXREaWN0cygicHJfbW9kZWwiKS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgdGhpcy5tb2RlbE9wdGlvbjEgPSByZXNwb25zZS5kYXRhOw0KICAgICAgdmFyIG9wdCA9IFtdOw0KICAgICAgcmVzcG9uc2UuZGF0YS5mb3JFYWNoKChlbGVtLCBpbmRleCkgPT4gew0KICAgICAgICB2YXIgb2JqID0ge307DQogICAgICAgIG9iai5sYWJlbCA9IGVsZW0uZGljdExhYmVsOw0KICAgICAgICBvYmoudmFsdWUgPSBlbGVtLmRpY3RWYWx1ZTsNCiAgICAgICAgb3B0LnB1c2gob2JqKTsNCiAgICAgIH0pOw0KICAgICAgdGhpcy5tb2RlbE9wdGlvbnMgPSBvcHQ7DQogICAgfSk7DQogICAgdGhpcy5nZXREaWN0cygicHJfc3BlYyIpLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICB0aGlzLnNwZWNPcHRpb24xID0gcmVzcG9uc2UuZGF0YTsNCiAgICAgIHZhciBvcHQgPSBbXTsNCiAgICAgIHJlc3BvbnNlLmRhdGEuZm9yRWFjaCgoZWxlbSwgaW5kZXgpID0+IHsNCiAgICAgICAgdmFyIG9iaiA9IHt9Ow0KICAgICAgICBvYmoubGFiZWwgPSBlbGVtLmRpY3RMYWJlbDsNCiAgICAgICAgb2JqLnZhbHVlID0gZWxlbS5kaWN0VmFsdWU7DQogICAgICAgIG9wdC5wdXNoKG9iaik7DQogICAgICB9KTsNCiAgICAgIHRoaXMuc3BlY09wdGlvbnMgPSBvcHQ7DQogICAgfSk7DQogICAgdGhpcy5nZXREaWN0cygicHJfaW5mbyIpLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICB0aGlzLnJlcXVpcmVJbmZvT3B0aW9uMSA9IHJlc3BvbnNlLmRhdGE7DQogICAgICB2YXIgb3B0ID0gW107DQogICAgICByZXNwb25zZS5kYXRhLmZvckVhY2goKGVsZW0sIGluZGV4KSA9PiB7DQogICAgICAgIHZhciBvYmogPSB7fTsNCiAgICAgICAgb2JqLmxhYmVsID0gZWxlbS5kaWN0TGFiZWw7DQogICAgICAgIG9iai52YWx1ZSA9IGVsZW0uZGljdFZhbHVlOw0KICAgICAgICBvcHQucHVzaChvYmopOw0KICAgICAgfSk7DQogICAgICB0aGlzLnJlcXVpcmVJbmZvT3B0aW9ucyA9IG9wdDsNCiAgICB9KTsNCiAgICB0aGlzLmdldERpY3RzKCJwcl9wcm92aW5jZSIpLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICB0aGlzLmJlbG9uZ1Byb3ZpbmNlT3B0aW9uczEgPSByZXNwb25zZS5kYXRhOw0KICAgICAgdmFyIG9wdCA9IFtdOw0KICAgICAgcmVzcG9uc2UuZGF0YS5mb3JFYWNoKChlbGVtLCBpbmRleCkgPT4gew0KICAgICAgICB2YXIgb2JqID0ge307DQogICAgICAgIG9iai5sYWJlbCA9IGVsZW0uZGljdExhYmVsOw0KICAgICAgICBvYmoudmFsdWUgPSBlbGVtLmRpY3RWYWx1ZTsNCiAgICAgICAgb3B0LnB1c2gob2JqKTsNCiAgICAgIH0pOw0KICAgICAgdGhpcy5iZWxvbmdQcm92aW5jZU9wdGlvbnMgPSBvcHQ7DQogICAgfSk7DQogICAgdGhpcy5nZXREaWN0cygicHJfYWZ0ZXJfc2FsZV95ZWFyIikudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgIHRoaXMuYWZ0ZXJTYWxlWWVhck9wdGlvbnMxID0gcmVzcG9uc2UuZGF0YTsNCiAgICAgIHZhciBvcHQgPSBbXTsNCiAgICAgIHJlc3BvbnNlLmRhdGEuZm9yRWFjaCgoZWxlbSwgaW5kZXgpID0+IHsNCiAgICAgICAgdmFyIG9iaiA9IHt9Ow0KICAgICAgICBvYmoubGFiZWwgPSBlbGVtLmRpY3RMYWJlbDsNCiAgICAgICAgb2JqLnZhbHVlID0gZWxlbS5kaWN0VmFsdWU7DQogICAgICAgIG9wdC5wdXNoKG9iaik7DQogICAgICB9KTsNCiAgICAgIHRoaXMuYWZ0ZXJTYWxlWWVhck9wdGlvbnMgPSBvcHQ7DQogICAgfSk7DQogICAgdGhpcy5nZXREaWN0cygicHJfZGF0YV90eXBlIikudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgIHRoaXMuaW5mb1R5cGVPcHRpb25zID0gcmVzcG9uc2UuZGF0YTsNCiAgICB9KTsNCiAgICAvL+m7mOiupOaKpeWkhw0KICAgIHRoaXMuZm9ybS5vcGVyYXRpb25UeXBlID0gJzEnOw0KICB9LA0KICBtZXRob2RzOiB7DQoNCiAgICAvLyDooajljZXph43nva4NCiAgICByZXNldCgpIHsNCiAgICAgIHRoaXMuZm9ybSA9IHsNCiAgICAgICAgcHJvamVjdElkOiBudWxsLA0KICAgICAgICBwcm9qZWN0Tm86IG51bGwsDQogICAgICAgIHByb2plY3ROYW1lOiBudWxsLA0KICAgICAgICBvcGVyYXRpb25UeXBlOiAxLA0KICAgICAgICBhdWRpdFN0YXR1czogIjEiLA0KICAgICAgICByZWplY3RSZWFzb246IG51bGwsDQogICAgICAgIHByb3ZpbmNlOiBudWxsLA0KICAgICAgICBjaXR5OiBudWxsLA0KICAgICAgICBkaXN0cmljdDogbnVsbCwNCiAgICAgICAgYWRkcmVzczogbnVsbCwNCiAgICAgICAgZWRpdFN0YXR1czogIjAiLA0KICAgICAgICBiZWxvbmdVc2VyOiBudWxsLA0KICAgICAgICBiaWRkaW5nQ29tcGFueTogbnVsbCwNCiAgICAgICAgb3BlbkRhdGU6IG51bGwsDQogICAgICAgIGJlbG9uZ1Byb3ZpbmNlOiBudWxsLA0KICAgICAgICBhZnRlclNhbGVZZWFyOiBudWxsLA0KICAgICAgICBoYW5nRGF0ZTogbnVsbCwNCiAgICAgICAgYmlkZGluZ1R5cGU6IG51bGwsDQogICAgICAgIGJ1ZGdldE1vbmV5OiBudWxsLA0KICAgICAgICBhdXRoQ29tcGFueTogbnVsbCwNCiAgICAgICAgYmlkZGluZ05ldDogbnVsbCwNCiAgICAgICAgZGlzdHJpYnV0b3I6IG51bGwsDQogICAgICAgIG1vZGVsOiBbXSwNCiAgICAgICAgc3BlYzogW10sDQogICAgICAgIGFyZWE6IG51bGwsDQogICAgICAgIGF1dGhGaWxlOiBudWxsLA0KICAgICAgICBhZnRlclNhbGVGaWxlOiBudWxsLA0KICAgICAgICByZXF1aXJlSW5mbzogW10sDQogICAgICAgIGluZm9UeXBlOiBbXSwNCiAgICAgICAgc2NhbkZpbGU6IG51bGwsDQogICAgICAgIHNlbmRBZGRyZXNzOiBudWxsLA0KICAgICAgICBtYWlsSW5mbzogbnVsbCwNCiAgICAgICAgZXhwcmVzc0luZm86IG51bGwsDQogICAgICAgIHJlbWFyazogbnVsbCwNCiAgICAgICAgc3BhcmUxOiBudWxsLA0KICAgICAgICBzcGFyZTI6IG51bGwsDQogICAgICB9Ow0KICAgICAgdGhpcy5yZXNldEZvcm0oImZvcm0iKTsNCiAgICB9LA0KICAgIC8vIOWkmumAieahhumAieS4reaVsOaNrg0KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsNCiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcCgoaXRlbSkgPT4gaXRlbS5wcm9qZWN0SWQpOw0KICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoICE9PSAxOw0KICAgICAgdGhpcy5tdWx0aXBsZSA9ICFzZWxlY3Rpb24ubGVuZ3RoOw0KICAgICAgY29uc29sZS5pbmZvKHNlbGVjdGlvbik7DQogICAgfSwNCg0KICAgIC8qKiDkv67mlLnmjInpkq7mk43kvZwgKi8NCiAgICBnZXRSZXBvcnRJbmZvKHByb2plY3RJZCkgew0KICAgICAgZ2V0UmVwb3J0KHByb2plY3RJZCkudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgdGhpcy5mb3JtID0gcmVzcG9uc2UuZGF0YTsNCiAgICAgICAgaWYgKHRoaXMuZm9ybS5tb2RlbCkgdGhpcy5mb3JtLm1vZGVsID0gdGhpcy5mb3JtLm1vZGVsLnNwbGl0KCIsIik7DQogICAgICAgIGVsc2UgdGhpcy5mb3JtLm1vZGVsID0gW107DQogICAgICAgIGlmICh0aGlzLmZvcm0ucmVxdWlyZUluZm8pDQogICAgICAgICAgdGhpcy5mb3JtLnJlcXVpcmVJbmZvID0gdGhpcy5mb3JtLnJlcXVpcmVJbmZvLnNwbGl0KCIsIik7DQogICAgICAgIGVsc2UgdGhpcy5mb3JtLnJlcXVpcmVJbmZvID0gW107DQogICAgICAgIGlmICh0aGlzLmZvcm0uaW5mb1R5cGUpDQogICAgICAgICAgdGhpcy5mb3JtLmluZm9UeXBlID0gdGhpcy5mb3JtLmluZm9UeXBlLnNwbGl0KCIsIik7DQogICAgICAgIGVsc2UgdGhpcy5mb3JtLmluZm9UeXBlID0gW107DQogICAgICAgIGlmICh0aGlzLmZvcm0uc3BlYykgdGhpcy5mb3JtLnNwZWMgPSB0aGlzLmZvcm0uc3BlYy5zcGxpdCgiLCIpOw0KICAgICAgICBlbHNlIHRoaXMuZm9ybS5zcGVjID0gW107DQogICAgICAgIHZhciBwcm92aW5jZXMgPSByZXNwb25zZS5kYXRhLnByb3ZpbmNlOw0KICAgICAgICBpZiAocHJvdmluY2VzLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICB2YXIgYWRkcmVzcyA9IHByb3ZpbmNlcy5zcGxpdCgiLyIpOw0KICAgICAgICAgIHZhciBjaXR5cyA9IFtdOw0KICAgICAgICAgIC8vIOecgeS7vQ0KICAgICAgICAgIGlmIChhZGRyZXNzLmxlbmd0aCA+IDApIGNpdHlzLnB1c2goVGV4dFRvQ29kZVthZGRyZXNzWzBdXS5jb2RlKTsNCiAgICAgICAgICAvLyDln47luIINCiAgICAgICAgICBpZiAoYWRkcmVzcy5sZW5ndGggPiAxKQ0KICAgICAgICAgICAgY2l0eXMucHVzaChUZXh0VG9Db2RlW2FkZHJlc3NbMF1dW2FkZHJlc3NbMV1dLmNvZGUpOw0KICAgICAgICAgIC8vIOWcsOWMug0KICAgICAgICAgIGlmIChhZGRyZXNzLmxlbmd0aCA+IDIpDQogICAgICAgICAgICBjaXR5cy5wdXNoKFRleHRUb0NvZGVbYWRkcmVzc1swXV1bYWRkcmVzc1sxXV1bYWRkcmVzc1syXV0uY29kZSk7DQoNCiAgICAgICAgICB0aGlzLnNlbGVjdGVkT3B0aW9ucyA9IGNpdHlzOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDmj5DkuqTmjInpkq4gKi8NCiAgICBzdWJtaXRGb3JtKCkgew0KICAgICAgbGV0IHRoYXQgPSB0aGlzOw0KICAgICAgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKCh2YWxpZCkgPT4gew0KICAgICAgICBpZiAodmFsaWQpIHsNCiAgICAgICAgICBpZiAodGhpcy5mb3JtLmluZm9UeXBlLmluZGV4T2YoJzEnKSA+PSAwICYmIHRoaXMuZm9ybS5zY2FuRmlsZSkgew0KICAgICAgICAgICAgbGV0IGVtYWlsUmVnID0gL15bYS16QS1aMC05Xy1dK0BbYS16QS1aMC05Xy1dKyhcLlthLXpBLVowLTlfLV0rKSskLzsNCiAgICAgICAgICAgIGlmICghZW1haWxSZWcudGVzdCh0aGlzLmZvcm0uc2NhbkZpbGUpKSB7DQogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIui1hOaWmeaOpeaUtuaWueW8j+mCrueuseagvOW8j+mUmeivryIpOw0KICAgICAgICAgICAgICByZXR1cm47DQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICAgIC8vIGlmICh0aGlzLmZvcm0ub3BlcmF0aW9uVHlwZSA9PSAyICYmICF0aGlzLmZvcm0uYXV0aEZpbGUpIHsNCiAgICAgICAgICAvLyAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuaOiOadg+exu+Wei+W/hemcgOS4iuS8oOaOiOadg+S5piIpOw0KICAgICAgICAgIC8vICAgcmV0dXJuOw0KICAgICAgICAgIC8vIH0NCiAgICAgICAgICB2YXIgZm9ybVN0ciA9IEpTT04uc3RyaW5naWZ5KHRoaXMuZm9ybSk7DQogICAgICAgICAgdmFyIGZvcm1EYXRhID0gSlNPTi5wYXJzZShmb3JtU3RyKTsNCiAgICAgICAgICBpZiAoZm9ybURhdGEubW9kZWwgJiYgZm9ybURhdGEubW9kZWwubGVuZ3RoID4gMCkNCiAgICAgICAgICAgIGZvcm1EYXRhLm1vZGVsID0gZm9ybURhdGEubW9kZWwuam9pbigiLCIpOw0KICAgICAgICAgIGVsc2UgZm9ybURhdGEubW9kZWwgPSB1bmRlZmluZWQ7DQogICAgICAgICAgaWYgKGZvcm1EYXRhLnJlcXVpcmVJbmZvICYmIGZvcm1EYXRhLnJlcXVpcmVJbmZvLmxlbmd0aCA+IDApDQogICAgICAgICAgICBmb3JtRGF0YS5yZXF1aXJlSW5mbyA9IGZvcm1EYXRhLnJlcXVpcmVJbmZvLmpvaW4oIiwiKTsNCiAgICAgICAgICBlbHNlIGZvcm1EYXRhLnJlcXVpcmVJbmZvID0gdW5kZWZpbmVkOw0KICAgICAgICAgIGlmIChmb3JtRGF0YS5pbmZvVHlwZSAmJiBmb3JtRGF0YS5pbmZvVHlwZS5sZW5ndGggPiAwKQ0KICAgICAgICAgICAgZm9ybURhdGEuaW5mb1R5cGUgPSBmb3JtRGF0YS5pbmZvVHlwZS5qb2luKCIsIik7DQogICAgICAgICAgZWxzZSBmb3JtRGF0YS5pbmZvVHlwZSA9IHVuZGVmaW5lZDsNCiAgICAgICAgICBpZiAoZm9ybURhdGEuc3BlYyAmJiBmb3JtRGF0YS5zcGVjLmxlbmd0aCA+IDApDQogICAgICAgICAgICBmb3JtRGF0YS5zcGVjID0gZm9ybURhdGEuc3BlYy5qb2luKCIsIik7DQogICAgICAgICAgZWxzZSBmb3JtRGF0YS5zcGVjID0gdW5kZWZpbmVkOw0KDQogICAgICAgICAgLy/mjojmnYPlhazlj7gNCiAgICAgICAgICBpZiAodGhpcy5hdXRoQ29tcGFueXMubGVuZ3RoID4gMCkgew0KICAgICAgICAgICAgdmFyIGFycmF5ID0gbmV3IEFycmF5KCk7DQogICAgICAgICAgICB0aGlzLmF1dGhDb21wYW55cy5mb3JFYWNoKGZ1bmN0aW9uIChlKSB7DQogICAgICAgICAgICAgIGFycmF5LnB1c2goZS52YWx1ZSk7DQogICAgICAgICAgICB9KQ0KICAgICAgICAgICAgZm9ybURhdGEuYXV0aENvbXBhbnkgKz0gIiwiICsgYXJyYXkuam9pbigiLCIpDQogICAgICAgICAgfQ0KDQogICAgICAgICAgY29uc3QgbG9hZGluZyA9IHRoaXMuJGxvYWRpbmcoew0KICAgICAgICAgICAgbG9jazogdHJ1ZSwvL2xvY2vnmoTkv67mlLnnrKYtLem7mOiupOaYr2ZhbHNlDQogICAgICAgICAgICB0ZXh0OiAnTG9hZGluZycsLy/mmL7npLrlnKjliqDovb3lm77moIfkuIvmlrnnmoTliqDovb3mlofmoYgNCiAgICAgICAgICAgIHNwaW5uZXI6ICdlbC1pY29uLWxvYWRpbmcnLC8v6Ieq5a6a5LmJ5Yqg6L295Zu+5qCH57G75ZCNDQogICAgICAgICAgICBiYWNrZ3JvdW5kOiAncmdiYSgwLCAwLCAwLCAwLjcpJywvL+mBrue9qeWxguminOiJsg0KICAgICAgICAgICAgdGFyZ2V0OiBkb2N1bWVudC5xdWVyeVNlbGVjdG9yKCcubWFpbi1jb250YWluZXInKS8vbG9hZGlu6KaG55uW55qEZG9t5YWD57Sg6IqC54K5DQogICAgICAgICAgfSk7DQogICAgICAgICAgZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50LnN0eWxlLm92ZXJmbG93WSA9ICdoaWRkZW4nIC8v56aB5q2i5bqV5bGCZGl25rua5YqoDQoNCiAgICAgICAgICBpZiAoZm9ybURhdGEucHJvamVjdElkICE9IG51bGwpIHsNCiAgICAgICAgICAgIHVwZGF0ZVJlcG9ydChmb3JtRGF0YSkudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgICAgICAgLy90aGlzLm1zZ1N1Y2Nlc3MoIuS/ruaUueaIkOWKnyIpOw0KICAgICAgICAgICAgICBpZiAodGhhdC5idXNpbmVzc0tleSkgew0KICAgICAgICAgICAgICAgIHRoYXQuJHJlZnNbJ2Zsb3cnXS50YXNrQ29tcGxldGUoIumHjeaWsOaPkOS6pCIpOw0KICAgICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICAgIHRoYXQuc3RhcnRGbG93KGZvcm1EYXRhKTsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHsNCiAgICAgICAgICAgICAgICBsb2FkaW5nLmNsb3NlKCk7DQogICAgICAgICAgICAgICAgZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50LnN0eWxlLm92ZXJmbG93WSA9ICdhdXRvJyAvL+WFgeiuuOW6leWxgmRpdua7muWKqA0KICAgICAgICAgICAgICB9LCAxMDAwKTsNCiAgICAgICAgICAgIH0pLmNhdGNoKHJlcyA9PiB7DQogICAgICAgICAgICAgIGNvbnNvbGUubG9nKHJlcykNCiAgICAgICAgICAgICAgbG9hZGluZy5jbG9zZSgpOw0KICAgICAgICAgICAgICBkb2N1bWVudC5kb2N1bWVudEVsZW1lbnQuc3R5bGUub3ZlcmZsb3dZID0gJ2F1dG8nIC8v5YWB6K645bqV5bGCZGl25rua5YqoDQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgYWRkUmVwb3J0KGZvcm1EYXRhKS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICAgICAgICBjb25zb2xlLmxvZygiPT09YWRkUmVwb3J0PT4+PiIpDQogICAgICAgICAgICAgIC8vdGhpcy5tc2dTdWNjZXNzKCLmlrDlop7miJDlip8iKTsNCiAgICAgICAgICAgICAgZm9ybURhdGEucHJvamVjdElkID0gcmVzcG9uc2UuZGF0YTsNCiAgICAgICAgICAgICAgdGhhdC5mb3JtLnByb2plY3RJZCA9IHJlc3BvbnNlLmRhdGE7DQogICAgICAgICAgICAgIHRoYXQuc3RhcnRGbG93KGZvcm1EYXRhKTsNCiAgICAgICAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7DQogICAgICAgICAgICAgICAgbG9hZGluZy5jbG9zZSgpOw0KICAgICAgICAgICAgICAgIGRvY3VtZW50LmRvY3VtZW50RWxlbWVudC5zdHlsZS5vdmVyZmxvd1kgPSAnYXV0bycgLy/lhYHorrjlupXlsYJkaXbmu5rliqgNCiAgICAgICAgICAgICAgfSwgMTAwMCk7DQogICAgICAgICAgICB9KS5jYXRjaChyZXMgPT4gew0KICAgICAgICAgICAgICBjb25zb2xlLmxvZyhyZXMpDQogICAgICAgICAgICAgIGxvYWRpbmcuY2xvc2UoKTsNCiAgICAgICAgICAgICAgZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50LnN0eWxlLm92ZXJmbG93WSA9ICdhdXRvJyAvL+WFgeiuuOW6leWxgmRpdua7muWKqA0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIHN0YXJ0Rmxvdyhmb3JtRGF0YSkgew0KICAgICAgLy/pobnnm67ljLrln58NCiAgICAgIC8vdmFyIGFyZWEgPSBmb3JtRGF0YS5kaXN0cmljdDsNCiAgICAgIC8v55So5oi35Yy65Z+fIDYtMTHkv67mlLnvvIzmoLnmja7nlKjmiLfmiYDlnKjljLrln5/liKTmlq0NCiAgICAgIHZhciB2YXJpYWJsZXMgPSB7fTsNCiAgICAgIHZhcmlhYmxlcy5QUk9DRVNTX0FSRUEgPSB0aGlzLiRzdG9yZS5zdGF0ZS51c2VyLnByb3ZpbmNlOw0KICAgICAgLy/mmK/lkKbnnIHotJ/otKPkurrop5LoibINCiAgICAgIGlmICh0aGlzLiRzdG9yZS5zdGF0ZS51c2VyLnJvbGVzICYmIHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIucm9sZXMuaW5jbHVkZXMoInByb3ZpbmNlX2FkbWluIikpIHsNCiAgICAgICAgdmFyaWFibGVzLmlzTWFuYWdlID0gMTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHZhcmlhYmxlcy5pc01hbmFnZSA9IDA7DQogICAgICB9DQogICAgICAvL+aWsOWinuWFqOi1sOaKpeWkh+WuoeaJuea1geeoiw0KICAgICAgLy8gaWYoZm9ybURhdGEub3BlcmF0aW9uVHlwZSA9PSAnMicpew0KICAgICAgLy8gICB2YXJpYWJsZXMuaXNBdXRoID0gdHJ1ZTsNCiAgICAgIC8vIH1lbHNlew0KICAgICAgLy8gICB2YXJpYWJsZXMuaXNBdXRoID0gZmFsc2U7DQogICAgICAvLyB9DQogICAgICAvL3ZhcmlhYmxlcy5pc0F1dGggPSBmYWxzZTsNCiAgICAgIHZhcmlhYmxlcy5CVVNJTkVTU0tFWSA9IGZvcm1EYXRhLnByb2plY3RJZDsNCiAgICAgIHZhciB0YXNrTmFtZSA9ICLpobnnm67miqXlpIciOw0KICAgICAgaWYgKGZvcm1EYXRhLm9wZXJhdGlvblR5cGUgPT0gJzInKSB7DQogICAgICAgIHRhc2tOYW1lID0gIumhueebruaOiOadgyI7DQogICAgICB9DQogICAgICB0aGlzLiRyZWZzWydmbG93J10uc3RhcnRGbG93KGZvcm1EYXRhLnByb2plY3RJZCwgdGFza05hbWUsIHZhcmlhYmxlcyk7DQogICAgfSwNCiAgICBkb3dubG9hZFNRUygpIHsNCiAgICAgIHRoaXMuZG93bmxvYWQoIua1t+S9s+mbhuWboi3mjojmnYPkuaYuZG9jeCIsIGZhbHNlKTsNCiAgICB9LA0KICAgIGRvd25sb2FkQ1JIKCkgew0KICAgICAgdGhpcy5kb3dubG9hZCgi5rW35L2z6ZuG5ZuiLeWUruWQjuacjeWKoeaJv+ivuuWHvS5kb2MiLCBmYWxzZSk7DQogICAgfSwNCiAgICBoYW5kbGVDaGFuZ2UodmFsdWUpIHsNCiAgICAgIGlmICghdmFsdWUgfHwgdmFsdWUubGVuZ3RoID09IDApIHsNCiAgICAgICAgdGhpcy5zZWxlY3RlZE9wdGlvbnMgPSBudWxsOw0KICAgICAgICB0aGlzLmZvcm0ucHJvdmluY2UgPSB1bmRlZmluZWQ7DQogICAgICAgIHRoaXMuZm9ybS5kaXN0cmljdCA9IHVuZGVmaW5lZDsNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQogICAgICB0aGlzLnNlbGVjdGVkT3B0aW9ucyA9IHZhbHVlOw0KICAgICAgdmFyIHR4dCA9ICIiOw0KICAgICAgdmFsdWUuZm9yRWFjaChmdW5jdGlvbiAoaXRlbSkgew0KICAgICAgICB0eHQgKz0gQ29kZVRvVGV4dFtpdGVtXSArICIvIjsNCiAgICAgIH0pOw0KICAgICAgaWYgKHR4dC5sZW5ndGggPiAxKSB7DQogICAgICAgIHR4dCA9IHR4dC5zdWJzdHJpbmcoMCwgdHh0Lmxlbmd0aCAtIDEpOw0KICAgICAgICB0aGlzLmZvcm0ucHJvdmluY2UgPSB0eHQ7DQogICAgICAgIHRoaXMuZm9ybS5kaXN0cmljdCA9IHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIucHJvdmluY2U7DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLmZvcm0ucHJvdmluY2UgPSB1bmRlZmluZWQ7DQogICAgICAgIHRoaXMuZm9ybS5kaXN0cmljdCA9IHVuZGVmaW5lZDsNCiAgICAgIH0NCiAgICB9LA0KICAgIGhhbmRsZVF1ZXJ5Q2l0eUNoYW5nZSh2YWx1ZSkgew0KICAgICAgdGhpcy5xdWVyeUFyZWEgPSB2YWx1ZTsNCiAgICAgIHZhciB0eHQgPSAiIjsNCiAgICAgIHZhbHVlLmZvckVhY2goZnVuY3Rpb24gKGl0ZW0pIHsNCiAgICAgICAgdHh0ICs9IENvZGVUb1RleHRbaXRlbV0gKyAiLyI7DQogICAgICB9KTsNCiAgICAgIGlmICh0eHQubGVuZ3RoID4gMSkgew0KICAgICAgICB0eHQgPSB0eHQuc3Vic3RyaW5nKDAsIHR4dC5sZW5ndGggLSAxKTsNCiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wcm92aW5jZSA9IHR4dDsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMucXVlcnlQYXJhbXMucHJvdmluY2UgPSB1bmRlZmluZWQ7DQogICAgICB9DQogICAgfSwNCiAgICAvKiog5a6h5om5ICovDQogICAgaGFuZGxlQ29tcGxldGUoKSB7DQogICAgICB0aGlzLiRyZWZzWydmbG93J10uaGFuZGxlQ29tcGxldGUoKTsNCiAgICB9LA0KICAgIC8qKiDpgIDlm54gKi8NCiAgICBoYW5kbGVSZXR1cm4oKSB7DQogICAgICB0aGlzLiRyZWZzWydmbG93J10uaGFuZGxlUmV0dXJuKCk7DQogICAgfSwNCiAgICAvKiog6L+U5Zue6aG16Z2iICovDQogICAgZ29CYWNrKCkgew0KICAgICAgLy8g5YWz6Zet5b2T5YmN5qCH562+6aG15bm26L+U5Zue5LiK5Liq6aG16Z2iDQogICAgICB0aGlzLiRzdG9yZS5kaXNwYXRjaCgidGFnc1ZpZXcvZGVsVmlldyIsIHRoaXMuJHJvdXRlKTsNCiAgICAgIHRoaXMuJHJvdXRlci5nbygtMSkNCiAgICB9LA0KICAgIHJlbW92ZURvbWFpbihpbmRleCkgew0KICAgICAgaWYgKGluZGV4ICE9PSAtMSkgew0KICAgICAgICB0aGlzLmF1dGhDb21wYW55cy5zcGxpY2UoaW5kZXgsIDEpDQogICAgICB9DQogICAgfSwNCiAgICBhZGREb21haW4oKSB7DQogICAgICB0aGlzLmF1dGhDb21wYW55cy5wdXNoKHsNCiAgICAgICAgdmFsdWU6ICcnLA0KICAgICAgICBrZXk6IERhdGUubm93KCkNCiAgICAgIH0pOw0KICAgIH0NCiAgfSwNCn07DQo="}, {"version": 3, "sources": ["form.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6UA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "form.vue", "sourceRoot": "src/views/project/report", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-card class=\"box-card\">\r\n      <div slot=\"header\" class=\"clearfix\">\r\n        <span class=\"el-icon-document\">项目报备流程</span>\r\n        <span style=\"float: right;\">\r\n          <el-button icon=\"el-icon-edit-outline\" type=\"success\" v-if=\"audit\" @click=\"handleComplete\">审批</el-button>\r\n          <el-button icon=\"el-icon-refresh-left\" type=\"warning\" v-if=\"audit\" @click=\"handleReturn\">退回</el-button>\r\n          <el-button type=\"primary\" @click=\"goBack\">返回</el-button>\r\n        </span>\r\n\r\n      </div>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" :disabled=\"!formEdit\" label-width=\"120px\">\r\n\r\n        <!-- <el-row> -->\r\n        <!-- <el-col :span=\"12\">\r\n            <el-form-item label=\"编辑状态\">\r\n              <el-radio-group v-model=\"form.editStatus\">\r\n                <el-radio\r\n                  v-for=\"dict in editStatusOptions\"\r\n                  :key=\"dict.dictValue\"\r\n                  :label=\"dict.dictValue\"\r\n                  >{{ dict.dictLabel }}</el-radio\r\n                >\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col> -->\r\n        <!-- <el-col :span=\"12\">\r\n            <el-form-item label=\"所属用户\" prop=\"belongUser\">\r\n              <el-select v-model=\"form.belongUser\" placeholder=\"请选择所属用户\">\r\n                <el-option label=\"请选择字典生成\" value=\"\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col> -->\r\n        <!-- </el-row> -->\r\n        <!-- <el-row>\r\n          <el-col :span=\"20\">\r\n            <el-form-item label=\"驳回原因\" prop=\"rejectReason\">\r\n              <el-input\r\n                v-model=\"form.rejectReason\"\r\n                type=\"textarea\"\r\n                placeholder=\"请输入内容\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row> -->\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"项目编号\" prop=\"projectNo\">\r\n              <el-input v-model=\"form.projectNo\" placeholder=\"若无编号则为当前时间(年月日时间)\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"项目名称\" prop=\"projectName\">\r\n              <el-input v-model=\"form.projectName\" placeholder=\"请输入项目名称\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"项目所在地\" prop=\"province\">\r\n              <el-cascader :options=\"options\" clearable :props=\"{ expandTrigger: 'hover' }\" v-model=\"selectedOptions\"\r\n                @change=\"handleChange\">\r\n              </el-cascader>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"详细地址\" prop=\"address\">\r\n              <el-input v-model=\"form.address\" placeholder=\"请输入详细地址\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"项目所属省份\" prop=\"belongProvince\">\r\n              <el-select v-model=\"form.belongProvince\" clearable placeholder=\"请选择所属省份\">\r\n                <el-option v-for=\"item in belongProvinceOptions\" :key=\"item.value\" :label=\"item.label\"\r\n                  :value=\"item.value\">\r\n                </el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <!-- <el-form-item label=\"招标单位联系人/联系电话\" prop=\"biddingContact\">\r\n              <el-input v-model=\"form.biddingContact\" placeholder=\"请输入联系人/联系电话\" />\r\n            </el-form-item> -->\r\n            <el-form-item label=\"招标单位\" prop=\"biddingCompany\">\r\n              <el-input v-model=\"form.biddingCompany\" placeholder=\"请输入招标单位\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"挂网日期\" prop=\"hangDate\">\r\n              <el-input v-model=\"form.hangDate\" placeholder=\"请输入挂网日期，没有请写无，日期格式XXXX（年）-XX（月）-XX（日\" />\r\n              <!-- <el-date-picker clearable size=\"small\" v-model=\"form.hangDate\" type=\"date\" value-format=\"yyyy-MM-dd\"\r\n                placeholder=\"选择挂网日期\">\r\n              </el-date-picker> -->\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"开标日期\" prop=\"openDate\">\r\n              <el-input v-model=\"form.openDate\" placeholder=\"请输入开标日期，没有请写无，日期格式XXXX（年）-XX（月）-XX（日\" />\r\n              <!-- <el-date-picker clearable size=\"small\" v-model=\"form.openDate\" type=\"date\" value-format=\"yyyy-MM-dd\"\r\n                placeholder=\"选择开标日期\">\r\n              </el-date-picker> -->\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"所属经销商\" prop=\"distributor\">\r\n              <el-input v-model=\"form.distributor\" placeholder=\"请输入经销商\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <!-- <el-form-item label=\"招标单位联系人/联系电话\" prop=\"biddingContact\">\r\n              <el-input v-model=\"form.biddingContact\" placeholder=\"请输入联系人/联系电话\" />\r\n            </el-form-item> -->\r\n            <el-form-item label=\"售后年限\" prop=\"afterSaleYear\">\r\n              <el-select v-model=\"form.afterSaleYear\" clearable placeholder=\"请选择售后年限\">\r\n                <el-option v-for=\"item in afterSaleYearOptions\" :key=\"item.value\" :label=\"item.label\"\r\n                  :value=\"item.value\">\r\n                </el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <!-- <el-col :span=\"12\">\r\n            <el-form-item label=\"预算金额\" prop=\"budgetMoney\">\r\n              <el-input\r\n                type=\"number\"\r\n                v-model=\"form.budgetMoney\"\r\n                placeholder=\"请输入预算金额\"\r\n              />\r\n            </el-form-item>\r\n          </el-col> -->\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"被授权公司\" prop=\"authCompany\">\r\n              <el-input v-model=\"form.authCompany\" placeholder=\"请输入授权公司\" />\r\n              <el-link @click=\"addDomain\" type=\"primary\" :disabled=\"!formEdit\">添加</el-link>\r\n            </el-form-item>\r\n            <el-form-item v-for=\"(company, index) in authCompanys\" :label=\"'被授权公司' + (index + 1)\" :key=\"company.key\">\r\n              <el-input v-model=\"company.value\" :placeholder=\"'被授权公司' + (index + 1)\" style=\"max-width:300px\" />\r\n              <el-link @click=\"removeDomain(index)\" type=\"primary\" :disabled=\"!formEdit\">删除</el-link>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"授权公司联系人/联系电话\" prop=\"authContact\">\r\n              <el-input v-model=\"form.authContact\" placeholder=\"请输入授权公司联系人/联系电话\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <!-- <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"招标信息公布网站\" prop=\"biddingNet\">\r\n              <el-input\r\n                v-model=\"form.biddingNet\"\r\n                placeholder=\"请输入招标信息公布网站\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"招标单位\" prop=\"biddingCompany\">\r\n              <el-input\r\n                v-model=\"form.biddingCompany\"\r\n                placeholder=\"请输入招标单位\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row> -->\r\n        <!-- <el-row v-if=\"form.operationType == 2\">\r\n          <el-col :span=\"20\">\r\n            <el-form-item label=\"模板下载\">\r\n              <el-col :span=\"8\">\r\n                <el-link @click=\"downloadSQS\" type=\"primary\" :disabled=\"!formEdit\">海佳集团-授权书.docx</el-link>\r\n              </el-col>\r\n              <el-col :span=\"8\">\r\n                <el-link @click=\"downloadCRH\" type=\"primary\" :disabled=\"!formEdit\">海佳集团-售后服务承诺函.docx</el-link>\r\n              </el-col>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row> -->\r\n        <!-- <el-form-item label=\"授权书\" v-if=\"form.operationType == 2\" :required=\"form.operationType == 2\">\r\n          <fileUpload v-model=\"form.authFile\" :fileType=\"['doc', 'docx']\" />\r\n        </el-form-item> -->\r\n        <el-form-item label=\"其余附件\">\r\n          <span style=\"color: red;\">请勿上传项目授权书、售后声明函</span>\r\n          <fileUpload v-model=\"form.afterSaleFile\" :fileType=\"['doc', 'docx']\" />\r\n        </el-form-item>\r\n        <el-row>\r\n          <el-col :span=\"20\">\r\n            <el-form-item label=\"投标产品型号\" prop=\"model\" :required=\"true\">\r\n              <el-cascader class=\"mobile-width\" style=\"width: 700px\" v-model=\"form.model\" placeholder=\"可输入产品型号搜索\"\r\n                :options=\"modelOptions\" :props=\"{ multiple: true }\" clearable filterable></el-cascader>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"20\">\r\n            <el-form-item label=\"投标产品规格\" prop=\"spec\" :required=\"true\">\r\n              <el-cascader class=\"mobile-width\" style=\"width: 700px\" v-model=\"form.spec\" placeholder=\"可输入产品规格搜索\"\r\n                :options=\"specOptions\" :props=\"{ multiple: true }\" clearable filterable></el-cascader>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"安装面积\" prop=\"area\">\r\n              <el-input v-model=\"form.area\" type=\"number\" placeholder=\"请输入安装面积\">\r\n                <template slot=\"append\">m²</template>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"20\">\r\n            <el-form-item label=\"所需资料\">\r\n              <el-cascader class=\"mobile-width\" style=\"width: 700px\" v-model=\"form.requireInfo\" placeholder=\"可输入资料类型搜索\"\r\n                :options=\"requireInfoOptions\" :props=\"{ multiple: true }\" clearable filterable></el-cascader>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <!-- <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"资料类型\">\r\n              <el-checkbox-group v-model=\"form.infoType\">\r\n                <el-checkbox\r\n                  v-for=\"dict in infoTypeOptions\"\r\n                  :key=\"dict.dictValue\"\r\n                  :label=\"dict.dictValue\"\r\n                >\r\n                  {{ dict.dictLabel }}\r\n                </el-checkbox>\r\n              </el-checkbox-group>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row> -->\r\n        <el-row>\r\n          <el-form-item label=\"资料接收方式\" prop=\"infoType\" :required=\"true\">\r\n            <el-checkbox-group v-model=\"form.infoType\" class=\"info-type\">\r\n              <!-- 选项A -->\r\n              <el-row style=\"display:flex;margin-bottom: 22px;\">\r\n                <el-col :span=\"12\" style=\"display:flex;\">\r\n                  <el-checkbox label=\"1\" style=\"margin-left:20px;margin-right:10px !important;\">邮件</el-checkbox>\r\n                  <el-form-item prop=\"scanFile\">\r\n                    <el-input v-model=\"form.scanFile\" placeholder=\"请输入邮箱地址\" style=\"width:300px;\"\r\n                      type=\"email\"></el-input>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"12\">\r\n                  <el-form-item label=\"邮件发送信息\" prop=\"mailInfo\">\r\n                    <el-input v-model=\"form.mailInfo\" placeholder=\"请输入邮件发送信息\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <!-- 选项B  -->\r\n              <el-row style=\"display:flex;margin-bottom: 22px;\">\r\n                <el-col :span=\"12\" style=\"display:flex;\">\r\n                  <el-checkbox label=\"2\" style=\"margin-left:20px;margin-right:10px !important;\">邮寄</el-checkbox>\r\n                  <el-form-item prop=\"sendAddress\">\r\n                    <el-input v-model=\"form.sendAddress\" placeholder=\"请输入收件地址\" style=\"width:300px;\"></el-input>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"12\">\r\n                  <el-form-item label=\"快递单号\" prop=\"expressInfo\">\r\n                    <el-input v-model=\"form.expressInfo\" placeholder=\"请输入快递单号\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </el-checkbox-group>\r\n          </el-form-item>\r\n        </el-row>\r\n        <!-- <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"邮件发送信息\" prop=\"mailInfo\">\r\n              <el-input\r\n                v-model=\"form.mailInfo\"\r\n                placeholder=\"请输入邮件发送信息\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"快递单号\" prop=\"expressInfo\">\r\n              <el-input\r\n                v-model=\"form.expressInfo\"\r\n                placeholder=\"请输入快递单号\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row> -->\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"操作类型\" prop=\"operationType\">\r\n              <el-radio-group v-model=\"form.operationType\">\r\n                <el-radio v-for=\"dict in operationTypeOptions\" :key=\"dict.dictValue\"\r\n                  :label=\"dict.dictValue\">{{ dict.dictLabel }}</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col>\r\n          <!-- <el-col :span=\"12\">\r\n            <el-form-item label=\"审核状态\">\r\n              <el-radio-group v-model=\"form.auditStatus\">\r\n                <el-radio\r\n                  v-for=\"dict in auditStatusOptions\"\r\n                  :key=\"dict.dictValue\"\r\n                  :label=\"dict.dictValue\"\r\n                  >{{ dict.dictLabel }}</el-radio\r\n                >\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col> -->\r\n        </el-row>\r\n        <el-form-item label=\"备注\" prop=\"remark\">\r\n          <el-input v-model=\"form.remark\" type=\"textarea\" placeholder=\"请输入备注\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <el-col :span=\"16\" :offset=\"8\" v-if=\"formEdit\">\r\n        <div style=\"margin-left:10%;margin-bottom: 20px;font-size: 14px;\">\r\n          <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n          <el-button @click=\"goBack\">取 消</el-button>\r\n        </div>\r\n      </el-col>\r\n    </el-card>\r\n    <flowable :key=\"businessKey\" ref=\"flow\" procDefKey=\"process_project_report\" :procInsId=\"procInsId\" :taskId=\"taskId\"\r\n      :finished=\"finished\"></flowable>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  getReport,\r\n  addReport,\r\n  updateReport,\r\n  checkNameUnique\r\n} from \"@/api/project/report\";\r\nimport FileUpload from \"@/components/FileUpload\";\r\nimport flowable from '@/views/flowable/task/record/index'\r\nimport { regionData, CodeToText, TextToCode } from \"element-china-area-data\";\r\nimport print from \"print-js\";\r\nexport default {\r\n  name: \"Report\",\r\n  components: {\r\n    flowable,\r\n    FileUpload,\r\n    print\r\n  },\r\n  data() {\r\n    var that = this;\r\n    var infoTypeValueVali = (rule, value, callback) => {\r\n      if (that.form.infoType.indexOf('1') >= 0 && !that.form.scanFile) {\r\n        callback(new Error(\"邮箱地址必填\"));\r\n        return;\r\n      }\r\n      callback();\r\n    };\r\n    var infoTypeValueVali2 = (rule, value, callback) => {\r\n      if (that.form.infoType.indexOf('2') >= 0 && !that.form.sendAddress) {\r\n        callback(new Error(\"收件地址必填\"));\r\n        return;\r\n      }\r\n      callback();\r\n    };\r\n    var nameVali = (rule, value, callback) => {\r\n      if (!that.form.projectName) {\r\n        callback(new Error(\"项目名称必填\"));\r\n      } else {\r\n        if (/\\s+/g.test(that.form.projectName)) {\r\n          callback(new Error(\"项目名称不规范\"));\r\n          return;\r\n        }\r\n        checkNameUnique({ projectName: that.form.projectName, projectId: that.form.projectId }).then((response) => {\r\n          if (response.data == 0) {\r\n            callback();\r\n          } else {\r\n            callback(new Error(\"项目名称已存在\"));\r\n          }\r\n        })\r\n      }\r\n    };\r\n    var codeVali = (rule, value, callback) => {\r\n      if (!that.form.projectNo) {\r\n        callback(new Error(\"项目编号必填\"));\r\n      } else if (/\\s+/g.test(that.form.projectNo)) {\r\n        callback(new Error(\"项目编号不规范\"));\r\n        return;\r\n      }\r\n      callback();\r\n    };\r\n    var openDateVali = (rule, value, callback) => {\r\n      if (!that.form.openDate) {\r\n        callback(new Error(\"开标日期必填\"));\r\n        return;\r\n      } else if (value === \"无\") {\r\n        callback();\r\n        return;\r\n      } else if (!/^\\d{4}-\\d{2}-\\d{2}$/.test(value)) {\r\n        callback(new Error(\"开标日期格式不合法，示例2025-01-01\"));\r\n        return;\r\n      }\r\n      callback();\r\n    };\r\n    var hangDateVali = (rule, value, callback) => {\r\n      if (!that.form.hangDate) {\r\n        callback(new Error(\"挂网日期必填\"));\r\n        return;\r\n      } else if (value === \"无\") {\r\n        callback();\r\n        return;\r\n      } else if (!/^\\d{4}-\\d{2}-\\d{2}$/.test(value)) {\r\n        callback(new Error(\"挂网日期格式不合法，示例2025-01-01\"));\r\n        return;\r\n      }\r\n      callback();\r\n    };\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 操作类型字典\r\n      operationTypeOptions: [],\r\n      // 审核状态字典\r\n      auditStatusOptions: [],\r\n      // 编辑状态字典\r\n      editStatusOptions: [],\r\n      // 招标方式字典\r\n      biddingTypeOptions: [],\r\n      // 投标产品型号字典\r\n      modelOptions: [],\r\n      modelOption1: [],\r\n      // 所需资料字典\r\n      requireInfoOptions: [],\r\n      requireInfoOption1: [],\r\n      // 资料类型字典\r\n      infoTypeOptions: [],\r\n      // 所属省份字典\r\n      belongProvinceOptions: [],\r\n      belongProvinceOptions1: [],\r\n      // 售后年限\r\n      afterSaleYearOptions: [],\r\n      afterSaleYearOptions1: [],\r\n      specOptions: [],\r\n      specOption1: [],\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      // 表单校验\r\n      rules: {\r\n        operationType: [{ required: true, message: \"操作类型必选\" }],\r\n        projectNo: [\r\n          { validator: codeVali, required: true, trigger: \"blur\" },\r\n        ],\r\n        projectName: [\r\n          { validator: nameVali, required: true, trigger: \"blur\" },\r\n        ],\r\n        address: [\r\n          { required: true, message: \"详细地址不能为空\", trigger: \"blur\" },\r\n        ],\r\n        biddingCompany: [\r\n          { required: true, message: \"招标单位不能为空\", trigger: \"blur\" },\r\n        ],\r\n        openDate: [\r\n          { required: true, validator: openDateVali, trigger: \"blur\" },\r\n        ],\r\n        afterSaleYear: [\r\n          { required: true, message: \"售后年限不能为空\", trigger: \"blur\" },\r\n        ],\r\n        hangDate: [\r\n          { required: true, validator: hangDateVali, trigger: \"blur\" },\r\n        ],\r\n        belongProvince: [\r\n          { required: true, message: \"售后年限不能为空\", trigger: \"blur\" },\r\n        ],\r\n        distributor: [\r\n          { required: true, message: \"所属经销商不能为空\", trigger: \"blur\" },\r\n        ],\r\n        scanFile: [\r\n          { validator: infoTypeValueVali, trigger: \"blur\" },\r\n        ],\r\n        sendAddress: [\r\n          { validator: infoTypeValueVali2, trigger: \"blur\" },\r\n        ],\r\n        model: [\r\n          { required: true, message: \"投标产品型号必选\" },\r\n        ],\r\n        spec: [\r\n          { required: true, message: \"投标产品规格必选\" },\r\n        ],\r\n        province: [\r\n          { required: true, message: \"项目所在地必选\" },\r\n        ],\r\n        infoType: [\r\n          { required: true, message: \"资料接收方式必选\", trigger: \"change\" },\r\n        ],\r\n        biddingContact: [\r\n          { required: true, message: \"招标单位联系人/联系电话必填\" },\r\n        ],\r\n        authContact: [\r\n          { required: true, message: \"授权公司联系人/联系电话必填\" },\r\n        ]\r\n      },\r\n      options: regionData,\r\n      selectedOptions: [],\r\n      queryArea: [],\r\n      //工作流参数\r\n      finished: 'false',\r\n      taskId: undefined,\r\n      procInsId: undefined,\r\n      businessKey: undefined,\r\n      audit: false,\r\n      formEdit: false,\r\n      //工作流参数end\r\n      authCompanys: [],\r\n    };\r\n  },\r\n  activated() {\r\n\r\n    this.reset();\r\n    //this.form.projectNo = moment(new Date()).format('YYYYMMDDHHmm');\r\n    this.taskId = this.$route.query && this.$route.query.taskId;\r\n    this.procInsId = this.$route.query && this.$route.query.procInsId;\r\n    this.finished = this.$route.query && this.$route.query.finished;\r\n    this.businessKey = this.$route.query && this.$route.query.businessKey;\r\n    let edit = this.$route.query && this.$route.query.formEdit;\r\n    if (edit == \"true\") {\r\n      this.formEdit = true;\r\n    } else {\r\n      this.formEdit = false;\r\n    }\r\n    if (this.businessKey) {\r\n      if (this.finished == \"false\" && !this.formEdit) {\r\n        this.audit = true;\r\n      }\r\n      this.getReportInfo(this.businessKey);\r\n    }\r\n    console.log(\"========project=========>activated>formEdit>>\" + this.formEdit);\r\n  },\r\n  created() {\r\n\r\n    this.reset();\r\n    //this.form.projectNo = moment(new Date()).format('YYYYMMDDHHmm');\r\n    this.taskId = this.$route.query && this.$route.query.taskId;\r\n    this.procInsId = this.$route.query && this.$route.query.procInsId;\r\n    this.finished = this.$route.query && this.$route.query.finished;\r\n    this.businessKey = this.$route.query && this.$route.query.businessKey;\r\n    let edit = this.$route.query && this.$route.query.formEdit;\r\n    if (edit == \"true\") {\r\n      this.formEdit = true;\r\n    } else {\r\n      this.formEdit = false;\r\n    }\r\n    if (this.businessKey) {\r\n      if (this.finished == \"false\" && !this.formEdit) {\r\n        this.audit = true;\r\n      }\r\n      this.getReportInfo(this.businessKey);\r\n    }\r\n    // this.audit = true;\r\n    console.log(\"=========project========>created>>formEdit>\" + this.formEdit);\r\n\r\n    this.getDicts(\"pr_operation_type\").then((response) => {\r\n      this.operationTypeOptions = response.data;\r\n    });\r\n    this.getDicts(\"pr_audit_status\").then((response) => {\r\n      this.auditStatusOptions = response.data;\r\n    });\r\n    this.getDicts(\"pr_edit_status\").then((response) => {\r\n      this.editStatusOptions = response.data;\r\n    });\r\n    // this.getDicts(\"pr_bidding_type\").then((response) => {\r\n    //   this.biddingTypeOptions = response.data;\r\n    // });\r\n    this.getDicts(\"pr_model\").then((response) => {\r\n      this.modelOption1 = response.data;\r\n      var opt = [];\r\n      response.data.forEach((elem, index) => {\r\n        var obj = {};\r\n        obj.label = elem.dictLabel;\r\n        obj.value = elem.dictValue;\r\n        opt.push(obj);\r\n      });\r\n      this.modelOptions = opt;\r\n    });\r\n    this.getDicts(\"pr_spec\").then((response) => {\r\n      this.specOption1 = response.data;\r\n      var opt = [];\r\n      response.data.forEach((elem, index) => {\r\n        var obj = {};\r\n        obj.label = elem.dictLabel;\r\n        obj.value = elem.dictValue;\r\n        opt.push(obj);\r\n      });\r\n      this.specOptions = opt;\r\n    });\r\n    this.getDicts(\"pr_info\").then((response) => {\r\n      this.requireInfoOption1 = response.data;\r\n      var opt = [];\r\n      response.data.forEach((elem, index) => {\r\n        var obj = {};\r\n        obj.label = elem.dictLabel;\r\n        obj.value = elem.dictValue;\r\n        opt.push(obj);\r\n      });\r\n      this.requireInfoOptions = opt;\r\n    });\r\n    this.getDicts(\"pr_province\").then((response) => {\r\n      this.belongProvinceOptions1 = response.data;\r\n      var opt = [];\r\n      response.data.forEach((elem, index) => {\r\n        var obj = {};\r\n        obj.label = elem.dictLabel;\r\n        obj.value = elem.dictValue;\r\n        opt.push(obj);\r\n      });\r\n      this.belongProvinceOptions = opt;\r\n    });\r\n    this.getDicts(\"pr_after_sale_year\").then((response) => {\r\n      this.afterSaleYearOptions1 = response.data;\r\n      var opt = [];\r\n      response.data.forEach((elem, index) => {\r\n        var obj = {};\r\n        obj.label = elem.dictLabel;\r\n        obj.value = elem.dictValue;\r\n        opt.push(obj);\r\n      });\r\n      this.afterSaleYearOptions = opt;\r\n    });\r\n    this.getDicts(\"pr_data_type\").then((response) => {\r\n      this.infoTypeOptions = response.data;\r\n    });\r\n    //默认报备\r\n    this.form.operationType = '1';\r\n  },\r\n  methods: {\r\n\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        projectId: null,\r\n        projectNo: null,\r\n        projectName: null,\r\n        operationType: 1,\r\n        auditStatus: \"1\",\r\n        rejectReason: null,\r\n        province: null,\r\n        city: null,\r\n        district: null,\r\n        address: null,\r\n        editStatus: \"0\",\r\n        belongUser: null,\r\n        biddingCompany: null,\r\n        openDate: null,\r\n        belongProvince: null,\r\n        afterSaleYear: null,\r\n        hangDate: null,\r\n        biddingType: null,\r\n        budgetMoney: null,\r\n        authCompany: null,\r\n        biddingNet: null,\r\n        distributor: null,\r\n        model: [],\r\n        spec: [],\r\n        area: null,\r\n        authFile: null,\r\n        afterSaleFile: null,\r\n        requireInfo: [],\r\n        infoType: [],\r\n        scanFile: null,\r\n        sendAddress: null,\r\n        mailInfo: null,\r\n        expressInfo: null,\r\n        remark: null,\r\n        spare1: null,\r\n        spare2: null,\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map((item) => item.projectId);\r\n      this.single = selection.length !== 1;\r\n      this.multiple = !selection.length;\r\n      console.info(selection);\r\n    },\r\n\r\n    /** 修改按钮操作 */\r\n    getReportInfo(projectId) {\r\n      getReport(projectId).then((response) => {\r\n        this.form = response.data;\r\n        if (this.form.model) this.form.model = this.form.model.split(\",\");\r\n        else this.form.model = [];\r\n        if (this.form.requireInfo)\r\n          this.form.requireInfo = this.form.requireInfo.split(\",\");\r\n        else this.form.requireInfo = [];\r\n        if (this.form.infoType)\r\n          this.form.infoType = this.form.infoType.split(\",\");\r\n        else this.form.infoType = [];\r\n        if (this.form.spec) this.form.spec = this.form.spec.split(\",\");\r\n        else this.form.spec = [];\r\n        var provinces = response.data.province;\r\n        if (provinces.length > 0) {\r\n          var address = provinces.split(\"/\");\r\n          var citys = [];\r\n          // 省份\r\n          if (address.length > 0) citys.push(TextToCode[address[0]].code);\r\n          // 城市\r\n          if (address.length > 1)\r\n            citys.push(TextToCode[address[0]][address[1]].code);\r\n          // 地区\r\n          if (address.length > 2)\r\n            citys.push(TextToCode[address[0]][address[1]][address[2]].code);\r\n\r\n          this.selectedOptions = citys;\r\n        }\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      let that = this;\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n          if (this.form.infoType.indexOf('1') >= 0 && this.form.scanFile) {\r\n            let emailReg = /^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\\.[a-zA-Z0-9_-]+)+$/;\r\n            if (!emailReg.test(this.form.scanFile)) {\r\n              this.$message.error(\"资料接收方式邮箱格式错误\");\r\n              return;\r\n            }\r\n          }\r\n          // if (this.form.operationType == 2 && !this.form.authFile) {\r\n          //   this.$message.error(\"授权类型必需上传授权书\");\r\n          //   return;\r\n          // }\r\n          var formStr = JSON.stringify(this.form);\r\n          var formData = JSON.parse(formStr);\r\n          if (formData.model && formData.model.length > 0)\r\n            formData.model = formData.model.join(\",\");\r\n          else formData.model = undefined;\r\n          if (formData.requireInfo && formData.requireInfo.length > 0)\r\n            formData.requireInfo = formData.requireInfo.join(\",\");\r\n          else formData.requireInfo = undefined;\r\n          if (formData.infoType && formData.infoType.length > 0)\r\n            formData.infoType = formData.infoType.join(\",\");\r\n          else formData.infoType = undefined;\r\n          if (formData.spec && formData.spec.length > 0)\r\n            formData.spec = formData.spec.join(\",\");\r\n          else formData.spec = undefined;\r\n\r\n          //授权公司\r\n          if (this.authCompanys.length > 0) {\r\n            var array = new Array();\r\n            this.authCompanys.forEach(function (e) {\r\n              array.push(e.value);\r\n            })\r\n            formData.authCompany += \",\" + array.join(\",\")\r\n          }\r\n\r\n          const loading = this.$loading({\r\n            lock: true,//lock的修改符--默认是false\r\n            text: 'Loading',//显示在加载图标下方的加载文案\r\n            spinner: 'el-icon-loading',//自定义加载图标类名\r\n            background: 'rgba(0, 0, 0, 0.7)',//遮罩层颜色\r\n            target: document.querySelector('.main-container')//loadin覆盖的dom元素节点\r\n          });\r\n          document.documentElement.style.overflowY = 'hidden' //禁止底层div滚动\r\n\r\n          if (formData.projectId != null) {\r\n            updateReport(formData).then((response) => {\r\n              //this.msgSuccess(\"修改成功\");\r\n              if (that.businessKey) {\r\n                that.$refs['flow'].taskComplete(\"重新提交\");\r\n              } else {\r\n                that.startFlow(formData);\r\n              }\r\n              setTimeout(() => {\r\n                loading.close();\r\n                document.documentElement.style.overflowY = 'auto' //允许底层div滚动\r\n              }, 1000);\r\n            }).catch(res => {\r\n              console.log(res)\r\n              loading.close();\r\n              document.documentElement.style.overflowY = 'auto' //允许底层div滚动\r\n            });\r\n          } else {\r\n            addReport(formData).then((response) => {\r\n              console.log(\"===addReport=>>>\")\r\n              //this.msgSuccess(\"新增成功\");\r\n              formData.projectId = response.data;\r\n              that.form.projectId = response.data;\r\n              that.startFlow(formData);\r\n              setTimeout(() => {\r\n                loading.close();\r\n                document.documentElement.style.overflowY = 'auto' //允许底层div滚动\r\n              }, 1000);\r\n            }).catch(res => {\r\n              console.log(res)\r\n              loading.close();\r\n              document.documentElement.style.overflowY = 'auto' //允许底层div滚动\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    startFlow(formData) {\r\n      //项目区域\r\n      //var area = formData.district;\r\n      //用户区域 6-11修改，根据用户所在区域判断\r\n      var variables = {};\r\n      variables.PROCESS_AREA = this.$store.state.user.province;\r\n      //是否省负责人角色\r\n      if (this.$store.state.user.roles && this.$store.state.user.roles.includes(\"province_admin\")) {\r\n        variables.isManage = 1;\r\n      } else {\r\n        variables.isManage = 0;\r\n      }\r\n      //新增全走报备审批流程\r\n      // if(formData.operationType == '2'){\r\n      //   variables.isAuth = true;\r\n      // }else{\r\n      //   variables.isAuth = false;\r\n      // }\r\n      //variables.isAuth = false;\r\n      variables.BUSINESSKEY = formData.projectId;\r\n      var taskName = \"项目报备\";\r\n      if (formData.operationType == '2') {\r\n        taskName = \"项目授权\";\r\n      }\r\n      this.$refs['flow'].startFlow(formData.projectId, taskName, variables);\r\n    },\r\n    downloadSQS() {\r\n      this.download(\"海佳集团-授权书.docx\", false);\r\n    },\r\n    downloadCRH() {\r\n      this.download(\"海佳集团-售后服务承诺函.doc\", false);\r\n    },\r\n    handleChange(value) {\r\n      if (!value || value.length == 0) {\r\n        this.selectedOptions = null;\r\n        this.form.province = undefined;\r\n        this.form.district = undefined;\r\n        return\r\n      }\r\n      this.selectedOptions = value;\r\n      var txt = \"\";\r\n      value.forEach(function (item) {\r\n        txt += CodeToText[item] + \"/\";\r\n      });\r\n      if (txt.length > 1) {\r\n        txt = txt.substring(0, txt.length - 1);\r\n        this.form.province = txt;\r\n        this.form.district = this.$store.state.user.province;\r\n      } else {\r\n        this.form.province = undefined;\r\n        this.form.district = undefined;\r\n      }\r\n    },\r\n    handleQueryCityChange(value) {\r\n      this.queryArea = value;\r\n      var txt = \"\";\r\n      value.forEach(function (item) {\r\n        txt += CodeToText[item] + \"/\";\r\n      });\r\n      if (txt.length > 1) {\r\n        txt = txt.substring(0, txt.length - 1);\r\n        this.queryParams.province = txt;\r\n      } else {\r\n        this.queryParams.province = undefined;\r\n      }\r\n    },\r\n    /** 审批 */\r\n    handleComplete() {\r\n      this.$refs['flow'].handleComplete();\r\n    },\r\n    /** 退回 */\r\n    handleReturn() {\r\n      this.$refs['flow'].handleReturn();\r\n    },\r\n    /** 返回页面 */\r\n    goBack() {\r\n      // 关闭当前标签页并返回上个页面\r\n      this.$store.dispatch(\"tagsView/delView\", this.$route);\r\n      this.$router.go(-1)\r\n    },\r\n    removeDomain(index) {\r\n      if (index !== -1) {\r\n        this.authCompanys.splice(index, 1)\r\n      }\r\n    },\r\n    addDomain() {\r\n      this.authCompanys.push({\r\n        value: '',\r\n        key: Date.now()\r\n      });\r\n    }\r\n  },\r\n};\r\n</script>\r\n<style>\r\n.box-card {\r\n  width: 100%;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n@media screen and (max-width: 599px) {\r\n  .el-form .el-col {\r\n    width: 100% !important;\r\n  }\r\n\r\n  .info-type .el-row {\r\n    flex-direction: column;\r\n  }\r\n\r\n  .info-type .el-input {\r\n    width: 100% !important;\r\n  }\r\n\r\n  .mobile-width {\r\n    width: 100% !important;\r\n  }\r\n\r\n  .app-container {\r\n    padding: 0 !important;\r\n  }\r\n}\r\n</style>"]}]}