{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/system/user/profile/userAvatar.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/system/user/profile/userAvatar.vue", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}