{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Notice/index.vue?vue&type=style&index=1&id=6ff69cea&lang=css", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Notice/index.vue", "mtime": 1655640984000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/css-loader/dist/cjs.js", "mtime": 1751171659443}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/loaders/stylePostLoader.js", "mtime": 1751171659924}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/postcss-loader/src/index.js", "mtime": 1751171659554}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLmVsLWJhZGdlX19jb250ZW50LmlzLWZpeGVkIHsKICAgIG1hcmdpbjogMTBweCAtMnB4Owp9Ci5lbC1iYWRnZV9fY29udGVudHsKICBoZWlnaHQ6IDIwcHg7Cn0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4DA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/Notice", "sourcesContent": ["<template>\n  <div>\n    <el-badge :value=\"count\" :max=\"99\">\n      <i class=\"el-icon-bell\" @click=\"click\" />\n    </el-badge>\n  </div>\n</template>\n\n<script>\nimport { nNotice } from '@/api/system/notice'\n\nexport default {\n  data() {\n    return {\n      hasNotice: false,\n      timer: '',\n      count: 0\n    }\n  },\n  mounted() {\n    //定时查\n    this.timer = setInterval(this.checkNotice, 60 * 1000 * 1)\n  },\n  beforeDestroy() {\n    clearInterval(this.timer)\n  },\n  created() {\n    this.checkNotice()\n  },\n  methods: {\n    click() {\n      this.$router.push({ path: '/user/notice' })\n    },\n    checkNotice() {\n      if (this.hasNotice) {\n        return\n      }\n      nNotice().then(response => {\n        this.hasNotice = response.data > 0\n        this.count = response.data\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n::v-deep .el-badge__content.is-fixed.is-dot {\n  right: 5px;\n  top: 10px;\n}\n\n.el-icon-bell {\n  font-size: 20px;\n  cursor: pointer;\n}\n\n\n</style>\n<style>\n.el-badge__content.is-fixed {\n    margin: 10px -2px;\n}\n.el-badge__content{\n  height: 20px;\n}\n</style>"]}]}