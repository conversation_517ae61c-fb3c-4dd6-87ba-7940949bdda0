{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/components/RightPanel/index.vue?vue&type=style&index=1&id=2b17496a&prod&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/components/RightPanel/index.vue", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/css-loader/dist/cjs.js", "mtime": 1751171659443}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/loaders/stylePostLoader.js", "mtime": 1751171659924}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/postcss-loader/src/index.js", "mtime": 1751171659554}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/sass-loader/dist/cjs.js", "mtime": 1751171659051}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5yaWdodFBhbmVsLWJhY2tncm91bmQgewogIHBvc2l0aW9uOiBmaXhlZDsKICB0b3A6IDA7CiAgbGVmdDogMDsKICBvcGFjaXR5OiAwOwogIHRyYW5zaXRpb246IG9wYWNpdHkgLjNzIGN1YmljLWJlemllciguNywgLjMsIC4xLCAxKTsKICBiYWNrZ3JvdW5kOiByZ2JhKDAsIDAsIDAsIC4yKTsKICB6LWluZGV4OiAtMTsKfQoKLnJpZ2h0UGFuZWwgewogIHdpZHRoOiAxMDAlOwogIG1heC13aWR0aDogMjYwcHg7CiAgaGVpZ2h0OiAxMDB2aDsKICBwb3NpdGlvbjogZml4ZWQ7CiAgdG9wOiAwOwogIHJpZ2h0OiAwOwogIGJveC1zaGFkb3c6IDBweCAwcHggMTVweCAwcHggcmdiYSgwLCAwLCAwLCAuMDUpOwogIHRyYW5zaXRpb246IGFsbCAuMjVzIGN1YmljLWJlemllciguNywgLjMsIC4xLCAxKTsKICB0cmFuc2Zvcm06IHRyYW5zbGF0ZSgxMDAlKTsKICBiYWNrZ3JvdW5kOiAjZmZmOwogIHotaW5kZXg6IDQwMDAwOwp9Cgouc2hvdyB7CiAgdHJhbnNpdGlvbjogYWxsIC4zcyBjdWJpYy1iZXppZXIoLjcsIC4zLCAuMSwgMSk7CgogIC5yaWdodFBhbmVsLWJhY2tncm91bmQgewogICAgei1pbmRleDogMjAwMDA7CiAgICBvcGFjaXR5OiAxOwogICAgd2lkdGg6IDEwMCU7CiAgICBoZWlnaHQ6IDEwMCU7CiAgfQoKICAucmlnaHRQYW5lbCB7CiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZSgwKTsKICB9Cn0KCi5oYW5kbGUtYnV0dG9uIHsKICB3aWR0aDogNDhweDsKICBoZWlnaHQ6IDQ4cHg7CiAgcG9zaXRpb246IGFic29sdXRlOwogIGxlZnQ6IC00OHB4OwogIHRleHQtYWxpZ246IGNlbnRlcjsKICBmb250LXNpemU6IDI0cHg7CiAgYm9yZGVyLXJhZGl1czogNnB4IDAgMCA2cHggIWltcG9ydGFudDsKICB6LWluZGV4OiAwOwogIHBvaW50ZXItZXZlbnRzOiBhdXRvOwogIGN1cnNvcjogcG9pbnRlcjsKICBjb2xvcjogI2ZmZjsKICBsaW5lLWhlaWdodDogNDhweDsKICBpIHsKICAgIGZvbnQtc2l6ZTogMjRweDsKICAgIGxpbmUtaGVpZ2h0OiA0OHB4OwogIH0KfQo="}, null]}