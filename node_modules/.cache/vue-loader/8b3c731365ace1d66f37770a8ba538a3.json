{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/project/report/index.vue?vue&type=style&index=0&id=2cca9007&lang=css", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/project/report/index.vue", "mtime": 1753529759964}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/css-loader/dist/cjs.js", "mtime": 1751171659443}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/loaders/stylePostLoader.js", "mtime": 1751171659924}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/postcss-loader/src/index.js", "mtime": 1751171659554}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKQG1lZGlhIHNjcmVlbiBhbmQgKG1pbi13aWR0aDogNjAwcHgpIHsKICAudmlldy1kaWFsb2cgewogICAgd2lkdGg6IDgwJSAhaW1wb3J0YW50OwogICAgZmxvYXQ6IHJpZ2h0OwogIH0KfQoKQG1lZGlhIHNjcmVlbiBhbmQgKG1heC13aWR0aDogNTk5cHgpIHsKICAudmlldy1kaWFsb2cgewogICAgd2lkdGg6IDEwMCUgIWltcG9ydGFudDsKICAgIGZsb2F0OiByaWdodDsKICB9CgogIC5lZGl0LWRpYWxvZyB7CiAgICB3aWR0aDogMTAwJSAhaW1wb3J0YW50OwoKICAgIG1hcmdpbi1ib3R0b206IDA7CiAgICBoZWlnaHQ6IDEwMCU7CiAgICBvdmVyZmxvdzogYXV0bzsKICB9CgogIC5lbC1kaWFsb2c6bm90KC5pcy1mdWxsc2NyZWVuKSB7CiAgICBtYXJnaW4tdG9wOiAwICFpbXBvcnRhbnQ7CiAgfQoKICAuZWwtZm9ybSAuZWwtY29sIHsKICAgIHdpZHRoOiAxMDAlICFpbXBvcnRhbnQ7CiAgfQoKICAuaW5mby10eXBlIC5lbC1yb3cgewogICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsKICB9CgogIC5pbmZvLXR5cGUgLmVsLWlucHV0IHsKICAgIHdpZHRoOiAxMDAlICFpbXBvcnRhbnQ7CiAgfQoKICAubW9iaWxlLXdpZHRoIHsKICAgIHdpZHRoOiAxMDAlICFpbXBvcnRhbnQ7CiAgfQp9CgoubGlrZVRpcCB7CiAgY29sb3I6IHJnYigyNDcsIDExLCAxMSk7CiAgZm9udC1zaXplOiAxMnB4Owp9CgouZWwtbG9hZGluZy1tYXNrIHsKICB6LWluZGV4OiAyMDAxICFpbXBvcnRhbnQ7Cn0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAw8DA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/project/report", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-row :gutter=\"20\">\n      <!--部门数据-->\n      <el-col :span=\"3\" :xs=\"24\">\n        <div class=\"head-container\">\n          <el-tree :data=\"auditStatusTree\" :props=\"defaultProps\" :expand-on-click-node=\"false\"\n            :filter-node-method=\"filterNode\" ref=\"tree\" default-expand-all node-key=\"id\" :current-node-key=\"9\"\n            @node-click=\"handleAuditNodeClick\" />\n          <el-tree :data=\"operationTypeTree\" :props=\"defaultProps\" :expand-on-click-node=\"false\"\n            :filter-node-method=\"filterNode\" ref=\"tree\" default-expand-all node-key=\"id\" :current-node-key=\"0\"\n            @node-click=\"handleOptNodeClick\" />\n          <el-tree v-if=\"showUType\" :data=\"userTypeTree\" :props=\"defaultProps\" :expand-on-click-node=\"false\"\n            :filter-node-method=\"filterNode\" ref=\"tree\" default-expand-all node-key=\"id\" :current-node-key=\"0\"\n            @node-click=\"handleUserNodeClick\" />\n        </div>\n      </el-col>\n      <!--用户数据-->\n      <el-col :span=\"21\" :xs=\"24\">\n        <el-form :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" v-show=\"showSearch\" label-width=\"100px\">\n          <!-- <el-form-item label=\"项目所在地\" label-width=\"100px\" prop=\"area\">\n            <el-cascader\n              size=\"small\"\n              clearable\n              :options=\"options\"\n              :props=\"{ checkStrictly: true, expandTrigger: 'hover' }\"\n              v-model=\"queryArea\"\n              @change=\"handleQueryCityChange\"\n            />\n          </el-form-item> -->\n          <el-form-item label=\"所属用户\" prop=\"belongUser\">\n            <el-input v-model=\"queryParams.belongUser\" placeholder=\"请输入所属用户\" clearable size=\"small\"\n              @keyup.enter.native=\"handleQuery\" />\n          </el-form-item>\n          <!-- <el-form-item label=\"项目所在地\" label-width=\"100px\" prop=\"province\">\n            <el-select\n              v-model=\"queryParams.province\"\n              placeholder=\"请选择项目所在地\"\n              clearable\n              size=\"small\"\n            >\n              <el-option\n                v-for=\"dict in options\"\n                :key=\"dict.dictValue\"\n                :label=\"dict.label\"\n                :value=\"dict.label\"\n              />\n            </el-select>\n          </el-form-item> -->\n          <el-form-item label=\"修改时间\">\n            <!-- <el-input\n              v-model=\"queryParams.updateTime\"\n              placeholder=\"请输入搜索内容\"\n              clearable\n              size=\"small\"\n              @keyup.enter.native=\"handleQuery\"\n            /> -->\n            <!-- <el-date-picker v-model=\"queryParams.updateTime\" value-format=\"yyyy-MM-dd\" align=\"right\" type=\"date\"\n              placeholder=\"选择日期\" :picker-options=\"pickerOptions\">\n            </el-date-picker> -->\n            <el-date-picker v-model=\"queryParams.updateTimeArr\" value-format=\"yyyy-MM-dd\" type=\"daterange\" align=\"right\"\n              unlink-panels range-separator=\"至\" start-placeholder=\"开始日期\" end-placeholder=\"结束日期\"\n              :picker-options=\"searchPickerOptions\">\n            </el-date-picker>\n          </el-form-item>\n          <el-form-item label=\"搜索\" prop=\"search\">\n            <el-select v-model=\"searchField\" size=\"small\" style=\"width: 120px; margin-right: 8px;\">\n              <el-option v-for=\"item in searchFieldOptions\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\" />\n            </el-select>\n            <el-input v-model=\"searchValue\"\n              :placeholder=\"`请输入${searchField === 'all' ? '内容' : searchFieldOptions.find(f => f.value === searchField).label}`\"\n              clearable size=\"small\" style=\"width: 200px\" @keyup.enter.native=\"handleQuery\" />\n          </el-form-item>\n          <el-form-item>\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n          </el-form-item>\n        </el-form>\n\n        <el-row :gutter=\"10\" class=\"mb8\">\n          <el-col :span=\"1.5\">\n            <el-button type=\"primary\" plain icon=\"el-icon-plus\" size=\"mini\" @click=\"handleAdd\"\n              v-hasPermi=\"['project:report:add']\">新增</el-button>\n          </el-col>\n          <el-col :span=\"1.5\">\n            <el-button type=\"success\" plain icon=\"el-icon-edit\" size=\"mini\" :disabled=\"single\" @click=\"handleUpdate\"\n              v-hasPermi=\"['project:report:edit']\">修改</el-button>\n          </el-col>\n          <el-col :span=\"1.5\">\n            <el-button type=\"danger\" plain icon=\"el-icon-delete\" size=\"mini\" :disabled=\"multiple\" @click=\"handleDelete\"\n              v-hasPermi=\"['project:report:remove']\">删除</el-button>\n          </el-col>\n          <el-col :span=\"1.5\">\n            <el-button type=\"info\" plain icon=\"el-icon-upload2\" size=\"mini\" @click=\"handleImport\"\n              v-hasPermi=\"['project:report:import']\">导入</el-button>\n          </el-col>\n          <el-col :span=\"1.5\">\n            <el-button type=\"warning\" plain icon=\"el-icon-download\" size=\"mini\" @click=\"clickExport\"\n              v-hasPermi=\"['project:report:export']\">导出</el-button>\n          </el-col>\n          <el-col :span=\"1.5\">\n            <el-button icon=\"el-icon-printer\" size=\"mini\" @click=\"clickPrint\" type=\"info\"\n              v-hasPermi=\"['project:report:print']\" plain>\n              打印\n            </el-button>\n          </el-col>\n          <right-toolbar :showSearch.sync=\"showSearch\" :showExport.sync=\"showExport\" :showPrint.sync=\"showPrint\"\n            @queryTable=\"getList\" @export=\"handleExport\" @print=\"handlePrint\" :columns=\"columns\"></right-toolbar>\n        </el-row>\n\n        <el-table v-loading=\"loading\" border :data=\"reportList\" @selection-change=\"handleSelectionChange\"\n          id=\"printArea\">\n          <el-table-column type=\"selection\" min-width=\"55\" align=\"center\" />\n          <el-table-column prop=\"projectId\" label=\"项目ID\" min-width=\"80\" />\n          <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" min-width=\"120\">\n            <template slot-scope=\"scope\">\n              <el-button size=\"mini\" type=\"text\" icon=\"el-icon-view\" @click=\"handleView(scope.row)\">查看</el-button>\n              <!-- <el-button v-has=\"[scope.row, 'auth']\"\n                size=\"mini\"\n                type=\"text\"\n                icon=\"el-icon-edit-outline\"\n                @click=\"handleAuth(scope.row)\"\n                >授权</el-button> -->\n              <!-- <el-dropdown v-has=\"[scope.row, 'edit']\">\n                <span class=\"el-dropdown-link\">\n                  &nbsp;&nbsp;<i class=\"el-icon-arrow-down el-icon--right\"></i>\n                </span>\n                <el-dropdown-menu slot=\"dropdown\">\n\n                  <el-dropdown-item icon=\"el-icon-edit\" @click.native=\"handleUpdate(scope.row)\" v-hasPermi=\"['project:report:edit']\">\n                    修改\n                  </el-dropdown-item>\n                  <el-dropdown-item icon=\"el-icon-delete\" @click.native=\"handleDelete(scope.row)\" v-hasPermi=\"['project:report:remove']\">\n                    删除\n                  </el-dropdown-item>\n                </el-dropdown-menu>\n              </el-dropdown> -->\n            </template>\n          </el-table-column>\n          <el-table-column label=\"所属用户\" min-width=\"150\" align=\"center\" prop=\"belongUser\" show-overflow-tooltip\n            v-if=\"columns['0'].visible\">\n            <template slot-scope=\"scope\">\n              <span @click=\"userSearch(scope.row.belongUser)\" class=\"link-type\">{{ scope.row.belongUser }}</span>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"项目编号\" min-width=\"150\" align=\"center\" prop=\"projectNo\" show-overflow-tooltip\n            v-if=\"columns['1'].visible\">\n            <template slot-scope=\"scope\">\n              <span v-html=\"highlightCell('projectNo', scope.row.projectNo)\"></span>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"项目名称\" min-width=\"300\" align=\"center\" prop=\"projectName\" :formatter=\"searchFormat\"\n            show-overflow-tooltip v-if=\"columns['2'].visible\">\n            <template slot-scope=\"scope\">\n              <span v-html=\"highlightCell('projectName', scope.row.projectName)\"></span>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"操作类型\" align=\"center\" prop=\"operationType\" :formatter=\"operationTypeFormat\"\n            v-if=\"columns['3'].visible\" min-width=\"100\">\n          </el-table-column>\n          <!-- <el-table-column\n            label=\"审核状态\"\n            align=\"center\"\n            prop=\"auditStatus\"\n            :formatter=\"auditStatusFormat\"\n            v-if=\"columns['3'].visible\"\n          />\n          <el-table-column\n            label=\"编辑状态\"\n            align=\"center\"\n            prop=\"editStatus\"\n            :formatter=\"editStatusFormat\"\n            v-if=\"columns['5'].visible\"\n          /> -->\n          <el-table-column min-width=\"200\" label=\"项目所在地\" align=\"center\" prop=\"province\" v-if=\"columns['4'].visible\" />\n          <el-table-column min-width=\"200\" label=\"详细地址\" align=\"center\" prop=\"address\" show-overflow-tooltip\n            v-if=\"columns['5'].visible\">\n            <template slot-scope=\"scope\">\n              <span v-html=\"highlightCell('address', scope.row.address)\"></span>\n            </template>\n          </el-table-column>\n          <el-table-column min-width=\"200\" label=\"被授权公司\" align=\"center\" prop=\"authCompany\" show-overflow-tooltip\n            v-if=\"columns['6'].visible\">\n            <template slot-scope=\"scope\">\n              <span v-html=\"highlightCell('authCompany', scope.row.authCompany)\"></span>\n            </template>\n          </el-table-column>\n          <el-table-column min-width=\"200\" label=\"所属经销商\" align=\"center\" prop=\"distributor\" show-overflow-tooltip\n            v-if=\"columns['7'].visible\" />\n          <el-table-column min-width=\"200\" label=\"招标单位\" align=\"center\" prop=\"biddingCompany\" show-overflow-tooltip\n            v-if=\"columns['8'].visible\">\n            <template slot-scope=\"scope\">\n              <span v-html=\"highlightCell('biddingCompany', scope.row.biddingCompany)\"></span>\n            </template>\n          </el-table-column>\n          <el-table-column min-width=\"150\" label=\"投标产品型号\" align=\"center\" prop=\"model\" :formatter=\"modelFormat\"\n            show-overflow-tooltip v-if=\"columns['9'].visible\" />\n          <el-table-column min-width=\"150\" label=\"投标产品规格\" align=\"center\" prop=\"spec\" :formatter=\"specFormat\"\n            v-if=\"columns['10'].visible\" show-overflow-tooltip />\n          <el-table-column min-width=\"150\" label=\"安装面积(m²)\" align=\"center\" prop=\"area\" show-overflow-tooltip\n            v-if=\"columns['11'].visible\" />\n          <el-table-column min-width=\"100\" label=\"所需资料\" align=\"center\" prop=\"requireInfo\" :formatter=\"requireInfoFormat\"\n            show-overflow-tooltip v-if=\"columns['12'].visible\" />\n          <el-table-column label=\"资料类型\" align=\"center\" prop=\"infoType\" :formatter=\"infoTypeFormat\"\n            v-if=\"columns['13'].visible\" />\n          <el-table-column min-width=\"150\" label=\"资料接收邮件\" align=\"center\" prop=\"scanFile\" show-overflow-tooltip\n            v-if=\"columns['14'].visible\" />\n          <el-table-column min-width=\"150\" label=\"资料接收地址\" align=\"center\" prop=\"sendAddress\" show-overflow-tooltip\n            v-if=\"columns['15'].visible\" />\n          <el-table-column min-width=\"150\" label=\"项目所属省份\" align=\"center\" prop=\"belongProvince\"\n            :formatter=\"belongProvinceFormat\" show-overflow-tooltip v-if=\"columns['16'].visible\" />\n          <el-table-column min-width=\"150\" label=\"售后年限\" align=\"center\" prop=\"afterSaleYear\"\n            :formatter=\"afterSaleYearFormat\" show-overflow-tooltip v-if=\"columns['17'].visible\" />\n          <el-table-column min-width=\"150\" label=\"开标日期\" align=\"center\" prop=\"openDate\" show-overflow-tooltip\n            v-if=\"columns['18'].visible\" />\n          <el-table-column min-width=\"150\" label=\"挂网日期\" align=\"center\" prop=\"hangDate\" show-overflow-tooltip\n            v-if=\"columns['19'].visible\" />\n          <el-table-column min-width=\"110\" label=\"提交时间\" align=\"center\" prop=\"createTime\" v-if=\"columns['20'].visible\"\n            show-overflow-tooltip />\n          <el-table-column min-width=\"110\" label=\"修改时间\" align=\"center\" prop=\"updateTime\" v-if=\"columns['21'].visible\"\n            show-overflow-tooltip />\n        </el-table>\n\n        <pagination v-show=\"total > 0\" :total=\"total\" :page.sync=\"queryParams.pageNum\"\n          :limit.sync=\"queryParams.pageSize\" :layout=\"pageLayout\" @pagination=\"getList\" />\n\n        <!-- 添加或修改项目报备对话框 -->\n        <el-dialog :title=\"title\" :visible.sync=\"open\" :close-on-click-modal=\"false\" width=\"80%\"\n          custom-class=\"edit-dialog\" append-to-body>\n          <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"编辑状态\">\n                  <el-radio-group :disabled=\"isAdmin\" v-model=\"form.editStatus\">\n                    <el-radio v-for=\"dict in editStatusOptions\" :key=\"dict.dictValue\"\n                      :label=\"dict.dictValue\">{{ dict.dictLabel }}</el-radio>\n                  </el-radio-group>\n                </el-form-item>\n              </el-col>\n              <!-- <el-col :span=\"12\">\n                <el-form-item label=\"所属用户\" prop=\"belongUser\">\n                  <el-select v-model=\"form.belongUser\" placeholder=\"请选择所属用户\">\n                    <el-option label=\"请选择字典生成\" value=\"\" />\n                  </el-select>\n                </el-form-item>\n              </el-col> -->\n            </el-row>\n            <!-- <el-row>\n              <el-col :span=\"20\">\n                <el-form-item label=\"驳回原因\" prop=\"rejectReason\">\n                  <el-input\n                    v-model=\"form.rejectReason\"\n                    type=\"textarea\"\n                    placeholder=\"请输入内容\"\n                  />\n                </el-form-item>\n              </el-col>\n            </el-row> -->\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"项目编号\" prop=\"projectNo\">\n                  <el-input v-model=\"form.projectNo\" placeholder=\"无编号则为提交时间(年月日时间)\" />\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"项目名称\" prop=\"projectName\">\n                  <el-input v-model=\"form.projectName\" placeholder=\"请输入项目名称\" />\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"项目所在地\" prop=\"province\">\n                  <el-cascader ref=\"cascader\" :options=\"options\" clearable :props=\"{ expandTrigger: 'hover' }\"\n                    v-model=\"selectedOptions\" @change=\"handleChange\">\n                  </el-cascader>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"详细地址\" prop=\"address\">\n                  <el-input v-model=\"form.address\" placeholder=\"请输入详细地址\" />\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"项目所属省份\" prop=\"belongProvince\">\n                  <el-select v-model=\"form.belongProvince\" clearable placeholder=\"请选择所属省份\">\n                    <el-option v-for=\"item in belongProvinceOptions\" :key=\"item.value\" :label=\"item.label\"\n                      :value=\"item.value\">\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"招标单位\" prop=\"biddingCompany\">\n                  <el-input v-model=\"form.biddingCompany\" placeholder=\"请输入招标单位\" />\n                </el-form-item>\n                <!-- <el-form-item label=\"招标单位联系人/联系电话\" prop=\"biddingContact\">\n                  <el-input v-model=\"form.biddingContact\" placeholder=\"请输入联系人/联系电话\" />\n                </el-form-item> -->\n              </el-col>\n            </el-row>\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"挂网日期\" prop=\"hangDate\">\n                  <el-input v-model=\"form.hangDate\" placeholder=\"请输入挂网日期，没有请写无，日期格式XXXX（年）-XX（月）-XX（日)\" />\n                  <!-- <el-date-picker clearable size=\"small\" v-model=\"form.hangDate\" type=\"date\" value-format=\"yyyy-MM-dd\"\n                    placeholder=\"选择挂网日期\">\n                  </el-date-picker> -->\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"开标日期\" prop=\"openDate\">\n                  <el-input v-model=\"form.openDate\" placeholder=\"请输入开标日期，没有请写无，日期格式XXXX（年）-XX（月）-XX（日\" />\n                  <!-- <el-date-picker clearable size=\"small\" v-model=\"form.openDate\" type=\"date\" value-format=\"yyyy-MM-dd\"\n                    placeholder=\"选择开标日期\">\n                  </el-date-picker> -->\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"所属经销商\" prop=\"distributor\">\n                  <el-input v-model=\"form.distributor\" placeholder=\"请输入经销商\" />\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <!-- <el-form-item label=\"招标单位联系人/联系电话\" prop=\"biddingContact\">\n              <el-input v-model=\"form.biddingContact\" placeholder=\"请输入联系人/联系电话\" />\n            </el-form-item> -->\n                <el-form-item label=\"售后年限\" prop=\"afterSaleYear\">\n                  <el-select v-model=\"form.afterSaleYear\" clearable placeholder=\"请选择所属省份\">\n                    <el-option v-for=\"item in afterSaleYearOptions\" :key=\"item.value\" :label=\"item.label\"\n                      :value=\"item.value\">\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <!-- <el-row>\n              <el-col :span=\"12\">\n\n              </el-col>\n            </el-row> -->\n            <!-- <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"开标日期\" prop=\"openDate\">\n                  <el-date-picker\n                    clearable\n                    size=\"small\"\n                    v-model=\"form.openDate\"\n                    type=\"date\"\n                    value-format=\"yyyy-MM-dd\"\n                    placeholder=\"选择开标日期\"\n                  >\n                  </el-date-picker>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"招标方式\" prop=\"biddingType\">\n                  <el-select\n                    v-model=\"form.biddingType\"\n                    placeholder=\"请选择招标方式\"\n                  >\n                    <el-option\n                      v-for=\"dict in biddingTypeOptions\"\n                      :key=\"dict.dictValue\"\n                      :label=\"dict.dictLabel\"\n                      :value=\"dict.dictValue\"\n                    ></el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n            </el-row> -->\n            <el-row>\n              <!-- <el-col :span=\"12\">\n                <el-form-item label=\"预算金额\" prop=\"budgetMoney\">\n                  <el-input\n                    type=\"number\"\n                    v-model=\"form.budgetMoney\"\n                    placeholder=\"请输入预算金额\"\n                  />\n                </el-form-item>\n              </el-col> -->\n              <el-col :span=\"12\">\n                <el-form-item label=\"被授权公司\" prop=\"authCompany\">\n                  <el-input v-model=\"form.authCompany\" placeholder=\"请输入授权公司\" />\n                  <el-link @click=\"addDomain\" type=\"primary\">添加</el-link>\n                </el-form-item>\n                <el-form-item v-for=\"(company, index) in authCompanys\" :label=\"'被授权公司' + (index + 1)\" :key=\"company.key\"\n                  class=\"info-type\">\n                  <el-input v-model=\"company.value\" :placeholder=\"'被授权公司' + (index + 1)\" style=\"max-width: 300px\" />\n                  <el-link @click=\"removeDomain(index)\" type=\"primary\">删除</el-link>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"授权公司联系人/联系电话\" prop=\"authContact\">\n                  <el-input v-model=\"form.authContact\" placeholder=\"请输入授权公司联系人/联系电话\" />\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <!-- <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"招标信息公布网站\" prop=\"biddingNet\">\n                  <el-input\n                    v-model=\"form.biddingNet\"\n                    placeholder=\"请输入招标信息公布网站\"\n                  />\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"招标单位\" prop=\"biddingCompany\">\n                  <el-input\n                    v-model=\"form.biddingCompany\"\n                    placeholder=\"请输入招标单位\"\n                  />\n                </el-form-item>\n              </el-col>\n            </el-row> -->\n            <!-- <el-row v-if=\"form.operationType == 2\">\n              <el-col :span=\"20\">\n                <el-form-item label=\"模板下载\">\n                  <el-col :span=\"8\">\n                    <el-link @click=\"downloadSQS\" type=\"primary\">海佳集团-授权书.docx</el-link>\n                  </el-col>\n                  <el-col :span=\"8\">\n                    <el-link @click=\"downloadCRH\" type=\"primary\">海佳集团-售后服务承诺函.docx</el-link>\n                  </el-col>\n                </el-form-item>\n              </el-col>\n            </el-row> -->\n            <!-- <el-form-item label=\"授权书\" v-if=\"form.operationType == 2\" :required=\"form.operationType == 2\">\n              <fileUpload v-model=\"form.authFile\" :fileType=\"['doc', 'docx']\" />\n            </el-form-item> -->\n            <el-form-item label=\"其余附件\">\n              <fileUpload v-model=\"form.afterSaleFile\" :fileType=\"['doc', 'docx']\" />\n            </el-form-item>\n            <el-row>\n              <el-col :span=\"20\">\n                <el-form-item label=\"投标产品型号\" prop=\"model\" :required=\"true\">\n                  <el-cascader class=\"mobile-width\" style=\"width: 700px\" v-model=\"form.model\" placeholder=\"请输入产品型号\"\n                    :options=\"modelOptions\" :props=\"{ multiple: true }\" clearable filterable></el-cascader>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row>\n              <el-col :span=\"20\">\n                <el-form-item label=\"投标产品规格\" prop=\"spec\" :required=\"true\">\n                  <el-cascader class=\"mobile-width\" style=\"width: 700px\" v-model=\"form.spec\" placeholder=\"请输入产品规格\"\n                    :options=\"specOptions\" :props=\"{ multiple: true }\" clearable filterable></el-cascader>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"安装面积\" prop=\"area\">\n                  <el-input v-model=\"form.area\" type=\"number\" placeholder=\"请输入安装面积\">\n                    <template slot=\"append\">m²</template>\n                  </el-input>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row>\n              <el-col :span=\"20\">\n                <el-form-item label=\"所需资料\">\n                  <el-cascader class=\"mobile-width\" style=\"width: 700px\" v-model=\"form.requireInfo\"\n                    placeholder=\"请输入资料类型\" :options=\"requireInfoOptions\" :props=\"{ multiple: true }\" clearable\n                    filterable></el-cascader>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <!-- <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"资料类型\">\n                  <el-checkbox-group v-model=\"form.infoType\">\n                    <el-checkbox\n                      v-for=\"dict in infoTypeOptions\"\n                      :key=\"dict.dictValue\"\n                      :label=\"dict.dictValue\"\n                    >\n                      {{ dict.dictLabel }}\n                    </el-checkbox>\n                  </el-checkbox-group>\n                </el-form-item>\n              </el-col>\n            </el-row> -->\n            <!-- <el-row>\n              <el-form-item label=\"资料接收方式\" prop=\"infoType\" :required=\"true\">\n                <el-checkbox-group v-model=\"form.infoType\" class=\"info-type\" style=\"display:flex;\">\n                \n                  <el-checkbox label=\"1\" style=\"margin-left:20px;margin-right:10px !important;\">邮件</el-checkbox>\n                  <el-form-item prop=\"scanFile\">\n                    <el-input v-model=\"form.scanFile\" placeholder=\"请输入邮箱地址\" type=\"email\" style=\"width:300px;\" ></el-input>\n                  </el-form-item>\n                \n                  <el-checkbox label=\"2\" style=\"margin-left:20px;margin-right:10px !important;\">邮寄</el-checkbox>\n                  <el-form-item prop=\"sendAddress\">\n                    <el-input v-model=\"form.sendAddress\" placeholder=\"请输入收件地址\" style=\"width:300px;\" ></el-input>\n                  </el-form-item>\n                </el-checkbox-group>\n              </el-form-item>\n            </el-row>\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"邮件发送信息\" prop=\"mailInfo\">\n                  <el-input\n                    v-model=\"form.mailInfo\"\n                    placeholder=\"请输入邮件发送信息\"\n                  />\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"快递单号\" prop=\"expressInfo\">\n                  <el-input\n                    v-model=\"form.expressInfo\"\n                    placeholder=\"请输入快递单号\"\n                  />\n                </el-form-item>\n              </el-col>\n            </el-row> -->\n            <el-row>\n              <el-form-item label=\"资料接收方式\" prop=\"infoType\" :required=\"true\">\n                <el-checkbox-group v-model=\"form.infoType\" class=\"info-type\">\n                  <!-- 选项A -->\n                  <el-row style=\"display: flex; margin-bottom: 22px\">\n                    <el-col :span=\"12\" style=\"display: flex\">\n                      <el-checkbox label=\"1\" style=\"margin-left: 20px; margin-right: 10px !important\">邮件</el-checkbox>\n                      <el-form-item prop=\"scanFile\">\n                        <el-input v-model=\"form.scanFile\" placeholder=\"请输入邮箱地址\" style=\"width: 300px\"\n                          type=\"email\"></el-input>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"12\">\n                      <el-form-item label=\"邮件发送信息\" prop=\"mailInfo\">\n                        <el-input v-model=\"form.mailInfo\" placeholder=\"请输入邮件发送信息\" />\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n                  <!-- 选项B  -->\n                  <el-row style=\"display: flex; margin-bottom: 22px\">\n                    <el-col :span=\"12\" style=\"display: flex\">\n                      <el-checkbox label=\"2\" style=\"margin-left: 20px; margin-right: 10px !important\">邮寄</el-checkbox>\n                      <el-form-item prop=\"sendAddress\">\n                        <el-input v-model=\"form.sendAddress\" placeholder=\"请输入收件地址\" style=\"width: 300px\"></el-input>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"12\">\n                      <el-form-item label=\"快递单号\" prop=\"expressInfo\">\n                        <el-input v-model=\"form.expressInfo\" placeholder=\"请输入快递单号\" />\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n                </el-checkbox-group>\n              </el-form-item>\n            </el-row>\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"操作类型\" prop=\"operationType\">\n                  <el-radio-group @change=\"optTypeChange\" v-model=\"form.operationType\">\n                    <el-radio v-for=\"dict in operationTypeOptions\" :key=\"dict.dictValue\"\n                      :label=\"dict.dictValue\">{{ dict.dictLabel }}</el-radio>\n                  </el-radio-group>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"审核状态\">\n                  <el-radio-group :disabled=\"auditStatusEdit\" v-model=\"form.auditStatus\">\n                    <el-radio v-for=\"dict in auditStatusOptions\" :key=\"dict.dictValue\"\n                      :label=\"dict.dictValue\">{{ dict.dictLabel }}</el-radio>\n                  </el-radio-group>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-form-item label=\"备注\" prop=\"remark\">\n              <el-input v-model=\"form.remark\" type=\"textarea\" placeholder=\"请输入备注\" />\n            </el-form-item>\n          </el-form>\n          <div slot=\"footer\" class=\"dialog-footer\">\n            <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n            <el-button @click=\"cancel\">取 消</el-button>\n          </div>\n        </el-dialog>\n        <!-- 查看项目报备对话框 -->\n        <el-dialog :title=\"title\" :visible.sync=\"viewOpen\" :fullscreen=\"true\" :lock-scroll=\"true\"\n          :destroy-on-close=\"true\" custom-class=\"view-dialog\" @close=\"viewOk\">\n          <flowable v-if=\"viewOpen\" ref=\"flow\" :procDefKey=\"defKey\" :procInsId=\"procInsId\" :taskId=\"taskId\"\n            :bizKey=\"bizKey\" :finished=\"finished\" :isAuthImages=\"isAuthImages\">\n            <template v-slot:title>项目信息</template>\n            <template v-slot:content>\n              <el-descriptions label-width=\"120px\" :column=\"isMobile ? 1 : 3\">\n                <el-descriptions-item label=\"操作类型\" prop=\"operationType\">\n                  {{ view.operationType }}\n                </el-descriptions-item>\n                <el-descriptions-item label=\"审核状态\">\n                  {{ view.auditStatus }}\n                </el-descriptions-item>\n                <!-- <el-descriptions-item label=\"驳回原因\" prop=\"rejectReason\">\n                {{ view.rejectReason }}\n              </el-descriptions-item> -->\n                <el-descriptions-item label=\"项目ID\">\n                  {{ view.projectId }}\n                </el-descriptions-item>\n                <el-descriptions-item label=\"项目编号\" prop=\"projectNo\">\n                  {{ view.projectNo }}\n                </el-descriptions-item>\n                <el-descriptions-item label=\"项目名称\" span=\"3\" prop=\"projectName\">\n                  <el-popover v-if=\"likecount > 0\" placement=\"top-start\" title=\"相似项目\" width=\"450\" trigger=\"hover\">\n                    <el-table :data=\"likeList\">\n                      <el-table-column width=\"100\" property=\"value\" label=\"项目ID\"></el-table-column>\n                      <el-table-column width=\"300\" property=\"name\" label=\"项目名称\" show-overflow-tooltip></el-table-column>\n                    </el-table>\n                    <el-badge slot=\"reference\" :value=\"likecount\" class=\"item\">\n                      {{ view.projectName\n                      }}<span class=\"likeTip\">&nbsp;&nbsp;存在相似项目</span>\n                    </el-badge>\n                  </el-popover>\n                  <span v-if=\"!likecount\">{{ view.projectName }}</span>\n                </el-descriptions-item>\n                <el-descriptions-item label=\"编辑状态\">\n                  {{ view.editStatus }}\n                </el-descriptions-item>\n                <el-descriptions-item label=\"项目所在地\" prop=\"area\">\n                  {{ view.province }}\n                </el-descriptions-item>\n                <el-descriptions-item label=\"详细地址\">\n                  {{ view.address }}\n                </el-descriptions-item>\n                <el-descriptions-item label=\"安装面积(m²)\">\n                  {{ view.area }}\n                </el-descriptions-item>\n                <el-descriptions-item label=\"被授权公司\">\n                  {{ view.authCompany }}\n                </el-descriptions-item>\n                <el-descriptions-item label=\"授权公司联系人/联系电话\">\n                  {{ view.authContact }}\n                </el-descriptions-item>\n                <el-descriptions-item label=\"招标单位\">\n                  {{ view.biddingCompany }}\n                </el-descriptions-item>\n                <!-- <el-descriptions-item label=\"招标单位联系人/联系电话\">\n                  {{ view.biddingContact }}\n                </el-descriptions-item> -->\n                <el-descriptions-item label=\"项目所属省份\" prop=\"belongProvince\">\n                  {{ view.belongProvince }}\n                </el-descriptions-item>\n                <el-descriptions-item label=\"售后年限\" prop=\"afterSaleYear\">\n                  {{ view.afterSaleYear }}\n                </el-descriptions-item>\n                <el-descriptions-item label=\"开标日期\">\n                  {{ view.openDate }}\n                </el-descriptions-item>\n                <el-descriptions-item label=\"挂网日期\">\n                  {{ view.hangDate }}\n                </el-descriptions-item>\n                <el-descriptions-item label=\"所属经销商\" prop=\"distributor\">\n                  {{ view.distributor }}\n                </el-descriptions-item>\n                <el-descriptions-item label=\"所需资料\" span=\"3\">\n                  <template v-if=\"infoList1.length > 0\">\n                    <ul class=\"infinite-list\" v-infinite-scroll=\"load\" style=\"overflow: auto\">\n                      <li v-for=\"(i, index) in infoList1\" v-bind:key=\"index\" class=\"infinite-list-item\">\n                        {{ i.dictLabel }}\n                        <el-link v-if=\"i.targetUrl && view.auditStatus == '已审批'\"\n                          @click.prevent=\"handleDownload(i.targetUrl)\" type=\"primary\">下载</el-link>\n                      </li>\n                    </ul>\n                    <ul class=\"infinite-list\" v-infinite-scroll=\"load\" style=\"overflow: auto\">\n                      <li v-for=\"(i, index) in infoList2\" v-bind:key=\"index\" class=\"infinite-list-item\">\n                        {{ i.dictLabel }}\n                        <el-link target=\"_blank\" v-if=\"i.targetUrl && view.auditStatus == '已审批'\"\n                          @click.prevent=\"handleDownload(i.targetUrl)\" type=\"primary\">下载</el-link>\n                      </li>\n                    </ul>\n                  </template>\n                  <span v-else>{{ view.requireInfo }}</span>\n                </el-descriptions-item>\n                <el-descriptions-item label=\"投标产品型号\">\n                  {{ view.model }}\n                </el-descriptions-item>\n                <el-descriptions-item label=\"投标产品规格\">\n                  {{ view.spec }}\n                </el-descriptions-item>\n                <!-- <el-descriptions-item label=\"资料类型\">\n                {{ view.infoType }}\n              </el-descriptions-item> -->\n                <el-descriptions-item label=\"授权书\" v-if=\"view.operationType == '授权'\">\n                  <el-link target=\"_blank\" v-if=\"view.authFile\" :href=\"view.authFile\" type=\"primary\">下载</el-link>\n                </el-descriptions-item>\n                <el-descriptions-item label=\"其余附件\" v-if=\"view.operationType == '授权'\">\n                  <el-link target=\"_blank\" v-if=\"view.afterSaleFile\" :href=\"view.afterSaleFile\"\n                    type=\"primary\">下载</el-link>\n                </el-descriptions-item>\n\n                <el-descriptions-item label=\"授权书图片\" v-if=\"view.operationType == '授权'\">\n                  <el-link target=\"_blank\" v-if=\"view.authImages && view.auditStatus === '已审批'\" :href=\"view.authImages\"\n                    type=\"primary\">下载</el-link>\n                </el-descriptions-item>\n                <el-descriptions-item label=\"资料接收邮件\" prop=\"scanFile\">\n                  {{ view.scanFile }}\n                </el-descriptions-item>\n                <el-descriptions-item label=\"资料接收地址\" prop=\"sendAddress\">\n                  {{ view.sendAddress }}\n                </el-descriptions-item>\n                <el-descriptions-item label=\"邮件发送信息\" prop=\"mailInfo\">\n                  {{ view.mailInfo }}\n                </el-descriptions-item>\n                <el-descriptions-item label=\"快递单号\" prop=\"expressInfo\">\n                  {{ view.expressInfo }}\n                </el-descriptions-item>\n                <el-descriptions-item label=\"备注\" prop=\"remark\">\n                  {{ view.remark }}\n                </el-descriptions-item>\n                <el-descriptions-item label=\"提交时间\">\n                  {{ view.createTime }}\n                </el-descriptions-item>\n                <el-descriptions-item label=\"修改时间\">\n                  {{ view.updateTime }}\n                </el-descriptions-item>\n              </el-descriptions>\n            </template>\n          </flowable>\n        </el-dialog>\n        <!-- 项目导入对话框 -->\n        <el-dialog :title=\"upload.title\" :visible.sync=\"upload.open\" width=\"400px\" append-to-body>\n          <el-upload ref=\"upload\" :limit=\"1\" accept=\".xlsx, .xls\" :headers=\"upload.headers\"\n            :action=\"upload.url + '?updateSupport=' + upload.updateSupport\" :disabled=\"upload.isUploading\"\n            :on-progress=\"handleFileUploadProgress\" :on-success=\"handleFileSuccess\" :auto-upload=\"false\" drag>\n            <i class=\"el-icon-upload\"></i>\n            <div class=\"el-upload__text\">\n              将文件拖到此处，或\n              <em>点击上传</em>\n            </div>\n            <div class=\"el-upload__tip\" slot=\"tip\">\n              <el-checkbox v-model=\"upload.updateSupport\" />是否更新已经存在的项目数据\n              <el-link type=\"info\" style=\"font-size: 12px\" @click=\"importTemplate\">下载模板</el-link>\n            </div>\n            <div class=\"el-upload__tip\" style=\"color: red\" slot=\"tip\">\n              提示：仅允许导入\"xls\"或\"xlsx\"格式文件！\n            </div>\n          </el-upload>\n          <div slot=\"footer\" class=\"dialog-footer\">\n            <el-button type=\"primary\" @click=\"submitFileForm\">确 定</el-button>\n            <el-button @click=\"upload.open = false\">取 消</el-button>\n          </div>\n        </el-dialog>\n      </el-col>\n    </el-row>\n  </div>\n</template>\n\n<script>\nimport {\n  listReport,\n  getReport,\n  delReport,\n  addReport,\n  updateReport,\n  exportReport,\n  importTemplate,\n  printReport,\n  checkNameUnique,\n  getLikeList,\n  authReport,\n} from \"@/api/project/report\";\nimport { getToken } from \"@/utils/auth\";\nimport FileUpload from \"@/components/FileUpload\";\nimport flowable from \"@/views/flowable/task/record/view\";\nimport { getInsIdByBizKey } from \"@/api/flowable/todo\";\nimport { regionData, CodeToText, TextToCode } from \"element-china-area-data\";\nimport print from \"print-js\";\nexport default {\n  name: \"Report\",\n  components: {\n    FileUpload,\n    print,\n    flowable,\n  },\n  data() {\n    var infoTypeValueVali = (rule, value, callback) => {\n      if (this.form.infoType.indexOf(\"1\") >= 0 && !this.form.scanFile) {\n        callback(new Error(\"邮箱地址必填\"));\n        return;\n      }\n      callback();\n    };\n    var infoTypeValueVali2 = (rule, value, callback) => {\n      if (this.form.infoType.indexOf(\"2\") >= 0 && !this.form.sendAddress) {\n        callback(new Error(\"收件地址必填\"));\n        return;\n      }\n      callback();\n    };\n    var nameVali = (rule, value, callback) => {\n      if (!this.form.projectName) {\n        callback(new Error(\"项目名称必填\"));\n      } else {\n        if (/\\s+/g.test(this.form.projectName)) {\n          callback(new Error(\"项目名称不规范\"));\n          return;\n        }\n        checkNameUnique({\n          projectName: this.form.projectName,\n          projectId: this.form.projectId,\n        }).then((response) => {\n          if (response.data == 0) {\n            callback();\n          } else {\n            callback(new Error(\"项目名称已存在\"));\n          }\n        });\n      }\n    };\n    var codeVali = (rule, value, callback) => {\n      if (!that.form.projectNo) {\n        callback(new Error(\"项目编号必填\"));\n      } else if (/\\s+/g.test(that.form.projectNo)) {\n        callback(new Error(\"项目编号不规范\"));\n        return;\n      }\n      callback();\n    };\n    var authFileValueVali = (rule, value, callback) => {\n      if (this.form.operationType == 2 && !this.form.authFile) {\n        callback(new Error(\"授权类型必传授权书\"));\n      }\n      callback();\n    };\n    var openDateVali = (rule, value, callback) => {\n      console.log(123566)\n      if (!that.form.openDate) {\n        callback(new Error(\"开标日期必填\"));\n        return;\n      } else if (value === \"无\") {\n        callback();\n        return;\n      } else if (!/^\\d{4}-\\d{2}-\\d{2}$/.test(value)) {\n        callback(new Error(\"开标日期格式不合法，示例2025-01-01\"));\n        return;\n      }\n      callback();\n    };\n    var hangDateVali = (rule, value, callback) => {\n      if (!that.form.hangDate) {\n        callback(new Error(\"挂网日期必填\"));\n        return;\n      } else if (value === \"无\") {\n        callback();\n        return;\n      } else if (!/^\\d{4}-\\d{2}-\\d{2}$/.test(value)) {\n        callback(new Error(\"挂网日期格式不合法，示例2025-01-01\"));\n        return;\n      }\n      callback();\n    };\n    return {\n      isMobile: false,\n      pageLayout: \"total, sizes, prev, pager, next, jumper\",\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      showExport: false,\n      showPrint: false,\n      // 总条数\n      total: 0,\n      // 项目报备表格数据\n      reportList: [],\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      open: false,\n      // 操作类型字典\n      operationTypeOptions: [],\n      // 审核状态字典\n      auditStatusOptions: [],\n      // 编辑状态字典\n      editStatusOptions: [],\n      // 招标方式字典\n      biddingTypeOptions: [],\n      // 投标产品型号字典\n      modelOptions: [],\n      modelOption1: [],\n      // 所需资料字典\n      requireInfoOptions: [],\n      requireInfoOption1: [],\n      // 资料类型字典\n      infoTypeOptions: [],\n      specOptions: [],\n      specOption1: [],\n      // 所属省份字典\n      belongProvinceOptions: [],\n      belongProvinceOptions1: [],\n      // 售后年限\n      afterSaleYearOptions: [],\n      afterSaleYearOptions1: [],\n      // 项目导入参数\n      upload: {\n        // 是否显示弹出层（用户导入）\n        open: false,\n        // 弹出层标题（用户导入）\n        title: \"\",\n        // 是否禁用上传\n        isUploading: false,\n        // 是否更新已经存在的用户数据\n        updateSupport: 0,\n        // 设置上传的请求头部\n        headers: { Authorization: \"Bearer \" + getToken() },\n        // 上传的地址\n        url: process.env.VUE_APP_BASE_API + \"/project/report/importData\",\n      },\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        projectNo: null,\n        projectName: null,\n        operationType: null,\n        auditStatus: null,\n        province: null,\n        userType: null,\n        belongUser: null,\n        updateTimeArr: [],\n        spare1: null,\n        address: null,\n        biddingCompany: null,\n        authCompany: null,\n        fullField: null\n      },\n      // 表单参数\n      form: {},\n      // 表单校验\n      // 表单校验\n      rules: {\n        operationType: [{ required: true, message: \"操作类型必选\" }],\n        projectNo: [{ required: true, validate: codeVali, trigger: \"blur\" }],\n        projectName: [{ required: true, validate: nameVali, trigger: \"blur\" }],\n        address: [\n          { required: true, message: \"详细地址不能为空\", trigger: \"blur\" },\n        ],\n        biddingCompany: [\n          { required: true, message: \"招标单位不能为空\", trigger: \"blur\" },\n        ],\n        openDate: [\n          { required: true, validate: openDateVali, trigger: \"blur\" },\n        ],\n        afterSaleYear: [\n          { required: true, message: \"售后年限不能为空\", trigger: \"blur\" },\n        ],\n        hangDate: [\n          { required: true, validate: hangDateVali, trigger: \"blur\" },\n        ],\n        belongProvince: [\n          { required: true, message: \"售后年限不能为空\", trigger: \"blur\" },\n        ],\n        distributor: [\n          { required: true, message: \"所属经销商不能为空\", trigger: \"blur\" },\n        ],\n        scanFile: [{ validator: infoTypeValueVali, trigger: \"blur\" }],\n        sendAddress: [{ validator: infoTypeValueVali2, trigger: \"blur\" }],\n        model: [{ required: true, message: \"投标产品型号必选\" }],\n        spec: [{ required: true, message: \"投标产品规格必选\" }],\n        province: [{ required: true, message: \"项目所在地必选\" }],\n        infoType: [\n          { required: true, message: \"资料接收方式必选\", trigger: \"change\" },\n        ],\n        authFile: [{ validator: authFileValueVali, trigger: \"change\" }],\n        biddingContact: [\n          { required: true, message: \"招标单位联系人/联系电话必填\" },\n        ],\n        authContact: [\n          { required: true, message: \"授权公司联系人/联系电话必填\" },\n        ],\n      },\n      // 列信息\n      columns: [\n        { key: \"belongUser\", index: 1, label: `所属用户`, visible: true },\n        { key: \"projectNo\", index: 2, label: `项目编号`, visible: true },\n        { key: \"projectName\", index: 3, label: `项目名称`, visible: true },\n        { key: \"operationType\", index: 4, label: `操作类型`, visible: true },\n        { key: \"province\", index: 5, label: `项目所在地`, visible: true },\n        { key: \"address\", index: 6, label: `详细地址`, visible: true },\n        { key: \"authCompany\", index: 7, label: `被授权公司`, visible: true },\n        { key: \"distributor\", index: 8, label: `所属经销商`, visible: true },\n        { key: \"biddingCompany\", index: 9, label: `招标单位`, visible: true },\n        { key: \"model\", index: 10, label: `投标产品型号`, visible: true },\n        { key: \"spec\", index: 11, label: `投标产品规格`, visible: true },\n        { key: \"area\", index: 12, label: `安装面积`, visible: true },\n        { key: \"requireInfo\", index: 13, label: `所需资料`, visible: true },\n        { key: \"infoType\", index: 14, label: `资料类型`, visible: true },\n        { key: \"scanFile\", index: 15, label: `资料接收邮件`, visible: true },\n        { key: \"scanFile\", index: 16, label: `资料接收地址`, visible: true },\n        { key: \"belongProvince\", index: 17, label: `项目所属省份`, visible: true },\n        { key: \"afterSaleYear\", index: 18, label: `售后年限`, visible: true },\n        { key: \"openDate\", index: 19, label: `开标日期`, visible: true },\n        { key: \"hangDate\", index: 20, label: `挂网日期`, visible: true },\n        { key: \"createTime\", index: 21, label: `提交时间`, visible: true },\n        { key: \"updateTime\", index: 22, label: `修改时间`, visible: true },\n        // { key: \"auditStatus\", index: 19, label: `审核状态`, visible: false },\n        // { key: \"editStatus\", index: 20, label: `编辑状态`, visible: false },\n        // { key: \"11\", index: 21, label: `授权书`, visible: false },\n        //{ key: \"12\", index: 23, label: `售后服务承诺函`, visible: false },\n      ],\n      options: regionData,\n      selectedOptions: [],\n      queryArea: [],\n      viewOpen: false,\n      view: {},\n      infoList1: [],\n      infoList2: [],\n      defKey: \"process_project_report\",\n      procInsId: undefined,\n      taskId: undefined,\n      finished: true,\n      bizKey: undefined,\n      auditStatusTree: [],\n      operationTypeTree: [],\n      userTypeTree: [],\n      defaultProps: {\n        children: \"children\",\n        label: \"label\",\n      },\n      oldOperationType: undefined,\n      showUType: true,\n      chooseOptType: undefined,\n      chooseAuditStatus: undefined,\n      chooseUserId: undefined,\n      chooseEditStatus: undefined,\n      chooseSpare2: undefined,\n      likeList: undefined,\n      likeCount: undefined,\n      authCompanys: [],\n      isAdmin: true,\n      auditStatusEdit: true,\n      isAuthImages: false,\n      searchPickerOptions: {\n        shortcuts: [{\n          text: '最近一周',\n          onClick(picker) {\n            const end = new Date();\n            const start = new Date();\n            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);\n            picker.$emit('pick', [start, end]);\n          }\n        }, {\n          text: '最近一个月',\n          onClick(picker) {\n            const end = new Date();\n            const start = new Date();\n            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);\n            picker.$emit('pick', [start, end]);\n          }\n        }, {\n          text: '最近三个月',\n          onClick(picker) {\n            const end = new Date();\n            const start = new Date();\n            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);\n            picker.$emit('pick', [start, end]);\n          }\n        }]\n      },\n      pickerOptions: {\n        disabledDate(time) {\n          return time.getTime() > Date.now();\n        },\n        shortcuts: [\n          {\n            text: \"今天\",\n            onClick(picker) {\n              picker.$emit(\"pick\", new Date());\n            },\n          },\n          {\n            text: \"昨天\",\n            onClick(picker) {\n              const date = new Date();\n              date.setTime(date.getTime() - 3600 * 1000 * 24);\n              picker.$emit(\"pick\", date);\n            },\n          },\n          {\n            text: \"一周前\",\n            onClick(picker) {\n              const date = new Date();\n              date.setTime(date.getTime() - 3600 * 1000 * 24 * 7);\n              picker.$emit(\"pick\", date);\n            },\n          },\n        ],\n      },\n      searchField: 'all', // 新增：当前选中的搜索字段，默认全字段\n      searchFieldOptions: [\n        { label: '全字段', value: 'all' },\n        { label: '项目编号', value: 'projectNo' },\n        { label: '项目名称', value: 'projectName' },\n        { label: '详细地址', value: 'address' },\n        { label: '招标单位', value: 'biddingCompany' },\n        { label: '授权公司', value: 'authCompany' }\n      ],\n      searchValue: '', // 新增：搜索内容\n      highlightFields: ['projectNo', 'projectName', 'address', 'biddingCompany', 'authCompany'], // 新增：高亮字段\n    };\n  },\n  activated() {\n    console.log(\"=report index==>>activated\");\n    this.getList();\n  },\n  created() {\n    this.getList();\n    this.getDicts(\"pr_operation_type\").then((response) => {\n      this.operationTypeOptions = response.data;\n      var opt = [];\n      opt.push({ id: 0, label: \"全部\" });\n      response.data.forEach((elem, index) => {\n        var obj = {};\n        obj.label = elem.dictLabel;\n        obj.id = elem.dictValue;\n        opt.push(obj);\n      });\n      var operationType = {};\n      operationType.label = \"操作类型\";\n      operationType.children = opt;\n      var operationTypes = [];\n      operationTypes.push(operationType);\n      this.operationTypeTree = operationTypes;\n    });\n    this.getDicts(\"pr_audit_status\").then((response) => {\n      var type = 0;\n      if (this.$store.state.user.roles) {\n        if (this.$store.state.user.roles.includes(\"common\")) {\n          type = 1;\n        }\n        if (this.$store.state.user.roles.includes(\"province_admin\")) {\n          type = 2;\n        }\n        if (this.$store.state.user.roles.includes(\"report_admin\")) {\n          type = 3;\n        }\n      }\n      this.auditStatusOptions = response.data;\n      var opt = [];\n      opt.push({ id: 9, label: \"全部\" });\n      if (type == 2 || type == 3) {\n        opt.push({ id: 10, label: \"未审批\" });\n      }\n      response.data.forEach((elem, index) => {\n        var obj = {};\n        obj.label = elem.dictLabel;\n        obj.id = elem.dictValue;\n        opt.push(obj);\n      });\n      var auditStatusTree = {};\n      auditStatusTree.label = \"审核状态\";\n      auditStatusTree.children = opt;\n      var auditStatusTrees = [];\n      auditStatusTrees.push(auditStatusTree);\n      this.auditStatusTree = auditStatusTrees;\n    });\n    this.getDicts(\"pr_edit_status\").then((response) => {\n      this.editStatusOptions = response.data;\n    });\n    // this.getDicts(\"pr_bidding_type\").then((response) => {\n    //   this.biddingTypeOptions = response.data;\n    // });\n    this.getDicts(\"pr_model\").then((response) => {\n      this.modelOption1 = response.data;\n      var opt = [];\n      response.data.forEach((elem, index) => {\n        var obj = {};\n        obj.label = elem.dictLabel;\n        obj.value = elem.dictValue;\n        opt.push(obj);\n      });\n      this.modelOptions = opt;\n    });\n    this.getDicts(\"pr_spec\").then((response) => {\n      this.specOption1 = response.data;\n      var opt = [];\n      response.data.forEach((elem, index) => {\n        var obj = {};\n        obj.label = elem.dictLabel;\n        obj.value = elem.dictValue;\n        opt.push(obj);\n      });\n      this.specOptions = opt;\n    });\n    this.getDicts(\"pr_info\").then((response) => {\n      this.requireInfoOption1 = response.data;\n      var opt = [];\n      response.data.forEach((elem, index) => {\n        var obj = {};\n        obj.label = elem.dictLabel;\n        obj.value = elem.dictValue;\n        opt.push(obj);\n      });\n      this.requireInfoOptions = opt;\n    });\n    this.getDicts(\"pr_province\").then((response) => {\n      this.belongProvinceOptions1 = response.data;\n      var opt = [];\n      response.data.forEach((elem, index) => {\n        var obj = {};\n        obj.label = elem.dictLabel;\n        obj.value = elem.dictValue;\n        opt.push(obj);\n      });\n      this.belongProvinceOptions = opt;\n    });\n    this.getDicts(\"pr_after_sale_year\").then((response) => {\n      this.afterSaleYearOptions1 = response.data;\n      var opt = [];\n      response.data.forEach((elem, index) => {\n        var obj = {};\n        obj.label = elem.dictLabel;\n        obj.value = elem.dictValue;\n        opt.push(obj);\n      });\n      this.afterSaleYearOptions = opt;\n    });\n    this.getDicts(\"pr_data_type\").then((response) => {\n      this.infoTypeOptions = response.data;\n    });\n\n    var opt = [];\n    opt.push({ id: 0, label: \"全部\" });\n    opt.push({ id: 2, label: \"普通用户\" });\n    opt.push({ id: 10, label: \"省负责人\" });\n\n    var userType = {};\n    userType.label = \"所属用户\";\n    userType.children = opt;\n    var userTypes = [];\n    userTypes.push(userType);\n    this.userTypeTree = userTypes;\n    if (\n      this.$store.state.user.roles &&\n      this.$store.state.user.roles.includes(\"common\")\n    ) {\n      this.showUType = false;\n    }\n    if (this._isMobile()) {\n      this.isMobile = true;\n      this.pageLayout = \"total, prev, next, jumper\";\n    }\n    if (\n      this.$store.state.user.roles &&\n      this.$store.state.user.roles.includes(\"report_admin\")\n    ) {\n      this.isAdmin = false;\n    }\n  },\n  methods: {\n    _isMobile() {\n      let flag = navigator.userAgent.match(\n        /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i\n      );\n      return flag;\n    },\n    /** 查询项目报备列表 */\n    getList() {\n      this.loading = true;\n      listReport(this.queryParams).then((response) => {\n        this.reportList = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    handleDownload(url) {\n      const a = document.createElement(\"a\"); // 创建一个HTML 元素\n      a.setAttribute(\"target\", \"_blank\");\n      a.setAttribute(\"download\", \"\"); //download属性\n      const href =\n        \"https://report.clled.com/prod-api/common/download/resource?resource=\" +\n        url;\n      console.log(href);\n      a.setAttribute(\"href\", href); // href链接\n      a.click(); // 自执行点击事件\n    },\n    // 审核状态节点单击事件\n    handleAuditNodeClick(data) {\n      if (data.id == 9) {\n        this.queryParams.auditStatus = undefined;\n        this.queryParams.node = undefined;\n        this.queryParams.spare1 = undefined;\n      } else {\n        if (data.id == 1 || data.id == 10) {\n          this.queryParams.auditStatus = 1;\n          if (this.$store.state.user.roles) {\n            if (this.$store.state.user.roles.includes(\"province_admin\")) {\n              this.queryParams.node = \"省负责人\";\n              if (data.id == 10) {\n                this.queryParams.spare1 = \"=\";\n              } else {\n                this.queryParams.spare1 = \"!=\";\n              }\n            }\n            if (this.$store.state.user.roles.includes(\"report_admin\")) {\n              this.queryParams.node = \"审核员\";\n              if (data.id == 10) {\n                this.queryParams.spare1 = \"=\";\n              } else {\n                this.queryParams.spare1 = \"!=\";\n              }\n            }\n          }\n        } else {\n          this.queryParams.auditStatus = data.id;\n          this.queryParams.node = undefined;\n          this.queryParams.spare1 = undefined;\n        }\n      }\n      this.getList();\n    },\n    // 操作类型节点单击事件\n    handleOptNodeClick(data) {\n      if (data.id == 0) {\n        this.queryParams.operationType = undefined;\n      } else {\n        this.queryParams.operationType = data.id;\n      }\n      this.getList();\n    },\n    // 用户类型节点单击事件\n    handleUserNodeClick(data) {\n      if (data.id == 0) {\n        this.queryParams.userType = undefined;\n      } else {\n        this.queryParams.userType = data.id;\n      }\n      this.getList();\n    },\n    // 筛选节点\n    filterNode(value, data) {\n      if (!value) return true;\n      return data.label.indexOf(value) !== -1;\n    },\n    searchFormat(row, column) {\n      if (\n        row.indexOf(this.queryParams.spare1) !== -1 &&\n        this.queryParams.spare1 !== \"\"\n      ) {\n        return row.replace(\n          this.queryParams.spare1,\n          '<font color=\"#f00\">' + this.queryParams.spare1 + \"</font>\"\n        );\n      } else {\n        return row;\n      }\n    },\n    // 操作类型字典翻译\n    operationTypeFormat(row, column) {\n      return this.selectDictLabel(this.operationTypeOptions, row.operationType);\n    },\n    // 审核状态字典翻译\n    auditStatusFormat(row, column) {\n      return this.selectDictLabel(this.auditStatusOptions, row.auditStatus);\n    },\n    // 编辑状态字典翻译\n    editStatusFormat(row, column) {\n      return this.selectDictLabel(this.editStatusOptions, row.editStatus);\n    },\n    // 招标方式字典翻译\n    biddingTypeFormat(row, column) {\n      return this.selectDictLabel(this.biddingTypeOptions, row.biddingType);\n    },\n    // 投标产品型号字典翻译\n    modelFormat(row, column) {\n      return this.selectDictLabels(this.modelOption1, row.model);\n    },\n    // 投标产品规格字典翻译\n    specFormat(row, column) {\n      return this.selectDictLabels(this.specOption1, row.spec);\n    },\n    // 所需资料字典翻译\n    requireInfoFormat(row, column) {\n      if (row.requireInfo) {\n        return this.selectDictLabels(this.requireInfoOption1, row.requireInfo);\n      }\n    },\n    // 资料类型字典翻译\n    infoTypeFormat(row, column) {\n      return this.selectDictLabels(this.infoTypeOptions, row.infoType);\n    },\n    // 所属省份字典翻译\n    belongProvinceFormat(row, column) {\n      return this.selectDictLabels(this.belongProvinceOptions1, row.belongProvince);\n    },\n    // 售后年限字典翻译\n    afterSaleYearFormat(row, column) {\n      return this.selectDictLabels(this.afterSaleYearOptions1, row.afterSaleYear);\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    viewOk() {\n      this.viewOpen = false;\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        projectId: null,\n        projectNo: null,\n        projectName: null,\n        operationType: null,\n        auditStatus: \"0\",\n        rejectReason: null,\n        province: null,\n        city: null,\n        district: null,\n        address: null,\n        editStatus: \"0\",\n        belongUser: null,\n        biddingCompany: null,\n        openDate: null,\n        biddingType: null,\n        budgetMoney: null,\n        authCompany: null,\n        biddingNet: null,\n        distributor: null,\n        model: [],\n        spec: [],\n        area: null,\n        authFile: null,\n        afterSaleFile: null,\n        requireInfo: [],\n        infoType: [],\n        scanFile: null,\n        sendAddress: null,\n        mailInfo: null,\n        expressInfo: null,\n        remark: null,\n        spare1: null,\n        spare2: null,\n      };\n      this.resetForm(\"form\");\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      // 清空所有相关字段\n      this.queryParams.projectNo = null;\n      this.queryParams.projectName = null;\n      this.queryParams.address = null;\n      this.queryParams.biddingCompany = null;\n      this.queryParams.authCompany = null;\n      this.queryParams.fullField = null;\n\n      if (this.searchField === 'all') {\n        this.queryParams.fullField = this.searchValue; // 假设后端 fullField 做全字段模糊\n      } else {\n        this.queryParams[this.searchField] = this.searchValue;\n      }\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.queryArea = [];\n      this.queryParams = {\n        pageNum: 1,\n        pageSize: 10,\n        projectNo: null,\n        projectName: null,\n        operationType: null,\n        auditStatus: null,\n        province: null,\n        userType: null,\n        belongUser: null,\n        spare1: null,\n        address: null,\n        biddingCompany: null,\n        authCompany: null,\n        fullField: null\n      };\n      this.searchField = 'all';\n      this.searchValue = '';\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map((item) => item.projectId);\n      this.single = selection.length !== 1;\n      this.multiple = !selection.length;\n      this.chooseOptType = selection[0].operationType;\n      this.chooseAuditStatus = selection[0].auditStatus;\n      this.chooseUserId = selection[0].userId;\n      this.chooseEditStatus = selection[0].editStatus;\n      this.chooseSpare2 = selection[0].spare2;\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      // this.reset();\n      // this.open = true;\n      // this.title = \"添加项目报备\";\n      this.$router.push({\n        path: \"/project/report/form\",\n        query: {\n          businessKey: undefined,\n          formEdit: true,\n        },\n      });\n    },\n    handleView(row) {\n      let that = this;\n      this.view = JSON.parse(JSON.stringify(row));\n      this.view.operationType = this.operationTypeFormat(row);\n      this.view.auditStatus = this.auditStatusFormat(row);\n      this.view.editStatus = this.editStatusFormat(row);\n      this.view.biddingType = this.biddingTypeFormat(row);\n      this.view.model = this.modelFormat(row);\n      this.view.spec = this.specFormat(row);\n      this.view.requireInfo = this.requireInfoFormat(row);\n      this.view.infoType = this.infoTypeFormat(row);\n      this.view.belongProvince = this.belongProvinceFormat(row);\n      this.view.afterSaleYear = this.afterSaleYearFormat(row);\n      if (row.requireInfos) {\n        this.selectDictLabels(this.requireInfoOption1, row.requireInfo);\n        //this.view.requireInfo =\n        // const infoList = this.view.requireInfo.split(\",\");\n        const half = Math.ceil(row.requireInfos.length / 2);\n\n        this.infoList1 = row.requireInfos.splice(0, half);\n        this.infoList2 = row.requireInfos.splice(-half);\n\n        // const tmpList1 = infoList.splice(0, half);\n        // const tmpList2 = infoList.splice(-half);\n        // tmpList1.forEach((element) => {\n        //   console.log(element);\n        // });\n        // 循环对象赋值\n      } else {\n        this.infoList1 = [];\n        this.infoList2 = [];\n      }\n\n      if (row.operationType == \"2\" && row.spare1 == \"1\") {\n        this.defKey = \"process_project_auth\";\n        this.title = \"查看项目报备转授权\";\n      } else {\n        this.defKey = \"process_project_report\";\n        this.title = \"查看项目报备/授权\";\n      }\n      const params = { bizKey: row.projectId, defKey: this.defKey };\n      getInsIdByBizKey(params).then((resp) => {\n        this.bizKey = row.projectId;\n        if (resp.data && resp.data.instanceId) {\n          this.procInsId = resp.data.instanceId;\n          this.taskId = resp.data.taskId;\n          //console.log(\"==handleView=>>\")\n          //console.log(resp.data)\n          if (\n            resp.data.instanceId &&\n            !resp.data.endTime &&\n            resp.data.assignee == this.$store.state.user.userId\n          ) {\n            if (\n              this.$store.state.user.roles &&\n              this.$store.state.user.roles.includes(\"report_admin\") &&\n              row.operationType == \"2\"\n            ) {\n              this.isAuthImages = true;\n            }\n            this.finished = false;\n          } else if (\n            this.$store.state.user.roles &&\n            this.$store.state.user.roles.includes(\"report_admin\") &&\n            row.node == \"审核员\"\n          ) {\n            if (row.operationType == \"2\") {\n              this.isAuthImages = true;\n            }\n            //审核员角色不控制谁操作\n            this.finished = false;\n          } else {\n            this.finished = true;\n          }\n        } else {\n          this.finished = true;\n          this.procInsId = undefined;\n          this.taskId = undefined;\n        }\n\n        // console.log(\"====>>>驳回\")\n        // //驳回用户\n        // if(row.auditStatus == '3' && row.userId == this.$store.state.user.userId){\n        //   this.finished = false;\n        // }\n        // console.log(\"====>>>驳回：\" + this.finished)\n\n        this.viewOpen = true;\n      });\n      getLikeList({\n        projectName: row.projectName,\n        projectId: row.projectId,\n      }).then((resp) => {\n        //console.log(resp)\n        if (resp.data && resp.data.length > 0) {\n          this.likeList = resp.data;\n          that.likecount = resp.data.length;\n        } else {\n          this.likeList = undefined;\n          that.likecount = undefined;\n        }\n      });\n    },\n    /** 授权按钮操作 */\n    handleAuth(row) {\n      const loading = this.$loading({\n        lock: true,\n        text: \"授权中...\",\n        spinner: \"el-icon-loading\",\n        background: \"rgba(0, 0, 0, 0.7)\",\n      });\n      authReport(row)\n        .then((resp) => {\n          loading.close();\n          this.msgSuccess(resp.msg);\n          this.$router.go(0);\n        })\n        .catch((e) => {\n          loading.close();\n        });\n    },\n    /** 修改按钮操作 */\n    handleUpdate() {\n      let that = this;\n      // that.auditStatusEdit = true;\n      // if(!that.isAdmin && that.chooseAuditStatus == 3){\n      //   that.auditStatusEdit = false;\n      // }else{}\n      //申请者\n      var isApply =\n        this.$store.state.user.roles &&\n        (this.$store.state.user.roles.includes(\"common\") ||\n          this.$store.state.user.roles.includes(\"province_admin\"));\n\n      if (isApply && this.chooseUserId != this.$store.state.user.userId) {\n        this.msgError(\"只能修改本人提交的项目\");\n        return;\n      }\n\n      if (this.chooseOptType == 2) {\n        if (isApply && this.chooseSpare2 != 1) {\n          this.msgError(\"授权被退回才能修改\");\n          return;\n        }\n      }\n      if (this.chooseAuditStatus == 3 && isApply) {\n        if (this.chooseEditStatus == \"0\") {\n          this.msgError(\"审批被驳回无法修改\");\n          return;\n        }\n      }\n      // if(this.chooseOptType == 1){\n      //   if(isApply && this.chooseUserId != this.$store.state.user.userId ){\n      //     this.msgError(\"只能修改本人提交的报备项目\");\n      //     return;\n      //   }\n      // }\n      this.reset();\n      const projectId = this.ids;\n      getReport(projectId).then((response) => {\n        this.form = response.data;\n        if (this.form.model) this.form.model = this.form.model.split(\",\");\n        else this.form.model = [];\n        if (this.form.requireInfo)\n          this.form.requireInfo = this.form.requireInfo.split(\",\");\n        else this.form.requireInfo = [];\n        if (this.form.infoType)\n          this.form.infoType = this.form.infoType.split(\",\");\n        else this.form.infoType = [];\n        if (this.form.spec) this.form.spec = this.form.spec.split(\",\");\n        else this.form.spec = [];\n\n        if (this.form.authCompany) {\n          that.authCompanys = [];\n          var array = this.form.authCompany.split(\",\");\n          array.forEach(function (e) {\n            that.authCompanys.push({\n              value: e,\n              key: Date.now(),\n            });\n          });\n          that.form.authCompany = that.authCompanys[0].value;\n          that.authCompanys.splice(0, 1);\n          //console.log(that.authCompanys)\n        } else this.authCompanys = [];\n\n        this.oldOperationType = response.data.operationType;\n\n        this.open = true;\n        this.title = \"修改项目报备\";\n        var provinces = response.data.province;\n        if (provinces.length > 0) {\n          var address = provinces.split(\"/\");\n          var citys = [];\n          // 省份\n          if (address.length > 0) citys.push(TextToCode[address[0]].code);\n          // 城市\n          if (address.length > 1)\n            citys.push(TextToCode[address[0]][address[1]].code);\n          // 地区\n          if (address.length > 2)\n            citys.push(TextToCode[address[0]][address[1]][address[2]].code);\n\n          this.selectedOptions = citys;\n        }\n      });\n    },\n    /** 提交按钮 */\n    submitForm() {\n      this.$refs[\"form\"].validate((valid) => {\n        if (valid) {\n          if (this.form.infoType.indexOf(\"1\") >= 0 && this.form.scanFile) {\n            let emailReg = /^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\\.[a-zA-Z0-9_-]+)+$/;\n            if (!emailReg.test(this.form.scanFile)) {\n              this.$message.error(\"资料接收方式邮箱格式错误\");\n              return;\n            }\n          }\n          if (this.form.operationType == 1) {\n            this.form.authFile = \"\";\n            this.form.afterSaleFile = \"\";\n          }\n          var formStr = JSON.stringify(this.form);\n          var formData = JSON.parse(formStr);\n          if (formData.model && formData.model.length > 0)\n            formData.model = formData.model.join(\",\");\n          else formData.model = undefined;\n          if (formData.requireInfo && formData.requireInfo.length > 0)\n            formData.requireInfo = formData.requireInfo.join(\",\");\n          else formData.requireInfo = undefined;\n          if (formData.infoType && formData.infoType.length > 0)\n            formData.infoType = formData.infoType.join(\",\");\n          else formData.infoType = undefined;\n          if (formData.spec && formData.spec.length > 0)\n            formData.spec = formData.spec.join(\",\");\n          else formData.spec = undefined;\n\n          //授权公司\n          if (this.authCompanys.length > 0) {\n            var array = new Array();\n            this.authCompanys.forEach(function (e) {\n              array.push(e.value);\n            });\n            formData.authCompany += \",\" + array.join(\",\");\n          }\n\n          if (formData.projectId != null) {\n            updateReport(formData).then((response) => {\n              this.msgSuccess(\"修改成功\");\n\n              if (this.oldOperationType == 1 && formData.operationType == 2) {\n                console.log(\"=====>>>报备改授权\");\n              }\n\n              this.open = false;\n              this.getList();\n            });\n          } else {\n            addReport(formData).then((response) => {\n              this.msgSuccess(\"新增成功\");\n              this.open = false;\n              this.getList();\n            });\n          }\n        }\n      });\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const projectIds = row.projectId || this.ids;\n      this.$confirm(\n        '是否确认删除项目报备编号为\"' + projectIds + '\"的数据项?',\n        \"警告\",\n        {\n          confirmButtonText: \"确定\",\n          cancelButtonText: \"取消\",\n          type: \"warning\",\n        }\n      )\n        .then(function () {\n          return delReport(projectIds);\n        })\n        .then(() => {\n          this.getList();\n          this.msgSuccess(\"删除成功\");\n        });\n    },\n    clickExport() {\n      this.showExport = true;\n    },\n    clickPrint() {\n      this.showPrint = true;\n    },\n    /** 导出按钮操作 */\n    handleExport(type) {\n      let loadingwin;\n      let that = this;\n      this.queryParams.spare1 = type;\n      var col = [];\n      this.columns.forEach((item) => {\n        if (item.visible) {\n          col.push(item.label);\n        }\n      });\n      this.queryParams.spare2 = col.join(\",\");\n      const queryParams = this.queryParams;\n      this.$confirm(\n        \"是否确认导出项目报备搜索结果\" +\n        (type == 0 ? \"本页\" : \"全部\") +\n        \"数据项?\",\n        \"警告\",\n        {\n          confirmButtonText: \"确定\",\n          cancelButtonText: \"取消\",\n          type: \"warning\",\n        }\n      )\n        .then(function () {\n          loadingwin = that.$loading({\n            lock: true, //lock的修改符--默认是false\n            text: \"导出中...\", //显示在加载图标下方的加载文案\n            spinner: \"el-icon-loading\", //自定义加载图标类名\n            background: \"rgba(0, 0, 0, 0.7)\", //遮罩层颜色\n            target: document.querySelector(\".app-wrapper\"), //loadin覆盖的dom元素节点\n          });\n          document.documentElement.style.overflowY = \"hidden\"; //禁止底层div滚动\n          return exportReport(queryParams);\n        })\n        .then((response) => {\n          this.download(response.msg);\n          this.showExport = false;\n          loadingwin.close();\n          document.documentElement.style.overflowY = \"auto\"; //允许底层div滚动\n        })\n        .catch(() => {\n          this.showExport = false;\n          loadingwin.close();\n          document.documentElement.style.overflowY = \"auto\"; //允许底层div滚动\n        });\n    },\n    /** 导入按钮操作 */\n    handleImport() {\n      this.upload.title = \"项目导入\";\n      this.upload.open = true;\n    },\n    /** 下载模板操作 */\n    importTemplate() {\n      importTemplate().then((response) => {\n        this.download(response.msg);\n      });\n    },\n    downloadSQS() {\n      this.download(\"海佳集团-授权书.docx\", false);\n    },\n    downloadCRH() {\n      this.download(\"海佳集团-售后服务承诺函.doc\", false);\n    },\n    // 文件上传中处理\n    handleFileUploadProgress(event, file, fileList) {\n      this.upload.isUploading = true;\n    },\n    // 文件上传成功处理\n    handleFileSuccess(response, file, fileList) {\n      this.upload.open = false;\n      this.upload.isUploading = false;\n      this.$refs.upload.clearFiles();\n      this.$alert(response.msg, \"导入结果\", { dangerouslyUseHTMLString: true });\n      this.getList();\n    },\n    // 提交上传文件\n    submitFileForm() {\n      this.$refs.upload.submit();\n    },\n    handleChange(value) {\n      if (!value || value.length == 0) {\n        this.selectedOptions = null;\n        this.form.province = undefined;\n        this.form.district = undefined;\n        return;\n      }\n      this.selectedOptions = value;\n      var txt = \"\";\n      value.forEach(function (item) {\n        txt += CodeToText[item] + \"/\";\n      });\n      if (txt.length > 1) {\n        txt = txt.substring(0, txt.length - 1);\n        this.form.district = this.$store.state.user.province;\n        this.form.province = txt;\n      } else {\n        this.form.province = undefined;\n        this.form.district = undefined;\n      }\n    },\n    handleQueryCityChange(value) {\n      this.queryArea = value;\n      var txt = \"\";\n      value.forEach(function (item) {\n        txt += CodeToText[item] + \"/\";\n      });\n      if (txt.length > 1) {\n        txt = txt.substring(0, txt.length - 1);\n        this.queryParams.province = txt;\n      } else {\n        this.queryParams.province = undefined;\n      }\n    },\n    handlePrint(type) {\n      this.queryParams.spare1 = type;\n      var properties = [];\n      //properties.push({field: 'index', displayName: '序号'});\n      properties.push({ field: \"projectId\", displayName: \"项目ID\" });\n      this.columns.forEach((item) => {\n        if (item.visible) {\n          properties.push({ field: item.key, displayName: item.label });\n        }\n      });\n      printReport(this.queryParams).then((response) => {\n        printJS({\n          printable: response.data,\n          type: \"json\",\n          properties: properties,\n          header: '<div style=\"text-align: center\"><h3>项目报备列表</h3></div>',\n          targetStyles: [\"*\"],\n          gridHeaderStyle:\n            \"margin-top:20px;border: 1px solid #000;text-align:center\",\n          gridStyle: \"border: 1px solid #000;text-align:center;min-width:50px;\",\n          style: \"@page {margin:0 10mm;margin-top:10mm;}\",\n        });\n      });\n      // printJS({\n      //   printable: \"printArea\",\n      //   type:'html',\n      //   header:null,\n      //   targetStyles:['*'],\n      //   style:\"@page {margin:0 10mm}\"\n      // })\n    },\n    // 删除 showNameCorlor 和 showNoCorlor 方法，新增高亮方法\n    highlightText(text, keyword) {\n      if (!keyword) return text;\n      // 全部高亮\n      return text ? text.replace(new RegExp(keyword, 'g'), `<font color=\"#f00\">${keyword}</font>`) : text;\n    },\n    highlightCell(field, text) {\n      if (this.searchField === 'all' && this.searchValue && this.highlightFields.includes(field)) {\n        return this.highlightText(text, this.searchValue);\n      }\n      if (this.searchField === field && this.searchValue) {\n        return this.highlightText(text, this.searchValue);\n      }\n      return text;\n    },\n    removeDomain(index) {\n      if (index !== -1) {\n        this.authCompanys.splice(index, 1);\n      }\n    },\n    addDomain() {\n      this.authCompanys.push({\n        value: \"\",\n        key: Date.now(),\n      });\n    },\n    userSearch(createBy) {\n      this.queryParams.belongUser = createBy;\n      this.handleQuery();\n    },\n    optTypeSearch(type) {\n      this.queryParams.operationType = type;\n      this.handleQuery();\n    },\n    optTypeChange(e) {\n      console.log(e);\n    },\n  },\n};\n</script>\n<style>\n@media screen and (min-width: 600px) {\n  .view-dialog {\n    width: 80% !important;\n    float: right;\n  }\n}\n\n@media screen and (max-width: 599px) {\n  .view-dialog {\n    width: 100% !important;\n    float: right;\n  }\n\n  .edit-dialog {\n    width: 100% !important;\n\n    margin-bottom: 0;\n    height: 100%;\n    overflow: auto;\n  }\n\n  .el-dialog:not(.is-fullscreen) {\n    margin-top: 0 !important;\n  }\n\n  .el-form .el-col {\n    width: 100% !important;\n  }\n\n  .info-type .el-row {\n    flex-direction: column;\n  }\n\n  .info-type .el-input {\n    width: 100% !important;\n  }\n\n  .mobile-width {\n    width: 100% !important;\n  }\n}\n\n.likeTip {\n  color: rgb(247, 11, 11);\n  font-size: 12px;\n}\n\n.el-loading-mask {\n  z-index: 2001 !important;\n}\n</style>\n"]}]}