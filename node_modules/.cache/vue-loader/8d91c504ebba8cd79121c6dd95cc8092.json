{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/components/parser/Parser.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/components/parser/Parser.vue", "mtime": 1650124704000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}