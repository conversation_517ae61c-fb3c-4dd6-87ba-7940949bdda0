{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/components/RightToolbar/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/components/RightToolbar/index.vue", "mtime": 1665827762000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+CA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/RightToolbar", "sourcesContent": ["<template>\n  <div class=\"top-right-btn\">\n    <el-row>\n      <el-tooltip class=\"item\" effect=\"dark\" :content=\"showSearch ? '隐藏搜索' : '显示搜索'\" placement=\"top\">\n        <el-button size=\"mini\" circle icon=\"el-icon-search\" @click=\"toggleSearch()\" />\n      </el-tooltip>\n      <el-tooltip class=\"item\" effect=\"dark\" content=\"刷新\" placement=\"top\">\n        <el-button size=\"mini\" circle icon=\"el-icon-refresh\" @click=\"refresh()\" />\n      </el-tooltip>\n      <el-tooltip class=\"item\" effect=\"dark\" content=\"显隐列\" placement=\"top\" v-if=\"columns\">\n        <el-button size=\"mini\" circle icon=\"el-icon-menu\" @click=\"showColumn()\" />\n      </el-tooltip>      \n    </el-row>    \n    <el-dialog :title=\"title\" :visible.sync=\"open\" @close=\"cancel\" append-to-body>\n      \n      <!-- <el-transfer\n        :titles=\"['显示', '隐藏']\"\n        v-model=\"value\"\n        :data=\"columns\"\n        @change=\"dataChange\"\n      ></el-transfer> -->\n      <el-row>\n        <el-checkbox :indeterminate=\"isIndeterminate\" v-model=\"checkAll\" @change=\"handleCheckAllChange\">全选</el-checkbox>\n        <div style=\"margin: 15px 0;\"></div>\n        <el-checkbox-group v-model=\"showColumns\" @change=\"handleshowColumnsChange\">\n          <el-checkbox v-for=\"column in columns\" :label=\"column.label\" :key=\"column.key\">{{column.label}}</el-checkbox>\n        </el-checkbox-group>\n      </el-row>\n      \n      <div v-if=\"showExport||showPrint\" slot=\"footer\" class=\"dialog-footer\">\n        <span style=\"margin-right:150px;\">\n          <span>数据范围:</span>\n          <el-radio-group v-model=\"type\">\n            <el-radio\n              v-for=\"dict in typeOptions\"\n              :key=\"dict.value\"\n              :label=\"dict.value\"\n              >{{ dict.lable }}</el-radio>\n          </el-radio-group>\n        </span>\n        <el-button type=\"primary\" @click=\"confirm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n<script>\nexport default {\n  name: \"RightToolbar\",\n  data() {\n    return {\n      // 显隐数据\n      value: [],\n      // 弹出层标题\n      title: \"显示/隐藏\",\n      // 是否显示弹出层\n      open: false,\n      typeOptions: [\n        {value: 0, lable: '本页'},\n        {value: 1, lable: '全部'},\n      ],\n      type: 0,\n      checkAll: false,\n      columnOptions: [],\n      showColumns: [],\n      exportColumns: [],\n      printColumns: [],\n      isIndeterminate: true\n    };\n  },\n  props: {\n    showSearch: {\n      type: Boolean,\n      default: true,\n    },\n    columns: {\n      type: Array,\n    },\n    showExport: {\n      type: Boolean,\n      default: false,\n    },\n    showPrint: {\n      type: Boolean,\n      default: false,\n    },\n  },\n  watch: {\n    showExport(val){\n      this.open = val;\n      if(val){\n        this.title = \"导出选项\";\n      }else{\n        this.title = \"显示/隐藏\";\n      }\n    },    \n    showPrint(val){\n      this.open = val;\n      if(val){\n        this.title = \"打印选项\";\n      }else{\n        this.title = \"显示/隐藏\";\n      }\n    }\n  },\n  created(){\n    this.showColumns = this.columns.map(x => {return x.label}) // 生成数组\n    this.columnOptions = this.showColumns\n  },\n  methods: {\n    // 搜索\n    toggleSearch() {\n      this.$emit(\"update:showSearch\", !this.showSearch);\n    },\n    // 刷新\n    refresh() {\n      this.$emit(\"queryTable\");\n    },\n    // 右侧列表元素变化\n    dataChange(data) {\n      for (var item in this.columns) {\n        const key = this.columns[item].key;\n        this.columns[item].visible = !data.includes(key);\n      }\n    },\n    // 打开显隐列dialog\n    showColumn() {\n      this.open = true;\n    },\n    confirm(){\n      if(this.showExport){\n        this.$emit(\"export\", this.type);\n      }\n      if(this.showPrint){\n        this.$emit(\"print\", this.type);\n      }\n    },\n    cancel(){\n      if(this.showExport){\n        this.$emit(\"update:showExport\", false);\n      }\n      if(this.showPrint){\n        this.$emit(\"update:showPrint\", false);\n      }\n    },\n    handleCheckAllChange(val) {\n      this.showColumns = val ? this.columnOptions : [];\n      this.isIndeterminate = false;\n      for (var item in this.columns) {\n        this.columns[item].visible = val;\n      }      \n    },\n    handleshowColumnsChange(value) {\n     // console.log(value)\n      // let checkedCount = value.length;\n      // this.checkAll = checkedCount === this.cities.length;\n      // this.isIndeterminate = checkedCount > 0 && checkedCount < this.cities.length;\n      for (var item in this.columns) {\n        const key = this.columns[item].label;\n        this.columns[item].visible = value.includes(key);\n      }\n    }\n  },\n};\n</script>\n<style lang=\"scss\" scoped>\n::v-deep .el-transfer__button {\n  border-radius: 50%;\n  padding: 12px;\n  display: block;\n  margin-left: 0px;\n}\n::v-deep .el-transfer__button:first-child {\n  margin-bottom: 10px;\n}\n</style>\n"]}]}