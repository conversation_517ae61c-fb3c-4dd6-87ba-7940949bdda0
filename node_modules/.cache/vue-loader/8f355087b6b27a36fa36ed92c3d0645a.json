{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/system/user/form.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/system/user/form.vue", "mtime": 1668865468000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7CiAgZ2V0VXNlciwKICBhZGRVc2VyLAogIHVwZGF0ZVVzZXIKfSBmcm9tICJAL2FwaS9zeXN0ZW0vdXNlciI7CmltcG9ydCBUcmVlc2VsZWN0IGZyb20gIkByaW9waGFlL3Z1ZS10cmVlc2VsZWN0IjsKaW1wb3J0ICJAcmlvcGhhZS92dWUtdHJlZXNlbGVjdC9kaXN0L3Z1ZS10cmVlc2VsZWN0LmNzcyI7CmltcG9ydCBJbWFnZVVwbG9hZCBmcm9tICJAL2NvbXBvbmVudHMvSW1hZ2VVcGxvYWQiOwppbXBvcnQgZmxvd2FibGUgZnJvbSAnQC92aWV3cy9mbG93YWJsZS90YXNrL3JlY29yZC9pbmRleCcKaW1wb3J0IHsgcmVnaW9uRGF0YSxDb2RlVG9UZXh0LCBUZXh0VG9Db2RlIH0gZnJvbSAiZWxlbWVudC1jaGluYS1hcmVhLWRhdGEiOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogIlVzZXIiLAogIGNvbXBvbmVudHM6IHsgVHJlZXNlbGVjdCwgSW1hZ2VVcGxvYWQsIGZsb3dhYmxlIH0sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIC8vIOmBrue9qeWxggogICAgICBsb2FkaW5nOiB0cnVlLAogICAgICAvLyDpgInkuK3mlbDnu4QKICAgICAgaWRzOiBbXSwKICAgICAgLy8g6Z2e5Y2V5Liq56aB55SoCiAgICAgIHNpbmdsZTogdHJ1ZSwKICAgICAgLy8g6Z2e5aSa5Liq56aB55SoCiAgICAgIG11bHRpcGxlOiB0cnVlLAogICAgICAvLyDmmL7npLrmkJzntKLmnaHku7YKICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwKICAgICAgLy8g5oC75p2h5pWwCiAgICAgIHRvdGFsOiAwLAogICAgICAvLyDnlKjmiLfooajmoLzmlbDmja4KICAgICAgdXNlckxpc3Q6IG51bGwsCiAgICAgIC8vIOW8ueWHuuWxguagh+mimAogICAgICB0aXRsZTogIiIsCiAgICAgIC8vIOmDqOmXqOagkemAiemhuQogICAgICBkZXB0T3B0aW9uczogdW5kZWZpbmVkLAogICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYIKICAgICAgb3BlbjogZmFsc2UsCiAgICAgIC8vIOmDqOmXqOWQjeensAogICAgICBkZXB0TmFtZTogdW5kZWZpbmVkLAogICAgICAvLyDpu5jorqTlr4bnoIEKICAgICAgaW5pdFBhc3N3b3JkOiB1bmRlZmluZWQsCiAgICAgIC8vIOaXpeacn+iMg+WbtAogICAgICBkYXRlUmFuZ2U6IFtdLAogICAgICAvLyDnirbmgIHmlbDmja7lrZflhbgKICAgICAgc3RhdHVzT3B0aW9uczogW10sCiAgICAgIC8vIOaAp+WIq+eKtuaAgeWtl+WFuAogICAgICBzZXhPcHRpb25zOiBbXSwKICAgICAgLy8g55+t5L+h6YCa55+l5a2X5YW4CiAgICAgIHNtc1NlbmRPcHRpb25zOiBbXSwKICAgICAgLy8g5a6h5qC454q25oCB5a2X5YW4CiAgICAgIGF1ZGl0U3RhdHVzT3B0aW9uczogW10sCiAgICAgIC8vIOinkuiJsumAiemhuQogICAgICByb2xlT3B0aW9uczogW10sCiAgICAgIC8vIOihqOWNleWPguaVsAogICAgICBmb3JtOiB7fSwKICAgICAgZGVmYXVsdFByb3BzOiB7CiAgICAgICAgY2hpbGRyZW46ICJjaGlsZHJlbiIsCiAgICAgICAgbGFiZWw6ICJsYWJlbCIsCiAgICAgIH0sCiAgICAgIC8vIOihqOWNleagoemqjAogICAgICBydWxlczogewogICAgICAgIHVzZXJOYW1lOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi55So5oi35ZCN5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0sCiAgICAgICAgXSwKICAgICAgICBjb21wYW55OiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5YWs5Y+45YWo56ew5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0sCiAgICAgICAgXSwKICAgICAgICBidXNpbmVzc05vOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6JCl5Lia5omn54Wn5Y+356CB5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0sCiAgICAgICAgXSwKICAgICAgICBidXNpbmVzc05vUGljOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6JCl5Lia5omn54Wn5Zu+54mH5b+F5LygIiB9LAogICAgICAgIF0sCiAgICAgICAgZW1haWw6IFsKICAgICAgICAgIHsKICAgICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICAgIHR5cGU6ICJlbWFpbCIsCiAgICAgICAgICAgIG1lc3NhZ2U6ICIn6K+36L6T5YWl5q2j56Gu55qE6YKu566x5Zyw5Z2AIiwKICAgICAgICAgICAgdHJpZ2dlcjogWyJibHVyIiwgImNoYW5nZSJdLAogICAgICAgICAgfSwKICAgICAgICBdLAogICAgICAgIHBob25lbnVtYmVyOiBbCiAgICAgICAgICB7CiAgICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgICBwYXR0ZXJuOiAvXjFbM3w0fDV8Nnw3fDh8OV1bMC05XVxkezh9JC8sCiAgICAgICAgICAgIG1lc3NhZ2U6ICLor7fovpPlhaXmraPnoa7nmoTmiYvmnLrlj7fnoIEiLAogICAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIsCiAgICAgICAgICB9LAogICAgICAgIF0sCiAgICAgICAgbmlja05hbWU6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLmiqXlpIfkurrlp5PlkI3kuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwKICAgICAgICBdLAogICAgICAgIHByb3ZpbmNlOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5omA5Zyo5Yy65Z+f5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0sCiAgICAgICAgXSwKICAgICAgICBhZGRyZXNzOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6LWE5paZ6YKu5a+E5Zyw5Z2A5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0sCiAgICAgICAgXSwKICAgICAgfSwKICAgICAgYXVkaXRTdGF0dXNUcmVlOiBbXSwKICAgICAgcHJvdmluY2VBbmRDaXR5RGF0YTogcmVnaW9uRGF0YSwKICAgICAgY2l0eU9wdGlvbnM6IFtdLAogICAgICBxdWVyeUFyZWE6IFtdLAogICAgICAvL+W3peS9nOa1geWPguaVsAogICAgICBmaW5pc2hlZDogJ2ZhbHNlJywKICAgICAgdGFza0lkOiB1bmRlZmluZWQsCiAgICAgIHByb2NJbnNJZDogdW5kZWZpbmVkLAogICAgICBidXNpbmVzc0tleTogdW5kZWZpbmVkLAogICAgICBhdWRpdDogZmFsc2UsCiAgICAgIGZvcm1FZGl0OiBmYWxzZQogICAgICAvL+W3peS9nOa1geWPguaVsGVuZAogICAgfTsKICB9LAogIG1vdW50ZWQoKXsKICAgIHRoaXMucmVzZXQoKTsKICB9LAogIGNyZWF0ZWQoKSB7CiAgICB0aGlzLnRhc2tJZCAgPSB0aGlzLiRyb3V0ZS5xdWVyeSAmJiB0aGlzLiRyb3V0ZS5xdWVyeS50YXNrSWQ7CiAgICB0aGlzLnByb2NJbnNJZCA9IHRoaXMuJHJvdXRlLnF1ZXJ5ICYmIHRoaXMuJHJvdXRlLnF1ZXJ5LnByb2NJbnNJZDsKICAgIHRoaXMuZmluaXNoZWQgPSAgdGhpcy4kcm91dGUucXVlcnkgJiYgdGhpcy4kcm91dGUucXVlcnkuZmluaXNoZWQ7CiAgICB0aGlzLmJ1c2luZXNzS2V5ID0gdGhpcy4kcm91dGUucXVlcnkgJiYgdGhpcy4kcm91dGUucXVlcnkuYnVzaW5lc3NLZXk7CiAgICBsZXQgZWRpdCA9ICB0aGlzLiRyb3V0ZS5xdWVyeSAmJiB0aGlzLiRyb3V0ZS5xdWVyeS5mb3JtRWRpdDsKICAgIGlmKGVkaXQgPT0gInRydWUiKXsKICAgICAgdGhpcy5mb3JtRWRpdCA9IHRydWU7CiAgICB9ZWxzZXsKICAgICAgdGhpcy5mb3JtRWRpdCA9IGZhbHNlOwogICAgfQogICAgaWYodGhpcy5idXNpbmVzc0tleSl7CiAgICAgIHRoaXMuZ2V0VXNlckluZm8odGhpcy5idXNpbmVzc0tleSk7CiAgICAgIGlmKHRoaXMuZmluaXNoZWQgPT0gImZhbHNlIiAmJiAhdGhpcy5mb3JtRWRpdCl7CiAgICAgICAgdGhpcy5hdWRpdCA9IHRydWU7CiAgICAgIH0KICAgIH1lbHNlewogICAgICBnZXRVc2VyKCkudGhlbigocmVzcG9uc2UpID0+IHsKICAgICAgICB0aGlzLnJvbGVPcHRpb25zID0gcmVzcG9uc2Uucm9sZXM7CiAgICAgICAgdGhpcy5mb3JtLnBhc3N3b3JkID0gdGhpcy5pbml0UGFzc3dvcmQ7CiAgICAgIH0pOwogICAgfQoKICAgIHRoaXMuZ2V0RGljdHMoInN5c19ub3JtYWxfZGlzYWJsZSIpLnRoZW4oKHJlc3BvbnNlKSA9PiB7CiAgICAgIHRoaXMuc3RhdHVzT3B0aW9ucyA9IHJlc3BvbnNlLmRhdGE7CiAgICB9KTsKICAgIHRoaXMuZ2V0RGljdHMoInN5c191c2VyX3NleCIpLnRoZW4oKHJlc3BvbnNlKSA9PiB7CiAgICAgIHRoaXMuc2V4T3B0aW9ucyA9IHJlc3BvbnNlLmRhdGE7CiAgICB9KTsKICAgIHRoaXMuZ2V0Q29uZmlnS2V5KCJzeXMudXNlci5pbml0UGFzc3dvcmQiKS50aGVuKChyZXNwb25zZSkgPT4gewogICAgICB0aGlzLmluaXRQYXNzd29yZCA9IHJlc3BvbnNlLm1zZzsKICAgIH0pOwogICAgdGhpcy5nZXREaWN0cygicHJfc21zX25vdGlmeSIpLnRoZW4oKHJlc3BvbnNlKSA9PiB7CiAgICAgIHRoaXMuc21zU2VuZE9wdGlvbnMgPSByZXNwb25zZS5kYXRhOwogICAgfSk7CiAgICB0aGlzLmdldERpY3RzKCJwcl9hdWRpdF9zdGF0dXMiKS50aGVuKChyZXNwb25zZSkgPT4gewogICAgICB0aGlzLmF1ZGl0U3RhdHVzT3B0aW9ucyA9IHJlc3BvbnNlLmRhdGE7CiAgICAgIHZhciBvcHQgPSBbXTsKICAgICAgb3B0LnB1c2goe2lkOiAwLCBsYWJlbDon5YWo6YOoJ30pCiAgICAgIHJlc3BvbnNlLmRhdGEuZm9yRWFjaCgoZWxlbSwgaW5kZXgpID0+IHsKICAgICAgICB2YXIgb2JqID0ge307CiAgICAgICAgb2JqLmxhYmVsID0gZWxlbS5kaWN0TGFiZWw7CiAgICAgICAgb2JqLmlkID0gZWxlbS5kaWN0VmFsdWU7CiAgICAgICAgb3B0LnB1c2gob2JqKTsKICAgICAgfSk7CiAgICAgIHZhciBhdWRpdFN0YXR1c1RyZWUgPSB7fTsKICAgICAgYXVkaXRTdGF0dXNUcmVlLmxhYmVsID0gIuWuoeaguOeKtuaAgSI7CiAgICAgIGF1ZGl0U3RhdHVzVHJlZS5jaGlsZHJlbiA9IG9wdDsKICAgICAgdmFyIGF1ZGl0U3RhdHVzVHJlZXMgPSBbXTsKICAgICAgYXVkaXRTdGF0dXNUcmVlcy5wdXNoKGF1ZGl0U3RhdHVzVHJlZSk7CiAgICAgIHRoaXMuYXVkaXRTdGF0dXNUcmVlID0gYXVkaXRTdGF0dXNUcmVlczsKICAgIH0pOwogIH0sCiAgbWV0aG9kczogewogICAgLy8g5Y+W5raI5oyJ6ZKuCiAgICBjYW5jZWwoKSB7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgIH0sCiAgICAvLyDooajljZXph43nva4KICAgIHJlc2V0KCkgewogICAgICB0aGlzLmZvcm0gPSB7CiAgICAgICAgdXNlcklkOiB1bmRlZmluZWQsCiAgICAgICAgZGVwdElkOiB1bmRlZmluZWQsCiAgICAgICAgdXNlck5hbWU6IHVuZGVmaW5lZCwKICAgICAgICBuaWNrTmFtZTogdW5kZWZpbmVkLAogICAgICAgIHVzZXJUeXBlOiB1bmRlZmluZWQsCiAgICAgICAgZW1haWw6IHVuZGVmaW5lZCwKICAgICAgICBwaG9uZW51bWJlcjogdW5kZWZpbmVkLAogICAgICAgIHNleDogIjAiLAogICAgICAgIGF2YXRhcjogdW5kZWZpbmVkLAogICAgICAgIHBhc3N3b3JkOiB1bmRlZmluZWQsCiAgICAgICAgc3RhdHVzOiAiMCIsCiAgICAgICAgZGVsRmxhZzogdW5kZWZpbmVkLAogICAgICAgIGxvZ2luSXA6IHVuZGVmaW5lZCwKICAgICAgICBsb2dpbkRhdGU6IHVuZGVmaW5lZCwKICAgICAgICBjcmVhdGVCeTogdW5kZWZpbmVkLAogICAgICAgIGNyZWF0ZVRpbWU6IHVuZGVmaW5lZCwKICAgICAgICB1cGRhdGVCeTogdW5kZWZpbmVkLAogICAgICAgIHVwZGF0ZVRpbWU6IHVuZGVmaW5lZCwKICAgICAgICByZW1hcms6IHVuZGVmaW5lZCwKICAgICAgICBjb21wYW55OiB1bmRlZmluZWQsCiAgICAgICAgYnVzaW5lc3NObzogdW5kZWZpbmVkLAogICAgICAgIGJ1c2luZXNzTm9QaWM6IHVuZGVmaW5lZCwKICAgICAgICBwcm92aW5jZTogdW5kZWZpbmVkLAogICAgICAgIGFkZHJlc3M6IHVuZGVmaW5lZCwKICAgICAgICBkZWFsZXI6IHVuZGVmaW5lZCwKICAgICAgICBzbXNTZW5kOiAiMCIsCiAgICAgICAgYXVkaXRTdGF0dXM6ICIxIiwKICAgICAgICByb2xlSWRzOiBbXSwKICAgICAgfTsKICAgICAgdGhpcy5yZXNldEZvcm0oImZvcm0iKTsKICAgIH0sCiAgICAvLyDlpJrpgInmoYbpgInkuK3mlbDmja4KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsKICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKChpdGVtKSA9PiBpdGVtLnVzZXJJZCk7CiAgICAgIHRoaXMuc2luZ2xlID0gc2VsZWN0aW9uLmxlbmd0aCAhPSAxOwogICAgICB0aGlzLm11bHRpcGxlID0gIXNlbGVjdGlvbi5sZW5ndGg7CiAgICB9LAogICAgLyoqIOS/ruaUueaMiemSruaTjeS9nCAqLwogICAgZ2V0VXNlckluZm8odXNlcklkKSB7CiAgICAgIGdldFVzZXIodXNlcklkKS50aGVuKChyZXNwb25zZSkgPT4gewogICAgICAgIHRoaXMuZm9ybSA9IHJlc3BvbnNlLmRhdGE7CiAgICAgICAgdGhpcy5wb3N0T3B0aW9ucyA9IHJlc3BvbnNlLnBvc3RzOwogICAgICAgIHRoaXMucm9sZU9wdGlvbnMgPSByZXNwb25zZS5yb2xlczsKICAgICAgICB0aGlzLmZvcm0ucG9zdElkcyA9IHJlc3BvbnNlLnBvc3RJZHM7CiAgICAgICAgdGhpcy5mb3JtLnJvbGVJZHMgPSByZXNwb25zZS5yb2xlSWRzOwogICAgICAgIHRoaXMuZm9ybS5wYXNzd29yZCA9ICIiOwogICAgICAgIHZhciBwcm92aW5jZXMgPSByZXNwb25zZS5kYXRhLnByb3ZpbmNlOwogICAgICAgIGlmKHByb3ZpbmNlcy5sZW5ndGggPiAwKXsKICAgICAgICAgIHZhciBhZGRyZXNzID0gcHJvdmluY2VzLnNwbGl0KCIvIik7CiAgICAgICAgICB2YXIgY2l0eXMgPSBbXTsKICAgICAgICAgIC8vIOecgeS7vQogICAgICAgICAgaWYoYWRkcmVzcy5sZW5ndGggPiAwKQogICAgICAgICAgY2l0eXMucHVzaChUZXh0VG9Db2RlW2FkZHJlc3NbMF1dLmNvZGUpOwogICAgICAgICAgLy8g5Z+O5biCCiAgICAgICAgICBpZihhZGRyZXNzLmxlbmd0aCA+IDEpCiAgICAgICAgICBjaXR5cy5wdXNoKFRleHRUb0NvZGVbYWRkcmVzc1swXV1bYWRkcmVzc1sxXV0uY29kZSk7CiAgICAgICAgICAvLyDlnLDljLoKICAgICAgICAgIGlmKGFkZHJlc3MubGVuZ3RoID4gMikKICAgICAgICAgIGNpdHlzLnB1c2goVGV4dFRvQ29kZVthZGRyZXNzWzBdXVthZGRyZXNzWzFdXVthZGRyZXNzWzJdXS5jb2RlKTsKICAgICAgICAgIAogICAgICAgICAgdGhpcy5jaXR5T3B0aW9ucyA9IGNpdHlzOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAoKICAgIC8qKiDmj5DkuqTmjInpkq4gKi8KICAgIHN1Ym1pdEZvcm06IGZ1bmN0aW9uICgpIHsKICAgICAgbGV0IHRoYXQgPSB0aGlzOwogICAgICAvLyBpZih0aGlzLmNpdHlPcHRpb25zLmxlbmd0aCA8IDEpewogICAgICAgIAogICAgICAvLyB9CiAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZSgodmFsaWQpID0+IHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIGNvbnN0IGxvYWRpbmcgPSB0aGlzLiRsb2FkaW5nKHsKICAgICAgICAgICAgbG9jazogdHJ1ZSwvL2xvY2vnmoTkv67mlLnnrKYtLem7mOiupOaYr2ZhbHNlCiAgICAgICAgICAgIHRleHQ6ICdMb2FkaW5nJywvL+aYvuekuuWcqOWKoOi9veWbvuagh+S4i+aWueeahOWKoOi9veaWh+ahiAogICAgICAgICAgICBzcGlubmVyOiAnZWwtaWNvbi1sb2FkaW5nJywvL+iHquWumuS5ieWKoOi9veWbvuagh+exu+WQjQogICAgICAgICAgICBiYWNrZ3JvdW5kOiAncmdiYSgwLCAwLCAwLCAwLjcpJywvL+mBrue9qeWxguminOiJsgogICAgICAgICAgICB0YXJnZXQ6IGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3IoJy5tYWluLWNvbnRhaW5lcicpLy9sb2FkaW7opobnm5bnmoRkb23lhYPntKDoioLngrkKICAgICAgICAgIH0pOyAKICAgICAgICAgIGRvY3VtZW50LmRvY3VtZW50RWxlbWVudC5zdHlsZS5vdmVyZmxvd1kgPSAnaGlkZGVuJyAvL+emgeatouW6leWxgmRpdua7muWKqAogICAgICAgICAgaWYgKHRoYXQuZm9ybS51c2VySWQgIT0gdW5kZWZpbmVkKSB7CiAgICAgICAgICAgIHVwZGF0ZVVzZXIodGhhdC5mb3JtKS50aGVuKChyZXNwb25zZSkgPT4gewogICAgICAgICAgICAgIGlmKHRoYXQuYnVzaW5lc3NLZXkpewogICAgICAgICAgICAgICAgdGhhdC4kcmVmc1snZmxvdyddLnRhc2tDb21wbGV0ZSgi6YeN5paw5o+Q5LqkIik7CiAgICAgICAgICAgICAgfWVsc2V7CiAgICAgICAgICAgICAgICB0aGF0LnN0YXJ0Rmxvdyh0aGF0LmZvcm0pOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHsKICAgICAgICAgICAgICAgIGxvYWRpbmcuY2xvc2UoKTsKICAgICAgICAgICAgICAgIGRvY3VtZW50LmRvY3VtZW50RWxlbWVudC5zdHlsZS5vdmVyZmxvd1kgPSAnYXV0bycgLy/lhYHorrjlupXlsYJkaXbmu5rliqgKICAgICAgICAgICAgICB9LCAyMDAwKTsKICAgICAgICAgICAgfSkuY2F0Y2gocmVzPT57CiAgICAgICAgICAgICAgY29uc29sZS5sb2cocmVzKQogICAgICAgICAgICAgIGxvYWRpbmcuY2xvc2UoKTsKICAgICAgICAgICAgICBkb2N1bWVudC5kb2N1bWVudEVsZW1lbnQuc3R5bGUub3ZlcmZsb3dZID0gJ2F1dG8nIC8v5YWB6K645bqV5bGCZGl25rua5YqoCiAgICAgICAgICAgIH0pOwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgdGhhdC5mb3JtLmF1ZGl0U3RhdHVzID0gMTsKICAgICAgICAgICAgYWRkVXNlcih0aGF0LmZvcm0pLnRoZW4oKHJlc3BvbnNlKSA9PiB7CiAgICAgICAgICAgICAgdGhhdC5mb3JtLnVzZXJJZCA9IHJlc3BvbnNlLmRhdGE7CiAgICAgICAgICAgICAgdGhhdC5zdGFydEZsb3codGhhdC5mb3JtKQogICAgICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4gewogICAgICAgICAgICAgICAgbG9hZGluZy5jbG9zZSgpOwogICAgICAgICAgICAgICAgZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50LnN0eWxlLm92ZXJmbG93WSA9ICdhdXRvJyAvL+WFgeiuuOW6leWxgmRpdua7muWKqAogICAgICAgICAgICAgIH0sIDIwMDApOwogICAgICAgICAgICB9KS5jYXRjaChyZXM9PnsKICAgICAgICAgICAgICBjb25zb2xlLmxvZyhyZXMpCiAgICAgICAgICAgICAgbG9hZGluZy5jbG9zZSgpOwogICAgICAgICAgICAgIGRvY3VtZW50LmRvY3VtZW50RWxlbWVudC5zdHlsZS5vdmVyZmxvd1kgPSAnYXV0bycgLy/lhYHorrjlupXlsYJkaXbmu5rliqgKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICBoYW5kbGVDaXR5Q2hhbmdlKHZhbHVlKSB7CiAgICAgIGlmKCF2YWx1ZSB8fCB2YWx1ZS5sZW5ndGggPT0gMCApewogICAgICAgIHRoaXMuY2l0eU9wdGlvbnMgPSBudWxsOwogICAgICAgIHRoaXMuZm9ybS5wcm92aW5jZSA9IHVuZGVmaW5lZDsKICAgICAgICB0aGlzLmZvcm0uZGlzdHJpY3QgPSB1bmRlZmluZWQ7CiAgICAgICAgcmV0dXJuCiAgICAgIH0KICAgICAgdGhpcy5jaXR5T3B0aW9ucyA9IHZhbHVlCiAgICAgIHZhciB0eHQgPSAiIjsKICAgICAgdmFsdWUuZm9yRWFjaChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgdHh0ICs9IENvZGVUb1RleHRbaXRlbV0rIi8iCiAgICAgIH0pOwogICAgICBpZih0eHQubGVuZ3RoID4gMSl7CiAgICAgICAgdHh0ID0gdHh0LnN1YnN0cmluZygwLCB0eHQubGVuZ3RoLTEpOwogICAgICAgIHRoaXMuZm9ybS5wcm92aW5jZT10eHQ7CiAgICAgICAgdGhpcy5mb3JtLmRpc3RyaWN0ID0gQ29kZVRvVGV4dFt2YWx1ZVswXV07CiAgICAgIH1lbHNlewogICAgICAgIHRoaXMuZm9ybS5wcm92aW5jZT11bmRlZmluZWQ7CiAgICAgICAgdGhpcy5mb3JtLmRpc3RyaWN0ID0gdW5kZWZpbmVkOwogICAgICB9CiAgICAgIAogICAgfSwKICAgIC8qKiDlrqHmibkgKi8KICAgIGhhbmRsZUNvbXBsZXRlKCl7CiAgICAgIHRoaXMuJHJlZnNbJ2Zsb3cnXS5oYW5kbGVDb21wbGV0ZSgpOwogICAgfSwKICAgIC8qKiDpgIDlm54gKi8KICAgIGhhbmRsZVJldHVybigpewogICAgICB0aGlzLiRyZWZzWydmbG93J10uaGFuZGxlUmV0dXJuKCk7CiAgICB9LAogICAgLyoqIOi/lOWbnumhtemdoiAqLwogICAgZ29CYWNrKCkgewogICAgICAvLyDlhbPpl63lvZPliY3moIfnrb7pobXlubbov5Tlm57kuIrkuKrpobXpnaIKICAgICAgdGhpcy4kc3RvcmUuZGlzcGF0Y2goInRhZ3NWaWV3L2RlbFZpZXciLCB0aGlzLiRyb3V0ZSk7CiAgICAgIHRoaXMuJHJvdXRlci5nbygtMSkKICAgIH0sCiAgICBzdGFydEZsb3coZm9ybSl7CiAgICAgIHZhciB2YXJpYWJsZXMgPSB7fTsKICAgICAgdmFyaWFibGVzLlBST0NFU1NfQVJFQSA9IGZvcm0uZGlzdHJpY3Q7CiAgICAgIC8v5piv5ZCm55yB6LSf6LSj5Lq66KeS6ImyCiAgICAgIGlmKHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIucm9sZXMgJiYgdGhpcy4kc3RvcmUuc3RhdGUudXNlci5yb2xlcy5pbmNsdWRlcygicHJvdmluY2VfYWRtaW4iKSl7CiAgICAgICAgdmFyaWFibGVzLmlzTWFuYWdlID0gMTsKICAgICAgfWVsc2V7CiAgICAgICAgdmFyaWFibGVzLmlzTWFuYWdlID0gMDsKICAgICAgfQogICAgICB2YXJpYWJsZXMuQlVTSU5FU1NLRVkgPSBmb3JtLnVzZXJJZDsgICAgICAgICAgICAKICAgICAgdGhpcy4kcmVmc1snZmxvdyddLnN0YXJ0Rmxvdyhmb3JtLnVzZXJJZCwgIueUqOaIt+WuoeaJuSIsIHZhcmlhYmxlcyk7CiAgICB9CiAgfSwKfTsK"}, {"version": 3, "sources": ["form.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2MA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "form.vue", "sourceRoot": "src/views/system/user", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-card class=\"box-card\" >\n        <div slot=\"header\" class=\"clearfix\">\n          <span class=\"el-icon-document\">用户审核流程</span>\n          <span style=\"float: right;\">\n            <el-button icon=\"el-icon-edit-outline\" type=\"success\" v-if=\"audit\" @click=\"handleComplete\">审批</el-button>\n            <el-button icon=\"el-icon-refresh-left\" type=\"warning\" v-if=\"audit\" @click=\"handleReturn\">退回</el-button>\n            <el-button type=\"primary\" @click=\"goBack\">返回</el-button>\n          </span>\n        </div>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" :disabled=\"!formEdit\" label-width=\"120px\">\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"用户账号\" prop=\"userName\">\n              <el-input v-model=\"form.userName\" placeholder=\"请输入用户账号\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"报备人姓名\" prop=\"nickName\">\n              <el-input\n                v-model=\"form.nickName\"\n                placeholder=\"请输入报备人姓名\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"角色\">\n              <el-select v-model=\"form.roleIds\" multiple placeholder=\"请选择\">\n                <el-option\n                  v-for=\"item in roleOptions\"\n                  :key=\"item.roleId\"\n                  :label=\"item.roleName\"\n                  :value=\"item.roleId\"\n                  :disabled=\"item.status == 1\"\n                ></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"资料接收邮箱\" prop=\"email\">\n              <el-input\n                v-model=\"form.email\"\n                placeholder=\"请输入资料接收邮箱\"\n                maxlength=\"50\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"报备人电话\" prop=\"phonenumber\">\n              <el-input\n                v-model=\"form.phonenumber\"\n                placeholder=\"请输入报备人电话\"\n                maxlength=\"11\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"用户性别\">\n              <el-select v-model=\"form.sex\" placeholder=\"请选择\">\n                <el-option\n                  v-for=\"dict in sexOptions\"\n                  :key=\"dict.dictValue\"\n                  :label=\"dict.dictLabel\"\n                  :value=\"dict.dictValue\"\n                ></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <!-- <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"头像地址\">\n              <imageUpload v-model=\"form.avatar\" />\n            </el-form-item>\n          </el-col>\n           <el-col :span=\"12\">\n            <el-form-item label=\"密码\" prop=\"password\">\n              <el-input\n                v-model=\"form.password\"\n                placeholder=\"请输入密码\"\n                type=\"password\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row> -->\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"帐号状态\">\n              <el-radio-group v-model=\"form.status\">\n                <el-radio\n                  v-for=\"dict in statusOptions\"\n                  :key=\"dict.dictValue\"\n                  :label=\"dict.dictValue\"\n                  >{{ dict.dictLabel }}</el-radio\n                >\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"备注\" prop=\"remark\">\n              <el-input\n                v-model=\"form.remark\"\n                type=\"textarea\"\n                placeholder=\"请输入内容\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"公司全称\" prop=\"company\">\n              <el-input v-model=\"form.company\" placeholder=\"请输入公司全称\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"营业执照号码\" prop=\"businessNo\">\n              <el-input\n                v-model=\"form.businessNo\"\n                placeholder=\"请输入营业执照号码\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"营业执照图片\" prop=\"businessNoPic\" :required=\"true\" error=\"营业执照图片必传\">\n              <imageUpload v-model=\"form.businessNoPic\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            \n            <el-form-item label=\"所在区域\" prop=\"province\">\n              <el-cascader\n                :options=\"provinceAndCityData\"\n                clearable\n                :props=\"{ expandTrigger: 'hover' }\"\n                v-model=\"cityOptions\"\n                @change=\"handleCityChange\"\n              >\n              </el-cascader>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"资料邮寄地址\" prop=\"address\">\n              <el-input\n                v-model=\"form.address\"\n                placeholder=\"请输入资料邮寄地址\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"隶属经销商\" prop=\"dealer\">\n              <el-input v-model=\"form.dealer\" placeholder=\"请输入隶属经销商\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"短信通知\">\n              <el-radio-group v-model=\"form.smsSend\">\n                <el-radio\n                  v-for=\"dict in smsSendOptions\"\n                  :key=\"dict.dictValue\"\n                  :label=\"dict.dictValue\"\n                  >{{ dict.dictLabel }}</el-radio\n                >\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n          <!-- <el-col :span=\"12\">\n            <el-form-item label=\"审核状态\">\n              <el-radio-group v-model=\"form.auditStatus\">\n                <el-radio\n                  v-for=\"dict in auditStatusOptions\"\n                  :key=\"dict.dictValue\"\n                  :label=\"dict.dictValue\"\n                  >{{ dict.dictLabel }}</el-radio\n                >\n              </el-radio-group>\n            </el-form-item>\n          </el-col> -->\n        </el-row>\n      </el-form>\n      <el-col :span=\"16\" :offset=\"8\" v-if=\"formEdit\" >\n        <div style=\"margin-left:10%;margin-bottom: 20px;font-size: 14px;\">\n          <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n          <el-button @click=\"goBack\">取 消</el-button>\n        </div>\n      </el-col>\n    </el-card>\n    <flowable ref=\"flow\" :key=\"businessKey\" procDefKey=\"process_user_reg\" :procInsId=\"procInsId\" :taskId=\"taskId\" :finished=\"finished\"></flowable>\n\n  </div>\n</template>\n\n<script>\nimport {\n  getUser,\n  addUser,\n  updateUser\n} from \"@/api/system/user\";\nimport Treeselect from \"@riophae/vue-treeselect\";\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\";\nimport ImageUpload from \"@/components/ImageUpload\";\nimport flowable from '@/views/flowable/task/record/index'\nimport { regionData,CodeToText, TextToCode } from \"element-china-area-data\";\nexport default {\n  name: \"User\",\n  components: { Treeselect, ImageUpload, flowable },\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 用户表格数据\n      userList: null,\n      // 弹出层标题\n      title: \"\",\n      // 部门树选项\n      deptOptions: undefined,\n      // 是否显示弹出层\n      open: false,\n      // 部门名称\n      deptName: undefined,\n      // 默认密码\n      initPassword: undefined,\n      // 日期范围\n      dateRange: [],\n      // 状态数据字典\n      statusOptions: [],\n      // 性别状态字典\n      sexOptions: [],\n      // 短信通知字典\n      smsSendOptions: [],\n      // 审核状态字典\n      auditStatusOptions: [],\n      // 角色选项\n      roleOptions: [],\n      // 表单参数\n      form: {},\n      defaultProps: {\n        children: \"children\",\n        label: \"label\",\n      },\n      // 表单校验\n      rules: {\n        userName: [\n          { required: true, message: \"用户名不能为空\", trigger: \"blur\" },\n        ],\n        company: [\n          { required: true, message: \"公司全称不能为空\", trigger: \"blur\" },\n        ],\n        businessNo: [\n          { required: true, message: \"营业执照号码不能为空\", trigger: \"blur\" },\n        ],\n        businessNoPic: [\n          { required: true, message: \"营业执照图片必传\" },\n        ],\n        email: [\n          {\n            required: true,\n            type: \"email\",\n            message: \"'请输入正确的邮箱地址\",\n            trigger: [\"blur\", \"change\"],\n          },\n        ],\n        phonenumber: [\n          {\n            required: true,\n            pattern: /^1[3|4|5|6|7|8|9][0-9]\\d{8}$/,\n            message: \"请输入正确的手机号码\",\n            trigger: \"blur\",\n          },\n        ],\n        nickName: [\n          { required: true, message: \"报备人姓名不能为空\", trigger: \"blur\" },\n        ],\n        province: [\n          { required: true, message: \"所在区域不能为空\", trigger: \"blur\" },\n        ],\n        address: [\n          { required: true, message: \"资料邮寄地址不能为空\", trigger: \"blur\" },\n        ],\n      },\n      auditStatusTree: [],\n      provinceAndCityData: regionData,\n      cityOptions: [],\n      queryArea: [],\n      //工作流参数\n      finished: 'false',\n      taskId: undefined,\n      procInsId: undefined,\n      businessKey: undefined,\n      audit: false,\n      formEdit: false\n      //工作流参数end\n    };\n  },\n  mounted(){\n    this.reset();\n  },\n  created() {\n    this.taskId  = this.$route.query && this.$route.query.taskId;\n    this.procInsId = this.$route.query && this.$route.query.procInsId;\n    this.finished =  this.$route.query && this.$route.query.finished;\n    this.businessKey = this.$route.query && this.$route.query.businessKey;\n    let edit =  this.$route.query && this.$route.query.formEdit;\n    if(edit == \"true\"){\n      this.formEdit = true;\n    }else{\n      this.formEdit = false;\n    }\n    if(this.businessKey){\n      this.getUserInfo(this.businessKey);\n      if(this.finished == \"false\" && !this.formEdit){\n        this.audit = true;\n      }\n    }else{\n      getUser().then((response) => {\n        this.roleOptions = response.roles;\n        this.form.password = this.initPassword;\n      });\n    }\n\n    this.getDicts(\"sys_normal_disable\").then((response) => {\n      this.statusOptions = response.data;\n    });\n    this.getDicts(\"sys_user_sex\").then((response) => {\n      this.sexOptions = response.data;\n    });\n    this.getConfigKey(\"sys.user.initPassword\").then((response) => {\n      this.initPassword = response.msg;\n    });\n    this.getDicts(\"pr_sms_notify\").then((response) => {\n      this.smsSendOptions = response.data;\n    });\n    this.getDicts(\"pr_audit_status\").then((response) => {\n      this.auditStatusOptions = response.data;\n      var opt = [];\n      opt.push({id: 0, label:'全部'})\n      response.data.forEach((elem, index) => {\n        var obj = {};\n        obj.label = elem.dictLabel;\n        obj.id = elem.dictValue;\n        opt.push(obj);\n      });\n      var auditStatusTree = {};\n      auditStatusTree.label = \"审核状态\";\n      auditStatusTree.children = opt;\n      var auditStatusTrees = [];\n      auditStatusTrees.push(auditStatusTree);\n      this.auditStatusTree = auditStatusTrees;\n    });\n  },\n  methods: {\n    // 取消按钮\n    cancel() {\n      this.reset();\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        userId: undefined,\n        deptId: undefined,\n        userName: undefined,\n        nickName: undefined,\n        userType: undefined,\n        email: undefined,\n        phonenumber: undefined,\n        sex: \"0\",\n        avatar: undefined,\n        password: undefined,\n        status: \"0\",\n        delFlag: undefined,\n        loginIp: undefined,\n        loginDate: undefined,\n        createBy: undefined,\n        createTime: undefined,\n        updateBy: undefined,\n        updateTime: undefined,\n        remark: undefined,\n        company: undefined,\n        businessNo: undefined,\n        businessNoPic: undefined,\n        province: undefined,\n        address: undefined,\n        dealer: undefined,\n        smsSend: \"0\",\n        auditStatus: \"1\",\n        roleIds: [],\n      };\n      this.resetForm(\"form\");\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map((item) => item.userId);\n      this.single = selection.length != 1;\n      this.multiple = !selection.length;\n    },\n    /** 修改按钮操作 */\n    getUserInfo(userId) {\n      getUser(userId).then((response) => {\n        this.form = response.data;\n        this.postOptions = response.posts;\n        this.roleOptions = response.roles;\n        this.form.postIds = response.postIds;\n        this.form.roleIds = response.roleIds;\n        this.form.password = \"\";\n        var provinces = response.data.province;\n        if(provinces.length > 0){\n          var address = provinces.split(\"/\");\n          var citys = [];\n          // 省份\n          if(address.length > 0)\n          citys.push(TextToCode[address[0]].code);\n          // 城市\n          if(address.length > 1)\n          citys.push(TextToCode[address[0]][address[1]].code);\n          // 地区\n          if(address.length > 2)\n          citys.push(TextToCode[address[0]][address[1]][address[2]].code);\n          \n          this.cityOptions = citys;\n        }\n      });\n    },\n\n    /** 提交按钮 */\n    submitForm: function () {\n      let that = this;\n      // if(this.cityOptions.length < 1){\n        \n      // }\n      this.$refs[\"form\"].validate((valid) => {\n        if (valid) {\n          const loading = this.$loading({\n            lock: true,//lock的修改符--默认是false\n            text: 'Loading',//显示在加载图标下方的加载文案\n            spinner: 'el-icon-loading',//自定义加载图标类名\n            background: 'rgba(0, 0, 0, 0.7)',//遮罩层颜色\n            target: document.querySelector('.main-container')//loadin覆盖的dom元素节点\n          }); \n          document.documentElement.style.overflowY = 'hidden' //禁止底层div滚动\n          if (that.form.userId != undefined) {\n            updateUser(that.form).then((response) => {\n              if(that.businessKey){\n                that.$refs['flow'].taskComplete(\"重新提交\");\n              }else{\n                that.startFlow(that.form);\n              }\n              setTimeout(() => {\n                loading.close();\n                document.documentElement.style.overflowY = 'auto' //允许底层div滚动\n              }, 2000);\n            }).catch(res=>{\n              console.log(res)\n              loading.close();\n              document.documentElement.style.overflowY = 'auto' //允许底层div滚动\n            });\n          } else {\n            that.form.auditStatus = 1;\n            addUser(that.form).then((response) => {\n              that.form.userId = response.data;\n              that.startFlow(that.form)\n              setTimeout(() => {\n                loading.close();\n                document.documentElement.style.overflowY = 'auto' //允许底层div滚动\n              }, 2000);\n            }).catch(res=>{\n              console.log(res)\n              loading.close();\n              document.documentElement.style.overflowY = 'auto' //允许底层div滚动\n            });\n          }\n        }\n      });\n    },\n    handleCityChange(value) {\n      if(!value || value.length == 0 ){\n        this.cityOptions = null;\n        this.form.province = undefined;\n        this.form.district = undefined;\n        return\n      }\n      this.cityOptions = value\n      var txt = \"\";\n      value.forEach(function (item) {\n          txt += CodeToText[item]+\"/\"\n      });\n      if(txt.length > 1){\n        txt = txt.substring(0, txt.length-1);\n        this.form.province=txt;\n        this.form.district = CodeToText[value[0]];\n      }else{\n        this.form.province=undefined;\n        this.form.district = undefined;\n      }\n      \n    },\n    /** 审批 */\n    handleComplete(){\n      this.$refs['flow'].handleComplete();\n    },\n    /** 退回 */\n    handleReturn(){\n      this.$refs['flow'].handleReturn();\n    },\n    /** 返回页面 */\n    goBack() {\n      // 关闭当前标签页并返回上个页面\n      this.$store.dispatch(\"tagsView/delView\", this.$route);\n      this.$router.go(-1)\n    },\n    startFlow(form){\n      var variables = {};\n      variables.PROCESS_AREA = form.district;\n      //是否省负责人角色\n      if(this.$store.state.user.roles && this.$store.state.user.roles.includes(\"province_admin\")){\n        variables.isManage = 1;\n      }else{\n        variables.isManage = 0;\n      }\n      variables.BUSINESSKEY = form.userId;            \n      this.$refs['flow'].startFlow(form.userId, \"用户审批\", variables);\n    }\n  },\n};\n</script>"]}]}