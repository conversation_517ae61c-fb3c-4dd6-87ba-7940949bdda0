{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/tool/build/JsonDrawer.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/tool/build/JsonDrawer.vue", "mtime": 1641277326000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["JsonDrawer.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "JsonDrawer.vue", "sourceRoot": "src/views/tool/build", "sourcesContent": ["<template>\n  <div>\n    <el-drawer v-bind=\"$attrs\" v-on=\"$listeners\" @opened=\"onOpen\" @close=\"onClose\">\n      <div class=\"action-bar\" :style=\"{'text-align': 'left'}\">\n        <span class=\"bar-btn\" @click=\"refresh\">\n          <i class=\"el-icon-refresh\" />\n          刷新\n        </span>\n        <span ref=\"copyBtn\" class=\"bar-btn copy-json-btn\">\n          <i class=\"el-icon-document-copy\" />\n          复制JSON\n        </span>\n        <span class=\"bar-btn\" @click=\"exportJsonFile\">\n          <i class=\"el-icon-download\" />\n          导出JSON文件\n        </span>\n        <span class=\"bar-btn delete-btn\" @click=\"$emit('update:visible', false)\">\n          <i class=\"el-icon-circle-close\" />\n          关闭\n        </span>\n      </div>\n      <div id=\"editorJson\" class=\"json-editor\" />\n    </el-drawer>\n  </div>\n</template>\n\n<script>\nimport { beautifierConf } from '@/utils/index'\nimport ClipboardJS from 'clipboard'\nimport { saveAs } from 'file-saver'\nimport loadMonaco from '@/utils/loadMonaco'\nimport loadBeautifier from '@/utils/loadBeautifier'\n\nlet beautifier\nlet monaco\n\nexport default {\n  components: {},\n  props: {\n    jsonStr: {\n      type: String,\n      required: true\n    }\n  },\n  data() {\n    return {}\n  },\n  computed: {},\n  watch: {},\n  created() {},\n  mounted() {\n    window.addEventListener('keydown', this.preventDefaultSave)\n    const clipboard = new ClipboardJS('.copy-json-btn', {\n      text: trigger => {\n        this.$notify({\n          title: '成功',\n          message: '代码已复制到剪切板，可粘贴。',\n          type: 'success'\n        })\n        return this.beautifierJson\n      }\n    })\n    clipboard.on('error', e => {\n      this.$message.error('代码复制失败')\n    })\n  },\n  beforeDestroy() {\n    window.removeEventListener('keydown', this.preventDefaultSave)\n  },\n  methods: {\n    preventDefaultSave(e) {\n      if (e.key === 's' && (e.metaKey || e.ctrlKey)) {\n        e.preventDefault()\n      }\n    },\n    onOpen() {\n      loadBeautifier(btf => {\n        beautifier = btf\n        this.beautifierJson = beautifier.js(this.jsonStr, beautifierConf.js)\n\n        loadMonaco(val => {\n          monaco = val\n          this.setEditorValue('editorJson', this.beautifierJson)\n        })\n      })\n    },\n    onClose() {},\n    setEditorValue(id, codeStr) {\n      if (this.jsonEditor) {\n        this.jsonEditor.setValue(codeStr)\n      } else {\n        this.jsonEditor = monaco.editor.create(document.getElementById(id), {\n          value: codeStr,\n          theme: 'vs-dark',\n          language: 'json',\n          automaticLayout: true\n        })\n        // ctrl + s 刷新\n        this.jsonEditor.onKeyDown(e => {\n          if (e.keyCode === 49 && (e.metaKey || e.ctrlKey)) {\n            this.refresh()\n          }\n        })\n      }\n    },\n    exportJsonFile() {\n      this.$prompt('文件名:', '导出文件', {\n        inputValue: `${+new Date()}.json`,\n        closeOnClickModal: false,\n        inputPlaceholder: '请输入文件名'\n      }).then(({ value }) => {\n        if (!value) value = `${+new Date()}.json`\n        const codeStr = this.jsonEditor.getValue()\n        const blob = new Blob([codeStr], { type: 'text/plain;charset=utf-8' })\n        saveAs(blob, value)\n      })\n    },\n    refresh() {\n      try {\n        this.$emit('refresh', JSON.parse(this.jsonEditor.getValue()))\n      } catch (error) {\n        this.$notify({\n          title: '错误',\n          message: 'JSON格式错误，请检查',\n          type: 'error'\n        })\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import '@/styles/mixin.scss';\n\n::v-deep .el-drawer__header {\n  display: none;\n}\n@include action-bar;\n\n.json-editor{\n  height: calc(100vh - 33px);\n}\n</style>\n"]}]}