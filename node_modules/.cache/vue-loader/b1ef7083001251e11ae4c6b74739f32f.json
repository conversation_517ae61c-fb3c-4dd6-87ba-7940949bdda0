{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/login.vue?vue&type=style&index=0&id=6c257334&prod&rel=stylesheet%2Fscss&lang=scss", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/login.vue", "mtime": 1716984016441}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/css-loader/dist/cjs.js", "mtime": 1751171659443}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/loaders/stylePostLoader.js", "mtime": 1751171659924}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/postcss-loader/src/index.js", "mtime": 1751171659554}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/sass-loader/dist/cjs.js", "mtime": 1751171659051}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLmxvZ2luIHsKICBkaXNwbGF5OiBmbGV4OwogIGp1c3RpZnktY29udGVudDogZmxleC1lbmQ7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBoZWlnaHQ6IDEwMCU7CiAgYmFja2dyb3VuZC1pbWFnZTogdXJsKCIuLi9hc3NldHMvaW1hZ2VzL2xvZ2luLWJhY2tncm91bmQud2VicCIpOwogIGJhY2tncm91bmQtc2l6ZTogY292ZXI7Cn0KQG1lZGlhIHNjcmVlbiBhbmQgKG1pbi13aWR0aDogNjAwcHgpIHsKICAubG9naW4tcGFkZGluZyB7CiAgICBwYWRkaW5nLXJpZ2h0OiAxMCU7CiAgfQp9CkBtZWRpYSBzY3JlZW4gYW5kIChtYXgtd2lkdGg6IDU5OXB4KSB7CiAgLmxvZ2luLXBhZGRpbmcgewogICAgcGFkZGluZy1sZWZ0OiAycHg7CiAgICBwYWRkaW5nLXJpZ2h0OiA3cHg7CiAgfQp9Ci50aXRsZSB7CiAgbWFyZ2luOiAwcHggYXV0byAzMHB4IGF1dG87CiAgdGV4dC1hbGlnbjogY2VudGVyOwogIGNvbG9yOiAjNzA3MDcwOwp9CgoubG9naW4tZm9ybSB7CiAgYm9yZGVyLXJhZGl1czogNnB4OwogIGJhY2tncm91bmQ6ICNmZmZmZmY7CiAgd2lkdGg6IDQwMHB4OwogIHBhZGRpbmc6IDI1cHggMjVweCA1cHggMjVweDsKICAuZWwtaW5wdXQgewogICAgaGVpZ2h0OiAzOHB4OwogICAgaW5wdXQgewogICAgICBoZWlnaHQ6IDM4cHg7CiAgICB9CiAgfQogIC5pbnB1dC1pY29uIHsKICAgIGhlaWdodDogMzlweDsKICAgIHdpZHRoOiAxNHB4OwogICAgbWFyZ2luLWxlZnQ6IDJweDsKICB9Cn0KLmxvZ2luLXRpcCB7CiAgZm9udC1zaXplOiAxM3B4OwogIHRleHQtYWxpZ246IGNlbnRlcjsKICBjb2xvcjogI2JmYmZiZjsKfQoubG9naW4tY29kZSB7CiAgd2lkdGg6IDMzJTsKICBoZWlnaHQ6IDM4cHg7CiAgZmxvYXQ6IHJpZ2h0OwogIGltZyB7CiAgICBjdXJzb3I6IHBvaW50ZXI7CiAgICB2ZXJ0aWNhbC1hbGlnbjogbWlkZGxlOwogIH0KfQouZWwtbG9naW4tZm9vdGVyIHsKICBoZWlnaHQ6IDQwcHg7CiAgbGluZS1oZWlnaHQ6IDQwcHg7CiAgcG9zaXRpb246IGZpeGVkOwogIGJvdHRvbTogMDsKICB3aWR0aDogMTAwJTsKICB0ZXh0LWFsaWduOiByaWdodDsKICBjb2xvcjogIzk5OTk5OTsKICBmb250LWZhbWlseTogQXJpYWw7CiAgZm9udC1zaXplOiAxMnB4OwogIGxldHRlci1zcGFjaW5nOiAxcHg7Cn0KLmxvZ2luLWNvZGUtaW1nIHsKICBoZWlnaHQ6IDM4cHg7Cn0KLmxvZ28td3JhcHBlciB7CiAgcG9zaXRpb246IGZpeGVkOwogIHRvcDogMzBweDsKICBsZWZ0OiA3MHB4Owp9Ci5sb2dvIGltZyB7CiAgbWF4LXdpZHRoOiAxNTNweDsKfQo="}, null]}