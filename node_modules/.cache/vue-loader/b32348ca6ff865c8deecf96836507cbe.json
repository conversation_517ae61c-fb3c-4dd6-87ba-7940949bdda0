{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Process/components/nodePanel/property/taskListener.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Process/components/nodePanel/property/taskListener.vue", "mtime": 1641277326000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["taskListener.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "taskListener.vue", "sourceRoot": "src/components/Process/components/nodePanel/property", "sourcesContent": ["<template>\n  <div>\n    <el-dialog\n      title=\"任务监听器\"\n      :visible.sync=\"dialogVisible\"\n      width=\"900px\"\n      :close-on-click-modal=\"false\"\n      :close-on-press-escape=\"false\"\n      :show-close=\"false\"\n      @closed=\"$emit('close')\"\n    >\n      <x-form ref=\"xForm\" v-model=\"formData\" :config=\"formConfig\">\n        <template #params=\"scope\">\n          <el-badge :value=\"scope.row.params ? scope.row.params.length : 0\" type=\"primary\">\n            <el-button size=\"small\" @click=\"configParam(scope.$index)\">配置</el-button>\n          </el-badge>\n        </template>\n      </x-form>\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" size=\"medium\" @click=\"closeDialog\">确 定</el-button>\n      </span>\n    </el-dialog>\n    <listenerParam v-if=\"showParamDialog\" :value=\"formData.taskListener[nowIndex].params\" @close=\"finishConfigParam\" />\n  </div>\n</template>\n\n<script>\nimport mixinPanel from '../../../common/mixinPanel'\nimport listenerParam from './listenerParam'\nexport default {\n  components: { listenerParam },\n  mixins: [mixinPanel],\n  data() {\n    return {\n      dialogVisible: true,\n      showParamDialog: false,\n      nowIndex: null,\n      formData: {\n        taskListener: []\n      }\n    }\n  },\n  computed: {\n    formConfig() {\n    //   const _this = this\n      return {\n        inline: false,\n        item: [\n          {\n            xType: 'tabs',\n            tabs: [\n              {\n                label: '任务监听器',\n                name: 'taskListener',\n                column: [\n                  {\n                    label: '事件',\n                    name: 'event',\n                    width: 180,\n                    rules: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],\n                    xType: 'select',\n                    dic: [\n                      { label: 'create', value: 'create' },\n                      { label: 'assignment', value: 'assignment' },\n                      { label: 'complete', value: 'complete' },\n                      { label: 'delete', value: 'delete' }\n                    ],\n                    tooltip: `create（创建）：当任务已经创建，并且所有任务参数都已经设置时触发。<br />\n                              assignment（指派）：当任务已经指派给某人时触发。请注意：当流程执行到达用户任务时，在触发create事件之前，会首先触发assignment事件。<br />\n                              complete（完成）：当任务已经完成，从运行时数据中删除前触发。<br />\n                              delete（删除）：在任务即将被删除前触发。请注意任务由completeTask正常完成时也会触发。\n                    `\n                  },\n                  {\n                    label: '类型',\n                    name: 'type',\n                    width: 180,\n                    rules: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],\n                    xType: 'select',\n                    dic: [\n                      { label: '类', value: 'class' },\n                      { label: '表达式', value: 'expression' },\n                      { label: '委托表达式', value: 'delegateExpression' }\n                    ]\n                  },\n                  {\n                    label: 'java 类名',\n                    name: 'className',\n                    xType: 'input',\n                    rules: [{ required: true, message: '请输入', trigger: ['blur', 'change'] }]\n                  },\n                  {\n                    xType: 'slot',\n                    label: '参数',\n                    width: 120,\n                    slot: true,\n                    name: 'params'\n                  }\n                ]\n              }\n            ]\n          }\n        ]\n      }\n    }\n  },\n  mounted() {\n    this.formData.taskListener = this.element.businessObject.extensionElements?.values\n      .filter(item => item.$type === 'flowable:TaskListener')\n      .map(item => {\n        let type\n        if ('class' in item) type = 'class'\n        if ('expression' in item) type = 'expression'\n        if ('delegateExpression' in item) type = 'delegateExpression'\n        return {\n          event: item.event,\n          type: type,\n          className: item[type],\n          params: item.fields?.map(field => {\n            let fieldType\n            if ('stringValue' in field) fieldType = 'stringValue'\n            if ('expression' in field) fieldType = 'expression'\n            return {\n              name: field.name,\n              type: fieldType,\n              value: field[fieldType]\n            }\n          }) ?? []\n        }\n      }) ?? []\n  },\n  methods: {\n    configParam(index) {\n      this.nowIndex = index\n      const nowObj = this.formData.taskListener[index]\n      if (!nowObj.params) {\n        nowObj.params = []\n      }\n      this.showParamDialog = true\n    },\n    finishConfigParam(param) {\n      this.showParamDialog = false\n      // hack 数量不更新问题\n      const cache = this.formData.taskListener[this.nowIndex]\n      cache.params = param\n      this.$set(this.formData.taskListener[this.nowIndex], this.nowIndex, cache)\n      this.nowIndex = null\n    },\n    updateElement() {\n      if (this.formData.taskListener?.length) {\n        let extensionElements = this.element.businessObject.get('extensionElements')\n        if (!extensionElements) {\n          extensionElements = this.modeler.get('moddle').create('bpmn:ExtensionElements')\n        }\n        // 清除旧值\n        extensionElements.values = extensionElements.values?.filter(item => item.$type !== 'flowable:TaskListener') ?? []\n        this.formData.taskListener.forEach(item => {\n          const taskListener = this.modeler.get('moddle').create('flowable:TaskListener')\n          taskListener['event'] = item.event\n          taskListener[item.type] = item.className\n          if (item.params && item.params.length) {\n            item.params.forEach(field => {\n              const fieldElement = this.modeler.get('moddle').create('flowable:Field')\n              fieldElement['name'] = field.name\n              fieldElement[field.type] = field.value\n              // 注意：flowable.json 中定义的string和expression类为小写，不然会和原生的String类冲突，此处为hack\n              // const valueElement = this.modeler.get('moddle').create(`flowable:${field.type}`, { body: field.value })\n              // fieldElement[field.type] = valueElement\n              taskListener.get('fields').push(fieldElement)\n            })\n          }\n          extensionElements.get('values').push(taskListener)\n        })\n        this.updateProperties({ extensionElements: extensionElements })\n      } else {\n        const extensionElements = this.element.businessObject[`extensionElements`]\n        if (extensionElements) {\n          extensionElements.values = extensionElements.values?.filter(item => item.$type !== 'flowable:TaskListener') ?? []\n        }\n      }\n    },\n    closeDialog() {\n      this.$refs.xForm.validate().then(() => {\n        this.updateElement()\n        this.dialogVisible = false\n      }).catch(e => console.error(e))\n    }\n  }\n}\n</script>\n\n<style>\n.flow-containers  .el-badge__content.is-fixed {\n    top: 18px;\n}\n</style>\n"]}]}