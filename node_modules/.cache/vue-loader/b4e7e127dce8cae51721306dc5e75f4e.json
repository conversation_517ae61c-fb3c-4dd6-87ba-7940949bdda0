{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/flowable/task/record/view.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/flowable/task/record/view.vue", "mtime": 1717760123606}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGZsb3dSZWNvcmQsIGdldEhpc0lucyB9IGZyb20gIkAvYXBpL2Zsb3dhYmxlL2ZpbmlzaGVkIjsKaW1wb3J0IFBhcnNlciBmcm9tICJAL2NvbXBvbmVudHMvcGFyc2VyL1BhcnNlciI7CmltcG9ydCBVcGxvYWQgZnJvbSAiQC9jb21wb25lbnRzL0ZpbGVVcGxvYWQiOwppbXBvcnQgewogIGRlZmluaXRpb25TdGFydEJ5S2V5LAogIGdldFByb2Nlc3NWYXJpYWJsZXMsCiAgcmVhZFhtbEJ5S2V5LAogIGdldEZsb3dWaWV3ZXIsCn0gZnJvbSAiQC9hcGkvZmxvd2FibGUvZGVmaW5pdGlvbiI7CmltcG9ydCB7CiAgY29tcGxldGUsCiAgcmVqZWN0VGFzaywKICByZXR1cm5MaXN0LAogIHJldHVyblRhc2ssCiAgZ2V0TmV4dEZsb3dOb2RlLAogIGRlbGVnYXRlLAogIGVuZFRhc2ssCn0gZnJvbSAiQC9hcGkvZmxvd2FibGUvdG9kbyI7CmltcG9ydCBmbG93IGZyb20gIkAvdmlld3MvZmxvd2FibGUvdGFzay9yZWNvcmQvZmxvdyI7CmltcG9ydCB7IHRyZWVzZWxlY3QgfSBmcm9tICJAL2FwaS9zeXN0ZW0vZGVwdCI7CmltcG9ydCAiQHJpb3BoYWUvdnVlLXRyZWVzZWxlY3QvZGlzdC92dWUtdHJlZXNlbGVjdC5jc3MiOwppbXBvcnQgVHJlZXNlbGVjdCBmcm9tICJAcmlvcGhhZS92dWUtdHJlZXNlbGVjdCI7CmltcG9ydCB7IGxpc3RVc2VyIH0gZnJvbSAiQC9hcGkvc3lzdGVtL3VzZXIiOwppbXBvcnQgbW9tZW50IGZyb20gIm1vbWVudCI7CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiUmVjb3JkIiwKICBjb21wb25lbnRzOiB7CiAgICBQYXJzZXIsCiAgICBmbG93LAogICAgVHJlZXNlbGVjdCwKICAgIFVwbG9hZCwKICB9LAogIHByb3BzOiB7CiAgICBwcm9jRGVmS2V5OiB7CiAgICAgIHR5cGU6IFN0cmluZywKICAgICAgZGVmYXVsdDogdW5kZWZpbmVkLAogICAgfSwKICAgIHRhc2tJZDogewogICAgICB0eXBlOiBTdHJpbmcsCiAgICAgIGRlZmF1bHQ6IHVuZGVmaW5lZCwKICAgIH0sCiAgICBwcm9jSW5zSWQ6IHsKICAgICAgdHlwZTogU3RyaW5nLAogICAgICBkZWZhdWx0OiB1bmRlZmluZWQsCiAgICB9LAogICAgYml6S2V5OiB7CiAgICAgIHR5cGU6IFN0cmluZywKICAgICAgZGVmYXVsdDogdW5kZWZpbmVkLAogICAgfSwKICAgIGZpbmlzaGVkOiB7CiAgICAgIHR5cGU6IEJvb2xlYW4sCiAgICAgIGRlZmF1bHQ6IHRydWUsCiAgICB9LAogICAgaXNBdXRoSW1hZ2VzOiB7CiAgICAgIHR5cGU6IEJvb2xlYW4sCiAgICAgIGRlZmF1bHQ6IGZhbHNlLAogICAgfSwKICAgIHZpZXdPcGVuOiB7CiAgICAgIHR5cGU6IEJvb2xlYW4sCiAgICAgIGRlZmF1bHQ6IGZhbHNlLAogICAgfSwKICB9LAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICAvLyDmqKHlnot4bWzmlbDmja4KICAgICAgeG1sRGF0YTogIiIsCiAgICAgIHRhc2tMaXN0OiBbXSwKICAgICAgLy8g6YOo6Zeo5ZCN56ewCiAgICAgIGRlcHROYW1lOiB1bmRlZmluZWQsCiAgICAgIC8vIOmDqOmXqOagkemAiemhuQogICAgICBkZXB0T3B0aW9uczogdW5kZWZpbmVkLAogICAgICAvLyDnlKjmiLfooajmoLzmlbDmja4KICAgICAgdXNlckxpc3Q6IG51bGwsCiAgICAgIGRlZmF1bHRQcm9wczogewogICAgICAgIGNoaWxkcmVuOiAiY2hpbGRyZW4iLAogICAgICAgIGxhYmVsOiAibGFiZWwiLAogICAgICB9LAogICAgICAvLyDmn6Xor6Llj4LmlbAKICAgICAgcXVlcnlQYXJhbXM6IHsKICAgICAgICBkZXB0SWQ6IHVuZGVmaW5lZCwKICAgICAgfSwKICAgICAgdHlwZUFycjogWyJ6aXAiLCAicmFyIl0sCiAgICAgIC8vIOmBrue9qeWxggogICAgICBsb2FkaW5nOiB0cnVlLAogICAgICBmbG93UmVjb3JkTGlzdDogW10sIC8vIOa1geeoi+a1gei9rOaVsOaNrgogICAgICBmbG93UmVjb3JkTGlzdHM6IHt9LAogICAgICBmb3JtQ29uZkNvcHk6IHt9LAogICAgICBzcmM6IG51bGwsCiAgICAgIHJ1bGVzOiB7fSwgLy8g6KGo5Y2V5qCh6aqMCiAgICAgIHZhcmlhYmxlc0Zvcm06IHt9LCAvLyDmtYHnqIvlj5jph4/mlbDmja4KICAgICAgdGFza0Zvcm06IHsKICAgICAgICByZXR1cm5UYXNrU2hvdzogZmFsc2UsIC8vIOaYr+WQpuWxleekuuWbnumAgOihqOWNlQogICAgICAgIGRlbGVnYXRlVGFza1Nob3c6IGZhbHNlLCAvLyDmmK/lkKblsZXnpLrlm57pgIDooajljZUKICAgICAgICBkZWZhdWx0VGFza1Nob3c6IHRydWUsIC8vIOm7mOiupOWkhOeQhgogICAgICAgIHNlbmRVc2VyU2hvdzogZmFsc2UsIC8vIOWuoeaJueeUqOaItwogICAgICAgIG11bHRpcGxlOiBmYWxzZSwKICAgICAgICBjb21tZW50OiAiIiwgLy8g5oSP6KeB5YaF5a65CiAgICAgICAgcHJvY0luc0lkOiAiIiwgLy8g5rWB56iL5a6e5L6L57yW5Y+3CiAgICAgICAgaW5zdGFuY2VJZDogIiIsIC8vIOa1geeoi+WunuS+i+e8luWPtwogICAgICAgIHRhc2tJZDogIiIsIC8vIOa1geeoi+S7u+WKoee8luWPtwogICAgICAgIHByb2NEZWZLZXk6ICIiLCAvLyDmtYHnqIvnvJblj7cKICAgICAgICB2YXJzOiAiIiwKICAgICAgICB0YXJnZXRLZXk6ICIiLAogICAgICAgIGF1dGhJbWFnZXM6ICIiLAogICAgICB9LAogICAgICB1c2VyRGF0YUxpc3Q6IFtdLCAvLyDmtYHnqIvlgJnpgInkuroKICAgICAgYXNzaWduZWU6IG51bGwsCiAgICAgIGZvcm1Db25mOiB7fSwgLy8g6buY6K6k6KGo5Y2V5pWw5o2uCiAgICAgIGZvcm1Db25mT3BlbjogZmFsc2UsIC8vIOaYr+WQpuWKoOi9vem7mOiupOihqOWNleaVsOaNrgogICAgICB2YXJpYWJsZXM6IFtdLCAvLyDmtYHnqIvlj5jph4/mlbDmja4KICAgICAgdmFyaWFibGVzRGF0YToge30sIC8vIOa1geeoi+WPmOmHj+aVsOaNrgogICAgICB2YXJpYWJsZU9wZW46IGZhbHNlLCAvLyDmmK/lkKbliqDovb3mtYHnqIvlj5jph4/mlbDmja4KICAgICAgcmV0dXJuVGFza0xpc3Q6IFtdLCAvLyDlm57pgIDliJfooajmlbDmja4KICAgICAgY29tcGxldGVUaXRsZTogbnVsbCwKICAgICAgY29tcGxldGVPcGVuOiBmYWxzZSwKICAgICAgcmV0dXJuVGl0bGU6IG51bGwsCiAgICAgIHJldHVybk9wZW46IGZhbHNlLAogICAgICByZWplY3RPcGVuOiBmYWxzZSwKICAgICAgcmVqZWN0VGl0bGU6IG51bGwsCiAgICAgIHJlamVjdE9wZW4xOiBmYWxzZSwKICAgICAgcmVqZWN0VGl0bGUxOiBudWxsLAogICAgICB1c2VyRGF0YTogW10sCiAgICAgIGF1ZGl0OiB0cnVlLAogICAgICBjYW5GaW5pc2g6IGZhbHNlLAogICAgICBmbG93SGlzOiBbXSwKICAgICAgZmxvd0FjdGl2ZTogbnVsbCwKICAgICAgYml6S2V5OiBudWxsLAogICAgICBpc0NvbW1vbjogZmFsc2UsCiAgICAgIGF1dGhJbWFnZXM6IFtdLAogICAgfTsKICB9LAogIGNyZWF0ZWQoKSB7CiAgICBpZiAoCiAgICAgIHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIucm9sZXMgJiYKICAgICAgdGhpcy4kc3RvcmUuc3RhdGUudXNlci5yb2xlcy5pbmNsdWRlcygiY29tbW9uIikKICAgICkgewogICAgICB0aGlzLmlzQ29tbW9uID0gdHJ1ZTsKICAgIH0KICAgIGNvbnNvbGUubG9nKCI9PT09PT09PXJlY29yZD09PT09PT09Y3JlYXRlZD0+Pj4iKTsKICAgIGNvbnNvbGUubG9nKHRoaXMuX3Byb3BzKTsKICAgIGxldCB0aGF0ID0gdGhpczsKICAgIGxldCB7IHRhc2tJZCwgcHJvY0RlZktleSwgcHJvY0luc0lkLCBmaW5pc2hlZCwgYml6S2V5IH0gPSB0aGlzLl9wcm9wczsKICAgIC8vIGlmKCF2aWV3T3Blbil7CiAgICAvLyAgIGNvbnNvbGUubG9nKCI9PT0+Pj7lhbPpl60s5LiN5riy5p+TIikKICAgIC8vICAgcmV0dXJuOwogICAgLy8gfQogICAgdGhpcy50YXNrRm9ybS50YXNrSWQgPSB0YXNrSWQ7CiAgICB0aGlzLnRhc2tGb3JtLnByb2NJbnNJZCA9IHByb2NJbnNJZDsKICAgIHRoaXMudGFza0Zvcm0uaW5zdGFuY2VJZCA9IHByb2NJbnNJZDsKICAgIC8vIOWIneWni+WMluihqOWNlQogICAgdGhpcy50YXNrRm9ybS5wcm9jRGVmS2V5ID0gcHJvY0RlZktleTsKICAgIHRoaXMuYml6S2V5ID0gYml6S2V5OwogICAgLy/ph43nva4KICAgIHRoYXQuZmxvd0hpcyA9IFtdOwogICAgdGhhdC5mbG93UmVjb3JkTGlzdHMgPSBudWxsOwogICAgLy8g5Zue5pi+5rWB56iL6K6w5b2VCiAgICBpZiAocHJvY0luc0lkKSB7CiAgICAgIHRoaXMuZ2V0Rmxvd1ZpZXdlcih0aGlzLnRhc2tGb3JtLnByb2NJbnNJZCwgdGhpcy50YXNrRm9ybS5wcm9jRGVmS2V5KTsKICAgICAgLy8g5rWB56iL5Lu75Yqh6YeN6I635Y+W5Y+Y6YeP6KGo5Y2VCiAgICAgIGlmICh0aGlzLnRhc2tGb3JtLnRhc2tJZCkgewogICAgICAgIHRoaXMucHJvY2Vzc1ZhcmlhYmxlcyh0aGlzLnRhc2tGb3JtLnRhc2tJZCk7CiAgICAgICAgdGhpcy5nZXROZXh0Rmxvd05vZGUodGhpcy50YXNrRm9ybS50YXNrSWQpOwogICAgICB9CiAgICAgIHRoaXMuZ2V0Rmxvd1JlY29yZExpc3QodGhpcy50YXNrRm9ybS5wcm9jSW5zSWQpOwogICAgICB0aGF0LmZsb3dBY3RpdmUgPSBwcm9jSW5zSWQ7CiAgICAgIGdldEhpc0lucyh7CiAgICAgICAgYml6S2V5OiB0aGF0LmJpektleSwKICAgICAgICBkZWZLZXk6IHByb2NEZWZLZXksCiAgICAgIH0pLnRoZW4oKHJlc3ApID0+IHsKICAgICAgICBpZiAocmVzcC5kYXRhICYmIHJlc3AuZGF0YS5sZW5ndGggPiAwKSB7CiAgICAgICAgICB0aGF0LnRhc2tGb3JtLnRhc2tOYW1lID0gcmVzcC5kYXRhWzBdLm5hbWU7CiAgICAgICAgfQogICAgICAgIGlmIChyZXNwLmRhdGEubGVuZ3RoID4gMSkgewogICAgICAgICAgdGhhdC5mbG93SGlzID0gcmVzcC5kYXRhOwogICAgICAgICAgdGhhdC5mbG93UmVjb3JkTGlzdHMgPSBuZXcgTWFwKCk7CiAgICAgICAgICB0aGF0LmZsb3dSZWNvcmRMaXN0cy5zZXQocHJvY0luc0lkLCB0aGF0LmZsb3dSZWNvcmRMaXN0KTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSBlbHNlIHsKICAgICAgdGhpcy5nZXRNb2RlbERldGFpbChwcm9jRGVmS2V5KTsKICAgIH0KICAgIHRoaXMuZmluaXNoZWQgPSBmaW5pc2hlZDsKICB9LAogIG1vdW50ZWQoKSB7CiAgICAvLyAvLyDooajljZXmlbDmja7lm57loavvvIzmqKHmi5/lvILmraXor7fmsYLlnLrmma8KICAgIC8vIHNldFRpbWVvdXQoKCkgPT4gewogICAgLy8gICAvLyDor7fmsYLlm57mnaXnmoTooajljZXmlbDmja4KICAgIC8vICAgY29uc3QgZGF0YSA9IHsKICAgIC8vICAgICBmaWVsZDEwMjogJzE4ODM2NjYyNTU1JwogICAgLy8gICB9CiAgICAvLyAgIC8vIOWbnuWhq+aVsOaNrgogICAgLy8gICB0aGlzLmZpbGxGb3JtRGF0YSh0aGlzLmZvcm1Db25mLCBkYXRhKQogICAgLy8gICAvLyDmm7TmlrDooajljZUKICAgIC8vICAgdGhpcy5rZXkgPSArbmV3IERhdGUoKS5nZXRUaW1lKCkKICAgIC8vIH0sIDEwMDApCiAgfSwKICBtZXRob2RzOiB7CiAgICAvKiog5p+l6K+i6YOo6Zeo5LiL5ouJ5qCR57uT5p6EICovCiAgICBnZXRUcmVlc2VsZWN0KCkgewogICAgICB0cmVlc2VsZWN0KCkudGhlbigocmVzcG9uc2UpID0+IHsKICAgICAgICB0aGlzLmRlcHRPcHRpb25zID0gcmVzcG9uc2UuZGF0YTsKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOafpeivoueUqOaIt+WIl+ihqCAqLwogICAgZ2V0TGlzdCgpIHsKICAgICAgbGlzdFVzZXIodGhpcy5hZGREYXRlUmFuZ2UodGhpcy5xdWVyeVBhcmFtcywgdGhpcy5kYXRlUmFuZ2UpKS50aGVuKAogICAgICAgIChyZXNwb25zZSkgPT4gewogICAgICAgICAgdGhpcy51c2VyTGlzdCA9IHJlc3BvbnNlLnJvd3M7CiAgICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWw7CiAgICAgICAgfQogICAgICApOwogICAgfSwKICAgIC8vIOetm+mAieiKgueCuQogICAgZmlsdGVyTm9kZSh2YWx1ZSwgZGF0YSkgewogICAgICBpZiAoIXZhbHVlKSByZXR1cm4gdHJ1ZTsKICAgICAgcmV0dXJuIGRhdGEubGFiZWwuaW5kZXhPZih2YWx1ZSkgIT09IC0xOwogICAgfSwKICAgIC8vIOiKgueCueWNleWHu+S6i+S7tgogICAgaGFuZGxlTm9kZUNsaWNrKGRhdGEpIHsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5kZXB0SWQgPSBkYXRhLmlkOwogICAgICB0aGlzLmdldExpc3QoKTsKICAgIH0sCiAgICAvKiogeG1sIOaWh+S7tiAqLwogICAgZ2V0TW9kZWxEZXRhaWwoZGVwbG95S2V5KSB7CiAgICAgIC8vIOWPkemAgeivt+axgu+8jOiOt+WPlnhtbAogICAgICByZWFkWG1sQnlLZXkoZGVwbG95S2V5KS50aGVuKChyZXMpID0+IHsKICAgICAgICB0aGlzLnhtbERhdGEgPSByZXMuZGF0YTsKICAgICAgfSk7CiAgICB9LAogICAgZ2V0Rmxvd1ZpZXdlcihwcm9jSW5zSWQsIGRlcGxveUtleSkgewogICAgICBnZXRGbG93Vmlld2VyKHByb2NJbnNJZCkudGhlbigocmVzKSA9PiB7CiAgICAgICAgdGhpcy50YXNrTGlzdCA9IHJlcy5kYXRhOwogICAgICAgIHRoaXMuZ2V0TW9kZWxEZXRhaWwoZGVwbG95S2V5KTsKICAgICAgfSk7CiAgICB9LAogICAgc2V0SWNvbih2YWwpIHsKICAgICAgaWYgKHZhbCkgewogICAgICAgIHJldHVybiAiZWwtaWNvbi1jaGVjayI7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgcmV0dXJuICJlbC1pY29uLXRpbWUiOwogICAgICB9CiAgICB9LAogICAgc2V0Q29sb3IodmFsKSB7CiAgICAgIGlmICh2YWwpIHsKICAgICAgICByZXR1cm4gIiMyYmM0MTgiOwogICAgICB9IGVsc2UgewogICAgICAgIHJldHVybiAiI2IzYmRiYiI7CiAgICAgIH0KICAgIH0sCiAgICAvLyDlpJrpgInmoYbpgInkuK3mlbDmja4KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsKICAgICAgdGhpcy51c2VyRGF0YSA9IHNlbGVjdGlvbjsKICAgICAgY29uc3QgdmFsID0gc2VsZWN0aW9uLm1hcCgoaXRlbSkgPT4gaXRlbS51c2VySWQpWzBdOwogICAgICBpZiAodmFsIGluc3RhbmNlb2YgQXJyYXkpIHsKICAgICAgICB0aGlzLnRhc2tGb3JtLnZhbHVlcyA9IHsKICAgICAgICAgIGFwcHJvdmFsOiB2YWwuam9pbigiLCIpLAogICAgICAgIH07CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy50YXNrRm9ybS52YWx1ZXMgPSB7CiAgICAgICAgICBhcHByb3ZhbDogdmFsLAogICAgICAgIH07CiAgICAgIH0KICAgIH0sCiAgICAvLyDlhbPpl63moIfnrb4KICAgIGhhbmRsZUNsb3NlKHRhZykgewogICAgICB0aGlzLnVzZXJEYXRhLnNwbGljZSh0aGlzLnVzZXJEYXRhLmluZGV4T2YodGFnKSwgMSk7CiAgICB9LAogICAgLyoqIOa1geeoi+WPmOmHj+i1i+WAvCAqLwogICAgaGFuZGxlQ2hlY2tDaGFuZ2UodmFsKSB7CiAgICAgIGlmICh2YWwgaW5zdGFuY2VvZiBBcnJheSkgewogICAgICAgIHRoaXMudGFza0Zvcm0udmFsdWVzID0gewogICAgICAgICAgYXBwcm92YWw6IHZhbC5qb2luKCIsIiksCiAgICAgICAgfTsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLnRhc2tGb3JtLnZhbHVlcyA9IHsKICAgICAgICAgIGFwcHJvdmFsOiB2YWwsCiAgICAgICAgfTsKICAgICAgfQogICAgfSwKICAgIC8qKiDmtYHnqIvmtYHovazorrDlvZUgKi8KICAgIGdldEZsb3dSZWNvcmRMaXN0KHByb2NJbnNJZCkgewogICAgICBsZXQgdGhhdCA9IHRoaXM7CiAgICAgIGNvbnN0IHBhcmFtcyA9IHsgcHJvY0luc0lkOiBwcm9jSW5zSWQgfTsKICAgICAgZmxvd1JlY29yZChwYXJhbXMpCiAgICAgICAgLnRoZW4oKHJlcykgPT4gewogICAgICAgICAgdGhhdC5mbG93UmVjb3JkTGlzdCA9IHJlcy5kYXRhLmZsb3dMaXN0OwogICAgICAgICAgLy8g5rWB56iL6L+H56iL5Lit5LiN5a2Y5Zyo5Yid5aeL5YyW6KGo5Y2VIOebtOaOpeivu+WPlueahOa1geeoi+WPmOmHj+S4reWtmOWCqOeahOihqOWNleWAvAogICAgICAgICAgaWYgKHJlcy5kYXRhLmZvcm1EYXRhKSB7CiAgICAgICAgICAgIHRoYXQuZm9ybUNvbmYgPSByZXMuZGF0YS5mb3JtRGF0YTsKICAgICAgICAgICAgdGhhdC5mb3JtQ29uZk9wZW4gPSB0cnVlOwogICAgICAgICAgfQogICAgICAgICAgaWYgKHRoYXQuZmxvd1JlY29yZExpc3RzKSB7CiAgICAgICAgICAgIHRoYXQuZmxvd1JlY29yZExpc3RzLnNldChwcm9jSW5zSWQsIHRoYXQuZmxvd1JlY29yZExpc3QpOwogICAgICAgICAgfQogICAgICAgIH0pCiAgICAgICAgLmNhdGNoKChyZXMpID0+IHsKICAgICAgICAgIHRoYXQuJHJvdXRlci5nbygwKTsKICAgICAgICB9KTsKICAgIH0sCiAgICBmaWxsRm9ybURhdGEoZm9ybSwgZGF0YSkgewogICAgICBmb3JtLmZpZWxkcy5mb3JFYWNoKChpdGVtKSA9PiB7CiAgICAgICAgY29uc3QgdmFsID0gZGF0YVtpdGVtLl9fdk1vZGVsX19dOwogICAgICAgIGlmICh2YWwpIHsKICAgICAgICAgIGl0ZW0uX19jb25maWdfXy5kZWZhdWx0VmFsdWUgPSB2YWw7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvKiog6I635Y+W5rWB56iL5Y+Y6YeP5YaF5a65ICovCiAgICBwcm9jZXNzVmFyaWFibGVzKHRhc2tJZCkgewogICAgICBpZiAodGFza0lkKSB7CiAgICAgICAgLy8g5o+Q5Lqk5rWB56iL55Sz6K+35pe25aGr5YaZ55qE6KGo5Y2V5a2Y5YWl5LqG5rWB56iL5Y+Y6YeP5Lit5ZCO57ut5Lu75Yqh5aSE55CG5pe26ZyA6KaB5bGV56S6CiAgICAgICAgZ2V0UHJvY2Vzc1ZhcmlhYmxlcyh0YXNrSWQpLnRoZW4oKHJlcykgPT4gewogICAgICAgICAgLy8gdGhpcy52YXJpYWJsZXMgPSByZXMuZGF0YS52YXJpYWJsZXM7CiAgICAgICAgICB0aGlzLnZhcmlhYmxlc0RhdGEgPSByZXMuZGF0YS52YXJpYWJsZXM7CiAgICAgICAgICB0aGlzLnZhcmlhYmxlT3BlbiA9IHRydWU7CiAgICAgICAgfSk7CiAgICAgIH0KICAgIH0sCiAgICAvKiog5qC55o2u5b2T5YmN5Lu75Yqh5oiW6ICF5rWB56iL6K6+6K6h6YWN572u55qE5LiL5LiA5q2l6IqC54K5ICovCiAgICBnZXROZXh0Rmxvd05vZGUodGFza0lkKSB7CiAgICAgIC8vIOagueaNruW9k+WJjeS7u+WKoeaIluiAhea1geeoi+iuvuiuoemFjee9rueahOS4i+S4gOatpeiKgueCuSB0b2RvIOaaguaXtuacqua2ieWPiuWIsOiAg+iZkee9keWFs+OAgeihqOi+vuW8j+WSjOWkmuiKgueCueaDheWGtQogICAgICBjb25zdCBwYXJhbXMgPSB7IHRhc2tJZDogdGFza0lkIH07CiAgICAgIGdldE5leHRGbG93Tm9kZShwYXJhbXMpLnRoZW4oKHJlcykgPT4gewogICAgICAgIGNvbnN0IGRhdGEgPSByZXMuZGF0YTsKICAgICAgICBpZiAoZGF0YSkgewogICAgICAgICAgaWYgKGRhdGEudHlwZSA9PT0gImFzc2lnbmVlIikgewogICAgICAgICAgICB0aGlzLnVzZXJEYXRhTGlzdCA9IHJlcy5kYXRhLnVzZXJMaXN0OwogICAgICAgICAgfSBlbHNlIGlmIChkYXRhLnR5cGUgPT09ICJjYW5kaWRhdGVVc2VycyIpIHsKICAgICAgICAgICAgdGhpcy51c2VyRGF0YUxpc3QgPSByZXMuZGF0YS51c2VyTGlzdDsKICAgICAgICAgICAgdGhpcy50YXNrRm9ybS5tdWx0aXBsZSA9IHRydWU7CiAgICAgICAgICB9IGVsc2UgaWYgKGRhdGEudHlwZSA9PT0gImNhbmRpZGF0ZUdyb3VwcyIpIHsKICAgICAgICAgICAgcmVzLmRhdGEucm9sZUxpc3QuZm9yRWFjaCgocm9sZSkgPT4gewogICAgICAgICAgICAgIHJvbGUudXNlcklkID0gcm9sZS5yb2xlSWQ7CiAgICAgICAgICAgICAgcm9sZS5uaWNrTmFtZSA9IHJvbGUucm9sZU5hbWU7CiAgICAgICAgICAgIH0pOwogICAgICAgICAgICB0aGlzLnVzZXJEYXRhTGlzdCA9IHJlcy5kYXRhLnJvbGVMaXN0OwogICAgICAgICAgICB0aGlzLnRhc2tGb3JtLm11bHRpcGxlID0gZmFsc2U7CiAgICAgICAgICB9IGVsc2UgaWYgKGRhdGEudHlwZSA9PT0gIm11bHRpSW5zdGFuY2UiKSB7CiAgICAgICAgICAgIHRoaXMudXNlckRhdGFMaXN0ID0gcmVzLmRhdGEudXNlckxpc3Q7CiAgICAgICAgICAgIHRoaXMudGFza0Zvcm0ubXVsdGlwbGUgPSB0cnVlOwogICAgICAgICAgfQogICAgICAgICAgdGhpcy50YXNrRm9ybS5zZW5kVXNlclNob3cgPSB0cnVlOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLmNhbkZpbmlzaCA9IHRydWU7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvKiog5a6h5om55Lu75Yqh6YCJ5oupICovCiAgICBoYW5kbGVDb21wbGV0ZSgpIHsKICAgICAgdGhpcy5jb21wbGV0ZU9wZW4gPSB0cnVlOwogICAgICB0aGlzLmNvbXBsZXRlVGl0bGUgPSAi5a6h5om55rWB56iLIjsKICAgICAgLy90aGlzLmdldFRyZWVzZWxlY3QoKTsKICAgIH0sCiAgICAvKiog5a6h5om55Lu75YqhICovCiAgICB0YXNrQ29tcGxldGUoY29tbWVudCkgewogICAgICAvLyBpZiAoIXRoaXMudGFza0Zvcm0udmFsdWVzKXsKICAgICAgLy8gICB0aGlzLm1zZ0Vycm9yKCLor7fpgInmi6nmtYHnqIvmjqXmlLbkurrlkZgiKTsKICAgICAgLy8gICByZXR1cm47CiAgICAgIC8vIH0KICAgICAgaWYgKAogICAgICAgIGNvbW1lbnQgJiYKICAgICAgICB0eXBlb2YgY29tbWVudCA9PSAic3RyaW5nIiAmJgogICAgICAgIGNvbW1lbnQuY29uc3RydWN0b3IgPT0gU3RyaW5nCiAgICAgICkgewogICAgICAgIHRoaXMudGFza0Zvcm0uY29tbWVudCA9IGNvbW1lbnQ7CiAgICAgIH0KICAgICAgaWYgKCF0aGlzLnRhc2tGb3JtLmNvbW1lbnQpIHsKICAgICAgICB0aGlzLm1zZ0Vycm9yKCLor7fovpPlhaXlrqHmibnmhI/op4EiKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgLy9pZih0aGlzLmNhbkZpbmlzaCl7CiAgICAgIHRoaXMudGFza0Zvcm0udmFsdWVzID0ge307CiAgICAgIHRoaXMudGFza0Zvcm0uYml6S2V5ID0gdGhpcy5iaXpLZXk7CiAgICAgIHRoaXMudGFza0Zvcm0udmFsdWVzLmlzUmVqZWN0ID0gZmFsc2U7CiAgICAgIC8vfQogICAgICBjb21wbGV0ZSh0aGlzLnRhc2tGb3JtKS50aGVuKChyZXNwb25zZSkgPT4gewogICAgICAgIHRoaXMubXNnU3VjY2VzcyhyZXNwb25zZS5tc2cpOwogICAgICAgIC8vdGhpcy5nb0JhY2soKTsKICAgICAgICB0aGlzLiRyb3V0ZXIuZ28oMCk7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDlp5TmtL7ku7vliqEgKi8KICAgIGhhbmRsZURlbGVnYXRlKCkgewogICAgICB0aGlzLnRhc2tGb3JtLmRlbGVnYXRlVGFza1Nob3cgPSB0cnVlOwogICAgICB0aGlzLnRhc2tGb3JtLmRlZmF1bHRUYXNrU2hvdyA9IGZhbHNlOwogICAgfSwKICAgIGhhbmRsZUFzc2lnbigpIHt9LAogICAgLyoqIOi/lOWbnumhtemdoiAqLwogICAgZ29CYWNrKCkgewogICAgICAvLyDlhbPpl63lvZPliY3moIfnrb7pobXlubbov5Tlm57kuIrkuKrpobXpnaIKICAgICAgdGhpcy4kc3RvcmUuZGlzcGF0Y2goInRhZ3NWaWV3L2RlbFZpZXciLCB0aGlzLiRyb3V0ZSk7CiAgICAgIHRoaXMuJHJvdXRlci5nbygtMSk7CiAgICB9LAogICAgLyoqIOaOpeaUtuWtkOe7hOS7tuS8oOeahOWAvCAqLwogICAgZ2V0RGF0YShkYXRhKSB7CiAgICAgIGlmIChkYXRhKSB7CiAgICAgICAgY29uc3QgdmFyaWFibGVzID0gW107CiAgICAgICAgZGF0YS5maWVsZHMuZm9yRWFjaCgoaXRlbSkgPT4gewogICAgICAgICAgbGV0IHZhcmlhYmxlRGF0YSA9IHt9OwogICAgICAgICAgdmFyaWFibGVEYXRhLmxhYmVsID0gaXRlbS5fX2NvbmZpZ19fLmxhYmVsOwogICAgICAgICAgLy8g6KGo5Y2V5YC85Li65aSa5Liq6YCJ6aG55pe2CiAgICAgICAgICBpZiAoaXRlbS5fX2NvbmZpZ19fLmRlZmF1bHRWYWx1ZSBpbnN0YW5jZW9mIEFycmF5KSB7CiAgICAgICAgICAgIGNvbnN0IGFycmF5ID0gW107CiAgICAgICAgICAgIGl0ZW0uX19jb25maWdfXy5kZWZhdWx0VmFsdWUuZm9yRWFjaCgodmFsKSA9PiB7CiAgICAgICAgICAgICAgYXJyYXkucHVzaCh2YWwpOwogICAgICAgICAgICB9KTsKICAgICAgICAgICAgdmFyaWFibGVEYXRhLnZhbCA9IGFycmF5OwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgdmFyaWFibGVEYXRhLnZhbCA9IGl0ZW0uX19jb25maWdfXy5kZWZhdWx0VmFsdWU7CiAgICAgICAgICB9CiAgICAgICAgICB2YXJpYWJsZXMucHVzaCh2YXJpYWJsZURhdGEpOwogICAgICAgIH0pOwogICAgICAgIHRoaXMudmFyaWFibGVzID0gdmFyaWFibGVzOwogICAgICB9CiAgICB9LAogICAgLy8g5o6l5pS25a2Q57uE5Lu25Zu+54mH5LiK5Lyg55qE5YC8CiAgICBpbWFnZVVybHModmFsdWUpIHsKICAgICAgdGhpcy50YXNrRm9ybS5hdXRoSW1hZ2VzID0gdmFsdWU7CiAgICB9LAogICAgLyoqIOeUs+ivt+a1geeoi+ihqOWNleaVsOaNruaPkOS6pCAqLwogICAgLy8gc3VibWl0Rm9ybShkYXRhKSB7CiAgICAvLyAgIGlmIChkYXRhKSB7CiAgICAvLyAgICAgY29uc3QgdmFyaWFibGVzID0gZGF0YS52YWxEYXRhOwogICAgLy8gICAgIGNvbnN0IGZvcm1EYXRhID0gZGF0YS5mb3JtRGF0YTsKICAgIC8vICAgICBmb3JtRGF0YS5kaXNhYmxlZCA9IHRydWU7CiAgICAvLyAgICAgZm9ybURhdGEuZm9ybUJ0bnMgPSBmYWxzZTsKICAgIC8vICAgICBpZiAodGhpcy50YXNrRm9ybS5wcm9jRGVmS2V5KSB7CiAgICAvLyAgICAgICB2YXJpYWJsZXMudmFyaWFibGVzID0gZm9ybURhdGE7CiAgICAvLyAgICAgICB2YXJpYWJsZXMuYnVzaW5lc3NLZXkgPSBkYXRhLmJ1c2luZXNzS2V5OwogICAgLy8gICAgICAgIC8vIOWQr+WKqOa1geeoi+W5tuWwhuihqOWNleaVsOaNruWKoOWFpea1geeoi+WPmOmHjwogICAgLy8gICAgICAgZGVmaW5pdGlvblN0YXJ0QnlLZXkodGhpcy50YXNrRm9ybS5wcm9jRGVmS2V5LCBKU09OLnN0cmluZ2lmeSh2YXJpYWJsZXMpKS50aGVuKHJlcyA9PiB7CiAgICAvLyAgICAgICAgIHRoaXMubXNnU3VjY2VzcyhyZXMubXNnKTsKICAgIC8vICAgICAgICAgdGhpcy5nb0JhY2soKTsKICAgIC8vICAgICAgIH0pCiAgICAvLyAgICAgfQogICAgLy8gICB9CiAgICAvLyB9LAogICAgc3RhcnRGbG93KGJ1c2luZXNzS2V5LCBuYW1lLCB2YXJpYWJsZXMpIHsKICAgICAgbGV0IHN0YXJ0RGF0ZSA9IG1vbWVudChuZXcgRGF0ZSgpKS5mb3JtYXQoIllZWVlNTURESEhtbXNzIik7CiAgICAgIGNvbnN0IGRhdGEgPSB7fTsKICAgICAgaWYgKHRoaXMudGFza0Zvcm0ucHJvY0RlZktleSkgewogICAgICAgIGlmICghdmFyaWFibGVzKSB7CiAgICAgICAgICBkYXRhLnZhcmlhYmxlcyA9IHt9OwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICBkYXRhLnZhcmlhYmxlcyA9IHZhcmlhYmxlczsKICAgICAgICB9CiAgICAgICAgZGF0YS5idXNpbmVzc0tleSA9IGJ1c2luZXNzS2V5OwogICAgICAgIGRhdGEucHJvY0RlZktleSA9IHRoaXMudGFza0Zvcm0ucHJvY0RlZktleTsKICAgICAgICBkYXRhLnRhc2tOYW1lID0gbmFtZTsKICAgICAgICAvLyDlkK/liqjmtYHnqIvlubblsIbooajljZXmlbDmja7liqDlhaXmtYHnqIvlj5jph48KICAgICAgICBkZWZpbml0aW9uU3RhcnRCeUtleShKU09OLnN0cmluZ2lmeShkYXRhKSkudGhlbigocmVzKSA9PiB7CiAgICAgICAgICB0aGlzLm1zZ1N1Y2Nlc3MocmVzLm1zZyk7CiAgICAgICAgICB0aGlzLmdvQmFjaygpOwogICAgICAgIH0pOwogICAgICB9CiAgICB9LAogICAgLyoqIOmps+WbnuS7u+WKoSAqLwogICAgaGFuZGxlUmVqZWN0KCkgewogICAgICB0aGlzLnJlamVjdE9wZW4gPSB0cnVlOwogICAgICB0aGlzLnJlamVjdFRpdGxlID0gIumAgOWbnua1geeoiyI7CiAgICB9LAogICAgLyoqIOmps+WbnuS7u+WKoSAqLwogICAgdGFza1JlamVjdCgpIHsKICAgICAgdGhpcy4kcmVmc1sidGFza0Zvcm0iXS52YWxpZGF0ZSgodmFsaWQpID0+IHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIHJlamVjdFRhc2sodGhpcy50YXNrRm9ybSkudGhlbigocmVzKSA9PiB7CiAgICAgICAgICAgIHRoaXMubXNnU3VjY2VzcyhyZXMubXNnKTsKICAgICAgICAgICAgdGhpcy4kcm91dGVyLmdvKDApOwogICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvKiog5Y+v6YCA5Zue5Lu75Yqh5YiX6KGoICovCiAgICBoYW5kbGVSZXR1cm4oKSB7CiAgICAgIHRoaXMucmV0dXJuT3BlbiA9IHRydWU7CiAgICAgIHRoaXMucmV0dXJuVGl0bGUgPSAi6YCA5Zue5rWB56iLIjsKICAgICAgcmV0dXJuTGlzdCh0aGlzLnRhc2tGb3JtKS50aGVuKChyZXMpID0+IHsKICAgICAgICB0aGlzLnJldHVyblRhc2tMaXN0ID0gcmVzLmRhdGE7CiAgICAgICAgdGhpcy50YXNrRm9ybS52YWx1ZXMgPSBudWxsOwogICAgICAgIGlmIChyZXMuZGF0YSAmJiByZXMuZGF0YS5sZW5ndGggPiAwKSB7CiAgICAgICAgICB0aGlzLnRhc2tGb3JtLnRhcmdldEtleSA9IHJlcy5kYXRhW3Jlcy5kYXRhLmxlbmd0aCAtIDFdLmlkOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLyoqIOaPkOS6pOmAgOWbnuS7u+WKoSAqLwogICAgdGFza1JldHVybigpIHsKICAgICAgdGhpcy4kcmVmc1sidGFza0Zvcm0iXS52YWxpZGF0ZSgodmFsaWQpID0+IHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIC8v6YCA5Zue5Lia5YqhSUTvvIznlKjkuo7kv67mlLnnirbmgIEKICAgICAgICAgIHRoaXMudGFza0Zvcm0uYml6S2V5ID0gdGhpcy5iaXpLZXk7CiAgICAgICAgICByZXR1cm5UYXNrKHRoaXMudGFza0Zvcm0pLnRoZW4oKHJlcykgPT4gewogICAgICAgICAgICB0aGlzLm1zZ1N1Y2Nlc3MocmVzLm1zZyk7CiAgICAgICAgICAgIHRoaXMuJHJvdXRlci5nbygwKTsKICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLyoqIOWPlua2iOWbnumAgOS7u+WKoeaMiemSriAqLwogICAgY2FuY2VsVGFzaygpIHsKICAgICAgdGhpcy50YXNrRm9ybS5yZXR1cm5UYXNrU2hvdyA9IGZhbHNlOwogICAgICB0aGlzLnRhc2tGb3JtLmRlZmF1bHRUYXNrU2hvdyA9IHRydWU7CiAgICAgIHRoaXMudGFza0Zvcm0uc2VuZFVzZXJTaG93ID0gdHJ1ZTsKICAgICAgdGhpcy5yZXR1cm5UYXNrTGlzdCA9IFtdOwogICAgfSwKICAgIC8qKiDlp5TmtL7ku7vliqEgKi8KICAgIHN1Ym1pdERlbGV0ZVRhc2soKSB7CiAgICAgIHRoaXMuJHJlZnNbInRhc2tGb3JtIl0udmFsaWRhdGUoKHZhbGlkKSA9PiB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICBkZWxlZ2F0ZSh0aGlzLnRhc2tGb3JtKS50aGVuKChyZXNwb25zZSkgPT4gewogICAgICAgICAgICB0aGlzLm1zZ1N1Y2Nlc3MocmVzcG9uc2UubXNnKTsKICAgICAgICAgICAgdGhpcy4kcm91dGVyLmdvKDApOwogICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvKiog5Y+W5raI5Zue6YCA5Lu75Yqh5oyJ6ZKuICovCiAgICBjYW5jZWxEZWxlZ2F0ZVRhc2soKSB7CiAgICAgIHRoaXMudGFza0Zvcm0uZGVsZWdhdGVUYXNrU2hvdyA9IGZhbHNlOwogICAgICB0aGlzLnRhc2tGb3JtLmRlZmF1bHRUYXNrU2hvdyA9IHRydWU7CiAgICAgIHRoaXMudGFza0Zvcm0uc2VuZFVzZXJTaG93ID0gdHJ1ZTsKICAgICAgdGhpcy5yZXR1cm5UYXNrTGlzdCA9IFtdOwogICAgfSwKICAgIGhhbmRsZUVuZCgpIHsKICAgICAgdGhpcy5yZWplY3RPcGVuMSA9IHRydWU7CiAgICAgIHRoaXMucmVqZWN0VGl0bGUxID0gIumps+Wbnua1geeoiyI7CiAgICB9LAogICAgLyoqIOmps+Wbnue7k+adn+S7u+WKoSAqLwogICAgdGFza0VuZCgpIHsKICAgICAgdGhpcy4kcmVmc1sidGFza0Zvcm0iXS52YWxpZGF0ZSgodmFsaWQpID0+IHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIHRoaXMudGFza0Zvcm0udmFsdWVzID0ge307CiAgICAgICAgICB0aGlzLnRhc2tGb3JtLnZhbHVlcy5pc1JlamVjdCA9IHRydWU7CiAgICAgICAgICBlbmRUYXNrKHRoaXMudGFza0Zvcm0pLnRoZW4oKHJlcykgPT4gewogICAgICAgICAgICB0aGlzLm1zZ1N1Y2Nlc3MocmVzLm1zZyk7CiAgICAgICAgICAgIHRoaXMuJHJvdXRlci5nbygwKTsKICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgaGFuZGxlQ2xpY2sodGFiLCBldmVudCkgewogICAgICBpZiAodGhpcy5mbG93UmVjb3JkTGlzdHMgJiYgdGhpcy5mbG93UmVjb3JkTGlzdHMuZ2V0KHRhYi5uYW1lKSkgewogICAgICAgIHRoaXMuZmxvd1JlY29yZExpc3QgPSB0aGlzLmZsb3dSZWNvcmRMaXN0cy5nZXQodGFiLm5hbWUpOwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuZ2V0Rmxvd1JlY29yZExpc3QodGFiLm5hbWUpOwogICAgICB9CiAgICB9LAogIH0sCn07Cg=="}, null]}