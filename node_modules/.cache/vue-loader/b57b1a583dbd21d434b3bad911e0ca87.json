{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/flowable/definition/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/flowable/definition/index.vue", "mtime": 1655049558000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGxpc3REZWZpbml0aW9uLCB1cGRhdGVTdGF0ZSwgZGVsRGVwbG95bWVudCwgYWRkRGVwbG95bWVudCwgdXBkYXRlRGVwbG95bWVudCwgZXhwb3J0RGVwbG95bWVudCwgZGVmaW5pdGlvblN0YXJ0LCByZWFkWG1sfSBmcm9tICJAL2FwaS9mbG93YWJsZS9kZWZpbml0aW9uIjsKaW1wb3J0IHsgZ2V0VG9rZW4gfSBmcm9tICJAL3V0aWxzL2F1dGgiOwppbXBvcnQgeyBnZXRGb3JtLCBhZGREZXBsb3lGb3JtICxsaXN0Rm9ybSB9IGZyb20gIkAvYXBpL2Zsb3dhYmxlL2Zvcm0iOwppbXBvcnQgUGFyc2VyIGZyb20gJ0AvY29tcG9uZW50cy9wYXJzZXIvUGFyc2VyJwppbXBvcnQgZmxvdyBmcm9tICdAL3ZpZXdzL2Zsb3dhYmxlL3Rhc2svcmVjb3JkL2Zsb3cnCgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogIkRlZmluaXRpb24iLAogIGNvbXBvbmVudHM6IHsKICAgIFBhcnNlciwKICAgIGZsb3cKICB9LAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICAvLyDpga7nvanlsYIKICAgICAgbG9hZGluZzogdHJ1ZSwKICAgICAgLy8g6YCJ5Lit5pWw57uECiAgICAgIGlkczogW10sCiAgICAgIC8vIOmdnuWNleS4quemgeeUqAogICAgICBzaW5nbGU6IHRydWUsCiAgICAgIC8vIOmdnuWkmuS4quemgeeUqAogICAgICBtdWx0aXBsZTogdHJ1ZSwKICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2CiAgICAgIHNob3dTZWFyY2g6IHRydWUsCiAgICAgIC8vIOaAu+adoeaVsAogICAgICB0b3RhbDogMCwKICAgICAgLy8g5rWB56iL5a6a5LmJ6KGo5qC85pWw5o2uCiAgICAgIGRlZmluaXRpb25MaXN0OiBbXSwKICAgICAgLy8g5by55Ye65bGC5qCH6aKYCiAgICAgIHRpdGxlOiAiIiwKICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGCCiAgICAgIG9wZW46IGZhbHNlLAogICAgICBmb3JtQ29uZk9wZW46IGZhbHNlLAogICAgICBmb3JtVGl0bGU6ICIiLAogICAgICBmb3JtRGVwbG95T3BlbjogZmFsc2UsCiAgICAgIGZvcm1EZXBsb3lUaXRsZTogIiIsCiAgICAgIGZvcm1MaXN0OiBbXSwKICAgICAgZm9ybVRvdGFsOjAsCiAgICAgIGZvcm1Db25mOiB7fSwgLy8g6buY6K6k6KGo5Y2V5pWw5o2uCiAgICAgIHJlYWRJbWFnZTp7CiAgICAgICAgb3BlbjogZmFsc2UsCiAgICAgICAgc3JjOiAiIiwKICAgICAgfSwKICAgICAgLy8gYnBtbi54bWwg5a+85YWlCiAgICAgIHVwbG9hZDogewogICAgICAgIC8vIOaYr+WQpuaYvuekuuW8ueWHuuWxgu+8iHhtbOWvvOWFpe+8iQogICAgICAgIG9wZW46IGZhbHNlLAogICAgICAgIC8vIOW8ueWHuuWxguagh+mimO+8iHhtbOWvvOWFpe+8iQogICAgICAgIHRpdGxlOiAiIiwKICAgICAgICAvLyDmmK/lkKbnpoHnlKjkuIrkvKAKICAgICAgICBpc1VwbG9hZGluZzogZmFsc2UsCiAgICAgICAgbmFtZTogbnVsbCwKICAgICAgICBjYXRlZ29yeTogbnVsbCwKICAgICAgICAvLyDorr7nva7kuIrkvKDnmoTor7fmsYLlpLTpg6gKICAgICAgICBoZWFkZXJzOiB7IEF1dGhvcml6YXRpb246ICJCZWFyZXIgIiArIGdldFRva2VuKCkgfSwKICAgICAgICAvLyDkuIrkvKDnmoTlnLDlnYAKICAgICAgICB1cmw6IHByb2Nlc3MuZW52LlZVRV9BUFBfQkFTRV9BUEkgKyAiL2Zsb3dhYmxlL2RlZmluaXRpb24vaW1wb3J0IgogICAgICB9LAogICAgICAvLyDmn6Xor6Llj4LmlbAKICAgICAgcXVlcnlQYXJhbXM6IHsKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICBuYW1lOiBudWxsLAogICAgICAgIGNhdGVnb3J5OiBudWxsLAogICAgICAgIGtleTogbnVsbCwKICAgICAgICB0ZW5hbnRJZDogbnVsbCwKICAgICAgICBkZXBsb3lUaW1lOiBudWxsLAogICAgICAgIGRlcml2ZWRGcm9tOiBudWxsLAogICAgICAgIGRlcml2ZWRGcm9tUm9vdDogbnVsbCwKICAgICAgICBwYXJlbnREZXBsb3ltZW50SWQ6IG51bGwsCiAgICAgICAgZW5naW5lVmVyc2lvbjogbnVsbAogICAgICB9LAogICAgICBmb3JtUXVlcnlQYXJhbXM6ewogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICB9LAogICAgICAvLyDmjILovb3ooajljZXliLDmtYHnqIvlrp7kvosKICAgICAgZm9ybURlcGxveVBhcmFtOnsKICAgICAgICBmb3JtSWQ6IG51bGwsCiAgICAgICAgZGVwbG95SWQ6IG51bGwKICAgICAgfSwKICAgICAgY3VycmVudFJvdzogbnVsbCwKICAgICAgLy8geG1sCiAgICAgIHhtbERhdGE6IiIsCiAgICAgIC8vIOihqOWNleWPguaVsAogICAgICBmb3JtOiB7fSwKICAgICAgLy8g6KGo5Y2V5qCh6aqMCiAgICAgIHJ1bGVzOiB7CiAgICAgIH0sCiAgICAgIHByb2Nlc3NDYXRlZ29yeU9wdGlvbnM6IFtdCiAgICB9OwogIH0sCiAgYWN0aXZhdGVkKCl7CiAgICB0aGlzLmdldExpc3QoKTsKICB9LAogIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmdldExpc3QoKTsKICAgIHRoaXMuZ2V0RGljdHMoInN5c19wcm9jZXNzX2NhdGVnb3J5IikudGhlbigocmVzcG9uc2UpID0+IHsKICAgICAgdGhpcy5wcm9jZXNzQ2F0ZWdvcnlPcHRpb25zID0gcmVzcG9uc2UuZGF0YTsKICAgIH0pOwogIH0sCiAgbWV0aG9kczogewogICAgLyoqIOafpeivoua1geeoi+WumuS5ieWIl+ihqCAqLwogICAgZ2V0TGlzdCgpIHsKICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsKICAgICAgbGlzdERlZmluaXRpb24odGhpcy5xdWVyeVBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5kZWZpbml0aW9uTGlzdCA9IHJlc3BvbnNlLmRhdGEucmVjb3JkczsKICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UuZGF0YS50b3RhbDsKICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgfSk7CiAgICB9LAogICAgLy8g57yW6L6R54q25oCB5a2X5YW457+76K+RCiAgICBwcm9jZXNzQ2F0ZWdvcnlGb3JtYXQocm93LCBjb2x1bW4pIHsKICAgICAgcmV0dXJuIHRoaXMuc2VsZWN0RGljdExhYmVsKHRoaXMucHJvY2Vzc0NhdGVnb3J5T3B0aW9ucywgcm93LmNhdGVnb3J5KTsKICAgIH0sCiAgICAvLyDlj5bmtojmjInpkq4KICAgIGNhbmNlbCgpIHsKICAgICAgdGhpcy5vcGVuID0gZmFsc2U7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgIH0sCiAgICAvLyDooajljZXph43nva4KICAgIHJlc2V0KCkgewogICAgICB0aGlzLmZvcm0gPSB7CiAgICAgICAgaWQ6IG51bGwsCiAgICAgICAgbmFtZTogbnVsbCwKICAgICAgICBjYXRlZ29yeTogbnVsbCwKICAgICAgICBrZXk6IG51bGwsCiAgICAgICAgdGVuYW50SWQ6IG51bGwsCiAgICAgICAgZGVwbG95VGltZTogbnVsbCwKICAgICAgICBkZXJpdmVkRnJvbTogbnVsbCwKICAgICAgICBkZXJpdmVkRnJvbVJvb3Q6IG51bGwsCiAgICAgICAgcGFyZW50RGVwbG95bWVudElkOiBudWxsLAogICAgICAgIGVuZ2luZVZlcnNpb246IG51bGwKICAgICAgfTsKICAgICAgdGhpcy5yZXNldEZvcm0oImZvcm0iKTsKICAgIH0sCiAgICAvKiog5pCc57Si5oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVRdWVyeSgpIHsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsKICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICB9LAogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqLwogICAgcmVzZXRRdWVyeSgpIHsKICAgICAgdGhpcy5yZXNldEZvcm0oInF1ZXJ5Rm9ybSIpOwogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7CiAgICB9LAogICAgLy8g5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7CiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcChpdGVtID0+IGl0ZW0uaWQpCiAgICAgIHRoaXMuc2luZ2xlID0gc2VsZWN0aW9uLmxlbmd0aCE9PTEKICAgICAgdGhpcy5tdWx0aXBsZSA9ICFzZWxlY3Rpb24ubGVuZ3RoCiAgICB9LAogICAgLyoqIOaWsOWinuaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlQWRkKCkgewogICAgICB0aGlzLnJlc2V0KCk7CiAgICAgIHRoaXMub3BlbiA9IHRydWU7CiAgICAgIHRoaXMudGl0bGUgPSAi5re75Yqg5rWB56iL5a6a5LmJIjsKICAgIH0sCiAgICAvKiog6Lez6L2s5Yiw5rWB56iL6K6+6K6h6aG16Z2iICovCiAgICBoYW5kbGVMb2FkWG1sKHJvdyl7CiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHsgcGF0aDogJy9mbG93YWJsZS9kZWZpbml0aW9uL21vZGVsJyxxdWVyeTogeyBkZXBsb3lJZDogcm93LmRlcGxveW1lbnRJZCB9fSkKICAgIH0sCiAgICAvKiog5rWB56iL5Zu+5p+l55yLICovCiAgICBoYW5kbGVSZWFkSW1hZ2UoZGVwbG95bWVudElkKXsKICAgICAgdGhpcy5yZWFkSW1hZ2UudGl0bGUgPSAi5rWB56iL5Zu+IjsKICAgICAgdGhpcy5yZWFkSW1hZ2Uub3BlbiA9IHRydWU7CiAgICAgIC8vIHRoaXMucmVhZEltYWdlLnNyYyA9IHByb2Nlc3MuZW52LlZVRV9BUFBfQkFTRV9BUEkgKyAiL2Zsb3dhYmxlL2RlZmluaXRpb24vcmVhZEltYWdlLyIgKyBkZXBsb3ltZW50SWQ7CiAgICAgICAvLyDlj5HpgIHor7fmsYLvvIzojrflj5Z4bWwKICAgICAgcmVhZFhtbChkZXBsb3ltZW50SWQpLnRoZW4ocmVzID0+ewogICAgICAgIHRoaXMueG1sRGF0YSA9IHJlcy5kYXRhCiAgICAgIH0pCiAgICB9LAogICAgLyoqIOihqOWNleafpeeciyAqLwogICAgaGFuZGxlRm9ybShmb3JtSWQpewogICAgICBnZXRGb3JtKGZvcm1JZCkudGhlbihyZXMgPT57CiAgICAgICAgdGhpcy5mb3JtVGl0bGUgPSAi6KGo5Y2V6K+m5oOFIjsKICAgICAgICB0aGlzLmZvcm1Db25mT3BlbiA9IHRydWU7CiAgICAgICAgdGhpcy5mb3JtQ29uZiA9IEpTT04ucGFyc2UocmVzLmRhdGEuZm9ybUNvbnRlbnQpCiAgICAgIH0pCiAgICB9LAogICAgLyoqIOWQr+WKqOa1geeoiyAqLwogICAgaGFuZGxlRGVmaW5pdGlvblN0YXJ0KHJvdyl7CiAgICAgIGRlZmluaXRpb25TdGFydChyb3cuaWQpLnRoZW4ocmVzID0+ewogICAgICAgIHRoaXMubXNnU3VjY2VzcyhyZXMubXNnKTsKICAgICAgfSkKICAgIH0sCiAgICAvKiog5oyC6L296KGo5Y2V5by55qGGICovCiAgICBoYW5kbGVBZGRGb3JtKHJvdyl7CiAgICAgIHRoaXMuZm9ybURlcGxveVBhcmFtLmRlcGxveUlkID0gcm93LmRlcGxveW1lbnRJZAogICAgICB0aGlzLkxpc3RGb3JtRGVwbG95KCkKICAgIH0sCiAgICAvKiog5oyC6L296KGo5Y2V5YiX6KGoICovCiAgICBMaXN0Rm9ybURlcGxveSgpewogICAgICBsaXN0Rm9ybSh0aGlzLmZvcm1RdWVyeVBhcmFtcykudGhlbihyZXMgPT57CiAgICAgICAgdGhpcy5mb3JtTGlzdCA9IHJlcy5yb3dzOwogICAgICAgIHRoaXMuZm9ybVRvdGFsID0gcmVzLnRvdGFsOwogICAgICAgIHRoaXMuZm9ybURlcGxveU9wZW4gPSB0cnVlOwogICAgICAgIHRoaXMuZm9ybURlcGxveVRpdGxlID0gIuaMgui9veihqOWNlSI7CiAgICAgIH0pCiAgICB9LAogICAgLy8gLyoqIOabtOaUueaMgui9veihqOWNleW8ueahhiAqLwogICAgLy8gaGFuZGxlRWRpdEZvcm0ocm93KXsKICAgIC8vICAgdGhpcy5mb3JtRGVwbG95UGFyYW0uZGVwbG95SWQgPSByb3cuZGVwbG95bWVudElkCiAgICAvLyAgIGNvbnN0IHF1ZXJ5UGFyYW1zID0gewogICAgLy8gICAgIHBhZ2VOdW06IDEsCiAgICAvLyAgICAgcGFnZVNpemU6IDEwCiAgICAvLyAgIH0KICAgIC8vICAgbGlzdEZvcm0ocXVlcnlQYXJhbXMpLnRoZW4ocmVzID0+ewogICAgLy8gICAgIHRoaXMuZm9ybUxpc3QgPSByZXMucm93czsKICAgIC8vICAgICB0aGlzLmZvcm1EZXBsb3lPcGVuID0gdHJ1ZTsKICAgIC8vICAgICB0aGlzLmZvcm1EZXBsb3lUaXRsZSA9ICLmjILovb3ooajljZUiOwogICAgLy8gICB9KQogICAgLy8gfSwKICAgIC8qKiDmjILovb3ooajljZUgKi8KICAgIHN1Ym1pdEZvcm1EZXBsb3kocm93KXsKICAgICAgdGhpcy5mb3JtRGVwbG95UGFyYW0uZm9ybUlkID0gcm93LmZvcm1JZDsKICAgICAgYWRkRGVwbG95Rm9ybSh0aGlzLmZvcm1EZXBsb3lQYXJhbSkudGhlbihyZXMgPT57CiAgICAgICAgdGhpcy5tc2dTdWNjZXNzKHJlcy5tc2cpOwogICAgICAgIHRoaXMuZm9ybURlcGxveU9wZW4gPSBmYWxzZTsKICAgICAgICB0aGlzLmdldExpc3QoKTsKICAgICAgfSkKICAgIH0sCiAgICBoYW5kbGVDdXJyZW50Q2hhbmdlKGRhdGEpIHsKICAgICAgaWYgKGRhdGEpIHsKICAgICAgICB0aGlzLmN1cnJlbnRSb3cgPSBKU09OLnBhcnNlKGRhdGEuZm9ybUNvbnRlbnQpOwogICAgICB9CiAgICB9LAogICAgLyoqIOaMgui1ty/mv4DmtLvmtYHnqIsgKi8KICAgIGhhbmRsZVVwZGF0ZVN1c3BlbnNpb25TdGF0ZShyb3cpewogICAgICBsZXQgc3RhdGUgPSAxOwogICAgICBpZiAocm93LnN1c3BlbnNpb25TdGF0ZSA9PT0gMSkgewogICAgICAgICAgc3RhdGUgPSAyCiAgICAgIH0KICAgICAgY29uc3QgcGFyYW1zID0gewogICAgICAgIGRlcGxveUlkOiByb3cuZGVwbG95bWVudElkLAogICAgICAgIHN0YXRlOiBzdGF0ZQogICAgICB9CiAgICAgIHVwZGF0ZVN0YXRlKHBhcmFtcykudGhlbihyZXMgPT4gewogICAgICAgIHRoaXMubXNnU3VjY2VzcyhyZXMubXNnKTsKICAgICAgICB0aGlzLmdldExpc3QoKTsKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOS/ruaUueaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlVXBkYXRlKHJvdykgewogICAgICB0aGlzLnJlc2V0KCk7CiAgICAgIGNvbnN0IGlkID0gcm93LmlkIHx8IHRoaXMuaWRzCiAgICAgIGdldERlcGxveW1lbnQoaWQpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMuZm9ybSA9IHJlc3BvbnNlLmRhdGE7CiAgICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsKICAgICAgICB0aGlzLnRpdGxlID0gIuS/ruaUuea1geeoi+WumuS5iSI7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDmj5DkuqTmjInpkq4gKi8KICAgIHN1Ym1pdEZvcm0oKSB7CiAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICBpZiAodGhpcy5mb3JtLmlkICE9IG51bGwpIHsKICAgICAgICAgICAgdXBkYXRlRGVwbG95bWVudCh0aGlzLmZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgICAgICAgIHRoaXMubXNnU3VjY2Vzcygi5L+u5pS55oiQ5YqfIik7CiAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7CiAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICAgICAgICAgIH0pOwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgYWRkRGVwbG95bWVudCh0aGlzLmZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgICAgICAgIHRoaXMubXNnU3VjY2Vzcygi5paw5aKe5oiQ5YqfIik7CiAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7CiAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICAgICAgICAgIH0pOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLyoqIOWIoOmZpOaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlRGVsZXRlKHJvdykgewogICAgICAvLyBjb25zdCBpZHMgPSByb3cuZGVwbG95bWVudElkIHx8IHRoaXMuaWRzOwogICAgICBjb25zdCBwYXJhbXMgPSB7CiAgICAgICAgZGVwbG95SWQ6IHJvdy5kZXBsb3ltZW50SWQKICAgICAgfQogICAgICB0aGlzLiRjb25maXJtKCfmmK/lkKbnoa7orqTliKDpmaTmtYHnqIvlrprkuYnnvJblj7fkuLoiJyArIHBhcmFtcy5kZXBsb3lJZCArICci55qE5pWw5o2u6aG5PycsICLorablkYoiLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLAogICAgICAgIHR5cGU6ICJ3YXJuaW5nIgogICAgICB9KS50aGVuKGZ1bmN0aW9uKCkgewogICAgICAgIHJldHVybiBkZWxEZXBsb3ltZW50KHBhcmFtcyk7CiAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICAgIHRoaXMubXNnU3VjY2Vzcygi5Yig6Zmk5oiQ5YqfIik7CiAgICAgIH0pCiAgICB9LAogICAgLyoqIOWvvOWHuuaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlRXhwb3J0KCkgewogICAgICBjb25zdCBxdWVyeVBhcmFtcyA9IHRoaXMucXVlcnlQYXJhbXM7CiAgICAgIHRoaXMuJGNvbmZpcm0oJ+aYr+WQpuehruiupOWvvOWHuuaJgOaciea1geeoi+WumuS5ieaVsOaNrumhuT8nLCAi6K2m5ZGKIiwgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAi56Gu5a6aIiwKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAi5Y+W5raIIiwKICAgICAgICB0eXBlOiAid2FybmluZyIKICAgICAgfSkudGhlbihmdW5jdGlvbigpIHsKICAgICAgICByZXR1cm4gZXhwb3J0RGVwbG95bWVudChxdWVyeVBhcmFtcyk7CiAgICAgIH0pLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMuZG93bmxvYWQocmVzcG9uc2UubXNnKTsKICAgICAgfSkKICAgIH0sCiAgICAvKiog5a+85YWlYnBtbi54bWzmlofku7YgKi8KICAgIGhhbmRsZUltcG9ydCgpewogICAgICB0aGlzLnVwbG9hZC50aXRsZSA9ICJicG1uMjAueG1s5paH5Lu25a+85YWlIjsKICAgICAgdGhpcy51cGxvYWQub3BlbiA9IHRydWU7CiAgICB9LAogICAgLy8g5paH5Lu25LiK5Lyg5Lit5aSE55CGCiAgICBoYW5kbGVGaWxlVXBsb2FkUHJvZ3Jlc3MoZXZlbnQsIGZpbGUsIGZpbGVMaXN0KSB7CiAgICAgIHRoaXMudXBsb2FkLmlzVXBsb2FkaW5nID0gdHJ1ZTsKICAgIH0sCiAgICAvLyDmlofku7bkuIrkvKDmiJDlip/lpITnkIYKICAgIGhhbmRsZUZpbGVTdWNjZXNzKHJlc3BvbnNlLCBmaWxlLCBmaWxlTGlzdCkgewogICAgICB0aGlzLnVwbG9hZC5vcGVuID0gZmFsc2U7CiAgICAgIHRoaXMudXBsb2FkLmlzVXBsb2FkaW5nID0gZmFsc2U7CiAgICAgIHRoaXMuJHJlZnMudXBsb2FkLmNsZWFyRmlsZXMoKTsKICAgICAgdGhpcy4kbWVzc2FnZShyZXNwb25zZS5tc2cpOwogICAgICB0aGlzLmdldExpc3QoKTsKICAgIH0sCiAgICAvLyDmj5DkuqTkuIrkvKDmlofku7YKICAgIHN1Ym1pdEZpbGVGb3JtKCkgewogICAgICB0aGlzLiRyZWZzLnVwbG9hZC5zdWJtaXQoKTsKICAgIH0KICB9Cn07Cg=="}, null]}