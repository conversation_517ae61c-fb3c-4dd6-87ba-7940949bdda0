{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/project/report/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/project/report/index.vue", "mtime": 1753529759964}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7CiAgbGlzdFJlcG9ydCwKICBnZXRSZXBvcnQsCiAgZGVsUmVwb3J0LAogIGFkZFJlcG9ydCwKICB1cGRhdGVSZXBvcnQsCiAgZXhwb3J0UmVwb3J0LAogIGltcG9ydFRlbXBsYXRlLAogIHByaW50UmVwb3J0LAogIGNoZWNrTmFtZVVuaXF1ZSwKICBnZXRMaWtlTGlzdCwKICBhdXRoUmVwb3J0LAp9IGZyb20gIkAvYXBpL3Byb2plY3QvcmVwb3J0IjsKaW1wb3J0IHsgZ2V0VG9rZW4gfSBmcm9tICJAL3V0aWxzL2F1dGgiOwppbXBvcnQgRmlsZVVwbG9hZCBmcm9tICJAL2NvbXBvbmVudHMvRmlsZVVwbG9hZCI7CmltcG9ydCBmbG93YWJsZSBmcm9tICJAL3ZpZXdzL2Zsb3dhYmxlL3Rhc2svcmVjb3JkL3ZpZXciOwppbXBvcnQgeyBnZXRJbnNJZEJ5Qml6S2V5IH0gZnJvbSAiQC9hcGkvZmxvd2FibGUvdG9kbyI7CmltcG9ydCB7IHJlZ2lvbkRhdGEsIENvZGVUb1RleHQsIFRleHRUb0NvZGUgfSBmcm9tICJlbGVtZW50LWNoaW5hLWFyZWEtZGF0YSI7CmltcG9ydCBwcmludCBmcm9tICJwcmludC1qcyI7CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiUmVwb3J0IiwKICBjb21wb25lbnRzOiB7CiAgICBGaWxlVXBsb2FkLAogICAgcHJpbnQsCiAgICBmbG93YWJsZSwKICB9LAogIGRhdGEoKSB7CiAgICB2YXIgaW5mb1R5cGVWYWx1ZVZhbGkgPSAocnVsZSwgdmFsdWUsIGNhbGxiYWNrKSA9PiB7CiAgICAgIGlmICh0aGlzLmZvcm0uaW5mb1R5cGUuaW5kZXhPZigiMSIpID49IDAgJiYgIXRoaXMuZm9ybS5zY2FuRmlsZSkgewogICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcigi6YKu566x5Zyw5Z2A5b+F5aGrIikpOwogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICBjYWxsYmFjaygpOwogICAgfTsKICAgIHZhciBpbmZvVHlwZVZhbHVlVmFsaTIgPSAocnVsZSwgdmFsdWUsIGNhbGxiYWNrKSA9PiB7CiAgICAgIGlmICh0aGlzLmZvcm0uaW5mb1R5cGUuaW5kZXhPZigiMiIpID49IDAgJiYgIXRoaXMuZm9ybS5zZW5kQWRkcmVzcykgewogICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcigi5pS25Lu25Zyw5Z2A5b+F5aGrIikpOwogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICBjYWxsYmFjaygpOwogICAgfTsKICAgIHZhciBuYW1lVmFsaSA9IChydWxlLCB2YWx1ZSwgY2FsbGJhY2spID0+IHsKICAgICAgaWYgKCF0aGlzLmZvcm0ucHJvamVjdE5hbWUpIHsKICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoIumhueebruWQjeensOW/heWhqyIpKTsKICAgICAgfSBlbHNlIHsKICAgICAgICBpZiAoL1xzKy9nLnRlc3QodGhpcy5mb3JtLnByb2plY3ROYW1lKSkgewogICAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCLpobnnm67lkI3np7DkuI3op4TojIMiKSk7CiAgICAgICAgICByZXR1cm47CiAgICAgICAgfQogICAgICAgIGNoZWNrTmFtZVVuaXF1ZSh7CiAgICAgICAgICBwcm9qZWN0TmFtZTogdGhpcy5mb3JtLnByb2plY3ROYW1lLAogICAgICAgICAgcHJvamVjdElkOiB0aGlzLmZvcm0ucHJvamVjdElkLAogICAgICAgIH0pLnRoZW4oKHJlc3BvbnNlKSA9PiB7CiAgICAgICAgICBpZiAocmVzcG9uc2UuZGF0YSA9PSAwKSB7CiAgICAgICAgICAgIGNhbGxiYWNrKCk7CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoIumhueebruWQjeensOW3suWtmOWcqCIpKTsKICAgICAgICAgIH0KICAgICAgICB9KTsKICAgICAgfQogICAgfTsKICAgIHZhciBjb2RlVmFsaSA9IChydWxlLCB2YWx1ZSwgY2FsbGJhY2spID0+IHsKICAgICAgaWYgKCF0aGF0LmZvcm0ucHJvamVjdE5vKSB7CiAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCLpobnnm67nvJblj7flv4XloasiKSk7CiAgICAgIH0gZWxzZSBpZiAoL1xzKy9nLnRlc3QodGhhdC5mb3JtLnByb2plY3RObykpIHsKICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoIumhueebrue8luWPt+S4jeinhOiMgyIpKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgY2FsbGJhY2soKTsKICAgIH07CiAgICB2YXIgYXV0aEZpbGVWYWx1ZVZhbGkgPSAocnVsZSwgdmFsdWUsIGNhbGxiYWNrKSA9PiB7CiAgICAgIGlmICh0aGlzLmZvcm0ub3BlcmF0aW9uVHlwZSA9PSAyICYmICF0aGlzLmZvcm0uYXV0aEZpbGUpIHsKICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoIuaOiOadg+exu+Wei+W/heS8oOaOiOadg+S5piIpKTsKICAgICAgfQogICAgICBjYWxsYmFjaygpOwogICAgfTsKICAgIHZhciBvcGVuRGF0ZVZhbGkgPSAocnVsZSwgdmFsdWUsIGNhbGxiYWNrKSA9PiB7CiAgICAgIGNvbnNvbGUubG9nKDEyMzU2NikKICAgICAgaWYgKCF0aGF0LmZvcm0ub3BlbkRhdGUpIHsKICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoIuW8gOagh+aXpeacn+W/heWhqyIpKTsKICAgICAgICByZXR1cm47CiAgICAgIH0gZWxzZSBpZiAodmFsdWUgPT09ICLml6AiKSB7CiAgICAgICAgY2FsbGJhY2soKTsKICAgICAgICByZXR1cm47CiAgICAgIH0gZWxzZSBpZiAoIS9eXGR7NH0tXGR7Mn0tXGR7Mn0kLy50ZXN0KHZhbHVlKSkgewogICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcigi5byA5qCH5pel5pyf5qC85byP5LiN5ZCI5rOV77yM56S65L6LMjAyNS0wMS0wMSIpKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgY2FsbGJhY2soKTsKICAgIH07CiAgICB2YXIgaGFuZ0RhdGVWYWxpID0gKHJ1bGUsIHZhbHVlLCBjYWxsYmFjaykgPT4gewogICAgICBpZiAoIXRoYXQuZm9ybS5oYW5nRGF0ZSkgewogICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcigi5oyC572R5pel5pyf5b+F5aGrIikpOwogICAgICAgIHJldHVybjsKICAgICAgfSBlbHNlIGlmICh2YWx1ZSA9PT0gIuaXoCIpIHsKICAgICAgICBjYWxsYmFjaygpOwogICAgICAgIHJldHVybjsKICAgICAgfSBlbHNlIGlmICghL15cZHs0fS1cZHsyfS1cZHsyfSQvLnRlc3QodmFsdWUpKSB7CiAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCLmjILnvZHml6XmnJ/moLzlvI/kuI3lkIjms5XvvIznpLrkvosyMDI1LTAxLTAxIikpOwogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICBjYWxsYmFjaygpOwogICAgfTsKICAgIHJldHVybiB7CiAgICAgIGlzTW9iaWxlOiBmYWxzZSwKICAgICAgcGFnZUxheW91dDogInRvdGFsLCBzaXplcywgcHJldiwgcGFnZXIsIG5leHQsIGp1bXBlciIsCiAgICAgIC8vIOmBrue9qeWxggogICAgICBsb2FkaW5nOiB0cnVlLAogICAgICAvLyDpgInkuK3mlbDnu4QKICAgICAgaWRzOiBbXSwKICAgICAgLy8g6Z2e5Y2V5Liq56aB55SoCiAgICAgIHNpbmdsZTogdHJ1ZSwKICAgICAgLy8g6Z2e5aSa5Liq56aB55SoCiAgICAgIG11bHRpcGxlOiB0cnVlLAogICAgICAvLyDmmL7npLrmkJzntKLmnaHku7YKICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwKICAgICAgc2hvd0V4cG9ydDogZmFsc2UsCiAgICAgIHNob3dQcmludDogZmFsc2UsCiAgICAgIC8vIOaAu+adoeaVsAogICAgICB0b3RhbDogMCwKICAgICAgLy8g6aG555uu5oql5aSH6KGo5qC85pWw5o2uCiAgICAgIHJlcG9ydExpc3Q6IFtdLAogICAgICAvLyDlvLnlh7rlsYLmoIfpopgKICAgICAgdGl0bGU6ICIiLAogICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYIKICAgICAgb3BlbjogZmFsc2UsCiAgICAgIC8vIOaTjeS9nOexu+Wei+Wtl+WFuAogICAgICBvcGVyYXRpb25UeXBlT3B0aW9uczogW10sCiAgICAgIC8vIOWuoeaguOeKtuaAgeWtl+WFuAogICAgICBhdWRpdFN0YXR1c09wdGlvbnM6IFtdLAogICAgICAvLyDnvJbovpHnirbmgIHlrZflhbgKICAgICAgZWRpdFN0YXR1c09wdGlvbnM6IFtdLAogICAgICAvLyDmi5vmoIfmlrnlvI/lrZflhbgKICAgICAgYmlkZGluZ1R5cGVPcHRpb25zOiBbXSwKICAgICAgLy8g5oqV5qCH5Lqn5ZOB5Z6L5Y+35a2X5YW4CiAgICAgIG1vZGVsT3B0aW9uczogW10sCiAgICAgIG1vZGVsT3B0aW9uMTogW10sCiAgICAgIC8vIOaJgOmcgOi1hOaWmeWtl+WFuAogICAgICByZXF1aXJlSW5mb09wdGlvbnM6IFtdLAogICAgICByZXF1aXJlSW5mb09wdGlvbjE6IFtdLAogICAgICAvLyDotYTmlpnnsbvlnovlrZflhbgKICAgICAgaW5mb1R5cGVPcHRpb25zOiBbXSwKICAgICAgc3BlY09wdGlvbnM6IFtdLAogICAgICBzcGVjT3B0aW9uMTogW10sCiAgICAgIC8vIOaJgOWxnuecgeS7veWtl+WFuAogICAgICBiZWxvbmdQcm92aW5jZU9wdGlvbnM6IFtdLAogICAgICBiZWxvbmdQcm92aW5jZU9wdGlvbnMxOiBbXSwKICAgICAgLy8g5ZSu5ZCO5bm06ZmQCiAgICAgIGFmdGVyU2FsZVllYXJPcHRpb25zOiBbXSwKICAgICAgYWZ0ZXJTYWxlWWVhck9wdGlvbnMxOiBbXSwKICAgICAgLy8g6aG555uu5a+85YWl5Y+C5pWwCiAgICAgIHVwbG9hZDogewogICAgICAgIC8vIOaYr+WQpuaYvuekuuW8ueWHuuWxgu+8iOeUqOaIt+WvvOWFpe+8iQogICAgICAgIG9wZW46IGZhbHNlLAogICAgICAgIC8vIOW8ueWHuuWxguagh+mimO+8iOeUqOaIt+WvvOWFpe+8iQogICAgICAgIHRpdGxlOiAiIiwKICAgICAgICAvLyDmmK/lkKbnpoHnlKjkuIrkvKAKICAgICAgICBpc1VwbG9hZGluZzogZmFsc2UsCiAgICAgICAgLy8g5piv5ZCm5pu05paw5bey57uP5a2Y5Zyo55qE55So5oi35pWw5o2uCiAgICAgICAgdXBkYXRlU3VwcG9ydDogMCwKICAgICAgICAvLyDorr7nva7kuIrkvKDnmoTor7fmsYLlpLTpg6gKICAgICAgICBoZWFkZXJzOiB7IEF1dGhvcml6YXRpb246ICJCZWFyZXIgIiArIGdldFRva2VuKCkgfSwKICAgICAgICAvLyDkuIrkvKDnmoTlnLDlnYAKICAgICAgICB1cmw6IHByb2Nlc3MuZW52LlZVRV9BUFBfQkFTRV9BUEkgKyAiL3Byb2plY3QvcmVwb3J0L2ltcG9ydERhdGEiLAogICAgICB9LAogICAgICAvLyDmn6Xor6Llj4LmlbAKICAgICAgcXVlcnlQYXJhbXM6IHsKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICBwcm9qZWN0Tm86IG51bGwsCiAgICAgICAgcHJvamVjdE5hbWU6IG51bGwsCiAgICAgICAgb3BlcmF0aW9uVHlwZTogbnVsbCwKICAgICAgICBhdWRpdFN0YXR1czogbnVsbCwKICAgICAgICBwcm92aW5jZTogbnVsbCwKICAgICAgICB1c2VyVHlwZTogbnVsbCwKICAgICAgICBiZWxvbmdVc2VyOiBudWxsLAogICAgICAgIHVwZGF0ZVRpbWVBcnI6IFtdLAogICAgICAgIHNwYXJlMTogbnVsbCwKICAgICAgICBhZGRyZXNzOiBudWxsLAogICAgICAgIGJpZGRpbmdDb21wYW55OiBudWxsLAogICAgICAgIGF1dGhDb21wYW55OiBudWxsLAogICAgICAgIGZ1bGxGaWVsZDogbnVsbAogICAgICB9LAogICAgICAvLyDooajljZXlj4LmlbAKICAgICAgZm9ybToge30sCiAgICAgIC8vIOihqOWNleagoemqjAogICAgICAvLyDooajljZXmoKHpqowKICAgICAgcnVsZXM6IHsKICAgICAgICBvcGVyYXRpb25UeXBlOiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuaTjeS9nOexu+Wei+W/hemAiSIgfV0sCiAgICAgICAgcHJvamVjdE5vOiBbeyByZXF1aXJlZDogdHJ1ZSwgdmFsaWRhdGU6IGNvZGVWYWxpLCB0cmlnZ2VyOiAiYmx1ciIgfV0sCiAgICAgICAgcHJvamVjdE5hbWU6IFt7IHJlcXVpcmVkOiB0cnVlLCB2YWxpZGF0ZTogbmFtZVZhbGksIHRyaWdnZXI6ICJibHVyIiB9XSwKICAgICAgICBhZGRyZXNzOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+m57uG5Zyw5Z2A5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0sCiAgICAgICAgXSwKICAgICAgICBiaWRkaW5nQ29tcGFueTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuaLm+agh+WNleS9jeS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9LAogICAgICAgIF0sCiAgICAgICAgb3BlbkRhdGU6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIHZhbGlkYXRlOiBvcGVuRGF0ZVZhbGksIHRyaWdnZXI6ICJibHVyIiB9LAogICAgICAgIF0sCiAgICAgICAgYWZ0ZXJTYWxlWWVhcjogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuWUruWQjuW5tOmZkOS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9LAogICAgICAgIF0sCiAgICAgICAgaGFuZ0RhdGU6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIHZhbGlkYXRlOiBoYW5nRGF0ZVZhbGksIHRyaWdnZXI6ICJibHVyIiB9LAogICAgICAgIF0sCiAgICAgICAgYmVsb25nUHJvdmluY2U6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLllK7lkI7lubTpmZDkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwKICAgICAgICBdLAogICAgICAgIGRpc3RyaWJ1dG9yOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5omA5bGe57uP6ZSA5ZWG5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0sCiAgICAgICAgXSwKICAgICAgICBzY2FuRmlsZTogW3sgdmFsaWRhdG9yOiBpbmZvVHlwZVZhbHVlVmFsaSwgdHJpZ2dlcjogImJsdXIiIH1dLAogICAgICAgIHNlbmRBZGRyZXNzOiBbeyB2YWxpZGF0b3I6IGluZm9UeXBlVmFsdWVWYWxpMiwgdHJpZ2dlcjogImJsdXIiIH1dLAogICAgICAgIG1vZGVsOiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuaKleagh+S6p+WTgeWei+WPt+W/hemAiSIgfV0sCiAgICAgICAgc3BlYzogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLmipXmoIfkuqflk4Hop4TmoLzlv4XpgIkiIH1dLAogICAgICAgIHByb3ZpbmNlOiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIumhueebruaJgOWcqOWcsOW/hemAiSIgfV0sCiAgICAgICAgaW5mb1R5cGU6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLotYTmlpnmjqXmlLbmlrnlvI/lv4XpgIkiLCB0cmlnZ2VyOiAiY2hhbmdlIiB9LAogICAgICAgIF0sCiAgICAgICAgYXV0aEZpbGU6IFt7IHZhbGlkYXRvcjogYXV0aEZpbGVWYWx1ZVZhbGksIHRyaWdnZXI6ICJjaGFuZ2UiIH1dLAogICAgICAgIGJpZGRpbmdDb250YWN0OiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5oub5qCH5Y2V5L2N6IGU57O75Lq6L+iBlOezu+eUteivneW/heWhqyIgfSwKICAgICAgICBdLAogICAgICAgIGF1dGhDb250YWN0OiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5o6I5p2D5YWs5Y+46IGU57O75Lq6L+iBlOezu+eUteivneW/heWhqyIgfSwKICAgICAgICBdLAogICAgICB9LAogICAgICAvLyDliJfkv6Hmga8KICAgICAgY29sdW1uczogWwogICAgICAgIHsga2V5OiAiYmVsb25nVXNlciIsIGluZGV4OiAxLCBsYWJlbDogYOaJgOWxnueUqOaIt2AsIHZpc2libGU6IHRydWUgfSwKICAgICAgICB7IGtleTogInByb2plY3RObyIsIGluZGV4OiAyLCBsYWJlbDogYOmhueebrue8luWPt2AsIHZpc2libGU6IHRydWUgfSwKICAgICAgICB7IGtleTogInByb2plY3ROYW1lIiwgaW5kZXg6IDMsIGxhYmVsOiBg6aG555uu5ZCN56ewYCwgdmlzaWJsZTogdHJ1ZSB9LAogICAgICAgIHsga2V5OiAib3BlcmF0aW9uVHlwZSIsIGluZGV4OiA0LCBsYWJlbDogYOaTjeS9nOexu+Wei2AsIHZpc2libGU6IHRydWUgfSwKICAgICAgICB7IGtleTogInByb3ZpbmNlIiwgaW5kZXg6IDUsIGxhYmVsOiBg6aG555uu5omA5Zyo5ZywYCwgdmlzaWJsZTogdHJ1ZSB9LAogICAgICAgIHsga2V5OiAiYWRkcmVzcyIsIGluZGV4OiA2LCBsYWJlbDogYOivpue7huWcsOWdgGAsIHZpc2libGU6IHRydWUgfSwKICAgICAgICB7IGtleTogImF1dGhDb21wYW55IiwgaW5kZXg6IDcsIGxhYmVsOiBg6KKr5o6I5p2D5YWs5Y+4YCwgdmlzaWJsZTogdHJ1ZSB9LAogICAgICAgIHsga2V5OiAiZGlzdHJpYnV0b3IiLCBpbmRleDogOCwgbGFiZWw6IGDmiYDlsZ7nu4/plIDllYZgLCB2aXNpYmxlOiB0cnVlIH0sCiAgICAgICAgeyBrZXk6ICJiaWRkaW5nQ29tcGFueSIsIGluZGV4OiA5LCBsYWJlbDogYOaLm+agh+WNleS9jWAsIHZpc2libGU6IHRydWUgfSwKICAgICAgICB7IGtleTogIm1vZGVsIiwgaW5kZXg6IDEwLCBsYWJlbDogYOaKleagh+S6p+WTgeWei+WPt2AsIHZpc2libGU6IHRydWUgfSwKICAgICAgICB7IGtleTogInNwZWMiLCBpbmRleDogMTEsIGxhYmVsOiBg5oqV5qCH5Lqn5ZOB6KeE5qC8YCwgdmlzaWJsZTogdHJ1ZSB9LAogICAgICAgIHsga2V5OiAiYXJlYSIsIGluZGV4OiAxMiwgbGFiZWw6IGDlronoo4XpnaLnp69gLCB2aXNpYmxlOiB0cnVlIH0sCiAgICAgICAgeyBrZXk6ICJyZXF1aXJlSW5mbyIsIGluZGV4OiAxMywgbGFiZWw6IGDmiYDpnIDotYTmlplgLCB2aXNpYmxlOiB0cnVlIH0sCiAgICAgICAgeyBrZXk6ICJpbmZvVHlwZSIsIGluZGV4OiAxNCwgbGFiZWw6IGDotYTmlpnnsbvlnotgLCB2aXNpYmxlOiB0cnVlIH0sCiAgICAgICAgeyBrZXk6ICJzY2FuRmlsZSIsIGluZGV4OiAxNSwgbGFiZWw6IGDotYTmlpnmjqXmlLbpgq7ku7ZgLCB2aXNpYmxlOiB0cnVlIH0sCiAgICAgICAgeyBrZXk6ICJzY2FuRmlsZSIsIGluZGV4OiAxNiwgbGFiZWw6IGDotYTmlpnmjqXmlLblnLDlnYBgLCB2aXNpYmxlOiB0cnVlIH0sCiAgICAgICAgeyBrZXk6ICJiZWxvbmdQcm92aW5jZSIsIGluZGV4OiAxNywgbGFiZWw6IGDpobnnm67miYDlsZ7nnIHku71gLCB2aXNpYmxlOiB0cnVlIH0sCiAgICAgICAgeyBrZXk6ICJhZnRlclNhbGVZZWFyIiwgaW5kZXg6IDE4LCBsYWJlbDogYOWUruWQjuW5tOmZkGAsIHZpc2libGU6IHRydWUgfSwKICAgICAgICB7IGtleTogIm9wZW5EYXRlIiwgaW5kZXg6IDE5LCBsYWJlbDogYOW8gOagh+aXpeacn2AsIHZpc2libGU6IHRydWUgfSwKICAgICAgICB7IGtleTogImhhbmdEYXRlIiwgaW5kZXg6IDIwLCBsYWJlbDogYOaMgue9keaXpeacn2AsIHZpc2libGU6IHRydWUgfSwKICAgICAgICB7IGtleTogImNyZWF0ZVRpbWUiLCBpbmRleDogMjEsIGxhYmVsOiBg5o+Q5Lqk5pe26Ze0YCwgdmlzaWJsZTogdHJ1ZSB9LAogICAgICAgIHsga2V5OiAidXBkYXRlVGltZSIsIGluZGV4OiAyMiwgbGFiZWw6IGDkv67mlLnml7bpl7RgLCB2aXNpYmxlOiB0cnVlIH0sCiAgICAgICAgLy8geyBrZXk6ICJhdWRpdFN0YXR1cyIsIGluZGV4OiAxOSwgbGFiZWw6IGDlrqHmoLjnirbmgIFgLCB2aXNpYmxlOiBmYWxzZSB9LAogICAgICAgIC8vIHsga2V5OiAiZWRpdFN0YXR1cyIsIGluZGV4OiAyMCwgbGFiZWw6IGDnvJbovpHnirbmgIFgLCB2aXNpYmxlOiBmYWxzZSB9LAogICAgICAgIC8vIHsga2V5OiAiMTEiLCBpbmRleDogMjEsIGxhYmVsOiBg5o6I5p2D5LmmYCwgdmlzaWJsZTogZmFsc2UgfSwKICAgICAgICAvL3sga2V5OiAiMTIiLCBpbmRleDogMjMsIGxhYmVsOiBg5ZSu5ZCO5pyN5Yqh5om/6K+65Ye9YCwgdmlzaWJsZTogZmFsc2UgfSwKICAgICAgXSwKICAgICAgb3B0aW9uczogcmVnaW9uRGF0YSwKICAgICAgc2VsZWN0ZWRPcHRpb25zOiBbXSwKICAgICAgcXVlcnlBcmVhOiBbXSwKICAgICAgdmlld09wZW46IGZhbHNlLAogICAgICB2aWV3OiB7fSwKICAgICAgaW5mb0xpc3QxOiBbXSwKICAgICAgaW5mb0xpc3QyOiBbXSwKICAgICAgZGVmS2V5OiAicHJvY2Vzc19wcm9qZWN0X3JlcG9ydCIsCiAgICAgIHByb2NJbnNJZDogdW5kZWZpbmVkLAogICAgICB0YXNrSWQ6IHVuZGVmaW5lZCwKICAgICAgZmluaXNoZWQ6IHRydWUsCiAgICAgIGJpektleTogdW5kZWZpbmVkLAogICAgICBhdWRpdFN0YXR1c1RyZWU6IFtdLAogICAgICBvcGVyYXRpb25UeXBlVHJlZTogW10sCiAgICAgIHVzZXJUeXBlVHJlZTogW10sCiAgICAgIGRlZmF1bHRQcm9wczogewogICAgICAgIGNoaWxkcmVuOiAiY2hpbGRyZW4iLAogICAgICAgIGxhYmVsOiAibGFiZWwiLAogICAgICB9LAogICAgICBvbGRPcGVyYXRpb25UeXBlOiB1bmRlZmluZWQsCiAgICAgIHNob3dVVHlwZTogdHJ1ZSwKICAgICAgY2hvb3NlT3B0VHlwZTogdW5kZWZpbmVkLAogICAgICBjaG9vc2VBdWRpdFN0YXR1czogdW5kZWZpbmVkLAogICAgICBjaG9vc2VVc2VySWQ6IHVuZGVmaW5lZCwKICAgICAgY2hvb3NlRWRpdFN0YXR1czogdW5kZWZpbmVkLAogICAgICBjaG9vc2VTcGFyZTI6IHVuZGVmaW5lZCwKICAgICAgbGlrZUxpc3Q6IHVuZGVmaW5lZCwKICAgICAgbGlrZUNvdW50OiB1bmRlZmluZWQsCiAgICAgIGF1dGhDb21wYW55czogW10sCiAgICAgIGlzQWRtaW46IHRydWUsCiAgICAgIGF1ZGl0U3RhdHVzRWRpdDogdHJ1ZSwKICAgICAgaXNBdXRoSW1hZ2VzOiBmYWxzZSwKICAgICAgc2VhcmNoUGlja2VyT3B0aW9uczogewogICAgICAgIHNob3J0Y3V0czogW3sKICAgICAgICAgIHRleHQ6ICfmnIDov5HkuIDlkagnLAogICAgICAgICAgb25DbGljayhwaWNrZXIpIHsKICAgICAgICAgICAgY29uc3QgZW5kID0gbmV3IERhdGUoKTsKICAgICAgICAgICAgY29uc3Qgc3RhcnQgPSBuZXcgRGF0ZSgpOwogICAgICAgICAgICBzdGFydC5zZXRUaW1lKHN0YXJ0LmdldFRpbWUoKSAtIDM2MDAgKiAxMDAwICogMjQgKiA3KTsKICAgICAgICAgICAgcGlja2VyLiRlbWl0KCdwaWNrJywgW3N0YXJ0LCBlbmRdKTsKICAgICAgICAgIH0KICAgICAgICB9LCB7CiAgICAgICAgICB0ZXh0OiAn5pyA6L+R5LiA5Liq5pyIJywKICAgICAgICAgIG9uQ2xpY2socGlja2VyKSB7CiAgICAgICAgICAgIGNvbnN0IGVuZCA9IG5ldyBEYXRlKCk7CiAgICAgICAgICAgIGNvbnN0IHN0YXJ0ID0gbmV3IERhdGUoKTsKICAgICAgICAgICAgc3RhcnQuc2V0VGltZShzdGFydC5nZXRUaW1lKCkgLSAzNjAwICogMTAwMCAqIDI0ICogMzApOwogICAgICAgICAgICBwaWNrZXIuJGVtaXQoJ3BpY2snLCBbc3RhcnQsIGVuZF0pOwogICAgICAgICAgfQogICAgICAgIH0sIHsKICAgICAgICAgIHRleHQ6ICfmnIDov5HkuInkuKrmnIgnLAogICAgICAgICAgb25DbGljayhwaWNrZXIpIHsKICAgICAgICAgICAgY29uc3QgZW5kID0gbmV3IERhdGUoKTsKICAgICAgICAgICAgY29uc3Qgc3RhcnQgPSBuZXcgRGF0ZSgpOwogICAgICAgICAgICBzdGFydC5zZXRUaW1lKHN0YXJ0LmdldFRpbWUoKSAtIDM2MDAgKiAxMDAwICogMjQgKiA5MCk7CiAgICAgICAgICAgIHBpY2tlci4kZW1pdCgncGljaycsIFtzdGFydCwgZW5kXSk7CiAgICAgICAgICB9CiAgICAgICAgfV0KICAgICAgfSwKICAgICAgcGlja2VyT3B0aW9uczogewogICAgICAgIGRpc2FibGVkRGF0ZSh0aW1lKSB7CiAgICAgICAgICByZXR1cm4gdGltZS5nZXRUaW1lKCkgPiBEYXRlLm5vdygpOwogICAgICAgIH0sCiAgICAgICAgc2hvcnRjdXRzOiBbCiAgICAgICAgICB7CiAgICAgICAgICAgIHRleHQ6ICLku4rlpKkiLAogICAgICAgICAgICBvbkNsaWNrKHBpY2tlcikgewogICAgICAgICAgICAgIHBpY2tlci4kZW1pdCgicGljayIsIG5ldyBEYXRlKCkpOwogICAgICAgICAgICB9LAogICAgICAgICAgfSwKICAgICAgICAgIHsKICAgICAgICAgICAgdGV4dDogIuaYqOWkqSIsCiAgICAgICAgICAgIG9uQ2xpY2socGlja2VyKSB7CiAgICAgICAgICAgICAgY29uc3QgZGF0ZSA9IG5ldyBEYXRlKCk7CiAgICAgICAgICAgICAgZGF0ZS5zZXRUaW1lKGRhdGUuZ2V0VGltZSgpIC0gMzYwMCAqIDEwMDAgKiAyNCk7CiAgICAgICAgICAgICAgcGlja2VyLiRlbWl0KCJwaWNrIiwgZGF0ZSk7CiAgICAgICAgICAgIH0sCiAgICAgICAgICB9LAogICAgICAgICAgewogICAgICAgICAgICB0ZXh0OiAi5LiA5ZGo5YmNIiwKICAgICAgICAgICAgb25DbGljayhwaWNrZXIpIHsKICAgICAgICAgICAgICBjb25zdCBkYXRlID0gbmV3IERhdGUoKTsKICAgICAgICAgICAgICBkYXRlLnNldFRpbWUoZGF0ZS5nZXRUaW1lKCkgLSAzNjAwICogMTAwMCAqIDI0ICogNyk7CiAgICAgICAgICAgICAgcGlja2VyLiRlbWl0KCJwaWNrIiwgZGF0ZSk7CiAgICAgICAgICAgIH0sCiAgICAgICAgICB9LAogICAgICAgIF0sCiAgICAgIH0sCiAgICAgIHNlYXJjaEZpZWxkOiAnYWxsJywgLy8g5paw5aKe77ya5b2T5YmN6YCJ5Lit55qE5pCc57Si5a2X5q6177yM6buY6K6k5YWo5a2X5q61CiAgICAgIHNlYXJjaEZpZWxkT3B0aW9uczogWwogICAgICAgIHsgbGFiZWw6ICflhajlrZfmrrUnLCB2YWx1ZTogJ2FsbCcgfSwKICAgICAgICB7IGxhYmVsOiAn6aG555uu57yW5Y+3JywgdmFsdWU6ICdwcm9qZWN0Tm8nIH0sCiAgICAgICAgeyBsYWJlbDogJ+mhueebruWQjeensCcsIHZhbHVlOiAncHJvamVjdE5hbWUnIH0sCiAgICAgICAgeyBsYWJlbDogJ+ivpue7huWcsOWdgCcsIHZhbHVlOiAnYWRkcmVzcycgfSwKICAgICAgICB7IGxhYmVsOiAn5oub5qCH5Y2V5L2NJywgdmFsdWU6ICdiaWRkaW5nQ29tcGFueScgfSwKICAgICAgICB7IGxhYmVsOiAn5o6I5p2D5YWs5Y+4JywgdmFsdWU6ICdhdXRoQ29tcGFueScgfQogICAgICBdLAogICAgICBzZWFyY2hWYWx1ZTogJycsIC8vIOaWsOWinu+8muaQnOe0ouWGheWuuQogICAgICBoaWdobGlnaHRGaWVsZHM6IFsncHJvamVjdE5vJywgJ3Byb2plY3ROYW1lJywgJ2FkZHJlc3MnLCAnYmlkZGluZ0NvbXBhbnknLCAnYXV0aENvbXBhbnknXSwgLy8g5paw5aKe77ya6auY5Lqu5a2X5q61CiAgICB9OwogIH0sCiAgYWN0aXZhdGVkKCkgewogICAgY29uc29sZS5sb2coIj1yZXBvcnQgaW5kZXg9PT4+YWN0aXZhdGVkIik7CiAgICB0aGlzLmdldExpc3QoKTsKICB9LAogIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmdldExpc3QoKTsKICAgIHRoaXMuZ2V0RGljdHMoInByX29wZXJhdGlvbl90eXBlIikudGhlbigocmVzcG9uc2UpID0+IHsKICAgICAgdGhpcy5vcGVyYXRpb25UeXBlT3B0aW9ucyA9IHJlc3BvbnNlLmRhdGE7CiAgICAgIHZhciBvcHQgPSBbXTsKICAgICAgb3B0LnB1c2goeyBpZDogMCwgbGFiZWw6ICLlhajpg6giIH0pOwogICAgICByZXNwb25zZS5kYXRhLmZvckVhY2goKGVsZW0sIGluZGV4KSA9PiB7CiAgICAgICAgdmFyIG9iaiA9IHt9OwogICAgICAgIG9iai5sYWJlbCA9IGVsZW0uZGljdExhYmVsOwogICAgICAgIG9iai5pZCA9IGVsZW0uZGljdFZhbHVlOwogICAgICAgIG9wdC5wdXNoKG9iaik7CiAgICAgIH0pOwogICAgICB2YXIgb3BlcmF0aW9uVHlwZSA9IHt9OwogICAgICBvcGVyYXRpb25UeXBlLmxhYmVsID0gIuaTjeS9nOexu+WeiyI7CiAgICAgIG9wZXJhdGlvblR5cGUuY2hpbGRyZW4gPSBvcHQ7CiAgICAgIHZhciBvcGVyYXRpb25UeXBlcyA9IFtdOwogICAgICBvcGVyYXRpb25UeXBlcy5wdXNoKG9wZXJhdGlvblR5cGUpOwogICAgICB0aGlzLm9wZXJhdGlvblR5cGVUcmVlID0gb3BlcmF0aW9uVHlwZXM7CiAgICB9KTsKICAgIHRoaXMuZ2V0RGljdHMoInByX2F1ZGl0X3N0YXR1cyIpLnRoZW4oKHJlc3BvbnNlKSA9PiB7CiAgICAgIHZhciB0eXBlID0gMDsKICAgICAgaWYgKHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIucm9sZXMpIHsKICAgICAgICBpZiAodGhpcy4kc3RvcmUuc3RhdGUudXNlci5yb2xlcy5pbmNsdWRlcygiY29tbW9uIikpIHsKICAgICAgICAgIHR5cGUgPSAxOwogICAgICAgIH0KICAgICAgICBpZiAodGhpcy4kc3RvcmUuc3RhdGUudXNlci5yb2xlcy5pbmNsdWRlcygicHJvdmluY2VfYWRtaW4iKSkgewogICAgICAgICAgdHlwZSA9IDI7CiAgICAgICAgfQogICAgICAgIGlmICh0aGlzLiRzdG9yZS5zdGF0ZS51c2VyLnJvbGVzLmluY2x1ZGVzKCJyZXBvcnRfYWRtaW4iKSkgewogICAgICAgICAgdHlwZSA9IDM7CiAgICAgICAgfQogICAgICB9CiAgICAgIHRoaXMuYXVkaXRTdGF0dXNPcHRpb25zID0gcmVzcG9uc2UuZGF0YTsKICAgICAgdmFyIG9wdCA9IFtdOwogICAgICBvcHQucHVzaCh7IGlkOiA5LCBsYWJlbDogIuWFqOmDqCIgfSk7CiAgICAgIGlmICh0eXBlID09IDIgfHwgdHlwZSA9PSAzKSB7CiAgICAgICAgb3B0LnB1c2goeyBpZDogMTAsIGxhYmVsOiAi5pyq5a6h5om5IiB9KTsKICAgICAgfQogICAgICByZXNwb25zZS5kYXRhLmZvckVhY2goKGVsZW0sIGluZGV4KSA9PiB7CiAgICAgICAgdmFyIG9iaiA9IHt9OwogICAgICAgIG9iai5sYWJlbCA9IGVsZW0uZGljdExhYmVsOwogICAgICAgIG9iai5pZCA9IGVsZW0uZGljdFZhbHVlOwogICAgICAgIG9wdC5wdXNoKG9iaik7CiAgICAgIH0pOwogICAgICB2YXIgYXVkaXRTdGF0dXNUcmVlID0ge307CiAgICAgIGF1ZGl0U3RhdHVzVHJlZS5sYWJlbCA9ICLlrqHmoLjnirbmgIEiOwogICAgICBhdWRpdFN0YXR1c1RyZWUuY2hpbGRyZW4gPSBvcHQ7CiAgICAgIHZhciBhdWRpdFN0YXR1c1RyZWVzID0gW107CiAgICAgIGF1ZGl0U3RhdHVzVHJlZXMucHVzaChhdWRpdFN0YXR1c1RyZWUpOwogICAgICB0aGlzLmF1ZGl0U3RhdHVzVHJlZSA9IGF1ZGl0U3RhdHVzVHJlZXM7CiAgICB9KTsKICAgIHRoaXMuZ2V0RGljdHMoInByX2VkaXRfc3RhdHVzIikudGhlbigocmVzcG9uc2UpID0+IHsKICAgICAgdGhpcy5lZGl0U3RhdHVzT3B0aW9ucyA9IHJlc3BvbnNlLmRhdGE7CiAgICB9KTsKICAgIC8vIHRoaXMuZ2V0RGljdHMoInByX2JpZGRpbmdfdHlwZSIpLnRoZW4oKHJlc3BvbnNlKSA9PiB7CiAgICAvLyAgIHRoaXMuYmlkZGluZ1R5cGVPcHRpb25zID0gcmVzcG9uc2UuZGF0YTsKICAgIC8vIH0pOwogICAgdGhpcy5nZXREaWN0cygicHJfbW9kZWwiKS50aGVuKChyZXNwb25zZSkgPT4gewogICAgICB0aGlzLm1vZGVsT3B0aW9uMSA9IHJlc3BvbnNlLmRhdGE7CiAgICAgIHZhciBvcHQgPSBbXTsKICAgICAgcmVzcG9uc2UuZGF0YS5mb3JFYWNoKChlbGVtLCBpbmRleCkgPT4gewogICAgICAgIHZhciBvYmogPSB7fTsKICAgICAgICBvYmoubGFiZWwgPSBlbGVtLmRpY3RMYWJlbDsKICAgICAgICBvYmoudmFsdWUgPSBlbGVtLmRpY3RWYWx1ZTsKICAgICAgICBvcHQucHVzaChvYmopOwogICAgICB9KTsKICAgICAgdGhpcy5tb2RlbE9wdGlvbnMgPSBvcHQ7CiAgICB9KTsKICAgIHRoaXMuZ2V0RGljdHMoInByX3NwZWMiKS50aGVuKChyZXNwb25zZSkgPT4gewogICAgICB0aGlzLnNwZWNPcHRpb24xID0gcmVzcG9uc2UuZGF0YTsKICAgICAgdmFyIG9wdCA9IFtdOwogICAgICByZXNwb25zZS5kYXRhLmZvckVhY2goKGVsZW0sIGluZGV4KSA9PiB7CiAgICAgICAgdmFyIG9iaiA9IHt9OwogICAgICAgIG9iai5sYWJlbCA9IGVsZW0uZGljdExhYmVsOwogICAgICAgIG9iai52YWx1ZSA9IGVsZW0uZGljdFZhbHVlOwogICAgICAgIG9wdC5wdXNoKG9iaik7CiAgICAgIH0pOwogICAgICB0aGlzLnNwZWNPcHRpb25zID0gb3B0OwogICAgfSk7CiAgICB0aGlzLmdldERpY3RzKCJwcl9pbmZvIikudGhlbigocmVzcG9uc2UpID0+IHsKICAgICAgdGhpcy5yZXF1aXJlSW5mb09wdGlvbjEgPSByZXNwb25zZS5kYXRhOwogICAgICB2YXIgb3B0ID0gW107CiAgICAgIHJlc3BvbnNlLmRhdGEuZm9yRWFjaCgoZWxlbSwgaW5kZXgpID0+IHsKICAgICAgICB2YXIgb2JqID0ge307CiAgICAgICAgb2JqLmxhYmVsID0gZWxlbS5kaWN0TGFiZWw7CiAgICAgICAgb2JqLnZhbHVlID0gZWxlbS5kaWN0VmFsdWU7CiAgICAgICAgb3B0LnB1c2gob2JqKTsKICAgICAgfSk7CiAgICAgIHRoaXMucmVxdWlyZUluZm9PcHRpb25zID0gb3B0OwogICAgfSk7CiAgICB0aGlzLmdldERpY3RzKCJwcl9wcm92aW5jZSIpLnRoZW4oKHJlc3BvbnNlKSA9PiB7CiAgICAgIHRoaXMuYmVsb25nUHJvdmluY2VPcHRpb25zMSA9IHJlc3BvbnNlLmRhdGE7CiAgICAgIHZhciBvcHQgPSBbXTsKICAgICAgcmVzcG9uc2UuZGF0YS5mb3JFYWNoKChlbGVtLCBpbmRleCkgPT4gewogICAgICAgIHZhciBvYmogPSB7fTsKICAgICAgICBvYmoubGFiZWwgPSBlbGVtLmRpY3RMYWJlbDsKICAgICAgICBvYmoudmFsdWUgPSBlbGVtLmRpY3RWYWx1ZTsKICAgICAgICBvcHQucHVzaChvYmopOwogICAgICB9KTsKICAgICAgdGhpcy5iZWxvbmdQcm92aW5jZU9wdGlvbnMgPSBvcHQ7CiAgICB9KTsKICAgIHRoaXMuZ2V0RGljdHMoInByX2FmdGVyX3NhbGVfeWVhciIpLnRoZW4oKHJlc3BvbnNlKSA9PiB7CiAgICAgIHRoaXMuYWZ0ZXJTYWxlWWVhck9wdGlvbnMxID0gcmVzcG9uc2UuZGF0YTsKICAgICAgdmFyIG9wdCA9IFtdOwogICAgICByZXNwb25zZS5kYXRhLmZvckVhY2goKGVsZW0sIGluZGV4KSA9PiB7CiAgICAgICAgdmFyIG9iaiA9IHt9OwogICAgICAgIG9iai5sYWJlbCA9IGVsZW0uZGljdExhYmVsOwogICAgICAgIG9iai52YWx1ZSA9IGVsZW0uZGljdFZhbHVlOwogICAgICAgIG9wdC5wdXNoKG9iaik7CiAgICAgIH0pOwogICAgICB0aGlzLmFmdGVyU2FsZVllYXJPcHRpb25zID0gb3B0OwogICAgfSk7CiAgICB0aGlzLmdldERpY3RzKCJwcl9kYXRhX3R5cGUiKS50aGVuKChyZXNwb25zZSkgPT4gewogICAgICB0aGlzLmluZm9UeXBlT3B0aW9ucyA9IHJlc3BvbnNlLmRhdGE7CiAgICB9KTsKCiAgICB2YXIgb3B0ID0gW107CiAgICBvcHQucHVzaCh7IGlkOiAwLCBsYWJlbDogIuWFqOmDqCIgfSk7CiAgICBvcHQucHVzaCh7IGlkOiAyLCBsYWJlbDogIuaZrumAmueUqOaItyIgfSk7CiAgICBvcHQucHVzaCh7IGlkOiAxMCwgbGFiZWw6ICLnnIHotJ/otKPkuroiIH0pOwoKICAgIHZhciB1c2VyVHlwZSA9IHt9OwogICAgdXNlclR5cGUubGFiZWwgPSAi5omA5bGe55So5oi3IjsKICAgIHVzZXJUeXBlLmNoaWxkcmVuID0gb3B0OwogICAgdmFyIHVzZXJUeXBlcyA9IFtdOwogICAgdXNlclR5cGVzLnB1c2godXNlclR5cGUpOwogICAgdGhpcy51c2VyVHlwZVRyZWUgPSB1c2VyVHlwZXM7CiAgICBpZiAoCiAgICAgIHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIucm9sZXMgJiYKICAgICAgdGhpcy4kc3RvcmUuc3RhdGUudXNlci5yb2xlcy5pbmNsdWRlcygiY29tbW9uIikKICAgICkgewogICAgICB0aGlzLnNob3dVVHlwZSA9IGZhbHNlOwogICAgfQogICAgaWYgKHRoaXMuX2lzTW9iaWxlKCkpIHsKICAgICAgdGhpcy5pc01vYmlsZSA9IHRydWU7CiAgICAgIHRoaXMucGFnZUxheW91dCA9ICJ0b3RhbCwgcHJldiwgbmV4dCwganVtcGVyIjsKICAgIH0KICAgIGlmICgKICAgICAgdGhpcy4kc3RvcmUuc3RhdGUudXNlci5yb2xlcyAmJgogICAgICB0aGlzLiRzdG9yZS5zdGF0ZS51c2VyLnJvbGVzLmluY2x1ZGVzKCJyZXBvcnRfYWRtaW4iKQogICAgKSB7CiAgICAgIHRoaXMuaXNBZG1pbiA9IGZhbHNlOwogICAgfQogIH0sCiAgbWV0aG9kczogewogICAgX2lzTW9iaWxlKCkgewogICAgICBsZXQgZmxhZyA9IG5hdmlnYXRvci51c2VyQWdlbnQubWF0Y2goCiAgICAgICAgLyhwaG9uZXxwYWR8cG9kfGlQaG9uZXxpUG9kfGlvc3xpUGFkfEFuZHJvaWR8TW9iaWxlfEJsYWNrQmVycnl8SUVNb2JpbGV8TVFRQnJvd3NlcnxKVUN8RmVubmVjfHdPU0Jyb3dzZXJ8QnJvd3Nlck5HfFdlYk9TfFN5bWJpYW58V2luZG93cyBQaG9uZSkvaQogICAgICApOwogICAgICByZXR1cm4gZmxhZzsKICAgIH0sCiAgICAvKiog5p+l6K+i6aG555uu5oql5aSH5YiX6KGoICovCiAgICBnZXRMaXN0KCkgewogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOwogICAgICBsaXN0UmVwb3J0KHRoaXMucXVlcnlQYXJhbXMpLnRoZW4oKHJlc3BvbnNlKSA9PiB7CiAgICAgICAgdGhpcy5yZXBvcnRMaXN0ID0gcmVzcG9uc2Uucm93czsKICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWw7CiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKICAgIGhhbmRsZURvd25sb2FkKHVybCkgewogICAgICBjb25zdCBhID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgiYSIpOyAvLyDliJvlu7rkuIDkuKpIVE1MIOWFg+e0oAogICAgICBhLnNldEF0dHJpYnV0ZSgidGFyZ2V0IiwgIl9ibGFuayIpOwogICAgICBhLnNldEF0dHJpYnV0ZSgiZG93bmxvYWQiLCAiIik7IC8vZG93bmxvYWTlsZ7mgKcKICAgICAgY29uc3QgaHJlZiA9CiAgICAgICAgImh0dHBzOi8vcmVwb3J0LmNsbGVkLmNvbS9wcm9kLWFwaS9jb21tb24vZG93bmxvYWQvcmVzb3VyY2U/cmVzb3VyY2U9IiArCiAgICAgICAgdXJsOwogICAgICBjb25zb2xlLmxvZyhocmVmKTsKICAgICAgYS5zZXRBdHRyaWJ1dGUoImhyZWYiLCBocmVmKTsgLy8gaHJlZumTvuaOpQogICAgICBhLmNsaWNrKCk7IC8vIOiHquaJp+ihjOeCueWHu+S6i+S7tgogICAgfSwKICAgIC8vIOWuoeaguOeKtuaAgeiKgueCueWNleWHu+S6i+S7tgogICAgaGFuZGxlQXVkaXROb2RlQ2xpY2soZGF0YSkgewogICAgICBpZiAoZGF0YS5pZCA9PSA5KSB7CiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5hdWRpdFN0YXR1cyA9IHVuZGVmaW5lZDsKICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLm5vZGUgPSB1bmRlZmluZWQ7CiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5zcGFyZTEgPSB1bmRlZmluZWQ7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgaWYgKGRhdGEuaWQgPT0gMSB8fCBkYXRhLmlkID09IDEwKSB7CiAgICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmF1ZGl0U3RhdHVzID0gMTsKICAgICAgICAgIGlmICh0aGlzLiRzdG9yZS5zdGF0ZS51c2VyLnJvbGVzKSB7CiAgICAgICAgICAgIGlmICh0aGlzLiRzdG9yZS5zdGF0ZS51c2VyLnJvbGVzLmluY2x1ZGVzKCJwcm92aW5jZV9hZG1pbiIpKSB7CiAgICAgICAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5ub2RlID0gIuecgei0n+i0o+S6uiI7CiAgICAgICAgICAgICAgaWYgKGRhdGEuaWQgPT0gMTApIHsKICAgICAgICAgICAgICAgIHRoaXMucXVlcnlQYXJhbXMuc3BhcmUxID0gIj0iOwogICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnNwYXJlMSA9ICIhPSI7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9CiAgICAgICAgICAgIGlmICh0aGlzLiRzdG9yZS5zdGF0ZS51c2VyLnJvbGVzLmluY2x1ZGVzKCJyZXBvcnRfYWRtaW4iKSkgewogICAgICAgICAgICAgIHRoaXMucXVlcnlQYXJhbXMubm9kZSA9ICLlrqHmoLjlkZgiOwogICAgICAgICAgICAgIGlmIChkYXRhLmlkID09IDEwKSB7CiAgICAgICAgICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnNwYXJlMSA9ICI9IjsKICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5zcGFyZTEgPSAiIT0iOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmF1ZGl0U3RhdHVzID0gZGF0YS5pZDsKICAgICAgICAgIHRoaXMucXVlcnlQYXJhbXMubm9kZSA9IHVuZGVmaW5lZDsKICAgICAgICAgIHRoaXMucXVlcnlQYXJhbXMuc3BhcmUxID0gdW5kZWZpbmVkOwogICAgICAgIH0KICAgICAgfQogICAgICB0aGlzLmdldExpc3QoKTsKICAgIH0sCiAgICAvLyDmk43kvZznsbvlnovoioLngrnljZXlh7vkuovku7YKICAgIGhhbmRsZU9wdE5vZGVDbGljayhkYXRhKSB7CiAgICAgIGlmIChkYXRhLmlkID09IDApIHsKICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLm9wZXJhdGlvblR5cGUgPSB1bmRlZmluZWQ7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5vcGVyYXRpb25UeXBlID0gZGF0YS5pZDsKICAgICAgfQogICAgICB0aGlzLmdldExpc3QoKTsKICAgIH0sCiAgICAvLyDnlKjmiLfnsbvlnovoioLngrnljZXlh7vkuovku7YKICAgIGhhbmRsZVVzZXJOb2RlQ2xpY2soZGF0YSkgewogICAgICBpZiAoZGF0YS5pZCA9PSAwKSB7CiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy51c2VyVHlwZSA9IHVuZGVmaW5lZDsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnVzZXJUeXBlID0gZGF0YS5pZDsKICAgICAgfQogICAgICB0aGlzLmdldExpc3QoKTsKICAgIH0sCiAgICAvLyDnrZvpgInoioLngrkKICAgIGZpbHRlck5vZGUodmFsdWUsIGRhdGEpIHsKICAgICAgaWYgKCF2YWx1ZSkgcmV0dXJuIHRydWU7CiAgICAgIHJldHVybiBkYXRhLmxhYmVsLmluZGV4T2YodmFsdWUpICE9PSAtMTsKICAgIH0sCiAgICBzZWFyY2hGb3JtYXQocm93LCBjb2x1bW4pIHsKICAgICAgaWYgKAogICAgICAgIHJvdy5pbmRleE9mKHRoaXMucXVlcnlQYXJhbXMuc3BhcmUxKSAhPT0gLTEgJiYKICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnNwYXJlMSAhPT0gIiIKICAgICAgKSB7CiAgICAgICAgcmV0dXJuIHJvdy5yZXBsYWNlKAogICAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5zcGFyZTEsCiAgICAgICAgICAnPGZvbnQgY29sb3I9IiNmMDAiPicgKyB0aGlzLnF1ZXJ5UGFyYW1zLnNwYXJlMSArICI8L2ZvbnQ+IgogICAgICAgICk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgcmV0dXJuIHJvdzsKICAgICAgfQogICAgfSwKICAgIC8vIOaTjeS9nOexu+Wei+Wtl+WFuOe/u+ivkQogICAgb3BlcmF0aW9uVHlwZUZvcm1hdChyb3csIGNvbHVtbikgewogICAgICByZXR1cm4gdGhpcy5zZWxlY3REaWN0TGFiZWwodGhpcy5vcGVyYXRpb25UeXBlT3B0aW9ucywgcm93Lm9wZXJhdGlvblR5cGUpOwogICAgfSwKICAgIC8vIOWuoeaguOeKtuaAgeWtl+WFuOe/u+ivkQogICAgYXVkaXRTdGF0dXNGb3JtYXQocm93LCBjb2x1bW4pIHsKICAgICAgcmV0dXJuIHRoaXMuc2VsZWN0RGljdExhYmVsKHRoaXMuYXVkaXRTdGF0dXNPcHRpb25zLCByb3cuYXVkaXRTdGF0dXMpOwogICAgfSwKICAgIC8vIOe8lui+keeKtuaAgeWtl+WFuOe/u+ivkQogICAgZWRpdFN0YXR1c0Zvcm1hdChyb3csIGNvbHVtbikgewogICAgICByZXR1cm4gdGhpcy5zZWxlY3REaWN0TGFiZWwodGhpcy5lZGl0U3RhdHVzT3B0aW9ucywgcm93LmVkaXRTdGF0dXMpOwogICAgfSwKICAgIC8vIOaLm+agh+aWueW8j+Wtl+WFuOe/u+ivkQogICAgYmlkZGluZ1R5cGVGb3JtYXQocm93LCBjb2x1bW4pIHsKICAgICAgcmV0dXJuIHRoaXMuc2VsZWN0RGljdExhYmVsKHRoaXMuYmlkZGluZ1R5cGVPcHRpb25zLCByb3cuYmlkZGluZ1R5cGUpOwogICAgfSwKICAgIC8vIOaKleagh+S6p+WTgeWei+WPt+Wtl+WFuOe/u+ivkQogICAgbW9kZWxGb3JtYXQocm93LCBjb2x1bW4pIHsKICAgICAgcmV0dXJuIHRoaXMuc2VsZWN0RGljdExhYmVscyh0aGlzLm1vZGVsT3B0aW9uMSwgcm93Lm1vZGVsKTsKICAgIH0sCiAgICAvLyDmipXmoIfkuqflk4Hop4TmoLzlrZflhbjnv7vor5EKICAgIHNwZWNGb3JtYXQocm93LCBjb2x1bW4pIHsKICAgICAgcmV0dXJuIHRoaXMuc2VsZWN0RGljdExhYmVscyh0aGlzLnNwZWNPcHRpb24xLCByb3cuc3BlYyk7CiAgICB9LAogICAgLy8g5omA6ZyA6LWE5paZ5a2X5YW457+76K+RCiAgICByZXF1aXJlSW5mb0Zvcm1hdChyb3csIGNvbHVtbikgewogICAgICBpZiAocm93LnJlcXVpcmVJbmZvKSB7CiAgICAgICAgcmV0dXJuIHRoaXMuc2VsZWN0RGljdExhYmVscyh0aGlzLnJlcXVpcmVJbmZvT3B0aW9uMSwgcm93LnJlcXVpcmVJbmZvKTsKICAgICAgfQogICAgfSwKICAgIC8vIOi1hOaWmeexu+Wei+Wtl+WFuOe/u+ivkQogICAgaW5mb1R5cGVGb3JtYXQocm93LCBjb2x1bW4pIHsKICAgICAgcmV0dXJuIHRoaXMuc2VsZWN0RGljdExhYmVscyh0aGlzLmluZm9UeXBlT3B0aW9ucywgcm93LmluZm9UeXBlKTsKICAgIH0sCiAgICAvLyDmiYDlsZ7nnIHku73lrZflhbjnv7vor5EKICAgIGJlbG9uZ1Byb3ZpbmNlRm9ybWF0KHJvdywgY29sdW1uKSB7CiAgICAgIHJldHVybiB0aGlzLnNlbGVjdERpY3RMYWJlbHModGhpcy5iZWxvbmdQcm92aW5jZU9wdGlvbnMxLCByb3cuYmVsb25nUHJvdmluY2UpOwogICAgfSwKICAgIC8vIOWUruWQjuW5tOmZkOWtl+WFuOe/u+ivkQogICAgYWZ0ZXJTYWxlWWVhckZvcm1hdChyb3csIGNvbHVtbikgewogICAgICByZXR1cm4gdGhpcy5zZWxlY3REaWN0TGFiZWxzKHRoaXMuYWZ0ZXJTYWxlWWVhck9wdGlvbnMxLCByb3cuYWZ0ZXJTYWxlWWVhcik7CiAgICB9LAogICAgLy8g5Y+W5raI5oyJ6ZKuCiAgICBjYW5jZWwoKSB7CiAgICAgIHRoaXMub3BlbiA9IGZhbHNlOwogICAgICB0aGlzLnJlc2V0KCk7CiAgICB9LAogICAgdmlld09rKCkgewogICAgICB0aGlzLnZpZXdPcGVuID0gZmFsc2U7CiAgICB9LAogICAgLy8g6KGo5Y2V6YeN572uCiAgICByZXNldCgpIHsKICAgICAgdGhpcy5mb3JtID0gewogICAgICAgIHByb2plY3RJZDogbnVsbCwKICAgICAgICBwcm9qZWN0Tm86IG51bGwsCiAgICAgICAgcHJvamVjdE5hbWU6IG51bGwsCiAgICAgICAgb3BlcmF0aW9uVHlwZTogbnVsbCwKICAgICAgICBhdWRpdFN0YXR1czogIjAiLAogICAgICAgIHJlamVjdFJlYXNvbjogbnVsbCwKICAgICAgICBwcm92aW5jZTogbnVsbCwKICAgICAgICBjaXR5OiBudWxsLAogICAgICAgIGRpc3RyaWN0OiBudWxsLAogICAgICAgIGFkZHJlc3M6IG51bGwsCiAgICAgICAgZWRpdFN0YXR1czogIjAiLAogICAgICAgIGJlbG9uZ1VzZXI6IG51bGwsCiAgICAgICAgYmlkZGluZ0NvbXBhbnk6IG51bGwsCiAgICAgICAgb3BlbkRhdGU6IG51bGwsCiAgICAgICAgYmlkZGluZ1R5cGU6IG51bGwsCiAgICAgICAgYnVkZ2V0TW9uZXk6IG51bGwsCiAgICAgICAgYXV0aENvbXBhbnk6IG51bGwsCiAgICAgICAgYmlkZGluZ05ldDogbnVsbCwKICAgICAgICBkaXN0cmlidXRvcjogbnVsbCwKICAgICAgICBtb2RlbDogW10sCiAgICAgICAgc3BlYzogW10sCiAgICAgICAgYXJlYTogbnVsbCwKICAgICAgICBhdXRoRmlsZTogbnVsbCwKICAgICAgICBhZnRlclNhbGVGaWxlOiBudWxsLAogICAgICAgIHJlcXVpcmVJbmZvOiBbXSwKICAgICAgICBpbmZvVHlwZTogW10sCiAgICAgICAgc2NhbkZpbGU6IG51bGwsCiAgICAgICAgc2VuZEFkZHJlc3M6IG51bGwsCiAgICAgICAgbWFpbEluZm86IG51bGwsCiAgICAgICAgZXhwcmVzc0luZm86IG51bGwsCiAgICAgICAgcmVtYXJrOiBudWxsLAogICAgICAgIHNwYXJlMTogbnVsbCwKICAgICAgICBzcGFyZTI6IG51bGwsCiAgICAgIH07CiAgICAgIHRoaXMucmVzZXRGb3JtKCJmb3JtIik7CiAgICB9LAogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlUXVlcnkoKSB7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDE7CiAgICAgIC8vIOa4heepuuaJgOacieebuOWFs+Wtl+autQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnByb2plY3RObyA9IG51bGw7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMucHJvamVjdE5hbWUgPSBudWxsOwogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmFkZHJlc3MgPSBudWxsOwogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmJpZGRpbmdDb21wYW55ID0gbnVsbDsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5hdXRoQ29tcGFueSA9IG51bGw7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMuZnVsbEZpZWxkID0gbnVsbDsKCiAgICAgIGlmICh0aGlzLnNlYXJjaEZpZWxkID09PSAnYWxsJykgewogICAgICAgIHRoaXMucXVlcnlQYXJhbXMuZnVsbEZpZWxkID0gdGhpcy5zZWFyY2hWYWx1ZTsgLy8g5YGH6K6+5ZCO56uvIGZ1bGxGaWVsZCDlgZrlhajlrZfmrrXmqKHns4oKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zW3RoaXMuc2VhcmNoRmllbGRdID0gdGhpcy5zZWFyY2hWYWx1ZTsKICAgICAgfQogICAgICB0aGlzLmdldExpc3QoKTsKICAgIH0sCiAgICAvKiog6YeN572u5oyJ6ZKu5pON5L2cICovCiAgICByZXNldFF1ZXJ5KCkgewogICAgICB0aGlzLnF1ZXJ5QXJlYSA9IFtdOwogICAgICB0aGlzLnF1ZXJ5UGFyYW1zID0gewogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgIHByb2plY3RObzogbnVsbCwKICAgICAgICBwcm9qZWN0TmFtZTogbnVsbCwKICAgICAgICBvcGVyYXRpb25UeXBlOiBudWxsLAogICAgICAgIGF1ZGl0U3RhdHVzOiBudWxsLAogICAgICAgIHByb3ZpbmNlOiBudWxsLAogICAgICAgIHVzZXJUeXBlOiBudWxsLAogICAgICAgIGJlbG9uZ1VzZXI6IG51bGwsCiAgICAgICAgc3BhcmUxOiBudWxsLAogICAgICAgIGFkZHJlc3M6IG51bGwsCiAgICAgICAgYmlkZGluZ0NvbXBhbnk6IG51bGwsCiAgICAgICAgYXV0aENvbXBhbnk6IG51bGwsCiAgICAgICAgZnVsbEZpZWxkOiBudWxsCiAgICAgIH07CiAgICAgIHRoaXMuc2VhcmNoRmllbGQgPSAnYWxsJzsKICAgICAgdGhpcy5zZWFyY2hWYWx1ZSA9ICcnOwogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7CiAgICB9LAogICAgLy8g5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7CiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcCgoaXRlbSkgPT4gaXRlbS5wcm9qZWN0SWQpOwogICAgICB0aGlzLnNpbmdsZSA9IHNlbGVjdGlvbi5sZW5ndGggIT09IDE7CiAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aDsKICAgICAgdGhpcy5jaG9vc2VPcHRUeXBlID0gc2VsZWN0aW9uWzBdLm9wZXJhdGlvblR5cGU7CiAgICAgIHRoaXMuY2hvb3NlQXVkaXRTdGF0dXMgPSBzZWxlY3Rpb25bMF0uYXVkaXRTdGF0dXM7CiAgICAgIHRoaXMuY2hvb3NlVXNlcklkID0gc2VsZWN0aW9uWzBdLnVzZXJJZDsKICAgICAgdGhpcy5jaG9vc2VFZGl0U3RhdHVzID0gc2VsZWN0aW9uWzBdLmVkaXRTdGF0dXM7CiAgICAgIHRoaXMuY2hvb3NlU3BhcmUyID0gc2VsZWN0aW9uWzBdLnNwYXJlMjsKICAgIH0sCiAgICAvKiog5paw5aKe5oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVBZGQoKSB7CiAgICAgIC8vIHRoaXMucmVzZXQoKTsKICAgICAgLy8gdGhpcy5vcGVuID0gdHJ1ZTsKICAgICAgLy8gdGhpcy50aXRsZSA9ICLmt7vliqDpobnnm67miqXlpIciOwogICAgICB0aGlzLiRyb3V0ZXIucHVzaCh7CiAgICAgICAgcGF0aDogIi9wcm9qZWN0L3JlcG9ydC9mb3JtIiwKICAgICAgICBxdWVyeTogewogICAgICAgICAgYnVzaW5lc3NLZXk6IHVuZGVmaW5lZCwKICAgICAgICAgIGZvcm1FZGl0OiB0cnVlLAogICAgICAgIH0sCiAgICAgIH0pOwogICAgfSwKICAgIGhhbmRsZVZpZXcocm93KSB7CiAgICAgIGxldCB0aGF0ID0gdGhpczsKICAgICAgdGhpcy52aWV3ID0gSlNPTi5wYXJzZShKU09OLnN0cmluZ2lmeShyb3cpKTsKICAgICAgdGhpcy52aWV3Lm9wZXJhdGlvblR5cGUgPSB0aGlzLm9wZXJhdGlvblR5cGVGb3JtYXQocm93KTsKICAgICAgdGhpcy52aWV3LmF1ZGl0U3RhdHVzID0gdGhpcy5hdWRpdFN0YXR1c0Zvcm1hdChyb3cpOwogICAgICB0aGlzLnZpZXcuZWRpdFN0YXR1cyA9IHRoaXMuZWRpdFN0YXR1c0Zvcm1hdChyb3cpOwogICAgICB0aGlzLnZpZXcuYmlkZGluZ1R5cGUgPSB0aGlzLmJpZGRpbmdUeXBlRm9ybWF0KHJvdyk7CiAgICAgIHRoaXMudmlldy5tb2RlbCA9IHRoaXMubW9kZWxGb3JtYXQocm93KTsKICAgICAgdGhpcy52aWV3LnNwZWMgPSB0aGlzLnNwZWNGb3JtYXQocm93KTsKICAgICAgdGhpcy52aWV3LnJlcXVpcmVJbmZvID0gdGhpcy5yZXF1aXJlSW5mb0Zvcm1hdChyb3cpOwogICAgICB0aGlzLnZpZXcuaW5mb1R5cGUgPSB0aGlzLmluZm9UeXBlRm9ybWF0KHJvdyk7CiAgICAgIHRoaXMudmlldy5iZWxvbmdQcm92aW5jZSA9IHRoaXMuYmVsb25nUHJvdmluY2VGb3JtYXQocm93KTsKICAgICAgdGhpcy52aWV3LmFmdGVyU2FsZVllYXIgPSB0aGlzLmFmdGVyU2FsZVllYXJGb3JtYXQocm93KTsKICAgICAgaWYgKHJvdy5yZXF1aXJlSW5mb3MpIHsKICAgICAgICB0aGlzLnNlbGVjdERpY3RMYWJlbHModGhpcy5yZXF1aXJlSW5mb09wdGlvbjEsIHJvdy5yZXF1aXJlSW5mbyk7CiAgICAgICAgLy90aGlzLnZpZXcucmVxdWlyZUluZm8gPQogICAgICAgIC8vIGNvbnN0IGluZm9MaXN0ID0gdGhpcy52aWV3LnJlcXVpcmVJbmZvLnNwbGl0KCIsIik7CiAgICAgICAgY29uc3QgaGFsZiA9IE1hdGguY2VpbChyb3cucmVxdWlyZUluZm9zLmxlbmd0aCAvIDIpOwoKICAgICAgICB0aGlzLmluZm9MaXN0MSA9IHJvdy5yZXF1aXJlSW5mb3Muc3BsaWNlKDAsIGhhbGYpOwogICAgICAgIHRoaXMuaW5mb0xpc3QyID0gcm93LnJlcXVpcmVJbmZvcy5zcGxpY2UoLWhhbGYpOwoKICAgICAgICAvLyBjb25zdCB0bXBMaXN0MSA9IGluZm9MaXN0LnNwbGljZSgwLCBoYWxmKTsKICAgICAgICAvLyBjb25zdCB0bXBMaXN0MiA9IGluZm9MaXN0LnNwbGljZSgtaGFsZik7CiAgICAgICAgLy8gdG1wTGlzdDEuZm9yRWFjaCgoZWxlbWVudCkgPT4gewogICAgICAgIC8vICAgY29uc29sZS5sb2coZWxlbWVudCk7CiAgICAgICAgLy8gfSk7CiAgICAgICAgLy8g5b6q546v5a+56LGh6LWL5YC8CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy5pbmZvTGlzdDEgPSBbXTsKICAgICAgICB0aGlzLmluZm9MaXN0MiA9IFtdOwogICAgICB9CgogICAgICBpZiAocm93Lm9wZXJhdGlvblR5cGUgPT0gIjIiICYmIHJvdy5zcGFyZTEgPT0gIjEiKSB7CiAgICAgICAgdGhpcy5kZWZLZXkgPSAicHJvY2Vzc19wcm9qZWN0X2F1dGgiOwogICAgICAgIHRoaXMudGl0bGUgPSAi5p+l55yL6aG555uu5oql5aSH6L2s5o6I5p2DIjsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLmRlZktleSA9ICJwcm9jZXNzX3Byb2plY3RfcmVwb3J0IjsKICAgICAgICB0aGlzLnRpdGxlID0gIuafpeeci+mhueebruaKpeWkhy/mjojmnYMiOwogICAgICB9CiAgICAgIGNvbnN0IHBhcmFtcyA9IHsgYml6S2V5OiByb3cucHJvamVjdElkLCBkZWZLZXk6IHRoaXMuZGVmS2V5IH07CiAgICAgIGdldEluc0lkQnlCaXpLZXkocGFyYW1zKS50aGVuKChyZXNwKSA9PiB7CiAgICAgICAgdGhpcy5iaXpLZXkgPSByb3cucHJvamVjdElkOwogICAgICAgIGlmIChyZXNwLmRhdGEgJiYgcmVzcC5kYXRhLmluc3RhbmNlSWQpIHsKICAgICAgICAgIHRoaXMucHJvY0luc0lkID0gcmVzcC5kYXRhLmluc3RhbmNlSWQ7CiAgICAgICAgICB0aGlzLnRhc2tJZCA9IHJlc3AuZGF0YS50YXNrSWQ7CiAgICAgICAgICAvL2NvbnNvbGUubG9nKCI9PWhhbmRsZVZpZXc9Pj4iKQogICAgICAgICAgLy9jb25zb2xlLmxvZyhyZXNwLmRhdGEpCiAgICAgICAgICBpZiAoCiAgICAgICAgICAgIHJlc3AuZGF0YS5pbnN0YW5jZUlkICYmCiAgICAgICAgICAgICFyZXNwLmRhdGEuZW5kVGltZSAmJgogICAgICAgICAgICByZXNwLmRhdGEuYXNzaWduZWUgPT0gdGhpcy4kc3RvcmUuc3RhdGUudXNlci51c2VySWQKICAgICAgICAgICkgewogICAgICAgICAgICBpZiAoCiAgICAgICAgICAgICAgdGhpcy4kc3RvcmUuc3RhdGUudXNlci5yb2xlcyAmJgogICAgICAgICAgICAgIHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIucm9sZXMuaW5jbHVkZXMoInJlcG9ydF9hZG1pbiIpICYmCiAgICAgICAgICAgICAgcm93Lm9wZXJhdGlvblR5cGUgPT0gIjIiCiAgICAgICAgICAgICkgewogICAgICAgICAgICAgIHRoaXMuaXNBdXRoSW1hZ2VzID0gdHJ1ZTsKICAgICAgICAgICAgfQogICAgICAgICAgICB0aGlzLmZpbmlzaGVkID0gZmFsc2U7CiAgICAgICAgICB9IGVsc2UgaWYgKAogICAgICAgICAgICB0aGlzLiRzdG9yZS5zdGF0ZS51c2VyLnJvbGVzICYmCiAgICAgICAgICAgIHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIucm9sZXMuaW5jbHVkZXMoInJlcG9ydF9hZG1pbiIpICYmCiAgICAgICAgICAgIHJvdy5ub2RlID09ICLlrqHmoLjlkZgiCiAgICAgICAgICApIHsKICAgICAgICAgICAgaWYgKHJvdy5vcGVyYXRpb25UeXBlID09ICIyIikgewogICAgICAgICAgICAgIHRoaXMuaXNBdXRoSW1hZ2VzID0gdHJ1ZTsKICAgICAgICAgICAgfQogICAgICAgICAgICAvL+WuoeaguOWRmOinkuiJsuS4jeaOp+WItuiwgeaTjeS9nAogICAgICAgICAgICB0aGlzLmZpbmlzaGVkID0gZmFsc2U7CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICB0aGlzLmZpbmlzaGVkID0gdHJ1ZTsKICAgICAgICAgIH0KICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy5maW5pc2hlZCA9IHRydWU7CiAgICAgICAgICB0aGlzLnByb2NJbnNJZCA9IHVuZGVmaW5lZDsKICAgICAgICAgIHRoaXMudGFza0lkID0gdW5kZWZpbmVkOwogICAgICAgIH0KCiAgICAgICAgLy8gY29uc29sZS5sb2coIj09PT0+Pj7pqbPlm54iKQogICAgICAgIC8vIC8v6amz5Zue55So5oi3CiAgICAgICAgLy8gaWYocm93LmF1ZGl0U3RhdHVzID09ICczJyAmJiByb3cudXNlcklkID09IHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIudXNlcklkKXsKICAgICAgICAvLyAgIHRoaXMuZmluaXNoZWQgPSBmYWxzZTsKICAgICAgICAvLyB9CiAgICAgICAgLy8gY29uc29sZS5sb2coIj09PT0+Pj7pqbPlm57vvJoiICsgdGhpcy5maW5pc2hlZCkKCiAgICAgICAgdGhpcy52aWV3T3BlbiA9IHRydWU7CiAgICAgIH0pOwogICAgICBnZXRMaWtlTGlzdCh7CiAgICAgICAgcHJvamVjdE5hbWU6IHJvdy5wcm9qZWN0TmFtZSwKICAgICAgICBwcm9qZWN0SWQ6IHJvdy5wcm9qZWN0SWQsCiAgICAgIH0pLnRoZW4oKHJlc3ApID0+IHsKICAgICAgICAvL2NvbnNvbGUubG9nKHJlc3ApCiAgICAgICAgaWYgKHJlc3AuZGF0YSAmJiByZXNwLmRhdGEubGVuZ3RoID4gMCkgewogICAgICAgICAgdGhpcy5saWtlTGlzdCA9IHJlc3AuZGF0YTsKICAgICAgICAgIHRoYXQubGlrZWNvdW50ID0gcmVzcC5kYXRhLmxlbmd0aDsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy5saWtlTGlzdCA9IHVuZGVmaW5lZDsKICAgICAgICAgIHRoYXQubGlrZWNvdW50ID0gdW5kZWZpbmVkOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLyoqIOaOiOadg+aMiemSruaTjeS9nCAqLwogICAgaGFuZGxlQXV0aChyb3cpIHsKICAgICAgY29uc3QgbG9hZGluZyA9IHRoaXMuJGxvYWRpbmcoewogICAgICAgIGxvY2s6IHRydWUsCiAgICAgICAgdGV4dDogIuaOiOadg+S4rS4uLiIsCiAgICAgICAgc3Bpbm5lcjogImVsLWljb24tbG9hZGluZyIsCiAgICAgICAgYmFja2dyb3VuZDogInJnYmEoMCwgMCwgMCwgMC43KSIsCiAgICAgIH0pOwogICAgICBhdXRoUmVwb3J0KHJvdykKICAgICAgICAudGhlbigocmVzcCkgPT4gewogICAgICAgICAgbG9hZGluZy5jbG9zZSgpOwogICAgICAgICAgdGhpcy5tc2dTdWNjZXNzKHJlc3AubXNnKTsKICAgICAgICAgIHRoaXMuJHJvdXRlci5nbygwKTsKICAgICAgICB9KQogICAgICAgIC5jYXRjaCgoZSkgPT4gewogICAgICAgICAgbG9hZGluZy5jbG9zZSgpOwogICAgICAgIH0pOwogICAgfSwKICAgIC8qKiDkv67mlLnmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZVVwZGF0ZSgpIHsKICAgICAgbGV0IHRoYXQgPSB0aGlzOwogICAgICAvLyB0aGF0LmF1ZGl0U3RhdHVzRWRpdCA9IHRydWU7CiAgICAgIC8vIGlmKCF0aGF0LmlzQWRtaW4gJiYgdGhhdC5jaG9vc2VBdWRpdFN0YXR1cyA9PSAzKXsKICAgICAgLy8gICB0aGF0LmF1ZGl0U3RhdHVzRWRpdCA9IGZhbHNlOwogICAgICAvLyB9ZWxzZXt9CiAgICAgIC8v55Sz6K+36ICFCiAgICAgIHZhciBpc0FwcGx5ID0KICAgICAgICB0aGlzLiRzdG9yZS5zdGF0ZS51c2VyLnJvbGVzICYmCiAgICAgICAgKHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIucm9sZXMuaW5jbHVkZXMoImNvbW1vbiIpIHx8CiAgICAgICAgICB0aGlzLiRzdG9yZS5zdGF0ZS51c2VyLnJvbGVzLmluY2x1ZGVzKCJwcm92aW5jZV9hZG1pbiIpKTsKCiAgICAgIGlmIChpc0FwcGx5ICYmIHRoaXMuY2hvb3NlVXNlcklkICE9IHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIudXNlcklkKSB7CiAgICAgICAgdGhpcy5tc2dFcnJvcigi5Y+q6IO95L+u5pS55pys5Lq65o+Q5Lqk55qE6aG555uuIik7CiAgICAgICAgcmV0dXJuOwogICAgICB9CgogICAgICBpZiAodGhpcy5jaG9vc2VPcHRUeXBlID09IDIpIHsKICAgICAgICBpZiAoaXNBcHBseSAmJiB0aGlzLmNob29zZVNwYXJlMiAhPSAxKSB7CiAgICAgICAgICB0aGlzLm1zZ0Vycm9yKCLmjojmnYPooqvpgIDlm57miY3og73kv67mlLkiKTsKICAgICAgICAgIHJldHVybjsKICAgICAgICB9CiAgICAgIH0KICAgICAgaWYgKHRoaXMuY2hvb3NlQXVkaXRTdGF0dXMgPT0gMyAmJiBpc0FwcGx5KSB7CiAgICAgICAgaWYgKHRoaXMuY2hvb3NlRWRpdFN0YXR1cyA9PSAiMCIpIHsKICAgICAgICAgIHRoaXMubXNnRXJyb3IoIuWuoeaJueiiq+mps+WbnuaXoOazleS/ruaUuSIpOwogICAgICAgICAgcmV0dXJuOwogICAgICAgIH0KICAgICAgfQogICAgICAvLyBpZih0aGlzLmNob29zZU9wdFR5cGUgPT0gMSl7CiAgICAgIC8vICAgaWYoaXNBcHBseSAmJiB0aGlzLmNob29zZVVzZXJJZCAhPSB0aGlzLiRzdG9yZS5zdGF0ZS51c2VyLnVzZXJJZCApewogICAgICAvLyAgICAgdGhpcy5tc2dFcnJvcigi5Y+q6IO95L+u5pS55pys5Lq65o+Q5Lqk55qE5oql5aSH6aG555uuIik7CiAgICAgIC8vICAgICByZXR1cm47CiAgICAgIC8vICAgfQogICAgICAvLyB9CiAgICAgIHRoaXMucmVzZXQoKTsKICAgICAgY29uc3QgcHJvamVjdElkID0gdGhpcy5pZHM7CiAgICAgIGdldFJlcG9ydChwcm9qZWN0SWQpLnRoZW4oKHJlc3BvbnNlKSA9PiB7CiAgICAgICAgdGhpcy5mb3JtID0gcmVzcG9uc2UuZGF0YTsKICAgICAgICBpZiAodGhpcy5mb3JtLm1vZGVsKSB0aGlzLmZvcm0ubW9kZWwgPSB0aGlzLmZvcm0ubW9kZWwuc3BsaXQoIiwiKTsKICAgICAgICBlbHNlIHRoaXMuZm9ybS5tb2RlbCA9IFtdOwogICAgICAgIGlmICh0aGlzLmZvcm0ucmVxdWlyZUluZm8pCiAgICAgICAgICB0aGlzLmZvcm0ucmVxdWlyZUluZm8gPSB0aGlzLmZvcm0ucmVxdWlyZUluZm8uc3BsaXQoIiwiKTsKICAgICAgICBlbHNlIHRoaXMuZm9ybS5yZXF1aXJlSW5mbyA9IFtdOwogICAgICAgIGlmICh0aGlzLmZvcm0uaW5mb1R5cGUpCiAgICAgICAgICB0aGlzLmZvcm0uaW5mb1R5cGUgPSB0aGlzLmZvcm0uaW5mb1R5cGUuc3BsaXQoIiwiKTsKICAgICAgICBlbHNlIHRoaXMuZm9ybS5pbmZvVHlwZSA9IFtdOwogICAgICAgIGlmICh0aGlzLmZvcm0uc3BlYykgdGhpcy5mb3JtLnNwZWMgPSB0aGlzLmZvcm0uc3BlYy5zcGxpdCgiLCIpOwogICAgICAgIGVsc2UgdGhpcy5mb3JtLnNwZWMgPSBbXTsKCiAgICAgICAgaWYgKHRoaXMuZm9ybS5hdXRoQ29tcGFueSkgewogICAgICAgICAgdGhhdC5hdXRoQ29tcGFueXMgPSBbXTsKICAgICAgICAgIHZhciBhcnJheSA9IHRoaXMuZm9ybS5hdXRoQ29tcGFueS5zcGxpdCgiLCIpOwogICAgICAgICAgYXJyYXkuZm9yRWFjaChmdW5jdGlvbiAoZSkgewogICAgICAgICAgICB0aGF0LmF1dGhDb21wYW55cy5wdXNoKHsKICAgICAgICAgICAgICB2YWx1ZTogZSwKICAgICAgICAgICAgICBrZXk6IERhdGUubm93KCksCiAgICAgICAgICAgIH0pOwogICAgICAgICAgfSk7CiAgICAgICAgICB0aGF0LmZvcm0uYXV0aENvbXBhbnkgPSB0aGF0LmF1dGhDb21wYW55c1swXS52YWx1ZTsKICAgICAgICAgIHRoYXQuYXV0aENvbXBhbnlzLnNwbGljZSgwLCAxKTsKICAgICAgICAgIC8vY29uc29sZS5sb2codGhhdC5hdXRoQ29tcGFueXMpCiAgICAgICAgfSBlbHNlIHRoaXMuYXV0aENvbXBhbnlzID0gW107CgogICAgICAgIHRoaXMub2xkT3BlcmF0aW9uVHlwZSA9IHJlc3BvbnNlLmRhdGEub3BlcmF0aW9uVHlwZTsKCiAgICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsKICAgICAgICB0aGlzLnRpdGxlID0gIuS/ruaUuemhueebruaKpeWkhyI7CiAgICAgICAgdmFyIHByb3ZpbmNlcyA9IHJlc3BvbnNlLmRhdGEucHJvdmluY2U7CiAgICAgICAgaWYgKHByb3ZpbmNlcy5sZW5ndGggPiAwKSB7CiAgICAgICAgICB2YXIgYWRkcmVzcyA9IHByb3ZpbmNlcy5zcGxpdCgiLyIpOwogICAgICAgICAgdmFyIGNpdHlzID0gW107CiAgICAgICAgICAvLyDnnIHku70KICAgICAgICAgIGlmIChhZGRyZXNzLmxlbmd0aCA+IDApIGNpdHlzLnB1c2goVGV4dFRvQ29kZVthZGRyZXNzWzBdXS5jb2RlKTsKICAgICAgICAgIC8vIOWfjuW4ggogICAgICAgICAgaWYgKGFkZHJlc3MubGVuZ3RoID4gMSkKICAgICAgICAgICAgY2l0eXMucHVzaChUZXh0VG9Db2RlW2FkZHJlc3NbMF1dW2FkZHJlc3NbMV1dLmNvZGUpOwogICAgICAgICAgLy8g5Zyw5Yy6CiAgICAgICAgICBpZiAoYWRkcmVzcy5sZW5ndGggPiAyKQogICAgICAgICAgICBjaXR5cy5wdXNoKFRleHRUb0NvZGVbYWRkcmVzc1swXV1bYWRkcmVzc1sxXV1bYWRkcmVzc1syXV0uY29kZSk7CgogICAgICAgICAgdGhpcy5zZWxlY3RlZE9wdGlvbnMgPSBjaXR5czsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDmj5DkuqTmjInpkq4gKi8KICAgIHN1Ym1pdEZvcm0oKSB7CiAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZSgodmFsaWQpID0+IHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIGlmICh0aGlzLmZvcm0uaW5mb1R5cGUuaW5kZXhPZigiMSIpID49IDAgJiYgdGhpcy5mb3JtLnNjYW5GaWxlKSB7CiAgICAgICAgICAgIGxldCBlbWFpbFJlZyA9IC9eW2EtekEtWjAtOV8tXStAW2EtekEtWjAtOV8tXSsoXC5bYS16QS1aMC05Xy1dKykrJC87CiAgICAgICAgICAgIGlmICghZW1haWxSZWcudGVzdCh0aGlzLmZvcm0uc2NhbkZpbGUpKSB7CiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi6LWE5paZ5o6l5pS25pa55byP6YKu566x5qC85byP6ZSZ6K+vIik7CiAgICAgICAgICAgICAgcmV0dXJuOwogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgICBpZiAodGhpcy5mb3JtLm9wZXJhdGlvblR5cGUgPT0gMSkgewogICAgICAgICAgICB0aGlzLmZvcm0uYXV0aEZpbGUgPSAiIjsKICAgICAgICAgICAgdGhpcy5mb3JtLmFmdGVyU2FsZUZpbGUgPSAiIjsKICAgICAgICAgIH0KICAgICAgICAgIHZhciBmb3JtU3RyID0gSlNPTi5zdHJpbmdpZnkodGhpcy5mb3JtKTsKICAgICAgICAgIHZhciBmb3JtRGF0YSA9IEpTT04ucGFyc2UoZm9ybVN0cik7CiAgICAgICAgICBpZiAoZm9ybURhdGEubW9kZWwgJiYgZm9ybURhdGEubW9kZWwubGVuZ3RoID4gMCkKICAgICAgICAgICAgZm9ybURhdGEubW9kZWwgPSBmb3JtRGF0YS5tb2RlbC5qb2luKCIsIik7CiAgICAgICAgICBlbHNlIGZvcm1EYXRhLm1vZGVsID0gdW5kZWZpbmVkOwogICAgICAgICAgaWYgKGZvcm1EYXRhLnJlcXVpcmVJbmZvICYmIGZvcm1EYXRhLnJlcXVpcmVJbmZvLmxlbmd0aCA+IDApCiAgICAgICAgICAgIGZvcm1EYXRhLnJlcXVpcmVJbmZvID0gZm9ybURhdGEucmVxdWlyZUluZm8uam9pbigiLCIpOwogICAgICAgICAgZWxzZSBmb3JtRGF0YS5yZXF1aXJlSW5mbyA9IHVuZGVmaW5lZDsKICAgICAgICAgIGlmIChmb3JtRGF0YS5pbmZvVHlwZSAmJiBmb3JtRGF0YS5pbmZvVHlwZS5sZW5ndGggPiAwKQogICAgICAgICAgICBmb3JtRGF0YS5pbmZvVHlwZSA9IGZvcm1EYXRhLmluZm9UeXBlLmpvaW4oIiwiKTsKICAgICAgICAgIGVsc2UgZm9ybURhdGEuaW5mb1R5cGUgPSB1bmRlZmluZWQ7CiAgICAgICAgICBpZiAoZm9ybURhdGEuc3BlYyAmJiBmb3JtRGF0YS5zcGVjLmxlbmd0aCA+IDApCiAgICAgICAgICAgIGZvcm1EYXRhLnNwZWMgPSBmb3JtRGF0YS5zcGVjLmpvaW4oIiwiKTsKICAgICAgICAgIGVsc2UgZm9ybURhdGEuc3BlYyA9IHVuZGVmaW5lZDsKCiAgICAgICAgICAvL+aOiOadg+WFrOWPuAogICAgICAgICAgaWYgKHRoaXMuYXV0aENvbXBhbnlzLmxlbmd0aCA+IDApIHsKICAgICAgICAgICAgdmFyIGFycmF5ID0gbmV3IEFycmF5KCk7CiAgICAgICAgICAgIHRoaXMuYXV0aENvbXBhbnlzLmZvckVhY2goZnVuY3Rpb24gKGUpIHsKICAgICAgICAgICAgICBhcnJheS5wdXNoKGUudmFsdWUpOwogICAgICAgICAgICB9KTsKICAgICAgICAgICAgZm9ybURhdGEuYXV0aENvbXBhbnkgKz0gIiwiICsgYXJyYXkuam9pbigiLCIpOwogICAgICAgICAgfQoKICAgICAgICAgIGlmIChmb3JtRGF0YS5wcm9qZWN0SWQgIT0gbnVsbCkgewogICAgICAgICAgICB1cGRhdGVSZXBvcnQoZm9ybURhdGEpLnRoZW4oKHJlc3BvbnNlKSA9PiB7CiAgICAgICAgICAgICAgdGhpcy5tc2dTdWNjZXNzKCLkv67mlLnmiJDlip8iKTsKCiAgICAgICAgICAgICAgaWYgKHRoaXMub2xkT3BlcmF0aW9uVHlwZSA9PSAxICYmIGZvcm1EYXRhLm9wZXJhdGlvblR5cGUgPT0gMikgewogICAgICAgICAgICAgICAgY29uc29sZS5sb2coIj09PT09Pj4+5oql5aSH5pS55o6I5p2DIik7CiAgICAgICAgICAgICAgfQoKICAgICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsKICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICBhZGRSZXBvcnQoZm9ybURhdGEpLnRoZW4oKHJlc3BvbnNlKSA9PiB7CiAgICAgICAgICAgICAgdGhpcy5tc2dTdWNjZXNzKCLmlrDlop7miJDlip8iKTsKICAgICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsKICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvKiog5Yig6Zmk5oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVEZWxldGUocm93KSB7CiAgICAgIGNvbnN0IHByb2plY3RJZHMgPSByb3cucHJvamVjdElkIHx8IHRoaXMuaWRzOwogICAgICB0aGlzLiRjb25maXJtKAogICAgICAgICfmmK/lkKbnoa7orqTliKDpmaTpobnnm67miqXlpIfnvJblj7fkuLoiJyArIHByb2plY3RJZHMgKyAnIueahOaVsOaNrumhuT8nLAogICAgICAgICLorablkYoiLAogICAgICAgIHsKICAgICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAi56Gu5a6aIiwKICAgICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLAogICAgICAgICAgdHlwZTogIndhcm5pbmciLAogICAgICAgIH0KICAgICAgKQogICAgICAgIC50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICAgIHJldHVybiBkZWxSZXBvcnQocHJvamVjdElkcyk7CiAgICAgICAgfSkKICAgICAgICAudGhlbigoKSA9PiB7CiAgICAgICAgICB0aGlzLmdldExpc3QoKTsKICAgICAgICAgIHRoaXMubXNnU3VjY2Vzcygi5Yig6Zmk5oiQ5YqfIik7CiAgICAgICAgfSk7CiAgICB9LAogICAgY2xpY2tFeHBvcnQoKSB7CiAgICAgIHRoaXMuc2hvd0V4cG9ydCA9IHRydWU7CiAgICB9LAogICAgY2xpY2tQcmludCgpIHsKICAgICAgdGhpcy5zaG93UHJpbnQgPSB0cnVlOwogICAgfSwKICAgIC8qKiDlr7zlh7rmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZUV4cG9ydCh0eXBlKSB7CiAgICAgIGxldCBsb2FkaW5nd2luOwogICAgICBsZXQgdGhhdCA9IHRoaXM7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMuc3BhcmUxID0gdHlwZTsKICAgICAgdmFyIGNvbCA9IFtdOwogICAgICB0aGlzLmNvbHVtbnMuZm9yRWFjaCgoaXRlbSkgPT4gewogICAgICAgIGlmIChpdGVtLnZpc2libGUpIHsKICAgICAgICAgIGNvbC5wdXNoKGl0ZW0ubGFiZWwpOwogICAgICAgIH0KICAgICAgfSk7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMuc3BhcmUyID0gY29sLmpvaW4oIiwiKTsKICAgICAgY29uc3QgcXVlcnlQYXJhbXMgPSB0aGlzLnF1ZXJ5UGFyYW1zOwogICAgICB0aGlzLiRjb25maXJtKAogICAgICAgICLmmK/lkKbnoa7orqTlr7zlh7rpobnnm67miqXlpIfmkJzntKLnu5PmnpwiICsKICAgICAgICAodHlwZSA9PSAwID8gIuacrOmhtSIgOiAi5YWo6YOoIikgKwogICAgICAgICLmlbDmja7pobk/IiwKICAgICAgICAi6K2m5ZGKIiwKICAgICAgICB7CiAgICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogIuehruWumiIsCiAgICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAi5Y+W5raIIiwKICAgICAgICAgIHR5cGU6ICJ3YXJuaW5nIiwKICAgICAgICB9CiAgICAgICkKICAgICAgICAudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgICBsb2FkaW5nd2luID0gdGhhdC4kbG9hZGluZyh7CiAgICAgICAgICAgIGxvY2s6IHRydWUsIC8vbG9ja+eahOS/ruaUueespi0t6buY6K6k5pivZmFsc2UKICAgICAgICAgICAgdGV4dDogIuWvvOWHuuS4rS4uLiIsIC8v5pi+56S65Zyo5Yqg6L295Zu+5qCH5LiL5pa555qE5Yqg6L295paH5qGICiAgICAgICAgICAgIHNwaW5uZXI6ICJlbC1pY29uLWxvYWRpbmciLCAvL+iHquWumuS5ieWKoOi9veWbvuagh+exu+WQjQogICAgICAgICAgICBiYWNrZ3JvdW5kOiAicmdiYSgwLCAwLCAwLCAwLjcpIiwgLy/pga7nvanlsYLpopzoibIKICAgICAgICAgICAgdGFyZ2V0OiBkb2N1bWVudC5xdWVyeVNlbGVjdG9yKCIuYXBwLXdyYXBwZXIiKSwgLy9sb2FkaW7opobnm5bnmoRkb23lhYPntKDoioLngrkKICAgICAgICAgIH0pOwogICAgICAgICAgZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50LnN0eWxlLm92ZXJmbG93WSA9ICJoaWRkZW4iOyAvL+emgeatouW6leWxgmRpdua7muWKqAogICAgICAgICAgcmV0dXJuIGV4cG9ydFJlcG9ydChxdWVyeVBhcmFtcyk7CiAgICAgICAgfSkKICAgICAgICAudGhlbigocmVzcG9uc2UpID0+IHsKICAgICAgICAgIHRoaXMuZG93bmxvYWQocmVzcG9uc2UubXNnKTsKICAgICAgICAgIHRoaXMuc2hvd0V4cG9ydCA9IGZhbHNlOwogICAgICAgICAgbG9hZGluZ3dpbi5jbG9zZSgpOwogICAgICAgICAgZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50LnN0eWxlLm92ZXJmbG93WSA9ICJhdXRvIjsgLy/lhYHorrjlupXlsYJkaXbmu5rliqgKICAgICAgICB9KQogICAgICAgIC5jYXRjaCgoKSA9PiB7CiAgICAgICAgICB0aGlzLnNob3dFeHBvcnQgPSBmYWxzZTsKICAgICAgICAgIGxvYWRpbmd3aW4uY2xvc2UoKTsKICAgICAgICAgIGRvY3VtZW50LmRvY3VtZW50RWxlbWVudC5zdHlsZS5vdmVyZmxvd1kgPSAiYXV0byI7IC8v5YWB6K645bqV5bGCZGl25rua5YqoCiAgICAgICAgfSk7CiAgICB9LAogICAgLyoqIOWvvOWFpeaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlSW1wb3J0KCkgewogICAgICB0aGlzLnVwbG9hZC50aXRsZSA9ICLpobnnm67lr7zlhaUiOwogICAgICB0aGlzLnVwbG9hZC5vcGVuID0gdHJ1ZTsKICAgIH0sCiAgICAvKiog5LiL6L295qih5p2/5pON5L2cICovCiAgICBpbXBvcnRUZW1wbGF0ZSgpIHsKICAgICAgaW1wb3J0VGVtcGxhdGUoKS50aGVuKChyZXNwb25zZSkgPT4gewogICAgICAgIHRoaXMuZG93bmxvYWQocmVzcG9uc2UubXNnKTsKICAgICAgfSk7CiAgICB9LAogICAgZG93bmxvYWRTUVMoKSB7CiAgICAgIHRoaXMuZG93bmxvYWQoIua1t+S9s+mbhuWboi3mjojmnYPkuaYuZG9jeCIsIGZhbHNlKTsKICAgIH0sCiAgICBkb3dubG9hZENSSCgpIHsKICAgICAgdGhpcy5kb3dubG9hZCgi5rW35L2z6ZuG5ZuiLeWUruWQjuacjeWKoeaJv+ivuuWHvS5kb2MiLCBmYWxzZSk7CiAgICB9LAogICAgLy8g5paH5Lu25LiK5Lyg5Lit5aSE55CGCiAgICBoYW5kbGVGaWxlVXBsb2FkUHJvZ3Jlc3MoZXZlbnQsIGZpbGUsIGZpbGVMaXN0KSB7CiAgICAgIHRoaXMudXBsb2FkLmlzVXBsb2FkaW5nID0gdHJ1ZTsKICAgIH0sCiAgICAvLyDmlofku7bkuIrkvKDmiJDlip/lpITnkIYKICAgIGhhbmRsZUZpbGVTdWNjZXNzKHJlc3BvbnNlLCBmaWxlLCBmaWxlTGlzdCkgewogICAgICB0aGlzLnVwbG9hZC5vcGVuID0gZmFsc2U7CiAgICAgIHRoaXMudXBsb2FkLmlzVXBsb2FkaW5nID0gZmFsc2U7CiAgICAgIHRoaXMuJHJlZnMudXBsb2FkLmNsZWFyRmlsZXMoKTsKICAgICAgdGhpcy4kYWxlcnQocmVzcG9uc2UubXNnLCAi5a+85YWl57uT5p6cIiwgeyBkYW5nZXJvdXNseVVzZUhUTUxTdHJpbmc6IHRydWUgfSk7CiAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgfSwKICAgIC8vIOaPkOS6pOS4iuS8oOaWh+S7tgogICAgc3VibWl0RmlsZUZvcm0oKSB7CiAgICAgIHRoaXMuJHJlZnMudXBsb2FkLnN1Ym1pdCgpOwogICAgfSwKICAgIGhhbmRsZUNoYW5nZSh2YWx1ZSkgewogICAgICBpZiAoIXZhbHVlIHx8IHZhbHVlLmxlbmd0aCA9PSAwKSB7CiAgICAgICAgdGhpcy5zZWxlY3RlZE9wdGlvbnMgPSBudWxsOwogICAgICAgIHRoaXMuZm9ybS5wcm92aW5jZSA9IHVuZGVmaW5lZDsKICAgICAgICB0aGlzLmZvcm0uZGlzdHJpY3QgPSB1bmRlZmluZWQ7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIHRoaXMuc2VsZWN0ZWRPcHRpb25zID0gdmFsdWU7CiAgICAgIHZhciB0eHQgPSAiIjsKICAgICAgdmFsdWUuZm9yRWFjaChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgIHR4dCArPSBDb2RlVG9UZXh0W2l0ZW1dICsgIi8iOwogICAgICB9KTsKICAgICAgaWYgKHR4dC5sZW5ndGggPiAxKSB7CiAgICAgICAgdHh0ID0gdHh0LnN1YnN0cmluZygwLCB0eHQubGVuZ3RoIC0gMSk7CiAgICAgICAgdGhpcy5mb3JtLmRpc3RyaWN0ID0gdGhpcy4kc3RvcmUuc3RhdGUudXNlci5wcm92aW5jZTsKICAgICAgICB0aGlzLmZvcm0ucHJvdmluY2UgPSB0eHQ7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy5mb3JtLnByb3ZpbmNlID0gdW5kZWZpbmVkOwogICAgICAgIHRoaXMuZm9ybS5kaXN0cmljdCA9IHVuZGVmaW5lZDsKICAgICAgfQogICAgfSwKICAgIGhhbmRsZVF1ZXJ5Q2l0eUNoYW5nZSh2YWx1ZSkgewogICAgICB0aGlzLnF1ZXJ5QXJlYSA9IHZhbHVlOwogICAgICB2YXIgdHh0ID0gIiI7CiAgICAgIHZhbHVlLmZvckVhY2goZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICB0eHQgKz0gQ29kZVRvVGV4dFtpdGVtXSArICIvIjsKICAgICAgfSk7CiAgICAgIGlmICh0eHQubGVuZ3RoID4gMSkgewogICAgICAgIHR4dCA9IHR4dC5zdWJzdHJpbmcoMCwgdHh0Lmxlbmd0aCAtIDEpOwogICAgICAgIHRoaXMucXVlcnlQYXJhbXMucHJvdmluY2UgPSB0eHQ7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wcm92aW5jZSA9IHVuZGVmaW5lZDsKICAgICAgfQogICAgfSwKICAgIGhhbmRsZVByaW50KHR5cGUpIHsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5zcGFyZTEgPSB0eXBlOwogICAgICB2YXIgcHJvcGVydGllcyA9IFtdOwogICAgICAvL3Byb3BlcnRpZXMucHVzaCh7ZmllbGQ6ICdpbmRleCcsIGRpc3BsYXlOYW1lOiAn5bqP5Y+3J30pOwogICAgICBwcm9wZXJ0aWVzLnB1c2goeyBmaWVsZDogInByb2plY3RJZCIsIGRpc3BsYXlOYW1lOiAi6aG555uuSUQiIH0pOwogICAgICB0aGlzLmNvbHVtbnMuZm9yRWFjaCgoaXRlbSkgPT4gewogICAgICAgIGlmIChpdGVtLnZpc2libGUpIHsKICAgICAgICAgIHByb3BlcnRpZXMucHVzaCh7IGZpZWxkOiBpdGVtLmtleSwgZGlzcGxheU5hbWU6IGl0ZW0ubGFiZWwgfSk7CiAgICAgICAgfQogICAgICB9KTsKICAgICAgcHJpbnRSZXBvcnQodGhpcy5xdWVyeVBhcmFtcykudGhlbigocmVzcG9uc2UpID0+IHsKICAgICAgICBwcmludEpTKHsKICAgICAgICAgIHByaW50YWJsZTogcmVzcG9uc2UuZGF0YSwKICAgICAgICAgIHR5cGU6ICJqc29uIiwKICAgICAgICAgIHByb3BlcnRpZXM6IHByb3BlcnRpZXMsCiAgICAgICAgICBoZWFkZXI6ICc8ZGl2IHN0eWxlPSJ0ZXh0LWFsaWduOiBjZW50ZXIiPjxoMz7pobnnm67miqXlpIfliJfooag8L2gzPjwvZGl2PicsCiAgICAgICAgICB0YXJnZXRTdHlsZXM6IFsiKiJdLAogICAgICAgICAgZ3JpZEhlYWRlclN0eWxlOgogICAgICAgICAgICAibWFyZ2luLXRvcDoyMHB4O2JvcmRlcjogMXB4IHNvbGlkICMwMDA7dGV4dC1hbGlnbjpjZW50ZXIiLAogICAgICAgICAgZ3JpZFN0eWxlOiAiYm9yZGVyOiAxcHggc29saWQgIzAwMDt0ZXh0LWFsaWduOmNlbnRlcjttaW4td2lkdGg6NTBweDsiLAogICAgICAgICAgc3R5bGU6ICJAcGFnZSB7bWFyZ2luOjAgMTBtbTttYXJnaW4tdG9wOjEwbW07fSIsCiAgICAgICAgfSk7CiAgICAgIH0pOwogICAgICAvLyBwcmludEpTKHsKICAgICAgLy8gICBwcmludGFibGU6ICJwcmludEFyZWEiLAogICAgICAvLyAgIHR5cGU6J2h0bWwnLAogICAgICAvLyAgIGhlYWRlcjpudWxsLAogICAgICAvLyAgIHRhcmdldFN0eWxlczpbJyonXSwKICAgICAgLy8gICBzdHlsZToiQHBhZ2Uge21hcmdpbjowIDEwbW19IgogICAgICAvLyB9KQogICAgfSwKICAgIC8vIOWIoOmZpCBzaG93TmFtZUNvcmxvciDlkowgc2hvd05vQ29ybG9yIOaWueazle+8jOaWsOWinumrmOS6ruaWueazlQogICAgaGlnaGxpZ2h0VGV4dCh0ZXh0LCBrZXl3b3JkKSB7CiAgICAgIGlmICgha2V5d29yZCkgcmV0dXJuIHRleHQ7CiAgICAgIC8vIOWFqOmDqOmrmOS6rgogICAgICByZXR1cm4gdGV4dCA/IHRleHQucmVwbGFjZShuZXcgUmVnRXhwKGtleXdvcmQsICdnJyksIGA8Zm9udCBjb2xvcj0iI2YwMCI+JHtrZXl3b3JkfTwvZm9udD5gKSA6IHRleHQ7CiAgICB9LAogICAgaGlnaGxpZ2h0Q2VsbChmaWVsZCwgdGV4dCkgewogICAgICBpZiAodGhpcy5zZWFyY2hGaWVsZCA9PT0gJ2FsbCcgJiYgdGhpcy5zZWFyY2hWYWx1ZSAmJiB0aGlzLmhpZ2hsaWdodEZpZWxkcy5pbmNsdWRlcyhmaWVsZCkpIHsKICAgICAgICByZXR1cm4gdGhpcy5oaWdobGlnaHRUZXh0KHRleHQsIHRoaXMuc2VhcmNoVmFsdWUpOwogICAgICB9CiAgICAgIGlmICh0aGlzLnNlYXJjaEZpZWxkID09PSBmaWVsZCAmJiB0aGlzLnNlYXJjaFZhbHVlKSB7CiAgICAgICAgcmV0dXJuIHRoaXMuaGlnaGxpZ2h0VGV4dCh0ZXh0LCB0aGlzLnNlYXJjaFZhbHVlKTsKICAgICAgfQogICAgICByZXR1cm4gdGV4dDsKICAgIH0sCiAgICByZW1vdmVEb21haW4oaW5kZXgpIHsKICAgICAgaWYgKGluZGV4ICE9PSAtMSkgewogICAgICAgIHRoaXMuYXV0aENvbXBhbnlzLnNwbGljZShpbmRleCwgMSk7CiAgICAgIH0KICAgIH0sCiAgICBhZGREb21haW4oKSB7CiAgICAgIHRoaXMuYXV0aENvbXBhbnlzLnB1c2goewogICAgICAgIHZhbHVlOiAiIiwKICAgICAgICBrZXk6IERhdGUubm93KCksCiAgICAgIH0pOwogICAgfSwKICAgIHVzZXJTZWFyY2goY3JlYXRlQnkpIHsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5iZWxvbmdVc2VyID0gY3JlYXRlQnk7CiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsKICAgIH0sCiAgICBvcHRUeXBlU2VhcmNoKHR5cGUpIHsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5vcGVyYXRpb25UeXBlID0gdHlwZTsKICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOwogICAgfSwKICAgIG9wdFR5cGVDaGFuZ2UoZSkgewogICAgICBjb25zb2xlLmxvZyhlKTsKICAgIH0sCiAgfSwKfTsK"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmvBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/project/report", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-row :gutter=\"20\">\n      <!--部门数据-->\n      <el-col :span=\"3\" :xs=\"24\">\n        <div class=\"head-container\">\n          <el-tree :data=\"auditStatusTree\" :props=\"defaultProps\" :expand-on-click-node=\"false\"\n            :filter-node-method=\"filterNode\" ref=\"tree\" default-expand-all node-key=\"id\" :current-node-key=\"9\"\n            @node-click=\"handleAuditNodeClick\" />\n          <el-tree :data=\"operationTypeTree\" :props=\"defaultProps\" :expand-on-click-node=\"false\"\n            :filter-node-method=\"filterNode\" ref=\"tree\" default-expand-all node-key=\"id\" :current-node-key=\"0\"\n            @node-click=\"handleOptNodeClick\" />\n          <el-tree v-if=\"showUType\" :data=\"userTypeTree\" :props=\"defaultProps\" :expand-on-click-node=\"false\"\n            :filter-node-method=\"filterNode\" ref=\"tree\" default-expand-all node-key=\"id\" :current-node-key=\"0\"\n            @node-click=\"handleUserNodeClick\" />\n        </div>\n      </el-col>\n      <!--用户数据-->\n      <el-col :span=\"21\" :xs=\"24\">\n        <el-form :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" v-show=\"showSearch\" label-width=\"100px\">\n          <!-- <el-form-item label=\"项目所在地\" label-width=\"100px\" prop=\"area\">\n            <el-cascader\n              size=\"small\"\n              clearable\n              :options=\"options\"\n              :props=\"{ checkStrictly: true, expandTrigger: 'hover' }\"\n              v-model=\"queryArea\"\n              @change=\"handleQueryCityChange\"\n            />\n          </el-form-item> -->\n          <el-form-item label=\"所属用户\" prop=\"belongUser\">\n            <el-input v-model=\"queryParams.belongUser\" placeholder=\"请输入所属用户\" clearable size=\"small\"\n              @keyup.enter.native=\"handleQuery\" />\n          </el-form-item>\n          <!-- <el-form-item label=\"项目所在地\" label-width=\"100px\" prop=\"province\">\n            <el-select\n              v-model=\"queryParams.province\"\n              placeholder=\"请选择项目所在地\"\n              clearable\n              size=\"small\"\n            >\n              <el-option\n                v-for=\"dict in options\"\n                :key=\"dict.dictValue\"\n                :label=\"dict.label\"\n                :value=\"dict.label\"\n              />\n            </el-select>\n          </el-form-item> -->\n          <el-form-item label=\"修改时间\">\n            <!-- <el-input\n              v-model=\"queryParams.updateTime\"\n              placeholder=\"请输入搜索内容\"\n              clearable\n              size=\"small\"\n              @keyup.enter.native=\"handleQuery\"\n            /> -->\n            <!-- <el-date-picker v-model=\"queryParams.updateTime\" value-format=\"yyyy-MM-dd\" align=\"right\" type=\"date\"\n              placeholder=\"选择日期\" :picker-options=\"pickerOptions\">\n            </el-date-picker> -->\n            <el-date-picker v-model=\"queryParams.updateTimeArr\" value-format=\"yyyy-MM-dd\" type=\"daterange\" align=\"right\"\n              unlink-panels range-separator=\"至\" start-placeholder=\"开始日期\" end-placeholder=\"结束日期\"\n              :picker-options=\"searchPickerOptions\">\n            </el-date-picker>\n          </el-form-item>\n          <el-form-item label=\"搜索\" prop=\"search\">\n            <el-select v-model=\"searchField\" size=\"small\" style=\"width: 120px; margin-right: 8px;\">\n              <el-option v-for=\"item in searchFieldOptions\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\" />\n            </el-select>\n            <el-input v-model=\"searchValue\"\n              :placeholder=\"`请输入${searchField === 'all' ? '内容' : searchFieldOptions.find(f => f.value === searchField).label}`\"\n              clearable size=\"small\" style=\"width: 200px\" @keyup.enter.native=\"handleQuery\" />\n          </el-form-item>\n          <el-form-item>\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n          </el-form-item>\n        </el-form>\n\n        <el-row :gutter=\"10\" class=\"mb8\">\n          <el-col :span=\"1.5\">\n            <el-button type=\"primary\" plain icon=\"el-icon-plus\" size=\"mini\" @click=\"handleAdd\"\n              v-hasPermi=\"['project:report:add']\">新增</el-button>\n          </el-col>\n          <el-col :span=\"1.5\">\n            <el-button type=\"success\" plain icon=\"el-icon-edit\" size=\"mini\" :disabled=\"single\" @click=\"handleUpdate\"\n              v-hasPermi=\"['project:report:edit']\">修改</el-button>\n          </el-col>\n          <el-col :span=\"1.5\">\n            <el-button type=\"danger\" plain icon=\"el-icon-delete\" size=\"mini\" :disabled=\"multiple\" @click=\"handleDelete\"\n              v-hasPermi=\"['project:report:remove']\">删除</el-button>\n          </el-col>\n          <el-col :span=\"1.5\">\n            <el-button type=\"info\" plain icon=\"el-icon-upload2\" size=\"mini\" @click=\"handleImport\"\n              v-hasPermi=\"['project:report:import']\">导入</el-button>\n          </el-col>\n          <el-col :span=\"1.5\">\n            <el-button type=\"warning\" plain icon=\"el-icon-download\" size=\"mini\" @click=\"clickExport\"\n              v-hasPermi=\"['project:report:export']\">导出</el-button>\n          </el-col>\n          <el-col :span=\"1.5\">\n            <el-button icon=\"el-icon-printer\" size=\"mini\" @click=\"clickPrint\" type=\"info\"\n              v-hasPermi=\"['project:report:print']\" plain>\n              打印\n            </el-button>\n          </el-col>\n          <right-toolbar :showSearch.sync=\"showSearch\" :showExport.sync=\"showExport\" :showPrint.sync=\"showPrint\"\n            @queryTable=\"getList\" @export=\"handleExport\" @print=\"handlePrint\" :columns=\"columns\"></right-toolbar>\n        </el-row>\n\n        <el-table v-loading=\"loading\" border :data=\"reportList\" @selection-change=\"handleSelectionChange\"\n          id=\"printArea\">\n          <el-table-column type=\"selection\" min-width=\"55\" align=\"center\" />\n          <el-table-column prop=\"projectId\" label=\"项目ID\" min-width=\"80\" />\n          <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" min-width=\"120\">\n            <template slot-scope=\"scope\">\n              <el-button size=\"mini\" type=\"text\" icon=\"el-icon-view\" @click=\"handleView(scope.row)\">查看</el-button>\n              <!-- <el-button v-has=\"[scope.row, 'auth']\"\n                size=\"mini\"\n                type=\"text\"\n                icon=\"el-icon-edit-outline\"\n                @click=\"handleAuth(scope.row)\"\n                >授权</el-button> -->\n              <!-- <el-dropdown v-has=\"[scope.row, 'edit']\">\n                <span class=\"el-dropdown-link\">\n                  &nbsp;&nbsp;<i class=\"el-icon-arrow-down el-icon--right\"></i>\n                </span>\n                <el-dropdown-menu slot=\"dropdown\">\n\n                  <el-dropdown-item icon=\"el-icon-edit\" @click.native=\"handleUpdate(scope.row)\" v-hasPermi=\"['project:report:edit']\">\n                    修改\n                  </el-dropdown-item>\n                  <el-dropdown-item icon=\"el-icon-delete\" @click.native=\"handleDelete(scope.row)\" v-hasPermi=\"['project:report:remove']\">\n                    删除\n                  </el-dropdown-item>\n                </el-dropdown-menu>\n              </el-dropdown> -->\n            </template>\n          </el-table-column>\n          <el-table-column label=\"所属用户\" min-width=\"150\" align=\"center\" prop=\"belongUser\" show-overflow-tooltip\n            v-if=\"columns['0'].visible\">\n            <template slot-scope=\"scope\">\n              <span @click=\"userSearch(scope.row.belongUser)\" class=\"link-type\">{{ scope.row.belongUser }}</span>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"项目编号\" min-width=\"150\" align=\"center\" prop=\"projectNo\" show-overflow-tooltip\n            v-if=\"columns['1'].visible\">\n            <template slot-scope=\"scope\">\n              <span v-html=\"highlightCell('projectNo', scope.row.projectNo)\"></span>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"项目名称\" min-width=\"300\" align=\"center\" prop=\"projectName\" :formatter=\"searchFormat\"\n            show-overflow-tooltip v-if=\"columns['2'].visible\">\n            <template slot-scope=\"scope\">\n              <span v-html=\"highlightCell('projectName', scope.row.projectName)\"></span>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"操作类型\" align=\"center\" prop=\"operationType\" :formatter=\"operationTypeFormat\"\n            v-if=\"columns['3'].visible\" min-width=\"100\">\n          </el-table-column>\n          <!-- <el-table-column\n            label=\"审核状态\"\n            align=\"center\"\n            prop=\"auditStatus\"\n            :formatter=\"auditStatusFormat\"\n            v-if=\"columns['3'].visible\"\n          />\n          <el-table-column\n            label=\"编辑状态\"\n            align=\"center\"\n            prop=\"editStatus\"\n            :formatter=\"editStatusFormat\"\n            v-if=\"columns['5'].visible\"\n          /> -->\n          <el-table-column min-width=\"200\" label=\"项目所在地\" align=\"center\" prop=\"province\" v-if=\"columns['4'].visible\" />\n          <el-table-column min-width=\"200\" label=\"详细地址\" align=\"center\" prop=\"address\" show-overflow-tooltip\n            v-if=\"columns['5'].visible\">\n            <template slot-scope=\"scope\">\n              <span v-html=\"highlightCell('address', scope.row.address)\"></span>\n            </template>\n          </el-table-column>\n          <el-table-column min-width=\"200\" label=\"被授权公司\" align=\"center\" prop=\"authCompany\" show-overflow-tooltip\n            v-if=\"columns['6'].visible\">\n            <template slot-scope=\"scope\">\n              <span v-html=\"highlightCell('authCompany', scope.row.authCompany)\"></span>\n            </template>\n          </el-table-column>\n          <el-table-column min-width=\"200\" label=\"所属经销商\" align=\"center\" prop=\"distributor\" show-overflow-tooltip\n            v-if=\"columns['7'].visible\" />\n          <el-table-column min-width=\"200\" label=\"招标单位\" align=\"center\" prop=\"biddingCompany\" show-overflow-tooltip\n            v-if=\"columns['8'].visible\">\n            <template slot-scope=\"scope\">\n              <span v-html=\"highlightCell('biddingCompany', scope.row.biddingCompany)\"></span>\n            </template>\n          </el-table-column>\n          <el-table-column min-width=\"150\" label=\"投标产品型号\" align=\"center\" prop=\"model\" :formatter=\"modelFormat\"\n            show-overflow-tooltip v-if=\"columns['9'].visible\" />\n          <el-table-column min-width=\"150\" label=\"投标产品规格\" align=\"center\" prop=\"spec\" :formatter=\"specFormat\"\n            v-if=\"columns['10'].visible\" show-overflow-tooltip />\n          <el-table-column min-width=\"150\" label=\"安装面积(m²)\" align=\"center\" prop=\"area\" show-overflow-tooltip\n            v-if=\"columns['11'].visible\" />\n          <el-table-column min-width=\"100\" label=\"所需资料\" align=\"center\" prop=\"requireInfo\" :formatter=\"requireInfoFormat\"\n            show-overflow-tooltip v-if=\"columns['12'].visible\" />\n          <el-table-column label=\"资料类型\" align=\"center\" prop=\"infoType\" :formatter=\"infoTypeFormat\"\n            v-if=\"columns['13'].visible\" />\n          <el-table-column min-width=\"150\" label=\"资料接收邮件\" align=\"center\" prop=\"scanFile\" show-overflow-tooltip\n            v-if=\"columns['14'].visible\" />\n          <el-table-column min-width=\"150\" label=\"资料接收地址\" align=\"center\" prop=\"sendAddress\" show-overflow-tooltip\n            v-if=\"columns['15'].visible\" />\n          <el-table-column min-width=\"150\" label=\"项目所属省份\" align=\"center\" prop=\"belongProvince\"\n            :formatter=\"belongProvinceFormat\" show-overflow-tooltip v-if=\"columns['16'].visible\" />\n          <el-table-column min-width=\"150\" label=\"售后年限\" align=\"center\" prop=\"afterSaleYear\"\n            :formatter=\"afterSaleYearFormat\" show-overflow-tooltip v-if=\"columns['17'].visible\" />\n          <el-table-column min-width=\"150\" label=\"开标日期\" align=\"center\" prop=\"openDate\" show-overflow-tooltip\n            v-if=\"columns['18'].visible\" />\n          <el-table-column min-width=\"150\" label=\"挂网日期\" align=\"center\" prop=\"hangDate\" show-overflow-tooltip\n            v-if=\"columns['19'].visible\" />\n          <el-table-column min-width=\"110\" label=\"提交时间\" align=\"center\" prop=\"createTime\" v-if=\"columns['20'].visible\"\n            show-overflow-tooltip />\n          <el-table-column min-width=\"110\" label=\"修改时间\" align=\"center\" prop=\"updateTime\" v-if=\"columns['21'].visible\"\n            show-overflow-tooltip />\n        </el-table>\n\n        <pagination v-show=\"total > 0\" :total=\"total\" :page.sync=\"queryParams.pageNum\"\n          :limit.sync=\"queryParams.pageSize\" :layout=\"pageLayout\" @pagination=\"getList\" />\n\n        <!-- 添加或修改项目报备对话框 -->\n        <el-dialog :title=\"title\" :visible.sync=\"open\" :close-on-click-modal=\"false\" width=\"80%\"\n          custom-class=\"edit-dialog\" append-to-body>\n          <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"编辑状态\">\n                  <el-radio-group :disabled=\"isAdmin\" v-model=\"form.editStatus\">\n                    <el-radio v-for=\"dict in editStatusOptions\" :key=\"dict.dictValue\"\n                      :label=\"dict.dictValue\">{{ dict.dictLabel }}</el-radio>\n                  </el-radio-group>\n                </el-form-item>\n              </el-col>\n              <!-- <el-col :span=\"12\">\n                <el-form-item label=\"所属用户\" prop=\"belongUser\">\n                  <el-select v-model=\"form.belongUser\" placeholder=\"请选择所属用户\">\n                    <el-option label=\"请选择字典生成\" value=\"\" />\n                  </el-select>\n                </el-form-item>\n              </el-col> -->\n            </el-row>\n            <!-- <el-row>\n              <el-col :span=\"20\">\n                <el-form-item label=\"驳回原因\" prop=\"rejectReason\">\n                  <el-input\n                    v-model=\"form.rejectReason\"\n                    type=\"textarea\"\n                    placeholder=\"请输入内容\"\n                  />\n                </el-form-item>\n              </el-col>\n            </el-row> -->\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"项目编号\" prop=\"projectNo\">\n                  <el-input v-model=\"form.projectNo\" placeholder=\"无编号则为提交时间(年月日时间)\" />\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"项目名称\" prop=\"projectName\">\n                  <el-input v-model=\"form.projectName\" placeholder=\"请输入项目名称\" />\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"项目所在地\" prop=\"province\">\n                  <el-cascader ref=\"cascader\" :options=\"options\" clearable :props=\"{ expandTrigger: 'hover' }\"\n                    v-model=\"selectedOptions\" @change=\"handleChange\">\n                  </el-cascader>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"详细地址\" prop=\"address\">\n                  <el-input v-model=\"form.address\" placeholder=\"请输入详细地址\" />\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"项目所属省份\" prop=\"belongProvince\">\n                  <el-select v-model=\"form.belongProvince\" clearable placeholder=\"请选择所属省份\">\n                    <el-option v-for=\"item in belongProvinceOptions\" :key=\"item.value\" :label=\"item.label\"\n                      :value=\"item.value\">\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"招标单位\" prop=\"biddingCompany\">\n                  <el-input v-model=\"form.biddingCompany\" placeholder=\"请输入招标单位\" />\n                </el-form-item>\n                <!-- <el-form-item label=\"招标单位联系人/联系电话\" prop=\"biddingContact\">\n                  <el-input v-model=\"form.biddingContact\" placeholder=\"请输入联系人/联系电话\" />\n                </el-form-item> -->\n              </el-col>\n            </el-row>\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"挂网日期\" prop=\"hangDate\">\n                  <el-input v-model=\"form.hangDate\" placeholder=\"请输入挂网日期，没有请写无，日期格式XXXX（年）-XX（月）-XX（日)\" />\n                  <!-- <el-date-picker clearable size=\"small\" v-model=\"form.hangDate\" type=\"date\" value-format=\"yyyy-MM-dd\"\n                    placeholder=\"选择挂网日期\">\n                  </el-date-picker> -->\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"开标日期\" prop=\"openDate\">\n                  <el-input v-model=\"form.openDate\" placeholder=\"请输入开标日期，没有请写无，日期格式XXXX（年）-XX（月）-XX（日\" />\n                  <!-- <el-date-picker clearable size=\"small\" v-model=\"form.openDate\" type=\"date\" value-format=\"yyyy-MM-dd\"\n                    placeholder=\"选择开标日期\">\n                  </el-date-picker> -->\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"所属经销商\" prop=\"distributor\">\n                  <el-input v-model=\"form.distributor\" placeholder=\"请输入经销商\" />\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <!-- <el-form-item label=\"招标单位联系人/联系电话\" prop=\"biddingContact\">\n              <el-input v-model=\"form.biddingContact\" placeholder=\"请输入联系人/联系电话\" />\n            </el-form-item> -->\n                <el-form-item label=\"售后年限\" prop=\"afterSaleYear\">\n                  <el-select v-model=\"form.afterSaleYear\" clearable placeholder=\"请选择所属省份\">\n                    <el-option v-for=\"item in afterSaleYearOptions\" :key=\"item.value\" :label=\"item.label\"\n                      :value=\"item.value\">\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <!-- <el-row>\n              <el-col :span=\"12\">\n\n              </el-col>\n            </el-row> -->\n            <!-- <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"开标日期\" prop=\"openDate\">\n                  <el-date-picker\n                    clearable\n                    size=\"small\"\n                    v-model=\"form.openDate\"\n                    type=\"date\"\n                    value-format=\"yyyy-MM-dd\"\n                    placeholder=\"选择开标日期\"\n                  >\n                  </el-date-picker>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"招标方式\" prop=\"biddingType\">\n                  <el-select\n                    v-model=\"form.biddingType\"\n                    placeholder=\"请选择招标方式\"\n                  >\n                    <el-option\n                      v-for=\"dict in biddingTypeOptions\"\n                      :key=\"dict.dictValue\"\n                      :label=\"dict.dictLabel\"\n                      :value=\"dict.dictValue\"\n                    ></el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n            </el-row> -->\n            <el-row>\n              <!-- <el-col :span=\"12\">\n                <el-form-item label=\"预算金额\" prop=\"budgetMoney\">\n                  <el-input\n                    type=\"number\"\n                    v-model=\"form.budgetMoney\"\n                    placeholder=\"请输入预算金额\"\n                  />\n                </el-form-item>\n              </el-col> -->\n              <el-col :span=\"12\">\n                <el-form-item label=\"被授权公司\" prop=\"authCompany\">\n                  <el-input v-model=\"form.authCompany\" placeholder=\"请输入授权公司\" />\n                  <el-link @click=\"addDomain\" type=\"primary\">添加</el-link>\n                </el-form-item>\n                <el-form-item v-for=\"(company, index) in authCompanys\" :label=\"'被授权公司' + (index + 1)\" :key=\"company.key\"\n                  class=\"info-type\">\n                  <el-input v-model=\"company.value\" :placeholder=\"'被授权公司' + (index + 1)\" style=\"max-width: 300px\" />\n                  <el-link @click=\"removeDomain(index)\" type=\"primary\">删除</el-link>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"授权公司联系人/联系电话\" prop=\"authContact\">\n                  <el-input v-model=\"form.authContact\" placeholder=\"请输入授权公司联系人/联系电话\" />\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <!-- <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"招标信息公布网站\" prop=\"biddingNet\">\n                  <el-input\n                    v-model=\"form.biddingNet\"\n                    placeholder=\"请输入招标信息公布网站\"\n                  />\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"招标单位\" prop=\"biddingCompany\">\n                  <el-input\n                    v-model=\"form.biddingCompany\"\n                    placeholder=\"请输入招标单位\"\n                  />\n                </el-form-item>\n              </el-col>\n            </el-row> -->\n            <!-- <el-row v-if=\"form.operationType == 2\">\n              <el-col :span=\"20\">\n                <el-form-item label=\"模板下载\">\n                  <el-col :span=\"8\">\n                    <el-link @click=\"downloadSQS\" type=\"primary\">海佳集团-授权书.docx</el-link>\n                  </el-col>\n                  <el-col :span=\"8\">\n                    <el-link @click=\"downloadCRH\" type=\"primary\">海佳集团-售后服务承诺函.docx</el-link>\n                  </el-col>\n                </el-form-item>\n              </el-col>\n            </el-row> -->\n            <!-- <el-form-item label=\"授权书\" v-if=\"form.operationType == 2\" :required=\"form.operationType == 2\">\n              <fileUpload v-model=\"form.authFile\" :fileType=\"['doc', 'docx']\" />\n            </el-form-item> -->\n            <el-form-item label=\"其余附件\">\n              <fileUpload v-model=\"form.afterSaleFile\" :fileType=\"['doc', 'docx']\" />\n            </el-form-item>\n            <el-row>\n              <el-col :span=\"20\">\n                <el-form-item label=\"投标产品型号\" prop=\"model\" :required=\"true\">\n                  <el-cascader class=\"mobile-width\" style=\"width: 700px\" v-model=\"form.model\" placeholder=\"请输入产品型号\"\n                    :options=\"modelOptions\" :props=\"{ multiple: true }\" clearable filterable></el-cascader>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row>\n              <el-col :span=\"20\">\n                <el-form-item label=\"投标产品规格\" prop=\"spec\" :required=\"true\">\n                  <el-cascader class=\"mobile-width\" style=\"width: 700px\" v-model=\"form.spec\" placeholder=\"请输入产品规格\"\n                    :options=\"specOptions\" :props=\"{ multiple: true }\" clearable filterable></el-cascader>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"安装面积\" prop=\"area\">\n                  <el-input v-model=\"form.area\" type=\"number\" placeholder=\"请输入安装面积\">\n                    <template slot=\"append\">m²</template>\n                  </el-input>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row>\n              <el-col :span=\"20\">\n                <el-form-item label=\"所需资料\">\n                  <el-cascader class=\"mobile-width\" style=\"width: 700px\" v-model=\"form.requireInfo\"\n                    placeholder=\"请输入资料类型\" :options=\"requireInfoOptions\" :props=\"{ multiple: true }\" clearable\n                    filterable></el-cascader>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <!-- <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"资料类型\">\n                  <el-checkbox-group v-model=\"form.infoType\">\n                    <el-checkbox\n                      v-for=\"dict in infoTypeOptions\"\n                      :key=\"dict.dictValue\"\n                      :label=\"dict.dictValue\"\n                    >\n                      {{ dict.dictLabel }}\n                    </el-checkbox>\n                  </el-checkbox-group>\n                </el-form-item>\n              </el-col>\n            </el-row> -->\n            <!-- <el-row>\n              <el-form-item label=\"资料接收方式\" prop=\"infoType\" :required=\"true\">\n                <el-checkbox-group v-model=\"form.infoType\" class=\"info-type\" style=\"display:flex;\">\n                \n                  <el-checkbox label=\"1\" style=\"margin-left:20px;margin-right:10px !important;\">邮件</el-checkbox>\n                  <el-form-item prop=\"scanFile\">\n                    <el-input v-model=\"form.scanFile\" placeholder=\"请输入邮箱地址\" type=\"email\" style=\"width:300px;\" ></el-input>\n                  </el-form-item>\n                \n                  <el-checkbox label=\"2\" style=\"margin-left:20px;margin-right:10px !important;\">邮寄</el-checkbox>\n                  <el-form-item prop=\"sendAddress\">\n                    <el-input v-model=\"form.sendAddress\" placeholder=\"请输入收件地址\" style=\"width:300px;\" ></el-input>\n                  </el-form-item>\n                </el-checkbox-group>\n              </el-form-item>\n            </el-row>\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"邮件发送信息\" prop=\"mailInfo\">\n                  <el-input\n                    v-model=\"form.mailInfo\"\n                    placeholder=\"请输入邮件发送信息\"\n                  />\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"快递单号\" prop=\"expressInfo\">\n                  <el-input\n                    v-model=\"form.expressInfo\"\n                    placeholder=\"请输入快递单号\"\n                  />\n                </el-form-item>\n              </el-col>\n            </el-row> -->\n            <el-row>\n              <el-form-item label=\"资料接收方式\" prop=\"infoType\" :required=\"true\">\n                <el-checkbox-group v-model=\"form.infoType\" class=\"info-type\">\n                  <!-- 选项A -->\n                  <el-row style=\"display: flex; margin-bottom: 22px\">\n                    <el-col :span=\"12\" style=\"display: flex\">\n                      <el-checkbox label=\"1\" style=\"margin-left: 20px; margin-right: 10px !important\">邮件</el-checkbox>\n                      <el-form-item prop=\"scanFile\">\n                        <el-input v-model=\"form.scanFile\" placeholder=\"请输入邮箱地址\" style=\"width: 300px\"\n                          type=\"email\"></el-input>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"12\">\n                      <el-form-item label=\"邮件发送信息\" prop=\"mailInfo\">\n                        <el-input v-model=\"form.mailInfo\" placeholder=\"请输入邮件发送信息\" />\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n                  <!-- 选项B  -->\n                  <el-row style=\"display: flex; margin-bottom: 22px\">\n                    <el-col :span=\"12\" style=\"display: flex\">\n                      <el-checkbox label=\"2\" style=\"margin-left: 20px; margin-right: 10px !important\">邮寄</el-checkbox>\n                      <el-form-item prop=\"sendAddress\">\n                        <el-input v-model=\"form.sendAddress\" placeholder=\"请输入收件地址\" style=\"width: 300px\"></el-input>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"12\">\n                      <el-form-item label=\"快递单号\" prop=\"expressInfo\">\n                        <el-input v-model=\"form.expressInfo\" placeholder=\"请输入快递单号\" />\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n                </el-checkbox-group>\n              </el-form-item>\n            </el-row>\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"操作类型\" prop=\"operationType\">\n                  <el-radio-group @change=\"optTypeChange\" v-model=\"form.operationType\">\n                    <el-radio v-for=\"dict in operationTypeOptions\" :key=\"dict.dictValue\"\n                      :label=\"dict.dictValue\">{{ dict.dictLabel }}</el-radio>\n                  </el-radio-group>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"审核状态\">\n                  <el-radio-group :disabled=\"auditStatusEdit\" v-model=\"form.auditStatus\">\n                    <el-radio v-for=\"dict in auditStatusOptions\" :key=\"dict.dictValue\"\n                      :label=\"dict.dictValue\">{{ dict.dictLabel }}</el-radio>\n                  </el-radio-group>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-form-item label=\"备注\" prop=\"remark\">\n              <el-input v-model=\"form.remark\" type=\"textarea\" placeholder=\"请输入备注\" />\n            </el-form-item>\n          </el-form>\n          <div slot=\"footer\" class=\"dialog-footer\">\n            <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n            <el-button @click=\"cancel\">取 消</el-button>\n          </div>\n        </el-dialog>\n        <!-- 查看项目报备对话框 -->\n        <el-dialog :title=\"title\" :visible.sync=\"viewOpen\" :fullscreen=\"true\" :lock-scroll=\"true\"\n          :destroy-on-close=\"true\" custom-class=\"view-dialog\" @close=\"viewOk\">\n          <flowable v-if=\"viewOpen\" ref=\"flow\" :procDefKey=\"defKey\" :procInsId=\"procInsId\" :taskId=\"taskId\"\n            :bizKey=\"bizKey\" :finished=\"finished\" :isAuthImages=\"isAuthImages\">\n            <template v-slot:title>项目信息</template>\n            <template v-slot:content>\n              <el-descriptions label-width=\"120px\" :column=\"isMobile ? 1 : 3\">\n                <el-descriptions-item label=\"操作类型\" prop=\"operationType\">\n                  {{ view.operationType }}\n                </el-descriptions-item>\n                <el-descriptions-item label=\"审核状态\">\n                  {{ view.auditStatus }}\n                </el-descriptions-item>\n                <!-- <el-descriptions-item label=\"驳回原因\" prop=\"rejectReason\">\n                {{ view.rejectReason }}\n              </el-descriptions-item> -->\n                <el-descriptions-item label=\"项目ID\">\n                  {{ view.projectId }}\n                </el-descriptions-item>\n                <el-descriptions-item label=\"项目编号\" prop=\"projectNo\">\n                  {{ view.projectNo }}\n                </el-descriptions-item>\n                <el-descriptions-item label=\"项目名称\" span=\"3\" prop=\"projectName\">\n                  <el-popover v-if=\"likecount > 0\" placement=\"top-start\" title=\"相似项目\" width=\"450\" trigger=\"hover\">\n                    <el-table :data=\"likeList\">\n                      <el-table-column width=\"100\" property=\"value\" label=\"项目ID\"></el-table-column>\n                      <el-table-column width=\"300\" property=\"name\" label=\"项目名称\" show-overflow-tooltip></el-table-column>\n                    </el-table>\n                    <el-badge slot=\"reference\" :value=\"likecount\" class=\"item\">\n                      {{ view.projectName\n                      }}<span class=\"likeTip\">&nbsp;&nbsp;存在相似项目</span>\n                    </el-badge>\n                  </el-popover>\n                  <span v-if=\"!likecount\">{{ view.projectName }}</span>\n                </el-descriptions-item>\n                <el-descriptions-item label=\"编辑状态\">\n                  {{ view.editStatus }}\n                </el-descriptions-item>\n                <el-descriptions-item label=\"项目所在地\" prop=\"area\">\n                  {{ view.province }}\n                </el-descriptions-item>\n                <el-descriptions-item label=\"详细地址\">\n                  {{ view.address }}\n                </el-descriptions-item>\n                <el-descriptions-item label=\"安装面积(m²)\">\n                  {{ view.area }}\n                </el-descriptions-item>\n                <el-descriptions-item label=\"被授权公司\">\n                  {{ view.authCompany }}\n                </el-descriptions-item>\n                <el-descriptions-item label=\"授权公司联系人/联系电话\">\n                  {{ view.authContact }}\n                </el-descriptions-item>\n                <el-descriptions-item label=\"招标单位\">\n                  {{ view.biddingCompany }}\n                </el-descriptions-item>\n                <!-- <el-descriptions-item label=\"招标单位联系人/联系电话\">\n                  {{ view.biddingContact }}\n                </el-descriptions-item> -->\n                <el-descriptions-item label=\"项目所属省份\" prop=\"belongProvince\">\n                  {{ view.belongProvince }}\n                </el-descriptions-item>\n                <el-descriptions-item label=\"售后年限\" prop=\"afterSaleYear\">\n                  {{ view.afterSaleYear }}\n                </el-descriptions-item>\n                <el-descriptions-item label=\"开标日期\">\n                  {{ view.openDate }}\n                </el-descriptions-item>\n                <el-descriptions-item label=\"挂网日期\">\n                  {{ view.hangDate }}\n                </el-descriptions-item>\n                <el-descriptions-item label=\"所属经销商\" prop=\"distributor\">\n                  {{ view.distributor }}\n                </el-descriptions-item>\n                <el-descriptions-item label=\"所需资料\" span=\"3\">\n                  <template v-if=\"infoList1.length > 0\">\n                    <ul class=\"infinite-list\" v-infinite-scroll=\"load\" style=\"overflow: auto\">\n                      <li v-for=\"(i, index) in infoList1\" v-bind:key=\"index\" class=\"infinite-list-item\">\n                        {{ i.dictLabel }}\n                        <el-link v-if=\"i.targetUrl && view.auditStatus == '已审批'\"\n                          @click.prevent=\"handleDownload(i.targetUrl)\" type=\"primary\">下载</el-link>\n                      </li>\n                    </ul>\n                    <ul class=\"infinite-list\" v-infinite-scroll=\"load\" style=\"overflow: auto\">\n                      <li v-for=\"(i, index) in infoList2\" v-bind:key=\"index\" class=\"infinite-list-item\">\n                        {{ i.dictLabel }}\n                        <el-link target=\"_blank\" v-if=\"i.targetUrl && view.auditStatus == '已审批'\"\n                          @click.prevent=\"handleDownload(i.targetUrl)\" type=\"primary\">下载</el-link>\n                      </li>\n                    </ul>\n                  </template>\n                  <span v-else>{{ view.requireInfo }}</span>\n                </el-descriptions-item>\n                <el-descriptions-item label=\"投标产品型号\">\n                  {{ view.model }}\n                </el-descriptions-item>\n                <el-descriptions-item label=\"投标产品规格\">\n                  {{ view.spec }}\n                </el-descriptions-item>\n                <!-- <el-descriptions-item label=\"资料类型\">\n                {{ view.infoType }}\n              </el-descriptions-item> -->\n                <el-descriptions-item label=\"授权书\" v-if=\"view.operationType == '授权'\">\n                  <el-link target=\"_blank\" v-if=\"view.authFile\" :href=\"view.authFile\" type=\"primary\">下载</el-link>\n                </el-descriptions-item>\n                <el-descriptions-item label=\"其余附件\" v-if=\"view.operationType == '授权'\">\n                  <el-link target=\"_blank\" v-if=\"view.afterSaleFile\" :href=\"view.afterSaleFile\"\n                    type=\"primary\">下载</el-link>\n                </el-descriptions-item>\n\n                <el-descriptions-item label=\"授权书图片\" v-if=\"view.operationType == '授权'\">\n                  <el-link target=\"_blank\" v-if=\"view.authImages && view.auditStatus === '已审批'\" :href=\"view.authImages\"\n                    type=\"primary\">下载</el-link>\n                </el-descriptions-item>\n                <el-descriptions-item label=\"资料接收邮件\" prop=\"scanFile\">\n                  {{ view.scanFile }}\n                </el-descriptions-item>\n                <el-descriptions-item label=\"资料接收地址\" prop=\"sendAddress\">\n                  {{ view.sendAddress }}\n                </el-descriptions-item>\n                <el-descriptions-item label=\"邮件发送信息\" prop=\"mailInfo\">\n                  {{ view.mailInfo }}\n                </el-descriptions-item>\n                <el-descriptions-item label=\"快递单号\" prop=\"expressInfo\">\n                  {{ view.expressInfo }}\n                </el-descriptions-item>\n                <el-descriptions-item label=\"备注\" prop=\"remark\">\n                  {{ view.remark }}\n                </el-descriptions-item>\n                <el-descriptions-item label=\"提交时间\">\n                  {{ view.createTime }}\n                </el-descriptions-item>\n                <el-descriptions-item label=\"修改时间\">\n                  {{ view.updateTime }}\n                </el-descriptions-item>\n              </el-descriptions>\n            </template>\n          </flowable>\n        </el-dialog>\n        <!-- 项目导入对话框 -->\n        <el-dialog :title=\"upload.title\" :visible.sync=\"upload.open\" width=\"400px\" append-to-body>\n          <el-upload ref=\"upload\" :limit=\"1\" accept=\".xlsx, .xls\" :headers=\"upload.headers\"\n            :action=\"upload.url + '?updateSupport=' + upload.updateSupport\" :disabled=\"upload.isUploading\"\n            :on-progress=\"handleFileUploadProgress\" :on-success=\"handleFileSuccess\" :auto-upload=\"false\" drag>\n            <i class=\"el-icon-upload\"></i>\n            <div class=\"el-upload__text\">\n              将文件拖到此处，或\n              <em>点击上传</em>\n            </div>\n            <div class=\"el-upload__tip\" slot=\"tip\">\n              <el-checkbox v-model=\"upload.updateSupport\" />是否更新已经存在的项目数据\n              <el-link type=\"info\" style=\"font-size: 12px\" @click=\"importTemplate\">下载模板</el-link>\n            </div>\n            <div class=\"el-upload__tip\" style=\"color: red\" slot=\"tip\">\n              提示：仅允许导入\"xls\"或\"xlsx\"格式文件！\n            </div>\n          </el-upload>\n          <div slot=\"footer\" class=\"dialog-footer\">\n            <el-button type=\"primary\" @click=\"submitFileForm\">确 定</el-button>\n            <el-button @click=\"upload.open = false\">取 消</el-button>\n          </div>\n        </el-dialog>\n      </el-col>\n    </el-row>\n  </div>\n</template>\n\n<script>\nimport {\n  listReport,\n  getReport,\n  delReport,\n  addReport,\n  updateReport,\n  exportReport,\n  importTemplate,\n  printReport,\n  checkNameUnique,\n  getLikeList,\n  authReport,\n} from \"@/api/project/report\";\nimport { getToken } from \"@/utils/auth\";\nimport FileUpload from \"@/components/FileUpload\";\nimport flowable from \"@/views/flowable/task/record/view\";\nimport { getInsIdByBizKey } from \"@/api/flowable/todo\";\nimport { regionData, CodeToText, TextToCode } from \"element-china-area-data\";\nimport print from \"print-js\";\nexport default {\n  name: \"Report\",\n  components: {\n    FileUpload,\n    print,\n    flowable,\n  },\n  data() {\n    var infoTypeValueVali = (rule, value, callback) => {\n      if (this.form.infoType.indexOf(\"1\") >= 0 && !this.form.scanFile) {\n        callback(new Error(\"邮箱地址必填\"));\n        return;\n      }\n      callback();\n    };\n    var infoTypeValueVali2 = (rule, value, callback) => {\n      if (this.form.infoType.indexOf(\"2\") >= 0 && !this.form.sendAddress) {\n        callback(new Error(\"收件地址必填\"));\n        return;\n      }\n      callback();\n    };\n    var nameVali = (rule, value, callback) => {\n      if (!this.form.projectName) {\n        callback(new Error(\"项目名称必填\"));\n      } else {\n        if (/\\s+/g.test(this.form.projectName)) {\n          callback(new Error(\"项目名称不规范\"));\n          return;\n        }\n        checkNameUnique({\n          projectName: this.form.projectName,\n          projectId: this.form.projectId,\n        }).then((response) => {\n          if (response.data == 0) {\n            callback();\n          } else {\n            callback(new Error(\"项目名称已存在\"));\n          }\n        });\n      }\n    };\n    var codeVali = (rule, value, callback) => {\n      if (!that.form.projectNo) {\n        callback(new Error(\"项目编号必填\"));\n      } else if (/\\s+/g.test(that.form.projectNo)) {\n        callback(new Error(\"项目编号不规范\"));\n        return;\n      }\n      callback();\n    };\n    var authFileValueVali = (rule, value, callback) => {\n      if (this.form.operationType == 2 && !this.form.authFile) {\n        callback(new Error(\"授权类型必传授权书\"));\n      }\n      callback();\n    };\n    var openDateVali = (rule, value, callback) => {\n      console.log(123566)\n      if (!that.form.openDate) {\n        callback(new Error(\"开标日期必填\"));\n        return;\n      } else if (value === \"无\") {\n        callback();\n        return;\n      } else if (!/^\\d{4}-\\d{2}-\\d{2}$/.test(value)) {\n        callback(new Error(\"开标日期格式不合法，示例2025-01-01\"));\n        return;\n      }\n      callback();\n    };\n    var hangDateVali = (rule, value, callback) => {\n      if (!that.form.hangDate) {\n        callback(new Error(\"挂网日期必填\"));\n        return;\n      } else if (value === \"无\") {\n        callback();\n        return;\n      } else if (!/^\\d{4}-\\d{2}-\\d{2}$/.test(value)) {\n        callback(new Error(\"挂网日期格式不合法，示例2025-01-01\"));\n        return;\n      }\n      callback();\n    };\n    return {\n      isMobile: false,\n      pageLayout: \"total, sizes, prev, pager, next, jumper\",\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      showExport: false,\n      showPrint: false,\n      // 总条数\n      total: 0,\n      // 项目报备表格数据\n      reportList: [],\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      open: false,\n      // 操作类型字典\n      operationTypeOptions: [],\n      // 审核状态字典\n      auditStatusOptions: [],\n      // 编辑状态字典\n      editStatusOptions: [],\n      // 招标方式字典\n      biddingTypeOptions: [],\n      // 投标产品型号字典\n      modelOptions: [],\n      modelOption1: [],\n      // 所需资料字典\n      requireInfoOptions: [],\n      requireInfoOption1: [],\n      // 资料类型字典\n      infoTypeOptions: [],\n      specOptions: [],\n      specOption1: [],\n      // 所属省份字典\n      belongProvinceOptions: [],\n      belongProvinceOptions1: [],\n      // 售后年限\n      afterSaleYearOptions: [],\n      afterSaleYearOptions1: [],\n      // 项目导入参数\n      upload: {\n        // 是否显示弹出层（用户导入）\n        open: false,\n        // 弹出层标题（用户导入）\n        title: \"\",\n        // 是否禁用上传\n        isUploading: false,\n        // 是否更新已经存在的用户数据\n        updateSupport: 0,\n        // 设置上传的请求头部\n        headers: { Authorization: \"Bearer \" + getToken() },\n        // 上传的地址\n        url: process.env.VUE_APP_BASE_API + \"/project/report/importData\",\n      },\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        projectNo: null,\n        projectName: null,\n        operationType: null,\n        auditStatus: null,\n        province: null,\n        userType: null,\n        belongUser: null,\n        updateTimeArr: [],\n        spare1: null,\n        address: null,\n        biddingCompany: null,\n        authCompany: null,\n        fullField: null\n      },\n      // 表单参数\n      form: {},\n      // 表单校验\n      // 表单校验\n      rules: {\n        operationType: [{ required: true, message: \"操作类型必选\" }],\n        projectNo: [{ required: true, validate: codeVali, trigger: \"blur\" }],\n        projectName: [{ required: true, validate: nameVali, trigger: \"blur\" }],\n        address: [\n          { required: true, message: \"详细地址不能为空\", trigger: \"blur\" },\n        ],\n        biddingCompany: [\n          { required: true, message: \"招标单位不能为空\", trigger: \"blur\" },\n        ],\n        openDate: [\n          { required: true, validate: openDateVali, trigger: \"blur\" },\n        ],\n        afterSaleYear: [\n          { required: true, message: \"售后年限不能为空\", trigger: \"blur\" },\n        ],\n        hangDate: [\n          { required: true, validate: hangDateVali, trigger: \"blur\" },\n        ],\n        belongProvince: [\n          { required: true, message: \"售后年限不能为空\", trigger: \"blur\" },\n        ],\n        distributor: [\n          { required: true, message: \"所属经销商不能为空\", trigger: \"blur\" },\n        ],\n        scanFile: [{ validator: infoTypeValueVali, trigger: \"blur\" }],\n        sendAddress: [{ validator: infoTypeValueVali2, trigger: \"blur\" }],\n        model: [{ required: true, message: \"投标产品型号必选\" }],\n        spec: [{ required: true, message: \"投标产品规格必选\" }],\n        province: [{ required: true, message: \"项目所在地必选\" }],\n        infoType: [\n          { required: true, message: \"资料接收方式必选\", trigger: \"change\" },\n        ],\n        authFile: [{ validator: authFileValueVali, trigger: \"change\" }],\n        biddingContact: [\n          { required: true, message: \"招标单位联系人/联系电话必填\" },\n        ],\n        authContact: [\n          { required: true, message: \"授权公司联系人/联系电话必填\" },\n        ],\n      },\n      // 列信息\n      columns: [\n        { key: \"belongUser\", index: 1, label: `所属用户`, visible: true },\n        { key: \"projectNo\", index: 2, label: `项目编号`, visible: true },\n        { key: \"projectName\", index: 3, label: `项目名称`, visible: true },\n        { key: \"operationType\", index: 4, label: `操作类型`, visible: true },\n        { key: \"province\", index: 5, label: `项目所在地`, visible: true },\n        { key: \"address\", index: 6, label: `详细地址`, visible: true },\n        { key: \"authCompany\", index: 7, label: `被授权公司`, visible: true },\n        { key: \"distributor\", index: 8, label: `所属经销商`, visible: true },\n        { key: \"biddingCompany\", index: 9, label: `招标单位`, visible: true },\n        { key: \"model\", index: 10, label: `投标产品型号`, visible: true },\n        { key: \"spec\", index: 11, label: `投标产品规格`, visible: true },\n        { key: \"area\", index: 12, label: `安装面积`, visible: true },\n        { key: \"requireInfo\", index: 13, label: `所需资料`, visible: true },\n        { key: \"infoType\", index: 14, label: `资料类型`, visible: true },\n        { key: \"scanFile\", index: 15, label: `资料接收邮件`, visible: true },\n        { key: \"scanFile\", index: 16, label: `资料接收地址`, visible: true },\n        { key: \"belongProvince\", index: 17, label: `项目所属省份`, visible: true },\n        { key: \"afterSaleYear\", index: 18, label: `售后年限`, visible: true },\n        { key: \"openDate\", index: 19, label: `开标日期`, visible: true },\n        { key: \"hangDate\", index: 20, label: `挂网日期`, visible: true },\n        { key: \"createTime\", index: 21, label: `提交时间`, visible: true },\n        { key: \"updateTime\", index: 22, label: `修改时间`, visible: true },\n        // { key: \"auditStatus\", index: 19, label: `审核状态`, visible: false },\n        // { key: \"editStatus\", index: 20, label: `编辑状态`, visible: false },\n        // { key: \"11\", index: 21, label: `授权书`, visible: false },\n        //{ key: \"12\", index: 23, label: `售后服务承诺函`, visible: false },\n      ],\n      options: regionData,\n      selectedOptions: [],\n      queryArea: [],\n      viewOpen: false,\n      view: {},\n      infoList1: [],\n      infoList2: [],\n      defKey: \"process_project_report\",\n      procInsId: undefined,\n      taskId: undefined,\n      finished: true,\n      bizKey: undefined,\n      auditStatusTree: [],\n      operationTypeTree: [],\n      userTypeTree: [],\n      defaultProps: {\n        children: \"children\",\n        label: \"label\",\n      },\n      oldOperationType: undefined,\n      showUType: true,\n      chooseOptType: undefined,\n      chooseAuditStatus: undefined,\n      chooseUserId: undefined,\n      chooseEditStatus: undefined,\n      chooseSpare2: undefined,\n      likeList: undefined,\n      likeCount: undefined,\n      authCompanys: [],\n      isAdmin: true,\n      auditStatusEdit: true,\n      isAuthImages: false,\n      searchPickerOptions: {\n        shortcuts: [{\n          text: '最近一周',\n          onClick(picker) {\n            const end = new Date();\n            const start = new Date();\n            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);\n            picker.$emit('pick', [start, end]);\n          }\n        }, {\n          text: '最近一个月',\n          onClick(picker) {\n            const end = new Date();\n            const start = new Date();\n            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);\n            picker.$emit('pick', [start, end]);\n          }\n        }, {\n          text: '最近三个月',\n          onClick(picker) {\n            const end = new Date();\n            const start = new Date();\n            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);\n            picker.$emit('pick', [start, end]);\n          }\n        }]\n      },\n      pickerOptions: {\n        disabledDate(time) {\n          return time.getTime() > Date.now();\n        },\n        shortcuts: [\n          {\n            text: \"今天\",\n            onClick(picker) {\n              picker.$emit(\"pick\", new Date());\n            },\n          },\n          {\n            text: \"昨天\",\n            onClick(picker) {\n              const date = new Date();\n              date.setTime(date.getTime() - 3600 * 1000 * 24);\n              picker.$emit(\"pick\", date);\n            },\n          },\n          {\n            text: \"一周前\",\n            onClick(picker) {\n              const date = new Date();\n              date.setTime(date.getTime() - 3600 * 1000 * 24 * 7);\n              picker.$emit(\"pick\", date);\n            },\n          },\n        ],\n      },\n      searchField: 'all', // 新增：当前选中的搜索字段，默认全字段\n      searchFieldOptions: [\n        { label: '全字段', value: 'all' },\n        { label: '项目编号', value: 'projectNo' },\n        { label: '项目名称', value: 'projectName' },\n        { label: '详细地址', value: 'address' },\n        { label: '招标单位', value: 'biddingCompany' },\n        { label: '授权公司', value: 'authCompany' }\n      ],\n      searchValue: '', // 新增：搜索内容\n      highlightFields: ['projectNo', 'projectName', 'address', 'biddingCompany', 'authCompany'], // 新增：高亮字段\n    };\n  },\n  activated() {\n    console.log(\"=report index==>>activated\");\n    this.getList();\n  },\n  created() {\n    this.getList();\n    this.getDicts(\"pr_operation_type\").then((response) => {\n      this.operationTypeOptions = response.data;\n      var opt = [];\n      opt.push({ id: 0, label: \"全部\" });\n      response.data.forEach((elem, index) => {\n        var obj = {};\n        obj.label = elem.dictLabel;\n        obj.id = elem.dictValue;\n        opt.push(obj);\n      });\n      var operationType = {};\n      operationType.label = \"操作类型\";\n      operationType.children = opt;\n      var operationTypes = [];\n      operationTypes.push(operationType);\n      this.operationTypeTree = operationTypes;\n    });\n    this.getDicts(\"pr_audit_status\").then((response) => {\n      var type = 0;\n      if (this.$store.state.user.roles) {\n        if (this.$store.state.user.roles.includes(\"common\")) {\n          type = 1;\n        }\n        if (this.$store.state.user.roles.includes(\"province_admin\")) {\n          type = 2;\n        }\n        if (this.$store.state.user.roles.includes(\"report_admin\")) {\n          type = 3;\n        }\n      }\n      this.auditStatusOptions = response.data;\n      var opt = [];\n      opt.push({ id: 9, label: \"全部\" });\n      if (type == 2 || type == 3) {\n        opt.push({ id: 10, label: \"未审批\" });\n      }\n      response.data.forEach((elem, index) => {\n        var obj = {};\n        obj.label = elem.dictLabel;\n        obj.id = elem.dictValue;\n        opt.push(obj);\n      });\n      var auditStatusTree = {};\n      auditStatusTree.label = \"审核状态\";\n      auditStatusTree.children = opt;\n      var auditStatusTrees = [];\n      auditStatusTrees.push(auditStatusTree);\n      this.auditStatusTree = auditStatusTrees;\n    });\n    this.getDicts(\"pr_edit_status\").then((response) => {\n      this.editStatusOptions = response.data;\n    });\n    // this.getDicts(\"pr_bidding_type\").then((response) => {\n    //   this.biddingTypeOptions = response.data;\n    // });\n    this.getDicts(\"pr_model\").then((response) => {\n      this.modelOption1 = response.data;\n      var opt = [];\n      response.data.forEach((elem, index) => {\n        var obj = {};\n        obj.label = elem.dictLabel;\n        obj.value = elem.dictValue;\n        opt.push(obj);\n      });\n      this.modelOptions = opt;\n    });\n    this.getDicts(\"pr_spec\").then((response) => {\n      this.specOption1 = response.data;\n      var opt = [];\n      response.data.forEach((elem, index) => {\n        var obj = {};\n        obj.label = elem.dictLabel;\n        obj.value = elem.dictValue;\n        opt.push(obj);\n      });\n      this.specOptions = opt;\n    });\n    this.getDicts(\"pr_info\").then((response) => {\n      this.requireInfoOption1 = response.data;\n      var opt = [];\n      response.data.forEach((elem, index) => {\n        var obj = {};\n        obj.label = elem.dictLabel;\n        obj.value = elem.dictValue;\n        opt.push(obj);\n      });\n      this.requireInfoOptions = opt;\n    });\n    this.getDicts(\"pr_province\").then((response) => {\n      this.belongProvinceOptions1 = response.data;\n      var opt = [];\n      response.data.forEach((elem, index) => {\n        var obj = {};\n        obj.label = elem.dictLabel;\n        obj.value = elem.dictValue;\n        opt.push(obj);\n      });\n      this.belongProvinceOptions = opt;\n    });\n    this.getDicts(\"pr_after_sale_year\").then((response) => {\n      this.afterSaleYearOptions1 = response.data;\n      var opt = [];\n      response.data.forEach((elem, index) => {\n        var obj = {};\n        obj.label = elem.dictLabel;\n        obj.value = elem.dictValue;\n        opt.push(obj);\n      });\n      this.afterSaleYearOptions = opt;\n    });\n    this.getDicts(\"pr_data_type\").then((response) => {\n      this.infoTypeOptions = response.data;\n    });\n\n    var opt = [];\n    opt.push({ id: 0, label: \"全部\" });\n    opt.push({ id: 2, label: \"普通用户\" });\n    opt.push({ id: 10, label: \"省负责人\" });\n\n    var userType = {};\n    userType.label = \"所属用户\";\n    userType.children = opt;\n    var userTypes = [];\n    userTypes.push(userType);\n    this.userTypeTree = userTypes;\n    if (\n      this.$store.state.user.roles &&\n      this.$store.state.user.roles.includes(\"common\")\n    ) {\n      this.showUType = false;\n    }\n    if (this._isMobile()) {\n      this.isMobile = true;\n      this.pageLayout = \"total, prev, next, jumper\";\n    }\n    if (\n      this.$store.state.user.roles &&\n      this.$store.state.user.roles.includes(\"report_admin\")\n    ) {\n      this.isAdmin = false;\n    }\n  },\n  methods: {\n    _isMobile() {\n      let flag = navigator.userAgent.match(\n        /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i\n      );\n      return flag;\n    },\n    /** 查询项目报备列表 */\n    getList() {\n      this.loading = true;\n      listReport(this.queryParams).then((response) => {\n        this.reportList = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    handleDownload(url) {\n      const a = document.createElement(\"a\"); // 创建一个HTML 元素\n      a.setAttribute(\"target\", \"_blank\");\n      a.setAttribute(\"download\", \"\"); //download属性\n      const href =\n        \"https://report.clled.com/prod-api/common/download/resource?resource=\" +\n        url;\n      console.log(href);\n      a.setAttribute(\"href\", href); // href链接\n      a.click(); // 自执行点击事件\n    },\n    // 审核状态节点单击事件\n    handleAuditNodeClick(data) {\n      if (data.id == 9) {\n        this.queryParams.auditStatus = undefined;\n        this.queryParams.node = undefined;\n        this.queryParams.spare1 = undefined;\n      } else {\n        if (data.id == 1 || data.id == 10) {\n          this.queryParams.auditStatus = 1;\n          if (this.$store.state.user.roles) {\n            if (this.$store.state.user.roles.includes(\"province_admin\")) {\n              this.queryParams.node = \"省负责人\";\n              if (data.id == 10) {\n                this.queryParams.spare1 = \"=\";\n              } else {\n                this.queryParams.spare1 = \"!=\";\n              }\n            }\n            if (this.$store.state.user.roles.includes(\"report_admin\")) {\n              this.queryParams.node = \"审核员\";\n              if (data.id == 10) {\n                this.queryParams.spare1 = \"=\";\n              } else {\n                this.queryParams.spare1 = \"!=\";\n              }\n            }\n          }\n        } else {\n          this.queryParams.auditStatus = data.id;\n          this.queryParams.node = undefined;\n          this.queryParams.spare1 = undefined;\n        }\n      }\n      this.getList();\n    },\n    // 操作类型节点单击事件\n    handleOptNodeClick(data) {\n      if (data.id == 0) {\n        this.queryParams.operationType = undefined;\n      } else {\n        this.queryParams.operationType = data.id;\n      }\n      this.getList();\n    },\n    // 用户类型节点单击事件\n    handleUserNodeClick(data) {\n      if (data.id == 0) {\n        this.queryParams.userType = undefined;\n      } else {\n        this.queryParams.userType = data.id;\n      }\n      this.getList();\n    },\n    // 筛选节点\n    filterNode(value, data) {\n      if (!value) return true;\n      return data.label.indexOf(value) !== -1;\n    },\n    searchFormat(row, column) {\n      if (\n        row.indexOf(this.queryParams.spare1) !== -1 &&\n        this.queryParams.spare1 !== \"\"\n      ) {\n        return row.replace(\n          this.queryParams.spare1,\n          '<font color=\"#f00\">' + this.queryParams.spare1 + \"</font>\"\n        );\n      } else {\n        return row;\n      }\n    },\n    // 操作类型字典翻译\n    operationTypeFormat(row, column) {\n      return this.selectDictLabel(this.operationTypeOptions, row.operationType);\n    },\n    // 审核状态字典翻译\n    auditStatusFormat(row, column) {\n      return this.selectDictLabel(this.auditStatusOptions, row.auditStatus);\n    },\n    // 编辑状态字典翻译\n    editStatusFormat(row, column) {\n      return this.selectDictLabel(this.editStatusOptions, row.editStatus);\n    },\n    // 招标方式字典翻译\n    biddingTypeFormat(row, column) {\n      return this.selectDictLabel(this.biddingTypeOptions, row.biddingType);\n    },\n    // 投标产品型号字典翻译\n    modelFormat(row, column) {\n      return this.selectDictLabels(this.modelOption1, row.model);\n    },\n    // 投标产品规格字典翻译\n    specFormat(row, column) {\n      return this.selectDictLabels(this.specOption1, row.spec);\n    },\n    // 所需资料字典翻译\n    requireInfoFormat(row, column) {\n      if (row.requireInfo) {\n        return this.selectDictLabels(this.requireInfoOption1, row.requireInfo);\n      }\n    },\n    // 资料类型字典翻译\n    infoTypeFormat(row, column) {\n      return this.selectDictLabels(this.infoTypeOptions, row.infoType);\n    },\n    // 所属省份字典翻译\n    belongProvinceFormat(row, column) {\n      return this.selectDictLabels(this.belongProvinceOptions1, row.belongProvince);\n    },\n    // 售后年限字典翻译\n    afterSaleYearFormat(row, column) {\n      return this.selectDictLabels(this.afterSaleYearOptions1, row.afterSaleYear);\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    viewOk() {\n      this.viewOpen = false;\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        projectId: null,\n        projectNo: null,\n        projectName: null,\n        operationType: null,\n        auditStatus: \"0\",\n        rejectReason: null,\n        province: null,\n        city: null,\n        district: null,\n        address: null,\n        editStatus: \"0\",\n        belongUser: null,\n        biddingCompany: null,\n        openDate: null,\n        biddingType: null,\n        budgetMoney: null,\n        authCompany: null,\n        biddingNet: null,\n        distributor: null,\n        model: [],\n        spec: [],\n        area: null,\n        authFile: null,\n        afterSaleFile: null,\n        requireInfo: [],\n        infoType: [],\n        scanFile: null,\n        sendAddress: null,\n        mailInfo: null,\n        expressInfo: null,\n        remark: null,\n        spare1: null,\n        spare2: null,\n      };\n      this.resetForm(\"form\");\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      // 清空所有相关字段\n      this.queryParams.projectNo = null;\n      this.queryParams.projectName = null;\n      this.queryParams.address = null;\n      this.queryParams.biddingCompany = null;\n      this.queryParams.authCompany = null;\n      this.queryParams.fullField = null;\n\n      if (this.searchField === 'all') {\n        this.queryParams.fullField = this.searchValue; // 假设后端 fullField 做全字段模糊\n      } else {\n        this.queryParams[this.searchField] = this.searchValue;\n      }\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.queryArea = [];\n      this.queryParams = {\n        pageNum: 1,\n        pageSize: 10,\n        projectNo: null,\n        projectName: null,\n        operationType: null,\n        auditStatus: null,\n        province: null,\n        userType: null,\n        belongUser: null,\n        spare1: null,\n        address: null,\n        biddingCompany: null,\n        authCompany: null,\n        fullField: null\n      };\n      this.searchField = 'all';\n      this.searchValue = '';\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map((item) => item.projectId);\n      this.single = selection.length !== 1;\n      this.multiple = !selection.length;\n      this.chooseOptType = selection[0].operationType;\n      this.chooseAuditStatus = selection[0].auditStatus;\n      this.chooseUserId = selection[0].userId;\n      this.chooseEditStatus = selection[0].editStatus;\n      this.chooseSpare2 = selection[0].spare2;\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      // this.reset();\n      // this.open = true;\n      // this.title = \"添加项目报备\";\n      this.$router.push({\n        path: \"/project/report/form\",\n        query: {\n          businessKey: undefined,\n          formEdit: true,\n        },\n      });\n    },\n    handleView(row) {\n      let that = this;\n      this.view = JSON.parse(JSON.stringify(row));\n      this.view.operationType = this.operationTypeFormat(row);\n      this.view.auditStatus = this.auditStatusFormat(row);\n      this.view.editStatus = this.editStatusFormat(row);\n      this.view.biddingType = this.biddingTypeFormat(row);\n      this.view.model = this.modelFormat(row);\n      this.view.spec = this.specFormat(row);\n      this.view.requireInfo = this.requireInfoFormat(row);\n      this.view.infoType = this.infoTypeFormat(row);\n      this.view.belongProvince = this.belongProvinceFormat(row);\n      this.view.afterSaleYear = this.afterSaleYearFormat(row);\n      if (row.requireInfos) {\n        this.selectDictLabels(this.requireInfoOption1, row.requireInfo);\n        //this.view.requireInfo =\n        // const infoList = this.view.requireInfo.split(\",\");\n        const half = Math.ceil(row.requireInfos.length / 2);\n\n        this.infoList1 = row.requireInfos.splice(0, half);\n        this.infoList2 = row.requireInfos.splice(-half);\n\n        // const tmpList1 = infoList.splice(0, half);\n        // const tmpList2 = infoList.splice(-half);\n        // tmpList1.forEach((element) => {\n        //   console.log(element);\n        // });\n        // 循环对象赋值\n      } else {\n        this.infoList1 = [];\n        this.infoList2 = [];\n      }\n\n      if (row.operationType == \"2\" && row.spare1 == \"1\") {\n        this.defKey = \"process_project_auth\";\n        this.title = \"查看项目报备转授权\";\n      } else {\n        this.defKey = \"process_project_report\";\n        this.title = \"查看项目报备/授权\";\n      }\n      const params = { bizKey: row.projectId, defKey: this.defKey };\n      getInsIdByBizKey(params).then((resp) => {\n        this.bizKey = row.projectId;\n        if (resp.data && resp.data.instanceId) {\n          this.procInsId = resp.data.instanceId;\n          this.taskId = resp.data.taskId;\n          //console.log(\"==handleView=>>\")\n          //console.log(resp.data)\n          if (\n            resp.data.instanceId &&\n            !resp.data.endTime &&\n            resp.data.assignee == this.$store.state.user.userId\n          ) {\n            if (\n              this.$store.state.user.roles &&\n              this.$store.state.user.roles.includes(\"report_admin\") &&\n              row.operationType == \"2\"\n            ) {\n              this.isAuthImages = true;\n            }\n            this.finished = false;\n          } else if (\n            this.$store.state.user.roles &&\n            this.$store.state.user.roles.includes(\"report_admin\") &&\n            row.node == \"审核员\"\n          ) {\n            if (row.operationType == \"2\") {\n              this.isAuthImages = true;\n            }\n            //审核员角色不控制谁操作\n            this.finished = false;\n          } else {\n            this.finished = true;\n          }\n        } else {\n          this.finished = true;\n          this.procInsId = undefined;\n          this.taskId = undefined;\n        }\n\n        // console.log(\"====>>>驳回\")\n        // //驳回用户\n        // if(row.auditStatus == '3' && row.userId == this.$store.state.user.userId){\n        //   this.finished = false;\n        // }\n        // console.log(\"====>>>驳回：\" + this.finished)\n\n        this.viewOpen = true;\n      });\n      getLikeList({\n        projectName: row.projectName,\n        projectId: row.projectId,\n      }).then((resp) => {\n        //console.log(resp)\n        if (resp.data && resp.data.length > 0) {\n          this.likeList = resp.data;\n          that.likecount = resp.data.length;\n        } else {\n          this.likeList = undefined;\n          that.likecount = undefined;\n        }\n      });\n    },\n    /** 授权按钮操作 */\n    handleAuth(row) {\n      const loading = this.$loading({\n        lock: true,\n        text: \"授权中...\",\n        spinner: \"el-icon-loading\",\n        background: \"rgba(0, 0, 0, 0.7)\",\n      });\n      authReport(row)\n        .then((resp) => {\n          loading.close();\n          this.msgSuccess(resp.msg);\n          this.$router.go(0);\n        })\n        .catch((e) => {\n          loading.close();\n        });\n    },\n    /** 修改按钮操作 */\n    handleUpdate() {\n      let that = this;\n      // that.auditStatusEdit = true;\n      // if(!that.isAdmin && that.chooseAuditStatus == 3){\n      //   that.auditStatusEdit = false;\n      // }else{}\n      //申请者\n      var isApply =\n        this.$store.state.user.roles &&\n        (this.$store.state.user.roles.includes(\"common\") ||\n          this.$store.state.user.roles.includes(\"province_admin\"));\n\n      if (isApply && this.chooseUserId != this.$store.state.user.userId) {\n        this.msgError(\"只能修改本人提交的项目\");\n        return;\n      }\n\n      if (this.chooseOptType == 2) {\n        if (isApply && this.chooseSpare2 != 1) {\n          this.msgError(\"授权被退回才能修改\");\n          return;\n        }\n      }\n      if (this.chooseAuditStatus == 3 && isApply) {\n        if (this.chooseEditStatus == \"0\") {\n          this.msgError(\"审批被驳回无法修改\");\n          return;\n        }\n      }\n      // if(this.chooseOptType == 1){\n      //   if(isApply && this.chooseUserId != this.$store.state.user.userId ){\n      //     this.msgError(\"只能修改本人提交的报备项目\");\n      //     return;\n      //   }\n      // }\n      this.reset();\n      const projectId = this.ids;\n      getReport(projectId).then((response) => {\n        this.form = response.data;\n        if (this.form.model) this.form.model = this.form.model.split(\",\");\n        else this.form.model = [];\n        if (this.form.requireInfo)\n          this.form.requireInfo = this.form.requireInfo.split(\",\");\n        else this.form.requireInfo = [];\n        if (this.form.infoType)\n          this.form.infoType = this.form.infoType.split(\",\");\n        else this.form.infoType = [];\n        if (this.form.spec) this.form.spec = this.form.spec.split(\",\");\n        else this.form.spec = [];\n\n        if (this.form.authCompany) {\n          that.authCompanys = [];\n          var array = this.form.authCompany.split(\",\");\n          array.forEach(function (e) {\n            that.authCompanys.push({\n              value: e,\n              key: Date.now(),\n            });\n          });\n          that.form.authCompany = that.authCompanys[0].value;\n          that.authCompanys.splice(0, 1);\n          //console.log(that.authCompanys)\n        } else this.authCompanys = [];\n\n        this.oldOperationType = response.data.operationType;\n\n        this.open = true;\n        this.title = \"修改项目报备\";\n        var provinces = response.data.province;\n        if (provinces.length > 0) {\n          var address = provinces.split(\"/\");\n          var citys = [];\n          // 省份\n          if (address.length > 0) citys.push(TextToCode[address[0]].code);\n          // 城市\n          if (address.length > 1)\n            citys.push(TextToCode[address[0]][address[1]].code);\n          // 地区\n          if (address.length > 2)\n            citys.push(TextToCode[address[0]][address[1]][address[2]].code);\n\n          this.selectedOptions = citys;\n        }\n      });\n    },\n    /** 提交按钮 */\n    submitForm() {\n      this.$refs[\"form\"].validate((valid) => {\n        if (valid) {\n          if (this.form.infoType.indexOf(\"1\") >= 0 && this.form.scanFile) {\n            let emailReg = /^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\\.[a-zA-Z0-9_-]+)+$/;\n            if (!emailReg.test(this.form.scanFile)) {\n              this.$message.error(\"资料接收方式邮箱格式错误\");\n              return;\n            }\n          }\n          if (this.form.operationType == 1) {\n            this.form.authFile = \"\";\n            this.form.afterSaleFile = \"\";\n          }\n          var formStr = JSON.stringify(this.form);\n          var formData = JSON.parse(formStr);\n          if (formData.model && formData.model.length > 0)\n            formData.model = formData.model.join(\",\");\n          else formData.model = undefined;\n          if (formData.requireInfo && formData.requireInfo.length > 0)\n            formData.requireInfo = formData.requireInfo.join(\",\");\n          else formData.requireInfo = undefined;\n          if (formData.infoType && formData.infoType.length > 0)\n            formData.infoType = formData.infoType.join(\",\");\n          else formData.infoType = undefined;\n          if (formData.spec && formData.spec.length > 0)\n            formData.spec = formData.spec.join(\",\");\n          else formData.spec = undefined;\n\n          //授权公司\n          if (this.authCompanys.length > 0) {\n            var array = new Array();\n            this.authCompanys.forEach(function (e) {\n              array.push(e.value);\n            });\n            formData.authCompany += \",\" + array.join(\",\");\n          }\n\n          if (formData.projectId != null) {\n            updateReport(formData).then((response) => {\n              this.msgSuccess(\"修改成功\");\n\n              if (this.oldOperationType == 1 && formData.operationType == 2) {\n                console.log(\"=====>>>报备改授权\");\n              }\n\n              this.open = false;\n              this.getList();\n            });\n          } else {\n            addReport(formData).then((response) => {\n              this.msgSuccess(\"新增成功\");\n              this.open = false;\n              this.getList();\n            });\n          }\n        }\n      });\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const projectIds = row.projectId || this.ids;\n      this.$confirm(\n        '是否确认删除项目报备编号为\"' + projectIds + '\"的数据项?',\n        \"警告\",\n        {\n          confirmButtonText: \"确定\",\n          cancelButtonText: \"取消\",\n          type: \"warning\",\n        }\n      )\n        .then(function () {\n          return delReport(projectIds);\n        })\n        .then(() => {\n          this.getList();\n          this.msgSuccess(\"删除成功\");\n        });\n    },\n    clickExport() {\n      this.showExport = true;\n    },\n    clickPrint() {\n      this.showPrint = true;\n    },\n    /** 导出按钮操作 */\n    handleExport(type) {\n      let loadingwin;\n      let that = this;\n      this.queryParams.spare1 = type;\n      var col = [];\n      this.columns.forEach((item) => {\n        if (item.visible) {\n          col.push(item.label);\n        }\n      });\n      this.queryParams.spare2 = col.join(\",\");\n      const queryParams = this.queryParams;\n      this.$confirm(\n        \"是否确认导出项目报备搜索结果\" +\n        (type == 0 ? \"本页\" : \"全部\") +\n        \"数据项?\",\n        \"警告\",\n        {\n          confirmButtonText: \"确定\",\n          cancelButtonText: \"取消\",\n          type: \"warning\",\n        }\n      )\n        .then(function () {\n          loadingwin = that.$loading({\n            lock: true, //lock的修改符--默认是false\n            text: \"导出中...\", //显示在加载图标下方的加载文案\n            spinner: \"el-icon-loading\", //自定义加载图标类名\n            background: \"rgba(0, 0, 0, 0.7)\", //遮罩层颜色\n            target: document.querySelector(\".app-wrapper\"), //loadin覆盖的dom元素节点\n          });\n          document.documentElement.style.overflowY = \"hidden\"; //禁止底层div滚动\n          return exportReport(queryParams);\n        })\n        .then((response) => {\n          this.download(response.msg);\n          this.showExport = false;\n          loadingwin.close();\n          document.documentElement.style.overflowY = \"auto\"; //允许底层div滚动\n        })\n        .catch(() => {\n          this.showExport = false;\n          loadingwin.close();\n          document.documentElement.style.overflowY = \"auto\"; //允许底层div滚动\n        });\n    },\n    /** 导入按钮操作 */\n    handleImport() {\n      this.upload.title = \"项目导入\";\n      this.upload.open = true;\n    },\n    /** 下载模板操作 */\n    importTemplate() {\n      importTemplate().then((response) => {\n        this.download(response.msg);\n      });\n    },\n    downloadSQS() {\n      this.download(\"海佳集团-授权书.docx\", false);\n    },\n    downloadCRH() {\n      this.download(\"海佳集团-售后服务承诺函.doc\", false);\n    },\n    // 文件上传中处理\n    handleFileUploadProgress(event, file, fileList) {\n      this.upload.isUploading = true;\n    },\n    // 文件上传成功处理\n    handleFileSuccess(response, file, fileList) {\n      this.upload.open = false;\n      this.upload.isUploading = false;\n      this.$refs.upload.clearFiles();\n      this.$alert(response.msg, \"导入结果\", { dangerouslyUseHTMLString: true });\n      this.getList();\n    },\n    // 提交上传文件\n    submitFileForm() {\n      this.$refs.upload.submit();\n    },\n    handleChange(value) {\n      if (!value || value.length == 0) {\n        this.selectedOptions = null;\n        this.form.province = undefined;\n        this.form.district = undefined;\n        return;\n      }\n      this.selectedOptions = value;\n      var txt = \"\";\n      value.forEach(function (item) {\n        txt += CodeToText[item] + \"/\";\n      });\n      if (txt.length > 1) {\n        txt = txt.substring(0, txt.length - 1);\n        this.form.district = this.$store.state.user.province;\n        this.form.province = txt;\n      } else {\n        this.form.province = undefined;\n        this.form.district = undefined;\n      }\n    },\n    handleQueryCityChange(value) {\n      this.queryArea = value;\n      var txt = \"\";\n      value.forEach(function (item) {\n        txt += CodeToText[item] + \"/\";\n      });\n      if (txt.length > 1) {\n        txt = txt.substring(0, txt.length - 1);\n        this.queryParams.province = txt;\n      } else {\n        this.queryParams.province = undefined;\n      }\n    },\n    handlePrint(type) {\n      this.queryParams.spare1 = type;\n      var properties = [];\n      //properties.push({field: 'index', displayName: '序号'});\n      properties.push({ field: \"projectId\", displayName: \"项目ID\" });\n      this.columns.forEach((item) => {\n        if (item.visible) {\n          properties.push({ field: item.key, displayName: item.label });\n        }\n      });\n      printReport(this.queryParams).then((response) => {\n        printJS({\n          printable: response.data,\n          type: \"json\",\n          properties: properties,\n          header: '<div style=\"text-align: center\"><h3>项目报备列表</h3></div>',\n          targetStyles: [\"*\"],\n          gridHeaderStyle:\n            \"margin-top:20px;border: 1px solid #000;text-align:center\",\n          gridStyle: \"border: 1px solid #000;text-align:center;min-width:50px;\",\n          style: \"@page {margin:0 10mm;margin-top:10mm;}\",\n        });\n      });\n      // printJS({\n      //   printable: \"printArea\",\n      //   type:'html',\n      //   header:null,\n      //   targetStyles:['*'],\n      //   style:\"@page {margin:0 10mm}\"\n      // })\n    },\n    // 删除 showNameCorlor 和 showNoCorlor 方法，新增高亮方法\n    highlightText(text, keyword) {\n      if (!keyword) return text;\n      // 全部高亮\n      return text ? text.replace(new RegExp(keyword, 'g'), `<font color=\"#f00\">${keyword}</font>`) : text;\n    },\n    highlightCell(field, text) {\n      if (this.searchField === 'all' && this.searchValue && this.highlightFields.includes(field)) {\n        return this.highlightText(text, this.searchValue);\n      }\n      if (this.searchField === field && this.searchValue) {\n        return this.highlightText(text, this.searchValue);\n      }\n      return text;\n    },\n    removeDomain(index) {\n      if (index !== -1) {\n        this.authCompanys.splice(index, 1);\n      }\n    },\n    addDomain() {\n      this.authCompanys.push({\n        value: \"\",\n        key: Date.now(),\n      });\n    },\n    userSearch(createBy) {\n      this.queryParams.belongUser = createBy;\n      this.handleQuery();\n    },\n    optTypeSearch(type) {\n      this.queryParams.operationType = type;\n      this.handleQuery();\n    },\n    optTypeChange(e) {\n      console.log(e);\n    },\n  },\n};\n</script>\n<style>\n@media screen and (min-width: 600px) {\n  .view-dialog {\n    width: 80% !important;\n    float: right;\n  }\n}\n\n@media screen and (max-width: 599px) {\n  .view-dialog {\n    width: 100% !important;\n    float: right;\n  }\n\n  .edit-dialog {\n    width: 100% !important;\n\n    margin-bottom: 0;\n    height: 100%;\n    overflow: auto;\n  }\n\n  .el-dialog:not(.is-fullscreen) {\n    margin-top: 0 !important;\n  }\n\n  .el-form .el-col {\n    width: 100% !important;\n  }\n\n  .info-type .el-row {\n    flex-direction: column;\n  }\n\n  .info-type .el-input {\n    width: 100% !important;\n  }\n\n  .mobile-width {\n    width: 100% !important;\n  }\n}\n\n.likeTip {\n  color: rgb(247, 11, 11);\n  font-size: 12px;\n}\n\n.el-loading-mask {\n  z-index: 2001 !important;\n}\n</style>\n"]}]}