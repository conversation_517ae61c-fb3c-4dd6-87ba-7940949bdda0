{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/components/FileUpload/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/components/FileUpload/index.vue", "mtime": 1718676532991}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}