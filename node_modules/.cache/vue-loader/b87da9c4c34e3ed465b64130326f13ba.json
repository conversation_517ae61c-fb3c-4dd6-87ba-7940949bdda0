{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Process/components/nodePanel/property/signal.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Process/components/nodePanel/property/signal.vue", "mtime": 1641277326000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["signal.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAoBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "signal.vue", "sourceRoot": "src/components/Process/components/nodePanel/property", "sourcesContent": ["<template>\n  <div>\n    <el-dialog\n      title=\"信号定义\"\n      :visible.sync=\"dialogVisible\"\n      width=\"700px\"\n      :close-on-click-modal=\"false\"\n      :close-on-press-escape=\"false\"\n      :show-close=\"false\"\n      @closed=\"$emit('close')\"\n    >\n      <x-form ref=\"xForm\" v-model=\"formData\" :config=\"formConfig\" />\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" size=\"medium\" @click=\"closeDialog\">确 定</el-button>\n      </span>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport mixinPanel from '../../../common/mixinPanel'\nexport default {\n  mixins: [mixinPanel],\n  data() {\n    return {\n      dialogVisible: true,\n      formData: {\n        signal: []\n      }\n    }\n  },\n  computed: {\n    formConfig() {\n    //   const _this = this\n      return {\n        inline: false,\n        item: [\n          {\n            xType: 'tabs',\n            tabs: [\n              {\n                label: '信号定义',\n                name: 'signal',\n                column: [\n                  {\n                    label: 'scope',\n                    name: 'scope',\n                    width: 180,\n                    rules: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],\n                    xType: 'select',\n                    dic: [\n                      { label: '全局', value: 'start' },\n                      { label: '流程实例', value: 'end' }\n                    ]\n                  },\n                  {\n                    label: 'id',\n                    name: 'id',\n                    width: 200,\n                    rules: [{ required: true, message: '请输入', trigger: ['blur', 'change'] }],\n                    xType: 'input'\n                  },\n                  {\n                    label: '名称',\n                    name: 'name',\n                    xType: 'input',\n                    rules: [{ required: true, message: '请输入', trigger: ['blur', 'change'] }]\n                  }\n                ]\n              }\n            ]\n          }\n        ]\n      }\n    }\n  },\n  mounted() {\n    // this.formData.signal = this.element.businessObject.extensionElements?.values.map(item => {\n    //   let type\n    //   if ('class' in item.$attrs) type = 'class'\n    //   if ('expression' in item.$attrs) type = 'expression'\n    //   if ('delegateExpression' in item.$attrs) type = 'delegateExpression'\n    //   return {\n    //     event: item.$attrs.event,\n    //     type: type,\n    //     className: item.$attrs[type]\n    //   }\n    // }) ?? []\n  },\n  methods: {\n    updateElement() {\n      if (this.formData.signal?.length) {\n        let extensionElements = this.element.businessObject.get('extensionElements')\n        if (!extensionElements) {\n          extensionElements = this.modeler.get('moddle').create('bpmn:signal')\n        }\n        const length = extensionElements.get('values').length\n        for (let i = 0; i < length; i++) {\n          // 清除旧值\n          extensionElements.get('values').pop()\n        }\n        this.updateProperties({ extensionElements: extensionElements })\n      } else {\n        const extensionElements = this.element.businessObject[`extensionElements`]\n        if (extensionElements) {\n          extensionElements.values = extensionElements.values?.filter(item => item.$type !== 'flowable:ExecutionListener')\n        }\n      }\n    },\n    closeDialog() {\n      this.$refs.xForm.validate().then(() => {\n        this.updateElement()\n        this.dialogVisible = false\n      }).catch(e => console.error(e))\n    }\n  }\n}\n</script>\n\n<style>\n.flow-containers  .el-badge__content.is-fixed {\n    top: 18px;\n}\n</style>\n"]}]}