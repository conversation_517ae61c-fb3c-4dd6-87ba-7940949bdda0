{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/flowable/task/record/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/flowable/task/record/index.vue", "mtime": 1668865255000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7Zmxvd1JlY29yZH0gZnJvbSAiQC9hcGkvZmxvd2FibGUvZmluaXNoZWQiOwppbXBvcnQgUGFyc2VyIGZyb20gJ0AvY29tcG9uZW50cy9wYXJzZXIvUGFyc2VyJwppbXBvcnQge2RlZmluaXRpb25TdGFydEJ5S2V5LCBnZXRQcm9jZXNzVmFyaWFibGVzLCByZWFkWG1sQnlLZXksIGdldEZsb3dWaWV3ZXJ9IGZyb20gIkAvYXBpL2Zsb3dhYmxlL2RlZmluaXRpb24iOwppbXBvcnQge2NvbXBsZXRlLCByZWplY3RUYXNrLCByZXR1cm5MaXN0LCByZXR1cm5UYXNrLCBnZXROZXh0Rmxvd05vZGUsIGRlbGVnYXRlfSBmcm9tICJAL2FwaS9mbG93YWJsZS90b2RvIjsKaW1wb3J0IGZsb3cgZnJvbSAnQC92aWV3cy9mbG93YWJsZS90YXNrL3JlY29yZC9mbG93JwppbXBvcnQge3RyZWVzZWxlY3R9IGZyb20gIkAvYXBpL3N5c3RlbS9kZXB0IjsKaW1wb3J0ICJAcmlvcGhhZS92dWUtdHJlZXNlbGVjdC9kaXN0L3Z1ZS10cmVlc2VsZWN0LmNzcyI7CmltcG9ydCBUcmVlc2VsZWN0IGZyb20gIkByaW9waGFlL3Z1ZS10cmVlc2VsZWN0IjsKaW1wb3J0IHtsaXN0VXNlcn0gZnJvbSAiQC9hcGkvc3lzdGVtL3VzZXIiOwppbXBvcnQgbW9tZW50IGZyb20gJ21vbWVudCc7CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiUmVjb3JkIiwKICBjb21wb25lbnRzOiB7CiAgICBQYXJzZXIsCiAgICBmbG93LAogICAgVHJlZXNlbGVjdAogIH0sCiAgcHJvcHM6IHsKICAgIHByb2NEZWZLZXk6IHsKICAgICAgdHlwZTogU3RyaW5nLAogICAgICBkZWZhdWx0OiB1bmRlZmluZWQKICAgIH0sCiAgICB0YXNrSWQ6IHsKICAgICAgdHlwZTogU3RyaW5nLAogICAgICBkZWZhdWx0OiB1bmRlZmluZWQKICAgIH0sCiAgICBwcm9jSW5zSWQ6IHsKICAgICAgdHlwZTogU3RyaW5nLAogICAgICBkZWZhdWx0OiB1bmRlZmluZWQKICAgIH0sCiAgfSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgLy8g5qih5Z6LeG1s5pWw5o2uCiAgICAgIHhtbERhdGE6ICIiLAogICAgICB0YXNrTGlzdDogW10sCiAgICAgIC8vIOmDqOmXqOWQjeensAogICAgICBkZXB0TmFtZTogdW5kZWZpbmVkLAogICAgICAvLyDpg6jpl6jmoJHpgInpobkKICAgICAgZGVwdE9wdGlvbnM6IHVuZGVmaW5lZCwKICAgICAgLy8g55So5oi36KGo5qC85pWw5o2uCiAgICAgIHVzZXJMaXN0OiBudWxsLAogICAgICBkZWZhdWx0UHJvcHM6IHsKICAgICAgICBjaGlsZHJlbjogImNoaWxkcmVuIiwKICAgICAgICBsYWJlbDogImxhYmVsIgogICAgICB9LAogICAgICAvLyDmn6Xor6Llj4LmlbAKICAgICAgcXVlcnlQYXJhbXM6IHsKICAgICAgICBkZXB0SWQ6IHVuZGVmaW5lZAogICAgICB9LAogICAgICAvLyDpga7nvanlsYIKICAgICAgbG9hZGluZzogdHJ1ZSwKICAgICAgZmxvd1JlY29yZExpc3Q6IFtdLCAvLyDmtYHnqIvmtYHovazmlbDmja4KICAgICAgZm9ybUNvbmZDb3B5OiB7fSwKICAgICAgc3JjOiBudWxsLAogICAgICBydWxlczoge30sIC8vIOihqOWNleagoemqjAogICAgICB2YXJpYWJsZXNGb3JtOiB7fSwgLy8g5rWB56iL5Y+Y6YeP5pWw5o2uCiAgICAgIHRhc2tGb3JtOnsKICAgICAgICByZXR1cm5UYXNrU2hvdzogZmFsc2UsIC8vIOaYr+WQpuWxleekuuWbnumAgOihqOWNlQogICAgICAgIGRlbGVnYXRlVGFza1Nob3c6IGZhbHNlLCAvLyDmmK/lkKblsZXnpLrlm57pgIDooajljZUKICAgICAgICBkZWZhdWx0VGFza1Nob3c6IHRydWUsIC8vIOm7mOiupOWkhOeQhgogICAgICAgIHNlbmRVc2VyU2hvdzogZmFsc2UsIC8vIOWuoeaJueeUqOaItwogICAgICAgIG11bHRpcGxlOiBmYWxzZSwKICAgICAgICBjb21tZW50OiIiLCAvLyDmhI/op4HlhoXlrrkKICAgICAgICBwcm9jSW5zSWQ6ICIiLCAvLyDmtYHnqIvlrp7kvovnvJblj7cKICAgICAgICBpbnN0YW5jZUlkOiAiIiwgLy8g5rWB56iL5a6e5L6L57yW5Y+3CiAgICAgICAgdGFza0lkOiAiIiAsLy8g5rWB56iL5Lu75Yqh57yW5Y+3CiAgICAgICAgcHJvY0RlZktleTogIiIsICAvLyDmtYHnqIvnvJblj7cKICAgICAgICB2YXJzOiAiIiwKICAgICAgICB0YXJnZXRLZXk6IiIKICAgICAgfSwKICAgICAgdXNlckRhdGFMaXN0OltdLCAvLyDmtYHnqIvlgJnpgInkuroKICAgICAgYXNzaWduZWU6IG51bGwsCiAgICAgIGZvcm1Db25mOiB7fSwgLy8g6buY6K6k6KGo5Y2V5pWw5o2uCiAgICAgIGZvcm1Db25mT3BlbjogZmFsc2UsIC8vIOaYr+WQpuWKoOi9vem7mOiupOihqOWNleaVsOaNrgogICAgICB2YXJpYWJsZXM6IFtdLCAvLyDmtYHnqIvlj5jph4/mlbDmja4KICAgICAgdmFyaWFibGVzRGF0YToge30sIC8vIOa1geeoi+WPmOmHj+aVsOaNrgogICAgICB2YXJpYWJsZU9wZW46IGZhbHNlLCAvLyDmmK/lkKbliqDovb3mtYHnqIvlj5jph4/mlbDmja4KICAgICAgcmV0dXJuVGFza0xpc3Q6IFtdLCAgLy8g5Zue6YCA5YiX6KGo5pWw5o2uCiAgICAgIGZpbmlzaGVkOiAnZmFsc2UnLAogICAgICBjb21wbGV0ZVRpdGxlOiBudWxsLAogICAgICBjb21wbGV0ZU9wZW46IGZhbHNlLAogICAgICByZXR1cm5UaXRsZTogbnVsbCwKICAgICAgcmV0dXJuT3BlbjogZmFsc2UsCiAgICAgIHJlamVjdE9wZW46IGZhbHNlLAogICAgICByZWplY3RUaXRsZTogbnVsbCwKICAgICAgdXNlckRhdGE6W10sCiAgICB9OwogIH0sCiAgY3JlYXRlZCgpIHsKICAgIGNvbnNvbGUubG9nKCI9PT09PT09PXJlY29yZD09PT09PT09Y3JlYXRlZD0+Pj4iKQogICAgY29uc29sZS5sb2codGhpcy5fcHJvcHMpCiAgICBsZXQge3Rhc2tJZCxwcm9jRGVmS2V5LHByb2NJbnNJZCxmaW5pc2hlZH09dGhpcy5fcHJvcHM7CiAgICB0aGlzLnRhc2tGb3JtLnRhc2tJZCAgPSB0YXNrSWQ7CiAgICB0aGlzLnRhc2tGb3JtLnByb2NJbnNJZCA9IHByb2NJbnNJZDsKICAgIHRoaXMudGFza0Zvcm0uaW5zdGFuY2VJZCA9IHByb2NJbnNJZDsKICAgIC8vIOWIneWni+WMluihqOWNlQogICAgdGhpcy50YXNrRm9ybS5wcm9jRGVmS2V5ICA9IHByb2NEZWZLZXk7CiAgICB0aGlzLmZpbmlzaGVkID0gZmluaXNoZWQ7CiAgICAvLyDlm57mmL7mtYHnqIvorrDlvZUKICAgIHRoaXMuZ2V0Rmxvd1ZpZXdlcih0aGlzLnRhc2tGb3JtLnByb2NJbnNJZCx0aGlzLnRhc2tGb3JtLnByb2NEZWZLZXkpOwogICAgLy8g5rWB56iL5Lu75Yqh6YeN6I635Y+W5Y+Y6YeP6KGo5Y2VCiAgICBpZiAodGhpcy50YXNrRm9ybS50YXNrSWQpewogICAgICB0aGlzLnByb2Nlc3NWYXJpYWJsZXMoIHRoaXMudGFza0Zvcm0udGFza0lkKQogICAgICB0aGlzLmdldE5leHRGbG93Tm9kZSh0aGlzLnRhc2tGb3JtLnRhc2tJZCkKICAgIH0KICAgIHRoaXMuZ2V0Rmxvd1JlY29yZExpc3QoIHRoaXMudGFza0Zvcm0ucHJvY0luc0lkKTsKICAgIHRoaXMuZmluaXNoZWQgPSAgdGhpcy4kcm91dGUucXVlcnkgJiYgdGhpcy4kcm91dGUucXVlcnkuZmluaXNoZWQKICB9LAogIGFjdGl2YXRlZCgpIHsKICAgIGNvbnNvbGUubG9nKCI9PT09PT09PXJlY29yZD09PT09PT09YWN0aXZhdGVkPT4+PiIpCiAgICBsZXQge3Rhc2tJZCxwcm9jRGVmS2V5LHByb2NJbnNJZCxmaW5pc2hlZH09dGhpcy5fcHJvcHM7CiAgICBjb25zb2xlLmxvZyh0aGlzLl9wcm9wcykKICAgIHRoaXMudGFza0Zvcm0udGFza0lkICA9IHRhc2tJZDsKICAgIHRoaXMudGFza0Zvcm0ucHJvY0luc0lkID0gcHJvY0luc0lkOwogICAgdGhpcy50YXNrRm9ybS5pbnN0YW5jZUlkID0gcHJvY0luc0lkOwogICAgLy8g5Yid5aeL5YyW6KGo5Y2VCiAgICB0aGlzLnRhc2tGb3JtLnByb2NEZWZLZXkgID0gcHJvY0RlZktleTsKICAgIHRoaXMuZmluaXNoZWQgPSBmaW5pc2hlZDsKICAgIC8vIOWbnuaYvua1geeoi+iusOW9lQogICAgdGhpcy5nZXRGbG93Vmlld2VyKHRoaXMudGFza0Zvcm0ucHJvY0luc0lkLHRoaXMudGFza0Zvcm0ucHJvY0RlZktleSk7CiAgICAvLyDmtYHnqIvku7vliqHph43ojrflj5blj5jph4/ooajljZUKICAgIGlmICh0aGlzLnRhc2tGb3JtLnRhc2tJZCl7CiAgICAgIHRoaXMucHJvY2Vzc1ZhcmlhYmxlcyggdGhpcy50YXNrRm9ybS50YXNrSWQpCiAgICAgIHRoaXMuZ2V0TmV4dEZsb3dOb2RlKHRoaXMudGFza0Zvcm0udGFza0lkKQogICAgfQogICAgdGhpcy5nZXRGbG93UmVjb3JkTGlzdCggdGhpcy50YXNrRm9ybS5wcm9jSW5zSWQpOwogICAgdGhpcy5maW5pc2hlZCA9ICB0aGlzLiRyb3V0ZS5xdWVyeSAmJiB0aGlzLiRyb3V0ZS5xdWVyeS5maW5pc2hlZAogIH0sCiAgbW91bnRlZCgpIHsKICAgIC8vIC8vIOihqOWNleaVsOaNruWbnuWhq++8jOaooeaLn+W8guatpeivt+axguWcuuaZrwogICAgLy8gc2V0VGltZW91dCgoKSA9PiB7CiAgICAvLyAgIC8vIOivt+axguWbnuadpeeahOihqOWNleaVsOaNrgogICAgLy8gICBjb25zdCBkYXRhID0gewogICAgLy8gICAgIGZpZWxkMTAyOiAnMTg4MzY2NjI1NTUnCiAgICAvLyAgIH0KICAgIC8vICAgLy8g5Zue5aGr5pWw5o2uCiAgICAvLyAgIHRoaXMuZmlsbEZvcm1EYXRhKHRoaXMuZm9ybUNvbmYsIGRhdGEpCiAgICAvLyAgIC8vIOabtOaWsOihqOWNlQogICAgLy8gICB0aGlzLmtleSA9ICtuZXcgRGF0ZSgpLmdldFRpbWUoKQogICAgLy8gfSwgMTAwMCkKICB9LAogIG1ldGhvZHM6IHsKICAgIC8qKiDmn6Xor6Lpg6jpl6jkuIvmi4nmoJHnu5PmnoQgKi8KICAgIGdldFRyZWVzZWxlY3QoKSB7CiAgICAgIHRyZWVzZWxlY3QoKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLmRlcHRPcHRpb25zID0gcmVzcG9uc2UuZGF0YTsKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOafpeivoueUqOaIt+WIl+ihqCAqLwogICAgZ2V0TGlzdCgpIHsKICAgICAgbGlzdFVzZXIodGhpcy5hZGREYXRlUmFuZ2UodGhpcy5xdWVyeVBhcmFtcywgdGhpcy5kYXRlUmFuZ2UpKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICAgIHRoaXMudXNlckxpc3QgPSByZXNwb25zZS5yb3dzOwogICAgICAgICAgdGhpcy50b3RhbCA9IHJlc3BvbnNlLnRvdGFsOwogICAgICAgIH0KICAgICAgKTsKICAgIH0sCiAgICAvLyDnrZvpgInoioLngrkKICAgIGZpbHRlck5vZGUodmFsdWUsIGRhdGEpIHsKICAgICAgaWYgKCF2YWx1ZSkgcmV0dXJuIHRydWU7CiAgICAgIHJldHVybiBkYXRhLmxhYmVsLmluZGV4T2YodmFsdWUpICE9PSAtMTsKICAgIH0sCiAgICAvLyDoioLngrnljZXlh7vkuovku7YKICAgIGhhbmRsZU5vZGVDbGljayhkYXRhKSB7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMuZGVwdElkID0gZGF0YS5pZDsKICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICB9LAogICAgLyoqIHhtbCDmlofku7YgKi8KICAgIGdldE1vZGVsRGV0YWlsKGRlcGxveUtleSkgewogICAgICAvLyDlj5HpgIHor7fmsYLvvIzojrflj5Z4bWwKICAgICAgcmVhZFhtbEJ5S2V5KGRlcGxveUtleSkudGhlbihyZXMgPT4gewogICAgICAgIHRoaXMueG1sRGF0YSA9IHJlcy5kYXRhCiAgICAgIH0pCiAgICB9LAogICAgZ2V0Rmxvd1ZpZXdlcihwcm9jSW5zSWQsZGVwbG95S2V5KSB7CiAgICAgIGdldEZsb3dWaWV3ZXIocHJvY0luc0lkKS50aGVuKHJlcyA9PiB7CiAgICAgICAgdGhpcy50YXNrTGlzdCA9IHJlcy5kYXRhCiAgICAgICAgdGhpcy5nZXRNb2RlbERldGFpbChkZXBsb3lLZXkpOwogICAgICB9KQogICAgfSwKICAgIHNldEljb24odmFsKSB7CiAgICAgIGlmICh2YWwpIHsKICAgICAgICByZXR1cm4gImVsLWljb24tY2hlY2siOwogICAgICB9IGVsc2UgewogICAgICAgIHJldHVybiAiZWwtaWNvbi10aW1lIjsKICAgICAgfQogICAgfSwKICAgIHNldENvbG9yKHZhbCkgewogICAgICBpZiAodmFsKSB7CiAgICAgICAgcmV0dXJuICIjMmJjNDE4IjsKICAgICAgfSBlbHNlIHsKICAgICAgICByZXR1cm4gIiNiM2JkYmIiOwogICAgICB9CiAgICB9LAogICAgLy8g5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7CiAgICAgIHRoaXMudXNlckRhdGEgPSBzZWxlY3Rpb24KICAgICAgY29uc3QgdmFsID0gc2VsZWN0aW9uLm1hcChpdGVtID0+IGl0ZW0udXNlcklkKVswXTsKICAgICAgaWYgKHZhbCBpbnN0YW5jZW9mIEFycmF5KSB7CiAgICAgICAgdGhpcy50YXNrRm9ybS52YWx1ZXMgPSB7CiAgICAgICAgICAiYXBwcm92YWwiOiB2YWwuam9pbignLCcpCiAgICAgICAgfQogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMudGFza0Zvcm0udmFsdWVzID0gewogICAgICAgICAgImFwcHJvdmFsIjogdmFsCiAgICAgICAgfQogICAgICB9CiAgICB9LAogICAgLy8g5YWz6Zet5qCH562+CiAgICBoYW5kbGVDbG9zZSh0YWcpIHsKICAgICAgdGhpcy51c2VyRGF0YS5zcGxpY2UodGhpcy51c2VyRGF0YS5pbmRleE9mKHRhZyksIDEpOwogICAgfSwKICAgIC8qKiDmtYHnqIvlj5jph4/otYvlgLwgKi8KICAgIGhhbmRsZUNoZWNrQ2hhbmdlKHZhbCkgewogICAgICBpZiAodmFsIGluc3RhbmNlb2YgQXJyYXkpIHsKICAgICAgICB0aGlzLnRhc2tGb3JtLnZhbHVlcyA9IHsKICAgICAgICAgICJhcHByb3ZhbCI6IHZhbC5qb2luKCcsJykKICAgICAgICB9CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy50YXNrRm9ybS52YWx1ZXMgPSB7CiAgICAgICAgICAiYXBwcm92YWwiOiB2YWwKICAgICAgICB9CiAgICAgIH0KICAgIH0sCiAgICAvKiog5rWB56iL5rWB6L2s6K6w5b2VICovCiAgICBnZXRGbG93UmVjb3JkTGlzdChwcm9jSW5zSWQpIHsKICAgICAgY29uc3QgcGFyYW1zID0ge3Byb2NJbnNJZDogcHJvY0luc0lkfQogICAgICBmbG93UmVjb3JkKHBhcmFtcykudGhlbihyZXMgPT4gewogICAgICAgIHRoaXMuZmxvd1JlY29yZExpc3QgPSByZXMuZGF0YS5mbG93TGlzdDsKICAgICAgICAvLyDmtYHnqIvov4fnqIvkuK3kuI3lrZjlnKjliJ3lp4vljJbooajljZUg55u05o6l6K+75Y+W55qE5rWB56iL5Y+Y6YeP5Lit5a2Y5YKo55qE6KGo5Y2V5YC8CiAgICAgICAgaWYgKHJlcy5kYXRhLmZvcm1EYXRhKSB7CiAgICAgICAgICB0aGlzLmZvcm1Db25mID0gcmVzLmRhdGEuZm9ybURhdGE7CiAgICAgICAgICB0aGlzLmZvcm1Db25mT3BlbiA9IHRydWUKICAgICAgICB9CiAgICAgIH0pLmNhdGNoKHJlcyA9PiB7CiAgICAgICAgdGhpcy5nb0JhY2soKTsKICAgICAgfSkKICAgIH0sCiAgICBmaWxsRm9ybURhdGEoZm9ybSwgZGF0YSkgewogICAgICBmb3JtLmZpZWxkcy5mb3JFYWNoKGl0ZW0gPT4gewogICAgICAgIGNvbnN0IHZhbCA9IGRhdGFbaXRlbS5fX3ZNb2RlbF9fXQogICAgICAgIGlmICh2YWwpIHsKICAgICAgICAgIGl0ZW0uX19jb25maWdfXy5kZWZhdWx0VmFsdWUgPSB2YWwKICAgICAgICB9CiAgICAgIH0pCiAgICB9LAogICAgLyoqIOiOt+WPlua1geeoi+WPmOmHj+WGheWuuSAqLwogICAgcHJvY2Vzc1ZhcmlhYmxlcyh0YXNrSWQpIHsKICAgICAgaWYgKHRhc2tJZCkgewogICAgICAgIC8vIOaPkOS6pOa1geeoi+eUs+ivt+aXtuWhq+WGmeeahOihqOWNleWtmOWFpeS6hua1geeoi+WPmOmHj+S4reWQjue7reS7u+WKoeWkhOeQhuaXtumcgOimgeWxleekugogICAgICAgIGdldFByb2Nlc3NWYXJpYWJsZXModGFza0lkKS50aGVuKHJlcyA9PiB7CiAgICAgICAgICAvLyB0aGlzLnZhcmlhYmxlcyA9IHJlcy5kYXRhLnZhcmlhYmxlczsKICAgICAgICAgIHRoaXMudmFyaWFibGVzRGF0YSA9IHJlcy5kYXRhLnZhcmlhYmxlczsKICAgICAgICAgIHRoaXMudmFyaWFibGVPcGVuID0gdHJ1ZQogICAgICAgIH0pOwogICAgICB9CiAgICB9LAogICAgLyoqIOagueaNruW9k+WJjeS7u+WKoeaIluiAhea1geeoi+iuvuiuoemFjee9rueahOS4i+S4gOatpeiKgueCuSAqLwogICAgZ2V0TmV4dEZsb3dOb2RlKHRhc2tJZCkgewogICAgICAvLyDmoLnmja7lvZPliY3ku7vliqHmiJbogIXmtYHnqIvorr7orqHphY3nva7nmoTkuIvkuIDmraXoioLngrkgdG9kbyDmmoLml7bmnKrmtonlj4rliLDogIPomZHnvZHlhbPjgIHooajovr7lvI/lkozlpJroioLngrnmg4XlhrUKICAgICAgY29uc3QgcGFyYW1zID0ge3Rhc2tJZDogdGFza0lkfQogICAgICBnZXROZXh0Rmxvd05vZGUocGFyYW1zKS50aGVuKHJlcyA9PiB7CiAgICAgICAgY29uc3QgZGF0YSA9IHJlcy5kYXRhOwogICAgICAgIGlmIChkYXRhKSB7CiAgICAgICAgICBpZiAoZGF0YS50eXBlID09PSAnYXNzaWduZWUnKSB7CiAgICAgICAgICAgIHRoaXMudXNlckRhdGFMaXN0ID0gcmVzLmRhdGEudXNlckxpc3Q7CiAgICAgICAgICB9IGVsc2UgaWYgKGRhdGEudHlwZSA9PT0gJ2NhbmRpZGF0ZVVzZXJzJykgewogICAgICAgICAgICB0aGlzLnVzZXJEYXRhTGlzdCA9IHJlcy5kYXRhLnVzZXJMaXN0OwogICAgICAgICAgICB0aGlzLnRhc2tGb3JtLm11bHRpcGxlID0gdHJ1ZTsKICAgICAgICAgIH0gZWxzZSBpZiAoZGF0YS50eXBlID09PSAnY2FuZGlkYXRlR3JvdXBzJykgewogICAgICAgICAgICByZXMuZGF0YS5yb2xlTGlzdC5mb3JFYWNoKHJvbGUgPT4gewogICAgICAgICAgICAgIHJvbGUudXNlcklkID0gcm9sZS5yb2xlSWQ7CiAgICAgICAgICAgICAgcm9sZS5uaWNrTmFtZSA9IHJvbGUucm9sZU5hbWU7CiAgICAgICAgICAgIH0pCiAgICAgICAgICAgIHRoaXMudXNlckRhdGFMaXN0ID0gcmVzLmRhdGEucm9sZUxpc3Q7CiAgICAgICAgICAgIHRoaXMudGFza0Zvcm0ubXVsdGlwbGUgPSBmYWxzZTsKICAgICAgICAgIH0gZWxzZSBpZiAoZGF0YS50eXBlID09PSAnbXVsdGlJbnN0YW5jZScpIHsKICAgICAgICAgICAgdGhpcy51c2VyRGF0YUxpc3QgPSByZXMuZGF0YS51c2VyTGlzdDsKICAgICAgICAgICAgdGhpcy50YXNrRm9ybS5tdWx0aXBsZSA9IHRydWU7CiAgICAgICAgICB9CiAgICAgICAgICB0aGlzLnRhc2tGb3JtLnNlbmRVc2VyU2hvdyA9IHRydWU7CiAgICAgICAgfQogICAgICB9KQogICAgfSwKICAgIC8qKiDlrqHmibnku7vliqHpgInmi6kgKi8KICAgIGhhbmRsZUNvbXBsZXRlKCkgewogICAgICB0aGlzLmNvbXBsZXRlT3BlbiA9IHRydWU7CiAgICAgIHRoaXMuY29tcGxldGVUaXRsZSA9ICLlrqHmibnmtYHnqIsiOwogICAgICAvL3RoaXMuZ2V0VHJlZXNlbGVjdCgpOwogICAgfSwKICAgIC8qKiDlrqHmibnku7vliqEgKi8KICAgIHRhc2tDb21wbGV0ZShjb21tZW50KSB7CiAgICAgIC8vIGlmICghdGhpcy50YXNrRm9ybS52YWx1ZXMpewogICAgICAvLyAgIHRoaXMubXNnRXJyb3IoIuivt+mAieaLqea1geeoi+aOpeaUtuS6uuWRmCIpOwogICAgICAvLyAgIHJldHVybjsKICAgICAgLy8gfQogICAgICBpZihjb21tZW50ICYmICh0eXBlb2YgY29tbWVudD09J3N0cmluZycpJiZjb21tZW50LmNvbnN0cnVjdG9yPT1TdHJpbmcpewogICAgICAgIHRoaXMudGFza0Zvcm0uY29tbWVudCA9IGNvbW1lbnQ7CiAgICAgIH0gCiAgICAgIGlmICghdGhpcy50YXNrRm9ybS5jb21tZW50KXsKICAgICAgICB0aGlzLm1zZ0Vycm9yKCLor7fovpPlhaXlrqHmibnmhI/op4EiKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgY29tcGxldGUodGhpcy50YXNrRm9ybSkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5tc2dTdWNjZXNzKHJlc3BvbnNlLm1zZyk7CiAgICAgICAgdGhpcy5nb0JhY2soKTsKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOWnlOa0vuS7u+WKoSAqLwogICAgaGFuZGxlRGVsZWdhdGUoKSB7CiAgICAgIHRoaXMudGFza0Zvcm0uZGVsZWdhdGVUYXNrU2hvdyA9IHRydWU7CiAgICAgIHRoaXMudGFza0Zvcm0uZGVmYXVsdFRhc2tTaG93ID0gZmFsc2U7CiAgICB9LAogICAgaGFuZGxlQXNzaWduKCl7CgogICAgfSwKICAgIC8qKiDov5Tlm57pobXpnaIgKi8KICAgIGdvQmFjaygpIHsKICAgICAgLy8g5YWz6Zet5b2T5YmN5qCH562+6aG15bm26L+U5Zue5LiK5Liq6aG16Z2iCiAgICAgIHRoaXMuJHN0b3JlLmRpc3BhdGNoKCJ0YWdzVmlldy9kZWxWaWV3IiwgdGhpcy4kcm91dGUpOwogICAgICB0aGlzLiRyb3V0ZXIuZ28oLTEpCiAgICB9LAogICAgLyoqIOaOpeaUtuWtkOe7hOS7tuS8oOeahOWAvCAqLwogICAgZ2V0RGF0YShkYXRhKSB7CiAgICAgIGlmIChkYXRhKSB7CiAgICAgICAgY29uc3QgdmFyaWFibGVzID0gW107CiAgICAgICAgZGF0YS5maWVsZHMuZm9yRWFjaChpdGVtID0+IHsKICAgICAgICAgIGxldCB2YXJpYWJsZURhdGEgPSB7fTsKICAgICAgICAgIHZhcmlhYmxlRGF0YS5sYWJlbCA9IGl0ZW0uX19jb25maWdfXy5sYWJlbAogICAgICAgICAgLy8g6KGo5Y2V5YC85Li65aSa5Liq6YCJ6aG55pe2CiAgICAgICAgICBpZiAoaXRlbS5fX2NvbmZpZ19fLmRlZmF1bHRWYWx1ZSBpbnN0YW5jZW9mIEFycmF5KSB7CiAgICAgICAgICAgIGNvbnN0IGFycmF5ID0gW107CiAgICAgICAgICAgIGl0ZW0uX19jb25maWdfXy5kZWZhdWx0VmFsdWUuZm9yRWFjaCh2YWwgPT4gewogICAgICAgICAgICAgIGFycmF5LnB1c2godmFsKQogICAgICAgICAgICB9KQogICAgICAgICAgICB2YXJpYWJsZURhdGEudmFsID0gYXJyYXk7CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICB2YXJpYWJsZURhdGEudmFsID0gaXRlbS5fX2NvbmZpZ19fLmRlZmF1bHRWYWx1ZQogICAgICAgICAgfQogICAgICAgICAgdmFyaWFibGVzLnB1c2godmFyaWFibGVEYXRhKQogICAgICAgIH0pCiAgICAgICAgdGhpcy52YXJpYWJsZXMgPSB2YXJpYWJsZXM7CiAgICAgIH0KICAgIH0sCiAgICAvKiog55Sz6K+35rWB56iL6KGo5Y2V5pWw5o2u5o+Q5LqkLS0tLeayoeeUqCAqLwogICAgLy8gc3VibWl0Rm9ybShkYXRhKSB7CiAgICAvLyAgIGlmIChkYXRhKSB7CiAgICAvLyAgICAgY29uc3QgdmFyaWFibGVzID0gZGF0YS52YWxEYXRhOwogICAgLy8gICAgIGNvbnN0IGZvcm1EYXRhID0gZGF0YS5mb3JtRGF0YTsKICAgIC8vICAgICBmb3JtRGF0YS5kaXNhYmxlZCA9IHRydWU7CiAgICAvLyAgICAgZm9ybURhdGEuZm9ybUJ0bnMgPSBmYWxzZTsKICAgIC8vICAgICBpZiAodGhpcy50YXNrRm9ybS5wcm9jRGVmS2V5KSB7CiAgICAvLyAgICAgICB2YXJpYWJsZXMudmFyaWFibGVzID0gZm9ybURhdGE7CiAgICAvLyAgICAgICB2YXJpYWJsZXMuYnVzaW5lc3NLZXkgPSBkYXRhLmJ1c2luZXNzS2V5OyAgICAgICAgICAKICAgIC8vICAgICAgICAvLyDlkK/liqjmtYHnqIvlubblsIbooajljZXmlbDmja7liqDlhaXmtYHnqIvlj5jph48KICAgIC8vICAgICAgIGRlZmluaXRpb25TdGFydEJ5S2V5KHRoaXMudGFza0Zvcm0ucHJvY0RlZktleSwgSlNPTi5zdHJpbmdpZnkodmFyaWFibGVzKSkudGhlbihyZXMgPT4gewogICAgLy8gICAgICAgICB0aGlzLm1zZ1N1Y2Nlc3MocmVzLm1zZyk7CiAgICAvLyAgICAgICAgIHRoaXMuZ29CYWNrKCk7CiAgICAvLyAgICAgICB9KQogICAgLy8gICAgIH0KICAgIC8vICAgfQogICAgLy8gfSwKICAgIHN0YXJ0RmxvdyhidXNpbmVzc0tleSwgbmFtZSwgdmFyaWFibGVzKSB7CiAgICAgIGxldCBzdGFydERhdGUgPSBtb21lbnQobmV3IERhdGUoKSkuZm9ybWF0KCdZWVlZTU1EREhIbW1zcycpOwogICAgICBjb25zdCBkYXRhID0ge30KICAgICAgaWYgKHRoaXMudGFza0Zvcm0ucHJvY0RlZktleSkgewogICAgICAgIGlmKCF2YXJpYWJsZXMpewogICAgICAgICAgZGF0YS52YXJpYWJsZXMgPSB7fTsKICAgICAgICB9ZWxzZXsKICAgICAgICAgIGRhdGEudmFyaWFibGVzID0gdmFyaWFibGVzOwogICAgICAgIH0KICAgICAgICAKICAgICAgICBkYXRhLmJ1c2luZXNzS2V5ID0gYnVzaW5lc3NLZXk7CiAgICAgICAgZGF0YS5wcm9jRGVmS2V5ID0gdGhpcy50YXNrRm9ybS5wcm9jRGVmS2V5OwogICAgICAgIGRhdGEudGFza05hbWUgPSBuYW1lOwogICAgICAgICAgLy8g5ZCv5Yqo5rWB56iL5bm25bCG6KGo5Y2V5pWw5o2u5Yqg5YWl5rWB56iL5Y+Y6YePCiAgICAgICAgZGVmaW5pdGlvblN0YXJ0QnlLZXkoSlNPTi5zdHJpbmdpZnkoZGF0YSkpLnRoZW4ocmVzID0+IHsKICAgICAgICAgIHRoaXMubXNnU3VjY2VzcyhyZXMubXNnKTsKICAgICAgICAgIHRoaXMuZ29CYWNrKCk7CiAgICAgICAgfSkKICAgICAgfQogICAgICAKICAgIH0sCiAgICAvKiog6amz5Zue5Lu75YqhICovCiAgICBoYW5kbGVSZWplY3QoKSB7CiAgICAgIHRoaXMucmVqZWN0T3BlbiA9IHRydWU7CiAgICAgIHRoaXMucmVqZWN0VGl0bGUgPSAi6amz5Zue5rWB56iLIjsKICAgIH0sCiAgICAvKiog6amz5Zue5Lu75YqhICovCiAgICB0YXNrUmVqZWN0KCkgewogICAgICB0aGlzLiRyZWZzWyJ0YXNrRm9ybSJdLnZhbGlkYXRlKHZhbGlkID0+IHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIHJlamVjdFRhc2sodGhpcy50YXNrRm9ybSkudGhlbihyZXMgPT4gewogICAgICAgICAgICB0aGlzLm1zZ1N1Y2Nlc3MocmVzLm1zZyk7CiAgICAgICAgICAgIHRoaXMuZ29CYWNrKCk7CiAgICAgICAgICB9KTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDlj6/pgIDlm57ku7vliqHliJfooaggKi8KICAgIGhhbmRsZVJldHVybigpIHsKICAgICAgdGhpcy5yZXR1cm5PcGVuID0gdHJ1ZTsKICAgICAgdGhpcy5yZXR1cm5UaXRsZSA9ICLpgIDlm57mtYHnqIsiOwogICAgICByZXR1cm5MaXN0KHRoaXMudGFza0Zvcm0pLnRoZW4ocmVzID0+IHsKICAgICAgICB0aGlzLnJldHVyblRhc2tMaXN0ID0gcmVzLmRhdGE7CiAgICAgICAgdGhpcy50YXNrRm9ybS52YWx1ZXMgPSBudWxsOwogICAgICB9KQogICAgfSwKICAgIC8qKiDmj5DkuqTpgIDlm57ku7vliqEgKi8KICAgdGFza1JldHVybigpIHsKICAgICAgdGhpcy4kcmVmc1sidGFza0Zvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICByZXR1cm5UYXNrKHRoaXMudGFza0Zvcm0pLnRoZW4ocmVzID0+IHsKICAgICAgICAgICAgdGhpcy5tc2dTdWNjZXNzKHJlcy5tc2cpOwogICAgICAgICAgICB0aGlzLmdvQmFjaygpCiAgICAgICAgICB9KTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDlj5bmtojlm57pgIDku7vliqHmjInpkq4gKi8KICAgIGNhbmNlbFRhc2soKSB7CiAgICAgIHRoaXMudGFza0Zvcm0ucmV0dXJuVGFza1Nob3cgPSBmYWxzZTsKICAgICAgdGhpcy50YXNrRm9ybS5kZWZhdWx0VGFza1Nob3cgPSB0cnVlOwogICAgICB0aGlzLnRhc2tGb3JtLnNlbmRVc2VyU2hvdyA9IHRydWU7CiAgICAgIHRoaXMucmV0dXJuVGFza0xpc3QgPSBbXTsKICAgIH0sCiAgICAvKiog5aeU5rS+5Lu75YqhICovCiAgICBzdWJtaXREZWxldGVUYXNrKCkgewogICAgICB0aGlzLiRyZWZzWyJ0YXNrRm9ybSJdLnZhbGlkYXRlKHZhbGlkID0+IHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIGRlbGVnYXRlKHRoaXMudGFza0Zvcm0pLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgICAgICB0aGlzLm1zZ1N1Y2Nlc3MocmVzcG9uc2UubXNnKTsKICAgICAgICAgICAgdGhpcy5nb0JhY2soKTsKICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLyoqIOWPlua2iOWbnumAgOS7u+WKoeaMiemSriAqLwogICAgY2FuY2VsRGVsZWdhdGVUYXNrKCkgewogICAgICB0aGlzLnRhc2tGb3JtLmRlbGVnYXRlVGFza1Nob3cgPSBmYWxzZTsKICAgICAgdGhpcy50YXNrRm9ybS5kZWZhdWx0VGFza1Nob3cgPSB0cnVlOwogICAgICB0aGlzLnRhc2tGb3JtLnNlbmRVc2VyU2hvdyA9IHRydWU7CiAgICAgIHRoaXMucmV0dXJuVGFza0xpc3QgPSBbXTsKICAgIH0sCiAgfQp9Owo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuJA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/flowable/task/record", "sourcesContent": ["<template>\n  <div class=\"\">\n    <!--流程流转记录-->\n    <el-card class=\"box-card\" v-if=\"flowRecordList\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span class=\"el-icon-notebook-1\">审批记录</span>\n          </div>\n          <el-col :span=\"16\" :offset=\"4\" >\n            <div class=\"block\">\n              <el-timeline>\n                <el-timeline-item\n                  v-for=\"(item,index ) in flowRecordList\"\n                  :key=\"index\"\n                  :icon=\"setIcon(item.finishTime)\"\n                  :color=\"setColor(item.finishTime)\"\n                >\n                  <p style=\"font-weight: 700\">{{item.taskName}}</p>\n                  <el-card :body-style=\"{ padding: '10px' }\">\n                    <label v-if=\"item.assigneeName\" style=\"font-weight: normal;margin-right: 30px;\">实际办理： {{item.assigneeName}} <el-tag type=\"info\" size=\"mini\">{{item.deptName}}</el-tag></label>\n                    <label v-if=\"item.candidate\" style=\"font-weight: normal;margin-right: 30px;\">候选办理： {{item.candidate}}</label>\n                    <label style=\"font-weight: normal\">接收时间： </label><label style=\"color:#8a909c;font-weight: normal\">{{item.createTime}}</label>\n                    <label v-if=\"item.finishTime\" style=\"margin-left: 30px;font-weight: normal\">办结时间： </label><label style=\"color:#8a909c;font-weight: normal\">{{item.finishTime}}</label>\n                    <label v-if=\"item.duration\" style=\"margin-left: 30px;font-weight: normal\">耗时： </label><label style=\"color:#8a909c;font-weight: normal\">{{item.duration}}</label>\n\n                    <p  v-if=\"item.comment\">\n                      <el-tag type=\"success\" v-if=\"item.comment.type === '1'\">  {{item.comment.comment}}</el-tag>\n                      <el-tag type=\"warning\" v-if=\"item.comment.type === '2'\">  {{item.comment.comment}}</el-tag>\n                      <el-tag type=\"danger\" v-if=\"item.comment.type === '3'\">  {{item.comment.comment}}</el-tag>\n                    </p>\n                  </el-card>\n                </el-timeline-item>\n              </el-timeline>\n            </div>\n          </el-col>\n      </el-card>\n    <el-card class=\"box-card\">\n        <div slot=\"header\" class=\"clearfix\">\n          <span class=\"el-icon-picture-outline\">流程图</span>\n        </div>\n        <flow :xmlData=\"xmlData\" :taskData=\"taskList\"></flow>\n    </el-card>\n\n    <!--审批正常流程-->\n    <el-dialog :title=\"completeTitle\" visible.sync=\"false\" width=\"60%\" append-to-body>\n      <el-row :gutter=\"20\">\n        <!--部门数据-->\n        <el-col :span=\"4\" :xs=\"24\">\n          <h6>部门列表</h6>\n          <div class=\"head-container\">\n            <el-input\n              v-model=\"deptName\"\n              placeholder=\"请输入部门名称\"\n              clearable\n              size=\"small\"\n              prefix-icon=\"el-icon-search\"\n              style=\"margin-bottom: 20px\"\n            />\n          </div>\n          <div class=\"head-container\">\n            <el-tree\n              :data=\"deptOptions\"\n              :props=\"defaultProps\"\n              :expand-on-click-node=\"false\"\n              :filter-node-method=\"filterNode\"\n              ref=\"tree\"\n              default-expand-all\n              @node-click=\"handleNodeClick\"\n            />\n          </div>\n        </el-col>\n        <el-col :span=\"12\" :xs=\"24\">\n          <h6>待选人员</h6>\n          <el-table\n            ref=\"singleTable\"\n            :data=\"userList\"\n            border\n            style=\"width: 100%\"\n            @selection-change=\"handleSelectionChange\">\n            <el-table-column type=\"selection\" width=\"50\" align=\"center\" />\n            <el-table-column label=\"用户名\" align=\"center\" prop=\"nickName\" />\n            <el-table-column label=\"部门\" align=\"center\" prop=\"dept.deptName\" />\n          </el-table>\n        </el-col>\n        <el-col :span=\"8\" :xs=\"24\">\n          <h6>已选人员</h6>\n          <el-tag\n            v-for=\"tag in userData\"\n            :key=\"tag.nickName\"\n            closable\n            @close=\"handleClose(tag)\">\n            {{tag.nickName}} {{tag.dept.deptName}}\n          </el-tag>\n        </el-col>\n      </el-row>\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-input style=\"width: 50%;margin-right: 34%\" type=\"textarea\" v-model=\"taskForm.comment\" placeholder=\"请输入处理意见\"/>\n        <el-button @click=\"completeOpen = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"taskComplete\">确 定</el-button>\n      </span>\n    </el-dialog>\n\n    <el-dialog :title=\"completeTitle\" :visible.sync=\"completeOpen\" width=\"40%\" append-to-body>\n      <el-form ref=\"taskForm\" :model=\"taskForm\" label-width=\"80px\" >\n        <el-form-item label=\"审批意见\" prop=\"comment\" :rules=\"[{ required: true, message: '请输入处理意见', trigger: 'blur' }]\">\n          <el-input style=\"width: 50%\" type=\"textarea\" v-model=\"taskForm.comment\" placeholder=\"请输入处理意见\"/>\n        </el-form-item>\n      </el-form>\n      <span slot=\"footer\" class=\"dialog-footer\">\n          <el-button @click=\"completeOpen = false\">取 消</el-button>\n          <el-button type=\"primary\" @click=\"taskComplete\">确 定</el-button>\n        </span>\n    </el-dialog>\n\n    <!--退回流程-->\n    <el-dialog :title=\"returnTitle\" :visible.sync=\"returnOpen\" width=\"40%\" append-to-body>\n        <el-form ref=\"taskForm\" :model=\"taskForm\" label-width=\"80px\" >\n            <el-form-item label=\"退回节点\" prop=\"targetKey\" :rules=\"[{ required: true, message: '请选择退回节点', trigger: 'change' }]\">\n              <el-radio-group v-model=\"taskForm.targetKey\">\n                <el-radio-button\n                  v-for=\"item in returnTaskList\"\n                  :key=\"item.id\"\n                  :label=\"item.id\"\n                >{{item.name}}</el-radio-button>\n              </el-radio-group>\n            </el-form-item>\n          <el-form-item label=\"退回意见\" prop=\"comment\" :rules=\"[{ required: true, message: '请输入意见', trigger: 'blur' }]\">\n            <el-input style=\"width: 50%\" type=\"textarea\" v-model=\"taskForm.comment\" placeholder=\"请输入意见\"/>\n          </el-form-item>\n        </el-form>\n        <span slot=\"footer\" class=\"dialog-footer\">\n            <el-button @click=\"returnOpen = false\">取 消</el-button>\n            <el-button type=\"primary\" @click=\"taskReturn\">确 定</el-button>\n        </span>\n    </el-dialog>\n\n    <!--驳回流程-->\n    <el-dialog :title=\"rejectTitle\" :visible.sync=\"rejectOpen\" width=\"40%\" append-to-body>\n      <el-form ref=\"taskForm\" :model=\"taskForm\" label-width=\"80px\" >\n        <el-form-item label=\"驳回意见\" prop=\"comment\" :rules=\"[{ required: true, message: '请输入意见', trigger: 'blur' }]\">\n          <el-input style=\"width: 50%\" type=\"textarea\" v-model=\"taskForm.comment\" placeholder=\"请输入意见\"/>\n        </el-form-item>\n      </el-form>\n      <span slot=\"footer\" class=\"dialog-footer\">\n          <el-button @click=\"rejectOpen = false\">取 消</el-button>\n          <el-button type=\"primary\" @click=\"taskReject\">确 定</el-button>\n        </span>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport {flowRecord} from \"@/api/flowable/finished\";\nimport Parser from '@/components/parser/Parser'\nimport {definitionStartByKey, getProcessVariables, readXmlByKey, getFlowViewer} from \"@/api/flowable/definition\";\nimport {complete, rejectTask, returnList, returnTask, getNextFlowNode, delegate} from \"@/api/flowable/todo\";\nimport flow from '@/views/flowable/task/record/flow'\nimport {treeselect} from \"@/api/system/dept\";\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\";\nimport Treeselect from \"@riophae/vue-treeselect\";\nimport {listUser} from \"@/api/system/user\";\nimport moment from 'moment';\nexport default {\n  name: \"Record\",\n  components: {\n    Parser,\n    flow,\n    Treeselect\n  },\n  props: {\n    procDefKey: {\n      type: String,\n      default: undefined\n    },\n    taskId: {\n      type: String,\n      default: undefined\n    },\n    procInsId: {\n      type: String,\n      default: undefined\n    },\n  },\n  data() {\n    return {\n      // 模型xml数据\n      xmlData: \"\",\n      taskList: [],\n      // 部门名称\n      deptName: undefined,\n      // 部门树选项\n      deptOptions: undefined,\n      // 用户表格数据\n      userList: null,\n      defaultProps: {\n        children: \"children\",\n        label: \"label\"\n      },\n      // 查询参数\n      queryParams: {\n        deptId: undefined\n      },\n      // 遮罩层\n      loading: true,\n      flowRecordList: [], // 流程流转数据\n      formConfCopy: {},\n      src: null,\n      rules: {}, // 表单校验\n      variablesForm: {}, // 流程变量数据\n      taskForm:{\n        returnTaskShow: false, // 是否展示回退表单\n        delegateTaskShow: false, // 是否展示回退表单\n        defaultTaskShow: true, // 默认处理\n        sendUserShow: false, // 审批用户\n        multiple: false,\n        comment:\"\", // 意见内容\n        procInsId: \"\", // 流程实例编号\n        instanceId: \"\", // 流程实例编号\n        taskId: \"\" ,// 流程任务编号\n        procDefKey: \"\",  // 流程编号\n        vars: \"\",\n        targetKey:\"\"\n      },\n      userDataList:[], // 流程候选人\n      assignee: null,\n      formConf: {}, // 默认表单数据\n      formConfOpen: false, // 是否加载默认表单数据\n      variables: [], // 流程变量数据\n      variablesData: {}, // 流程变量数据\n      variableOpen: false, // 是否加载流程变量数据\n      returnTaskList: [],  // 回退列表数据\n      finished: 'false',\n      completeTitle: null,\n      completeOpen: false,\n      returnTitle: null,\n      returnOpen: false,\n      rejectOpen: false,\n      rejectTitle: null,\n      userData:[],\n    };\n  },\n  created() {\n    console.log(\"========record========created=>>>\")\n    console.log(this._props)\n    let {taskId,procDefKey,procInsId,finished}=this._props;\n    this.taskForm.taskId  = taskId;\n    this.taskForm.procInsId = procInsId;\n    this.taskForm.instanceId = procInsId;\n    // 初始化表单\n    this.taskForm.procDefKey  = procDefKey;\n    this.finished = finished;\n    // 回显流程记录\n    this.getFlowViewer(this.taskForm.procInsId,this.taskForm.procDefKey);\n    // 流程任务重获取变量表单\n    if (this.taskForm.taskId){\n      this.processVariables( this.taskForm.taskId)\n      this.getNextFlowNode(this.taskForm.taskId)\n    }\n    this.getFlowRecordList( this.taskForm.procInsId);\n    this.finished =  this.$route.query && this.$route.query.finished\n  },\n  activated() {\n    console.log(\"========record========activated=>>>\")\n    let {taskId,procDefKey,procInsId,finished}=this._props;\n    console.log(this._props)\n    this.taskForm.taskId  = taskId;\n    this.taskForm.procInsId = procInsId;\n    this.taskForm.instanceId = procInsId;\n    // 初始化表单\n    this.taskForm.procDefKey  = procDefKey;\n    this.finished = finished;\n    // 回显流程记录\n    this.getFlowViewer(this.taskForm.procInsId,this.taskForm.procDefKey);\n    // 流程任务重获取变量表单\n    if (this.taskForm.taskId){\n      this.processVariables( this.taskForm.taskId)\n      this.getNextFlowNode(this.taskForm.taskId)\n    }\n    this.getFlowRecordList( this.taskForm.procInsId);\n    this.finished =  this.$route.query && this.$route.query.finished\n  },\n  mounted() {\n    // // 表单数据回填，模拟异步请求场景\n    // setTimeout(() => {\n    //   // 请求回来的表单数据\n    //   const data = {\n    //     field102: '18836662555'\n    //   }\n    //   // 回填数据\n    //   this.fillFormData(this.formConf, data)\n    //   // 更新表单\n    //   this.key = +new Date().getTime()\n    // }, 1000)\n  },\n  methods: {\n    /** 查询部门下拉树结构 */\n    getTreeselect() {\n      treeselect().then(response => {\n        this.deptOptions = response.data;\n      });\n    },\n    /** 查询用户列表 */\n    getList() {\n      listUser(this.addDateRange(this.queryParams, this.dateRange)).then(response => {\n          this.userList = response.rows;\n          this.total = response.total;\n        }\n      );\n    },\n    // 筛选节点\n    filterNode(value, data) {\n      if (!value) return true;\n      return data.label.indexOf(value) !== -1;\n    },\n    // 节点单击事件\n    handleNodeClick(data) {\n      this.queryParams.deptId = data.id;\n      this.getList();\n    },\n    /** xml 文件 */\n    getModelDetail(deployKey) {\n      // 发送请求，获取xml\n      readXmlByKey(deployKey).then(res => {\n        this.xmlData = res.data\n      })\n    },\n    getFlowViewer(procInsId,deployKey) {\n      getFlowViewer(procInsId).then(res => {\n        this.taskList = res.data\n        this.getModelDetail(deployKey);\n      })\n    },\n    setIcon(val) {\n      if (val) {\n        return \"el-icon-check\";\n      } else {\n        return \"el-icon-time\";\n      }\n    },\n    setColor(val) {\n      if (val) {\n        return \"#2bc418\";\n      } else {\n        return \"#b3bdbb\";\n      }\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.userData = selection\n      const val = selection.map(item => item.userId)[0];\n      if (val instanceof Array) {\n        this.taskForm.values = {\n          \"approval\": val.join(',')\n        }\n      } else {\n        this.taskForm.values = {\n          \"approval\": val\n        }\n      }\n    },\n    // 关闭标签\n    handleClose(tag) {\n      this.userData.splice(this.userData.indexOf(tag), 1);\n    },\n    /** 流程变量赋值 */\n    handleCheckChange(val) {\n      if (val instanceof Array) {\n        this.taskForm.values = {\n          \"approval\": val.join(',')\n        }\n      } else {\n        this.taskForm.values = {\n          \"approval\": val\n        }\n      }\n    },\n    /** 流程流转记录 */\n    getFlowRecordList(procInsId) {\n      const params = {procInsId: procInsId}\n      flowRecord(params).then(res => {\n        this.flowRecordList = res.data.flowList;\n        // 流程过程中不存在初始化表单 直接读取的流程变量中存储的表单值\n        if (res.data.formData) {\n          this.formConf = res.data.formData;\n          this.formConfOpen = true\n        }\n      }).catch(res => {\n        this.goBack();\n      })\n    },\n    fillFormData(form, data) {\n      form.fields.forEach(item => {\n        const val = data[item.__vModel__]\n        if (val) {\n          item.__config__.defaultValue = val\n        }\n      })\n    },\n    /** 获取流程变量内容 */\n    processVariables(taskId) {\n      if (taskId) {\n        // 提交流程申请时填写的表单存入了流程变量中后续任务处理时需要展示\n        getProcessVariables(taskId).then(res => {\n          // this.variables = res.data.variables;\n          this.variablesData = res.data.variables;\n          this.variableOpen = true\n        });\n      }\n    },\n    /** 根据当前任务或者流程设计配置的下一步节点 */\n    getNextFlowNode(taskId) {\n      // 根据当前任务或者流程设计配置的下一步节点 todo 暂时未涉及到考虑网关、表达式和多节点情况\n      const params = {taskId: taskId}\n      getNextFlowNode(params).then(res => {\n        const data = res.data;\n        if (data) {\n          if (data.type === 'assignee') {\n            this.userDataList = res.data.userList;\n          } else if (data.type === 'candidateUsers') {\n            this.userDataList = res.data.userList;\n            this.taskForm.multiple = true;\n          } else if (data.type === 'candidateGroups') {\n            res.data.roleList.forEach(role => {\n              role.userId = role.roleId;\n              role.nickName = role.roleName;\n            })\n            this.userDataList = res.data.roleList;\n            this.taskForm.multiple = false;\n          } else if (data.type === 'multiInstance') {\n            this.userDataList = res.data.userList;\n            this.taskForm.multiple = true;\n          }\n          this.taskForm.sendUserShow = true;\n        }\n      })\n    },\n    /** 审批任务选择 */\n    handleComplete() {\n      this.completeOpen = true;\n      this.completeTitle = \"审批流程\";\n      //this.getTreeselect();\n    },\n    /** 审批任务 */\n    taskComplete(comment) {\n      // if (!this.taskForm.values){\n      //   this.msgError(\"请选择流程接收人员\");\n      //   return;\n      // }\n      if(comment && (typeof comment=='string')&&comment.constructor==String){\n        this.taskForm.comment = comment;\n      } \n      if (!this.taskForm.comment){\n        this.msgError(\"请输入审批意见\");\n        return;\n      }\n      complete(this.taskForm).then(response => {\n        this.msgSuccess(response.msg);\n        this.goBack();\n      });\n    },\n    /** 委派任务 */\n    handleDelegate() {\n      this.taskForm.delegateTaskShow = true;\n      this.taskForm.defaultTaskShow = false;\n    },\n    handleAssign(){\n\n    },\n    /** 返回页面 */\n    goBack() {\n      // 关闭当前标签页并返回上个页面\n      this.$store.dispatch(\"tagsView/delView\", this.$route);\n      this.$router.go(-1)\n    },\n    /** 接收子组件传的值 */\n    getData(data) {\n      if (data) {\n        const variables = [];\n        data.fields.forEach(item => {\n          let variableData = {};\n          variableData.label = item.__config__.label\n          // 表单值为多个选项时\n          if (item.__config__.defaultValue instanceof Array) {\n            const array = [];\n            item.__config__.defaultValue.forEach(val => {\n              array.push(val)\n            })\n            variableData.val = array;\n          } else {\n            variableData.val = item.__config__.defaultValue\n          }\n          variables.push(variableData)\n        })\n        this.variables = variables;\n      }\n    },\n    /** 申请流程表单数据提交----没用 */\n    // submitForm(data) {\n    //   if (data) {\n    //     const variables = data.valData;\n    //     const formData = data.formData;\n    //     formData.disabled = true;\n    //     formData.formBtns = false;\n    //     if (this.taskForm.procDefKey) {\n    //       variables.variables = formData;\n    //       variables.businessKey = data.businessKey;          \n    //        // 启动流程并将表单数据加入流程变量\n    //       definitionStartByKey(this.taskForm.procDefKey, JSON.stringify(variables)).then(res => {\n    //         this.msgSuccess(res.msg);\n    //         this.goBack();\n    //       })\n    //     }\n    //   }\n    // },\n    startFlow(businessKey, name, variables) {\n      let startDate = moment(new Date()).format('YYYYMMDDHHmmss');\n      const data = {}\n      if (this.taskForm.procDefKey) {\n        if(!variables){\n          data.variables = {};\n        }else{\n          data.variables = variables;\n        }\n        \n        data.businessKey = businessKey;\n        data.procDefKey = this.taskForm.procDefKey;\n        data.taskName = name;\n          // 启动流程并将表单数据加入流程变量\n        definitionStartByKey(JSON.stringify(data)).then(res => {\n          this.msgSuccess(res.msg);\n          this.goBack();\n        })\n      }\n      \n    },\n    /** 驳回任务 */\n    handleReject() {\n      this.rejectOpen = true;\n      this.rejectTitle = \"驳回流程\";\n    },\n    /** 驳回任务 */\n    taskReject() {\n      this.$refs[\"taskForm\"].validate(valid => {\n        if (valid) {\n          rejectTask(this.taskForm).then(res => {\n            this.msgSuccess(res.msg);\n            this.goBack();\n          });\n        }\n      });\n    },\n    /** 可退回任务列表 */\n    handleReturn() {\n      this.returnOpen = true;\n      this.returnTitle = \"退回流程\";\n      returnList(this.taskForm).then(res => {\n        this.returnTaskList = res.data;\n        this.taskForm.values = null;\n      })\n    },\n    /** 提交退回任务 */\n   taskReturn() {\n      this.$refs[\"taskForm\"].validate(valid => {\n        if (valid) {\n          returnTask(this.taskForm).then(res => {\n            this.msgSuccess(res.msg);\n            this.goBack()\n          });\n        }\n      });\n    },\n    /** 取消回退任务按钮 */\n    cancelTask() {\n      this.taskForm.returnTaskShow = false;\n      this.taskForm.defaultTaskShow = true;\n      this.taskForm.sendUserShow = true;\n      this.returnTaskList = [];\n    },\n    /** 委派任务 */\n    submitDeleteTask() {\n      this.$refs[\"taskForm\"].validate(valid => {\n        if (valid) {\n          delegate(this.taskForm).then(response => {\n            this.msgSuccess(response.msg);\n            this.goBack();\n          });\n        }\n      });\n    },\n    /** 取消回退任务按钮 */\n    cancelDelegateTask() {\n      this.taskForm.delegateTaskShow = false;\n      this.taskForm.defaultTaskShow = true;\n      this.taskForm.sendUserShow = true;\n      this.returnTaskList = [];\n    },\n  }\n};\n</script>\n<style lang=\"scss\" scoped>\n@media screen and (max-width: 600px) {\n  .el-timeline {\n      margin: 0;\n      font-size: 14px;\n      list-style: none;\n      padding-left: 0;\n  }\n}\n.test-form {\n  margin: 15px auto;\n  width: 800px;\n  padding: 15px;\n}\n\n.clearfix:before,\n.clearfix:after {\n  display: table;\n  content: \"\";\n}\n.clearfix:after {\n  clear: both\n}\n\n.box-card {\n  width: 100%;\n  margin-bottom: 20px;\n}\n\n.el-tag + .el-tag {\n  margin-left: 10px;\n}\n</style>\n"]}]}