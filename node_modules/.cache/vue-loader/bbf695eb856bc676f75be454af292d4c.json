{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/project/report/form.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/project/report/form.vue", "mtime": 1752653873866}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICBnZXRSZXBvcnQsDQogIGFkZFJlcG9ydCwNCiAgdXBkYXRlUmVwb3J0LA0KICBjaGVja05hbWVVbmlxdWUNCn0gZnJvbSAiQC9hcGkvcHJvamVjdC9yZXBvcnQiOw0KaW1wb3J0IEZpbGVVcGxvYWQgZnJvbSAiQC9jb21wb25lbnRzL0ZpbGVVcGxvYWQiOw0KaW1wb3J0IGZsb3dhYmxlIGZyb20gJ0Avdmlld3MvZmxvd2FibGUvdGFzay9yZWNvcmQvaW5kZXgnDQppbXBvcnQgeyByZWdpb25EYXRhLCBDb2RlVG9UZXh0LCBUZXh0VG9Db2RlIH0gZnJvbSAiZWxlbWVudC1jaGluYS1hcmVhLWRhdGEiOw0KaW1wb3J0IHByaW50IGZyb20gInByaW50LWpzIjsNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogIlJlcG9ydCIsDQogIGNvbXBvbmVudHM6IHsNCiAgICBmbG93YWJsZSwNCiAgICBGaWxlVXBsb2FkLA0KICAgIHByaW50DQogIH0sDQogIGRhdGEoKSB7DQogICAgdmFyIHRoYXQgPSB0aGlzOw0KICAgIHZhciBpbmZvVHlwZVZhbHVlVmFsaSA9IChydWxlLCB2YWx1ZSwgY2FsbGJhY2spID0+IHsNCiAgICAgIGlmICh0aGF0LmZvcm0uaW5mb1R5cGUuaW5kZXhPZignMScpID49IDAgJiYgIXRoYXQuZm9ybS5zY2FuRmlsZSkgew0KICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoIumCrueuseWcsOWdgOW/heWhqyIpKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KICAgICAgY2FsbGJhY2soKTsNCiAgICB9Ow0KICAgIHZhciBpbmZvVHlwZVZhbHVlVmFsaTIgPSAocnVsZSwgdmFsdWUsIGNhbGxiYWNrKSA9PiB7DQogICAgICBpZiAodGhhdC5mb3JtLmluZm9UeXBlLmluZGV4T2YoJzInKSA+PSAwICYmICF0aGF0LmZvcm0uc2VuZEFkZHJlc3MpIHsNCiAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCLmlLbku7blnLDlnYDlv4XloasiKSk7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCiAgICAgIGNhbGxiYWNrKCk7DQogICAgfTsNCiAgICB2YXIgbmFtZVZhbGkgPSAocnVsZSwgdmFsdWUsIGNhbGxiYWNrKSA9PiB7DQogICAgICBpZiAoIXRoYXQuZm9ybS5wcm9qZWN0TmFtZSkgew0KICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoIumhueebruWQjeensOW/heWhqyIpKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGlmICgvXHMrL2cudGVzdCh0aGF0LmZvcm0ucHJvamVjdE5hbWUpKSB7DQogICAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCLpobnnm67lkI3np7DkuI3op4TojIMiKSk7DQogICAgICAgICAgcmV0dXJuOw0KICAgICAgICB9DQogICAgICAgIGNoZWNrTmFtZVVuaXF1ZSh7IHByb2plY3ROYW1lOiB0aGF0LmZvcm0ucHJvamVjdE5hbWUsIHByb2plY3RJZDogdGhhdC5mb3JtLnByb2plY3RJZCB9KS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICAgIGlmIChyZXNwb25zZS5kYXRhID09IDApIHsNCiAgICAgICAgICAgIGNhbGxiYWNrKCk7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcigi6aG555uu5ZCN56ew5bey5a2Y5ZyoIikpOw0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCiAgICAgIH0NCiAgICB9Ow0KICAgIHZhciBjb2RlVmFsaSA9IChydWxlLCB2YWx1ZSwgY2FsbGJhY2spID0+IHsNCiAgICAgIGlmICghdGhhdC5mb3JtLnByb2plY3RObykgew0KICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoIumhueebrue8luWPt+W/heWhqyIpKTsNCiAgICAgIH0gZWxzZSBpZiAoL1xzKy9nLnRlc3QodGhhdC5mb3JtLnByb2plY3RObykpIHsNCiAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCLpobnnm67nvJblj7fkuI3op4TojIMiKSk7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCiAgICAgIGNhbGxiYWNrKCk7DQogICAgfTsNCiAgICByZXR1cm4gew0KICAgICAgLy8g6YGu572p5bGCDQogICAgICBsb2FkaW5nOiB0cnVlLA0KICAgICAgLy8g5pON5L2c57G75Z6L5a2X5YW4DQogICAgICBvcGVyYXRpb25UeXBlT3B0aW9uczogW10sDQogICAgICAvLyDlrqHmoLjnirbmgIHlrZflhbgNCiAgICAgIGF1ZGl0U3RhdHVzT3B0aW9uczogW10sDQogICAgICAvLyDnvJbovpHnirbmgIHlrZflhbgNCiAgICAgIGVkaXRTdGF0dXNPcHRpb25zOiBbXSwNCiAgICAgIC8vIOaLm+agh+aWueW8j+Wtl+WFuA0KICAgICAgYmlkZGluZ1R5cGVPcHRpb25zOiBbXSwNCiAgICAgIC8vIOaKleagh+S6p+WTgeWei+WPt+Wtl+WFuA0KICAgICAgbW9kZWxPcHRpb25zOiBbXSwNCiAgICAgIG1vZGVsT3B0aW9uMTogW10sDQogICAgICAvLyDmiYDpnIDotYTmlpnlrZflhbgNCiAgICAgIHJlcXVpcmVJbmZvT3B0aW9uczogW10sDQogICAgICByZXF1aXJlSW5mb09wdGlvbjE6IFtdLA0KICAgICAgLy8g6LWE5paZ57G75Z6L5a2X5YW4DQogICAgICBpbmZvVHlwZU9wdGlvbnM6IFtdLA0KICAgICAgLy8g5omA5bGe55yB5Lu95a2X5YW4DQogICAgICBiZWxvbmdQcm92aW5jZU9wdGlvbnM6IFtdLA0KICAgICAgYmVsb25nUHJvdmluY2VPcHRpb25zMTogW10sDQogICAgICAvLyDllK7lkI7lubTpmZANCiAgICAgIGFmdGVyU2FsZVllYXJPcHRpb25zOiBbXSwNCiAgICAgIGFmdGVyU2FsZVllYXJPcHRpb25zMTogW10sDQogICAgICBzcGVjT3B0aW9uczogW10sDQogICAgICBzcGVjT3B0aW9uMTogW10sDQogICAgICAvLyDooajljZXlj4LmlbANCiAgICAgIGZvcm06IHt9LA0KICAgICAgLy8g6KGo5Y2V5qCh6aqMDQogICAgICAvLyDooajljZXmoKHpqowNCiAgICAgIHJ1bGVzOiB7DQogICAgICAgIG9wZXJhdGlvblR5cGU6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5pON5L2c57G75Z6L5b+F6YCJIiB9XSwNCiAgICAgICAgcHJvamVjdE5vOiBbDQogICAgICAgICAgeyB2YWxpZGF0b3I6IGNvZGVWYWxpLCByZXF1aXJlZDogdHJ1ZSwgdHJpZ2dlcjogImJsdXIiIH0sDQogICAgICAgIF0sDQogICAgICAgIHByb2plY3ROYW1lOiBbDQogICAgICAgICAgeyB2YWxpZGF0b3I6IG5hbWVWYWxpLCByZXF1aXJlZDogdHJ1ZSwgdHJpZ2dlcjogImJsdXIiIH0sDQogICAgICAgIF0sDQogICAgICAgIGFkZHJlc3M6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+m57uG5Zyw5Z2A5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0sDQogICAgICAgIF0sDQogICAgICAgIGJpZGRpbmdDb21wYW55OiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuaLm+agh+WNleS9jeS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9LA0KICAgICAgICBdLA0KICAgICAgICBvcGVuRGF0ZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLlvIDmoIfml6XmnJ/kuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgXSwNCiAgICAgICAgYWZ0ZXJTYWxlWWVhcjogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLllK7lkI7lubTpmZDkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgXSwNCiAgICAgICAgaGFuZ0RhdGU6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5oyC572R5pel5pyf5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0sDQogICAgICAgIF0sDQogICAgICAgIGJlbG9uZ1Byb3ZpbmNlOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuWUruWQjuW5tOmZkOS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9LA0KICAgICAgICBdLA0KICAgICAgICBkaXN0cmlidXRvcjogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLmiYDlsZ7nu4/plIDllYbkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgXSwNCiAgICAgICAgc2NhbkZpbGU6IFsNCiAgICAgICAgICB7IHZhbGlkYXRvcjogaW5mb1R5cGVWYWx1ZVZhbGksIHRyaWdnZXI6ICJibHVyIiB9LA0KICAgICAgICBdLA0KICAgICAgICBzZW5kQWRkcmVzczogWw0KICAgICAgICAgIHsgdmFsaWRhdG9yOiBpbmZvVHlwZVZhbHVlVmFsaTIsIHRyaWdnZXI6ICJibHVyIiB9LA0KICAgICAgICBdLA0KICAgICAgICBtb2RlbDogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLmipXmoIfkuqflk4Hlnovlj7flv4XpgIkiIH0sDQogICAgICAgIF0sDQogICAgICAgIHNwZWM6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5oqV5qCH5Lqn5ZOB6KeE5qC85b+F6YCJIiB9LA0KICAgICAgICBdLA0KICAgICAgICBwcm92aW5jZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLpobnnm67miYDlnKjlnLDlv4XpgIkiIH0sDQogICAgICAgIF0sDQogICAgICAgIGluZm9UeXBlOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIui1hOaWmeaOpeaUtuaWueW8j+W/hemAiSIsIHRyaWdnZXI6ICJjaGFuZ2UiIH0sDQogICAgICAgIF0sDQogICAgICAgIGJpZGRpbmdDb250YWN0OiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuaLm+agh+WNleS9jeiBlOezu+S6ui/ogZTns7vnlLXor53lv4XloasiIH0sDQogICAgICAgIF0sDQogICAgICAgIGF1dGhDb250YWN0OiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuaOiOadg+WFrOWPuOiBlOezu+S6ui/ogZTns7vnlLXor53lv4XloasiIH0sDQogICAgICAgIF0NCiAgICAgIH0sDQogICAgICBvcHRpb25zOiByZWdpb25EYXRhLA0KICAgICAgc2VsZWN0ZWRPcHRpb25zOiBbXSwNCiAgICAgIHF1ZXJ5QXJlYTogW10sDQogICAgICAvL+W3peS9nOa1geWPguaVsA0KICAgICAgZmluaXNoZWQ6ICdmYWxzZScsDQogICAgICB0YXNrSWQ6IHVuZGVmaW5lZCwNCiAgICAgIHByb2NJbnNJZDogdW5kZWZpbmVkLA0KICAgICAgYnVzaW5lc3NLZXk6IHVuZGVmaW5lZCwNCiAgICAgIGF1ZGl0OiBmYWxzZSwNCiAgICAgIGZvcm1FZGl0OiBmYWxzZSwNCiAgICAgIC8v5bel5L2c5rWB5Y+C5pWwZW5kDQogICAgICBhdXRoQ29tcGFueXM6IFtdLA0KICAgIH07DQogIH0sDQogIGFjdGl2YXRlZCgpIHsNCg0KICAgIHRoaXMucmVzZXQoKTsNCiAgICAvL3RoaXMuZm9ybS5wcm9qZWN0Tm8gPSBtb21lbnQobmV3IERhdGUoKSkuZm9ybWF0KCdZWVlZTU1EREhIbW0nKTsNCiAgICB0aGlzLnRhc2tJZCA9IHRoaXMuJHJvdXRlLnF1ZXJ5ICYmIHRoaXMuJHJvdXRlLnF1ZXJ5LnRhc2tJZDsNCiAgICB0aGlzLnByb2NJbnNJZCA9IHRoaXMuJHJvdXRlLnF1ZXJ5ICYmIHRoaXMuJHJvdXRlLnF1ZXJ5LnByb2NJbnNJZDsNCiAgICB0aGlzLmZpbmlzaGVkID0gdGhpcy4kcm91dGUucXVlcnkgJiYgdGhpcy4kcm91dGUucXVlcnkuZmluaXNoZWQ7DQogICAgdGhpcy5idXNpbmVzc0tleSA9IHRoaXMuJHJvdXRlLnF1ZXJ5ICYmIHRoaXMuJHJvdXRlLnF1ZXJ5LmJ1c2luZXNzS2V5Ow0KICAgIGxldCBlZGl0ID0gdGhpcy4kcm91dGUucXVlcnkgJiYgdGhpcy4kcm91dGUucXVlcnkuZm9ybUVkaXQ7DQogICAgaWYgKGVkaXQgPT0gInRydWUiKSB7DQogICAgICB0aGlzLmZvcm1FZGl0ID0gdHJ1ZTsNCiAgICB9IGVsc2Ugew0KICAgICAgdGhpcy5mb3JtRWRpdCA9IGZhbHNlOw0KICAgIH0NCiAgICBpZiAodGhpcy5idXNpbmVzc0tleSkgew0KICAgICAgaWYgKHRoaXMuZmluaXNoZWQgPT0gImZhbHNlIiAmJiAhdGhpcy5mb3JtRWRpdCkgew0KICAgICAgICB0aGlzLmF1ZGl0ID0gdHJ1ZTsNCiAgICAgIH0NCiAgICAgIHRoaXMuZ2V0UmVwb3J0SW5mbyh0aGlzLmJ1c2luZXNzS2V5KTsNCiAgICB9DQogICAgY29uc29sZS5sb2coIj09PT09PT09cHJvamVjdD09PT09PT09PT5hY3RpdmF0ZWQ+Zm9ybUVkaXQ+PiIgKyB0aGlzLmZvcm1FZGl0KTsNCiAgfSwNCiAgY3JlYXRlZCgpIHsNCg0KICAgIHRoaXMucmVzZXQoKTsNCiAgICAvL3RoaXMuZm9ybS5wcm9qZWN0Tm8gPSBtb21lbnQobmV3IERhdGUoKSkuZm9ybWF0KCdZWVlZTU1EREhIbW0nKTsNCiAgICB0aGlzLnRhc2tJZCA9IHRoaXMuJHJvdXRlLnF1ZXJ5ICYmIHRoaXMuJHJvdXRlLnF1ZXJ5LnRhc2tJZDsNCiAgICB0aGlzLnByb2NJbnNJZCA9IHRoaXMuJHJvdXRlLnF1ZXJ5ICYmIHRoaXMuJHJvdXRlLnF1ZXJ5LnByb2NJbnNJZDsNCiAgICB0aGlzLmZpbmlzaGVkID0gdGhpcy4kcm91dGUucXVlcnkgJiYgdGhpcy4kcm91dGUucXVlcnkuZmluaXNoZWQ7DQogICAgdGhpcy5idXNpbmVzc0tleSA9IHRoaXMuJHJvdXRlLnF1ZXJ5ICYmIHRoaXMuJHJvdXRlLnF1ZXJ5LmJ1c2luZXNzS2V5Ow0KICAgIGxldCBlZGl0ID0gdGhpcy4kcm91dGUucXVlcnkgJiYgdGhpcy4kcm91dGUucXVlcnkuZm9ybUVkaXQ7DQogICAgaWYgKGVkaXQgPT0gInRydWUiKSB7DQogICAgICB0aGlzLmZvcm1FZGl0ID0gdHJ1ZTsNCiAgICB9IGVsc2Ugew0KICAgICAgdGhpcy5mb3JtRWRpdCA9IGZhbHNlOw0KICAgIH0NCiAgICBpZiAodGhpcy5idXNpbmVzc0tleSkgew0KICAgICAgaWYgKHRoaXMuZmluaXNoZWQgPT0gImZhbHNlIiAmJiAhdGhpcy5mb3JtRWRpdCkgew0KICAgICAgICB0aGlzLmF1ZGl0ID0gdHJ1ZTsNCiAgICAgIH0NCiAgICAgIHRoaXMuZ2V0UmVwb3J0SW5mbyh0aGlzLmJ1c2luZXNzS2V5KTsNCiAgICB9DQogICAgLy8gdGhpcy5hdWRpdCA9IHRydWU7DQogICAgY29uc29sZS5sb2coIj09PT09PT09PXByb2plY3Q9PT09PT09PT5jcmVhdGVkPj5mb3JtRWRpdD4iICsgdGhpcy5mb3JtRWRpdCk7DQoNCiAgICB0aGlzLmdldERpY3RzKCJwcl9vcGVyYXRpb25fdHlwZSIpLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICB0aGlzLm9wZXJhdGlvblR5cGVPcHRpb25zID0gcmVzcG9uc2UuZGF0YTsNCiAgICB9KTsNCiAgICB0aGlzLmdldERpY3RzKCJwcl9hdWRpdF9zdGF0dXMiKS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgdGhpcy5hdWRpdFN0YXR1c09wdGlvbnMgPSByZXNwb25zZS5kYXRhOw0KICAgIH0pOw0KICAgIHRoaXMuZ2V0RGljdHMoInByX2VkaXRfc3RhdHVzIikudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgIHRoaXMuZWRpdFN0YXR1c09wdGlvbnMgPSByZXNwb25zZS5kYXRhOw0KICAgIH0pOw0KICAgIC8vIHRoaXMuZ2V0RGljdHMoInByX2JpZGRpbmdfdHlwZSIpLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgLy8gICB0aGlzLmJpZGRpbmdUeXBlT3B0aW9ucyA9IHJlc3BvbnNlLmRhdGE7DQogICAgLy8gfSk7DQogICAgdGhpcy5nZXREaWN0cygicHJfbW9kZWwiKS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgdGhpcy5tb2RlbE9wdGlvbjEgPSByZXNwb25zZS5kYXRhOw0KICAgICAgdmFyIG9wdCA9IFtdOw0KICAgICAgcmVzcG9uc2UuZGF0YS5mb3JFYWNoKChlbGVtLCBpbmRleCkgPT4gew0KICAgICAgICB2YXIgb2JqID0ge307DQogICAgICAgIG9iai5sYWJlbCA9IGVsZW0uZGljdExhYmVsOw0KICAgICAgICBvYmoudmFsdWUgPSBlbGVtLmRpY3RWYWx1ZTsNCiAgICAgICAgb3B0LnB1c2gob2JqKTsNCiAgICAgIH0pOw0KICAgICAgdGhpcy5tb2RlbE9wdGlvbnMgPSBvcHQ7DQogICAgfSk7DQogICAgdGhpcy5nZXREaWN0cygicHJfc3BlYyIpLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICB0aGlzLnNwZWNPcHRpb24xID0gcmVzcG9uc2UuZGF0YTsNCiAgICAgIHZhciBvcHQgPSBbXTsNCiAgICAgIHJlc3BvbnNlLmRhdGEuZm9yRWFjaCgoZWxlbSwgaW5kZXgpID0+IHsNCiAgICAgICAgdmFyIG9iaiA9IHt9Ow0KICAgICAgICBvYmoubGFiZWwgPSBlbGVtLmRpY3RMYWJlbDsNCiAgICAgICAgb2JqLnZhbHVlID0gZWxlbS5kaWN0VmFsdWU7DQogICAgICAgIG9wdC5wdXNoKG9iaik7DQogICAgICB9KTsNCiAgICAgIHRoaXMuc3BlY09wdGlvbnMgPSBvcHQ7DQogICAgfSk7DQogICAgdGhpcy5nZXREaWN0cygicHJfaW5mbyIpLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICB0aGlzLnJlcXVpcmVJbmZvT3B0aW9uMSA9IHJlc3BvbnNlLmRhdGE7DQogICAgICB2YXIgb3B0ID0gW107DQogICAgICByZXNwb25zZS5kYXRhLmZvckVhY2goKGVsZW0sIGluZGV4KSA9PiB7DQogICAgICAgIHZhciBvYmogPSB7fTsNCiAgICAgICAgb2JqLmxhYmVsID0gZWxlbS5kaWN0TGFiZWw7DQogICAgICAgIG9iai52YWx1ZSA9IGVsZW0uZGljdFZhbHVlOw0KICAgICAgICBvcHQucHVzaChvYmopOw0KICAgICAgfSk7DQogICAgICB0aGlzLnJlcXVpcmVJbmZvT3B0aW9ucyA9IG9wdDsNCiAgICB9KTsNCiAgICB0aGlzLmdldERpY3RzKCJwcl9wcm92aW5jZSIpLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICB0aGlzLmJlbG9uZ1Byb3ZpbmNlT3B0aW9uczEgPSByZXNwb25zZS5kYXRhOw0KICAgICAgdmFyIG9wdCA9IFtdOw0KICAgICAgcmVzcG9uc2UuZGF0YS5mb3JFYWNoKChlbGVtLCBpbmRleCkgPT4gew0KICAgICAgICB2YXIgb2JqID0ge307DQogICAgICAgIG9iai5sYWJlbCA9IGVsZW0uZGljdExhYmVsOw0KICAgICAgICBvYmoudmFsdWUgPSBlbGVtLmRpY3RWYWx1ZTsNCiAgICAgICAgb3B0LnB1c2gob2JqKTsNCiAgICAgIH0pOw0KICAgICAgdGhpcy5iZWxvbmdQcm92aW5jZU9wdGlvbnMgPSBvcHQ7DQogICAgfSk7DQogICAgdGhpcy5nZXREaWN0cygicHJfYWZ0ZXJfc2FsZV95ZWFyIikudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgIHRoaXMuYWZ0ZXJTYWxlWWVhck9wdGlvbnMxID0gcmVzcG9uc2UuZGF0YTsNCiAgICAgIHZhciBvcHQgPSBbXTsNCiAgICAgIHJlc3BvbnNlLmRhdGEuZm9yRWFjaCgoZWxlbSwgaW5kZXgpID0+IHsNCiAgICAgICAgdmFyIG9iaiA9IHt9Ow0KICAgICAgICBvYmoubGFiZWwgPSBlbGVtLmRpY3RMYWJlbDsNCiAgICAgICAgb2JqLnZhbHVlID0gZWxlbS5kaWN0VmFsdWU7DQogICAgICAgIG9wdC5wdXNoKG9iaik7DQogICAgICB9KTsNCiAgICAgIHRoaXMuYWZ0ZXJTYWxlWWVhck9wdGlvbnMgPSBvcHQ7DQogICAgfSk7DQogICAgdGhpcy5nZXREaWN0cygicHJfZGF0YV90eXBlIikudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgIHRoaXMuaW5mb1R5cGVPcHRpb25zID0gcmVzcG9uc2UuZGF0YTsNCiAgICB9KTsNCiAgICAvL+m7mOiupOaKpeWkhw0KICAgIHRoaXMuZm9ybS5vcGVyYXRpb25UeXBlID0gJzEnOw0KICB9LA0KICBtZXRob2RzOiB7DQoNCiAgICAvLyDooajljZXph43nva4NCiAgICByZXNldCgpIHsNCiAgICAgIHRoaXMuZm9ybSA9IHsNCiAgICAgICAgcHJvamVjdElkOiBudWxsLA0KICAgICAgICBwcm9qZWN0Tm86IG51bGwsDQogICAgICAgIHByb2plY3ROYW1lOiBudWxsLA0KICAgICAgICBvcGVyYXRpb25UeXBlOiAxLA0KICAgICAgICBhdWRpdFN0YXR1czogIjEiLA0KICAgICAgICByZWplY3RSZWFzb246IG51bGwsDQogICAgICAgIHByb3ZpbmNlOiBudWxsLA0KICAgICAgICBjaXR5OiBudWxsLA0KICAgICAgICBkaXN0cmljdDogbnVsbCwNCiAgICAgICAgYWRkcmVzczogbnVsbCwNCiAgICAgICAgZWRpdFN0YXR1czogIjAiLA0KICAgICAgICBiZWxvbmdVc2VyOiBudWxsLA0KICAgICAgICBiaWRkaW5nQ29tcGFueTogbnVsbCwNCiAgICAgICAgb3BlbkRhdGU6IG51bGwsDQogICAgICAgIGJlbG9uZ1Byb3ZpbmNlOiBudWxsLA0KICAgICAgICBhZnRlclNhbGVZZWFyOiBudWxsLA0KICAgICAgICBoYW5nRGF0ZTogbnVsbCwNCiAgICAgICAgYmlkZGluZ1R5cGU6IG51bGwsDQogICAgICAgIGJ1ZGdldE1vbmV5OiBudWxsLA0KICAgICAgICBhdXRoQ29tcGFueTogbnVsbCwNCiAgICAgICAgYmlkZGluZ05ldDogbnVsbCwNCiAgICAgICAgZGlzdHJpYnV0b3I6IG51bGwsDQogICAgICAgIG1vZGVsOiBbXSwNCiAgICAgICAgc3BlYzogW10sDQogICAgICAgIGFyZWE6IG51bGwsDQogICAgICAgIGF1dGhGaWxlOiBudWxsLA0KICAgICAgICBhZnRlclNhbGVGaWxlOiBudWxsLA0KICAgICAgICByZXF1aXJlSW5mbzogW10sDQogICAgICAgIGluZm9UeXBlOiBbXSwNCiAgICAgICAgc2NhbkZpbGU6IG51bGwsDQogICAgICAgIHNlbmRBZGRyZXNzOiBudWxsLA0KICAgICAgICBtYWlsSW5mbzogbnVsbCwNCiAgICAgICAgZXhwcmVzc0luZm86IG51bGwsDQogICAgICAgIHJlbWFyazogbnVsbCwNCiAgICAgICAgc3BhcmUxOiBudWxsLA0KICAgICAgICBzcGFyZTI6IG51bGwsDQogICAgICB9Ow0KICAgICAgdGhpcy5yZXNldEZvcm0oImZvcm0iKTsNCiAgICB9LA0KICAgIC8vIOWkmumAieahhumAieS4reaVsOaNrg0KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsNCiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcCgoaXRlbSkgPT4gaXRlbS5wcm9qZWN0SWQpOw0KICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoICE9PSAxOw0KICAgICAgdGhpcy5tdWx0aXBsZSA9ICFzZWxlY3Rpb24ubGVuZ3RoOw0KICAgICAgY29uc29sZS5pbmZvKHNlbGVjdGlvbik7DQogICAgfSwNCg0KICAgIC8qKiDkv67mlLnmjInpkq7mk43kvZwgKi8NCiAgICBnZXRSZXBvcnRJbmZvKHByb2plY3RJZCkgew0KICAgICAgZ2V0UmVwb3J0KHByb2plY3RJZCkudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgdGhpcy5mb3JtID0gcmVzcG9uc2UuZGF0YTsNCiAgICAgICAgaWYgKHRoaXMuZm9ybS5tb2RlbCkgdGhpcy5mb3JtLm1vZGVsID0gdGhpcy5mb3JtLm1vZGVsLnNwbGl0KCIsIik7DQogICAgICAgIGVsc2UgdGhpcy5mb3JtLm1vZGVsID0gW107DQogICAgICAgIGlmICh0aGlzLmZvcm0ucmVxdWlyZUluZm8pDQogICAgICAgICAgdGhpcy5mb3JtLnJlcXVpcmVJbmZvID0gdGhpcy5mb3JtLnJlcXVpcmVJbmZvLnNwbGl0KCIsIik7DQogICAgICAgIGVsc2UgdGhpcy5mb3JtLnJlcXVpcmVJbmZvID0gW107DQogICAgICAgIGlmICh0aGlzLmZvcm0uaW5mb1R5cGUpDQogICAgICAgICAgdGhpcy5mb3JtLmluZm9UeXBlID0gdGhpcy5mb3JtLmluZm9UeXBlLnNwbGl0KCIsIik7DQogICAgICAgIGVsc2UgdGhpcy5mb3JtLmluZm9UeXBlID0gW107DQogICAgICAgIGlmICh0aGlzLmZvcm0uc3BlYykgdGhpcy5mb3JtLnNwZWMgPSB0aGlzLmZvcm0uc3BlYy5zcGxpdCgiLCIpOw0KICAgICAgICBlbHNlIHRoaXMuZm9ybS5zcGVjID0gW107DQogICAgICAgIHZhciBwcm92aW5jZXMgPSByZXNwb25zZS5kYXRhLnByb3ZpbmNlOw0KICAgICAgICBpZiAocHJvdmluY2VzLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICB2YXIgYWRkcmVzcyA9IHByb3ZpbmNlcy5zcGxpdCgiLyIpOw0KICAgICAgICAgIHZhciBjaXR5cyA9IFtdOw0KICAgICAgICAgIC8vIOecgeS7vQ0KICAgICAgICAgIGlmIChhZGRyZXNzLmxlbmd0aCA+IDApIGNpdHlzLnB1c2goVGV4dFRvQ29kZVthZGRyZXNzWzBdXS5jb2RlKTsNCiAgICAgICAgICAvLyDln47luIINCiAgICAgICAgICBpZiAoYWRkcmVzcy5sZW5ndGggPiAxKQ0KICAgICAgICAgICAgY2l0eXMucHVzaChUZXh0VG9Db2RlW2FkZHJlc3NbMF1dW2FkZHJlc3NbMV1dLmNvZGUpOw0KICAgICAgICAgIC8vIOWcsOWMug0KICAgICAgICAgIGlmIChhZGRyZXNzLmxlbmd0aCA+IDIpDQogICAgICAgICAgICBjaXR5cy5wdXNoKFRleHRUb0NvZGVbYWRkcmVzc1swXV1bYWRkcmVzc1sxXV1bYWRkcmVzc1syXV0uY29kZSk7DQoNCiAgICAgICAgICB0aGlzLnNlbGVjdGVkT3B0aW9ucyA9IGNpdHlzOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDmj5DkuqTmjInpkq4gKi8NCiAgICBzdWJtaXRGb3JtKCkgew0KICAgICAgbGV0IHRoYXQgPSB0aGlzOw0KICAgICAgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKCh2YWxpZCkgPT4gew0KICAgICAgICBpZiAodmFsaWQpIHsNCiAgICAgICAgICBpZiAodGhpcy5mb3JtLmluZm9UeXBlLmluZGV4T2YoJzEnKSA+PSAwICYmIHRoaXMuZm9ybS5zY2FuRmlsZSkgew0KICAgICAgICAgICAgbGV0IGVtYWlsUmVnID0gL15bYS16QS1aMC05Xy1dK0BbYS16QS1aMC05Xy1dKyhcLlthLXpBLVowLTlfLV0rKSskLzsNCiAgICAgICAgICAgIGlmICghZW1haWxSZWcudGVzdCh0aGlzLmZvcm0uc2NhbkZpbGUpKSB7DQogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIui1hOaWmeaOpeaUtuaWueW8j+mCrueuseagvOW8j+mUmeivryIpOw0KICAgICAgICAgICAgICByZXR1cm47DQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICAgIGlmICh0aGlzLmZvcm0ub3BlcmF0aW9uVHlwZSA9PSAyICYmICF0aGlzLmZvcm0uYXV0aEZpbGUpIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuaOiOadg+exu+Wei+W/hemcgOS4iuS8oOaOiOadg+S5piIpOw0KICAgICAgICAgICAgcmV0dXJuOw0KICAgICAgICAgIH0NCiAgICAgICAgICB2YXIgZm9ybVN0ciA9IEpTT04uc3RyaW5naWZ5KHRoaXMuZm9ybSk7DQogICAgICAgICAgdmFyIGZvcm1EYXRhID0gSlNPTi5wYXJzZShmb3JtU3RyKTsNCiAgICAgICAgICBpZiAoZm9ybURhdGEubW9kZWwgJiYgZm9ybURhdGEubW9kZWwubGVuZ3RoID4gMCkNCiAgICAgICAgICAgIGZvcm1EYXRhLm1vZGVsID0gZm9ybURhdGEubW9kZWwuam9pbigiLCIpOw0KICAgICAgICAgIGVsc2UgZm9ybURhdGEubW9kZWwgPSB1bmRlZmluZWQ7DQogICAgICAgICAgaWYgKGZvcm1EYXRhLnJlcXVpcmVJbmZvICYmIGZvcm1EYXRhLnJlcXVpcmVJbmZvLmxlbmd0aCA+IDApDQogICAgICAgICAgICBmb3JtRGF0YS5yZXF1aXJlSW5mbyA9IGZvcm1EYXRhLnJlcXVpcmVJbmZvLmpvaW4oIiwiKTsNCiAgICAgICAgICBlbHNlIGZvcm1EYXRhLnJlcXVpcmVJbmZvID0gdW5kZWZpbmVkOw0KICAgICAgICAgIGlmIChmb3JtRGF0YS5pbmZvVHlwZSAmJiBmb3JtRGF0YS5pbmZvVHlwZS5sZW5ndGggPiAwKQ0KICAgICAgICAgICAgZm9ybURhdGEuaW5mb1R5cGUgPSBmb3JtRGF0YS5pbmZvVHlwZS5qb2luKCIsIik7DQogICAgICAgICAgZWxzZSBmb3JtRGF0YS5pbmZvVHlwZSA9IHVuZGVmaW5lZDsNCiAgICAgICAgICBpZiAoZm9ybURhdGEuc3BlYyAmJiBmb3JtRGF0YS5zcGVjLmxlbmd0aCA+IDApDQogICAgICAgICAgICBmb3JtRGF0YS5zcGVjID0gZm9ybURhdGEuc3BlYy5qb2luKCIsIik7DQogICAgICAgICAgZWxzZSBmb3JtRGF0YS5zcGVjID0gdW5kZWZpbmVkOw0KDQogICAgICAgICAgLy/mjojmnYPlhazlj7gNCiAgICAgICAgICBpZiAodGhpcy5hdXRoQ29tcGFueXMubGVuZ3RoID4gMCkgew0KICAgICAgICAgICAgdmFyIGFycmF5ID0gbmV3IEFycmF5KCk7DQogICAgICAgICAgICB0aGlzLmF1dGhDb21wYW55cy5mb3JFYWNoKGZ1bmN0aW9uIChlKSB7DQogICAgICAgICAgICAgIGFycmF5LnB1c2goZS52YWx1ZSk7DQogICAgICAgICAgICB9KQ0KICAgICAgICAgICAgZm9ybURhdGEuYXV0aENvbXBhbnkgKz0gIiwiICsgYXJyYXkuam9pbigiLCIpDQogICAgICAgICAgfQ0KDQogICAgICAgICAgY29uc3QgbG9hZGluZyA9IHRoaXMuJGxvYWRpbmcoew0KICAgICAgICAgICAgbG9jazogdHJ1ZSwvL2xvY2vnmoTkv67mlLnnrKYtLem7mOiupOaYr2ZhbHNlDQogICAgICAgICAgICB0ZXh0OiAnTG9hZGluZycsLy/mmL7npLrlnKjliqDovb3lm77moIfkuIvmlrnnmoTliqDovb3mlofmoYgNCiAgICAgICAgICAgIHNwaW5uZXI6ICdlbC1pY29uLWxvYWRpbmcnLC8v6Ieq5a6a5LmJ5Yqg6L295Zu+5qCH57G75ZCNDQogICAgICAgICAgICBiYWNrZ3JvdW5kOiAncmdiYSgwLCAwLCAwLCAwLjcpJywvL+mBrue9qeWxguminOiJsg0KICAgICAgICAgICAgdGFyZ2V0OiBkb2N1bWVudC5xdWVyeVNlbGVjdG9yKCcubWFpbi1jb250YWluZXInKS8vbG9hZGlu6KaG55uW55qEZG9t5YWD57Sg6IqC54K5DQogICAgICAgICAgfSk7DQogICAgICAgICAgZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50LnN0eWxlLm92ZXJmbG93WSA9ICdoaWRkZW4nIC8v56aB5q2i5bqV5bGCZGl25rua5YqoDQoNCiAgICAgICAgICBpZiAoZm9ybURhdGEucHJvamVjdElkICE9IG51bGwpIHsNCiAgICAgICAgICAgIHVwZGF0ZVJlcG9ydChmb3JtRGF0YSkudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgICAgICAgLy90aGlzLm1zZ1N1Y2Nlc3MoIuS/ruaUueaIkOWKnyIpOw0KICAgICAgICAgICAgICBpZiAodGhhdC5idXNpbmVzc0tleSkgew0KICAgICAgICAgICAgICAgIHRoYXQuJHJlZnNbJ2Zsb3cnXS50YXNrQ29tcGxldGUoIumHjeaWsOaPkOS6pCIpOw0KICAgICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICAgIHRoYXQuc3RhcnRGbG93KGZvcm1EYXRhKTsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHsNCiAgICAgICAgICAgICAgICBsb2FkaW5nLmNsb3NlKCk7DQogICAgICAgICAgICAgICAgZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50LnN0eWxlLm92ZXJmbG93WSA9ICdhdXRvJyAvL+WFgeiuuOW6leWxgmRpdua7muWKqA0KICAgICAgICAgICAgICB9LCAxMDAwKTsNCiAgICAgICAgICAgIH0pLmNhdGNoKHJlcyA9PiB7DQogICAgICAgICAgICAgIGNvbnNvbGUubG9nKHJlcykNCiAgICAgICAgICAgICAgbG9hZGluZy5jbG9zZSgpOw0KICAgICAgICAgICAgICBkb2N1bWVudC5kb2N1bWVudEVsZW1lbnQuc3R5bGUub3ZlcmZsb3dZID0gJ2F1dG8nIC8v5YWB6K645bqV5bGCZGl25rua5YqoDQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgYWRkUmVwb3J0KGZvcm1EYXRhKS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICAgICAgICBjb25zb2xlLmxvZygiPT09YWRkUmVwb3J0PT4+PiIpDQogICAgICAgICAgICAgIC8vdGhpcy5tc2dTdWNjZXNzKCLmlrDlop7miJDlip8iKTsNCiAgICAgICAgICAgICAgZm9ybURhdGEucHJvamVjdElkID0gcmVzcG9uc2UuZGF0YTsNCiAgICAgICAgICAgICAgdGhhdC5mb3JtLnByb2plY3RJZCA9IHJlc3BvbnNlLmRhdGE7DQogICAgICAgICAgICAgIHRoYXQuc3RhcnRGbG93KGZvcm1EYXRhKTsNCiAgICAgICAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7DQogICAgICAgICAgICAgICAgbG9hZGluZy5jbG9zZSgpOw0KICAgICAgICAgICAgICAgIGRvY3VtZW50LmRvY3VtZW50RWxlbWVudC5zdHlsZS5vdmVyZmxvd1kgPSAnYXV0bycgLy/lhYHorrjlupXlsYJkaXbmu5rliqgNCiAgICAgICAgICAgICAgfSwgMTAwMCk7DQogICAgICAgICAgICB9KS5jYXRjaChyZXMgPT4gew0KICAgICAgICAgICAgICBjb25zb2xlLmxvZyhyZXMpDQogICAgICAgICAgICAgIGxvYWRpbmcuY2xvc2UoKTsNCiAgICAgICAgICAgICAgZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50LnN0eWxlLm92ZXJmbG93WSA9ICdhdXRvJyAvL+WFgeiuuOW6leWxgmRpdua7muWKqA0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIHN0YXJ0Rmxvdyhmb3JtRGF0YSkgew0KICAgICAgLy/pobnnm67ljLrln58NCiAgICAgIC8vdmFyIGFyZWEgPSBmb3JtRGF0YS5kaXN0cmljdDsNCiAgICAgIC8v55So5oi35Yy65Z+fIDYtMTHkv67mlLnvvIzmoLnmja7nlKjmiLfmiYDlnKjljLrln5/liKTmlq0NCiAgICAgIHZhciB2YXJpYWJsZXMgPSB7fTsNCiAgICAgIHZhcmlhYmxlcy5QUk9DRVNTX0FSRUEgPSB0aGlzLiRzdG9yZS5zdGF0ZS51c2VyLnByb3ZpbmNlOw0KICAgICAgLy/mmK/lkKbnnIHotJ/otKPkurrop5LoibINCiAgICAgIGlmICh0aGlzLiRzdG9yZS5zdGF0ZS51c2VyLnJvbGVzICYmIHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIucm9sZXMuaW5jbHVkZXMoInByb3ZpbmNlX2FkbWluIikpIHsNCiAgICAgICAgdmFyaWFibGVzLmlzTWFuYWdlID0gMTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHZhcmlhYmxlcy5pc01hbmFnZSA9IDA7DQogICAgICB9DQogICAgICAvL+aWsOWinuWFqOi1sOaKpeWkh+WuoeaJuea1geeoiw0KICAgICAgLy8gaWYoZm9ybURhdGEub3BlcmF0aW9uVHlwZSA9PSAnMicpew0KICAgICAgLy8gICB2YXJpYWJsZXMuaXNBdXRoID0gdHJ1ZTsNCiAgICAgIC8vIH1lbHNlew0KICAgICAgLy8gICB2YXJpYWJsZXMuaXNBdXRoID0gZmFsc2U7DQogICAgICAvLyB9DQogICAgICAvL3ZhcmlhYmxlcy5pc0F1dGggPSBmYWxzZTsNCiAgICAgIHZhcmlhYmxlcy5CVVNJTkVTU0tFWSA9IGZvcm1EYXRhLnByb2plY3RJZDsNCiAgICAgIHZhciB0YXNrTmFtZSA9ICLpobnnm67miqXlpIciOw0KICAgICAgaWYgKGZvcm1EYXRhLm9wZXJhdGlvblR5cGUgPT0gJzInKSB7DQogICAgICAgIHRhc2tOYW1lID0gIumhueebruaOiOadgyI7DQogICAgICB9DQogICAgICB0aGlzLiRyZWZzWydmbG93J10uc3RhcnRGbG93KGZvcm1EYXRhLnByb2plY3RJZCwgdGFza05hbWUsIHZhcmlhYmxlcyk7DQogICAgfSwNCiAgICBkb3dubG9hZFNRUygpIHsNCiAgICAgIHRoaXMuZG93bmxvYWQoIua1t+S9s+mbhuWboi3mjojmnYPkuaYuZG9jeCIsIGZhbHNlKTsNCiAgICB9LA0KICAgIGRvd25sb2FkQ1JIKCkgew0KICAgICAgdGhpcy5kb3dubG9hZCgi5rW35L2z6ZuG5ZuiLeWUruWQjuacjeWKoeaJv+ivuuWHvS5kb2MiLCBmYWxzZSk7DQogICAgfSwNCiAgICBoYW5kbGVDaGFuZ2UodmFsdWUpIHsNCiAgICAgIGlmICghdmFsdWUgfHwgdmFsdWUubGVuZ3RoID09IDApIHsNCiAgICAgICAgdGhpcy5zZWxlY3RlZE9wdGlvbnMgPSBudWxsOw0KICAgICAgICB0aGlzLmZvcm0ucHJvdmluY2UgPSB1bmRlZmluZWQ7DQogICAgICAgIHRoaXMuZm9ybS5kaXN0cmljdCA9IHVuZGVmaW5lZDsNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQogICAgICB0aGlzLnNlbGVjdGVkT3B0aW9ucyA9IHZhbHVlOw0KICAgICAgdmFyIHR4dCA9ICIiOw0KICAgICAgdmFsdWUuZm9yRWFjaChmdW5jdGlvbiAoaXRlbSkgew0KICAgICAgICB0eHQgKz0gQ29kZVRvVGV4dFtpdGVtXSArICIvIjsNCiAgICAgIH0pOw0KICAgICAgaWYgKHR4dC5sZW5ndGggPiAxKSB7DQogICAgICAgIHR4dCA9IHR4dC5zdWJzdHJpbmcoMCwgdHh0Lmxlbmd0aCAtIDEpOw0KICAgICAgICB0aGlzLmZvcm0ucHJvdmluY2UgPSB0eHQ7DQogICAgICAgIHRoaXMuZm9ybS5kaXN0cmljdCA9IHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIucHJvdmluY2U7DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLmZvcm0ucHJvdmluY2UgPSB1bmRlZmluZWQ7DQogICAgICAgIHRoaXMuZm9ybS5kaXN0cmljdCA9IHVuZGVmaW5lZDsNCiAgICAgIH0NCiAgICB9LA0KICAgIGhhbmRsZVF1ZXJ5Q2l0eUNoYW5nZSh2YWx1ZSkgew0KICAgICAgdGhpcy5xdWVyeUFyZWEgPSB2YWx1ZTsNCiAgICAgIHZhciB0eHQgPSAiIjsNCiAgICAgIHZhbHVlLmZvckVhY2goZnVuY3Rpb24gKGl0ZW0pIHsNCiAgICAgICAgdHh0ICs9IENvZGVUb1RleHRbaXRlbV0gKyAiLyI7DQogICAgICB9KTsNCiAgICAgIGlmICh0eHQubGVuZ3RoID4gMSkgew0KICAgICAgICB0eHQgPSB0eHQuc3Vic3RyaW5nKDAsIHR4dC5sZW5ndGggLSAxKTsNCiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wcm92aW5jZSA9IHR4dDsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMucXVlcnlQYXJhbXMucHJvdmluY2UgPSB1bmRlZmluZWQ7DQogICAgICB9DQogICAgfSwNCiAgICAvKiog5a6h5om5ICovDQogICAgaGFuZGxlQ29tcGxldGUoKSB7DQogICAgICB0aGlzLiRyZWZzWydmbG93J10uaGFuZGxlQ29tcGxldGUoKTsNCiAgICB9LA0KICAgIC8qKiDpgIDlm54gKi8NCiAgICBoYW5kbGVSZXR1cm4oKSB7DQogICAgICB0aGlzLiRyZWZzWydmbG93J10uaGFuZGxlUmV0dXJuKCk7DQogICAgfSwNCiAgICAvKiog6L+U5Zue6aG16Z2iICovDQogICAgZ29CYWNrKCkgew0KICAgICAgLy8g5YWz6Zet5b2T5YmN5qCH562+6aG15bm26L+U5Zue5LiK5Liq6aG16Z2iDQogICAgICB0aGlzLiRzdG9yZS5kaXNwYXRjaCgidGFnc1ZpZXcvZGVsVmlldyIsIHRoaXMuJHJvdXRlKTsNCiAgICAgIHRoaXMuJHJvdXRlci5nbygtMSkNCiAgICB9LA0KICAgIHJlbW92ZURvbWFpbihpbmRleCkgew0KICAgICAgaWYgKGluZGV4ICE9PSAtMSkgew0KICAgICAgICB0aGlzLmF1dGhDb21wYW55cy5zcGxpY2UoaW5kZXgsIDEpDQogICAgICB9DQogICAgfSwNCiAgICBhZGREb21haW4oKSB7DQogICAgICB0aGlzLmF1dGhDb21wYW55cy5wdXNoKHsNCiAgICAgICAgdmFsdWU6ICcnLA0KICAgICAgICBrZXk6IERhdGUubm93KCkNCiAgICAgIH0pOw0KICAgIH0NCiAgfSwNCn07DQo="}, null]}