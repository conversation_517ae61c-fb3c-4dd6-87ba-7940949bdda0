{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/map.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/map.vue", "mtime": 1660748060000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["map.vue"], "names": [], "mappings": ";;;;;;;;;;;AAWA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA", "file": "map.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\n  <div>\n    <div class=\"mapBox\" id=\"mapChar\" style=\"height:600px; width:100%;\"></div>\n    <div class=\"counting\">\n      <span>城市项目报备统计</span>\n      <el-row v-for=\"(item,index) in data\" :key=\"index\"><el-col><span class=\"name\">{{item.name}}</span> <span class=\"value\">{{item.value}}</span></el-col></el-row>\n    </div>\n  </div>\n</template>\n\n<script>\nimport * as echarts from \"echarts\";\nimport 'echarts/map/js/china.js' // 引入中国地图数据\nimport mapData from \"./china.json\";\nimport {indexReportCount} from \"@/api/project/report\";\n\nexport default {\n  name: \"index\",\n  data() {\n    return {\n      // 版本号\n      version: \"3.3.0\",\n      data:[]\n    };\n  },\n  mounted() {\n    \n    console.log(\"=======mounted== map=>>>\")\n    this.chartInit();\n    //this.map();\n    let _this = this;//赋值vue的this\n    window.onresize = ()=>{\n      //调用methods中的事件\n      _this.mapResize();\n    }\n    \n  },\n  methods: {\n    goTarget(href) {\n      window.open(href, \"_blank\");\n    },\n    mapResize(){\n      const oMap = echarts.init(document.getElementById(\"mapChar\"));\n      oMap.getDom().style.height = (window.innerHeight-86) +'px';//600 + \"px\";\n      oMap.resize();\n    },\n    chartInit() {\n      //初始Echarts实例对象\n      const oMap = echarts.init(document.getElementById(\"mapChar\"));\n\n      //指定加载省、市、县、区（注：由于这里没有用模块化方式导入，所以把zunyi.json文件改为zunyi.js文件，并在里面用一个zunyi常量来引入的）\n      echarts.registerMap(\"china\", mapData);\n      \n      var option = {\n        title: {\n          text: \"海佳彩亮项目管理系统\",\n          left: \"left\",\n        },\n        tooltip: {\n          trigger: \"item\",\n          showDelay: 0,\n          transitionDuration: 0.2,\n        },\n        visualMap: {\n          show:false,\n          left: \"right\",\n          min: 10,\n          max: 1000,\n          inRange: {\n            color: [\n              \"#D5DCE4\",\n              \"#AED1FF\",\n              \"#5E9BEB\",\n              \"#1A74EB\",\n            ],\n          },\n          text: [\"High\", \"Low\"],\n          calculable: true,\n        },\n        \n        series: [\n          {\n            name: \"项目报备\",\n            type: \"map\",\n            roam: false,//缩放\n            center: [104.114129, 37.550339],//当前视角的中心点\n            zoom: 1.4,//当前视角的缩放比例\n            map: \"china\",\n            emphasis: {\n              label: {\n                show: true,\n              },\n            },\n            data: [\n              // { name: \"福建\", value: 4822023 },\n              // { name: \"河北\", value: 73149 },\n              // { name: \"山西\", value: 655255 },\n              // { name: \"辽宁\", value: 291031 },\n              // { name: \"吉林\", value: 3801430 },\n              // { name: \"黑龙江\", value: 5187582 },\n              // { name: \"江苏\", value: 3590347 },\n              // { name: \"浙江\", value: 917092 },\n              // { name: \"安徽\", value: 632323 },\n              // { name: \"江西\", value: 19317568 },\n              // { name: \"山东\", value: 999945 },\n              // { name: \"河南\", value: 1392313 },\n              // { name: \"湖北\", value: 15728 },\n              // { name: \"湖南\", value: 12875255 },\n              // { name: \"广东\", value: 657334 },\n              // { name: \"海南\", value: 3074186 },\n              // { name: \"四川\", value: 2885905 },\n              // { name: \"贵州\", value: 4380415 },\n              // { name: \"云南\", value: 461893 },\n              // { name: \"陕西\", value: 1329192 },\n              // { name: \"甘肃\", value: 54563 },\n              // { name: \"青海\", value: 666144 },\n              // { name: \"台湾\", value: 9883360 },\n              // { name: \"北京\", value: 5379139 },\n              // { name: \"上海\", value: 2984926 },\n              // { name: \"重庆\", value: 6021988 },\n              // { name: \"天津\", value: 1005141 },\n              // { name: \"广西\", value: 1855525 },\n              // { name: \"宁夏\", value: 2758931 },\n              // { name: \"西藏\", value: 1320718 },\n              // { name: \"新疆\", value: 8864590 },\n              // { name: \"内蒙古\", value: 2085538 },\n              // { name: \"香港\", value: 19570261 },\n              // { name: \"澳门\", value: 9752073 },\n            ],\n            itemStyle: {\n              normal: {\n                borderWidth: 1.5,//边际线大小\n                //areaColor: '#323c48',\n                borderColor: '#fff'\n              },\n              emphasis: {//鼠标移入高亮显颜色\n                areaColor: '#FFC278'\n              }\n            },\n          },\n        ],\n      };\n      indexReportCount({}).then((response) => {\n        option.series[0].data = response.data;\n        this.data = response.data.slice(0, 10);\n        oMap.setOption(option);\n      })\n      \n    },\n    \n  },\n};\n</script>\n\n<style scoped lang=\"scss\">\n.mapBox{\n  background: url(\"../assets/images/home-bg.png\") center;\n  background-size:100%;\n\tbackground-repeat:no-repeat;\n}\n.counting{\n  position: absolute;\n  top: 100px;\n  left: 50px;\n}\n.counting .el-row{\n  margin-top: 15px;\n}\n.counting .el-col{\n  display: flex;\n  justify-content: space-between;\n  padding-bottom: 10px;\n  border-bottom: solid 1px #D7D7EB;\n}\n</style>\n\n"]}]}