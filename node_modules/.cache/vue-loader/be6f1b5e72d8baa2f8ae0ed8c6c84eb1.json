{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/layout/components/Navbar.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/layout/components/Navbar.vue", "mtime": 1655131056000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IG1hcEdldHRlcnMgfSBmcm9tICd2dWV4JwppbXBvcnQgQnJlYWRjcnVtYiBmcm9tICdAL2NvbXBvbmVudHMvQnJlYWRjcnVtYicKaW1wb3J0IEhhbWJ1cmdlciBmcm9tICdAL2NvbXBvbmVudHMvSGFtYnVyZ2VyJwppbXBvcnQgU2NyZWVuZnVsbCBmcm9tICdAL2NvbXBvbmVudHMvU2NyZWVuZnVsbCcKaW1wb3J0IFNpemVTZWxlY3QgZnJvbSAnQC9jb21wb25lbnRzL1NpemVTZWxlY3QnCmltcG9ydCBTZWFyY2ggZnJvbSAnQC9jb21wb25lbnRzL0hlYWRlclNlYXJjaCcKaW1wb3J0IE5vdGljZSBmcm9tICdAL2NvbXBvbmVudHMvTm90aWNlJwoKZXhwb3J0IGRlZmF1bHQgewogIGNvbXBvbmVudHM6IHsKICAgIEJyZWFkY3J1bWIsCiAgICBIYW1idXJnZXIsCiAgICBTY3JlZW5mdWxsLAogICAgU2l6ZVNlbGVjdCwKICAgIFNlYXJjaCwKICAgIE5vdGljZQogIH0sCiAgY29tcHV0ZWQ6IHsKICAgIC4uLm1hcEdldHRlcnMoWwogICAgICAnc2lkZWJhcicsCiAgICAgICdhdmF0YXInLAogICAgICAnZGV2aWNlJwogICAgXSksCiAgICBzZXR0aW5nOiB7CiAgICAgIGdldCgpIHsKICAgICAgICByZXR1cm4gdGhpcy4kc3RvcmUuc3RhdGUuc2V0dGluZ3Muc2hvd1NldHRpbmdzCiAgICAgIH0sCiAgICAgIHNldCh2YWwpIHsKICAgICAgICB0aGlzLiRzdG9yZS5kaXNwYXRjaCgnc2V0dGluZ3MvY2hhbmdlU2V0dGluZycsIHsKICAgICAgICAgIGtleTogJ3Nob3dTZXR0aW5ncycsCiAgICAgICAgICB2YWx1ZTogdmFsCiAgICAgICAgfSkKICAgICAgfQogICAgfQogIH0sCiAgbWV0aG9kczogewogICAgdG9nZ2xlU2lkZUJhcigpIHsKICAgICAgdGhpcy4kc3RvcmUuZGlzcGF0Y2goJ2FwcC90b2dnbGVTaWRlQmFyJykKICAgIH0sCiAgICBhc3luYyBsb2dvdXQoKSB7CiAgICAgIHRoaXMuJGNvbmZpcm0oJ+ehruWumuazqOmUgOW5tumAgOWHuuezu+e7n+WQl++8nycsICfmj5DnpLonLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLAogICAgICAgIHR5cGU6ICd3YXJuaW5nJwogICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICB0aGlzLiRzdG9yZS5kaXNwYXRjaCgnTG9nT3V0JykudGhlbigoKSA9PiB7CiAgICAgICAgICBsb2NhdGlvbi5ocmVmID0gJy9pbmRleCc7CiAgICAgICAgfSkKICAgICAgfSkKICAgIH0sCiAgICB1c2VyTmFtZSgpewogICAgICByZXR1cm4gdGhpcy4kc3RvcmUuc3RhdGUudXNlci5uYW1lOwogICAgfQogIH0KfQo="}, {"version": 3, "sources": ["Navbar.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqDA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Navbar.vue", "sourceRoot": "src/layout/components", "sourcesContent": ["<template>\n  <div class=\"navbar\">\n    <hamburger id=\"hamburger-container\" :is-active=\"sidebar.opened\" class=\"hamburger-container\" @toggleClick=\"toggleSideBar\" />\n\n    <breadcrumb id=\"breadcrumb-container\" class=\"breadcrumb-container\" />\n\n    <div class=\"right-menu\">\n      <template v-if=\"device!=='mobile'\">\n        <search id=\"header-search\" class=\"right-menu-item\" />\n\n        <el-tooltip content=\"访问官网\" effect=\"dark\" placement=\"bottom\">\n          <a href=\"http://www.clled.com/\" target=\"_blank\" class=\"right-menu-item hover-effect\">\n            <i class=\"el-icon-house\"></i>\n          </a>\n        </el-tooltip>\n\n        <screenfull id=\"screenfull\" class=\"right-menu-item hover-effect\" />\n\n        <el-tooltip content=\"布局大小\" effect=\"dark\" placement=\"bottom\">\n          <size-select id=\"size-select\" class=\"right-menu-item hover-effect\" />\n        </el-tooltip>\n\n        <el-tooltip content=\"通知中心\" effect=\"dark\" placement=\"bottom\">\n          <notice class=\"right-menu-item\" />\n        </el-tooltip>\n\n      </template>\n\n      <el-dropdown class=\"avatar-container right-menu-item hover-effect\" trigger=\"click\">\n        <div class=\"avatar-wrapper\">\n          <img :src=\"avatar\" class=\"user-avatar\">\n          <i class=\"el-icon-caret-bottom\" />\n        </div>\n        <el-dropdown-menu slot=\"dropdown\">\n          <el-dropdown-item>\n            <span>账号:{{userName()}}</span>\n          </el-dropdown-item>\n          <router-link to=\"/user/profile\">\n            <el-dropdown-item>个人中心</el-dropdown-item>\n          </router-link>\n          <el-dropdown-item @click.native=\"setting = true\">\n            <span>布局设置</span>\n          </el-dropdown-item>\n          <el-dropdown-item divided @click.native=\"logout\">\n            <span>退出登录</span>\n          </el-dropdown-item>\n        </el-dropdown-menu>\n      </el-dropdown>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { mapGetters } from 'vuex'\nimport Breadcrumb from '@/components/Breadcrumb'\nimport Hamburger from '@/components/Hamburger'\nimport Screenfull from '@/components/Screenfull'\nimport SizeSelect from '@/components/SizeSelect'\nimport Search from '@/components/HeaderSearch'\nimport Notice from '@/components/Notice'\n\nexport default {\n  components: {\n    Breadcrumb,\n    Hamburger,\n    Screenfull,\n    SizeSelect,\n    Search,\n    Notice\n  },\n  computed: {\n    ...mapGetters([\n      'sidebar',\n      'avatar',\n      'device'\n    ]),\n    setting: {\n      get() {\n        return this.$store.state.settings.showSettings\n      },\n      set(val) {\n        this.$store.dispatch('settings/changeSetting', {\n          key: 'showSettings',\n          value: val\n        })\n      }\n    }\n  },\n  methods: {\n    toggleSideBar() {\n      this.$store.dispatch('app/toggleSideBar')\n    },\n    async logout() {\n      this.$confirm('确定注销并退出系统吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        this.$store.dispatch('LogOut').then(() => {\n          location.href = '/index';\n        })\n      })\n    },\n    userName(){\n      return this.$store.state.user.name;\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.navbar {\n  height: 50px;\n  overflow: hidden;\n  position: relative;\n  background: #fff;\n  box-shadow: 0 1px 4px rgba(0,21,41,.08);\n\n  .hamburger-container {\n    line-height: 46px;\n    height: 100%;\n    float: left;\n    cursor: pointer;\n    transition: background .3s;\n    -webkit-tap-highlight-color:transparent;\n\n    &:hover {\n      background: rgba(0, 0, 0, .025)\n    }\n  }\n\n  .breadcrumb-container {\n    float: left;\n  }\n\n  .errLog-container {\n    display: inline-block;\n    vertical-align: top;\n  }\n\n  .right-menu {\n    float: right;\n    height: 100%;\n    line-height: 50px;\n\n    &:focus {\n      outline: none;\n    }\n\n    .right-menu-item {\n      display: inline-block;\n      padding: 0 8px;\n      height: 100%;\n      font-size: 18px;\n      color: #5a5e66;\n      vertical-align: text-bottom;\n\n      &.hover-effect {\n        cursor: pointer;\n        transition: background .3s;\n\n        &:hover {\n          background: rgba(0, 0, 0, .025)\n        }\n      }\n    }\n\n    .avatar-container {\n      margin-right: 30px;\n\n      .avatar-wrapper {\n        margin-top: 5px;\n        position: relative;\n\n        .user-avatar {\n          cursor: pointer;\n          width: 40px;\n          height: 40px;\n          border-radius: 10px;\n        }\n\n        .el-icon-caret-bottom {\n          cursor: pointer;\n          position: absolute;\n          right: -20px;\n          top: 25px;\n          font-size: 12px;\n        }\n      }\n    }\n  }\n}\n</style>\n"]}]}