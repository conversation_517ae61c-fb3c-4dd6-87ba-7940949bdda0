{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/login.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/login.vue", "mtime": 1716984016441}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IHJlZ2lzdGVyIH0gZnJvbSAiQC9hcGkvc3lzdGVtL3VzZXIiOwppbXBvcnQgeyBnZXRDb2RlSW1nLCByZXNldENhcHRjaGEsIHJlc2V0UHdkQnlQaG9uZSB9IGZyb20gIkAvYXBpL2xvZ2luIjsKaW1wb3J0IENvb2tpZXMgZnJvbSAianMtY29va2llIjsKaW1wb3J0IHsgZW5jcnlwdCwgZGVjcnlwdCB9IGZyb20gIkAvdXRpbHMvanNlbmNyeXB0IjsKaW1wb3J0IEltYWdlVXBsb2FkIGZyb20gIkAvY29tcG9uZW50cy9JbWFnZVVwbG9hZCI7CmltcG9ydCB7IHJlZ2lvbkRhdGEsIENvZGVUb1RleHQgfSBmcm9tICJlbGVtZW50LWNoaW5hLWFyZWEtZGF0YSI7CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiTG9naW4iLAogIGNvbXBvbmVudHM6IHsgSW1hZ2VVcGxvYWQgfSwKICBkYXRhKCkgewogICAgLy8g6Ieq5a6a5LmJ5qCh6aqM6KeE5YiZCiAgICB2YXIgYmFyZ2FpblBpYyA9IChydWxlLCB2YWx1ZSwgY2FsbGJhY2spID0+IHsKICAgICAgaWYgKCF0aGlzLmltZ05hbWUpIHsKICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoIuiQpeS4muaJp+eFp+eFp+eJh+W/heS8oCIpKTsKICAgICAgfSBlbHNlIHsKICAgICAgICBjYWxsYmFjaygpOwogICAgICB9CiAgICB9OwogICAgcmV0dXJuIHsKICAgICAgY29kZVVybDogIiIsCiAgICAgIGNvb2tpZVBhc3N3b3JkOiAiIiwKICAgICAgbG9naW5Gb3JtOiB7CiAgICAgICAgdXNlcm5hbWU6ICIiLAogICAgICAgIHBhc3N3b3JkOiAiIiwKICAgICAgICByZW1lbWJlck1lOiBmYWxzZSwKICAgICAgICBjb2RlOiAiIiwKICAgICAgICB1dWlkOiAiIiwKICAgICAgfSwKICAgICAgbG9naW5SdWxlczogewogICAgICAgIHVzZXJuYW1lOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCB0cmlnZ2VyOiAiYmx1ciIsIG1lc3NhZ2U6ICLnlKjmiLflkI3kuI3og73kuLrnqboiIH0sCiAgICAgICAgXSwKICAgICAgICBwYXNzd29yZDogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgdHJpZ2dlcjogImJsdXIiLCBtZXNzYWdlOiAi5a+G56CB5LiN6IO95Li656m6IiB9LAogICAgICAgIF0sCiAgICAgICAgY29kZTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgdHJpZ2dlcjogImNoYW5nZSIsIG1lc3NhZ2U6ICLpqozor4HnoIHkuI3og73kuLrnqboiIH0sCiAgICAgICAgXSwKICAgICAgfSwKICAgICAgbG9hZGluZzogZmFsc2UsCiAgICAgIHJlZGlyZWN0OiB1bmRlZmluZWQsCiAgICAgIC8vIOW8ueWHuuWxguagh+mimAogICAgICB0aXRsZTogIiIsCiAgICAgIC8vIOaYr+WQpuaYvuekuuW8ueWHuuWxggogICAgICBvcGVuOiBmYWxzZSwKICAgICAgLy8g6KGo5Y2V5qCh6aqMCiAgICAgIHJ1bGVzOiB7CiAgICAgICAgdXNlck5hbWU6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLnlKjmiLflkI3kuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwKICAgICAgICBdLAogICAgICAgIGNvbXBhbnk6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLlhazlj7jlhajnp7DkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwKICAgICAgICBdLAogICAgICAgIGJ1c2luZXNzTm86IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLokKXkuJrmiafnhaflj7fnoIHkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwKICAgICAgICBdLAogICAgICAgIHByb3ZpbmNlOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5omA5Zyo5Yy65Z+f5b+F6YCJIiwgdHJpZ2dlcjogImJsdXIiIH0sCiAgICAgICAgXSwKICAgICAgICBpbWdOYW1lOiBbCiAgICAgICAgICB7CiAgICAgICAgICAgIHZhbGlkYXRvcjogYmFyZ2FpblBpYywKICAgICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICAgIG1lc3NhZ2U6ICLokKXkuJrmiafnhafnhafniYflv4XkvKAiLAogICAgICAgICAgICB0cmlnZ2VyOiAiY2hhbmdlIiwKICAgICAgICAgIH0sCiAgICAgICAgXSwKICAgICAgICBwYXNzd29yZDogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgdHJpZ2dlcjogImJsdXIiLCBtZXNzYWdlOiAi5a+G56CB5LiN6IO95Li656m6IiB9LAogICAgICAgIF0sCiAgICAgICAgcGFzc3dvcmQyOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCB0cmlnZ2VyOiAiYmx1ciIsIG1lc3NhZ2U6ICLnoa7orqTlr4bnoIHkuI3og73kuLrnqboiIH0sCiAgICAgICAgXSwKICAgICAgICBlbWFpbDogWwogICAgICAgICAgewogICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgICAgdHlwZTogImVtYWlsIiwKICAgICAgICAgICAgbWVzc2FnZTogIifor7fovpPlhaXmraPnoa7nmoTpgq7nrrHlnLDlnYAiLAogICAgICAgICAgICB0cmlnZ2VyOiBbImJsdXIiLCAiY2hhbmdlIl0sCiAgICAgICAgICB9LAogICAgICAgIF0sCiAgICAgICAgcGhvbmVudW1iZXI6IFsKICAgICAgICAgIHsKICAgICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICAgIHBhdHRlcm46IC9eMVszfDR8NXw2fDd8OHw5XVswLTldXGR7OH0kLywKICAgICAgICAgICAgbWVzc2FnZTogIuivt+i+k+WFpeato+ehrueahOaJi+acuuWPt+eggSIsCiAgICAgICAgICAgIHRyaWdnZXI6ICJibHVyIiwKICAgICAgICAgIH0sCiAgICAgICAgXSwKICAgICAgICBuaWNrTmFtZTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuaKpeWkh+S6uuWnk+WQjeS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9LAogICAgICAgIF0sCiAgICAgICAgYWRkcmVzczogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIui1hOaWmemCruWvhOWcsOWdgOS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9LAogICAgICAgIF0sCiAgICAgICAgY29kZTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgdHJpZ2dlcjogImNoYW5nZSIsIG1lc3NhZ2U6ICLpqozor4HnoIHkuI3og73kuLrnqboiIH0sCiAgICAgICAgXSwKICAgICAgfSwKICAgICAgLy8g5oCn5Yir54q25oCB5a2X5YW4CiAgICAgIHNleE9wdGlvbnM6IFtdLAogICAgICAvLyDnn63kv6HpgJrnn6XlrZflhbgKICAgICAgc21zU2VuZE9wdGlvbnM6IFtdLAogICAgICAvLyDlrqHmoLjnirbmgIHlrZflhbgKICAgICAgYXVkaXRTdGF0dXNPcHRpb25zOiBbXSwKICAgICAgLy8g6KeS6Imy6YCJ6aG5CiAgICAgIHJvbGVPcHRpb25zOiBbXSwKICAgICAgLy8g6KGo5Y2V5Y+C5pWwCiAgICAgIGZvcm06IHt9LAogICAgICBwcm92aW5jZUFuZENpdHlEYXRhOiByZWdpb25EYXRhLAogICAgICBjaXR5T3B0aW9uczogW10sCiAgICAgIGltZzogdW5kZWZpbmVkLAogICAgICBpbWdOYW1lOiB1bmRlZmluZWQsCiAgICAgIGltZ0ZpbGU6IHVuZGVmaW5lZCwKICAgICAgcmVzdE9wZW46IGZhbHNlLAogICAgICByZXNldENvZGVUeHQ6ICLojrflj5bpqozor4HnoIEiLAogICAgICByZXNldFB3ZEZvcm06IHsKICAgICAgICB1c2VybmFtZTogIiIsCiAgICAgICAgcGFzc3dvcmQ6ICIiLAogICAgICAgIGNvZGU6ICIiLAogICAgICB9LAogICAgICByZXNldFJ1bGVzOiB7CiAgICAgICAgdXNlcm5hbWU6IFsKICAgICAgICAgIHsKICAgICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICAgIHBhdHRlcm46IC9eMVszfDR8NXw2fDd8OHw5XVswLTldXGR7OH0kLywKICAgICAgICAgICAgbWVzc2FnZTogIuivt+i+k+WFpeato+ehrueahOaJi+acuuWPt+eggSIsCiAgICAgICAgICAgIHRyaWdnZXI6ICJibHVyIiwKICAgICAgICAgIH0sCiAgICAgICAgXSwKICAgICAgICBwYXNzd29yZDogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgdHJpZ2dlcjogImJsdXIiLCBtZXNzYWdlOiAi5a+G56CB5LiN6IO95Li656m6IiB9LAogICAgICAgIF0sCiAgICAgICAgY29kZTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgdHJpZ2dlcjogImNoYW5nZSIsIG1lc3NhZ2U6ICLpqozor4HnoIHkuI3og73kuLrnqboiIH0sCiAgICAgICAgXSwKICAgICAgfSwKICAgIH07CiAgfSwKICB3YXRjaDogewogICAgJHJvdXRlOiB7CiAgICAgIGhhbmRsZXI6IGZ1bmN0aW9uIChyb3V0ZSkgewogICAgICAgIHRoaXMucmVkaXJlY3QgPSByb3V0ZS5xdWVyeSAmJiByb3V0ZS5xdWVyeS5yZWRpcmVjdDsKICAgICAgfSwKICAgICAgaW1tZWRpYXRlOiB0cnVlLAogICAgfSwKICB9LAogIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmdldENvZGUoKTsKICAgIHRoaXMuZ2V0Q29va2llKCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICBnZXRDb2RlKCkgewogICAgICBnZXRDb2RlSW1nKCkudGhlbigocmVzKSA9PiB7CiAgICAgICAgdGhpcy5jb2RlVXJsID0gImRhdGE6aW1hZ2UvZ2lmO2Jhc2U2NCwiICsgcmVzLmltZzsKICAgICAgICB0aGlzLmxvZ2luRm9ybS51dWlkID0gcmVzLnV1aWQ7CiAgICAgIH0pOwogICAgfSwKICAgIGdldENvb2tpZSgpIHsKICAgICAgY29uc3QgdXNlcm5hbWUgPSBDb29raWVzLmdldCgidXNlcm5hbWUiKTsKICAgICAgY29uc3QgcGFzc3dvcmQgPSBDb29raWVzLmdldCgicGFzc3dvcmQiKTsKICAgICAgY29uc3QgcmVtZW1iZXJNZSA9IENvb2tpZXMuZ2V0KCJyZW1lbWJlck1lIik7CiAgICAgIHRoaXMubG9naW5Gb3JtID0gewogICAgICAgIHVzZXJuYW1lOiB1c2VybmFtZSA9PT0gdW5kZWZpbmVkID8gdGhpcy5sb2dpbkZvcm0udXNlcm5hbWUgOiB1c2VybmFtZSwKICAgICAgICBwYXNzd29yZDoKICAgICAgICAgIHBhc3N3b3JkID09PSB1bmRlZmluZWQgPyB0aGlzLmxvZ2luRm9ybS5wYXNzd29yZCA6IGRlY3J5cHQocGFzc3dvcmQpLAogICAgICAgIHJlbWVtYmVyTWU6IHJlbWVtYmVyTWUgPT09IHVuZGVmaW5lZCA/IGZhbHNlIDogQm9vbGVhbihyZW1lbWJlck1lKSwKICAgICAgfTsKICAgIH0sCiAgICBoYW5kbGVMb2dpbigpIHsKICAgICAgdGhpcy4kcmVmcy5sb2dpbkZvcm0udmFsaWRhdGUoKHZhbGlkKSA9PiB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOwogICAgICAgICAgaWYgKHRoaXMubG9naW5Gb3JtLnJlbWVtYmVyTWUpIHsKICAgICAgICAgICAgQ29va2llcy5zZXQoInVzZXJuYW1lIiwgdGhpcy5sb2dpbkZvcm0udXNlcm5hbWUsIHsgZXhwaXJlczogMzAgfSk7CiAgICAgICAgICAgIENvb2tpZXMuc2V0KCJwYXNzd29yZCIsIGVuY3J5cHQodGhpcy5sb2dpbkZvcm0ucGFzc3dvcmQpLCB7CiAgICAgICAgICAgICAgZXhwaXJlczogMzAsCiAgICAgICAgICAgIH0pOwogICAgICAgICAgICBDb29raWVzLnNldCgicmVtZW1iZXJNZSIsIHRoaXMubG9naW5Gb3JtLnJlbWVtYmVyTWUsIHsKICAgICAgICAgICAgICBleHBpcmVzOiAzMCwKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICBDb29raWVzLnJlbW92ZSgidXNlcm5hbWUiKTsKICAgICAgICAgICAgQ29va2llcy5yZW1vdmUoInBhc3N3b3JkIik7CiAgICAgICAgICAgIENvb2tpZXMucmVtb3ZlKCJyZW1lbWJlck1lIik7CiAgICAgICAgICB9CiAgICAgICAgICB0aGlzLiRzdG9yZQogICAgICAgICAgICAuZGlzcGF0Y2goIkxvZ2luIiwgdGhpcy5sb2dpbkZvcm0pCiAgICAgICAgICAgIC50aGVuKCgpID0+IHsKICAgICAgICAgICAgICB0aGlzLiRyb3V0ZXIucHVzaCh7IHBhdGg6IHRoaXMucmVkaXJlY3QgfHwgIi8iIH0pLmNhdGNoKCgpID0+IHt9KTsKICAgICAgICAgICAgfSkKICAgICAgICAgICAgLmNhdGNoKCgpID0+IHsKICAgICAgICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgICAgICAgICB0aGlzLmdldENvZGUoKTsKICAgICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvLyDooajljZXph43nva4KICAgIHJlc2V0KCkgewogICAgICB0aGlzLmZvcm0gPSB7CiAgICAgICAgdXNlcklkOiB1bmRlZmluZWQsCiAgICAgICAgdXNlck5hbWU6IHVuZGVmaW5lZCwKICAgICAgICBuaWNrTmFtZTogdW5kZWZpbmVkLAogICAgICAgIHVzZXJUeXBlOiB1bmRlZmluZWQsCiAgICAgICAgZW1haWw6IHVuZGVmaW5lZCwKICAgICAgICBwaG9uZW51bWJlcjogdW5kZWZpbmVkLAogICAgICAgIHNleDogIjIiLAogICAgICAgIGF2YXRhcjogdW5kZWZpbmVkLAogICAgICAgIHBhc3N3b3JkOiB1bmRlZmluZWQsCiAgICAgICAgY29tcGFueTogdW5kZWZpbmVkLAogICAgICAgIGJ1c2luZXNzTm86IHVuZGVmaW5lZCwKICAgICAgICBidXNpbmVzc05vUGljOiB1bmRlZmluZWQsCiAgICAgICAgcHJvdmluY2U6IHVuZGVmaW5lZCwKICAgICAgICBhZGRyZXNzOiB1bmRlZmluZWQsCiAgICAgICAgZGVhbGVyOiB1bmRlZmluZWQsCiAgICAgIH07CiAgICAgIHRoaXMuaW1nID0gdW5kZWZpbmVkOwogICAgICB0aGlzLmltZ05hbWUgPSB1bmRlZmluZWQ7CiAgICAgIHRoaXMuaW1nRmlsZSA9IHVuZGVmaW5lZDsKICAgICAgdGhpcy5yZXNldEZvcm0oImZvcm0iKTsKICAgIH0sCiAgICAvLyDlj5bmtojmjInpkq4KICAgIGNhbmNlbCgpIHsKICAgICAgdGhpcy5vcGVuID0gZmFsc2U7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgIH0sCiAgICAvKiog5paw5aKe5oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVBZGQoKSB7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgICAgLy90aGlzLmdldFRyZWVzZWxlY3QoKTsKICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsKICAgICAgdGhpcy50aXRsZSA9ICLnlKjmiLfms6jlhowiOwogICAgfSwKICAgIGhhbmRsZVJlc3QoKSB7CiAgICAgIC8vdGhpcy5nZXRUcmVlc2VsZWN0KCk7CiAgICAgIHRoaXMucmVzdE9wZW4gPSB0cnVlOwogICAgfSwKICAgIC8qKiDmj5DkuqTmjInpkq4gKi8KICAgIHN1Ym1pdEZvcm06IGZ1bmN0aW9uICgpIHsKICAgICAgaWYgKHRoaXMuZm9ybS5wYXNzd29yZCAhPSB0aGlzLmZvcm0ucGFzc3dvcmQyKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi5Lik5qyh6L6T5YWl5a+G56CB5LiN5LiA6Ie0Iik7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIC8vIHRoaXMuZm9ybS5maWxlID0gdGhpcy5pbWdGaWxlOwogICAgICAvLyBjb25zb2xlLmxvZyh0aGlzLmZvcm0pCgogICAgICB0aGlzLiRyZWZzWyJmb3JtIl0udmFsaWRhdGUoKHZhbGlkKSA9PiB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICBsZXQgcGFyYW0gPSBuZXcgRm9ybURhdGEoKTsKICAgICAgICAgIHBhcmFtLmFwcGVuZCgiZmlsZSIsIHRoaXMuaW1nRmlsZSk7CiAgICAgICAgICBwYXJhbS5hcHBlbmQoInVzZXJOYW1lIiwgdGhpcy5mb3JtLnVzZXJOYW1lKTsKICAgICAgICAgIHBhcmFtLmFwcGVuZCgibmlja05hbWUiLCB0aGlzLmZvcm0ubmlja05hbWUpOwogICAgICAgICAgcGFyYW0uYXBwZW5kKCJlbWFpbCIsIHRoaXMuZm9ybS5lbWFpbCk7CiAgICAgICAgICBwYXJhbS5hcHBlbmQoInBob25lbnVtYmVyIiwgdGhpcy5mb3JtLnBob25lbnVtYmVyKTsKICAgICAgICAgIHBhcmFtLmFwcGVuZCgicGFzc3dvcmQiLCB0aGlzLmZvcm0ucGFzc3dvcmQpOwogICAgICAgICAgcGFyYW0uYXBwZW5kKCJjb21wYW55IiwgdGhpcy5mb3JtLmNvbXBhbnkpOwogICAgICAgICAgcGFyYW0uYXBwZW5kKCJidXNpbmVzc05vIiwgdGhpcy5mb3JtLmJ1c2luZXNzTm8pOwogICAgICAgICAgcGFyYW0uYXBwZW5kKCJwcm92aW5jZSIsIHRoaXMuZm9ybS5wcm92aW5jZSk7CiAgICAgICAgICBwYXJhbS5hcHBlbmQoImFkZHJlc3MiLCB0aGlzLmZvcm0uYWRkcmVzcyk7CiAgICAgICAgICBwYXJhbS5hcHBlbmQoImRlYWxlciIsIHRoaXMuZm9ybS5kZWFsZXIpOwogICAgICAgICAgYGA7CiAgICAgICAgICByZWdpc3RlcihwYXJhbSkudGhlbigocmVzcG9uc2UpID0+IHsKICAgICAgICAgICAgdGhpcy5tc2dTdWNjZXNzKCLmj5DkuqTmiJDlip8iKTsKICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7CiAgICAgICAgICAgIHRoaXMucmVzZXQoKTsKICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgaGFuZGxlQ2l0eUNoYW5nZSh2YWx1ZSkgewogICAgICB0aGlzLmNpdHlPcHRpb25zID0gdmFsdWU7CiAgICAgIHZhciB0eHQgPSAiIjsKICAgICAgdmFsdWUuZm9yRWFjaChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgIHR4dCArPSBDb2RlVG9UZXh0W2l0ZW1dICsgIi8iOwogICAgICB9KTsKICAgICAgaWYgKHR4dC5sZW5ndGggPiAxKSB7CiAgICAgICAgdHh0ID0gdHh0LnN1YnN0cmluZygwLCB0eHQubGVuZ3RoIC0gMSk7CiAgICAgICAgdGhpcy5mb3JtLnByb3ZpbmNlID0gdHh0OwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuZm9ybS5wcm92aW5jZSA9IHVuZGVmaW5lZDsKICAgICAgfQogICAgfSwKICAgIGFkZEltZyhmaWxlMSkgewogICAgICBjb25zdCBmaWxlID0gZmlsZTEucmF3OwogICAgICBjb25zdCBpc0pQRyA9IGZpbGUudHlwZSA9PT0gImltYWdlL2pwZWciOwogICAgICBjb25zdCBpc1BORyA9IGZpbGUudHlwZSA9PT0gImltYWdlL3BuZyI7CiAgICAgIGNvbnN0IGlzV0VCUCA9IGZpbGUudHlwZSA9PT0gImltYWdlL3dlYnAiOwogICAgICBjb25zdCBpc0x0MU0gPSBmaWxlLnNpemUgLyAxMDI0IC8gMTAyNCA8IDE7CgogICAgICBpZiAoIWlzSlBHICYmICFpc1BORyAmJiAhaXNXRUJQKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi5LiK5Lyg5Zu+54mH5Y+q6IO95pivIEpQR+OAgVBOR+OAgVdFQlAg5qC85byPISIpOwogICAgICB9IGVsc2UgaWYgKCFpc0x0MU0pIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLkuIrkvKDlm77niYflpKflsI/kuI3og73otoXov4cgMU1CISIpOwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuaW1nRmlsZSA9IGZpbGU7CiAgICAgICAgdGhpcy5pbWdOYW1lID0gZmlsZS5uYW1lOwogICAgICAgIHZhciBzZWxmID0gdGhpczsKICAgICAgICAvL+WumuS5ieS4gOS4quaWh+S7tumYheivu+WZqAogICAgICAgIHZhciByZWFkZXIgPSBuZXcgRmlsZVJlYWRlcigpOwogICAgICAgIC8v5paH5Lu26KOF6L295ZCO5bCG5YW25pi+56S65Zyo5Zu+54mH6aKE6KeI6YeMCiAgICAgICAgcmVhZGVyLm9ubG9hZCA9IGZ1bmN0aW9uIChlKSB7CiAgICAgICAgICAvL+WwhmJhZGU2NOS9jeWbvueJh+S/neWtmOiHs+aVsOe7hOmHjOS+m+S4iumdouWbvueJh+aYvuekugogICAgICAgICAgc2VsZi5pbWcgPSBlLnRhcmdldC5yZXN1bHQ7CiAgICAgICAgfTsKICAgICAgICByZWFkZXIucmVhZEFzRGF0YVVSTChmaWxlKTsKICAgICAgfQogICAgICB0aGlzLiRyZWZzLmZvcm0udmFsaWRhdGVGaWVsZCgiaW1nTmFtZSIpOwogICAgfSwKICAgIHJlbW92ZUltYWdlKCkgewogICAgICB0aGlzLmltZ0ZpbGUgPSB1bmRlZmluZWQ7CiAgICAgIHRoaXMuaW1nID0gdW5kZWZpbmVkOwogICAgICB0aGlzLmltZ05hbWUgPSB1bmRlZmluZWQ7CiAgICB9LAogICAgZ2V0UGhvbmVDb2RlKCkgewogICAgICBsZXQgdGhhdCA9IHRoaXM7CiAgICAgIGlmICh0aGlzLnJlc2V0UHdkRm9ybS51c2VybmFtZSA9PSAiIikgewogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuivt+i+k+WFpeaJi+acuuWPtyIpOwogICAgICAgIHJldHVybjsKICAgICAgfQoKICAgICAgcmVzZXRDYXB0Y2hhKHRoaXMucmVzZXRQd2RGb3JtKS50aGVuKChyZXNwKSA9PiB7CiAgICAgICAgdGhhdC5yZXNldENvZGVUeHQgPSAiNjBz5ZCO6YeN5paw6I635Y+WIjsKICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHsKICAgICAgICAgIHRoYXQucmVzZXRDb2RlVHh0ID0gIuiOt+WPlumqjOivgeeggSI7CiAgICAgICAgfSwgNjAgKiAxMDAwKTsKICAgICAgfSk7CiAgICB9LAogICAgaGFuZFJlc2V0UHdkKCkgewogICAgICBsZXQgdGhhdCA9IHRoaXM7CiAgICAgIHRoaXMuJHJlZnNbInJlc2V0UHdkRm9ybSJdLnZhbGlkYXRlKCh2YWxpZCkgPT4gewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgaWYgKAogICAgICAgICAgICB0aGlzLnJlc2V0UHdkRm9ybS5wYXNzd29yZCA9PSAiIiB8fAogICAgICAgICAgICB0aGlzLnJlc2V0UHdkRm9ybS5wYXNzd29yZCAhPSB0aGlzLnJlc2V0UHdkRm9ybS5wYXNzd29yZDIKICAgICAgICAgICkgewogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLkuKTmrKHovpPlhaXlr4bnoIHkuI3kuIDoh7QiKTsKICAgICAgICAgICAgcmV0dXJuOwogICAgICAgICAgfQogICAgICAgICAgaWYgKHRoaXMucmVzZXRQd2RGb3JtLnBhc3N3b3JkLmxlbmd0aCA8IDYpIHsKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi5a+G56CB6ZW/5bqm5LiN6IO95bCP5LqOIik7CiAgICAgICAgICAgIHJldHVybjsKICAgICAgICAgIH0KICAgICAgICAgIHJlc2V0UHdkQnlQaG9uZSh0aGlzLnJlc2V0UHdkRm9ybSkudGhlbigocmVzcCkgPT4gewogICAgICAgICAgICB0aGlzLm1zZ1N1Y2Nlc3MoIumHjee9ruaIkOWKnyIpOwogICAgICAgICAgICB0aGF0LnJlc3RPcGVuID0gZmFsc2U7CiAgICAgICAgICB9KTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICB9LAp9Owo="}, {"version": 3, "sources": ["login.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyUA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "login.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\n  <div class=\"login login-padding\">\n    <div class=\"logo-wrapper\">\n      <div class=\"logo\">\n        <img src=\"../assets/images/logo.png\" alt=\"logo\" />\n      </div>\n    </div>\n    <el-form\n      ref=\"loginForm\"\n      :model=\"loginForm\"\n      :rules=\"loginRules\"\n      class=\"login-form\"\n    >\n      <h3 class=\"title\">项目管理系统</h3>\n      <el-form-item prop=\"username\">\n        <el-input\n          v-model=\"loginForm.username\"\n          type=\"text\"\n          auto-complete=\"off\"\n          placeholder=\"账号\"\n        >\n          <svg-icon\n            slot=\"prefix\"\n            icon-class=\"user\"\n            class=\"el-input__icon input-icon\"\n          />\n        </el-input>\n      </el-form-item>\n      <el-form-item prop=\"password\">\n        <el-input\n          v-model=\"loginForm.password\"\n          type=\"password\"\n          auto-complete=\"off\"\n          placeholder=\"密码\"\n          @keyup.enter.native=\"handleLogin\"\n        >\n          <svg-icon\n            slot=\"prefix\"\n            icon-class=\"password\"\n            class=\"el-input__icon input-icon\"\n          />\n        </el-input>\n      </el-form-item>\n      <el-form-item prop=\"code\">\n        <el-input\n          v-model=\"loginForm.code\"\n          auto-complete=\"off\"\n          placeholder=\"验证码\"\n          style=\"width: 63%\"\n          @keyup.enter.native=\"handleLogin\"\n        >\n          <svg-icon\n            slot=\"prefix\"\n            icon-class=\"validCode\"\n            class=\"el-input__icon input-icon\"\n          />\n        </el-input>\n        <div class=\"login-code\">\n          <img :src=\"codeUrl\" @click=\"getCode\" class=\"login-code-img\" />\n        </div>\n      </el-form-item>\n\n      <el-row :gutter=\"20\">\n        <el-col :span=\"7\">\n          <el-checkbox\n            v-model=\"loginForm.rememberMe\"\n            style=\"margin: 0px 0px 25px 0px\"\n            >记住密码</el-checkbox\n          >\n        </el-col>\n        <el-col :span=\"7\" :offset=\"4\"\n          ><el-link @click=\"handleRest\" type=\"primary\"\n            >忘记密码</el-link\n          ></el-col\n        >\n        <el-col :span=\"6\" style=\"padding-left: 10%\"\n          ><el-link @click=\"handleAdd\" type=\"primary\">注册</el-link></el-col\n        >\n      </el-row>\n      <el-form-item style=\"width: 100%\">\n        <el-button\n          :loading=\"loading\"\n          size=\"medium\"\n          type=\"primary\"\n          style=\"width: 100%\"\n          @click.native.prevent=\"handleLogin\"\n        >\n          <span v-if=\"!loading\">登 录</span>\n          <span v-else>登 录 中...</span>\n        </el-button>\n      </el-form-item>\n    </el-form>\n    <!--  底部  -->\n    <div class=\"el-login-footer\">\n      <span\n        >Copyright © 2018 福建省海佳集团股份有限公司.All Rights Reserved.\n        闽ICP备18002975号</span\n      >\n    </div>\n\n    <!-- 添加或修改参数配置对话框 -->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"open\"\n      :close-on-click-modal=\"false\"\n      width=\"1000px\"\n      append-to-body\n    >\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"用户账号\" prop=\"userName\">\n              <el-input v-model=\"form.userName\" placeholder=\"请输入用户账号\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"报备人姓名\" prop=\"nickName\">\n              <el-input\n                v-model=\"form.nickName\"\n                placeholder=\"请输入报备人姓名\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"报备人电话\" prop=\"phonenumber\">\n              <el-input\n                v-model=\"form.phonenumber\"\n                placeholder=\"请输入报备人电话\"\n                maxlength=\"11\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"所在区域\" prop=\"province\" :required=\"true\">\n              <el-cascader\n                :options=\"provinceAndCityData\"\n                clearable\n                :props=\"{ expandTrigger: 'hover' }\"\n                v-model=\"cityOptions\"\n                @change=\"handleCityChange\"\n              >\n              </el-cascader>\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"密码\" prop=\"password\">\n              <el-input\n                v-model=\"form.password\"\n                placeholder=\"请输入密码\"\n                type=\"password\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"确认密码\" prop=\"password2\">\n              <el-input\n                v-model=\"form.password2\"\n                placeholder=\"请输入密码\"\n                type=\"password\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"营业执照号码\" prop=\"businessNo\">\n              <el-input\n                v-model=\"form.businessNo\"\n                placeholder=\"请输入营业执照号码\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"公司全称\" prop=\"company\">\n              <el-input v-model=\"form.company\" placeholder=\"请输入公司全称\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <!-- <el-col :span=\"12\">\n            <el-form-item label=\"营业执照图片\" prop=\"businessNoPic\">\n              <imageUpload v-model=\"form.businessNoPic\" />\n            </el-form-item>\n          </el-col> -->\n          <el-col :span=\"12\">\n            <el-form-item label=\"营业执照图片\" prop=\"imgName\">\n              <el-upload\n                action=\"/undefind\"\n                :on-change=\"addImg\"\n                :auto-upload=\"false\"\n                :show-file-list=\"false\"\n              >\n                <el-image v-if=\"!img\" :src=\"img\" :style=\"`width:150px;`\">\n                  <div slot=\"error\" class=\"image-slot\">\n                    <i class=\"el-icon-plus\" :style=\"`height:60px;`\" />\n                  </div>\n                </el-image>\n                <div v-else class=\"image\">\n                  <el-image :src=\"img\" :style=\"`width:150px;`\" fit=\"fill\" />\n                  <div class=\"mask\">\n                    <div class=\"actions\">\n                      <span title=\"移除\" @click.stop=\"removeImage\">\n                        <i class=\"el-icon-delete\" />\n                      </span>\n                    </div>\n                  </div>\n                </div>\n              </el-upload>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"隶属经销商\" prop=\"dealer\">\n              <el-input v-model=\"form.dealer\" placeholder=\"请输入隶属经销商\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"资料邮寄地址\" prop=\"address\">\n              <el-input\n                v-model=\"form.address\"\n                placeholder=\"请输入资料邮寄地址\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"资料接收邮箱\" prop=\"email\">\n              <el-input\n                v-model=\"form.email\"\n                placeholder=\"请输入资料接收邮箱\"\n                maxlength=\"50\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n    <el-dialog\n      title=\"重置密码\"\n      :visible.sync=\"restOpen\"\n      :close-on-click-modal=\"false\"\n      width=\"400px\"\n      append-to-body\n    >\n      <el-form\n        ref=\"resetPwdForm\"\n        :model=\"resetPwdForm\"\n        :rules=\"resetRules\"\n        label-width=\"90px\"\n      >\n        <el-form-item prop=\"username\" label=\"手机号码\">\n          <el-input\n            v-model=\"resetPwdForm.username\"\n            type=\"text\"\n            auto-complete=\"off\"\n            placeholder=\"手机号码\"\n          >\n            <svg-icon\n              slot=\"prefix\"\n              icon-class=\"user\"\n              class=\"el-input__icon input-icon\"\n            />\n          </el-input>\n        </el-form-item>\n        <el-form-item prop=\"password\" label=\"密码\">\n          <el-input\n            v-model=\"resetPwdForm.password\"\n            type=\"password\"\n            auto-complete=\"off\"\n            placeholder=\"密码\"\n          >\n            <svg-icon\n              slot=\"prefix\"\n              icon-class=\"password\"\n              class=\"el-input__icon input-icon\"\n            />\n          </el-input>\n        </el-form-item>\n        <el-form-item prop=\"password2\" label=\"确认密码\" required>\n          <el-input\n            v-model=\"resetPwdForm.password2\"\n            type=\"password\"\n            auto-complete=\"off\"\n            placeholder=\"确认密码\"\n          >\n            <svg-icon\n              slot=\"prefix\"\n              icon-class=\"password\"\n              class=\"el-input__icon input-icon\"\n            />\n          </el-input>\n        </el-form-item>\n        <el-form-item prop=\"code\" label=\"验证码\">\n          <el-input\n            v-model=\"resetPwdForm.code\"\n            auto-complete=\"off\"\n            placeholder=\"验证码\"\n            style=\"width: 50%\"\n          >\n          </el-input>\n          <div class=\"login-code\" style=\"width: 45%\">\n            <el-link\n              @click=\"getPhoneCode\"\n              type=\"primary\"\n              :disabled=\"resetCodeTxt != '获取验证码'\"\n              >{{ resetCodeTxt }}</el-link\n            >\n          </div>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"handResetPwd\">确 定</el-button>\n        <el-button @click=\"restOpen = false\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { register } from \"@/api/system/user\";\nimport { getCodeImg, resetCaptcha, resetPwdByPhone } from \"@/api/login\";\nimport Cookies from \"js-cookie\";\nimport { encrypt, decrypt } from \"@/utils/jsencrypt\";\nimport ImageUpload from \"@/components/ImageUpload\";\nimport { regionData, CodeToText } from \"element-china-area-data\";\nexport default {\n  name: \"Login\",\n  components: { ImageUpload },\n  data() {\n    // 自定义校验规则\n    var bargainPic = (rule, value, callback) => {\n      if (!this.imgName) {\n        callback(new Error(\"营业执照照片必传\"));\n      } else {\n        callback();\n      }\n    };\n    return {\n      codeUrl: \"\",\n      cookiePassword: \"\",\n      loginForm: {\n        username: \"\",\n        password: \"\",\n        rememberMe: false,\n        code: \"\",\n        uuid: \"\",\n      },\n      loginRules: {\n        username: [\n          { required: true, trigger: \"blur\", message: \"用户名不能为空\" },\n        ],\n        password: [\n          { required: true, trigger: \"blur\", message: \"密码不能为空\" },\n        ],\n        code: [\n          { required: true, trigger: \"change\", message: \"验证码不能为空\" },\n        ],\n      },\n      loading: false,\n      redirect: undefined,\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      open: false,\n      // 表单校验\n      rules: {\n        userName: [\n          { required: true, message: \"用户名不能为空\", trigger: \"blur\" },\n        ],\n        company: [\n          { required: true, message: \"公司全称不能为空\", trigger: \"blur\" },\n        ],\n        businessNo: [\n          { required: true, message: \"营业执照号码不能为空\", trigger: \"blur\" },\n        ],\n        province: [\n          { required: true, message: \"所在区域必选\", trigger: \"blur\" },\n        ],\n        imgName: [\n          {\n            validator: bargainPic,\n            required: true,\n            message: \"营业执照照片必传\",\n            trigger: \"change\",\n          },\n        ],\n        password: [\n          { required: true, trigger: \"blur\", message: \"密码不能为空\" },\n        ],\n        password2: [\n          { required: true, trigger: \"blur\", message: \"确认密码不能为空\" },\n        ],\n        email: [\n          {\n            required: true,\n            type: \"email\",\n            message: \"'请输入正确的邮箱地址\",\n            trigger: [\"blur\", \"change\"],\n          },\n        ],\n        phonenumber: [\n          {\n            required: true,\n            pattern: /^1[3|4|5|6|7|8|9][0-9]\\d{8}$/,\n            message: \"请输入正确的手机号码\",\n            trigger: \"blur\",\n          },\n        ],\n        nickName: [\n          { required: true, message: \"报备人姓名不能为空\", trigger: \"blur\" },\n        ],\n        address: [\n          { required: true, message: \"资料邮寄地址不能为空\", trigger: \"blur\" },\n        ],\n        code: [\n          { required: true, trigger: \"change\", message: \"验证码不能为空\" },\n        ],\n      },\n      // 性别状态字典\n      sexOptions: [],\n      // 短信通知字典\n      smsSendOptions: [],\n      // 审核状态字典\n      auditStatusOptions: [],\n      // 角色选项\n      roleOptions: [],\n      // 表单参数\n      form: {},\n      provinceAndCityData: regionData,\n      cityOptions: [],\n      img: undefined,\n      imgName: undefined,\n      imgFile: undefined,\n      restOpen: false,\n      resetCodeTxt: \"获取验证码\",\n      resetPwdForm: {\n        username: \"\",\n        password: \"\",\n        code: \"\",\n      },\n      resetRules: {\n        username: [\n          {\n            required: true,\n            pattern: /^1[3|4|5|6|7|8|9][0-9]\\d{8}$/,\n            message: \"请输入正确的手机号码\",\n            trigger: \"blur\",\n          },\n        ],\n        password: [\n          { required: true, trigger: \"blur\", message: \"密码不能为空\" },\n        ],\n        code: [\n          { required: true, trigger: \"change\", message: \"验证码不能为空\" },\n        ],\n      },\n    };\n  },\n  watch: {\n    $route: {\n      handler: function (route) {\n        this.redirect = route.query && route.query.redirect;\n      },\n      immediate: true,\n    },\n  },\n  created() {\n    this.getCode();\n    this.getCookie();\n  },\n  methods: {\n    getCode() {\n      getCodeImg().then((res) => {\n        this.codeUrl = \"data:image/gif;base64,\" + res.img;\n        this.loginForm.uuid = res.uuid;\n      });\n    },\n    getCookie() {\n      const username = Cookies.get(\"username\");\n      const password = Cookies.get(\"password\");\n      const rememberMe = Cookies.get(\"rememberMe\");\n      this.loginForm = {\n        username: username === undefined ? this.loginForm.username : username,\n        password:\n          password === undefined ? this.loginForm.password : decrypt(password),\n        rememberMe: rememberMe === undefined ? false : Boolean(rememberMe),\n      };\n    },\n    handleLogin() {\n      this.$refs.loginForm.validate((valid) => {\n        if (valid) {\n          this.loading = true;\n          if (this.loginForm.rememberMe) {\n            Cookies.set(\"username\", this.loginForm.username, { expires: 30 });\n            Cookies.set(\"password\", encrypt(this.loginForm.password), {\n              expires: 30,\n            });\n            Cookies.set(\"rememberMe\", this.loginForm.rememberMe, {\n              expires: 30,\n            });\n          } else {\n            Cookies.remove(\"username\");\n            Cookies.remove(\"password\");\n            Cookies.remove(\"rememberMe\");\n          }\n          this.$store\n            .dispatch(\"Login\", this.loginForm)\n            .then(() => {\n              this.$router.push({ path: this.redirect || \"/\" }).catch(() => {});\n            })\n            .catch(() => {\n              this.loading = false;\n              this.getCode();\n            });\n        }\n      });\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        userId: undefined,\n        userName: undefined,\n        nickName: undefined,\n        userType: undefined,\n        email: undefined,\n        phonenumber: undefined,\n        sex: \"2\",\n        avatar: undefined,\n        password: undefined,\n        company: undefined,\n        businessNo: undefined,\n        businessNoPic: undefined,\n        province: undefined,\n        address: undefined,\n        dealer: undefined,\n      };\n      this.img = undefined;\n      this.imgName = undefined;\n      this.imgFile = undefined;\n      this.resetForm(\"form\");\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.reset();\n      //this.getTreeselect();\n      this.open = true;\n      this.title = \"用户注册\";\n    },\n    handleRest() {\n      //this.getTreeselect();\n      this.restOpen = true;\n    },\n    /** 提交按钮 */\n    submitForm: function () {\n      if (this.form.password != this.form.password2) {\n        this.$message.error(\"两次输入密码不一致\");\n        return;\n      }\n      // this.form.file = this.imgFile;\n      // console.log(this.form)\n\n      this.$refs[\"form\"].validate((valid) => {\n        if (valid) {\n          let param = new FormData();\n          param.append(\"file\", this.imgFile);\n          param.append(\"userName\", this.form.userName);\n          param.append(\"nickName\", this.form.nickName);\n          param.append(\"email\", this.form.email);\n          param.append(\"phonenumber\", this.form.phonenumber);\n          param.append(\"password\", this.form.password);\n          param.append(\"company\", this.form.company);\n          param.append(\"businessNo\", this.form.businessNo);\n          param.append(\"province\", this.form.province);\n          param.append(\"address\", this.form.address);\n          param.append(\"dealer\", this.form.dealer);\n          ``;\n          register(param).then((response) => {\n            this.msgSuccess(\"提交成功\");\n            this.open = false;\n            this.reset();\n          });\n        }\n      });\n    },\n    handleCityChange(value) {\n      this.cityOptions = value;\n      var txt = \"\";\n      value.forEach(function (item) {\n        txt += CodeToText[item] + \"/\";\n      });\n      if (txt.length > 1) {\n        txt = txt.substring(0, txt.length - 1);\n        this.form.province = txt;\n      } else {\n        this.form.province = undefined;\n      }\n    },\n    addImg(file1) {\n      const file = file1.raw;\n      const isJPG = file.type === \"image/jpeg\";\n      const isPNG = file.type === \"image/png\";\n      const isWEBP = file.type === \"image/webp\";\n      const isLt1M = file.size / 1024 / 1024 < 1;\n\n      if (!isJPG && !isPNG && !isWEBP) {\n        this.$message.error(\"上传图片只能是 JPG、PNG、WEBP 格式!\");\n      } else if (!isLt1M) {\n        this.$message.error(\"上传图片大小不能超过 1MB!\");\n      } else {\n        this.imgFile = file;\n        this.imgName = file.name;\n        var self = this;\n        //定义一个文件阅读器\n        var reader = new FileReader();\n        //文件装载后将其显示在图片预览里\n        reader.onload = function (e) {\n          //将bade64位图片保存至数组里供上面图片显示\n          self.img = e.target.result;\n        };\n        reader.readAsDataURL(file);\n      }\n      this.$refs.form.validateField(\"imgName\");\n    },\n    removeImage() {\n      this.imgFile = undefined;\n      this.img = undefined;\n      this.imgName = undefined;\n    },\n    getPhoneCode() {\n      let that = this;\n      if (this.resetPwdForm.username == \"\") {\n        this.$message.error(\"请输入手机号\");\n        return;\n      }\n\n      resetCaptcha(this.resetPwdForm).then((resp) => {\n        that.resetCodeTxt = \"60s后重新获取\";\n        setTimeout(() => {\n          that.resetCodeTxt = \"获取验证码\";\n        }, 60 * 1000);\n      });\n    },\n    handResetPwd() {\n      let that = this;\n      this.$refs[\"resetPwdForm\"].validate((valid) => {\n        if (valid) {\n          if (\n            this.resetPwdForm.password == \"\" ||\n            this.resetPwdForm.password != this.resetPwdForm.password2\n          ) {\n            this.$message.error(\"两次输入密码不一致\");\n            return;\n          }\n          if (this.resetPwdForm.password.length < 6) {\n            this.$message.error(\"密码长度不能小于\");\n            return;\n          }\n          resetPwdByPhone(this.resetPwdForm).then((resp) => {\n            this.msgSuccess(\"重置成功\");\n            that.restOpen = false;\n          });\n        }\n      });\n    },\n  },\n};\n</script>\n\n<style rel=\"stylesheet/scss\" lang=\"scss\">\n.login {\n  display: flex;\n  justify-content: flex-end;\n  align-items: center;\n  height: 100%;\n  background-image: url(\"../assets/images/login-background.webp\");\n  background-size: cover;\n}\n@media screen and (min-width: 600px) {\n  .login-padding {\n    padding-right: 10%;\n  }\n}\n@media screen and (max-width: 599px) {\n  .login-padding {\n    padding-left: 2px;\n    padding-right: 7px;\n  }\n}\n.title {\n  margin: 0px auto 30px auto;\n  text-align: center;\n  color: #707070;\n}\n\n.login-form {\n  border-radius: 6px;\n  background: #ffffff;\n  width: 400px;\n  padding: 25px 25px 5px 25px;\n  .el-input {\n    height: 38px;\n    input {\n      height: 38px;\n    }\n  }\n  .input-icon {\n    height: 39px;\n    width: 14px;\n    margin-left: 2px;\n  }\n}\n.login-tip {\n  font-size: 13px;\n  text-align: center;\n  color: #bfbfbf;\n}\n.login-code {\n  width: 33%;\n  height: 38px;\n  float: right;\n  img {\n    cursor: pointer;\n    vertical-align: middle;\n  }\n}\n.el-login-footer {\n  height: 40px;\n  line-height: 40px;\n  position: fixed;\n  bottom: 0;\n  width: 100%;\n  text-align: right;\n  color: #999999;\n  font-family: Arial;\n  font-size: 12px;\n  letter-spacing: 1px;\n}\n.login-code-img {\n  height: 38px;\n}\n.logo-wrapper {\n  position: fixed;\n  top: 30px;\n  left: 70px;\n}\n.logo img {\n  max-width: 153px;\n}\n</style>\n"]}]}