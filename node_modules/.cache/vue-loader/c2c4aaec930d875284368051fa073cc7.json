{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/monitor/server/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/monitor/server/index.vue", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldFNlcnZlciB9IGZyb20gIkAvYXBpL21vbml0b3Ivc2VydmVyIjsKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiU2VydmVyIiwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgLy8g5Yqg6L295bGC5L+h5oGvCiAgICAgIGxvYWRpbmc6IFtdLAogICAgICAvLyDmnI3liqHlmajkv6Hmga8KICAgICAgc2VydmVyOiBbXQogICAgfTsKICB9LAogIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmdldExpc3QoKTsKICAgIHRoaXMub3BlbkxvYWRpbmcoKTsKICB9LAogIG1ldGhvZHM6IHsKICAgIC8qKiDmn6Xor6LmnI3liqHlmajkv6Hmga8gKi8KICAgIGdldExpc3QoKSB7CiAgICAgIGdldFNlcnZlcigpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMuc2VydmVyID0gcmVzcG9uc2UuZGF0YTsKICAgICAgICB0aGlzLmxvYWRpbmcuY2xvc2UoKTsKICAgICAgfSk7CiAgICB9LAogICAgLy8g5omT5byA5Yqg6L295bGCCiAgICBvcGVuTG9hZGluZygpIHsKICAgICAgdGhpcy5sb2FkaW5nID0gdGhpcy4kbG9hZGluZyh7CiAgICAgICAgbG9jazogdHJ1ZSwKICAgICAgICB0ZXh0OiAi5ou85ZG96K+75Y+W5LitIiwKICAgICAgICBzcGlubmVyOiAiZWwtaWNvbi1sb2FkaW5nIiwKICAgICAgICBiYWNrZ3JvdW5kOiAicmdiYSgwLCAwLCAwLCAwLjcpIgogICAgICB9KTsKICAgIH0KICB9Cn07Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8KA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/monitor/server", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-row>\n      <el-col :span=\"12\" class=\"card-box\">\n        <el-card>\n          <div slot=\"header\"><span>CPU</span></div>\n          <div class=\"el-table el-table--enable-row-hover el-table--medium\">\n            <table cellspacing=\"0\" style=\"width: 100%;\">\n              <thead>\n                <tr>\n                  <th class=\"is-leaf\"><div class=\"cell\">属性</div></th>\n                  <th class=\"is-leaf\"><div class=\"cell\">值</div></th>\n                </tr>\n              </thead>\n              <tbody>\n                <tr>\n                  <td><div class=\"cell\">核心数</div></td>\n                  <td><div class=\"cell\" v-if=\"server.cpu\">{{ server.cpu.cpuNum }}</div></td>\n                </tr>\n                <tr>\n                  <td><div class=\"cell\">用户使用率</div></td>\n                  <td><div class=\"cell\" v-if=\"server.cpu\">{{ server.cpu.used }}%</div></td>\n                </tr>\n                <tr>\n                  <td><div class=\"cell\">系统使用率</div></td>\n                  <td><div class=\"cell\" v-if=\"server.cpu\">{{ server.cpu.sys }}%</div></td>\n                </tr>\n                <tr>\n                  <td><div class=\"cell\">当前空闲率</div></td>\n                  <td><div class=\"cell\" v-if=\"server.cpu\">{{ server.cpu.free }}%</div></td>\n                </tr>\n              </tbody>\n            </table>\n          </div>\n        </el-card>\n      </el-col>\n\n      <el-col :span=\"12\" class=\"card-box\">\n        <el-card>\n          <div slot=\"header\"><span>内存</span></div>\n          <div class=\"el-table el-table--enable-row-hover el-table--medium\">\n            <table cellspacing=\"0\" style=\"width: 100%;\">\n              <thead>\n                <tr>\n                  <th class=\"is-leaf\"><div class=\"cell\">属性</div></th>\n                  <th class=\"is-leaf\"><div class=\"cell\">内存</div></th>\n                  <th class=\"is-leaf\"><div class=\"cell\">JVM</div></th>\n                </tr>\n              </thead>\n              <tbody>\n                <tr>\n                  <td><div class=\"cell\">总内存</div></td>\n                  <td><div class=\"cell\" v-if=\"server.mem\">{{ server.mem.total }}G</div></td>\n                  <td><div class=\"cell\" v-if=\"server.jvm\">{{ server.jvm.total }}M</div></td>\n                </tr>\n                <tr>\n                  <td><div class=\"cell\">已用内存</div></td>\n                  <td><div class=\"cell\" v-if=\"server.mem\">{{ server.mem.used}}G</div></td>\n                  <td><div class=\"cell\" v-if=\"server.jvm\">{{ server.jvm.used}}M</div></td>\n                </tr>\n                <tr>\n                  <td><div class=\"cell\">剩余内存</div></td>\n                  <td><div class=\"cell\" v-if=\"server.mem\">{{ server.mem.free }}G</div></td>\n                  <td><div class=\"cell\" v-if=\"server.jvm\">{{ server.jvm.free }}M</div></td>\n                </tr>\n                <tr>\n                  <td><div class=\"cell\">使用率</div></td>\n                  <td><div class=\"cell\" v-if=\"server.mem\" :class=\"{'text-danger': server.mem.usage > 80}\">{{ server.mem.usage }}%</div></td>\n                  <td><div class=\"cell\" v-if=\"server.jvm\" :class=\"{'text-danger': server.jvm.usage > 80}\">{{ server.jvm.usage }}%</div></td>\n                </tr>\n              </tbody>\n            </table>\n          </div>\n        </el-card>\n      </el-col>\n\n      <el-col :span=\"24\" class=\"card-box\">\n        <el-card>\n          <div slot=\"header\">\n            <span>服务器信息</span>\n          </div>\n          <div class=\"el-table el-table--enable-row-hover el-table--medium\">\n            <table cellspacing=\"0\" style=\"width: 100%;\">\n              <tbody>\n                <tr>\n                  <td><div class=\"cell\">服务器名称</div></td>\n                  <td><div class=\"cell\" v-if=\"server.sys\">{{ server.sys.computerName }}</div></td>\n                  <td><div class=\"cell\">操作系统</div></td>\n                  <td><div class=\"cell\" v-if=\"server.sys\">{{ server.sys.osName }}</div></td>\n                </tr>\n                <tr>\n                  <td><div class=\"cell\">服务器IP</div></td>\n                  <td><div class=\"cell\" v-if=\"server.sys\">{{ server.sys.computerIp }}</div></td>\n                  <td><div class=\"cell\">系统架构</div></td>\n                  <td><div class=\"cell\" v-if=\"server.sys\">{{ server.sys.osArch }}</div></td>\n                </tr>\n              </tbody>\n            </table>\n          </div>\n        </el-card>\n      </el-col>\n\n      <el-col :span=\"24\" class=\"card-box\">\n        <el-card>\n          <div slot=\"header\">\n            <span>Java虚拟机信息</span>\n          </div>\n          <div class=\"el-table el-table--enable-row-hover el-table--medium\">\n            <table cellspacing=\"0\" style=\"width: 100%;\">\n              <tbody>\n                <tr>\n                  <td><div class=\"cell\">Java名称</div></td>\n                  <td><div class=\"cell\" v-if=\"server.jvm\">{{ server.jvm.name }}</div></td>\n                  <td><div class=\"cell\">Java版本</div></td>\n                  <td><div class=\"cell\" v-if=\"server.jvm\">{{ server.jvm.version }}</div></td>\n                </tr>\n                <tr>\n                  <td><div class=\"cell\">启动时间</div></td>\n                  <td><div class=\"cell\" v-if=\"server.jvm\">{{ server.jvm.startTime }}</div></td>\n                  <td><div class=\"cell\">运行时长</div></td>\n                  <td><div class=\"cell\" v-if=\"server.jvm\">{{ server.jvm.runTime }}</div></td>\n                </tr>\n                <tr>\n                  <td colspan=\"1\"><div class=\"cell\">安装路径</div></td>\n                  <td colspan=\"3\"><div class=\"cell\" v-if=\"server.jvm\">{{ server.jvm.home }}</div></td>\n                </tr>\n                <tr>\n                  <td colspan=\"1\"><div class=\"cell\">项目路径</div></td>\n                  <td colspan=\"3\"><div class=\"cell\" v-if=\"server.sys\">{{ server.sys.userDir }}</div></td>\n                </tr>\n              </tbody>\n            </table>\n          </div>\n        </el-card>\n      </el-col>\n\n      <el-col :span=\"24\" class=\"card-box\">\n        <el-card>\n          <div slot=\"header\">\n            <span>磁盘状态</span>\n          </div>\n          <div class=\"el-table el-table--enable-row-hover el-table--medium\">\n            <table cellspacing=\"0\" style=\"width: 100%;\">\n              <thead>\n                <tr>\n                  <th class=\"is-leaf\"><div class=\"cell\">盘符路径</div></th>\n                  <th class=\"is-leaf\"><div class=\"cell\">文件系统</div></th>\n                  <th class=\"is-leaf\"><div class=\"cell\">盘符类型</div></th>\n                  <th class=\"is-leaf\"><div class=\"cell\">总大小</div></th>\n                  <th class=\"is-leaf\"><div class=\"cell\">可用大小</div></th>\n                  <th class=\"is-leaf\"><div class=\"cell\">已用大小</div></th>\n                  <th class=\"is-leaf\"><div class=\"cell\">已用百分比</div></th>\n                </tr>\n              </thead>\n              <tbody v-if=\"server.sysFiles\">\n                <tr v-for=\"sysFile in server.sysFiles\">\n                  <td><div class=\"cell\">{{ sysFile.dirName }}</div></td>\n                  <td><div class=\"cell\">{{ sysFile.sysTypeName }}</div></td>\n                  <td><div class=\"cell\">{{ sysFile.typeName }}</div></td>\n                  <td><div class=\"cell\">{{ sysFile.total }}</div></td>\n                  <td><div class=\"cell\">{{ sysFile.free }}</div></td>\n                  <td><div class=\"cell\">{{ sysFile.used }}</div></td>\n                  <td><div class=\"cell\" :class=\"{'text-danger': sysFile.usage > 80}\">{{ sysFile.usage }}%</div></td>\n                </tr>\n              </tbody>\n            </table>\n          </div>\n        </el-card>\n      </el-col>\n    </el-row>\n  </div>\n</template>\n\n<script>\nimport { getServer } from \"@/api/monitor/server\";\n\nexport default {\n  name: \"Server\",\n  data() {\n    return {\n      // 加载层信息\n      loading: [],\n      // 服务器信息\n      server: []\n    };\n  },\n  created() {\n    this.getList();\n    this.openLoading();\n  },\n  methods: {\n    /** 查询服务器信息 */\n    getList() {\n      getServer().then(response => {\n        this.server = response.data;\n        this.loading.close();\n      });\n    },\n    // 打开加载层\n    openLoading() {\n      this.loading = this.$loading({\n        lock: true,\n        text: \"拼命读取中\",\n        spinner: \"el-icon-loading\",\n        background: \"rgba(0, 0, 0, 0.7)\"\n      });\n    }\n  }\n};\n</script>"]}]}