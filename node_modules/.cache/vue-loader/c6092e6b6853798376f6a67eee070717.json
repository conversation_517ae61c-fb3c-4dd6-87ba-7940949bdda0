{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/layout/components/Sidebar/index.vue?vue&type=template&id=d990f1f6", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/layout/components/Sidebar/index.vue", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/loaders/templateLoader.js", "mtime": 1751171659981}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:dmFyIHJlbmRlciA9IGZ1bmN0aW9uICgpIHt2YXIgX3ZtPXRoaXM7dmFyIF9oPV92bS4kY3JlYXRlRWxlbWVudDt2YXIgX2M9X3ZtLl9zZWxmLl9jfHxfaDtyZXR1cm4gX2MoJ2Rpdicse2NsYXNzOnsnaGFzLWxvZ28nOl92bS5zaG93TG9nb30sc3R5bGU6KHsgYmFja2dyb3VuZENvbG9yOiBfdm0uc2V0dGluZ3Muc2lkZVRoZW1lID09PSAndGhlbWUtZGFyaycgPyBfdm0udmFyaWFibGVzLm1lbnVCZyA6IF92bS52YXJpYWJsZXMubWVudUxpZ2h0QmcgfSl9LFsoX3ZtLnNob3dMb2dvKT9fYygnbG9nbycse2F0dHJzOnsiY29sbGFwc2UiOl92bS5pc0NvbGxhcHNlfX0pOl92bS5fZSgpLF9jKCdlbC1zY3JvbGxiYXInLHtjbGFzczpfdm0uc2V0dGluZ3Muc2lkZVRoZW1lLGF0dHJzOnsid3JhcC1jbGFzcyI6InNjcm9sbGJhci13cmFwcGVyIn19LFtfYygnZWwtbWVudScse2F0dHJzOnsiZGVmYXVsdC1hY3RpdmUiOl92bS5hY3RpdmVNZW51LCJjb2xsYXBzZSI6X3ZtLmlzQ29sbGFwc2UsImJhY2tncm91bmQtY29sb3IiOl92bS5zZXR0aW5ncy5zaWRlVGhlbWUgPT09ICd0aGVtZS1kYXJrJyA/IF92bS52YXJpYWJsZXMubWVudUJnIDogX3ZtLnZhcmlhYmxlcy5tZW51TGlnaHRCZywidGV4dC1jb2xvciI6X3ZtLnNldHRpbmdzLnNpZGVUaGVtZSA9PT0gJ3RoZW1lLWRhcmsnID8gX3ZtLnZhcmlhYmxlcy5tZW51VGV4dCA6ICdyZ2JhKDAsMCwwLC42NSknLCJ1bmlxdWUtb3BlbmVkIjp0cnVlLCJhY3RpdmUtdGV4dC1jb2xvciI6X3ZtLnNldHRpbmdzLnRoZW1lLCJjb2xsYXBzZS10cmFuc2l0aW9uIjpmYWxzZSwibW9kZSI6InZlcnRpY2FsIn19LF92bS5fbCgoX3ZtLnNpZGViYXJSb3V0ZXJzKSxmdW5jdGlvbihyb3V0ZSxpbmRleCl7cmV0dXJuIF9jKCdzaWRlYmFyLWl0ZW0nLHtrZXk6cm91dGUucGF0aCAgKyBpbmRleCxhdHRyczp7Iml0ZW0iOnJvdXRlLCJiYXNlLXBhdGgiOnJvdXRlLnBhdGh9fSl9KSwxKV0sMSldLDEpfQp2YXIgc3RhdGljUmVuZGVyRm5zID0gW10KCmV4cG9ydCB7IHJlbmRlciwgc3RhdGljUmVuZGVyRm5zIH0="}]}