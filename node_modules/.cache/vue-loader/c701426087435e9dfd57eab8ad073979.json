{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/project/data/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/project/data/index.vue", "mtime": 1718616598765}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7CiAgbGlzdFByb2plY3REYXRhLAogIGdldFByb2plY3REYXRhLAogIGRlbFByb2plY3REYXRhLAogIGFkZFByb2plY3REYXRhLAogIHVwZGF0ZVByb2plY3REYXRhLAogIGV4cG9ydFByb2plY3REYXRhLAp9IGZyb20gIkAvYXBpL3Byb2plY3QvZGF0YSI7CmltcG9ydCBFZGl0b3IgZnJvbSAiQC9jb21wb25lbnRzL0VkaXRvciI7CmltcG9ydCBVcGxvYWQgZnJvbSAiQC9jb21wb25lbnRzL0ZpbGVVcGxvYWQiOwoKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICJQcm9qZWN0RGF0YSIsCiAgY29tcG9uZW50czogewogICAgRWRpdG9yLAogICAgVXBsb2FkLAogIH0sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIC8vIOmBrue9qeWxggogICAgICBsb2FkaW5nOiB0cnVlLAogICAgICAvLyDpgInkuK3mlbDnu4QKICAgICAgaWRzOiBbXSwKICAgICAgLy8g6Z2e5Y2V5Liq56aB55SoCiAgICAgIHNpbmdsZTogdHJ1ZSwKICAgICAgLy8g6Z2e5aSa5Liq56aB55SoCiAgICAgIG11bHRpcGxlOiB0cnVlLAogICAgICAvLyDmmL7npLrmkJzntKLmnaHku7YKICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwKICAgICAgLy8g5oC75p2h5pWwCiAgICAgIHRvdGFsOiAwLAogICAgICAvLyDlhazlkYrooajmoLzmlbDmja4KICAgICAgcHJvamVjdERhdGFMaXN0OiBbXSwKICAgICAgdHlwZUFycjogWyJwZGYiXSwKICAgICAgLy8g5by55Ye65bGC5qCH6aKYCiAgICAgIHRpdGxlOiAiIiwKICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGCCiAgICAgIG9wZW46IGZhbHNlLAogICAgICAvLyDnsbvlnovmlbDmja7lrZflhbgKICAgICAgc3RhdHVzT3B0aW9uczogW10sCiAgICAgIC8vIOeKtuaAgeaVsOaNruWtl+WFuAogICAgICB0eXBlT3B0aW9uczogW10sCiAgICAgIC8vIOafpeivouWPguaVsAogICAgICBxdWVyeVBhcmFtczogewogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgIG5vdGljZVRpdGxlOiB1bmRlZmluZWQsCiAgICAgICAgY3JlYXRlQnk6IHVuZGVmaW5lZCwKICAgICAgICBzdGF0dXM6IHVuZGVmaW5lZCwKICAgICAgfSwKICAgICAgLy8g5omA6ZyA6LWE5paZ5a2X5YW4CiAgICAgIHJlcXVpcmVJbmZvT3B0aW9uczogW10sCiAgICAgIHJlcXVpcmVJbmZvT3B0aW9uMTogW10sCiAgICAgIC8vIOihqOWNleWPguaVsAogICAgICBmb3JtOiB7fSwKICAgICAgLy8g6KGo5Y2V5qCh6aqMCiAgICAgIHJ1bGVzOiB7CiAgICAgICAgZGljdFZhbHVlOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6LWE5paZ5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0sCiAgICAgICAgXSwKICAgICAgICB1cmw6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLkuIrkvKDnmoTmlofku7bkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiY2hhbmdlIiB9LAogICAgICAgIF0sCiAgICAgIH0sCiAgICB9OwogIH0sCiAgY3JlYXRlZCgpIHsKICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgdGhpcy5nZXREaWN0cygic3lzX25vdGljZV9zdGF0dXMiKS50aGVuKChyZXNwb25zZSkgPT4gewogICAgICB0aGlzLnN0YXR1c09wdGlvbnMgPSByZXNwb25zZS5kYXRhOwogICAgfSk7CiAgICB0aGlzLmdldERpY3RzKCJwcl9pbmZvIikudGhlbigocmVzcG9uc2UpID0+IHsKICAgICAgdGhpcy5yZXF1aXJlSW5mb09wdGlvbjEgPSByZXNwb25zZS5kYXRhOwogICAgICB2YXIgb3B0ID0gW107CiAgICAgIHJlc3BvbnNlLmRhdGEuZm9yRWFjaCgoZWxlbSwgaW5kZXgpID0+IHsKICAgICAgICB2YXIgb2JqID0ge307CiAgICAgICAgb2JqLmxhYmVsID0gZWxlbS5kaWN0TGFiZWw7CiAgICAgICAgb2JqLnZhbHVlID0gZWxlbS5kaWN0VmFsdWU7CiAgICAgICAgb3B0LnB1c2gob2JqKTsKICAgICAgfSk7CiAgICAgIHRoaXMucmVxdWlyZUluZm9PcHRpb25zID0gb3B0OwogICAgfSk7CiAgfSwKICBtZXRob2RzOiB7CiAgICAvKiog5p+l6K+i6LWE5paZ5YiX6KGoICovCiAgICBnZXRMaXN0KCkgewogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOwogICAgICBsaXN0UHJvamVjdERhdGEodGhpcy5xdWVyeVBhcmFtcykudGhlbigocmVzcG9uc2UpID0+IHsKICAgICAgICB0aGlzLnByb2plY3REYXRhTGlzdCA9IHJlc3BvbnNlLnJvd3M7CiAgICAgICAgdGhpcy50b3RhbCA9IHJlc3BvbnNlLnRvdGFsOwogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOwogICAgICB9KTsKICAgIH0sCiAgICB1cmwodmFsdWUpIHsKICAgICAgdGhpcy5mb3JtLnVybCA9IHZhbHVlOwogICAgfSwKICAgIC8vIOWFrOWRiueKtuaAgeWtl+WFuOe/u+ivkQogICAgc3RhdHVzRm9ybWF0KHJvdywgY29sdW1uKSB7CiAgICAgIHJldHVybiB0aGlzLnNlbGVjdERpY3RMYWJlbCh0aGlzLnN0YXR1c09wdGlvbnMsIHJvdy5zdGF0dXMpOwogICAgfSwKICAgIC8vIOWFrOWRiueKtuaAgeWtl+WFuOe/u+ivkQogICAgZGljdFZhbHVlRm9ybWF0KHJvdywgY29sdW1uKSB7CiAgICAgIHJldHVybiB0aGlzLnNlbGVjdERpY3RMYWJlbCh0aGlzLnJlcXVpcmVJbmZvT3B0aW9uMSwgcm93LmRpY3RWYWx1ZSk7CiAgICB9LAogICAgLy8g5Y+W5raI5oyJ6ZKuCiAgICBjYW5jZWwoKSB7CiAgICAgIHRoaXMub3BlbiA9IGZhbHNlOwogICAgICB0aGlzLnJlc2V0KCk7CiAgICB9LAogICAgLy8g6KGo5Y2V6YeN572uCiAgICByZXNldCgpIHsKICAgICAgdGhpcy5mb3JtID0gewogICAgICAgIGRpY3RWYWx1ZTogdW5kZWZpbmVkLAogICAgICAgIHVybDogdW5kZWZpbmVkLAogICAgICAgIHN0YXR1czogIjAiLAogICAgICB9OwogICAgICB0aGlzLnJlc2V0Rm9ybSgiZm9ybSIpOwogICAgfSwKICAgIC8qKiDmkJzntKLmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZVF1ZXJ5KCkgewogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOwogICAgICB0aGlzLmdldExpc3QoKTsKICAgIH0sCiAgICAvKiog6YeN572u5oyJ6ZKu5pON5L2cICovCiAgICByZXNldFF1ZXJ5KCkgewogICAgICB0aGlzLnJlc2V0Rm9ybSgicXVlcnlGb3JtIik7CiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsKICAgIH0sCiAgICAvLyDlpJrpgInmoYbpgInkuK3mlbDmja4KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsKICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKChpdGVtKSA9PiBpdGVtLmlkKTsKICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoICE9IDE7CiAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aDsKICAgIH0sCiAgICAvKiog5paw5aKe5oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVBZGQoKSB7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsKICAgICAgdGhpcy50aXRsZSA9ICLmt7vliqDotYTmlpkiOwogICAgfSwKICAgIC8qKiDkv67mlLnmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZVVwZGF0ZShyb3cpIHsKICAgICAgdGhpcy5yZXNldCgpOwoKICAgICAgdGhpcy5mb3JtID0gewogICAgICAgIGlkOiByb3cuaWQsCiAgICAgICAgdXJsOiByb3cudXJsLAogICAgICAgIGRpY3RWYWx1ZTogcm93LmRpY3RWYWx1ZSwKICAgICAgfTsKICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsKICAgICAgdGhpcy50aXRsZSA9ICLkv67mlLnotYTmlpkiOwogICAgfSwKICAgIGhhbmRsZURvd25sb2FkKHJvdykgewogICAgICBjb25zdCBhID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgiYSIpOyAvLyDliJvlu7rkuIDkuKpIVE1MIOWFg+e0oAogICAgICBhLnNldEF0dHJpYnV0ZSgidGFyZ2V0IiwgIl9ibGFuayIpOwogICAgICBhLnNldEF0dHJpYnV0ZSgiZG93bmxvYWQiLCAiIik7IC8vZG93bmxvYWTlsZ7mgKcKICAgICAgY29uc3QgaHJlZiA9CiAgICAgICAgImh0dHBzOi8vcmVwb3J0LmNsbGVkLmNvbS9wcm9kLWFwaS9jb21tb24vZG93bmxvYWQvcmVzb3VyY2U/cmVzb3VyY2U9IiArCiAgICAgICAgcm93LnVybDsKICAgICAgYS5zZXRBdHRyaWJ1dGUoImhyZWYiLCBocmVmKTsgLy8gaHJlZumTvuaOpQogICAgICBhLmNsaWNrKCk7IC8vIOiHquaJp+ihjOeCueWHu+S6i+S7tgogICAgfSwKICAgIC8qKiDmj5DkuqTmjInpkq4gKi8KICAgIHN1Ym1pdEZvcm06IGZ1bmN0aW9uICgpIHsKICAgICAgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKCh2YWxpZCkgPT4gewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgaWYgKHRoaXMuZm9ybS51cmwgPT0gdW5kZWZpbmVkIHx8IHRoaXMuZm9ybS51cmwgPT0gIiIpIHsKICAgICAgICAgICAgcmV0dXJuIHRoaXMubXNnRXJyb3IoIuivt+mAieaLqemcgOimgeS4iuS8oOeahOaWh+S7tiIpOwogICAgICAgICAgfQogICAgICAgICAgaWYgKHRoaXMuZm9ybS5pZCAhPSB1bmRlZmluZWQpIHsKICAgICAgICAgICAgdXBkYXRlUHJvamVjdERhdGEodGhpcy5mb3JtKS50aGVuKChyZXNwb25zZSkgPT4gewogICAgICAgICAgICAgIHRoaXMubXNnU3VjY2Vzcygi5L+u5pS55oiQ5YqfIik7CiAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7CiAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICAgICAgICAgIH0pOwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgYWRkUHJvamVjdERhdGEodGhpcy5mb3JtKS50aGVuKChyZXNwb25zZSkgPT4gewogICAgICAgICAgICAgIHRoaXMubXNnU3VjY2Vzcygi5paw5aKe5oiQ5YqfIik7CiAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7CiAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICAgICAgICAgIH0pOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLyoqIOWIoOmZpOaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlRGVsZXRlKHJvdykgewogICAgICBjb25zdCBpZHMgPSByb3cuaWQgfHwgdGhpcy5pZHM7CiAgICAgIHRoaXMuJGNvbmZpcm0oCiAgICAgICAgJ+aYr+WQpuehruiupOWIoOmZpOmhueebrui1hOaWmee8luWPt+S4uiInICsgaWRzICsgJyLnmoTmlbDmja7pobk/JywKICAgICAgICAi6K2m5ZGKIiwKICAgICAgICB7CiAgICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogIuehruWumiIsCiAgICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAi5Y+W5raIIiwKICAgICAgICAgIHR5cGU6ICJ3YXJuaW5nIiwKICAgICAgICB9CiAgICAgICkKICAgICAgICAudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgICByZXR1cm4gZGVsUHJvamVjdERhdGEoaWRzKTsKICAgICAgICB9KQogICAgICAgIC50aGVuKCgpID0+IHsKICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICAgICAgdGhpcy5tc2dTdWNjZXNzKCLliKDpmaTmiJDlip8iKTsKICAgICAgICB9KTsKICAgIH0sCiAgfSwKfTsK"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/project/data", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form\n      :model=\"queryParams\"\n      ref=\"queryForm\"\n      :inline=\"true\"\n      v-show=\"showSearch\"\n      label-width=\"68px\"\n    >\n      <el-form-item label=\"所需资料\" prop=\"dictValue\">\n        <el-select\n          v-model=\"queryParams.dictValue\"\n          placeholder=\"所需资料\"\n          clearable\n          size=\"small\"\n        >\n          <el-option\n            v-for=\"dict in requireInfoOptions\"\n            :key=\"dict.value\"\n            :label=\"dict.label\"\n            :value=\"dict.value\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item>\n        <el-button\n          type=\"primary\"\n          icon=\"el-icon-search\"\n          size=\"mini\"\n          @click=\"handleQuery\"\n          >搜索</el-button\n        >\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\"\n          >重置</el-button\n        >\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"primary\"\n          plain\n          icon=\"el-icon-plus\"\n          size=\"mini\"\n          @click=\"handleAdd\"\n          v-hasPermi=\"['project:data:add']\"\n          >新增</el-button\n        >\n      </el-col>\n      <!-- <el-col :span=\"1.5\">\n        <el-button\n          type=\"success\"\n          plain\n          icon=\"el-icon-edit\"\n          size=\"mini\"\n          :disabled=\"single\"\n          @click=\"handleUpdate\"\n          v-hasPermi=\"['project:data:edit']\"\n          >修改</el-button\n        >\n      </el-col> -->\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"danger\"\n          plain\n          icon=\"el-icon-delete\"\n          size=\"mini\"\n          :disabled=\"multiple\"\n          @click=\"handleDelete\"\n          v-hasPermi=\"['project:data:remove']\"\n          >删除</el-button\n        >\n      </el-col>\n      <right-toolbar\n        :showSearch.sync=\"showSearch\"\n        @queryTable=\"getList\"\n      ></right-toolbar>\n    </el-row>\n\n    <el-table\n      v-loading=\"loading\"\n      :data=\"projectDataList\"\n      @selection-change=\"handleSelectionChange\"\n    >\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column label=\"序号\" align=\"center\" prop=\"id\" width=\"100\" />\n      <el-table-column\n        label=\"资料\"\n        align=\"center\"\n        prop=\"dictValue\"\n        :formatter=\"dictValueFormat\"\n        :show-overflow-tooltip=\"true\"\n        width=\"100\"\n      />\n      <el-table-column\n        label=\"地址\"\n        align=\"center\"\n        prop=\"url\"\n        :show-overflow-tooltip=\"true\"\n      />\n      <!-- <el-table-column\n        label=\"状态\"\n        align=\"center\"\n        prop=\"status\"\n        :formatter=\"statusFormat\"\n        width=\"100\"\n      /> -->\n      <el-table-column\n        label=\"创建者\"\n        align=\"center\"\n        prop=\"createBy\"\n        width=\"100\"\n      />\n      <el-table-column\n        label=\"创建时间\"\n        align=\"center\"\n        prop=\"createTime\"\n        width=\"100\"\n      >\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.createTime, \"{y}-{m}-{d}\") }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column\n        label=\"操作\"\n        align=\"center\"\n        class-name=\"small-padding fixed-width\"\n      >\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-edit\"\n            @click=\"handleUpdate(scope.row)\"\n            v-hasPermi=\"['project:data:edit']\"\n            >修改</el-button\n          >\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-delete\"\n            @click=\"handleDelete(scope.row)\"\n            v-hasPermi=\"['project:data:remove']\"\n            >删除</el-button\n          >\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-download\"\n            @click=\"handleDownload(scope.row)\"\n            v-hasPermi=\"['project:data:download']\"\n            >下载</el-button\n          >\n          <!-- <el-link\n            target=\"_blank\"\n            v-hasPermi=\"['project:data:download']\"\n            :href=\"scope.row.url\"\n            type=\"primary\"\n            >下载</el-link\n          > -->\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <pagination\n      v-show=\"total > 0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 添加或修改公告对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"780px\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"所需资料\" prop=\"dictValue\">\n              <el-select\n                v-model=\"form.dictValue\"\n                placeholder=\"所需资料\"\n                clearable\n                size=\"small\"\n              >\n                <el-option\n                  v-for=\"dict in requireInfoOptions\"\n                  :key=\"dict.value\"\n                  :label=\"dict.label\"\n                  :value=\"dict.value\"\n                />\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"内容\">\n              <Upload\n                :fileSize=\"100\"\n                :value=\"form.url\"\n                @input=\"url\"\n                :fileType=\"typeArr\"\n              ></Upload>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport {\n  listProjectData,\n  getProjectData,\n  delProjectData,\n  addProjectData,\n  updateProjectData,\n  exportProjectData,\n} from \"@/api/project/data\";\nimport Editor from \"@/components/Editor\";\nimport Upload from \"@/components/FileUpload\";\n\nexport default {\n  name: \"ProjectData\",\n  components: {\n    Editor,\n    Upload,\n  },\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 公告表格数据\n      projectDataList: [],\n      typeArr: [\"pdf\"],\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      open: false,\n      // 类型数据字典\n      statusOptions: [],\n      // 状态数据字典\n      typeOptions: [],\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        noticeTitle: undefined,\n        createBy: undefined,\n        status: undefined,\n      },\n      // 所需资料字典\n      requireInfoOptions: [],\n      requireInfoOption1: [],\n      // 表单参数\n      form: {},\n      // 表单校验\n      rules: {\n        dictValue: [\n          { required: true, message: \"资料不能为空\", trigger: \"blur\" },\n        ],\n        url: [\n          { required: true, message: \"上传的文件不能为空\", trigger: \"change\" },\n        ],\n      },\n    };\n  },\n  created() {\n    this.getList();\n    this.getDicts(\"sys_notice_status\").then((response) => {\n      this.statusOptions = response.data;\n    });\n    this.getDicts(\"pr_info\").then((response) => {\n      this.requireInfoOption1 = response.data;\n      var opt = [];\n      response.data.forEach((elem, index) => {\n        var obj = {};\n        obj.label = elem.dictLabel;\n        obj.value = elem.dictValue;\n        opt.push(obj);\n      });\n      this.requireInfoOptions = opt;\n    });\n  },\n  methods: {\n    /** 查询资料列表 */\n    getList() {\n      this.loading = true;\n      listProjectData(this.queryParams).then((response) => {\n        this.projectDataList = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    url(value) {\n      this.form.url = value;\n    },\n    // 公告状态字典翻译\n    statusFormat(row, column) {\n      return this.selectDictLabel(this.statusOptions, row.status);\n    },\n    // 公告状态字典翻译\n    dictValueFormat(row, column) {\n      return this.selectDictLabel(this.requireInfoOption1, row.dictValue);\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        dictValue: undefined,\n        url: undefined,\n        status: \"0\",\n      };\n      this.resetForm(\"form\");\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map((item) => item.id);\n      this.single = selection.length != 1;\n      this.multiple = !selection.length;\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.reset();\n      this.open = true;\n      this.title = \"添加资料\";\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset();\n\n      this.form = {\n        id: row.id,\n        url: row.url,\n        dictValue: row.dictValue,\n      };\n      this.open = true;\n      this.title = \"修改资料\";\n    },\n    handleDownload(row) {\n      const a = document.createElement(\"a\"); // 创建一个HTML 元素\n      a.setAttribute(\"target\", \"_blank\");\n      a.setAttribute(\"download\", \"\"); //download属性\n      const href =\n        \"https://report.clled.com/prod-api/common/download/resource?resource=\" +\n        row.url;\n      a.setAttribute(\"href\", href); // href链接\n      a.click(); // 自执行点击事件\n    },\n    /** 提交按钮 */\n    submitForm: function () {\n      this.$refs[\"form\"].validate((valid) => {\n        if (valid) {\n          if (this.form.url == undefined || this.form.url == \"\") {\n            return this.msgError(\"请选择需要上传的文件\");\n          }\n          if (this.form.id != undefined) {\n            updateProjectData(this.form).then((response) => {\n              this.msgSuccess(\"修改成功\");\n              this.open = false;\n              this.getList();\n            });\n          } else {\n            addProjectData(this.form).then((response) => {\n              this.msgSuccess(\"新增成功\");\n              this.open = false;\n              this.getList();\n            });\n          }\n        }\n      });\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const ids = row.id || this.ids;\n      this.$confirm(\n        '是否确认删除项目资料编号为\"' + ids + '\"的数据项?',\n        \"警告\",\n        {\n          confirmButtonText: \"确定\",\n          cancelButtonText: \"取消\",\n          type: \"warning\",\n        }\n      )\n        .then(function () {\n          return delProjectData(ids);\n        })\n        .then(() => {\n          this.getList();\n          this.msgSuccess(\"删除成功\");\n        });\n    },\n  },\n};\n</script>\n"]}]}