{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/layout/components/TagsView/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/layout/components/TagsView/index.vue", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/layout/components/TagsView", "sourcesContent": ["<template>\n  <div id=\"tags-view-container\" class=\"tags-view-container\">\n    <scroll-pane ref=\"scrollPane\" class=\"tags-view-wrapper\" @scroll=\"handleScroll\">\n      <router-link\n        v-for=\"tag in visitedViews\"\n        ref=\"tag\"\n        :key=\"tag.path\"\n        :class=\"isActive(tag)?'active':''\"\n        :to=\"{ path: tag.path, query: tag.query, fullPath: tag.fullPath }\"\n        tag=\"span\"\n        class=\"tags-view-item\"\n        :style=\"activeStyle(tag)\"\n        @click.middle.native=\"!isAffix(tag)?closeSelectedTag(tag):''\"\n        @contextmenu.prevent.native=\"openMenu(tag,$event)\"\n      >\n        {{ tag.title }}\n        <span v-if=\"!isAffix(tag)\" class=\"el-icon-close\" @click.prevent.stop=\"closeSelectedTag(tag)\" />\n      </router-link>\n    </scroll-pane>\n    <ul v-show=\"visible\" :style=\"{left:left+'px',top:top+'px'}\" class=\"contextmenu\">\n      <li @click=\"refreshSelectedTag(selectedTag)\">刷新页面</li>\n      <li v-if=\"!isAffix(selectedTag)\" @click=\"closeSelectedTag(selectedTag)\">关闭当前</li>\n      <li @click=\"closeOthersTags\">关闭其他</li>\n      <li @click=\"closeAllTags(selectedTag)\">关闭所有</li>\n    </ul>\n  </div>\n</template>\n\n<script>\nimport ScrollPane from './ScrollPane'\nimport path from 'path'\n\nexport default {\n  components: { ScrollPane },\n  data() {\n    return {\n      visible: false,\n      top: 0,\n      left: 0,\n      selectedTag: {},\n      affixTags: []\n    }\n  },\n  computed: {\n    visitedViews() {\n      return this.$store.state.tagsView.visitedViews\n    },\n    routes() {\n      return this.$store.state.permission.routes\n    },\n    theme() {\n      return this.$store.state.settings.theme;\n    }\n  },\n  watch: {\n    $route() {\n      this.addTags()\n      this.moveToCurrentTag()\n    },\n    visible(value) {\n      if (value) {\n        document.body.addEventListener('click', this.closeMenu)\n      } else {\n        document.body.removeEventListener('click', this.closeMenu)\n      }\n    }\n  },\n  mounted() {\n    this.initTags()\n    this.addTags()\n  },\n  methods: {\n    isActive(route) {\n      return route.path === this.$route.path\n    },\n    activeStyle(tag) {\n      if (!this.isActive(tag)) return {};\n      return {\n        \"background-color\": this.theme,\n        \"border-color\": this.theme\n      };\n    },\n    isAffix(tag) {\n      return tag.meta && tag.meta.affix\n    },\n    filterAffixTags(routes, basePath = '/') {\n      let tags = []\n      routes.forEach(route => {\n        if (route.meta && route.meta.affix) {\n          const tagPath = path.resolve(basePath, route.path)\n          tags.push({\n            fullPath: tagPath,\n            path: tagPath,\n            name: route.name,\n            meta: { ...route.meta }\n          })\n        }\n        if (route.children) {\n          const tempTags = this.filterAffixTags(route.children, route.path)\n          if (tempTags.length >= 1) {\n            tags = [...tags, ...tempTags]\n          }\n        }\n      })\n      return tags\n    },\n    initTags() {\n      const affixTags = this.affixTags = this.filterAffixTags(this.routes)\n      for (const tag of affixTags) {\n        // Must have tag name\n        if (tag.name) {\n          this.$store.dispatch('tagsView/addVisitedView', tag)\n        }\n      }\n    },\n    addTags() {\n      const { name } = this.$route\n      if (name) {\n        this.$store.dispatch('tagsView/addView', this.$route)\n      }\n      return false\n    },\n    moveToCurrentTag() {\n      const tags = this.$refs.tag\n      this.$nextTick(() => {\n        for (const tag of tags) {\n          if (tag.to.path === this.$route.path) {\n            this.$refs.scrollPane.moveToTarget(tag)\n            // when query is different then update\n            if (tag.to.fullPath !== this.$route.fullPath) {\n              this.$store.dispatch('tagsView/updateVisitedView', this.$route)\n            }\n            break\n          }\n        }\n      })\n    },\n    refreshSelectedTag(view) {\n      this.$store.dispatch('tagsView/delCachedView', view).then(() => {\n        const { fullPath } = view\n        this.$nextTick(() => {\n          this.$router.replace({\n            path: '/redirect' + fullPath\n          })\n        })\n      })\n    },\n    closeSelectedTag(view) {\n      this.$store.dispatch('tagsView/delView', view).then(({ visitedViews }) => {\n        if (this.isActive(view)) {\n          this.toLastView(visitedViews, view)\n        }\n      })\n    },\n    closeOthersTags() {\n      this.$router.push(this.selectedTag).catch(()=>{});\n      this.$store.dispatch('tagsView/delOthersViews', this.selectedTag).then(() => {\n        this.moveToCurrentTag()\n      })\n    },\n    closeAllTags(view) {\n      this.$store.dispatch('tagsView/delAllViews').then(({ visitedViews }) => {\n        if (this.affixTags.some(tag => tag.path === this.$route.path)) {\n          return\n        }\n        this.toLastView(visitedViews, view)\n      })\n    },\n    toLastView(visitedViews, view) {\n      const latestView = visitedViews.slice(-1)[0]\n      if (latestView) {\n        this.$router.push(latestView.fullPath)\n      } else {\n        // now the default is to redirect to the home page if there is no tags-view,\n        // you can adjust it according to your needs.\n        if (view.name === 'Dashboard') {\n          // to reload home page\n          this.$router.replace({ path: '/redirect' + view.fullPath })\n        } else {\n          this.$router.push('/')\n        }\n      }\n    },\n    openMenu(tag, e) {\n      const menuMinWidth = 105\n      const offsetLeft = this.$el.getBoundingClientRect().left // container margin left\n      const offsetWidth = this.$el.offsetWidth // container width\n      const maxLeft = offsetWidth - menuMinWidth // left boundary\n      const left = e.clientX - offsetLeft + 15 // 15: margin right\n\n      if (left > maxLeft) {\n        this.left = maxLeft\n      } else {\n        this.left = left\n      }\n\n      this.top = e.clientY\n      this.visible = true\n      this.selectedTag = tag\n    },\n    closeMenu() {\n      this.visible = false\n    },\n    handleScroll() {\n      this.closeMenu()\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.tags-view-container {\n  height: 34px;\n  width: 100%;\n  background: #fff;\n  border-bottom: 1px solid #d8dce5;\n  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, .12), 0 0 3px 0 rgba(0, 0, 0, .04);\n  .tags-view-wrapper {\n    .tags-view-item {\n      display: inline-block;\n      position: relative;\n      cursor: pointer;\n      height: 26px;\n      line-height: 26px;\n      border: 1px solid #d8dce5;\n      color: #495060;\n      background: #fff;\n      padding: 0 8px;\n      font-size: 12px;\n      margin-left: 5px;\n      margin-top: 4px;\n      &:first-of-type {\n        margin-left: 15px;\n      }\n      &:last-of-type {\n        margin-right: 15px;\n      }\n      &.active {\n        background-color: #42b983;\n        color: #fff;\n        border-color: #42b983;\n        &::before {\n          content: '';\n          background: #fff;\n          display: inline-block;\n          width: 8px;\n          height: 8px;\n          border-radius: 50%;\n          position: relative;\n          margin-right: 2px;\n        }\n      }\n    }\n  }\n  .contextmenu {\n    margin: 0;\n    background: #fff;\n    z-index: 3000;\n    position: absolute;\n    list-style-type: none;\n    padding: 5px 0;\n    border-radius: 4px;\n    font-size: 12px;\n    font-weight: 400;\n    color: #333;\n    box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, .3);\n    li {\n      margin: 0;\n      padding: 7px 16px;\n      cursor: pointer;\n      &:hover {\n        background: #eee;\n      }\n    }\n  }\n}\n</style>\n\n<style lang=\"scss\">\n//reset element css of el-icon-close\n.tags-view-wrapper {\n  .tags-view-item {\n    .el-icon-close {\n      width: 16px;\n      height: 16px;\n      vertical-align: 2px;\n      border-radius: 50%;\n      text-align: center;\n      transition: all .3s cubic-bezier(.645, .045, .355, 1);\n      transform-origin: 100% 50%;\n      &:before {\n        transform: scale(.6);\n        display: inline-block;\n        vertical-align: -3px;\n      }\n      &:hover {\n        background-color: #b4bccc;\n        color: #fff;\n      }\n    }\n  }\n}\n</style>\n"]}]}