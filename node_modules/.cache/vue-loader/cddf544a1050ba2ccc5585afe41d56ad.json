{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/tool/build/IconsDialog.vue?vue&type=style&index=0&id=9733a8b8&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/tool/build/IconsDialog.vue", "mtime": 1641277326000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/css-loader/dist/cjs.js", "mtime": 1751171659443}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/loaders/stylePostLoader.js", "mtime": 1751171659924}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/postcss-loader/src/index.js", "mtime": 1751171659554}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/sass-loader/dist/cjs.js", "mtime": 1751171659051}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgouaWNvbi11bCB7CiAgbWFyZ2luOiAwOwogIHBhZGRpbmc6IDA7CiAgZm9udC1zaXplOiAwOwogIGxpIHsKICAgIGxpc3Qtc3R5bGUtdHlwZTogbm9uZTsKICAgIHRleHQtYWxpZ246IGNlbnRlcjsKICAgIGZvbnQtc2l6ZTogMTRweDsKICAgIGRpc3BsYXk6IGlubGluZS1ibG9jazsKICAgIHdpZHRoOiAxNi42NiU7CiAgICBib3gtc2l6aW5nOiBib3JkZXItYm94OwogICAgaGVpZ2h0OiAxMDhweDsKICAgIHBhZGRpbmc6IDE1cHggNnB4IDZweCA2cHg7CiAgICBjdXJzb3I6IHBvaW50ZXI7CiAgICBvdmVyZmxvdzogaGlkZGVuOwogICAgJjpob3ZlciB7CiAgICAgIGJhY2tncm91bmQ6ICNmMmYyZjI7CiAgICB9CiAgICAmLmFjdGl2ZS1pdGVtewogICAgICBiYWNrZ3JvdW5kOiAjZTFmM2ZiOwogICAgICBjb2xvcjogIzdhNmRmMAogICAgfQogICAgPiBpIHsKICAgICAgZm9udC1zaXplOiAzMHB4OwogICAgICBsaW5lLWhlaWdodDogNTBweDsKICAgIH0KICB9Cn0KLmljb24tZGlhbG9nIHsKICA6OnYtZGVlcCAuZWwtZGlhbG9nIHsKICAgIGJvcmRlci1yYWRpdXM6IDhweDsKICAgIG1hcmdpbi1ib3R0b206IDA7CiAgICBtYXJnaW4tdG9wOiA0dmggIWltcG9ydGFudDsKICAgIGRpc3BsYXk6IGZsZXg7CiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOwogICAgbWF4LWhlaWdodDogOTJ2aDsKICAgIG92ZXJmbG93OiBoaWRkZW47CiAgICBib3gtc2l6aW5nOiBib3JkZXItYm94OwogICAgLmVsLWRpYWxvZ19faGVhZGVyIHsKICAgICAgcGFkZGluZy10b3A6IDE0cHg7CiAgICB9CiAgICAuZWwtZGlhbG9nX19ib2R5IHsKICAgICAgbWFyZ2luOiAwIDIwcHggMjBweCAyMHB4OwogICAgICBwYWRkaW5nOiAwOwogICAgICBvdmVyZmxvdzogYXV0bzsKICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["IconsDialog.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0EA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "IconsDialog.vue", "sourceRoot": "src/views/tool/build", "sourcesContent": ["<template>\n  <div class=\"icon-dialog\">\n    <el-dialog\n      v-bind=\"$attrs\"\n      width=\"980px\"\n      :modal-append-to-body=\"false\"\n      v-on=\"$listeners\"\n      @open=\"onOpen\"\n      @close=\"onClose\"\n    >\n      <div slot=\"title\">\n        选择图标\n        <el-input\n          v-model=\"key\"\n          size=\"mini\"\n          :style=\"{width: '260px'}\"\n          placeholder=\"请输入图标名称\"\n          prefix-icon=\"el-icon-search\"\n          clearable\n        />\n      </div>\n      <ul class=\"icon-ul\">\n        <li\n          v-for=\"icon in iconList\"\n          :key=\"icon\"\n          :class=\"active===icon?'active-item':''\"\n          @click=\"onSelect(icon)\"\n        >\n          <i :class=\"icon\" />\n          <div>{{ icon }}</div>\n        </li>\n      </ul>\n    </el-dialog>\n  </div>\n</template>\n<script>\nimport iconList from '@/utils/generator/icon.json'\n\nconst originList = iconList.map(name => `el-icon-${name}`)\n\nexport default {\n  inheritAttrs: false,\n  props: ['current'],\n  data() {\n    return {\n      iconList: originList,\n      active: null,\n      key: ''\n    }\n  },\n  watch: {\n    key(val) {\n      if (val) {\n        this.iconList = originList.filter(name => name.indexOf(val) > -1)\n      } else {\n        this.iconList = originList\n      }\n    }\n  },\n  methods: {\n    onOpen() {\n      this.active = this.current\n      this.key = ''\n    },\n    onClose() {},\n    onSelect(icon) {\n      this.active = icon\n      this.$emit('select', icon)\n      this.$emit('update:visible', false)\n    }\n  }\n}\n</script>\n<style lang=\"scss\" scoped>\n.icon-ul {\n  margin: 0;\n  padding: 0;\n  font-size: 0;\n  li {\n    list-style-type: none;\n    text-align: center;\n    font-size: 14px;\n    display: inline-block;\n    width: 16.66%;\n    box-sizing: border-box;\n    height: 108px;\n    padding: 15px 6px 6px 6px;\n    cursor: pointer;\n    overflow: hidden;\n    &:hover {\n      background: #f2f2f2;\n    }\n    &.active-item{\n      background: #e1f3fb;\n      color: #7a6df0\n    }\n    > i {\n      font-size: 30px;\n      line-height: 50px;\n    }\n  }\n}\n.icon-dialog {\n  ::v-deep .el-dialog {\n    border-radius: 8px;\n    margin-bottom: 0;\n    margin-top: 4vh !important;\n    display: flex;\n    flex-direction: column;\n    max-height: 92vh;\n    overflow: hidden;\n    box-sizing: border-box;\n    .el-dialog__header {\n      padding-top: 14px;\n    }\n    .el-dialog__body {\n      margin: 0 20px 20px 20px;\n      padding: 0;\n      overflow: auto;\n    }\n  }\n}\n</style>\n"]}]}