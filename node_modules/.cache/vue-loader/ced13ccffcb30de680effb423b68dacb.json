{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/system/user/profile/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/system/user/profile/index.vue", "mtime": 1651940958000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB1c2VyQXZhdGFyIGZyb20gIi4vdXNlckF2YXRhciI7CmltcG9ydCB1c2VySW5mbyBmcm9tICIuL3VzZXJJbmZvIjsKaW1wb3J0IHJlc2V0UHdkIGZyb20gIi4vcmVzZXRQd2QiOwppbXBvcnQgeyBnZXRVc2VyUHJvZmlsZSB9IGZyb20gIkAvYXBpL3N5c3RlbS91c2VyIjsKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiUHJvZmlsZSIsCiAgY29tcG9uZW50czogeyB1c2VyQXZhdGFyLCB1c2VySW5mbywgcmVzZXRQd2QgfSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgdXNlcjoge30sCiAgICAgIHJvbGVHcm91cDoge30sCiAgICAgIHBvc3RHcm91cDoge30sCiAgICAgIGFjdGl2ZVRhYjogInVzZXJpbmZvIgogICAgfTsKICB9LAogIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmdldFVzZXIoKTsKICB9LAogIG1ldGhvZHM6IHsKICAgIGdldFVzZXIoKSB7CiAgICAgIGdldFVzZXJQcm9maWxlKCkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy51c2VyID0gcmVzcG9uc2UuZGF0YTsKICAgICAgICB0aGlzLnJvbGVHcm91cCA9IHJlc3BvbnNlLnJvbGVHcm91cDsKICAgICAgICB0aGlzLnBvc3RHcm91cCA9IHJlc3BvbnNlLnBvc3RHcm91cDsKICAgICAgfSk7CiAgICB9CiAgfQp9Owo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/user/profile", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-row :gutter=\"20\">\n      <el-col :span=\"6\" :xs=\"24\">\n        <el-card class=\"box-card\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>个人信息</span>\n          </div>\n          <div>\n            <div class=\"text-center\">\n              <userAvatar :user=\"user\" />\n            </div>\n            <ul class=\"list-group list-group-striped\">\n              <li class=\"list-group-item\">\n                <svg-icon icon-class=\"user\" />用户账号\n                <div class=\"pull-right\">{{ user.userName }}</div>\n              </li>\n              <li class=\"list-group-item\">\n                <svg-icon icon-class=\"phone\" />手机号码\n                <div class=\"pull-right\">{{ user.phonenumber }}</div>\n              </li>\n              <li class=\"list-group-item\">\n                <svg-icon icon-class=\"email\" />用户邮箱\n                <div class=\"pull-right\">{{ user.email }}</div>\n              </li>\n              <!-- <li class=\"list-group-item\">\n                <svg-icon icon-class=\"tree\" />所属部门\n                <div class=\"pull-right\" v-if=\"user.dept\">{{ user.dept.deptName }} / {{ postGroup }}</div>\n              </li> -->\n              <!-- <li class=\"list-group-item\">\n                <svg-icon icon-class=\"tree\" />所属岗位\n                <div class=\"pull-right\">{{ postGroup }}</div>\n              </li> -->\n              <li class=\"list-group-item\">\n                <svg-icon icon-class=\"peoples\" />所属角色\n                <div class=\"pull-right\">{{ roleGroup }}</div>\n              </li>\n              <li class=\"list-group-item\">\n                <svg-icon icon-class=\"date\" />创建日期\n                <div class=\"pull-right\">{{ user.createTime }}</div>\n              </li>\n            </ul>\n          </div>\n        </el-card>\n      </el-col>\n      <el-col :span=\"18\" :xs=\"24\">\n        <el-card>\n          <div slot=\"header\" class=\"clearfix\">\n            <span>基本资料</span>\n          </div>\n          <el-tabs v-model=\"activeTab\">\n            <el-tab-pane label=\"基本资料\" name=\"userinfo\">\n              <userInfo :user=\"user\" />\n            </el-tab-pane>\n            <el-tab-pane label=\"修改密码\" name=\"resetPwd\">\n              <resetPwd :user=\"user\" />\n            </el-tab-pane>\n          </el-tabs>\n        </el-card>\n      </el-col>\n    </el-row>\n  </div>\n</template>\n\n<script>\nimport userAvatar from \"./userAvatar\";\nimport userInfo from \"./userInfo\";\nimport resetPwd from \"./resetPwd\";\nimport { getUserProfile } from \"@/api/system/user\";\n\nexport default {\n  name: \"Profile\",\n  components: { userAvatar, userInfo, resetPwd },\n  data() {\n    return {\n      user: {},\n      roleGroup: {},\n      postGroup: {},\n      activeTab: \"userinfo\"\n    };\n  },\n  created() {\n    this.getUser();\n  },\n  methods: {\n    getUser() {\n      getUserProfile().then(response => {\n        this.user = response.data;\n        this.roleGroup = response.roleGroup;\n        this.postGroup = response.postGroup;\n      });\n    }\n  }\n};\n</script>\n"]}]}