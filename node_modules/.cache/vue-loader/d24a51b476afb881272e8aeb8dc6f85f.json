{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/system/user/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/system/user/index.vue", "mtime": 1665234686000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7CiAgbGlzdFVzZXIsCiAgZ2V0VXNlciwKICBkZWxVc2VyLAogIGFkZFVzZXIsCiAgdXBkYXRlVXNlciwKICBleHBvcnRVc2VyLAogIHJlc2V0VXNlclB3ZCwKICBjaGFuZ2VVc2VyU3RhdHVzLAogIGltcG9ydFRlbXBsYXRlLAogIHByaW50VXNlcgp9IGZyb20gIkAvYXBpL3N5c3RlbS91c2VyIjsKaW1wb3J0IHsgZ2V0VG9rZW4gfSBmcm9tICJAL3V0aWxzL2F1dGgiOwppbXBvcnQgeyB0cmVlc2VsZWN0IH0gZnJvbSAiQC9hcGkvc3lzdGVtL2RlcHQiOwppbXBvcnQgVHJlZXNlbGVjdCBmcm9tICJAcmlvcGhhZS92dWUtdHJlZXNlbGVjdCI7CmltcG9ydCAiQHJpb3BoYWUvdnVlLXRyZWVzZWxlY3QvZGlzdC92dWUtdHJlZXNlbGVjdC5jc3MiOwppbXBvcnQgSW1hZ2VVcGxvYWQgZnJvbSAiQC9jb21wb25lbnRzL0ltYWdlVXBsb2FkIjsKaW1wb3J0IHsgcHJvdmluY2VBbmRDaXR5RGF0YSxyZWdpb25EYXRhLENvZGVUb1RleHQsIFRleHRUb0NvZGUgfSBmcm9tICJlbGVtZW50LWNoaW5hLWFyZWEtZGF0YSI7CmltcG9ydCBmbG93YWJsZSBmcm9tICdAL3ZpZXdzL2Zsb3dhYmxlL3Rhc2svcmVjb3JkL3ZpZXcnOwppbXBvcnQge2dldEluc0lkQnlCaXpLZXl9IGZyb20gIkAvYXBpL2Zsb3dhYmxlL3RvZG8iOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogIlVzZXIiLAogIGNvbXBvbmVudHM6IHsgVHJlZXNlbGVjdCwgSW1hZ2VVcGxvYWQsZmxvd2FibGUgfSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgaXNNb2JpbGU6IGZhbHNlLAogICAgICBwYWdlTGF5b3V0OiAidG90YWwsIHNpemVzLCBwcmV2LCBwYWdlciwgbmV4dCwganVtcGVyIiwKICAgICAgLy8g6YGu572p5bGCCiAgICAgIGxvYWRpbmc6IHRydWUsCiAgICAgIC8vIOmAieS4reaVsOe7hAogICAgICBpZHM6IFtdLAogICAgICAvLyDpnZ7ljZXkuKrnpoHnlKgKICAgICAgc2luZ2xlOiB0cnVlLAogICAgICAvLyDpnZ7lpJrkuKrnpoHnlKgKICAgICAgbXVsdGlwbGU6IHRydWUsCiAgICAgIC8vIOaYvuekuuaQnOe0ouadoeS7tgogICAgICBzaG93U2VhcmNoOiB0cnVlLAogICAgICBzaG93RXhwb3J0OiBmYWxzZSwKICAgICAgc2hvd1ByaW50OiBmYWxzZSwKICAgICAgLy8g5oC75p2h5pWwCiAgICAgIHRvdGFsOiAwLAogICAgICAvLyDnlKjmiLfooajmoLzmlbDmja4KICAgICAgdXNlckxpc3Q6IG51bGwsCiAgICAgIC8vIOW8ueWHuuWxguagh+mimAogICAgICB0aXRsZTogIiIsCiAgICAgIC8vIOmDqOmXqOagkemAiemhuQogICAgICBkZXB0T3B0aW9uczogdW5kZWZpbmVkLAogICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYIKICAgICAgb3BlbjogZmFsc2UsCiAgICAgIC8vIOmDqOmXqOWQjeensAogICAgICBkZXB0TmFtZTogdW5kZWZpbmVkLAogICAgICAvLyDpu5jorqTlr4bnoIEKICAgICAgaW5pdFBhc3N3b3JkOiB1bmRlZmluZWQsCiAgICAgIC8vIOaXpeacn+iMg+WbtAogICAgICBkYXRlUmFuZ2U6IFtdLAogICAgICAvLyDnirbmgIHmlbDmja7lrZflhbgKICAgICAgc3RhdHVzT3B0aW9uczogW10sCiAgICAgIC8vIOaAp+WIq+eKtuaAgeWtl+WFuAogICAgICBzZXhPcHRpb25zOiBbXSwKICAgICAgLy8g55+t5L+h6YCa55+l5a2X5YW4CiAgICAgIHNtc1NlbmRPcHRpb25zOiBbXSwKICAgICAgLy8g5a6h5qC454q25oCB5a2X5YW4CiAgICAgIGF1ZGl0U3RhdHVzT3B0aW9uczogW10sCiAgICAgIC8vIOinkuiJsumAiemhuQogICAgICByb2xlT3B0aW9uczogW10sCiAgICAgIC8vIOihqOWNleWPguaVsAogICAgICBmb3JtOiB7fSwKICAgICAgZGVmYXVsdFByb3BzOiB7CiAgICAgICAgY2hpbGRyZW46ICJjaGlsZHJlbiIsCiAgICAgICAgbGFiZWw6ICJsYWJlbCIsCiAgICAgIH0sCiAgICAgIC8vIOeUqOaIt+WvvOWFpeWPguaVsAogICAgICB1cGxvYWQ6IHsKICAgICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYLvvIjnlKjmiLflr7zlhaXvvIkKICAgICAgICBvcGVuOiBmYWxzZSwKICAgICAgICAvLyDlvLnlh7rlsYLmoIfpopjvvIjnlKjmiLflr7zlhaXvvIkKICAgICAgICB0aXRsZTogIiIsCiAgICAgICAgLy8g5piv5ZCm56aB55So5LiK5LygCiAgICAgICAgaXNVcGxvYWRpbmc6IGZhbHNlLAogICAgICAgIC8vIOaYr+WQpuabtOaWsOW3sue7j+WtmOWcqOeahOeUqOaIt+aVsOaNrgogICAgICAgIHVwZGF0ZVN1cHBvcnQ6IDAsCiAgICAgICAgLy8g6K6+572u5LiK5Lyg55qE6K+35rGC5aS06YOoCiAgICAgICAgaGVhZGVyczogeyBBdXRob3JpemF0aW9uOiAiQmVhcmVyICIgKyBnZXRUb2tlbigpIH0sCiAgICAgICAgLy8g5LiK5Lyg55qE5Zyw5Z2ACiAgICAgICAgdXJsOiBwcm9jZXNzLmVudi5WVUVfQVBQX0JBU0VfQVBJICsgIi9zeXN0ZW0vdXNlci9pbXBvcnREYXRhIiwKICAgICAgfSwKICAgICAgLy8g5p+l6K+i5Y+C5pWwCiAgICAgIHF1ZXJ5UGFyYW1zOiB7CiAgICAgICAgcGFnZU51bTogMSwKICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgdXNlck5hbWU6IHVuZGVmaW5lZCwKICAgICAgICBwaG9uZW51bWJlcjogdW5kZWZpbmVkLAogICAgICAgIHN0YXR1czogdW5kZWZpbmVkLAogICAgICAgIGRlcHRJZDogdW5kZWZpbmVkLAogICAgICAgIGF1ZGl0U3RhdHVzOiB1bmRlZmluZWQsCiAgICAgIH0sCiAgICAgIC8vIOWIl+S/oeaBrwogICAgICBjb2x1bW5zOiBbCiAgICAgICAgeyBrZXk6ICdzdGF0dXMnLCBpbmRleDogMCwgbGFiZWw6IGDluJDlj7fnirbmgIFgLCB2aXNpYmxlOiB0cnVlIH0sCiAgICAgICAgeyBrZXk6ICd1c2VySWQnLCBpbmRleDogMSwgbGFiZWw6IGDnlKjmiLdJRGAsIHZpc2libGU6IHRydWUgfSwKICAgICAgICB7IGtleTogJ3VzZXJOYW1lJywgaW5kZXg6IDIsIGxhYmVsOiBg55So5oi36LSm5Y+3YCwgdmlzaWJsZTogdHJ1ZSB9LAogICAgICAgIHsga2V5OiAnbmlja05hbWUnLCBpbmRleDogMywgbGFiZWw6IGDmiqXlpIfkurrlp5PlkI1gLCB2aXNpYmxlOiB0cnVlIH0sCiAgICAgICAgeyBrZXk6ICd1c2VyVHlwZScsIGluZGV4OiA0LCBsYWJlbDogYOeUqOaIt+exu+Wei2AsIHZpc2libGU6IHRydWUgfSwKICAgICAgICB7IGtleTogJ2F1ZGl0U3RhdHVzJywgaW5kZXg6IDUsIGxhYmVsOiBg5a6h5qC454q25oCBYCwgdmlzaWJsZTogdHJ1ZSB9LAogICAgICAgIHsga2V5OiAnZW1haWwnLCBpbmRleDogNiwgbGFiZWw6IGDotYTmlpnmjqXmlLbpgq7nrrFgLCB2aXNpYmxlOiB0cnVlIH0sCiAgICAgICAgeyBrZXk6ICdwaG9uZW51bWJlcicsIGluZGV4OiA3LCBsYWJlbDogYOaKpeWkh+S6uueUteivnWAsIHZpc2libGU6IHRydWUgfSwKICAgICAgICB7IGtleTogJ3NleCcsIGluZGV4OiA4LCBsYWJlbDogYOeUqOaIt+aAp+WIq2AsIHZpc2libGU6IHRydWUgfSwKICAgICAgICB7IGtleTogJ2F2YXRhcicsIGluZGV4OiA5LCBsYWJlbDogYOWktOWDj2AsIHZpc2libGU6IHRydWUgfSwKICAgICAgICB7IGtleTogJ2NvbXBhbnknLCBpbmRleDogMTAsIGxhYmVsOiBg5YWs5Y+45YWo56ewYCwgdmlzaWJsZTogdHJ1ZSB9LAogICAgICAgIHsga2V5OiAnYnVzaW5lc3NObycsIGluZGV4OiAxMSwgbGFiZWw6IGDokKXkuJrmiafnhaflj7fnoIFgLCB2aXNpYmxlOiB0cnVlIH0sCiAgICAgICAgeyBrZXk6ICdidXNpbmVzc05vUGljJywgaW5kZXg6IDEyLCBsYWJlbDogYOiQpeS4muaJp+eFp+WbvueJh2AsIHZpc2libGU6IHRydWUgfSwKICAgICAgICB7IGtleTogJ3Byb3ZpbmNlJywgaW5kZXg6IDEzLCBsYWJlbDogYOaJgOWcqOWMuuWfn2AsIHZpc2libGU6IHRydWUgfSwKICAgICAgICB7IGtleTogJ2FkZHJlc3MnLCBpbmRleDogMTQsIGxhYmVsOiBg6LWE5paZ6YKu5a+E5Zyw5Z2AYCwgdmlzaWJsZTogdHJ1ZSB9LAogICAgICAgIHsga2V5OiAnZGVhbGVyJywgaW5kZXg6IDE1LCBsYWJlbDogYOmatuWxnue7j+mUgOWVhmAsIHZpc2libGU6IHRydWUgfSwKICAgICAgICB7IGtleTogJ2NyZWF0ZVRpbWUnLCBpbmRleDogMTYsIGxhYmVsOiBg5o+Q5Lqk5pe26Ze0YCwgdmlzaWJsZTogdHJ1ZSB9LAogICAgICAgIHsga2V5OiAndXBkYXRlVGltZScsIGluZGV4OiAxNywgbGFiZWw6IGDmnIDlkI7kv67mlLnml7bpl7RgLCB2aXNpYmxlOiB0cnVlIH0sCiAgICAgICAgeyBrZXk6ICdzbXNTZW5kJywgaW5kZXg6IDE4LCBsYWJlbDogYOefreS/oemAmuefpWAsIHZpc2libGU6IHRydWUgfSwKICAgICAgXSwKICAgICAgLy8g6KGo5Y2V5qCh6aqMCiAgICAgIHJ1bGVzOiB7CiAgICAgICAgdXNlck5hbWU6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLnlKjmiLflkI3kuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwKICAgICAgICBdLAogICAgICAgIGNvbXBhbnk6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLlhazlj7jlhajnp7DkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwKICAgICAgICBdLAogICAgICAgIGJ1c2luZXNzTm86IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLokKXkuJrmiafnhaflj7fnoIHkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwKICAgICAgICBdLAogICAgICAgIGJ1c2luZXNzTm9QaWM6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLokKXkuJrmiafnhaflm77niYflv4XkvKAiIH0sCiAgICAgICAgXSwKICAgICAgICBlbWFpbDogWwogICAgICAgICAgewogICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgICAgdHlwZTogImVtYWlsIiwKICAgICAgICAgICAgbWVzc2FnZTogIifor7fovpPlhaXmraPnoa7nmoTpgq7nrrHlnLDlnYAiLAogICAgICAgICAgICB0cmlnZ2VyOiBbImJsdXIiLCAiY2hhbmdlIl0sCiAgICAgICAgICB9LAogICAgICAgIF0sCiAgICAgICAgcGhvbmVudW1iZXI6IFsKICAgICAgICAgIHsKICAgICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICAgIHBhdHRlcm46IC9eMVszfDR8NXw2fDd8OHw5XVswLTldXGR7OH0kLywKICAgICAgICAgICAgbWVzc2FnZTogIuivt+i+k+WFpeato+ehrueahOaJi+acuuWPt+eggSIsCiAgICAgICAgICAgIHRyaWdnZXI6ICJibHVyIiwKICAgICAgICAgIH0sCiAgICAgICAgXSwKICAgICAgICBuaWNrTmFtZTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuaKpeWkh+S6uuWnk+WQjeS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9LAogICAgICAgIF0sCiAgICAgICAgcHJvdmluY2U6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLmiYDlnKjljLrln5/kuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwKICAgICAgICBdLAogICAgICAgIGFkZHJlc3M6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLotYTmlpnpgq7lr4TlnLDlnYDkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwKICAgICAgICBdLAogICAgICB9LAogICAgICBhdWRpdFN0YXR1c1RyZWU6IFtdLAogICAgICBwcm92aW5jZUFuZENpdHlEYXRhOiByZWdpb25EYXRhLAogICAgICBjaXR5T3B0aW9uczogW10sCiAgICAgIHF1ZXJ5QXJlYTogW10sCiAgICAgIHByb3ZpbmNlVHJlZXM6IFtdLAogICAgICB2aWV3T3BlbjogZmFsc2UsCiAgICAgIHZpZXc6IHt9LAogICAgICBwcm9jSW5zSWQ6IHVuZGVmaW5lZCwKICAgICAgdGFza0lkOiB1bmRlZmluZWQsCiAgICAgIGJpektleTogdW5kZWZpbmVkLAogICAgICBzaG93QXJlYTogZmFsc2UsCiAgICB9OwogIH0sCiAgd2F0Y2g6IHsKICAgIC8vIOagueaNruWQjeensOetm+mAiemDqOmXqOagkQogICAgLy8gZGVwdE5hbWUodmFsKSB7CiAgICAvLyAgIHRoaXMuJHJlZnMudHJlZS5maWx0ZXIodmFsKTsKICAgIC8vIH0KICB9LAogIGNyZWF0ZWQoKSB7CgogICAgaWYodGhpcy4kc3RvcmUuc3RhdGUudXNlci5yb2xlcyAmJiAKICAgICAgKHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIucm9sZXMuaW5jbHVkZXMoImFkbWluIikgfHwgdGhpcy4kc3RvcmUuc3RhdGUudXNlci5yb2xlcy5pbmNsdWRlcygidXNlcl9hZG1pbiIpCiAgICAgIHx8IHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIucm9sZXMuaW5jbHVkZXMoInJlcG9ydF9hZG1pbiIpLy8gfHwgdGhpcy4kc3RvcmUuc3RhdGUudXNlci5yb2xlcy5pbmNsdWRlcygicHJvdmluY2VfYWRtaW4iKQogICAgICApKXsKICAgICAgICB0aGlzLnNob3dBcmVhID0gdHJ1ZTsKICAgIH0KICAgIAogICAgdGhpcy5nZXRMaXN0KCk7CiAgICAvL3RoaXMuZ2V0VHJlZXNlbGVjdCgpOwogICAgdGhpcy5nZXREaWN0cygic3lzX25vcm1hbF9kaXNhYmxlIikudGhlbigocmVzcG9uc2UpID0+IHsKICAgICAgdGhpcy5zdGF0dXNPcHRpb25zID0gcmVzcG9uc2UuZGF0YTsKICAgIH0pOwogICAgdGhpcy5nZXREaWN0cygic3lzX3VzZXJfc2V4IikudGhlbigocmVzcG9uc2UpID0+IHsKICAgICAgdGhpcy5zZXhPcHRpb25zID0gcmVzcG9uc2UuZGF0YTsKICAgIH0pOwogICAgdGhpcy5nZXRDb25maWdLZXkoInN5cy51c2VyLmluaXRQYXNzd29yZCIpLnRoZW4oKHJlc3BvbnNlKSA9PiB7CiAgICAgIHRoaXMuaW5pdFBhc3N3b3JkID0gcmVzcG9uc2UubXNnOwogICAgfSk7CiAgICB0aGlzLmdldERpY3RzKCJwcl9zbXNfbm90aWZ5IikudGhlbigocmVzcG9uc2UpID0+IHsKICAgICAgdGhpcy5zbXNTZW5kT3B0aW9ucyA9IHJlc3BvbnNlLmRhdGE7CiAgICB9KTsKICAgIHRoaXMuZ2V0RGljdHMoInByX2F1ZGl0X3N0YXR1cyIpLnRoZW4oKHJlc3BvbnNlKSA9PiB7CiAgICAgIHZhciB0eXBlID0gMDsKICAgICAgaWYodGhpcy4kc3RvcmUuc3RhdGUudXNlci5yb2xlcyl7CiAgICAgICAgaWYodGhpcy4kc3RvcmUuc3RhdGUudXNlci5yb2xlcy5pbmNsdWRlcygiY29tbW9uIikpewogICAgICAgICAgdHlwZSA9IDE7CiAgICAgICAgfSAKICAgICAgICBpZih0aGlzLiRzdG9yZS5zdGF0ZS51c2VyLnJvbGVzLmluY2x1ZGVzKCJwcm92aW5jZV9hZG1pbiIpKXsKICAgICAgICAgIHR5cGUgPSAyOwogICAgICAgIH0KICAgICAgICBpZih0aGlzLiRzdG9yZS5zdGF0ZS51c2VyLnJvbGVzLmluY2x1ZGVzKCJyZXBvcnRfYWRtaW4iKSl7CiAgICAgICAgICB0eXBlID0gMzsKICAgICAgICB9CiAgICAgICAgaWYodGhpcy4kc3RvcmUuc3RhdGUudXNlci5yb2xlcy5pbmNsdWRlcygidXNlcl9hZG1pbiIpKXsKICAgICAgICAgIHR5cGUgPSA0OwogICAgICAgIH0KICAgICAgfQogICAgICB0aGlzLmF1ZGl0U3RhdHVzT3B0aW9ucyA9IHJlc3BvbnNlLmRhdGE7CiAgICAgIHZhciBvcHQgPSBbXTsKICAgICAgb3B0LnB1c2goe2lkOiA5LCBsYWJlbDon5YWo6YOoJ30pCiAgICAgIGlmKHR5cGUgPT0gMiB8fCB0eXBlID09IDMgfHwgdHlwZSA9PSA0KXsKICAgICAgICBvcHQucHVzaCh7aWQ6IDEwLCBsYWJlbDon5pyq5a6h5om5J30pCiAgICAgIH0KICAgICAgcmVzcG9uc2UuZGF0YS5mb3JFYWNoKChlbGVtLCBpbmRleCkgPT4gewogICAgICAgIHZhciBvYmogPSB7fTsKICAgICAgICBvYmoubGFiZWwgPSBlbGVtLmRpY3RMYWJlbDsKICAgICAgICBvYmouaWQgPSBlbGVtLmRpY3RWYWx1ZTsKICAgICAgICBvcHQucHVzaChvYmopOwogICAgICB9KTsKICAgICAgdmFyIGF1ZGl0U3RhdHVzVHJlZSA9IHt9OwogICAgICBhdWRpdFN0YXR1c1RyZWUubGFiZWwgPSAi5a6h5qC454q25oCBIjsKICAgICAgYXVkaXRTdGF0dXNUcmVlLmNoaWxkcmVuID0gb3B0OwogICAgICB2YXIgYXVkaXRTdGF0dXNUcmVlcyA9IFtdOwogICAgICBhdWRpdFN0YXR1c1RyZWVzLnB1c2goYXVkaXRTdGF0dXNUcmVlKTsKICAgICAgdGhpcy5hdWRpdFN0YXR1c1RyZWUgPSBhdWRpdFN0YXR1c1RyZWVzOwogICAgfSk7CiAgICAvL+aJgOWcqOWMuuWfn+aVsOaNruWkhOeQhgogICAgdmFyIG9wdCA9IFtdOwogICAgb3B0LnB1c2goe2lkOiAwLCBsYWJlbDon5YWo6YOoJ30pCiAgICBwcm92aW5jZUFuZENpdHlEYXRhLmZvckVhY2goKGVsZW0sIGluZGV4KSA9PiB7CiAgICAgICAgdmFyIG9iaiA9IHt9OwogICAgICAgIG9iai5sYWJlbCA9IGVsZW0ubGFiZWw7CiAgICAgICAgb2JqLmlkID0gZWxlbS5sYWJlbDsKICAgICAgICBvcHQucHVzaChvYmopOwogICAgfSk7CiAgICB2YXIgcHJvdmluY2VUcmVlID0ge307CiAgICBwcm92aW5jZVRyZWUubGFiZWwgPSAi5omA5Zyo5Yy65Z+fIjsKICAgIHByb3ZpbmNlVHJlZS5jaGlsZHJlbiA9IG9wdDsKICAgIHZhciBwcm92aW5jZVRyZWVzID0gW107CiAgICBwcm92aW5jZVRyZWVzLnB1c2gocHJvdmluY2VUcmVlKTsKICAgIHRoaXMucHJvdmluY2VUcmVlcyA9IHByb3ZpbmNlVHJlZXM7CiAgICBpZih0aGlzLl9pc01vYmlsZSgpKXsKICAgICAgdGhpcy5pc01vYmlsZSA9IHRydWU7CiAgICAgIHRoaXMucGFnZUxheW91dCA9ICJ0b3RhbCwgcHJldiwgbmV4dCwganVtcGVyIjsKICAgIH0KICB9LAogIG1ldGhvZHM6IHsKICAgIF9pc01vYmlsZSgpIHsKICAgICAgbGV0IGZsYWcgPSBuYXZpZ2F0b3IudXNlckFnZW50Lm1hdGNoKC8ocGhvbmV8cGFkfHBvZHxpUGhvbmV8aVBvZHxpb3N8aVBhZHxBbmRyb2lkfE1vYmlsZXxCbGFja0JlcnJ5fElFTW9iaWxlfE1RUUJyb3dzZXJ8SlVDfEZlbm5lY3x3T1NCcm93c2VyfEJyb3dzZXJOR3xXZWJPU3xTeW1iaWFufFdpbmRvd3MgUGhvbmUpL2kpCiAgICAgIHJldHVybiBmbGFnOwogICAgfSwKICAgIC8qKiDmn6Xor6LnlKjmiLfliJfooaggKi8KICAgIGdldExpc3QoKSB7CiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7CiAgICAgIGxpc3RVc2VyKHRoaXMuYWRkRGF0ZVJhbmdlKHRoaXMucXVlcnlQYXJhbXMsIHRoaXMuZGF0ZVJhbmdlKSkudGhlbigKICAgICAgICAocmVzcG9uc2UpID0+IHsKICAgICAgICAgIHRoaXMudXNlckxpc3QgPSByZXNwb25zZS5yb3dzOwogICAgICAgICAgdGhpcy50b3RhbCA9IHJlc3BvbnNlLnRvdGFsOwogICAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICAgICAgfQogICAgICApOwogICAgfSwKICAgIC8vIOeUqOaIt+aAp+WIq+Wtl+WFuOe/u+ivkQogICAgc2V4Rm9ybWF0KHJvdywgY29sdW1uKSB7CiAgICAgIHJldHVybiB0aGlzLnNlbGVjdERpY3RMYWJlbCh0aGlzLnNleE9wdGlvbnMsIHJvdy5zZXgpOwogICAgfSwKICAgIC8vIOW4kOWPt+eKtuaAgeWtl+WFuOe/u+ivkQogICAgc3RhdHVzRm9ybWF0KHJvdywgY29sdW1uKSB7CiAgICAgIHJldHVybiB0aGlzLnNlbGVjdERpY3RMYWJlbCh0aGlzLnN0YXR1c09wdGlvbnMsIHJvdy5zdGF0dXMpOwogICAgfSwKICAgIC8vIOefreS/oemAmuefpeWtl+WFuOe/u+ivkQogICAgc21zU2VuZEZvcm1hdChyb3csIGNvbHVtbikgewogICAgICByZXR1cm4gdGhpcy5zZWxlY3REaWN0TGFiZWwodGhpcy5zbXNTZW5kT3B0aW9ucywgcm93LnNtc1NlbmQpOwogICAgfSwKICAgIC8vIOWuoeaguOeKtuaAgeWtl+WFuOe/u+ivkQogICAgYXVkaXRTdGF0dXNGb3JtYXQocm93LCBjb2x1bW4pIHsKICAgICAgcmV0dXJuIHRoaXMuc2VsZWN0RGljdExhYmVsKHRoaXMuYXVkaXRTdGF0dXNPcHRpb25zLCByb3cuYXVkaXRTdGF0dXMpOwogICAgfSwKICAgIC8qKiDmn6Xor6Lpg6jpl6jkuIvmi4nmoJHnu5PmnoQgKi8KICAgIGdldFRyZWVzZWxlY3QoKSB7CiAgICAgIHRyZWVzZWxlY3QoKS50aGVuKChyZXNwb25zZSkgPT4gewogICAgICAgIHRoaXMuZGVwdE9wdGlvbnMgPSByZXNwb25zZS5kYXRhOwogICAgICB9KTsKICAgIH0sCiAgICAvLyDnrZvpgInoioLngrkKICAgIGZpbHRlck5vZGUodmFsdWUsIGRhdGEpIHsKICAgICAgaWYgKCF2YWx1ZSkgcmV0dXJuIHRydWU7CiAgICAgIHJldHVybiBkYXRhLmxhYmVsLmluZGV4T2YodmFsdWUpICE9PSAtMTsKICAgIH0sCiAgICAvLyDoioLngrnljZXlh7vkuovku7YKICAgIGhhbmRsZU5vZGVDbGljayhkYXRhKSB7CiAgICAgIGlmKGRhdGEuaWQgPT0gOSl7CiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5hdWRpdFN0YXR1cyA9IHVuZGVmaW5lZDsKICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLm5vZGUgPSB1bmRlZmluZWQ7CiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5zcGFyZTEgPSB1bmRlZmluZWQ7CiAgICAgIH1lbHNlewogICAgICAgIGlmKGRhdGEuaWQgPT0gMSB8fCBkYXRhLmlkID09IDEwKXsKICAgICAgICAgIHRoaXMucXVlcnlQYXJhbXMuYXVkaXRTdGF0dXMgPSAxOwogICAgICAgICAgaWYodGhpcy4kc3RvcmUuc3RhdGUudXNlci5yb2xlcyl7CiAgICAgICAgICAgIGlmKHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIucm9sZXMuaW5jbHVkZXMoInByb3ZpbmNlX2FkbWluIikpewogICAgICAgICAgICAgIHRoaXMucXVlcnlQYXJhbXMubm9kZSA9ICfnnIHotJ/otKPkuronOwogICAgICAgICAgICAgIGlmKGRhdGEuaWQgPT0gMTApewogICAgICAgICAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5zcGFyZTEgPSAnPSc7CiAgICAgICAgICAgICAgfWVsc2V7CiAgICAgICAgICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnNwYXJlMSA9ICchPSc7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9CiAgICAgICAgICAgIGlmKHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIucm9sZXMuaW5jbHVkZXMoInJlcG9ydF9hZG1pbiIpKXsKICAgICAgICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLm5vZGUgPSAn5a6h5qC45ZGYJzsKICAgICAgICAgICAgICBpZihkYXRhLmlkID09IDEwKXsKICAgICAgICAgICAgICAgIHRoaXMucXVlcnlQYXJhbXMuc3BhcmUxID0gJz0nOwogICAgICAgICAgICAgIH1lbHNlewogICAgICAgICAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5zcGFyZTEgPSAnIT0nOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfQogICAgICAgICAgICBpZih0aGlzLiRzdG9yZS5zdGF0ZS51c2VyLnJvbGVzLmluY2x1ZGVzKCJ1c2VyX2FkbWluIikpewogICAgICAgICAgICAgIHRoaXMucXVlcnlQYXJhbXMubm9kZSA9ICfnrqHnkIblkZgnOwogICAgICAgICAgICAgIGlmKGRhdGEuaWQgPT0gMTApewogICAgICAgICAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5zcGFyZTEgPSAnPSc7CiAgICAgICAgICAgICAgfWVsc2V7CiAgICAgICAgICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnNwYXJlMSA9ICchPSc7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgfWVsc2V7CiAgICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmF1ZGl0U3RhdHVzID0gZGF0YS5pZDsKICAgICAgICAgIHRoaXMucXVlcnlQYXJhbXMubm9kZSA9IHVuZGVmaW5lZDsKICAgICAgICAgIHRoaXMucXVlcnlQYXJhbXMuc3BhcmUxID0gdW5kZWZpbmVkOwogICAgICAgIH0KICAgICAgfQogICAgICB0aGlzLmdldExpc3QoKTsKICAgIH0sCiAgICAvLyDljLrln5/oioLngrnljZXlh7vkuovku7YKICAgIGhhbmRsZVByb3ZpbmNlQ2xpY2soZGF0YSkgewogICAgICBpZihkYXRhLmlkID09IDApewogICAgICAgIHRoaXMucXVlcnlQYXJhbXMucHJvdmluY2U9dW5kZWZpbmVkOwogICAgICB9ZWxzZXsKICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnByb3ZpbmNlID0gZGF0YS5pZDsKICAgICAgfQogICAgICB0aGlzLmdldExpc3QoKTsKICAgIH0sCiAgICAvLyDnlKjmiLfnirbmgIHkv67mlLkKICAgIGhhbmRsZVN0YXR1c0NoYW5nZShyb3cpIHsKICAgICAgbGV0IHRleHQgPSByb3cuc3RhdHVzID09PSAiMCIgPyAi5ZCv55SoIiA6ICLlgZznlKgiOwogICAgICB0aGlzLiRjb25maXJtKAogICAgICAgICfnoa7orqTopoEiJyArIHRleHQgKyAnIiInICsgcm93LnVzZXJOYW1lICsgJyLnlKjmiLflkJc/JywKICAgICAgICAi6K2m5ZGKIiwKICAgICAgICB7CiAgICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogIuehruWumiIsCiAgICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAi5Y+W5raIIiwKICAgICAgICAgIHR5cGU6ICJ3YXJuaW5nIiwKICAgICAgICB9CiAgICAgICkKICAgICAgICAudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgICByZXR1cm4gY2hhbmdlVXNlclN0YXR1cyhyb3cudXNlcklkLCByb3cuc3RhdHVzKTsKICAgICAgICB9KQogICAgICAgIC50aGVuKCgpID0+IHsKICAgICAgICAgIHRoaXMubXNnU3VjY2Vzcyh0ZXh0ICsgIuaIkOWKnyIpOwogICAgICAgIH0pCiAgICAgICAgLmNhdGNoKGZ1bmN0aW9uICgpIHsKICAgICAgICAgIHJvdy5zdGF0dXMgPSByb3cuc3RhdHVzID09PSAiMCIgPyAiMSIgOiAiMCI7CiAgICAgICAgfSk7CiAgICB9LAogICAgLy8g5Y+W5raI5oyJ6ZKuCiAgICBjYW5jZWwoKSB7CiAgICAgIHRoaXMub3BlbiA9IGZhbHNlOwogICAgICB0aGlzLnJlc2V0KCk7CiAgICB9LAogICAgLy8g6KGo5Y2V6YeN572uCiAgICByZXNldCgpIHsKICAgICAgdGhpcy5mb3JtID0gewogICAgICAgIHVzZXJJZDogdW5kZWZpbmVkLAogICAgICAgIGRlcHRJZDogdW5kZWZpbmVkLAogICAgICAgIHVzZXJOYW1lOiB1bmRlZmluZWQsCiAgICAgICAgbmlja05hbWU6IHVuZGVmaW5lZCwKICAgICAgICB1c2VyVHlwZTogdW5kZWZpbmVkLAogICAgICAgIGVtYWlsOiB1bmRlZmluZWQsCiAgICAgICAgcGhvbmVudW1iZXI6IHVuZGVmaW5lZCwKICAgICAgICBzZXg6ICIwIiwKICAgICAgICBhdmF0YXI6IHVuZGVmaW5lZCwKICAgICAgICBwYXNzd29yZDogdW5kZWZpbmVkLAogICAgICAgIHN0YXR1czogIjAiLAogICAgICAgIGRlbEZsYWc6IHVuZGVmaW5lZCwKICAgICAgICBsb2dpbklwOiB1bmRlZmluZWQsCiAgICAgICAgbG9naW5EYXRlOiB1bmRlZmluZWQsCiAgICAgICAgY3JlYXRlQnk6IHVuZGVmaW5lZCwKICAgICAgICBjcmVhdGVUaW1lOiB1bmRlZmluZWQsCiAgICAgICAgdXBkYXRlQnk6IHVuZGVmaW5lZCwKICAgICAgICB1cGRhdGVUaW1lOiB1bmRlZmluZWQsCiAgICAgICAgcmVtYXJrOiB1bmRlZmluZWQsCiAgICAgICAgY29tcGFueTogdW5kZWZpbmVkLAogICAgICAgIGJ1c2luZXNzTm86IHVuZGVmaW5lZCwKICAgICAgICBidXNpbmVzc05vUGljOiB1bmRlZmluZWQsCiAgICAgICAgcHJvdmluY2U6IFtdLAogICAgICAgIGFkZHJlc3M6IHVuZGVmaW5lZCwKICAgICAgICBkZWFsZXI6IHVuZGVmaW5lZCwKICAgICAgICBzbXNTZW5kOiAiMCIsCiAgICAgICAgYXVkaXRTdGF0dXM6ICIwIiwKICAgICAgICByb2xlSWRzOiBbXSwKICAgICAgfTsKICAgICAgdGhpcy5yZXNldEZvcm0oImZvcm0iKTsKICAgIH0sCiAgICAvKiog5pCc57Si5oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVRdWVyeSgpIHsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlID0gMTsKICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICB9LAogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqLwogICAgcmVzZXRRdWVyeSgpIHsKICAgICAgdGhpcy5kYXRlUmFuZ2UgPSBbXTsKICAgICAgdGhpcy5xdWVyeUFyZWEgPSBbXTsKICAgICAgdGhpcy5yZXNldEZvcm0oInF1ZXJ5Rm9ybSIpOwogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7CiAgICB9LAogICAgLy8g5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7CiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcCgoaXRlbSkgPT4gaXRlbS51c2VySWQpOwogICAgICB0aGlzLnNpbmdsZSA9IHNlbGVjdGlvbi5sZW5ndGggIT0gMTsKICAgICAgdGhpcy5tdWx0aXBsZSA9ICFzZWxlY3Rpb24ubGVuZ3RoOwogICAgfSwKICAgIGhhbmRsZVZpZXcocm93KXsKICAgICAgdGhpcy52aWV3ID0gSlNPTi5wYXJzZShKU09OLnN0cmluZ2lmeShyb3cpKTsgOwogICAgICB0aGlzLnZpZXcuYXVkaXRTdGF0dXMgPSB0aGlzLmF1ZGl0U3RhdHVzRm9ybWF0KHJvdyk7CiAgICAgIHRoaXMudmlldy5zdGF0dXMgPSB0aGlzLnN0YXR1c0Zvcm1hdChyb3cpOwogICAgICB0aGlzLnZpZXcuc21zU2VuZCA9IHRoaXMuc21zU2VuZEZvcm1hdChyb3cpOwogICAgICB0aGlzLnRpdGxlID0gIuafpeeci+eUqOaIt+ivpuaDhSI7CiAgICAgIGNvbnN0IHBhcmFtcyA9IHtiaXpLZXk6IHJvdy51c2VySWQsIGRlZktleTogJ3Byb2Nlc3NfdXNlcl9yZWcnfQogICAgICB0aGlzLmJpektleSA9IHJvdy51c2VySWQ7CiAgICAgIGdldEluc0lkQnlCaXpLZXkocGFyYW1zKS50aGVuKChyZXNwKT0+ewogICAgICAgIGlmKHJlc3AuZGF0YSAmJiByZXNwLmRhdGEuaW5zdGFuY2VJZCl7CiAgICAgICAgICB0aGlzLnByb2NJbnNJZCA9IHJlc3AuZGF0YS5pbnN0YW5jZUlkOwogICAgICAgICAgdGhpcy50YXNrSWQgPSByZXNwLmRhdGEudGFza0lkOwogICAgICAgICAgaWYocmVzcC5kYXRhLmluc3RhbmNlSWQgJiYgIXJlc3AuZGF0YS5lbmRUaW1lICYmIHJlc3AuZGF0YS5hc3NpZ25lZSA9PSB0aGlzLiRzdG9yZS5zdGF0ZS51c2VyLnVzZXJJZCl7CiAgICAgICAgICAgIHRoaXMuZmluaXNoZWQgPSBmYWxzZTsgICAgICAgICAgCiAgICAgICAgICB9ZWxzZSBpZih0aGlzLiRzdG9yZS5zdGF0ZS51c2VyLnJvbGVzICYmICh0aGlzLiRzdG9yZS5zdGF0ZS51c2VyLnJvbGVzLmluY2x1ZGVzKCJyZXBvcnRfYWRtaW4iKSkpewogICAgICAgICAgICAvL+WuoeaguOWRmOinkuiJsuS4jeaOp+WItuiwgeaTjeS9nAogICAgICAgICAgICB0aGlzLmZpbmlzaGVkID0gZmFsc2U7CiAgICAgICAgICB9ZWxzZXsKICAgICAgICAgICAgdGhpcy5maW5pc2hlZCA9IHRydWU7CiAgICAgICAgICB9ICAgICAgICAgIAogICAgICAgIH1lbHNlewogICAgICAgICAgdGhpcy5maW5pc2hlZCA9IHRydWU7CiAgICAgICAgfQogICAgICAgIHRoaXMudmlld09wZW4gPSB0cnVlOwogICAgICB9KQogICAgfSwKICAgIC8qKiDmlrDlop7mjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZUFkZCgpIHsKICAgICAgLy8gdGhpcy5yZXNldCgpOwogICAgICAvLyAvL3RoaXMuZ2V0VHJlZXNlbGVjdCgpOwogICAgICAvLyBnZXRVc2VyKCkudGhlbigocmVzcG9uc2UpID0+IHsKICAgICAgLy8gICB0aGlzLnBvc3RPcHRpb25zID0gcmVzcG9uc2UucG9zdHM7CiAgICAgIC8vICAgdGhpcy5yb2xlT3B0aW9ucyA9IHJlc3BvbnNlLnJvbGVzOwogICAgICAvLyAgIHRoaXMub3BlbiA9IHRydWU7CiAgICAgIC8vICAgdGhpcy50aXRsZSA9ICLmt7vliqDnlKjmiLciOwogICAgICAvLyAgIHRoaXMuZm9ybS5wYXNzd29yZCA9IHRoaXMuaW5pdFBhc3N3b3JkOwogICAgICAvLyB9KTsKICAgICAgdGhpcy4kcm91dGVyLnB1c2goeyBwYXRoOiAnL3N5c3RlbS91c2VyL2Zvcm0nLAogICAgICAgIHF1ZXJ5OiB7CiAgICAgICAgICBidXNpbmVzc0tleTogdW5kZWZpbmVkLAogICAgICAgICAgZm9ybUVkaXQ6IHRydWUKICAgICAgfX0pCiAgICB9LAogICAgLyoqIOS/ruaUueaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlVXBkYXRlKHJvdykgewogICAgICB0aGlzLnJlc2V0KCk7CiAgICAgIC8vdGhpcy5nZXRUcmVlc2VsZWN0KCk7CiAgICAgIGNvbnN0IHVzZXJJZCA9IHJvdy51c2VySWQgfHwgdGhpcy5pZHM7CiAgICAgIGdldFVzZXIodXNlcklkKS50aGVuKChyZXNwb25zZSkgPT4gewogICAgICAgIHRoaXMuZm9ybSA9IHJlc3BvbnNlLmRhdGE7CiAgICAgICAgdGhpcy5wb3N0T3B0aW9ucyA9IHJlc3BvbnNlLnBvc3RzOwogICAgICAgIHRoaXMucm9sZU9wdGlvbnMgPSByZXNwb25zZS5yb2xlczsKICAgICAgICB0aGlzLmZvcm0ucG9zdElkcyA9IHJlc3BvbnNlLnBvc3RJZHM7CiAgICAgICAgdGhpcy5mb3JtLnJvbGVJZHMgPSByZXNwb25zZS5yb2xlSWRzOwogICAgICAgIHRoaXMub3BlbiA9IHRydWU7CiAgICAgICAgdGhpcy50aXRsZSA9ICLkv67mlLnnlKjmiLciOwogICAgICAgIHRoaXMuZm9ybS5wYXNzd29yZCA9ICIiOwogICAgICAgIHZhciBwcm92aW5jZXMgPSByZXNwb25zZS5kYXRhLnByb3ZpbmNlOwogICAgICAgIGlmKHByb3ZpbmNlcy5sZW5ndGggPiAwKXsKICAgICAgICAgIHZhciBhZGRyZXNzID0gcHJvdmluY2VzLnNwbGl0KCIvIik7CiAgICAgICAgICB2YXIgY2l0eXMgPSBbXTsKICAgICAgICAgIC8vIOecgeS7vQogICAgICAgICAgaWYoYWRkcmVzcy5sZW5ndGggPiAwKQogICAgICAgICAgY2l0eXMucHVzaChUZXh0VG9Db2RlW2FkZHJlc3NbMF1dLmNvZGUpOwogICAgICAgICAgLy8g5Z+O5biCCiAgICAgICAgICBpZihhZGRyZXNzLmxlbmd0aCA+IDEpCiAgICAgICAgICBjaXR5cy5wdXNoKFRleHRUb0NvZGVbYWRkcmVzc1swXV1bYWRkcmVzc1sxXV0uY29kZSk7CiAgICAgICAgICAvLyDlnLDljLoKICAgICAgICAgIGlmKGFkZHJlc3MubGVuZ3RoID4gMikKICAgICAgICAgIGNpdHlzLnB1c2goVGV4dFRvQ29kZVthZGRyZXNzWzBdXVthZGRyZXNzWzFdXVthZGRyZXNzWzJdXS5jb2RlKTsKICAgICAgICAgIAogICAgICAgICAgdGhpcy5jaXR5T3B0aW9ucyA9IGNpdHlzOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLyoqIOmHjee9ruWvhueggeaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlUmVzZXRQd2Qocm93KSB7CiAgICAgIHRoaXMuJHByb21wdCgn6K+36L6T5YWlIicgKyByb3cudXNlck5hbWUgKyAnIueahOaWsOWvhueggScsICLmj5DnpLoiLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLAogICAgICB9KQogICAgICAgIC50aGVuKCh7IHZhbHVlIH0pID0+IHsKICAgICAgICAgIHJlc2V0VXNlclB3ZChyb3cudXNlcklkLCB2YWx1ZSkudGhlbigocmVzcG9uc2UpID0+IHsKICAgICAgICAgICAgdGhpcy5tc2dTdWNjZXNzKCLkv67mlLnmiJDlip/vvIzmlrDlr4bnoIHmmK/vvJoiICsgdmFsdWUpOwogICAgICAgICAgfSk7CiAgICAgICAgfSkKICAgICAgICAuY2F0Y2goKCkgPT4ge30pOwogICAgfSwKICAgIC8qKiDmj5DkuqTmjInpkq4gKi8KICAgIHN1Ym1pdEZvcm06IGZ1bmN0aW9uICgpIHsKICAgICAgaWYodGhpcy5jaXR5T3B0aW9ucy5sZW5ndGggPCAxKXsKICAgICAgICAKICAgICAgfQogICAgICB0aGlzLiRyZWZzWyJmb3JtIl0udmFsaWRhdGUoKHZhbGlkKSA9PiB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICBpZiAodGhpcy5mb3JtLnVzZXJJZCAhPSB1bmRlZmluZWQpIHsKICAgICAgICAgICAgdXBkYXRlVXNlcih0aGlzLmZvcm0pLnRoZW4oKHJlc3BvbnNlKSA9PiB7CiAgICAgICAgICAgICAgdGhpcy5tc2dTdWNjZXNzKCLkv67mlLnmiJDlip8iKTsKICAgICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsKICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICBhZGRVc2VyKHRoaXMuZm9ybSkudGhlbigocmVzcG9uc2UpID0+IHsKICAgICAgICAgICAgICB0aGlzLm1zZ1N1Y2Nlc3MoIuaWsOWinuaIkOWKnyIpOwogICAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlOwogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICAgICAgICB9KTsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDliKDpmaTmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZURlbGV0ZShyb3cpIHsKICAgICAgY29uc3QgdXNlcklkcyA9IHJvdy51c2VySWQgfHwgdGhpcy5pZHM7CiAgICAgIHRoaXMuJGNvbmZpcm0oCiAgICAgICAgJ+aYr+WQpuehruiupOWIoOmZpOeUqOaIt+e8luWPt+S4uiInICsgdXNlcklkcyArICci55qE5pWw5o2u6aG5PycsCiAgICAgICAgIuitpuWRiiIsCiAgICAgICAgewogICAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLAogICAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogIuWPlua2iCIsCiAgICAgICAgICB0eXBlOiAid2FybmluZyIsCiAgICAgICAgfQogICAgICApCiAgICAgICAgLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgICAgcmV0dXJuIGRlbFVzZXIodXNlcklkcyk7CiAgICAgICAgfSkKICAgICAgICAudGhlbigoKSA9PiB7CiAgICAgICAgICB0aGlzLmdldExpc3QoKTsKICAgICAgICAgIHRoaXMubXNnU3VjY2Vzcygi5Yig6Zmk5oiQ5YqfIik7CiAgICAgICAgfSk7CiAgICB9LAogICAgLyoqIOWvvOWFpeaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlSW1wb3J0KCkgewogICAgICB0aGlzLnVwbG9hZC50aXRsZSA9ICLnlKjmiLflr7zlhaUiOwogICAgICB0aGlzLnVwbG9hZC5vcGVuID0gdHJ1ZTsKICAgIH0sCiAgICAvKiog5LiL6L295qih5p2/5pON5L2cICovCiAgICBpbXBvcnRUZW1wbGF0ZSgpIHsKICAgICAgaW1wb3J0VGVtcGxhdGUoKS50aGVuKChyZXNwb25zZSkgPT4gewogICAgICAgIHRoaXMuZG93bmxvYWQocmVzcG9uc2UubXNnKTsKICAgICAgfSk7CiAgICB9LAogICAgLy8g5paH5Lu25LiK5Lyg5Lit5aSE55CGCiAgICBoYW5kbGVGaWxlVXBsb2FkUHJvZ3Jlc3MoZXZlbnQsIGZpbGUsIGZpbGVMaXN0KSB7CiAgICAgIHRoaXMudXBsb2FkLmlzVXBsb2FkaW5nID0gdHJ1ZTsKICAgIH0sCiAgICAvLyDmlofku7bkuIrkvKDmiJDlip/lpITnkIYKICAgIGhhbmRsZUZpbGVTdWNjZXNzKHJlc3BvbnNlLCBmaWxlLCBmaWxlTGlzdCkgewogICAgICB0aGlzLnVwbG9hZC5vcGVuID0gZmFsc2U7CiAgICAgIHRoaXMudXBsb2FkLmlzVXBsb2FkaW5nID0gZmFsc2U7CiAgICAgIHRoaXMuJHJlZnMudXBsb2FkLmNsZWFyRmlsZXMoKTsKICAgICAgdGhpcy4kYWxlcnQocmVzcG9uc2UubXNnLCAi5a+85YWl57uT5p6cIiwgeyBkYW5nZXJvdXNseVVzZUhUTUxTdHJpbmc6IHRydWUgfSk7CiAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgfSwKICAgIC8vIOaPkOS6pOS4iuS8oOaWh+S7tgogICAgc3VibWl0RmlsZUZvcm0oKSB7CiAgICAgIHRoaXMuJHJlZnMudXBsb2FkLnN1Ym1pdCgpOwogICAgfSwKICAgIGhhbmRsZUNpdHlDaGFuZ2UodmFsdWUpIHsKICAgICAgaWYoIXZhbHVlIHx8IHZhbHVlLmxlbmd0aCA9PSAwICl7CiAgICAgICAgdGhpcy5jaXR5T3B0aW9ucyA9IG51bGw7CiAgICAgICAgdGhpcy5mb3JtLnByb3ZpbmNlID0gdW5kZWZpbmVkOwogICAgICAgIHRoaXMuZm9ybS5kaXN0cmljdCA9IHVuZGVmaW5lZDsKICAgICAgICByZXR1cm4KICAgICAgfQogICAgICB0aGlzLmNpdHlPcHRpb25zID0gdmFsdWUKICAgICAgdmFyIHR4dCA9ICIiOwogICAgICB2YWx1ZS5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgICB0eHQgKz0gQ29kZVRvVGV4dFtpdGVtXSsiLyIKICAgICAgfSk7CiAgICAgIGlmKHR4dC5sZW5ndGggPiAxKXsKICAgICAgICB0eHQgPSB0eHQuc3Vic3RyaW5nKDAsIHR4dC5sZW5ndGgtMSk7CiAgICAgICAgdGhpcy5mb3JtLnByb3ZpbmNlPXR4dDsKICAgICAgICB0aGlzLmZvcm0uZGlzdHJpY3QgPSBDb2RlVG9UZXh0W3ZhbHVlWzBdXTsKICAgICAgfWVsc2V7CiAgICAgICAgdGhpcy5mb3JtLnByb3ZpbmNlPXVuZGVmaW5lZDsKICAgICAgfQogICAgICAKICAgIH0sCiAgICBoYW5kbGVRdWVyeUNpdHlDaGFuZ2UodmFsdWUpIHsKICAgICAgdGhpcy5xdWVyeUFyZWEgPSB2YWx1ZQogICAgICB2YXIgdHh0ID0gIiI7CiAgICAgIHZhbHVlLmZvckVhY2goZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICAgIHR4dCArPSBDb2RlVG9UZXh0W2l0ZW1dKyIvIgogICAgICB9KTsKICAgICAgaWYodHh0Lmxlbmd0aCA+IDEpewogICAgICAgIHR4dCA9IHR4dC5zdWJzdHJpbmcoMCwgdHh0Lmxlbmd0aC0xKTsKICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnByb3ZpbmNlPXR4dDsKICAgICAgfWVsc2V7CiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wcm92aW5jZT11bmRlZmluZWQ7CiAgICAgIH0KICAgICAgCiAgICB9LAogICAgY2xpY2tFeHBvcnQoKXsKICAgICAgdGhpcy5zaG93RXhwb3J0ID0gdHJ1ZTsKICAgIH0sCiAgICBjbGlja1ByaW50KCl7CiAgICAgIHRoaXMuc2hvd1ByaW50ID0gdHJ1ZTsKICAgIH0sCiAgICAvKiog5a+85Ye65oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVFeHBvcnQodHlwZSkgewogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnNwYXJlMSA9IHR5cGU7CiAgICAgIHZhciBjb2wgPSBbXQogICAgICB0aGlzLmNvbHVtbnMuZm9yRWFjaChpdGVtPT57CiAgICAgICAgaWYoaXRlbS52aXNpYmxlKXsKICAgICAgICAgIGNvbC5wdXNoKGl0ZW0ubGFiZWwpOwogICAgICAgIH0KICAgICAgfSkKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5zcGFyZTIgPSBjb2wuam9pbignLCcpOwogICAgICBjb25zdCBxdWVyeVBhcmFtcyA9IHRoaXMucXVlcnlQYXJhbXM7CiAgICAgIHRoaXMuJGNvbmZpcm0oIuaYr+WQpuehruiupOWvvOWHuueUqOaIt+aQnOe0oue7k+aenCIgKyAodHlwZT09MD8n5pys6aG1Jzon5YWo6YOoJykgKyAi5pWw5o2u6aG5PyIsICLorablkYoiLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLAogICAgICAgIHR5cGU6ICJ3YXJuaW5nIiwKICAgICAgfSkKICAgICAgICAudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgICByZXR1cm4gZXhwb3J0VXNlcihxdWVyeVBhcmFtcyk7CiAgICAgICAgfSkKICAgICAgICAudGhlbigocmVzcG9uc2UpID0+IHsKICAgICAgICAgIHRoaXMuZG93bmxvYWQocmVzcG9uc2UubXNnKTsKICAgICAgICAgIHRoaXMuc2hvd0V4cG9ydCA9IGZhbHNlOwogICAgICAgICAgbG9hZGluZy5jbG9zZSgpOwogICAgICAgIH0pOwogICAgfSwKICAgIGhhbmRsZVByaW50KHR5cGUpewogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnNwYXJlMSA9IHR5cGU7CiAgICAgIHZhciBwcm9wZXJ0aWVzPVtdOwogICAgICBwcm9wZXJ0aWVzLnB1c2goe2ZpZWxkOiAnaW5kZXgnLCBkaXNwbGF5TmFtZTogJ+W6j+WPtyd9KTsKICAgICAgdGhpcy5jb2x1bW5zLmZvckVhY2goaXRlbT0+ewogICAgICAgIGlmKGl0ZW0udmlzaWJsZSl7CiAgICAgICAgICBwcm9wZXJ0aWVzLnB1c2goe2ZpZWxkOiBpdGVtLmtleSwgZGlzcGxheU5hbWU6IGl0ZW0ubGFiZWx9KTsKICAgICAgICB9CiAgICAgIH0pOwogICAgICBwcmludFVzZXIodGhpcy5xdWVyeVBhcmFtcykudGhlbigocmVzcG9uc2UpID0+IHsKICAgICAgICBwcmludEpTKHsKICAgICAgICAgIHByaW50YWJsZTogcmVzcG9uc2UuZGF0YSwKICAgICAgICAgIHR5cGU6J2pzb24nLAogICAgICAgICAgcHJvcGVydGllczogcHJvcGVydGllcywKICAgICAgICAgIGhlYWRlcjonPGRpdiBzdHlsZT0idGV4dC1hbGlnbjogY2VudGVyIj48aDM+55So5oi35YiX6KGo5YiX6KGoPC9oMz48L2Rpdj4nLAogICAgICAgICAgdGFyZ2V0U3R5bGVzOlsnKiddLAogICAgICAgICAgZ3JpZEhlYWRlclN0eWxlOiAnYm9yZGVyOiAxcHggc29saWQgIzAwMDt0ZXh0LWFsaWduOmNlbnRlcicsCiAgICAgICAgICBncmlkU3R5bGU6ICdib3JkZXI6IDFweCBzb2xpZCAjMDAwO3RleHQtYWxpZ246Y2VudGVyJywKICAgICAgICAgIHN0eWxlOiJAcGFnZSB7bWFyZ2luOjAgMTBtbX0iCiAgICAgICAgfSkKCiAgICAgIH0pOwogICAgfSwKICB9LAp9Owo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAupBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/user", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-row :gutter=\"20\">\n      <!--部门数据-->\n      <el-col :span=\"4\" :xs=\"24\">\n        <div class=\"head-container\">\n          <el-tree\n            :data=\"auditStatusTree\"\n            :props=\"defaultProps\"\n            :expand-on-click-node=\"false\"\n            :filter-node-method=\"filterNode\"\n            ref=\"tree\"\n            default-expand-all\n            node-key=\"id\"\n            :current-node-key=\"0\"\n            @node-click=\"handleNodeClick\"\n          />\n          <el-tree\n            :data=\"provinceTrees\"\n            :props=\"defaultProps\"\n            :expand-on-click-node=\"false\"\n            :filter-node-method=\"filterNode\"\n            ref=\"tree\"\n            :default-expand-all=\"isMobile?false:true\"\n            node-key=\"id\"\n            :current-node-key=\"0\"\n            @node-click=\"handleProvinceClick\"\n            v-if=\"showArea\"\n          />\n        </div>\n      </el-col>\n      <!--用户数据-->\n      <el-col :span=\"20\" :xs=\"24\">\n        <el-form\n          :model=\"queryParams\"\n          ref=\"queryForm\"\n          :inline=\"true\"\n          v-show=\"showSearch\"\n          label-width=\"100px\"\n        >\n          <!-- <el-form-item label=\"所在区域\" prop=\"area\">\n            <el-cascader\n              size=\"small\"\n              clearable\n              :options=\"provinceAndCityData\"\n              :props=\"{ checkStrictly: true, expandTrigger: 'hover' }\"\n              @change=\"handleQueryCityChange\"\n            />\n          </el-form-item> -->\n          <el-form-item label=\"用户名称\" prop=\"userName\">\n            <el-input\n              v-model=\"queryParams.userName\"\n              placeholder=\"请输入用户名称\"\n              clearable\n              size=\"small\"\n              style=\"width: 240px\"\n              @keyup.enter.native=\"handleQuery\"\n            />\n          </el-form-item>\n          <el-form-item label=\"手机号码\" prop=\"phonenumber\">\n            <el-input\n              v-model=\"queryParams.phonenumber\"\n              placeholder=\"请输入手机号码\"\n              clearable\n              size=\"small\"\n              style=\"width: 240px\"\n              @keyup.enter.native=\"handleQuery\"\n            />\n          </el-form-item>\n          <el-form-item label=\"状态\" prop=\"status\">\n            <el-select\n              v-model=\"queryParams.status\"              \n              placeholder=\"用户状态\"\n              clearable\n              size=\"small\"\n              style=\"width: 240px\"\n            >\n              <el-option\n                v-for=\"dict in statusOptions\"\n                :key=\"dict.dictValue\"\n                :label=\"dict.dictLabel\"\n                :value=\"dict.dictValue\"\n              />\n            </el-select>\n          </el-form-item>\n          <el-form-item label=\"提交时间\">\n            <el-date-picker\n              v-model=\"dateRange\"\n              size=\"small\"\n              style=\"width: 240px\"\n              value-format=\"yyyy-MM-dd\"\n              type=\"daterange\"\n              range-separator=\"-\"\n              start-placeholder=\"开始日期\"\n              end-placeholder=\"结束日期\"\n            ></el-date-picker>\n          </el-form-item>\n          <el-form-item>\n            <el-button\n              type=\"primary\"\n              icon=\"el-icon-search\"\n              size=\"mini\"\n              @click=\"handleQuery\"\n              >搜索</el-button\n            >\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\"\n              >重置</el-button\n            >\n          </el-form-item>\n        </el-form>\n\n        <el-row :gutter=\"10\" class=\"mb8\">\n          <el-col :span=\"1.5\">\n            <el-button\n              type=\"primary\"\n              plain\n              icon=\"el-icon-plus\"\n              size=\"mini\"\n              @click=\"handleAdd\"\n              v-hasPermi=\"['system:user:add']\"\n              >新增</el-button\n            >\n          </el-col>\n          <el-col :span=\"1.5\">\n            <el-button\n              type=\"success\"\n              plain\n              icon=\"el-icon-edit\"\n              size=\"mini\"\n              :disabled=\"single\"\n              @click=\"handleUpdate\"\n              v-hasPermi=\"['system:user:edit']\"\n              >修改</el-button\n            >\n          </el-col>\n          <el-col :span=\"1.5\">\n            <el-button\n              type=\"danger\"\n              plain\n              icon=\"el-icon-delete\"\n              size=\"mini\"\n              :disabled=\"multiple\"\n              @click=\"handleDelete\"\n              v-hasPermi=\"['system:user:remove']\"\n              >删除</el-button\n            >\n          </el-col>\n          <el-col :span=\"1.5\">\n            <el-button\n              type=\"info\"\n              plain\n              icon=\"el-icon-upload2\"\n              size=\"mini\"\n              @click=\"handleImport\"\n              v-hasPermi=\"['system:user:import']\"\n            >导入</el-button>\n          </el-col>\n          <el-col :span=\"1.5\">\n            <el-button\n              type=\"warning\"\n              plain\n              icon=\"el-icon-download\"\n              size=\"mini\"\n              @click=\"clickExport\"\n              v-hasPermi=\"['system:user:export']\"\n              >导出</el-button\n            >\n          </el-col>\n          <el-col :span=\"1.5\">\n            <el-button icon=\"el-icon-printer\" size=\"mini\" @click=\"clickPrint\" type=\"info\" plain v-hasPermi=\"['system:user:print']\">打印</el-button>\n          </el-col>\n          <right-toolbar\n            :showSearch.sync=\"showSearch\"\n            :showExport.sync=\"showExport\"\n            :showPrint.sync=\"showPrint\"\n            @queryTable=\"getList\"\n            @export=\"handleExport\"\n            @print=\"handlePrint\"\n            :columns=\"columns\"\n          ></right-toolbar>\n        </el-row>\n\n        <el-table\n          border\n          v-loading=\"loading\"\n          :data=\"userList\"\n          @selection-change=\"handleSelectionChange\"\n        >\n          <el-table-column type=\"selection\" width=\"50\" align=\"center\" />\n          <el-table-column type=\"index\" label=\"序号\" width=\"50\" />\n          <el-table-column\n            label=\"操作\"\n            align=\"center\"\n            width=\"60\"\n            class-name=\"small-padding fixed-width\"\n          >\n            <template slot-scope=\"scope\">\n              <el-button\n                size=\"mini\"\n                type=\"text\"\n                icon=\"el-icon-view\"\n                @click=\"handleView(scope.row)\"\n                >查看</el-button>\n              <el-button\n                size=\"mini\"\n                type=\"text\"\n                icon=\"el-icon-edit\"\n                @click=\"handleUpdate(scope.row)\"\n                v-hasPermi=\"['system:user:edit']\"\n              >修改</el-button>\n              <el-button\n                v-if=\"scope.row.userId !== 1\"\n                size=\"mini\"\n                type=\"text\"\n                icon=\"el-icon-delete\"\n                @click=\"handleDelete(scope.row)\"\n                v-hasPermi=\"['system:user:remove']\"\n              >删除</el-button>\n              <el-button\n                size=\"mini\"\n                type=\"text\"\n                icon=\"el-icon-key\"\n                @click=\"handleResetPwd(scope.row)\"\n                v-hasPermi=\"['system:user:resetPwd']\"\n              >重置</el-button>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"帐号状态\" align=\"center\" prop=\"status\" v-if=\"columns[0].visible\" >\n            <template slot-scope=\"scope\">\n              <el-switch\n                :disabled=\"!showArea\"\n                v-model=\"scope.row.status\"\n                active-value=\"0\"\n                inactive-value=\"1\"\n                @change=\"handleStatusChange(scope.row)\"\n              ></el-switch>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"用户ID\" align=\"center\" prop=\"userId\" v-if=\"columns[1].visible\" />\n          <el-table-column label=\"用户账号\" align=\"center\" prop=\"userName\" v-if=\"columns[2].visible\" />\n          <el-table-column label=\"报备人姓名\" align=\"center\" prop=\"nickName\" v-if=\"columns[3].visible\" />\n          <!-- <el-table-column label=\"用户类型\" align=\"center\" prop=\"userType\" v-if=\"columns[4].visible\" /> -->\n          <el-table-column\n            label=\"审核状态\"\n            align=\"center\"\n            prop=\"auditStatus\"\n            :formatter=\"auditStatusFormat\"\n             v-if=\"columns[5].visible\"\n          />\n          <el-table-column label=\"资料接收邮箱\" align=\"center\" prop=\"email\" v-if=\"columns[6].visible\" />\n          <el-table-column\n            label=\"报备人电话\"\n            align=\"center\"\n            prop=\"phonenumber\"\n             v-if=\"columns[7].visible\"\n          />\n          <el-table-column\n            label=\"用户性别\"\n            align=\"center\"\n            prop=\"sex\"\n            :formatter=\"sexFormat\"\n             v-if=\"columns[8].visible\"\n          />\n          <!-- <el-table-column\n            label=\"头像地址\"\n            align=\"center\"\n            prop=\"avatar\"\n            width=\"100\"\n             if=\"columns[9].visible\"\n          \n            <template slot-scope=\"scope\">\n              <img :src=\"scope.row.avatar\" width=\"100\" height=\"100\" />\n            </template>\n          </el-table-column>> -->\n          <el-table-column label=\"公司全称\" align=\"center\" prop=\"company\" v-if=\"columns[10].visible\" />\n          <el-table-column\n            label=\"营业执照号码\"\n            align=\"center\"\n            prop=\"businessNo\"\n             v-if=\"columns[11].visible\"\n          />\n          <el-table-column\n            label=\"营业执照图片\"\n            align=\"center\"\n            prop=\"businessNoPic\"\n            width=\"150\"\n             v-if=\"columns[12].visible\"\n          >\n            <template slot-scope=\"scope\">\n              <img :src=\"scope.row.businessNoPic\" width=\"150\" height=\"100\" />\n            </template>\n          </el-table-column>\n          <el-table-column label=\"所在区域\" align=\"center\" prop=\"province\" v-if=\"columns[13].visible\" />\n          <el-table-column label=\"资料邮寄地址\" align=\"center\" prop=\"address\" v-if=\"columns[14].visible\" />\n          <el-table-column label=\"隶属经销商\" align=\"center\" prop=\"dealer\" v-if=\"columns[15].visible\" />\n          <el-table-column\n            label=\"提交时间\"\n            align=\"center\"\n            prop=\"createTime\"\n            v-if=\"columns[16].visible\"\n            width=\"160\"\n          >\n            <template slot-scope=\"scope\">\n              <span>{{ parseTime(scope.row.createTime) }}</span>\n            </template>\n          </el-table-column>\n          <el-table-column\n            label=\"最后修改时间\"\n            align=\"center\"\n            prop=\"updateTime\"\n            v-if=\"columns[17].visible\"\n            width=\"160\"\n          >\n            <template slot-scope=\"scope\">\n              <span>{{ parseTime(scope.row.updateTime) }}</span>\n            </template>\n          </el-table-column>\n          <el-table-column\n            label=\"短信通知\"\n            align=\"center\"\n            prop=\"smsSend\"\n            :formatter=\"smsSendFormat\"\n             v-if=\"columns[18].visible\"\n          />\n          \n        </el-table>\n\n        <pagination\n          v-show=\"total > 0\"\n          :total=\"total\"\n          :page.sync=\"queryParams.pageNum\"\n          :limit.sync=\"queryParams.pageSize\"\n          :layout=\"pageLayout\"\n          @pagination=\"getList\"\n        />\n      </el-col>\n    </el-row>\n\n    <!-- 添加或修改参数配置对话框 -->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"open\"\n      :close-on-click-modal=\"false\"\n      width=\"80%\"\n      custom-class=\"edit-dialog\"\n      append-to-body\n    >\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"用户账号\" prop=\"userName\">\n              <el-input v-model=\"form.userName\" placeholder=\"请输入用户账号\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"报备人姓名\" prop=\"nickName\">\n              <el-input\n                v-model=\"form.nickName\"\n                placeholder=\"请输入报备人姓名\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"角色\">\n              <el-select v-model=\"form.roleIds\" multiple placeholder=\"请选择\">\n                <el-option\n                  v-for=\"item in roleOptions\"\n                  :key=\"item.roleId\"\n                  :label=\"item.roleName\"\n                  :value=\"item.roleId\"\n                  :disabled=\"item.status == 1\"\n                ></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"资料接收邮箱\" prop=\"email\">\n              <el-input\n                v-model=\"form.email\"\n                placeholder=\"请输入资料接收邮箱\"\n                maxlength=\"50\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <!-- <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"岗位\">\n              <el-select v-model=\"form.postIds\" multiple placeholder=\"请选择\">\n                <el-option\n                  v-for=\"item in postOptions\"\n                  :key=\"item.postId\"\n                  :label=\"item.postName\"\n                  :value=\"item.postId\"\n                  :disabled=\"item.status == 1\"\n                ></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row> -->\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"报备人电话\" prop=\"phonenumber\">\n              <el-input\n                v-model=\"form.phonenumber\"\n                placeholder=\"请输入报备人电话\"\n                maxlength=\"11\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"用户性别\">\n              <el-select v-model=\"form.sex\" placeholder=\"请选择\">\n                <el-option\n                  v-for=\"dict in sexOptions\"\n                  :key=\"dict.dictValue\"\n                  :label=\"dict.dictLabel\"\n                  :value=\"dict.dictValue\"\n                ></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <!-- <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"头像地址\">\n              <imageUpload v-model=\"form.avatar\" />\n            </el-form-item>\n          </el-col>\n           <el-col :span=\"12\">\n            <el-form-item label=\"密码\" prop=\"password\">\n              <el-input\n                v-model=\"form.password\"\n                placeholder=\"请输入密码\"\n                type=\"password\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row> -->\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"帐号状态\">\n              <el-radio-group v-model=\"form.status\">\n                <el-radio\n                  v-for=\"dict in statusOptions\"\n                  :key=\"dict.dictValue\"\n                  :label=\"dict.dictValue\"\n                  >{{ dict.dictLabel }}</el-radio\n                >\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"备注\" prop=\"remark\">\n              <el-input\n                v-model=\"form.remark\"\n                type=\"textarea\"\n                placeholder=\"请输入内容\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"公司全称\" prop=\"company\">\n              <el-input v-model=\"form.company\" placeholder=\"请输入公司全称\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"营业执照号码\" prop=\"businessNo\">\n              <el-input\n                v-model=\"form.businessNo\"\n                placeholder=\"请输入营业执照号码\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"营业执照图片\" prop=\"businessNoPic\" :required=\"true\" error=\"营业执照图片必传\">\n              <imageUpload v-model=\"form.businessNoPic\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            \n            <el-form-item label=\"所在区域\" prop=\"province\">\n              <el-cascader\n                :options=\"provinceAndCityData\"\n                clearable\n                :props=\"{ expandTrigger: 'hover' }\"\n                v-model=\"cityOptions\"\n                @change=\"handleCityChange\"\n              >\n              </el-cascader>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"资料邮寄地址\" prop=\"address\">\n              <el-input\n                v-model=\"form.address\"\n                placeholder=\"请输入资料邮寄地址\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"隶属经销商\" prop=\"dealer\">\n              <el-input v-model=\"form.dealer\" placeholder=\"请输入隶属经销商\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"短信通知\">\n              <el-radio-group v-model=\"form.smsSend\">\n                <el-radio\n                  v-for=\"dict in smsSendOptions\"\n                  :key=\"dict.dictValue\"\n                  :label=\"dict.dictValue\"\n                  >{{ dict.dictLabel }}</el-radio\n                >\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"审核状态\">\n              <el-radio-group v-model=\"form.auditStatus\">\n                <el-radio\n                  v-for=\"dict in auditStatusOptions\"\n                  :key=\"dict.dictValue\"\n                  :label=\"dict.dictValue\"\n                  >{{ dict.dictLabel }}</el-radio\n                >\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 用户导入对话框 -->\n    <el-dialog\n      :title=\"upload.title\"\n      :visible.sync=\"upload.open\"\n      width=\"400px\"\n      append-to-body\n    >\n      <el-upload\n        ref=\"upload\"\n        :limit=\"1\"\n        accept=\".xlsx, .xls\"\n        :headers=\"upload.headers\"\n        :action=\"upload.url + '?updateSupport=' + upload.updateSupport\"\n        :disabled=\"upload.isUploading\"\n        :on-progress=\"handleFileUploadProgress\"\n        :on-success=\"handleFileSuccess\"\n        :auto-upload=\"false\"\n        drag\n      >\n        <i class=\"el-icon-upload\"></i>\n        <div class=\"el-upload__text\">\n          将文件拖到此处，或\n          <em>点击上传</em>\n        </div>\n        <div class=\"el-upload__tip\" slot=\"tip\">\n          <el-checkbox\n            v-model=\"upload.updateSupport\"\n          />是否更新已经存在的用户数据\n          <el-link type=\"info\" style=\"font-size: 12px\" @click=\"importTemplate\"\n            >下载模板</el-link\n          >\n        </div>\n        <div class=\"el-upload__tip\" style=\"color: red\" slot=\"tip\">\n          提示：仅允许导入“xls”或“xlsx”格式文件！\n        </div>\n      </el-upload>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitFileForm\">确 定</el-button>\n        <el-button @click=\"upload.open = false\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 查看项目报备对话框 -->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"viewOpen\"\n      :fullscreen=\"true\"\n      :lock-scroll=\"true\"\n      :destroy-on-close=\"true\"\n      custom-class=\"view-dialog\"\n    >\n      \n      <flowable v-if=\"viewOpen\" ref=\"flow\" procDefKey=\"process_user_reg\" :procInsId=\"procInsId\" :taskId=\"taskId\" :finished=\"finished\" :bizKey=\"bizKey\">\n        <template v-slot:title>用户信息</template>\n        <template v-slot:content>\n          <el-descriptions label-width=\"120px\" :column=\"isMobile?1:3\">\n            <el-descriptions-item label=\"帐号状态\">\n              {{ view.status }}\n            </el-descriptions-item>\n            <el-descriptions-item label=\"审核状态\">\n              {{ view.auditStatus }}\n            </el-descriptions-item>\n            <el-descriptions-item label=\"用户ID\">\n              {{ view.userId }}\n            </el-descriptions-item>\n            <el-descriptions-item label=\"用户账号\">\n              {{ view.userName }}\n            </el-descriptions-item>\n            <el-descriptions-item label=\"报备人姓名\">\n              {{ view.nickName }}\n            </el-descriptions-item>\n            <el-descriptions-item label=\"用户类型\">\n              {{ view.userType }}\n            </el-descriptions-item>\n            <el-descriptions-item label=\"资料接收邮箱\">\n              {{ view.email }}\n            </el-descriptions-item>\n            <el-descriptions-item label=\"报备人电话\">\n              {{ view.phonenumber }}\n            </el-descriptions-item>\n            <el-descriptions-item label=\"公司全称\">\n              {{ view.company }}\n            </el-descriptions-item>\n            <el-descriptions-item label=\"营业执照号码\">\n              {{ view.businessNo }}\n            </el-descriptions-item>\n            <el-descriptions-item label=\"所在区域\">\n              {{ view.province }}\n            </el-descriptions-item>\n            <el-descriptions-item label=\"资料邮寄地址\">\n              {{ view.address }}\n            </el-descriptions-item>\n            <el-descriptions-item label=\"隶属经销商\">\n              {{ view.dealer }}\n            </el-descriptions-item>        \n            <el-descriptions-item label=\"短信通知\">\n              {{ view.smsSend }}\n            </el-descriptions-item>\n            <el-descriptions-item label=\"创建时间\">\n              {{ view.createTime }}\n            </el-descriptions-item>\n            <el-descriptions-item label=\"修改时间\">\n              {{ view.updateTime }}\n            </el-descriptions-item>\n            <el-descriptions-item label=\"备注\" prop=\"remark\">\n              {{ view.remark }}\n            </el-descriptions-item>\n          </el-descriptions>\n        </template>\n      </flowable>\n    </el-dialog>\n\n  </div>\n</template>\n\n<script>\nimport {\n  listUser,\n  getUser,\n  delUser,\n  addUser,\n  updateUser,\n  exportUser,\n  resetUserPwd,\n  changeUserStatus,\n  importTemplate,\n  printUser\n} from \"@/api/system/user\";\nimport { getToken } from \"@/utils/auth\";\nimport { treeselect } from \"@/api/system/dept\";\nimport Treeselect from \"@riophae/vue-treeselect\";\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\";\nimport ImageUpload from \"@/components/ImageUpload\";\nimport { provinceAndCityData,regionData,CodeToText, TextToCode } from \"element-china-area-data\";\nimport flowable from '@/views/flowable/task/record/view';\nimport {getInsIdByBizKey} from \"@/api/flowable/todo\";\nexport default {\n  name: \"User\",\n  components: { Treeselect, ImageUpload,flowable },\n  data() {\n    return {\n      isMobile: false,\n      pageLayout: \"total, sizes, prev, pager, next, jumper\",\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      showExport: false,\n      showPrint: false,\n      // 总条数\n      total: 0,\n      // 用户表格数据\n      userList: null,\n      // 弹出层标题\n      title: \"\",\n      // 部门树选项\n      deptOptions: undefined,\n      // 是否显示弹出层\n      open: false,\n      // 部门名称\n      deptName: undefined,\n      // 默认密码\n      initPassword: undefined,\n      // 日期范围\n      dateRange: [],\n      // 状态数据字典\n      statusOptions: [],\n      // 性别状态字典\n      sexOptions: [],\n      // 短信通知字典\n      smsSendOptions: [],\n      // 审核状态字典\n      auditStatusOptions: [],\n      // 角色选项\n      roleOptions: [],\n      // 表单参数\n      form: {},\n      defaultProps: {\n        children: \"children\",\n        label: \"label\",\n      },\n      // 用户导入参数\n      upload: {\n        // 是否显示弹出层（用户导入）\n        open: false,\n        // 弹出层标题（用户导入）\n        title: \"\",\n        // 是否禁用上传\n        isUploading: false,\n        // 是否更新已经存在的用户数据\n        updateSupport: 0,\n        // 设置上传的请求头部\n        headers: { Authorization: \"Bearer \" + getToken() },\n        // 上传的地址\n        url: process.env.VUE_APP_BASE_API + \"/system/user/importData\",\n      },\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        userName: undefined,\n        phonenumber: undefined,\n        status: undefined,\n        deptId: undefined,\n        auditStatus: undefined,\n      },\n      // 列信息\n      columns: [\n        { key: 'status', index: 0, label: `帐号状态`, visible: true },\n        { key: 'userId', index: 1, label: `用户ID`, visible: true },\n        { key: 'userName', index: 2, label: `用户账号`, visible: true },\n        { key: 'nickName', index: 3, label: `报备人姓名`, visible: true },\n        { key: 'userType', index: 4, label: `用户类型`, visible: true },\n        { key: 'auditStatus', index: 5, label: `审核状态`, visible: true },\n        { key: 'email', index: 6, label: `资料接收邮箱`, visible: true },\n        { key: 'phonenumber', index: 7, label: `报备人电话`, visible: true },\n        { key: 'sex', index: 8, label: `用户性别`, visible: true },\n        { key: 'avatar', index: 9, label: `头像`, visible: true },\n        { key: 'company', index: 10, label: `公司全称`, visible: true },\n        { key: 'businessNo', index: 11, label: `营业执照号码`, visible: true },\n        { key: 'businessNoPic', index: 12, label: `营业执照图片`, visible: true },\n        { key: 'province', index: 13, label: `所在区域`, visible: true },\n        { key: 'address', index: 14, label: `资料邮寄地址`, visible: true },\n        { key: 'dealer', index: 15, label: `隶属经销商`, visible: true },\n        { key: 'createTime', index: 16, label: `提交时间`, visible: true },\n        { key: 'updateTime', index: 17, label: `最后修改时间`, visible: true },\n        { key: 'smsSend', index: 18, label: `短信通知`, visible: true },\n      ],\n      // 表单校验\n      rules: {\n        userName: [\n          { required: true, message: \"用户名不能为空\", trigger: \"blur\" },\n        ],\n        company: [\n          { required: true, message: \"公司全称不能为空\", trigger: \"blur\" },\n        ],\n        businessNo: [\n          { required: true, message: \"营业执照号码不能为空\", trigger: \"blur\" },\n        ],\n        businessNoPic: [\n          { required: true, message: \"营业执照图片必传\" },\n        ],\n        email: [\n          {\n            required: true,\n            type: \"email\",\n            message: \"'请输入正确的邮箱地址\",\n            trigger: [\"blur\", \"change\"],\n          },\n        ],\n        phonenumber: [\n          {\n            required: true,\n            pattern: /^1[3|4|5|6|7|8|9][0-9]\\d{8}$/,\n            message: \"请输入正确的手机号码\",\n            trigger: \"blur\",\n          },\n        ],\n        nickName: [\n          { required: true, message: \"报备人姓名不能为空\", trigger: \"blur\" },\n        ],\n        province: [\n          { required: true, message: \"所在区域不能为空\", trigger: \"blur\" },\n        ],\n        address: [\n          { required: true, message: \"资料邮寄地址不能为空\", trigger: \"blur\" },\n        ],\n      },\n      auditStatusTree: [],\n      provinceAndCityData: regionData,\n      cityOptions: [],\n      queryArea: [],\n      provinceTrees: [],\n      viewOpen: false,\n      view: {},\n      procInsId: undefined,\n      taskId: undefined,\n      bizKey: undefined,\n      showArea: false,\n    };\n  },\n  watch: {\n    // 根据名称筛选部门树\n    // deptName(val) {\n    //   this.$refs.tree.filter(val);\n    // }\n  },\n  created() {\n\n    if(this.$store.state.user.roles && \n      (this.$store.state.user.roles.includes(\"admin\") || this.$store.state.user.roles.includes(\"user_admin\")\n      || this.$store.state.user.roles.includes(\"report_admin\")// || this.$store.state.user.roles.includes(\"province_admin\")\n      )){\n        this.showArea = true;\n    }\n    \n    this.getList();\n    //this.getTreeselect();\n    this.getDicts(\"sys_normal_disable\").then((response) => {\n      this.statusOptions = response.data;\n    });\n    this.getDicts(\"sys_user_sex\").then((response) => {\n      this.sexOptions = response.data;\n    });\n    this.getConfigKey(\"sys.user.initPassword\").then((response) => {\n      this.initPassword = response.msg;\n    });\n    this.getDicts(\"pr_sms_notify\").then((response) => {\n      this.smsSendOptions = response.data;\n    });\n    this.getDicts(\"pr_audit_status\").then((response) => {\n      var type = 0;\n      if(this.$store.state.user.roles){\n        if(this.$store.state.user.roles.includes(\"common\")){\n          type = 1;\n        } \n        if(this.$store.state.user.roles.includes(\"province_admin\")){\n          type = 2;\n        }\n        if(this.$store.state.user.roles.includes(\"report_admin\")){\n          type = 3;\n        }\n        if(this.$store.state.user.roles.includes(\"user_admin\")){\n          type = 4;\n        }\n      }\n      this.auditStatusOptions = response.data;\n      var opt = [];\n      opt.push({id: 9, label:'全部'})\n      if(type == 2 || type == 3 || type == 4){\n        opt.push({id: 10, label:'未审批'})\n      }\n      response.data.forEach((elem, index) => {\n        var obj = {};\n        obj.label = elem.dictLabel;\n        obj.id = elem.dictValue;\n        opt.push(obj);\n      });\n      var auditStatusTree = {};\n      auditStatusTree.label = \"审核状态\";\n      auditStatusTree.children = opt;\n      var auditStatusTrees = [];\n      auditStatusTrees.push(auditStatusTree);\n      this.auditStatusTree = auditStatusTrees;\n    });\n    //所在区域数据处理\n    var opt = [];\n    opt.push({id: 0, label:'全部'})\n    provinceAndCityData.forEach((elem, index) => {\n        var obj = {};\n        obj.label = elem.label;\n        obj.id = elem.label;\n        opt.push(obj);\n    });\n    var provinceTree = {};\n    provinceTree.label = \"所在区域\";\n    provinceTree.children = opt;\n    var provinceTrees = [];\n    provinceTrees.push(provinceTree);\n    this.provinceTrees = provinceTrees;\n    if(this._isMobile()){\n      this.isMobile = true;\n      this.pageLayout = \"total, prev, next, jumper\";\n    }\n  },\n  methods: {\n    _isMobile() {\n      let flag = navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i)\n      return flag;\n    },\n    /** 查询用户列表 */\n    getList() {\n      this.loading = true;\n      listUser(this.addDateRange(this.queryParams, this.dateRange)).then(\n        (response) => {\n          this.userList = response.rows;\n          this.total = response.total;\n          this.loading = false;\n        }\n      );\n    },\n    // 用户性别字典翻译\n    sexFormat(row, column) {\n      return this.selectDictLabel(this.sexOptions, row.sex);\n    },\n    // 帐号状态字典翻译\n    statusFormat(row, column) {\n      return this.selectDictLabel(this.statusOptions, row.status);\n    },\n    // 短信通知字典翻译\n    smsSendFormat(row, column) {\n      return this.selectDictLabel(this.smsSendOptions, row.smsSend);\n    },\n    // 审核状态字典翻译\n    auditStatusFormat(row, column) {\n      return this.selectDictLabel(this.auditStatusOptions, row.auditStatus);\n    },\n    /** 查询部门下拉树结构 */\n    getTreeselect() {\n      treeselect().then((response) => {\n        this.deptOptions = response.data;\n      });\n    },\n    // 筛选节点\n    filterNode(value, data) {\n      if (!value) return true;\n      return data.label.indexOf(value) !== -1;\n    },\n    // 节点单击事件\n    handleNodeClick(data) {\n      if(data.id == 9){\n        this.queryParams.auditStatus = undefined;\n        this.queryParams.node = undefined;\n        this.queryParams.spare1 = undefined;\n      }else{\n        if(data.id == 1 || data.id == 10){\n          this.queryParams.auditStatus = 1;\n          if(this.$store.state.user.roles){\n            if(this.$store.state.user.roles.includes(\"province_admin\")){\n              this.queryParams.node = '省负责人';\n              if(data.id == 10){\n                this.queryParams.spare1 = '=';\n              }else{\n                this.queryParams.spare1 = '!=';\n              }\n            }\n            if(this.$store.state.user.roles.includes(\"report_admin\")){\n              this.queryParams.node = '审核员';\n              if(data.id == 10){\n                this.queryParams.spare1 = '=';\n              }else{\n                this.queryParams.spare1 = '!=';\n              }\n            }\n            if(this.$store.state.user.roles.includes(\"user_admin\")){\n              this.queryParams.node = '管理员';\n              if(data.id == 10){\n                this.queryParams.spare1 = '=';\n              }else{\n                this.queryParams.spare1 = '!=';\n              }\n            }\n          }\n        }else{\n          this.queryParams.auditStatus = data.id;\n          this.queryParams.node = undefined;\n          this.queryParams.spare1 = undefined;\n        }\n      }\n      this.getList();\n    },\n    // 区域节点单击事件\n    handleProvinceClick(data) {\n      if(data.id == 0){\n        this.queryParams.province=undefined;\n      }else{\n        this.queryParams.province = data.id;\n      }\n      this.getList();\n    },\n    // 用户状态修改\n    handleStatusChange(row) {\n      let text = row.status === \"0\" ? \"启用\" : \"停用\";\n      this.$confirm(\n        '确认要\"' + text + '\"\"' + row.userName + '\"用户吗?',\n        \"警告\",\n        {\n          confirmButtonText: \"确定\",\n          cancelButtonText: \"取消\",\n          type: \"warning\",\n        }\n      )\n        .then(function () {\n          return changeUserStatus(row.userId, row.status);\n        })\n        .then(() => {\n          this.msgSuccess(text + \"成功\");\n        })\n        .catch(function () {\n          row.status = row.status === \"0\" ? \"1\" : \"0\";\n        });\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        userId: undefined,\n        deptId: undefined,\n        userName: undefined,\n        nickName: undefined,\n        userType: undefined,\n        email: undefined,\n        phonenumber: undefined,\n        sex: \"0\",\n        avatar: undefined,\n        password: undefined,\n        status: \"0\",\n        delFlag: undefined,\n        loginIp: undefined,\n        loginDate: undefined,\n        createBy: undefined,\n        createTime: undefined,\n        updateBy: undefined,\n        updateTime: undefined,\n        remark: undefined,\n        company: undefined,\n        businessNo: undefined,\n        businessNoPic: undefined,\n        province: [],\n        address: undefined,\n        dealer: undefined,\n        smsSend: \"0\",\n        auditStatus: \"0\",\n        roleIds: [],\n      };\n      this.resetForm(\"form\");\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.page = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.dateRange = [];\n      this.queryArea = [];\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map((item) => item.userId);\n      this.single = selection.length != 1;\n      this.multiple = !selection.length;\n    },\n    handleView(row){\n      this.view = JSON.parse(JSON.stringify(row)); ;\n      this.view.auditStatus = this.auditStatusFormat(row);\n      this.view.status = this.statusFormat(row);\n      this.view.smsSend = this.smsSendFormat(row);\n      this.title = \"查看用户详情\";\n      const params = {bizKey: row.userId, defKey: 'process_user_reg'}\n      this.bizKey = row.userId;\n      getInsIdByBizKey(params).then((resp)=>{\n        if(resp.data && resp.data.instanceId){\n          this.procInsId = resp.data.instanceId;\n          this.taskId = resp.data.taskId;\n          if(resp.data.instanceId && !resp.data.endTime && resp.data.assignee == this.$store.state.user.userId){\n            this.finished = false;          \n          }else if(this.$store.state.user.roles && (this.$store.state.user.roles.includes(\"report_admin\"))){\n            //审核员角色不控制谁操作\n            this.finished = false;\n          }else{\n            this.finished = true;\n          }          \n        }else{\n          this.finished = true;\n        }\n        this.viewOpen = true;\n      })\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      // this.reset();\n      // //this.getTreeselect();\n      // getUser().then((response) => {\n      //   this.postOptions = response.posts;\n      //   this.roleOptions = response.roles;\n      //   this.open = true;\n      //   this.title = \"添加用户\";\n      //   this.form.password = this.initPassword;\n      // });\n      this.$router.push({ path: '/system/user/form',\n        query: {\n          businessKey: undefined,\n          formEdit: true\n      }})\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset();\n      //this.getTreeselect();\n      const userId = row.userId || this.ids;\n      getUser(userId).then((response) => {\n        this.form = response.data;\n        this.postOptions = response.posts;\n        this.roleOptions = response.roles;\n        this.form.postIds = response.postIds;\n        this.form.roleIds = response.roleIds;\n        this.open = true;\n        this.title = \"修改用户\";\n        this.form.password = \"\";\n        var provinces = response.data.province;\n        if(provinces.length > 0){\n          var address = provinces.split(\"/\");\n          var citys = [];\n          // 省份\n          if(address.length > 0)\n          citys.push(TextToCode[address[0]].code);\n          // 城市\n          if(address.length > 1)\n          citys.push(TextToCode[address[0]][address[1]].code);\n          // 地区\n          if(address.length > 2)\n          citys.push(TextToCode[address[0]][address[1]][address[2]].code);\n          \n          this.cityOptions = citys;\n        }\n      });\n    },\n    /** 重置密码按钮操作 */\n    handleResetPwd(row) {\n      this.$prompt('请输入\"' + row.userName + '\"的新密码', \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n      })\n        .then(({ value }) => {\n          resetUserPwd(row.userId, value).then((response) => {\n            this.msgSuccess(\"修改成功，新密码是：\" + value);\n          });\n        })\n        .catch(() => {});\n    },\n    /** 提交按钮 */\n    submitForm: function () {\n      if(this.cityOptions.length < 1){\n        \n      }\n      this.$refs[\"form\"].validate((valid) => {\n        if (valid) {\n          if (this.form.userId != undefined) {\n            updateUser(this.form).then((response) => {\n              this.msgSuccess(\"修改成功\");\n              this.open = false;\n              this.getList();\n            });\n          } else {\n            addUser(this.form).then((response) => {\n              this.msgSuccess(\"新增成功\");\n              this.open = false;\n              this.getList();\n            });\n          }\n        }\n      });\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const userIds = row.userId || this.ids;\n      this.$confirm(\n        '是否确认删除用户编号为\"' + userIds + '\"的数据项?',\n        \"警告\",\n        {\n          confirmButtonText: \"确定\",\n          cancelButtonText: \"取消\",\n          type: \"warning\",\n        }\n      )\n        .then(function () {\n          return delUser(userIds);\n        })\n        .then(() => {\n          this.getList();\n          this.msgSuccess(\"删除成功\");\n        });\n    },\n    /** 导入按钮操作 */\n    handleImport() {\n      this.upload.title = \"用户导入\";\n      this.upload.open = true;\n    },\n    /** 下载模板操作 */\n    importTemplate() {\n      importTemplate().then((response) => {\n        this.download(response.msg);\n      });\n    },\n    // 文件上传中处理\n    handleFileUploadProgress(event, file, fileList) {\n      this.upload.isUploading = true;\n    },\n    // 文件上传成功处理\n    handleFileSuccess(response, file, fileList) {\n      this.upload.open = false;\n      this.upload.isUploading = false;\n      this.$refs.upload.clearFiles();\n      this.$alert(response.msg, \"导入结果\", { dangerouslyUseHTMLString: true });\n      this.getList();\n    },\n    // 提交上传文件\n    submitFileForm() {\n      this.$refs.upload.submit();\n    },\n    handleCityChange(value) {\n      if(!value || value.length == 0 ){\n        this.cityOptions = null;\n        this.form.province = undefined;\n        this.form.district = undefined;\n        return\n      }\n      this.cityOptions = value\n      var txt = \"\";\n      value.forEach(function (item) {\n          txt += CodeToText[item]+\"/\"\n      });\n      if(txt.length > 1){\n        txt = txt.substring(0, txt.length-1);\n        this.form.province=txt;\n        this.form.district = CodeToText[value[0]];\n      }else{\n        this.form.province=undefined;\n      }\n      \n    },\n    handleQueryCityChange(value) {\n      this.queryArea = value\n      var txt = \"\";\n      value.forEach(function (item) {\n          txt += CodeToText[item]+\"/\"\n      });\n      if(txt.length > 1){\n        txt = txt.substring(0, txt.length-1);\n        this.queryParams.province=txt;\n      }else{\n        this.queryParams.province=undefined;\n      }\n      \n    },\n    clickExport(){\n      this.showExport = true;\n    },\n    clickPrint(){\n      this.showPrint = true;\n    },\n    /** 导出按钮操作 */\n    handleExport(type) {\n      this.queryParams.spare1 = type;\n      var col = []\n      this.columns.forEach(item=>{\n        if(item.visible){\n          col.push(item.label);\n        }\n      })\n      this.queryParams.spare2 = col.join(',');\n      const queryParams = this.queryParams;\n      this.$confirm(\"是否确认导出用户搜索结果\" + (type==0?'本页':'全部') + \"数据项?\", \"警告\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\",\n      })\n        .then(function () {\n          return exportUser(queryParams);\n        })\n        .then((response) => {\n          this.download(response.msg);\n          this.showExport = false;\n          loading.close();\n        });\n    },\n    handlePrint(type){\n      this.queryParams.spare1 = type;\n      var properties=[];\n      properties.push({field: 'index', displayName: '序号'});\n      this.columns.forEach(item=>{\n        if(item.visible){\n          properties.push({field: item.key, displayName: item.label});\n        }\n      });\n      printUser(this.queryParams).then((response) => {\n        printJS({\n          printable: response.data,\n          type:'json',\n          properties: properties,\n          header:'<div style=\"text-align: center\"><h3>用户列表列表</h3></div>',\n          targetStyles:['*'],\n          gridHeaderStyle: 'border: 1px solid #000;text-align:center',\n          gridStyle: 'border: 1px solid #000;text-align:center',\n          style:\"@page {margin:0 10mm}\"\n        })\n\n      });\n    },\n  },\n};\n</script>\n<style>\n@media screen and (min-width: 600px) {\n  .view-dialog{\n    width: 80% !important;\n    float: right;\n  }\n}\n@media screen and (max-width: 599px) {\n  .el-form .el-col{\n    width: 100% !important;\n  }\n  .edit-dialog{\n    width: 100% !important;\n    margin-bottom: 0;\n    height: 100%;\n    overflow: auto;\n  }\n  .el-dialog:not(.is-fullscreen) {\n    margin-top: 0 !important;\n  }\n}\n</style>"]}]}