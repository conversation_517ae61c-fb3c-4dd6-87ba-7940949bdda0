{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/components/ImageUpload/index.vue?vue&type=template&id=82a94682&scoped=true", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/components/ImageUpload/index.vue", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/loaders/templateLoader.js", "mtime": 1751171659981}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:dmFyIHJlbmRlciA9IGZ1bmN0aW9uICgpIHsKICB2YXIgX3ZtID0gdGhpcwogIHZhciBfaCA9IF92bS4kY3JlYXRlRWxlbWVudAogIHZhciBfYyA9IF92bS5fc2VsZi5fYyB8fCBfaAogIHJldHVybiBfYygKICAgICJkaXYiLAogICAgeyBzdGF0aWNDbGFzczogImNvbXBvbmVudC11cGxvYWQtaW1hZ2UiIH0sCiAgICBbCiAgICAgIF9jKAogICAgICAgICJlbC11cGxvYWQiLAogICAgICAgIHsKICAgICAgICAgIHN0YXRpY1N0eWxlOiB7IGRpc3BsYXk6ICJpbmxpbmUtYmxvY2siLCAidmVydGljYWwtYWxpZ24iOiAidG9wIiB9LAogICAgICAgICAgYXR0cnM6IHsKICAgICAgICAgICAgYWN0aW9uOiBfdm0udXBsb2FkSW1nVXJsLAogICAgICAgICAgICAibGlzdC10eXBlIjogInBpY3R1cmUtY2FyZCIsCiAgICAgICAgICAgICJvbi1zdWNjZXNzIjogX3ZtLmhhbmRsZVVwbG9hZFN1Y2Nlc3MsCiAgICAgICAgICAgICJiZWZvcmUtdXBsb2FkIjogX3ZtLmhhbmRsZUJlZm9yZVVwbG9hZCwKICAgICAgICAgICAgIm9uLWVycm9yIjogX3ZtLmhhbmRsZVVwbG9hZEVycm9yLAogICAgICAgICAgICBuYW1lOiAiZmlsZSIsCiAgICAgICAgICAgICJzaG93LWZpbGUtbGlzdCI6IGZhbHNlLAogICAgICAgICAgICBoZWFkZXJzOiBfdm0uaGVhZGVycywKICAgICAgICAgIH0sCiAgICAgICAgfSwKICAgICAgICBbCiAgICAgICAgICAhX3ZtLnZhbHVlCiAgICAgICAgICAgID8gX2MoImVsLWltYWdlIiwgeyBhdHRyczogeyBzcmM6IF92bS52YWx1ZSB9IH0sIFsKICAgICAgICAgICAgICAgIF9jKAogICAgICAgICAgICAgICAgICAiZGl2IiwKICAgICAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgICAgIHN0YXRpY0NsYXNzOiAiaW1hZ2Utc2xvdCIsCiAgICAgICAgICAgICAgICAgICAgYXR0cnM6IHsgc2xvdDogImVycm9yIiB9LAogICAgICAgICAgICAgICAgICAgIHNsb3Q6ICJlcnJvciIsCiAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgIFtfYygiaSIsIHsgc3RhdGljQ2xhc3M6ICJlbC1pY29uLXBsdXMiIH0pXQogICAgICAgICAgICAgICAgKSwKICAgICAgICAgICAgICBdKQogICAgICAgICAgICA6IF9jKAogICAgICAgICAgICAgICAgImRpdiIsCiAgICAgICAgICAgICAgICB7IHN0YXRpY0NsYXNzOiAiaW1hZ2UiIH0sCiAgICAgICAgICAgICAgICBbCiAgICAgICAgICAgICAgICAgIF9jKCJlbC1pbWFnZSIsIHsKICAgICAgICAgICAgICAgICAgICBzdHlsZTogIndpZHRoOjE1MHB4O2hlaWdodDoxNTBweDsiLAogICAgICAgICAgICAgICAgICAgIGF0dHJzOiB7IHNyYzogX3ZtLnZhbHVlLCBmaXQ6ICJmaWxsIiB9LAogICAgICAgICAgICAgICAgICB9KSwKICAgICAgICAgICAgICAgICAgX2MoImRpdiIsIHsgc3RhdGljQ2xhc3M6ICJtYXNrIiB9LCBbCiAgICAgICAgICAgICAgICAgICAgX2MoImRpdiIsIHsgc3RhdGljQ2xhc3M6ICJhY3Rpb25zIiB9LCBbCiAgICAgICAgICAgICAgICAgICAgICBfYygKICAgICAgICAgICAgICAgICAgICAgICAgInNwYW4iLAogICAgICAgICAgICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgYXR0cnM6IHsgdGl0bGU6ICLpooTop4giIH0sCiAgICAgICAgICAgICAgICAgICAgICAgICAgb246IHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsaWNrOiBmdW5jdGlvbiAoJGV2ZW50KSB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICRldmVudC5zdG9wUHJvcGFnYXRpb24oKQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICBfdm0uZGlhbG9nVmlzaWJsZSA9IHRydWUKICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICAgICAgW19jKCJpIiwgeyBzdGF0aWNDbGFzczogImVsLWljb24tem9vbS1pbiIgfSldCiAgICAgICAgICAgICAgICAgICAgICApLAogICAgICAgICAgICAgICAgICAgICAgX2MoCiAgICAgICAgICAgICAgICAgICAgICAgICJzcGFuIiwKICAgICAgICAgICAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgICAgICAgICAgIGF0dHJzOiB7IHRpdGxlOiAi56e76ZmkIiB9LAogICAgICAgICAgICAgICAgICAgICAgICAgIG9uOiB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGljazogZnVuY3Rpb24gKCRldmVudCkgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAkZXZlbnQuc3RvcFByb3BhZ2F0aW9uKCkKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIF92bS5yZW1vdmVJbWFnZSgkZXZlbnQpCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAgICAgIFtfYygiaSIsIHsgc3RhdGljQ2xhc3M6ICJlbC1pY29uLWRlbGV0ZSIgfSldCiAgICAgICAgICAgICAgICAgICAgICApLAogICAgICAgICAgICAgICAgICAgIF0pLAogICAgICAgICAgICAgICAgICBdKSwKICAgICAgICAgICAgICAgIF0sCiAgICAgICAgICAgICAgICAxCiAgICAgICAgICAgICAgKSwKICAgICAgICBdLAogICAgICAgIDEKICAgICAgKSwKICAgICAgX2MoCiAgICAgICAgImVsLWRpYWxvZyIsCiAgICAgICAgewogICAgICAgICAgYXR0cnM6IHsKICAgICAgICAgICAgdmlzaWJsZTogX3ZtLmRpYWxvZ1Zpc2libGUsCiAgICAgICAgICAgIHRpdGxlOiAi6aKE6KeIIiwKICAgICAgICAgICAgd2lkdGg6ICI4MDAiLAogICAgICAgICAgICAiYXBwZW5kLXRvLWJvZHkiOiAiIiwKICAgICAgICAgIH0sCiAgICAgICAgICBvbjogewogICAgICAgICAgICAidXBkYXRlOnZpc2libGUiOiBmdW5jdGlvbiAoJGV2ZW50KSB7CiAgICAgICAgICAgICAgX3ZtLmRpYWxvZ1Zpc2libGUgPSAkZXZlbnQKICAgICAgICAgICAgfSwKICAgICAgICAgIH0sCiAgICAgICAgfSwKICAgICAgICBbCiAgICAgICAgICBfYygiaW1nIiwgewogICAgICAgICAgICBzdGF0aWNTdHlsZTogewogICAgICAgICAgICAgIGRpc3BsYXk6ICJibG9jayIsCiAgICAgICAgICAgICAgIm1heC13aWR0aCI6ICIxMDAlIiwKICAgICAgICAgICAgICBtYXJnaW46ICIwIGF1dG8iLAogICAgICAgICAgICB9LAogICAgICAgICAgICBhdHRyczogeyBzcmM6IF92bS52YWx1ZSB9LAogICAgICAgICAgfSksCiAgICAgICAgXQogICAgICApLAogICAgXSwKICAgIDEKICApCn0KdmFyIHN0YXRpY1JlbmRlckZucyA9IFtdCnJlbmRlci5fd2l0aFN0cmlwcGVkID0gdHJ1ZQoKZXhwb3J0IHsgcmVuZGVyLCBzdGF0aWNSZW5kZXJGbnMgfQ=="}]}