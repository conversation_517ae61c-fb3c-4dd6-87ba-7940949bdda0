{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/flowable/task/process/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/flowable/task/process/index.vue", "mtime": 1662389810000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7CiAgZ2V0RGVwbG95bWVudCwKICBkZWxEZXBsb3ltZW50LAogIGFkZERlcGxveW1lbnQsCiAgdXBkYXRlRGVwbG95bWVudCwKICBleHBvcnREZXBsb3ltZW50LAogIGZsb3dSZWNvcmQKfSBmcm9tICJAL2FwaS9mbG93YWJsZS9maW5pc2hlZCI7CmltcG9ydCB7IG15UHJvY2Vzc0xpc3Qsc3RvcFByb2Nlc3MgfSBmcm9tICJAL2FwaS9mbG93YWJsZS9wcm9jZXNzIjsKaW1wb3J0IHtsaXN0RGVmaW5pdGlvbn0gZnJvbSAiQC9hcGkvZmxvd2FibGUvZGVmaW5pdGlvbiI7CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiRGVwbG95IiwKICBjb21wb25lbnRzOiB7CiAgfSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgLy8g6YGu572p5bGCCiAgICAgIGxvYWRpbmc6IHRydWUsCiAgICAgIHByb2Nlc3NMb2FkaW5nOiB0cnVlLAogICAgICAvLyDpgInkuK3mlbDnu4QKICAgICAgaWRzOiBbXSwKICAgICAgLy8g6Z2e5Y2V5Liq56aB55SoCiAgICAgIHNpbmdsZTogdHJ1ZSwKICAgICAgLy8g6Z2e5aSa5Liq56aB55SoCiAgICAgIG11bHRpcGxlOiB0cnVlLAogICAgICAvLyDmmL7npLrmkJzntKLmnaHku7YKICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwKICAgICAgLy8g5oC75p2h5pWwCiAgICAgIHRvdGFsOiAwLAogICAgICBwcm9jZXNzVG90YWw6MCwKICAgICAgLy8g5oiR5Y+R6LW355qE5rWB56iL5YiX6KGo5pWw5o2uCiAgICAgIG15UHJvY2Vzc0xpc3Q6IFtdLAogICAgICAvLyDlvLnlh7rlsYLmoIfpopgKICAgICAgdGl0bGU6ICIiLAogICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYIKICAgICAgb3BlbjogZmFsc2UsCiAgICAgIHNyYzogIiIsCiAgICAgIGRlZmluaXRpb25MaXN0OltdLAogICAgICAvLyDmn6Xor6Llj4LmlbAKICAgICAgcXVlcnlQYXJhbXM6IHsKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICBuYW1lOiBudWxsLAogICAgICAgIGNhdGVnb3J5OiBudWxsLAogICAgICAgIGtleTogbnVsbCwKICAgICAgICB0ZW5hbnRJZDogbnVsbCwKICAgICAgICBkZXBsb3lUaW1lOiBudWxsLAogICAgICAgIGRlcml2ZWRGcm9tOiBudWxsLAogICAgICAgIGRlcml2ZWRGcm9tUm9vdDogbnVsbCwKICAgICAgICBwYXJlbnREZXBsb3ltZW50SWQ6IG51bGwsCiAgICAgICAgZW5naW5lVmVyc2lvbjogbnVsbAogICAgICB9LAogICAgICAvLyDooajljZXlj4LmlbAKICAgICAgZm9ybToge30sCiAgICAgIC8vIOihqOWNleagoemqjAogICAgICBydWxlczogewogICAgICB9LAogICAgfTsKICB9LAogIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmdldExpc3QoKTsKICB9LAogIG1ldGhvZHM6IHsKICAgIC8qKiDmn6Xor6LmtYHnqIvlrprkuYnliJfooaggKi8KICAgIGdldExpc3QoKSB7CiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7CiAgICAgIG15UHJvY2Vzc0xpc3QodGhpcy5xdWVyeVBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5teVByb2Nlc3NMaXN0ID0gcmVzcG9uc2UuZGF0YS5yZWNvcmRzOwogICAgICAgIHRoaXMudG90YWwgPSByZXNwb25zZS5kYXRhLnRvdGFsOwogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOwogICAgICB9KTsKICAgIH0sCiAgICAvLyDlj5bmtojmjInpkq4KICAgIGNhbmNlbCgpIHsKICAgICAgdGhpcy5vcGVuID0gZmFsc2U7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgIH0sCiAgICAvLyDooajljZXph43nva4KICAgIHJlc2V0KCkgewogICAgICB0aGlzLmZvcm0gPSB7CiAgICAgICAgaWQ6IG51bGwsCiAgICAgICAgbmFtZTogbnVsbCwKICAgICAgICBjYXRlZ29yeTogbnVsbCwKICAgICAgICBrZXk6IG51bGwsCiAgICAgICAgdGVuYW50SWQ6IG51bGwsCiAgICAgICAgZGVwbG95VGltZTogbnVsbCwKICAgICAgICBkZXJpdmVkRnJvbTogbnVsbCwKICAgICAgICBkZXJpdmVkRnJvbVJvb3Q6IG51bGwsCiAgICAgICAgcGFyZW50RGVwbG95bWVudElkOiBudWxsLAogICAgICAgIGVuZ2luZVZlcnNpb246IG51bGwKICAgICAgfTsKICAgICAgdGhpcy5yZXNldEZvcm0oImZvcm0iKTsKICAgIH0sCiAgICAvKiog5pCc57Si5oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVRdWVyeSgpIHsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsKICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICB9LAogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqLwogICAgcmVzZXRRdWVyeSgpIHsKICAgICAgdGhpcy5yZXNldEZvcm0oInF1ZXJ5Rm9ybSIpOwogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7CiAgICB9LAogICAgLy8g5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7CiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcChpdGVtID0+IGl0ZW0uaWQpCiAgICAgIHRoaXMuc2luZ2xlID0gc2VsZWN0aW9uLmxlbmd0aCE9PTEKICAgICAgdGhpcy5tdWx0aXBsZSA9ICFzZWxlY3Rpb24ubGVuZ3RoCiAgICB9LAogICAgLyoqIOaWsOWinuaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlQWRkKCkgewogICAgICB0aGlzLm9wZW4gPSB0cnVlOwogICAgICB0aGlzLnRpdGxlID0gIuWPkei1t+a1geeoiyI7CiAgICAgIHRoaXMubGlzdERlZmluaXRpb24oKTsKICAgIH0sCiAgICBsaXN0RGVmaW5pdGlvbigpewogICAgICBsaXN0RGVmaW5pdGlvbih0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLmRlZmluaXRpb25MaXN0ID0gcmVzcG9uc2UuZGF0YS5yZWNvcmRzOwogICAgICAgIHRoaXMucHJvY2Vzc1RvdGFsID0gcmVzcG9uc2UuZGF0YS50b3RhbDsKICAgICAgICB0aGlzLnByb2Nlc3NMb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiAg5Y+R6LW35rWB56iL55Sz6K+3ICovCiAgICBoYW5kbGVTdGFydFByb2Nlc3Mocm93KXsKICAgICAgLy8gdGhpcy4kcm91dGVyLnB1c2goeyBwYXRoOiAnL2Zsb3dhYmxlL3Rhc2svcmVjb3JkL2luZGV4JywKICAgICAgLy8gICBxdWVyeTogewogICAgICAvLyAgICAgZGVwbG95SWQ6IHJvdy5kZXBsb3ltZW50SWQsCiAgICAgIC8vICAgICBwcm9jRGVmSWQ6cm93LmlkLAogICAgICAvLyAgICAgZmluaXNoZWQ6IHRydWUKICAgICAgLy8gICAgIH0KICAgICAgLy8gfSkKICAgICAgdmFyIHBhdGg7CiAgICAgIGlmKHJvdy5rZXkgPT0gJ3Byb2Nlc3NfcHJvamVjdF9yZXBvcnQnKXsKICAgICAgICBwYXRoID0gJy9wcm9qZWN0L3JlcG9ydC9mb3JtJzsKICAgICAgfWVsc2UgaWYocm93LmtleSA9PSAncHJvY2Vzc191c2VyX3JlZycpewogICAgICAgIHBhdGggPSAnL3N5c3RlbS91c2VyL2Zvcm0nOwogICAgICB9CgogICAgICB0aGlzLiRyb3V0ZXIucHVzaCh7IHBhdGg6IHBhdGh9KQogICAgfSwKICAgIC8qKiAg5Y+W5raI5rWB56iL55Sz6K+3ICovCiAgICBoYW5kbGVTdG9wKHJvdyl7CiAgICAgIGNvbnN0IHBhcmFtcyA9IHsKICAgICAgICBpbnN0YW5jZUlkOiByb3cucHJvY0luc0lkCiAgICAgIH0KICAgICAgc3RvcFByb2Nlc3MocGFyYW1zKS50aGVuKCByZXMgPT4gewogICAgICAgIHRoaXMubXNnU3VjY2VzcyhyZXMubXNnKTsKICAgICAgICB0aGlzLmdldExpc3QoKTsKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOa1geeoi+a1gei9rOiusOW9lSAqLwogICAgaGFuZGxlRmxvd1JlY29yZChyb3cpewogICAgICAvLyB0aGlzLiRyb3V0ZXIucHVzaCh7IHBhdGg6ICcvZmxvd2FibGUvdGFzay9yZWNvcmQvaW5kZXgnLAogICAgICAvLyAgIHF1ZXJ5OiB7CiAgICAgIC8vICAgICBwcm9jSW5zSWQ6IHJvdy5wcm9jSW5zSWQsCiAgICAgIC8vICAgICBkZXBsb3lJZDogcm93LmRlcGxveUlkLAogICAgICAvLyAgICAgdGFza0lkOiByb3cudGFza0lkLAogICAgICAvLyAgICAgZmluaXNoZWQ6IGZhbHNlCiAgICAgIC8vIH19KQoKICAgICAgdmFyIHBhdGg7CiAgICAgIC8vY29uc29sZS5sb2cocm93LnByb2NEZWZLZXkpCiAgICAgIGlmKHJvdy5wcm9jRGVmS2V5ID09ICdwcm9jZXNzX3Byb2plY3RfcmVwb3J0Jyl7CiAgICAgICAgcGF0aCA9ICcvcHJvamVjdC9yZXBvcnQvZm9ybSc7CiAgICAgIH1lbHNlIGlmKHJvdy5wcm9jRGVmS2V5ID09ICdwcm9jZXNzX3VzZXJfcmVnJyl7CiAgICAgICAgcGF0aCA9ICcvc3lzdGVtL3VzZXIvZm9ybSc7CiAgICAgIH0KCiAgICAgIGxldCBmaW5pc2hlZCA9IHRydWU7CiAgICAgIGxldCBmb3JtRWRpdCA9IGZhbHNlOwogICAgICBpZihyb3cuZmluaXNoVGltZSA9PSBudWxsICYmIHJvdy5hc3NpZ25lZUlkID09IHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIudXNlcklkKXsKICAgICAgICBmaW5pc2hlZCA9IGZhbHNlOwogICAgICAgIGZvcm1FZGl0ID0gdHJ1ZTsKICAgICAgfQogICAgICB0aGlzLiRyb3V0ZXIucHVzaCh7IHBhdGg6IHBhdGgsCiAgICAgICAgcXVlcnk6IHsKICAgICAgICAgIGJ1c2luZXNzS2V5OiByb3cuYnVzaW5lc3NLZXksCiAgICAgICAgICBwcm9jSW5zSWQ6IHJvdy5wcm9jSW5zSWQsCiAgICAgICAgICB0YXNrSWQ6IHJvdy50YXNrSWQsCiAgICAgICAgICBmaW5pc2hlZDogZmluaXNoZWQsCiAgICAgICAgICBmb3JtRWRpdDogZm9ybUVkaXQKICAgICAgfX0pCiAgICB9LAogICAgLyoqIOS/ruaUueaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlVXBkYXRlKHJvdykgewogICAgICB0aGlzLnJlc2V0KCk7CiAgICAgIGNvbnN0IGlkID0gcm93LmlkIHx8IHRoaXMuaWRzCiAgICAgIGdldERlcGxveW1lbnQoaWQpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMuZm9ybSA9IHJlc3BvbnNlLmRhdGE7CiAgICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsKICAgICAgICB0aGlzLnRpdGxlID0gIuS/ruaUuea1geeoi+WumuS5iSI7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDmj5DkuqTmjInpkq4gKi8KICAgIHN1Ym1pdEZvcm0oKSB7CiAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICBpZiAodGhpcy5mb3JtLmlkICE9IG51bGwpIHsKICAgICAgICAgICAgdXBkYXRlRGVwbG95bWVudCh0aGlzLmZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgICAgICAgIHRoaXMubXNnU3VjY2Vzcygi5L+u5pS55oiQ5YqfIik7CiAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7CiAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICAgICAgICAgIH0pOwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgYWRkRGVwbG95bWVudCh0aGlzLmZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgICAgICAgIHRoaXMubXNnU3VjY2Vzcygi5paw5aKe5oiQ5YqfIik7CiAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7CiAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICAgICAgICAgIH0pOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLyoqIOWIoOmZpOaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlRGVsZXRlKHJvdykgewogICAgICBjb25zdCBpZHMgPSByb3cuaWQgfHwgdGhpcy5pZHM7CiAgICAgIHRoaXMuJGNvbmZpcm0oJ+aYr+WQpuehruiupOWIoOmZpOa1geeoi+WumuS5iee8luWPt+S4uiInICsgaWRzICsgJyLnmoTmlbDmja7pobk/JywgIuitpuWRiiIsIHsKICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogIuehruWumiIsCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogIuWPlua2iCIsCiAgICAgICAgdHlwZTogIndhcm5pbmciCiAgICAgIH0pLnRoZW4oZnVuY3Rpb24oKSB7CiAgICAgICAgcmV0dXJuIGRlbERlcGxveW1lbnQoaWRzKTsKICAgICAgfSkudGhlbigoKSA9PiB7CiAgICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICAgICAgdGhpcy5tc2dTdWNjZXNzKCLliKDpmaTmiJDlip8iKTsKICAgICAgfSkKICAgIH0sCiAgICAvKiog5a+85Ye65oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVFeHBvcnQoKSB7CiAgICAgIGNvbnN0IHF1ZXJ5UGFyYW1zID0gdGhpcy5xdWVyeVBhcmFtczsKICAgICAgdGhpcy4kY29uZmlybSgn5piv5ZCm56Gu6K6k5a+85Ye65omA5pyJ5rWB56iL5a6a5LmJ5pWw5o2u6aG5PycsICLorablkYoiLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLAogICAgICAgIHR5cGU6ICJ3YXJuaW5nIgogICAgICB9KS50aGVuKGZ1bmN0aW9uKCkgewogICAgICAgIHJldHVybiBleHBvcnREZXBsb3ltZW50KHF1ZXJ5UGFyYW1zKTsKICAgICAgfSkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5kb3dubG9hZChyZXNwb25zZS5tc2cpOwogICAgICB9KQogICAgfQogIH0KfTsK"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuJA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/flowable/task/process", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"任务名称\" prop=\"name\">\n        <el-input\n          v-model=\"queryParams.name\"\n          placeholder=\"请输入名称\"\n          clearable\n          size=\"small\"\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"开始时间\" prop=\"deployTime\">\n        <el-date-picker clearable size=\"small\"\n                        v-model=\"queryParams.deployTime\"\n                        type=\"date\"\n                        value-format=\"yyyy-MM-dd\"\n                        placeholder=\"选择时间\">\n        </el-date-picker>\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <!-- <el-col :span=\"1.5\">\n        <el-button\n          type=\"primary\"\n          plain\n          icon=\"el-icon-plus\"\n          size=\"mini\"\n          @click=\"handleAdd\"\n          v-hasPermi=\"['system:deployment:add']\"\n        >新增流程</el-button>\n      </el-col> -->\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"danger\"\n          plain\n          icon=\"el-icon-delete\"\n          size=\"mini\"\n          :disabled=\"multiple\"\n          @click=\"handleDelete\"\n          v-hasPermi=\"['system:deployment:remove']\"\n        >删除</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"warning\"\n          plain\n          icon=\"el-icon-download\"\n          size=\"mini\"\n          @click=\"handleExport\"\n          v-hasPermi=\"['system:deployment:export']\"\n        >导出</el-button>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <el-table v-loading=\"loading\" :data=\"myProcessList\" border @selection-change=\"handleSelectionChange\">\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column label=\"流程编号\" align=\"center\" prop=\"procInsId\" :show-overflow-tooltip=\"true\"/>\n      <el-table-column label=\"流程名称\" align=\"center\" prop=\"procDefName\" :show-overflow-tooltip=\"true\"/>\n      <el-table-column label=\"任务名称\" align=\"center\" prop=\"name\" :show-overflow-tooltip=\"true\"/>\n      <!-- <el-table-column label=\"流程类别\" align=\"center\" prop=\"category\" width=\"100px\" /> -->\n      <el-table-column label=\"流程版本\" align=\"center\" width=\"80px\">\n        <template slot-scope=\"scope\">\n          <el-tag size=\"medium\" >v{{ scope.row.procDefVersion }}</el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"提交时间\" align=\"center\" prop=\"createTime\" width=\"180\"/>\n      <el-table-column label=\"流程状态\" align=\"center\" width=\"100\">\n        <template slot-scope=\"scope\">\n          <el-tag v-if=\"scope.row.finishTime == null\" size=\"mini\">进行中</el-tag>\n          <el-tag type=\"success\" v-if=\"scope.row.finishTime != null\" size=\"mini\">已完成</el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"耗时\" align=\"center\" prop=\"duration\" width=\"180\"/>\n      <el-table-column label=\"当前节点\" align=\"center\" prop=\"taskName\"/>\n      <el-table-column label=\"办理\" align=\"center\">\n        <template slot-scope=\"scope\">\n          <label v-if=\"scope.row.assigneeName\">{{scope.row.assigneeName}} <el-tag type=\"info\" size=\"mini\">{{scope.row.deptName}}</el-tag></label>\n          <label v-if=\"scope.row.candidate\">{{scope.row.candidate}}</label>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"scope\">\n          <el-dropdown>\n            <span class=\"el-dropdown-link\">\n              更多操作<i class=\"el-icon-arrow-down el-icon--right\"></i>\n            </span>\n            <el-dropdown-menu slot=\"dropdown\">\n              <el-dropdown-item icon=\"el-icon-tickets\" @click.native=\"handleFlowRecord(scope.row)\">\n                详情\n              </el-dropdown-item>\n              <el-dropdown-item icon=\"el-icon-circle-close\" @click.native=\"handleStop(scope.row)\">\n                取消申请\n              </el-dropdown-item>\n              <el-dropdown-item icon=\"el-icon-delete\" @click.native=\"handleDelete(scope.row)\" v-hasPermi=\"['system:deployment:remove']\">\n                删除\n              </el-dropdown-item>\n            </el-dropdown-menu>\n          </el-dropdown>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 发起流程 -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"60%\" append-to-body>\n      <el-table v-loading=\"processLoading\" fit :data=\"definitionList\" border >\n        <el-table-column label=\"流程名称\" align=\"center\" prop=\"name\" />\n        <el-table-column label=\"流程版本\" align=\"center\">\n          <template slot-scope=\"scope\">\n            <el-tag size=\"medium\" >v{{ scope.row.version }}</el-tag>\n          </template>\n        </el-table-column>\n        <!-- <el-table-column label=\"流程分类\" align=\"center\" prop=\"category\" /> -->\n        <el-table-column label=\"操作\" align=\"center\" width=\"300\" class-name=\"small-padding fixed-width\">\n          <template slot-scope=\"scope\">\n            <el-button\n              size=\"mini\"\n              type=\"text\"\n              icon=\"el-icon-edit-outline\"\n              @click=\"handleStartProcess(scope.row)\"\n            >发起流程</el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n      <pagination\n        v-show=\"processTotal>0\"\n        :total=\"processTotal\"\n        :page.sync=\"queryParams.pageNum\"\n        :limit.sync=\"queryParams.pageSize\"\n        @pagination=\"listDefinition\"\n      />\n    </el-dialog>\n\n  </div>\n</template>\n\n<script>\nimport {\n  getDeployment,\n  delDeployment,\n  addDeployment,\n  updateDeployment,\n  exportDeployment,\n  flowRecord\n} from \"@/api/flowable/finished\";\nimport { myProcessList,stopProcess } from \"@/api/flowable/process\";\nimport {listDefinition} from \"@/api/flowable/definition\";\nexport default {\n  name: \"Deploy\",\n  components: {\n  },\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      processLoading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      processTotal:0,\n      // 我发起的流程列表数据\n      myProcessList: [],\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      open: false,\n      src: \"\",\n      definitionList:[],\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        name: null,\n        category: null,\n        key: null,\n        tenantId: null,\n        deployTime: null,\n        derivedFrom: null,\n        derivedFromRoot: null,\n        parentDeploymentId: null,\n        engineVersion: null\n      },\n      // 表单参数\n      form: {},\n      // 表单校验\n      rules: {\n      },\n    };\n  },\n  created() {\n    this.getList();\n  },\n  methods: {\n    /** 查询流程定义列表 */\n    getList() {\n      this.loading = true;\n      myProcessList(this.queryParams).then(response => {\n        this.myProcessList = response.data.records;\n        this.total = response.data.total;\n        this.loading = false;\n      });\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        id: null,\n        name: null,\n        category: null,\n        key: null,\n        tenantId: null,\n        deployTime: null,\n        derivedFrom: null,\n        derivedFromRoot: null,\n        parentDeploymentId: null,\n        engineVersion: null\n      };\n      this.resetForm(\"form\");\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.id)\n      this.single = selection.length!==1\n      this.multiple = !selection.length\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.open = true;\n      this.title = \"发起流程\";\n      this.listDefinition();\n    },\n    listDefinition(){\n      listDefinition(this.queryParams).then(response => {\n        this.definitionList = response.data.records;\n        this.processTotal = response.data.total;\n        this.processLoading = false;\n      });\n    },\n    /**  发起流程申请 */\n    handleStartProcess(row){\n      // this.$router.push({ path: '/flowable/task/record/index',\n      //   query: {\n      //     deployId: row.deploymentId,\n      //     procDefId:row.id,\n      //     finished: true\n      //     }\n      // })\n      var path;\n      if(row.key == 'process_project_report'){\n        path = '/project/report/form';\n      }else if(row.key == 'process_user_reg'){\n        path = '/system/user/form';\n      }\n\n      this.$router.push({ path: path})\n    },\n    /**  取消流程申请 */\n    handleStop(row){\n      const params = {\n        instanceId: row.procInsId\n      }\n      stopProcess(params).then( res => {\n        this.msgSuccess(res.msg);\n        this.getList();\n      });\n    },\n    /** 流程流转记录 */\n    handleFlowRecord(row){\n      // this.$router.push({ path: '/flowable/task/record/index',\n      //   query: {\n      //     procInsId: row.procInsId,\n      //     deployId: row.deployId,\n      //     taskId: row.taskId,\n      //     finished: false\n      // }})\n\n      var path;\n      //console.log(row.procDefKey)\n      if(row.procDefKey == 'process_project_report'){\n        path = '/project/report/form';\n      }else if(row.procDefKey == 'process_user_reg'){\n        path = '/system/user/form';\n      }\n\n      let finished = true;\n      let formEdit = false;\n      if(row.finishTime == null && row.assigneeId == this.$store.state.user.userId){\n        finished = false;\n        formEdit = true;\n      }\n      this.$router.push({ path: path,\n        query: {\n          businessKey: row.businessKey,\n          procInsId: row.procInsId,\n          taskId: row.taskId,\n          finished: finished,\n          formEdit: formEdit\n      }})\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset();\n      const id = row.id || this.ids\n      getDeployment(id).then(response => {\n        this.form = response.data;\n        this.open = true;\n        this.title = \"修改流程定义\";\n      });\n    },\n    /** 提交按钮 */\n    submitForm() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          if (this.form.id != null) {\n            updateDeployment(this.form).then(response => {\n              this.msgSuccess(\"修改成功\");\n              this.open = false;\n              this.getList();\n            });\n          } else {\n            addDeployment(this.form).then(response => {\n              this.msgSuccess(\"新增成功\");\n              this.open = false;\n              this.getList();\n            });\n          }\n        }\n      });\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const ids = row.id || this.ids;\n      this.$confirm('是否确认删除流程定义编号为\"' + ids + '\"的数据项?', \"警告\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      }).then(function() {\n        return delDeployment(ids);\n      }).then(() => {\n        this.getList();\n        this.msgSuccess(\"删除成功\");\n      })\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      const queryParams = this.queryParams;\n      this.$confirm('是否确认导出所有流程定义数据项?', \"警告\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      }).then(function() {\n        return exportDeployment(queryParams);\n      }).then(response => {\n        this.download(response.msg);\n      })\n    }\n  }\n};\n</script>\n\n"]}]}