{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/layout/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/layout/index.vue", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBSaWdodFBhbmVsIGZyb20gJ0AvY29tcG9uZW50cy9SaWdodFBhbmVsJwppbXBvcnQgeyBBcHBNYWluLCBOYXZiYXIsIFNldHRpbmdzLCBTaWRlYmFyLCBUYWdzVmlldyB9IGZyb20gJy4vY29tcG9uZW50cycKaW1wb3J0IFJlc2l6ZU1peGluIGZyb20gJy4vbWl4aW4vUmVzaXplSGFuZGxlcicKaW1wb3J0IHsgbWFwU3RhdGUgfSBmcm9tICd2dWV4JwppbXBvcnQgdmFyaWFibGVzIGZyb20gJ0AvYXNzZXRzL3N0eWxlcy92YXJpYWJsZXMuc2NzcycKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnTGF5b3V0JywKICBjb21wb25lbnRzOiB7CiAgICBBcHBNYWluLAogICAgTmF2YmFyLAogICAgUmlnaHRQYW5lbCwKICAgIFNldHRpbmdzLAogICAgU2lkZWJhciwKICAgIFRhZ3NWaWV3CiAgfSwKICBtaXhpbnM6IFtSZXNpemVNaXhpbl0sCiAgY29tcHV0ZWQ6IHsKICAgIC4uLm1hcFN0YXRlKHsKICAgICAgdGhlbWU6IHN0YXRlID0+IHN0YXRlLnNldHRpbmdzLnRoZW1lLAogICAgICBzaWRlVGhlbWU6IHN0YXRlID0+IHN0YXRlLnNldHRpbmdzLnNpZGVUaGVtZSwKICAgICAgc2lkZWJhcjogc3RhdGUgPT4gc3RhdGUuYXBwLnNpZGViYXIsCiAgICAgIGRldmljZTogc3RhdGUgPT4gc3RhdGUuYXBwLmRldmljZSwKICAgICAgc2hvd1NldHRpbmdzOiBzdGF0ZSA9PiBzdGF0ZS5zZXR0aW5ncy5zaG93U2V0dGluZ3MsCiAgICAgIG5lZWRUYWdzVmlldzogc3RhdGUgPT4gc3RhdGUuc2V0dGluZ3MudGFnc1ZpZXcsCiAgICAgIGZpeGVkSGVhZGVyOiBzdGF0ZSA9PiBzdGF0ZS5zZXR0aW5ncy5maXhlZEhlYWRlcgogICAgfSksCiAgICBjbGFzc09iaigpIHsKICAgICAgcmV0dXJuIHsKICAgICAgICBoaWRlU2lkZWJhcjogIXRoaXMuc2lkZWJhci5vcGVuZWQsCiAgICAgICAgb3BlblNpZGViYXI6IHRoaXMuc2lkZWJhci5vcGVuZWQsCiAgICAgICAgd2l0aG91dEFuaW1hdGlvbjogdGhpcy5zaWRlYmFyLndpdGhvdXRBbmltYXRpb24sCiAgICAgICAgbW9iaWxlOiB0aGlzLmRldmljZSA9PT0gJ21vYmlsZScKICAgICAgfQogICAgfSwKICAgIHZhcmlhYmxlcygpIHsKICAgICAgcmV0dXJuIHZhcmlhYmxlczsKICAgIH0KICB9LAogIG1ldGhvZHM6IHsKICAgIGhhbmRsZUNsaWNrT3V0c2lkZSgpIHsKICAgICAgdGhpcy4kc3RvcmUuZGlzcGF0Y2goJ2FwcC9jbG9zZVNpZGVCYXInLCB7IHdpdGhvdXRBbmltYXRpb246IGZhbHNlIH0pCiAgICB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAkBA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/layout", "sourcesContent": ["<template>\n  <div :class=\"classObj\" class=\"app-wrapper\" :style=\"{'--current-color': theme}\">\n    <div v-if=\"device==='mobile'&&sidebar.opened\" class=\"drawer-bg\" @click=\"handleClickOutside\"/>\n    <sidebar class=\"sidebar-container\" :style=\"{ backgroundColor: sideTheme === 'theme-dark' ? variables.menuBg : variables.menuLightBg }\" />\n    <div :class=\"{hasTagsView:needTagsView}\" class=\"main-container\">\n      <div :class=\"{'fixed-header':fixedHeader}\">\n        <navbar />\n        <tags-view v-if=\"needTagsView\" />\n      </div>\n      <app-main />\n      <right-panel v-if=\"showSettings\">\n        <settings />\n      </right-panel>\n    </div>\n  </div>\n</template>\n\n<script>\nimport RightPanel from '@/components/RightPanel'\nimport { AppMain, Navbar, Settings, Sidebar, TagsView } from './components'\nimport ResizeMixin from './mixin/ResizeHandler'\nimport { mapState } from 'vuex'\nimport variables from '@/assets/styles/variables.scss'\n\nexport default {\n  name: 'Layout',\n  components: {\n    AppMain,\n    Navbar,\n    RightPanel,\n    Settings,\n    Sidebar,\n    TagsView\n  },\n  mixins: [ResizeMixin],\n  computed: {\n    ...mapState({\n      theme: state => state.settings.theme,\n      sideTheme: state => state.settings.sideTheme,\n      sidebar: state => state.app.sidebar,\n      device: state => state.app.device,\n      showSettings: state => state.settings.showSettings,\n      needTagsView: state => state.settings.tagsView,\n      fixedHeader: state => state.settings.fixedHeader\n    }),\n    classObj() {\n      return {\n        hideSidebar: !this.sidebar.opened,\n        openSidebar: this.sidebar.opened,\n        withoutAnimation: this.sidebar.withoutAnimation,\n        mobile: this.device === 'mobile'\n      }\n    },\n    variables() {\n      return variables;\n    }\n  },\n  methods: {\n    handleClickOutside() {\n      this.$store.dispatch('app/closeSideBar', { withoutAnimation: false })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n  @import \"~@/assets/styles/mixin.scss\";\n  @import \"~@/assets/styles/variables.scss\";\n\n  .app-wrapper {\n    @include clearfix;\n    position: relative;\n    height: 100%;\n    width: 100%;\n\n    &.mobile.openSidebar {\n      position: fixed;\n      top: 0;\n    }\n  }\n\n  .drawer-bg {\n    background: #000;\n    opacity: 0.3;\n    width: 100%;\n    top: 0;\n    height: 100%;\n    position: absolute;\n    z-index: 999;\n  }\n\n  .fixed-header {\n    position: fixed;\n    top: 0;\n    right: 0;\n    z-index: 9;\n    width: calc(100% - #{$sideBarWidth});\n    transition: width 0.28s;\n  }\n\n  .hideSidebar .fixed-header {\n    width: calc(100% - 54px)\n  }\n\n  .mobile .fixed-header {\n    width: 100%;\n  }\n</style>\n"]}]}