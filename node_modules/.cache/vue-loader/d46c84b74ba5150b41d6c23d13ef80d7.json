{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/layout/components/Sidebar/index.vue?vue&type=template&id=33ec43fc", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/layout/components/Sidebar/index.vue", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/loaders/templateLoader.js", "mtime": 1751171659981}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:dmFyIHJlbmRlciA9IGZ1bmN0aW9uICgpIHsKICB2YXIgX3ZtID0gdGhpcwogIHZhciBfaCA9IF92bS4kY3JlYXRlRWxlbWVudAogIHZhciBfYyA9IF92bS5fc2VsZi5fYyB8fCBfaAogIHJldHVybiBfYygKICAgICJkaXYiLAogICAgewogICAgICBjbGFzczogeyAiaGFzLWxvZ28iOiBfdm0uc2hvd0xvZ28gfSwKICAgICAgc3R5bGU6IHsKICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6CiAgICAgICAgICBfdm0uc2V0dGluZ3Muc2lkZVRoZW1lID09PSAidGhlbWUtZGFyayIKICAgICAgICAgICAgPyBfdm0udmFyaWFibGVzLm1lbnVCZwogICAgICAgICAgICA6IF92bS52YXJpYWJsZXMubWVudUxpZ2h0QmcsCiAgICAgIH0sCiAgICB9LAogICAgWwogICAgICBfdm0uc2hvd0xvZ28KICAgICAgICA/IF9jKCJsb2dvIiwgeyBhdHRyczogeyBjb2xsYXBzZTogX3ZtLmlzQ29sbGFwc2UgfSB9KQogICAgICAgIDogX3ZtLl9lKCksCiAgICAgIF9jKAogICAgICAgICJlbC1zY3JvbGxiYXIiLAogICAgICAgIHsKICAgICAgICAgIGNsYXNzOiBfdm0uc2V0dGluZ3Muc2lkZVRoZW1lLAogICAgICAgICAgYXR0cnM6IHsgIndyYXAtY2xhc3MiOiAic2Nyb2xsYmFyLXdyYXBwZXIiIH0sCiAgICAgICAgfSwKICAgICAgICBbCiAgICAgICAgICBfYygKICAgICAgICAgICAgImVsLW1lbnUiLAogICAgICAgICAgICB7CiAgICAgICAgICAgICAgYXR0cnM6IHsKICAgICAgICAgICAgICAgICJkZWZhdWx0LWFjdGl2ZSI6IF92bS5hY3RpdmVNZW51LAogICAgICAgICAgICAgICAgY29sbGFwc2U6IF92bS5pc0NvbGxhcHNlLAogICAgICAgICAgICAgICAgImJhY2tncm91bmQtY29sb3IiOgogICAgICAgICAgICAgICAgICBfdm0uc2V0dGluZ3Muc2lkZVRoZW1lID09PSAidGhlbWUtZGFyayIKICAgICAgICAgICAgICAgICAgICA/IF92bS52YXJpYWJsZXMubWVudUJnCiAgICAgICAgICAgICAgICAgICAgOiBfdm0udmFyaWFibGVzLm1lbnVMaWdodEJnLAogICAgICAgICAgICAgICAgInRleHQtY29sb3IiOgogICAgICAgICAgICAgICAgICBfdm0uc2V0dGluZ3Muc2lkZVRoZW1lID09PSAidGhlbWUtZGFyayIKICAgICAgICAgICAgICAgICAgICA/IF92bS52YXJpYWJsZXMubWVudVRleHQKICAgICAgICAgICAgICAgICAgICA6ICJyZ2JhKDAsMCwwLC42NSkiLAogICAgICAgICAgICAgICAgInVuaXF1ZS1vcGVuZWQiOiB0cnVlLAogICAgICAgICAgICAgICAgImFjdGl2ZS10ZXh0LWNvbG9yIjogX3ZtLnNldHRpbmdzLnRoZW1lLAogICAgICAgICAgICAgICAgImNvbGxhcHNlLXRyYW5zaXRpb24iOiBmYWxzZSwKICAgICAgICAgICAgICAgIG1vZGU6ICJ2ZXJ0aWNhbCIsCiAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgfSwKICAgICAgICAgICAgX3ZtLl9sKF92bS5zaWRlYmFyUm91dGVycywgZnVuY3Rpb24gKHJvdXRlLCBpbmRleCkgewogICAgICAgICAgICAgIHJldHVybiBfYygic2lkZWJhci1pdGVtIiwgewogICAgICAgICAgICAgICAga2V5OiByb3V0ZS5wYXRoICsgaW5kZXgsCiAgICAgICAgICAgICAgICBhdHRyczogeyBpdGVtOiByb3V0ZSwgImJhc2UtcGF0aCI6IHJvdXRlLnBhdGggfSwKICAgICAgICAgICAgICB9KQogICAgICAgICAgICB9KSwKICAgICAgICAgICAgMQogICAgICAgICAgKSwKICAgICAgICBdLAogICAgICAgIDEKICAgICAgKSwKICAgIF0sCiAgICAxCiAgKQp9CnZhciBzdGF0aWNSZW5kZXJGbnMgPSBbXQpyZW5kZXIuX3dpdGhTdHJpcHBlZCA9IHRydWUKCmV4cG9ydCB7IHJlbmRlciwgc3RhdGljUmVuZGVyRm5zIH0="}]}