{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/system/role/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/system/role/index.vue", "mtime": 1662389786000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGxpc3RSb2xlLCBnZXRSb2xlLCBkZWxSb2xlLCBhZGRSb2xlLCB1cGRhdGVSb2xlLCBleHBvcnRSb2xlLCBkYXRhU2NvcGUsIGNoYW5nZVJvbGVTdGF0dXMgfSBmcm9tICJAL2FwaS9zeXN0ZW0vcm9sZSI7CmltcG9ydCB7IHRyZWVzZWxlY3QgYXMgbWVudVRyZWVzZWxlY3QsIHJvbGVNZW51VHJlZXNlbGVjdCB9IGZyb20gIkAvYXBpL3N5c3RlbS9tZW51IjsKaW1wb3J0IHsgdHJlZXNlbGVjdCBhcyBkZXB0VHJlZXNlbGVjdCwgcm9sZURlcHRUcmVlc2VsZWN0IH0gZnJvbSAiQC9hcGkvc3lzdGVtL2RlcHQiOwppbXBvcnQgeyBwcm92aW5jZUFuZENpdHlEYXRhIH0gZnJvbSAiZWxlbWVudC1jaGluYS1hcmVhLWRhdGEiOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogIlJvbGUiLAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICAvLyDpga7nvanlsYIKICAgICAgbG9hZGluZzogdHJ1ZSwKICAgICAgLy8g6YCJ5Lit5pWw57uECiAgICAgIGlkczogW10sCiAgICAgIC8vIOmdnuWNleS4quemgeeUqAogICAgICBzaW5nbGU6IHRydWUsCiAgICAgIC8vIOmdnuWkmuS4quemgeeUqAogICAgICBtdWx0aXBsZTogdHJ1ZSwKICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2CiAgICAgIHNob3dTZWFyY2g6IHRydWUsCiAgICAgIC8vIOaAu+adoeaVsAogICAgICB0b3RhbDogMCwKICAgICAgLy8g6KeS6Imy6KGo5qC85pWw5o2uCiAgICAgIHJvbGVMaXN0OiBbXSwKICAgICAgLy8g5by55Ye65bGC5qCH6aKYCiAgICAgIHRpdGxlOiAiIiwKICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGCCiAgICAgIG9wZW46IGZhbHNlLAogICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYLvvIjmlbDmja7mnYPpmZDvvIkKICAgICAgb3BlbkRhdGFTY29wZTogZmFsc2UsCiAgICAgIG1lbnVFeHBhbmQ6IGZhbHNlLAogICAgICBtZW51Tm9kZUFsbDogZmFsc2UsCiAgICAgIGRlcHRFeHBhbmQ6IHRydWUsCiAgICAgIGRlcHROb2RlQWxsOiBmYWxzZSwKICAgICAgLy8g5pel5pyf6IyD5Zu0CiAgICAgIGRhdGVSYW5nZTogW10sCiAgICAgIC8vIOeKtuaAgeaVsOaNruWtl+WFuAogICAgICBzdGF0dXNPcHRpb25zOiBbXSwKICAgICAgLy8g5pWw5o2u6IyD5Zu06YCJ6aG5CiAgICAgIGRhdGFTY29wZU9wdGlvbnM6IFsKICAgICAgICB7CiAgICAgICAgICB2YWx1ZTogIjEiLAogICAgICAgICAgbGFiZWw6ICLlhajpg6jmlbDmja7mnYPpmZAiCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICB2YWx1ZTogIjIiLAogICAgICAgICAgbGFiZWw6ICLoh6rlrprmlbDmja7mnYPpmZAiCiAgICAgICAgfSwKICAgICAgICAvLyB7CiAgICAgICAgLy8gICB2YWx1ZTogIjMiLAogICAgICAgIC8vICAgbGFiZWw6ICLmnKzpg6jpl6jmlbDmja7mnYPpmZAiCiAgICAgICAgLy8gfSwKICAgICAgICAvLyB7CiAgICAgICAgLy8gICB2YWx1ZTogIjQiLAogICAgICAgIC8vICAgbGFiZWw6ICLmnKzpg6jpl6jlj4rku6XkuIvmlbDmja7mnYPpmZAiCiAgICAgICAgLy8gfSwKICAgICAgICB7CiAgICAgICAgICB2YWx1ZTogIjUiLAogICAgICAgICAgbGFiZWw6ICLku4XmnKzkurrmlbDmja7mnYPpmZAiCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICB2YWx1ZTogIjYiLAogICAgICAgICAgbGFiZWw6ICLoh6rlrprkuYnljLrln5/mlbDmja7mnYPpmZAiCiAgICAgICAgfQogICAgICBdLAogICAgICAvLyDoj5zljZXliJfooagKICAgICAgbWVudU9wdGlvbnM6IFtdLAogICAgICAvLyDpg6jpl6jliJfooagKICAgICAgZGVwdE9wdGlvbnM6IFtdLAogICAgICAvLyDmn6Xor6Llj4LmlbAKICAgICAgcXVlcnlQYXJhbXM6IHsKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICByb2xlTmFtZTogdW5kZWZpbmVkLAogICAgICAgIHJvbGVLZXk6IHVuZGVmaW5lZCwKICAgICAgICBzdGF0dXM6IHVuZGVmaW5lZAogICAgICB9LAogICAgICAvLyDooajljZXlj4LmlbAKICAgICAgZm9ybToge30sCiAgICAgIGRlZmF1bHRQcm9wczogewogICAgICAgIGNoaWxkcmVuOiAiY2hpbGRyZW4iLAogICAgICAgIGxhYmVsOiAibGFiZWwiCiAgICAgIH0sCiAgICAgIC8vIOihqOWNleagoemqjAogICAgICBydWxlczogewogICAgICAgIHJvbGVOYW1lOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6KeS6Imy5ZCN56ew5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0KICAgICAgICBdLAogICAgICAgIHJvbGVLZXk6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLmnYPpmZDlrZfnrKbkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfQogICAgICAgIF0sCiAgICAgICAgcm9sZVNvcnQ6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLop5LoibLpobrluo/kuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfQogICAgICAgIF0KICAgICAgfSwKICAgICAgcHJvdmluY2U6IHByb3ZpbmNlQW5kQ2l0eURhdGEKICAgIH07CiAgfSwKICBjcmVhdGVkKCkgewogICAgdGhpcy5nZXRMaXN0KCk7CiAgICB0aGlzLmdldERpY3RzKCJzeXNfbm9ybWFsX2Rpc2FibGUiKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgdGhpcy5zdGF0dXNPcHRpb25zID0gcmVzcG9uc2UuZGF0YTsKICAgIH0pOwogIH0sCiAgbWV0aG9kczogewogICAgLyoqIOafpeivouinkuiJsuWIl+ihqCAqLwogICAgZ2V0TGlzdCgpIHsKICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsKICAgICAgbGlzdFJvbGUodGhpcy5hZGREYXRlUmFuZ2UodGhpcy5xdWVyeVBhcmFtcywgdGhpcy5kYXRlUmFuZ2UpKS50aGVuKAogICAgICAgIHJlc3BvbnNlID0+IHsKICAgICAgICAgIHRoaXMucm9sZUxpc3QgPSByZXNwb25zZS5yb3dzOwogICAgICAgICAgdGhpcy50b3RhbCA9IHJlc3BvbnNlLnRvdGFsOwogICAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICAgICAgfQogICAgICApOwogICAgfSwKICAgIC8qKiDmn6Xor6Loj5zljZXmoJHnu5PmnoQgKi8KICAgIGdldE1lbnVUcmVlc2VsZWN0KCkgewogICAgICBtZW51VHJlZXNlbGVjdCgpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMubWVudU9wdGlvbnMgPSByZXNwb25zZS5kYXRhOwogICAgICB9KTsKICAgIH0sCiAgICAvKiog5p+l6K+i6YOo6Zeo5qCR57uT5p6EICovCiAgICBnZXREZXB0VHJlZXNlbGVjdCgpIHsKICAgICAgZGVwdFRyZWVzZWxlY3QoKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLmRlcHRPcHRpb25zID0gcmVzcG9uc2UuZGF0YTsKICAgICAgfSk7CiAgICB9LAogICAgLy8g5omA5pyJ6I+c5Y2V6IqC54K55pWw5o2uCiAgICBnZXRNZW51QWxsQ2hlY2tlZEtleXMoKSB7CiAgICAgIC8vIOebruWJjeiiq+mAieS4reeahOiPnOWNleiKgueCuQogICAgICBsZXQgY2hlY2tlZEtleXMgPSB0aGlzLiRyZWZzLm1lbnUuZ2V0Q2hlY2tlZEtleXMoKTsKICAgICAgLy8g5Y2K6YCJ5Lit55qE6I+c5Y2V6IqC54K5CiAgICAgIGxldCBoYWxmQ2hlY2tlZEtleXMgPSB0aGlzLiRyZWZzLm1lbnUuZ2V0SGFsZkNoZWNrZWRLZXlzKCk7CiAgICAgIGNoZWNrZWRLZXlzLnVuc2hpZnQuYXBwbHkoY2hlY2tlZEtleXMsIGhhbGZDaGVja2VkS2V5cyk7CiAgICAgIHJldHVybiBjaGVja2VkS2V5czsKICAgIH0sCiAgICAvLyDmiYDmnInpg6jpl6joioLngrnmlbDmja4KICAgIGdldERlcHRBbGxDaGVja2VkS2V5cygpIHsKICAgICAgLy8g55uu5YmN6KKr6YCJ5Lit55qE6YOo6Zeo6IqC54K5CiAgICAgIGxldCBjaGVja2VkS2V5cyA9IHRoaXMuJHJlZnMuZGVwdC5nZXRDaGVja2VkS2V5cygpOwogICAgICAvLyDljYrpgInkuK3nmoTpg6jpl6joioLngrkKICAgICAgbGV0IGhhbGZDaGVja2VkS2V5cyA9IHRoaXMuJHJlZnMuZGVwdC5nZXRIYWxmQ2hlY2tlZEtleXMoKTsKICAgICAgY2hlY2tlZEtleXMudW5zaGlmdC5hcHBseShjaGVja2VkS2V5cywgaGFsZkNoZWNrZWRLZXlzKTsKICAgICAgcmV0dXJuIGNoZWNrZWRLZXlzOwogICAgfSwKICAgIC8qKiDmoLnmja7op5LoibJJROafpeivouiPnOWNleagkee7k+aehCAqLwogICAgZ2V0Um9sZU1lbnVUcmVlc2VsZWN0KHJvbGVJZCkgewogICAgICByZXR1cm4gcm9sZU1lbnVUcmVlc2VsZWN0KHJvbGVJZCkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5tZW51T3B0aW9ucyA9IHJlc3BvbnNlLm1lbnVzOwogICAgICAgIHJldHVybiByZXNwb25zZTsKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOagueaNruinkuiJsklE5p+l6K+i6YOo6Zeo5qCR57uT5p6EICovCiAgICBnZXRSb2xlRGVwdFRyZWVzZWxlY3Qocm9sZUlkKSB7CiAgICAgIHJldHVybiByb2xlRGVwdFRyZWVzZWxlY3Qocm9sZUlkKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLmRlcHRPcHRpb25zID0gcmVzcG9uc2UuZGVwdHM7CiAgICAgICAgcmV0dXJuIHJlc3BvbnNlOwogICAgICB9KTsKICAgIH0sCiAgICAvLyDop5LoibLnirbmgIHkv67mlLkKICAgIGhhbmRsZVN0YXR1c0NoYW5nZShyb3cpIHsKICAgICAgbGV0IHRleHQgPSByb3cuc3RhdHVzID09PSAiMCIgPyAi5ZCv55SoIiA6ICLlgZznlKgiOwogICAgICB0aGlzLiRjb25maXJtKCfnoa7orqTopoEiJyArIHRleHQgKyAnIiInICsgcm93LnJvbGVOYW1lICsgJyLop5LoibLlkJc/JywgIuitpuWRiiIsIHsKICAgICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAi56Gu5a6aIiwKICAgICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLAogICAgICAgICAgdHlwZTogIndhcm5pbmciCiAgICAgICAgfSkudGhlbihmdW5jdGlvbigpIHsKICAgICAgICAgIHJldHVybiBjaGFuZ2VSb2xlU3RhdHVzKHJvdy5yb2xlSWQsIHJvdy5zdGF0dXMpOwogICAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgICAgdGhpcy5tc2dTdWNjZXNzKHRleHQgKyAi5oiQ5YqfIik7CiAgICAgICAgfSkuY2F0Y2goZnVuY3Rpb24oKSB7CiAgICAgICAgICByb3cuc3RhdHVzID0gcm93LnN0YXR1cyA9PT0gIjAiID8gIjEiIDogIjAiOwogICAgICAgIH0pOwogICAgfSwKICAgIC8vIOWPlua2iOaMiemSrgogICAgY2FuY2VsKCkgewogICAgICB0aGlzLm9wZW4gPSBmYWxzZTsKICAgICAgdGhpcy5yZXNldCgpOwogICAgfSwKICAgIC8vIOWPlua2iOaMiemSru+8iOaVsOaNruadg+mZkO+8iQogICAgY2FuY2VsRGF0YVNjb3BlKCkgewogICAgICB0aGlzLm9wZW5EYXRhU2NvcGUgPSBmYWxzZTsKICAgICAgdGhpcy5yZXNldCgpOwogICAgfSwKICAgIC8vIOihqOWNlemHjee9rgogICAgcmVzZXQoKSB7CiAgICAgIGlmICh0aGlzLiRyZWZzLm1lbnUgIT0gdW5kZWZpbmVkKSB7CiAgICAgICAgdGhpcy4kcmVmcy5tZW51LnNldENoZWNrZWRLZXlzKFtdKTsKICAgICAgfQogICAgICB0aGlzLm1lbnVFeHBhbmQgPSBmYWxzZSwKICAgICAgdGhpcy5tZW51Tm9kZUFsbCA9IGZhbHNlLAogICAgICB0aGlzLmRlcHRFeHBhbmQgPSB0cnVlLAogICAgICB0aGlzLmRlcHROb2RlQWxsID0gZmFsc2UsCiAgICAgIHRoaXMuZm9ybSA9IHsKICAgICAgICByb2xlSWQ6IHVuZGVmaW5lZCwKICAgICAgICByb2xlTmFtZTogdW5kZWZpbmVkLAogICAgICAgIHJvbGVLZXk6IHVuZGVmaW5lZCwKICAgICAgICByb2xlU29ydDogMCwKICAgICAgICBzdGF0dXM6ICIwIiwKICAgICAgICBtZW51SWRzOiBbXSwKICAgICAgICBkZXB0SWRzOiBbXSwKICAgICAgICBtZW51Q2hlY2tTdHJpY3RseTogdHJ1ZSwKICAgICAgICBkZXB0Q2hlY2tTdHJpY3RseTogdHJ1ZSwKICAgICAgICByZW1hcms6IHVuZGVmaW5lZCwKICAgICAgICBhcmVhOiB1bmRlZmluZWQsCiAgICAgIH07CiAgICAgIHRoaXMucmVzZXRGb3JtKCJmb3JtIik7CiAgICB9LAogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlUXVlcnkoKSB7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDE7CiAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgfSwKICAgIC8qKiDph43nva7mjInpkq7mk43kvZwgKi8KICAgIHJlc2V0UXVlcnkoKSB7CiAgICAgIHRoaXMuZGF0ZVJhbmdlID0gW107CiAgICAgIHRoaXMucmVzZXRGb3JtKCJxdWVyeUZvcm0iKTsKICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOwogICAgfSwKICAgIC8vIOWkmumAieahhumAieS4reaVsOaNrgogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgewogICAgICB0aGlzLmlkcyA9IHNlbGVjdGlvbi5tYXAoaXRlbSA9PiBpdGVtLnJvbGVJZCkKICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoIT0xCiAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aAogICAgfSwKICAgIC8vIOagkeadg+mZkO+8iOWxleW8gC/mipjlj6DvvIkKICAgIGhhbmRsZUNoZWNrZWRUcmVlRXhwYW5kKHZhbHVlLCB0eXBlKSB7CiAgICAgIGlmICh0eXBlID09ICdtZW51JykgewogICAgICAgIGxldCB0cmVlTGlzdCA9IHRoaXMubWVudU9wdGlvbnM7CiAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCB0cmVlTGlzdC5sZW5ndGg7IGkrKykgewogICAgICAgICAgdGhpcy4kcmVmcy5tZW51LnN0b3JlLm5vZGVzTWFwW3RyZWVMaXN0W2ldLmlkXS5leHBhbmRlZCA9IHZhbHVlOwogICAgICAgIH0KICAgICAgfSBlbHNlIGlmICh0eXBlID09ICdkZXB0JykgewogICAgICAgIGxldCB0cmVlTGlzdCA9IHRoaXMuZGVwdE9wdGlvbnM7CiAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCB0cmVlTGlzdC5sZW5ndGg7IGkrKykgewogICAgICAgICAgdGhpcy4kcmVmcy5kZXB0LnN0b3JlLm5vZGVzTWFwW3RyZWVMaXN0W2ldLmlkXS5leHBhbmRlZCA9IHZhbHVlOwogICAgICAgIH0KICAgICAgfQogICAgfSwKICAgIC8vIOagkeadg+mZkO+8iOWFqOmAiS/lhajkuI3pgInvvIkKICAgIGhhbmRsZUNoZWNrZWRUcmVlTm9kZUFsbCh2YWx1ZSwgdHlwZSkgewogICAgICBpZiAodHlwZSA9PSAnbWVudScpIHsKICAgICAgICB0aGlzLiRyZWZzLm1lbnUuc2V0Q2hlY2tlZE5vZGVzKHZhbHVlID8gdGhpcy5tZW51T3B0aW9uczogW10pOwogICAgICB9IGVsc2UgaWYgKHR5cGUgPT0gJ2RlcHQnKSB7CiAgICAgICAgdGhpcy4kcmVmcy5kZXB0LnNldENoZWNrZWROb2Rlcyh2YWx1ZSA/IHRoaXMuZGVwdE9wdGlvbnM6IFtdKTsKICAgICAgfQogICAgfSwKICAgIC8vIOagkeadg+mZkO+8iOeItuWtkOiBlOWKqO+8iQogICAgaGFuZGxlQ2hlY2tlZFRyZWVDb25uZWN0KHZhbHVlLCB0eXBlKSB7CiAgICAgIGlmICh0eXBlID09ICdtZW51JykgewogICAgICAgIHRoaXMuZm9ybS5tZW51Q2hlY2tTdHJpY3RseSA9IHZhbHVlID8gdHJ1ZTogZmFsc2U7CiAgICAgIH0gZWxzZSBpZiAodHlwZSA9PSAnZGVwdCcpIHsKICAgICAgICB0aGlzLmZvcm0uZGVwdENoZWNrU3RyaWN0bHkgPSB2YWx1ZSA/IHRydWU6IGZhbHNlOwogICAgICB9CiAgICB9LAogICAgLyoqIOaWsOWinuaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlQWRkKCkgewogICAgICB0aGlzLnJlc2V0KCk7CiAgICAgIHRoaXMuZ2V0TWVudVRyZWVzZWxlY3QoKTsKICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsKICAgICAgdGhpcy50aXRsZSA9ICLmt7vliqDop5LoibIiOwogICAgfSwKICAgIC8qKiDkv67mlLnmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZVVwZGF0ZShyb3cpIHsKICAgICAgdGhpcy5yZXNldCgpOwogICAgICBjb25zdCByb2xlSWQgPSByb3cucm9sZUlkIHx8IHRoaXMuaWRzCiAgICAgIGNvbnN0IHJvbGVNZW51ID0gdGhpcy5nZXRSb2xlTWVudVRyZWVzZWxlY3Qocm9sZUlkKTsKICAgICAgZ2V0Um9sZShyb2xlSWQpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMuZm9ybSA9IHJlc3BvbnNlLmRhdGE7CiAgICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsKICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgICAgICByb2xlTWVudS50aGVuKHJlcyA9PiB7CiAgICAgICAgICAgIHRoaXMuJHJlZnMubWVudS5zZXRDaGVja2VkS2V5cyhyZXMuY2hlY2tlZEtleXMpOwogICAgICAgICAgfSk7CiAgICAgICAgfSk7CiAgICAgICAgdGhpcy50aXRsZSA9ICLkv67mlLnop5LoibIiOwogICAgICB9KTsKICAgIH0sCiAgICAvKiog5YiG6YWN5pWw5o2u5p2D6ZmQ5pON5L2cICovCiAgICBoYW5kbGVEYXRhU2NvcGUocm93KSB7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgICAgY29uc3Qgcm9sZURlcHRUcmVlc2VsZWN0ID0gdGhpcy5nZXRSb2xlRGVwdFRyZWVzZWxlY3Qocm93LnJvbGVJZCk7CiAgICAgIGdldFJvbGUocm93LnJvbGVJZCkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5mb3JtID0gcmVzcG9uc2UuZGF0YTsKICAgICAgICB0aGlzLm9wZW5EYXRhU2NvcGUgPSB0cnVlOwogICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgICAgIHJvbGVEZXB0VHJlZXNlbGVjdC50aGVuKHJlcyA9PiB7CiAgICAgICAgICAgIHRoaXMuJHJlZnMuZGVwdC5zZXRDaGVja2VkS2V5cyhyZXMuY2hlY2tlZEtleXMpOwogICAgICAgICAgfSk7CiAgICAgICAgfSk7CiAgICAgICAgdGhpcy50aXRsZSA9ICLliIbphY3mlbDmja7mnYPpmZAiOwogICAgICB9KTsKICAgIH0sCiAgICAvKiog5o+Q5Lqk5oyJ6ZKuICovCiAgICBzdWJtaXRGb3JtOiBmdW5jdGlvbigpIHsKICAgICAgCiAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICBpZiAodGhpcy5mb3JtLnJvbGVJZCAhPSB1bmRlZmluZWQpIHsKICAgICAgICAgICAgdGhpcy5mb3JtLm1lbnVJZHMgPSB0aGlzLmdldE1lbnVBbGxDaGVja2VkS2V5cygpOwogICAgICAgICAgICB1cGRhdGVSb2xlKHRoaXMuZm9ybSkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgICAgICAgdGhpcy5tc2dTdWNjZXNzKCLkv67mlLnmiJDlip8iKTsKICAgICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsKICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICB0aGlzLmZvcm0ubWVudUlkcyA9IHRoaXMuZ2V0TWVudUFsbENoZWNrZWRLZXlzKCk7CiAgICAgICAgICAgIGFkZFJvbGUodGhpcy5mb3JtKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICAgICAgICB0aGlzLm1zZ1N1Y2Nlc3MoIuaWsOWinuaIkOWKnyIpOwogICAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlOwogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICAgICAgICB9KTsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDmj5DkuqTmjInpkq7vvIjmlbDmja7mnYPpmZDvvIkgKi8KICAgIHN1Ym1pdERhdGFTY29wZTogZnVuY3Rpb24oKSB7CiAgICAgIC8vY29uc29sZS5sb2codGhpcy5mb3JtKTsKICAgICAgaWYgKHRoaXMuZm9ybS5yb2xlSWQgIT0gdW5kZWZpbmVkKSB7CiAgICAgICAgdGhpcy5mb3JtLmRlcHRJZHMgPSB0aGlzLmdldERlcHRBbGxDaGVja2VkS2V5cygpOwogICAgICAgIGRhdGFTY29wZSh0aGlzLmZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgICAgdGhpcy5tc2dTdWNjZXNzKCLkv67mlLnmiJDlip8iKTsKICAgICAgICAgIHRoaXMub3BlbkRhdGFTY29wZSA9IGZhbHNlOwogICAgICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICAgICAgfSk7CiAgICAgIH0KICAgIH0sCiAgICAvKiog5Yig6Zmk5oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVEZWxldGUocm93KSB7CiAgICAgIGNvbnN0IHJvbGVJZHMgPSByb3cucm9sZUlkIHx8IHRoaXMuaWRzOwogICAgICB0aGlzLiRjb25maXJtKCfmmK/lkKbnoa7orqTliKDpmaTop5LoibLnvJblj7fkuLoiJyArIHJvbGVJZHMgKyAnIueahOaVsOaNrumhuT8nLCAi6K2m5ZGKIiwgewogICAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLAogICAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogIuWPlua2iCIsCiAgICAgICAgICB0eXBlOiAid2FybmluZyIKICAgICAgICB9KS50aGVuKGZ1bmN0aW9uKCkgewogICAgICAgICAgcmV0dXJuIGRlbFJvbGUocm9sZUlkcyk7CiAgICAgICAgfSkudGhlbigoKSA9PiB7CiAgICAgICAgICB0aGlzLmdldExpc3QoKTsKICAgICAgICAgIHRoaXMubXNnU3VjY2Vzcygi5Yig6Zmk5oiQ5YqfIik7CiAgICAgICAgfSkKICAgIH0sCiAgICAvKiog5a+85Ye65oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVFeHBvcnQoKSB7CiAgICAgIGNvbnN0IHF1ZXJ5UGFyYW1zID0gdGhpcy5xdWVyeVBhcmFtczsKICAgICAgdGhpcy4kY29uZmlybSgn5piv5ZCm56Gu6K6k5a+85Ye65omA5pyJ6KeS6Imy5pWw5o2u6aG5PycsICLorablkYoiLCB7CiAgICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogIuehruWumiIsCiAgICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAi5Y+W5raIIiwKICAgICAgICAgIHR5cGU6ICJ3YXJuaW5nIgogICAgICAgIH0pLnRoZW4oZnVuY3Rpb24oKSB7CiAgICAgICAgICByZXR1cm4gZXhwb3J0Um9sZShxdWVyeVBhcmFtcyk7CiAgICAgICAgfSkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgICB0aGlzLmRvd25sb2FkKHJlc3BvbnNlLm1zZyk7CiAgICAgICAgfSkKICAgIH0KICB9Cn07Cg=="}, null]}