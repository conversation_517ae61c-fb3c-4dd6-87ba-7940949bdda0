{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/components/IconSelect/index.vue?vue&type=style&index=0&id=6504d548&rel=stylesheet%2Fscss&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/components/IconSelect/index.vue", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/css-loader/dist/cjs.js", "mtime": 1751171659443}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/loaders/stylePostLoader.js", "mtime": 1751171659924}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/postcss-loader/src/index.js", "mtime": 1751171659554}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/sass-loader/dist/cjs.js", "mtime": 1751171659051}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLmljb24tYm9keSB7CiAgd2lkdGg6IDEwMCU7CiAgcGFkZGluZzogMTBweDsKICAuaWNvbi1saXN0IHsKICAgIGhlaWdodDogMjAwcHg7CiAgICBvdmVyZmxvdy15OiBzY3JvbGw7CiAgICBkaXYgewogICAgICBoZWlnaHQ6IDMwcHg7CiAgICAgIGxpbmUtaGVpZ2h0OiAzMHB4OwogICAgICBtYXJnaW4tYm90dG9tOiAtNXB4OwogICAgICBjdXJzb3I6IHBvaW50ZXI7CiAgICAgIHdpZHRoOiAzMyU7CiAgICAgIGZsb2F0OiBsZWZ0OwogICAgfQogICAgc3BhbiB7CiAgICAgIGRpc3BsYXk6IGlubGluZS1ibG9jazsKICAgICAgdmVydGljYWwtYWxpZ246IC0wLjE1ZW07CiAgICAgIGZpbGw6IGN1cnJlbnRDb2xvcjsKICAgICAgb3ZlcmZsb3c6IGhpZGRlbjsKICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6CA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/IconSelect", "sourcesContent": ["<!-- <AUTHOR> -->\n<template>\n  <div class=\"icon-body\">\n    <el-input v-model=\"name\" style=\"position: relative;\" clearable placeholder=\"请输入图标名称\" @clear=\"filterIcons\" @input.native=\"filterIcons\">\n      <i slot=\"suffix\" class=\"el-icon-search el-input__icon\" />\n    </el-input>\n    <div class=\"icon-list\">\n      <div v-for=\"(item, index) in iconList\" :key=\"index\" @click=\"selectedIcon(item)\">\n        <svg-icon :icon-class=\"item\" style=\"height: 30px;width: 16px;\" />\n        <span>{{ item }}</span>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport icons from './requireIcons'\nexport default {\n  name: 'IconSelect',\n  data() {\n    return {\n      name: '',\n      iconList: icons\n    }\n  },\n  methods: {\n    filterIcons() {\n      this.iconList = icons\n      if (this.name) {\n        this.iconList = this.iconList.filter(item => item.includes(this.name))\n      }\n    },\n    selectedIcon(name) {\n      this.$emit('selected', name)\n      document.body.click()\n    },\n    reset() {\n      this.name = ''\n      this.iconList = icons\n    }\n  }\n}\n</script>\n\n<style rel=\"stylesheet/scss\" lang=\"scss\" scoped>\n  .icon-body {\n    width: 100%;\n    padding: 10px;\n    .icon-list {\n      height: 200px;\n      overflow-y: scroll;\n      div {\n        height: 30px;\n        line-height: 30px;\n        margin-bottom: -5px;\n        cursor: pointer;\n        width: 33%;\n        float: left;\n      }\n      span {\n        display: inline-block;\n        vertical-align: -0.15em;\n        fill: currentColor;\n        overflow: hidden;\n      }\n    }\n  }\n</style>\n"]}]}