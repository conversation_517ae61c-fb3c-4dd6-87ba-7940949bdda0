{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/monitor/cache/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/monitor/cache/index.vue", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/monitor/cache", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-row>\n      <el-col :span=\"24\" class=\"card-box\">\n        <el-card>\n          <div slot=\"header\"><span>基本信息</span></div>\n          <div class=\"el-table el-table--enable-row-hover el-table--medium\">\n            <table cellspacing=\"0\" style=\"width: 100%\">\n              <tbody>\n                <tr>\n                  <td><div class=\"cell\">Redis版本</div></td>\n                  <td><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.redis_version }}</div></td>\n                  <td><div class=\"cell\">运行模式</div></td>\n                  <td><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.redis_mode == \"standalone\" ? \"单机\" : \"集群\" }}</div></td>\n                  <td><div class=\"cell\">端口</div></td>\n                  <td><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.tcp_port }}</div></td>\n                  <td><div class=\"cell\">客户端数</div></td>\n                  <td><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.connected_clients }}</div></td>\n                </tr>\n                <tr>\n                  <td><div class=\"cell\">运行时间(天)</div></td>\n                  <td><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.uptime_in_days }}</div></td>\n                  <td><div class=\"cell\">使用内存</div></td>\n                  <td><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.used_memory_human }}</div></td>\n                  <td><div class=\"cell\">使用CPU</div></td>\n                  <td><div class=\"cell\" v-if=\"cache.info\">{{ parseFloat(cache.info.used_cpu_user_children).toFixed(2) }}</div></td>\n                  <td><div class=\"cell\">内存配置</div></td>\n                  <td><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.maxmemory_human }}</div></td>\n                </tr>\n                <tr>\n                  <td><div class=\"cell\">AOF是否开启</div></td>\n                  <td><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.aof_enabled == \"0\" ? \"否\" : \"是\" }}</div></td>\n                  <td><div class=\"cell\">RDB是否成功</div></td>\n                  <td><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.rdb_last_bgsave_status }}</div></td>\n                  <td><div class=\"cell\">Key数量</div></td>\n                  <td><div class=\"cell\" v-if=\"cache.dbSize\">{{ cache.dbSize }} </div></td>\n                  <td><div class=\"cell\">网络入口/出口</div></td>\n                  <td><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.instantaneous_input_kbps }}kps/{{cache.info.instantaneous_output_kbps}}kps</div></td>\n                </tr>\n              </tbody>\n            </table>\n          </div>\n        </el-card>\n      </el-col>\n\n      <el-col :span=\"12\" class=\"card-box\">\n        <el-card>\n          <div slot=\"header\"><span>命令统计</span></div>\n          <div class=\"el-table el-table--enable-row-hover el-table--medium\">\n            <div ref=\"commandstats\" style=\"height: 420px\" />\n          </div>\n        </el-card>\n      </el-col>\n\n      <el-col :span=\"12\" class=\"card-box\">\n        <el-card>\n          <div slot=\"header\">\n            <span>内存信息</span>\n          </div>\n          <div class=\"el-table el-table--enable-row-hover el-table--medium\">\n            <div ref=\"usedmemory\" style=\"height: 420px\" />\n          </div>\n        </el-card>\n      </el-col>\n    </el-row>\n  </div>\n</template>\n\n<script>\nimport { getCache } from \"@/api/monitor/cache\";\nimport echarts from \"echarts\";\n\nexport default {\n  name: \"Server\",\n  data() {\n    return {\n      // 加载层信息\n      loading: [],\n      // 统计命令信息\n      commandstats: null,\n      // 使用内存\n      usedmemory: null,\n      // cache信息\n      cache: [],\n    };\n  },\n  created() {\n    this.getList();\n    this.openLoading();\n  },\n  methods: {\n    /** 查缓存询信息 */\n    getList() {\n      getCache().then((response) => {\n        this.cache = response.data;\n        this.loading.close();\n\n        this.commandstats = echarts.init(this.$refs.commandstats, \"macarons\");\n        this.commandstats.setOption({\n          tooltip: {\n            trigger: \"item\",\n            formatter: \"{a} <br/>{b} : {c} ({d}%)\",\n          },\n          series: [\n            {\n              name: \"命令\",\n              type: \"pie\",\n              roseType: \"radius\",\n              radius: [15, 95],\n              center: [\"50%\", \"38%\"],\n              data: response.data.commandStats,\n              animationEasing: \"cubicInOut\",\n              animationDuration: 1000,\n            },\n          ],\n        });\n        this.usedmemory = echarts.init(this.$refs.usedmemory, \"macarons\");\n        this.usedmemory.setOption({\n          tooltip: {\n            formatter: \"{b} <br/>{a} : \" + this.cache.info.used_memory_human,\n          },\n          series: [\n            {\n              name: \"峰值\",\n              type: \"gauge\",\n              min: 0,\n              max: 1000,\n              detail: {\n                formatter: this.cache.info.used_memory_human,\n              },\n              data: [\n                {\n                  value: parseFloat(this.cache.info.used_memory_human),\n                  name: \"内存消耗\",\n                },\n              ],\n            },\n          ],\n        });\n      });\n    },\n    // 打开加载层\n    openLoading() {\n      this.loading = this.$loading({\n        lock: true,\n        text: \"拼命读取中\",\n        spinner: \"el-icon-loading\",\n        background: \"rgba(0, 0, 0, 0.7)\",\n      });\n    },\n  },\n};\n</script>\n"]}]}