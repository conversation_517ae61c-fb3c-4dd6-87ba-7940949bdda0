{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/layout/components/Navbar.vue?vue&type=style&index=0&id=d16d6306&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/layout/components/Navbar.vue", "mtime": 1655131056000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/css-loader/dist/cjs.js", "mtime": 1751171659443}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/loaders/stylePostLoader.js", "mtime": 1751171659924}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/postcss-loader/src/index.js", "mtime": 1751171659554}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/sass-loader/dist/cjs.js", "mtime": 1751171659051}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Navbar.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+GA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Navbar.vue", "sourceRoot": "src/layout/components", "sourcesContent": ["<template>\n  <div class=\"navbar\">\n    <hamburger id=\"hamburger-container\" :is-active=\"sidebar.opened\" class=\"hamburger-container\" @toggleClick=\"toggleSideBar\" />\n\n    <breadcrumb id=\"breadcrumb-container\" class=\"breadcrumb-container\" />\n\n    <div class=\"right-menu\">\n      <template v-if=\"device!=='mobile'\">\n        <search id=\"header-search\" class=\"right-menu-item\" />\n\n        <el-tooltip content=\"访问官网\" effect=\"dark\" placement=\"bottom\">\n          <a href=\"http://www.clled.com/\" target=\"_blank\" class=\"right-menu-item hover-effect\">\n            <i class=\"el-icon-house\"></i>\n          </a>\n        </el-tooltip>\n\n        <screenfull id=\"screenfull\" class=\"right-menu-item hover-effect\" />\n\n        <el-tooltip content=\"布局大小\" effect=\"dark\" placement=\"bottom\">\n          <size-select id=\"size-select\" class=\"right-menu-item hover-effect\" />\n        </el-tooltip>\n\n        <el-tooltip content=\"通知中心\" effect=\"dark\" placement=\"bottom\">\n          <notice class=\"right-menu-item\" />\n        </el-tooltip>\n\n      </template>\n\n      <el-dropdown class=\"avatar-container right-menu-item hover-effect\" trigger=\"click\">\n        <div class=\"avatar-wrapper\">\n          <img :src=\"avatar\" class=\"user-avatar\">\n          <i class=\"el-icon-caret-bottom\" />\n        </div>\n        <el-dropdown-menu slot=\"dropdown\">\n          <el-dropdown-item>\n            <span>账号:{{userName()}}</span>\n          </el-dropdown-item>\n          <router-link to=\"/user/profile\">\n            <el-dropdown-item>个人中心</el-dropdown-item>\n          </router-link>\n          <el-dropdown-item @click.native=\"setting = true\">\n            <span>布局设置</span>\n          </el-dropdown-item>\n          <el-dropdown-item divided @click.native=\"logout\">\n            <span>退出登录</span>\n          </el-dropdown-item>\n        </el-dropdown-menu>\n      </el-dropdown>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { mapGetters } from 'vuex'\nimport Breadcrumb from '@/components/Breadcrumb'\nimport Hamburger from '@/components/Hamburger'\nimport Screenfull from '@/components/Screenfull'\nimport SizeSelect from '@/components/SizeSelect'\nimport Search from '@/components/HeaderSearch'\nimport Notice from '@/components/Notice'\n\nexport default {\n  components: {\n    Breadcrumb,\n    Hamburger,\n    Screenfull,\n    SizeSelect,\n    Search,\n    Notice\n  },\n  computed: {\n    ...mapGetters([\n      'sidebar',\n      'avatar',\n      'device'\n    ]),\n    setting: {\n      get() {\n        return this.$store.state.settings.showSettings\n      },\n      set(val) {\n        this.$store.dispatch('settings/changeSetting', {\n          key: 'showSettings',\n          value: val\n        })\n      }\n    }\n  },\n  methods: {\n    toggleSideBar() {\n      this.$store.dispatch('app/toggleSideBar')\n    },\n    async logout() {\n      this.$confirm('确定注销并退出系统吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        this.$store.dispatch('LogOut').then(() => {\n          location.href = '/index';\n        })\n      })\n    },\n    userName(){\n      return this.$store.state.user.name;\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.navbar {\n  height: 50px;\n  overflow: hidden;\n  position: relative;\n  background: #fff;\n  box-shadow: 0 1px 4px rgba(0,21,41,.08);\n\n  .hamburger-container {\n    line-height: 46px;\n    height: 100%;\n    float: left;\n    cursor: pointer;\n    transition: background .3s;\n    -webkit-tap-highlight-color:transparent;\n\n    &:hover {\n      background: rgba(0, 0, 0, .025)\n    }\n  }\n\n  .breadcrumb-container {\n    float: left;\n  }\n\n  .errLog-container {\n    display: inline-block;\n    vertical-align: top;\n  }\n\n  .right-menu {\n    float: right;\n    height: 100%;\n    line-height: 50px;\n\n    &:focus {\n      outline: none;\n    }\n\n    .right-menu-item {\n      display: inline-block;\n      padding: 0 8px;\n      height: 100%;\n      font-size: 18px;\n      color: #5a5e66;\n      vertical-align: text-bottom;\n\n      &.hover-effect {\n        cursor: pointer;\n        transition: background .3s;\n\n        &:hover {\n          background: rgba(0, 0, 0, .025)\n        }\n      }\n    }\n\n    .avatar-container {\n      margin-right: 30px;\n\n      .avatar-wrapper {\n        margin-top: 5px;\n        position: relative;\n\n        .user-avatar {\n          cursor: pointer;\n          width: 40px;\n          height: 40px;\n          border-radius: 10px;\n        }\n\n        .el-icon-caret-bottom {\n          cursor: pointer;\n          position: absolute;\n          right: -20px;\n          top: 25px;\n          font-size: 12px;\n        }\n      }\n    }\n  }\n}\n</style>\n"]}]}