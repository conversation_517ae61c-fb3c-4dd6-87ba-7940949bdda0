{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/monitor/druid/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/monitor/druid/index.vue", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiRHJ1aWQiLAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBzcmM6IHByb2Nlc3MuZW52LlZVRV9BUFBfQkFTRV9BUEkgKyAiL2RydWlkL2luZGV4Lmh0bWwiLAogICAgICBoZWlnaHQ6IGRvY3VtZW50LmRvY3VtZW50RWxlbWVudC5jbGllbnRIZWlnaHQgLSA5NC41ICsgInB4OyIsCiAgICAgIGxvYWRpbmc6IHRydWUKICAgIH07CiAgfSwKICBtb3VudGVkOiBmdW5jdGlvbigpIHsKICAgIHNldFRpbWVvdXQoKCkgPT4gewogICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsKICAgIH0sIDIzMCk7CiAgICBjb25zdCB0aGF0ID0gdGhpczsKICAgIHdpbmRvdy5vbnJlc2l6ZSA9IGZ1bmN0aW9uIHRlbXAoKSB7CiAgICAgIHRoYXQuaGVpZ2h0ID0gZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50LmNsaWVudEhlaWdodCAtIDk0LjUgKyAicHg7IjsKICAgIH07CiAgfQp9Owo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/monitor/druid", "sourcesContent": ["<template>\n  <div v-loading=\"loading\" :style=\"'height:'+ height\">\n    <iframe :src=\"src\" frameborder=\"no\" style=\"width: 100%;height: 100%\" scrolling=\"auto\" />\n  </div>\n</template>\n<script>\nexport default {\n  name: \"Druid\",\n  data() {\n    return {\n      src: process.env.VUE_APP_BASE_API + \"/druid/index.html\",\n      height: document.documentElement.clientHeight - 94.5 + \"px;\",\n      loading: true\n    };\n  },\n  mounted: function() {\n    setTimeout(() => {\n      this.loading = false;\n    }, 230);\n    const that = this;\n    window.onresize = function temp() {\n      that.height = document.documentElement.clientHeight - 94.5 + \"px;\";\n    };\n  }\n};\n</script>\n"]}]}