{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/system/user/profile/userAvatar.vue?vue&type=template&id=e81d90b0&scoped=true", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/system/user/profile/userAvatar.vue", "mtime": 1640316250000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/loaders/templateLoader.js", "mtime": 1751171659981}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}