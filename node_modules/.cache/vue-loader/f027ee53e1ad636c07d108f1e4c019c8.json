{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/views/system/user/profile/notice.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/views/system/user/profile/notice.vue", "mtime": 1662301014000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBzdG9yZSBmcm9tICdAL3N0b3JlJwppbXBvcnQgeyBsaXN0Tm90aWNlLCBnZXROb3RpY2UsIHJlYWROb3RpY2UsIGRlbE5vdGljZSB9IGZyb20gJ0AvYXBpL3N5c3RlbS9ub3RpY2UnCmltcG9ydCBQYWdpbmF0aW9uIGZyb20gJ0AvY29tcG9uZW50cy9QYWdpbmF0aW9uJyAvLyBTZWNvbmRhcnkgcGFja2FnZSBiYXNlZCBvbiBlbC1wYWdpbmF0aW9uCmltcG9ydCBfIGZyb20gJ2xvZGFzaCcKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnQWRtaW5Ob3RpY2UnLAogIGNvbXBvbmVudHM6IHsgUGFnaW5hdGlvbiB9LAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBsaXN0OiBbXSwKICAgICAgdG90YWw6IDAsCiAgICAgIGxpc3RMb2FkaW5nOiB0cnVlLAogICAgICBsaXN0UXVlcnk6IHsKICAgICAgICBwYWdlOiAxLAogICAgICAgIGxpbWl0OiAyMCwKICAgICAgICB0aXRsZTogJycsCiAgICAgICAgdHlwZTogJ3VucmVhZCcsCiAgICAgICAgc29ydDogJ2NyZWF0ZV90aW1lJywKICAgICAgICBvcmRlcjogJ2Rlc2MnLAogICAgICAgIHVzZXJJZDogdW5kZWZpbmVkLAogICAgICB9LAogICAgICBtdWx0aXBsZVNlbGVjdGlvbjogW10sCiAgICAgIG5vdGljZTogewogICAgICAgIHRpdGxlOiAnJywKICAgICAgICBzb3VyY2U6ICcnLAogICAgICAgIGNvbnRlbnQ6ICcnLAogICAgICAgIGFkZFRpbWU6ICcnCiAgICAgIH0sCiAgICAgIG5vdGljZVZpc2libGU6IGZhbHNlCiAgICB9CiAgfSwKICBjcmVhdGVkKCkgewogICAgY29uc3Qgcm9sZXMgPSBzdG9yZS5nZXR0ZXJzICYmIHN0b3JlLmdldHRlcnMucm9sZXMKICAgIGNvbnN0IHVzZXJJZCA9IHN0b3JlLmdldHRlcnMgJiYgc3RvcmUuZ2V0dGVycy51c2VySWQKICAgIGlmKCEocm9sZXMgJiYgcm9sZXMuaW5jbHVkZXMoImFkbWluIikpKXsKICAgICAgdGhpcy5saXN0UXVlcnkudXNlcklkID0gdXNlcklkOwogICAgfQogICAgdGhpcy5nZXRMaXN0KCkKICB9LAogIG1ldGhvZHM6IHsKICAgIGdldExpc3QoKSB7CiAgICAgIHRoaXMubGlzdExvYWRpbmcgPSB0cnVlCiAgICAgIGxpc3ROb3RpY2UodGhpcy5saXN0UXVlcnkpCiAgICAgICAgLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgICAgdGhpcy5saXN0ID0gcmVzcG9uc2Uucm93cwogICAgICAgICAgdGhpcy50b3RhbCA9IHJlc3BvbnNlLnRvdGFsCiAgICAgICAgICB0aGlzLmxpc3RMb2FkaW5nID0gZmFsc2UKICAgICAgICB9KQogICAgICAgIC5jYXRjaCgoKSA9PiB7CiAgICAgICAgICB0aGlzLmxpc3QgPSBbXQogICAgICAgICAgdGhpcy50b3RhbCA9IDAKICAgICAgICAgIHRoaXMubGlzdExvYWRpbmcgPSBmYWxzZQogICAgICAgIH0pCiAgICB9LAogICAgaGFuZGxlRmlsdGVyKCkgewogICAgICB0aGlzLmxpc3RRdWVyeS5wYWdlID0gMQogICAgICB0aGlzLmdldExpc3QoKQogICAgfSwKICAgIGhhbmRsZURlbGV0ZShyb3cpIHsKICAgICAgaWYoIXJvdy51c2VySWQpewogICAgICAgIHRoaXMuJG5vdGlmeS5lcnJvcih7CiAgICAgICAgICB0aXRsZTogJ+Wksei0pScsCiAgICAgICAgICBtZXNzYWdlOiAn57O757uf6YCa55+l5peg5rOV5Yig6ZmkJwogICAgICAgIH0pCiAgICAgICAgcmV0dXJuCiAgICAgIH0KICAgICAgZGVsTm90aWNlKHJvdy5ub3RpY2VJZCkKICAgICAgICAudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgICB0aGlzLiRub3RpZnkuc3VjY2Vzcyh7CiAgICAgICAgICAgIHRpdGxlOiAn5oiQ5YqfJywKICAgICAgICAgICAgbWVzc2FnZTogJ+WIoOmZpOmAmuefpeaIkOWKnycKICAgICAgICAgIH0pCiAgICAgICAgICB0aGlzLmdldExpc3QoKQogICAgICAgIH0pCiAgICAgICAgLmNhdGNoKHJlc3BvbnNlID0+IHsKICAgICAgICAgIHRoaXMuJG5vdGlmeS5lcnJvcih7CiAgICAgICAgICAgIHRpdGxlOiAn5aSx6LSlJywKICAgICAgICAgICAgbWVzc2FnZTogcmVzcG9uc2UuZXJybXNnCiAgICAgICAgICB9KQogICAgICAgIH0pCiAgICB9LAogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHZhbCkgewogICAgICB0aGlzLm11bHRpcGxlU2VsZWN0aW9uID0gdmFsCiAgICB9LAogICAgaGFuZGxlQmF0Y2hEZWxldGUoKSB7CiAgICAgIGlmICh0aGlzLm11bHRpcGxlU2VsZWN0aW9uLmxlbmd0aCA9PT0gMCkgewogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+ivt+mAieaLqeiHs+WwkeS4gOadoeiusOW9lScpCiAgICAgICAgcmV0dXJuCiAgICAgIH0KICAgICAgY29uc3QgaWRzID0gW10KICAgICAgXy5mb3JFYWNoKHRoaXMubXVsdGlwbGVTZWxlY3Rpb24sIGZ1bmN0aW9uKGl0ZW0pIHsKICAgICAgICBpZihpdGVtLnVzZXJJZCl7CiAgICAgICAgICBpZHMucHVzaChpdGVtLm5vdGljZUlkKQogICAgICAgIH0KICAgICAgfSkKICAgICAgaWYoaWRzLmxlbmd0aCA9PSAwKXsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgZGVsTm90aWNlKGlkcykKICAgICAgICAudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgICB2YXIgbXNnID0gaWRzLmxlbmd0aCA+IDEgPyAn5om56YeP5Yig6Zmk6YCa55+l5oiQ5YqfJyA6ICfliKDpmaTmiJDlip8nOwogICAgICAgICAgdGhpcy4kbm90aWZ5LnN1Y2Nlc3MoewogICAgICAgICAgICB0aXRsZTogJ+aIkOWKnycsCiAgICAgICAgICAgIG1lc3NhZ2U6ICfmibnph4/liKDpmaTpgJrnn6XmiJDlip8nCiAgICAgICAgICB9KQogICAgICAgICAgdGhpcy5nZXRMaXN0KCkKICAgICAgICB9KQogICAgICAgIC5jYXRjaChyZXNwb25zZSA9PiB7CiAgICAgICAgICB0aGlzLiRub3RpZnkuZXJyb3IoewogICAgICAgICAgICB0aXRsZTogJ+Wksei0pScsCiAgICAgICAgICAgIG1lc3NhZ2U6IHJlc3BvbnNlLmVycm1zZwogICAgICAgICAgfSkKICAgICAgICB9KQogICAgfSwKICAgIGhhbmRsZVJlYWQocm93KSB7CiAgICAgIGdldE5vdGljZShyb3cubm90aWNlSWQpCiAgICAgICAgLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgICAgdGhpcy5ub3RpY2UgPSByZXNwb25zZS5kYXRhCiAgICAgICAgICB0aGlzLm5vdGljZVZpc2libGUgPSB0cnVlCiAgICAgICAgfSkKICAgICAgaWYocm93LnVzZXJJZCl7CiAgICAgICAgcmVhZE5vdGljZShyb3cubm90aWNlSWQpCiAgICAgIH0KICAgIH0sCiAgICBhZnRlclJlYWQoKSB7CiAgICAgIHRoaXMubm90aWNlVmlzaWJsZSA9IGZhbHNlCiAgICAgIHRoaXMuZ2V0TGlzdCgpCiAgICB9LAogICAgaGFuZGxlQmF0Y2hSZWFkKCkgewogICAgICBpZiAodGhpcy5tdWx0aXBsZVNlbGVjdGlvbi5sZW5ndGggPT09IDApIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfor7fpgInmi6noh7PlsJHkuIDmnaHorrDlvZUnKQogICAgICAgIHJldHVybgogICAgICB9CiAgICAgIGNvbnN0IGlkcyA9IFtdCiAgICAgIF8uZm9yRWFjaCh0aGlzLm11bHRpcGxlU2VsZWN0aW9uLCBmdW5jdGlvbihpdGVtKSB7CiAgICAgICAgaWYoaXRlbS51c2VySWQpewogICAgICAgICAgaWRzLnB1c2goaXRlbS5ub3RpY2VJZCkKICAgICAgICB9CiAgICAgIH0pCiAgICAgIGlmKGlkcy5sZW5ndGggPT0gMCl7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIAogICAgICByZWFkTm90aWNlKGlkcykKICAgICAgICAudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgICB2YXIgbXNnID0gaWRzLmxlbmd0aCA+IDEgPyAn5om56YeP6K6+572u6YCa55+l5bey6K+75oiQ5YqfJyA6ICflt7Lor7vmiJDlip8nOwogICAgICAgICAgdGhpcy4kbm90aWZ5LnN1Y2Nlc3MoewogICAgICAgICAgICB0aXRsZTogJ+aIkOWKnycsCiAgICAgICAgICAgIG1lc3NhZ2U6IG1zZwogICAgICAgICAgfSkKICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpCiAgICAgICAgfSkKICAgICAgICAuY2F0Y2gocmVzcG9uc2UgPT4gewogICAgICAgICAgdGhpcy4kbm90aWZ5LmVycm9yKHsKICAgICAgICAgICAgdGl0bGU6ICflpLHotKUnLAogICAgICAgICAgICBtZXNzYWdlOiByZXNwb25zZS5lcnJtc2cKICAgICAgICAgIH0pCiAgICAgICAgfSkKICAgIH0sCiAgICBzZWxlY3RFbmFibGUocm93LCByb3dJbmRleCkgewogICAgICBpZiAoIXJvdy51c2VySWQpIHsKICAgICAgICByZXR1cm4gZmFsc2U7CiAgICAgIH0KICAgICAgcmV0dXJuIHRydWU7CiAgICB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["notice.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6DA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "notice.vue", "sourceRoot": "src/views/system/user/profile", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n\n    <!-- 查询和其他操作 -->\n    <div class=\"filter-container\">\n      <el-input v-model=\"listQuery.title\" size=\"small\" clearable class=\"filter-item\" style=\"width: 200px;\" placeholder=\"请输入标题关键字\" />\n      <el-button size=\"mini\" class=\"filter-item\" type=\"primary\" icon=\"el-icon-search\" @click=\"handleFilter\">查找</el-button>\n    </div>\n\n    <div class=\"operator-container\">\n      <el-button size=\"mini\" class=\"filter-item\" type=\"primary\" icon=\"el-icon-edit\" @click=\"handleBatchRead\">批量已读</el-button>\n      <el-button size=\"mini\" class=\"filter-item\" type=\"danger\" icon=\"el-icon-delete\" @click=\"handleBatchDelete\">批量删除</el-button>\n    </div>\n\n    <!-- <el-tabs v-model=\"listQuery.type\" @tab-click=\"handleFilter\">\n      <el-tab-pane label=\"未读通知\" name=\"unread\" />\n      <el-tab-pane label=\"已读通知\" name=\"read\" />\n      <el-tab-pane label=\"所有通知\" name=\"all\" />\n    </el-tabs> -->\n\n    <!-- 查询结果 -->\n    <el-table v-loading=\"listLoading\" :data=\"list\" element-loading-text=\"正在查询中。。。\" fit highlight-current-row @selection-change=\"handleSelectionChange\">\n      <el-table-column type=\"selection\" :selectable=\"selectEnable\" width=\"55\" />\n\n      <el-table-column align=\"center\" label=\"通知类型\" prop=\"userId\" width=\"120\">\n        <template slot-scope=\"scope\">\n          <el-tag :type=\"scope.row.userId ? 'success' : 'error' \">{{ scope.row.userId ? '个人' : '系统' }}</el-tag>\n        </template>\n      </el-table-column>\n\n      <el-table-column align=\"center\" label=\"通知标题\" prop=\"noticeTitle\" />\n\n      <el-table-column align=\"center\" label=\"通知时间\" prop=\"createTime\" width=\"180\" />\n\n      <el-table-column align=\"center\" label=\"通知状态\" prop=\"readTime\" width=\"120\">\n        <template slot-scope=\"scope\" v-if=\"scope.row.userId\">\n          <el-tag :type=\"scope.row.readTime ? 'success' : 'error' \">{{ scope.row.userId ? (scope.row.readTime ? '已读' : '未读') : ''}}</el-tag>\n        </template>\n      </el-table-column>\n\n      <el-table-column align=\"center\" label=\"操作\" width=\"200\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"scope\">\n          <el-button type=\"text\" icon=\"el-icon-view\" size=\"mini\" @click=\"handleRead(scope.row)\">阅读</el-button>\n          <el-button v-if=\"scope.row.userId\" type=\"text\" icon=\"el-icon-delete\" size=\"mini\" @click=\"handleDelete(scope.row)\">删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <pagination v-show=\"total>0\" :total=\"total\" :page.sync=\"listQuery.page\" :limit.sync=\"listQuery.limit\" @pagination=\"getList\" />\n\n    <el-dialog :title=\"notice.noticeTitle\" :visible.sync=\"noticeVisible\" center>\n      <el-divider content-position=\"left\">{{ notice.createBy }} 于 {{ notice.createTime }} 通知如下内容：</el-divider>\n      <div v-html=\"notice.noticeContent\" />\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"afterRead\">确定</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport store from '@/store'\nimport { listNotice, getNotice, readNotice, delNotice } from '@/api/system/notice'\nimport Pagination from '@/components/Pagination' // Secondary package based on el-pagination\nimport _ from 'lodash'\n\nexport default {\n  name: 'AdminNotice',\n  components: { Pagination },\n  data() {\n    return {\n      list: [],\n      total: 0,\n      listLoading: true,\n      listQuery: {\n        page: 1,\n        limit: 20,\n        title: '',\n        type: 'unread',\n        sort: 'create_time',\n        order: 'desc',\n        userId: undefined,\n      },\n      multipleSelection: [],\n      notice: {\n        title: '',\n        source: '',\n        content: '',\n        addTime: ''\n      },\n      noticeVisible: false\n    }\n  },\n  created() {\n    const roles = store.getters && store.getters.roles\n    const userId = store.getters && store.getters.userId\n    if(!(roles && roles.includes(\"admin\"))){\n      this.listQuery.userId = userId;\n    }\n    this.getList()\n  },\n  methods: {\n    getList() {\n      this.listLoading = true\n      listNotice(this.listQuery)\n        .then(response => {\n          this.list = response.rows\n          this.total = response.total\n          this.listLoading = false\n        })\n        .catch(() => {\n          this.list = []\n          this.total = 0\n          this.listLoading = false\n        })\n    },\n    handleFilter() {\n      this.listQuery.page = 1\n      this.getList()\n    },\n    handleDelete(row) {\n      if(!row.userId){\n        this.$notify.error({\n          title: '失败',\n          message: '系统通知无法删除'\n        })\n        return\n      }\n      delNotice(row.noticeId)\n        .then(response => {\n          this.$notify.success({\n            title: '成功',\n            message: '删除通知成功'\n          })\n          this.getList()\n        })\n        .catch(response => {\n          this.$notify.error({\n            title: '失败',\n            message: response.errmsg\n          })\n        })\n    },\n    handleSelectionChange(val) {\n      this.multipleSelection = val\n    },\n    handleBatchDelete() {\n      if (this.multipleSelection.length === 0) {\n        this.$message.error('请选择至少一条记录')\n        return\n      }\n      const ids = []\n      _.forEach(this.multipleSelection, function(item) {\n        if(item.userId){\n          ids.push(item.noticeId)\n        }\n      })\n      if(ids.length == 0){\n        return;\n      }\n      delNotice(ids)\n        .then(response => {\n          var msg = ids.length > 1 ? '批量删除通知成功' : '删除成功';\n          this.$notify.success({\n            title: '成功',\n            message: '批量删除通知成功'\n          })\n          this.getList()\n        })\n        .catch(response => {\n          this.$notify.error({\n            title: '失败',\n            message: response.errmsg\n          })\n        })\n    },\n    handleRead(row) {\n      getNotice(row.noticeId)\n        .then(response => {\n          this.notice = response.data\n          this.noticeVisible = true\n        })\n      if(row.userId){\n        readNotice(row.noticeId)\n      }\n    },\n    afterRead() {\n      this.noticeVisible = false\n      this.getList()\n    },\n    handleBatchRead() {\n      if (this.multipleSelection.length === 0) {\n        this.$message.error('请选择至少一条记录')\n        return\n      }\n      const ids = []\n      _.forEach(this.multipleSelection, function(item) {\n        if(item.userId){\n          ids.push(item.noticeId)\n        }\n      })\n      if(ids.length == 0){\n        return;\n      }\n      \n      readNotice(ids)\n        .then(response => {\n          var msg = ids.length > 1 ? '批量设置通知已读成功' : '已读成功';\n          this.$notify.success({\n            title: '成功',\n            message: msg\n          })\n          this.getList()\n        })\n        .catch(response => {\n          this.$notify.error({\n            title: '失败',\n            message: response.errmsg\n          })\n        })\n    },\n    selectEnable(row, rowIndex) {\n      if (!row.userId) {\n        return false;\n      }\n      return true;\n    }\n  }\n}\n</script>\n"]}]}