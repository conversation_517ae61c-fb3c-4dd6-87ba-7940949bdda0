{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/components/tinymce/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/components/tinymce/index.vue", "mtime": 1641277326000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;AAKA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/tinymce", "sourcesContent": ["<template>\n  <textarea :id=\"tinymceId\" style=\"visibility: hidden\" />\n</template>\n\n<script>\nimport loadTinymce from '@/utils/loadTinymce'\nimport { plugins, toolbar } from './config'\nimport { debounce } from 'throttle-debounce'\n\nlet num = 1\n\nexport default {\n  props: {\n    id: {\n      type: String,\n      default: () => {\n        num === 10000 && (num = 1)\n        return `tinymce${+new Date()}${num++}`\n      }\n    },\n    value: {\n      default: ''\n    }\n  },\n  data() {\n    return {\n      tinymceId: this.id\n    }\n  },\n  mounted() {\n    loadTinymce(tinymce => {\n      // eslint-disable-next-line global-require\n      require('./zh_CN')\n      let conf = {\n        selector: `#${this.tinymceId}`,\n        language: 'zh_CN',\n        menubar: 'file edit insert view format table',\n        plugins,\n        toolbar,\n        height: 300,\n        branding: false,\n        object_resizing: false,\n        end_container_on_empty_block: true,\n        powerpaste_word_import: 'clean',\n        code_dialog_height: 450,\n        code_dialog_width: 1000,\n        advlist_bullet_styles: 'square',\n        advlist_number_styles: 'default',\n        default_link_target: '_blank',\n        link_title: false,\n        nonbreaking_force_tab: true\n      }\n      conf = Object.assign(conf, this.$attrs)\n      conf.init_instance_callback = editor => {\n        if (this.value) editor.setContent(this.value)\n        this.vModel(editor)\n      }\n      tinymce.init(conf)\n    })\n  },\n  destroyed() {\n    this.destroyTinymce()\n  },\n  methods: {\n    vModel(editor) {\n      // 控制连续写入时setContent的触发频率\n      const debounceSetContent = debounce(250, editor.setContent)\n      this.$watch('value', (val, prevVal) => {\n        if (editor && val !== prevVal && val !== editor.getContent()) {\n          if (typeof val !== 'string') val = val.toString()\n          debounceSetContent.call(editor, val)\n        }\n      })\n\n      editor.on('change keyup undo redo', () => {\n        this.$emit('input', editor.getContent())\n      })\n    },\n    destroyTinymce() {\n      if (!window.tinymce) return\n      const tinymce = window.tinymce.get(this.tinymceId)\n      if (tinymce) {\n        tinymce.destroy()\n      }\n    }\n  }\n}\n</script>\n"]}]}