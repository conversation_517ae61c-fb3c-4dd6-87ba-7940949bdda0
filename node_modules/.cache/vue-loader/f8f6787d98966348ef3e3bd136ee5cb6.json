{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Process/components/nodePanel/task.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Process/components/nodePanel/task.vue", "mtime": 1650105580000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["task.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "task.vue", "sourceRoot": "src/components/Process/components/nodePanel", "sourcesContent": ["<template>\n  <div>\n    <x-form ref=\"xForm\" v-model=\"formData\" :config=\"formConfig\">\n      <template #executionListener>\n        <el-badge :value=\"executionListenerLength\">\n          <el-button size=\"small\" @click=\"dialogName = 'executionListenerDialog'\">编辑</el-button>\n        </el-badge>\n      </template>\n      <template #taskListener>\n        <el-badge :value=\"taskListenerLength\">\n          <el-button size=\"small\" @click=\"dialogName = 'taskListenerDialog'\">编辑</el-button>\n        </el-badge>\n      </template>\n      <template #multiInstance>\n        <el-badge :is-dot=\"hasMultiInstance\">\n          <el-button size=\"small\" @click=\"dialogName = 'multiInstanceDialog'\">编辑</el-button>\n        </el-badge>\n      </template>\n    </x-form>\n    <executionListenerDialog\n      v-if=\"dialogName === 'executionListenerDialog'\"\n      :element=\"element\"\n      :modeler=\"modeler\"\n      @close=\"finishExecutionListener\"\n    />\n    <taskListenerDialog\n      v-if=\"dialogName === 'taskListenerDialog'\"\n      :element=\"element\"\n      :modeler=\"modeler\"\n      @close=\"finishTaskListener\"\n    />\n    <multiInstanceDialog\n      v-if=\"dialogName === 'multiInstanceDialog'\"\n      :element=\"element\"\n      :modeler=\"modeler\"\n      @close=\"finishMultiInstance\"\n    />\n  </div>\n</template>\n\n<script>\nimport mixinPanel from '../../common/mixinPanel'\nimport executionListenerDialog from './property/executionListener'\nimport taskListenerDialog from './property/taskListener'\nimport multiInstanceDialog from './property/multiInstance'\nimport { commonParse, userTaskParse } from '../../common/parseElement'\nexport default {\n  components: {\n    executionListenerDialog,\n    taskListenerDialog,\n    multiInstanceDialog\n  },\n  mixins: [mixinPanel],\n  props: {\n    users: {\n      type: Array,\n      required: true\n    },\n    groups: {\n      type: Array,\n      required: true\n    }\n  },\n  data() {\n    return {\n      userTypeOption: [\n        { label: '指定人员', value: 'assignee' },\n        { label: '候选人员', value: 'candidateUsers' },\n        { label: '候选组', value: 'candidateGroups' }\n      ],\n      dataTypeOption: [\n        { label: '固定', value: 'fixed' },\n        { label: '动态', value: 'dynamic' }\n      ],\n      dialogName: '',\n      executionListenerLength: 0,\n      taskListenerLength: 0,\n      hasMultiInstance: false,\n      formData: {}\n    }\n  },\n  computed: {\n    formConfig() {\n      const _this = this\n      return {\n        inline: false,\n        item: [\n          {\n            xType: 'input',\n            name: 'id',\n            label: '节点 id',\n            rules: [{ required: true, message: 'Id 不能为空' }]\n          },\n          {\n            xType: 'input',\n            name: 'name',\n            label: '节点名称',\n            rules: [{ required: true, message: '节点名称不能为空' }]\n          },\n          {\n            xType: 'input',\n            name: 'documentation',\n            label: '节点描述'\n          },\n          {\n            xType: 'slot',\n            name: 'executionListener',\n            label: '执行监听器'\n          },\n          {\n            xType: 'slot',\n            name: 'taskListener',\n            label: '任务监听器',\n            show: !!_this.showConfig.taskListener\n          },\n          {\n            xType: 'select',\n            name: 'userType',\n            label: '人员类型',\n            dic: _this.userTypeOption,\n            show: !!_this.showConfig.userType\n          },\n          {\n            xType: 'radio',\n            name: 'dataType',\n            label: '指定方式',\n            dic: _this.dataTypeOption,\n            show: !!_this.showConfig.dataType,\n            rules: [{ required: true, message: '请指定方式' }]\n          },\n          // {\n          //   xType: 'input',\n          //   name: 'assigneeFixed',\n          //   label: '指定人(表达式)',\n          //   show: !!_this.showConfig.assigneeFixed && _this.formData.userType === 'assignee' && _this.formData.dataType === 'fixed'\n          // },\n          // {\n          //   xType: 'input',\n          //   name: 'candidateUsersFixed',\n          //   label: '候选人(表达式)',\n          //   show: !!_this.showConfig.candidateUsersFixed && _this.formData.userType === 'candidateUsers' && _this.formData.dataType === 'fixed'\n          // },\n          // {\n          //   xType: 'input',\n          //   name: 'candidateGroupsFixed',\n          //   label: '候选组(表达式)',\n          //   show: !!_this.showConfig.candidateGroupsFixed && _this.formData.userType === 'candidateGroups' && _this.formData.dataType === 'fixed'\n          // },\n          {\n            xType: 'select',\n            name: 'assignee',\n            label: '指定人员',\n            allowCreate: true,\n            filterable: true,\n            dic: { data: _this.users, label: 'nickName', value: 'userId' },\n            show: !!_this.showConfig.assignee && _this.formData.userType === 'assignee'\n          },\n          {\n            xType: 'select',\n            name: 'candidateUsers',\n            label: '候选人员',\n            multiple: true,\n            allowCreate: true,\n            filterable: true,\n            dic: { data: _this.users, label: 'nickName', value: 'userId' },\n            show: !!_this.showConfig.candidateUsers && _this.formData.userType === 'candidateUsers'\n          },\n          {\n            xType: 'select',\n            name: 'candidateGroups',\n            label: '候选组',\n            multiple: true,\n            allowCreate: true,\n            filterable: true,\n            dic: { data: _this.groups, label: 'roleName', value: 'roleId' },\n            show: !!_this.showConfig.candidateGroups && _this.formData.userType === 'candidateGroups'\n          },\n          {\n            xType: 'slot',\n            name: 'multiInstance',\n            label: '多实例'\n          },\n          {\n            xType: 'switch',\n            name: 'async',\n            label: '异步',\n            activeText: '是',\n            inactiveText: '否',\n            show: !!_this.showConfig.async\n          },\n          {\n            xType: 'input',\n            name: 'priority',\n            label: '优先级',\n            show: !!_this.showConfig.priority\n          },\n          {\n            xType: 'input',\n            name: 'formKey',\n            label: '表单标识key',\n            show: !!_this.showConfig.formKey\n          },\n          {\n            xType: 'input',\n            name: 'skipExpression',\n            label: '跳过表达式',\n            show: !!_this.showConfig.skipExpression\n          },\n          {\n            xType: 'switch',\n            name: 'isForCompensation',\n            label: '是否为补偿',\n            activeText: '是',\n            inactiveText: '否',\n            show: !!_this.showConfig.isForCompensation\n          },\n          {\n            xType: 'switch',\n            name: 'triggerable',\n            label: '服务任务可触发',\n            activeText: '是',\n            inactiveText: '否',\n            show: !!_this.showConfig.triggerable\n          },\n          {\n            xType: 'switch',\n            name: 'autoStoreVariables',\n            label: '自动存储变量',\n            activeText: '是',\n            inactiveText: '否',\n            show: !!_this.showConfig.autoStoreVariables\n          },\n          {\n            xType: 'input',\n            name: 'ruleVariablesInput',\n            label: '输入变量',\n            show: !!_this.showConfig.ruleVariablesInput\n          },\n          {\n            xType: 'input',\n            name: 'rules',\n            label: '规则',\n            show: !!_this.showConfig.rules\n          },\n          {\n            xType: 'input',\n            name: 'resultVariable',\n            label: '结果变量',\n            show: !!_this.showConfig.resultVariable\n          },\n          {\n            xType: 'switch',\n            name: 'exclude',\n            label: '排除',\n            activeText: '是',\n            inactiveText: '否',\n            show: !!_this.showConfig.exclude\n          },\n          {\n            xType: 'input',\n            name: 'class',\n            label: '类',\n            show: !!_this.showConfig.class\n          },\n          {\n            xType: 'datePicker',\n            type: 'datetime',\n            name: 'dueDate',\n            label: '到期时间',\n            show: !!_this.showConfig.dueDate\n          }\n        ]\n      }\n    }\n  },\n  watch: {\n    'formData.userType': function(val, oldVal) {\n      if (oldVal) {\n        const types = ['assignee', 'candidateUsers', 'candidateGroups']\n        types.forEach(type => {\n          delete this.element.businessObject.$attrs[`flowable:${type}`]\n          delete this.formData[type]\n        })\n      }\n    },\n    // 动态选择流程执行人\n    'formData.dataType': function(val) {\n      const that = this\n      this.updateProperties({'flowable:dataType': val})\n      if (val === 'dynamic') {\n        this.updateProperties({'flowable:userType': that.formData.userType})\n      }\n      // 切换时 删除之前选中的值\n      const types = ['assignee', 'candidateUsers', 'candidateGroups']\n      types.forEach(type => {\n        delete this.element.businessObject.$attrs[`flowable:${type}`]\n        delete this.formData[type]\n      })\n      // 传值到父组件\n      const params = {\n        dataType: val,\n        userType: this.formData.userType\n      }\n      this.$emit('dataType', params)\n    },\n    'formData.assignee': function(val) {\n        if (this.formData.userType !== 'assignee') {\n          delete this.element.businessObject.$attrs[`flowable:assignee`]\n          return\n        }\n        this.updateProperties({'flowable:assignee': val})\n    },\n    'formData.candidateUsers': function(val) {\n        if (this.formData.userType !== 'candidateUsers') {\n          delete this.element.businessObject.$attrs[`flowable:candidateUsers`]\n          return\n        }\n        this.updateProperties({'flowable:candidateUsers': val?.join(',')})\n    },\n    'formData.candidateGroups': function(val) {\n        if (this.formData.userType !== 'candidateGroups') {\n          delete this.element.businessObject.$attrs[`flowable:candidateGroups`]\n          return\n        }\n        this.updateProperties({'flowable:candidateGroups': val?.join(',')})\n    },\n    'formData.async': function(val) {\n      if (val === '') val = null\n      this.updateProperties({ 'flowable:async': true })\n    },\n    'formData.dueDate': function(val) {\n      if (val === '') val = null\n      this.updateProperties({ 'flowable:dueDate': val })\n    },\n    'formData.formKey': function(val) {\n      if (val === '') val = null\n      this.updateProperties({ 'flowable:formKey': val })\n    },\n    'formData.priority': function(val) {\n      if (val === '') val = null\n      this.updateProperties({ 'flowable:priority': val })\n    },\n    'formData.skipExpression': function(val) {\n      if (val === '') val = null\n      this.updateProperties({ 'flowable:skipExpression': val })\n    },\n    'formData.isForCompensation': function(val) {\n      if (val === '') val = null\n      this.updateProperties({ 'isForCompensation': val })\n    },\n    'formData.triggerable': function(val) {\n      if (val === '') val = null\n      this.updateProperties({ 'flowable:triggerable': val })\n    },\n    'formData.class': function(val) {\n      if (val === '') val = null\n      this.updateProperties({ 'flowable:class': val })\n    },\n    'formData.autoStoreVariables': function(val) {\n      if (val === '') val = null\n      this.updateProperties({ 'flowable:autoStoreVariables': val })\n    },\n    'formData.exclude': function(val) {\n      if (val === '') val = null\n      this.updateProperties({ 'flowable:exclude': val })\n    },\n    'formData.ruleVariablesInput': function(val) {\n      if (val === '') val = null\n      this.updateProperties({ 'flowable:ruleVariablesInput': val })\n    },\n    'formData.rules': function(val) {\n      if (val === '') val = null\n      this.updateProperties({ 'flowable:rules': val })\n    },\n    'formData.resultVariable': function(val) {\n      if (val === '') val = null\n      this.updateProperties({ 'flowable:resultVariable': val })\n    }\n  },\n  created() {\n    let cache = commonParse(this.element)\n    cache = userTaskParse(cache)\n    this.formData = cache\n    this.computedExecutionListenerLength()\n    this.computedTaskListenerLength()\n    this.computedHasMultiInstance()\n  },\n  methods: {\n    computedExecutionListenerLength() {\n      this.executionListenerLength = this.element.businessObject.extensionElements?.values\n        ?.filter(item => item.$type === 'flowable:ExecutionListener').length ?? 0\n    },\n    computedTaskListenerLength() {\n      this.taskListenerLength = this.element.businessObject.extensionElements?.values\n        ?.filter(item => item.$type === 'flowable:TaskListener').length ?? 0\n    },\n    computedHasMultiInstance() {\n      if (this.element.businessObject.loopCharacteristics) {\n        this.hasMultiInstance = true\n      } else {\n        this.hasMultiInstance = false\n      }\n    },\n    finishExecutionListener() {\n      if (this.dialogName === 'executionListenerDialog') {\n        this.computedExecutionListenerLength()\n      }\n      this.dialogName = ''\n    },\n    finishTaskListener() {\n      if (this.dialogName === 'taskListenerDialog') {\n        this.computedTaskListenerLength()\n      }\n      this.dialogName = ''\n    },\n    finishMultiInstance() {\n      if (this.dialogName === 'multiInstanceDialog') {\n        this.computedHasMultiInstance()\n      }\n      this.dialogName = ''\n    }\n  }\n}\n</script>\n\n<style></style>\n"]}]}