{"remainingRequest": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Process/components/nodePanel/property/listenerParam.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/develop/code/project/haijia/adminweb/src/components/Process/components/nodePanel/property/listenerParam.vue", "mtime": 1641277326000}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/babel-loader/lib/index.js", "mtime": 1751171659567}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/cache-loader/dist/cjs.js", "mtime": 1751171659106}, {"path": "/Users/<USER>/develop/code/project/haijia/adminweb/node_modules/vue-loader/lib/index.js", "mtime": 1751171659664}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["listenerParam.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAoBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "listenerParam.vue", "sourceRoot": "src/components/Process/components/nodePanel/property", "sourcesContent": ["<template>\n  <div>\n    <el-dialog\n      title=\"监听器参数\"\n      :visible.sync=\"dialogVisible\"\n      width=\"700px\"\n      :close-on-click-modal=\"false\"\n      :close-on-press-escape=\"false\"\n      :show-close=\"false\"\n      @closed=\"$emit('close', formData.paramList)\"\n    >\n      <x-form ref=\"xForm\" v-model=\"formData\" :config=\"formConfig\" />\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" size=\"medium\" @click=\"closeDialog\">确 定</el-button>\n      </span>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport mixinXcrud from '../../../common/mixinXcrud'\nexport default {\n  mixins: [mixinXcrud],\n  props: {\n    value: {\n      type: Array,\n      default: () => []\n    }\n  },\n  data() {\n    return {\n      dialogVisible: true,\n      formData: {\n        paramList: this.value\n      }\n    }\n  },\n  computed: {\n    formConfig() {\n      return {\n        inline: false,\n        item: [\n          {\n            xType: 'tabs',\n            tabs: [\n              {\n                label: '监听器参数',\n                name: 'paramList',\n                column: [\n                  {\n                    label: '类型',\n                    name: 'type',\n                    width: 180,\n                    rules: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],\n                    xType: 'select',\n                    dic: [\n                      { label: '字符串', value: 'stringValue' },\n                      { label: '表达式', value: 'expression' }\n                    ]\n                  },\n                  {\n                    label: '名称',\n                    name: 'name',\n                    width: 180,\n                    rules: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],\n                    xType: 'input'\n                  },\n                  {\n                    label: '值',\n                    name: 'value',\n                    xType: 'input',\n                    rules: [{ required: true, message: '请输入', trigger: ['blur', 'change'] }]\n                  }\n                ]\n              }\n            ]\n          }\n        ]\n      }\n    }\n  },\n  methods: {\n    closeDialog() {\n      this.$refs.xForm.validate().then(() => {\n        this.dialogVisible = false\n      }).catch(e => console.error(e))\n    }\n  }\n}\n</script>\n\n<style>\n.flow-containers  .el-badge__content.is-fixed {\n    top: 18px;\n}\n</style>\n"]}]}