<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span class="el-icon-document">项目报备流程</span>
        <span style="float: right;">
          <el-button icon="el-icon-edit-outline" type="success" v-if="audit" @click="handleComplete">审批</el-button>
          <el-button icon="el-icon-refresh-left" type="warning" v-if="audit" @click="handleReturn">退回</el-button>
          <el-button type="primary" @click="goBack">返回</el-button>
        </span>

      </div>
      <el-form ref="form" :model="form" :rules="rules" :disabled="!formEdit" label-width="120px">

        <!-- <el-row> -->
        <!-- <el-col :span="12">
            <el-form-item label="编辑状态">
              <el-radio-group v-model="form.editStatus">
                <el-radio
                  v-for="dict in editStatusOptions"
                  :key="dict.dictValue"
                  :label="dict.dictValue"
                  >{{ dict.dictLabel }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col> -->
        <!-- <el-col :span="12">
            <el-form-item label="所属用户" prop="belongUser">
              <el-select v-model="form.belongUser" placeholder="请选择所属用户">
                <el-option label="请选择字典生成" value="" />
              </el-select>
            </el-form-item>
          </el-col> -->
        <!-- </el-row> -->
        <!-- <el-row>
          <el-col :span="20">
            <el-form-item label="驳回原因" prop="rejectReason">
              <el-input
                v-model="form.rejectReason"
                type="textarea"
                placeholder="请输入内容"
              />
            </el-form-item>
          </el-col>
        </el-row> -->
        <el-row>
          <el-col :span="12">
            <el-form-item label="项目编号" prop="projectNo">
              <el-input v-model="form.projectNo" placeholder="若无编号则为当前时间(年月日时间)" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目名称" prop="projectName">
              <el-input v-model="form.projectName" placeholder="请输入项目名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="项目所在地" prop="province">
              <el-cascader :options="options" clearable :props="{ expandTrigger: 'hover' }" v-model="selectedOptions"
                @change="handleChange">
              </el-cascader>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="详细地址" prop="address">
              <el-input v-model="form.address" placeholder="请输入详细地址" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="项目所属省份" prop="belongProvince">
              <el-select v-model="form.belongProvince" clearable placeholder="请选择所属省份">
                <el-option v-for="item in belongProvinceOptions" :key="item.value" :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <!-- <el-form-item label="招标单位联系人/联系电话" prop="biddingContact">
              <el-input v-model="form.biddingContact" placeholder="请输入联系人/联系电话" />
            </el-form-item> -->
            <el-form-item label="招标单位" prop="biddingCompany">
              <el-input v-model="form.biddingCompany" placeholder="请输入招标单位" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="挂网日期" prop="hangDate">
              <el-input v-model="form.hangDate" placeholder="请输入挂网日期，没有请写无，日期格式XXXX（年）-XX（月）-XX（日" />
              <!-- <el-date-picker clearable size="small" v-model="form.hangDate" type="date" value-format="yyyy-MM-dd"
                placeholder="选择挂网日期">
              </el-date-picker> -->
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="开标日期" prop="openDate">
              <el-input v-model="form.openDate" placeholder="请输入开标日期，没有请写无，日期格式XXXX（年）-XX（月）-XX（日" />
              <!-- <el-date-picker clearable size="small" v-model="form.openDate" type="date" value-format="yyyy-MM-dd"
                placeholder="选择开标日期">
              </el-date-picker> -->
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="所属经销商" prop="distributor">
              <el-input v-model="form.distributor" placeholder="请输入经销商" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <!-- <el-form-item label="招标单位联系人/联系电话" prop="biddingContact">
              <el-input v-model="form.biddingContact" placeholder="请输入联系人/联系电话" />
            </el-form-item> -->
            <el-form-item label="售后年限" prop="afterSaleYear">
              <el-select v-model="form.afterSaleYear" clearable placeholder="请选择售后年限">
                <el-option v-for="item in afterSaleYearOptions" :key="item.value" :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <!-- <el-col :span="12">
            <el-form-item label="预算金额" prop="budgetMoney">
              <el-input
                type="number"
                v-model="form.budgetMoney"
                placeholder="请输入预算金额"
              />
            </el-form-item>
          </el-col> -->
          <el-col :span="12">
            <el-form-item label="被授权公司" prop="authCompany">
              <el-input v-model="form.authCompany" placeholder="请输入授权公司" />
              <el-link @click="addDomain" type="primary" :disabled="!formEdit">添加</el-link>
            </el-form-item>
            <el-form-item v-for="(company, index) in authCompanys" :label="'被授权公司' + (index + 1)" :key="company.key">
              <el-input v-model="company.value" :placeholder="'被授权公司' + (index + 1)" style="max-width:300px" />
              <el-link @click="removeDomain(index)" type="primary" :disabled="!formEdit">删除</el-link>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="授权公司联系人/联系电话" prop="authContact">
              <el-input v-model="form.authContact" placeholder="请输入授权公司联系人/联系电话" />
            </el-form-item>
          </el-col>
        </el-row>
        <!-- <el-row>
          <el-col :span="12">
            <el-form-item label="招标信息公布网站" prop="biddingNet">
              <el-input
                v-model="form.biddingNet"
                placeholder="请输入招标信息公布网站"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="招标单位" prop="biddingCompany">
              <el-input
                v-model="form.biddingCompany"
                placeholder="请输入招标单位"
              />
            </el-form-item>
          </el-col>
        </el-row> -->
        <!-- <el-row v-if="form.operationType == 2">
          <el-col :span="20">
            <el-form-item label="模板下载">
              <el-col :span="8">
                <el-link @click="downloadSQS" type="primary" :disabled="!formEdit">海佳集团-授权书.docx</el-link>
              </el-col>
              <el-col :span="8">
                <el-link @click="downloadCRH" type="primary" :disabled="!formEdit">海佳集团-售后服务承诺函.docx</el-link>
              </el-col>
            </el-form-item>
          </el-col>
        </el-row> -->
        <!-- <el-form-item label="授权书" v-if="form.operationType == 2" :required="form.operationType == 2">
          <fileUpload v-model="form.authFile" :fileType="['doc', 'docx']" />
        </el-form-item> -->
        <el-form-item label="其余附件">
          <span style="color: red;">请勿上传项目授权书、售后声明函</span>
          <fileUpload v-model="form.afterSaleFile" :fileType="['doc', 'docx']" />
        </el-form-item>
        <el-row>
          <el-col :span="20">
            <el-form-item label="投标产品型号" prop="model" :required="true">
              <el-cascader class="mobile-width" style="width: 700px" v-model="form.model" placeholder="可输入产品型号搜索"
                :options="modelOptions" :props="{ multiple: true }" clearable filterable></el-cascader>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="20">
            <el-form-item label="投标产品规格" prop="spec" :required="true">
              <el-cascader class="mobile-width" style="width: 700px" v-model="form.spec" placeholder="可输入产品规格搜索"
                :options="specOptions" :props="{ multiple: true }" clearable filterable></el-cascader>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="安装面积" prop="area">
              <el-input v-model="form.area" type="number" placeholder="请输入安装面积">
                <template slot="append">m²</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="20">
            <el-form-item label="所需资料">
              <el-cascader class="mobile-width" style="width: 700px" v-model="form.requireInfo" placeholder="可输入资料类型搜索"
                :options="requireInfoOptions" :props="{ multiple: true }" clearable filterable></el-cascader>
            </el-form-item>
          </el-col>
        </el-row>
        <!-- <el-row>
          <el-col :span="12">
            <el-form-item label="资料类型">
              <el-checkbox-group v-model="form.infoType">
                <el-checkbox
                  v-for="dict in infoTypeOptions"
                  :key="dict.dictValue"
                  :label="dict.dictValue"
                >
                  {{ dict.dictLabel }}
                </el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-col>
        </el-row> -->
        <el-row>
          <el-form-item label="资料接收方式" prop="infoType" :required="true">
            <el-checkbox-group v-model="form.infoType" class="info-type">
              <!-- 选项A -->
              <el-row style="display:flex;margin-bottom: 22px;">
                <el-col :span="12" style="display:flex;">
                  <el-checkbox label="1" style="margin-left:20px;margin-right:10px !important;">邮件</el-checkbox>
                  <el-form-item prop="scanFile">
                    <el-input v-model="form.scanFile" placeholder="请输入邮箱地址" style="width:300px;"
                      type="email"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="邮件发送信息" prop="mailInfo">
                    <el-input v-model="form.mailInfo" placeholder="请输入邮件发送信息" />
                  </el-form-item>
                </el-col>
              </el-row>
              <!-- 选项B  -->
              <el-row style="display:flex;margin-bottom: 22px;">
                <el-col :span="12" style="display:flex;">
                  <el-checkbox label="2" style="margin-left:20px;margin-right:10px !important;">邮寄</el-checkbox>
                  <el-form-item prop="sendAddress">
                    <el-input v-model="form.sendAddress" placeholder="请输入收件地址" style="width:300px;"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="快递单号" prop="expressInfo">
                    <el-input v-model="form.expressInfo" placeholder="请输入快递单号" />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-checkbox-group>
          </el-form-item>
        </el-row>
        <!-- <el-row>
          <el-col :span="12">
            <el-form-item label="邮件发送信息" prop="mailInfo">
              <el-input
                v-model="form.mailInfo"
                placeholder="请输入邮件发送信息"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="快递单号" prop="expressInfo">
              <el-input
                v-model="form.expressInfo"
                placeholder="请输入快递单号"
              />
            </el-form-item>
          </el-col>
        </el-row> -->
        <el-row>
          <el-col :span="12">
            <el-form-item label="操作类型" prop="operationType">
              <el-radio-group @change="handleOperationTypeChange" v-model="form.operationType">
                <el-radio v-for="dict in operationTypeOptions" :key="dict.dictValue"
                  :label="dict.dictValue">{{ dict.dictLabel }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="12">
            <el-form-item label="审核状态">
              <el-radio-group v-model="form.auditStatus">
                <el-radio
                  v-for="dict in auditStatusOptions"
                  :key="dict.dictValue"
                  :label="dict.dictValue"
                  >{{ dict.dictLabel }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col> -->
        </el-row>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <el-col :span="16" :offset="8" v-if="formEdit">
        <div style="margin-left:10%;margin-bottom: 20px;font-size: 14px;">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="goBack">取 消</el-button>
        </div>
      </el-col>
    </el-card>
    <flowable :key="businessKey" ref="flow" procDefKey="process_project_report" :procInsId="procInsId" :taskId="taskId"
      :finished="finished"></flowable>
  </div>
</template>

<script>
import {
  getReport,
  addReport,
  updateReport,
  checkNameUnique
} from "@/api/project/report";
import FileUpload from "@/components/FileUpload";
import flowable from '@/views/flowable/task/record/index'
import { regionData, CodeToText, TextToCode } from "element-china-area-data";
import print from "print-js";
export default {
  name: "Report",
  components: {
    flowable,
    FileUpload,
    print
  },
  data() {
    var that = this;
    var infoTypeValueVali = (rule, value, callback) => {
      if (that.form.infoType.indexOf('1') >= 0 && !that.form.scanFile) {
        callback(new Error("邮箱地址必填"));
        return;
      }
      callback();
    };
    var infoTypeValueVali2 = (rule, value, callback) => {
      if (that.form.infoType.indexOf('2') >= 0 && !that.form.sendAddress) {
        callback(new Error("收件地址必填"));
        return;
      }
      callback();
    };
    var nameVali = (rule, value, callback) => {
      if (!that.form.projectName) {
        callback(new Error("项目名称必填"));
      } else {
        if (/\s+/g.test(that.form.projectName)) {
          callback(new Error("项目名称不规范"));
          return;
        }
        checkNameUnique({ projectName: that.form.projectName, projectId: that.form.projectId }).then((response) => {
          if (response.data == 0) {
            callback();
          } else {
            callback(new Error("项目名称已存在"));
          }
        })
      }
    };
    var codeVali = (rule, value, callback) => {
      if (!that.form.projectNo) {
        callback(new Error("项目编号必填"));
      } else if (/\s+/g.test(that.form.projectNo)) {
        callback(new Error("项目编号不规范"));
        return;
      }
      callback();
    };
    var openDateVali = (rule, value, callback) => {
      if (!that.form.openDate) {
        callback(new Error("开标日期必填"));
        return;
      } else if (value === "无") {
        callback();
        return;
      } else if (!/^\d{4}-\d{2}-\d{2}$/.test(value)) {
        callback(new Error("开标日期格式不合法，示例2025-01-01"));
        return;
      }
      callback();
    };
    var hangDateVali = (rule, value, callback) => {
      if (!that.form.hangDate) {
        callback(new Error("挂网日期必填"));
        return;
      } else if (value === "无") {
        callback();
        return;
      } else if (!/^\d{4}-\d{2}-\d{2}$/.test(value)) {
        callback(new Error("挂网日期格式不合法，示例2025-01-01"));
        return;
      }
      callback();
    };
    return {
      // 遮罩层
      loading: true,
      // 操作类型字典
      operationTypeOptions: [],
      // 审核状态字典
      auditStatusOptions: [],
      // 编辑状态字典
      editStatusOptions: [],
      // 招标方式字典
      biddingTypeOptions: [],
      // 投标产品型号字典
      modelOptions: [],
      modelOption1: [],
      // 所需资料字典
      requireInfoOptions: [],
      requireInfoOption1: [],
      // 资料类型字典
      infoTypeOptions: [],
      // 所属省份字典
      belongProvinceOptions: [],
      belongProvinceOptions1: [],
      // 售后年限
      afterSaleYearOptions: [],
      afterSaleYearOptions1: [],
      specOptions: [],
      specOption1: [],
      // 表单参数
      form: {},
      // 表单校验
      // 表单校验
      rules: {
        operationType: [{ required: true, message: "操作类型必选" }],
        projectNo: [
          { validator: codeVali, required: true, trigger: "blur" },
        ],
        projectName: [
          { validator: nameVali, required: true, trigger: "blur" },
        ],
        address: [
          { required: true, message: "详细地址不能为空", trigger: "blur" },
        ],
        biddingCompany: [
          { required: true, message: "招标单位不能为空", trigger: "blur" },
        ],
        openDate: [
          { required: true, validator: openDateVali, trigger: "blur" },
        ],
        afterSaleYear: [
          { required: true, message: "售后年限不能为空", trigger: "blur" },
        ],
        hangDate: [
          { required: true, validator: hangDateVali, trigger: "blur" },
        ],
        belongProvince: [
          { required: true, message: "售后年限不能为空", trigger: "blur" },
        ],
        distributor: [
          { required: true, message: "所属经销商不能为空", trigger: "blur" },
        ],
        scanFile: [
          { validator: infoTypeValueVali, trigger: "blur" },
        ],
        sendAddress: [
          { validator: infoTypeValueVali2, trigger: "blur" },
        ],
        model: [
          { required: true, message: "投标产品型号必选" },
        ],
        spec: [
          { required: true, message: "投标产品规格必选" },
        ],
        province: [
          { required: true, message: "项目所在地必选" },
        ],
        infoType: [
          { required: true, message: "资料接收方式必选", trigger: "change" },
        ],
        biddingContact: [
          { required: true, message: "招标单位联系人/联系电话必填" },
        ],
        authContact: [
          { required: true, message: "授权公司联系人/联系电话必填" },
        ]
      },
      options: regionData,
      selectedOptions: [],
      queryArea: [],
      //工作流参数
      finished: 'false',
      taskId: undefined,
      procInsId: undefined,
      businessKey: undefined,
      audit: false,
      formEdit: false,
      //工作流参数end
      previousOperationType: null, // 存储切换前的操作类型
      authCompanys: [],
    };
  },
  activated() {

    this.reset();
    //this.form.projectNo = moment(new Date()).format('YYYYMMDDHHmm');
    this.taskId = this.$route.query && this.$route.query.taskId;
    this.procInsId = this.$route.query && this.$route.query.procInsId;
    this.finished = this.$route.query && this.$route.query.finished;
    this.businessKey = this.$route.query && this.$route.query.businessKey;
    let edit = this.$route.query && this.$route.query.formEdit;
    if (edit == "true") {
      this.formEdit = true;
    } else {
      this.formEdit = false;
    }
    if (this.businessKey) {
      if (this.finished == "false" && !this.formEdit) {
        this.audit = true;
      }
      this.getReportInfo(this.businessKey);
    }
    console.log("========project=========>activated>formEdit>>" + this.formEdit);
  },
  created() {

    this.reset();
    //this.form.projectNo = moment(new Date()).format('YYYYMMDDHHmm');
    this.taskId = this.$route.query && this.$route.query.taskId;
    this.procInsId = this.$route.query && this.$route.query.procInsId;
    this.finished = this.$route.query && this.$route.query.finished;
    this.businessKey = this.$route.query && this.$route.query.businessKey;
    let edit = this.$route.query && this.$route.query.formEdit;
    if (edit == "true") {
      this.formEdit = true;
    } else {
      this.formEdit = false;
    }
    if (this.businessKey) {
      if (this.finished == "false" && !this.formEdit) {
        this.audit = true;
      }
      this.getReportInfo(this.businessKey);
    }
    // this.audit = true;
    console.log("=========project========>created>>formEdit>" + this.formEdit);

    this.getDicts("pr_operation_type").then((response) => {
      this.operationTypeOptions = response.data;
    });
    this.getDicts("pr_audit_status").then((response) => {
      this.auditStatusOptions = response.data;
    });
    this.getDicts("pr_edit_status").then((response) => {
      this.editStatusOptions = response.data;
    });
    // this.getDicts("pr_bidding_type").then((response) => {
    //   this.biddingTypeOptions = response.data;
    // });
    this.getDicts("pr_model").then((response) => {
      this.modelOption1 = response.data;
      var opt = [];
      response.data.forEach((elem, index) => {
        var obj = {};
        obj.label = elem.dictLabel;
        obj.value = elem.dictValue;
        opt.push(obj);
      });
      this.modelOptions = opt;
    });
    this.getDicts("pr_spec").then((response) => {
      this.specOption1 = response.data;
      var opt = [];
      response.data.forEach((elem, index) => {
        var obj = {};
        obj.label = elem.dictLabel;
        obj.value = elem.dictValue;
        opt.push(obj);
      });
      this.specOptions = opt;
    });
    this.getDicts("pr_info").then((response) => {
      this.requireInfoOption1 = response.data;
      var opt = [];
      response.data.forEach((elem, index) => {
        var obj = {};
        obj.label = elem.dictLabel;
        obj.value = elem.dictValue;
        opt.push(obj);
      });
      this.requireInfoOptions = opt;
    });
    this.getDicts("pr_province").then((response) => {
      this.belongProvinceOptions1 = response.data;
      var opt = [];
      response.data.forEach((elem, index) => {
        var obj = {};
        obj.label = elem.dictLabel;
        obj.value = elem.dictValue;
        opt.push(obj);
      });
      this.belongProvinceOptions = opt;
    });
    this.getDicts("pr_after_sale_year").then((response) => {
      this.afterSaleYearOptions1 = response.data;
      var opt = [];
      response.data.forEach((elem, index) => {
        var obj = {};
        obj.label = elem.dictLabel;
        obj.value = elem.dictValue;
        opt.push(obj);
      });
      this.afterSaleYearOptions = opt;
    });
    this.getDicts("pr_data_type").then((response) => {
      this.infoTypeOptions = response.data;
    });
    //默认报备
    this.form.operationType = '1';
    this.previousOperationType = '1'; // 设置初始操作类型
  },
  methods: {

    // 表单重置
    reset() {
      this.form = {
        projectId: null,
        projectNo: null,
        projectName: null,
        operationType: 1,
        auditStatus: "1",
        rejectReason: null,
        province: null,
        city: null,
        district: null,
        address: null,
        editStatus: "0",
        belongUser: null,
        biddingCompany: null,
        openDate: null,
        belongProvince: null,
        afterSaleYear: null,
        hangDate: null,
        biddingType: null,
        budgetMoney: null,
        authCompany: null,
        biddingNet: null,
        distributor: null,
        model: [],
        spec: [],
        area: null,
        authFile: null,
        afterSaleFile: null,
        requireInfo: [],
        infoType: [],
        scanFile: null,
        sendAddress: null,
        mailInfo: null,
        expressInfo: null,
        remark: null,
        spare1: null,
        spare2: null,
      };
      this.previousOperationType = null; // 重置操作类型记录
      this.resetForm("form");
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.projectId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
      console.info(selection);
    },

    /** 修改按钮操作 */
    getReportInfo(projectId) {
      getReport(projectId).then((response) => {
        this.form = response.data;
        if (this.form.model) this.form.model = this.form.model.split(",");
        else this.form.model = [];
        if (this.form.requireInfo)
          this.form.requireInfo = this.form.requireInfo.split(",");
        else this.form.requireInfo = [];
        if (this.form.infoType)
          this.form.infoType = this.form.infoType.split(",");
        else this.form.infoType = [];
        if (this.form.spec) this.form.spec = this.form.spec.split(",");
        else this.form.spec = [];
        var provinces = response.data.province;
        if (provinces.length > 0) {
          var address = provinces.split("/");
          var citys = [];
          // 省份
          if (address.length > 0) citys.push(TextToCode[address[0]].code);
          // 城市
          if (address.length > 1)
            citys.push(TextToCode[address[0]][address[1]].code);
          // 地区
          if (address.length > 2)
            citys.push(TextToCode[address[0]][address[1]][address[2]].code);

          this.selectedOptions = citys;
        }

        // 设置初始操作类型
        this.previousOperationType = response.data.operationType;
      });
    },
    /** 提交按钮 */
    submitForm() {
      let that = this;
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.infoType.indexOf('1') >= 0 && this.form.scanFile) {
            let emailReg = /^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/;
            if (!emailReg.test(this.form.scanFile)) {
              this.$message.error("资料接收方式邮箱格式错误");
              return;
            }
          }
          // if (this.form.operationType == 2 && !this.form.authFile) {
          //   this.$message.error("授权类型必需上传授权书");
          //   return;
          // }
          var formStr = JSON.stringify(this.form);
          var formData = JSON.parse(formStr);
          if (formData.model && formData.model.length > 0)
            formData.model = formData.model.join(",");
          else formData.model = undefined;
          if (formData.requireInfo && formData.requireInfo.length > 0)
            formData.requireInfo = formData.requireInfo.join(",");
          else formData.requireInfo = undefined;
          if (formData.infoType && formData.infoType.length > 0)
            formData.infoType = formData.infoType.join(",");
          else formData.infoType = undefined;
          if (formData.spec && formData.spec.length > 0)
            formData.spec = formData.spec.join(",");
          else formData.spec = undefined;

          //授权公司
          if (this.authCompanys.length > 0) {
            var array = new Array();
            this.authCompanys.forEach(function (e) {
              array.push(e.value);
            })
            formData.authCompany += "," + array.join(",")
          }

          const loading = this.$loading({
            lock: true,//lock的修改符--默认是false
            text: 'Loading',//显示在加载图标下方的加载文案
            spinner: 'el-icon-loading',//自定义加载图标类名
            background: 'rgba(0, 0, 0, 0.7)',//遮罩层颜色
            target: document.querySelector('.main-container')//loadin覆盖的dom元素节点
          });
          document.documentElement.style.overflowY = 'hidden' //禁止底层div滚动

          if (formData.projectId != null) {
            updateReport(formData).then((response) => {
              //this.msgSuccess("修改成功");
              if (that.businessKey) {
                that.$refs['flow'].taskComplete("重新提交");
              } else {
                that.startFlow(formData);
              }
              setTimeout(() => {
                loading.close();
                document.documentElement.style.overflowY = 'auto' //允许底层div滚动
              }, 1000);
            }).catch(res => {
              console.log(res)
              loading.close();
              document.documentElement.style.overflowY = 'auto' //允许底层div滚动
            });
          } else {
            addReport(formData).then((response) => {
              console.log("===addReport=>>>")
              //this.msgSuccess("新增成功");
              formData.projectId = response.data;
              that.form.projectId = response.data;
              that.startFlow(formData);
              setTimeout(() => {
                loading.close();
                document.documentElement.style.overflowY = 'auto' //允许底层div滚动
              }, 1000);
            }).catch(res => {
              console.log(res)
              loading.close();
              document.documentElement.style.overflowY = 'auto' //允许底层div滚动
            });
          }
        }
      });
    },
    startFlow(formData) {
      //项目区域
      //var area = formData.district;
      //用户区域 6-11修改，根据用户所在区域判断
      var variables = {};
      variables.PROCESS_AREA = this.$store.state.user.province;
      //是否省负责人角色
      if (this.$store.state.user.roles && this.$store.state.user.roles.includes("province_admin")) {
        variables.isManage = 1;
      } else {
        variables.isManage = 0;
      }
      //新增全走报备审批流程
      // if(formData.operationType == '2'){
      //   variables.isAuth = true;
      // }else{
      //   variables.isAuth = false;
      // }
      //variables.isAuth = false;
      variables.BUSINESSKEY = formData.projectId;
      var taskName = "项目报备";
      if (formData.operationType == '2') {
        taskName = "项目授权";
      }
      this.$refs['flow'].startFlow(formData.projectId, taskName, variables);
    },
    downloadSQS() {
      this.download("海佳集团-授权书.docx", false);
    },
    downloadCRH() {
      this.download("海佳集团-售后服务承诺函.doc", false);
    },
    handleChange(value) {
      if (!value || value.length == 0) {
        this.selectedOptions = null;
        this.form.province = undefined;
        this.form.district = undefined;
        return
      }
      this.selectedOptions = value;
      var txt = "";
      value.forEach(function (item) {
        txt += CodeToText[item] + "/";
      });
      if (txt.length > 1) {
        txt = txt.substring(0, txt.length - 1);
        this.form.province = txt;
        this.form.district = this.$store.state.user.province;
      } else {
        this.form.province = undefined;
        this.form.district = undefined;
      }
    },
    handleQueryCityChange(value) {
      this.queryArea = value;
      var txt = "";
      value.forEach(function (item) {
        txt += CodeToText[item] + "/";
      });
      if (txt.length > 1) {
        txt = txt.substring(0, txt.length - 1);
        this.queryParams.province = txt;
      } else {
        this.queryParams.province = undefined;
      }
    },
    /** 审批 */
    handleComplete() {
      this.$refs['flow'].handleComplete();
    },
    /** 退回 */
    handleReturn() {
      this.$refs['flow'].handleReturn();
    },
    /** 返回页面 */
    goBack() {
      // 关闭当前标签页并返回上个页面
      this.$store.dispatch("tagsView/delView", this.$route);
      this.$router.go(-1)
    },
    removeDomain(index) {
      if (index !== -1) {
        this.authCompanys.splice(index, 1)
      }
    },
    addDomain() {
      this.authCompanys.push({
        value: '',
        key: Date.now()
      });
    },

    // 处理操作类型切换的确认
    handleOperationTypeChange(newValue) {
      // 如果是第一次设置或者值没有变化，直接设置
      if (this.previousOperationType === null || this.previousOperationType === newValue) {
        this.previousOperationType = newValue;
        return;
      }

      // 显示确认对话框
      this.$confirm('切换操作类型将可能影响表单数据，确定要切换吗？', '确认切换', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 用户确认切换
        this.previousOperationType = newValue;
        this.optTypeChange(newValue);
      }).catch(() => {
        // 用户取消切换，恢复到之前的值
        this.$nextTick(() => {
          this.form.operationType = this.previousOperationType;
        });
      });
    },

    optTypeChange(e) {
      console.log('操作类型已切换为:', e);
      // 这里可以添加切换后的逻辑处理
      // 比如清空某些字段、显示/隐藏某些表单项等
    }
  },
};
</script>
<style>
.box-card {
  width: 100%;
  margin-bottom: 20px;
}

@media screen and (max-width: 599px) {
  .el-form .el-col {
    width: 100% !important;
  }

  .info-type .el-row {
    flex-direction: column;
  }

  .info-type .el-input {
    width: 100% !important;
  }

  .mobile-width {
    width: 100% !important;
  }

  .app-container {
    padding: 0 !important;
  }
}
</style>