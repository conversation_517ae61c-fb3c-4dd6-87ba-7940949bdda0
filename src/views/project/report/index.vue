<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--部门数据-->
      <el-col :span="3" :xs="24">
        <div class="head-container">
          <el-tree :data="auditStatusTree" :props="defaultProps" :expand-on-click-node="false"
            :filter-node-method="filterNode" ref="tree" default-expand-all node-key="id" :current-node-key="9"
            @node-click="handleAuditNodeClick" />
          <el-tree :data="operationTypeTree" :props="defaultProps" :expand-on-click-node="false"
            :filter-node-method="filterNode" ref="tree" default-expand-all node-key="id" :current-node-key="0"
            @node-click="handleOptNodeClick" />
          <el-tree v-if="showUType" :data="userTypeTree" :props="defaultProps" :expand-on-click-node="false"
            :filter-node-method="filterNode" ref="tree" default-expand-all node-key="id" :current-node-key="0"
            @node-click="handleUserNodeClick" />
        </div>
      </el-col>
      <!--用户数据-->
      <el-col :span="21" :xs="24">
        <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="100px">
          <!-- <el-form-item label="项目所在地" label-width="100px" prop="area">
            <el-cascader
              size="small"
              clearable
              :options="options"
              :props="{ checkStrictly: true, expandTrigger: 'hover' }"
              v-model="queryArea"
              @change="handleQueryCityChange"
            />
          </el-form-item> -->
          <el-form-item label="所属用户" prop="belongUser">
            <el-input v-model="queryParams.belongUser" placeholder="请输入所属用户" clearable size="small"
              @keyup.enter.native="handleQuery" />
          </el-form-item>
          <!-- <el-form-item label="项目所在地" label-width="100px" prop="province">
            <el-select
              v-model="queryParams.province"
              placeholder="请选择项目所在地"
              clearable
              size="small"
            >
              <el-option
                v-for="dict in options"
                :key="dict.dictValue"
                :label="dict.label"
                :value="dict.label"
              />
            </el-select>
          </el-form-item> -->
          <el-form-item label="修改时间">
            <!-- <el-input
              v-model="queryParams.updateTime"
              placeholder="请输入搜索内容"
              clearable
              size="small"
              @keyup.enter.native="handleQuery"
            /> -->
            <!-- <el-date-picker v-model="queryParams.updateTime" value-format="yyyy-MM-dd" align="right" type="date"
              placeholder="选择日期" :picker-options="pickerOptions">
            </el-date-picker> -->
            <el-date-picker v-model="queryParams.updateTimeArr" value-format="yyyy-MM-dd" type="daterange" align="right"
              unlink-panels range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
              :picker-options="searchPickerOptions">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="搜索" prop="search">
            <el-select v-model="searchField" size="small" style="width: 120px; margin-right: 8px;">
              <el-option v-for="item in searchFieldOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
            <el-input v-model="searchValue"
              :placeholder="`请输入${searchField === 'all' ? '内容' : searchFieldOptions.find(f => f.value === searchField).label}`"
              clearable size="small" style="width: 200px" @keyup.enter.native="handleQuery" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
              v-hasPermi="['project:report:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
              v-hasPermi="['project:report:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
              v-hasPermi="['project:report:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="info" plain icon="el-icon-upload2" size="mini" @click="handleImport"
              v-hasPermi="['project:report:import']">导入</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="el-icon-download" size="mini" @click="clickExport"
              v-hasPermi="['project:report:export']">导出</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button icon="el-icon-printer" size="mini" @click="clickPrint" type="info"
              v-hasPermi="['project:report:print']" plain>
              打印
            </el-button>
          </el-col>
          <right-toolbar :showSearch.sync="showSearch" :showExport.sync="showExport" :showPrint.sync="showPrint"
            @queryTable="getList" @export="handleExport" @print="handlePrint" :columns="columns"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" border :data="reportList" @selection-change="handleSelectionChange"
          id="printArea">
          <el-table-column type="selection" min-width="55" align="center" />
          <el-table-column prop="projectId" label="项目ID" min-width="80" />
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width" min-width="120">
            <template slot-scope="scope">
              <el-button size="mini" type="text" icon="el-icon-view" @click="handleView(scope.row)">查看</el-button>
              <!-- <el-button v-has="[scope.row, 'auth']"
                size="mini"
                type="text"
                icon="el-icon-edit-outline"
                @click="handleAuth(scope.row)"
                >授权</el-button> -->
              <!-- <el-dropdown v-has="[scope.row, 'edit']">
                <span class="el-dropdown-link">
                  &nbsp;&nbsp;<i class="el-icon-arrow-down el-icon--right"></i>
                </span>
                <el-dropdown-menu slot="dropdown">

                  <el-dropdown-item icon="el-icon-edit" @click.native="handleUpdate(scope.row)" v-hasPermi="['project:report:edit']">
                    修改
                  </el-dropdown-item>
                  <el-dropdown-item icon="el-icon-delete" @click.native="handleDelete(scope.row)" v-hasPermi="['project:report:remove']">
                    删除
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown> -->
            </template>
          </el-table-column>
          <el-table-column label="所属用户" min-width="150" align="center" prop="belongUser" show-overflow-tooltip
            v-if="columns['0'].visible">
            <template slot-scope="scope">
              <span @click="userSearch(scope.row.belongUser)" class="link-type">{{ scope.row.belongUser }}</span>
            </template>
          </el-table-column>
          <el-table-column label="项目编号" min-width="150" align="center" prop="projectNo" show-overflow-tooltip
            v-if="columns['1'].visible">
            <template slot-scope="scope">
              <span v-html="highlightCell('projectNo', scope.row.projectNo)"></span>
            </template>
          </el-table-column>
          <el-table-column label="项目名称" min-width="300" align="center" prop="projectName" :formatter="searchFormat"
            show-overflow-tooltip v-if="columns['2'].visible">
            <template slot-scope="scope">
              <span v-html="highlightCell('projectName', scope.row.projectName)"></span>
            </template>
          </el-table-column>
          <el-table-column label="操作类型" align="center" prop="operationType" :formatter="operationTypeFormat"
            v-if="columns['3'].visible" min-width="100">
          </el-table-column>
          <!-- <el-table-column
            label="审核状态"
            align="center"
            prop="auditStatus"
            :formatter="auditStatusFormat"
            v-if="columns['3'].visible"
          />
          <el-table-column
            label="编辑状态"
            align="center"
            prop="editStatus"
            :formatter="editStatusFormat"
            v-if="columns['5'].visible"
          /> -->
          <el-table-column min-width="200" label="项目所在地" align="center" prop="province" v-if="columns['4'].visible" />
          <el-table-column min-width="200" label="详细地址" align="center" prop="address" show-overflow-tooltip
            v-if="columns['5'].visible">
            <template slot-scope="scope">
              <span v-html="highlightCell('address', scope.row.address)"></span>
            </template>
          </el-table-column>
          <el-table-column min-width="200" label="被授权公司" align="center" prop="authCompany" show-overflow-tooltip
            v-if="columns['6'].visible">
            <template slot-scope="scope">
              <span v-html="highlightCell('authCompany', scope.row.authCompany)"></span>
            </template>
          </el-table-column>
          <el-table-column min-width="200" label="所属经销商" align="center" prop="distributor" show-overflow-tooltip
            v-if="columns['7'].visible" />
          <el-table-column min-width="200" label="招标单位" align="center" prop="biddingCompany" show-overflow-tooltip
            v-if="columns['8'].visible">
            <template slot-scope="scope">
              <span v-html="highlightCell('biddingCompany', scope.row.biddingCompany)"></span>
            </template>
          </el-table-column>
          <el-table-column min-width="150" label="投标产品型号" align="center" prop="model" :formatter="modelFormat"
            show-overflow-tooltip v-if="columns['9'].visible" />
          <el-table-column min-width="150" label="投标产品规格" align="center" prop="spec" :formatter="specFormat"
            v-if="columns['10'].visible" show-overflow-tooltip />
          <el-table-column min-width="150" label="安装面积(m²)" align="center" prop="area" show-overflow-tooltip
            v-if="columns['11'].visible" />
          <el-table-column min-width="100" label="所需资料" align="center" prop="requireInfo" :formatter="requireInfoFormat"
            show-overflow-tooltip v-if="columns['12'].visible" />
          <el-table-column label="资料类型" align="center" prop="infoType" :formatter="infoTypeFormat"
            v-if="columns['13'].visible" />
          <el-table-column min-width="150" label="资料接收邮件" align="center" prop="scanFile" show-overflow-tooltip
            v-if="columns['14'].visible" />
          <el-table-column min-width="150" label="资料接收地址" align="center" prop="sendAddress" show-overflow-tooltip
            v-if="columns['15'].visible" />
          <el-table-column min-width="150" label="项目所属省份" align="center" prop="belongProvince"
            :formatter="belongProvinceFormat" show-overflow-tooltip v-if="columns['16'].visible" />
          <el-table-column min-width="150" label="售后年限" align="center" prop="afterSaleYear"
            :formatter="afterSaleYearFormat" show-overflow-tooltip v-if="columns['17'].visible" />
          <el-table-column min-width="150" label="开标日期" align="center" prop="openDate" show-overflow-tooltip
            v-if="columns['18'].visible" />
          <el-table-column min-width="150" label="挂网日期" align="center" prop="hangDate" show-overflow-tooltip
            v-if="columns['19'].visible" />
          <el-table-column min-width="110" label="提交时间" align="center" prop="createTime" v-if="columns['20'].visible"
            show-overflow-tooltip />
          <el-table-column min-width="110" label="修改时间" align="center" prop="updateTime" v-if="columns['21'].visible"
            show-overflow-tooltip />
        </el-table>

        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize" :layout="pageLayout" @pagination="getList" />

        <!-- 添加或修改项目报备对话框 -->
        <el-dialog :title="title" :visible.sync="open" :close-on-click-modal="false" width="80%"
          custom-class="edit-dialog" append-to-body>
          <el-form ref="form" :model="form" :rules="rules" label-width="120px">
            <el-row>
              <el-col :span="12">
                <el-form-item label="编辑状态">
                  <el-radio-group :disabled="isAdmin" v-model="form.editStatus">
                    <el-radio v-for="dict in editStatusOptions" :key="dict.dictValue"
                      :label="dict.dictValue">{{ dict.dictLabel }}</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <!-- <el-col :span="12">
                <el-form-item label="所属用户" prop="belongUser">
                  <el-select v-model="form.belongUser" placeholder="请选择所属用户">
                    <el-option label="请选择字典生成" value="" />
                  </el-select>
                </el-form-item>
              </el-col> -->
            </el-row>
            <!-- <el-row>
              <el-col :span="20">
                <el-form-item label="驳回原因" prop="rejectReason">
                  <el-input
                    v-model="form.rejectReason"
                    type="textarea"
                    placeholder="请输入内容"
                  />
                </el-form-item>
              </el-col>
            </el-row> -->
            <el-row>
              <el-col :span="12">
                <el-form-item label="项目编号" prop="projectNo">
                  <el-input v-model="form.projectNo" placeholder="无编号则为提交时间(年月日时间)" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="项目名称" prop="projectName">
                  <el-input v-model="form.projectName" placeholder="请输入项目名称" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="项目所在地" prop="province">
                  <el-cascader ref="cascader" :options="options" clearable :props="{ expandTrigger: 'hover' }"
                    v-model="selectedOptions" @change="handleChange">
                  </el-cascader>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="详细地址" prop="address">
                  <el-input v-model="form.address" placeholder="请输入详细地址" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="项目所属省份" prop="belongProvince">
                  <el-select v-model="form.belongProvince" clearable placeholder="请选择所属省份">
                    <el-option v-for="item in belongProvinceOptions" :key="item.value" :label="item.label"
                      :value="item.value">
                    </el-option>
                  </el-select>
                </el-form-item>

              </el-col>
              <el-col :span="12">
                <el-form-item label="招标单位" prop="biddingCompany">
                  <el-input v-model="form.biddingCompany" placeholder="请输入招标单位" />
                </el-form-item>
                <!-- <el-form-item label="招标单位联系人/联系电话" prop="biddingContact">
                  <el-input v-model="form.biddingContact" placeholder="请输入联系人/联系电话" />
                </el-form-item> -->
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="挂网日期" prop="hangDate">
                  <el-input v-model="form.hangDate" placeholder="请输入挂网日期，没有请写无，日期格式XXXX（年）-XX（月）-XX（日)" />
                  <!-- <el-date-picker clearable size="small" v-model="form.hangDate" type="date" value-format="yyyy-MM-dd"
                    placeholder="选择挂网日期">
                  </el-date-picker> -->
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="开标日期" prop="openDate">
                  <el-input v-model="form.openDate" placeholder="请输入开标日期，没有请写无，日期格式XXXX（年）-XX（月）-XX（日" />
                  <!-- <el-date-picker clearable size="small" v-model="form.openDate" type="date" value-format="yyyy-MM-dd"
                    placeholder="选择开标日期">
                  </el-date-picker> -->
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="所属经销商" prop="distributor">
                  <el-input v-model="form.distributor" placeholder="请输入经销商" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <!-- <el-form-item label="招标单位联系人/联系电话" prop="biddingContact">
              <el-input v-model="form.biddingContact" placeholder="请输入联系人/联系电话" />
            </el-form-item> -->
                <el-form-item label="售后年限" prop="afterSaleYear">
                  <el-select v-model="form.afterSaleYear" clearable placeholder="请选择所属省份">
                    <el-option v-for="item in afterSaleYearOptions" :key="item.value" :label="item.label"
                      :value="item.value">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <!-- <el-row>
              <el-col :span="12">

              </el-col>
            </el-row> -->
            <!-- <el-row>
              <el-col :span="12">
                <el-form-item label="开标日期" prop="openDate">
                  <el-date-picker
                    clearable
                    size="small"
                    v-model="form.openDate"
                    type="date"
                    value-format="yyyy-MM-dd"
                    placeholder="选择开标日期"
                  >
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="招标方式" prop="biddingType">
                  <el-select
                    v-model="form.biddingType"
                    placeholder="请选择招标方式"
                  >
                    <el-option
                      v-for="dict in biddingTypeOptions"
                      :key="dict.dictValue"
                      :label="dict.dictLabel"
                      :value="dict.dictValue"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row> -->
            <el-row>
              <!-- <el-col :span="12">
                <el-form-item label="预算金额" prop="budgetMoney">
                  <el-input
                    type="number"
                    v-model="form.budgetMoney"
                    placeholder="请输入预算金额"
                  />
                </el-form-item>
              </el-col> -->
              <el-col :span="12">
                <el-form-item label="被授权公司" prop="authCompany">
                  <el-input v-model="form.authCompany" placeholder="请输入授权公司" />
                  <el-link @click="addDomain" type="primary">添加</el-link>
                </el-form-item>
                <el-form-item v-for="(company, index) in authCompanys" :label="'被授权公司' + (index + 1)" :key="company.key"
                  class="info-type">
                  <el-input v-model="company.value" :placeholder="'被授权公司' + (index + 1)" style="max-width: 300px" />
                  <el-link @click="removeDomain(index)" type="primary">删除</el-link>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="授权公司联系人/联系电话" prop="authContact">
                  <el-input v-model="form.authContact" placeholder="请输入授权公司联系人/联系电话" />
                </el-form-item>
              </el-col>
            </el-row>
            <!-- <el-row>
              <el-col :span="12">
                <el-form-item label="招标信息公布网站" prop="biddingNet">
                  <el-input
                    v-model="form.biddingNet"
                    placeholder="请输入招标信息公布网站"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="招标单位" prop="biddingCompany">
                  <el-input
                    v-model="form.biddingCompany"
                    placeholder="请输入招标单位"
                  />
                </el-form-item>
              </el-col>
            </el-row> -->
            <!-- <el-row v-if="form.operationType == 2">
              <el-col :span="20">
                <el-form-item label="模板下载">
                  <el-col :span="8">
                    <el-link @click="downloadSQS" type="primary">海佳集团-授权书.docx</el-link>
                  </el-col>
                  <el-col :span="8">
                    <el-link @click="downloadCRH" type="primary">海佳集团-售后服务承诺函.docx</el-link>
                  </el-col>
                </el-form-item>
              </el-col>
            </el-row> -->
            <!-- <el-form-item label="授权书" v-if="form.operationType == 2" :required="form.operationType == 2">
              <fileUpload v-model="form.authFile" :fileType="['doc', 'docx']" />
            </el-form-item> -->
            <el-form-item label="其余附件">
              <span style="color: red;">请勿上传项目授权书、售后声明函</span>
              <fileUpload v-model="form.afterSaleFile" :fileType="['doc', 'docx']" />
            </el-form-item>
            <el-row>
              <el-col :span="20">
                <el-form-item label="投标产品型号" prop="model" :required="true">
                  <el-cascader class="mobile-width" style="width: 700px" v-model="form.model" placeholder="请输入产品型号"
                    :options="modelOptions" :props="{ multiple: true }" clearable filterable></el-cascader>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="20">
                <el-form-item label="投标产品规格" prop="spec" :required="true">
                  <el-cascader class="mobile-width" style="width: 700px" v-model="form.spec" placeholder="请输入产品规格"
                    :options="specOptions" :props="{ multiple: true }" clearable filterable></el-cascader>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="安装面积" prop="area">
                  <el-input v-model="form.area" type="number" placeholder="请输入安装面积">
                    <template slot="append">m²</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="20">
                <el-form-item label="所需资料">
                  <el-cascader class="mobile-width" style="width: 700px" v-model="form.requireInfo"
                    placeholder="请输入资料类型" :options="requireInfoOptions" :props="{ multiple: true }" clearable
                    filterable></el-cascader>
                </el-form-item>
              </el-col>
            </el-row>
            <!-- <el-row>
              <el-col :span="12">
                <el-form-item label="资料类型">
                  <el-checkbox-group v-model="form.infoType">
                    <el-checkbox
                      v-for="dict in infoTypeOptions"
                      :key="dict.dictValue"
                      :label="dict.dictValue"
                    >
                      {{ dict.dictLabel }}
                    </el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
              </el-col>
            </el-row> -->
            <!-- <el-row>
              <el-form-item label="资料接收方式" prop="infoType" :required="true">
                <el-checkbox-group v-model="form.infoType" class="info-type" style="display:flex;">
                
                  <el-checkbox label="1" style="margin-left:20px;margin-right:10px !important;">邮件</el-checkbox>
                  <el-form-item prop="scanFile">
                    <el-input v-model="form.scanFile" placeholder="请输入邮箱地址" type="email" style="width:300px;" ></el-input>
                  </el-form-item>
                
                  <el-checkbox label="2" style="margin-left:20px;margin-right:10px !important;">邮寄</el-checkbox>
                  <el-form-item prop="sendAddress">
                    <el-input v-model="form.sendAddress" placeholder="请输入收件地址" style="width:300px;" ></el-input>
                  </el-form-item>
                </el-checkbox-group>
              </el-form-item>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="邮件发送信息" prop="mailInfo">
                  <el-input
                    v-model="form.mailInfo"
                    placeholder="请输入邮件发送信息"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="快递单号" prop="expressInfo">
                  <el-input
                    v-model="form.expressInfo"
                    placeholder="请输入快递单号"
                  />
                </el-form-item>
              </el-col>
            </el-row> -->
            <el-row>
              <el-form-item label="资料接收方式" prop="infoType" :required="true">
                <el-checkbox-group v-model="form.infoType" class="info-type">
                  <!-- 选项A -->
                  <el-row style="display: flex; margin-bottom: 22px">
                    <el-col :span="12" style="display: flex">
                      <el-checkbox label="1" style="margin-left: 20px; margin-right: 10px !important">邮件</el-checkbox>
                      <el-form-item prop="scanFile">
                        <el-input v-model="form.scanFile" placeholder="请输入邮箱地址" style="width: 300px"
                          type="email"></el-input>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="邮件发送信息" prop="mailInfo">
                        <el-input v-model="form.mailInfo" placeholder="请输入邮件发送信息" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <!-- 选项B  -->
                  <el-row style="display: flex; margin-bottom: 22px">
                    <el-col :span="12" style="display: flex">
                      <el-checkbox label="2" style="margin-left: 20px; margin-right: 10px !important">邮寄</el-checkbox>
                      <el-form-item prop="sendAddress">
                        <el-input v-model="form.sendAddress" placeholder="请输入收件地址" style="width: 300px"></el-input>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="快递单号" prop="expressInfo">
                        <el-input v-model="form.expressInfo" placeholder="请输入快递单号" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-checkbox-group>
              </el-form-item>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="操作类型" prop="operationType">
                  <el-radio-group @change="handleOperationTypeChange" v-model="form.operationType">
                    <el-radio v-for="dict in operationTypeOptions" :key="dict.dictValue"
                      :label="dict.dictValue">{{ dict.dictLabel }}</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="审核状态">
                  <el-radio-group :disabled="auditStatusEdit" v-model="form.auditStatus">
                    <el-radio v-for="dict in auditStatusOptions" :key="dict.dictValue"
                      :label="dict.dictValue">{{ dict.dictLabel }}</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item label="备注" prop="remark">
              <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitForm">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
          </div>
        </el-dialog>
        <!-- 查看项目报备对话框 -->
        <el-dialog :title="title" :visible.sync="viewOpen" :fullscreen="true" :lock-scroll="true"
          :destroy-on-close="true" custom-class="view-dialog" @close="viewOk">
          <flowable v-if="viewOpen" ref="flow" :procDefKey="defKey" :procInsId="procInsId" :taskId="taskId"
            :bizKey="bizKey" :finished="finished" :isAuthImages="isAuthImages">
            <template v-slot:title>项目信息</template>
            <template v-slot:content>
              <el-descriptions label-width="120px" :column="isMobile ? 1 : 3">
                <el-descriptions-item label="操作类型" prop="operationType">
                  {{ view.operationType }}
                </el-descriptions-item>
                <el-descriptions-item label="审核状态">
                  {{ view.auditStatus }}
                </el-descriptions-item>
                <!-- <el-descriptions-item label="驳回原因" prop="rejectReason">
                {{ view.rejectReason }}
              </el-descriptions-item> -->
                <el-descriptions-item label="项目ID">
                  {{ view.projectId }}
                </el-descriptions-item>
                <el-descriptions-item label="项目编号" prop="projectNo">
                  {{ view.projectNo }}
                </el-descriptions-item>
                <el-descriptions-item label="项目名称" span="3" prop="projectName">
                  <el-popover v-if="likecount > 0" placement="top-start" title="相似项目" width="450" trigger="hover">
                    <el-table :data="likeList">
                      <el-table-column width="100" property="value" label="项目ID"></el-table-column>
                      <el-table-column width="300" property="name" label="项目名称" show-overflow-tooltip></el-table-column>
                    </el-table>
                    <el-badge slot="reference" :value="likecount" class="item">
                      {{ view.projectName
                      }}<span class="likeTip">&nbsp;&nbsp;存在相似项目</span>
                    </el-badge>
                  </el-popover>
                  <span v-if="!likecount">{{ view.projectName }}</span>
                </el-descriptions-item>
                <el-descriptions-item label="编辑状态">
                  {{ view.editStatus }}
                </el-descriptions-item>
                <el-descriptions-item label="项目所在地" prop="area">
                  {{ view.province }}
                </el-descriptions-item>
                <el-descriptions-item label="详细地址">
                  {{ view.address }}
                </el-descriptions-item>
                <el-descriptions-item label="安装面积(m²)">
                  {{ view.area }}
                </el-descriptions-item>
                <el-descriptions-item label="被授权公司">
                  {{ view.authCompany }}
                </el-descriptions-item>
                <el-descriptions-item label="授权公司联系人/联系电话">
                  {{ view.authContact }}
                </el-descriptions-item>
                <!-- <el-descriptions-item label="招标单位联系人/联系电话">
                  {{ view.biddingContact }}
                </el-descriptions-item> -->
                <el-descriptions-item label="项目所属省份" prop="belongProvince">
                  {{ view.belongProvince }}
                </el-descriptions-item>
                <el-descriptions-item label="招标单位">
                  {{ view.biddingCompany }}
                </el-descriptions-item>
                <el-descriptions-item label="挂网日期">
                  {{ view.hangDate }}
                </el-descriptions-item>
                <el-descriptions-item label="开标日期">
                  {{ view.openDate }}
                </el-descriptions-item>
                <el-descriptions-item label="所属经销商">
                  {{ view.distributor }}
                </el-descriptions-item>
                <el-descriptions-item label="售后年限" prop="afterSaleYear">
                  {{ view.afterSaleYear }}
                </el-descriptions-item>
                <el-descriptions-item label="所需资料" span="3">
                  <template v-if="infoList1.length > 0">
                    <ul class="infinite-list" v-infinite-scroll="load" style="overflow: auto">
                      <li v-for="(i, index) in infoList1" v-bind:key="index" class="infinite-list-item">
                        {{ i.dictLabel }}
                        <el-link v-if="i.targetUrl && view.auditStatus == '已审批'"
                          @click.prevent="handleDownload(i.targetUrl)" type="primary">下载</el-link>
                      </li>
                    </ul>
                    <ul class="infinite-list" v-infinite-scroll="load" style="overflow: auto">
                      <li v-for="(i, index) in infoList2" v-bind:key="index" class="infinite-list-item">
                        {{ i.dictLabel }}
                        <el-link target="_blank" v-if="i.targetUrl && view.auditStatus == '已审批'"
                          @click.prevent="handleDownload(i.targetUrl)" type="primary">下载</el-link>
                      </li>
                    </ul>
                  </template>
                  <span v-else>{{ view.requireInfo }}</span>
                </el-descriptions-item>
                <el-descriptions-item label="投标产品型号">
                  {{ view.model }}
                </el-descriptions-item>
                <el-descriptions-item label="投标产品规格">
                  {{ view.spec }}
                </el-descriptions-item>
                <!-- <el-descriptions-item label="资料类型">
                {{ view.infoType }}
              </el-descriptions-item> -->
                <el-descriptions-item label="授权书" v-if="view.operationType == '授权'">
                  <el-link target="_blank" v-if="view.authFile" :href="view.authFile" type="primary">下载</el-link>
                </el-descriptions-item>
                <el-descriptions-item label="其余附件" v-if="view.operationType == '授权'">
                  <el-link target="_blank" v-if="view.afterSaleFile" :href="view.afterSaleFile"
                    type="primary">下载</el-link>
                </el-descriptions-item>

                <el-descriptions-item label="授权书图片" v-if="view.operationType == '授权'">
                  <el-link target="_blank" v-if="view.authImages && view.auditStatus === '已审批'" :href="view.authImages"
                    type="primary">下载</el-link>
                </el-descriptions-item>
                <el-descriptions-item label="资料接收邮件" prop="scanFile">
                  {{ view.scanFile }}
                </el-descriptions-item>
                <el-descriptions-item label="资料接收地址" prop="sendAddress">
                  {{ view.sendAddress }}
                </el-descriptions-item>
                <el-descriptions-item label="邮件发送信息" prop="mailInfo">
                  {{ view.mailInfo }}
                </el-descriptions-item>
                <el-descriptions-item label="快递单号" prop="expressInfo">
                  {{ view.expressInfo }}
                </el-descriptions-item>
                <el-descriptions-item label="备注" prop="remark">
                  {{ view.remark }}
                </el-descriptions-item>
                <el-descriptions-item label="提交时间">
                  {{ view.createTime }}
                </el-descriptions-item>
                <el-descriptions-item label="修改时间">
                  {{ view.updateTime }}
                </el-descriptions-item>
              </el-descriptions>
            </template>
          </flowable>
        </el-dialog>
        <!-- 项目导入对话框 -->
        <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
          <el-upload ref="upload" :limit="1" accept=".xlsx, .xls" :headers="upload.headers"
            :action="upload.url + '?updateSupport=' + upload.updateSupport" :disabled="upload.isUploading"
            :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" :auto-upload="false" drag>
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">
              将文件拖到此处，或
              <em>点击上传</em>
            </div>
            <div class="el-upload__tip" slot="tip">
              <el-checkbox v-model="upload.updateSupport" />是否更新已经存在的项目数据
              <el-link type="info" style="font-size: 12px" @click="importTemplate">下载模板</el-link>
            </div>
            <div class="el-upload__tip" style="color: red" slot="tip">
              提示：仅允许导入"xls"或"xlsx"格式文件！
            </div>
          </el-upload>
          <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitFileForm">确 定</el-button>
            <el-button @click="upload.open = false">取 消</el-button>
          </div>
        </el-dialog>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import {
  listReport,
  getReport,
  delReport,
  addReport,
  updateReport,
  exportReport,
  importTemplate,
  printReport,
  checkNameUnique,
  getLikeList,
  authReport,
} from "@/api/project/report";
import { getToken } from "@/utils/auth";
import FileUpload from "@/components/FileUpload";
import flowable from "@/views/flowable/task/record/view";
import { getInsIdByBizKey } from "@/api/flowable/todo";
import { regionData, CodeToText, TextToCode } from "element-china-area-data";
import print from "print-js";
export default {
  name: "Report",
  components: {
    FileUpload,
    print,
    flowable,
  },
  data() {
    var infoTypeValueVali = (rule, value, callback) => {
      if (this.form.infoType.indexOf("1") >= 0 && !this.form.scanFile) {
        callback(new Error("邮箱地址必填"));
        return;
      }
      callback();
    };
    var infoTypeValueVali2 = (rule, value, callback) => {
      if (this.form.infoType.indexOf("2") >= 0 && !this.form.sendAddress) {
        callback(new Error("收件地址必填"));
        return;
      }
      callback();
    };
    var nameVali = (rule, value, callback) => {
      if (!this.form.projectName) {
        callback(new Error("项目名称必填"));
      } else {
        if (/\s+/g.test(this.form.projectName)) {
          callback(new Error("项目名称不规范"));
          return;
        }
        checkNameUnique({
          projectName: this.form.projectName,
          projectId: this.form.projectId,
        }).then((response) => {
          if (response.data == 0) {
            callback();
          } else {
            callback(new Error("项目名称已存在"));
          }
        });
      }
    };
    var codeVali = (rule, value, callback) => {
      if (!this.form.projectNo) {
        callback(new Error("项目编号必填"));
      } else if (/\s+/g.test(this.form.projectNo)) {
        callback(new Error("项目编号不规范"));
        return;
      }
      callback();
    };
    var authFileValueVali = (rule, value, callback) => {
      if (this.form.operationType == 2 && !this.form.authFile) {
        callback(new Error("授权类型必传授权书"));
      }
      callback();
    };
    var openDateVali = (rule, value, callback) => {
      if (!this.form.openDate) {
        callback(new Error("开标日期必填"));
        return;
      } else if (value === "无") {
        callback();
        return;
      } else if (!/^\d{4}-\d{2}-\d{2}$/.test(value)) {
        callback(new Error("开标日期格式不合法，示例2025-01-01"));
        return;
      }
      callback();
    };
    var hangDateVali = (rule, value, callback) => {
      if (!this.form.hangDate) {
        callback(new Error("挂网日期必填"));
        return;
      } else if (value === "无") {
        callback();
        return;
      } else if (!/^\d{4}-\d{2}-\d{2}$/.test(value)) {
        callback(new Error("挂网日期格式不合法，示例2025-01-01"));
        return;
      }
      callback();
    };
    return {
      isMobile: false,
      pageLayout: "total, sizes, prev, pager, next, jumper",
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      showExport: false,
      showPrint: false,
      // 总条数
      total: 0,
      // 项目报备表格数据
      reportList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 操作类型字典
      operationTypeOptions: [],
      // 审核状态字典
      auditStatusOptions: [],
      // 编辑状态字典
      editStatusOptions: [],
      // 招标方式字典
      biddingTypeOptions: [],
      // 投标产品型号字典
      modelOptions: [],
      modelOption1: [],
      // 所需资料字典
      requireInfoOptions: [],
      requireInfoOption1: [],
      // 资料类型字典
      infoTypeOptions: [],
      specOptions: [],
      specOption1: [],
      // 所属省份字典
      belongProvinceOptions: [],
      belongProvinceOptions1: [],
      // 售后年限
      afterSaleYearOptions: [],
      afterSaleYearOptions1: [],
      // 项目导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/project/report/importData",
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectNo: null,
        projectName: null,
        operationType: null,
        auditStatus: null,
        province: null,
        userType: null,
        belongUser: null,
        updateTimeArr: [],
        spare1: null,
        address: null,
        biddingCompany: null,
        authCompany: null,
        fullField: null
      },
      // 表单参数
      form: {},
      // 表单校验
      // 表单校验
      rules: {
        operationType: [{ required: true, message: "操作类型必选" }],
        projectNo: [{ required: true, validator: codeVali, trigger: "blur" }],
        projectName: [{ required: true, validator: nameVali, trigger: "blur" }],
        address: [
          { required: true, message: "详细地址不能为空", trigger: "blur" },
        ],
        biddingCompany: [
          { required: true, message: "招标单位不能为空", trigger: "blur" },
        ],
        openDate: [
          { required: true, validator: openDateVali, trigger: "blur" },
        ],
        afterSaleYear: [
          { required: true, message: "售后年限不能为空", trigger: "blur" },
        ],
        hangDate: [
          { required: true, validator: hangDateVali, trigger: "blur" },
        ],
        belongProvince: [
          { required: true, message: "售后年限不能为空", trigger: "blur" },
        ],
        distributor: [
          { required: true, message: "所属经销商不能为空", trigger: "blur" },
        ],
        scanFile: [{ validator: infoTypeValueVali, trigger: "blur" }],
        sendAddress: [{ validator: infoTypeValueVali2, trigger: "blur" }],
        model: [{ required: true, message: "投标产品型号必选" }],
        spec: [{ required: true, message: "投标产品规格必选" }],
        province: [{ required: true, message: "项目所在地必选" }],
        infoType: [
          { required: true, message: "资料接收方式必选", trigger: "change" },
        ],
        authFile: [{ validator: authFileValueVali, trigger: "change" }],
        biddingContact: [
          { required: true, message: "招标单位联系人/联系电话必填" },
        ],
        authContact: [
          { required: true, message: "授权公司联系人/联系电话必填" },
        ],
      },
      // 列信息
      columns: [
        { key: "belongUser", index: 1, label: `所属用户`, visible: true },
        { key: "projectNo", index: 2, label: `项目编号`, visible: true },
        { key: "projectName", index: 3, label: `项目名称`, visible: true },
        { key: "operationType", index: 4, label: `操作类型`, visible: true },
        { key: "province", index: 5, label: `项目所在地`, visible: true },
        { key: "address", index: 6, label: `详细地址`, visible: true },
        { key: "authCompany", index: 7, label: `被授权公司`, visible: true },
        { key: "distributor", index: 8, label: `所属经销商`, visible: true },
        { key: "biddingCompany", index: 9, label: `招标单位`, visible: true },
        { key: "model", index: 10, label: `投标产品型号`, visible: true },
        { key: "spec", index: 11, label: `投标产品规格`, visible: true },
        { key: "area", index: 12, label: `安装面积`, visible: true },
        { key: "requireInfo", index: 13, label: `所需资料`, visible: true },
        { key: "infoType", index: 14, label: `资料类型`, visible: true },
        { key: "scanFile", index: 15, label: `资料接收邮件`, visible: true },
        { key: "scanFile", index: 16, label: `资料接收地址`, visible: true },
        { key: "belongProvince", index: 17, label: `项目所属省份`, visible: true },
        { key: "afterSaleYear", index: 18, label: `售后年限`, visible: true },
        { key: "openDate", index: 19, label: `开标日期`, visible: true },
        { key: "hangDate", index: 20, label: `挂网日期`, visible: true },
        { key: "createTime", index: 21, label: `提交时间`, visible: true },
        { key: "updateTime", index: 22, label: `修改时间`, visible: true },
        // { key: "auditStatus", index: 19, label: `审核状态`, visible: false },
        // { key: "editStatus", index: 20, label: `编辑状态`, visible: false },
        // { key: "11", index: 21, label: `授权书`, visible: false },
        //{ key: "12", index: 23, label: `售后服务承诺函`, visible: false },
      ],
      options: regionData,
      selectedOptions: [],
      queryArea: [],
      viewOpen: false,
      view: {},
      infoList1: [],
      infoList2: [],
      defKey: "process_project_report",
      procInsId: undefined,
      taskId: undefined,
      finished: true,
      bizKey: undefined,
      auditStatusTree: [],
      operationTypeTree: [],
      userTypeTree: [],
      defaultProps: {
        children: "children",
        label: "label",
      },
      oldOperationType: undefined,
      previousOperationType: null, // 存储切换前的操作类型
      showUType: true,
      chooseOptType: undefined,
      chooseAuditStatus: undefined,
      chooseUserId: undefined,
      chooseEditStatus: undefined,
      chooseSpare2: undefined,
      likeList: undefined,
      likeCount: undefined,
      authCompanys: [],
      isAdmin: true,
      auditStatusEdit: true,
      isAuthImages: false,
      searchPickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
        shortcuts: [
          {
            text: "今天",
            onClick(picker) {
              picker.$emit("pick", new Date());
            },
          },
          {
            text: "昨天",
            onClick(picker) {
              const date = new Date();
              date.setTime(date.getTime() - 3600 * 1000 * 24);
              picker.$emit("pick", date);
            },
          },
          {
            text: "一周前",
            onClick(picker) {
              const date = new Date();
              date.setTime(date.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", date);
            },
          },
        ],
      },
      searchField: 'all', // 新增：当前选中的搜索字段，默认全字段
      searchFieldOptions: [
        { label: '全字段', value: 'all' },
        { label: '项目编号', value: 'projectNo' },
        { label: '项目名称', value: 'projectName' },
        { label: '详细地址', value: 'address' },
        { label: '招标单位', value: 'biddingCompany' },
        { label: '授权公司', value: 'authCompany' }
      ],
      searchValue: '', // 新增：搜索内容
      highlightFields: ['projectNo', 'projectName', 'address', 'biddingCompany', 'authCompany'], // 新增：高亮字段
    };
  },
  activated() {
    console.log("=report index==>>activated");
    this.getList();
  },
  created() {
    this.getList();
    this.getDicts("pr_operation_type").then((response) => {
      this.operationTypeOptions = response.data;
      var opt = [];
      opt.push({ id: 0, label: "全部" });
      response.data.forEach((elem, index) => {
        var obj = {};
        obj.label = elem.dictLabel;
        obj.id = elem.dictValue;
        opt.push(obj);
      });
      var operationType = {};
      operationType.label = "操作类型";
      operationType.children = opt;
      var operationTypes = [];
      operationTypes.push(operationType);
      this.operationTypeTree = operationTypes;
    });
    this.getDicts("pr_audit_status").then((response) => {
      var type = 0;
      if (this.$store.state.user.roles) {
        if (this.$store.state.user.roles.includes("common")) {
          type = 1;
        }
        if (this.$store.state.user.roles.includes("province_admin")) {
          type = 2;
        }
        if (this.$store.state.user.roles.includes("report_admin")) {
          type = 3;
        }
      }
      this.auditStatusOptions = response.data;
      var opt = [];
      opt.push({ id: 9, label: "全部" });
      if (type == 2 || type == 3) {
        opt.push({ id: 10, label: "未审批" });
      }
      response.data.forEach((elem, index) => {
        var obj = {};
        obj.label = elem.dictLabel;
        obj.id = elem.dictValue;
        opt.push(obj);
      });
      var auditStatusTree = {};
      auditStatusTree.label = "审核状态";
      auditStatusTree.children = opt;
      var auditStatusTrees = [];
      auditStatusTrees.push(auditStatusTree);
      this.auditStatusTree = auditStatusTrees;
    });
    this.getDicts("pr_edit_status").then((response) => {
      this.editStatusOptions = response.data;
    });
    // this.getDicts("pr_bidding_type").then((response) => {
    //   this.biddingTypeOptions = response.data;
    // });
    this.getDicts("pr_model").then((response) => {
      this.modelOption1 = response.data;
      var opt = [];
      response.data.forEach((elem, index) => {
        var obj = {};
        obj.label = elem.dictLabel;
        obj.value = elem.dictValue;
        opt.push(obj);
      });
      this.modelOptions = opt;
    });
    this.getDicts("pr_spec").then((response) => {
      this.specOption1 = response.data;
      var opt = [];
      response.data.forEach((elem, index) => {
        var obj = {};
        obj.label = elem.dictLabel;
        obj.value = elem.dictValue;
        opt.push(obj);
      });
      this.specOptions = opt;
    });
    this.getDicts("pr_info").then((response) => {
      this.requireInfoOption1 = response.data;
      var opt = [];
      response.data.forEach((elem, index) => {
        var obj = {};
        obj.label = elem.dictLabel;
        obj.value = elem.dictValue;
        opt.push(obj);
      });
      this.requireInfoOptions = opt;
    });
    this.getDicts("pr_province").then((response) => {
      this.belongProvinceOptions1 = response.data;
      var opt = [];
      response.data.forEach((elem, index) => {
        var obj = {};
        obj.label = elem.dictLabel;
        obj.value = elem.dictValue;
        opt.push(obj);
      });
      this.belongProvinceOptions = opt;
    });
    this.getDicts("pr_after_sale_year").then((response) => {
      this.afterSaleYearOptions1 = response.data;
      var opt = [];
      response.data.forEach((elem, index) => {
        var obj = {};
        obj.label = elem.dictLabel;
        obj.value = elem.dictValue;
        opt.push(obj);
      });
      this.afterSaleYearOptions = opt;
    });
    this.getDicts("pr_data_type").then((response) => {
      this.infoTypeOptions = response.data;
    });

    var opt = [];
    opt.push({ id: 0, label: "全部" });
    opt.push({ id: 2, label: "普通用户" });
    opt.push({ id: 10, label: "省负责人" });

    var userType = {};
    userType.label = "所属用户";
    userType.children = opt;
    var userTypes = [];
    userTypes.push(userType);
    this.userTypeTree = userTypes;
    if (
      this.$store.state.user.roles &&
      this.$store.state.user.roles.includes("common")
    ) {
      this.showUType = false;
    }
    if (this._isMobile()) {
      this.isMobile = true;
      this.pageLayout = "total, prev, next, jumper";
    }
    if (
      this.$store.state.user.roles &&
      this.$store.state.user.roles.includes("report_admin")
    ) {
      this.isAdmin = false;
    }
  },
  methods: {
    _isMobile() {
      let flag = navigator.userAgent.match(
        /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i
      );
      return flag;
    },
    /** 查询项目报备列表 */
    getList() {
      this.loading = true;
      listReport(this.queryParams).then((response) => {
        this.reportList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    handleDownload(url) {
      const a = document.createElement("a"); // 创建一个HTML 元素
      a.setAttribute("target", "_blank");
      a.setAttribute("download", ""); //download属性
      const href =
        "https://report.clled.com/prod-api/common/download/resource?resource=" +
        url;
      console.log(href);
      a.setAttribute("href", href); // href链接
      a.click(); // 自执行点击事件
    },
    // 审核状态节点单击事件
    handleAuditNodeClick(data) {
      if (data.id == 9) {
        this.queryParams.auditStatus = undefined;
        this.queryParams.node = undefined;
        this.queryParams.spare1 = undefined;
      } else {
        if (data.id == 1 || data.id == 10) {
          this.queryParams.auditStatus = 1;
          if (this.$store.state.user.roles) {
            if (this.$store.state.user.roles.includes("province_admin")) {
              this.queryParams.node = "省负责人";
              if (data.id == 10) {
                this.queryParams.spare1 = "=";
              } else {
                this.queryParams.spare1 = "!=";
              }
            }
            if (this.$store.state.user.roles.includes("report_admin")) {
              this.queryParams.node = "审核员";
              if (data.id == 10) {
                this.queryParams.spare1 = "=";
              } else {
                this.queryParams.spare1 = "!=";
              }
            }
          }
        } else {
          this.queryParams.auditStatus = data.id;
          this.queryParams.node = undefined;
          this.queryParams.spare1 = undefined;
        }
      }
      this.getList();
    },
    // 操作类型节点单击事件
    handleOptNodeClick(data) {
      if (data.id == 0) {
        this.queryParams.operationType = undefined;
      } else {
        this.queryParams.operationType = data.id;
      }
      this.getList();
    },
    // 用户类型节点单击事件
    handleUserNodeClick(data) {
      if (data.id == 0) {
        this.queryParams.userType = undefined;
      } else {
        this.queryParams.userType = data.id;
      }
      this.getList();
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    searchFormat(row, column) {
      if (
        row.indexOf(this.queryParams.spare1) !== -1 &&
        this.queryParams.spare1 !== ""
      ) {
        return row.replace(
          this.queryParams.spare1,
          '<font color="#f00">' + this.queryParams.spare1 + "</font>"
        );
      } else {
        return row;
      }
    },
    // 操作类型字典翻译
    operationTypeFormat(row, column) {
      return this.selectDictLabel(this.operationTypeOptions, row.operationType);
    },
    // 审核状态字典翻译
    auditStatusFormat(row, column) {
      return this.selectDictLabel(this.auditStatusOptions, row.auditStatus);
    },
    // 编辑状态字典翻译
    editStatusFormat(row, column) {
      return this.selectDictLabel(this.editStatusOptions, row.editStatus);
    },
    // 招标方式字典翻译
    biddingTypeFormat(row, column) {
      return this.selectDictLabel(this.biddingTypeOptions, row.biddingType);
    },
    // 投标产品型号字典翻译
    modelFormat(row, column) {
      return this.selectDictLabels(this.modelOption1, row.model);
    },
    // 投标产品规格字典翻译
    specFormat(row, column) {
      return this.selectDictLabels(this.specOption1, row.spec);
    },
    // 所需资料字典翻译
    requireInfoFormat(row, column) {
      if (row.requireInfo) {
        return this.selectDictLabels(this.requireInfoOption1, row.requireInfo);
      }
    },
    // 资料类型字典翻译
    infoTypeFormat(row, column) {
      return this.selectDictLabels(this.infoTypeOptions, row.infoType);
    },
    // 所属省份字典翻译
    belongProvinceFormat(row, column) {
      return this.selectDictLabels(this.belongProvinceOptions1, row.belongProvince);
    },
    // 售后年限字典翻译
    afterSaleYearFormat(row, column) {
      return this.selectDictLabels(this.afterSaleYearOptions1, row.afterSaleYear);
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    viewOk() {
      this.viewOpen = false;
    },
    // 表单重置
    reset() {
      this.form = {
        projectId: null,
        projectNo: null,
        projectName: null,
        operationType: null,
        auditStatus: "0",
        rejectReason: null,
        province: null,
        city: null,
        district: null,
        address: null,
        editStatus: "0",
        belongUser: null,
        biddingCompany: null,
        openDate: null,
        biddingType: null,
        budgetMoney: null,
        authCompany: null,
        biddingNet: null,
        distributor: null,
        model: [],
        spec: [],
        area: null,
        authFile: null,
        afterSaleFile: null,
        requireInfo: [],
        infoType: [],
        scanFile: null,
        sendAddress: null,
        mailInfo: null,
        expressInfo: null,
        remark: null,
        spare1: null,
        spare2: null,
      };
      this.previousOperationType = null; // 重置操作类型记录
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      // 清空所有相关字段
      this.queryParams.projectNo = null;
      this.queryParams.projectName = null;
      this.queryParams.address = null;
      this.queryParams.biddingCompany = null;
      this.queryParams.authCompany = null;
      this.queryParams.fullField = null;

      if (this.searchField === 'all') {
        this.queryParams.fullField = this.searchValue; // 假设后端 fullField 做全字段模糊
      } else {
        this.queryParams[this.searchField] = this.searchValue;
      }
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryArea = [];
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        projectNo: null,
        projectName: null,
        operationType: null,
        auditStatus: null,
        province: null,
        userType: null,
        belongUser: null,
        spare1: null,
        address: null,
        biddingCompany: null,
        authCompany: null,
        fullField: null
      };
      this.searchField = 'all';
      this.searchValue = '';
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.projectId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
      this.chooseOptType = selection[0].operationType;
      this.chooseAuditStatus = selection[0].auditStatus;
      this.chooseUserId = selection[0].userId;
      this.chooseEditStatus = selection[0].editStatus;
      this.chooseSpare2 = selection[0].spare2;
    },
    /** 新增按钮操作 */
    handleAdd() {
      // this.reset();
      // this.open = true;
      // this.title = "添加项目报备";
      this.$router.push({
        path: "/project/report/form",
        query: {
          businessKey: undefined,
          formEdit: true,
        },
      });
    },
    handleView(row) {
      let that = this;
      this.view = JSON.parse(JSON.stringify(row));
      this.view.operationType = this.operationTypeFormat(row);
      this.view.auditStatus = this.auditStatusFormat(row);
      this.view.editStatus = this.editStatusFormat(row);
      this.view.biddingType = this.biddingTypeFormat(row);
      this.view.model = this.modelFormat(row);
      this.view.spec = this.specFormat(row);
      this.view.requireInfo = this.requireInfoFormat(row);
      this.view.infoType = this.infoTypeFormat(row);
      this.view.belongProvince = this.belongProvinceFormat(row);
      this.view.afterSaleYear = this.afterSaleYearFormat(row);
      if (row.requireInfos) {
        this.selectDictLabels(this.requireInfoOption1, row.requireInfo);
        //this.view.requireInfo =
        // const infoList = this.view.requireInfo.split(",");
        const half = Math.ceil(row.requireInfos.length / 2);

        this.infoList1 = row.requireInfos.splice(0, half);
        this.infoList2 = row.requireInfos.splice(-half);

        // const tmpList1 = infoList.splice(0, half);
        // const tmpList2 = infoList.splice(-half);
        // tmpList1.forEach((element) => {
        //   console.log(element);
        // });
        // 循环对象赋值
      } else {
        this.infoList1 = [];
        this.infoList2 = [];
      }

      if (row.operationType == "2" && row.spare1 == "1") {
        this.defKey = "process_project_auth";
        this.title = "查看项目报备转授权";
      } else {
        this.defKey = "process_project_report";
        this.title = "查看项目报备/授权";
      }
      const params = { bizKey: row.projectId, defKey: this.defKey };
      getInsIdByBizKey(params).then((resp) => {
        this.bizKey = row.projectId;
        if (resp.data && resp.data.instanceId) {
          this.procInsId = resp.data.instanceId;
          this.taskId = resp.data.taskId;
          //console.log("==handleView=>>")
          //console.log(resp.data)
          if (
            resp.data.instanceId &&
            !resp.data.endTime &&
            resp.data.assignee == this.$store.state.user.userId
          ) {
            if (
              this.$store.state.user.roles &&
              this.$store.state.user.roles.includes("report_admin") &&
              row.operationType == "2"
            ) {
              this.isAuthImages = true;
            }
            this.finished = false;
          } else if (
            this.$store.state.user.roles &&
            this.$store.state.user.roles.includes("report_admin") &&
            row.node == "审核员"
          ) {
            if (row.operationType == "2") {
              this.isAuthImages = true;
            }
            //审核员角色不控制谁操作
            this.finished = false;
          } else {
            this.finished = true;
          }
        } else {
          this.finished = true;
          this.procInsId = undefined;
          this.taskId = undefined;
        }

        // console.log("====>>>驳回")
        // //驳回用户
        // if(row.auditStatus == '3' && row.userId == this.$store.state.user.userId){
        //   this.finished = false;
        // }
        // console.log("====>>>驳回：" + this.finished)

        this.viewOpen = true;
      });
      getLikeList({
        projectName: row.projectName,
        projectId: row.projectId,
      }).then((resp) => {
        //console.log(resp)
        if (resp.data && resp.data.length > 0) {
          this.likeList = resp.data;
          that.likecount = resp.data.length;
        } else {
          this.likeList = undefined;
          that.likecount = undefined;
        }
      });
    },
    /** 授权按钮操作 */
    handleAuth(row) {
      const loading = this.$loading({
        lock: true,
        text: "授权中...",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });
      authReport(row)
        .then((resp) => {
          loading.close();
          this.msgSuccess(resp.msg);
          this.$router.go(0);
        })
        .catch((e) => {
          loading.close();
        });
    },
    /** 修改按钮操作 */
    handleUpdate() {
      let that = this;
      // that.auditStatusEdit = true;
      // if(!that.isAdmin && that.chooseAuditStatus == 3){
      //   that.auditStatusEdit = false;
      // }else{}
      //申请者
      var isApply =
        this.$store.state.user.roles &&
        (this.$store.state.user.roles.includes("common") ||
          this.$store.state.user.roles.includes("province_admin"));

      if (isApply && this.chooseUserId != this.$store.state.user.userId) {
        this.msgError("只能修改本人提交的项目");
        return;
      }

      if (this.chooseOptType == 2) {
        if (isApply && this.chooseSpare2 != 1) {
          this.msgError("授权被退回才能修改");
          return;
        }
      }
      if (this.chooseAuditStatus == 3 && isApply) {
        if (this.chooseEditStatus == "0") {
          this.msgError("审批被驳回无法修改");
          return;
        }
      }
      // if(this.chooseOptType == 1){
      //   if(isApply && this.chooseUserId != this.$store.state.user.userId ){
      //     this.msgError("只能修改本人提交的报备项目");
      //     return;
      //   }
      // }
      this.reset();
      const projectId = this.ids;
      getReport(projectId).then((response) => {
        this.form = response.data;
        if (this.form.model) this.form.model = this.form.model.split(",");
        else this.form.model = [];
        if (this.form.requireInfo)
          this.form.requireInfo = this.form.requireInfo.split(",");
        else this.form.requireInfo = [];
        if (this.form.infoType)
          this.form.infoType = this.form.infoType.split(",");
        else this.form.infoType = [];
        if (this.form.spec) this.form.spec = this.form.spec.split(",");
        else this.form.spec = [];

        if (this.form.authCompany) {
          that.authCompanys = [];
          var array = this.form.authCompany.split(",");
          array.forEach(function (e) {
            that.authCompanys.push({
              value: e,
              key: Date.now(),
            });
          });
          that.form.authCompany = that.authCompanys[0].value;
          that.authCompanys.splice(0, 1);
          //console.log(that.authCompanys)
        } else this.authCompanys = [];

        this.oldOperationType = response.data.operationType;
        this.previousOperationType = response.data.operationType; // 设置初始操作类型

        this.open = true;
        this.title = "修改项目报备";
        var provinces = response.data.province;
        if (provinces.length > 0) {
          var address = provinces.split("/");
          var citys = [];
          // 省份
          if (address.length > 0) citys.push(TextToCode[address[0]].code);
          // 城市
          if (address.length > 1)
            citys.push(TextToCode[address[0]][address[1]].code);
          // 地区
          if (address.length > 2)
            citys.push(TextToCode[address[0]][address[1]][address[2]].code);

          this.selectedOptions = citys;
        }
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.infoType.indexOf("1") >= 0 && this.form.scanFile) {
            let emailReg = /^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/;
            if (!emailReg.test(this.form.scanFile)) {
              this.$message.error("资料接收方式邮箱格式错误");
              return;
            }
          }
          if (this.form.operationType == 1) {
            this.form.authFile = "";
            this.form.afterSaleFile = "";
          }
          var formStr = JSON.stringify(this.form);
          var formData = JSON.parse(formStr);
          if (formData.model && formData.model.length > 0)
            formData.model = formData.model.join(",");
          else formData.model = undefined;
          if (formData.requireInfo && formData.requireInfo.length > 0)
            formData.requireInfo = formData.requireInfo.join(",");
          else formData.requireInfo = undefined;
          if (formData.infoType && formData.infoType.length > 0)
            formData.infoType = formData.infoType.join(",");
          else formData.infoType = undefined;
          if (formData.spec && formData.spec.length > 0)
            formData.spec = formData.spec.join(",");
          else formData.spec = undefined;

          //授权公司
          if (this.authCompanys.length > 0) {
            var array = new Array();
            this.authCompanys.forEach(function (e) {
              array.push(e.value);
            });
            formData.authCompany += "," + array.join(",");
          }

          if (formData.projectId != null) {
            updateReport(formData).then((response) => {
              this.msgSuccess("修改成功");

              if (this.oldOperationType == 1 && formData.operationType == 2) {
                console.log("=====>>>报备改授权");
              }

              this.open = false;
              this.getList();
            });
          } else {
            addReport(formData).then((response) => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const projectIds = row.projectId || this.ids;
      this.$confirm(
        '是否确认删除项目报备编号为"' + projectIds + '"的数据项?',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(function () {
          return delReport(projectIds);
        })
        .then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        });
    },
    clickExport() {
      this.showExport = true;
    },
    clickPrint() {
      this.showPrint = true;
    },
    /** 导出按钮操作 */
    handleExport(type) {
      let loadingwin;
      let that = this;
      this.queryParams.spare1 = type;
      var col = [];
      this.columns.forEach((item) => {
        if (item.visible) {
          col.push(item.label);
        }
      });
      this.queryParams.spare2 = col.join(",");
      const queryParams = this.queryParams;
      this.$confirm(
        "是否确认导出项目报备搜索结果" +
        (type == 0 ? "本页" : "全部") +
        "数据项?",
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(function () {
          loadingwin = that.$loading({
            lock: true, //lock的修改符--默认是false
            text: "导出中...", //显示在加载图标下方的加载文案
            spinner: "el-icon-loading", //自定义加载图标类名
            background: "rgba(0, 0, 0, 0.7)", //遮罩层颜色
            target: document.querySelector(".app-wrapper"), //loadin覆盖的dom元素节点
          });
          document.documentElement.style.overflowY = "hidden"; //禁止底层div滚动
          return exportReport(queryParams);
        })
        .then((response) => {
          this.download(response.msg);
          this.showExport = false;
          loadingwin.close();
          document.documentElement.style.overflowY = "auto"; //允许底层div滚动
        })
        .catch(() => {
          this.showExport = false;
          loadingwin.close();
          document.documentElement.style.overflowY = "auto"; //允许底层div滚动
        });
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "项目导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      importTemplate().then((response) => {
        this.download(response.msg);
      });
    },
    downloadSQS() {
      this.download("海佳集团-授权书.docx", false);
    },
    downloadCRH() {
      this.download("海佳集团-售后服务承诺函.doc", false);
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert(response.msg, "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
    handleChange(value) {
      if (!value || value.length == 0) {
        this.selectedOptions = null;
        this.form.province = undefined;
        this.form.district = undefined;
        return;
      }
      this.selectedOptions = value;
      var txt = "";
      value.forEach(function (item) {
        txt += CodeToText[item] + "/";
      });
      if (txt.length > 1) {
        txt = txt.substring(0, txt.length - 1);
        this.form.district = this.$store.state.user.province;
        this.form.province = txt;
      } else {
        this.form.province = undefined;
        this.form.district = undefined;
      }
    },
    handleQueryCityChange(value) {
      this.queryArea = value;
      var txt = "";
      value.forEach(function (item) {
        txt += CodeToText[item] + "/";
      });
      if (txt.length > 1) {
        txt = txt.substring(0, txt.length - 1);
        this.queryParams.province = txt;
      } else {
        this.queryParams.province = undefined;
      }
    },
    handlePrint(type) {
      this.queryParams.spare1 = type;
      var properties = [];
      //properties.push({field: 'index', displayName: '序号'});
      properties.push({ field: "projectId", displayName: "项目ID" });
      this.columns.forEach((item) => {
        if (item.visible) {
          properties.push({ field: item.key, displayName: item.label });
        }
      });
      printReport(this.queryParams).then((response) => {
        printJS({
          printable: response.data,
          type: "json",
          properties: properties,
          header: '<div style="text-align: center"><h3>项目报备列表</h3></div>',
          targetStyles: ["*"],
          gridHeaderStyle:
            "margin-top:20px;border: 1px solid #000;text-align:center",
          gridStyle: "border: 1px solid #000;text-align:center;min-width:50px;",
          style: "@page {margin:0 10mm;margin-top:10mm;}",
        });
      });
      // printJS({
      //   printable: "printArea",
      //   type:'html',
      //   header:null,
      //   targetStyles:['*'],
      //   style:"@page {margin:0 10mm}"
      // })
    },
    // 删除 showNameCorlor 和 showNoCorlor 方法，新增高亮方法
    highlightText(text, keyword) {
      if (!keyword) return text;
      // 全部高亮
      return text ? text.replace(new RegExp(keyword, 'g'), `<font color="#f00">${keyword}</font>`) : text;
    },
    highlightCell(field, text) {
      if (this.searchField === 'all' && this.searchValue && this.highlightFields.includes(field)) {
        return this.highlightText(text, this.searchValue);
      }
      if (this.searchField === field && this.searchValue) {
        return this.highlightText(text, this.searchValue);
      }
      return text;
    },
    removeDomain(index) {
      if (index !== -1) {
        this.authCompanys.splice(index, 1);
      }
    },
    addDomain() {
      this.authCompanys.push({
        value: "",
        key: Date.now(),
      });
    },
    userSearch(createBy) {
      this.queryParams.belongUser = createBy;
      this.handleQuery();
    },
    optTypeSearch(type) {
      this.queryParams.operationType = type;
      this.handleQuery();
    },
    // 处理操作类型切换的确认
    handleOperationTypeChange(newValue) {
      // 如果是第一次设置或者值没有变化，直接设置
      if (this.previousOperationType === null || this.previousOperationType === newValue) {
        this.previousOperationType = newValue;
        return;
      }

      // 显示确认对话框
      this.$confirm('切换操作类型将可能影响表单数据，确定要切换吗？', '确认切换', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 用户确认切换
        this.previousOperationType = newValue;
        this.optTypeChange(newValue);
      }).catch(() => {
        // 用户取消切换，恢复到之前的值
        this.$nextTick(() => {
          this.form.operationType = this.previousOperationType;
        });
      });
    },

    optTypeChange(e) {
      console.log('操作类型已切换为:', e);
      // 这里可以添加切换后的逻辑处理
      // 比如清空某些字段、显示/隐藏某些表单项等
    },
  },
};
</script>
<style>
@media screen and (min-width: 600px) {
  .view-dialog {
    width: 80% !important;
    float: right;
  }
}

@media screen and (max-width: 599px) {
  .view-dialog {
    width: 100% !important;
    float: right;
  }

  .edit-dialog {
    width: 100% !important;

    margin-bottom: 0;
    height: 100%;
    overflow: auto;
  }

  .el-dialog:not(.is-fullscreen) {
    margin-top: 0 !important;
  }

  .el-form .el-col {
    width: 100% !important;
  }

  .info-type .el-row {
    flex-direction: column;
  }

  .info-type .el-input {
    width: 100% !important;
  }

  .mobile-width {
    width: 100% !important;
  }
}

.likeTip {
  color: rgb(247, 11, 11);
  font-size: 12px;
}

.el-loading-mask {
  z-index: 2001 !important;
}
</style>
